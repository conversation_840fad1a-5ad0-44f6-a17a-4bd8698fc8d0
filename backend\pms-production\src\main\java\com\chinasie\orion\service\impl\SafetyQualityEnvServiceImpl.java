package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.bo.DeptConvertBO;
import com.chinasie.orion.bo.OrionRoleConfig;
import com.chinasie.orion.bo.UserConvertBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.AssessmentLevelEnum;
import com.chinasie.orion.domain.dto.SafetyPyramidParamDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvStatisticDTO;
import com.chinasie.orion.domain.dto.SimpleSqeDTO;
import com.chinasie.orion.domain.dto.train.SimpleSearchDTO;
import com.chinasie.orion.domain.entity.SafetyQualityEnv;
import com.chinasie.orion.domain.vo.SafetyQualityEnvExcelVO;
import com.chinasie.orion.domain.vo.SafetyQualityEnvVO;
import com.chinasie.orion.domain.vo.count.BaseScoreVO;
import com.chinasie.orion.domain.vo.count.SafetyPyramidCount;
import com.chinasie.orion.domain.vo.count.SafetyQualityEnvCountVO;
import com.chinasie.orion.domain.vo.count.TableScoreVO;
import com.chinasie.orion.domain.vo.env.DeviationStatisticsVO;
import com.chinasie.orion.domain.vo.env.DeviationTop;
import com.chinasie.orion.domain.vo.env.DeviationTopVO;
import com.chinasie.orion.domain.vo.env.RowStatisticsVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.SafetyQualityEnvMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.SafetyQualityEnvService;
import com.chinasie.orion.service.TrainContactService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.jetbrains.annotations.Nullable;
import org.eclipse.jetty.util.StringUtil;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.AssessmentLevelEnum.*;
import static com.chinasie.orion.constant.DictConts.PMS_ASSESSMENT_LEVEL;
import static com.chinasie.orion.constant.DictConts.PMS_PYRAMID_CATEGORY;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/14:59
 * @description:
 */
@Service
@Slf4j
public class SafetyQualityEnvServiceImpl extends OrionBaseServiceImpl<SafetyQualityEnvMapper, SafetyQualityEnv> implements SafetyQualityEnvService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;


    private MajorRepairPlanService majorRepairPlanService;

    @Autowired
    private TrainContactService trainContactService;
    @Autowired
    private OrionRoleConfig orionRoleConfig;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private UserRedisHelper userRedisHelper;

//    private static  final String  ENV_PM_CODE ="env_pm_code";
    @Autowired
    public void setMajorRepairPlanService(MajorRepairPlanService majorRepairPlanService) {
        this.majorRepairPlanService = majorRepairPlanService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SafetyQualityEnvVO detail(String id, String pageCode) throws Exception {
        SafetyQualityEnv safetyQualityEnv = this.getById(id);
        SafetyQualityEnvVO result = BeanCopyUtils.convertTo(safetyQualityEnv, SafetyQualityEnvVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param safetyQualityEnvDTO
     */
    @Override
    public String create(SafetyQualityEnvDTO safetyQualityEnvDTO) throws Exception {
        SafetyQualityEnv safetyQualityEnv = BeanCopyUtils.convertTo(safetyQualityEnvDTO, SafetyQualityEnv::new);
        this.save(safetyQualityEnv);

        String rsp = safetyQualityEnv.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param safetyQualityEnvDTO
     */
    @Override
    public Boolean edit(SafetyQualityEnvDTO safetyQualityEnvDTO) throws Exception {
        SafetyQualityEnv safetyQualityEnv = BeanCopyUtils.convertTo(safetyQualityEnvDTO, SafetyQualityEnv::new);

        String userId=CurrentUserHelper.getCurrentUserId();
        if(StringUtils.hasText(safetyQualityEnvDTO.getEventLocationCode())){
            List<String> baseCodeList=  trainContactService.getCodeMap(userId,orionRoleConfig.getEnvPmCode());
            if(!baseCodeList.contains(safetyQualityEnvDTO.getEventLocationCode())){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "无权操作");
            }
        }
        safetyQualityEnv.setModifyId(CurrentUserHelper.getCurrentUserId());
        safetyQualityEnv.setModifyTime(new Date());
        this.updateById(safetyQualityEnv);

        String rsp = safetyQualityEnv.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SafetyQualityEnvVO> pages(Page<SafetyQualityEnvDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SafetyQualityEnv::getCreateTime);


        Page<SafetyQualityEnv> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SafetyQualityEnv::new));
        SafetyQualityEnvDTO query = pageRequest.getQuery();
        //增加列表筛选大修轮次
        if(query != null && StringUtil.isNotBlank(query.getMajorRepairTurn())){
            condition.eq(SafetyQualityEnv::getMajorRepairTurn,query.getMajorRepairTurn());
        }
        PageResult<SafetyQualityEnv> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SafetyQualityEnvVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SafetyQualityEnvVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SafetyQualityEnvVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<SafetyQualityEnvVO> vos) throws Exception {
        List<DictValueVO> gateGroupDict = dictRedisHelper.getDictListByCode(PMS_PYRAMID_CATEGORY);
        Map<String, String> numberToDesc = gateGroupDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        List<DictValueVO> assessmentDict = dictRedisHelper.getDictListByCode(PMS_ASSESSMENT_LEVEL);
        Map<String, String> assessmentToDesc = assessmentDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
        Map<String, String> basePlaceMap = basePlaceService.allMapSimpleList();
//        List<SimpleDeptVO> allSimpleDept = deptRedisHelper.getAllSimpleDept();
        //Map<String, SimpleDeptVO> deptVOMap = allSimpleDept.stream().collect(Collectors.toMap(SimpleDeptVO::getAbbreviation, Function.identity()));

        String userId =CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList=  trainContactService.getCodeMap(userId,orionRoleConfig.getEnvPmCode());

        Boolean bool = CollectionUtils.isEmpty(baseCodeList)?Boolean.FALSE:Boolean.TRUE;

        List<String> userCodes = new ArrayList<>();
        List<String> deptCodes = new ArrayList<>();
        for (SafetyQualityEnvVO vo : vos) {
            if (StringUtils.hasText(vo.getDeptCode())){
                deptCodes.add(vo.getDeptCode());
            }
            if (StringUtils.hasText(vo.getReviewerNumber())){
                userCodes.add(vo.getReviewerNumber());
            }
        }

        Map<String, String> userCodeToName = UserConvertBO.getMapByCodesForRedis(userCodes,true);
        Map<String, String> deptCodeToName = DeptConvertBO.getMapByCodesForMapper(deptCodes,true);
        vos.forEach(vo -> {
            if (StringUtils.hasText(vo.getReviewerNumber())){
                vo.setReviewerName(userCodeToName.getOrDefault(vo.getReviewerNumber(), ""));
            }
            if (StringUtils.hasText(vo.getDeptCode())){
                vo.setDeptName(deptCodeToName.getOrDefault(vo.getDeptCode(), ""));
            }
            vo.setPyramidCategoryName(numberToDesc.getOrDefault(vo.getPyramidCategory(), ""));
            vo.setAssessmentLevelName(assessmentToDesc.getOrDefault(vo.getAssessmentLevel(), ""));
            vo.setEventLocationCodeName(basePlaceMap.getOrDefault(vo.getBaseCode(),""));
//            if(deptVOMap.get(vo.getRspDeptCode()) != null){
//                SimpleDeptVO simpleDeptVO = deptVOMap.get(vo.getRspDeptCode());
//                //vo.setRspDeptName(simpleDeptVO.getName() + "(" + simpleDeptVO.getAbbreviation() + ")");
//                vo.setRspDeptName(simpleDeptVO.getAbbreviation());
//            }
            if(StringUtils.hasText(vo.getBaseCode())){
                String baseCode = vo.getBaseCode();
                vo.setEdit(baseCodeList.contains(baseCode));
            }else{
                vo.setEdit(bool);
            }
        });
    }

    @Override
    public Boolean editSimple(SimpleSqeDTO simpleSqeDTO) {
        String id = simpleSqeDTO.getId();
        SafetyQualityEnv byId = this.getById(id);
        if (null == byId) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或已删除");
        }
        Boolean isAssessed = simpleSqeDTO.getIsAssessed();
        if(Objects.equals(isAssessed, Boolean.FALSE)){
            byId.setAssessmentLevel(null);
        }else{
            byId.setAssessmentLevel(simpleSqeDTO.getAssessmentLevel());
        }
        byId.setPyramidCategory(simpleSqeDTO.getPyramidCategory());
        byId.setMajorRepairTurn(simpleSqeDTO.getMajorRepairTurn());
        byId.setIsAssessed(simpleSqeDTO.getIsAssessed());
        byId.setBaseCode(simpleSqeDTO.getBaseCode());
        byId.setEventLocation(simpleSqeDTO.getEventLocation());
        byId.setModifyId(CurrentUserHelper.getCurrentUserId());
        byId.setModifyTime(new Date());
        return this.updateById(byId);
    }

    @Override
    public SafetyQualityEnvStatisticDTO listByMajorRepairTurn(SimpleSearchDTO searchDTO) throws Exception {
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        String majorRepairTurn = searchDTO.getMajorRepairTurn();
        if (StrUtil.isEmpty(majorRepairTurn)) {
            return new SafetyQualityEnvStatisticDTO();
        }
        condition.eq(SafetyQualityEnv::getMajorRepairTurn, searchDTO.getMajorRepairTurn());
        if (!StringUtils.hasText(searchDTO.getKeyword())) {
            condition.like(SafetyQualityEnv::getEventTopic, searchDTO.getKeyword());
        }
        List<SafetyQualityEnv> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new SafetyQualityEnvStatisticDTO();
        }
        List<SafetyQualityEnvVO> safetyQualityEnvVOS = BeanCopyUtils.convertListTo(list, SafetyQualityEnvVO::new);
        HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
        int index_event_count =0, a_violation_count=0, b_violation_count=0, c_violation_count=0, f1_defect_count=0, f2_defect_count=0;
        int index_event_fo_count=0,  index_event_is_count=0,  index_event_rp_count=0, index_event_other_count=0;
        for (SafetyQualityEnvVO item : safetyQualityEnvVOS) {
            if(StringUtils.hasText(item.getAssessmentLevel())){
                if(item.getAssessmentLevel().startsWith("pms_index_event")) {
                    index_event_count++;
                    stringIntegerHashMap.put(pms_index_event.getKey(), index_event_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_fo")) {
                    index_event_fo_count++;
                    stringIntegerHashMap.put(pms_index_event_fo.getKey(), index_event_fo_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_is")) {
                    index_event_is_count++;
                    stringIntegerHashMap.put(pms_index_event_is.getKey(), index_event_is_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_rp")) {
                    index_event_rp_count++;
                    stringIntegerHashMap.put(pms_index_event_rp.getKey(), index_event_rp_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_other")) {
                    index_event_other_count++;
                    stringIntegerHashMap.put(pms_index_event_other.getKey(), index_event_other_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_a_violation")) {
                    a_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_a_violation.getKey(), a_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_b_violation")) {
                    b_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_b_violation.getKey(), b_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_c_violation")) {
                    c_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_c_violation.getKey(), c_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_f1_defect")) {
                    f1_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f1_defect.getKey(), f1_defect_count);
                }

                if(item.getAssessmentLevel().equals("pms_f2_defect")) {
                    f2_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), f2_defect_count);
                }
            }
        }

        if(!stringIntegerHashMap.containsKey(pms_index_event_other.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_index_event_other.getKey(), 0);
        }

        if(!stringIntegerHashMap.containsKey(pms_index_event_rp.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_index_event_rp.getKey(), 0);
        }

        if(!stringIntegerHashMap.containsKey(pms_index_event_is.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_index_event_is.getKey(), 0);
        }

        if(!stringIntegerHashMap.containsKey(pms_index_event_fo.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_index_event_fo.getKey(), 0);
        }



        if(!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f2_defect.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), 0);
        }

        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f1_defect.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f1_defect.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_c_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_c_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_b_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_b_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_a_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_a_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event.getKey())) {
            stringIntegerHashMap.put(pms_index_event.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f2_defect.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), 0);
        }

        SafetyQualityEnvStatisticDTO safetyQualityEnvStatisticDTO = new SafetyQualityEnvStatisticDTO();
        safetyQualityEnvStatisticDTO.setSafetyQualityEnvVOList(safetyQualityEnvVOS);
        safetyQualityEnvStatisticDTO.setEvenStatisticMap(stringIntegerHashMap);
        setEveryName(safetyQualityEnvVOS);
        return safetyQualityEnvStatisticDTO;
    }


    @Override
    public  HashMap<String, HashMap<String, Integer>> safetyQualityList(List<String> majorRepairTurnList){
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        if (majorRepairTurnList.isEmpty()) {
            return new HashMap<String, HashMap<String, Integer>>();
        }
        condition
                .eq(SafetyQualityEnv::getLogicStatus,1)
                .in(SafetyQualityEnv::getMajorRepairTurn,majorRepairTurnList);
        List<SafetyQualityEnv> list = this.list(condition);

        int index_event_count =0, a_violation_count=0, b_violation_count=0, c_violation_count=0, f1_defect_count=0, f2_defect_count=0;
        int index_event_fo_count=0,  index_event_is_count=0,  index_event_rp_count=0, index_event_other_count=0;

        HashMap<String, HashMap<String, Integer>> stringHashMapHashMap = new HashMap<>();

        for (SafetyQualityEnv item : list) {
            HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
            if(StringUtils.hasText(item.getAssessmentLevel())){
                if(item.getAssessmentLevel().startsWith("pms_index_event")) {
                    index_event_count++;
                    stringIntegerHashMap.put(pms_index_event.getKey(), index_event_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_fo")) {
                    index_event_fo_count++;
                    stringIntegerHashMap.put(pms_index_event_fo.getKey(), index_event_fo_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_is")) {
                    index_event_is_count++;
                    stringIntegerHashMap.put(pms_index_event_is.getKey(), index_event_is_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_rp")) {
                    index_event_rp_count++;
                    stringIntegerHashMap.put(pms_index_event_rp.getKey(), index_event_rp_count);
                }

                if(item.getAssessmentLevel().equals("pms_index_event_other")) {
                    index_event_other_count++;
                    stringIntegerHashMap.put(pms_index_event_other.getKey(), index_event_other_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_a_violation")) {
                    a_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_a_violation.getKey(), a_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_b_violation")) {
                    b_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_b_violation.getKey(), b_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_class_c_violation")) {
                    c_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_c_violation.getKey(), c_violation_count);
                }

                if(item.getAssessmentLevel().equals("pms_f1_defect")) {
                    f1_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f1_defect.getKey(), f1_defect_count);
                }

                if(item.getAssessmentLevel().equals("pms_f2_defect")) {
                    f2_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), f2_defect_count);
                }

                stringHashMapHashMap.put(item.getMajorRepairTurn(),stringIntegerHashMap);
            }
        }

        return stringHashMapHashMap;
    }

    @Override
    public void exportByExcel(SimpleSearchDTO searchDTO,HttpServletResponse response) throws Exception {

        /**
         *  序号、隐含编号、事件主题、事件等级、事件地点、检查人、检查人所在部门、责任中心、事件描述、事件位置
         *  、分类类型、隐患类型、事发日期、大修轮次、隐患/事件领域、事件类型、事发日期
         *  、是否已关闭、直接责任部门、金字塔类别、是否考核、考核级别。
         * 排序：
         * 按事发日期从新到旧排序。（如2024-09-20~2024-09-25期间的事发日期，2024-09-25日排在最前面，2024-09-20日排在最后面）。
         */
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        String majorRepairTurn = searchDTO.getMajorRepairTurn();
        condition.eq(SafetyQualityEnv::getMajorRepairTurn, majorRepairTurn);
        if (StringUtils.hasText(searchDTO.getKeyword())) {
            condition.like(SafetyQualityEnv::getEventTopic, searchDTO.getKeyword());
        }if (!CollectionUtils.isEmpty(searchDTO.getIdList())) {
            condition.in(SafetyQualityEnv::getId, searchDTO.getIdList());
        }
        List<SafetyQualityEnv> list = this.list(condition);
        List<SafetyQualityEnvExcelVO> safetyQualityEnvExcelVOList= new ArrayList<>();
        if(!CollectionUtils.isEmpty(list)){
            List<SafetyQualityEnvVO> safetyQualityEnvVOS = BeanCopyUtils.convertListTo(list, SafetyQualityEnvVO::new);
            setEveryName(safetyQualityEnvVOS);
            AtomicInteger i = new AtomicInteger(1);
            for (SafetyQualityEnvVO safetyQualityEnvVO : safetyQualityEnvVOS) {
                SafetyQualityEnvExcelVO safetyQualityEnvExcelVO=  BeanCopyUtils.convertTo(safetyQualityEnvVO,SafetyQualityEnvExcelVO::new);
                safetyQualityEnvExcelVO.setSort(i.get());
                safetyQualityEnvExcelVO.setIsAssessed(Objects.isNull(safetyQualityEnvVO.getIsAssessed())?"":Objects.equals(safetyQualityEnvVO.getIsAssessed(),true)?"是":"否");
                safetyQualityEnvExcelVO.setIsClosed(Objects.isNull(safetyQualityEnvVO.getIsClosed())?"":Objects.equals(safetyQualityEnvVO.getIsClosed(),true)?"是":"否");
                i.incrementAndGet();
                safetyQualityEnvExcelVOList.add(safetyQualityEnvExcelVO);
            }
        }
        String fileName = "安质环管理.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SafetyQualityEnvExcelVO.class,safetyQualityEnvExcelVOList );
    }

    @Override
    public List<DeviationStatisticsVO> deviationStatistics(SimpleSearchDTO simpleSearchDTO) {
        String majorRepairTurn = simpleSearchDTO.getMajorRepairTurn();

        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        condition.eq(SafetyQualityEnv::getMajorRepairTurn, majorRepairTurn);
        condition.select(SafetyQualityEnv::getCreateTime,SafetyQualityEnv::getPyramidCategory
                ,SafetyQualityEnv::getHiddenEvent,SafetyQualityEnv::getRspDeptCode
                ,SafetyQualityEnv::getRspDeptName);
        condition.isNotNull(SafetyQualityEnv::getPyramidCategory)
                .ne(SafetyQualityEnv::getPyramidCategory,"");
        condition.in(SafetyQualityEnv::getHiddenEvent,Arrays.asList("安全","质量"));
        List<SafetyQualityEnv> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<DictValueVO> gateGroupDict = dictRedisHelper.getDictListByCode(PMS_PYRAMID_CATEGORY);
        Map<String, String> numberToDesc = gateGroupDict.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));

        Map<String, List<SafetyQualityEnv>> deptToList= list.stream().filter(item-> StringUtils.hasText(item.getRspDeptCode()))
                .collect(Collectors.groupingBy(SafetyQualityEnv::getRspDeptCode));

        List<DeviationStatisticsVO> deviationStatisticsVOList = new ArrayList<>();

        Map<String, RowStatisticsVO> rowStatisticsVOMap1 = new HashMap<>();


        List<String> deptCodeList = new ArrayList<>();
         deptToList.forEach((K,V)->{
             deptCodeList.add(K);
         });
        Map<String,String> codeToAbbre = new HashMap<>();
        if(!CollectionUtils.isEmpty(deptCodeList)){
            LambdaQueryWrapperX<DeptDO> deptDOLambdaQueryWrapperX = new LambdaQueryWrapperX<>(DeptDO.class);
            deptDOLambdaQueryWrapperX.in(DeptDO::getDeptCode,deptCodeList);
            deptDOLambdaQueryWrapperX.select(DeptDO::getId,DeptDO::getDeptCode,DeptDO::getAbbreviation,DeptDO::getName);
            List<DeptDO> deptDOList =deptDOMapper.selectList(deptDOLambdaQueryWrapperX);
            codeToAbbre = deptDOList.stream().filter(item-> StringUtils.hasText(item.getCodeName()))
                    .collect(Collectors.toMap(DeptDO::getDeptCode, DeptDO::getCodeName, (k1, k2) -> k1));
        }

        for (Map.Entry<String, List<SafetyQualityEnv>> stringListEntry : deptToList.entrySet()) {
            DeviationStatisticsVO deviationStatisticsVO = new DeviationStatisticsVO();

            String deptCode = stringListEntry.getKey();
            List<SafetyQualityEnv> safetyQualityEnvList= stringListEntry.getValue();

            Map<String, RowStatisticsVO> rowStatisticsVOMap = new HashMap<>();
            String rspDeptName= "";
            for (SafetyQualityEnv safetyQualityEnv : safetyQualityEnvList) {
                RowStatisticsVO rowStatisticsVO=  rowStatisticsVOMap.getOrDefault(safetyQualityEnv.getPyramidCategory(), new RowStatisticsVO());
                // 获取当前日期
                Date today = new Date();
                // 设置日期格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                // 将日期转换为字符串
                String todayStr = sdf.format(today);
                String dateToCheckStr = Objects.nonNull(safetyQualityEnv.getCreateTime())? sdf.format(safetyQualityEnv.getCreateTime()) : "";

                if(Objects.equals(safetyQualityEnv.getHiddenEvent(),"安全")){
                    // 判断dateToCheck是否是今天
                    if (todayStr.equals(dateToCheckStr)) {
                        rowStatisticsVO.setSafetyDayCount(rowStatisticsVO.getSafetyDayCount()+1);
                    }
                    rowStatisticsVO.setSafetyCount(rowStatisticsVO.getSafetyCount()+1);
                }else if(Objects.equals(safetyQualityEnv.getHiddenEvent(),"质量")){
                    if (todayStr.equals(dateToCheckStr)) {
                        rowStatisticsVO.setQualityDayCount(rowStatisticsVO.getQualityDayCount()+1);
                    }
                    rowStatisticsVO.setQualityCount(rowStatisticsVO.getQualityCount()+1);
                }
                rowStatisticsVOMap.put(safetyQualityEnv.getPyramidCategory(),rowStatisticsVO);
                if(StringUtils.hasText(safetyQualityEnv.getRspDeptName())){
                    rspDeptName = safetyQualityEnv.getRspDeptName();
                }
            }
            deviationStatisticsVO.setDeptCode(deptCode);
            if(codeToAbbre.containsKey(deptCode)){
                deviationStatisticsVO.setName(codeToAbbre.get(deptCode));
            }else{
                deviationStatisticsVO.setName(rspDeptName);
            }
            deviationStatisticsVO.setUniqueId(IdUtil.nanoId());

            List<RowStatisticsVO> rowStatisticsVOList = new ArrayList<>();

            for (Map.Entry<String, String> stringStringEntry : numberToDesc.entrySet()) {
                String dictNumber=  stringStringEntry.getKey();

                RowStatisticsVO rowStatisticsVO= rowStatisticsVOMap.getOrDefault(dictNumber,new RowStatisticsVO());
                rowStatisticsVO.setDictName(stringStringEntry.getValue());
                rowStatisticsVO.setDictNumber(dictNumber);
                rowStatisticsVOList.add(rowStatisticsVO);
            }

            for (RowStatisticsVO rowStatisticsVO : rowStatisticsVOList) {
                RowStatisticsVO rowStatisticsVO1= rowStatisticsVOMap1.getOrDefault(rowStatisticsVO.getDictNumber(),new RowStatisticsVO());
                rowStatisticsVO1.setDictName(rowStatisticsVO.getDictName());
                rowStatisticsVO1.setDictNumber(rowStatisticsVO.getDictNumber());
                rowStatisticsVO1.setQualityCount(rowStatisticsVO1.getQualityCount()+rowStatisticsVO.getQualityCount());
                rowStatisticsVO1.setQualityDayCount(rowStatisticsVO1.getQualityDayCount()+rowStatisticsVO.getQualityDayCount());
                rowStatisticsVO1.setSafetyCount(rowStatisticsVO1.getSafetyCount()+rowStatisticsVO.getSafetyCount());
                rowStatisticsVO1.setSafetyDayCount(rowStatisticsVO1.getSafetyDayCount()+rowStatisticsVO.getSafetyDayCount());
                rowStatisticsVOMap1.put(rowStatisticsVO.getDictNumber(),rowStatisticsVO1);
            }

            deviationStatisticsVO.setRowStatisticsVOList(rowStatisticsVOList);
            deviationStatisticsVOList.add(deviationStatisticsVO);
        }

        List<DeviationStatisticsVO> all = new ArrayList<>();

        DeviationStatisticsVO allDeviationStatisticsVO = new DeviationStatisticsVO();
        allDeviationStatisticsVO.setUniqueId(IdUtil.nanoId());
        allDeviationStatisticsVO.setName("合计");
        List<RowStatisticsVO> rowStatisticsVOList = new ArrayList<>();
        for (Map.Entry<String, RowStatisticsVO> stringRowStatisticsVOEntry : rowStatisticsVOMap1.entrySet()) {
            rowStatisticsVOList.add(stringRowStatisticsVOEntry.getValue());
        }
        allDeviationStatisticsVO.setRowStatisticsVOList(rowStatisticsVOList);
        all.add(allDeviationStatisticsVO);
        all.addAll(deviationStatisticsVOList);
        return all;
    }

    @Override
    public DeviationTopVO deviationTop(SimpleSearchDTO searchDTO) {
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        condition.eq(SafetyQualityEnv::getMajorRepairTurn, searchDTO.getMajorRepairTurn());
        condition.select(SafetyQualityEnv::getCreateTime,SafetyQualityEnv::getPyramidCategory
                ,SafetyQualityEnv::getHiddenEvent,SafetyQualityEnv::getRspDeptCode
                ,SafetyQualityEnv::getRspDeptName,SafetyQualityEnv::getEventType,SafetyQualityEnv::getId);
        condition.isNotNull(SafetyQualityEnv::getEventType)
                .ne(SafetyQualityEnv::getEventType,"");
        List<SafetyQualityEnv> list = this.list(condition);
        DeviationTopVO deviationTopVO = new DeviationTopVO();
        if(CollectionUtils.isEmpty(list)){
            return deviationTopVO;
        }
        long topSize=searchDTO.getTopSize();

        List<DeviationTop> safetyList = new ArrayList<>();
        List<DeviationTop> qualityList = new ArrayList<>();
        Map<String,List<SafetyQualityEnv>> hiddenEventMap = list.stream().filter(item -> StringUtils.hasText(item.getHiddenEvent())).collect(Collectors.groupingBy(SafetyQualityEnv::getHiddenEvent));
        for (Map.Entry<String, List<SafetyQualityEnv>> stringListEntry : hiddenEventMap.entrySet()) {
            List<SafetyQualityEnv> safetyQualityEnvList=  stringListEntry.getValue();
            String hiddenEvent = stringListEntry.getKey();
            Map<String, Long> eventTypeMap = safetyQualityEnvList.stream()
                    .collect(Collectors.groupingBy(SafetyQualityEnv::getEventType,Collectors.mapping(SafetyQualityEnv::getEventType,Collectors.counting())));
//            for (Map.Entry<String, Long> stringLongEntry : eventTypeMap.entrySet()) {
//                DeviationTop deviationTop = new DeviationTop();
//                deviationTop.setCount(stringLongEntry.getValue());
//                deviationTop.setEventType(stringLongEntry.getKey());
//                safetyList.add(deviationTop);
//            }

            if(Objects.equals(hiddenEvent,"安全")){
                for (Map.Entry<String, Long> stringLongEntry : eventTypeMap.entrySet()) {
                    DeviationTop deviationTop = new DeviationTop();
                    deviationTop.setCount(stringLongEntry.getValue());
                    deviationTop.setEventType(stringLongEntry.getKey());
                    safetyList.add(deviationTop);
                }
            }else if(Objects.equals(hiddenEvent,"质量")) {
                for (Map.Entry<String, Long> stringLongEntry : eventTypeMap.entrySet()) {
                    DeviationTop deviationTop = new DeviationTop();
                    deviationTop.setCount(stringLongEntry.getValue());
                    deviationTop.setEventType(stringLongEntry.getKey());
                    qualityList.add(deviationTop);
                }
            }
        }
        if(!CollectionUtils.isEmpty(safetyList)){
            safetyList.sort(Comparator.comparing(DeviationTop::getCount).reversed());
            deviationTopVO.setSafetyList(safetyList.stream()
                    .limit(Math.min(topSize, safetyList.size()))
                    .collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(qualityList)){
            qualityList.sort(Comparator.comparing(DeviationTop::getCount).reversed());
            deviationTopVO.setQualityList(qualityList.stream()
                    .limit(Math.min(topSize, qualityList.size()))
                    .collect(Collectors.toList()));
        }
        return deviationTopVO;
    }


    @Override
    public SafetyQualityEnvStatisticDTO evenStatistic(SimpleSearchDTO searchDTO) throws Exception {
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
        String majorRepairTurn = searchDTO.getMajorRepairTurn();
        if (StrUtil.isEmpty(majorRepairTurn)) {
            return new SafetyQualityEnvStatisticDTO();
        }
        condition.eq(SafetyQualityEnv::getMajorRepairTurn, searchDTO.getMajorRepairTurn());
        if (StringUtils.hasText(searchDTO.getKeyword())) {
            condition.like(SafetyQualityEnv::getEventTopic, searchDTO.getKeyword());
        }
        List<SafetyQualityEnv> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new SafetyQualityEnvStatisticDTO();
        }
        List<SafetyQualityEnvVO> safetyQualityEnvVOS = BeanCopyUtils.convertListTo(list, SafetyQualityEnvVO::new);
        HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
        List<SafetyQualityEnvVO> safetyQualityEnvStatisticVOS = new ArrayList<>();

        int index_event_count =0, a_violation_count=0, b_violation_count=0, c_violation_count=0,
                f1_defect_count=0, f2_defect_count=0,index_event_fo_count = 0,index_event_is_count = 0,
                index_event_rp_count = 0,index_event_other_count = 0;
        for (SafetyQualityEnvVO item : safetyQualityEnvVOS) {
            if(StringUtils.hasText(item.getAssessmentLevel())){
                if(item.getAssessmentLevel().startsWith("pms_index_event")) {
                    index_event_count++;
                    stringIntegerHashMap.put(pms_index_event.getKey(), index_event_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }

                if(item.getAssessmentLevel().equals("pms_class_a_violation")) {
                    a_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_a_violation.getKey(), a_violation_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }

                if(item.getAssessmentLevel().equals("pms_class_b_violation")) {
                    b_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_b_violation.getKey(), b_violation_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }

                if(item.getAssessmentLevel().equals("pms_class_c_violation")) {
                    c_violation_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_c_violation.getKey(), c_violation_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }

                if(item.getAssessmentLevel().equals("pms_f1_defect")) {
                    f1_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f1_defect.getKey(), f1_defect_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }

                if(item.getAssessmentLevel().equals("pms_f2_defect")) {
                    f2_defect_count++;
                    stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), f2_defect_count);
                    safetyQualityEnvStatisticVOS.add(item);
                }
            }
        }
        if(!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f2_defect.getKey())){
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), 0);
        }

        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f1_defect.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f1_defect.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_c_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_c_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_b_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_b_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_class_a_violation.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_class_a_violation.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event.getKey())) {
            stringIntegerHashMap.put(pms_index_event.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(AssessmentLevelEnum.pms_f2_defect.getKey())) {
            stringIntegerHashMap.put(AssessmentLevelEnum.pms_f2_defect.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event_fo.getKey())) {
            stringIntegerHashMap.put(pms_index_event_fo.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event_is.getKey())) {
            stringIntegerHashMap.put(pms_index_event_is.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event_other.getKey())) {
            stringIntegerHashMap.put(pms_index_event_other.getKey(), 0);
        }
        if (!stringIntegerHashMap.containsKey(pms_index_event_rp.getKey())) {
            stringIntegerHashMap.put(pms_index_event_rp.getKey(), 0);
        }

        SafetyQualityEnvStatisticDTO safetyQualityEnvStatisticDTO = new SafetyQualityEnvStatisticDTO();
        safetyQualityEnvStatisticDTO.setSafetyQualityEnvVOList(safetyQualityEnvStatisticVOS);
        safetyQualityEnvStatisticDTO.setEvenStatisticMap(stringIntegerHashMap);
        setEveryName(safetyQualityEnvStatisticVOS);
        return safetyQualityEnvStatisticDTO;
    }

    @Override
    public SafetyQualityEnvCountVO pyramidCountList(SafetyPyramidParamDTO searchDTO) {
        List<SafetyQualityEnv> list = getSafetyQualityEnvList(searchDTO);
        if (list == null) {
            return null;
        }
        SafetyQualityEnvCountVO safetyQualityEnvCountVO = new SafetyQualityEnvCountVO();
        // 1.统计金字塔质量/安全类别
        setQualitySafety(list, safetyQualityEnvCountVO);
        // 2.1 获取考核指标对应分值
        Map<String, Integer> processTypeMap = AssessmentLevelEnum.processType();
        Map<String, Integer> resultTypeMap = AssessmentLevelEnum.resultType();
        list = list.stream().filter(env -> env != null && Boolean.TRUE.equals(env.getIsAssessed())).collect(Collectors.toList());
        // 2.2 基地外部
        setBaseInfo(list, safetyQualityEnvCountVO,processTypeMap, resultTypeMap);
        // 2.3 中心统计
        setCenterInfo(list, safetyQualityEnvCountVO, processTypeMap, resultTypeMap);
        return safetyQualityEnvCountVO;
    }

    private void setBaseInfo(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, Integer> processTypeMap, Map<String, Integer> resultTypeMap) {
        // 1.1 设置基地外部表头
        TableScoreVO tableScoreVO = new TableScoreVO();
        List<TableScoreVO.HeaderTitle> headerTitleList = new ArrayList<>();
        Map<String, String> basePlaceMap = basePlaceService.allMapSimpleList();
        for (Map.Entry<String, String> stringStringEntry : basePlaceMap.entrySet()) {
            TableScoreVO.HeaderTitle headerTitle = new TableScoreVO.HeaderTitle();
            headerTitle.setName(stringStringEntry.getValue());
            headerTitle.setKey(stringStringEntry.getKey());
            headerTitleList.add(headerTitle);
        }
        Map<String,List<TableScoreVO.RowValue>> rowValues = new HashMap<>();
        tableScoreVO.setHeaderTitleList(headerTitleList);
        // 1.2 统计表格
        setBaseSupervisedBias(safetyQualityEnvCountVO, basePlaceMap, rowValues, tableScoreVO,list);
        // 1.3 计算柱状图
        setBaseScoreVOS(list, safetyQualityEnvCountVO, processTypeMap, resultTypeMap, basePlaceMap);
    }

    private void setBaseScoreVOS(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, Integer> processTypeMap, Map<String, Integer> resultTypeMap, Map<String, String> basePlaceMap) {
        List<SafetyQualityEnv> locationCodeList = list.stream().filter(item->StringUtils.hasText(item.getBaseCode())
                && StringUtils.hasText(item.getAssessmentLevel())).collect(Collectors.toList());
        Map<String, Map<String, Long>> locationMap = new HashMap<>();
        List<BaseScoreVO> locationScoreVOS = new ArrayList<>();
        //指标事件统一为pms_index_event
        if(!CollectionUtils.isEmpty(locationCodeList)){
            locationMap = locationCodeList.stream()
                    .collect(Collectors.groupingBy(SafetyQualityEnv::getBaseCode
                            , Collectors.groupingBy(item->{
                                if(item.getAssessmentLevel().startsWith("pms_index_event")){
                                    return "pms_index_event";
                                }
                                return  item.getAssessmentLevel();
                            }, Collectors.counting())));

        }
        for (Map.Entry<String, String> stringStringEntry : basePlaceMap.entrySet()) {
            BaseScoreVO baseScoreVO = new BaseScoreVO();
            String key = stringStringEntry.getKey();
            String desc= stringStringEntry.getValue();
            Map<String, Long> value = locationMap.getOrDefault(key,new HashMap<>());
            baseScoreVO.setName(desc);
            //计算各类型的分数
            calculateBaseScoreVO(processTypeMap, resultTypeMap, baseScoreVO, value);
            locationScoreVOS.add(baseScoreVO);

        }

        safetyQualityEnvCountVO.setBaseScoreVOS(locationScoreVOS);
    }

    private void setBaseSupervisedBias(SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, String> basePlaceMap, Map<String, List<TableScoreVO.RowValue>> rowValues, TableScoreVO tableScoreVO, List<SafetyQualityEnv> list) {
        List<SafetyQualityEnv> allmentList = list.stream().
                filter(item->StringUtils.hasText(item.getAssessmentLevel())
                        && StringUtils.hasText(item.getBaseCode())).collect(Collectors.toList());
        Map<String, Map<String, Long>> genderCountMap = new HashMap<>();
        //指标事件统一为pms_index_event
        if(!CollectionUtils.isEmpty(allmentList)){
            genderCountMap = allmentList.stream()
                    .collect(Collectors.groupingBy(item->{
                                if(item.getAssessmentLevel().startsWith("pms_index_event")){
                                    return "pms_index_event";
                                }
                                return  item.getAssessmentLevel();
                            },
                            Collectors.groupingBy(SafetyQualityEnv::getBaseCode, Collectors.counting())));
        }
        Map<String, String> ketToDescMap = AssessmentLevelEnum.oldTOKeyDesc();
        for (Map.Entry<String, String> ketToDesc : ketToDescMap.entrySet()) {
            String key = ketToDesc.getKey();
            //genderCountMap为不同assessmentLevel的总数
            Map<String, Long> stringLongMap = genderCountMap.get(key);
            List<TableScoreVO.RowValue> row = new ArrayList<>();
            for (Map.Entry<String, String> stringStringEntry : basePlaceMap.entrySet()) {
                TableScoreVO.RowValue rowValue = new TableScoreVO.RowValue();
                String baseCode = stringStringEntry.getKey();
                Long aLong = 0L;
                if(!MapUtil.isEmpty(stringLongMap)){
                    aLong = stringLongMap.get(baseCode);
                }
                if(null == aLong){
                    aLong =0L;
                }
                rowValue.setCount(aLong);
                rowValue.setKey(stringStringEntry.getKey());
                row.add(rowValue);
            }
            rowValues.put(ketToDesc.getValue(),row);
        }

        tableScoreVO.setRowValues(rowValues);
        safetyQualityEnvCountVO.setBaseSupervisedBias(tableScoreVO);
    }

    private void setCenterInfo(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, Integer> processTypeMap, Map<String, Integer> resultTypeMap) {
        List<SimpleDeptVO> deptByIds = deptRedisHelper.getAllSimpleDept();
        Map<String, String> ketToDescMap = AssessmentLevelEnum.oldTOKeyDesc();
        Map<String,String> deptCodeToabb = new HashMap<>();
        List<String> rowDeptCode= new ArrayList<>();
        // 1.1 设置中心表头
        List<String> deptCodeList = new ArrayList<>(Arrays.asList("ISC", "CITEC", "MEC", "SEC", "SOC", "QEC", "ERC", "PDC", "MIC", "SIC", "CSC", "DTC"));
        List<SimpleDeptVO> deptVOList =  deptByIds.stream().filter(item-> deptCodeList.contains(item.getAbbreviation()))
                .distinct().collect(Collectors.toList());
        Map<String, SimpleDeptVO> abbToEntityMap = deptVOList.stream().collect(Collectors.toMap(SimpleDeptVO::getAbbreviation, Function.identity(), (k1, k2) -> k1));
        List<TableScoreVO.HeaderTitle> headerTitleLista = new ArrayList<>();
        for (String s : deptCodeList) {
            TableScoreVO.HeaderTitle headerTitle = new TableScoreVO.HeaderTitle();
            SimpleDeptVO simpleDeptVO = abbToEntityMap.get(s);
            if(null == simpleDeptVO){
                continue;
            }
            headerTitle.setName(s);
            headerTitle.setKey(simpleDeptVO.getAbbreviation());
            headerTitleLista.add(headerTitle);
            rowDeptCode.add(simpleDeptVO.getAbbreviation());
            deptCodeToabb.put(simpleDeptVO.getAbbreviation(),s);
        }
        // 1.2 统计表格
        setCenterSupervisedBias(list, safetyQualityEnvCountVO, ketToDescMap, deptCodeToabb, headerTitleLista);
        // 1.3 统计柱状图
        setCenterScoreVOS(list, safetyQualityEnvCountVO, processTypeMap, resultTypeMap, deptCodeToabb, rowDeptCode);
    }

    private void setCenterScoreVOS(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, Integer> processTypeMap, Map<String, Integer> resultTypeMap, Map<String, String> deptCodeToabb, List<String> rowDeptCode) {
        List<SafetyQualityEnv> safetyQualityEnvs =  list.stream().filter(item-> StringUtils.hasText(item.getAssessmentLevel())&&
                StringUtils.hasText(item.getRspDeptCode())).collect(Collectors.toList());
        List<BaseScoreVO> rspCenterScoreVOS = new ArrayList<>();
        Map<String, Map<String, Long>> rspDeptCodeMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(safetyQualityEnvs)){
            rspDeptCodeMap = list.stream().filter(item-> StringUtils.hasText(item.getAssessmentLevel())&&
                            StringUtils.hasText(item.getRspDeptCode()))
                    .collect(Collectors.groupingBy(SafetyQualityEnv::getRspDeptCode
                            , Collectors.groupingBy(SafetyQualityEnv::getAssessmentLevel, Collectors.counting())));
        }
        for (String s : rowDeptCode) {
            BaseScoreVO baseScoreVO = new BaseScoreVO();
            Map<String, Long> value = rspDeptCodeMap.getOrDefault(s,new HashMap<>());
            baseScoreVO.setName(deptCodeToabb.get(s));
            //计算各类型的分数
            calculateBaseScoreVO(processTypeMap, resultTypeMap, baseScoreVO, value);
            rspCenterScoreVOS.add(baseScoreVO);
        }

        safetyQualityEnvCountVO.setCenterScoreVOS(rspCenterScoreVOS);
    }

    private void setCenterSupervisedBias(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO, Map<String, String> ketToDescMap, Map<String, String> deptCodeToabb, List<TableScoreVO.HeaderTitle> headerTitleLista) {
        Map<String,List<TableScoreVO.RowValue>> centerRows = new HashMap<>();
        TableScoreVO centerRowScoreVO = new TableScoreVO();
        centerRowScoreVO.setHeaderTitleList(headerTitleLista);

        List<SafetyQualityEnv> safetyQualityEnv= list.stream().filter(item-> StringUtils.hasText(item.getAssessmentLevel())&&
                StringUtils.hasText(item.getRspDeptCode())).collect(Collectors.toList());
        Map<String, Map<String, Long>> centerCountMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(safetyQualityEnv)){
            centerCountMap = list.stream().filter(item-> StringUtils.hasText(item.getAssessmentLevel())&&
                    StringUtils.hasText(item.getRspDeptCode()))
                    .collect(Collectors.groupingBy(item->{
                                if(item.getAssessmentLevel().startsWith("pms_index_event")){
                                    return "pms_index_event";
                                }
                                return  item.getAssessmentLevel();
                            },
                            Collectors.groupingBy(SafetyQualityEnv::getRspDeptCode, Collectors.counting())));
        }

        for (Map.Entry<String, String>  keyValue : ketToDescMap.entrySet()) {
            // 违章key
            String key =keyValue.getKey();
            // 违章描述
            String value = keyValue.getValue();

            Map<String, Long> deptCodeToCountMap = centerCountMap.getOrDefault(key,new HashMap<>());
            List<TableScoreVO.RowValue> row = new ArrayList<>();
            for (Map.Entry<String, String> deptEntry : deptCodeToabb.entrySet()) {
                String deptCode = deptEntry.getKey();
                String abbCode = deptEntry.getValue();
                Long count = deptCodeToCountMap.getOrDefault(deptCode,0L);
                TableScoreVO.RowValue rowValue = new TableScoreVO.RowValue();
                rowValue.setCount(count);
                rowValue.setKey(abbCode);
                row.add(rowValue);
            }
            centerRows.put(value,row);
        }

        centerRowScoreVO.setRowValues(centerRows);
        safetyQualityEnvCountVO.setCenterSupervisedBias(centerRowScoreVO);
    }

    private void calculateBaseScoreVO(Map<String, Integer> processTypeMap, Map<String, Integer> resultTypeMap, BaseScoreVO baseScoreVO, Map<String, Long> value) {
        BigDecimal processTotal = BigDecimal.valueOf(100);
        BigDecimal resultTotal = BigDecimal.valueOf(100);
        BigDecimal processScore = BigDecimal.ZERO;
        BigDecimal resultScore = BigDecimal.ZERO;
        for (Map.Entry<String, Long> stringLongEntry : value.entrySet()) {
            String key1 = stringLongEntry.getKey();
            Long value1 = stringLongEntry.getValue();
            Integer result = resultTypeMap.get(key1);
            if (null == result) {
                Integer process = processTypeMap.get(key1) == null ? 0 : processTypeMap.get(key1);
                processScore = processScore.add(BigDecimal.valueOf(process*value1));
            } else {
                resultScore =resultScore.add(BigDecimal.valueOf(result*value1));
            }
        }
        //扣完分后，乘以倍数
        if(processScore.compareTo(BigDecimal.ZERO) >= 0){
            processTotal = processTotal.subtract(processScore).multiply(BigDecimal.valueOf(0.3));
        }
        if(resultScore.compareTo(BigDecimal.ZERO) >= 0){
            resultTotal = resultTotal.subtract(resultScore).multiply(BigDecimal.valueOf(0.7));
        }
        //最低分为0
        if(processTotal.compareTo(BigDecimal.ZERO) <= 0){
            processTotal = BigDecimal.ZERO;
        }
        if(resultTotal.compareTo(BigDecimal.ZERO) <= 0){
            resultTotal = BigDecimal.ZERO;
        }
        baseScoreVO.setValue(processTotal.add(resultTotal));
    }

    private void setQualitySafety(List<SafetyQualityEnv> list, SafetyQualityEnvCountVO safetyQualityEnvCountVO) {
        List<DictValueVO> pyramidCategory = dictRedisHelper.getDictListByCode(PMS_PYRAMID_CATEGORY);
        Set<String> allSet = new HashSet<>();
        allSet.add("安全");
        allSet.add("质量");

        List<SafetyQualityEnv> safetyQualityEnvList = list.stream().filter(item->StringUtils.hasText(item.getHiddenEvent()) && StringUtils.hasText(item.getPyramidCategory())).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(safetyQualityEnvList)){
            Map<String, Map<String, Long>> pyramidCateGory = safetyQualityEnvList.stream()
                    .collect(Collectors.groupingBy(SafetyQualityEnv::getHiddenEvent
                            , Collectors.groupingBy(SafetyQualityEnv::getPyramidCategory, Collectors.counting())));

            for (String s : allSet) {
                SafetyPyramidCount safetyCount = new SafetyPyramidCount();
                Map<String, Long>  pyramidCateGoryMap  = pyramidCateGory.getOrDefault(s,new HashMap<>());
                List<SafetyPyramidCount.SafetyPyramid> safetyPyramids = new ArrayList<>();
                for (DictValueVO dictValueVO : pyramidCategory) {
                    SafetyPyramidCount.SafetyPyramid safetyPyramid = new SafetyPyramidCount.SafetyPyramid();
                    Long aLong = pyramidCateGoryMap.get(dictValueVO.getNumber());
                    safetyPyramid.setCount(null == aLong ? 0L : aLong);
                    safetyPyramid.setAssessmentLevel(dictValueVO.getNumber());
                    safetyPyramids.add(safetyPyramid);
                }
                safetyCount.setCountList(safetyPyramids);
                safetyCount.setKey(s);
                if(Objects.equals("安全",s)){
                    safetyQualityEnvCountVO.setSafety(safetyCount);
                }
                if(Objects.equals("质量",s)){
                    safetyQualityEnvCountVO.setQuality(safetyCount);
                }
            }

        }
    }

    @Nullable
    private List<SafetyQualityEnv> getSafetyQualityEnvList(SafetyPyramidParamDTO searchDTO) {
        List<String> repairRoundList = searchDTO.getRepairRoundList();
        List<Date> dateScope = searchDTO.getDateScope();
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);

        Date date = new Date();
        DateTime beginDate = DateUtil.beginOfYear(date);
        DateTime endDate = DateUtil.endOfYear(date);
        if (!CollectionUtils.isEmpty(dateScope)) {
            condition.between(SafetyQualityEnv::getOccurrenceDate, dateScope.get(0), dateScope.get(1));
        }else{
            condition.between(SafetyQualityEnv::getOccurrenceDate, beginDate, endDate);
        }
        if (!CollectionUtils.isEmpty(repairRoundList)) {
            condition.in(SafetyQualityEnv::getMajorRepairTurn, repairRoundList);
        }
        return this.list(condition);
    }

    @Override
    public List<String> repairRoundList() {
        LambdaQueryWrapperX<SafetyQualityEnv> condition = new LambdaQueryWrapperX<>(SafetyQualityEnv.class);
//        Date date = new Date();
//        DateTime beginDate = DateUtil.beginOfYear(date);
//        DateTime endDate = DateUtil.endOfYear(date);
//        condition.between(SafetyQualityEnv::getOccurrenceDate, beginDate, endDate);
        condition.select(SafetyQualityEnv::getMajorRepairTurn);
        List<SafetyQualityEnv> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        return list.stream().map(SafetyQualityEnv::getMajorRepairTurn).distinct().collect(Collectors.toList());
    }


}

