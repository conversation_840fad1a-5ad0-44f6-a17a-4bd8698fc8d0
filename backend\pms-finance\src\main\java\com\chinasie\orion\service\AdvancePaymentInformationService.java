package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.AdvancePaymentInformationDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInformation;
import com.chinasie.orion.domain.vo.AdvancePaymentInformationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * AdvancePaymentInformation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 17:25:21
 */
public interface AdvancePaymentInformationService extends  OrionBaseService<AdvancePaymentInformation>  {


        /**
         *  详情
         *
         * * @param id
         */
    AdvancePaymentInformationVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param advancePaymentInformationDTO
         */
        String create(AdvancePaymentInformationDTO advancePaymentInformationDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param advancePaymentInformationDTO
         */
        Boolean edit(AdvancePaymentInformationDTO advancePaymentInformationDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<AdvancePaymentInformationVO> pages( Page<AdvancePaymentInformationDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<AdvancePaymentInformationVO> vos)throws Exception;
}
