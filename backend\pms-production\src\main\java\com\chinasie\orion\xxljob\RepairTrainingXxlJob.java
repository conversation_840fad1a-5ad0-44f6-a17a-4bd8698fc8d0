package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.ContactManageTypeEnum;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.constant.TrainRoleEnum;
import com.chinasie.orion.domain.entity.TrainCenter;
import com.chinasie.orion.domain.entity.TrainContact;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.TrainCenterService;
import com.chinasie.orion.service.TrainContactService;
import com.chinasie.orion.service.TrainManageService;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class RepairTrainingXxlJob {


    @Autowired
    private TrainManageService trainManageService;

    @Autowired
    private TrainCenterService trainCenterService;

    @Autowired
    private TrainContactService trainContactService;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private FileApiService fileApiService;

    @XxlJob(value = "repairTrainingNoticeJob")
    public void repairTrainingNoticeJob() throws Exception {
        LambdaQueryWrapperX<TrainManage> wrapper = new LambdaQueryWrapperX<>(TrainManage.class);

        Date begin = new Date();
        Date end = DateUtil.addDays(begin, 3);
        wrapper.between(TrainManage::getCompleteDate, begin, end);
        wrapper.eq(TrainManage::getIsCheck, false);
        List<TrainManage> trainManages = trainManageService.list(wrapper);
        //获取培训中心
        List<String> trainIds = trainManages.stream().map(TrainManage::getId).collect(Collectors.toList());
        if (trainIds.isEmpty()){
            throw new Exception("培训中心不存在");
        }
        LambdaQueryWrapperX<TrainCenter> centerWrapper = new LambdaQueryWrapperX<>(TrainCenter.class);
        centerWrapper.in(TrainCenter::getTrainId, trainIds);
        List<TrainCenter> centers = trainCenterService.list(centerWrapper);
        //培训和讲师名称映射
        Map<String, String> trainIdToLecturer = centers.stream().filter(trainCenter -> trainCenter.getContactPersonIds() != null)
                .collect(Collectors.toMap(TrainCenter::getTrainId, TrainCenter::getContactPersonIds));
        List<String> ids = centers.stream().map(TrainCenter::getId).distinct().collect(Collectors.toList());
        if (ids.isEmpty()){
            throw new Exception("培训中心不存在");
        }
        List<FileVO> fileVOS = fileApiService.listMaxFileByDataIds(trainIds);
        Map<String, List<FileVO>> dataIdToFiles = fileVOS.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        trainManages.forEach(item -> {
            //判断是否上传了培训证明
            if (dataIdToFiles.getOrDefault(trainIdToLecturer.getOrDefault(item.getId(), ""), new ArrayList<>()).isEmpty()){
                String userIds = trainIdToLecturer.getOrDefault(item.getId(), "");
                List<String> userIdList =  Arrays.stream(userIds.split(",")).collect(Collectors.toList());
                mscBuildHandlerManager.send(item, MessageNodeDict.NODE_REPAIR_TRAIN, userIdList);
            }
        });
    }

    @XxlJob(value = "repairTrainingNoticeJobCheaked")
    public void repairTrainingNoticeJobCheaked() throws Exception {
        LambdaQueryWrapperX<TrainManage> wrapper = new LambdaQueryWrapperX<>(TrainManage.class);
        Date begin = new Date();
        Date end = DateUtil.addDays(begin, 3);
        wrapper.between(TrainManage::getCompleteDate, begin, end);
        wrapper.eq(TrainManage::getIsCheck, true);
        List<TrainManage> trainManages = trainManageService.list(wrapper);
        List<String> trainIds = trainManages.stream().map(TrainManage::getId).collect(Collectors.toList());
        if (trainIds.isEmpty()){
            throw new Exception("培训中心不存在");
        }
        List<String> baseCodes = trainManages.stream().map(TrainManage::getBaseCode).collect(Collectors.toList());
        List<TrainContact> trainContacts = trainContactService.list(new LambdaQueryWrapperX<>(TrainContact.class)
                .in(TrainContact::getBaseCode, baseCodes)
                .eq(TrainContact::getContactType, ContactManageTypeEnum.TRAIN_ENGINEER));
        List<FileVO> fileVOS = fileApiService.listMaxFileByDataIds(trainIds);
        Map<String, List<FileVO>> newDataIdToFiles = fileVOS.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        Map<String, String> codeToIds = trainContacts.stream().collect(Collectors
                .toMap(TrainContact::getBaseCode, TrainContact::getContactPersonIds, (v1, v2) -> (v1+","+v2)));
        trainManages.forEach(item -> {
            //判断是否上传了培训证明
            if (newDataIdToFiles.getOrDefault(item.getId(), new ArrayList<>()).isEmpty()){
                String userIds = codeToIds.get(item.getBaseCode());
                List<String> userIdList =  Arrays.stream(userIds.split(",")).collect(Collectors.toList());
                mscBuildHandlerManager.send(item, MessageNodeDict.NODE_REPAIR_TRAIN, userIdList);
            }
        });
    }

}
