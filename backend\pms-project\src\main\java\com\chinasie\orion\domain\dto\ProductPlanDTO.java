package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotBlank;

/**
 * ProductPlan DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
@ApiModel(value = "ProductPlanDTO对象", description = "产品策划")
@Data
@ExcelIgnoreUnannotated
public class ProductPlanDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目立项ID
     */
    @ApiModelProperty(value = "项目立项ID")
    @ExcelProperty(value = "项目立项ID ", index = 0)
    @NotBlank(message = "项目立项ID不可为空")
    private String projectApprovalId;

    /**
     * 技术指标描述
     */
    @ApiModelProperty(value = "技术指标描述")
    @ExcelProperty(value = "技术指标描述 ", index = 1)
    @NotBlank(message = "技术指标描述不可为空")
    private String description;

    /**
     * 技术指标名称
     */
    @ApiModelProperty(value = "技术指标名称")
    @ExcelProperty(value = "技术指标名称 ", index = 2)
    @NotBlank(message = "技术指标名称不可为空")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 3)
    private String number;

}
