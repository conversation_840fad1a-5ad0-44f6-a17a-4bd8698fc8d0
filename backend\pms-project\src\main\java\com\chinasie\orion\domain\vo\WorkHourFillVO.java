package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * WorkHourFill Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillVO对象", description = "工时填报")
@Data
public class WorkHourFillVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    private String workHourType;

    /**
     * 填报角色
     */
    @ApiModelProperty(value = "填报角色")
    private String fillRole;

    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    private String assigneeName;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;

//    /**
//     * 工时填报明细
//     */
//    @ApiModelProperty(value = "工时填报明细")
//    List<WorkHourFillDayInfoVO> dayDetailList;
}
