<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import { useChart } from './useChart';
import Api from '/@/api';

const projectId:string = inject('projectId');
const loading:Ref<boolean> = ref(false);
const questionInfo:Ref<Record<string, any>> = ref({});
const dataOptions:ComputedRef<any[]> = computed(() => [
  {
    name: '问题总数',
    value: questionInfo.value.total || 0,
  },
  {
    color: '#60C057',
    name: '已解决问题',
    value: questionInfo.value.solvedCount || 0,
  },
  {
    color: '#52C9F5',
    name: '未解决问题',
    value: questionInfo.value.unSolvedCount || 0,
  },
]);
const legendOptions = computed(() => dataOptions.value.filter((item) => item.color));
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: legendOptions.value.map((item) => item.color),
  series: [
    {
      name: '问题总数',
      type: 'pie',
      radius: ['70%', '90%'],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      data: legendOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
  chartInstance.value.hideLoading();
});

onMounted(() => {
  getQuestionInfo();
});

// 获取问题统计数据
async function getQuestionInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectOverviewNew/problemCount').fetch({
      projectId,
    }, '', 'GET');
    questionInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="container-question">
    <div>
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
        class="custom-legend-title"
      >
        <span>{{ item.name }}：</span>
        <span class="value">{{ item.value || 0 }}个</span>
      </div>
    </div>
    <spin-view
      v-if="loading"
      class="chart-question"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="chart-question"
    />
    <div>
      <div
        v-for="(item,index) in legendOptions"
        :key="index"
        class="custom-legend-item"
      >
        <span :style="{backgroundColor:item.color}" />
        <span>{{ item.name }}<span />
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-question {
  display: flex;
  align-items: center;
}

.custom-legend-title + .custom-legend-title {
  margin-top: 10px;
}

.chart-question {
  width: 0;
  flex-grow: 1;
  height: 150px;
  margin: 0 12px;
}
</style>
