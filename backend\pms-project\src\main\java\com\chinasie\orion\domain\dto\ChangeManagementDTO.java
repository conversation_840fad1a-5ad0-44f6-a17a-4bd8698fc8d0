package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/15:00
 * @description:
 */
@ApiModel(value = "ChangeManagementDTO对象", description = "变更管理")
public class ChangeManagementDTO extends ObjectDTO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * 预览图
     */
    @ApiModelProperty(value = "预览图")
    private String previewPath;

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReason(){
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getPreviewPath(){
        return previewPath;
    }

    public void setPreviewPath(String previewPath) {
        this.previewPath = previewPath;
    }

}
