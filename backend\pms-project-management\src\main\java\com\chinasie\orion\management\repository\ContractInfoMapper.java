package com.chinasie.orion.management.repository;

import com.chinasie.orion.management.domain.entity.ContractExtendInfo;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.entity.EmailRecord;
import com.chinasie.orion.management.domain.vo.ContractXxlJobVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * ContractInfo Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Mapper
public interface ContractInfoMapper extends OrionBaseMapper<ContractInfo> {


    /**
     * 查询所有存在完工款里程碑中的合同信息
     * @return 结果
     */
    List<ContractExtendInfo> getContractInfo();

    /**
     * 获取日期节点框架合同
     * @return 结果
     */
    List<ContractXxlJobVO> getContractXxlJobDate();

    /**
     * 获取框架剩余金额节点框架合同
     * @return 结果
     */
    List<ContractXxlJobVO> getContractXxlJobAmount();

    /**
     * 获取邮件记录信息
     * @return 结果
     */
    List<EmailRecord> getEmailRecord();

    /**
     * 批量插入
     * @param emailRecordList 记录
     * @return 结果
     */
    boolean insertBatch(@Param("emailRecordList") List<EmailRecord> emailRecordList);

    /**
     * 根据number和year查出ids
     * @param numberList
     * @param year
     * @return
     */
    List<String> getContractMainIds(@Param("numberList") List<String> numberList, @Param("year") Integer year);

    /**
     * 查询合同中需求单有全部为使用金额大于汇总金额的编号
     * @param collect 结果
     * @return 结果
     */
    List<String> getRequireInfo(@Param("collect") List<String> collect);


    Integer getContractTotal(Date start, Date end);
}

