package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 *
 * 安质环看板考核指标vo
 **/
@ApiModel(value = "AmpereRingBoardConfigKpiVO对象",description = "安质环看板维护考核指标对象")
@Data
public class AmpereRingBoardConfigKpiVO extends ObjectVO implements Serializable {
    /**
     * 事件等级code
     */
    @ApiModelProperty(value = "事件等级code")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 考核指标
     */
    @ApiModelProperty(value = "考核指标")
    private String kpiCode;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;


    /**
     * type  人 1;  部门 2
     */
    @ApiModelProperty(value = "考核类型")
    private String type;
}
