<template>
  <Layout class="ui-2-0">
    <div class="content-top">
      <a-row class="mb10">
        <a-col :span="12">
          <a-dropdown :trigger="['click']">
            <a
              class="ant-dropdown-link title"
              @click.prevent
            >
              {{ data?.line?.name }}
              <DownOutlined />
            </a>
            <template #overlay>
              <a-menu @click="handleMenu">
                <a-menu-item
                  v-for="s in data?.list"
                  :key="s.id"
                >
                  {{ s.name }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <br>
          <a-space
            :size="40"
            class="sub-title"
          >
            <span>共 {{ data?.line?.count }} 条数据</span>
            <span>最近一次修改人：{{ data?.line?.modifyName }}</span>
            <span>修改时间：{{ data?.line?.modifyTime }}</span>
          </a-space>
        </a-col>
        <a-col :span="12">
          <div class="fr">
            <a-space :size="20">
              <!--            <a-button-->
              <!--              type="primary"-->
              <!--              size="large"-->
              <!--            >-->
              <!--              基线对比-->
              <!--            </a-button>-->
              <BasicButton
                v-if=" isPower('JX_container_button_01', powerData) "
                @click="$emit('back')"
              >
                <RollbackOutlined />返回
              </BasicButton>
            </a-space>
          </div>
        </a-col>
      </a-row>
    </div>

    <STableTree
      v-if="data.isShowTable"
      v-model:selectedRowKeys="selectedRowKeys"
      :data="data.dataSTableTree"
      :loading="data.loadingSTable"
      :y="data.heightSTable"
      :showTitleBtn="false"
      :showAction="false"
    />
  </Layout>
</template>

<script lang="ts">
import {
  Col, Dropdown, Menu, Row, Space,
} from 'ant-design-vue';
import { BasicButton, isPower, Layout } from 'lyra-component-vue3';
import { DownOutlined, RollbackOutlined } from '@ant-design/icons-vue';
import STableTree
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/projectsPlan/components/STableTree.vue';
import { inject, reactive, toRefs } from 'vue';

export default {
  name: 'LineDetails',
  components: {
    BasicButton,
    STableTree,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    DownOutlined,
    ADropdown: Dropdown,
    ASpace: Space,
    RollbackOutlined,
    ARow: Row,
    ACol: Col,
    Layout,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['back', 'query'],
  setup(_, { emit }) {
    const state = reactive({
      isShowTable: true,
      dataSTableTree: [],
      loadingSTable: false,
      selectedRowKeys: [],
      powerData: [],
    });
    state.powerData = inject('powerData');
    function handleMenu({ key }) {
      state.selectedRowKeys = [];
      emit('query', key);
    }
    return {
      ...toRefs(state),
      handleMenu,
      isPower,
    };
  },
};
</script>

<style scoped lang="less">
  .ui-2-0 {
    width: 100% !important;
  }
  .title {
    font-size: 16px;
    color: #1e1e1e;
  }
  .sub-title {
    font-size: 12px;
    color: #969eb4;
  }

  .content-top {
    margin: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0 ~`getPrefixVar('content-padding-left')`;
    border-bottom: 1px solid  ~`getPrefixVar('border-color-base')`
  }
</style>
