package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.WarningSettingDTO;
import com.chinasie.orion.domain.dto.WarningSettingQueryDTO;
import com.chinasie.orion.domain.entity.WarningSetting;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.WarningSettingVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:01
 * @description:
 */
public interface WarningSettingService extends OrionBaseService<WarningSetting> {

    /**
     * 获取预警类型列表
     * @return
     */
    List<SimpleVo> getWarningTypeList();

    /**
     * 获取预警设置列表
     * @param warningSettingQueryDTO
     * @return
     * @throws Exception
     */
    List<WarningSettingVO> getWarningSettingList(WarningSettingQueryDTO warningSettingQueryDTO) throws Exception;

    /**
     * 获取预警设置详情
     * @param id
     * @return
     * @throws Exception
     */
    WarningSettingVO getWarningSettingDetail(String id) throws Exception;

    /**
     * 编辑预警设置
     * @param warningSettingDTO
     * @return
     * @throws Exception
     */
    Boolean editWarningSetting(WarningSettingDTO warningSettingDTO) throws Exception;

    Boolean takeEffectWarningSetting(TakeEffectDTO takeEffectDTO) throws Exception;

    /**
     * 预警设置提醒人
     * @param projectId
     * @return
     * @throws Exception
     */
    List<SimpleVo> getProjectRoleList(String projectId) throws Exception;

    Boolean removeByProjectIds(List<String> projectIds) throws Exception;
}
