package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.WorkHourEstimateDTO;
import com.chinasie.orion.domain.entity.WorkHourEstimate;
import com.chinasie.orion.domain.vo.WorkHourEstimateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
/**
 * <p>
 * WorkHourEstimate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
public interface WorkHourEstimateService  extends OrionBaseService<WorkHourEstimate> {
    /**
     *  详情
     *
     * * @param id
     */
    WorkHourEstimateVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param workHourEstimateDTO
     */
    Boolean create(List<WorkHourEstimateDTO> workHourEstimateDTOs)  throws Exception;

    /**
     *  提交
     *
     * * @param workHourEstimateDTO
     */
    Boolean submit(List<WorkHourEstimateDTO> workHourEstimateDTOs)  throws Exception;

    /**
     *  编辑
     *
     * * @param workHourEstimateDTO
     */
    Boolean edit(WorkHourEstimateDTO workHourEstimateDTO) throws Exception;

    /**
     *  编辑提交
     *
     * * @param workHourEstimateDTO
     */
    Boolean editSubmit(WorkHourEstimateDTO workHourEstimateDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;

    /**
     *  提交（批量）
     *
     * * @param ids
     */
    Boolean batchSubmit(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<WorkHourEstimateVO> pages(Page<WorkHourEstimateDTO> pageRequest) throws Exception;

}
