<template>
  <div>
    <DetailsLayout
      border-bottom
      title="物资服务计划信息"
      :data-source="props.detailsData"
      :list="projectBasicInfo"
      :column="3"
    />
    <DetailsLayout
      border-bottom
      title="物资服务主数据"
      :data-source="props.detailsData"
      :list="masterData"
      :column="3"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ref, Ref, onMounted, watch,
} from 'vue';
import { useRoute } from 'vue-router';
import { BasicButton } from 'lyra-component-vue3';
import {
  Collapse, CollapsePanel, Rate, Textarea,
} from 'ant-design-vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { getEvaluateItemData } from '/@/views/pms/api/projectEvaluation';
import Api from '/@/api';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
const props = defineProps({
  detailsData: {
    type: Object,
  },
});
const route = useRoute();
const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '物资/服务计划编号',
    field: 'number',
  },
  {
    label: '需求人',
    field: 'demandPersonName',
  },
  {
    label: '需求人工号',
    field: 'demandPersonJobNumber',
  },
  {
    label: '类型',
    field: 'type',
  },
  {
    label: '需求日期',
    field: 'demandTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '创建日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '备注',
    field: 'remark',
    gridColumn: '1/5',
  },

]);
const masterData: Ref<any[]> = ref([
  {
    label: '物资/服务编码',
    field: 'goodsServiceNumber',
  },
  {
    label: '规格型号',
    field: 'normsModel',
  },
  {
    label: '计量单位',
    field: 'unit',
  },
  {
    label: '需求数量',
    field: 'demandAmount',
  },
  {
    label: '入库数量',
    field: 'totalStoreAmount',
  },
  {
    label: '采购计划编号',
    field: 'buyPlanId',
    // gridColumn: '1/5',
  },
  {
    label: '物资服务描述',
    field: 'description',
  },
]);

// const materialData = ref();
// watch(
//   () => props.detailsData,
//   (newValue, oldValue) => {
//     materialData.value = newValue;
//   },
// );
onMounted(() => {
});
</script>
<style scoped lang="less">

</style>
