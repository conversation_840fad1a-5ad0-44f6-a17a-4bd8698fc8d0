package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * RelationOrgToPerson Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@TableName(value = "pmsx_relation_org_to_person")
@ApiModel(value = "RelationOrgToPersonEntity对象", description = "大修组织人员关系表")
@Data

public class RelationOrgToPerson  implements Serializable{

    @ApiModelProperty(value = "id")
    @TableField(value = "id")
    private String id;

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    @TableField(value = "person_id")
    private String personId;

    /**
     * 大修组织id
     */
    @ApiModelProperty(value = "大修组织id")
    @TableField(value = "repair_org_id")
    private String repairOrgId;

    @TableField(
        fill = FieldFill.INSERT,
        value = "creator_id"
    )
    private String creatorId;

        @TableField(
        fill = FieldFill.INSERT,
        value = "create_time"
    )
    private Date createTime;

    @TableField(
            fill = FieldFill.INSERT,
            value = "modify_id"
    )
    private String modifyId;

    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "modify_time"
    )
    private Date modifyTime;

    @TableLogic
    private Integer logicStatus;

    @TableField(value = "plan_begin_time")
    private Date beginTime;

    @TableField(value = "plan_end_time")
    private Date endTime;

    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private String number;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    @TableField(value = "leave_reason", updateStrategy = FieldStrategy.IGNORED)
    private String leaveReason;

    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    @TableField(value = "leave_remark", updateStrategy = FieldStrategy.IGNORED)
    private String leaveRemark;
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;
    @ApiModelProperty(value = "新人：是/否 true/false")
    @TableField(value = "newcomer")
    private Boolean newcomer;
    @TableField(value = "sex")
    private String sex;
    @TableField(value = "code")
    private String code;
    @TableField(value = "name")
    private String name;
    @TableField(value = "basic_user_id")
    private String basicUserId;
    @ApiModelProperty(value = "是否基地常驻")
    @TableField(value = "is_base_permanent",updateStrategy = FieldStrategy.IGNORED)
    private Boolean isBasePermanent;
    @ApiModelProperty(value = "实际入场日期")
    @TableField(value = "act_in_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actInDate;
    @ApiModelProperty(value = "实际离场日期")
    @TableField(value = "act_out_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actOutDate;
//    @ApiModelProperty(value = "计划入场日期")
//    @TableField(value = "in_date", updateStrategy = FieldStrategy.IGNORED)
//    private Date inDate;
//    @ApiModelProperty(value = "计划离场日期")
//    @TableField(value = "out_date", updateStrategy = FieldStrategy.IGNORED)
//    private Date outDate;
    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    @TableField(value = "is_finish_out_handover", updateStrategy = FieldStrategy.IGNORED)
    private Boolean isFinishOutHandover;
    @ApiModelProperty(value = "是否再次入场")
    @TableField(value = "is_again_in", updateStrategy = FieldStrategy.IGNORED)
    private Boolean isAgainIn;
    @ApiModelProperty(value = "进场倒计时（天）")
    @TableField(value = "in_days")
    private long inDays;
    @ApiModelProperty(value = "新人接口人")
    @TableField(value = "newcomer_match_person", updateStrategy = FieldStrategy.IGNORED)
    private String newcomerMatchPerson;
    @TableField(value = "user_code")
    private String userCode;

}
