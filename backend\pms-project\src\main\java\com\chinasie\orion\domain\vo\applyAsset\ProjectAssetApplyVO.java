package com.chinasie.orion.domain.vo.applyAsset;

import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectAssetApply VO对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:14:44
 */
@ApiModel(value = "ProjectAssetApplyVO对象", description = "资产转固申请主表")
@Data
public class ProjectAssetApplyVO extends  ObjectVO   implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    private String number;


    /**
     * 申请名称
     */
    @ApiModelProperty(value = "申请名称")
    private String name;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String resPerson;

    @ApiModelProperty(value = "申请人名称")
    private String resPersonName;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    private String resDept;

    @ApiModelProperty(value = "申请部门名称")
    private String resDeptName;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date resTime;


    /**
     * 申请说明
     */
    @ApiModelProperty(value = "申请说明")
    private String resDescribe;


    /**
     * 转固时间（生效时间）
     */
    @ApiModelProperty(value = "转固时间（生效时间）")
    private Date finishTime;

    /**
     * wbs列表
     */
    @ApiModelProperty(value = "wbs列表")
    List<ProjectAssetApplyDetailWbsVO> detailWbsVOList;

    /**
     * 资产列表
     */
    @ApiModelProperty(value = "wbs列表")
    List<ProjectAssetApplyDetailAssetsVO> detailAssetsVOList;

    @ApiModelProperty(value = "附件")
    private List<DocumentVO> attachments;
}
