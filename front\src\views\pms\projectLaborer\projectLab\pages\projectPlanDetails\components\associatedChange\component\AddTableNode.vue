<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="formType=='add'"
        :loading="loadingBtn"
        @cancel-click="cancel"
        @ok-click="confirm"
      />
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, nextTick, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  BasicDrawer, BasicForm, SelectUserModal, useDrawerInner, useForm, useModal,
} from 'lyra-component-vue3';
import { Input, message } from 'ant-design-vue';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import { useRoute } from 'vue-router';
import { getBasicConfig } from '../../../api/document';
import Api from '/@/api';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    BasicForm,
    AInput: Input,
    SelectUserModal,
    DrawerFooterButtons,
  },
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },

    isRisk: {
      type: Boolean,
      default: false,
    },
    from: {
      type: String,
      default: '',
    },

  },
  emits: ['update'],
  setup(props, { emit }) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const projectId: any = inject('projectId');
    const basicConfig = getBasicConfig('PASChangeApplyType');
    const route = useRoute();
    const state :any = reactive({
      loading: false,
      loadingBtn: false,
      checked: false,
      formType: 'add',
      treeData: [],
      fieldList: [],
      formId: '',
      departmentData: [],
      centerOptions: [],
      responsiblerId: '',
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.loading = false;
      state.formType = drawerData.type;
      clearValidate();
      resetFields();
      state.showMore = false;
      state.fieldList = [];
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增变更' });
        state.ecrDir = drawerData.data.ecrDir;
        let ecrDirList = findParent(props.treeData, drawerData.data.ecrDir);
        setFieldsValue({ ecrDir: ecrDirList });
      } else {
        state.formId = drawerData.data.id;
        setDrawerProps({ title: '编辑变更' });
        getItemData(state.formId);
      }
    });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    function getItemData(id) {
      state.loading = true;
      new Api('/pas').fetch('', `ecr/${id}`, 'GET').then((res) => {
        state.loading = false;
        basicConfig.getClassificationTypeList(res.ecrType, { status: 1 }).then((res1) => {
          state.fieldList = res1;
          appendFrom();
          state.ecrDir = res.ecrDir;
          let ecrDirList = findParent(props.treeData, state.ecrDir);
          res = Object.assign(res, { ecrDir: ecrDirList });
          if (Array.isArray(res.attrValues) && res1.length > 0) {
            res.attrValues.forEach((item) => {
              let fileItem = res1.find((item1) => item1.id === item.attrId);
              res[item.attrId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
            });
          }
          state.responsiblerId = res.responsiblerId || '';
          setFieldsValue(res);
        });
      }).catch((err) => {
        state.loading = false;
      });
    }
    let validateNumber = async (rule, value) => {
      if (value === '' || typeof value === 'undefined') {
        return Promise.resolve();
      }
      if (Number(value) < 0 || Number(value) % 1 > 0) {
        return Promise.reject('请输入正整数');
      }
      return Promise.resolve();
    };
    let validateNumber1 = async (rule, value) => {
      if (value === '' || typeof value === 'undefined') {
        return Promise.resolve();
      }
      if (Number(value) < 0) {
        return Promise.reject('请输入正数');
      }
      return Promise.resolve();
    };
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '标题',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入标题',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 11,
          },
          helpMessage: '文档创建完成后自动生成编号',
          slot: 'number',
          componentProps: {
            // disabled: true
            disabled: true,
          },
          ifShow: !props.isRisk,
        },
        {
          field: 'ecrDir',
          component: 'Cascader',
          label: '路径',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            disabled: true,
            options: computed(() => props.treeData),
          },
          ifShow: !props.isRisk,
        },

        {
          field: 'changeWay',
          component: 'Select',
          label: '变更方式',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              message: '请选择变更方式',
              trigger: 'blur',
              type: 'number',
            },
          ],
          componentProps: {
            placeholder: '请选择变更方式',
            options: [
              {
                label: '快速变更',
                value: 1,
              },
              {
                label: '工程变更',
                value: 2,
              },
            ],
          },
        },
        {
          field: 'responsiblerName',
          component: 'Input',
          label: '责任人',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            placeholder: '请输入责任人',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ responsiblerName: data[0].name });
                  state.responsiblerId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ responsiblerName: data[0].name });
                      state.responsiblerId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.principalId = '';
            },
          },
        },
        {
          field: 'ecrType',
          component: 'TreeSelect',
          label: '所属类型',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            placeholder: '请选择类型',
            treeData: computed(() => state.treeData),
            fieldNames: {
              label: 'name',
              key: 'id',
              value: 'id',
              children: 'children',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'applyTime',
          component: 'DatePicker',
          label: '申请时间',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            placeholder: '请选择申请时间',
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'description',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            showCount: true,
            style: { height: '130px' },
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      let attrValues = [];
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          attrValues.push({
            attrId: item.id,
            value: Array.isArray(formData[item.id]) ? formData[item.id].join(';') : formData[item.id],
          });
          delete formData[item.id];
        });
      }
      formData.responsiblerId = state.responsiblerId;
      formData.attrValues = attrValues;
      let ecrDirs = formData.ecrDir;
      formData.ecrDir = props.isRisk ? '' : formData.ecrDir[formData.ecrDir.length - 1];
      state.loadingBtn = true;
      if (state.formType === 'add') {
        formData.dataSourceId = route.query.itemId;
        let api = '';
        if (props.from) {
          formData.projectId = projectId.value;
          api = props.from === 'risk' ? '/pas/ecr/riskRelationEcr/createEcr' : '/pas/ecr/questionRelationEcr/createEcr';
        } else {
          api = '/pas/ecr';
        }

        new Api(api).fetch(formData, '', 'POST').then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
            setFieldsValue({ ecrDir: ecrDirs });
            visibleChange(false);
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        return new Api('/pas').fetch(formData, 'ecr', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    const visibleChange = (val) => {
      if (!val) {
        if (state.fieldList.length > 0) {
          state.fieldList.forEach((item) => {
            removeSchemaByFiled(item.id);
          });
        }
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    onMounted(() => {
      getFormData(0);
    });
    function getFormData(id) {
      basicConfig.getDocumentTree({ status: 1 }).then((res) => {
        state.treeData = res;
      });
    }
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.id);
        });
      }
      nextTick(() => {
        basicConfig.getClassificationTypeList(val, { status: 1 }).then((res) => {
          state.fieldList = res;
          appendFrom();
        });
      });
    }
    function appendFrom() {
      state.fieldList.forEach((item, index) => {
        let options = [];
        let fieldItem = {};
        let offset = 0;
        if (state.fieldList.length % 2 === 1) {
          offset = index % 2 === 1 ? 2 : 0;
        } else {
          offset = index % 2 === 1 ? 0 : 2;
        }
        if (item.type === '1') {
          fieldItem = {
            field: item.id,
            component: 'Input',
            required: item.require === 1,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
          };
        } else {
          options = item.options.split(';').map((item1) => ({
            label: item1,
            value: item1,
          }));
          let componentProps:any = {
            options,
          };
          let rules = [
            {
              type: 'string',
              required: item.require === 1,
              message: `请选择${item.name}`,
              trigger: 'change',
            },
          ];
          if (item.type === '3') {
            componentProps.mode = 'multiple';
            rules[0].type = 'array';
          }
          fieldItem = {
            field: item.id,
            component: 'Select',
            rules,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
            componentProps,
          };
        }
        appendSchemaByField(
          fieldItem,
          'applyTime',
        );
      });
    }

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      visibleChange,
      tableRef,
      selectUserRegister,
    };
  },
});

</script>
<style lang="less">
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: ~`getPrefixVar('primary-color')`;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid ~`getPrefixVar('primary-color')`;
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
