package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.JobMaterialStatisticDTO;
import com.chinasie.orion.domain.dto.job.StartEndDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.material.JobMaterialParamDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobMaterial;
import com.chinasie.orion.domain.dto.JobMaterialDTO;
import com.chinasie.orion.domain.vo.JobMaterialVO;

import com.chinasie.orion.service.JobMaterialService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobMaterial 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@RestController
@RequestMapping("/job-material")
@Api(tags = "作业相关的物资")
public class  JobMaterialController  {

    @Autowired
    private JobMaterialService jobMaterialService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业相关的物资】详情", type = "JobMaterial", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobMaterialVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobMaterialVO rsp = jobMaterialService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobMaterialDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业相关的物资】数据【{{#jobMaterialDTO.name}}】", type = "JobMaterial", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobMaterialDTO jobMaterialDTO) throws Exception {
        String rsp =  jobMaterialService.create(jobMaterialDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobMaterialDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业相关的物资】数据【{{#jobMaterialDTO.name}}】", type = "JobMaterial", subType = "编辑", bizNo = "{{#jobMaterialDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobMaterialDTO jobMaterialDTO) throws Exception {
        Boolean rsp = jobMaterialService.edit(jobMaterialDTO);
        return new ResponseDTO<>(rsp);
    }





    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业相关的物资】数据", type = "JobMaterial", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobMaterialService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业相关的物资】数据", type = "JobMaterial", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobMaterialService.remove(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量-新）")
    @RequestMapping(value = "/remove/batch/new", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业相关的物资】数据", type = "JobMaterial", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeBatchNew(@Validated @RequestBody JobMaterialParamDTO jobMaterialParamDTO) throws Exception {
        Boolean rsp = jobMaterialService.removeBatchNew(jobMaterialParamDTO);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业相关的物资】分页数据", type = "JobMaterial", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobMaterialVO>> pages(@RequestBody Page<JobMaterialDTO> pageRequest) throws Exception {
        Page<JobMaterialVO> rsp =  jobMaterialService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业相关的物资导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【作业相关的物资】导入模板", type = "JobMaterial", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobMaterialService.downloadExcelTpl(response);
    }

    @ApiOperation("作业相关的物资导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【作业相关的物资】导入", type = "JobMaterial", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = jobMaterialService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业相关的物资导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【作业相关的物资】导入", type = "JobMaterial", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobMaterialService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业相关的物资导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【作业相关的物资】导入", type = "JobMaterial", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobMaterialService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业相关的物资导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【作业相关的物资】数据", type = "JobMaterial", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        jobMaterialService.exportByExcel(searchConditions, response);
    }


    /**
     * 编辑
     *
     * @param startEndDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑计划开始计划结束时间")
    @RequestMapping(value = "/edit/date", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业相关的物资】物资【{{#startEndDTO.id}}】的计划开始计划结束时间", type = "JobMaterial", subType = "编辑时间", bizNo = "{{#startEndDTO.id}}")
    public ResponseDTO<Boolean> editDate(@Validated @RequestBody StartEndDTO startEndDTO) throws Exception {
        Boolean rsp = jobMaterialService.editDate(startEndDTO);
        return new ResponseDTO<>(rsp);
    }
}
