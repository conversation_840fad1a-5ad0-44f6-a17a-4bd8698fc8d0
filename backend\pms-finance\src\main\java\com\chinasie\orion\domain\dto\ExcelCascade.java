package com.chinasie.orion.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExcelCascade {
        /**
         * 下拉的数据
         */
        private List<String> options;

        /**
         * 级联时父级列索引
         */
        private String parentColIndexTpl;

        /**
         * 当前下拉列索引
         */
        private Integer curColIndex;

        /**
         * 下拉列表首行位置
         */
        private Integer firstRow;

        /**
         * 下拉列表末行位置
         */
        private Integer lastRow;

        /**
         * 下拉列表名称
         */
        private String title;
}
