<template>
  <div class="tool-wrap">
    <RightTool
      :btn-list="toolBtnList"
      @clickType="toolBtnHandle"
    />
    <!--添加编辑-->
    <AddEditDrawer
      :data-type="$attrs.dataType"
      :user-id="$attrs.userId"
      :project-id="$attrs.projectId"
      :proc-inst-name="$attrs.procInstName"
      :delivery-id="$attrs.deliveryId"
      :group-id="$attrs.groupId"
      @register="addEditDrawerRegister"
      @addSuccessChange="addSuccessChange"
    />
    <!--启动-->
    <AgreeDrawer
      :on-ok="agreeOnOk"
      @register="agreeDrawerRegister"
    />
    <!--转办-->
    <FlowTurn @register="flowTurnRegister" />
    <!--驳回-->
    <RejectDrawer @register="flowRejectRegister" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted,
} from 'vue';
// import { RightTool } from '/@/components/RightTool';
import {
  useDrawer, RightTool,
} from 'lyra-component-vue3';
import AddEditDrawer from './Drawer/AddEditDrawer.vue';
import AgreeDrawer from './Drawer/AgreeDrawer.vue';
import RejectDrawer from './Drawer/RejectDrawer.vue';
import { toolBtnHandleFn } from '../methods/toolBtn';
import { submitFlow } from '../methods/method';
import FlowTurn from './component/FlowTurn.vue';

export default defineComponent({
  components: {
    RightTool,
    AddEditDrawer,
    AgreeDrawer,
    RejectDrawer,
    FlowTurn,
  },
  props: {
    toolBtnList: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['toolBtnClick'],
  setup(props, context) {
    const { emit, attrs } = context;
    const state = reactive({
      turnMethods: {},
    });

    // 添加编辑
    const [addEditDrawerRegister, { openDrawer: openAddDrawer }] = useDrawer();
    // 启动
    const [agreeDrawerRegister, { openDrawer: openAgreeDrawer }] = useDrawer();
    // 驳回
    const [flowRejectRegister, { openDrawer: openRejectDrawer }] = useDrawer();

    // 转办
    function flowTurnRegister(methods) {
      state.turnMethods = methods;
    }

    function toolBtnHandle(type) {
      emit('toolBtnClick', type);
      toolBtnHandleFn(type, context);
    }

    // 添加成功
    function addSuccessChange() {
      attrs?.menuMethods?.load();
    }

    onMounted(() => {
      init();
    });

    function init() {
      const rightMethods = {
        openAddDrawer,
        openAgreeDrawer,
        openRejectDrawer,
        ...state.turnMethods,
      };
      emit('init', rightMethods);
    }

    function agreeOnOk(submitParams) {
      const { taskDefinitionKey, comment } = submitParams;
      return submitFlow(context, taskDefinitionKey, comment);
    }

    return {
      ...toRefs(state),
      addEditDrawerRegister,
      agreeDrawerRegister,
      flowRejectRegister,
      flowTurnRegister,
      toolBtnHandle,
      addSuccessChange,
      agreeOnOk,
    };
  },
});
</script>

<style scoped lang="less">
  .tool-wrap {
    height: 100%;
  }
</style>
