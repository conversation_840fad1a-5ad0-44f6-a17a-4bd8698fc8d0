package com.chinasie.orion.constant;

public enum ProjectSchemeProcessEnum {
    WORK_PROCESS("workProcess","工作流"),BUSINESS_PROCESS("businessProcess","业务流");
    private String value;

    private String Name;

    ProjectSchemeProcessEnum(String value, String Name) {
        this.value = value;
        this.Name = Name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }
}
