<template>
  <Card
    content-title="通知消息"
    class="notice-wrap"
  >
    <div>调试字体图标 <span class="orion-icon-accountbook" /></div>
    <div class="list-wrap">
      <div class="flex-te action">
        <span class="tip">[未读]</span>
        <span>您收到一条来自刘xx的下发的项目任务'反应堆压力容器内构件设</span>
      </div>
      <div class="flex-te">
        <span class="tip">[未读]</span>
        <span>您收到一条来自刘xx的下发的项目任务'反应堆压力容器内构件设</span>
      </div>
      <div class="flex-te">
        <span class="tip">[未读]</span>
        <span>您收到一条来自刘xx的下发的项目任务'反应堆压力容器内构件设</span>
      </div>
      <div class="flex-te">
        <span class="tip">[未读]</span>
        <span>您收到一条来自刘xx的下发的项目任务'反应堆压力容器内构件设</span>
      </div>
    </div>
  </Card>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Card from './Card.vue';

export default defineComponent({
  name: 'Notice',
  components: {
    Card,
  },
});
</script>

<style scoped lang="less">
  .notice-wrap {
    height: 250px;
  }

  .list-wrap {
    > div {
      padding: 10px 0 10px 15px;
      margin-left: 10px;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #ccc;
        border-radius: 10px;
        left: 0;
        top: 50%;
        margin-top: -4px;
      }

      > .tip {
        margin-right: 6px;
        display: none;
      }

      &.action {
        &:before {
          background-color: #f32e2e;
        }

        > .tip {
          display: inline-block;
          color: #f32e2e;
        }
      }
    }
  }
</style>
