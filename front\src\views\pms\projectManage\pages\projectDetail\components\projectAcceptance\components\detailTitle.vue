<template>
  <div class="details-container_acceptance ">
    <div class="details-container-title">
      {{ $props.title }}
    </div>
  </div>
</template>
<script lang='ts' setup>
const props = defineProps<{
  title: string
}>();
</script>

<style lang='less' scoped>
.details-container_acceptance {
  display: flex;
  flex-direction: column;
    padding: ~`getPrefixVar('content-margin-top')` 0 0 0;
    .details-container-title {
      position: relative;
      font-size: 18px;
      font-weight: 500;
      color: ~`getPrefixVar('sider-dark-bg-color')`;
      padding: 0 ~`getPrefixVar('button-margin')`;
      margin: 0 ~`getPrefixVar('content-margin')`;
      line-height: 28px;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: ~`getPrefixVar('primary-color')`;
    }

    &.is-form-item{
      margin: 0;
    }
  }
}

</style>
