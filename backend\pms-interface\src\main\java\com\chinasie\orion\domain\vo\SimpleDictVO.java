package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/11:04
 * @description:
 */
@Data
@NoArgsConstructor
public class SimpleDictVO implements Serializable {
    @ApiModelProperty(value = "key")
    private String key;
    @ApiModelProperty(value = "描述")
    private String desc;
    @ApiModelProperty(value = "status")
    private Integer status;

    public SimpleDictVO(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public SimpleDictVO(String desc, Integer status) {
        this.desc = desc;
        this.status = status;
    }
}
