package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * AdjustmentVoucher VO对象
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
@ApiModel(value = "AdjustmentVoucherVO对象", description = "调账凭证数据表")
@Data
public class AdjustmentVoucherVO extends ObjectVO implements Serializable {

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    private String companyCode;


    /**
     * 科目
     */
    @ApiModelProperty(value = "科目")
    private String subject;


    /**
     * 分配
     */
    @ApiModelProperty(value = "分配")
    private String allocation;


    /**
     * 过账期间
     */
    @ApiModelProperty(value = "过账期间")
    private String postingPeriod;


    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String voucherNum;


    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    private Date voucherDate;


    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    private Date postingDate;


    /**
     * 本币金额
     */
    @ApiModelProperty(value = "本币金额")
    private BigDecimal localCurrencyAmount;


    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    private String profitCenter;


    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenter;


    /**
     * 文本文档
     */
    @ApiModelProperty(value = "文本文档")
    private String conText;


    /**
     * wbs要素
     */
    @ApiModelProperty(value = "wbs要素")
    private String wbsElement;


    /**
     * Tr.prt
     */
    @ApiModelProperty(value = "Tr.prt")
    private String trprt;


    /**
     * 承诺项目
     */
    @ApiModelProperty(value = "承诺项目")
    private String committedProject;


    /**
     * 基金中心
     */
    @ApiModelProperty(value = "基金中心")
    private String fundingCenter;


    /**
     * 付款基准日期
     */
    @ApiModelProperty(value = "付款基准日期")
    private Date payReferenceDate;

    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    @ApiModelProperty(value = "合同里程碑")
    private String milestoneName;

    @ApiModelProperty(value = "合同里程碑id")
    private String milestoneId;

    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    private Integer hangingConnectStatus;
}
