package com.chinasie.orion.constant;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/4 14:09
 */
public enum  RiskTakeEffectEnum {
    EFFECT(1, "是"),
    UN_EFFECT(0, "否");
    private Integer status;
    private String name;

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    RiskTakeEffectEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static String getNameByStatus(Integer status){
        if (EFFECT.getStatus().equals(status)) {
            return EFFECT.getName();
        }
        if (UN_EFFECT.getStatus().equals(status)) {
            return UN_EFFECT.getName();
        }
        return "";
    }
}
