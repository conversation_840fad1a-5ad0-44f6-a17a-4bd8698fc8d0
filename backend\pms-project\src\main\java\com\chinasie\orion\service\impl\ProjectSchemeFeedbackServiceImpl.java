package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.ProjectSchemeFeedback;
import com.chinasie.orion.repository.ProjectSchemeFeedbackMapper;
import com.chinasie.orion.service.ProjectSchemeFeedbackService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;




/**
 * <p>
 * ProjectSchemeFeedback 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-19 18:29:03
 */
@Service
@Slf4j
public class ProjectSchemeFeedbackServiceImpl extends OrionBaseServiceImpl<ProjectSchemeFeedbackMapper, ProjectSchemeFeedback> implements ProjectSchemeFeedbackService {
}
