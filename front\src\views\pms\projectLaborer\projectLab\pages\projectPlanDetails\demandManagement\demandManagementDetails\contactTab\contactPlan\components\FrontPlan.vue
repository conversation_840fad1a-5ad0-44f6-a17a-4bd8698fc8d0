<template>
  <Layout class="ui-2-0">
    <BasicTable
      ref="tableRef"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
      :row-selection="false"
    />
  </Layout>
</template>

<script lang="ts">
import {
  Layout, BasicTable,
} from 'lyra-component-vue3';
import {
  onMounted, reactive, toRefs, ref, inject,
} from 'vue';
import Api from '/@/api';
export default {
  name: 'FrontPlan',
  components: {
    Layout,
    BasicTable,
  },
  setup(props, { emit }) {
    const formData: any = inject('formData', {});
    const state = reactive({
      tableRef: ref(),
      columns: [
        {
          title: '姓名',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '依赖关系',
          dataIndex: 'typeName',
          width: 150,
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          width: 150,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '计划进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 150,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '开始日期',
          dataIndex: 'planPredictStartTime',
          width: 150,
        },
        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
          width: 150,
        },
      ],
      dataSource: [],
      loading: false,
    });
    function getPage() {
      state.loading = true;
      new Api('/pms')
        .fetch({
          planId: formData?.value.id,
          type: 1,
        }, 'before-after-to-plan/list', 'POST')
        .then((res) => {
          state.dataSource = res;
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped>
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
    height: 500px;
  }
</style>
