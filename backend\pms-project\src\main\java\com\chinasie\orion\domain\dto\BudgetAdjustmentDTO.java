package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetAdjustment DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@ApiModel(value = "BudgetAdjustmentDTO对象", description = "预算调整表")
@Data
@ExcelIgnoreUnannotated
public class BudgetAdjustmentDTO extends ObjectDTO implements Serializable {

    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算申请总金额")
    @ExcelProperty(value = "预算申请总金额 ", index = 0)
    private BigDecimal budgetMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    @ExcelProperty(value = "2月预算 ", index = 1)
    private BigDecimal februaryMoney;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    @ExcelProperty(value = "1月预算 ", index = 2)
    private BigDecimal januaryMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    @ExcelProperty(value = "4月预算 ", index = 3)
    private BigDecimal aprilMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    @ExcelProperty(value = "6月预算 ", index = 4)
    private BigDecimal juneMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    @ExcelProperty(value = "3月预算 ", index = 5)
    private BigDecimal marchMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    @ExcelProperty(value = "7月预算 ", index = 6)
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    @ExcelProperty(value = "8月预算 ", index = 7)
    private BigDecimal augustMoney;

    @ApiModelProperty(value = "9月预算")
    private BigDecimal septemberMoney;
    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    @ExcelProperty(value = "10月预算 ", index = 8)
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    @ExcelProperty(value = "11月预算 ", index = 9)
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    @ExcelProperty(value = "12月预算 ", index = 10)
    private BigDecimal decemberMoney;

    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    @ExcelProperty(value = "第一季度预算 ", index = 11)
    private BigDecimal firstQuarterMoney;

    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    @ExcelProperty(value = "第二季度预算 ", index = 12)
    private BigDecimal secondQuarter;

    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    @ExcelProperty(value = "第三季度预算 ", index = 13)
    private BigDecimal thirdQuarter;

    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    @ExcelProperty(value = "第四季度预算 ", index = 14)
    private BigDecimal fourthQuarter;


    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    @ExcelProperty(value = "5月预算 ", index = 16)
    private BigDecimal mayMoney;

    /**
     * 调整单Id
     */
    @ApiModelProperty(value = "调整单Id")
    @ExcelProperty(value = "调整单Id ", index = 17)
    private String formId;

    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    @ExcelProperty(value = "预算Id ", index = 18)
    private String budgetId;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @ExcelProperty(value = "项目Id ", index = 19)
    private String projectId;

    @ApiModelProperty(value = "是否是调整数据改变数据")
    private String isChange;


}
