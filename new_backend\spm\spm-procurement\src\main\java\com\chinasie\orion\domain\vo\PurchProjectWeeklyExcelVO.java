package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.BooleanEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PurchProjectWeekly VO对象
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@ApiModel(value = "PurchProjectWeeklyVO对象", description = "采购项目实施周报")
@Data
@ColumnWidth(25)
@ContentRowHeight(20)
@HeadRowHeight(24)
@ExcelIgnoreUnannotated
public class PurchProjectWeeklyExcelVO implements Serializable {

    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private String id;

    @ApiModelProperty(value = "采购计划编号")
    @ExcelProperty(value = "采购计划编号", index = 0)
    private String purchasePlanCode;


    @ApiModelProperty(value = "采购立项申请名称")
    @ExcelProperty(value = "采购立项申请名称", index = 1)
    private String purchaseName;


    @ApiModelProperty(value = "采购申请单编码")
    @ExcelProperty(value = "采购申请单编码", index = 2)
    private String purchReqDocCode;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 3)
    private String projectName;

    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型", index = 4)
    private String contractType;

    @ApiModelProperty(value = "需求部门")
    @ExcelProperty(value = "需求部门", index = 5)
    private String applyDepartment;

    @ApiModelProperty(value = "技术负责人")
    @ExcelProperty(value = "技术负责人", index = 6)
    private String techRespons;

    @ApiModelProperty(value = "商务负责人")
    @ExcelProperty(value = "商务负责人", index = 7)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String bizRespons;

    @ApiModelProperty(value = "采购立项审批完成时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @ExcelProperty(value = "采购立项审批完成时间", index = 8)
    private String purchReqEndTime;

    @ApiModelProperty(value = "采购立项申请金额")
    @ExcelProperty(value = "采购立项申请金额", index = 9)
    private BigDecimal purchReqAmount;

    @ApiModelProperty(value = "时间")
    @ExcelProperty(value = "时间 ", index = 10)
    private String projectWeeklyTime;

    @ApiModelProperty(value = "截至本周已经耗时")
    @ExcelProperty(value = "截至本周已经耗时", index = 11)
    private Integer alreadyTime;

    @ApiModelProperty(value = "截至本周合同状态")
    @ExcelProperty(value = "截至本周合同状态", index = 12)
    private String contractStatus;


    @ApiModelProperty(value = "上周工作完成情况")
    @ExcelProperty(value = "上周工作完成情况", index = 13)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String lastWorkContent;

    @ApiModelProperty(value = "上周工作安排")
    @ExcelProperty(value = "上周工作安排", index = 14)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String lastWorkPlan;

    @ApiModelProperty(value = "下周工作安排")
    @ExcelProperty(value = "下周工作安排", index = 15)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String nextWorkPlan;


    @ApiModelProperty(value = "需商务部门领导关注内容")
    @ExcelProperty(value = "需商务部门领导关注内容", index = 16)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String busLeaderAttentionContent;

    @ApiModelProperty(value = "需技术部门领导关注内容")
    @ExcelProperty(value = "需技术部门领导关注内容", index = 17)
    @ContentStyle(wrapped = BooleanEnum.TRUE)
    private String techLeaderAttentionContent;


    @ExcelIgnore
    private Boolean isNextComplete;

    @ApiModelProperty(value = "预计下周是否能完成")
    @ExcelProperty(value = "预计下周是否能完成", index = 18)
    private String isNextCompleteName;

    @ExcelIgnore
    private Boolean isSign;

    @ApiModelProperty(value = "是否已完成合同签章")
    @ExcelProperty(value = "是否已完成合同签章", index = 19)
    private String isSignName;

    @ExcelIgnore
    private Boolean isLastComplete;

    @ApiModelProperty(value = "上周工作是否按计划完成")
    @ExcelProperty(value = "上周工作是否按计划完成", index = 20)
    private String isLastCompleteName;

    @ApiModelProperty(value = "数据来源.0:采购申请;1:采购实施")
    @ExcelProperty(value = "数据来源", index = 21)
    private String dataSource;




    @ApiModelProperty(value = "周")
    @ExcelIgnore
    private Integer week;

    @ApiModelProperty(value = "开始日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    @ExcelIgnore
    private Date weekBegin;


    @ApiModelProperty(value = "结束日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    @ExcelIgnore
    private Date weekEnd;


    @ApiModelProperty(value = "年")
    @ExcelIgnore
    private Integer year;

}
