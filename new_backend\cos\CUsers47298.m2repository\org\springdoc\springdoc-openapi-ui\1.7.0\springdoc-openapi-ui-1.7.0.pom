<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springdoc</groupId>
		<artifactId>springdoc-openapi</artifactId>
		<version>1.7.0</version>
	</parent>
	<artifactId>springdoc-openapi-ui</artifactId>
	<dependencies>
		<!-- springdoc-core -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-webmvc-core</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<!-- swagger-ui dependencies -->
		<dependency>
			<groupId>org.webjars</groupId>
			<artifactId>swagger-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- Actuator dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-core</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Automatic-Module-Name>org.springdoc.openapi.ui
							</Automatic-Module-Name>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>