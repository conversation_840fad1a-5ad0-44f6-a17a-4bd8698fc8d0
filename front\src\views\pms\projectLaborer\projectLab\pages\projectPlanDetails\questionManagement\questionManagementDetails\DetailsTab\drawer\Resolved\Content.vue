<template>
  <BasicForm @register="formRegister" />
  <SelectUserModal
    selectType="radio"
    @register="SelectUserModalRegister"
  />
</template>

<script lang="ts">
import {
  BasicForm, useForm, SelectUserModal, useModal,
} from 'lyra-component-vue3';
import { h, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { StatusEnum } from '../../enum';
export default {
  name: 'Content',
  components: {
    BasicForm,
    SelectUserModal,
  },
  props: {
    editStatus: {
      type: Number,
      default: 0,
    },
  },
  emits: ['init'],
  setup(props, { emit }) {
    const state = reactive({
      principalId: undefined,
    });
    const [SelectUserModalRegister, { openModal }] = useModal();

    const [formRegister, formMethods] = useForm({
      schemas: [
        ...(props.editStatus !== StatusEnum.CLOSE.status ? [
          {
            field: 'principalName',
            component: 'Input',
            label: '负责人',
            required: true,
            colProps: {
              span: 24,
            },
            componentProps: {
            // disabled: true,
              placeholder: '请点击右侧选择',
              onClick: () => {
                openModal(true, {
                  async onOk(data) {
                    formMethods.setFieldsValue({
                      principalName: data[0].name,
                    });
                    state.principalId = data[0].id;
                  },
                });
              },
              addonAfter: h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
                    display: 'block',
                  },
                  onClick: () => {
                    openModal(true, {
                      async onOk(data) {
                        formMethods.setFieldsValue({
                          principalName: data[0].name,
                        });
                        state.principalId = data[0].id;
                      },
                    });
                  },
                },
                '请选择',
              ),
              async onChange(value) {
                message.info('请选择');
                formMethods.setFieldsValue({
                  principalName: undefined,
                });
                state.principalId = undefined;
              },
            },
          },
        ] : []),

        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 500,
          },
        },
      ],
      showActionButtonGroup: false,
      labelWidth: 80,
    });

    onMounted(() => {
      emit('init', formMethods, state);
    });

    return {
      formRegister,
      SelectUserModalRegister,
    };
  },
};
</script>

<style scoped>

</style>
