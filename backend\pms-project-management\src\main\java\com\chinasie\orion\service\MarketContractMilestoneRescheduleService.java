package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.MarketContractMilestoneRescheduleAddDTO;
import com.chinasie.orion.domain.entity.MarketContractMilestoneReschedule;
import com.chinasie.orion.domain.dto.MarketContractMilestoneRescheduleDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MarketContractMilestoneReschedule 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
public interface MarketContractMilestoneRescheduleService  extends  OrionBaseService<MarketContractMilestoneReschedule>  {


    /**
     *  详情
     *
     * * @param id
     */
    MarketContractMilestoneRescheduleVO detail(String id,String pageCode)throws Exception;

    /**
     *  查询里程碑改期列表
     *
     * * @param id
     */
    List<MarketContractMilestoneRescheduleVO> listByMilestoneId(String milestoneId)throws Exception;

    /**
     *  新增
     *
     * * @param marketContractMilestoneRescheduleDTO
     */
    String create(MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param marketContractMilestoneRescheduleDTO
     */
    Boolean edit(MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractMilestoneRescheduleVO> pages( Page<MarketContractMilestoneRescheduleDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<MarketContractMilestoneRescheduleVO> vos)throws Exception;

    /**
     * 跟踪确认新增（改期新增）
     * @param dto
     */
    void addReschedule(MarketContractMilestoneRescheduleAddDTO dto) throws Exception;

    /**
     * 跟踪确认新增详情（改期新增）
     * @param dto
     * @return
     */
    MarketContractMilestoneRescheduleAddDTO addRescheduleDetail(MarketContractMilestoneRescheduleAddDTO dto)throws Exception;

    Boolean addList(List<String> list);
}

