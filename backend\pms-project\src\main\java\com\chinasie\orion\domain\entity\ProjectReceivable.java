package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectReceivable Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@TableName(value = "pmsx_project_receivable")
@ApiModel(value = "ProjectReceivable对象", description = "项目应收表")
@Data
public class ProjectReceivable extends ObjectEntity implements Serializable{

    /**
//     * 客户名称
//     */
//    @ApiModelProperty(value = "客户名称")
//    @TableField(value = "name" )
//    private String name;
//
//    /**
//     * 应收编码
//     */
//    @ApiModelProperty(value = "应收编码")
//    @TableField(value = "number" )
//    private String number;
//
//    /**
//     * 排序
//     */
//    @ApiModelProperty(value = "排序")
//    @TableField(value = "sort" )
//    private Integer sort;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称id")
    @TableField(value = "stakeholder_id" )
    private String stakeholderId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @TableField(value = "contract_number" )
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id" )
    private String contractId;

    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点")
    @TableField(value = "collection_point" )
    private String collectionPoint;

    /**
     * 应收日期
     */
    @ApiModelProperty(value = "应收日期")
    @TableField(value = "receivable_date" )
    private Date receivableDate;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @TableField(value = "amount_receivable" )
    private BigDecimal amountReceivable;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    @TableField(value = "funds_received" )
    private BigDecimal fundsReceived;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期")
    @TableField(value = "sale_sate" )
    private Date saleSate;


    /**
     * 未收金额
     */
    @ApiModelProperty(value = "未收金额")
    @TableField(value = "no_amount_received" )
    private BigDecimal noAmountReceived;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

}
