package com.chinasie.orion.service.reporting.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.bo.StatusBo;
import com.chinasie.orion.constant.reporting.ProjectReportMessageNodeEnums;
import com.chinasie.orion.constant.reporting.ReportingBusEnum;
import com.chinasie.orion.constant.reporting.TimeSearchTypeEnums;
import com.chinasie.orion.domain.dto.reporting.*;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatement;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatementContent;
import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.reporting.*;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.reporting.ProjectDailyStatementMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.service.reporting.ProjectDailyStatementContentService;
import com.chinasie.orion.service.reporting.ProjectDailyStatementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.StatusPolicyConstant.DAILY_STATUS_POLICY_ID;

/**
 * <p>
 * ProjectDailyStatement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@Slf4j
@Service
public class ProjectDailyStatementServiceImpl extends OrionBaseServiceImpl<ProjectDailyStatementMapper, ProjectDailyStatement> implements ProjectDailyStatementService {

    @Autowired
    private ProjectDailyStatementMapper projectDailyStatementMapper;

    @Autowired
    private ProjectDailyStatementContentService projectDailyStatementContentService;

    @Autowired
    private LyraFileBO fileBo;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    protected PmsMQProducer mqProducer;

    @Autowired
    private MessageCenterApi messageCenterApi;

    @Autowired
    private InternalAssociationRedisHelper internalAssociationRedisHelper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private CurrentUserHelper currentUserHelper;

    @Resource
    private StatusBo statusBo;

    @Resource
    private SearchHelper searchHelper;

    @Resource
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private CurrentUserHelper userHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectDailyStatementVO detail(String id, String pageCode) throws Exception {
        ProjectDailyStatement projectDailyStatement = projectDailyStatementMapper.selectById(id);
        ProjectDailyStatementVO projectDailyStatementVO = BeanCopyUtils.convertTo(projectDailyStatement, ProjectDailyStatementVO::new);
        Map<String, List<ProjectDailyStatementContentVO>> idToList = projectDailyStatementContentService.getMapByDataIdList(Collections.singletonList(projectDailyStatementVO.getId()));
        List<ProjectDailyStatementContentVO> orDefault = idToList.getOrDefault(projectDailyStatementVO.getId(), Collections.emptyList());
        // 添加关联对象名称
        //todo 内容 对应的 关联对象数据
        projectDailyStatementContentService.setRelationshipName(orDefault);
        projectDailyStatementVO.setProjectDailyStatementContentVOList(orDefault);

        List<String> userIdList = new ArrayList<>();
        String carbonCopyBy = projectDailyStatementVO.getCarbonCopyBy();
//        if (StringUtils.hasText(carbonCopyBy) && carbonCopyBy.contains(",")) {
        if (StringUtils.hasText(carbonCopyBy)) {
            userIdList.addAll(Arrays.asList(carbonCopyBy.split(",")));
            List<UserVO> userByIds = userRedisHelper.getUserByIds(userIdList);
            projectDailyStatementVO.setCarbonCopyByNames(userByIds.stream().map(UserVO::getName).collect(Collectors.joining(",")));
            projectDailyStatementVO.setCarbonCopyByList(userByIds.stream().map(UserVO::getId).collect(Collectors.toList()));
            projectDailyStatementVO.setCarbonCopyNameByList(userByIds.stream().map(UserVO::getName).collect(Collectors.toList()));
        }

        projectDailyStatementVO.setRespName(userRedisHelper.getUserById(projectDailyStatement.getResp()).getName());
        List<DataStatusVO> statusEntityVoList = statusBo.getStatusList(DAILY_STATUS_POLICY_ID);
        Map<Integer, DataStatusVO> statusEntityVoMap = statusEntityVoList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        projectDailyStatementVO.setBusStatusName(statusEntityVoMap.get(projectDailyStatement.getBusStatus()));
        projectDailyStatementVO.setReviewedByName(userRedisHelper.getUserById(projectDailyStatement.getReviewedBy()).getName());

        // 添加明日日报内容
        Date tomorrow = getTomorrow(projectDailyStatement.getDaily());
        LambdaQueryWrapperX<ProjectDailyStatement> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProjectDailyStatement::getDaily, tomorrow)
                .eq(ProjectDailyStatement::getCreatorId, projectDailyStatement.getCreatorId());
        List<ProjectDailyStatement> nextDailylist = list(wrapperX);
        if (!nextDailylist.isEmpty()) {
            nextDailylist.sort(Comparator.comparing(ProjectDailyStatement::getCreateTime).reversed());
            ProjectDailyStatement nextDaily = nextDailylist.get(0);
            // 获取明日计划
            List<ProjectDailyStatementContent> listByDailyId = projectDailyStatementContentService.getListByDailyId(nextDaily.getId());
            if (!CollectionUtils.isBlank(listByDailyId)) {
                List<ProjectDailyStatementContentVO> projectDailyStatementContentDTOS = BeanCopyUtils.convertListTo(listByDailyId, ProjectDailyStatementContentVO::new);
                // 添加关联对象名称
                projectDailyStatementContentService.setRelationshipName(projectDailyStatementContentDTOS);
                projectDailyStatementVO.setNexDayVOList(projectDailyStatementContentDTOS);
                String userId = currentUserHelper.getUserId();
                //按钮判断,是本人&&是未提交状态
                if (userId.equals(projectDailyStatement.getCreatorId()) && ReportingBusEnum.NOT_SUBMITTED.getCode().equals(projectDailyStatement.getBusStatus())) {
                    projectDailyStatementVO.setEdit(true);
                    projectDailyStatementVO.setCommit(true);
                }
                if (ReportingBusEnum.SUBMITTED.getCode().equals(projectDailyStatement.getBusStatus())) {
                    projectDailyStatementVO.setAudit(true);
                }
            }

        }
        List<com.chinasie.orion.file.api.domain.vo.FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        List<DocumentVO> documentVOList = new ArrayList<>();
//        if(ResponseUtils.success(fileDtoList)){
//            List<FileDto> fileDtoList = fileDto.getResult();
        fileDtoList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(o.getOwnerName());
            documentVO.setModifyId(o.getModifyId());
            documentVO.setModifyName(o.getModifyName());
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setFileSize(o.getFileSize());
            documentVO.setCreateTime(o.getCreateTime());
            UserVO createUser = userRedisHelper.getUserById(o.getCreatorId());
            documentVO.setCreatorName(createUser.getName());
            documentVOList.add(documentVO);

        });
//        }
        projectDailyStatementVO.setDocumentVOList(documentVOList);
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(projectDailyStatementVO.getProjectId(), currentUserId);
        if (!CollectionUtils.isBlank(roleCodeList)) {
            pmsAuthUtil.setDetailAuths(projectDailyStatementVO, currentUserId, pageCode, projectDailyStatementVO.getDataStatus(), ProjectDailyStatementVO::setDetailAuthList, projectDailyStatementVO.getCreatorId(), projectDailyStatementVO.getModifyId(), projectDailyStatementVO.getOwnerId(), roleCodeList);
        }
        return projectDailyStatementVO;
    }

    /**
     * 新增
     * <p>
     * * @param projectDailyStatementDTO
     */
    @Override
    public ProjectDailyStatementVO create(ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception {
        // 日报是否存在验证
        String userId = currentUserHelper.getUserId();
        Boolean exist = ifExist(userId, projectDailyStatementDTO.getProjectId(), projectDailyStatementDTO.getDaily());
        if (exist) {
            throw new PMSException(PMSErrorCode.PMS_DATA_DAILY_HAVE, "该项目的当日计划已存在");
        }
        ProjectDailyStatement projectDailyStatement = BeanCopyUtils.convertTo(projectDailyStatementDTO, ProjectDailyStatement::new);
        projectDailyStatement.setBusStatus(ReportingBusEnum.NOT_SUBMITTED.getCode());
        projectDailyStatement.setCommitTime(new Date());
        projectDailyStatement.setResp(currentUserHelper.getUserId());
        projectDailyStatementMapper.insert(projectDailyStatement);
        //  日报ID
        String id = projectDailyStatement.getId();
        ProjectDailyStatementVO rsp = BeanCopyUtils.convertTo(projectDailyStatement, ProjectDailyStatementVO::new);
        List<ProjectDailyStatementContentDTO> contentVOList = projectDailyStatementDTO.getContentVOList();
        for (ProjectDailyStatementContentDTO projectDailyStatementContentVO : contentVOList) {
            projectDailyStatementContentVO.setDailyStatementId(id);
        }

        // 下一天数据的vo集合
        List<ProjectDailyStatementContentDTO> nexDayVOList = projectDailyStatementDTO.getNexDayVOList();
        if (!CollectionUtils.isBlank(nexDayVOList)) {
            ProjectDailyStatement projectDailyStatement1 = saveNextDaily(projectDailyStatementDTO);
            nexDayVOList.forEach(p -> p.setDailyStatementId(projectDailyStatement1.getId()));
            contentVOList.addAll(nexDayVOList);
        }
        // 新增关联内容
        projectDailyStatementContentService.createBatch(contentVOList);
        //是否提交
        ReportingBusEnum reportingBusEnum = projectDailyStatementDTO.getReportingKey();
        if (!ObjectUtils.isEmpty(reportingBusEnum) && reportingBusEnum.equals(ReportingBusEnum.SUBMITTED)) {
            this.submitById(id);
        }
        //文件处理
        List<FileVO> fileDTOList = projectDailyStatementDTO.getFileDTOList();
        if (CollectionUtils.isBlank(fileDTOList)) {
            return rsp;
        }
        fileDTOList.forEach(fileDto -> fileDto.setDataId(id));
        List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(fileDTOList, FileDTO::new);
        fileBo.addBatch(fileDTOS);

        searchHelper.sendDataChangeMessage(id);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectDailyStatementDTO
     */
    @Override
    public Boolean edit(ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception {
        // 日报是否存在验证
        String userId = currentUserHelper.getUserId();
//        Boolean exist = ifExist(userId, projectDailyStatementDTO.getProjectId(), projectDailyStatementDTO.getDaily());
//        if (exist){
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL,"你的修改的日期该项目日报已存");
//        }
        ProjectDailyStatement projectDailyStatement = BeanCopyUtils.convertTo(projectDailyStatementDTO, ProjectDailyStatement::new);
        projectDailyStatement.setCreatorId(userId);
        int update = projectDailyStatementMapper.updateById(projectDailyStatement);
        //日报内容修改
        List<ProjectDailyStatementContentDTO> contentVOList = projectDailyStatementDTO.getContentVOList();
        List<ProjectDailyStatementContent> contents = projectDailyStatementContentService.getListByDailyId(projectDailyStatement.getId());
        if (!CollectionUtils.isBlank(contents)) {
            projectDailyStatementContentService.deleteByDataIdList(Arrays.asList(projectDailyStatement.getId()));
        }
        List<ProjectDailyStatementContentDTO> saveContentList = new ArrayList<>();
        if (!CollectionUtils.isBlank(contentVOList)) {
            contentVOList.forEach(item -> {
                item.setDailyStatementId(projectDailyStatement.getId());
            });
            saveContentList.addAll(contentVOList);
        }

        // 获取明日日报及内容,只有SAVE状态的明日日报可修改
        List<ProjectDailyStatement> nextDailylist = getNextSaveStatusDaily(projectDailyStatement);
        if (!nextDailylist.isEmpty()) {
            // 存在开始状态的日报就修改
            nextDailylist.sort(Comparator.comparing(ProjectDailyStatement::getCreateTime).reversed());
            ProjectDailyStatement nextDaily = nextDailylist.get(0);
            updateContent(projectDailyStatementDTO, nextDaily);
            saveContentList.addAll(projectDailyStatementDTO.getNexDayVOList());
        } else {
            if (!CollectionUtils.isBlank(projectDailyStatementDTO.getNexDayVOList())) {
                ProjectDailyStatement projectDailyStatementNext = saveNextDaily(projectDailyStatementDTO);
                List<ProjectDailyStatementContentDTO> nexDayVOList = projectDailyStatementDTO.getNexDayVOList();
                nexDayVOList.forEach(n -> {
                    n.setDailyStatementId(projectDailyStatementNext.getId());
                });
                saveContentList.addAll(nexDayVOList);
            }
        }

        projectDailyStatementContentService.createBatch(saveContentList);
        //编辑文件修改
        List<FileVO> fileDTOList = projectDailyStatementDTO.getFileDTOList();
        if (!CollectionUtils.isBlank(fileDTOList)) {
            List<FileVO> result = fileBo.getFilesByDataId(projectDailyStatement.getId());
//            List<FileVO> result = filesByDataId.getResult();
            Map<String, FileVO> fileVOMap = new HashMap<>();
            if (!CollectionUtils.isBlank(result)) {
                fileVOMap = result.stream().collect(Collectors.toMap(FileVO::getId, file -> file));
            }
            List<String> removeFile = new ArrayList<>();
            List<FileDTO> saveFile = new ArrayList<>();
            Map<String, FileVO> finalFileVOMap = fileVOMap;
            result.forEach(f -> {
                removeFile.add(f.getId());
            });
            fileDTOList.forEach(
                    f -> {
                        f.setId(null);
                        f.setDataId(projectDailyStatement.getId());

                        saveFile.add(BeanCopyUtils.convertTo(f, FileDTO::new));
                    }
            );
            if (!CollectionUtils.isBlank(saveFile)) {
                fileBo.addBatch(saveFile);
            }
            if (!CollectionUtils.isBlank(removeFile)) {
                fileBo.deleteFileByIds(removeFile);
            }

            // }
        } else {
            List<FileVO> result = fileBo.getFilesByDataId(projectDailyStatement.getId());
//            List<FileVO> result = filesByDataId.getResult();
            List<String> removeFile = new ArrayList<>();
            result.forEach(f -> {
                if (!org.junit.platform.commons.util.StringUtils.isBlank(f.getId())) {
                    removeFile.add(f.getId());
                }
            });
            if (CollectionUtil.isNotEmpty(removeFile)) {
                fileBo.deleteFileByIds(removeFile);
            }

        }

        searchHelper.sendDataChangeMessage(projectDailyStatement.getId());
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectBatchIds(ids);
        if (CollectionUtils.isBlank(projectDailyStatements)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        boolean b = projectDailyStatements.stream().anyMatch(projectDailyStatement ->
                projectDailyStatement.getBusStatus().intValue() != ReportingBusEnum.NOT_SUBMITTED.getCode().intValue() || !Objects.equals(currentUserId, projectDailyStatement.getCreatorId()));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "所选数据有已提交的数据不能删除/所选数据含有不是当前人的数据");
        }
        // todo 需要验证是否是真删除
        int delete = projectDailyStatementMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete) && SqlHelper.retBool(projectDailyStatementContentService.deleteByDataIdList(ids));
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectDailyStatementVO> pages(Page<ListDailyRequest> pageRequest) throws Exception {
        Page<ProjectDailyStatementVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        Page<ProjectDailyStatement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectDailyStatement::new));
        ListDailyRequest query = pageRequest.getQuery();
        if (StrUtil.isBlank(query.getProjectId()) && CollectionUtils.isBlank(query.getProjectIds())) {
            return pageResult;
        }


        LambdaQueryWrapperX<ProjectDailyStatement> queryWrapperX = new LambdaQueryWrapperX<>();
        String keyword = pageRequest.getQuery().getKeyword();
        if (StringUtils.hasText(keyword)) {
            LambdaQueryWrapperX<ProjectDailyStatementContent> dailyStatementContentQueryWrapperX = new LambdaQueryWrapperX<>();
            dailyStatementContentQueryWrapperX.like(ProjectDailyStatementContent::getContent, keyword);
            List<ProjectDailyStatementContent> dailyStatementContents = projectDailyStatementContentService.list(dailyStatementContentQueryWrapperX);
            if (CollectionUtils.isBlank(dailyStatementContents)) {
                return pageResult;
            }
            List<String> dailyIds = dailyStatementContents.stream().map(ProjectDailyStatementContent::getDailyStatementId).collect(Collectors.toList());
            queryWrapperX.in(ProjectDailyStatement::getId, dailyIds);

//            queryWrapperX.selectAll(ProjectDailyStatement.class);
//            queryWrapperX.leftJoin(ProjectDailyStatementContent.class, ProjectDailyStatementContent::getDailyStatementId, ProjectDailyStatement::getId);
//            queryWrapperX.like(ProjectDailyStatementContent::getContent, keyword);
        }
        // 条件设置
        setCondition(queryWrapperX, pageRequest.getQuery());
        PageResult<ProjectDailyStatement> page = projectDailyStatementMapper.selectPage(realPageRequest, queryWrapperX);
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        List<ProjectDailyStatement> content = page.getContent();
        if (CollectionUtils.isBlank(content)) {
            return pageResult;
        }
        // 只是获取了 数据ID对应的 内容
        List<String> idList = content.stream().map(ProjectDailyStatement::getId).collect(Collectors.toList());
        // 内容 对应的 关联对象数据
        Map<String, List<ProjectDailyStatementContentVO>> idToList = projectDailyStatementContentService.getMapByDataIdList(idList);
        List<ProjectDailyStatementVO> list = new ArrayList<>();
        List<DataStatusVO> statusEntityVoList = statusBo.getStatusList(DAILY_STATUS_POLICY_ID);
        Map<Integer, DataStatusVO> statusEntityVoMap = statusEntityVoList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        for (ProjectDailyStatement projectDailyStatement : content) {
            ProjectDailyStatementVO projectDailyStatementVO = BeanCopyUtils.convertTo(projectDailyStatement, ProjectDailyStatementVO::new);
            projectDailyStatementVO.setProjectDailyStatementContentVOList(idToList.getOrDefault(projectDailyStatementVO.getId(), Collections.emptyList()));

            projectDailyStatementVO.setBusStatusName(statusEntityVoMap.get(projectDailyStatement.getBusStatus()));
            // 获取的关联内容的数据
            List<ProjectDailyStatementContentVO> contentVOList = idToList.getOrDefault(projectDailyStatement.getId(), Collections.emptyList());
            BigDecimal sum = BigDecimal.ZERO;
            for (ProjectDailyStatementContentVO projectDailyStatementContentVO : contentVOList) {
                sum = sum.add(projectDailyStatementContentVO.getTaskTime());
            }
            projectDailyStatementVO.setTaskTime(sum);
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(projectDailyStatement.getResp());
            projectDailyStatementVO.setRespName(simpleUserById == null ? "" : simpleUserById.getName());
            SimpleUser auditPerson = userRedisHelper.getSimpleUserById(projectDailyStatement.getReviewedBy());
            projectDailyStatementVO.setReviewedByName(auditPerson == null ? "" : auditPerson.getName());

            String creatorId = projectDailyStatementVO.getCreatorId();
            Integer busStatus = projectDailyStatement.getBusStatus();
            String reviewedBy = projectDailyStatementVO.getReviewedBy();
            if (creatorId.equals(currentUserId) && Objects.equals(ReportingBusEnum.NOT_SUBMITTED.getCode(), busStatus)) {
                projectDailyStatementVO.setEdit(Boolean.TRUE);
                projectDailyStatementVO.setCommit(Boolean.TRUE);
                projectDailyStatementVO.setWarn(Boolean.TRUE);
            } else if (Objects.equals(currentUserId, reviewedBy) && Objects.equals(ReportingBusEnum.SUBMITTED.getCode(), busStatus)) {
                projectDailyStatementVO.setAudit(Boolean.TRUE);
            }
            list.add(projectDailyStatementVO);
        }
        pageResult.setTotalSize(page.getTotalSize());

        Map<String, List<String>> dataRoleMap = getDataRoleMap(list);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), list, ProjectDailyStatementVO::getId, ProjectDailyStatementVO::getDataStatus, ProjectDailyStatementVO::setRdAuthList,
                ProjectDailyStatementVO::getCreatorId,
                ProjectDailyStatementVO::getModifyId,
                ProjectDailyStatementVO::getOwnerId,
                dataRoleMap);
        pageResult.setContent(list);
        return pageResult;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectDailyStatementVO> list) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectDailyStatementVO v : list) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            if (!CollectionUtils.isBlank(roles)) {
                dataRoleCodeMap.put(v.getId(), roles);
            }
        }
        return dataRoleCodeMap;
    }

    @Override
    public ProjectDailyStatementVO todayInfo(String day, String projectId) throws Exception {
        DateTime parse = DateUtil.parse(day, "yyyy-MM-dd");
        LambdaQueryWrapperX<ProjectDailyStatement> projectDailyStatementLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectDailyStatementLambdaQueryWrapperX.eq(ProjectDailyStatement::getDaily, parse);
        projectDailyStatementLambdaQueryWrapperX.eq(ProjectDailyStatement::getProjectId, projectId);
        projectDailyStatementLambdaQueryWrapperX.eq(ProjectDailyStatement::getCreatorId, CurrentUserHelper.getCurrentUserId());
        projectDailyStatementLambdaQueryWrapperX.orderByDesc(ProjectDailyStatement::getCreateTime);
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectList(projectDailyStatementLambdaQueryWrapperX);
        if (CollectionUtils.isBlank(projectDailyStatements)) {
            return null;
        }
        ProjectDailyStatementVO projectDailyStatementVO = BeanCopyUtils.convertTo(projectDailyStatements.get(0), ProjectDailyStatementVO::new);
        // 内容 对应的 关联对象数据
        Map<String, List<ProjectDailyStatementContentVO>> idToList = projectDailyStatementContentService.getMapByDataIdList(Collections.singletonList(projectDailyStatementVO.getId()));
        projectDailyStatementVO.setProjectDailyStatementContentVOList(idToList.getOrDefault(projectDailyStatementVO.getId(), Collections.emptyList()));
        // 获取明日计划的内容
        LambdaQueryWrapperX<ProjectDailyStatement> nextDay = new LambdaQueryWrapperX<>();
        nextDay.eq(ProjectDailyStatement::getDaily, parse);
        nextDay.eq(ProjectDailyStatement::getProjectId, projectId);
        nextDay.eq(ProjectDailyStatement::getCreatorId, CurrentUserHelper.getCurrentUserId());
        nextDay.orderByDesc(ProjectDailyStatement::getCreateTime);
        List<ProjectDailyStatement> nextDaily = projectDailyStatementMapper.selectList(nextDay);
        if (CollectionUtils.isBlank(nextDaily)) {
            ProjectDailyStatement projectDailyStatement = nextDaily.get(0);
            String id = projectDailyStatement.getId();
            List<String> arrayList = new ArrayList<>();
            arrayList.add(id);
            List<ProjectDailyStatementContentVO> contentVOS = projectDailyStatementContentService.getListByDailyIds(arrayList);
            projectDailyStatementVO.setNexDayVOList(contentVOS);
        }
        return projectDailyStatementVO;
    }

    @Override
    public Boolean submitById(String id) {
        ProjectDailyStatement projectDailyStatement = projectDailyStatementMapper.selectById(id);
        // 创建人就是责任人
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        if (!Objects.equals(projectDailyStatement.getCreatorId(), currentUserId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，您不是该数据的创建人/责任人不能进行提交操作");
        }
        String reviewedBy = projectDailyStatement.getReviewedBy();
        String carbonCopyBy = projectDailyStatement.getCarbonCopyBy();
        //定义接收人
        List<String> recivies = new ArrayList<>();
        recivies.add(reviewedBy);
        if (StringUtils.hasText(carbonCopyBy)) {
            if (carbonCopyBy.contains(",")) {
                recivies.addAll(Arrays.asList(carbonCopyBy.split(",")));
            } else {
                recivies.add(carbonCopyBy);
            }
        }
        projectDailyStatement.setBusStatus(ReportingBusEnum.SUBMITTED.getCode());
        projectDailyStatement.setModifyTime(new Date());
        //修改状态
        int submit = projectDailyStatementMapper.updateById(projectDailyStatement);
        String url = "/pms/dayReportDetails?id=" + projectDailyStatement.getProjectId() + "&curId=" + projectDailyStatement.getId();
        // todo 待测试
        this.senMessageCommit(id, projectDailyStatement.getCreatorName()
                , DateUtil.format(projectDailyStatement.getDaily()
                        , "yyyy-MM-dd")
                , currentUserId, recivies, url);
        return SqlHelper.retBool(submit);
    }

    @Override
    public Boolean audit(AuditParamDTO auditParamDTO) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> idList = auditParamDTO.getIdList();
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectBatchIds(idList);
        boolean b = projectDailyStatements.stream().anyMatch(p -> !Objects.equals(p.getReviewedBy(), currentUserId));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，请选择您能审核的数据进行审核操作");
        }
        projectDailyStatements.forEach(projectDailyStatement -> {
                    projectDailyStatement.setBusStatus(ReportingBusEnum.APPROVED.getCode());
                    projectDailyStatement.setScore(auditParamDTO.getScore());
                    projectDailyStatement.setEvaluateDate(new Date());
                    projectDailyStatement.setModifyTime(new Date());
                    projectDailyStatement.setEvaluate(auditParamDTO.getEvaluate());
                }
        );
        projectDailyStatementMapper.updateBatch(projectDailyStatements, projectDailyStatements.size());
        for (ProjectDailyStatement projectDailyStatement : projectDailyStatements) {
            String projectId = projectDailyStatement.getProjectId();
            String id = projectDailyStatement.getId();
            String url = "/pms/dayReportDetails?id=" + projectId + "&curId=" + id;

            //  发送消息前先将原有代办自动变为已办
//            messageCenterApi.evocationlessage(projectDailyStatement.getId());
            // todo 发送消息前先将原有代办自动变为已办
            this.senMessageAudit(projectDailyStatement.getId()
                    , DateUtil.format(projectDailyStatement.getDaily(), "yyyy-MM-dd")
                    , projectDailyStatement.getScore()
                    , projectDailyStatement.getEvaluate()
                    , currentUserId, Collections.singletonList(projectDailyStatement.getCreatorId())
                    , url);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean reject(List<String> idList) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectBatchIds(idList);
        boolean b = projectDailyStatements.stream().anyMatch(p -> !Objects.equals(p.getReviewedBy(), currentUserId));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，请选择您能驳回的数据进行驳回操作");
        }
        projectDailyStatements.forEach(projectDailyStatement -> {
            projectDailyStatement.setBusStatus(ReportingBusEnum.NOT_SUBMITTED.getCode());
            projectDailyStatement.setModifyTime(new Date());
        });
        projectDailyStatementMapper.updateBatch(projectDailyStatements, projectDailyStatements.size());
        return Boolean.TRUE;
    }

    @Override
    public void exportData(ExportParamDTO exportParamDTO, HttpServletResponse response) throws Exception {
        List<String> idList = exportParamDTO.getIdList();
        LambdaQueryWrapperX<ProjectDailyStatement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (!CollectionUtils.isBlank(idList)) {
            lambdaQueryWrapperX.in(ProjectDailyStatement::getId, idList);
        }
        // 条件设置
        setCondition(lambdaQueryWrapperX, exportParamDTO);
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectList(lambdaQueryWrapperX);

        List<String> distinctIdList = projectDailyStatements.stream().map(ObjectEntity::getId).distinct().collect(Collectors.toList());
        Map<String, List<ProjectDailyStatementContentVO>> idToList = projectDailyStatementContentService.getMapByDataIdList(distinctIdList);

        List<DailyExportDTO> list = new ArrayList<>();
        for (int i = 0; i < projectDailyStatements.size(); i++) {
            ProjectDailyStatement projectDailyStatement = projectDailyStatements.get(i);
            DailyExportDTO dailyExportDTO = BeanCopyUtils.convertTo(projectDailyStatement, DailyExportDTO::new);
            dailyExportDTO.setSort(String.valueOf(i + 1));
            dailyExportDTO.setBusStatusName(ReportingBusEnum.getEnum(projectDailyStatement.getBusStatus()).getMessage());
            //  projectDailyStatement.getDataStatus() 该对象最好判空处理
            DataStatusVO dataStatus = projectDailyStatement.getDataStatus();
            if (null != dataStatus) {
                dailyExportDTO.setStatusName(projectDailyStatement.getDataStatus().getName());
            }
            // 获取的关联内容的数据
            List<ProjectDailyStatementContentVO> contentVOList = idToList.getOrDefault(projectDailyStatement.getId(), Collections.emptyList());
            List<String> workContentList = new ArrayList<>();
            BigDecimal sum = BigDecimal.ZERO;
            List<String> relationshipNameList = new ArrayList<>();
            for (ProjectDailyStatementContentVO projectDailyStatementContentVO : contentVOList) {
                workContentList.add(projectDailyStatementContentVO.getContent());
                sum = sum.add(projectDailyStatementContentVO.getTaskTime());
                //todo 关联的对象名称未处理
                relationshipNameList.add(projectDailyStatementContentVO.getRelationshipName());
            }
            dailyExportDTO.setTaskTime(sum);
            dailyExportDTO.setContent(workContentList.stream().collect(Collectors.joining(";")));
            dailyExportDTO.setRelationObj(relationshipNameList.stream().filter(p -> StringUtils.hasText(p)).collect(Collectors.joining(";")));
            //
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(projectDailyStatement.getResp());
            dailyExportDTO.setRespName(simpleUserById == null ? "" : simpleUserById.getName());
            SimpleUser auditPerson = userRedisHelper.getSimpleUserById(projectDailyStatement.getReviewedBy());
            dailyExportDTO.setReviewedByName(auditPerson == null ? "" : auditPerson.getName());
            dailyExportDTO.setDaily(projectDailyStatement.getDaily());
            list.add(dailyExportDTO);
        }
        /**
         * 导出
         * */
        ExcelUtils.write(response, "项目日报.xlsx", "项目日报", DailyExportDTO.class, list);
    }

    @Override
    public List<DailyStatementCardVO> cardList(ListDailyRequest pageRequest) throws Exception {
        // 设置当前人
//        pageRequest.setCreatorId(CurrentUserHelper.getCurrentUserId());
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<ProjectDailyStatement> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(ProjectDailyStatement::getCreatorId, currentUserId);
        this.setCondition(queryWrapperX, pageRequest);
        List<ProjectDailyStatement> list = this.list(queryWrapperX);

        if (CollectionUtils.isBlank(list)) {
            return new ArrayList<>();
        }

        Map<Date, List<ProjectDailyStatement>> personDayToListMap = list.stream().collect(Collectors.groupingBy(ProjectDailyStatement::getDaily));
        List<ProjectDailyStatement> projectDailyStatementList = new ArrayList<>();
        for (Map.Entry<Date, List<ProjectDailyStatement>> dateListEntry : personDayToListMap.entrySet()) {
            List<ProjectDailyStatement> value = dateListEntry.getValue();
            value.sort(Comparator.comparing(ProjectDailyStatement::getCreateTime).reversed());
            projectDailyStatementList.add(value.get(0));
        }

        List<String> idList = projectDailyStatementList.stream().map(ObjectEntity::getId).collect(Collectors.toList());
        Map<String, List<ProjectDailyStatementContentVO>> idToList = projectDailyStatementContentService.getMapByDataIdList(idList);

        List<DailyStatementCardVO> dailyStatementCardVOList = new ArrayList<>();
        for (ProjectDailyStatement projectDailyStatement : projectDailyStatementList) {
            DailyStatementCardVO dailyStatementCardVO = new DailyStatementCardVO();
            dailyStatementCardVO.setDaily(projectDailyStatement.getDaily());
            dailyStatementCardVO.setId(projectDailyStatement.getId());
            dailyStatementCardVO.setBusStatus(projectDailyStatement.getBusStatus());
            dailyStatementCardVO.setProjectDailyStatementContentVOList(idToList.get(projectDailyStatement.getId()));
            if (ReportingBusEnum.NOT_SUBMITTED.getCode().equals(projectDailyStatement.getBusStatus())) {
                dailyStatementCardVO.setEdit(true);
            }
            dailyStatementCardVOList.add(dailyStatementCardVO);
        }
        return dailyStatementCardVOList;
    }

    @Override
    public TreeMap<Date, ProjectDailyCardVO> checkCard(ListDailyRequest request) throws Exception {
        LambdaQueryWrapperX<ProjectDailyStatement> wrapperX = new LambdaQueryWrapperX<>();
        setCondition(wrapperX, request);
        List<ProjectDailyStatement> projectDailyStatements = projectDailyStatementMapper.selectList(wrapperX);
        if (CollectionUtils.isBlank(projectDailyStatements)) {
            //没有日报
            return new TreeMap<>();
        }
        // 日报ids
        List<String> projectDailyStatementsIds = projectDailyStatements.stream().map(ProjectDailyStatement::getId).collect(Collectors.toList());
        // 日报日期set
        Set<Date> dailySet = projectDailyStatements.stream().map(ProjectDailyStatement::getDaily).collect(Collectors.toSet());
        Map<String, List<ProjectDailyStatementContentVO>> mapByDataIdList = projectDailyStatementContentService.getMapByDataIdList(projectDailyStatementsIds);
        // 转VO
        List<ProjectDailyStatementCardVO> projectDailyStatementCardVOS = BeanCopyUtils.convertListTo(projectDailyStatements, ProjectDailyStatementCardVO::new);
        projectDailyStatementCardVOS.forEach(projectDailyStatementVO -> {
            projectDailyStatementVO.setProjectDailyStatementContentVOList(mapByDataIdList.get(projectDailyStatementVO.getId()));
        });
        TreeMap<Date, ProjectDailyCardVO> cardVOMap = new TreeMap<>();
        dailySet.forEach(daily -> {
            cardVOMap.put(daily, new ProjectDailyCardVO());
        });
        // 状态分类
        projectDailyStatementCardVOS.forEach(card -> {
            Integer busStatus = card.getBusStatus();
            // 是否有审核按钮
            if (ReportingBusEnum.SUBMITTED.getCode().equals(busStatus)) {
                card.setAudit(true);
            }
            // 未提交需要提醒按钮
            if (ReportingBusEnum.NOT_SUBMITTED.getCode().equals(busStatus)) {
                card.setWarn(true);
            }
            ProjectDailyCardVO projectDailyCardVO = cardVOMap.get(card.getDaily());
            if (ReportingBusEnum.NOT_SUBMITTED.getCode().equals(card.getBusStatus())) {
                projectDailyCardVO.getNoSubmitted().add(card);
            } else {
                projectDailyCardVO.getSubmitted().add(card);
            }
        });
        // 添加数量
        for (Map.Entry<Date, ProjectDailyCardVO> dateListEntry : cardVOMap.entrySet()) {
            ProjectDailyCardVO daily = dateListEntry.getValue();
            daily.setSubmittedNumber(daily.getSubmitted().size());
            daily.setNoSubmittedNumber(daily.getNoSubmitted().size());
        }
        return cardVOMap;
    }

    @Override
    public Boolean warn(String id, String url) {
        ProjectDailyStatement projectDailyStatement = getById(id);
        if (projectDailyStatement == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，数据不存在请刷新页面重新提醒");
        }
        ArrayList<String> recipientIdList = new ArrayList<>();
        recipientIdList.add(projectDailyStatement.getCreatorId());
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        String detailUrl = "/pms/dayReportDetails?id=" + projectDailyStatement.getProjectId() + "&curId=" + id;
        senMessageIdea(id
                , DateUtil.format(projectDailyStatement.getDaily(), "yyyy-MM-dd")
                , currentUserId
                , recipientIdList
                , detailUrl);
        return true;
    }

    @Override
    public List<StatusVo> getStatus() {
        return null;
    }

    private void setCondition(LambdaQueryWrapperX<ProjectDailyStatement> queryWrapperX, ListDailyRequest query) throws Exception {

        TimeSearchTypeEnums timeType = query.getTimeType();
        Date weekBegin = null;
        Date weekEnd = null;
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        switch (timeType) {
            case CUSTOM:
            case ALL:
                weekBegin = query.getStartTime();
                weekEnd = query.getEndTime();
                break;
            case THIS_WEEK:
                weekBegin = DateUtil.beginOfWeek(currentDate);
                weekEnd = DateUtil.endOfWeek(currentDate);
                break;
            case THIS_MONTH:
                weekBegin = DateUtil.beginOfMonth(currentDate);
                weekEnd = DateUtil.endOfMonth(currentDate);
                break;
            case LAST_MONTH:
                calendar.setTime(currentDate);
                calendar.add(Calendar.MONTH, -1);
                Date lastMonth = calendar.getTime();
                weekBegin = DateUtil.beginOfMonth(lastMonth);
                weekEnd = DateUtil.endOfMonth(lastMonth);
                break;
            case NEXT_MONTH:
                calendar.setTime(currentDate);
                calendar.add(Calendar.MONTH, 1);
                Date nextMonth = calendar.getTime();
                weekBegin = DateUtil.beginOfMonth(nextMonth);
                weekEnd = DateUtil.endOfMonth(nextMonth);
                break;
            case THIS_DAY:
                queryWrapperX.eq(ProjectDailyStatement::getDaily, getNowDate());
                break;
        }
        if (Objects.nonNull(weekBegin) && Objects.nonNull(weekEnd)) {
            Calendar weekEndCalender = Calendar.getInstance();
            weekEndCalender.setTime(weekEnd);
            weekEndCalender.set(Calendar.HOUR_OF_DAY, 23);
            weekEndCalender.set(Calendar.MINUTE, 59);
            weekEndCalender.set(Calendar.SECOND, 59);
            weekEnd = weekEndCalender.getTime();
            queryWrapperX.between(ProjectDailyStatement::getDaily, weekBegin, weekEnd);
        }


        String resp = query.getResp();
        if (StringUtils.hasText(resp)) {
            queryWrapperX.eq(ProjectDailyStatement::getResp, resp);
        }
        String reviewedBy = query.getReviewedBy();
        if (StringUtils.hasText(reviewedBy)) {
            queryWrapperX.eq(ProjectDailyStatement::getReviewedBy, reviewedBy);
        }
        // 过滤掉 未填写完成的数据
        queryWrapperX.ne(ProjectDailyStatement::getBusStatus, ReportingBusEnum.SAVE.getCode());

        // 判断是否需要过滤 提交人
        // 如果是项目ID 那么需要过滤是否是项目经理
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            queryWrapperX.eq(ProjectDailyStatement::getProjectId, query.getProjectId());

            Boolean isPm = projectService.isPmToUserId(projectId, currentUserId);
            Boolean pageType = query.getPageType();
            // 如果是审核页面
            if (pageType) {
                // 如果不是项目经理
                if (!isPm) {
                    queryWrapperX.eq(ProjectDailyStatement::getReviewedBy, currentUserId);
                }
//                queryWrapperX.eq(ProjectDailyStatement::getBusStatus, ReportingBusEnum.SUBMITTED.getCode());
            } else {
                queryWrapperX.eq(ProjectDailyStatement::getCreatorId, currentUserId);
            }
        } else {
            if (CollectionUtils.isBlank(query.getProjectIds())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请刷新页面选择项目进入!");
            }
            Boolean pageType = query.getPageType();
            // 如果是审核页面
            if (pageType) {
                List<String> projectIds = projectService.getProjectIds();
                List<String> projectIdList = query.getProjectIds().stream().filter(p -> projectIds.contains(p)).collect(Collectors.toList());
                if (CollectionUtils.isBlank(projectIdList)) {
                    queryWrapperX.eq(ProjectDailyStatement::getReviewedBy, currentUserId);
                } else {
                    queryWrapperX.and(m -> m.eq(ProjectDailyStatement::getReviewedBy, currentUserId).or().in(ProjectDailyStatement::getProjectId, projectIdList));
                }

            } else {
                queryWrapperX.in(ProjectDailyStatement::getProjectId, query.getProjectIds());
                queryWrapperX.eq(ProjectDailyStatement::getCreatorId, currentUserId);
            }
        }

    }

    public Date getNowDate() throws ParseException {
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateTimeFormat.parse(DateUtil.today());
    }

    public Date getTomorrow(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.DAY_OF_YEAR, 1);
        return instance.getTime();
    }


    private void updateContent(ProjectDailyStatementDTO projectDailyStatementDTO, ProjectDailyStatement nextDaily) {
        if (!projectDailyStatementDTO.getNexDayVOList().isEmpty()) {
            List<ProjectDailyStatementContentDTO> nexDayVOList = projectDailyStatementDTO.getNexDayVOList();
            LambdaQueryWrapperX<ProjectDailyStatementContent> removeWrapper = new LambdaQueryWrapperX<>();
            removeWrapper.eq(ProjectDailyStatementContent::getDailyStatementId, nextDaily.getId());
            projectDailyStatementContentService.remove(removeWrapper);
            nexDayVOList.forEach(n -> {
                n.setDailyStatementId(nextDaily.getId());
            });
        } else {
            LambdaQueryWrapperX<ProjectDailyStatementContent> removeWrapper = new LambdaQueryWrapperX<>();
            removeWrapper.eq(ProjectDailyStatementContent::getDailyStatementId, nextDaily.getId());
            projectDailyStatementContentService.remove(removeWrapper);
        }
    }


    private List<ProjectDailyStatement> getNextSaveStatusDaily(ProjectDailyStatement projectDailyStatement) {
        Date tomorrow = getTomorrow(projectDailyStatement.getDaily());
        LambdaQueryWrapperX<ProjectDailyStatement> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProjectDailyStatement::getDaily, tomorrow)
                .eq(ProjectDailyStatement::getBusStatus, ReportingBusEnum.SAVE.getCode())
                .eq(ProjectDailyStatement::getCreatorId, projectDailyStatement.getCreatorId());
        return projectDailyStatementMapper.selectList(wrapperX);
    }

    private ProjectDailyStatement saveNextDaily(ProjectDailyStatementDTO projectDailyStatementDTO) {
        ProjectDailyStatement projectDailyStatementNext = new ProjectDailyStatement();
        projectDailyStatementNext.setDaily(getTomorrow(projectDailyStatementDTO.getDaily()));
        projectDailyStatementNext.setResp(null);
        projectDailyStatementNext.setCarbonCopyBy(null);
        projectDailyStatementNext.setSummary(null);
        projectDailyStatementNext.setProjectId(projectDailyStatementDTO.getProjectId());
        projectDailyStatementNext.setBusStatus(ReportingBusEnum.SAVE.getCode());
        projectDailyStatementMapper.insert(projectDailyStatementNext);
        return projectDailyStatementNext;
    }

    private Boolean ifExist(String creatorId, String projectId, Date day) {
        boolean exist = false;
        LambdaQueryWrapperX<ProjectDailyStatement> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProjectDailyStatement::getCreatorId, creatorId);
        wrapperX.eq(ProjectDailyStatement::getProjectId, projectId);
        wrapperX.eq(ProjectDailyStatement::getDaily, day);
        wrapperX.ne(ProjectDailyStatement::getBusStatus, ReportingBusEnum.SAVE.getCode());
        long count = count(wrapperX);
        if (count > 0) {
            exist = true;
        }
        return exist;
    }

    public void senMessageCommit(String dataId, String personName, String day, String personId, List<String> recipientIdList, String url) {
        // todo 提交后的消息跳转地址
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$personName$", personName);
        messageMap.put("$day$", day);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.DAILY_COMMIT.getNode())
                .titleMap(messageMap)
                //.messageUrl(String.format(JUMP_URL, project.getId()))
                .messageUrl(url)
                .messageUrlName("日报提交")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("提交消息发送成功：日报id：{}", dataId);
    }

    public void senMessageIdea(String dataId, String day, String personId, List<String> recipientIdList, String url) {
        // todo 提交后的消息跳转地址
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$day$", day);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.DAILY_IDEA.getNode())
                .titleMap(messageMap)
                .messageUrl(url)
                .messageUrlName("日报提醒")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("提醒消息发送成功：日报id：{}", dataId);
    }

    public void senMessageAudit(String dataId, String day, BigDecimal score, String idea, String personId, List<String> recipientIdList, String detailUrl) {
        // todo 提交后的消息跳转地址
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$day$", day);
        messageMap.put("$score$", score);
        messageMap.put("$idea$", idea);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.DAILY_AUDIT.getNode())
                .titleMap(messageMap)
                .messageUrl(detailUrl)
                .messageUrlName("日报完成审核")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("审核消息发送成功：日报id：{}", dataId);
    }


}
