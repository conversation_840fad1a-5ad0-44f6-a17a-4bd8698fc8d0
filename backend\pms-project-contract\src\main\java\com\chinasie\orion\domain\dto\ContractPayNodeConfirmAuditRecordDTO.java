package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractPayNodeConfirmAuditRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-27 15:12:55
 */
@ApiModel(value = "ContractPayNodeConfirmAuditRecordDTO对象", description = "合同支付节点确认审核记录")
@Data
public class ContractPayNodeConfirmAuditRecordDTO extends ObjectDTO implements Serializable {

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditComment;

    /**
     * 审核用户Id
     */
    @ApiModelProperty(value = "审核用户Id")
    private String auditUserId;

    /**
     * 支付节点确认id
     */
    @ApiModelProperty(value = "支付节点确认id")
    private String confirmId;

    /**
     * 提交id
     */
    @ApiModelProperty(value = "提交id")
    private String submitId;

}
