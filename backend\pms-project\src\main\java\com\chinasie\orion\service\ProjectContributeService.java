package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ProjectContributeDTO;
import com.chinasie.orion.domain.entity.ProjectContribute;
import com.chinasie.orion.domain.vo.ProjectContributeVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectContribute 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
public interface ProjectContributeService  extends  OrionBaseService<ProjectContribute>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProjectContributeVO detail(String id,String pageCode)throws Exception;

    /**
     * 新增编辑删除项目贡献情况
     * @param projectContributeList
     * @param projectId
     * @return
     * @throws Exception
     */
    Boolean saveOrRemove(List<ProjectContributeDTO> projectContributeList, String projectId) throws Exception;

    /**
     * 获取项目贡献情况列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectContributeVO> getList(String projectId) throws Exception;
}
