-- -------------------------------------
-- xxljob
-- -------------------------------------
INSERT INTO xxl_job_group (id, app_name, title, address_type, address_list, update_time) VALUES (5, 'xxl-job-executor-pms', 'pms服务', 0, 'http://*************:8701/', '2024-03-20 16:25:44');

INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (16, 5, '投资计划关闭', '2023-05-22 20:53:24', '2023-05-22 20:57:25', 'Orion', '', 'CRON', '0 0 0 1 1 ?', 'DO_NOTHING', 'FIRST', 'investmentSchemeClose', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-22 20:53:24', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (17, 5, '提醒编制月度反馈', '2023-05-22 20:55:44', '2023-05-22 20:56:49', 'Orion', '', 'CRON', '0 0 0 15 * ?', 'DO_NOTHING', 'FIRST', 'investmentSchemeFeedbackSendTodo', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-22 20:55:44', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (18, 5, '资产转固-项目转固', '2023-05-23 18:04:33', '2023-05-27 15:48:04', 'orion', '', 'CRON', '* * 3 * * ?', 'DO_NOTHING', 'FIRST', 'transferCapitalizeSendMes', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-23 18:04:33', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (19, 5, '项目相关文档变动数据采集', '2023-05-25 13:42:27', '2023-09-26 14:24:51', 'orion', '', 'CRON', '0 0/1 * * * ?', 'DO_NOTHING', 'FIRST', 'processProjectRelatedFiles', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-25 13:42:27', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (20, 4, '计划临期提醒', '2023-05-29 11:46:19', '2023-09-26 14:24:31', 'Orion', '', 'CRON', '0 0 8 * * ?', 'DO_NOTHING', 'FIRST', 'sendPlanArrivedEdnTimeJob', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-29 11:46:19', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (21, 5, '同步刷新检测（每两小时执行）', '2023-05-30 22:42:52', '2023-05-30 22:50:01', 'orion', '', 'CRON', '* * 0/2 * * ?', 'DO_NOTHING', 'FIRST', 'allDataRefreshData', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-30 22:42:52', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (22, 5, '预算同步(每天凌晨一点)', '2023-05-30 22:49:01', '2023-05-30 22:49:34', 'orion', '', 'CRON', '* * 1 * * ? *', 'DO_NOTHING', 'FIRST', 'refreshBudgeNumberListToDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-30 22:49:01', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (23, 5, '采购计划同步（每天凌晨0点执行）', '2023-05-30 22:51:56', '2023-05-30 22:51:56', 'orion', '', 'CRON', '* * 0 * * ?', 'DO_NOTHING', 'FIRST', 'refreshPurchasePlanToDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-30 22:51:56', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (24, 5, '合同同步（每天凌晨2点执行）', '2023-05-30 22:53:33', '2023-05-30 22:53:33', 'orion', '', 'CRON', '* * 2 * * ? *', 'DO_NOTHING', 'FIRST', 'refreshContractToDay', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-30 22:53:33', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (25, 5, '预算预警（每小时执行）', '2023-05-30 22:54:40', '2023-05-30 22:54:40', 'orion', '', 'CRON', '* * * * * ? *', 'DO_NOTHING', 'FIRST', 'budgeWarning', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-05-30 22:54:40', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (26, 5, '项目责任科室/班组/部门信息采集', '2023-06-08 18:54:55', '2023-06-08 18:54:55', 'orion', '', 'CRON', '0 0/10 * * * ?', 'DO_NOTHING', 'FIRST', 'batchProcessProjectResDept', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-06-08 18:54:55', '', 0, 0, 0);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (27, 5, '项目预警设置', '2023-10-17 14:19:37', '2023-10-19 14:08:30', 'orion', '', 'CRON', '0 30 0 * * ?', 'DO_NOTHING', 'FIRST', 'warningSettingJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-10-17 14:19:37', '', 1, 1710865800000, 1710952200000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (28, 5, '更改项目合同状态为合同履行', '2023-10-28 15:44:02', '2023-10-28 15:44:09', 'orion', '', 'CRON', '0 10 0,12 * * ?', 'DO_NOTHING', 'FIRST', 'projectContractChangeStatusJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-10-28 15:44:02', '', 1, 1710907800000, 1710951000000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (29, 5, '更改项目合同支付节点状态为待支付', '2023-10-28 15:44:38', '2023-10-28 15:44:48', 'orion', '', 'CRON', '0 20 0,12 * * ?', 'DO_NOTHING', 'FIRST', 'projectContractPayNodeChangeStatus', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-10-28 15:44:38', '', 1, 1710908400000, 1710951600000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (32, 6, '全文检索数据抓取', '2023-11-05 22:24:49', '2024-01-02 14:38:35', 'orion', '', 'CRON', '0/10 * * * * ?', 'DO_NOTHING', 'FIRST', 'search-fetchIndexData', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-10-27 17:05:39', '', 1, 1710923220000, 1710923230000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (33, 5, '项目内计划统计报表定时任务', '2023-12-22 18:33:38', '2023-12-25 13:31:12', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectNewSchemeStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:33:38', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (34, 5, '项目内需求状态趋势统计报表定时任务', '2023-12-22 18:34:31', '2023-12-25 13:31:16', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectDemandStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:34:31', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (35, 5, '项目内问题状态趋势统计报表定时任务', '2023-12-22 18:34:51', '2023-12-25 13:31:20', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectQuestionStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:34:51', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (36, 5, '项目内风险状态趋势统计报表定时任务', '2023-12-22 18:35:19', '2023-12-25 13:31:24', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectRiskStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:35:19', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (37, 5, '项目内交付物状态趋势统计报表定时任务', '2023-12-22 18:35:56', '2023-12-25 13:31:28', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectDeliverableStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:35:56', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (38, 5, '项目物资状态趋势统计报表定时任务', '2023-12-22 18:36:32', '2023-12-25 13:31:32', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectGoodsStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:36:32', '', 1, 1710864000000, 1710950400000);
INSERT INTO xxl_job_info (id, job_group, job_desc, add_time, update_time, author, alarm_email, schedule_type, schedule_conf, misfire_strategy, executor_route_strategy, executor_handler, executor_param, executor_block_strategy, executor_timeout, executor_fail_retry_count, glue_type, glue_source, glue_remark, glue_updatetime, child_jobid, trigger_status, trigger_last_time, trigger_next_time) VALUES (39, 5, '项目采购状态趋势统计报表定时任务', '2023-12-22 18:37:35', '2023-12-25 13:31:35', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectPurchaseStatisticDailyCount', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-12-22 18:37:35', '', 1, 1710864000000, 1710950400000);
-- -------------------------------------
-- xxljob
-- -------------------------------------
INSERT INTO `xxl_job_info` (`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (47, 5, '项目计划反馈提醒', '2024-04-19 18:34:16', '2024-04-22 09:40:22', 'orion', '', 'CRON', '0 * * * * ?', 'DO_NOTHING', 'FIRST', 'projectSchemeFeedback', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-04-19 18:34:16', '', 1, 1713768900000, 1713777060000);
