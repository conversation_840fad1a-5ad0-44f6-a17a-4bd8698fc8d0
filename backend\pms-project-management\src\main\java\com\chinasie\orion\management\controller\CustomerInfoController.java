package com.chinasie.orion.management.controller;

import com.chinasie.orion.management.domain.dto.CustomerInfoDTO;
import com.chinasie.orion.management.domain.vo.CustomerInfoVO;
import com.chinasie.orion.management.service.CustomerInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;



import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * customerInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@RestController
@RequestMapping("/customerInfo")
@Api(tags = "客户管理")
public class CustomerInfoController {

    @Autowired
    private CustomerInfoService customerInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#id}}】", type = "客户管理", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<CustomerInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        CustomerInfoVO rsp = customerInfoService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#customerInfoDTO.name}}】", type = "客户管理", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody CustomerInfoDTO customerInfoDTO) throws Exception {
        String rsp =  customerInfoService.create(customerInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#customerInfoDTO.name}}】", type = "客户管理", subType = "编辑", bizNo = "{{#customerInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody CustomerInfoDTO customerInfoDTO) throws Exception {
        Boolean rsp = customerInfoService.edit(customerInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "客户管理", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = customerInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "客户管理", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = customerInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "客户管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST,produces = {MediaType.APPLICATION_JSON_VALUE},consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseDTO<Page<CustomerInfoVO>> pages(@RequestBody Page<CustomerInfoDTO> pageRequest) throws Exception {
        Page<CustomerInfoVO> rsp =  customerInfoService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("客户管理导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "客户管理", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        customerInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("客户管理导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "客户管理", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = customerInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("客户管理导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "客户管理", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  customerInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消客户管理导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "客户管理", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  customerInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("客户管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "客户管理", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<String> ids, HttpServletResponse response) throws Exception {
        customerInfoService.exportByExcel(ids, response);
    }


    /**
     * 修改状态为启用
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "/open/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用了数据", type = "客户管理", subType = "启用", bizNo = "{{#id}}")
    public ResponseDTO<String> open(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        String rsp = customerInfoService.open(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 修改状态为停用
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "停用")
    @RequestMapping(value = "/close/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】停用了数据", type = "客户管理", subType = "停用", bizNo = "{{#id}}")
    public ResponseDTO<String> close(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        String rsp = customerInfoService.close(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划查询甲方单位名称")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "客户管理", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getIncomeCustomerInfoPages", method = RequestMethod.POST,produces = {MediaType.APPLICATION_JSON_VALUE},consumes = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseDTO<Page<CustomerInfoVO>> getIncomeCustomerInfoPages(@RequestBody Page<CustomerInfoDTO> pageRequest) throws Exception {
        Page<CustomerInfoVO> rsp =  customerInfoService.getIncomeCustomerInfoPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
