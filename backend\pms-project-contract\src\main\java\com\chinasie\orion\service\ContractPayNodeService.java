package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ContractPayNodeDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeStatusConfirmDTO;
import com.chinasie.orion.domain.entity.ContractPayNode;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.domain.vo.ContractPayNodeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ContractPayNode 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
public interface ContractPayNodeService extends OrionBaseService<ContractPayNode> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractPayNodeDetailVO detail(String id) throws Exception;

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ContractPayNodeVO> pages(Page<ContractPayNodeDTO> pageRequest) throws Exception;

    /**
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ContractPayNodeVO> listByContractId(String contractId) throws Exception;

    /**
     * 支付状态确认
     *
     * @param contractPayNodeStatusConfirmDTO
     * @return
     * @throws Exception
     */
    Boolean statusConfirm(ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO) throws Exception;
}
