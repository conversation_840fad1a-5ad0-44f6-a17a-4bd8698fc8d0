package com.chinasie.orion.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.RabbitMQMessageHandler;
import com.chinasie.orion.constant.MonthInvestmentSchemeFeedbackStatus;
import com.chinasie.orion.constant.YearInvestmentSchemeStatus;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.YearInvestmentSchemeMonthFeedbackDTO;
import com.chinasie.orion.domain.entity.MonthInvestmentScheme;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.YearInvestmentScheme;
import com.chinasie.orion.domain.entity.YearInvestmentSchemeMonthFeedback;
import com.chinasie.orion.domain.vo.MonthInvestmentSchemeVO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeMonthFeedbackVO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.YearInvestmentSchemeMonthFeedbackRepository;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;

import com.chinasie.orion.service.MonthInvestmentSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.YearInvestmentSchemeMonthFeedbackService;
import com.chinasie.orion.service.YearInvestmentSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * YearInvestmentSchemeMonthFeedback 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15 19:21:49
 */
@Service
@Slf4j
public class YearInvestmentSchemeMonthFeedbackServiceImpl extends OrionBaseServiceImpl<YearInvestmentSchemeMonthFeedbackRepository,YearInvestmentSchemeMonthFeedback> implements YearInvestmentSchemeMonthFeedbackService {

    @Resource
    private ProjectService projectService;

    @Autowired
    private RabbitMQMessageHandler rabbitMQMessageHandler;

    @Resource
    private YearInvestmentSchemeService yearInvestmentSchemeService;

    @Resource
    private MonthInvestmentSchemeService monthInvestmentSchemeService;


    @Resource
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private MessageCenterApi messageCenterApi;


    @Override
    public YearInvestmentSchemeMonthFeedbackVO initValue(String yearId) throws Exception {
        YearInvestmentSchemeVO yearInvestmentSchemeVO = yearInvestmentSchemeService.detail(yearId, null);
        YearInvestmentSchemeMonthFeedbackVO result = new YearInvestmentSchemeMonthFeedbackVO();
        String month = DateUtil.format(new Date(), "M");
        result.setNumber(yearInvestmentSchemeVO.getNumber() + "-" + month);
        String year = DateUtil.format(new Date(), "yyyy");
        result.setName(year + "年" + month + "月反馈表");
        result.setYear(year);
        result.setMonth(month + "月");
        result.setYearProcess(yearInvestmentSchemeVO.getYearProcess());

        List<MonthInvestmentSchemeVO> monthInvestmentSchemes = yearInvestmentSchemeVO.getMonthInvestmentSchemes();
        List<MonthInvestmentSchemeVO> currentMonthInvestmentSchemeVOs = monthInvestmentSchemes.stream().filter(o -> StrUtil.endWith(o.getName(), result.getMonth())).collect(Collectors.toList());
        MonthInvestmentSchemeVO monthInvestmentSchemeVO = currentMonthInvestmentSchemeVOs.get(0);
        result.setMplanDo(monthInvestmentSchemeVO.getPredicate());

        LambdaQueryWrapperX<YearInvestmentSchemeMonthFeedback> feedbackLambdaQueryWrapperX =   new LambdaQueryWrapperX(YearInvestmentSchemeMonthFeedback.class);
        feedbackLambdaQueryWrapperX.eq(YearInvestmentSchemeMonthFeedback::getMonth, (Integer.parseInt(month) - 1) + "月");
        feedbackLambdaQueryWrapperX.eq(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearId);
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedback = this.list(feedbackLambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(yearInvestmentSchemeMonthFeedback)) {
            result.setLastPracticeDo(new BigDecimal("0"));
        } else {
            result.setLastPracticeDo(yearInvestmentSchemeMonthFeedback.get(0).getMpracticeDo());
        }
        BigDecimal yInvestmentPlan = yearInvestmentSchemeVO.getArchitecture().add(yearInvestmentSchemeVO.getInstallation()).add(yearInvestmentSchemeVO.getDevice()).add(yearInvestmentSchemeVO.getOther());
        result.setYinvestmentPlan(yInvestmentPlan);
        return result;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public YearInvestmentSchemeMonthFeedbackVO detail(String id, String pageCode) throws Exception {
        YearInvestmentSchemeMonthFeedback yearInvestmentSchemeMonthFeedback = this.getById(id);
        YearInvestmentSchemeMonthFeedbackVO result = BeanCopyUtils.convertTo(yearInvestmentSchemeMonthFeedback, YearInvestmentSchemeMonthFeedbackVO::new);

        YearInvestmentSchemeVO yearInvestmentSchemeVO = yearInvestmentSchemeService.detail(yearInvestmentSchemeMonthFeedback.getYearInvestmentId(), null);
        result.setProjectName(yearInvestmentSchemeVO.getProjectName());
        result.setProjectNumber(yearInvestmentSchemeVO.getProjectNumber());
        result.setYearProcess(yearInvestmentSchemeVO.getYearProcess());

        List<MonthInvestmentSchemeVO> monthInvestmentSchemes = yearInvestmentSchemeVO.getMonthInvestmentSchemes();
        List<MonthInvestmentSchemeVO> currentMonthInvestmentSchemeVOs = monthInvestmentSchemes.stream().filter(o -> StrUtil.endWith(o.getName(), result.getMonth())).collect(Collectors.toList());
        MonthInvestmentSchemeVO monthInvestmentSchemeVO = currentMonthInvestmentSchemeVOs.get(0);
        result.setMplanDo(monthInvestmentSchemeVO.getPredicate());

        List<YearInvestmentSchemeMonthFeedback> lastYearInvestmentSchemeMonthFeedback = this.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).eq(YearInvestmentSchemeMonthFeedback::getMonth, (Integer.parseInt(result.getMonth().replace("月", "")) - 1) + "月").eq(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearInvestmentSchemeMonthFeedback.getYearInvestmentId()));
        if (CollectionUtils.isEmpty(lastYearInvestmentSchemeMonthFeedback)) {
            result.setLastPracticeDo(new BigDecimal("0"));
        } else {
            result.setLastPracticeDo(lastYearInvestmentSchemeMonthFeedback.get(0).getMpracticeDo());
        }
        BigDecimal yInvestmentPlan = yearInvestmentSchemeVO.getArchitecture().add(yearInvestmentSchemeVO.getInstallation()).add(yearInvestmentSchemeVO.getDevice()).add(yearInvestmentSchemeVO.getOther());
        result.setYinvestmentPlan(yInvestmentPlan);


        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), CurrentUserHelper.getCurrentUserId());

        pmsAuthUtil.setDetailAuths(result, CurrentUserHelper.getCurrentUserId(), pageCode, result.getDataStatus(), YearInvestmentSchemeMonthFeedbackVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param yearInvestmentSchemeMonthFeedbackDTO
     */
    @Override
    public YearInvestmentSchemeMonthFeedbackVO create(YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception {
        YearInvestmentScheme yearInvestmentScheme = yearInvestmentSchemeService.getById(yearInvestmentSchemeMonthFeedbackDTO.getYearInvestmentId());
        YearInvestmentSchemeMonthFeedback yearInvestmentSchemeMonthFeedback = BeanCopyUtils.convertTo(yearInvestmentSchemeMonthFeedbackDTO, YearInvestmentSchemeMonthFeedback::new);

        //是不是当前年的计划反馈
        if (!StrUtil.equals(yearInvestmentScheme.getYearName(), DateUtil.format(new Date(), "yyyy"))) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只能创建本年度的计划反馈");
        }


        //新增时判断当年的投资计划是否已经存在
        String mothName = yearInvestmentSchemeMonthFeedback.getMonth();
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbacks = this.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).eq(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearInvestmentSchemeMonthFeedbackDTO.getYearInvestmentId()).eq(YearInvestmentSchemeMonthFeedback::getMonth, mothName));
        if (!CollectionUtils.isEmpty(yearInvestmentSchemeMonthFeedbacks)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该月度反馈已存在");
        }


        yearInvestmentSchemeMonthFeedback.setProjectId(yearInvestmentScheme.getProjectId());
        if (Objects.isNull(yearInvestmentSchemeMonthFeedback.getMpracticeDo())) {
            yearInvestmentSchemeMonthFeedback.setMpracticeDo(BigDecimal.ZERO);
        }

        this.save(yearInvestmentSchemeMonthFeedback);
        YearInvestmentSchemeMonthFeedbackVO rsp = BeanCopyUtils.convertTo(yearInvestmentSchemeMonthFeedback, YearInvestmentSchemeMonthFeedbackVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param yearInvestmentSchemeMonthFeedbackDTO
     */
    @Override
    public YearInvestmentSchemeMonthFeedbackVO edit(YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception {
        YearInvestmentSchemeMonthFeedback yearInvestmentSchemeMonthFeedback = BeanCopyUtils.convertTo(yearInvestmentSchemeMonthFeedbackDTO, YearInvestmentSchemeMonthFeedback::new);
        if (Objects.isNull(yearInvestmentSchemeMonthFeedback.getMpracticeDo())) {
            yearInvestmentSchemeMonthFeedback.setMpracticeDo(BigDecimal.ZERO);
        }
        Boolean result = this.updateById(yearInvestmentSchemeMonthFeedback);
        YearInvestmentSchemeMonthFeedbackVO detail = this.detail(yearInvestmentSchemeMonthFeedbackDTO.getId(), null);
        return detail;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        Boolean result = this.removeBatchByIds(ids);
        return result;
    }


    /**
     * 列表
     * <p>
     * * @param pageRequest
     */
    @Override
    public List<YearInvestmentSchemeMonthFeedbackVO> list(String yearId, String pageCode, String containerCode) throws Exception {
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbacks = this.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).eq(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearId));

        Map<String, YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbackMonthMap = yearInvestmentSchemeMonthFeedbacks.stream().collect(Collectors.toMap(YearInvestmentSchemeMonthFeedback::getMonth, Function.identity()));

        List<YearInvestmentSchemeMonthFeedbackVO> yearInvestmentSchemeMonthFeedbackVOS = BeanCopyUtils.convertListTo(yearInvestmentSchemeMonthFeedbacks, YearInvestmentSchemeMonthFeedbackVO::new);

        YearInvestmentSchemeVO yearInvestmentSchemeVO = yearInvestmentSchemeService.detail(yearId, null);
        yearInvestmentSchemeMonthFeedbackVOS.forEach(vo -> {
            vo.setYearProcess(yearInvestmentSchemeVO.getYearProcess());

            List<MonthInvestmentSchemeVO> monthInvestmentSchemes = yearInvestmentSchemeVO.getMonthInvestmentSchemes();
            List<MonthInvestmentSchemeVO> currentMonthInvestmentSchemeVOs = monthInvestmentSchemes.stream().filter(o -> StrUtil.endWith(o.getName(), vo.getMonth())).collect(Collectors.toList());
            MonthInvestmentSchemeVO monthInvestmentSchemeVO = currentMonthInvestmentSchemeVOs.get(0);
            vo.setMplanDo(monthInvestmentSchemeVO.getPredicate());

            YearInvestmentSchemeMonthFeedback lastYearInvestmentSchemeMonthFeedback = yearInvestmentSchemeMonthFeedbackMonthMap.get((Integer.parseInt(vo.getMonth().replace("月", "")) - 1) + "月");
            if (Objects.isNull(lastYearInvestmentSchemeMonthFeedback)) {
                vo.setLastPracticeDo(new BigDecimal("0"));
            } else {
                vo.setLastPracticeDo(lastYearInvestmentSchemeMonthFeedback.getMpracticeDo());
            }
            BigDecimal yInvestmentPlan = yearInvestmentSchemeVO.getArchitecture().add(yearInvestmentSchemeVO.getInstallation()).add(yearInvestmentSchemeVO.getDevice()).add(yearInvestmentSchemeVO.getOther());
            vo.setYinvestmentPlan(yInvestmentPlan);
        });

        PowerParams power = new PowerParams();
        power.setPageCode(pageCode);
        power.setContainerCode(containerCode);
        yearInvestmentSchemeMonthFeedbackVOS.sort(Comparator.comparing(YearInvestmentSchemeMonthFeedbackVO::getCreateTime));
        return yearInvestmentSchemeMonthFeedbackVOS;
    }


    @Override
    public Boolean updateStatus(ChangeStatusMessageDTO message) throws Exception {
        YearInvestmentSchemeMonthFeedback yearInvestmentSchemeMonthFeedback = this.getById(message.getBusinessId());
        yearInvestmentSchemeMonthFeedback.setStatus(message.getStatus());
        this.updateById(yearInvestmentSchemeMonthFeedback);
        if (Objects.equals(message.getStatus(), MonthInvestmentSchemeFeedbackStatus.CHANGE_WRITE.getCode())) {
            List<MonthInvestmentScheme> monthInvestmentSchemes = monthInvestmentSchemeService.list(new LambdaQueryWrapperX<>(MonthInvestmentScheme.class)
                    .eq(MonthInvestmentScheme::getYearId, yearInvestmentSchemeMonthFeedback.getYearInvestmentId())
                    .like(MonthInvestmentScheme::getName, yearInvestmentSchemeMonthFeedback.getMonth())
            );
            if (!CollectionUtils.isEmpty(monthInvestmentSchemes)) {
                MonthInvestmentScheme monthInvestmentScheme = monthInvestmentSchemes.get(0);
                monthInvestmentScheme.setActual(yearInvestmentSchemeMonthFeedback.getMpracticeDo());
                monthInvestmentScheme.setHasDo(1);
                monthInvestmentScheme.setFeedbackId(message.getBusinessId());
                monthInvestmentSchemeService.updateById(monthInvestmentScheme);
                YearInvestmentScheme yearInvestmentScheme = yearInvestmentSchemeService.getById(monthInvestmentScheme.getYearId());
                yearInvestmentScheme.setTotalDo(monthInvestmentScheme.getActual());
                yearInvestmentSchemeService.updateById(yearInvestmentScheme);
            }

        }
        //消除待办
        String projectId = yearInvestmentSchemeMonthFeedback.getProjectId();
        Project project = projectService.getById(projectId);
        //变更待办
        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(project.getResPerson())
                .businessId(yearInvestmentSchemeMonthFeedback.getYearInvestmentId())
                .build());
        log.debug("月度投资计划反馈待办变更响应：{}", JSON.toJSONString(responseDTO));
        return true;
    }

    @Override
    public void sendTodo() throws Exception {
        List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class)
                .eq(YearInvestmentScheme::getYearName, DateUtil.format(new Date(), "yyyy"))
                .eq(YearInvestmentScheme::getStatus, YearInvestmentSchemeStatus.GIVE.getCode())
        );
        if (CollectionUtils.isEmpty(yearInvestmentSchemes)) {
            return;
        }
        //去掉调整的
        List<YearInvestmentScheme> needSend = new ArrayList<>();
        Map<Boolean, List<YearInvestmentScheme>> isChangeMap = yearInvestmentSchemes.stream().collect(Collectors.groupingBy(o -> StrUtil.isNotBlank(o.getOldId())));
        List<YearInvestmentScheme> changed = isChangeMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        needSend.addAll(changed);
        List<String> changeIds = changed.stream().map(YearInvestmentScheme::getOldId).distinct().collect(Collectors.toList());
        List<YearInvestmentScheme> notChanged = isChangeMap.getOrDefault(Boolean.FALSE, new ArrayList<>());
        notChanged.removeIf(o -> changeIds.contains(o.getId()));
        needSend.addAll(notChanged);

        //判断是否已经填写了月度反馈
        List<String> yearIds = needSend.stream().map(YearInvestmentScheme::getId).collect(Collectors.toList());
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbacks = this.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class).in(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearIds).eq(YearInvestmentSchemeMonthFeedback::getMonth, DateUtil.format(new Date(), "M") + "月"));
        if (!CollectionUtils.isEmpty(yearInvestmentSchemeMonthFeedbacks)) {
            List<String> writed = yearInvestmentSchemeMonthFeedbacks.stream().map(YearInvestmentSchemeMonthFeedback::getYearInvestmentId).collect(Collectors.toList());
            needSend.removeIf(o -> writed.contains(o.getId()));
        }


        List<String> projectIds = needSend.stream().map(YearInvestmentScheme::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);

        Map<String, List<Project>> recipientIdListMap = projects.stream().collect(Collectors.groupingBy(Project::getId));

        needSend.forEach(e -> {
            Project project = recipientIdListMap.getOrDefault(e.getProjectId(), new ArrayList<>()).get(0);
            List<String> receivers = new ArrayList<>(Collections.singletonList(project.getResPerson()));

            HashMap<String, Object> variableMap = new HashMap<>();
            variableMap.put("$projectName$", project.getName());
            variableMap.put("$yearName$", e.getYearName() + "年");
            variableMap.put("$monthName$", DateUtil.format(new Date(), "M") + "月");

            SendMessageDTO sendMessageDTO = buildMessage(MessageNodeNumberDict.investment_feedback_write_note,
                    "/pms/annualInvestment/" + e.getId(),
                    TodoTypeDict.TODO_TYPE_TASK,
                    receivers,
                    e,
                    variableMap,
                    YearInvestmentScheme::getId,
                    YearInvestmentScheme::getPlatformId,
                    YearInvestmentScheme::getOrgId, "月度计划反馈提醒");

            // 发送消息
            sendM(sendMessageDTO);
        });
    }


    public <T> void sendM(SendMessageDTO sendMessageDTO) {
        log.info("消息发送：{}", JSONUtil.toJsonStr(sendMessageDTO));
        rabbitMQMessageHandler.sendMessage(sendMessageDTO);
    }

    private static <T> SendMessageDTO buildMessage(String msgNodeNumber, String url, Integer todoType, List<String> receivers, T businessObj, Map<String, Object> variableMap, Function<T, String> getId, Function<T, String> getPId, Function<T, String> getOId, String flowType) {

        Map<String, Object> businessData = new HashMap<>();
        businessData.put("flowType", flowType);
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .businessNodeCode(msgNodeNumber)
                .titleMap(variableMap)
                .messageMap(variableMap)
                .messageUrl(url)
                .messageUrlName("详情")
                .recipientIdList(receivers)
                .senderId(Objects.isNull(CurrentUserHelper.getCurrentUserId()) ? "user00000000000000000100000000000000" : CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(todoType)
                .businessId(getId.apply(businessObj))
                .platformId(getPId.apply(businessObj))
                .orgId(getOId.apply(businessObj))
                .businessData(JSONUtil.toJsonStr(businessData))
                .build();
        return sendMessageDTO;
    }

}


