package com.chinasie.orion.domain.dto.job;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/10/17:48
 * @description:
 */
@Data
public class EditContactUserDTO implements Serializable {
    @ApiModelProperty("人员管理ID")
    @NotEmpty(message = "人员管理ID不能为空")
    private String id;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    private String contactUser;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人编号")
    private String contactUserCode;
    @ApiModelProperty(value = "接口人名称")
    private String contactUserName;
}
