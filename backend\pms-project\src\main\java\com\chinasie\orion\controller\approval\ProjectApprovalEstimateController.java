package com.chinasie.orion.controller.approval;

import com.chinasie.orion.domain.dto.InterOutTrialBasicDataDTO;
import com.chinasie.orion.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.domain.dto.approval.*;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateMaterial;
import com.chinasie.orion.domain.vo.InterOutTrialBasicDataVO;
import com.chinasie.orion.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateExpenseSubjectVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateMaterialVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.domain.vo.approval.SubjectSummaryVO;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.pdm.api.domain.dto.BasicMaterialsDTO;
import com.chinasie.orion.pdm.api.domain.vo.BasicMaterialsVO;
import com.chinasie.orion.pdm.api.service.MaterialsApiService;
import com.chinasie.orion.pdm.api.service.ProductApiService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateExpenseSubjectService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateInterOutTrialFeeService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateLaborFeeService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.awt.*;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectApprovalEstimate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06 17:12:54
 */
@RestController
@RequestMapping("/projectApprovalEstimate")
@Api(tags = "项目立项-概算")
public class ProjectApprovalEstimateController {

    @Autowired
    private ProjectApprovalEstimateMaterialService projectApprovalEstimateMaterialService;


    @Autowired
    private ProjectApprovalEstimateExpenseSubjectService projectApprovalEstimateExpenseSubjectService;

    @Autowired
    private ProjectApprovalEstimateInterOutTrialFeeService projectApprovalEstimateInterOutTrialFeeService;

    @Autowired
    private ProjectApprovalEstimateLaborFeeService projectApprovalEstimateLaborFeeService;

    @Autowired
    private PasFeignService pasFeignService;


    @Autowired
    private MaterialsApiService materialsApiService;

    @Autowired
    private ProductApiService productApiService;

    /**
     * 批量编辑科目概算金额
     * @param list
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量编辑科目概算金额")
    @RequestMapping(value = "/subject/editAmount", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量编辑【项目立项-概算】科目概算金额", type = "ProjectApprovalEstimateExpenseSubject", subType = "批量编辑科目概算金额", bizNo = "")
    public ResponseDTO<Boolean> editAmountBatch(@RequestBody  List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        Boolean rsp = projectApprovalEstimateExpenseSubjectService.editAmountBatch(list);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除概算科目
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除概算科目")
    @RequestMapping(value = "/subject/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【项目立项-概算】概算科目", type = "ProjectApprovalEstimateExpenseSubject", subType = "删除概算科目", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalEstimateExpenseSubjectService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除概算科目（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除概算科目（批量）")
    @RequestMapping(value = "/subject/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目立项-概算】概算科目", type = "ProjectApprovalEstimateExpenseSubject", subType = "批量删除概算科目", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateExpenseSubjectService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 引用概算模板
     * @param approvalId
     * @param estimateTemplateId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "引用概算模板")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】引用【项目立项-概算】模板", type = "ProjectApprovalEstimateExpenseSubject", subType = "引用概算模板", bizNo = "{{#approvalId}}")
    @RequestMapping(value = "/addTemplate", method = RequestMethod.POST)
    public ResponseDTO<Boolean> addTemplate(@RequestParam("approvalId") String approvalId, @RequestParam("estimateTemplateId") String estimateTemplateId) throws Exception {
        Boolean rsp =  projectApprovalEstimateExpenseSubjectService.addTemplate(approvalId, estimateTemplateId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 添加概算科目
     * @param projectApprovalEstimateExpenseSubjectDTOList
     * @param approvalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加概算科目")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】添加【项目立项-概算】概算科目", type = "ProjectApprovalEstimateExpenseSubject", subType = "添加概算科目", bizNo = "{{#approvalId}}")
    @RequestMapping(value = "/addExpenseSubject", method = RequestMethod.POST)
    public ResponseDTO<Boolean> addExpenseSubject(@RequestBody List<ProjectApprovalEstimateExpenseSubjectDTO> projectApprovalEstimateExpenseSubjectDTOList, @RequestParam("approvalId") String approvalId) throws Exception {
        Boolean rsp =  projectApprovalEstimateExpenseSubjectService.addExpenseSubject(projectApprovalEstimateExpenseSubjectDTOList, approvalId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表树
     *
     * @param approvalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表树")
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目立项-概算】树数据", type = "ProjectApprovalEstimateExpenseSubject", subType = "列表树", bizNo = "{{#approvalId}}")
    @RequestMapping(value = "/subject/tree/list", method = RequestMethod.POST)
    public ResponseDTO<List<ProjectApprovalEstimateExpenseSubjectVO>> getExpenseSubjectList(@RequestParam("approvalId") String approvalId) throws Exception {
        List<ProjectApprovalEstimateExpenseSubjectVO> rsp =  projectApprovalEstimateExpenseSubjectService.getExpenseSubjectList(approvalId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 科目计算
     * @param id
     * @param list
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "科目计算")
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目立项-概算】科目计算数据", type = "ProjectApprovalEstimateExpenseSubject", subType = "科目计算", bizNo = "{{#id}}")
    @RequestMapping(value = "/subject/calculate", method = RequestMethod.POST)
    public ResponseDTO<BigDecimal> calculate(@RequestParam("id") String id, @RequestBody List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        BigDecimal rsp =  projectApprovalEstimateExpenseSubjectService.calculate(id, list);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 科目批量计算
     * @param approvalId
     * @param list
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "科目批量计算")
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目立项-概算】科目批量计算数据", type = "ProjectApprovalEstimateExpenseSubject", subType = "科目批量计算", bizNo = "{{#approvalId}}")
    @RequestMapping(value = "/subject/calculate/batch", method = RequestMethod.POST)
    public ResponseDTO<List<ProjectApprovalEstimateExpenseSubjectVO>> calculateBatch(@RequestParam("approvalId") String approvalId, @RequestBody List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        List<ProjectApprovalEstimateExpenseSubjectVO> rsp =  projectApprovalEstimateExpenseSubjectService.calculateBatch(approvalId, list);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 科目汇总
     *
     * @param approvalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "科目汇总")
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目立项-概算】科目汇总数据", type = "ProjectApprovalEstimateExpenseSubject", subType = "科目汇总", bizNo = "{{#approvalId}}")
    @RequestMapping(value = "/subject/summary", method = RequestMethod.GET)
    public ResponseDTO<SubjectSummaryVO> subjectSummary(@RequestParam("approvalId") String approvalId) throws Exception {
        SubjectSummaryVO rsp =  projectApprovalEstimateExpenseSubjectService.subjectSummary(approvalId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量添加概算物料
     * @param projectApprovalEstimateMaterialCreateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量添加概算物料")
    @RequestMapping(value = "/material/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量添加【项目立项-概算】概算物料", type = "ProjectApprovalEstimateMaterial", subType = "批量添加概算物料", bizNo = "")
    public ResponseDTO<Boolean> createMaterial(@RequestBody @Validated ProjectApprovalEstimateMaterialCreateDTO projectApprovalEstimateMaterialCreateDTO) throws Exception {
        Boolean rsp = projectApprovalEstimateMaterialService.createBatch(projectApprovalEstimateMaterialCreateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 批量编辑概算物料
     * @param projectApprovalEstimateMaterialDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量编辑概算物料")
    @RequestMapping(value = "/material/editAmount", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量编辑【项目立项-概算】概算物料", type = "ProjectApprovalEstimateMaterial", subType = "批量编辑概算物料", bizNo = "")
    public ResponseDTO<Boolean> editMaterialAmountBatch(@RequestBody List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList) throws Exception {
        Boolean rsp = projectApprovalEstimateMaterialService.editAmountBatch(projectApprovalEstimateMaterialDTOList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量删除概算物料
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量删除概算物料")
    @RequestMapping(value = "/material/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目立项-概算】概算物料", type = "ProjectApprovalEstimateMaterial", subType = "批量删除概算物料", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeMaterial(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateMaterialService.remove(ids);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取概算物料列表
     *
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取概算物料列表")
    @RequestMapping(value = "/material/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】概算物料列表", type = "ProjectApprovalEstimateMaterial", subType = "获取概算物料列表", bizNo = "{{#projectApprovalId}}")
    public ResponseDTO<ProjectApprovalEstimateVO> getMaterialList(@RequestParam("projectApprovalId") String projectApprovalId) throws Exception {
        ProjectApprovalEstimateVO rsp = projectApprovalEstimateMaterialService.getMaterialList(projectApprovalId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 概算物料改配
     *
     * @param changeMaterialDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "概算物料改配")
    @RequestMapping(value = "/material/change", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【项目立项-概算】概算物料改配", type = "ProjectApprovalEstimateMaterial", subType = "概算物料改配", bizNo = "")
    public ResponseDTO<ProjectApprovalEstimateMaterialVO> changeMaterial(@RequestBody @Validated ChangeMaterialDTO changeMaterialDTO) throws Exception {
        ProjectApprovalEstimateMaterialVO rsp = projectApprovalEstimateMaterialService.changeMaterial(changeMaterialDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 同步价格
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "同步价格")
    @RequestMapping(value = "/material/syncPrice", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【项目立项-概算】概算物料同步价格", type = "ProjectApprovalEstimateMaterial", subType = "概算物料同步价格", bizNo = "")
    public ResponseDTO<Boolean> syncMaterialPrice(@RequestParam("projectApprovalId") String projectApprovalId) throws Exception {
        Boolean rsp = projectApprovalEstimateMaterialService.syncMaterialPrice(projectApprovalId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量新增概算内外部试验数据
     * @param projectApprovalEstimateInterOutTrialFeeCreateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增概算内外部试验数据")
    @RequestMapping(value = "/interOutTrial/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【项目立项-概算】概算内外部试验数据", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "批量新增概算内外部试验数据", bizNo = "")
    public ResponseDTO<Boolean> createTrialBatch(@RequestBody @Validated ProjectApprovalEstimateInterOutTrialFeeCreateDTO projectApprovalEstimateInterOutTrialFeeCreateDTO) throws Exception {
        Boolean rsp = projectApprovalEstimateInterOutTrialFeeService.createBatch(projectApprovalEstimateInterOutTrialFeeCreateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量编辑概算内外部试验费用
     * @param projectApprovalEstimateInterOutTrialFeeDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量编辑概算内外部试验费用")
    @RequestMapping(value = "/interOutTrial/editAmount", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量编辑【项目立项-概算】概算内外部试验费用", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "批量编辑概算内外部试验费用", bizNo = "")
    public ResponseDTO<Boolean> editTrialAmountBatch(@RequestBody List<ProjectApprovalEstimateInterOutTrialFeeDTO> projectApprovalEstimateInterOutTrialFeeDTOList) throws Exception {
        Boolean rsp = projectApprovalEstimateInterOutTrialFeeService.editAmountBatch(projectApprovalEstimateInterOutTrialFeeDTOList);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "批量删除概算内外部试验费用")
    @RequestMapping(value = "/interOutTrial/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目立项-概算】概算内外部试验费用", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "批量删除概算内外部试验费用", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> removeTrial(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateInterOutTrialFeeService.remove(ids);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取概算内外部试验列表")
    @RequestMapping(value = "/interOutTrial/editAmount", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】概算内外部试验列表", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "获取概算内外部试验列表", bizNo = "{{#projectApprovalId}}")
    public ResponseDTO<ProjectApprovalEstimateVO> getTrialListByType(@RequestParam("projectApprovalId") String projectApprovalId, @RequestParam("type") String type) throws Exception {
        ProjectApprovalEstimateVO rsp = projectApprovalEstimateInterOutTrialFeeService.getListByType(projectApprovalId, type);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 批量新增工资及劳务费编制数据
     * @param projectApprovalEstimateLaborFeeDTOList
     * @param projectApprovalId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增工资及劳务费编制数据")
    @RequestMapping(value = "/labor/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【项目立项-概算】工资及劳务费编制数据", type = "ProjectApprovalEstimateLaborFee", subType = "批量新增工资及劳务费编制数据", bizNo = "")
    public ResponseDTO<Boolean> createLaborBatch(@RequestBody List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList,
                                                 @RequestParam("projectApprovalId") String projectApprovalId) throws Exception {
        Boolean rsp = projectApprovalEstimateLaborFeeService.createBatch(projectApprovalEstimateLaborFeeDTOList, projectApprovalId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量编辑概算内外部试验费用
     * @param projectApprovalEstimateLaborFeeDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量编辑概算内外部试验费用")
    @RequestMapping(value = "/labor/editAmount", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量编辑【项目立项-概算】概算内外部试验费用", type = "ProjectApprovalEstimateLaborFee", subType = "批量编辑概算内外部试验费用", bizNo = "")
    public ResponseDTO<Boolean> editLaborAmountBatch(@RequestBody List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList) throws Exception {
        Boolean rsp = projectApprovalEstimateLaborFeeService.editAmountBatch(projectApprovalEstimateLaborFeeDTOList);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "批量删除概算内外部试验费用")
    @RequestMapping(value = "/labor/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目立项-概算】概算内外部试验费用", type = "ProjectApprovalEstimateLaborFee", subType = "批量删除概算内外部试验费用", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> removeLabor(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateLaborFeeService.remove(ids);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取工资及劳务费编制数据列表")
    @RequestMapping(value = "/labor/editAmount", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】工资及劳务费编制数据列表", type = "ProjectApprovalEstimateLaborFee", subType = "获取工资及劳务费编制数据列表", bizNo = "{{#projectApprovalId}}")
    public ResponseDTO<ProjectApprovalEstimateVO> getLaborList(@RequestParam("projectApprovalId") String projectApprovalId) throws Exception {
        ProjectApprovalEstimateVO rsp = projectApprovalEstimateLaborFeeService.getLaborList(projectApprovalId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取概算内外部试验费用")
    @RequestMapping(value = "/getEstimatPage", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】概算内外部试验费用", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "获取概算内外部试验费用", bizNo = "")
    public ResponseDTO<Page<InterOutTrialBasicDataVO>> getEstimatPage(@RequestBody Page<InterOutTrialBasicDataDTO> interOutTrialBasicDataDTOPage) throws Exception {
        Page<InterOutTrialBasicDataVO> page= pasFeignService.getUsePage(interOutTrialBasicDataDTOPage).getResult();
        return new ResponseDTO<>(page);
    }

    @ApiOperation(value = "获取产品物料")
    @RequestMapping(value = "/getProductEstimateMaterialPage", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】产品物料", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "获取产品物料", bizNo = "")
    public ResponseDTO<Page<BasicMaterialsVO>> getAllTreePage(@RequestBody Page<BasicMaterialsDTO> basicMaterialsDTOPage) throws Exception {
        Page<BasicMaterialsVO> page= materialsApiService.pages(basicMaterialsDTOPage);
        return new ResponseDTO<>(page);
    }

    @ApiOperation(value = "获取产品分页")
    @RequestMapping(value = "/getProductlPage", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目立项-概算】产品分页", type = "ProjectApprovalEstimateInterOutTrialFee", subType = "获取产品分页", bizNo = "")
    public ResponseDTO<Page<com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO>> getPage(@RequestBody Page<com.chinasie.orion.pdm.api.domain.dto.ProductEstimateMaterialDTO> productEstimateMaterialPage) throws Exception {
        Page<com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO> page= productApiService.getPage(productEstimateMaterialPage);
        return new ResponseDTO<>(page);
    }

}
