import Api from '/@/api';

/**
 * 同步立项&策划信息
 * @param id 项目id
 */
export const syncApprovalById = (id) => new Api(`/pms/project/syncApproval/${id}`).fetch('', '', 'GET');
/**
 * 申请PLM产品编码
 * @param id 项目id
 */
export const applyPLMById = (id) => new Api(`/pms/project/applyPLM/${id}`).fetch('', '', 'GET');

/**
 * 启动项目
 * @param id 项目id
 */
export const startById = (id) => new Api(`/pms/project/start/${id}`).fetch('', '', 'GET');

/**
 * 关闭项目
 * @param id 项目id
 */
export const closeById = (id) => new Api(`/pms/project/close/${id}`).fetch('', '', 'GET');
