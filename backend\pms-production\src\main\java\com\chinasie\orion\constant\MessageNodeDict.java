package com.chinasie.orion.constant;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MessageNodeDict {

    /**
     * 大修重点项目选择通知
     */
    public static String NODE_REPAIR_IMPORTANT = "NODE_REPAIR_IMPROTANT";

    /**
     * 大修-每日任务进度通知
     */
    public static String NODE_MAJOR_PROCESS = "NODE_MAJOR_PROCESS";

    /**
     * 每日任务工作进展反馈
     */
    public static String NODE_JOB_PROCESS_FB = "NODE_JOB_PROCESS_FB";

    /**
     * 大修日常培训通知
     */
    public static String NODE_REPAIR_TRAIN = "NODE_REPAIR_TRAIN";

    /**
     * 重大项目日常反馈通知
     */
    public static String NODE_PROJECT_PROGRESS = "NODE_PROJECT_PROGRESS";

    /**
     * 固定资产鉴定提醒通知
     */
    public static String NODE_FIXED_NOTIFY = "NODE_FIXED_NOTIFY";

    /**
     * 人员证书到期通知
     */
    public static String NODE_PERSON_VERIFICATION = "NODE_PERSON_VERIFICATION";

    /**
     * 人员岗位授权到期通知
     */
    public static String NODE_POSITION_AUTHORIZE = "NODE_POSITION_AUTHORIZE";

    /**
     * 重大项目管理进展更新
     */
    public static String NODE_IMPORTANT_PROGRESS = "NODE_IMPORTANT_PROGRESS";

    /**
     * 重大项目作业进展更新
     */
    public static String NODE_JOB_PROGRESS = "NODE_JOB_PROGRESS";


    /**
     *  行动项督办提醒
     */
    public static String NODE_ACTION_ITEM_SUPERVISE="NODE_ACTION_ITEM_SUPERVISE";

    /**
     *  行动项提醒
     */
    public static String NODE_ACTION_ITEM_REMIND="NODE_ACTION_ITEM_REMIND";
}

