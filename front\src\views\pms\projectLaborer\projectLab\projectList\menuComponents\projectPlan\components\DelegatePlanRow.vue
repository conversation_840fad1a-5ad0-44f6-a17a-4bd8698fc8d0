<script lang="ts" setup>
import {
  defineComponent, h, onMounted, Ref, ref, computed,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { useForm, BasicForm } from 'lyra-component-vue3';
const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const rspUserOptions:Ref<any[]> = ref([]); // 负责人Id
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [

    {
      field: 'rspUser',
      label: '负责人：',
      colProps: { span: 12 },
      component: 'Select',
      rules: [
        {
          required: true,
          type: 'string',
          message: '请选择负责人',
        },
      ],
      componentProps: {
        allowClear: true,
        options: computed(() => rspUserOptions.value),
      },
    },
    {
      field: 'reasonTransfer',
      component: 'InputTextArea',
      label: '转办原因',
      colProps: {
        span: 24,
      },
      required: true,
      componentProps: {
        maxlength: 500,
        placeholder: '请输入原因',
        rows: 4,
      },
    },
  ],
});
onMounted(async () => {
  rspUserOptions.value = await getUserListByProjectId(props.record.projectId);
});

function getUserListByProjectId(projectId) {
  const url = `/pms/project-role-user/${projectId}/user/list`;
  return new Api(url).fetch('', '', 'GET')
    .then((res) => res.map((item) => ({
      value: item.id,
      label: item.name,
    })));
}
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    let itemData = rspUserOptions.value.find((item) => item.value === formData.rspUser);
    formData.rspSubDept = itemData.deptId;
    formData.rspSubDeptName = itemData.deptName;
    formData.id = props.record.id;
    await new Api('/pms').fetch(formData, 'projectScheme/transfer', 'PUT');
    message.success('任务转办成功');
  },
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
