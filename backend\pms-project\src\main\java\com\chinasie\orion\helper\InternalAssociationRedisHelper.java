package com.chinasie.orion.helper;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.entity.ProjectInternalAssociation;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.service.ProjectInternalAssociationService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yk
 * @date: 2023/11/17 10:12
 * @description:
 */
@Component
public class InternalAssociationRedisHelper {
    private static final Logger log = LoggerFactory.getLogger(InternalAssociationRedisHelper.class);

    private final String CACHE_INTERNAL_ASSOCIATION_KEY = "pms::internalAssociation";

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectInternalAssociationService projectInternalAssociationService;

    /**
     * 从缓存获取关联对象数据.
     *
     * @param ids
     * @return
     */
    private List<ProjectInternalAssociationRedisVO> getCachedInternalAssociationEntity(List<String> ids) {
        if(CollectionUtils.isBlank(ids)){
            return null;
        }
        List<ProjectInternalAssociationRedisVO> projectInternalAssociationRedisVOS = orionJ2CacheService.list(CACHE_INTERNAL_ASSOCIATION_KEY,ids, ProjectInternalAssociationRedisVO ::new);
        return projectInternalAssociationRedisVOS;
    }

    /**
     * 从缓存获取关联对象数据.
     *
     * @param id
     * @return
     */
    private ProjectInternalAssociationRedisVO getCachedInternalAssociationEntity(String id) {
        String format = String.format(CACHE_INTERNAL_ASSOCIATION_KEY, id);
        return orionJ2CacheService.get(CACHE_INTERNAL_ASSOCIATION_KEY,id, ProjectInternalAssociationRedisVO ::new);
    }

    public List<ProjectInternalAssociationRedisVO> queryForEntityByIds(List<String> ids) throws Exception {
        if(CollectionUtils.isBlank(ids)){
            return null;
        }
        List<ProjectInternalAssociationRedisVO> internalAssociationRedisVOList = getCachedInternalAssociationEntity(ids);
        List<String> idList = new ArrayList<>(ids);
        if(!CollectionUtils.isBlank(internalAssociationRedisVOList)){
            List<String> cacheIds = internalAssociationRedisVOList.stream().map(ProjectInternalAssociationRedisVO :: getId).collect(Collectors.toList());
            idList.removeAll(cacheIds);
        }
        if(CollectionUtils.isBlank(idList)){
            return internalAssociationRedisVOList;
        }
        List<ProjectInternalAssociationRedisVO> result = new ArrayList<>();

        List<ProjectInternalAssociation> projectInternalAssociations = projectInternalAssociationService.listByIds(idList);
        if(!CollectionUtils.isBlank(projectInternalAssociations)){
            List<ProjectInternalAssociationRedisVO> redisVOList = cacheInternalAssociationEntity(projectInternalAssociations);
            result.addAll(redisVOList);
        }
        result.addAll(internalAssociationRedisVOList);

        return result;
    }

    public ProjectInternalAssociationRedisVO queryForEntityById(String id) throws Exception {
        if(!StringUtils.hasText(id)){
            return null;
        }
        ProjectInternalAssociationRedisVO internalAssociation = getCachedInternalAssociationEntity(id);
        if (internalAssociation != null) {
            return internalAssociation;
        }

        ProjectInternalAssociation projectInternalAssociation = projectInternalAssociationService.getById(id);
        ProjectInternalAssociationRedisVO projectInternalAssociationRedisVO = cacheInternalAssociationEntity(projectInternalAssociation);
        return projectInternalAssociationRedisVO;
    }
    /**
     * 缓存projectInternalAssociation数据.
     *
     * @param projectInternalAssociation
     */
    public ProjectInternalAssociationRedisVO cacheInternalAssociationEntity(ProjectInternalAssociation projectInternalAssociation) {
        if (projectInternalAssociation == null) {
            return null;
        }

        // 60天基础上加上一个随机值, 避免数据同时失效.
        long expireTime = cnternalAssociationCacheExpireTime();
        ProjectInternalAssociationRedisVO rsp1 = BeanCopyUtils.convertTo(projectInternalAssociation,ProjectInternalAssociationRedisVO::new);
        orionJ2CacheService.set(CACHE_INTERNAL_ASSOCIATION_KEY,rsp1.getId(),rsp1,expireTime);
        return rsp1;
    }

    /**
     * 缓存projectInternalAssociation数据.
     *
     * @param projectInternalAssociations
     */
    private List<ProjectInternalAssociationRedisVO> cacheInternalAssociationEntity(List<ProjectInternalAssociation> projectInternalAssociations) {
        if (CollectionUtils.isBlank(projectInternalAssociations)) {
            return null;
        }
        // 60天基础上加上一个随机值, 避免数据同时失效.
        long expireTime = cnternalAssociationCacheExpireTime();
       List<ProjectInternalAssociationRedisVO>  result =   BeanCopyUtils.convertListTo(projectInternalAssociations, ProjectInternalAssociationRedisVO ::new);
       Map<String,Object> associationRedisVOMap = result.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO :: getId, Function.identity()));
       orionJ2CacheService.set(CACHE_INTERNAL_ASSOCIATION_KEY,associationRedisVOMap,expireTime);
       return  result;
    }

    private int cnternalAssociationCacheExpireTime() {
        return 24 * 60 * 60 * 60 + 24 * 8 * new Random().nextInt(10);
    }
}
