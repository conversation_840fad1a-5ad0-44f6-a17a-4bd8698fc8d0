package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectRoleUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 计划变更时，消息推送处理
 *
 * 给新责任人发送待办；
 * 给项目责任人发提醒、项目经理发送提醒；
 *
 */
@Slf4j
@Component("editSchemeSendMsgAdapter")
public class EditSchemeSendMsgAdapter extends SendMessageCommonAdapter {

    /**
     * 项目计划详情页
     */
    private final static String JUMP_URL = "/pms/ProPlanDetails/%s";

    @Resource
    private ProjectRoleUserService roleUserService;

    @Resource
    private ProjectProperties projectProperties;

    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        List<ProjectScheme> projectSchemeList = schemeMsgDTO.getProjectSchemeList();
        for (ProjectScheme item : projectSchemeList) {
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            List<String> recipientIds = recipientIds(item);
            Map<String, Object> messageMap = new HashMap<>(2);
            messageMap.put("$schemeName$", item.getName());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder()
                            .put("flowType", TYPE_FLOW_TYPE_MAP.get(MsgBusinessTypeEnum.EDIT))
                            .put("parentId",parentId)
                            .put("id",item.getId())
                            .put("type", "plan")
                            .put("projectId", item.getProjectId())
                            .build()))
                    .businessId(item.getId())
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.EDIT))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .titleMap(messageMap)
                    .messageUrl(String.format(JUMP_URL, item.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(recipientIds)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            messageDTOList.add(sendMsc);
        }
        return messageDTOList;
    }


    /**
     * 计划负责人为项目成员时：发送到项目负责人和计划负责人
     * 计划负责人为项目负责人时，发送消息到项目经理
     *
     * @param projectScheme
     * @return
     */
    private List<String> recipientIds(ProjectScheme projectScheme) throws Exception {
        List<String> recipientIds = CollUtil.toList();
        String rspUser = projectScheme.getRspUser();
        recipientIds.add(rspUser);
        List<ProjectRoleUser> pmLeader = roleUserService.findUserListByCode(projectScheme.getProjectId(), projectProperties.getInitProjectLeader());
        if (CollUtil.isEmpty(pmLeader)) {
            List<String> pmLeaderId = pmLeader.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
            if (pmLeaderId.contains(rspUser)) {
                List<ProjectRoleUser> pmList = roleUserService.findUserListByCode(projectScheme.getProjectId(), projectProperties.getInitManagerRoleCode());
                if (CollUtil.isNotEmpty(pmList)) {
                    recipientIds.addAll(pmList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList()));
                }
            } else {
                recipientIds.addAll(pmLeaderId);
            }
        }
        String participantUsers = projectScheme.getParticipantUsers();
        if(StringUtils.hasText(participantUsers)){
            recipientIds.addAll(List.of(participantUsers.split(",")));
        }
        return recipientIds;
    }
}
