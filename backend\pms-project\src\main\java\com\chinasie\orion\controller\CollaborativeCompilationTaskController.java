package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CollaborativeCompilationTaskDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.UrgePlanRequestDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskFallBackDTO;
import com.chinasie.orion.domain.dto.collaborativecompilationtask.TaskIssueDTO;
import com.chinasie.orion.domain.dto.projectscheme.FallBackDTO;
import com.chinasie.orion.domain.dto.projectscheme.IssueDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.vo.CollaborativeCompilationTaskVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.service.CollaborativeCompilationTaskService;
import com.mzt.logapi.starter.annotation.LogRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * CollaborativeCompilationTask 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
@RestController
@RequestMapping("/collaborativeCompilationTask")
@Api(tags = "协同编制任务表")
public class  CollaborativeCompilationTaskController  {

    @Autowired
    private CollaborativeCompilationTaskService collaborativeCompilationTaskService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取详情【{{#collaborativeCompilationTaskDTO.name}}】", type = "协同编制任务表", subType = "获取详情", bizNo = "{{#id}}")
    public ResponseDTO<CollaborativeCompilationTaskVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        CollaborativeCompilationTaskVO rsp = collaborativeCompilationTaskService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param collaborativeCompilationTaskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#collaborativeCompilationTaskDTO.name}}】", type = "协同编制任务表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        String rsp =  collaborativeCompilationTaskService.create(collaborativeCompilationTaskDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param collaborativeCompilationTaskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#collaborativeCompilationTaskDTO.name}}】", type = "协同编制任务表", subType = "编辑", bizNo = "{{#collaborativeCompilationTaskDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        Boolean rsp = collaborativeCompilationTaskService.edit(collaborativeCompilationTaskDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "协同编制任务表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = collaborativeCompilationTaskService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "协同编制任务表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = collaborativeCompilationTaskService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "协同编制任务表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<CollaborativeCompilationTaskVO>> pages(@RequestBody Page<CollaborativeCompilationTaskDTO> pageRequest) throws Exception {
        Page<CollaborativeCompilationTaskVO> rsp =  collaborativeCompilationTaskService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("获取协同任务任务")
    @PostMapping(value = "/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取协同任务任务", type = "协同编制任务表", subType = "获取协同任务任务", bizNo = "")
    public ResponseDTO<List<ProjectScheme>> projectSchemeList(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.getList(collaborativeCompilationTaskDTO));
    }

    @ApiOperation("上移")
    @PutMapping(value = "/up/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】上移", type = "协同编制任务", subType = "上移(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> up(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.up(id));
    }

    @ApiOperation("下移")
    @PutMapping(value = "/down/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】下移", type = "协同编制任务", subType = "下移(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> down(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.down(id));
    }

    @ApiOperation("置顶")
    @PutMapping(value = "/top/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】置顶", type = "协同编制任务", subType = "置顶(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> top(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.top(id));
    }

    @ApiOperation("取消置顶")
    @PutMapping(value = "/unTop/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】取消置顶", type = "协同编制任务", subType = "取消置顶(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> unTop(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.unTop(id));
    }

    @ApiOperation("任务下发")
    @PutMapping(value = "/issue")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】任务下发", type = "协同编制任务", subType = "任务下发(过程记录)", bizNo = "{{#issueDTO.id}}")
    })
    public ResponseDTO<Boolean> issue(@RequestBody TaskIssueDTO issueDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.issue(issueDTO));
    }

    @ApiOperation("任务执行完成")
    @PutMapping(value = "/finish")
    @LogRecords({

    })
    public ResponseDTO<Boolean> finish(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.finish(collaborativeCompilationTaskDTO));
    }

    @ApiOperation("任务催办")
    @PostMapping(value = "/urgePlan")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】任务催办", type = "协同编制任务", subType = "任务催办(过程记录)", bizNo = "{{#urgePlanRequestDTO.projectSchemeId}}")
    })
    public ResponseDTO<Boolean> urgePlan(@RequestBody UrgePlanRequestDTO urgePlanRequestDTO) throws Exception {
        Boolean rsp = collaborativeCompilationTaskService.urgePlan(urgePlanRequestDTO);
        return ResponseDTO.success(rsp);
    }



    @ApiOperation("开始执行")
    @LogRecord(success = "【{USER{#logUserId}}】更新实际执行时间", type = "协同编制任务", subType = "更新实际开始时间(过程记录)",bizNo = "{{#id}}")
    @PutMapping(value = "/projectScheme/actualBeginTime/update")
    ResponseDTO<Boolean> updateActualBeginTime(@RequestParam("id") String id){
        Boolean result = collaborativeCompilationTaskService.updateActualBeginTime(id);
        return new ResponseDTO<>(result);
    }

    @ApiOperation("任务退回")
    @LogRecord(success = "【{USER{#logUserId}}】协同编制任务退回", type = "协同编制任务", subType = "协同编制任务退回(过程记录)", bizNo = "{{#fallBackDTO.id}}")
    @PostMapping(value = "/fallback")
    ResponseDTO<Boolean> schemeFallback(@RequestBody TaskFallBackDTO fallBackDTO){
        Boolean result = collaborativeCompilationTaskService.taskFallback(fallBackDTO);
        return new ResponseDTO<>(result);
    }

    @ApiOperation("任务撤回")
    @GetMapping(value = "/revocation")
    @LogRecord(success = "【{USER{#logUserId}}】任务撤回", type = "协同编制任务", subType = "任务撤回(过程记录)", bizNo = "{{#id}}")
    public ResponseDTO<String> revocation(@RequestParam String id) throws Exception {
        String rsp = collaborativeCompilationTaskService.revocation(id);
        return ResponseDTO.success(rsp);
    }


    @ApiOperation("完成确认")
    @PutMapping(value = "/completeConfirmation")
    @LogRecord(success = "【{USER{#logUserId}}】完成确认", type = "协同编制任务", subType = "完成确认", bizNo = "")
    public ResponseDTO<Boolean> completeConfirmation(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.completeConfirmation(collaborativeCompilationTaskDTO));
    }


    @ApiOperation("引用模板")
    @LogRecord(success = "【{USER{#logUserId}}】引用模板", type = "协同编制任务", subType = "引用模板",bizNo = "{{#id}}")
    @PutMapping(value = "/getDocument")
    ResponseDTO<Boolean> getDocument(@RequestParam("modelId") String modelId,@RequestParam("approvalId") String approvalId) throws Exception {
        Boolean result = collaborativeCompilationTaskService.getDocument(modelId,approvalId);
        return new ResponseDTO<>(result);
    }

    @ApiOperation("任务转办")
    @PutMapping(value = "/transfer")
    @LogRecord(success = "【{USER{#logUserId}}】任务转办", type = "协同编制任务", subType = "任务转办(过程记录)", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> transfer(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.transfer(collaborativeCompilationTaskDTO));
    }

    @ApiOperation("获取人员")
    @GetMapping(value = "/getUser")
    @LogRecord(success = "【{USER{#logUserId}}】获取人员", type = "协同编制任务", subType = "获取人员", bizNo = "")
    public ResponseDTO<List<UserVO>> getUser() throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.getUser());
    }

    @ApiOperation("获取状态")
    @GetMapping(value = "/getStatus")
    @LogRecord(success = "【{USER{#logUserId}}】获取状态", type = "协同编制任务", subType = "获取状态", bizNo = "")
    public ResponseDTO<List<UserVO>> getStatus() throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.getStatus());
    }

    @ApiOperation("获取内容")
    @GetMapping(value = "/getContent")
    @LogRecord(success = "【{USER{#logUserId}}】获取内容", type = "协同编制任务", subType = "获取内容", bizNo = "")
    public ResponseDTO<List<UserVO>> getContent(@RequestParam String id) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.getContent(id));
    }

    @ApiOperation("修改任务内容")
    @PutMapping(value = "/updateContent")
    @LogRecord(success = "【{USER{#logUserId}}】修改任务内容", type = "协同编制任务", subType = "修改任务内容", bizNo = "")
    public ResponseDTO<Boolean> updateContent(@RequestBody CollaborativeCompilationTaskDTO collaborativeCompilationTaskDTO) throws Exception {
        return ResponseDTO.success(collaborativeCompilationTaskService.editContent(collaborativeCompilationTaskDTO));
    }

}
