package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.entity.SupplierContact;
import com.chinasie.orion.management.domain.vo.SupplierContactVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.management.domain.dto.SupplierBankDTO;
import com.chinasie.orion.management.domain.entity.SupplierBank;
import com.chinasie.orion.management.domain.vo.SupplierBankVO;
import com.chinasie.orion.management.repository.SupplierBankMapper;
import com.chinasie.orion.management.service.SupplierBankService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * SupplierBank 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierBankServiceImpl extends OrionBaseServiceImpl<SupplierBankMapper, SupplierBank> implements SupplierBankService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierBankVO detail(String id, String pageCode) throws Exception {
        SupplierBank supplierBank = this.getById(id);
        SupplierBankVO result = BeanCopyUtils.convertTo(supplierBank, SupplierBankVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierBankDTO
     */
    @Override
    public String create(SupplierBankDTO supplierBankDTO) throws Exception {
        SupplierBank supplierBank = BeanCopyUtils.convertTo(supplierBankDTO, SupplierBank::new);
        this.save(supplierBank);

        String rsp = supplierBank.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierBankDTO
     */
    @Override
    public Boolean edit(SupplierBankDTO supplierBankDTO) throws Exception {
        SupplierBank supplierBank = BeanCopyUtils.convertTo(supplierBankDTO, SupplierBank::new);

        this.updateById(supplierBank);

        String rsp = supplierBank.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierBankVO> pages(String mainTableId, Page<SupplierBankDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierBank> condition = new LambdaQueryWrapperX<>(SupplierBank.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierBank::getCreateTime);

        condition.eq(SupplierBank::getMainTableId, mainTableId);

        Page<SupplierBank> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierBank::new));

        PageResult<SupplierBank> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierBankVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierBankVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierBankVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "银行信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierBankDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierBankExcelListener excelReadListener = new SupplierBankExcelListener();
        EasyExcel.read(inputStream, SupplierBankDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierBankDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("银行信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierBank> supplierBankes = BeanCopyUtils.convertListTo(dtoS, SupplierBank::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierBank-import::id", importId, supplierBankes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierBank> supplierBankes = (List<SupplierBank>) orionJ2CacheService.get("ncf::SupplierBank-import::id", importId);
        log.info("银行信息导入的入库数据={}", JSONUtil.toJsonStr(supplierBankes));

        this.saveBatch(supplierBankes);
        orionJ2CacheService.delete("ncf::SupplierBank-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierBank-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierBank> condition = new LambdaQueryWrapperX<>(SupplierBank.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SupplierBank::getCreateTime);
        List<SupplierBank> supplierBankes = this.list(condition);

        List<SupplierBankDTO> dtos = BeanCopyUtils.convertListTo(supplierBankes, SupplierBankDTO::new);

        String fileName = "银行信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierBankDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierBankVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public Page<SupplierBankVO> getByCode(Page<SupplierBankDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierBank> condition = new LambdaQueryWrapperX<>(SupplierBank.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierBank::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }
        condition.eq(SupplierBank::getSupplierCode, pageRequest.getQuery().getSupplierCode());

        Page<SupplierBank> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierBank::new));

        PageResult<SupplierBank> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierBankVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierBankVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierBankVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    public static class SupplierBankExcelListener extends AnalysisEventListener<SupplierBankDTO> {

        private final List<SupplierBankDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierBankDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierBankDTO> getData() {
            return data;
        }
    }


}
