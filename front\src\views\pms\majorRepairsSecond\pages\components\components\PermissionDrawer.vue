<script setup lang="ts">
import {
  Dropdown, List, ListItem, Menu, MenuItem,
} from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { BasicButton, openSelectUserModal } from 'lyra-component-vue3';
import {
  computed, onMounted, ref, unref,
} from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any>;
}>();

onMounted(() => {
  getAuthData();
});

const roleDtoList = ref<Array<{
  permissionCode: 'read' | 'write'
  objectValue: string
  objectName: string
  objectCode: string
}>>([]);
const loadingRef = ref<boolean>(false);

async function getAuthData() {
  loadingRef.value = true;
  try {
    const result = await new Api(`/pms/commonDataAuthRole/data/auth/roles/dataId?dataId=${props?.record?.data?.id}`).fetch('', '', 'POST');
    roleDtoList.value = result?.User?.map((item) => ({
      objectValue: item.objectValue,
      objectName: item.objectName,
      objectCode: item.objectCode,
      permissionCode: item.permissionCode,
    })) || [];
  } finally {
    loadingRef.value = false;
  }
}

function handleSelectUser() {
  openSelectUserModal(unref(roleDtoList).map((item: Record<string, any>) => ({
    id: item.objectValue,
    name: item.objectName,
    code: item.objectCode,
    permissionCode: item.permissionCode,
  })), {
    okHandle(user) {
      roleDtoList.value = user.map((item) => ({
        objectValue: item.id,
        objectName: item.name,
        objectCode: item.code,
        permissionCode: item.permissionCode || 'read',
      }));
    },
  });
}

function handleMenuClick({ key }, item) {
  item.permissionCode = key;
}

const authParams = computed(() => ({
  authObject: 'User',
  businessType: 'PRODUCTION',
  dataId: props?.record?.data?.id,
  dataType: props?.record?.data?.dataType,
  roleDtoList: unref(roleDtoList),
}));

function handleRemove(item) {
  roleDtoList.value = unref(roleDtoList).filter((i) => i.objectValue !== item.objectValue);
}

function getPopupContainer(triggerNode) {
  return triggerNode?.parentNode;
}

defineExpose({
  confirm() {
    return new Api('/pms/commonDataAuthRole/add').fetch(unref(authParams), '', 'POST');
  },
});
</script>

<template>
  <div
    v-loading="loadingRef"
    class="permission-wrapper"
  >
    <BasicButton
      block
      icon="sie-icon-tianjiaxinzeng"
      @click="handleSelectUser"
    >
      添加授权
    </BasicButton>
    <List
      class="pt20 pb20"
      item-layout="horizontal"
      :data-source="roleDtoList"
    >
      <template #renderItem="{ item }">
        <ListItem>
          <div class="flex flex-ver">
            <span>{{ item.objectName }}</span>
            <span class="desc-txt c33">{{ item.objectCode }}</span>
          </div>

          <Dropdown :getPopupContainer="getPopupContainer">
            <a
              class="ant-dropdown-link mr25"
              style="margin-left: auto"
              @click.prevent
            >
              {{ item.permissionCode === 'read' ? '仅浏览' : '可编辑' }}
              <DownOutlined />
            </a>
            <template #overlay>
              <Menu @click="handleMenuClick($event,item)">
                <MenuItem key="read">
                  <div class="flex flex-ver">
                    <span>仅浏览</span>
                    <span class="desc-txt">可浏览授权信息</span>
                  </div>
                </MenuItem>
                <MenuItem key="write">
                  <div class="flex flex-ver">
                    <span>可编辑</span>
                    <span class="desc-txt">可浏览、编辑授权信息</span>
                  </div>
                </MenuItem>
              </Menu>
            </template>
          </Dropdown>

          <a
            class="ant-dropdown-link"
            @click.prevent="handleRemove(item)"
          >
            移除
          </a>
        </ListItem>
      </template>
    </List>
  </div>
</template>

<style scoped lang="less">
.permission-wrapper {
  position: relative;
  padding: 12px 20px;
}

.desc-txt {
  font-size: 12px;
  color: #ccc;
}
</style>
