import type { GlobEnvConfig } from '/#/config';
// import process from 'process';

// @ts-ignore
import { warn } from '/@/utils/log';
// @ts-ignore
import pkg from '../../package.json';
// @ts-ignore
import { getConfigFileName } from '../../build/getConfigFileName';
import { useQiankun } from '/@/utils/qiankun';

// 编译环境
export enum EnvEnum {
  LOCAL = 'local', // 本地
  DEV = 'dev', // 开发
  TEST = 'test', // 测试
  DEMO = 'demo', // 演示
  PROD = 'prod' // 生产
}

// webpack模式
export enum WebpackModeEnum {
  // 开发模式
  DEVELOPMENT = 'development',
  // 生产模式
  PRODUCTION = 'production'
}

export function getEnvConfig() {
  return {
    WEBPACK_MODE: process.env.WEBPACK_MODE as WebpackModeEnum,
    NAME: process.env.NAME as string,
    ENV: process.env.ENV as EnvEnum,
    HOST: process.env.HOST as string,
    IS_VERIFY_POWER: process.env.IS_VERIFY_POWER as string,
  };
}

export function getCommonStoragePrefix() {
  const { VITE_GLOB_APP_SHORT_NAME } = getAppEnvConfig();
  return `${VITE_GLOB_APP_SHORT_NAME}__${getEnv()}`.toUpperCase();
}

// Generate cache key according to version
export function getStorageShortName() {
  return `${getCommonStoragePrefix()}${`__${pkg.version}`}__`.toUpperCase();
}

export function getAppEnvConfig() {
  const ENV_NAME = getConfigFileName();

  // const ENV = (getEnvConfig().DEV
  //   // eslint-disable-next-line operator-linebreak
  //   ? // Get the global configuration (the configuration will be extracted independently when packaging)
  //   (getEnvConfig() as unknown as GlobEnvConfig)
  //   : window[ENV_NAME as any]) as unknown as GlobEnvConfig;

  const ENV = {
    VITE_GLOB_APP_TITLE: '管理系统',
    VITE_GLOB_API_URL: '/api',
    VITE_GLOB_APP_SHORT_NAME: 'vue_vben_admin',
    VITE_GLOB_API_URL_PREFIX: '',
    VITE_GLOB_UPLOAD_URL: '/upload',
  };

  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
  } = ENV;

  if (!/^[a-zA-Z\_]*$/.test(VITE_GLOB_APP_SHORT_NAME)) {
    warn(
      'VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.',
    );
  }

  return {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_UPLOAD_URL,
  };
}

/**
 * 获取环境名称
 */
export function getEnv() {
  return getEnvConfig().ENV;
}

/**
 * 是否开发环境
 */
export function isDevMode() {
  // @ts-ignore
  return getEnvConfig().DEV === EnvEnum.DEV;
}

/**
 * 是否生产环境
 */
export function isTestMode() {
  return getEnvConfig().ENV === EnvEnum.TEST;
}

/**
 * 是否演示环境
 */
export function isDemoMode(): boolean {
  return getEnvConfig().ENV === EnvEnum.DEMO;
}

/**
 * 是否生产环境
 */
export function isProdMode(): boolean {
  return getEnvConfig().ENV === EnvEnum.PROD;
}

/**
 * webpack 模式
 */
export function getNodeEnv() {
  return getEnvConfig().WEBPACK_MODE;
}

/**
 * 是否webpack开发模式
 */
export function isDev() {
  return getNodeEnv() === WebpackModeEnum.DEVELOPMENT;
}

/**
 * 获取运行环境名称
 */
export function getEnvName() {
  return getEnvConfig().ENV;
}

/**
 * 获取是否验证权限
 */
export function isVerifyPower() {
  return useQiankun().mainEnv?.isVerifyPower() ?? getEnvConfig().IS_VERIFY_POWER !== 'false';
}

/**
 * 获取根服务地址
 */
export function getEnvLocation() {
  return process.env.LOCATION + (process.env.PORT ? `:${process.env.PORT}` : '');
}
