<script setup lang="ts">
import { STable } from '@surely-vue/table';
import dayjs from 'dayjs';

const props = defineProps<{
  commentList: any[]
  commentLoading: boolean
}>();

const columns = [
  {
    title: '节点名称',
    dataIndex: 'taskName',
    width: 100,
  },
  {
    title: '审批人/委托人',
    dataIndex: 'userName',
    width: 110,
  },
  {
    title: '工号',
    dataIndex: 'userCode',
    width: 100,
  },
  {
    title: '所级',
    dataIndex: 'deptName',
    width: 120,
  },
  {
    title: '任务到达时间',
    dataIndex: 'createTime',
    width: 150,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '任务完成时间',
    dataIndex: 'endTime',
    width: 150,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--';
    },
  },
  {
    title: '耗时',
    dataIndex: 'remainTime',
    width: 100,
  },
  {
    title: '流转方式',
    dataIndex: 'opertion',
    width: 100,
  },
  {
    title: '审批意见',
    dataIndex: 'suggestion',
  },
];
</script>

<template>
  <STable
    rowKey="id"
    size="small"
    :dataSource="commentList"
    :columns="columns"
    :loading="commentLoading"
    :pagination="false"
    :scroll="{y:300}"
  />
</template>

<style scoped lang="less">

</style>
