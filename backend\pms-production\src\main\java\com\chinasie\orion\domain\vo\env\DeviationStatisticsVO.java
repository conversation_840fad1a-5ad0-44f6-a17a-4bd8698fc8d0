package com.chinasie.orion.domain.vo.env;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/16/15:32
 * @description:
 */
@Data
public class DeviationStatisticsVO implements Serializable {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("唯一ID： 随机生成的ID")
    private String uniqueId;
    @ApiModelProperty("部门编号")
    private String deptCode;
    @ApiModelProperty("统计")
    private List<RowStatisticsVO> rowStatisticsVOList;
}
