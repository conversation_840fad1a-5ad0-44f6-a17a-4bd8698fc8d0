package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractClaim VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractClaimVO对象", description = "合同索赔信息表")
@Data
public class ContractClaimVO extends ObjectVO implements Serializable {

    /**
     * 索赔编号
     */
    @ApiModelProperty(value = "索赔编号")
    private String claimId;


    /**
     * 索赔标题
     */
    @ApiModelProperty(value = "索赔标题")
    private String claimTitle;


    /**
     * 索赔状态
     */
    @ApiModelProperty(value = "索赔状态")
    private String claimStatus;


    /**
     * 索赔方向
     */
    @ApiModelProperty(value = "索赔方向")
    private String claimDirection;


    /**
     * 索赔处理时间
     */
    @ApiModelProperty(value = "索赔处理时间")
    private Date claimProcessTime;


    /**
     * 索赔申请时间
     */
    @ApiModelProperty(value = "索赔申请时间")
    private Date claimRequestTime;


    /**
     * 累计索赔金额（含本次）
     */
    @ApiModelProperty(value = "累计索赔金额（含本次）")
    private BigDecimal cumulativeClaimAmount;


    /**
     * 总累计索赔占原合同价%
     */
    @ApiModelProperty(value = "总累计索赔占原合同价%")
    private String totalClaimPctOfOrigPrice;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String businessRspUserName;
}
