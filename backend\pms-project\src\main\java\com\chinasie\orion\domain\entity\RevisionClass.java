package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * RevisionClass 版本Entity对象
 *
 * <AUTHOR> sie
 * @since 2022-09-23
 */
@TableName(value = "pms_revision_class")
@ApiModel(value = "RevisionClass对象", description = "版本")
@Data
public class RevisionClass extends ObjectEntity {
    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    @TableField(value = "next_rev_id")
    private String nextRevId;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    @TableField(value = "rev_order")
    private Integer revOrder;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    @TableField(value = "rev_key")
    private String revKey;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    @TableField(value = "previous_rev_id")
    private String previousRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    @TableField(value = "rev_id")
    private String revId;

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    @TableField(value = "initial_rev_id")
    private String initialRevId;
}
