<template>
  <div
    v-loading="loading"
    class="p10 manHourMain"
  >
    <div class="title">
      <span>项目工时</span>
      <span class="sub-title">（共 {{ form.planCount }} 条任务）</span>
    </div>
    <div class="manHourContent">
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number">{{ form.manHour || 0 }}</span>
          <span>h</span>
        </div>
        <div class="sub-text">
          预估工时
        </div>
      </div>
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number">{{ form.realityManHour || 0 }}</span>
          <span>h</span>
        </div>
        <div class="sub-text">
          登记工时
        </div>
      </div>
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number">
            {{ form.manHourSchedule && parseInt(form.manHourSchedule * 100) }}
          </span>
          <span>%</span>
        </div>
        <div class="sub-text">
          工时进度
          <a-tooltip placement="top">
            <template #title>
              <span class="tooltip">工时进度=已登记工时/（已登记工时+剩余工时）×100%</span>
            </template>
            <InfoCircleOutlined />
          </a-tooltip>
        </div>
      </div>
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number"> {{ form.residueManHour || 0 }}</span>
          <span>h</span>
        </div>
        <div class="sub-text">
          剩余工时
        </div>
      </div>
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number">{{ form.deviationManHour || 0 }}</span>
          <span>h</span>
        </div>
        <div class="sub-text">
          预估偏差
          <a-tooltip placement="top">
            <template #title>
              <span class="tooltip">预估偏差=预估工时-（已登记工时+剩余工时）</span>
            </template>
            <InfoCircleOutlined />
          </a-tooltip>
        </div>
      </div>
      <div
        class="text-center floatItem"
      >
        <div>
          <span class="number">{{ form.deviationSchedule || 0 }}</span>
          <span>%</span>
        </div>
        <div class="sub-text">
          预估偏差率
          <a-tooltip placement="top">
            <template #title>
              <span class="tooltip">
                预估偏差率=[预估工时-（已登记工时+剩余工时）]/预估工时×100%
              </span>
            </template>
            <InfoCircleOutlined />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="mt20">
      <div class="sub-text">
        预估工时
      </div>
      <a-tooltip title="预估工时">
        <a-progress
          :percent="((form.manHour-(-form.deviationManHour))/form.manHour)*100"
          :show-info="false"
          stroke-linecap="square"
          :stroke-color="{
            '0%': '#448ef7',
            '100%': '#448ef7',
          }"
        />
      </a-tooltip>
    </div>
    <div class="mt10">
      <div class="sub-text">
        登记工时 + 剩余工时
      </div>
      <a-tooltip title="登记工时 + 剩余工时">
        <a-progress
          v-if="form.residueManHour!==0"
          :percent="(form.realityManHour/(form.realityManHour+form.residueManHour))*100"
          :show-info="false"
          trailColor="#ffb71f"
          :stroke-color="{
            '0%': '#56b282',
            '100%': '#56b282',
          }"
          stroke-linecap="square"
        />
        <a-progress
          v-else
          :percent="(form.realityManHour/(form.realityManHour+form.residueManHour))*100"
          :show-info="false"
          stroke-linecap="square"
        />
      </a-tooltip>
    </div>
    <div class="mt20 mb15 box">
      <div class="box-item mr5">
        <div class="color1 mr5" />预估工时
      </div>
      <div class="box-item mr5">
        <div class="color2 mr5" />登记工时
      </div>
      <div class="box-item mr5">
        <div class="color3 mr5" />剩余工时
      </div>
      <div class="box-item mr5">
        <div class="color4 mr5" />偏差工时
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Row, Col, Tooltip, Progress,
} from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import Api from '/@/api';
import { inject, reactive, toRefs } from 'vue';
export default {
  name: 'ManHour',
  components: {
    AProgress: Progress,
    InfoCircleOutlined,
    ATooltip: Tooltip,
    ARow: Row,
    ACol: Col,
  },
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      loading: false,
      form: {},
    });
    function init() {
      const url = `project-overview/manHour?projectId=${formData?.value?.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.form = res;
          state.loading = false;
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    init();
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
.manHourMain {
  .tooltip {
    font-size: 12px !important;
  }
  .manHourContent{
    overflow: auto;
    .floatItem{
      float: left;
      min-width: 33.333%;
      //padding: 0px 10px;
      box-sizing: border-box;
    }
  }

  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .sub-title {
    font-size: 12px;
    color: #686f8b;
  }

  .sub-text {
    font-size: 14px;
    color: #686f8b;
  }

  .number {
    font-family: 'DINAlternate-Bold', 'DIN Alternate Bold', 'DIN Alternate';
    font-weight: 700;
    font-style: normal;
    font-size: 36px;
    margin-right: 5px;
  }

  .box {
    overflow: auto;
    .box-item {
      width: 20%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 6px;
      min-width: 115px;
      float: left;
      height: 18px;
      margin-bottom: 5px;

      .color1 {
        display: inline-block;
        width: 30%;
        height: 63%;
        background: #448ef7;
        border-radius: 2px;
      }

      .color2 {
        display: inline-block;
        width: 30%;
        height: 63%;
        background: #56b282;
        border-radius: 2px;

      }

      .color3 {
        display: inline-block;
        width: 30%;
        height: 63%;
        background: #ffb71f;
        border-radius: 2px;

      }

      .color4 {
        display: inline-block;
        width: 30%;
        height: 63%;
        background: #f5f5f5;
        border-radius: 2px;

      }
    }
  }
}
</style>
