package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:40
 * @description:
 */

@ApiModel(value = "MajorRepairPlanVO对象", description = "大修计划")
@Data
public class MajorRepairPlanVO extends ObjectVO implements Serializable {

    /**
     * 大修轮次（全局唯一）
     */
    @ApiModelProperty(value = "大修轮次（全局唯一）")
    private String repairRound;


    /**3
     * 大修名称
     */
    @ApiModelProperty(value = "大修名称")
    private String name;


    /**
     * 大修类别
     */
    @ApiModelProperty(value = "大修类别")
    private String type;

    @ApiModelProperty(value = "大修类别名称")
    private String typeName;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;


    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;


    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    /**
     * 工期（天数）
     */
    @ApiModelProperty(value = "工期（天数）")
    private Integer workDuration;

    /**
     * 实际工期（天数）
     */
    @ApiModelProperty(value = "实际工期（天数）")
    private Long actualWorkDuration;

    /**
     * 大修经理
     */
    @ApiModelProperty(value = "大修经理")
    private String repairManager;
    @ApiModelProperty(value = "大修经理名称")
    private String repairManagerName;

    //在“实际开始时间”之前添加的作业，相加的总和
    @ApiModelProperty(value = "计划作业数")
    private long preJobNum;
    //在“实际开始时间”之后添加的作业，相加的总和
    @ApiModelProperty(value = "新增作业数")
    private long saveJobNum;
    //计划作业数+新增作业数;
    @ApiModelProperty(value = "总作业数")
    private long totalJobNum;
    //作业状态为“APPV”和“SCHD”的作业，相加的总和;
    @ApiModelProperty(value = "准备完成数")
    private long prepareFinishNum;
    //准备完成数:总作业数;
    @ApiModelProperty(value = "准备率")
    private BigDecimal prepareRate;
    //作业状态为“CSR”和"CPL"的作业，相加的总和;
    @ApiModelProperty(value = "已完成作业数")
    private long finishedNum;
    //已完成作业数÷总作业数。
    @ApiModelProperty(value = "完成率")
    private BigDecimal finishRate;

    // 计划入场总人数:统计大修计划中每个作业，已获得岗位授权的总人数，统计数量;
    @ApiModelProperty(value = "计划入场总人数")
    private long preInputPersonNum;
    // .实际入场数:根据计划入场人员(参照上面)的员工号，以及作业实施基地，到人员管理中査询人员是否进入该基地，并统计数量,
    @ApiModelProperty(value = "实际入场数")
    private long actInputPersonNum;

    // .已离场数:根据实际入场数，查询人员台账中是否有离场记录，并统计数量
    @ApiModelProperty(value = "已离场数")
    private long outPersonNum;



    // .工器具计划入场数:统计大修计划中每个作业，添加的物资管理需求，统计数量;
    @ApiModelProperty(value = "工器具计划入场数")
    private long preToolInputNum;

    // .工器具实际入场总数:1.固定资产根据固定资产编码，以及作业实施基地，到物资管理中査询物资是否进入该基地，并统计数量2.根据非固定资产添加时选择的作业，反查本次大修并统计数量;
    @ApiModelProperty(value = "工器具实际入场总数")
    private long actToolInputNum;

    // 工具已离场数:根据工器具实际入场总数，查询物资台账中是否有离场记录，并统计数量。
    @ApiModelProperty(value = "工具已离场数")
    private long actToolOutNum;


    @ApiModelProperty(value = "基地编号")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    private String baseName;
}

