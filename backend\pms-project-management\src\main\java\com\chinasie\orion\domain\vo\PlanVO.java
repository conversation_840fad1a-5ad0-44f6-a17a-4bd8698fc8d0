package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PlanVO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    public String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名")
    public String contractName;

    /**
     * 合同状态
     */
    @ApiModelProperty("合同状态")
    public Integer contractStatus;


    /**
     * 合同状态
     */
    @ApiModelProperty("合同状态名称")
    public String contractStatusName;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    public Integer status;


    /**
     * 状态
     */
    @ApiModelProperty("状态名称")
    public String statusName;

    /**
     * 用人单位代号
     */
    @ApiModelProperty("用人单位代号")
    public String centerCode;

    /**
     * 用人单位名称
     */
    @ApiModelProperty("用人单位名称")
    public String centerName;

    /**
     * 人力成本总预算
     */
    @ApiModelProperty("人力成本总预算")
    public BigDecimal personTotalBudget;

    /**
     * 人力成本实际执行
     */
    @ApiModelProperty("人力成本实际执行")
    public BigDecimal personActualTotalMoney;


    /**
     * 岗级成本总预算
     */
    @ApiModelProperty("岗级成本总预算")
    public BigDecimal positionTotalMoney;


    /**
     * 岗级成本实际执行
     */
    @ApiModelProperty("岗级成本实际执行")
    public BigDecimal positionActualTotalMoney;

    /**
     * 岗位计划总人数
     */
    @ApiModelProperty("岗位计划总人数")
    public Integer personTotalCount;

    /**
     * 岗位实际执行人数
     */
    @ApiModelProperty("岗位实际人数")
    public Integer personActualPerson;

    /**
     * 成本明细列表
     */
    public List<CostVO> costList;

    /**
     * 表头信息
     */
    @ApiModelProperty("表头信息")
    public Map<String,BigDecimal> sheetHeadsMap;

}
