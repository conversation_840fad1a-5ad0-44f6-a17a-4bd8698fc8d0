package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * JobPackage DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:59
 */
@ApiModel(value = "MajorPersonStatisticDTO对象", description = "大修信息统计")
@Data
@ExcelIgnoreUnannotated
public class MajorPersonStatisticDTO {

    @ApiModelProperty(value = "计划入场数量")
    private long planNumberJoin=0l;

    @ApiModelProperty(value = "实际入场数量")
    private long actualNumberJoin=0l;

    @ApiModelProperty(value = "计划入场率")
    private BigDecimal planJoinRatio=BigDecimal.ZERO;

    @ApiModelProperty(value = "实际入场率")
    private BigDecimal actualJoinRatio=BigDecimal.ZERO;

    @ApiModelProperty(value = "已离场数")
    private long outNumber=0l;


}
