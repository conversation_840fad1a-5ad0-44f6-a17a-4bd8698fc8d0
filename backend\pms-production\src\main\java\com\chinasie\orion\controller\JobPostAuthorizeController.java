package com.chinasie.orion.controller;

import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.dto.AuthorizeJobPostRequirementDTO;
import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.vo.AuthorizeJobPostRequirementVO;
import com.chinasie.orion.domain.vo.job.AuthorizeInfoVO;
import com.chinasie.orion.domain.vo.job.JobEquivalentInfoVO;
import com.chinasie.orion.domain.vo.job.JobPostAuthorizeDetailVO;
import com.chinasie.orion.domain.vo.job.JobPostAuthorizeInfoVO;
import com.chinasie.orion.domain.vo.validation.ValidationResult;
import com.chinasie.orion.handler.status.JobPostAuthorizeChangeStatusReceiver;
import com.chinasie.orion.service.AuthorizeJobPostRequirementService;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.JobPostAuthorize;
import com.chinasie.orion.domain.dto.JobPostAuthorizeDTO;
import com.chinasie.orion.domain.vo.JobPostAuthorizeVO;

import com.chinasie.orion.service.JobPostAuthorizeService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobPostAuthorize 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@RestController
@RequestMapping("/job-post-authorize")
@Api(tags = "作业授权信息")
public class  JobPostAuthorizeController  {

    @Autowired
    private JobPostAuthorizeService jobPostAuthorizeService;

    @Autowired
    private AuthorizeJobPostRequirementService authorizeJobPostRequirementService;

    @Resource
    private JobPostAuthorizeChangeStatusReceiver jobPostAuthorizeChangeStatusReceiver;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<JobPostAuthorizeVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobPostAuthorizeVO rsp = jobPostAuthorizeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception {
        String rsp =  jobPostAuthorizeService.create(jobPostAuthorizeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     *
     * @param jobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加人员")
    @RequestMapping(value = "/add/person/list", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增数据【{{#jobPostAuthorizeDTO.idList}}】", type = "JobPostAuthorize", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> addPersonList(@RequestBody JobAuthorizeUserParamDTO jobPostAuthorizeDTO) throws Exception {
        Boolean rsp =  jobPostAuthorizeService.addPersonList(jobPostAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "编辑", bizNo = "{{#jobPostAuthorizeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.edit(jobPostAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personJobPostDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑作业岗位")
    @RequestMapping(value = "/edit/job/post", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑作业岗位【{{#personJobPostDTO.jobPostCode}}】", type = "JobPostAuthorize", subType = "编辑", bizNo = "{{#jobPostAuthorizeDTO.id}}")
    public ResponseDTO<Boolean> editJobPost(@Validated  @RequestBody PersonJobPostDTO personJobPostDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.editJobPost(personJobPostDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "JobPostAuthorize", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobPostAuthorizeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobPostAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobPostAuthorizeService.remove(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量--新）")
    @RequestMapping(value = "/remove/batch/new", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobPostAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeBatchNew(@Validated @RequestBody JobPersonParamDTO jobPersonParamDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.removeBatchNew(jobPersonParamDTO);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "JobPostAuthorize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobPostAuthorizeVO>> pages(@RequestBody Page<JobPostAuthorizeDTO> pageRequest) throws Exception {
        Page<JobPostAuthorizeVO> rsp =  jobPostAuthorizeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业授权信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "JobPostAuthorize", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobPostAuthorizeService.downloadExcelTpl(response);
    }

    @ApiOperation("作业授权信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "JobPostAuthorize", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = jobPostAuthorizeService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业授权信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "JobPostAuthorize", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobPostAuthorizeService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业授权信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "JobPostAuthorize", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobPostAuthorizeService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业授权信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "JobPostAuthorize", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        jobPostAuthorizeService.exportByExcel(searchConditions, response);
    }


        // 岗位授权
    /**
     * 编辑
     *
     * @param jobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权管理---保存")
    @RequestMapping(value = "/manage/save", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权保存【{{#jobPostAuthorizeDTO.id}}】", type = "JobPostAuthorize", subType = "编辑", bizNo = "{{#jobPostAuthorizeDTO.id}}")
    public ResponseDTO<Boolean> manageSave(@RequestBody JobPostAuthorizeParamDTO jobPostAuthorizeDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.manageSave(jobPostAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }






    /**
     * 编辑
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权管理---信息获取")
    @RequestMapping(value = "/manage/info/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权信息获取【{{#id}}】", type = "JobPostAuthorize", subType = "获取", bizNo = "{{#id}}")
    public ResponseDTO<JobPostAuthorizeDetailVO> manageInfo(@PathVariable("id") String id) throws Exception {
        JobPostAuthorizeDetailVO rsp = jobPostAuthorizeService.manageInfo(id);
        return new ResponseDTO<>(rsp);
    }




    /**
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权--- 获取岗位等效信息")
    @RequestMapping(value = "/manage/equivalent/info/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权保存【{{#id}}】", type = "JobPostAuthorize", subType = "", bizNo = "{{#id}}")
    public ResponseDTO<List<JobEquivalentInfoVO>> manageEquivalentInfoList(@PathVariable("id") String id) throws Exception {
        List<JobEquivalentInfoVO> rsp = jobPostAuthorizeService.manageEquivalentInfoList(id);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 移除岗位等效信息
     * @param  idList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权--- 移除岗位等效信息")
    @RequestMapping(value = "/manage/equivalent/info", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权保存【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> manageEquivalentInfoDel(@RequestBody List<String> idList) throws Exception {
        Boolean rsp = jobPostAuthorizeService.delJobEquList(idList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * @param jobPostEquDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权--- 新增等效岗位")
    @RequestMapping(value = "/manage/equivalent/save/", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权保存【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<Boolean> manageEquivalentInfoSave(@RequestBody JobPostEquDTO jobPostEquDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.manageEquivalentInfoSave(jobPostEquDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param userCode
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权--- 获取当前账号可以岗位等效的授权管理信息")
    @RequestMapping(value = "/manage/equivalent/list/user", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【{{#userCode}}】用户可供岗位等效的授权信息", type = "JobPostAuthorize", subType = "获取", bizNo = "")
    public ResponseDTO<List<AuthorizeInfoVO>> manageEquivalentInfoListByUser(@RequestParam("userCode") String userCode) throws Exception {
        List<AuthorizeInfoVO> rsp = jobPostAuthorizeService.manageEquivalentInfoListByUser(userCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     *  注释：不要删除该方法
     * @param idList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "授权核验")
    @Deprecated
    @RequestMapping(value = "/{jobId}/authorize/validation", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】授权核验【{{#idList}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<String> authorizeValidation(@PathVariable("jobId") String jobId, @RequestBody List<String> idList) throws Exception {
        String validationResult = jobPostAuthorizeService.authorizeValidation(jobId,idList);
        return new ResponseDTO<>(validationResult);
    }

    /**
     * @param idList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "授权核验（新）")
    @RequestMapping(value = "/{jobId}/authorize/validation/new", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】授权核验【{{#idList}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<ValidationResult> authorizeValidationNew(@PathVariable("jobId") String jobId,@RequestBody List<String> idList) throws Exception {
        ValidationResult validationResult  = jobPostAuthorizeService.authorizeValidationNew(jobId,idList);
        return new ResponseDTO<>(validationResult);
    }

    /**
     * @param userCode
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取人员下的岗位授权")
    @RequestMapping(value = "/person/jobPost/authorize", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】授权核验【{{#idList}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<List<JobPostAuthorizeInfoVO>> personJobPostAuthorizeList(@RequestParam("userCode") String userCode) throws Exception {
        List<JobPostAuthorizeInfoVO> rsp = jobPostAuthorizeService.personJobPostAuthorizeList(userCode);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "岗位授权要求")
    @LogRecord(success = "【{USER{#logUserId}}】获取大修状态列表", type = "MajorRepairPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/requirementList/list", method = RequestMethod.POST)
    public ResponseDTO<List<AuthorizeJobPostRequirementVO>> statusList(@ApiParam("授权ID") @RequestParam("jobAuthorizeId") String jobAuthorizeId) throws Exception {
        List<AuthorizeJobPostRequirementVO> rsp =  authorizeJobPostRequirementService.listByAuthorizeId(jobAuthorizeId);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation(value = "岗位授权要求--新增")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】创建岗位授权信息", type = "MajorRepairPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/requirementList/create", method = RequestMethod.POST)
    public ResponseDTO<String> createJobPost(@ApiParam("授权ID") AuthorizeJobPostRequirementDTO  authorizeJobPostRequirementDTO) throws Exception {
        String rsp =  authorizeJobPostRequirementService.create(authorizeJobPostRequirementDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "岗位授权--- 获取人员下的岗位授权信息（分页）")
    @RequestMapping(value = "/user/equivalent/page/", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】岗位授权保存【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<Page<JobPostAuthorizeInfoVO>> userEquivalentPage(@RequestBody Page<JobPostAuthorizeDTO> pageRequest) throws Exception {
        Page<JobPostAuthorizeInfoVO> rsp = jobPostAuthorizeService.userEquivalentPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * @param changeStatusDto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业数据授权状态变更测试")
    @RequestMapping(value = "/change/status", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】作业数据授权状态变更【{{#jobPostAuthorizeDTO.name}}】", type = "JobPostAuthorize", subType = "", bizNo = "")
    public ResponseDTO<Boolean> changeStatus(@RequestBody  ChangeStatusMessageDTO changeStatusDto) throws Exception {
        jobPostAuthorizeChangeStatusReceiver.consumerCreateMessage(changeStatusDto);
        return new ResponseDTO<>(Boolean.TRUE);
    }


    /**
     * 编辑
     *
     * @param startEndDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑计划开始计划结束时间")
    @RequestMapping(value = "/edit/date", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑人员授权【{{#startEndDTO.id}}】的计划开始计划结束时间", type = "JobPostAuthorize", subType = "编辑时间", bizNo = "{{#startEndDTO.id}}")
    public ResponseDTO<Boolean> editDate(@Validated @RequestBody StartEndDTO startEndDTO) throws Exception {
        Boolean rsp = jobPostAuthorizeService.editDate(startEndDTO);
        return new ResponseDTO<>(rsp);
    }


}
