package com.chinasie.orion.service;


import com.chinasie.orion.constant.JobManageStatusEnum;
import com.chinasie.orion.domain.dto.job.MajorUserLikeParamDTO;
import com.chinasie.orion.domain.entity.MajorUserLike;
import com.chinasie.orion.domain.dto.MajorUserLikeDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.domain.vo.MajorUserLikeVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MajorUserLike 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
public interface MajorUserLikeService extends OrionBaseService<MajorUserLike> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MajorUserLikeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param majorUserLikeDTO
     */
    String create(MajorUserLikeDTO majorUserLikeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param majorUserLikeDTO
     */
    Boolean edit(MajorUserLikeDTO majorUserLikeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MajorUserLikeVO> pages(Page<MajorUserLikeDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MajorUserLikeVO> vos) throws Exception;

    /**
     *  获取大修列表
     * @param statusEnum
     * @return
     */
    List<MajorUserLikeVO> majorInfoList(JobManageStatusEnum statusEnum);

    Boolean addBatch(List<MajorUserLikeParamDTO> majorUserLikeDTOList, JobManageStatusEnum statusEnum);

    /**
     *  获取大修列表
     * @param statusEnum
     * @return
     */
    List<MajorRepairPlanVO> majorList(JobManageStatusEnum statusEnum);
}
