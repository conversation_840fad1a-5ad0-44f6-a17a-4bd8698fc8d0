---
description: Web Scraping Project MCP Rules
globs: 
alwaysApply: false
---
# 网站内容抓取项目 MCP 规则

## 规则目标
使用网络爬虫技术抓取指定网站的所有内容页面，并生成结构化的设计文档。

## 前置条件
1. 已确定目标网站
2. 已获取必要的访问权限（如适用）
3. 已安装必要的开发工具和库

## 执行步骤

### 1. 项目初始化与需求分析
```mcp
{
  "name": "项目初始化与需求分析",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建项目目录结构",
      "structure": [
        "./docs/",
        "./src/",
        "./src/scrapers/",
        "./src/parsers/",
        "./src/exporters/",
        "./data/",
        "./tests/"
      ]
    },
    {
      "action": "目标网站分析",
      "tasks": [
        "确定网站URL模式",
        "分析网站结构和导航",
        "识别内容页面特征",
        "确定抓取深度和范围",
        "评估网站反爬虫机制"
      ],
      "output": "./docs/website_analysis.md"
    },
    {
      "action": "技术选型",
      "options": {
        "language": ["Python", "JavaScript", "Go"],
        "frameworks": ["Scrapy", "Puppeteer", "Selenium", "Beautiful Soup", "Axios"],
        "storage": ["JSON", "CSV", "SQLite", "MongoDB"],
        "concurrency": ["同步", "异步", "多线程", "分布式"]
      },
      "output": "./docs/tech_stack.md"
    }
  ]
}
```

### 2. 爬虫设计与开发
```mcp
{
  "name": "爬虫设计与开发",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "爬虫架构设计",
      "components": [
        "URL管理器",
        "网页下载器",
        "内容解析器",
        "数据存储器",
        "调度器"
      ],
      "output": "./docs/crawler_architecture.md"
    },
    {
      "action": "开发URL发现模块",
      "features": [
        "起始URL配置",
        "URL提取规则",
        "URL过滤规则",
        "URL优先级队列",
        "URL去重机制"
      ],
      "output": "./src/scrapers/url_manager.py"
    },
    {
      "action": "开发网页下载模块",
      "features": [
        "HTTP请求配置",
        "User-Agent轮换",
        "代理IP支持",
        "请求频率控制",
        "错误处理与重试",
        "响应缓存"
      ],
      "output": "./src/scrapers/downloader.py"
    },
    {
      "action": "开发内容解析模块",
      "features": [
        "HTML解析器",
        "CSS选择器",
        "XPath表达式",
        "正则表达式",
        "数据清洗",
        "结构化数据提取"
      ],
      "output": "./src/parsers/content_parser.py"
    },
    {
      "action": "开发数据存储模块",
      "features": [
        "文件存储",
        "数据库存储",
        "增量更新",
        "数据备份"
      ],
      "output": "./src/exporters/data_storage.py"
    }
  ]
}
```

### 3. 爬虫测试与优化
```mcp
{
  "name": "爬虫测试与优化",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "单元测试",
      "tests": [
        "URL管理器测试",
        "网页下载器测试",
        "内容解析器测试",
        "数据存储器测试"
      ],
      "output": "./tests/unit_tests.py"
    },
    {
      "action": "集成测试",
      "tests": [
        "小规模爬取测试",
        "错误处理测试",
        "性能基准测试"
      ],
      "output": "./tests/integration_tests.py"
    },
    {
      "action": "性能优化",
      "techniques": [
        "并发请求调优",
        "内存使用优化",
        "网络请求优化",
        "解析效率提升"
      ],
      "output": "./docs/performance_optimization.md"
    },
    {
      "action": "反爬策略应对",
      "techniques": [
        "请求延迟调整",
        "User-Agent策略优化",
        "IP代理策略",
        "Cookie管理",
        "验证码处理"
      ],
      "output": "./docs/anti_scraping_strategies.md"
    }
  ]
}
```

### 4. 数据处理与导出
```mcp
{
  "name": "数据处理与导出",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "数据清洗",
      "tasks": [
        "去除HTML标签",
        "修复格式问题",
        "去除重复内容",
        "标准化数据格式"
      ],
      "output": "./src/exporters/data_cleaner.py"
    },
    {
      "action": "数据结构化",
      "tasks": [
        "定义数据模型",
        "映射字段关系",
        "处理嵌套数据",
        "添加元数据"
      ],
      "output": "./src/exporters/data_structurer.py"
    },
    {
      "action": "数据导出",
      "formats": [
        "JSON",
        "CSV",
        "Markdown",
        "HTML",
        "数据库"
      ],
      "output": "./src/exporters/data_exporter.py"
    },
    {
      "action": "生成PRD文档",
      "sections": [
        "项目概述",
        "网站分析",
        "数据结构",
        "内容分类",
        "功能需求",
        "技术实现",
        "使用说明"
      ],
      "output": "./docs/prd.md"
    }
  ]
}
```

### 5. 部署与监控
```mcp
{
  "name": "部署与监控",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "部署准备",
      "tasks": [
        "环境配置文档",
        "依赖管理",
        "配置文件准备",
        "启动脚本编写"
      ],
      "output": "./docs/deployment_guide.md"
    },
    {
      "action": "爬虫调度配置",
      "options": {
        "定时任务": "cron表达式配置",
        "触发条件": "事件触发配置",
        "并发控制": "资源限制配置"
      },
      "output": "./src/scheduler.py"
    },
    {
      "action": "监控系统配置",
      "metrics": [
        "爬取速度",
        "成功率",
        "数据量",
        "资源使用",
        "错误日志"
      ],
      "output": "./src/monitor.py"
    },
    {
      "action": "异常处理机制",
      "scenarios": [
        "网络异常",
        "解析失败",
        "存储错误",
        "资源限制",
        "目标网站变更"
      ],
      "output": "./src/error_handler.py"
    }
  ]
}
```

## 验收标准
1. 能够成功抓取目标网站的所有内容页面
2. 抓取的数据结构完整、格式正确
3. 生成的PRD文档内容全面、结构清晰
4. 爬虫运行稳定，具备错误处理能力
5. 符合网站访问规范，不造成目标网站负担

## 输出物
1. 完整的爬虫代码
2. 抓取的网站数据
3. ./docs/prd.md 设计文档
4. 部署与使用文档

## 注意事项
1. 遵守网站的robots.txt规则
2. 控制爬取频率，避免对目标网站造成压力
3. 尊重版权，合法使用抓取的内容
4. 保护敏感数据，遵守数据隐私法规
5. 定期更新爬虫以适应网站变化
