package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectAchievementFloderDTO;
import com.chinasie.orion.domain.vo.ProjectAchievementFloderVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectAchievementFloderService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * <p>
 * ProjectAchievementFloder 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 13:43:27
 */
@RestController
@RequestMapping("/projectAchievementFloder")
@Api(tags = "项目成果文件夹表")
public class ProjectAchievementFloderController {

    @Autowired
    private ProjectAchievementFloderService projectAchievementFloderService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目成果文件夹表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectAchievementFloderVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectAchievementFloderVO rsp = projectAchievementFloderService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectAchievementFloderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectAchievementFloderDTO.name}}】", type = "项目成果文件夹表", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectAchievementFloderVO> create(@RequestBody ProjectAchievementFloderDTO projectAchievementFloderDTO) throws Exception {
        ProjectAchievementFloderVO rsp =  projectAchievementFloderService.create(projectAchievementFloderDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectAchievementFloderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectAchievementFloderDTO.name}}】", type = "项目成果文件夹表", subType = "编辑", bizNo = "{{#projectAchievementFloderDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectAchievementFloderDTO projectAchievementFloderDTO) throws Exception {
        Boolean rsp = projectAchievementFloderService.edit(projectAchievementFloderDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目成果文件夹表", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectAchievementFloderService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目成果文件夹表", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectAchievementFloderVO>> pages(@RequestBody Page<ProjectAchievementFloderDTO> pageRequest) throws Exception {
        Page<ProjectAchievementFloderVO> rsp =  projectAchievementFloderService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/lists/{approvalId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目成果文件夹表", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<ProjectAchievementFloderVO>> lists(@PathVariable("approvalId") String approvalId) throws Exception {
        List<ProjectAchievementFloderVO> rsp = projectAchievementFloderService.lists(approvalId);
        return new ResponseDTO<>(rsp);
    }
}

