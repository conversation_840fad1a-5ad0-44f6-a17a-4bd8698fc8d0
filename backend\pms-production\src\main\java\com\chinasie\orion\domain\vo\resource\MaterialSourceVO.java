package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:20
 * @description:
 */
@Data
public class MaterialSourceVO implements Serializable {

    @ApiModelProperty(value = "数据id")
    private String id;
    @ApiModelProperty(value = "物资code")
    private String materialCode;
    @ApiModelProperty(value = "姓名")
    private String materialName;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "作业名称")
    private String jobName;
    @ApiModelProperty(value = "作业Id")
    private String jobId;
    @ApiModelProperty(value = "重叠天数")
    private int overlapCount;
    @ApiModelProperty(value = "数据类型: 1-物资，2-作业，3.任务")
    private String dataType;

    @ApiModelProperty(value = "进出场时间")
    private List<InAndOutDateVO> inAndOutDateVOList;

    @ApiModelProperty(value = "唯一Id")
    private String uniqueId;

    @ApiModelProperty(value = "作业计划开始时间")
    private Date jobBeginDate;
    @ApiModelProperty(value = "作业计划开始时间")
    private Date jobEndDate;

    @ApiModelProperty(value = "物资ID")
    private String materialId;

    private Date taskEndDate;

    private Date taskBeginDate;
    @ApiModelProperty(value = "顺序")
    private Integer sort;
    @ApiModelProperty(value = "所属基地")
    private String baseCode;

    private Date minDate;

    @ApiModelProperty(value = "对比时间：参照数据为任务类型的数据的时间")
    private List<InAndOutDateVO> targetDateVOList;
}
