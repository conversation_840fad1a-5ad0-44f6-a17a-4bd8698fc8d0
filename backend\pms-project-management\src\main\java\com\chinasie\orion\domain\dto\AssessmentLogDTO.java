package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * AssessmentLog DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:28:12
 */
@ApiModel(value = "AssessmentLogDTO对象", description = "审核记录表")
@Data
@ExcelIgnoreUnannotated
public class AssessmentLogDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 1)
    private String centerName;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @ExcelProperty(value = "审批意见 ", index = 2)
    private String assessmentAdvice;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    @ExcelProperty(value = "审批人id ", index = 3)
    private String personId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    @ExcelProperty(value = "审批人姓名 ", index = 4)
    private String personName;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @ExcelProperty(value = "提交时间 ", index = 5)
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @ExcelProperty(value = "审批时间 ", index = 6)
    private Date assessmentTime;

    @ApiModelProperty(value = "审批类型")
    private String type;

    @ApiModelProperty(value = "年份")
    private Integer year;
}
