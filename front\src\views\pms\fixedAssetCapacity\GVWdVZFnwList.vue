<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem, Layout, OrionTable, isPower,
} from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '资产代码',
      width: 100,
      dataIndex: 'code',
    },
    {
      title: '资产编码/条码',
      width: 190,
      dataIndex: 'number',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      width: 370,
      dataIndex: 'name',
    },
    {
      title: '数量',
      width: 100,
      dataIndex: 'numCount',
    },
    {
      title: '成本中心名称',
      width: 115,
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      width: 222,
      dataIndex: 'spModel',
    },
    {
      title: '是否需要检定',
      width: 115,
      dataIndex: 'isNeedVerification',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '下次检定日期',
      width: 115,
      dataIndex: 'nextVerificationTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('span', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '责任人工号',
      width: 115,
      dataIndex: 'rspUserNumber',
    },
    {
      title: '责任人姓名',
      width: 110,
      dataIndex: 'rspUserName',
    },
    {
      title: '使用人工号',
      width: 110,
      dataIndex: 'useUserNumber',
    },
    {
      title: '使用人姓名',
      width: 100,
      dataIndex: 'useUserName',
    },
    {
      title: '资产存放地',
      width: 100,
      dataIndex: 'storageLocationName',
    },
  ],
  smallSearchField: [
    'number',
    'name',
    'productCode',
  ],
  api: (params: Record<string, any>) => new Api('/pms/fixed-assets').fetch({
    ...params,
    power: {
      containerCode: 'table-list-container-042a7f-GVWdVZFnw-DJ4Jtdyp',
      pageCode: 'list-container-042a7f-GVWdVZFnw',
    },
  }, 'page', 'POST'),
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_GDZCNLK_container_01_button_01', powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSFixedAssetCapacityDetails',
    params: {
      id,
    },
  });
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSFixedAssetCapacity',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
