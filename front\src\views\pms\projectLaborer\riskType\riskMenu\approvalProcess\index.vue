<template>
  <Layout2>
    <OrionTable
      ref="tableRef"
      :options="options"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('LXSZ_container_button_42',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="btnClick('add');"
        >
          新增
        </BasicButton>
        <BasicButton
          v-if="isPower('LXSZ_container_button_43',powerData)"
          icon="sie-icon-shanchu"
          @click="btnClick('delete');"
        >
          删除
        </BasicButton>
      </template>
      <template #action="{record}">
        <BasicTableAction :actions="actionsBtn(record)" />
      </template>
    </OrionTable>
    <AddDataSourceModal
      @register="myModalR"
      @flows="flows"
    />
  </Layout2>
</template>

<script lang="ts">
import {
  computed,
  defineComponent, inject, reactive, toRefs, watch,
} from 'vue';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, Layout2, OrionTable, useModal,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import AddDataSourceModal from './addDataSourceModal/index.vue';

export default defineComponent({
  name: 'Index',
  components: {
    BasicTableAction,
    BasicButton,
    Layout2,
    OrionTable,
    AddDataSourceModal,
  },
  props: {
    selectChangeData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: [],
  setup(props) {
    const [myModalR, myModalM] = useModal();
    const powerData: any = inject('powerData', {});
    const state = reactive({
      tableRef: null,
      options: {
        rowKey: 'relationId',
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: false,
        isFilter2: true,
        filterConfigName: 'PAS_BECURRENTMANAGE_TYPESETTINGS_RISKTYPE_CAREFUL',
        smallSearchField: ['name'],
        pagination: false,
        api: () => new Api(`/pms/projectPlan-type-to-process/list/${props.selectChangeData.id}`).fetch('', '', 'GET'),
        columns: [
          {
            title: '流程名称',
            dataIndex: 'name',
          },
          {
            title: '描述',
            dataIndex: 'description',
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ],
      },
    });

    const state6 = reactive({
      btnList: [
        { type: 'add' },
        // { type: 'check' },
        {
          type: 'delete',
          powerCode: 'LXSZ_container_button_44',
        },
        // { type: 'open' },
      ],
    });
    watch(() => props.selectChangeData, () => {
      state.tableRef.reload();
    });
    function btnClick(type) {
      switch (type) {
        case 'check':
          break;
        case 'add':
          addFn();
          break;
        case 'delete':
          if (evenJudge()) {
            deleteFn();
          }
          break;
        case 'open':
          break;
      }
    }
    function addFn() {
      myModalM.setModalProps ({
        height: 640,
        minHeight: 80,
        showOkBtn: false,
        showCancelBtn: false,
      });
      myModalM.openModal(true, { dataType: props.selectChangeData.className });
    }
    async function deleteFn() {
      let rowList = evenJudge();
      let isDelete = rowList.every((item) => item.currentType);
      if (!isDelete) {
        message.warning('不可删除父级创建的流程');
        return;
      }
      Modal.confirm({
        title: '删除提示',
        content: '您确认要删除这些数据吗？',
        async onOk() {
          return await new Api('/pms/projectPlan-type-to-process').fetch(evenJudge().map((item) => item.relationId), '', 'DELETE').then(() => {
            message.success('删除成功');
            state.tableRef.reload();
          });
        },
      });
    }
    function evenJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length === 0) {
        message.info('请至少选择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    async function flows(flows) {
      if (flows?.length < 1) {
        return;
      }
      await new Api(`/pms/projectPlan-type-to-process/add-batch/${props.selectChangeData.id}`).fetch(flows.map((item) => item.flowKey), '', 'POST').then((res) => {
        message.info('添加成功');
        state.tableRef.reload();
        myModalM.openModal(false);
      }).catch(() => {
        myModalM.openModal(false);
      });
    }

    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '删除',
          isShow: computed(() => state6.btnList.some((item) => item.type === 'delete')),
          modal() {
            return new Api('/pms/projectPlan-type-to-process').fetch([record.relationId], '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      btnClick,
      myModalR,
      flows,
      actionsBtn,
      isPower,
      powerData,
    };
  },
});
</script>

<style scoped lang="less"></style>
