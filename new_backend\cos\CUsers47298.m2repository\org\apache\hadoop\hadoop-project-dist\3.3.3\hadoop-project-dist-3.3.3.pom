<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-project</artifactId>
    <version>3.3.3</version>
    <relativePath>../hadoop-project</relativePath>
  </parent>
  <artifactId>hadoop-project-dist</artifactId>
  <version>3.3.3</version>
  <description>Apache Hadoop Project Dist POM</description>
  <name>Apache Hadoop Project Dist POM</name>
  <packaging>pom</packaging>

  <properties>
    <hadoop.tmp.dir>${project.build.directory}/test</hadoop.tmp.dir>
    <test.build.data>${project.build.directory}/test/data</test.build.data>
    <hadoop.log.dir>${project.build.directory}/log</hadoop.log.dir>
    <test.build.webapps>${project.build.directory}/test-classes/webapps</test.build.webapps>
    <test.cache.data>${project.build.directory}/test-classes</test.cache.data>
    <test.build.classes>${project.build.directory}/test-classes</test.build.classes>

    <hadoop.component>UNDEF</hadoop.component>
    <snappy.lib></snappy.lib>
    <bundle.snappy>false</bundle.snappy>
    <zstd.lib></zstd.lib>
    <bundle.zstd>false</bundle.zstd>
    <bundle.zstd.in.bin>false</bundle.zstd.in.bin>
    <isal.lib></isal.lib>
    <bundle.isal>false</bundle.isal>
    <openssl.lib></openssl.lib>
    <bundle.openssl>false</bundle.openssl>
    <bundle.openssl.in.bin>false</bundle.openssl.in.bin>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>prepare-jar</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
          <execution>
            <id>prepare-test-jar</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/*.class</include>
                <include>webapps/**</include>
                <include>META-INF/LICENSE.txt</include>
                <include>META-INF/NOTICE.txt</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>${basedir}/dev-support/findbugsExcludeFile.xml</excludeFilterFile>
          <maxHeap>2048</maxHeap>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <maxmemory>768m</maxmemory>
          <quiet>true</quiet>
          <verbose>false</verbose>
          <source>${maven.compile.source}</source>
          <charset>${maven.compile.encoding}</charset>
          <reportOutputDirectory>${project.build.directory}/site</reportOutputDirectory>
          <destDir>${project.build.directory}/api</destDir>
          <groups>
            <group>
              <title>${project.name} API</title>
              <packages>org.apache.hadoop*</packages>
            </group>
          </groups>

          <!-- switch on dependency-driven aggregation -->
          <includeDependencySources>false</includeDependencySources>

          <dependencySourceIncludes>
            <!-- include ONLY dependencies I control -->
            <dependencySourceInclude>org.apache.hadoop:hadoop-annotations</dependencySourceInclude>
          </dependencySourceIncludes>

        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>docs</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <properties>
        <jdiff.stable.api>3.3.2</jdiff.stable.api>
        <jdiff.stability>-unstable</jdiff.stability>
        <!-- Commented out for HADOOP-11776 -->
        <!-- Uncomment param name="${jdiff.compatibility}" in javadoc doclet if compatibility is not empty -->
        <jdiff.compatibility></jdiff.compatibility>
        <jdiff.javadoc.maxmemory>512m</jdiff.javadoc.maxmemory>
      </properties>
      <dependencies>
        <dependency>
          <groupId>xerces</groupId>
          <artifactId>xercesImpl</artifactId>
          <version>${xerces.jdiff.version}</version>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
             <execution>
                <goals>
                  <goal>javadoc-no-fork</goal>
                </goals>
                <phase>prepare-package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <execution>
                <id>site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>copy</goal>
                </goals>
                <configuration>
                  <artifactItems>
                    <artifactItem>
                      <groupId>jdiff</groupId>
                      <artifactId>jdiff</artifactId>
                      <version>${jdiff.version}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>jdiff.jar</destFileName>
                    </artifactItem>
                    <artifactItem>
                      <groupId>org.apache.hadoop</groupId>
                      <artifactId>hadoop-annotations</artifactId>
                      <version>${project.version}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>hadoop-annotations.jar</destFileName>
                    </artifactItem>
                    <artifactItem>
                      <groupId>xerces</groupId>
                      <artifactId>xercesImpl</artifactId>
                      <version>${xerces.jdiff.version}</version>
                      <overWrite>false</overWrite>
                      <outputDirectory>${project.build.directory}</outputDirectory>
                      <destFileName>xerces.jar</destFileName>
                    </artifactItem>
                  </artifactItems>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>

              <!-- Pre site -->
              <execution>
                <id>pre-site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target if="is.hadoop.common.component">
                    <!-- HADOOP-13428: This is for workaround issues of JDiff
                         - https://sourceforge.net/p/javadiff/bugs/19/ -->
                    <exec executable="patch" input="dev-support/jdiff-workaround.patch" dir="${basedir}">
                        <arg value="-p3"/>
                    </exec>
                  </target>
                </configuration>
              </execution>


              <execution>
                <id>site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target if="is.hadoop.component">
                    <mkdir dir="${project.build.directory}/docs-src"/>

                    <!-- Docs -->
                    <style basedir="${basedir}/src/main/resources"
                           destdir="${project.build.directory}/site"
                           includes="core-default.xml"
                           style="${basedir}/src/main/xsl/configuration.xsl"/>

                    <!-- Jdiff -->
                    <mkdir dir="${project.build.directory}/site/jdiff/xml"/>

                    <javadoc maxmemory="${jdiff.javadoc.maxmemory}" verbose="yes">
                      <doclet name="org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet"
                              path="${project.build.directory}/hadoop-annotations.jar:${project.build.directory}/jdiff.jar">
                        <param name="-apidir" value="${project.build.directory}/site/jdiff/xml"/>
                        <param name="-apiname" value="${project.name} ${project.version}"/>
                        <param name="${jdiff.stability}"/>
                      </doclet>
                      <packageset dir="${basedir}/src/main/java"/>
                      <classpath>
                        <path refid="maven.compile.classpath"/>
                      </classpath>
                    </javadoc>
                    <javadoc sourcepath="${basedir}/src/main/java"
                             destdir="${project.build.directory}/site/jdiff/xml"
                             sourceFiles="${basedir}/dev-support/jdiff/Null.java"
                             maxmemory="${jdiff.javadoc.maxmemory}">
                      <doclet name="org.apache.hadoop.classification.tools.IncludePublicAnnotationsJDiffDoclet"
                              path="${project.build.directory}/hadoop-annotations.jar:${project.build.directory}/jdiff.jar:${project.build.directory}/xerces.jar">
                        <param name="-oldapi" value="${project.name} ${jdiff.stable.api}"/>
                        <param name="-newapi" value="${project.name} ${project.version}"/>
                        <param name="-oldapidir" value="${basedir}/dev-support/jdiff"/>
                        <param name="-newapidir" value="${project.build.directory}/site/jdiff/xml"/>
                        <param name="-javadocold"
                               value="https://hadoop.apache.org/docs/r${jdiff.stable.api}/api/"/>
                        <param name="-javadocnew" value="${project.build.directory}/site/api"/>
                        <param name="-stats"/>
                        <param name="${jdiff.stability}"/>
                        <!--param name="${jdiff.compatibility}"/-->
                      </doclet>
                      <classpath>
                        <path refid="maven.compile.classpath"/>
                      </classpath>
                    </javadoc>
                  </target>
                </configuration>
              </execution>

              <!-- post site phase -->
              <execution>
                <id>post-site</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target if="is.hadoop.common.component">
                    <!-- HADOOP-13428: Revert temporary patch-->
                    <exec executable="patch" input="dev-support/jdiff-workaround.patch" dir="${basedir}">
                        <arg value="-p3"/>
                        <arg value="-R"/>
                    </exec>
                  </target>
                </configuration>
              </execution>

            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>dist</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>${exec-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>pre-dist</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>${shell-executable}</executable>
                  <workingDirectory>${project.build.directory}</workingDirectory>
                  <requiresOnline>false</requiresOnline>
                  <arguments>
                    <argument>${project.parent.basedir}/../dev-support/bin/dist-copynativelibs</argument>
                    <argument>--version=${project.version}</argument>
                    <argument>--builddir=${project.build.directory}</argument>
                    <argument>--artifactid=${project.artifactId}</argument>
                    <argument>--isalbundle=${bundle.isal}</argument>
                    <argument>--isallib=${isal.lib}</argument>
                    <argument>--openssllib=${openssl.lib}</argument>
                    <argument>--opensslbinbundle=${bundle.openssl.in.bin}</argument>
                    <argument>--openssllibbundle=${bundle.openssl}</argument>
                    <argument>--snappylib=${snappy.lib}</argument>
                    <argument>--snappylibbundle=${bundle.snappy}</argument>
                    <argument>--zstdbinbundle=${bundle.zstd.in.bin}</argument>
                    <argument>--zstdlib=${zstd.lib}</argument>
                    <argument>--zstdlibbundle=${bundle.zstd}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>tar</id>
                <phase>package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target if="tar">
                    <!-- Using Unix script to preserve symlinks -->
                    <echo file="${project.build.directory}/dist-maketar.sh">
                      cd "${project.build.directory}"
                      tar cf - ${project.artifactId}-${project.version} | gzip > ${project.artifactId}-${project.version}.tar.gz
                    </echo>
                    <exec executable="${shell-executable}" dir="${project.build.directory}" failonerror="true">
                      <arg line="./dist-maketar.sh"/>
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-assemblies</artifactId>
                <version>${hadoop.version}</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>dist</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <appendAssemblyId>false</appendAssemblyId>
                  <attach>false</attach>
                  <finalName>${project.artifactId}-${project.version}</finalName>
                  <descriptorRefs>
                    <descriptorRef>hadoop-dist</descriptorRef>
                  </descriptorRefs>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>doclet</id>
      <activation>
        <jdk>(,10)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <doclet>org.apache.hadoop.classification.tools.ExcludePrivateAnnotationsStandardDoclet</doclet>
              <docletArtifacts>
                <docletArtifact>
                  <groupId>org.apache.hadoop</groupId>
                  <artifactId>hadoop-annotations</artifactId>
                  <version>${hadoop.version}</version>
                </docletArtifact>
              </docletArtifacts>
              <useStandardDocletOptions>true</useStandardDocletOptions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
