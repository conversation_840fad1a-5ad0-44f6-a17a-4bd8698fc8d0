<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            class="canncel"
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            class="confirm"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, useForm,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import {
  editApi, endTypeApi, saveApi, getDetailsApi,
} from '/@/views/pms/projectLaborer/api/endManagement';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
  },
  props: {
    addApi: {
      type: Function,
      default: () => null,
    },
    dirIdName: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state :any = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: '',
      typeOptions: [],
      principalOptions: [],
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.projectId = drawerData.projectId;
      state.formId = drawerData.id;
      clearValidate();
      resetFields();
      getPrincipalOptions();
      if (drawerData.type === 'add') {
        drawerData.data.startTime = drawerData.data.startTime ? dayjs(drawerData.data.startTime) : '';
        drawerData.data.endTime = drawerData.data.endTime ? dayjs(drawerData.data.endTime) : '';
        drawerData.data.number = '新增完成时自动生成编号';
        setFieldsValue(drawerData.data);
        setDrawerProps({ title: '新增结项' });
      } else {
        state.addMore = true;
        setDrawerProps({ title: '编辑结项' });
        getItemData(state.formId);
      }
    });

    function getItemData(id) {
      state.loading = true;
      getDetailsApi(id, '').then((res) => {
        state.loading = false;
        res.startTime = res.startTime ? dayjs(res.startTime) : '';
        res.endTime = res.endTime ? dayjs(res.endTime) : '';
        setFieldsValue(res);
      }).catch((err) => {
        state.loading = false;
      });
    }
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 24,
          },
          label: '名称:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 11,
          },
          rules: [
            // { required: true, trigger: 'blur' },
          ],
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          defaultValue: '新增完成时自动生成编号',
        },
        {
          field: 'type',
          component: 'Select',
          label: '结项类型:',
          colProps: {
            span: 11,
            offset: 2,
          },
          required: true,
          componentProps: {
            options: computed(() => state.typeOptions),
            fieldNames: {
              label: 'name',
              value: 'id',
            },
          },
        },
        {
          field: 'principalId',
          component: 'Select',
          label: '负责人:',
          colProps: {
            span: 11,
          },
          componentProps: {
            options: computed(() => state.principalOptions),
          },
        },
        {
          field: 'startTime',
          component: 'DatePicker',
          label: '开始时间:',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'endTime',
          component: 'DatePicker',
          label: '实际结束日期:',
          colProps: {
            span: 11,
          },
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'reason',
          component: 'InputTextArea',
          label: '结项申请理由:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData:any = await validateFields(); //   开始时间
      formData.startTime = formData.startTime
        ? dayjs(formData.startTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : '';
      formData.endTime = formData.endTime
        ? dayjs(formData.endTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : '';
      formData.projectId = state.projectId;
      state.loadingBtn = true;
      const love = {
        name: formData?.name,
        className: 'PostProject',
        moduleName: '项目管理-结项管理',
        type: state.formType === 'add' ? 'SAVE' : 'UPDATE',
        remark: state.formType === 'add' ? `新增了【${formData?.name}】` : `编辑了【${formData?.name}】`,
      };
      if (state.formType === 'add') {
        formData.number = '';
        saveApi(formData, love).then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        editApi(formData, love).then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    function getPrincipalOptions() {
      roleListApi({ name: '' }, state.projectId).then((res) => {
        state.principalOptions = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      });
    }
    onMounted(async () => {
      state.typeOptions = await endTypeApi();
    });

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
