// 格式化金额
export function formatMoney(value: number | string, precision: number = 1): string {
  // 第一个需要的数字，第二个是转换的单位
  let money = isNaN(Number(value || 0)) ? 0 : Number(value || 0)
    .toFixed(2);
  money = Number(Number(Number(money) / precision)
    .toFixed(2))
    .toLocaleString();

  if (money.includes('.')) {
    if (money.split('.')[1]?.toString()?.length === 1) {
      return `${money}0`;
    }
    return money;
  }
  return `${money}.00`;
}

// 求两个数的最大公约数
export function gcd(a, b) {
  if (b === 0) {
    return a;
  }
  return gcd(b, a % b);
}

// 格式化数字输入框输入金额
export function formatInputMoney(money:number|string) {
  money = money?.toString()?.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1') ?? '';
  let int = money.toString()
    .split('.')[0];
  let float = money.toString()
    .split('.')[1]?.slice(0, 2);
  money = [int, float]?.filter((item) => item)
    .join('.');
  return parseFloat(parseFloat(money).toFixed(2));
}