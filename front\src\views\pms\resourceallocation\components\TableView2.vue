<script setup lang="ts">
import { STable } from '@surely-vue/table';
import {
    computed, nextTick, onMounted, Ref, ref, watch, watchEffect,
} from 'vue';
import { Menu, MenuItem, message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import _ from 'lodash-es';
import dayjs from 'dayjs';
import router from '/@/router';
import { addResizeListener } from '/@/utils/event';
import { getYearTimestamps } from '../../overhaulManagement/components/TableCalendar/utils';
import { openModal, BasicButton } from 'lyra-component-vue3';

interface AntVTableProps {
    data: any[]
    type: string
    year: string
    repairRound: string
    isEdit: boolean
    minDate: number
    legend: any[]
}

const props = defineProps<AntVTableProps>();
const emits = defineEmits<{
    (e: 'close')
}>();

const dataSource: Ref<any[]> = ref([]);
const yearQuarterTimestamps = computed(() => getYearTimestamps(Number(props.year)).yearQuarterTimestamps);

watch([
    () => props.data,
    () => props.quarterKey,
    () => props.legend,
], () => {
    dataSource.value = formatData(props.data);
}, {
    deep: true,
});

const inAndOutDateVOMap = computed(() => dataSource.value.reduce((prev, next) => {
    next?.inAndOutDateVOList?.forEach((item, index) => {
        prev.set(item.id, {
            ...item,
            listIndex: index,
        });
    });
    return prev;
}, new Map()));

// 时间标记数据
const datePositionCache = computed(() => new Map<string, {
    positions: string[]
    overlapIds: string[]
}>());

function checkDatePosition(targetDate: string, record: Record<string, any>): {
    positions: string[]
    overlapIds: string[]
} {
    // const dateRanges = record?.inAndOutDateVOList || [];
    const dateRanges = record?.realInOutDate || [];
    const cacheKey = `${record.id}_${record.dataType}_${targetDate}_${dateRanges.map((item) => `${item.inDate}-${item.outDate}`)}`;
    if (datePositionCache.value.has(cacheKey)) {
        return datePositionCache.value.get(cacheKey)!;
    }

    const positions = [];
    const overlapIds = [];
    const baseOverlaps = new Map<string, string>();
    //这里测试分段逻辑
    for (let i = 0; i < dateRanges.length; i++) {
        const {
            realStartDate, realEndDate
        } = dateRanges[i];

        const targetDateStr = Number(targetDate);
        const inDateStr = dayjs(realStartDate).valueOf();
        const outDateStr = dayjs(realEndDate || realStartDate).valueOf();

        // if (targetDateStr >= inDateStr && targetDateStr <= outDateStr && leaderData !== true) {
        //     overlapIds.push(id);
        // }

        if (targetDateStr === inDateStr && targetDateStr === outDateStr) {
            positions.push('unit');
            continue;
        }

        if (targetDateStr === inDateStr) {
            positions.push('start');
            continue;
        }

        if (targetDateStr > inDateStr && targetDateStr < outDateStr) {
            console.log('targetDateStr > inDateStr && targetDateStr < outDateStr')
            positions.push('middle');
            continue;
        }

        if (targetDateStr === outDateStr) {
            console.log('targetDateStr === outDateStr')
            positions.push('end');
        }
    }
    const arr = [...new Set(positions)];
    const result = {
        positions: arr.length > 1 ? ['data-cell', 'middle'] : arr.length ? ['data-cell', ...arr] : arr,
        overlapIds,
    };
    datePositionCache.value.set(cacheKey, result);
    return result;
}

// 缓存 `countMatchingDateRanges` 的结果
const cachedCounts = computed(() => new Map<string, string[]>());

function countMatchingDateRanges(targetDate: string | number, record: Record<string, any>): string[] {
    const dateRanges = record?.targetDateVOIds?.map((item) => inAndOutDateVOMap.value.get(item)) || [];
    if (dateRanges && dateRanges.filter((item) => item).length === 0) return [];

    const key = `${record.id}_${record.dataType}_${targetDate}_${dateRanges.map((item) => `${item.inDate}-${item.outDate}`)}`;

    if (cachedCounts.value.has(key)) {
        return cachedCounts.value.get(key);
    }

    targetDate = Number(targetDate);
    const ids: string[] = [];

    for (const { inDate, outDate, id } of dateRanges) {
        const startDateObj = dayjs(inDate).valueOf();
        const endDateObj = dayjs(outDate || inDate).valueOf();
        if (targetDate >= startDateObj && targetDate <= endDateObj) {
            ids.push(id);
        }
    }

    cachedCounts.value.set(key, ids);

    return ids;
}

// 时间对象数据格式化时间戳数组
function getDateRangeTimestamps(data): Set<number> {
    const timestampsSet = new Set<number>();
    const quarterMinDate = yearQuarterTimestamps.value[props.quarterKey][0];
    const quarterMaxDate = yearQuarterTimestamps.value[props.quarterKey][yearQuarterTimestamps.value[props.quarterKey].length - 1];
    data.forEach((item) => {
        const start = Math.max(dayjs(item.inDate).valueOf(), quarterMinDate);
        const end = Math.min(dayjs(item.outDate).valueOf(), quarterMaxDate);

        for (let date = start; date <= end; date = dayjs(date).add(1, 'day').valueOf()) {
            timestampsSet.add(date);
        }
    });

    return timestampsSet;
}

const inOutOverlapMapKey = computed(() => (record) => (true ? record.userCode : record.materialCode));
const inOutOverlapMap = new Map<string, number>();

// 优化 `formatData` 函数
function formatData(data: any[]) {
    const len: number = data.length;
    const arr: any[] = [];
    inOutOverlapMap.clear();
    for (let i = 0; i < len; i++) {
        const item = data[i];
        if ('targetDateVOList' in item) {
            item.targetDateVOIds = item.targetDateVOList?.map((item) => item.id);
        }
        switch (item.dataType) {
            case '2':
                if (i < len - 1 && data[i + 1].dataType === '3') {
                    item.rowSpan = 2;
                    let overlapCount = 0;
                    getDateRangeTimestamps(data[i + 1]?.inAndOutDateVOList || []).forEach((timestamp) => {
                        const taskOverlapIds = countMatchingDateRanges(timestamp, data[i + 1]);
                        if (taskOverlapIds.length > 0) {
                            overlapCount++;
                        }
                    });
                    item.overlapCount = overlapCount;

                    inOutOverlapMap.set(inOutOverlapMapKey.value(item), (inOutOverlapMap.get(inOutOverlapMapKey.value(item)) || 0) + overlapCount);
                }
                break;
            case '3':
                if (i >= 1 && data[i - 1].dataType === '2') {
                    item.rowSpan = 0;
                }
                break;
        }

        delete item.targetDateVOList;
        arr.push(item);
    }
    return arr;
}

function customRender({ record, text, column }) {
    const obj = {
        props: {
            rowSpan: record?.rowSpan || 1,
        } as any,
        children: '' as any,
    };
    switch (column.dataIndex) {
        case 'userCode':
        case 'fullName':
        case 'materialCode':
        case 'materialName':
            if (record.dataType === '1') {
                obj.children = text;
            }
            break;
        case 'overlapCount':
            if (record.dataType === '1') {
                obj.children = inOutOverlapMap.get(inOutOverlapMapKey.value(record)) || '';
            }
            if (record.dataType === '2') {
                obj.children = text || '';
            }
            break;
        case 'jobName':
            if (record.dataType === '2') {
                obj.children = text;
            }
            break;
        default:
            if (record.dataType !== '1') {
                obj.children = text;
            }
    }
    return obj;
}

function formatDateString(record: { inDate?: string, outDate?: string }) {
    let inDateString: string = '';
    let outDateString: string = '';
    if (record?.inDate) {
        inDateString = dayjs(record.inDate).format('YYYY.MM.DD');
    }
    if (record?.outDate) {
        outDateString = dayjs(record.outDate).format('YYYY.MM.DD');
    }
    return [inDateString, outDateString].filter((item) => item).join('-');
}

function getDateStrings(record, isToolTip: boolean = false) {
    return record?.inAndOutDateVOList?.filter((item) => isToolTip || (record.dataType === '1' && item.currentQuarter) || record.dataType !== '1')?.map((item) => formatDateString(item));
}

function getColumns() {
    const quarterColumns = yearQuarterTimestamps.value[props.quarterKey].map((item) => ({
        title: dayjs(item).format('D') === '1' ? dayjs(item).format('M.D') : dayjs(item).format('D'),
        dataIndex: item,
        width: 45,
        key: item,
        align: 'center',
        type: 'day',
    }));
    return [
        true ? [
            {
                title: '员工号',
                dataIndex: 'userCode',
                fixed: 'left',
                align: 'center',
                width: 80,
                ellipsis: true,
                customRender,
            },
            {
                title: '姓名',
                dataIndex: 'fullName',
                fixed: 'left',
                align: 'center',
                width: 80,
                customRender,
            },
        ] : [],
        true ? [
            {
                title: '物资编码',
                dataIndex: 'materialCode',
                fixed: 'left',
                align: 'center',
                width: 125,
                ellipsis: true,
                customRender,
            },
            {
                title: '物资名称',
                dataIndex: 'materialName',
                fixed: 'left',
                align: 'center',
                width: 100,
                ellipsis: true,
                customRender,
            },
        ] : [],
        true ? [
            {
                title: '大修轮次',
                dataIndex: 'repairRound',
                fixed: 'left',
                align: 'center',
                width: 80,
                customRender,
            },
        ] : [],
        {
            title: '作业名称',
            dataIndex: 'jobName',
            fixed: 'left',
            width: 100,
            customRender,
        },
        true ? [
            {
                title: '重叠天数',
                dataIndex: 'overlapCount',
                fixed: 'left',
                align: 'center',
                width: 80,
                customRender,
            },
        ] : [],
        {
            title: '时间周期',
            dataIndex: 'dataType',
            width: 80,
            colSpan: 2,
            align: 'center',
            fixed: 'left',
            customRender({ text }) {
                switch (text) {
                    case '1':
                        return '计划进出';
                    case '2':
                        return '计划作业';
                    case '3':
                        return '计划任务';
                }
            },
        },
        {
            title: '',
            dataIndex: 'inAndOutDateVOList',
            width: 160,
            fixed: 'left',
            colSpan: 0,
            tooltip: {
                trigger: 'click',
                placement: 'topLeft',
                overlayStyle: {
                    maxWidth: '475px',
                },
            },
            customRender({ record }) {
                return `${getDateStrings(record)?.[0] || ''}`;
            },
        },
        ...quarterColumns,
    ];
}

// 选择任务单元格
enum CellStatusText {
    ADD = 'add',
    EDIT = 'edit',
}

enum CellDataType {
    INOUT = 0,
    TASK = 1,
}

function open() {
    const modal = openModal({
        title: '弹窗标题',
        content(h) {
            return h('div', '自定义内容')
        },
        onOk() {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve()
                }, 3000)
            })
        }
    })

    setTimeout(() => {
        modal.close()
    }, 3000)
}

function handleCell(record, column) {
    console.log('handleCell');
    open();
    const targetDate = Number(column.dataIndex);
    if (record?.jobBeginDate && record?.jobEndDate) {
        if (dayjs(record.jobBeginDate).valueOf() > targetDate || dayjs(record.jobEndDate).valueOf() < targetDate) {
            return message.info('请选择计划作业时间周期内的日期');
        }
    }

    const editIndex = record?.inAndOutDateVOList?.findIndex((item) => item.statusText || (!item.inDate && !item.outDate));

    if (true || editIndex !== -1) {
        const item = record.inAndOutDateVOList[editIndex];
        if (!item?.inDate && !item?.outDate) {
            Object.assign(item, {
                id: item?.id,
                type: record.dataType === '1' ? CellDataType.INOUT : CellDataType.TASK,
                statusText: CellStatusText.ADD,
                currentQuarter: true,
            });
        }

        switch (item.statusText) {
            case CellStatusText.ADD:
                item.inDate = targetDate;
                item.statusText = CellStatusText.EDIT;
                break;
            case CellStatusText.EDIT:
                if (targetDate < dayjs(item.inDate).valueOf()) {
                    item.outDate = _.cloneDeep(item.inDate);
                    item.inDate = targetDate;
                } else {
                    item.outDate = targetDate;
                }
                delete item.statusText;
                record.inAndOutDateVOList.splice(editIndex, 1, item);
                break;
        }
    } else {
        switch (record.dataType) {
            case '1':
                message.info('计划进出时间已设置，请右键删除后操作');
                break;
            case '3':
                message.info('计划任务时间已设置，请右键删除后操作');
                break;
        }
    }
}

function customRow(record) {
    return {
        style: { background: record?.dataType === '1' ? '#F8F9FC !important' : record?.dataType === '2' ? '#FFFFFF !important' : '#F1FBF5 !important' },
    };
}

// 存储单元格数据
const dataCellMap = computed(() => new Map<string, Record<string, any>>());

function customCell({ column, record }) {
    if (column.type === 'day') {
        let { positions, overlapIds } = checkDatePosition(column.dataIndex, record);//这里可以决定渲染或者填充的表格positions = ['data-cell', 'middle']
        let cellClassName = [];
        if (positions && positions.length) {
            //这里可以根据不同的类型来设置不同的颜色
            // cellClassName.push(`${record.dataType === '1' ? 'in-out' : record.dataType === '2' ? 'job' : 'task'}`);
            //   cellClassName.push(`${'in-out'}`);
            switch (record.dataType) {
                // case '1':
                //   if (overlapIds.length > 1) {
                //     dataCellMap.value.set(`${record.uniqueId}-${column.dataIndex}`, {
                //       unLeaderData: overlapIds.filter((id) => !inAndOutDateVOMap.value.get(id)?.leaderData),
                //       overlapIds,
                //       timestamp: column.dataIndex,
                //     });
                //     cellClassName.push('overlap');
                //   } else {
                //     dataCellMap.value.delete(`${record.uniqueId}-${column.dataIndex}`);
                //   }
                //   break;
                // case '3':
                //   //重叠部分
                //   const taskOverlapIds = countMatchingDateRanges(column.dataIndex, record);
                //   if (taskOverlapIds.length > 0) {
                //     cellClassName.push('overlap');
                //   }
                //   dataCellMap.value.set(`${record.uniqueId}-${column.dataIndex}`, {
                //     showMenu: true,
                //     overlapIds,
                //     timestamp: column.dataIndex,
                //   });
                //   break;
                // default:
            }
            //   console.log(cellClassName)
        } else if (record.dataType === '3') {
            dataCellMap.value.delete(`${record.uniqueId}-${column.dataIndex}`);
        }
        // console.log(props);
        // cellClassName = props.legend.filter((item) => console.log(cellClassName,item.class)).map((item) => item.class);
        //下面这段确定了所要渲染的表格及对应的颜色
        // cellClassName = props.legend.filter((item) => cellClassName.includes(item.class) && !item.disabled).map((item) => item.class);
        // console.log(cellClassName);
        return {
            class: positions.concat(cellClassName).join(' '),
            onClick() {
                if (!props.isEdit) return;
                handleCell(record, column);
            },
        };
    }

    if (column.dataIndex === 'jobName') {
        return {
            class: `job-name-row ${record.rowSpan ? 'job-name-row-3' : 'job-name-row-1'}`,
            title: record.jobName,
            onClick() {
                if (record.jobId && props.repairRound) {
                    const jobId = String(record.jobId).trim();
                    const repairRound = String(props.repairRound).trim();
                    if (jobId && repairRound) {
                        emits('close');
                        try {
                            router.push({
                                name: 'OverhaulOperationDetails',
                                params: {
                                    id: jobId,
                                },
                                query: {
                                    id: repairRound,
                                },
                            });
                        } catch (error) {
                            console.error('Router push failed:', error);
                        }
                    } else {
                        console.error('Invalid jobId or repairRound');
                    }
                }
            },
        };
    }
    return {};
}

const isMenuVisible = computed(() => ({ record, column }) => {
    const cellData = dataCellMap.value.get(`${record.uniqueId}-${column.dataIndex}`);
    switch (record.dataType) {
        case '1':
            return !!cellData?.unLeaderData?.length;
        case '3':
            return !!cellData?.showMenu;
        default:
            return false;
    }
});

async function menuClick({ domEvent }, { record, column }) {
    const { uniqueId, dataType } = record;
    const { dataIndex } = column;
    const cellData = dataCellMap.value.get(`${uniqueId}-${dataIndex}`);
    const cellOverlapId = cellData?.overlapIds?.at(-1);

    if (cellOverlapId) {
        const { listIndex } = inAndOutDateVOMap.value.get(cellOverlapId);
        record.inAndOutDateVOList.splice(listIndex, 1, {
            id: cellOverlapId,
            type: dataType === '1' ? CellDataType.INOUT : CellDataType.TASK,
            statusText: CellStatusText.ADD,
            currentQuarter: true,
        });
    }
}

const tableRef = ref();

watchEffect(async () => {
    if (tableRef.value && props.minDate) {
        await nextTick();
        tableRef.value.scrollTo({
            columnKey: props.minDate,
            behavior: 'smooth',
        });
    }
});

const tableWrapRef: Ref = ref();
const tableWrapHeight: Ref = ref(0);
onMounted(() => {
    addResizeListener(tableWrapRef.value, nodeResize);
});

function nodeResize() {
    const height = (tableWrapRef.value?.clientHeight || 0) - 60;
    tableWrapHeight.value = height < 0 ? 0 : height;
}

const getRepeat = computed(() => (record) => {
    const length = record?.inAndOutDateVOList?.length || 0;
    return Math.min(length, 3);
});

defineExpose({
    getData() {
        return dataSource.value;
    },
    quarterChange() {
        const minDate = yearQuarterTimestamps.value[props.quarterKey][0];
        const maxDate = yearQuarterTimestamps.value[props.quarterKey][yearQuarterTimestamps.value[props.quarterKey].length - 1];

        const timestamps = [];
        for (const [_key, { inDate, outDate }] of inAndOutDateVOMap.value.entries()) {
            if (inDate && outDate && dayjs(inDate).valueOf() <= maxDate) {
                const startDate = dayjs(inDate).valueOf();
                const timestamp = Math.min(Math.max(minDate, startDate), Math.min(startDate, maxDate));
                timestamps.push(timestamp);
            }
        }

        const minDateTimestamp = timestamps.length ? Math.max(Math.min(...timestamps), minDate) : minDate;
        if (tableRef.value && minDateTimestamp !== Infinity) {
            tableRef.value.scrollTo({
                columnKey: minDateTimestamp,
                behavior: 'smooth',
            });
        }
    },
});
</script>

<template>
    <div ref="tableWrapRef" class="table-wrap">
        <STable ref="tableRef" bordered :ignoreCellKey="true" :data-source="dataSource" rowKey="uniqueId" size="small"
            :rowHeight="38" :pagination="false" :columns="getColumns()" :animateRows="false" :customCell="customCell"
            :customRow="customRow" :scroll="{ x: 'max-content', y: tableWrapHeight }" expand-row-by-click>
            <!--tooltip-->
            <template #tooltipTitle="{ record }">
                <div class="tooltip-title" :style="{
                    gridTemplateColumns: `repeat(${getRepeat(record)}, 140px)`
                }">
                    <span v-for="(item, index) in getDateStrings(record, true)" :key="index">
                        {{ item }}
                    </span>
                </div>
            </template>
            <!--右键菜单-->
            <template v-if="isEdit" #contextmenuPopup="args">
                <Menu v-if="isMenuVisible(args)" style="width: 80px" @click="menuClick($event, args)">
                    <MenuItem key="del">
                    <DeleteOutlined />
                    删除
                    </MenuItem>
                </Menu>
            </template>
        </STable>
    </div>
</template>

<style scoped lang="less">
.table-wrap {
    height: calc(100% - 115px);
    min-height: 400px;
}

:deep(.data-cell) {
    .surely-table-cell-inner {
        position: absolute;
        content: '';
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 50% !important;
        overflow: hidden;
        z-index: 1;
    }

    &.unit {
        .surely-table-cell-inner {
            border-radius: 4px;
        }
    }

    &.start {
        border-right: none !important;

        .surely-table-cell-inner {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
    }

    &.end {
        .surely-table-cell-inner {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }

    &.middle {
        border-right: none !important;

        .surely-table-cell-inner {
            border-radius: 0;
        }
    }

    &.in-out {
        .surely-table-cell-inner {
            background-color: #B5C2DD;
        }
    }

    &.job {
        .surely-table-cell-inner {
            background-color: #AFE8C5;

            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #60D38D;
            }
        }
    }

    &.task {
        .surely-table-cell-inner {
            background-color: #60D38D;
        }
    }

    &.overlap {
        .surely-table-cell-inner {
            background-color: #FF928A;
        }
    }
}

.tooltip-title {
    display: grid;
    gap: 10px 20px;
}

:deep(.job-name-row) {
    .surely-table-cell-inner {
        overflow: unset;
    }

    .surely-table-cell-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
        overflow: hidden;
        max-height: 75px;
    }

    &-1 {
        .surely-table-cell-content {
            -webkit-line-clamp: 1;
        }
    }

    &-3 {
        .surely-table-cell-content {
            -webkit-line-clamp: 3;
        }
    }
}

:deep(.ant-menu-item) {
    line-height: 24px;
    height: 24px;
}

:deep(.cp) {
    cursor: pointer;
}

:deep(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
}
</style>
