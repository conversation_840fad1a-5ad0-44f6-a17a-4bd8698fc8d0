<template>
  <OrionTable
    ref="tableRef"
    class="pdmBasicTable"
    :options="tableOptions"
    :columns="columns"
    :dataSource="dataSource"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PAS_WTGLXQ_container_02_01_button_01',powerData)"
        type="primary"
        icon="add"
        @click="clickType('add')"
      >
        新增
      </BasicButton>
      <BasicButton
        v-if="

          ('PAS_WTGLXQ_container_02_01_button_02',powerData)"
        icon="delete"
        @click="clickType('delete')"
      >
        删除
      </BasicButton>
    </template>

    <template #planPredictStartTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #planPredictEndTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>

    <template #action="{record}">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>

  <RightTool
    :btn-list="btnList"
    @clickType="clickType"
  />
  <!-- 查看详情弹窗 -->
  <checkDetails :data="nodeData" />
  <!-- 简易弹窗提醒 -->
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{ message }}</span>
    </div>
  </messageModal>
  <!-- 新建/编辑抽屉 -->
  <addProjectModal
    :data="addNodeModalData"
    :list-data="editdataSource"
    :projectid="id"
    @success="successSave"
  />
  <!-- 从系统添加 -->
  <AddSystemRole
    :id="id"
    :data="addSystemModalData"
    @success="successSave"
  />

  <!-- 高级搜索抽屉 -->
  <searchModal
    :data="searchData"
    :projectid="id"
    @search="searchTable"
  />
  <SearchModal
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, onMounted, reactive, Ref, toRefs, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, RightTool, useDrawer, DataStatusTag,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import SearchModal from './SearchModal.vue';
import Api from '/@/api';
import { useQiankun } from '/@/utils/qiankun/useQiankun';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicTableAction,
    BasicButton,
    OrionTable,
    InfoCircleOutlined,
    messageModal,
    checkDetails,
    RightTool,
    addProjectModal,
    searchModal,
    AddSystemRole,
    SearchModal,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const router = useRouter();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const tableRef = ref(null);
    const state = reactive({
      tableRef: null,
      searchvlaue: '',
      editdataSource: {},
      selectedRowKeys: [],
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},

      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
    });
    const formData:any = inject('formData');
    const powerData:Ref = inject('powerData');
    const state6 = reactive({
      btnList: [
        'check',
        'open',
        'add',
        'delete',
      ],
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            if (isPower('PAS_WTGLXQ_container_02_01_button_04', powerData.value)) {
              return h(
                'span',
                {
                  class: 'action-btn',
                  title: text,
                  onClick(e) {
                    let routerName = record.planType !== '0' ? 'PlanDetails' : 'MilestoneDetails';
                    router.push({
                      name: routerName,
                      query: {
                        id: record.id,
                        projectId: formData?.value.projectId,
                      },
                    });
                  },
                },
                text,
              );
            }
            return text;
          },
          align: 'left',
          ellipsis: true,
        },

        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          key: 'planType',
          width: '100px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'planTypeName' },
          ellipsis: true,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',
          width: '80px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '计划进度',
          dataIndex: 'scheduleName',
          key: 'schedule',
          width: '90px',
          align: 'left',
          slots: { customRender: 'scheduleName' },
          ellipsis: true,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          key: 'priorityLevel',
          width: '80px',
          align: 'left',
          slots: { customRender: 'priorityLevelName' },
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: '80px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          customRender({ record }) {
            return record.dataStatus ? h(DataStatusTag, {
              statusData: record.dataStatus,
            }) : '';
          },
        },
        {
          title: '开始日期',
          dataIndex: 'planPredictStartTime',
          key: 'planPredictStartTime',
          width: '130px',
          align: 'left',
          slots: { customRender: 'planPredictStartTime' },
          sorter: true,
          ellipsis: true,
        },

        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
          key: 'planPredictEndTime',
          width: '150px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'planPredictEndTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    });

    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order === 'ascend';
      state.tablehttp.orders[0].column = sorter.columnKey;

      getFormData();
    };
    /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          openPlan();
          break;
        case 'delete':
          multiDelete();
          break;
        case 'search':
          openSearchDrawer(true);
          break;
      }
    };
    const openPlan = () => {
      if (lengthCheckHandle()) return;
      router.push({
        name: 'PlanDetails',
        query: {
          id: state.selectedRowKeys[0],
          projectId: formData?.value.projectId,
        },
      });
    };
    const addSystemRoleHandle = () => {
      state.addSystemModalData = { formType: 'add' };
    };
    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = [...state.selectedRows];
    };

    /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;
      getFormData();
    });

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        id: formData?.value.id,
        planIds: state.selectedRowKeys,
      };
      const love = {
        className: 'QuestionManagement',
        moduleName: '项目管理-问题管理-关联计划',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      new Api('/pas/question-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          tableRef.value.reload();
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };

    const getFormData = async (obj = {}) => {
      state.tablehttp.query.projectId = formData?.value.id;
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    function searchEmit(data) {
      getFormData(data);
    }
    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id === state.selectedRowKeys[0]),
      };
    };
    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      getFormData();
      state.searchvlaue = '';
    };

    /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
    /* 搜索右上 */
    const onSearch = () => {
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
    /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      tableRef.value.reload();
      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };

    const tableOptions = {
      showIndexColumn: false,
      pagination: false,
      bordered: false,
      deleteToolButton: 'add|delete|enable|disable',
      rowClick: clickRow,
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onChange: onSelectChange,
      },
      api(params) {
        return new Api(`/pas/question-management/relation/plan/${formData?.value.id}`).fetch(params, '', 'POST');
      },
    };

    const actionsBtn = (record) => {
      const actions:ITableActionItem<any>[] = [
        {
          text: '删除',
          isShow: () => isPower('PAS_WTGLXQ_container_02_01_button_03', powerData.value),
          modal() {
            const newArr = {
              id: formData?.value.id,
              planIds: [record.id],
            };
            return new Api('/pas/question-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
              .then((res) => {
                message.success('删除成功');
                tableRef.value.reload();
              })
              .catch(() => {
                state.showVisible = false;
              });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      addSystemRoleHandle,
      searchRegister,
      searchEmit,
      tableOptions,
      actionsBtn,
      powerData,
      tableRef,
    };
  },
  methods: { isPower },
});
</script>
<style lang="less" scoped>
</style>
