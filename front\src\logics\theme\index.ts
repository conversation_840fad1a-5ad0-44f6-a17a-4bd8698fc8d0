// import { replaceStyleVariables } from 'vite-plugin-theme/es/client';
// import { mixLighten, mixDarken, tinycolor } from 'vite-plugin-theme/es/colorUtils';
// import { getThemeColors, generateColors } from '../../../build/config/themeConfig';

export async function changeTheme() {
  // const colors = generateColors({
  //   // mixDarken,
  //   // mixLighten,
  //   // tinycolor,
  //   color,
  // });

  // return await replaceStyleVariables({
  //   // colorVariables: [...getThemeColors(color), ...colors],
  //   colorVariables: []
  // });
}
