package com.chinasie.orion.domain.vo.projectOverviewNew;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/14:49
 * @description:
 */
@Data
@ApiModel(value = "ProjectInfoVO", description = "项目详情")
public class ProjectInfoVO implements Serializable {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目负责人")
    private String pm;

    /**
     * 项目立项时间
     */
    @ApiModelProperty(value = "项目立项时间")
    private Date projectApproveTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date projectStartTime;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态id")
    private String statusId;

    /**
     * 项目状态名称
     */
    @ApiModelProperty(value = "项目状态名称")
    private String statusIdName;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Double schedule;


    @ApiModelProperty(value = "计划未开始数量")
    private Integer noStartCount=0;
    @ApiModelProperty(value = "计划进行中数量")
    private Integer underwayCount=0;
    @ApiModelProperty(value = "计划已完成数量")
    private Integer completeCount=0;
}
