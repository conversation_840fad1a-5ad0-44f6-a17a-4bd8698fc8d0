package com.chinasie.orion.domain.dto.acceptance;

import java.util.Objects;

/**
 * 验收单类型.
 */
public enum AcceptanceFormType {
    // 采购计划项验收单
    ACCEPTANCE_FORM,
    // 项目验收单
    PROJECT;

    public static boolean typeExists(String value) {
        try {
            AcceptanceFormType type = valueOf(value);
            return type != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static AcceptanceFormType getType(String value) {
        try {
            return valueOf(value);
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean checkType(AcceptanceFormType type, String value) {
        return Objects.equals(type, getType(value));
    }
}
