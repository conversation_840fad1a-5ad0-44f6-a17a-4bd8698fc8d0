<script setup lang="ts">
import { Popover, Table } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  h, nextTick, onMounted, reactive,
} from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps<{
  years: number[]
  data: Record<string, any>
  loading: boolean
}>();

const router = useRouter();
const children = [];
for (let j = 0; j < 2; j++) {
  for (let i = 1; i <= 12; i++) {
    let obj = {
      title: i,
      dataIndex: j === 0 && i === 1 ? 'repair-block' : '',
      align: 'center',
      width: 70,
    };
    children.push(obj);
  }
}

const columns: any[] = [
  {
    title: '',
    dataIndex: 'index',
    width: 40,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '大修轮次',
    dataIndex: 'repairRound',
    align: 'center',
    width: 80,
    fixed: 'left',
    customRender({ text, record }) {
      return h('div', {
        class: 'flex-te action-btn',
        title: text,
        onClick: () => navDetails(record?.repairRoundId),
      }, text);
    },
  },
  {
    title: '工期(天)',
    dataIndex: 'workDuration',
    width: 70,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '所属基地',
    dataIndex: 'baseName',
    align: 'center',
    width: 100,
    fixed: 'left',
  },
  {
    title: '开始时间',
    dataIndex: 'beginTime',
    width: 100,
    align: 'center',
    fixed: 'left',
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    width: 100,
    align: 'center',
    fixed: 'left',
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '大修类型',
    dataIndex: 'typeName',
    align: 'center',
    fixed: 'left',
  },
  {
    title: '大修日历',
    children,
  },
];

function customHeaderRow() {
  return {
    class: 'table-header-row',
  };
}

function customRow(record) {
  return {
    class: `${record.majorStatus === 160 ? 'finished' : record.majorStatus === 110 ? 'progress' : record.majorStatus === 121 ? 'prepare' : ''} repair-block-row table-body-row`,
  };
}

// 获取当前大修图例左定位
function getLeft(beginTime: string) {
  return getWidthByDate(beginTime);
}

// 根据日期获取宽度
function getWidthByDate(date: Dayjs | string): number {
  const minDate = dayjs(props.years[0].toString()).startOf('year').valueOf();
  const maxDate = dayjs(props.years[1].toString()).endOf('year').valueOf();
  let nowDate = dayjs(date).valueOf();
  if (nowDate < minDate) {
    nowDate = minDate;
  } else if (nowDate > maxDate) {
    nowDate = maxDate;
  }
  const month = dayjs(nowDate).month();
  const step = 70;
  const yearStep = (dayjs(nowDate).year() - props.years[0]) * 12 * step;
  const monthDay = dayjs(nowDate).daysInMonth();
  const diffDay = dayjs(nowDate).diff(dayjs(nowDate).startOf('month'), 'day') + 1;
  const ratio = diffDay / monthDay;
  const width = step * month + ratio * step + yearStep;
  return Math.ceil(width) < 0 ? 0 : Math.ceil(width);
}

// 获取当前大修图例宽度
function getWidth(beginTime: string, endTime: string) {
  return getWidthByDate(endTime) - getWidthByDate(beginTime);
}

function navDetails(id: string) {
  router.push({
    name: 'MajorRepairsSecondDetail',
    params: {
      id,
    },
  });
}

const scroll = reactive({
  x: 2260,
  y: 300,
});

function getTitle(record) {
  switch (record?.majorStatus) {
    case 110:
      return `正在进行的大修[${record?.repairRound}]`;
    case 121:
      return `正在准备的大修[${record?.repairRound}]`;
    case 160:
      return `已完成的大修[${record?.repairRound}]`;
  }
}

onMounted(async () => {
  await nextTick();
  defaultScroll();
});

function defaultScroll() {
  const month = dayjs().month() + 1 - 2;
  document.querySelector('.ant-table-body').scrollLeft = month * 70 - 75;
}
</script>

<template>
  <Table
    :customHeaderRow="customHeaderRow"
    :customRow="customRow"
    :dataSource="data?.list||[]"
    :columns="columns"
    :pagination="false"
    bordered
    size="small"
    :loading="loading"
    :scroll="scroll"
  >
    <template #bodyCell="{ record, index, column}">
      <span v-if="column.dataIndex==='index'">{{ index + 1 }}</span>
      <Popover
        v-if="column.dataIndex==='repair-block'"
        :title="getTitle(record)"
        placement="rightBottom"
      >
        <template #content>
          <div class="flex flex-ver">
            <span>计划开始时间：{{
              record?.beginTime ? dayjs(record?.beginPlanTime).format('YYYY-MM-DD') : ''
            }}</span>
            <span>计划完成时间：{{
              record?.endTime ? dayjs(record?.endPlanTime).format('YYYY-MM-DD') : ''
            }}</span>
            <span>实际开始时间：{{
              record?.actualBeginTime ? dayjs(record?.actualBeginTime).format('YYYY-MM-DD') : ''
            }}</span>
            <span>实际完成时间：{{
              record?.actualEndTime ? dayjs(record?.actualEndTime).format('YYYY-MM-DD') : ''
            }}</span>
            <span>大修工期：{{ record?.workDuration || '' }}</span>
            <span>大修经理：{{ record?.majorRepairManagerName || '' }}</span>
            <span>大修类别：{{ record?.typeName || '' }}</span>
            <span>当前时间：{{ dayjs().format('YYYY-MM-DD') }}</span>
          </div>
        </template>
        <div
          class="repair-block"
          :style="{width:getWidth(record.beginTime,record.endTime)+'px',left:getLeft(record.beginTime)+'px'}"
          @click="navDetails(record.repairRoundId)"
        >
          {{ record.repairRound }}
        </div>
      </Popover>
    </template>
  </Table>
</template>

<style scoped lang="less">
:deep(.ant-table-header) {
  & > table {
    border-top: none !important;
  }
}

:deep(.table-header-row) {
  > th {
    background-color: #eee !important;
    border-right: 2px solid #fff !important;
    border-bottom: 2px solid #fff !important;

    &.ant-table-cell-scrollbar {
      display: none;
    }
  }
}

:deep(.table-body-row) {
  > td {
    border-right: 2px solid #fff !important;
    border-bottom: 2px solid #fff !important;
  }

  &.finished > td {
    background-color: #DBECBA;
  }

  &.progress > td {
    background-color: #B3E3F8;
  }

  &.prepare > td {
    background-color: #FFF3B3;
  }
}

:deep(.repair-block-row) {
  position: relative;
  overflow: hidden;

  .repair-block {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    cursor: pointer;
    transform: translateY(-50%);
    width: 50px;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    z-index: 1;
    white-space: nowrap;
  }

  &.finished .repair-block {
    background-color: #85BD19;
  }

  &.progress .repair-block {
    background-color: #00A1E5;
  }

  &.prepare .repair-block {
    background-color: #FFD600;
  }
}
</style>
