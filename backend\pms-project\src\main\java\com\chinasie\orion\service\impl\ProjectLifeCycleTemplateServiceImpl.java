package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.TakeEffectEnum;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleTemplateDTO;
import com.chinasie.orion.domain.entity.ProjectLifeCycleTemplate;
import com.chinasie.orion.domain.vo.ProjectLifeCycleTemplateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectLifeCycleTemplateMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectLifeCycleTemplateService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * ProjectLifeCycleTemplate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@Service
@Slf4j
public class ProjectLifeCycleTemplateServiceImpl extends OrionBaseServiceImpl<ProjectLifeCycleTemplateMapper, ProjectLifeCycleTemplate> implements ProjectLifeCycleTemplateService {

    @Autowired
    private LyraFileBO fileBo;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectLifeCycleTemplateDTO detail(String id, String pageCode) throws Exception {
        ProjectLifeCycleTemplate projectLifeCycleTemplate = this.getById(id);
        ProjectLifeCycleTemplateDTO result = BeanCopyUtils.convertTo(projectLifeCycleTemplate, ProjectLifeCycleTemplateDTO::new);
        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        result.setFileDtoList(fileDtoList);

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectLifeCycleTemplateDTO
     */
    @Override
    public String create(ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception {
        ProjectLifeCycleTemplate projectLifeCycleTemplate = BeanCopyUtils.convertTo(projectLifeCycleTemplateDTO, ProjectLifeCycleTemplate::new);
        projectLifeCycleTemplate.setFileNum(projectLifeCycleTemplateDTO.getFileDtoList().size());
        projectLifeCycleTemplate.setStatus(TakeEffectEnum.UN_EFFECT.getStatus());
        this.save(projectLifeCycleTemplate);

        String id = projectLifeCycleTemplate.getId();
        List<FileVO> fileDtoList = projectLifeCycleTemplateDTO.getFileDtoList();
        if (CollectionUtil.isNotEmpty(fileDtoList)) {
            fileDtoList.forEach(f -> f.setDataId(id));
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(fileDtoList, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }
        return id;
    }

    /**
     * 编辑
     * <p>
     * * @param projectLifeCycleTemplateDTO
     */
    @Override
    public Boolean edit(ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception {
        ProjectLifeCycleTemplate projectLifeCycleTemplate = BeanCopyUtils.convertTo(projectLifeCycleTemplateDTO, ProjectLifeCycleTemplate::new);
        projectLifeCycleTemplate.setFileNum(projectLifeCycleTemplateDTO.getFileDtoList().size());
        projectLifeCycleTemplate.setModifyId(null);
        projectLifeCycleTemplate.setModifyTime(null);
        this.updateById(projectLifeCycleTemplate);

        String id = projectLifeCycleTemplate.getId();
        List<FileVO> newFileDtoList = projectLifeCycleTemplateDTO.getFileDtoList();
        handleFile(id, newFileDtoList);
        return true;
    }

    /**
     * 新增删除文件
     *
     * @param id
     * @param newFileDtoList
     * @throws Exception
     */
    @Override
    public void handleFile(String id, List<FileVO> newFileDtoList) throws Exception {
        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        List<String> fileIdList = newFileDtoList.stream().filter(f -> StrUtil.isNotBlank(f.getId())).map(FileVO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fileDtoList)) {
            List<String> deleteIdList = fileDtoList.stream().filter(f -> !fileIdList.contains(f.getId())).map(FileVO::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deleteIdList)) {
                fileBo.deleteFileByIds(deleteIdList);
            }
        }

        List<FileVO> saveFileList = newFileDtoList.stream().filter(f -> StrUtil.isBlank(f.getId())).peek(p -> p.setDataId(id)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(saveFileList)) {
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(saveFileList, FileDTO::new);
            fileBo.addBatch(fileDTOS);
        }
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectLifeCycleTemplate> projectLifeCycleTemplateList = this.listByIds(ids);
        if (projectLifeCycleTemplateList.stream().anyMatch(a -> TakeEffectEnum.EFFECT.getStatus().equals(a.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "删除失败，存在启用状态的数据");
        }
        this.removeBatchByIds(ids);
        List<FileVO> fileDtoList = fileBo.getFileDtoListByDataIds(ids);
        if (CollectionUtil.isNotEmpty(fileDtoList)) {
            fileBo.deleteFileByIds(fileDtoList.stream().map(FileVO::getId).collect(Collectors.toList()));
        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectLifeCycleTemplateVO> pages(Page<ProjectLifeCycleTemplateDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectLifeCycleTemplate> condition = new LambdaQueryWrapperX<>(ProjectLifeCycleTemplate.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ProjectLifeCycleTemplateDTO query = pageRequest.getQuery();
        if (query != null) {
            Integer status = query.getStatus();
            if (status != null) {
                condition.eq(ProjectLifeCycleTemplate::getStatus, status);
            }
        }
        List<OrderItem> orders = pageRequest.getOrders();
        if (CollectionUtil.isEmpty(orders)) {
            condition.orderByDesc(ProjectLifeCycleTemplate::getCreateTime);
        } else {
            SearchConditionUtils.analysisOrder(orders, condition);
        }

        Page<ProjectLifeCycleTemplate> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectLifeCycleTemplate> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectLifeCycleTemplateVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectLifeCycleTemplateVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectLifeCycleTemplateVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 启用禁用
     *
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    @Override
    public Boolean takeEffectBatch(TakeEffectDTO takeEffectDTO) throws Exception {
        List<String> idList = takeEffectDTO.getIdList();
        Integer takeEffect = takeEffectDTO.getTakeEffect();
        if (!TakeEffectEnum.EFFECT.getStatus().equals(takeEffect) && !TakeEffectEnum.UN_EFFECT.getStatus().equals(takeEffect)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectLifeCycleTemplate> projectLifeCycleTemplateList = this.listByIds(idList);
        if (CollectionUtil.isEmpty(projectLifeCycleTemplateList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        projectLifeCycleTemplateList.forEach(f -> f.setStatus(takeEffect));
        return this.updateBatchById(projectLifeCycleTemplateList);
    }

    @Override
    public List<String> copy(List<String> ids) throws Exception {
        List<ProjectLifeCycleTemplate> projectLifeCycleTemplateList = this.listByIds(ids);
        if (CollectionUtil.isEmpty(projectLifeCycleTemplateList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<String> result = new ArrayList<>();
        for (ProjectLifeCycleTemplate projectLifeCycleTemplate : projectLifeCycleTemplateList) {
            String oldId = projectLifeCycleTemplate.getId();
            projectLifeCycleTemplate.setId(null);
            projectLifeCycleTemplate.setCreatorId(null);
            projectLifeCycleTemplate.setCreateTime(null);
            projectLifeCycleTemplate.setStatus(TakeEffectEnum.UN_EFFECT.getStatus());
            projectLifeCycleTemplate.setName(String.format("%s_副本", projectLifeCycleTemplate.getName()));
            this.save(projectLifeCycleTemplate);
            fileBo.copyFileByOldIdToNewId(projectLifeCycleTemplate.getId(), oldId);
            result.add(projectLifeCycleTemplate.getId());
        }
        return result;
    }

}
