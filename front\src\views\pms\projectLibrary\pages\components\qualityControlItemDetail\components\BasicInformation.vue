<script setup lang="ts">
import { BasicCard, DataStatusTag } from 'lyra-component-vue3';
import { computed, h } from 'vue';
import { getExecuteItem } from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/utill';
import { Tag } from 'ant-design-vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const list = [
  {
    label: '质控点',
    field: 'point',
  },
  {
    label: '状态',
    field: 'dataStatus',
    valueRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },

  {
    label: '执行情况',
    field: 'execute',
    valueRender: ({ text }) => {
      const { color, child } = getExecuteItem(text);
      return h(Tag, { color }, child);
    },
  },
  {
    label: '质控措施类型',
    field: 'typeName',
  },
  {
    label: '质控阶段',
    field: 'stage',
  },
  {
    label: '过程',
    field: 'processName',
  },
  {
    label: '质控活动',
    field: 'activityName',
  },
  {
    label: '负责人',
    field: 'resPersonName',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '修改人',
    field: 'modifyName',
  },
  {
    label: '修改时间',
    field: 'modifyTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '完成确认人',
    field: 'affirmName',
  },
  {
    label: '交付文件名称',
    field: 'deliveryFileName',
    // gridColumn: '2/4',
  },
  {
    label: '完成情况说明',
    field: 'completionStatement',
    gridColumn: '3/4',
  },
  {
    label: '控制方案',
    field: 'scheme',
    gridColumn: '1/4',
  },

];
const basicGridProps = computed(() => ({
  list,
  dataSource: props.data,
}));

</script>

<template>
  <div>
    <BasicCard
      title="基础信息"
      :gridContentProps="basicGridProps"
    />
  </div>
</template>

<style scoped lang="less">

</style>
