<template>
  <Layout3
    v-loading="fetching"
    :projectData="planDetailsData"
    :type="2"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
        :record="planDetailsData"
      />
    </template>

    <template #footer>
      <WorkflowAction
        v-if="planDetailsData.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <Layout3Content>
      <a-collapse
        v-model:activeKey="activeKey"
        :bordered="false"
        style="background: rgb(255, 255, 255)"
      >
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <a-collapse-panel
          key="1"
          header="计划详情"
          :style="customStyle"
        >
          <DetailView
            :data="planDetailsData"
            @handleGetDetail="getPlanDetail"
          />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="showWorkManageList"
          key="6"
          header="作业管理"
          :style="customStyle"
        >
          <WorkManageList v-if="planDetailsData" />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="showWorkManageList"
          key="7"
          header="人员管理"
          :style="customStyle"
        >
          <PersonManage v-if="planDetailsData" />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="showWorkManageList"
          key="8"
          header="物资管理"
          :style="customStyle"
        >
          <MaterialManage v-if="planDetailsData" />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="planDetailsData"
          key="2"
          header="活动项"
          :style="customStyle"
        >
          <linkContent
            :formId="id"
          />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="planDetailsData"
          key="3"
          header="交付物"
          :style="customStyle"
        >
          <Deliverable
            v-if="projectId"
            :formId="id"
          />
        </a-collapse-panel>
        <a-collapse-panel
          key="4"
          header="过程记录"
          :style="customStyle"
        >
          <PlanningRecord
            v-if="planDetailsData"
            :formId="id"
          />
        </a-collapse-panel>
        <a-collapse-panel
          key="5"
          header="附件"
          :style="customStyle"
        >
          <RiskContactDoc
            v-if="planDetailsData"
            :formId="id"
          />
        </a-collapse-panel>
      </a-collapse>
    </Layout3Content>
    <!-- 编辑，变更 -->
    <EditPlan
      @register="registerEdit"
      @update="getPlanDetail"
    />
  </Layout3>
</template>

<script lang="ts" setup>
import {
  computed,
  ComputedRef,
  createVNode,
  getCurrentInstance,
  onMounted,
  provide,
  readonly,
  Ref,
  ref,
  unref,
} from 'vue';
import {
  BasicTableAction,
  isPower,
  ITableActionItem,
  Layout3,
  Layout3Content,
  openModal,
  useDrawer,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';

import { useRoute, useRouter } from 'vue-router';
import { CaretRightOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
import { Modal, Collapse as aCollapse, CollapsePanel as aCollapsePanel } from 'ant-design-vue';
import DetailView from './components/projectPlan/components/DetailView.vue';
import Api from '/@/api';
import { renderNotAuthPage } from '/@/views/pms/utils';
import EditPlan from '../../projectList/menuComponents/projectPlan/components/EditPlan.vue';
import Deliverable from './components/deliverable/index.vue';
import { useUserStore } from '/@/store/modules/user';
import { getProjectRoleUser } from '/@/views/pms/projectLaborer/projectLab/api';
import RiskContactDoc from './components/contactTab/ContactDoc/index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import LinkContent from './LinkContent/linkContent.vue';
import PlanningRecord from './PlanningRecord/PlanningRecord.vue';
import {
  completePlanRow,
  delegateRowPlan,
  initDetailsBtn,
  planDoneRow,
  planRecordsRow,
  writeDelayReasonRow,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectPlan';
import ReasonForReturn
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectPlan/components/ReasonForReturn.vue';
import WorkManageList from './WorkManage/WorkManageList.vue';
import PersonManage from './WorkManage/PersonManage.vue';
import MaterialManage from './WorkManage/MaterialManage.vue';

const route = useRoute();
const router = useRouter();
const processRef: Ref = ref();
const userInfo = useUserStore().getUserInfo;
const showDetail = ref(false);
const showWorkManageList = computed(() => planDetailsData.value?.isWork);
const activeKey = ref([
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
]);
const customStyle = 'background: #fff;border-radius: 4px;margin-bottom: 10px;border: 0;overflow: hidden';

const props = defineProps({
  modalData: {
    type: Object,
    default: () => ({}),
  },
});
const pageInstance = getCurrentInstance();
const powerData = ref([]);
provide('powerData', powerData);

const planDetailsData = ref<any>({});
// 项目计划id
const projectSchemeId = props.modalData?.id || route?.params?.id?.toString();
const id = ref<string>(projectSchemeId);

const projectSchemeIdRef = ref(id);
// 项目id
const projectId = ref<string>('');
const processViewRef: Ref = ref();
provide('projectId', projectId);

provide('formData', planDetailsData);
provide('projectSchemeId', readonly(projectSchemeIdRef));
// 项目编号
const projectNumber = ref<string>('');
provide('projectNumber', readonly(projectNumber));
const [registerEdit, { openDrawer: openEdit }] = useDrawer();
// 获取当前项目用户的角色信息
const roleUserInfo: Ref<any[]> = ref([]);

async function reqProjectRoleUser() {
  roleUserInfo.value = await getProjectRoleUser(unref(projectId));
}

const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: planDetailsData.value,
  afterEvent: (type, props) => {
    processViewRef.value?.init();
    getPlanDetail();
  },
}));

// 获取项目详情
const fetching: Ref<boolean> = ref(false);
const getPlanDetail = async () => {
  fetching.value = true;
  try {
    const res = await new Api(`/pms/projectScheme/${id.value}`).fetch(
      {
        pageCode: 'PMS00027',
      },
      '',
      'GET',
    );
    setTitleByRootTabsKey(route?.query?.rootTabsKey as string, res.name);
    powerData.value = res?.detailAuthList ?? [];
    renderNotAuthPage({
      vm: pageInstance,
      powerData: powerData.value,
    });
    res.ownerName = res?.rspUserName;
    projectId.value = res?.projectId;
    projectNumber.value = res?.projectNumber;
    planDetailsData.value = res;
    showDetail.value = true;
  } finally {
    fetching.value = false;
  }
};

onMounted(async () => {
  await getPlanDetail();
  await reqProjectRoleUser();
});

const actionsBtn: ComputedRef<ITableActionItem[]> = computed(() => initDetailsBtn(planDetailsData.value, powerData, userInfo, actionClick));

function actionClick(type) {
  switch (type) {
    case 'edit':
      openEdit(true, {
        editType: 'edit',
        data: planDetailsData.value,
      });
      break;
    case 'startExecution': // 执行提醒
      startExecution(planDetailsData.value);
      break;
    case 'sendBack':// 退回
      sendBack(planDetailsData.value);
      break;
    case 'writeDelayReasonRow':
      writeDelayReasonRow(planDetailsData.value, updateForm);
      break;
    case 'planRecords':
      planRecordsRow(planDetailsData.value, updateForm);
      break;
    case 'executionComplete':
      planDoneRow(planDetailsData.value, updateForm);
      break;
    case 'completePlanRow':
      completePlanRow(planDetailsData.value, updateForm);
      break;
    case 'delegateRowPlan':
      delegateRowPlan(planDetailsData.value, updateForm);
      break;
  }
}

function startExecution(record) {
  Modal.confirm({
    title: () => '执行提醒',
    icon: () => createVNode(InfoCircleOutlined),
    content: () => '是否开始执行此计划？',
    async onOk() {
      await startExecutionImpl(record?.id);
      updateForm();
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {
    },
  });
}

function sendBack(record) {
  const refReasonForReturn = ref();
  openModal({
    title: '计划退回',
    height: 350,
    content(h) {
      return h(ReasonForReturn, { ref: refReasonForReturn });
    },
    async onOk() {
      const { reason } = await refReasonForReturn.value.refForm.validate();
      const data = {
        id: record.id,
        reason,
      };
      const url = '/pms/projectScheme/fallback';
      await new Api(url).fetch(data, '', 'POST');
      updateForm();
    },
  });
}

function updateForm() {
  new Api(`/pms/projectScheme/${id.value}`).fetch(
    {
      pageCode: 'PMS00027',
    },
    '',
    'GET',
  ).then((res) => {
    powerData.value = res?.detailAuthList ?? [];
    res.ownerName = res?.rspUserName;
    planDetailsData.value = res;
  });
}

function startExecutionImpl(id) {
  const url = `/pms/projectScheme/projectScheme/actualBeginTime/update?id=${id}`;
  return new Api(url).fetch({}, '', 'PUT');
}
</script>
<style scoped lang="less">
:deep(.ant-collapse-content>.ant-collapse-content-box,.ant-basic-table.default-spacing){
  padding-bottom: 0;
}
</style>
