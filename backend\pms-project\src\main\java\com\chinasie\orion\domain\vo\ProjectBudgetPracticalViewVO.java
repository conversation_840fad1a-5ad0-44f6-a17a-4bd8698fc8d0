package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectBudgetPracticalViewVO {
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "预算")
    private BigDecimal budget = BigDecimal.ZERO;
    @ApiModelProperty(value = "实际")
    private BigDecimal practical = BigDecimal.ZERO;;
    @ApiModelProperty(value = "执行率==百分比")
    private String percent = "0.00%";;
}
