<script setup lang="ts">
import {
  BasicButton, BasicForm, OrionTable, useForm,
} from 'lyra-component-vue3';
import {
  computed, reactive, onMounted, Ref, ref,
} from 'vue';

import Api from '/@/api';
import { openAddBudgetModal } from './components/AddBudget/index';

const props = defineProps<{
  formId: string | undefined,
  projectId: string | undefined
}>();

// 对于自定义规则进行处理
const schemas = [
  {
    field: 'name',
    label: '调整单标题',
    component: 'Input',
    colProps: {
      span: 24,
    },
    type: 'input',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'remark',
    label: '调整说明',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    type: 'textarea',
    componentProps: {
      showCount: true,
      maxlength: 200,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
];
const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props.formId && getFormData();
});
const selectedData = ref([]);
const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/budgetAdjustmentFrom').fetch('', props.formId, 'GET');
    selectedData.value = result.budgetAdjustmentVOS ?? [];
    setFieldsValue({ ...result });
  } finally {
    loading.value = false;
  }
}

const columns = [
  {
    title: '预算申请编码',
    dataIndex: 'number',
  },
  {
    title: '预算名称',
    dataIndex: 'name',
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterId',
  },
  {
    title: '科目名称',
    dataIndex: 'expenseSubjectName',
  },
  {
    title: '科目编码',
    dataIndex: 'expenseSubjectNumber',
  },
  {
    title: '期间类型',
    dataIndex: 'timeTypeName',
    align: 'center',
  },
  {
    title: '预算期间',
    dataIndex: 'budgetTime',
  },
  {
    title: '预算对象类型',
    dataIndex: 'budgetObjectTypeName',
  },
  {
    title: '预算对象',
    dataIndex: 'budgetObjectName',
  },
  {
    title: '币种',
    dataIndex: 'currencyName',
  },
  {
    title: '预算申请总额（元）',
    dataIndex: 'budgetMoney',
  },
  {
    title: '1月',
    dataIndex: 'januaryMoney',
    align: 'center',
  },
  {
    title: '2月',
    dataIndex: 'februaryMoney',
    align: 'center',
  },
  {
    title: '3月',
    dataIndex: 'marchMoney',
    align: 'center',
  },
  {
    title: '第一季度',
    dataIndex: 'firstQuarterMoney',
    align: 'center',
  },
  {
    title: '4月',
    dataIndex: 'aprilMoney',
    align: 'center',
  },
  {
    title: '5月',
    dataIndex: 'mayMoney',
    align: 'center',
  },
  {
    title: '6月',
    dataIndex: 'juneMoney',
    align: 'center',
  },
  {
    title: '第二季度',
    dataIndex: 'secondQuarter',
    align: 'center',
  },
  {
    title: '7月',
    dataIndex: 'julyMoney',
    align: 'center',
  },
  {
    title: '8月',
    dataIndex: 'augustMoney',
    align: 'center',
  },
  {
    title: '9月',
    dataIndex: 'septemberMoney',
    align: 'center',
  },
  {
    title: '第三季度',
    dataIndex: 'thirdQuarter',
    align: 'center',
  },
  {
    title: '10月',
    dataIndex: 'octoberMoney',
    align: 'center',
  },
  {
    title: '11月',
    dataIndex: 'novemberMoney',
    align: 'center',
  },
  {
    title: '12月',
    dataIndex: 'decemberMoney',
    align: 'center',
  },
  {
    title: '第四季度',
    dataIndex: 'fourthQuarter',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 220,
    slots: { customRender: 'action' },
  },
];
const selectedRowKeys = ref([]);
const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  showIndexColumn: false,
  rowSelection: {
    selectedRowKeys,
    onChange: (keys = []) => {
      selectedRowKeys.value = keys;
    },
  },

  showSmallSearch: false,
  pagination: false,
  columns,
  resizeHeightOffset: 60,

  dataSource: selectedData,
};

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.formId,
      projectId: props?.projectId,
      ...formValues,

      budgetAdjustmentDTOS: selectedData.value.map((item) => ({
        ...item,
        budgetId: item.id,
        id: '',
      })),
    };
    // selectedData.value = option.tableData.map((item) => ({ ...item, budgetId: item.id, id: '' }));

    return new Promise((resolve, reject) => {
      new Api('/pms/budgetAdjustmentFrom').fetch(params, props.formId ? 'edit' : 'add', props.formId ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
const tableRef = ref();

function openAddBudget() {
  openAddBudgetModal({
    title: '关联预算',
    width: '80%',
    selectType: 'checkbox',
    selectedData: tableRef.value.getDataSource(),
    columns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
      };
      delete params.tableMethods;
      delete params.node;
      return new Api('/pms/budgetManagement/page').fetch(params, '', 'POST');
    },
    onOk(option: Record<string, any>) {
      selectedData.value = option.tableData;

      tableRef.value.reload();
    },

  });
}
function deleteTable() {
  let tableData = tableRef.value.getDataSource();
  // 使用 Array.prototype.filter 过滤出不包含在 idToDelete 中的项
  tableData = tableData.filter((item) => !selectedRowKeys.value.includes(item.id));
  selectedData.value = tableData;
  tableRef.value.reload();
}

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
  <OrionTable
    ref="tableRef"
    :max-height="200"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="openAddBudget"
      >
        添加预算
      </BasicButton>
      <BasicButton
        icon="sie-icon-del"
        :disabled="selectedRowKeys.length===0"
        @click="deleteTable()"
      >
        移除
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
