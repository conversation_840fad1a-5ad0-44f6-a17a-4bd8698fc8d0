package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/21 11:33
 * @description:
 */
@Data
public class TreeSimpleVO implements Serializable {

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "父级id")
    private String parentId;

    private List<TreeSimpleVO> child;

}