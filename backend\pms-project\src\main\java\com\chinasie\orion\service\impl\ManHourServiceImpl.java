package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.domain.dto.ManHourDTO;
import com.chinasie.orion.domain.dto.ObjectDTO;
import com.chinasie.orion.domain.entity.ManHour;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.ManHourVo;
import com.chinasie.orion.domain.vo.PlanManHourVo;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ManHourRepository;
import com.chinasie.orion.service.ManHourService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:54
 * @description:
 */
@Service
public class ManHourServiceImpl extends OrionBaseServiceImpl<ManHourRepository, ManHour> implements ManHourService {
    @Resource
    private UserBo userBo;
    @Resource
    private ProjectSchemeService projectSchemeService;

    @Override
    public PageResult<ManHourVo> pageList(PageRequest<ManHourDTO> pageRequest) throws Exception {
        List<ManHourVo> manHourVos = new ArrayList<>();
        PageResult<ManHourVo> pageResult = new PageResult<>();

        IPage<ManHour> manHourIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<ManHour> page = this.page(manHourIPage);
        if (page != null && page.getTotal() > 0) {
            List<ManHour> content = page.getRecords();
            if (!CollectionUtils.isEmpty(content) && content.size() > 0) {
                Set<String> userIdList = new HashSet<>();
                for (ManHour manHour : content) {
                    userIdList.add(manHour.getCreatorId());
                    userIdList.add(manHour.getModifyId());
                    userIdList.add(manHour.getOwnerId());
                }
                Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
                for (ManHour planBaseLineInfo : content) {
                    String creatorId = planBaseLineInfo.getCreatorId();
                    String modifyId = planBaseLineInfo.getModifyId();
                    String ownerId = planBaseLineInfo.getOwnerId();
                    ManHourVo planBaseLineInfoVo = new ManHourVo();
                    BeanUtils.copyProperties(planBaseLineInfo, planBaseLineInfoVo);
                    planBaseLineInfoVo.setCreatorName(idToNameMap.get(creatorId));
                    planBaseLineInfoVo.setModifyName(idToNameMap.get(modifyId));
                    planBaseLineInfoVo.setOwnerName(idToNameMap.get(ownerId));
                    manHourVos.add(planBaseLineInfoVo);
                }
            }
            pageResult.setPageNum(page.getCurrent());
            pageResult.setPageSize(page.getSize());
            pageResult.setTotalSize(page.getTotal());
            pageResult.setTotalPages(page.getPages());
        } else {
            pageResult.setPageNum(0L);
            pageResult.setPageSize(0L);
            pageResult.setTotalSize(0L);
            pageResult.setTotalPages(0L);
        }
        pageResult.setContent(manHourVos);
        return pageResult;
    }

    @Override
    public PlanManHourVo listByPlanId(String planId) throws Exception {
        PlanManHourVo planManHourVo = new PlanManHourVo();

        LambdaQueryWrapper<ManHour> manHourLambdaQueryWrapper = new LambdaQueryWrapper<>(ManHour.class);
        manHourLambdaQueryWrapper.eq(ManHour::getPlanId, planId);
        List<ManHourVo> manHourVoList = new ArrayList<>();
        List<ManHour> manHourDTOS = this.list(manHourLambdaQueryWrapper);

        ProjectScheme planDetailVo = projectSchemeService.getById(planId);

        Integer durationDays = planDetailVo.getDurationDays();
        if (Objects.isNull(durationDays)) {
            durationDays = 1;
        }

        BigDecimal used = manHourDTOS.stream().map(ManHour::getRealityManHour).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal manHourSchedule = used.divide(new BigDecimal(String.valueOf(durationDays * 8)), 2, RoundingMode.HALF_UP);
        String scheduleName = "0%";
        if (!ObjectUtils.isEmpty(manHourSchedule)) {
            DecimalFormat df = new DecimalFormat("0.00%");
            scheduleName = df.format(manHourSchedule);
        }
        BeanCopyUtils.copyProperties(planDetailVo, planManHourVo);
        planManHourVo.setManHourScheduleName(scheduleName);

        if (CollectionUtils.isEmpty(manHourDTOS)) {
            return planManHourVo;
        }
        Set<String> userIdList = new HashSet<>();
        for (ManHour manHour : manHourDTOS) {
            userIdList.add(manHour.getCreatorId());
            userIdList.add(manHour.getModifyId());
            userIdList.add(manHour.getOwnerId());
        }
        Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
        for (ManHour planBaseLineInfo : manHourDTOS) {
            String creatorId = planBaseLineInfo.getCreatorId();
            String modifyId = planBaseLineInfo.getModifyId();
            String ownerId = planBaseLineInfo.getOwnerId();
            ManHourVo planBaseLineInfoVo = new ManHourVo();
            BeanUtils.copyProperties(planBaseLineInfo, planBaseLineInfoVo);
            planBaseLineInfoVo.setCreatorName(idToNameMap.get(creatorId));
            planBaseLineInfoVo.setModifyName(idToNameMap.get(modifyId));
            planBaseLineInfoVo.setOwnerName(idToNameMap.get(ownerId));
            manHourVoList.add(planBaseLineInfoVo);
        }
        manHourVoList.sort(Comparator.comparing(ObjectDTO::getModifyTime).reversed());
        planManHourVo.setManHourVos(manHourVoList);
        return planManHourVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveManHour(ManHourDTO manHourDTO) throws Exception {
        String memberId = manHourDTO.getMemberId();
        String name = manHourDTO.getName();
        if ( StrUtil.isBlank(name)) {
            name = this.getName(memberId);
            manHourDTO.setName(name);
            manHourDTO.setMemberName(name);
        } else {
            manHourDTO.setMemberName(name);
        }
        String number = String.format("GS%s", IdUtil.objectId());
        manHourDTO.setNumber(number);
        String planId = manHourDTO.getPlanId();
        ManHour manHour = BeanCopyUtils.convertTo(manHourDTO, ManHour::new);
        this.save(manHour);
        return manHour.getId();
    }

    public String getName(String memberId) throws Exception {
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(Arrays.asList(memberId));
        if (null == nameByUserIdMap || nameByUserIdMap.size() <= 0) {
            return "未知";
        }
        return nameByUserIdMap.get(memberId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateManHour(ManHourDTO manHourDTO) throws Exception {
        String memberId = manHourDTO.getMemberId();
        String name = manHourDTO.getName();
        if ( StrUtil.isBlank(name)) {
            name = this.getName(memberId);
            manHourDTO.setName(name);
            manHourDTO.setMemberName(name);
        } else {
            manHourDTO.setMemberName(name);
        }

        ManHour manHour = BeanCopyUtils.convertTo(manHourDTO, ManHour::new);
        return this.updateById(manHour);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delByIdList(List<String> idList) throws Exception {
        LambdaQueryWrapper<ManHour> manHourLambdaQueryWrapper = new LambdaQueryWrapper<>(ManHour.class);
        manHourLambdaQueryWrapper.in(ManHour::getId, idList.toArray());
        List<ManHour> manHourDTOS = this.list(manHourLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(manHourDTOS)) {
            return true;
        }
        return this.removeBatchByIds(idList);
    }

}
