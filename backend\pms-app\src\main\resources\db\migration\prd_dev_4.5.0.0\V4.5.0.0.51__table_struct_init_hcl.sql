CREATE TABLE `pmsx_relation_org_to_material` (
`id` varchar(64) NOT NULL  COMMENT '主键',
`class_name` varchar(64)   COMMENT '创建人',
`creator_id` varchar(64) NOT NULL  COMMENT '创建人',
`modify_time` datetime NOT NULL  COMMENT '修改时间',
`owner_id` varchar(64)   COMMENT '拥有者',
`create_time` datetime NOT NULL  COMMENT '创建时间',
`modify_id` varchar(64) NOT NULL  COMMENT '修改人',
`remark` varchar(1024)   COMMENT '备注',
`platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
`org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
`status` int NOT NULL  COMMENT '状态',
`logic_status` int NOT NULL  COMMENT '逻辑删除字段',
`repair_org_id` varchar(64)   COMMENT '大修组织id',
`material_id` varchar(64)   COMMENT '物质管理id',
`material_number` varchar(64)   COMMENT '物质编码',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='大修组织和大修组织物资关系';


