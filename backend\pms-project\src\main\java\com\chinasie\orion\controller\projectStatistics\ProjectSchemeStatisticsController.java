package com.chinasie.orion.controller.projectStatistics;


import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectSchemeStatisticsDTO;
import com.chinasie.orion.domain.vo.EvaluationProjectVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectSchemeStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectSchemeStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/projectSchemeStatistics")
@Api(tags = "项目内计划统计")
public class ProjectSchemeStatisticsController {
    @Autowired
    private ProjectSchemeStatisticsService projectSchemeStatisticsService;


    @ApiOperation(value = "计划状态分布统计")
    @RequestMapping(value = "/getProjectSchemeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectSchemeStatisticsVO> getProjectSchemeStatusStatistics( @RequestBody ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) throws Exception {
        ProjectSchemeStatisticsVO rsp =  projectSchemeStatisticsService.getProjectSchemeStatusStatistics(projectSchemeStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "计划状态负责人统计")
    @RequestMapping(value = "/getProjectSchemeRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectSchemeStatisticsVO>> getProjectSchemeRspUserStatistics( @RequestBody ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) throws Exception {
        List<ProjectSchemeStatisticsVO> rsp =  projectSchemeStatisticsService.getProjectSchemeRspUserStatistics(projectSchemeStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "计划完成趋势统计")
    @RequestMapping(value = "/getProjectSchemeCompleteStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划完成趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectSchemeStatisticsVO>> getProjectSchemeCompleteStatistics( @RequestBody ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) throws Exception {
        List<ProjectSchemeStatisticsVO> rsp =  projectSchemeStatisticsService.getProjectSchemeCompleteStatistics(projectSchemeStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "计划状态变更趋势统计")
    @RequestMapping(value = "/getProjectSchemeChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划状态变更趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectSchemeStatisticsVO>> getProjectSchemeChangeStatusStatistics( @RequestBody ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) throws Exception {
        List<ProjectSchemeStatisticsVO> rsp =  projectSchemeStatisticsService.getProjectSchemeChangeStatusStatistics(projectSchemeStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "计划新增趋势统计")
    @RequestMapping(value = "/getProjectSchemeCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectSchemeStatisticsVO>> getProjectSchemeCreateStatistics( @RequestBody ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) throws Exception {
        List<ProjectSchemeStatisticsVO> rsp =  projectSchemeStatisticsService.getProjectSchemeCreateStatistics(projectSchemeStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "计划分页查询")
    @RequestMapping(value = "/getProjectSchemePages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【计划分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeVO>> getProjectSchemePages(@RequestBody Page<ProjectSchemeStatisticsDTO> pageRequest) throws Exception {
        Page<ProjectSchemeVO> rsp =  projectSchemeStatisticsService.getProjectSchemePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
