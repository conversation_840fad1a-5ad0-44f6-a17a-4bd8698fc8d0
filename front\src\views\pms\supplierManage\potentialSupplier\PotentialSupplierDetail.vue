<script setup lang="ts">
import {
  IDataStatus, isPower, Layout3,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  BasicInfoTab, CertificateInfoTab, ContactTab, BankInfoTab, QualificationTab, HistoryCapitalExamination, RestrictedEventRecordTab, BusinessInfoTab,
} from '../components';
import Api from '/@/api';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const supplierId = computed(() => route.params?.id);
const supplierInfo = ref({});
provide('supplierInfo', supplierInfo);
const detailsPowerData: Ref = ref(null);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({}));

const powerData = ref();
function getPowerDataHandle(data) {
  powerData.value = data;
}
const menuData = computed(() => [
  {
    id: 'QaErwp23',
    name: '基本信息',
    componentName: 'BasicInfoTab',
    code: 'PMS_QZGYXQ_container_01',
  },
  {
    id: 'dZBbeKme',
    name: '证件信息',
    powerCode: 'PMS_QZGYXQ_container_05',
    componentName: 'CertificateInfoTab',
    code: 'PMS_QZGYXQ_container_05',
  },
  {
    id: 'SkKQcPpQ',
    name: '联系人信息',
    componentName: 'ContactTab',
    code: 'PMS_QZGYXQ_container_06',
  },
  {
    id: 'aDC8Cw3E',
    name: '银行信息',
    componentName: 'BankInfoTab',
    code: 'PMS_QZGYXQ_container_04',
  },
  {
    id: 'kajixMzi',
    name: '资质信息',
    componentName: 'QualificationTab',
    code: 'PMS_QZGYXQ_container_07',
  },
  {
    id: '52yKhR5b',
    name: '历史资审记录',
    componentName: 'HistoryCapitalExamination',
    code: 'PMS_QZGYXQ_container_08',
  },
  {
    id: '5Pky5ZRi',
    name: '受限事件记录',
    componentName: 'RestrictedEventRecordTab',
    code: 'PMS_QZGYXQ_container_03',
  },
  {
    id: 'C5PEChsp',
    name: '商务信息',
    componentName: 'BusinessInfoTab',
    code: 'PMS_QZGYXQ_container_02',
  },
].filter((item) => isPower(item.code, powerData.value)));

function menuChange({ id }) {
  actionId.value = id;
}
const getSupplierInfo = async () => {
  try {
    const result = await new Api('/pms/supplierInfo').fetch('', unref(supplierId), 'GET');
    supplierInfo.value = result;
  } catch (e) {}
};
watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(async () => {
  await getSupplierInfo();
});

const loading: Ref<boolean> = ref(false);

</script>

<template>
  <Layout3
    v-if="true"
    v-get-power="{pageCode:'PMS00013',getPowerDataHandle}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    class="purchase-manage-layout"
    @menuChange="menuChange"
  >
    <template #code>
      <h2
        class="page-title"
        :title="supplierInfo?.name"
      >
        {{ supplierInfo?.name || "--" }}
      </h2>
      <div class="page-subtitle">
        <span>供应商编号：{{ supplierInfo?.supplierNumber }}</span>
      </div>
    </template>
    <template v-if="true">
      <BasicInfoTab v-if="'QaErwp23'===actionId " />
      <CertificateInfoTab v-if="'dZBbeKme'===actionId " />
      <ContactTab v-if="'SkKQcPpQ'===actionId " />
      <BankInfoTab v-if="'aDC8Cw3E'===actionId " />
      <QualificationTab v-if="'kajixMzi'===actionId " />
      <HistoryCapitalExamination v-if="'52yKhR5b'===actionId " />
      <RestrictedEventRecordTab v-if="'5Pky5ZRi'===actionId " />
      <BusinessInfoTab v-if="'C5PEChsp'===actionId" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px;
  height: auto;
  .header-main{
    min-height: 33px;
    height: auto;
  }
  .project-title {
    width: auto !important;
    max-width: 800px !important;
    min-width: 300px;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }
  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.page-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 18px;
  color: #000000D9;
  margin-bottom: 5px;
}

.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;
  width: 400px;
  padding-bottom: 6px;

  span {
    margin-right: 20px;
  }
}
.purchase-manage-layout{
  :deep(.ant-menu-overflow){
    min-width: 750px;
  }
}
</style>