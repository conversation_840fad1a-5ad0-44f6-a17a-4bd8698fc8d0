package com.chinasie.orion.domain.vo;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * DocumentDecomposition VO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@ApiModel(value = "DocumentDecompositionVO对象", description = "文档分解")
@Data
public class DocumentDecompositionVO extends  ObjectVO   implements TreeUtils.TreeNode<String, DocumentDecompositionVO> ,Serializable{

        /**
         * 父级id
         */
        @ApiModelProperty(value = "父级id")
        private String parentId;
        @ApiModelProperty(value = "父级名称")
        private String parentName;


        /**
         * 关联任务
         */
        @ApiModelProperty(value = "关联任务")
        private String taskId;
        @ApiModelProperty(value = "关联任务名称")
        private String taskName;


        /**
         * 条目标题
         */
        @ApiModelProperty(value = "条目标题")
        private String name;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


        /**
         * 主表ID
         */
        @ApiModelProperty(value = "主表ID")
        private String mainTableId;

        /**
         * 条目编制内容
         */
        @ApiModelProperty(value = "条目编制内容")
        private String content;


        /**
         * 是否关联任务
         */
        @ApiModelProperty(value = "是否关联任务")
        private Boolean isRelationTask;

        /**
         * 子项
         */
        @ApiModelProperty(value = "子项")
        private List<DocumentDecompositionVO> children;
}
