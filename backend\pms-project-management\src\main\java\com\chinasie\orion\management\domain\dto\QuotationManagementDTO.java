package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.entity.QuotationManageCustContact;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * QuotationManagement DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "QuotationManagementDTO对象", description = "报价管理")
@Data
@ExcelIgnoreUnannotated
public class QuotationManagementDTO extends ObjectDTO implements Serializable {

    /**
     * 项目编号-需求编号
     */
    @ApiModelProperty(value = "项目编号-需求编号")
    @ExcelProperty(value = "项目编号-需求编号 ", index = 0)
    private String requirementNumber;

    /**
     * 报价单ID
     */
    @ApiModelProperty(value = "报价单编码")
    @ExcelProperty(value = "报价单编码 ", index = 1)
    private String quotationId;

    /**
     * 业务目标
     */
    @ApiModelProperty(value = "业务目标")
    @ExcelProperty(value = "业务目标 ", index = 2)
    private String busiGoal;

    /**
     * 业务目标内容
     */
    @ApiModelProperty(value = "业务目标内容")
    @ExcelProperty(value = "业务目标内容 ", index = 3)
    private String busiGoalCont;

    /**
     * 业务信息
     */
    @ApiModelProperty(value = "业务信息")
    @ExcelProperty(value = "业务信息 ", index = 4)
    private String busiInfo;

    /**
     * 业务信息内容
     */
    @ApiModelProperty(value = "业务信息内容")
    @ExcelProperty(value = "业务信息内容 ", index = 5)
    private String busiInfoCont;

    /**
     * 成本估算（资源）
     */
    @ApiModelProperty(value = "成本估算（资源）")
    @ExcelProperty(value = "成本估算（资源） ", index = 6)
    private String costEstRes;

    /**
     * 成本估算（资源）内容
     */
    @ApiModelProperty(value = "成本估算（资源）内容")
    @ExcelProperty(value = "成本估算（资源）内容 ", index = 7)
    private String costEstResCont;

    /**
     * 成本估算（人力、资源占用）
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）")
    @ExcelProperty(value = "成本估算（人力、资源占用） ", index = 8)
    private String costEstRrRes;

    /**
     * 成本估算（人力、资源占用）内容
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）内容")
    @ExcelProperty(value = "成本估算（人力、资源占用）内容 ", index = 9)
    private String costEstHrResCont;

    /**
     * 收益分析
     */
    @ApiModelProperty(value = "收益分析")
    @ExcelProperty(value = "收益分析 ", index = 10)
    private String revAnal;

    /**
     * 收益分析内容
     */
    @ApiModelProperty(value = "收益分析内容")
    @ExcelProperty(value = "收益分析内容 ", index = 11)
    private String revAnalCont;

    /**
     * 其他信息
     */
    @ApiModelProperty(value = "其他信息")
    @ExcelProperty(value = "其他信息 ", index = 12)
    private String otherInfo;

    /**
     * 其他信息内容
     */
    @ApiModelProperty(value = "其他信息内容")
    @ExcelProperty(value = "其他信息内容 ", index = 13)
    private String otherInfoCont;

    /**
     * 报价内容
     */
    @ApiModelProperty(value = "报价内容")
    @ExcelProperty(value = "报价内容 ", index = 14)
    private String quoteContent;

    /**
     * 报价方案详情
     */
    @ApiModelProperty(value = "报价方案详情")
    @ExcelProperty(value = "报价方案详情 ", index = 15)
    private String quotePlanDetail;

    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    @ExcelProperty(value = "报价金额 ", index = 16)
    private BigDecimal quoteAmt;

    /**
     * 报出币种
     */
    @ApiModelProperty(value = "报出币种")
    @ExcelProperty(value = "报出币种 ", index = 17)
    private String currency;

    /**
     * 底线价格
     */
    @ApiModelProperty(value = "底线价格")
    @ExcelProperty(value = "底线价格 ", index = 18)
    private BigDecimal floorPrice;

    /**
     * 报价发出时间
     */
    @ApiModelProperty(value = "报价发出时间")
    @ExcelProperty(value = "报价发出时间 ", index = 19)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户")
    @ExcelProperty(value = "发出报价用户 ", index = 20)
    private String issuer;

    /**
     * 报价结果
     */
    @ApiModelProperty(value = "报价结果")
    @ExcelProperty(value = "报价结果 ", index = 21)
    private String result;

    /**
     * 报价结果备注
     */
    @ApiModelProperty(value = "报价结果备注")
    @ExcelProperty(value = "报价结果备注 ", index = 22)
    private String resultNote;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    @ExcelProperty(value = "报价状态 ", index = 23)
    private String quotationStatus;

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    @ExcelProperty(value = "需求ID ", index = 24)
    private String requirementId;

    /**
     * 是否涉及现场工作
     */
    @ApiModelProperty(value = "是否涉及现场工作")
    @ExcelProperty(value = "是否涉及现场工作 ", index = 25)
    private String fieldwork;

    /**
     * 报价接收人
     */
    @ApiModelProperty(value = "报价接收人")
    @ExcelProperty(value = "报价接收人 ", index = 27)
    private String quoteAcceptPen;

    /**
     * 报价接收方
     */
    @ApiModelProperty(value = "报价接收方")
    @ExcelProperty(value = "报价接收方 ", index = 28)
    private String quoteAcceptCom;

    /**
     * 报价发出途径
     */
    @ApiModelProperty(value = "报价发出途径")
    @ExcelProperty(value = "报价发出途径 ", index = 29)
    private String issueWay;
    /**
     * 报价备注
     */
    @ApiModelProperty(value = "报价备注")
    @ExcelProperty(value = "报价备注 ", index = 30)
    private String quoteRemark;
    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    @ExcelProperty(value = "报价名称 ", index = 31)
    private String quotationName;

    /**
     * 重新报价原因
     */
    @ApiModelProperty(value = "重新报价原因")
    @ExcelProperty(value = "重新报价原因 ", index = 32)
    private String reQuoteReason;

    /**
     * 作废原因
     */
    @ApiModelProperty(value = "作废原因")
    @ExcelProperty(value = "作废原因 ", index = 33)
    private String obsoleteReason;
    /**
     * 报价执行情况
     */
    @ApiModelProperty(value = "报价执行情况")
    @ExcelProperty(value = "报价执行情况 ", index = 34)
    private String quoteExecuCondition;


    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    @ExcelProperty(value = "是否融资贸易业务 ", index = 35)
    private Boolean finTradeBus;
    /**
     * 附件材料上传
     */
    @ApiModelProperty(value = "附件材料上传")
    @Valid
    private List<FileInfoDTO> quoteFileInfoDTOList;

    @ApiModelProperty(value = "报价定价附件列表")
    @ExcelProperty(value = "报价定价附件列表")
    private List<FileDTO> bidQuoteFileList;

    @ApiModelProperty(value = "重新报价，原报价单ID")
    private String reQuotationId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型 ", index = 36)
    private String businessType;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    /**
     * 权限用户id
     */
    private String userId;

    /**
     * 权限部门id
     */
    private List<String> deptIds;

    @ApiModelProperty(value = "系统中触发发出报价的用户")
    private String sendOutUser;

    @ApiModelProperty(value = "系统中触发发出报价的时间")
    private Date sendOutTime;

    @ApiModelProperty(value = "客户-联系人")
    private List<QuotationManageCustContact> custContacts;


    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @ExcelProperty(value = "优先级1低2中3高 ", index = 33)
    private String priority;

    @ApiModelProperty(value = "优先级排序0升1降序")
    private String prioritySort;

    /**
     * 中标金额
     */
    @ApiModelProperty(value = "中标金额")
    private BigDecimal winningBidAmount;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String businessPerson;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRes;

}
