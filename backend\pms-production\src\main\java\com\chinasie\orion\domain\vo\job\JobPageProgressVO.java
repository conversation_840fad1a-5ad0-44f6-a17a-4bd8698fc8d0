package com.chinasie.orion.domain.vo.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/15/14:45
 * @description:
 */
@Data
public class JobPageProgressVO implements Serializable {

    @ApiModelProperty(value = "重大项目名称")
    private String importProjectName;
    @ApiModelProperty(value = "作业名称")
    private String jobName;
    @ApiModelProperty(value = "作业编码")
    private String jobNumber;
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    private String jobId;


    /**
     * 工作日期
     */
    @ApiModelProperty(value = "工作日期")
    private Date workDate;


    /**
     * 总体进展
     */
    @ApiModelProperty(value = "总体进展")
    private BigDecimal progressSchedule;


    /**
     * 工作进展
     */
    @ApiModelProperty(value = "工作进展")
    private String progressDetail;
    @ApiModelProperty(value = "备注")
    private String remark;
}
