<template>
  <BasicForm @register="registerForm" />
  <SelectUserModal
    :selectType="state.mode==='single'?'radio':''"
    @register="selectUserRegister"
  />
</template>

<script setup lang="ts">
import { defineExpose, h, reactive } from 'vue';
import {
  BasicForm, FormSchema, useForm, SelectUserModal, useModal,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Button, Select } from 'ant-design-vue';

const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
const state = reactive({
  manUserOptions: [],
  mode: '',
});
const schemas: FormSchema[] = [
  {
    field: 'formType',
    component: 'ApiSelect',
    label: '单据类型',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    defaultValue: 'opinion_form',
    componentProps: {
      api: () => new Api('/pms/idea-form/getFormTypeDict/dict').fetch('', '', 'GET'),
      labelField: 'desc',
      valueField: 'key',
    },
  },
  {
    label: '编码',
    field: 'number',
    component: 'Input',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: (model) => ({
      disabled: true,
      addonAfter: h(
        'span',
        {
          style: { cursor: 'pointer' },
          onClick: () => {
            new Api('/pms/idea-form/number').fetch('', '', 'GET').then((res) => {
              if (res) {
                model.formActionType.setFieldsValue({ number: res });
              } else {
                model.formActionType.setFieldsValue({ number: '' });
              }
            });
          },
        },
        '获取',
      ),
      placeholder: '点击右侧按钮获取',
    }),
  },
  {
    field: 'publishDeptId',
    component: 'ApiSelect',
    label: '发布方部门',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pmi/organization/org-type').fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'reviewDeptIdList',
    component: 'ApiSelect',
    label: '接收方部门',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      mode: 'multiple',
      api: () => new Api('/pmi/organization/org-type').fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'manUser',
    component: 'Input',
    label: '接收人',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {},
    render: (model) => h('div', {
      style: {
        width: '100%',
        height: '32px',
      },
    }, [
      h(Select, {
        style: {
          display: 'inline-block',
          width: '85%',
        },
        disabled: true,
        options: state.manUserOptions,
        value: model.model.manUser,
        open: false,
        showArrow: false,
      }),
      h(Button, {
        style: { display: 'inline-block' },
        onClick: () => {
          state.mode = 'single';
          selectUserOpenModal(true, {
            async onOk(data) {
              state.manUserOptions = data.map((item) => ({
                label: item.name,
                value: item.id,
              }));
              await FormMethods.setFieldsValue({
                manUser: data[0].id,
              });
            },
          });
        },
      }, '选择'),
    ]),
  },
  {
    field: 'specialtyCode',
    component: 'Input',
    label: '专业代码',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: 'thirdVerify',
    component: 'ApiSelect',
    label: '第三方审查备案',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pms/interface-management/third/verify/dict').fetch('', '', 'GET'),
      labelField: 'desc',
      valueField: 'key',
    },
  },
  {
    field: 'replySuggest',
    component: 'ApiSelect',
    label: '答复意见',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pms/idea-form/getReplySuggest/dict').fetch('', '', 'GET'),
      labelField: 'desc',
      valueField: 'key',
    },
  },

  {
    field: 'desc',
    component: 'InputTextArea',
    label: '回复意见描述',
    colProps: {
      span: 24,
    },
    rules: [{ required: true }],
    componentProps: {
      row: 4,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      maxlength: 500,
      row: 4,
      showCount: true,
    },
  },
];
const [registerForm, FormMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});

function setManUserOptions(data) {
  state.manUserOptions = data;
}

defineExpose({
  FormMethods,
  setManUserOptions,
});
</script>

<style scoped lang="less">

</style>
