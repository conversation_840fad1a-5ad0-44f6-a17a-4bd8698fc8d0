<template>
  <div class="establishment">
    <EstablishmentTask
      v-if="tableType==='task'"
      @change-tabs="changeTabs"
    >
      <template #slotRight>
        <ARadioGroup
          v-model:value="tableType"
          class="select-btn"
        >
          <a-radio-button
            v-if="isPower('PMS_XMLX_container_03_head_button_07',powerData)"
            value="task"
            class="select-btn-left"
          >
            协同编制任务
          </a-radio-button>
          <a-radio-button
            v-if="isPower('PMS_XMLX_container_03_head_button_08',powerData)"
            value="document"
            class="select-btn-right"
          >
            文档分解
          </a-radio-button>
        </ARadioGroup>
      </template>
    </EstablishmentTask>
    <EstablishmentDocument v-else>
      <template #slotRight>
        <ARadioGroup
          v-model:value="tableType"
          class="select-btn"
        >
          <a-radio-button
            v-if="isPower('PMS_XMLX_container_03_head_button_07',powerData)"
            value="task"
            class="select-btn-left"
          >
            协同编制任务
          </a-radio-button>
          <a-radio-button
            v-if="isPower('PMS_XMLX_container_03_head_button_08',powerData)"
            value="document"
            class="select-btn-right"
          >
            文档分解
          </a-radio-button>
        </ARadioGroup>
      </template>
    </EstablishmentDocument>
  </div>
</template>

<script lang="ts" setup>
import {
  computed, inject, Ref, ref,
} from 'vue';
import { RadioGroup as ARadioGroup, RadioButton as ARadioButton } from 'ant-design-vue';
import { isPower } from 'lyra-component-vue3';
import EstablishmentTask from './EstablishmentTask.vue';
import EstablishmentDocument from './EstablishmentDocument.vue';
const powerData = inject('powerData', []);

const emit = defineEmits(['changeTabs']);
const tableType :Ref<string> = ref('task');
function changeTabs(record) {
  emit('changeTabs', record);
}
</script>
<style lang="less" scoped>
.select-btn-left{
  border-radius: 4px 0 0 4px;
}
.select-btn-right{
  border-radius: 0 4px 4px 0;
}
.establishment{
  height: 100%;
}
</style>