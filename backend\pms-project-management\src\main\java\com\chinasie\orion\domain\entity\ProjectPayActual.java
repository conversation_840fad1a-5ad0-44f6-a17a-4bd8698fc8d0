package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectPayActual Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:18
 */
@TableName(value = "pmsx_project_pay_actual")
@ApiModel(value = "ProjectPayActualEntity对象", description = "实际成本金额")
@Data

public class ProjectPayActual extends  ObjectEntity  implements Serializable{

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @TableField(value = "psphi")
    private String psphi;

    /**
     * 对象
     */
    @ApiModelProperty(value = "对象")
    @TableField(value = "objnr")
    private String objnr;

    /**
     * 对象名称
     */
    @ApiModelProperty(value = "对象名称")
    @TableField(value = "post_one")
    private String postOne;

    /**
     * WBS编码
     */
    @ApiModelProperty(value = "WBS编码")
    @TableField(value = "posid")
    private String posid;

    /**
     * 对象货币值
     */
    @ApiModelProperty(value = "对象货币值")
    @TableField(value = "wogbtr")
    private String wogbtr;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @TableField(value = "budat")
    private Date budat;

    /**
     * 凭证抬头文本
     */
    @ApiModelProperty(value = "凭证抬头文本")
    @TableField(value = "bltxt")
    private String bltxt;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value = "凭证编码")
    @TableField(value = "belnr")
    private String belnr;

    /**
     * 参考凭证编码
     */
    @ApiModelProperty(value = "参考凭证编码")
    @TableField(value = "refbn")
    private String refbn;

    /**
     * 陈本要素
     */
    @ApiModelProperty(value = "陈本要素")
    @TableField(value = "kstar")
    private String kstar;

    /**
     * 成本要素名称
     */
    @ApiModelProperty(value = "成本要素名称")
    @TableField(value = "ktext")
    private String ktext;

    /**
     * 冲转科目名
     */
    @ApiModelProperty(value = "冲转科目名")
    @TableField(value = "gkont")
    private String gkont;

    /**
     * 采购凭证
     */
    @ApiModelProperty(value = "采购凭证")
    @TableField(value = "ebeln")
    private String ebeln;

    /**
     * 采购订单文本
     */
    @ApiModelProperty(value = "采购订单文本")
    @TableField(value = "txz_one")
    private String txzOne;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @TableField(value = "bukrs")
    private String bukrs;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "sgtxt")
    private String sgtxt;

    /**
     * 功能范围
     */
    @ApiModelProperty(value = "功能范围")
    @TableField(value = "fkber")
    private String fkber;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @TableField(value = "gjahr")
    private String gjahr;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @TableField(value = "insert_time")
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

}
