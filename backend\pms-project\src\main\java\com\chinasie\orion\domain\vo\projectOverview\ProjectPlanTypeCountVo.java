package com.chinasie.orion.domain.vo.projectOverview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/14:49
 * @description:
 */
@Data
public class ProjectPlanTypeCountVo implements Serializable {
    @ApiModelProperty(value = "计划类型ID")
    private String planTypeId;
    @ApiModelProperty(value = "计划类型名称")
    private String planTypeName;
    @ApiModelProperty(value = "数据数量")
    private Integer count=0;

    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount=0;
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount=0;
    @ApiModelProperty(value = "进行中数量")
    private Integer runningCount=0;
}
