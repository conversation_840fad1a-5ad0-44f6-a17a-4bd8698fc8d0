<template>
  <!--  <FloatBox-->
  <!--    @push="clickSeePush"-->
  <!--    @search="clickSearchKnowledge"-->
  <!--    @share="clickExperienceShare"-->
  <!--  />-->
  <Push v-model:visible="visiblePush" />
  <Search v-model:visible="visibleSearch" />
  <ExperienceShare
    v-if="experienceShare.visible"
    :data="experienceShare"
    @submit="submitExperienceShare"
  />
  <Edit
    v-if="edit.visible"
    :data="edit"
  />
</template>

<script>
import { defineComponent, toRefs, reactive } from 'vue';
import FloatBox from './FloatBox.vue';
import Push from './Push.vue';
import Search from './Search.vue';
import ExperienceShare from './Share.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import Edit from '/@/views/pms/projectLaborer/knowledgeEditData//Edit.vue';

export default defineComponent({
  name: 'PushModel',
  components: {
    Edit,
    Search,
    ExperienceShare,
    // FloatBox,
    Push,
  },
  setup() {
    const userStore = useUserStore();
    const state = reactive({
      visiblePush: false,
      visibleShare: false,
      visibleSearch: false,
      experienceShare: {},
      edit: {},
    });
    function clickSearchKnowledge() {
      state.visibleSearch = true;
    }
    function clickSeePush() {
      state.visiblePush = true;
    }
    function clickExperienceShare() {
      const url_key = 'knowledgeInfo/getKey';
      new Api('/kms').fetch('', url_key, 'GET').then((id) => {
        state.experienceShare = {
          visible: true,
          title: '知识分享',
          form: {
            name: undefined,
            summary: undefined,
            content: undefined,
            projectName: undefined,
            permissionJsonDtoList: [
              {
                type: 1,
                isPublic: false,
                projectList: [],
                organizationList: [],
                personList: [],
              },
              {
                type: 2,
                isPublic: false,
                projectList: [],
                organizationList: [],
                personList: [],
              },
              {
                type: 3,
                isPublic: false,
                projectList: [],
                organizationList: [],
                personList: [],
              },
            ],
            id,
            creatorId: userStore.getUserInfo.id,
            creatorName: userStore.getUserInfo.name,
            deptName: userStore.getUserInfo.organizations.map((s) => s.name).join(','),
          },
        };
      });
    }

    function submitExperienceShare(val) {
      state.edit = {
        visible: true,
        title: '新增知识',
        type: 'share',
        form: JSON.parse(JSON.stringify(val.form)),
      };
    }

    return {
      ...toRefs(state),
      clickSeePush,
      clickSearchKnowledge,
      submitExperienceShare,
      clickExperienceShare,
    };
  },
});
</script>
