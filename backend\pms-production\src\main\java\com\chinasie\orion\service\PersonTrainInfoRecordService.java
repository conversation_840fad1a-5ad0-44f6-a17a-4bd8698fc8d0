package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.dto.PersonTrainInfoRecordDTO;
import com.chinasie.orion.domain.vo.PersonTrainInfoRecordVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PersonTrainInfoRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
public interface PersonTrainInfoRecordService  extends  OrionBaseService<PersonTrainInfoRecord>  {


        /**
         *  详情
         *
         * * @param id
         */
    PersonTrainInfoRecordVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param personTrainInfoRecordDTO
         */
        String create(PersonTrainInfoRecordDTO personTrainInfoRecordDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param personTrainInfoRecordDTO
         */
        Boolean edit(PersonTrainInfoRecordDTO personTrainInfoRecordDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<PersonTrainInfoRecordVO> pages( Page<PersonTrainInfoRecordDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<PersonTrainInfoRecordVO> vos)throws Exception;

    List<PersonTrainInfoRecord> listByUserCodeAndBaseCode(String userCode, String basePlaceCode, String keyWord);

    /**
     *  获取用户已经拥有的 培训信息
     * @param userCodeList
     */
    List<PersonTrainInfoRecord> listByUserCodeList(List<String> userCodeList);

    List<PersonTrainInfoRecordVO> listByEntity(PersonTrainInfoRecordDTO pageRequest) throws Exception;
}
