package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectSchemeStatisticsDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectSchemeStatisticsVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectSchemeRepository;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.projectStatistics.PlanStatusStatisticsService;
import com.chinasie.orion.service.projectStatistics.ProjectSchemeStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.chinasie.orion.dict.Status.levelMap;

@Service
public class ProjectSchemeStatisticsServiceImpl implements ProjectSchemeStatisticsService {
    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private PlanStatusStatisticsService planStatusStatisticsService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private ProjectSchemeRepository projectSchemeRepository;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Override
    public ProjectSchemeStatisticsVO getProjectSchemeStatusStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) {
        ProjectSchemeStatisticsVO projectSchemeStatisticsVO = new ProjectSchemeStatisticsVO();
        LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectSchemeLambdaQueryWrapperX.select("status,count(id) as count");
        projectSchemeLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeStatisticsDTO.getProjectId());
        projectSchemeLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getNodeType, projectSchemeStatisticsDTO.getNodeType());
        projectSchemeLambdaQueryWrapperX.groupBy(ProjectScheme::getStatus);
        List<Map<String, Object>> list = projectSchemeService.listMaps(projectSchemeLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("101".equals(map.get("status").toString())) {
                projectSchemeStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("111".equals(map.get("status").toString())) {
                projectSchemeStatisticsVO.setCompleteCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectSchemeStatisticsVO.setReleaseCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectSchemeStatisticsVO;
    }

    @Override
    public List<ProjectSchemeStatisticsVO> getProjectSchemeRspUserStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) {
        List<ProjectSchemeStatisticsVO> projectSchemeStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectSchemeLambdaQueryWrapperX.select("rsp_user as rspUser,IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) waitReleaseCount ," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as releaseCount," +
                "IFNULL( sum( CASE  WHEN `status`=111 THEN 1 ELSE 0 END ), 0 ) as completeCount");
        projectSchemeLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeStatisticsDTO.getProjectId());
        projectSchemeLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getNodeType, projectSchemeStatisticsDTO.getNodeType());
        projectSchemeLambdaQueryWrapperX.groupBy(ProjectScheme::getRspUser);
        List<Map<String, Object>> list = projectSchemeService.listMaps(projectSchemeLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectSchemeStatisticsVO projectSchemeStatisticsVO = new ProjectSchemeStatisticsVO();
            projectSchemeStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectSchemeStatisticsVO.setRspuserName(userVO.getName());
            }
            projectSchemeStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("waitReleaseCount").toString()));
            projectSchemeStatisticsVO.setReleaseCount(Integer.parseInt(map.get("releaseCount").toString()));
            projectSchemeStatisticsVO.setCompleteCount(Integer.parseInt(map.get("completeCount").toString()));
            projectSchemeStatisticsVOs.add(projectSchemeStatisticsVO);
        }
        return projectSchemeStatisticsVOs;
    }

    @Override
    public List<ProjectSchemeStatisticsVO> getProjectSchemeChangeStatusStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) {
        List<ProjectSchemeStatisticsVO> projectSchemeStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<PlanStatusStatistics> projectSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectSchemeStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) <= 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            if(i==0){
                endDate=new Date();
            }
            sql = sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN wait_release_count ELSE 0 END ), 0 ) as waitRelaseCountTime" + i + ","+
            "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') = '"+sdf.format(endDate) +"' THEN release_count ELSE 0 END ), 0 ) as relaseCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN complete_count ELSE 0 END ), 0 ) as completeCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectSchemeLambdaQueryWrapperX.select(sql);
        projectSchemeLambdaQueryWrapperX.eq(PlanStatusStatistics::getProjectId, projectSchemeStatisticsDTO.getProjectId());
        projectSchemeLambdaQueryWrapperX.eqIfPresent(PlanStatusStatistics::getTypeId, projectSchemeStatisticsDTO.getNodeType());
        List<Map<String, Object>> list = planStatusStatisticsService.listMaps(projectSchemeLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectSchemeStatisticsVO projectSchemeStatisticsVO = new ProjectSchemeStatisticsVO();
            projectSchemeStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("waitRelaseCountTime" + i).toString()));
            projectSchemeStatisticsVO.setReleaseCount(Integer.parseInt(map.get("relaseCountTime" + i).toString()));
            projectSchemeStatisticsVO.setCompleteCount(Integer.parseInt(map.get("completeCountTime" + i).toString()));
            projectSchemeStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectSchemeStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectSchemeStatisticsVOs.add(projectSchemeStatisticsVO);
        }
        Collections.reverse(projectSchemeStatisticsVOs);
        return projectSchemeStatisticsVOs;
    }


    @Override
    public List<ProjectSchemeStatisticsVO> getProjectSchemeCreateStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) {
        List<ProjectSchemeStatisticsVO> projectSchemeStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectSchemeStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) <= 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i ;
            if(i<9){
               sql=sql+"," ;
            }
        }
        projectSchemeLambdaQueryWrapperX.select(sql);
        projectSchemeLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeStatisticsDTO.getProjectId());
        projectSchemeLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getNodeType, projectSchemeStatisticsDTO.getNodeType());
        List<Map<String, Object>> list = projectSchemeService.listMaps(projectSchemeLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectSchemeStatisticsVO projectSchemeStatisticsVO = new ProjectSchemeStatisticsVO();
            projectSchemeStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("time" + i).toString()));
            projectSchemeStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectSchemeStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectSchemeStatisticsVOs.add(projectSchemeStatisticsVO);
        }
        Collections.reverse(projectSchemeStatisticsVOs);
        return projectSchemeStatisticsVOs;
    }

    @Override
    public List<ProjectSchemeStatisticsVO> getProjectSchemeCompleteStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO) {
        List<ProjectSchemeStatisticsVO> projectSchemeStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectSchemeStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectSchemeStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `actual_end_time`>= '" + sdf.format(startDate) + "'  and  `actual_end_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectSchemeLambdaQueryWrapperX.select(sql);
        projectSchemeLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeStatisticsDTO.getProjectId());
        projectSchemeLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getNodeType, projectSchemeStatisticsDTO.getNodeType());
        projectSchemeLambdaQueryWrapperX.eq(ProjectScheme::getStatus,111);
        List<Map<String, Object>> list = projectSchemeService.listMaps(projectSchemeLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectSchemeStatisticsVO projectSchemeStatisticsVO = new ProjectSchemeStatisticsVO();
            projectSchemeStatisticsVO.setCompleteCount(Integer.parseInt(map.get("time" + i).toString()));
            projectSchemeStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectSchemeStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectSchemeStatisticsVOs.add(projectSchemeStatisticsVO);
        }
        Collections.reverse(projectSchemeStatisticsVOs);
        return projectSchemeStatisticsVOs;
    }

    @Override
    public Page<ProjectSchemeVO> getProjectSchemePages(Page<ProjectSchemeStatisticsDTO> pageRequest) throws Exception {
        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectScheme::new));
        LambdaQueryWrapperX<ProjectScheme> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO = pageRequest.getQuery();
            objectLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getStatus, projectSchemeStatisticsDTO.getStatus());
            objectLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getNodeType, projectSchemeStatisticsDTO.getNodeType());
            objectLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(ProjectScheme::getRspUser, projectSchemeStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectSchemeStatisticsDTO.getCreateTime())) {
                if (projectSchemeStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getCreateTime,DateUtil.beginOfDay(projectSchemeStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectSchemeStatisticsDTO.getCreateTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getCreateTime,DateUtil.beginOfWeek(projectSchemeStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectSchemeStatisticsDTO.getCreateTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getCreateTime,DateUtil.beginOfQuarter(projectSchemeStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectSchemeStatisticsDTO.getCreateTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getCreateTime,DateUtil.beginOfMonth(projectSchemeStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectSchemeStatisticsDTO.getCreateTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getCreateTime,DateUtil.beginOfYear(projectSchemeStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectSchemeStatisticsDTO.getCreateTime()));
                }
            }
            if (ObjectUtil.isNotEmpty(projectSchemeStatisticsDTO.getCompleteTime())) {
                if (projectSchemeStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getActualEndTime,DateUtil.beginOfDay(projectSchemeStatisticsDTO.getCompleteTime()),DateUtil.endOfDay(projectSchemeStatisticsDTO.getCompleteTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getActualEndTime,DateUtil.beginOfWeek(projectSchemeStatisticsDTO.getCompleteTime()),DateUtil.endOfWeek(projectSchemeStatisticsDTO.getCompleteTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getActualEndTime,DateUtil.beginOfQuarter(projectSchemeStatisticsDTO.getCompleteTime()),DateUtil.endOfQuarter(projectSchemeStatisticsDTO.getCompleteTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getActualEndTime,DateUtil.beginOfMonth(projectSchemeStatisticsDTO.getCompleteTime()),DateUtil.endOfMonth(projectSchemeStatisticsDTO.getCompleteTime()));
                }
                if (projectSchemeStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(ProjectScheme::getActualEndTime,DateUtil.beginOfYear(projectSchemeStatisticsDTO.getCompleteTime()),DateUtil.endOfYear(projectSchemeStatisticsDTO.getCompleteTime()));
                }
            }
        }
        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        if (!CollectionUtil.isNotEmpty(schemeVOS)) {
            return pageResult;
        }
        for (ProjectSchemeVO projectSchemeVO : schemeVOS) {
            if (null != levelMap) {
                projectSchemeVO.setLevelName(StrUtil.toString(levelMap.get(StrUtil.toString(projectSchemeVO.getLevel()))));
            }
            projectSchemeVO.setStatusName(Status.codeMapping(projectSchemeVO.getStatus()));
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSectionId());
                projectSchemeVO.setRspSubDeptName(null == dept ? "" : dept.getName());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                UserVO rspUser = userRedisHelper.getUserById(projectSchemeVO.getRspUser());
                projectSchemeVO.setRspUserName(null == rspUser ? "" : rspUser.getName());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSubDept());
                projectSchemeVO.setRspSectionName(null == dept ? "" : dept.getName());
            }
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
        }
        pageResult.setContent(schemeVOS);
        return pageResult;
    }

}
