<script setup lang="ts">
import { BasicButton, isPower, OrionTable } from 'lyra-component-vue3';
import {
  CSSProperties, h, inject, onActivated, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { openMaterialDrawer } from '/@/views/pms/overhaulManagement/utils';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { message } from 'ant-design-vue/lib/components';
import { get as _get, isArray } from 'lodash-es';
import dayjs from 'dayjs';
import { useMaterialExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';

const detailsData: Record<string, any> = inject('detailsData');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};
const { exportApi } = useMaterialExcel();
const tableRef: Ref = ref();
const keyword: Ref = ref('');
const tableOptions = {
  showToolButton: false,
  isSpacing: false,
  smallSearchField: [],
  api: (params: Record<string, any>) => new Api('/pms/schemeToMaterial/material').fetch({
    ...params,
    query: {
      repairRound: detailsData?.repairRound,
      keyword: unref(keyword),
    },
    power: {
      pageCode: 'PMSMajorRepairsSecondDetail',
      containerCode: 'PMS_DXXQEC_container_04',
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 240,
    },
    {
      title: '项目计划',
      dataIndex: 'planSchemeName',
      width: 240,
    },
    {
      title: '资产类型',
      dataIndex: 'assetTypeName',
    },
    {
      title: '资产编码/条码',
      dataIndex: 'materialNumber',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '规格型号',
      dataIndex: 'specificationModel',
    },
    {
      title: '入场数量',
      dataIndex: ['materialManageVO', 'inputStockNum'],
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '计划入场离场时间',
      dataIndex: ['materialManageVO', 'inAndOutDateList'],
      width: 260,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'RangePicker',
          isKeepEdit: true,
          editFlag: record.materialManageVO?.status !== 1,
          record,
          text: (text || []).map((item) => (item ? dayjs(item).format('YYYY-MM-DD') : '')).join(' 至 '),
          componentValue: text || [],
          componentProps: {
            allowClear: false,
            valueFormat: 'YYYY-MM-DD',
          },
          onSubmit(dates: [string, string], resolve: (value: any) => void) {
            new Api('/pms/material-manage/edit/date').fetch({
              id: record.materialManageVO?.id,
              inAndOutDateList: dates[0] || [],
            }, '', 'PUT').then(() => {
              resolve(true);
              message.success('操作成功');
              updateTable();
            }).catch(() => {
              resolve(false);
            });
          },
        });
      },
    },
    {
      title: '实际入场日期',
      dataIndex: ['materialManageVO', 'actInDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际离场日期',
      dataIndex: ['materialManageVO', 'actOutDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否合格',
      dataIndex: ['materialManageVO', 'isPass'],
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    // {
    //   title: '是否可用',
    //   dataIndex: ['materialManageVO', 'isAvailable'],
    //   customRender({ text }) {
    //     return text ? '是' : text === false ? '否' : '';
    //   },
    // },
    {
      title: '进场时间倒计时（天）',
      dataIndex: ['materialManageVO', 'inDays'],
      width: 200,
    },
    {
      title: '物资状态',
      dataIndex: ['materialManageVO', 'status'],
      customRender({ text }) {
        switch (text) {
          case 0:
            return '待入场';
          case 1:
            return '已入场';
          case 2:
            return '已离场';
        }
      },
    },
    {
      title: '参与作业数',
      dataIndex: 'jobNum',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '进场',
      onClick(record) {
        const dateList = _get(record, 'materialManageVO.inAndOutDateList');
        if (!isArray(dateList) || dateList.length !== 2 || !(dateList || []).every(Boolean)) {
          message.warn('请物资的计划入场离场时间后再试');
          return;
        }
        openMaterialDrawer({
          operationType: 'in',
          jobId: detailsData.id,
          id: record.materialManageVO?.id,
        }, () => {
          updateTable();
        });
      },
      isShow: (record) => [[0, 2].includes(_get(record, 'materialManageVO.status'))].every(Boolean),
    },
    {
      text: '离场',
      onClick(record) {
        openMaterialDrawer({
          operationType: 'out',
          jobId: detailsData.id,
          id: record.materialManageVO?.id,
        }, () => {
          updateTable();
        });
      },
      isShow: (record) => [[1].includes(_get(record, 'materialManageVO.status'))].every(Boolean),
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

onActivated(() => {
  updateTable();
});
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      rowKey="materialNumber"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-daochu"
          @click="exportApi({
            repairRound:detailsData?.repairRound,
          })"
        >
          导出
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
