package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@ApiModel(value = "ContractPayNodeVO对象", description = "合同支付节点信息")
@Data
public class ContractPayNodeVO extends ObjectVO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 结算类型
     */
    @ApiModelProperty(value = "结算类型")
    private String settlementType;

    /**
     * 结算类型名称
     */
    @ApiModelProperty(value = "结算类型名称")
    private String settlementTypeName;


    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    private String payType;

    /**
     * 支付类型名称
     */
    @ApiModelProperty(value = "支付类型名称")
    private String payTypeName;

    /**
     * 初始计划支付时间
     */
    @ApiModelProperty(value = "初始计划支付时间")
    private Date initPlanPayDate;

    /**
     * 初始计划支付金额
     */
    @ApiModelProperty(value = "初始计划支付金额")
    private BigDecimal initPlanPayAmt;

    /**
     * 支付百分比
     */
    @ApiModelProperty(value = "支付百分比")
    private BigDecimal payPercentage;

    /**
     * 支付说明
     */
    @ApiModelProperty(value = "支付说明")
    private String payDesc;

    /**
     * 支付日期
     */
    @ApiModelProperty(value = "支付日期")
    private Date payDate;

    @ApiModelProperty(value = "支付状态")
    private String statusName;

    @ApiModelProperty(value = "应收编号")
    private String receivableId;

    @ApiModelProperty(value = "应收编号")
    private String receivableNumber;


}
