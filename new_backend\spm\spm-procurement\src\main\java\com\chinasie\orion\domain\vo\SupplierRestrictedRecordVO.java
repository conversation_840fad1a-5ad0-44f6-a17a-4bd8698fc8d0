package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierRestrictedRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierRestrictedRecordVO对象", description = "受限事件记录")
@Data
public class SupplierRestrictedRecordVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNumber;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    private String applicationNumber;


    /**
     * 受限类型
     */
    @ApiModelProperty(value = "受限类型")
    private String restrictedType;


    /**
     * 整改状态
     */
    @ApiModelProperty(value = "整改状态")
    private String rectificationStatus;


    /**
     * 黑名单类型
     */
    @ApiModelProperty(value = "黑名单类型")
    private String blacklistType;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 申报日期
     */
    @ApiModelProperty(value = "申报日期")
    private Date declarationDate;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;


    /**
     * 申报公司
     */
    @ApiModelProperty(value = "申报公司")
    private String declaringCompany;


    /**
     * 内容描述
     */
    @ApiModelProperty(value = "内容描述")
    private String contentDescription;


    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    private String approvalCompletionTime;


    /**
     * 是否解冻
     */
    @ApiModelProperty(value = "是否解冻")
    private String whetherThawed;


    /**
     * 受限范围
     */
    @ApiModelProperty(value = "受限范围")
    private String restrictedScope;


    /**
     * 集团发送SAP
     */
    @ApiModelProperty(value = "集团发送SAP")
    private String groupSendsSap;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
