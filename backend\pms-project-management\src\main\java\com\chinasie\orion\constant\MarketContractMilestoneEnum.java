package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum MarketContractMilestoneEnum {

    PLANACCEPTDATE("plan_accept_date","合同约定验收日期"),
    EXCEPTACCEPTDATE("expect_accept_date","初始预估验收日期"),
    MILESTONEAMT("milestone_amt","合同约定验收金额"),
    EXCEPTACCEPTAMT("except_acceptance_amt","初始预估验收金额"),
    ;


    private String code;

    private String desc;


    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    MarketContractMilestoneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
