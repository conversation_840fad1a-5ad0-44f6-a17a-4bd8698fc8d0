<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.apache.skywalking</groupId>
    <artifactId>java-agent</artifactId>
    <version>8.12.0</version>

    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>21</version>
        <relativePath />
    </parent>

    <modules>
        <module>apm-commons</module>
        <module>apm-protocol</module>
        <module>apm-sniffer</module>
        <module>apm-application-toolkit</module>
    </modules>
    <packaging>pom</packaging>

    <name>java-agent</name>
    <url>https://github.com/apache/skywalking-java</url>

    <scm>
        <url>https://github.com/apache/skywalking-java</url>
        <connection>scm:git:https://github.com/apache/skywalking-java.git</connection>
        <developerConnection>scm:git:https://github.com/apache/skywalking-java.git</developerConnection>
        <tag>v8.12.0</tag>
    </scm>

    <issueManagement>
        <system>GitHub</system>
        <url>https://github.com/apache/skywalking/issues</url>
    </issueManagement>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <mailingLists>
        <mailingList>
            <name>SkyWalking Developer List</name>
            <post><EMAIL></post>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
        </mailingList>
        <mailingList>
            <name>SkyWalking Commits</name>
            <post><EMAIL></post>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
        </mailingList>
    </mailingLists>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <compiler.version>1.8</compiler.version>
        <powermock.version>2.0.7</powermock.version>
        <checkstyle.version>6.18</checkstyle.version>
        <junit.version>4.12</junit.version>
        <mockito-core.version>3.5.13</mockito-core.version>
        <lombok.version>1.18.20</lombok.version>

        <!-- core lib dependency -->
        <bytebuddy.version>1.12.13</bytebuddy.version>
        <grpc.version>1.44.0</grpc.version>
        <netty.version>4.1.79.Final</netty.version>
        <gson.version>2.8.9</gson.version>
        <os-maven-plugin.version>1.6.2</os-maven-plugin.version>
        <protobuf-maven-plugin.version>0.6.1</protobuf-maven-plugin.version>
        <com.google.protobuf.protoc.version>3.17.3</com.google.protobuf.protoc.version>
        <protoc-gen-grpc-java.plugin.version>1.44.0</protoc-gen-grpc-java.plugin.version>
        <netty-tcnative-boringssl-static.version>2.0.48.Final</netty-tcnative-boringssl-static.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <objenesis.version>3.1</objenesis.version>
        <!-- necessary for Java 9+ -->
        <org.apache.tomcat.annotations-api.version>6.0.53</org.apache.tomcat.annotations-api.version>

        <!-- Plugin versions -->
        <docker.plugin.version>0.4.13</docker.plugin.version>
        <takari-maven-plugin.version>0.6.1</takari-maven-plugin.version>
        <exec-maven-plugin.version>1.6.0</exec-maven-plugin.version>
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <maven-dependency-plugin.version>2.10</maven-dependency-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-assembly-plugin.version>3.1.0</maven-assembly-plugin.version>
        <maven-failsafe-plugin.version>2.22.0</maven-failsafe-plugin.version>
        <build-helper-maven-plugin.version>3.2.0</build-helper-maven-plugin.version>
        <maven-jar-plugin.version>3.1.0</maven-jar-plugin.version>
        <maven-shade-plugin.version>3.1.1</maven-shade-plugin.version>
        <maven-enforcer-plugin.version>3.0.0-M2</maven-enforcer-plugin.version>
        <maven-compiler-plugin.version>3.8.0</maven-compiler-plugin.version>
        <maven-resource-plugin.version>3.1.0</maven-resource-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <versions-maven-plugin.version>2.5</versions-maven-plugin.version>
        <coveralls-maven-plugin.version>4.3.0</coveralls-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.1.0</maven-checkstyle-plugin.version>
        <jmh.version>1.33</jmh.version>
        <gmaven-plugin.version>1.5</gmaven-plugin.version>
        <checkstyle.fails.on.error>true</checkstyle.fails.on.error>

    </properties>

    <profiles>
        <profile>
            <id>all</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>${exec-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>git submodule update</id>
                                <phase>initialize</phase>
                                <inherited>false</inherited>
                                <configuration>
                                    <executable>git</executable>
                                    <arguments>
                                        <argument>submodule</argument>
                                        <argument>update</argument>
                                        <argument>--init</argument>
                                        <argument>--recursive</argument>
                                    </arguments>
                                </configuration>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
            <version>${jmh.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${bytebuddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>${objenesis.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>${jmh.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- mvn -N io.takari:maven:wrapper -Dmaven=3.5.4 -->
                <plugin>
                    <groupId>io.takari</groupId>
                    <artifactId>maven</artifactId>
                    <version>${takari-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                    <configuration>
                        <forceCreation>true</forceCreation>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${maven-docker-plugin.version}</version>
                    <configuration>
                        <containerNamePattern>%a-%t-%i</containerNamePattern>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>${os-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>initialize</phase>
                        <goals>
                            <goal>detect</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven-enforcer-plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-java</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <!-- Build has not yet been updated for Java 9+ -->
                                    <version>1.8</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>3.6</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${compiler.version}</source>
                    <target>${compiler.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resource-plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>${docker.plugin.version}</version>
                <configuration>
                    <skipDocker>true</skipDocker>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>none</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <configuration>
                    <configLocation>${maven.multiModuleProjectDirectory}/apm-checkstyle/checkStyle.xml</configLocation>
                    <headerLocation>${maven.multiModuleProjectDirectory}/apm-checkstyle/CHECKSTYLE_HEAD</headerLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <failOnViolation>${checkstyle.fails.on.error}</failOnViolation>
                    <sourceDirectories>
                        <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
                        <sourceDirectory>${project.build.testSourceDirectory}</sourceDirectory>
                    </sourceDirectories>
                    <resourceIncludes>
                        **/*.properties,
                        **/*.sh,
                        **/*.bat,
                        **/*.yml,
                        **/*.yaml,
                        **/*.xml
                    </resourceIncludes>
                    <resourceExcludes>
                        **/.asf.yaml,
                        **/.github/**
                    </resourceExcludes>
                    <excludes>
                        **/target/generated-test-sources/**,
                        org/apache/skywalking/apm/network/register/v2/**/*.java,
                        org/apache/skywalking/apm/network/common/**/*.java,
                        org/apache/skywalking/apm/network/servicemesh/**/*.java,
                        org/apache/skywalking/apm/network/language/**/*.java,
                        org/apache/skywalking/oap/server/core/remote/grpc/proto/*.java,
                        org/apache/skywalking/oal/rt/grammar/*.java,
                        org/apache/skywalking/oap/server/exporter/grpc/*.java,
                        org/apache/skywalking/oap/server/configuration/service/*.java,
                        **/jmh_generated/*_jmhType*.java,
                        **/jmh_generated/*_jmhTest.java
                    </excludes>
                    <propertyExpansion>
                        import.control=${maven.multiModuleProjectDirectory}/apm-checkstyle/importControl.xml
                    </propertyExpansion>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
