<script setup lang="ts">
import { computed, inject, ref } from 'vue';
import {
  openDrawer, OrionTable, openModal, isPower,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { generateNumbering, getAllIds } from './utils';
import DrawerItem from './DrawerItem.vue';
import ModalPreview from './ModalPreview.vue';
import DrawerView from './DrawerView.vue';
import {
  add, edit, generateTask, listTree, remove, removeById,
} from '/@/views/pms/api/documentDecomposition';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const powerData: any = inject('powerData', []);
const tableRef = ref(null);
const loadingTask = ref(false);
const columns = [
  {
    title: '序号',
    dataIndex: 'level',
    width: 80,
    fixed: 'left',
  },
  {
    title: '编码',
    dataIndex: 'number',
    fixed: 'left',
    minWidth: 160,
  },
  {
    title: '条目标题',
    dataIndex: 'name',
    fixed: 'left',
    minWidth: 250,
  },
  {
    title: '关联任务',
    dataIndex: 'taskName',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '查看',
    isShow: () => isPower('WDMBKXQ_container_02_button_05', powerData.value),
    async onClick(record) {
      handleView(record.id);
    },
  },
  {
    text: '编辑',
    isShow: () => isPower('WDMBKXQ_container_02_button_06', powerData.value),
    onClick: (record) => {
      editEntry(record);
    },
  },
  {
    text: '删除',
    isShow: () => isPower('WDMBKXQ_container_02_button_07', powerData.value),
    async modal(record) {
      await removeById(record.id);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },
  {
    text: '添加子项',
    isShow: () => isPower('WDMBKXQ_container_02_button_08', powerData.value),
    async onClick(record) {
      addEntryChild(record);
    },
  },

];
const tableOptions = ref({
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: computed(() => {
    let str = 'add|enable|disable';
    if (!isPower('WDMBKXQ_container_02_button_04', powerData.value)) str += '|delete';
    return str;
  }),
  rowSelection: {
    type: 'check',
  },
  expandIconColumnIndex: 4,
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: false,
  isFilter2: false,
  api: () => listTree(props.id).then((res) => {
    // 根据层级生成类似于 "1.2.3" 这样的序号
    generateNumbering(res || []);
    return res;
  }),
  columns,
  actions,
  // 批量自定义删除
  batchDeleteApi: ({ ids }) => remove(ids),
});

const initData = (data) => {
  tableRef.value.expandedRowKeys = getAllIds(data);
};

const addEntry = () => {
  const refDrawer = ref();
  openDrawer({
    title: '新增条目',
    width: 1100,
    content(h) {
      return h(DrawerItem, {
        ref: refDrawer,
      });
    },
    async onOk() {
      const values = await refDrawer.value.handleSubmit();
      await add({
        ...values,
        mainTableId: props.id,
        parentId: 0,
      });
      tableRef.value.reload();
    },
  });
};
const editEntry = (record) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑条目',
    width: 1100,
    content(h) {
      return h(DrawerItem, {
        ref: refDrawer,
        id: record.id,
      });
    },
    async onOk() {
      const values = await refDrawer.value.handleSubmit();
      await edit({
        ...record,
        ...values,
      });
      tableRef.value.reload();
    },
  });
};
const addEntryChild = (record) => {
  const refDrawer = ref();
  openDrawer({
    title: '新增子条目',
    width: 1100,
    content(h) {
      return h(DrawerItem, {
        ref: refDrawer,
        parentName: record.name,
      });
    },
    async onOk() {
      const values = await refDrawer.value.handleSubmit();
      await add({
        ...values,
        mainTableId: props.id,
        parentId: record.id,
      });
      tableRef.value.reload();
    },
  });
};
// arf
const globalPreview = () => {
  openModal({
    title: '全局预览',
    width: 1200,
    height: 600,
    content(h) {
      return h(ModalPreview, {
        mainTableId: props.id,
      });
    },
  });
};
const handleView = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '条目详情',
    width: 1100,
    content(h) {
      return h(DrawerView, {
        ref: refDrawer,
        id,
      });
    },
  });
};
const createTask = async () => {
  try {
    loadingTask.value = true;
    await generateTask(props.id);
    message.success('生成任务成功');
  } finally {
    loadingTask.value = false;
  }
};

</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      class="plan"
      :options="tableOptions"
      @initData="initData"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('WDMBKXQ_container_02_button_01', powerData)"
          type="primary"
          icon="add"
          @click="addEntry"
        >
          添加条目
        </BasicButton>
        <BasicButton
          v-if="isPower('WDMBKXQ_container_02_button_02', powerData)"
          icon="orion-icon-folder-add"
          :loading="loadingTask"
          @click="createTask"
        >
          生成任务
        </BasicButton>
        <BasicButton
          v-if="isPower('WDMBKXQ_container_02_button_03', powerData)"
          icon="orion-icon-desktop"
          @click="globalPreview"
        >
          全局预览
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
