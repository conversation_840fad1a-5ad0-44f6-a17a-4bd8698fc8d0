package com.chinasie.orion.management.constant;

/**
 * 合同类型
 */

public enum ContractTypeEnum {

    KJHT("框架协议","100"),
    ZDD("标准订单","101");

    private String name;
    private String desc;

    ContractTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ContractTypeEnum lt : ContractTypeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}