package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectRewardPunishment DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectRewardPunishmentDTO对象", description = "项目奖惩情况")
@Data
@ExcelIgnoreUnannotated
public class ProjectRewardPunishmentDTO extends  ObjectDTO   implements Serializable{

    /**
     * 奖惩类型
     */
    @ApiModelProperty(value = "奖惩类型")
    @ExcelProperty(value = "奖惩类型 ", index = 0)
    private String type;

    /**
     * 情况
     */
    @ApiModelProperty(value = "情况")
    @ExcelProperty(value = "情况 ", index = 1)
    private String situation;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 2)
    private String projectId;


}
