import { isNull } from '/@/utils/is';

/**
 * @description:  为当前的tree增加一个父级的id,并且去掉子节点为0的children属性
 * @return Array
 */
export function addParentIdWrapper(tree: any) {
  function addParentId(data: any, parentId: string) {
    data.forEach((ele: any) => {
      const { children, id } = ele;
      ele.parent = parentId;
      if (children?.length) {
        addParentId(children, id);
      } else {
        ele.isLeaf = true; // 是叶子节点
        Reflect.deleteProperty(ele, 'children'); // 删除子节点为0的children属性,去掉组件tree的展开图标
      }
    });
  }
  addParentId(tree, '0'); // 顶级节点的父节点为0
  return tree;
}

/**
 * @description: 把树弄成平级（每个节点之间的联系依靠id属性）
 * @return Array
 */
export function flattenTreeDataClosure(data: any) {
  const flattenData: any = [];
  function flattenTree(data: any) {
    data.forEach((ele: any) => {
      const { children } = ele;
      flattenData.push(ele);
      if (children?.length) {
        flattenTree(children);
      }
    });
  }
  flattenTree(data);
  return flattenData;
}

/**
 * @description: 找到当前ID所有的父级节点,一级一级向上遍历
 * @param {array} tree  老需要查找的id
 * @param {string} findId  老需要查找的id
 * @param {boolean} hasSelf 返回的时候,是否含有自己的id
 * @param {boolean} isReturnObject  返回的时候,是否是对象的数组形式
 * @param {string} fieldName 字段名修改
 * @return Array
 */

export function findParents(
  tree: Array<any>,
  findId: string,
  hasSelf = true,
  isReturnObject = false,
  fieldName = 'id',
) {
  const ids: any = [];

  const getId = function (treeData: any, id: string) {
    treeData.find((node: any) => {
      const { parentId, children } = node;
      if (node[fieldName] === id) {
        if (id === findId) {
          // 他自己
          hasSelf && ids.unshift(isReturnObject ? node : id);
        } else {
          ids.unshift(isReturnObject ? node : id); // 添加数组到第一个位置
        }

        // 根节点没有parentId,不需要再往上查找
        if (parentId !== '0') {
          getId(tree, parentId);
          return true; // 不需要再遍历了,跳出循环
        }
      } else if (children) {
        getId(children, id);
      }
    });
  };

  getId(tree, findId);
  return ids;
}

/**
 * @description: 通过节点的id,找到当前节点的信息
 * *
 */
export function findNodeById(tree: Array<any>, id: string): any {
  for (const item of tree) {
    if (item.id === id) {
      return item;
    }
    if (item.children) {
      const res = findNodeById(item.children, id);
      if (res) return res;
    }
  }
  return null;
}

/**
 * @description: 删除树节点
 * *
 */
export function deletedNodeById(tree: Array<any>, deleteId: string): any {
  const treeData = JSON.parse(JSON.stringify(tree));
  const deleteParentNode = (data: any) => {
    const ret: any = [];
    data.forEach((ele: any) => {
      // eslint-disable-next-line prefer-const
      let { children, id } = ele;

      if (id !== deleteId) {
        ret.push(ele);
      }
      if (children?.length) {
        ele.children = deleteParentNode(children);
      }
    });
    return ret;
  };
  return deleteParentNode(treeData);
}

/**
 *
 * @description: 增加树节点
 *
 * *
 */

export function addTreeNode(tree: Array<any>, parentId: string, addNodeData: object) {
  // parentId 父节点id
  const treeData = JSON.parse(JSON.stringify(tree));
  const newNode = {
    ...addNodeData,
    parentId,
  };
  if (parentId === '0' || !parentId) {
    // 当前是一级节点
    treeData.push(newNode);
    return treeData;
  }
  const addNode = (data: any) => {
    data.forEach((ele: any) => {
      // eslint-disable-next-line prefer-const
      let { children, id } = ele;
      if (id === parentId) {
        ele.isLeaf = false;
        if (children?.length) {
          ele.children.push(newNode);
        } else {
          ele.children = [newNode];
        }
      }
      if (children?.length) {
        addNode(children);
      }
    });
  };

  addNode(treeData);
  return treeData;
}

/**
 *
 * @description: 编辑树节点
 *
 * *
 */

export function editTreeNode(tree: Array<any>, id: string, newNodeData: object) {
  let findNode = findNodeById(tree, id);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars,no-unused-vars
  findNode = {
    ...findNode,
    ...newNodeData,
  };
  return tree;
}

/**
 *
 * @description: 上移,下移树节点
 *
 * *
 */

export function moveTreeNode(tree: Array<any>, nodeId: any, moveType = 'up') {
  // @ts-ignore
  const node = findNodeById(tree, nodeId); // 找到当前id的节点,用于获取当前节点的属性
  const { id, parentId } = node;

  let parentNode: any; // 当前操作的节点

  if (parentId === '0' || isNull(parentId)) {
    parentNode = {
      id: '0',
      children: tree,
    };
  } else {
    parentNode = findNodeById(tree, parentId);
  }

  let pos = 0;
  const length = parentNode.children.length;

  parentNode.children.forEach((node: any, index: number) => {
    if (node.id === id) {
      pos = index;
    }
  });

  if (pos === 0 && moveType === 'up') {
    throw Error('元素已经在最顶级无法进行上移');
  } else if (pos === length - 1 && moveType === 'down') {
    throw Error('元素已经在最底级无法进行下移动');
  } else if (moveType === 'up') {
    const temp = parentNode.children[pos - 1];
    parentNode.children[pos - 1] = node;
    parentNode.children[pos] = temp;
  } else {
    const temp = parentNode.children[pos + 1];
    parentNode.children[pos + 1] = node;
    parentNode.children[pos] = temp;
  }
}
