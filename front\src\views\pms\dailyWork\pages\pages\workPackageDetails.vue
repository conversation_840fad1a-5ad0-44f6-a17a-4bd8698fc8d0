<script setup lang="ts">
import { BasicButton, IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import {
  UseBusinessWorkflowReturn, WorkflowAction, WorkflowProps, WorkflowView,
} from 'lyra-workflow-component-vue3';
import PackageInfo from './components/PackageInfo.vue';
import { usePagePower } from '/@/views/pms/hooks';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.assetName,
  projectCode: detailsData.number,
  dataStatus: detailsData.dataStatus,
}));

const menuData = computed(() => [
  {
    id: 'package',
    name: '工作包信息',
    powerCode: 'PMS_ZYGZBXQ_container_02',
  },
  {
    id: 'workflow',
    name: '流程',
    powerCode: 'PMS_ZYGZBXQ_container_03',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/job-manage/package/info').fetch({}, dataId.value, 'GET');
    Object.keys(result || {}).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

const workflowActionRef: Ref<UseBusinessWorkflowReturn> = ref();
const workflowViewRef: Ref<UseBusinessWorkflowReturn> = ref();
const workflowProps = computed(() => ({
  Api,
  businessData: detailsData,
  async afterEvent() {
    await getDetails();
    await workflowViewRef.value?.init();
  },
} as WorkflowProps));

// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd);
const workflowLoading: Ref<boolean> = ref(false);

function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  }, 'start', workflowLoading);
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout3
    v-get-power="{ pageCode: 'PMSDailyWorkPackageDetails',getPowerDataHandle}"
    v-loading="loading"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicButton
        v-if="showWorkflowAdd"
        v-is-power="['PMS_ZYGZBXQ_container_01_button_01']"
        type="primary"
        :loading="workflowLoading"
        icon="sie-icon-qidongliucheng"
        @click="handleAddWorkflow"
      >
        发起流程
      </BasicButton>
    </template>
    <template v-if="detailsData?.id">
      <PackageInfo v-if="actionId==='package'" />
      <WorkflowView
        v-if="actionId==='workflow'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template
      v-if="detailsData?.id"
      #footer
    >
      <WorkflowAction
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
