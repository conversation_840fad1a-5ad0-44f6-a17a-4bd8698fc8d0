<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>41</version>
    <relativePath />
  </parent>

  <artifactId>maven-compiler-plugin</artifactId>
  <version>3.13.0</version>
  <packaging>maven-plugin</packaging>

  <name>Apache Maven Compiler Plugin</name>
  <description>The Compiler Plugin is used to compile the sources of your project.</description>
  <inceptionYear>2001</inceptionYear>

  <contributors>
    <contributor>
      <name>Jan Sievers</name>
    </contributor>
  </contributors>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://github.com/apache/maven-compiler-plugin.git</connection>
    <developerConnection>scm:git:https://github.com/apache/maven-compiler-plugin.git</developerConnection>
    <tag>maven-compiler-plugin-3.13.0</tag>
    <url>https://github.com/apache/maven-compiler-plugin/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/MCOMPILER</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-maven.apache.org/job/Maven/job/maven-box/job/maven-compiler-plugin/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <mavenVersion>3.6.3</mavenVersion>
    <plexusCompilerVersion>2.15.0</plexusCompilerVersion>

    <groovyVersion>2.4.21</groovyVersion>
    <groovyEclipseCompilerVersion>3.7.0</groovyEclipseCompilerVersion>
    <groovy-eclipse-batch>2.5.14-02</groovy-eclipse-batch>
    <plexus-java.version>1.2.0</plexus-java.version>
    <javaVersion>8</javaVersion>
    <maven.it.failure.ignore>false</maven.it.failure.ignore>
    <project.build.outputTimestamp>2024-03-15T07:27:59Z</project.build.outputTimestamp>
    <invoker.junitPackageName>org.apache.maven.plugins.compiler.its</invoker.junitPackageName>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- Maven -->
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${mavenVersion}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>3.4.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-incremental</artifactId>
      <version>1.1</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.maven</groupId>
          <artifactId>maven-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven</groupId>
          <artifactId>maven-plugin-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.maven.shared</groupId>
          <artifactId>maven-shared-utils</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-annotations</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-java</artifactId>
      <version>${plexus-java.version}</version>
    </dependency>

    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-api</artifactId>
      <version>${plexusCompilerVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-manager</artifactId>
      <version>${plexusCompilerVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-compiler-javac</artifactId>
      <version>${plexusCompilerVersion}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>4.0.0-alpha-2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <!-- used by maven-plugin-testing-harness -->
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-xml</artifactId>
      <version>3.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.8.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>plexus.snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/plexus-snapshots</url>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <configuration>
            <java>
              <includes>
                <include>src/**/*.java</include>
              </includes>
            </java>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-report-plugin</artifactId>
          <configuration>
            <requirementsHistories>
              <requirementsHistory>
                <version>from 3.13.0</version>
                <maven>3.6.3</maven>
                <jdk>8</jdk>
              </requirementsHistory>
              <requirementsHistory>
                <version>from 3.9.0 to 3.12.1</version>
                <maven>3.2.5</maven>
                <jdk>8</jdk>
              </requirementsHistory>
              <requirementsHistory>
                <version>from 3.0 to 3.8.1</version>
                <maven>3.0</maven>
                <jdk>7</jdk>
              </requirementsHistory>
            </requirementsHistories>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>sisu-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <executions>
                <execution>
                  <id>integration-test</id>
                  <configuration>
                    <environmentVariables>
                      <JENKINS_MAVEN_AGENT_DISABLED>true</JENKINS_MAVEN_AGENT_DISABLED>
                    </environmentVariables>
                    <debug>true</debug>
                    <projectsDirectory>src/it</projectsDirectory>
                    <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                    <pomIncludes>
                      <pomInclude>*/pom.xml</pomInclude>
                      <pomInclude>extras/*/pom.xml</pomInclude>
                      <pomInclude>multirelease-patterns/*/pom.xml</pomInclude>
                    </pomIncludes>
                    <!--
                      ! Unfortunately we can't define an execution order.
                      ! https://issues.apache.org/jira/browse/MINVOKER-174
                      -->
                    <setupIncludes>
                      <setupInclude>setup*/pom.xml</setupInclude>
                    </setupIncludes>
                    <postBuildHookScript>verify</postBuildHookScript>
                    <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                    <settingsFile>src/it/settings.xml</settingsFile>
                    <ignoreFailures>${maven.it.failure.ignore}</ignoreFailures>
                    <streamLogsOnFailures>true</streamLogsOnFailures>
                    <goals>
                      <goal>clean</goal>
                      <goal>test-compile</goal>
                    </goals>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>
