package com.chinasie.orion.service.impl;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.ProjectProgressDTO;
import com.chinasie.orion.domain.dto.MajorPersonStatisticDTO;
import com.chinasie.orion.domain.dto.SchemeToMaterialDTO;
import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.importantProject.ImportParamDTO;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.ProjectProgressDTO;
import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.domain.dto.ImportantProjectDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobProgress;
import com.chinasie.orion.domain.entity.OrgProgress;
import com.chinasie.orion.domain.vo.*;


import com.chinasie.orion.domain.vo.JobProgressVO;
import com.chinasie.orion.domain.vo.ProjectScheduleVO;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.domain.vo.count.DateComputeVO;
import com.chinasie.orion.domain.vo.count.ProjectJobCountVO;
import com.chinasie.orion.domain.vo.count.ProjectPackageCountVO;
import com.chinasie.orion.domain.vo.excel.JobProgressExportVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.repository.JobProgressMapper;
import com.chinasie.orion.repository.ProjectProgressMapper;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.ImportantProjectMapper;
import com.chinasie.orion.page.PageResult;

import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobPackageService;
import com.chinasie.orion.service.JobProgressService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ImportantProject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
@Service
@Slf4j
public class ImportantProjectServiceImpl extends  OrionBaseServiceImpl<ImportantProjectMapper, ImportantProject>   implements ImportantProjectService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private JobProgressService progressService;

    @Autowired
    private JobProgressMapper jobProgressMapper;

    @Autowired
    private ProjectProgressMapper projectProgressMapper;

    @Autowired
    @Lazy
    private SchemeToMaterialService schemeToMaterialService;
    @Autowired
    @Lazy
    private SchemeToPersonService schemeToPersonService;

    @Autowired
    @Lazy
    private MajorRepairStatisticService majorRepairStatisticService;


    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ImportantProjectVO detail(String id,String pageCode) throws Exception {
        ImportantProject importantProject =this.getById(id);
        if (Objects.isNull(importantProject)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"数据不存在或者已被删除s！");
        }
        ImportantProjectVO result = BeanCopyUtils.convertTo(importantProject,ImportantProjectVO::new);
        setEveryName(Collections.singletonList(result));

        List<String> jobIds = jobManageMapper.selectProjectJobIdsByProjectId(id);
        result.setJobList(jobIds);
        return result;
    }

    /**
     *  新增
     *
     * * @param importantProjectDTO
     */
    @Override
    public  String create(ImportantProjectDTO importantProjectDTO) throws Exception {
        isExists(importantProjectDTO);
        getDate(importantProjectDTO);
        ImportantProject importantProject =BeanCopyUtils.convertTo(importantProjectDTO,ImportantProject::new);
        this.save(importantProject);
        //插入关系表
        String id=importantProject.getId();
        if (!CollectionUtils.isEmpty(importantProjectDTO.getJobIdList())){
            jobManageMapper.insertBatch(id, importantProjectDTO.getJobIdList());
        }

        return id;
    }

    @Override
    public DateComputeVO computeDate(List<String> ids) {
        ImportantProjectDTO importantProjectDTO = new ImportantProjectDTO();
        importantProjectDTO.setJobIdList(ids);
        getDate(importantProjectDTO);
        DateComputeVO dateComputeVO = new DateComputeVO();
        dateComputeVO.setPlanStart(importantProjectDTO.getPlanStart());
        dateComputeVO.setPlanEnd(importantProjectDTO.getPlanEnd());
        dateComputeVO.setActureStart(importantProjectDTO.getActureStart());
        dateComputeVO.setActureEnd(importantProjectDTO.getActureEnd());
        return dateComputeVO;
    }

    @Override
    public Page<SchemeToMaterialVO> materialPages(Page<ImportParamDTO> materialParamDTOPage) throws Exception {
        ImportParamDTO materialParamDTO=  materialParamDTOPage.getQuery();
        Page<SchemeToMaterialVO> pageResult = new Page<>(materialParamDTOPage.getPageNum(), materialParamDTOPage.getPageSize(), materialParamDTOPage.getTotalSize());
        if(Objects.isNull(materialParamDTO)){
            return  pageResult;
        }
        String importID=  materialParamDTO.getImportantId();
        String keyword= materialParamDTO.getKeyword();
        List<String> materialList= jobManageMapper.getMaterialIdList(importID,keyword);
        if(CollectionUtils.isEmpty(materialList)){
            return  pageResult;
        }
        String repairRound= materialParamDTO.getRepairRound();
        Page<SchemeToMaterialDTO> pageRequest = new Page<>();
        SchemeToMaterialDTO scheme  = new SchemeToMaterialDTO();
        scheme.setRepairRound(repairRound);
        scheme.setMaterialIds(materialList);
        pageRequest.setQuery(scheme);
        pageRequest.setPageNum(materialParamDTOPage.getPageNum());
        pageRequest.setPageSize(materialParamDTOPage.getPageSize());
        return   schemeToMaterialService.materialPages(pageRequest);
    }

    @Override
    public MajorPersonStatisticDTO getMaterialStatistic(ImportParamDTO materialParamDTO) {
        String importID=  materialParamDTO.getImportantId();
        String keyword= materialParamDTO.getKeyword();
        List<String> materialList= jobManageMapper.getMaterialIdList(importID,keyword);
        MajorPersonStatisticDTO majorPersonStatisticDTO= new MajorPersonStatisticDTO();
        if(CollectionUtils.isEmpty(materialList)){
            return majorPersonStatisticDTO;
        }
        majorRepairStatisticService.getCountToMaterial(majorPersonStatisticDTO,materialList.stream().distinct().collect(Collectors.toList()));;
        return majorPersonStatisticDTO;
    }

    @Override
    public Page<SchemeToPersonVO> personPages(Page<ImportParamDTO> pageRequest) {
        ImportParamDTO personPagetDto=  pageRequest.getQuery();
        Page<SchemeToPersonVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        if(Objects.isNull(personPagetDto)){
            return  pageResult;
        }
        String importID=  personPagetDto.getImportantId();
        String keyword= personPagetDto.getKeyword();
        List<String> personIdList= jobManageMapper.getPersonIdList(importID,keyword);
        if(CollectionUtils.isEmpty(personIdList)){
            return  pageResult;
        }
        String repairRound= personPagetDto.getRepairRound();
        Page<SchemeToPersonDTO> requestPage = new Page<>();
        SchemeToPersonDTO scheme  = new SchemeToPersonDTO();
        scheme.setRepairRound(repairRound);
        scheme.setPersonIdList(personIdList);
        requestPage.setQuery(scheme);
        requestPage.setPageNum(pageRequest.getPageNum());
        requestPage.setPageSize(pageRequest.getPageSize());
        return   schemeToPersonService.personPages(requestPage);

    }

    @Override
    public MajorPersonStatisticDTO getPersonStatistic(ImportParamDTO importParamDTO) {
        String importID=  importParamDTO.getImportantId();
        String keyword= importParamDTO.getKeyword();
        List<String> personIdList= jobManageMapper.getPersonIdList(importID,keyword);
        MajorPersonStatisticDTO majorPersonStatisticDTO= new MajorPersonStatisticDTO();
        if(CollectionUtils.isEmpty(personIdList)){
            return majorPersonStatisticDTO;
        }
        majorRepairStatisticService.getCountToPerson(majorPersonStatisticDTO,personIdList.stream().distinct().collect(Collectors.toList()));;
        return majorPersonStatisticDTO;
    }


    private ImportantProjectDTO getDate(ImportantProjectDTO importantProjectDTO){
        List<String> jobIdList = importantProjectDTO.getJobIdList();
        if (!CollectionUtils.isEmpty(importantProjectDTO.getJobIdList())){
            Date planStart = null;
            Date planEnd = null;
            Date actualStart = null;
            Date actualEnd = null;
            Boolean actualEndFlag = true;
            List<JobManage> jobManages = jobManageMapper.selectBatchIds(jobIdList);
            for (JobManage jobManage : jobManages) {
                if (Objects.isNull(planStart)||(Objects.nonNull(jobManage.getBeginTime())&&jobManage.getBeginTime().before(planStart))){
                    planStart = jobManage.getBeginTime();
                }
                if (Objects.isNull(planEnd)||(Objects.nonNull(jobManage.getEndTime())&&jobManage.getEndTime().after(planEnd))){
                    planEnd = jobManage.getEndTime();
                }
                if (Objects.isNull(actualStart)||(Objects.nonNull(jobManage.getActualBeginTime())&&jobManage.getActualBeginTime().before(actualStart))){
                    actualStart = jobManage.getActualBeginTime();
                }
                if (Objects.isNull(actualEnd)||(Objects.nonNull(jobManage.getActualEndTime())&&jobManage.getActualEndTime().after(actualEnd))){
                    actualEnd = jobManage.getActualEndTime();
                }
                if(jobManage.getActualEndTime()==null){
                    actualEndFlag = false;
                }
            }
            importantProjectDTO.setPlanStart(planStart);
            importantProjectDTO.setPlanEnd(planEnd);
            importantProjectDTO.setActureStart(actualStart);
            if (actualEndFlag){
                importantProjectDTO.setActureEnd(actualEnd);
            }else {
                importantProjectDTO.setActureEnd(null);
            }
        }
        return importantProjectDTO;
    }



    private boolean isExists(ImportantProjectDTO importantProjectDTO) {
        LambdaQueryWrapperX<ImportantProject> queryLamWrapperX=new LambdaQueryWrapperX<>(ImportantProject.class);
        if (StringUtils.hasText(importantProjectDTO.getId())){
            queryLamWrapperX.ne(ImportantProject::getId,importantProjectDTO.getId());
        }
        queryLamWrapperX.eq(ImportantProject::getRepairRound,importantProjectDTO.getRepairRound());
        queryLamWrapperX.eq(ImportantProject::getProjectName,importantProjectDTO.getProjectName());
        boolean exists = this.baseMapper.exists(queryLamWrapperX);
        if (exists){
            throw new BaseException(MyExceptionCode.ERROR_EXIST_DATA.getErrorCode(), "名称已经存,请修改名称");
        }
        return false;
    }

    /**
     *  编辑
     *
     * * @param importantProjectDTO
     */
    @Override
    public Boolean edit(ImportantProjectDTO importantProjectDTO) throws Exception {
        isExists(importantProjectDTO);
        getDate(importantProjectDTO);
        ImportantProject importantProject =BeanCopyUtils.convertTo(importantProjectDTO,ImportantProject::new);

        this.updateById(importantProject);

        String rsp=importantProject.getId();
        jobManageMapper.deleteRelations(rsp);
        List<String> jobIdList = importantProjectDTO.getJobIdList();
        if (!CollectionUtils.isEmpty(jobIdList)){
            jobManageMapper.insertBatch(rsp, jobIdList);
        }
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectJobCountVO> projectJobCountVOList = jobManageMapper.countJobNum(ids);
        if (!CollectionUtils.isEmpty(projectJobCountVOList)){
            throw new BaseException(MyExceptionCode.ERROR_DELETE_DATA.getErrorCode(),"项目已关联作业，无法删除");
        }
        this.removeBatchByIds(ids);
        //移除进展关系以及进展
        jobManageMapper.deleteProgressByProjectId(ids);
        List<String> progressIds = jobManageMapper.selectProgressByProjectId(ids);
        if (!CollectionUtils.isEmpty(progressIds)){
            progressService.removeBatchByIds(progressIds);
        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ImportantProjectVO> pages( Page<ImportantProjectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ImportantProject> condition = new LambdaQueryWrapperX<>( ImportantProject. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        String repairRound = pageRequest.getQuery().getRepairRound();
        condition.innerJoin(DeptDO.class, DeptDO::getId, ImportantProject::getDeptId);
        condition.eq(ImportantProject::getRepairRound,repairRound);
        condition.orderByDesc(ImportantProject::getCreateTime);


        Page<ImportantProject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ImportantProject::new));
        PageResult<ImportantProject> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ImportantProjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ImportantProjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ImportantProjectVO::new);
        if (!vos.isEmpty()){
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "重大项目导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ImportantProjectDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ImportantProjectExcelListener excelReadListener = new ImportantProjectExcelListener();
        EasyExcel.read(inputStream,ImportantProjectDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ImportantProjectDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("重大项目导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ImportantProject> importantProjectes =BeanCopyUtils.convertListTo(dtoS,ImportantProject::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ImportantProject-import::id", importId, importantProjectes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ImportantProject> importantProjectes = (List<ImportantProject>) orionJ2CacheService.get("pmsx::ImportantProject-import::id", importId);
        log.info("重大项目导入的入库数据={}", JSONUtil.toJsonStr(importantProjectes));

        this.saveBatch(importantProjectes);
        orionJ2CacheService.delete("pmsx::ImportantProject-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ImportantProject-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<ImportantProject> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ImportantProject> condition = new LambdaQueryWrapperX<>( ImportantProject. class);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        if (!StringUtils.isEmpty(pageRequest.getQuery().getRepairRound())){
            condition.eq(ImportantProject::getRepairRound,pageRequest.getQuery().getRepairRound());
        }
        condition.orderByDesc(ImportantProject::getCreateTime);
        List<ImportantProject> importantProjectes =   this.list(condition);

        List<ImportantProjectVO> vos = BeanCopyUtils.convertListTo(importantProjectes, ImportantProjectVO::new);
        if (!CollectionUtils.isEmpty(vos)){
            setEveryName(vos);
        }
        Integer i = 1;
        for (ImportantProjectVO vo : vos) {
            vo.setOrder(i++);
        }
        String fileName = "重大项目数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ImportantProjectVO.class,vos );

    }

    @Override
    public void  setEveryName(List<ImportantProjectVO> vos)throws Exception {
        List<String> projectIds = vos.stream().map(ImportantProjectVO::getId).collect(Collectors.toList());
        //统计作业数量
        List<ProjectJobCountVO> projectJobCountList = jobManageMapper.countJobNum(projectIds);
        Map<String, Integer> projectIdToCount = projectJobCountList.stream()
                .collect(Collectors.toMap(ProjectJobCountVO::getProjectId, ProjectJobCountVO::getJobCount));

        //统计工作包进展
        List<ProjectPackageCountVO> projectPackageAllCountList = jobManageMapper.countJobPackageByProjectIds(projectIds);
        List<ProjectPackageCountVO> projectPackageFinishCountList = jobManageMapper.countJobPackageByProjectIdsFinish(projectIds);
        //所有工作包数量
        Map<String, Integer> projectIdToPackageAllCount = new HashMap<>();
        if (!ArrayUtil.isEmpty(projectIdToPackageAllCount)){
            projectIdToPackageAllCount = projectPackageAllCountList.stream().
                    collect(Collectors.toMap(ProjectPackageCountVO::getProjectId, ProjectPackageCountVO::getPackageCount));
        }
        //已完成工作包数量
        Map<String, Integer> projectIdToPackageFinishCount = new HashMap<>();
        if (!ArrayUtil.isEmpty(projectPackageFinishCountList)){
            projectIdToPackageFinishCount = projectPackageFinishCountList.stream().
                    collect(Collectors.toMap(ProjectPackageCountVO::getProjectId, ProjectPackageCountVO::getPackageCount));
        }
        //获取完成进度
        List<ProjectScheduleVO> newestSchedule = jobManageMapper.getNewestSchedule(projectIds);
        Map<String, BigDecimal> idTOSchedule = newestSchedule.stream()
                .collect(Collectors.toMap(ProjectScheduleVO::getId, ProjectScheduleVO::getSchedule));
        //获取部门信息
        List<String> deptIds = vos.stream().map(ImportantProjectVO::getDeptId).collect(Collectors.toList());
        List<SimpleDeptVO> deptList = deptRedisHelper.getSimpleDeptByIds(deptIds);
        Map<String, String> idToName = deptList.stream().collect(Collectors.toMap(SimpleDeptVO::getId, SimpleDeptVO::getName));
        for (ImportantProjectVO vo : vos) {
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            vo.setJobCount(projectIdToCount.getOrDefault(vo.getId(), 0));
            Integer finishedPackageCount = projectIdToPackageFinishCount.getOrDefault(vo.getId(), 0);
            Integer allPackageCount = projectIdToPackageAllCount.getOrDefault(vo.getId(), 0);
            vo.setPackageSchedule(finishedPackageCount+"/"+allPackageCount);
            BigDecimal schedule = idTOSchedule.getOrDefault(vo.getId(), new BigDecimal(0));
            vo.setSchedule(schedule.toString()+"%");
            vo.setDeptName(idToName.getOrDefault(vo.getDeptId(), ""));
        }
    }
    @OperationPower(operationType = OperationPowerType.PAGE)
    @Override
    public Page<JobProgressVO> getJobProgressByProjectId(Page<JobProgressDTO> pageRequest) {
        LambdaQueryWrapperX<JobProgress> condition = new LambdaQueryWrapperX<>(JobProgress. class);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (StringUtils.hasText(pageRequest.getQuery().getProjectId())){
            condition.innerJoin(ProjectProgressDTO.class,ProjectProgressDTO::getProgressId, JobProgress::getId);
            condition.eq(ProjectProgressDTO::getProjectId, pageRequest.getQuery().getProjectId());
        }else if (StringUtils.hasText(pageRequest.getQuery().getRepairOrgId())){
            condition.innerJoin(OrgProgress.class,OrgProgress::getProgressId, JobProgress::getId);
            condition.eq(OrgProgress::getRepairOrgId, pageRequest.getQuery().getRepairOrgId());
        }

        condition.orderByDesc(JobProgress::getWorkDate);


        Page<JobProgress> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobProgress::new));

        PageResult<JobProgress> page = jobProgressMapper.selectPage(realPageRequest, condition);

        Page<JobProgressVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobProgressVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobProgressVO::new);

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void addProgress(JobProgressDTO jobProgressDTO) {
        JobProgress jobProgress = BeanCopyUtils.convertTo(jobProgressDTO, JobProgress::new);
        isExistsSameDate(jobProgressDTO);
        jobProgressMapper.insert(jobProgress);
        //关系插入
        ProjectProgressDTO projectProgressDTO = new ProjectProgressDTO();
        projectProgressDTO.setProgressId(jobProgress.getId());
        projectProgressDTO.setProjectId(jobProgressDTO.getProjectId());
        projectProgressMapper.insert(projectProgressDTO);
        ImportantProject importantProject = this.getBaseMapper().selectById(jobProgressDTO.getProjectId());
        //发送消息给大修指挥的人
        mscBuildHandlerManager.send(importantProject, MessageNodeDict.NODE_IMPORTANT_PROGRESS);
    }

    @Override
    public void editProgress(JobProgressDTO jobProgressDTO) {
        isExistsSameDate(jobProgressDTO);
        JobProgress jobProgress = BeanCopyUtils.convertTo(jobProgressDTO, JobProgress::new);
        jobProgressMapper.updateById(jobProgress);
    }

    @Override
    public Boolean deleteProgress(List<String> ids) {
        jobProgressMapper.deleteBatchIds(ids);
        return jobManageMapper.deleteProgressByProgressId(ids);
    }

    @Override
    public void getJobProgressByProjectIdExport(JobProgressDTO jobProgressDTO, HttpServletResponse response) throws Exception {
        if(StringUtils.isEmpty(jobProgressDTO.getProjectId())){
            throw new BaseException(MyExceptionCode.ERROR_DELETE_DATA.getErrorCode(),"所属重大项目不能为空");
        }
        LambdaQueryWrapperX<JobProgress> condition = new LambdaQueryWrapperX<>(JobProgress. class);
        condition.innerJoin(ProjectProgressDTO.class,ProjectProgressDTO::getProgressId, JobProgress::getId);
        condition.eq(ProjectProgressDTO::getProjectId, jobProgressDTO.getProjectId());
        condition.orderByDesc(JobProgress::getWorkDate);
        List<JobProgress> jobProgresses =  jobProgressMapper.selectList(condition);
        List<JobProgressExportVO> jobProgressExportVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(jobProgresses)){
            AtomicInteger i = new AtomicInteger(1);
            for (JobProgress jobProgress : jobProgresses) {
                JobProgressExportVO jobProgressExportVO= BeanCopyUtils.convertTo(jobProgress, JobProgressExportVO::new);
                jobProgressExportVO.setProgressSchedule(Objects.isNull(jobProgress.getProgressSchedule())? BigDecimal.ZERO.toString():
                        jobProgress.getProgressSchedule().doubleValue() + "%");
                jobProgressExportVO.setSort(i.get());
                i.incrementAndGet();
                jobProgressExportVOList.add(jobProgressExportVO);
            }
        }
        String fileName = "作业工作进展数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobProgressExportVO.class,jobProgressExportVOList );
    }


    private void isExistsSameDate(JobProgressDTO jobProgressDTO){
        String projectId = jobProgressDTO.getProjectId();
        Integer count = 0;
        if (StringUtils.hasText(jobProgressDTO.getId())){
            count = jobManageMapper.selectCountProgressDateJobId(projectId,jobProgressDTO.getWorkDate(),jobProgressDTO.getId());
        }else{
            count = jobManageMapper.selectCountProgressDate(projectId,jobProgressDTO.getWorkDate());
        }
        Assert.isTrue(count<=0,()-> new BaseException(MyExceptionCode.ERROR_EXIST_DATA.getErrorCode(),"该日期工作进度已存在!"));
    }


    public static class ImportantProjectExcelListener extends AnalysisEventListener<ImportantProjectDTO> {

        private final List<ImportantProjectDTO> data = new ArrayList<>();

        @Override
        public void invoke(ImportantProjectDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ImportantProjectDTO> getData() {
            return data;
        }
    }


}
