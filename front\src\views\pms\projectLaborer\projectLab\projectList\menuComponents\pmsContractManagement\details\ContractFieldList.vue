<template>
  <UploadList
    :listApi="listApi"
    :edit="false"
    :powerCode="powerCodes"
    :powerData="powerData"
  />
</template>

<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { inject } from 'vue';

const route = useRoute();
const powerData = inject('powerData');
const powerCodes = {
  delete: 'PMS_HTGLXQ_container_file_01',
  download: 'PMS_HTGLXQ_container_file_02',
  preview: 'PMS_HTGLXQ_container_file_04',
  edit: 'PMS_HTGLXQ_container_file_05',
};

async function listApi() {
  return await new Api('/res/manage/file/new').fetch('', route.params.id, 'GET').then((res) => {
    if (res?.length) {
      res.forEach((item) => {
        delete item.children;
      });
    }
    return res;
  });
}
</script>
