package com.chinasie.orion.domain.entity.projectStatistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsStatusStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2023-12-21 14:07:54
 */
@TableName(value = "pmsx_goods_status_statistics")
@ApiModel(value = "GoodsStatusStatisticsEntity对象", description = "物资状态趋势统计表")
@Data
public class GoodsStatusStatistics  implements Serializable{

    /**
     * 统计ID
     */
    @ApiModelProperty(value = "统计ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "date_str")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    @TableField(value = "uk")
    private String uk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 未审核数量
     */
    @ApiModelProperty(value = "未审核数量")
    @TableField(value = "no_audit_count")
    private Integer noAuditCount;

    /**
     * 审核中数量
     */
    @ApiModelProperty(value = "审核中数量")
    @TableField(value = "under_review_count")
    private Integer underReviewCount;

    /**
     * 已审核数量
     */
    @ApiModelProperty(value = "已审核数量")
    @TableField(value = "reviewed_count")
    private Integer reviewedCount;

    /**
     * 已入库数量
     */
    @ApiModelProperty(value = "已入库数量")
    @TableField(value = "store_count")
    private Integer storeCount;

}
