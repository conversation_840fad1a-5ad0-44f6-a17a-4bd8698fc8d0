package com.chinasie.orion.domain.vo.train;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.TrainEquivalentVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/05/19:17
 * @description:
 */
@ApiModel(value = "PersonTrainVO对象", description = "人员培训信息列表")
@Data
public class PersonTrainVO  implements Serializable {


    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    private String trainNumber;

    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    private String trainName;

    @ApiModelProperty(value = "培训基地名称")
    private String baseName;

    @ApiModelProperty(value = "培训基地编码")
    private String baseCode;
    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    private BigDecimal lessonHour;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间/完成时间")
    private Date endDate;

    @ApiModelProperty(value = "是否等效")
    private Boolean isEquivalent;
    /**
     * 办结时间
     */
    @ApiModelProperty(value = "到期时间时间")
    private Date expireTime;

    @ApiModelProperty(value = "附件")
    private List<FileVO> fileVOList;
    @ApiModelProperty(value = "等效列表")
    private List<TrainEquivalentVO> equivalentVOList;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;


    @ApiModelProperty(value = "参培中心关联表Id")
    private String trainCenterId;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    private String content;
    @ApiModelProperty(value = "数据ID")
    private String id ;
}
