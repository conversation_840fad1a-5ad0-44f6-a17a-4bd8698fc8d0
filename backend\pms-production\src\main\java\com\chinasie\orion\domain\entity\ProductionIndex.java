package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProductionIndex Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@TableName(value = "pmsx_production_index")
@ApiModel(value = "ProductionIndexEntity对象", description = "生产看板指标维护")
@Data

public class ProductionIndex extends ObjectEntity implements Serializable {

    /**
     * 指标编码
     */
    @ApiModelProperty(value = "指标编码")
    @TableField(value = "index_number")
    private String indexNumber;

    /**
     * 考核类型
     */
    @ApiModelProperty(value = "考核类型")
    @TableField(value = "index_type")
    private String indexType;

    /**
     * 目标值
     */
    @ApiModelProperty(value = "目标值")
    @TableField(value = "index_target")
    private String indexTarget;

    /**
     * 实际值
     */
    @ApiModelProperty(value = "实际值")
    @TableField(value = "index_actual")
    private String indexActual;

    /**
     * 状态灯
     */
    @ApiModelProperty(value = "状态灯")
    @TableField(value = "index_status")
    private String indexStatus;

}
