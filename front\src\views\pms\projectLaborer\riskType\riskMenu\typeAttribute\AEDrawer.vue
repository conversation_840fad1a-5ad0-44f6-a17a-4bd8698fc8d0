<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      :width="500"
      @register="register"
      @close="cancelClick('close')"
    >
      <BasicForm
        class="oobbj"
        @register="registerForm"
      />

      <template #footer>
        <DrawerFooterButtons
          v-model:checked="checked"
          :isContinue="cloneData[0]==='add'"
          :loading="loading"
          @cancelClick="cancelClick('close')"
          @okClick="okClick"
        />
      </template>
    </BasicDrawer>
    <AttributeModal
      @register="attrModal"
      @yesEmit="yesEmit"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  h, computed,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm, useModal,
} from 'lyra-component-vue3';
import { Checkbox, Button, message } from 'ant-design-vue';
import Api from '/@/api';
import AttributeModal from './AttributeModal.vue';
import { DrawerFooterButtons } from '/@/views/components';

export default defineComponent({
  name: '',
  components: {
    BasicDrawer,
    BasicForm,
    AttributeModal,
    DrawerFooterButtons,
  },
  props: {
    selectChangeData: {},
  },
  emits: ['addSuccess'],
  setup(props, { emit }) {
    const [attrModal, attrM] = useModal();
    const state: any = reactive({
      cloneData: [],
      checked: undefined,
      loading: false,
      TableData: [], // 引用属性数据
      isAddTableDadaId: false, // 是否添加引用属性的标识
    });

    function resetData() {
      state.cloneData = [];
      state.checked = undefined;
      state.loading = false;
      state.TableData = [];
      state.isAddTableDadaId = false;
    }

    const schemas: FormSchema[] = [
      {
        field: 'name',
        component: 'Input',
        label: '名称:',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
          {
            min: 2,
            max: 15,
            message: '名称2-15位',
            trigger: 'blur',
          },
        ],
        componentProps: {
          onChange: () => {
            // state.isAddTableDadaId = false
          },
          maxlength: 15,
          suffix: h(
            'span',
            {
              class: 'action-btn',
              onClick: () => {
                attrM.setModalProps({
                  title: '属性库列表',
                  width: '60%',
                  height: 500,
                });
                attrM.openModal(true);
              },
            },
            '引用属性',
          ),
        },
        ifShow() {
          if (state.cloneData[0] === 'add') {
            return true;
          }
          return false;
        },
      },
      {
        field: 'name',
        component: 'Input',
        label: '名称:',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
        ],
        componentProps: {
          onChange: (e) => {
            // state.isAddTableDadaId = false
          },
          suffix: h(
            'span',
            {
              class: 'action-btn',
              onClick: () => {
                attrM.setModalProps({
                  title: '属性库列表',
                  width: 1200,
                  height: 500,
                });
                attrM.openModal(true);
              },
            },
            '',
          ),
        },
        ifShow() {
          if (state.cloneData[0] === 'edit') {
            return true;
          }
          return false;
        },
      },
      {
        field: 'number',
        component: 'Input',
        label: '属性编码:',
        // rules: [
        //   { required: true, trigger: 'blur' },
        // ],
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'type',
        component: 'Select',
        label: '类型:',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
        ],
        componentProps: {
          options: [
            {
              label: '输入项',
              value: '1',
            },
            {
              label: '单选项',
              value: '2',
            },
            {
              label: '复选项',
              value: '3',
            },
          ],
          disabled: computed(() => state.isAddTableDadaId || state.cloneData[0] === 'edit'),
        },
      },
      {
        field: 'options',
        component: 'InputTextArea',
        label: '选项值:',
        helpMessage: '多个值用分号 (;) 隔开',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
        ],
        componentProps: {},
        ifShow(renderCallbackParams) {
          if (renderCallbackParams.model.type === '2' || renderCallbackParams.model.type === '3') {
            return true;
          }
          return false;
        },
      },
      {
        field: 'require',
        component: 'Select',
        label: '是否必填:',
        rules: [
          {
            required: true,
            trigger: 'blur',
            type: 'number',
          },
        ],
        componentProps: {
          options: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
      },
      {
        field: 'remark',
        component: 'InputTextArea',
        label: '描述:',
        componentProps: {
          rows: 4,
          showCount: true,
          maxlength: 100,
        },
      },
    ];
    const [registerForm, F1] = useForm({
      layout: 'vertical',
      baseColProps: {
        span: 24,
      },
      schemas,
      showSubmitButton: false,
      showResetButton: false,
    });
    const [register, DrawerM] = useDrawerInner((data) => {
      state.cloneData = JSON.parse(JSON.stringify(data));
      if (state.cloneData[0] === 'edit') {
        // if (state.cloneData[1].fileToolList?.length > 0) {
        //   state.cloneData[1].fileToolList = state.cloneData[1].fileToolList.map((item) => item.id);
        // }
        F1.setFieldsValue(state.cloneData[1].editData);
      }
    });

    /**
         * @description: 提交确定
         * */
    async function okClick() {
      await F1.validate();
      state.loading = true;
      const fd = F1.getFieldsValue();
      // console.log("----- fd -----", fd)
      // 如果格式不对,此处可以对请求参数进行拷贝并处理
      const params = JSON.parse(JSON.stringify(fd));
      // params.fileToolList = params.fileToolList.map((item) => ({
      // id: item
      // }));
      params.typeId = props.selectChangeData.id;
      if (state.isAddTableDadaId) {
        params.id = state.TableData[0].id;
      }
      if (state.cloneData[0] === 'add') {
        new Api('/pms/projectPlan-type-to-risk-type-attribute').fetch(params, '', 'POST').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      } else {
        // 如果格式不对或需要添加某些id,此处可以对请求参数进行处理
        params.id = state.cloneData[1].editData.id;
        new Api('/pms/projectPlan-type-to-risk-type-attribute').fetch(params, '', 'PUT').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      }
    }

    /**
         * @description: 取消
         * */
    function cancelClick(type) {
      state.loading = false;
      if (type === 'close') {
        resetData();
        F1.resetFields();
        DrawerM.closeDrawer();
      } else if (!state.checked) {
        F1.resetFields();
        DrawerM.closeDrawer();
      } else {
        F1.resetFields();
      }
    }

    function yesEmit(data) {
      state.TableData = JSON.parse(JSON.stringify(data));
      F1.setFieldsValue({
        name: data[0]?.name,
        type: data[0]?.type,
        number: data[0]?.number,
        require: data[0]?.require,
        options: data[0]?.options,
        remark: data[0]?.remark,
      });
      state.isAddTableDadaId = true;
    }

    return {
      ...toRefs(state),
      register,
      registerForm,
      okClick,
      cancelClick,
      attrModal,
      yesEmit,
    };
  },
});
</script>
<style lang="less" scoped>
//:deep(.bbj){
//  background-color: red !important;
//  &>.ant-row{
//    .ant-col{
//      .ant-form-item{
//        display: block !important;
//      }
//    }
//  }
//
//}
.oobbj {
  .ant-form-item {
    display: block !important;
  }
}
</style>
