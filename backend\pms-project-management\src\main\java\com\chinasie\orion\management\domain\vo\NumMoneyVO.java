package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NumMoneyVO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@ApiModel(value = "NumMoneyVO对象", description = "数量金额对象")
@Data
public class NumMoneyVO extends ObjectVO implements Serializable {

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer number;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private Double money;

}
