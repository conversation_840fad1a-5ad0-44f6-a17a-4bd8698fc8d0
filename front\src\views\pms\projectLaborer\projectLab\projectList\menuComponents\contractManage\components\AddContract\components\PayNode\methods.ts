import { Ref, unref } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

/**
 * 节点是否已支付
 * @param payNode
 */
export function isNodePay(payNode: any) {
  return payNode?.dataStatus?.statusValue === 130;
}

/**
 * 是否是已经保存的节点
 * @description 合同变更时，已经保存的节点，不可直接更改，只允许删除后重新添加
 * @param payNode
 * @param type
 */
export function isSavePayNodeAndChange(payNode: any, type: 'add' | 'change') {
  return payNode.id.indexOf('add') < 0 && type === 'change';
}

interface UsePayNodeVerifyProps {
  dataSource: Ref<any[]>,
  getAllMoney: ()=>number
}

/**
 * 支付节点的逻辑验证
 * @param props
 */
export function usePayNodeVerify(props: UsePayNodeVerifyProps) {
  const { dataSource, getAllMoney } = props;

  // 必填项验证
  function verifyRequired(index: number) {
    const data = unref(dataSource);
    const record = data[index];
    // 必填项
    const requiredKeys = [
      'settlementType',
      'payType',
      'initPlanPayDate',
      'initPlanPayAmt',
      'payPercentage',
    ];
    for (const key of requiredKeys) {
      if (!record[key]) {
        return false;
      }
    }
    return true;
  }

  // 验证支付日期，后续支付日期必须大于前一个支付日期
  function verifyPayDate(index: number, val?:any) {
    const data = unref(dataSource);
    const record = data[index];

    const preData = (index <= 0 ? 0 : dayjs(data[index - 1].initPlanPayDate).valueOf());
    const currentDate = dayjs(val ?? record.initPlanPayDate).valueOf();

    return currentDate >= preData;
  }

  /**
   * 验证金额
   * @description 所有节点不能超过总金额
   * @param index
   * @param val
   */
  function verifyMoney(index: number, val?:number):boolean | number {
    // 总金额
    const allMoney = getAllMoney();
    const data = unref(dataSource);

    let money = 0;

    for (let i = 0; i < data.length; i++) {
      if (i <= index) {
        if (i === index && val) {
          money += Number(val);
        } else {
          money += Number(data[i].initPlanPayAmt ?? 0);
        }
      } else {
        break;
      }
    }

    // 如果是刚好输入的
    if (val) {
      if (money < allMoney) {
        // 如果没有超，直接返回原数值
        return val;
      }
      // 如果超了，返回超出的值
      return allMoney - (money - val);
    }

    // 返回布尔，主要是用于总体验证
    return money <= allMoney;
  }

  /**
   * 验证支付百分百
   */
  function verifyPercentage(index:number, val?:number) {
    // 总金额
    const all = 100;
    const data = unref(dataSource);

    let percentage = 0;

    for (let i = 0; i < data.length; i++) {
      if (i <= index) {
        if (i === index && val) {
          percentage += Number(val);
        } else {
          percentage += Number(data[i].payPercentage ?? 0);
        }
      } else {
        break;
      }
    }

    // 如果是刚好输入的
    if (val) {
      if (percentage < all) {
        // 如果没有超，直接返回原数值
        return val;
      }
      // 如果超了，返回超出的值
      return all - (percentage - val);
    }

    // 返回布尔，主要是用于总体验证
    return percentage <= all;
  }

  function verify(type: 'submit') {
    return new Promise((resolve, reject) => {
      const data = unref(dataSource);

      for (let i = 0; i < data.length; i++) {
        // 提交时再进行必填项验证
        if (type === 'submit' && !verifyRequired(i)) {
          const errMsg = '请完善合同支付节点必填项';
          message.error(errMsg);
          reject(errMsg);
          return;
        }

        if (!verifyPayDate(i)) {
          const errMsg = `合同支付节点日期，后续支付日期必须大于前一个支付日期。 错误节点:${i + 1}`;
          message.error(errMsg);
          reject(errMsg);
          return;
        }

        if (!verifyMoney(i)) {
          const errMsg = `合同支付节点:初始计划支付金额，不能大于合同金额。 错误节点:${i + 1}`;
          message.error(errMsg);
          reject(errMsg);
          return;
        }

        if (!verifyPercentage(i)) {
          const errMsg = `合同支付节点:支付百分比，总百分比不能大于100。 错误节点:${i + 1}`;
          message.error(errMsg);
          reject(errMsg);
          return;
        }
      }

      resolve(unref(dataSource));
    });
  }

  return {
    verify,
    verifyRequired,
    verifyPayDate,
    verifyMoney,
    verifyPercentage,
  };
}
