<template>
  <div class="checkDetails">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="440"
      class="checkDetailsDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <div class="role-content-drawer">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane
            key="1"
            tab="概述"
            class="tabPaneStyle"
          />
        </a-tabs>
        <basicTitle
          :title="'基本信息'"
          class="checkDetailsMessage"
        >
          <div class="messageContent">
            <template
              v-for="(item, index) in valueList"
              :key="index"
            >
              <div class="messageContent_row">
                <span class="messageContent_row_label">{{ item.label }}</span>
                <span
                  v-if="item.fieldName!=='statusName'"
                  class="messageContent_row_value"
                >{{
                  ['modifyTime', 'createTime'].includes(item.fieldName)
                    ? dayjs(datavalue[0][item.fieldName]).format('YYYY-MM-DD HH:mm:ss')
                    : datavalue[0][item.fieldName]
                }}</span>
                <UserStatus
                  v-if="['statusName'].includes(item.fieldName)"
                  style="display: inline-block"
                  :status="datavalue[0].status"
                />
              </div>
            </template>
          </div>
        </basicTitle>
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch,
} from 'vue';
import { Tabs } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  BasicDrawer, UserStatus,
} from 'lyra-component-vue3';

export default defineComponent({
  components: {
    basicTitle,
    aTabs: Tabs,
    aTabPane: Tabs.TabPane,
    BasicDrawer,
    UserStatus,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      visible: false,
      title: '查看详情',
      nextCheck: false,
      activeKey: '1',
      valueList: [
        {
          label: '姓名',
          fieldName: 'name',
        },
        {
          label: '工号',
          fieldName: 'number',
        },
        // { label: '所在部门', fieldName: 'deptName' },
        {
          label: '所在部门',
          fieldName: 'orgName',
        },
        {
          label: '电话',
          fieldName: 'mobile',
        },
        {
          label: '邮箱',
          fieldName: 'email',
        },
        {
          label: '状态',
          fieldName: 'statusName',
        },
        {
          label: '描述',
          fieldName: 'remark',
        },
        {
          label: '修改人',
          fieldName: 'modifyName',
        },
        {
          label: '修改时间',
          fieldName: 'modifyTime',
        },
        {
          label: '创建人',
          fieldName: 'creatorName',
        },
        {
          label: '创建时间',
          fieldName: 'createTime',
        },
      ],
      datavalue: {},
    });
    const formRef = ref();

    watch(
      () => props.data,
      (newVal) => {
        state.visible = true;
        state.datavalue = { ...newVal };
      },
    );

    return {
      ...toRefs(state),
      formRef,
      close() {
        state.visible = false;
        emit('close', false);
      },
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
.role-content-drawer {
  padding: 16px 20px;
}
</style>
