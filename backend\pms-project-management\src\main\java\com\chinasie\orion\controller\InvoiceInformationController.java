package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.InvoiceInformationDTO;
import com.chinasie.orion.domain.entity.InvoiceInformation;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.domain.vo.InvoiceInformationVO;
import com.chinasie.orion.service.InvoiceInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * InvoiceInformation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@RestController
@RequestMapping("/invoiceInformation")
@Api(tags = "发票信息")
public class  InvoiceInformationController  {

    @Autowired
    private InvoiceInformationService invoiceInformationService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<InvoiceInformationVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        InvoiceInformationVO rsp = invoiceInformationService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param invoiceInformationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#invoiceInformationDTO.name}}】", type = "InvoiceInformation", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody InvoiceInformationDTO invoiceInformationDTO) throws Exception {
        String rsp =  invoiceInformationService.create(invoiceInformationDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param invoiceInformationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#invoiceInformationDTO.name}}】", type = "InvoiceInformation", subType = "编辑", bizNo = "{{#invoiceInformationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  InvoiceInformationDTO invoiceInformationDTO) throws Exception {
        Boolean rsp = invoiceInformationService.edit(invoiceInformationDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "InvoiceInformation", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = invoiceInformationService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "InvoiceInformation", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = invoiceInformationService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "InvoiceInformation", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<InvoiceInformationVO>> pages(@RequestBody Page<InvoiceInformationDTO> pageRequest) throws Exception {
        Page<InvoiceInformationVO> rsp =  invoiceInformationService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("发票信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "InvoiceInformation", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        invoiceInformationService.downloadExcelTpl(response);
    }

    @ApiOperation("发票信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "InvoiceInformation", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = invoiceInformationService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("发票信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "InvoiceInformation", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  invoiceInformationService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消发票信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "InvoiceInformation", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  invoiceInformationService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("发票信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "InvoiceInformation", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        invoiceInformationService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("发票信息表统计")
    @GetMapping(value = "/getTotal")
    @LogRecord(success = "【{USER{#logUserId}}】统计数据", type = "AdvancePaymentInvoiced", subType = "统计数据", bizNo = "")
    public ResponseDTO<InvoiceInformationVO> getTotal(@RequestParam String contractId) throws Exception {
        InvoiceInformationVO rsp  =invoiceInformationService.getTotal(contractId);
        return  new ResponseDTO<>(rsp);
    }
}
