<template>
  <BasicDrawer
    v-bind="$attrs"
    width="600"
    :showFooter="true"
    @register="drawerRegister"
    @visibleChange="visibleChange"
    @ok="okHandle"
  >
    <CreatePlanMain
      v-if="state.visible"
      :type="state.type"
      :record="state.record"
      @formInit="formInit"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner, FormActionType } from 'lyra-component-vue3';
import { reactive } from 'vue';
import dayjs from 'dayjs';
import CreatePlanMain from './CreatePlanMain.vue';
import { submitAddOrEditPlan } from '/@/views/pms/api';

type DrawerType = 'edit' | 'add'

const state = reactive({
  type: 'add' as DrawerType,
  record: null,
  parentId: '',
  visible: false,
  formMethods: null as FormActionType | null,
  successChange: null as ()=>void | null,
});

const [drawerRegister, { setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner((props: {
  type?: DrawerType,
  record: any,
  parentId?: string,
  successChange?: ()=>void
}) => {
  state.type = props?.type;
  state.record = props?.record;
  state.parentId = props?.parentId;
  state.successChange = props?.successChange;
  state.visible = true;
  init(props);
});

function init(props) {
  setTitle();
}

function setTitle() {
  setDrawerProps({
    title: state.type === 'add' ? '新增计划' : '编辑计划',
  });
}

function visibleChange(visible) {
  if (!visible) {
    state.visible = visible;
  }
}

function formInit(formMethods:FormActionType) {
  state.formMethods = formMethods;
}

async function okHandle() {
  const formValues = await state.formMethods.validate();
  formValues.parentId = state.parentId;
  formValues.startTime = formValues.startTime ? dayjs(formValues.startTime).valueOf() : '';
  formValues.endTime = formValues.endTime ? dayjs(formValues.endTime).valueOf() : '';
  if (state.type === 'edit') {
    formValues.id = state.record?.id;
  }

  changeOkLoading(true);
  submitAddOrEditPlan(state.type, formValues).then(() => {
    state.successChange && state.successChange();
    closeDrawer();
  }).finally(() => {
    changeOkLoading(false);
  });
}
</script>

<style scoped>

</style>
