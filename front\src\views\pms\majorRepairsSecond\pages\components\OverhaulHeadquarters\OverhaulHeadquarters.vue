<script setup lang="ts">
import {
  BasicButton, isPower, randomString,
} from 'lyra-component-vue3';
import {
  computed, inject, reactive, ref, watchEffect,
} from 'vue';
import { get as _get } from 'lodash-es';
import ScrollableRow from './ScrollableRow.vue';
import TraColumn from './TraColumn.vue';
import { openFormDrawerOrModal } from '../../utils/util';
import RoleManage from '../RoleManage.vue';
import ModalMemberManage from '../ModalMemberManage.vue';
import Api from '/@/api';

const props = defineProps({
  parentData: {
    type: Object,
    default: () => {},
  },
});
const parent = computed(() => props.parentData);
const powerData = computed(() => inject('powerData'));
const refreshUpdateWorkKey = inject('refreshUpdateWorkKey');

const firstLevelMember = ref([]);
const secondLevelMember = ref([]);
const thirdLevelMember = ref([]);

function handleAdd() {
  openFormDrawerOrModal(RoleManage, {
    title: '大修角色',
    parent: props.parentData,
  }, () => {
    getMemberByLevel();
  });
}
function handleRowColumnAdd(conf, updateFn) {
  openFormDrawerOrModal(ModalMemberManage, {
    title: '成员管理',
    ...({
      ...(conf || {}),
      parent: props.parentData,
    }),
  }, () => {
    updateFn();
    refreshUpdateWorkKey.value = randomString(32);
  });
}

async function getMemberByLevel() {
  await getFirstMemberByLevel();
  await geSecondMemberByLevel();
  await geThirdMemberByLevel();
}
async function getMember(level) {
  const res = await new Api('/pms/majorRepairPlanMember/pageByLevel').fetch({
    pageNum: 1,
    pageSize: 100,
    query: {
      majorRepairTurn: _get(parent.value, 'repairRound'),
      roleLevel: level,
    },
  }, '', 'POST');
  return res.content;
}
async function getFirstMemberByLevel() {
  firstLevelMember.value = await getMember(1);
}
async function geSecondMemberByLevel() {
  secondLevelMember.value = await getMember(2);
}
async function geThirdMemberByLevel() {
  thirdLevelMember.value = await getMember(3);
}

watchEffect(() => {
  if (_get(parent.value, 'repairRound')) {
    getMemberByLevel();
  }
});
</script>

<template>
  <div class="overhaul-headquarters">
    <BasicButton
      v-if="isPower('PMS_DXXQEC_container_08_button_04',powerData)"
      type="primary"
      icon="add"
      @click="handleAdd"
    >
      添加角色
    </BasicButton>
    <div class="trapezium-manage">
      <div class="rect-wrap" />
      <ScrollableRow
        class-name="trapezium-first"
        :role-level="1"
        :show-add-btn="isPower('PMS_DXXQEC_container_08_button_03',powerData)"
        @on-row-column-to-add="(conf)=>{
          handleRowColumnAdd(conf,getFirstMemberByLevel)
        }"
      >
        <tra-column
          v-for="(item,index) in firstLevelMember"
          :key="index"
        >
          <div class="tra-name">
            {{ item.userName }}
          </div>
          <div class="tra-role">
            {{ item.roleName }}
          </div>
        </tra-column>
      </ScrollableRow>
      <ScrollableRow
        class-name="trapezium-second"
        :role-level="2"
        :show-add-btn="isPower('PMS_DXXQEC_container_08_button_02',powerData)"
        @on-row-column-to-add="(conf)=>{
          handleRowColumnAdd(conf,geSecondMemberByLevel)
        }"
      >
        <tra-column
          v-for="(item,index) in secondLevelMember"
          :key="index"
        >
          <div class="tra-name">
            {{ item.userName }}
          </div>
          <div class="tra-role">
            {{ item.roleName }}
          </div>
        </tra-column>
      </ScrollableRow>
      <ScrollableRow
        class-name="trapezium-third"
        :role-level="3"
        :show-add-btn="isPower('PMS_DXXQEC_container_08_button_01',powerData)"
        @on-row-column-to-add="(conf)=>{
          handleRowColumnAdd(conf,geThirdMemberByLevel)
        }"
      >
        <tra-column
          v-for="(item,index) in thirdLevelMember"
          :key="index"
        >
          <div class="tra-name">
            {{ item.userName }}
          </div>
          <div class="tra-role">
            {{ item.roleName }}
          </div>
        </tra-column>
      </ScrollableRow>
    </div>
  </div>
</template>

<style scoped lang="less">
.trapezium-manage {
  position: relative;
  padding-bottom: 30px;
  padding-right: 16px;
  .rect-wrap {
    width: 16px;
    height: 100%;
    background: #fff;
    position: absolute;
    left: 248px;
    top: 0;
    transform: skew(27deg);
  }

  .trapezium-first {
    margin-left: 109px;
    margin-top: 32px;
    border-bottom: 100px solid #89acff;
    width: calc(100% - 224px);
    padding: 0 1px 0 38px;
    :deep(.trapezium-row-inner){
      background: linear-gradient(to right, #89acff, #d9e4fc);
      .tf-right{
        background: #f0f4fc;
      }
    }
  }

  .trapezium-second {
    margin-left: 55px;
    border-bottom: 100px solid #bacfff;
    margin-top: 12px;
    width: calc(100% - 114px);
    padding: 0 1px 0 149px;
    :deep(.trapezium-row-inner){
      background: linear-gradient(to right, #bacfff, #e4ebfa);
      .tra-column{
        background: #e7edfc;
      }
    }
  }

  .trapezium-third {
    margin-top: 12px;
    border-bottom: 100px solid #d2dcf3;
    padding: 0 1px 0 262px;
    :deep(.trapezium-row-inner){
      background: linear-gradient(to right, #d2dcf3, #e3ebfc);
      .tf-right{
        background: #f0f4fc;
      }
      .tra-column{
        background: #eef3fb;
      }
    }
  }
  .tra-role{
    color: #7d7f85;
    width: 100%;
    overflow: hidden;
    padding: 0 10px;
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
  }
}
</style>