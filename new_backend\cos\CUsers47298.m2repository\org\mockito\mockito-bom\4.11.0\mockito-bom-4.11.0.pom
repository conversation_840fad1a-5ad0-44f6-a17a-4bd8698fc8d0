<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.mockito</groupId>
  <artifactId>mockito-bom</artifactId>
  <version>4.11.0</version>
  <packaging>pom</packaging>
  <name>mockito-bom</name>
  <description>Mockito Bill of Materials (BOM)</description>
  <url>https://github.com/mockito/mockito</url>
  <licenses>
    <license>
      <name>The MIT License</name>
      <url>https://github.com/mockito/mockito/blob/main/LICENSE</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mockitoguy</id>
      <name><PERSON><PERSON><PERSON><PERSON><PERSON></name>
      <url>https://github.com/mockitoguy</url>
      <roles>
        <role>Core developer</role>
      </roles>
    </developer>
    <developer>
      <id>bric3</id>
      <name>Brice Dutheil</name>
      <url>https://github.com/bric3</url>
      <roles>
        <role>Core developer</role>
      </roles>
    </developer>
    <developer>
      <id>raphw</id>
      <name>Rafael Winterhalter</name>
      <url>https://github.com/raphw</url>
      <roles>
        <role>Core developer</role>
      </roles>
    </developer>
    <developer>
      <id>TimvdLippe</id>
      <name>Tim van der Lippe</name>
      <url>https://github.com/TimvdLippe</url>
      <roles>
        <role>Core developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/mockito/mockito.git</url>
  </scm>
  <issueManagement>
    <system>GitHub issues</system>
    <url>https://github.com/mockito/mockito/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GH Actions</system>
    <url>https://github.com/mockito/mockito/actions</url>
  </ciManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-android</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-errorprone</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-proxy</artifactId>
        <version>4.11.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-subclass</artifactId>
        <version>4.11.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
