package com.chinasie.orion.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.IncomePlanDataRateDTO;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * IncomePlanDataRate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18 14:28:32
 */
@RestController
@RequestMapping("/incomePlanDataRate")
@Api(tags = "多项目税率维护保存")
public class IncomePlanDataRateController {

    @Autowired
    private IncomePlanDataMapper incomePlanDataMapper;

    @Autowired
    private IncomeProvisionInformationMapper incomeProvisionInformationMapper;

    @Autowired
    private BillingAccountInformationMapper billingAccountInformationMapper;

    @Autowired
    private AdvancePaymentInformationMapper advancePaymentInformationMapper;

    @Autowired
    private ProjectService projectService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据", type = "多项目税率维护保存", subType = "详情", bizNo = "{{#id}}")

    public ResponseDTO<IncomePlanDataRateVO> detail(@PathVariable(value = "id") String id,
                                                    @RequestParam(required = false)String pageCode) throws Exception {
        IncomePlanDataRateVO incomenPlanDataRate = new IncomePlanDataRateVO();
        // IncomePlanData incomePlanData = incomePlanDataMapper.selectById(id);

        LambdaQueryWrapperX<IncomeProvisionInformation> provisionCondition = new LambdaQueryWrapperX<>( IncomeProvisionInformation. class);
        provisionCondition.eq(IncomeProvisionInformation::getIncomePlanDataId, id);
        List<IncomeProvisionInformation> incomeProvisionInformationList = incomeProvisionInformationMapper.selectList(provisionCondition);

        LambdaQueryWrapperX<BillingAccountInformation> billingCondition = new LambdaQueryWrapperX<>( BillingAccountInformation. class);
        billingCondition.eq(BillingAccountInformation::getIncomePlanDataId, id);

        List<BillingAccountInformation> billingAccountInformationList = billingAccountInformationMapper.selectList(billingCondition);
        List<BillingAccountInformationVO> billingAccountInformationVOList = BeanCopyUtils.convertListTo(billingAccountInformationList, BillingAccountInformationVO::new);


        List<String> projectIds = billingAccountInformationList.stream().map(BillingAccountInformation::getProjectId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(projectIds)) {
          List<Project> projectList =  projectService.listByIds(projectIds);
          Map<String,String> projectMap = projectList.stream().collect(Collectors.toMap(Project::getId,Project::getName));
            billingAccountInformationVOList.forEach(ba ->{
                if(StrUtil.isNotBlank(ba.getProjectId())){
                    ba.setProjectName(projectMap.get(ba.getProjectId()));
                }
            });
        }
        LambdaQueryWrapperX<AdvancePaymentInformation> advanceCondition = new LambdaQueryWrapperX<>( AdvancePaymentInformation. class);
        advanceCondition.eq(AdvancePaymentInformation::getIncomePlanDataId, id);
        List<AdvancePaymentInformation> advancePaymentInformationList = advancePaymentInformationMapper.selectList(advanceCondition);

        // incomenPlanDataRate.setIncomePlanDataRate(incomePlanData);
        incomenPlanDataRate.setIncomeProvisionInformation(incomeProvisionInformationList);
        incomenPlanDataRate.setBillingAccountInformation(billingAccountInformationVOList);
        incomenPlanDataRate.setAdvancePaymentInformation(advancePaymentInformationList);

        return new ResponseDTO<>(incomenPlanDataRate);
    }
}
