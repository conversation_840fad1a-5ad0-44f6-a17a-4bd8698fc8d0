package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.RequireInfoDTO;
import com.chinasie.orion.domain.entity.RequireInfo;
import com.chinasie.orion.domain.vo.RequireInfoTotalVO;
import com.chinasie.orion.domain.vo.RequireInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * RequireInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface RequireInfoService extends OrionBaseService<RequireInfo> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    RequireInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param requireInfoDTO
     */
    String create(RequireInfoDTO requireInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param requireInfoDTO
     */
    Boolean edit(RequireInfoDTO requireInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<RequireInfoVO> pages(String mainTableId, Page<RequireInfoDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合需求单信息
     * <p>
     * * @param code
     */
    List<RequireInfoVO> getByCode(RequireInfoDTO p) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(RequireInfoDTO requireInfoDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<RequireInfoVO> vos) throws Exception;

    RequireInfoTotalVO getRequireInfoTotal(RequireInfoDTO requireInfoDTO) throws Exception;
}
