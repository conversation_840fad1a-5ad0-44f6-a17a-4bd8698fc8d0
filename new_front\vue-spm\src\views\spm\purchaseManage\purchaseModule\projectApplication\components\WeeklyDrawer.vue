<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import {
  onMounted, Ref, ref, computed,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import isoWeek from 'dayjs/plugin/isoWeek';
import { getCurrentWeek } from '../../utils';
dayjs.extend(isoWeek);
const props = defineProps<{
  record: Record<string, any> | null,
  isEdit: boolean,
  detailId: string,
}>();

const schemas = [
  {
    field: 'week',
    component: 'Select',
    label: '时间',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'number',
      },
    ],
    componentProps: {
      disabled: props.isEdit,
      options: computed(() => weekOptions.value),
      fieldNames: {
        label: 'name',
        value: 'value',
      },
      onChange: handleWeekChange,
    },
  },
  {
    field: 'isLastComplete',
    label: '上周工作是否安计划完成',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
  {
    field: 'lastWorkPlan',
    label: '上周工作安排',
    colProps: { span: 24 },
    componentProps: {
      disabled: true,
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'InputTextArea',
  },
  {
    field: 'lastWorkContent',
    label: '上周工作完成情况',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
  {
    field: 'nextWorkPlan',
    label: '下周工作安排',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },

  {
    field: 'techLeaderAttentionContent',
    label: '需技术部门领导关注内容',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'InputTextArea',
  },
  {
    field: 'busLeaderAttentionContent',
    label: '需商务部门领导关注内容',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [],
    },
    component: 'InputTextArea',
  },
  {
    field: 'isSign',
    label: '是否已完成合同签章',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
      onChange: (value) => {
        if (value === true) {
          Modal.confirm({
            title: '提示',
            content: '请及时整理合同归档文件',
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              setFieldsValue({ isSign: true });
            },
            onCancel: () => {
              setFieldsValue({ isSign: false });
            },
          });
          setFieldsValue({ isSign: false });
        } else {
          setFieldsValue({ isSign: false });
        }
      },
    },
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
  {
    field: 'isNextComplete',
    label: '预计下周是否能完成',
    colProps: { span: 12 },
    helpMessage: '当已耗时超过50时，预计下周是否能完成为必填项',
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: computed(() => {
      if (tableFormData.value?.usedTime > 20) {
        return [
          {
            required: true,
            trigger: 'change',
            type: 'boolean',
          },
        ];
      }
      return [];
    }),
    component: 'Select',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const loading: Ref<boolean> = ref(false);
const formData = ref<Record<string, any>>({});
const weekOptions: Ref<Record<any, any>[]> = ref([]);
const finishedWeeks = ref<number[]>([]);
const tableFormData = ref<Record<string, any>>({});
const purchReqDocCode = ref<string | undefined>('');
onMounted(async () => {
  // 1. 获取当前年和 purchReqDocCode

  props?.detailId && getFormData();

  if (props.isEdit) {
    getFormDataDetail();
  }
});
async function getFormDataDetail() {
  loading.value = true;
  try {
    const result = await new Api('/spm/purchProjectWeekly').fetch('', props?.record?.id, 'GET');
    formData.value = result;
    const usedTime = Number(result?.usedTime || '0');
    const isNextCompleteSchema = schemas.find((s) => s.field === 'isNextComplete');
    if (isNextCompleteSchema) {
      if (usedTime > 50) {
        isNextCompleteSchema.rules = [
          {
            required: true,
            trigger: 'change',
            type: 'boolean',
          },
        ];
      } else {
        isNextCompleteSchema.rules = [];
      }
    }

    setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/spm/ncfFormpurchaseRequest').fetch('', props?.detailId, 'GET');
    tableFormData.value = result;
    setFieldsValue({
      isSign: !!result?.isSign,
    });

    const now = dayjs();
    const currentYear = now.year();
    purchReqDocCode.value = tableFormData.value?.code;

    // 2. 查询已填报的周
    if (purchReqDocCode.value) {
      const params = {
        purchReqDocCode: purchReqDocCode.value,
        dataSource: '0',
        year: currentYear,
      };
      const result = await new Api('/spm/purchProjectWeekly/selectFinishWeek').fetch(params, '', 'POST');
      if (Array.isArray(result)) {
        finishedWeeks.value = result;
      }
    }

    // 3. 生成所有周选项，并标记已填报的周为禁用
    const allWeeks = getCurrentWeek(dayjs(), 'year').weeks;
    weekOptions.value = allWeeks.map((w) => ({
      ...w,
      disabled: finishedWeeks.value.includes(w.value),
    }));

    // 5. 设置默认周
    const currentWeek = now.isoWeek();
    if (!finishedWeeks.value.includes(currentWeek)) {
      setFieldsValue({
        week: currentWeek,
        year: currentYear,
      });
      handleWeekChange(currentWeek);
    } else if (!props.isEdit) {
      setFieldsValue({
        week: undefined,
        year: currentYear,
      });
    }
  } finally {
    loading.value = false;
  }
}

async function checkWeeklyExist(dateObj) {
  const year = dayjs(dateObj).year();
  const week = dayjs(dateObj).isoWeek();

  if (!purchReqDocCode.value) return;
  const params = {
    purchReqDocCode: purchReqDocCode.value,
    dataSource: '0',
    year,
    week,
  };
  const result = await new Api('/spm/purchProjectWeekly/checkCreate').fetch(params, '', 'POST');
  if (result?.exist) {
    message.warning('该周已存在周报，请勿重复创建');
  }
}

function getWeekRange(year: number, week: number) {
  // dayjs 的 isoWeek 设置周，isoWeekday(1)为周一，isoWeekday(7)为周日
  const monday = dayjs().year(year).isoWeek(week).isoWeekday(1)
    .format('YYYY-MM-DD');
  const sunday = dayjs().year(year).isoWeek(week).isoWeekday(7)
    .format('YYYY-MM-DD');
  return {
    weekBegin: monday,
    weekEnd: sunday,
  };
}

// week切换时查询上周工作安排并填充
async function handleWeekChange(week) {
  const year = dayjs().year(); // 你可以根据实际表单year字段获取

  if (!purchReqDocCode.value || !week) return;
  const params = {
    purchReqDocCode: purchReqDocCode.value,
    dataSource: '0',
    year,
    week,
  };
  const result = await new Api('/spm/purchProjectWeekly/selectLastWeekPlan').fetch(params, '', 'POST');

  if (result) {
    setFieldsValue({ lastWorkPlan: result });
  } else {
    setFieldsValue({ lastWorkPlan: '' });
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const year = formValues.year || dayjs().year();
    const week = formValues.week;
    const { weekBegin, weekEnd } = getWeekRange(year, week);

    const params = {
      purchReqDocCode: purchReqDocCode.value,
      ...formValues,
      weekBegin,
      weekEnd,
      year,
      dataSource: '0',
      id: props?.isEdit ? props?.record?.id : undefined,
    };

    return new Promise((resolve, reject) => {
      new Api('/spm/purchProjectWeekly').fetch(params, props?.isEdit ? 'edit' : 'add', props?.isEdit ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less"></style>
