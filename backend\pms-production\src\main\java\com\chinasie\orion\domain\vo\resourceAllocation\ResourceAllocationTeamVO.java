package com.chinasie.orion.domain.vo.resourceAllocation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "ResourceAllocationTeamVO对象", description = "大修参修班组信息")
@Data
public class ResourceAllocationTeamVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "组织名称")
    private String name;

    @ApiModelProperty(value = "组织编码")
    private String code;

    @ApiModelProperty(value = "组织链路")
    private String chainPath;

    @ApiModelProperty(value = "上级编码")
    private String parentId;

    @ApiModelProperty(value = "组织层级")
    private String level;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "组织类型")
    private String levelType;

    @ApiModelProperty(value = "重叠天数")
    private Integer overNumbers;

    @ApiModelProperty(value = "班组人员")
    private List<ResourceAllocationInfos> children;

}
