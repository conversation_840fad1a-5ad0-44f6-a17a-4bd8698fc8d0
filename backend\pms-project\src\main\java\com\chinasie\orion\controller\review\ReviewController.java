package com.chinasie.orion.controller.review;

import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.dto.review.ReviewBaseDTO;
import com.chinasie.orion.domain.dto.review.ReviewDTO;
import com.chinasie.orion.domain.dto.review.ReviewDeliveryDTO;
import com.chinasie.orion.domain.vo.review.IdAndNameVO;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.handler.status.ReviewChangeStatusReceiver;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.rabbitmq.client.Channel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * Review 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@RestController
@RequestMapping("/review")
@Api(tags = "项目评审")
public class ReviewController {

    @Autowired
    private ReviewService reviewService;
    @Autowired
    private ReviewChangeStatusReceiver receiver;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评审】数据详情【{{#number}}】", type = "Review", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ReviewVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ReviewVO rsp = reviewService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param reviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【项目评审】【{{#reviewDTO.name}}】", type = "Review", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Valid ReviewDTO reviewDTO) throws Exception {
        String rsp =  reviewService.create(reviewDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param reviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目评审】数据【{{#reviewDTO.name}}】", type = "Review", subType = "编辑", bizNo = "{{#reviewDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ReviewDTO reviewDTO) throws Exception {
        Boolean rsp = reviewService.edit(reviewDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【项目评审】数据", type = "Review", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = reviewService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目评审】数据", type = "Review", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评审】数据", type = "Review", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ReviewVO>> pages(@RequestBody Page<ReviewDTO> pageRequest) throws Exception {
        Page<ReviewVO> rsp =  reviewService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取项目专员
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取项目专员")
    @RequestMapping(value = "/manageUser/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【项目评审】项目专员", type = "Review", subType = "分页查询", bizNo = "")
    public ResponseDTO<IdAndNameVO> pages(@PathVariable("projectId")String projectId) throws Exception {
        IdAndNameVO rsp =  reviewService.manageUser(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 基础消息编辑
     *
     * @param reviewBaseDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "基础信息编辑")
    @RequestMapping(value = "/baseEdit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目评审】交付物基础信息", type = "Review", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> baseEdit(@RequestBody @Valid ReviewBaseDTO reviewBaseDTO) throws Exception {
        Boolean rsp = reviewService.baseEdit(reviewBaseDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 交付物信息编辑
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "交付物信息编辑")
    @RequestMapping(value = "/deliverableEdit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目评审】交付物信息", type = "Review", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> deliverableEdit(@RequestBody @Valid ReviewDeliveryDTO dto) throws Exception {
        Boolean rsp = reviewService.deliverableEdit(dto);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 交付物信息查询
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "交付物信息查询")
    @RequestMapping(value = "/deliverableQuery/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评审】交付物信息", type = "Review", subType = "查询", bizNo = "{{#id}}")
    public ResponseDTO<ReviewVO> deliverableQuery(@PathVariable("id")String id) throws Exception {
        ReviewVO rsp = reviewService.deliverableQuery(id);
        return new ResponseDTO<>(rsp);
    }

//    @RequestMapping(value = "/statusEdit", method = RequestMethod.POST)
//    @Transactional(rollbackFor = Exception.class)
//    public ResponseDTO<Boolean> statusEdit(@RequestBody ChangeStatusMessageDTO msg) throws Exception {
//        receiver.consumerCreateMessage(msg);
//        return new ResponseDTO<>(true);
//    }
}
