package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.TrainEquivalentDTO;
import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.entity.TrainEquivalent;
import com.chinasie.orion.domain.vo.TrainEquivalentVO;
import com.chinasie.orion.domain.vo.train.PersonTrainEffectVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TrainEquivalentMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.PersonTrainInfoRecordService;
import com.chinasie.orion.service.TrainEquivalentService;
import com.chinasie.orion.service.TrainPersonService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * TrainEquivalent 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
@Service
@Slf4j
public class TrainEquivalentServiceImpl extends OrionBaseServiceImpl<TrainEquivalentMapper, TrainEquivalent> implements TrainEquivalentService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;

    private TrainPersonService trainPersonService;

    private BasicUserService basicUserService;

    private UserRedisHelper userRedisHelper;

    private PersonTrainInfoRecordService personTrainInfoRecordService;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private TrainEquivalentMapper trainEquivalentMapper;

    @Autowired
    private DataStatusNBO dataStatusNBO;


    @Autowired
    public void setPersonTrainInfoRecordService(PersonTrainInfoRecordService personTrainInfoRecordService) {
        this.personTrainInfoRecordService = personTrainInfoRecordService;
    }

    @Autowired
    public void setBasicUserService(BasicUserService basicUserService) {
        this.basicUserService = basicUserService;
    }

    @Autowired
    public void setUserRedisHelper(UserRedisHelper userRedisHelper) {
        this.userRedisHelper = userRedisHelper;
    }

    @Autowired
    public void setTrainPersonService(TrainPersonService trainPersonService) {
        this.trainPersonService = trainPersonService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TrainEquivalentVO detail(String id, String pageCode) throws Exception {
        TrainEquivalent trainEquivalent = this.getById(id);
        TrainEquivalentVO result = BeanCopyUtils.convertTo(trainEquivalent, TrainEquivalentVO::new);
        setEveryName(Collections.singletonList(result));

        List<FileVO> existFileList = fileApiService.getFilesByDataId(id);
        result.setFileVOList(existFileList);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param trainEquivalentDTO
     */
    @Override
    public String create(TrainEquivalentDTO trainEquivalentDTO) throws Exception {
        TrainEquivalent trainEquivalent = BeanCopyUtils.convertTo(trainEquivalentDTO, TrainEquivalent::new);

        trainEquivalent.setUserCode(trainEquivalentDTO.getPersonNumber());


        // 14156 【培训等效】同一个人、同一个培训、同一个基地只能办理一次等效。这里可重复办理
//
//
//        if (this.count(new LambdaQueryWrapperX<>(TrainEquivalent.class)
//                .eq(TrainEquivalent::getUserCode, trainEquivalentDTO.getPersonNumber())
//                .eq(TrainEquivalent::getTrainNumber, trainEquivalentDTO.getTrainNumber())
//                .eq(TrainEquivalent::getBaseCode, trainEquivalentDTO.getBaseCode())
//        ) > 0) {
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "同一个人、同一个培训、同一个基地只能办理一次等效。");
//        }
        this.save(trainEquivalent);

        String rsp = trainEquivalent.getId();


        List<FileDTO> fileDTOList = trainEquivalentDTO.getFileDTOList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(rsp);
                item.setDataType("TrainEquivalent");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param trainEquivalentDTO
     */
    @Override
    public Boolean edit(TrainEquivalentDTO trainEquivalentDTO) throws Exception {
        TrainEquivalent trainEquivalent = BeanCopyUtils.convertTo(trainEquivalentDTO, TrainEquivalent::new);
//        if (this.count(new LambdaQueryWrapperX<>(TrainEquivalent.class)
//                .eq(TrainEquivalent::getUserCode, trainEquivalentDTO.getPersonNumber())
//                .eq(TrainEquivalent::getTrainNumber, trainEquivalentDTO.getTrainNumber())
//                .eq(TrainEquivalent::getBaseCode, trainEquivalentDTO.getBaseCode())
//                .ne(TrainEquivalent::getId,trainEquivalentDTO.getId())
//        ) > 0) {
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "同一个人、同一个培训、同一个基地只能办理一次等效。");
//        }
        this.updateById(trainEquivalent);

        String dataId = trainEquivalent.getId();

        //编辑附件
        List<FileDTO> fileDTOList = trainEquivalentDTO.getFileDTOList();
        List<FileVO> existFileList = fileApiService.getFilesByDataId(dataId);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            List<String> filesIds = existFileList.stream().map(FileVO::getId).collect(Collectors.toList());
            fileApiService.removeBatchByIds(filesIds);
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setId(null);
                item.setDataId(dataId);
                item.setDataType("TrainEquivalent");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainEquivalentVO> pages(Page<TrainEquivalentDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TrainEquivalent> condition = new LambdaQueryWrapperX<>(TrainEquivalent.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainEquivalent::getCreateTime);

        SimpleUserVO simpleUserVO= userBaseApiService.getUserById(CurrentUserHelper.getCurrentUserId());
        if(Objects.isNull(simpleUserVO)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "用户不存在或已删除");
        }
        condition.eq(TrainEquivalent::getUserCode,simpleUserVO.getCode());
        Page<TrainEquivalent> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainEquivalent::new));
        IPage<TrainEquivalent> mPage = MyBatisUtils.buildPage(realPageRequest);
        //PageResult<TrainEquivalent> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        IPage<TrainEquivalent> result = trainEquivalentMapper.selectDataPermissionPage(mPage, TrainEquivalent.class, condition);
        Page<TrainEquivalentVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        List<TrainEquivalentVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), TrainEquivalentVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<TrainEquivalentVO> vos) throws Exception {
        List<String> trainNumberList = new ArrayList<>();

        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(TrainEquivalent.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
        List<String> baseIdList = new ArrayList<>();
        List<String> basicCodeList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (TrainEquivalentVO vo : vos) {
            if(Objects.nonNull(vo.getStatus())){
                vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
            }
            trainNumberList.add(vo.getTrainNumber());
            baseIdList.add(vo.getTrainCenterId());
            basicCodeList.add(vo.getUserCode());
            idList.add(vo.getId());
        }
        Map<String, PersonTrainInfoRecord> idToEntity = new HashMap<>();
        //todo 临时处理 ，因基础数据不完全
        Map<String, BasicUser> mapByNumberList = basicUserService.getMapByNumberList(basicCodeList);
        if (!CollectionUtils.isEmpty(baseIdList)) {
            List<PersonTrainInfoRecord> list = personTrainInfoRecordService.listByIds(baseIdList);
            if (!CollectionUtils.isEmpty(list)) {
                for (PersonTrainInfoRecord personTrainInfoRecord : list) {
                    idToEntity.put(personTrainInfoRecord.getId(), personTrainInfoRecord);
                }
            }
        }
        List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(idList);
        Map<String, List<FileVO>> idToListFile = new HashMap<>();
        if (CollectionUtils.isEmpty(fileVOList)) {
            idToListFile = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        }
        Map<String, List<FileVO>> finalIdToListFile = idToListFile;
        vos.forEach(vo -> {
            PersonTrainInfoRecord trainManage = idToEntity.getOrDefault(vo.getTrainCenterId(), new PersonTrainInfoRecord());
            vo.setTrainName(trainManage.getTrainName());
            vo.setBaseName(trainManage.getBaseName());
            vo.setLessonHour(trainManage.getLessonHour());
            vo.setTrainNumber(trainManage.getTrainNumber());

            vo.setExpireTime(trainManage.getExpireTime());
            vo.setEndDate(trainManage.getEndDate());
            vo.setTrainLecturer(trainManage.getTrainLecturer());
            BasicUser basicUser = mapByNumberList.getOrDefault(vo.getUserCode(), new BasicUser());
            vo.setFullName(basicUser.getFullName());
            vo.setNowPosition(basicUser.getNowPosition());
            vo.setFileVOList(finalIdToListFile.getOrDefault(vo.getId(), new ArrayList<>()));
        });
    }

    @Override
    public List<PersonTrainVO> trainCurrentPersonList(EquivalentParamDTO equivalentParamDTO) {
        UserVO user = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        List<PersonTrainVO> personTrainVOList = trainPersonService.trainCurrentPersonList(equivalentParamDTO);
        return personTrainVOList;
    }

    @Override
    public List<PersonTrainEffectVO> currentPersonEffectTrainList(EquivalentParamDTO equivalentParamDTO) {
        UserVO user = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        if (null == user) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "用户未登录，请重新登录");
        }
        String userCode = user.getCode();
        String basePlaceCode = equivalentParamDTO.getBasePlaceCode();

        List<PersonTrainInfoRecord> personTrainInfos = personTrainInfoRecordService.listByUserCodeAndBaseCode(userCode, basePlaceCode, equivalentParamDTO.getKeyWord());

        List<PersonTrainEffectVO> personTrainEffectVOList = new ArrayList<>();
        for (PersonTrainInfoRecord personTrainInfo : personTrainInfos) {
            PersonTrainEffectVO personTrainEffectVO = new PersonTrainEffectVO();
            personTrainEffectVO.setId(personTrainInfo.getId());
            personTrainEffectVO.setTrainName(personTrainInfo.getTrainName());
            personTrainEffectVO.setTrainNumber(personTrainInfo.getTrainNumber());
            personTrainEffectVO.setTrainCenterId(personTrainInfo.getId());
            personTrainEffectVO.setBaseName(personTrainInfo.getBaseName());
            personTrainEffectVO.setEndDate(personTrainInfo.getEndDate());
            personTrainEffectVO.setExpireTime(personTrainInfo.getExpireTime());
            personTrainEffectVO.setLessonHour(personTrainInfo.getLessonHour());
            personTrainEffectVO.setBaseCode(personTrainInfo.getBaseCode());
            personTrainEffectVOList.add(personTrainEffectVO);
        }
        return personTrainEffectVOList;
    }

    @Override
    public List<TrainEquivalentVO> currentEquivalentList(EquivalentParamDTO equivalentParamDTO) throws Exception {

        UserVO user = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        LambdaQueryWrapperX<TrainEquivalent> wrapperX = new LambdaQueryWrapperX<>(TrainEquivalent.class);
        wrapperX.eq(TrainEquivalent::getUserCode, user.getCode());
        List<TrainEquivalent> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<TrainEquivalentVO> equivalentVOList = BeanCopyUtils.convertListTo(list, TrainEquivalentVO::new);
        setEveryName(equivalentVOList);
        return equivalentVOList;
    }

    @Override
    public Map<String, List<TrainEquivalentVO>> listSimpleByIds(List<String> centerIdList) throws Exception {
        if (CollectionUtils.isEmpty(centerIdList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapperX<TrainEquivalent> wrapperX = new LambdaQueryWrapperX<>(TrainEquivalent.class);
        wrapperX.in(TrainEquivalent::getTrainCenterId, centerIdList);
        List<TrainEquivalent> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(list.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList()));
        Map<String, List<FileVO>> idToList = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        List<TrainEquivalentVO> equivalentVOList = BeanCopyUtils.convertListTo(list, TrainEquivalentVO::new);
        equivalentVOList.forEach(item -> {
            item.setFileVOList(idToList.getOrDefault(item.getId(), new ArrayList<>()));
        });
        return equivalentVOList.stream().collect(Collectors.groupingBy(TrainEquivalentVO::getTrainCenterId));
    }


}
