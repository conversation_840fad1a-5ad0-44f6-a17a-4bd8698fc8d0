package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.entity.MarketContractCustContact;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.reflect.Parameter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * MarketContract DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@ApiModel(value = "MarketContractDTO对象", description = "市场合同")
@Data
@ExcelIgnoreUnannotated
public class MarketContractExcelExportDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;


    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopic;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String techRspDept;

    @ApiModelProperty(value = "承担部门名称")
    @ExcelProperty(value = "承担部门 ", index = 1)
    private String techRspDeptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 3)
    private String number;


    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String custPersonId;


    @ApiModelProperty(value = "客户编码")
    @ExcelProperty(value = "客户编码 ", index = 4)
    private String cusNumber;


    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称 ", index = 5)
    private String cusName;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 7)
    private String name;


    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    @ExcelProperty(value = "销售业务分类 ", index = 8)
    private String custSaleBusType;

    @ApiModelProperty(value = "客户-销售业务名称")
    private String custSaleBusName;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @ExcelProperty(value = "技术接口人 ", index = 9)
    private String techRspUser;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 10)
    private String currencyName;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @ExcelProperty(value = "合同金额 ", index = 11)
    private BigDecimal contractAmt;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "合同开始时间")
    @ExcelProperty(value = "合同开始时间 ", index = 12)
    private Date effectDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "合同结束时间")
    @ExcelProperty(value = "合同结束时间", index = 13)
    private Date completeDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "流程发起日期")
    @ExcelProperty(value = "流程发起日期", index = 14)
    private Date flowStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "流程结束日期")
    @ExcelProperty(value = "流程结束日期", index = 15)
    private Date flowEndTime;

    @ApiModelProperty(value = "是否关联交易")
    private Boolean relTransAppr;

    @ApiModelProperty(value = "是否关联交易")
    @ExcelProperty(value = "是否关联交易", index = 16)
    private String relTransApprName;


    /**
     * 报价单id
     */
    @ApiModelProperty(value = "报价单id")
    private String quoteId;


    @ApiModelProperty(value = "项目报价审批流程")
    @ExcelProperty(value = "项目报价审批流程", index = 17)
    private String quote;


    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型", index = 18)
    private String businessTypeName;


    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "交易审批id")
    private String transApprId;

    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "关联交易报审流程")
    @ExcelProperty(value = "关联交易报审流程 ", index = 19)
    private String transApprNumber;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型", index = 20)
    private String contractTypeName;


    @ApiModelProperty("流程状态")
    private Integer status;

    @ApiModelProperty("流程状态")
    @ExcelProperty(value = "流程状态 ", index = 21)
    private String statusName;

    @ApiModelProperty("流程发起人")
    @ExcelProperty(value = "发起人姓名 ", index = 22)
    private String flowCreatePersonName;

    @ApiModelProperty("流程发起人工号")
    @ExcelProperty(value = "发起人工号 ", index = 23)
    private String flowCreatePersonNumber;


    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求Id")
    private String requirementId;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求名称")
    @ExcelProperty(value = "来源需求名称 ", index = 24)
    private String requirementName;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求编码")
    @ExcelProperty(value = "来源需求编号 ", index = 25)
    private String requirementNumber;


    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    @ExcelProperty(value = "报价单名称 ", index = 26)
    private String quoteName;


    /**
     * 报价编号
     */
    /**
     * 报价编号
     */
    @ApiModelProperty(value = "报价单编号")
    @ExcelProperty(value = "来源报价单编号 ", index = 27)
    private String quoteNumber;


    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同id")
    private String frameContractId;

    /**
     * 关联框架合同名称
     */
    @ApiModelProperty(value = "关联框架合同名称")
    @ExcelProperty(value = "关联框架合同名称 ", index = 28)
    private String frameContractName;

    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同编码")
    @ExcelProperty(value = "关联框架合同编码 ", index = 29)
    private String frameContractNumber;
    /**
     * 合同签署时间
     */
    @ApiModelProperty(value = "合同签署时间")
    @ExcelProperty(value = "合同签署时间 ", index = 30)
    private Date signDate;

    /**
     * 所级
     */
    @ApiModelProperty(value = "所级")
    @ExcelProperty(value = "所级 ", index = 2)
    private String deptName;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @ExcelProperty(value = "客户范围 ", index = 6)
    private String busScope;
    /**
     * 合同获取方式
     */
    @ApiModelProperty(value = "合同获取方式")
    @ExcelProperty(value = "合同获取方式 ", index = 31)
    private String contractMethod;

    /**
     * 客户联系人1
     */
    @ApiModelProperty(value = "客户联系人1")
    @ExcelProperty(value = "客户联系人1 ", index = 32)
    private String custContactId1;

    /**
     * 客户联系人1手机号
     */
    @ApiModelProperty(value = "客户联系人1手机号")
    @ExcelProperty(value = "客户联系人1手机号 ", index = 33)
    private String custContactId1Phone;

    /**
     * 客户联系人1联系人角色
     */
    @ApiModelProperty(value = "客户联系人1联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    @ExcelProperty(value = "客户联系人1联系人角色 ", index = 34)
    private String custContactId1Type;

    /**
     * 客户联系人2
     */
    @ApiModelProperty(value = "客户联系人2")
    @ExcelProperty(value = "客户联系人2 ", index = 35)
    private String custContactId2;

    /**
     * 客户联系人2手机号
     */
    @ApiModelProperty(value = "客户联系人2手机号")
    @ExcelProperty(value = "客户联系人2手机号 ", index = 36)
    private String custContactId2Phone;

    /**
     * 客户联系人2联系人角色
     */
    @ApiModelProperty(value = "客户联系人2联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    @ExcelProperty(value = "客户联系人2联系人角色 ", index = 37)
    private String custContactId2Type;

    /**
     * 客户联系人3
     */
    @ApiModelProperty(value = "客户联系人3")
    @ExcelProperty(value = "客户联系人3", index = 38)
    private String custContactId3;

    /**
     * 客户联系人3手机号
     */
    @ApiModelProperty(value = "客户联系人3手机号")
    @ExcelProperty(value = "客户联系人3手机号 ", index = 39)
    private String custContactId3Phone;

    /**
     * 客户联系人3联系人角色
     */
    @ApiModelProperty(value = "客户联系人3联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    @ExcelProperty(value = "客户联系人3联系人角色 ", index = 40)
    private String custContactId3Type;

    /**
     * 是否有需求
     */
    @ApiModelProperty(value = "是否有需求")
    @ExcelProperty(value = "是否有需求 ", index = 41)
    private String isHaveRequire;

    /**
     * 是否有报价
     */
    @ApiModelProperty(value = "是否有报价")
    @ExcelProperty(value = "是否有报价 ", index = 42)
    private String isHaveQuote;


    @ApiModelProperty(value = "合同签署人")
    @ExcelProperty(value = "合同签署人 ", index = 43)
    private String contractSignUserName;

    @ApiModelProperty(value = "框架下子订单类型")
    private String subOrderType;

    @ApiModelProperty(value = "客户-客户关系。编码")
    private String custGroupInOut;

    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "框架合同金额")
    private BigDecimal frameContractAmt;

    @ApiModelProperty(value = "需求业务类型")
    private String requireBusinessType;

}
