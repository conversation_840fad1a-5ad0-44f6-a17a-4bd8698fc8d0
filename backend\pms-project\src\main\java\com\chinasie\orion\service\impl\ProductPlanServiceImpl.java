package com.chinasie.orion.service.impl;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.ProductPlanDTO;
import com.chinasie.orion.domain.entity.ProductPlan;
import com.chinasie.orion.domain.vo.ProductPlanVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProductPlanMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProductPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * ProductPlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
@Service
@Slf4j
public class ProductPlanServiceImpl extends OrionBaseServiceImpl<ProductPlanMapper, ProductPlan> implements ProductPlanService {


    @Autowired
    private NumberApiService numberApiService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProductPlanVO detail(String id, String pageCode) throws Exception {
        ProductPlan productPlan =this.getById(id);
        ProductPlanVO result = BeanCopyUtils.convertTo(productPlan,ProductPlanVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param productPlanDTO
     */
    @Override
    public  String create(ProductPlanDTO productPlanDTO) throws Exception {
        ProductPlan productPlan =BeanCopyUtils.convertTo(productPlanDTO,ProductPlan::new);

        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(ProductPlan.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String generate = numberApiService.generate(generateNumberRequest);
        productPlan.setNumber(generate);

        this.save(productPlan);
        return productPlan.getId();
    }

    /**
     *  编辑
     *
     * * @param productPlanDTO
     */
    @Override
    public Boolean edit(ProductPlanDTO productPlanDTO) throws Exception {
        ProductPlan productPlan =BeanCopyUtils.convertTo(productPlanDTO,ProductPlan::new);

        this.updateById(productPlan);

        String rsp=productPlan.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProductPlanVO> pages( Page<ProductPlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProductPlan> condition = new LambdaQueryWrapperX<>( ProductPlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        ProductPlanDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotNull(query)){
             String projectApprovalId = query.getProjectApprovalId();
             if (StrUtil.isNotBlank(projectApprovalId)){
                 condition.eq(ProductPlan::getProjectApprovalId, projectApprovalId);
             }
        }
        condition.orderByDesc(ProductPlan::getCreateTime);


        Page<ProductPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProductPlan::new));

        PageResult<ProductPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProductPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProductPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProductPlanVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }



    @Override
    public void  setEveryName(List<ProductPlanVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<ProductPlanVO> getList( String approvalId) throws Exception {
        LambdaQueryWrapperX<ProductPlan> condition = new LambdaQueryWrapperX<>(ProductPlan.class);
        condition.eq(ProductPlan::getProjectApprovalId, approvalId);
        condition.orderByDesc(ProductPlan::getCreateTime);
        List<ProductPlan> list = this.list(condition);
        List<ProductPlanVO> vos = BeanCopyUtils.convertListTo(list, ProductPlanVO::new);
        return vos;
    }



}
