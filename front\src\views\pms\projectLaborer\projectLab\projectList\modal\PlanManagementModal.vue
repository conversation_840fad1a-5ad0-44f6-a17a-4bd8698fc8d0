<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="modalDetails checkDrawer"
    title="查看基本信息"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="modalDetails_main"
    >
      <div class="modalDetails_content">
        <div class="viewTitle">
          <div class="rowItem titleLabel">
            {{ formData.name }}
          </div>
          <div class="rowItem">
            <div class="rowCell">
              <div class="rowCell_icon icon_user">
                <i class="orion-icon-user" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ formData.creatorName }}
                </div>
                <div class="val_bot">
                  创建人
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-hourglass" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  <DataStatusTag :status-data="formData.dataStatus" />
                </div>
                <div class="val_bot">
                  状态
                </div>
              </div>
            </div>

            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div
                  class="val_top"
                  :class="{ red: formData.priorityLevelName === '最高' }"
                >
                  {{ formData.priorityLevelName }}
                </div>
                <div class="val_bot">
                  优先级
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ stampDate(formData.modifyTime) }}
                </div>
                <div class="val_bot">
                  修改时间
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BasicTabs
        v-model:tabsIndex="tabsIndex"
        :tabs="tabs"
        @tabsChange="tabsChange"
      />
      <div
        class="modalDetailsPage"
      >
        <Summarize
          v-if="actionId===2222221"
          pageType="modal"
          :formId="formId"
        />
        <FrontPlan
          v-if="actionId===2222222"
          pageType="modal"
          :formId="formId"
        />
        <Deliverable
          v-if="actionId===2222223"
          pageType="modal"
          :formId="formId"
          :projectId="formData.projectId"
        />
        <ManHour
          v-if="actionId===2222225"
          pageType="modal"
          :formId="formId"
        />
        <RelatedContent
          v-if="actionId===2222226"
          pageType="modal"
          :formId="formId"
          :projectId="formData.projectId"
        />
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, nextTick, provide, ref, readonly,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicTabs, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import Summarize from '../menuComponents/planManagement/planDetails/summarize/index.vue';
import FrontPlan from '../menuComponents/planManagement/planDetails/frontPlan/index.vue';
import Deliverable from '../menuComponents/planManagement/planDetails/deliverable/index.vue';
import ManHour from '../menuComponents/planManagement/planDetails/manHour/index.vue';
import RelatedContent from '../menuComponents/planManagement/planDetails/relatedContent/index.vue';
export default defineComponent({
  name: 'PlanManagementModal',
  components: {
    BasicDrawer,
    BasicTabs,
    DataStatusTag,
    Summarize,
    FrontPlan,
    Deliverable,
    ManHour,
    RelatedContent,
  },
  setup(props, { emit }) {
    const state:any = reactive({
      formData: {} as any,
      formId: '',
      showTabs: true,
      loading: false,
      tabsIndex: 0,
      actionId: '',
      provideProjectId: '',
      tabs: [
        {
          name: '概述',
          id: 2222221,
        },
        {
          name: '前置计划',
          id: 2222222,
        },
        {
          name: '交付物',
          id: 2222223,
        },
        {
          name: '工时',
          id: 2222225,
        },
        {
          name: '关联内容',
          id: 2222226,
        },
      ],
    });
    const [modalRegister, { closeDrawer, setDrawerProps, changeLoading }] = useDrawerInner((drawerData) => {
      state.formId = drawerData.id;
      getFormData(drawerData.id);
      state.provideProjectId = ref(state.formId);
    });

    // provide('provideProjectId', readonly(provideProjectId));
    function getFormData(id) {
      new Api(`/pms/plan/${id}`).fetch('', '', 'GET').then((data) => {
        state.formData = data;
        nextTick(() => {
          state.tabsIndex = 0;
          state.actionId = state.tabs[0].id;
        });
      });
    }
    const tabsChange = (index, item) => {
      state.actionId = item.id;
    };
    // 数据
    provide(
      'formData',
      computed(() => state.formData),
    );
    onMounted(() => {
    });

    const visibleChange = (val) => {
      if (!val) {
        state.actionId = '';
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };

    return {
      ...toRefs(state),
      modalRegister,
      stampDate,
      tabsChange,
      visibleChange,
    };
  },
});

</script>
<style lang="less">
.modalDetails{
  .ant-drawer-body{
    padding:0px;
    .scrollbar__view{
      height: 100%;
    }
  }
  .modalDetails_main{
    display: flex;
    height: 100%;
    flex-direction: column;
  }
  .modalDetails_content{
    padding: 10px;
    .fa {
      font-family: 'FontAwesome';
    }
    .viewTitle {
      * {
        font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
      }
      padding-bottom: 20px;
      border-bottom: 1px dashed #e4e4e7;
      .titleLabel {
        font-weight: 700;
        font-style: normal;
        font-size: 20px;
        color: #000000;
        height: 60px;
        line-height: 60px;
      }
      .rowItem {
        display: flex;
        .rowCell {
          display: flex;
          width: 250px;
          .rowCell_icon {
            height: 40px;
            width: 40px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 5px;
          }
          .icon_user {
            background: #6e72fb;
            color: #ffffff;
          }
          .icon_calendar {
            background: #f1f4fd;
            color: #5678dd;
          }
          .rowCell_val {
            .val_top {
              font-weight: 500;
              font-style: normal;
              font-size: 16px;
              height: 25px;
            }
            .val_bot {
              font-weight: 400;
              font-style: normal;
              font-size: 12px;
              color: #686f8b;
            }
          }
        }
      }
    }
  }
  .tabs-main{
    padding: 0px 10px;
  }
  .modalDetailsPage{
    flex: 1;
    display: flex;
  }
}

</style>
