import Api from '/@/api';

/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/pas/qualityStep/add').fetch(params, '', 'POST');

/**
 * 确定
 * @param params 参数
 */
export const affirm = (params) => new Api('/pas/qualityStep/affirm').fetch(params, '', 'POST');

/**
 * 生产编码
 */
export const creatCode = () => new Api('/pas/qualityStep/creatCode').fetch('', '', 'GET');

/**
 * 提交
 * @param params 参数
 */
export const commit = (params) => new Api('/pas/qualityStep/commit').fetch(params, '', 'POST');

/**
 * 质控措施导入下载模板(Excel)
 */
export const downloadExcelTpl = () => new Api('/pas/qualityStep/download/excel/tpl').fetch('', '', 'GET');

/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/pas/qualityStep/edit').fetch(params, '', 'PUT');

/**
 * 质控措施导出（Excel）
 * @param params 参数
 */
export const exportExcel = (params) => new Api('/pas/qualityStep/export/excel').fetch(params, '', 'POST');

/**
 * 取消质控措施导入（Excel）
 * @param importId 参数
 */
export const importExcelCancel = (importId) => new Api(`/pas/qualityStep/import/excel/cancel/${importId}`).fetch('', '', 'POST');

/**
 * 质控措施导入校验（Excel）
 * @param params 参数
 */
export const importExcelCheck = (params) => new Api('/pas/qualityStep/import/excel/check').fetch(params, '', 'POST');

/**
 * 质控措施导入（Excel）
 * @param importId 参数
 */
export const importExcel = (importId) => new Api(`/pas/qualityStep/import/excel/${importId}`).fetch('', '', 'POST');

/**
 * 分页查询
 * @param params 参数
 */
export const page = (params) => new Api('/pas/qualityStep/page').fetch(params, '', 'POST');

/**
 * 驳回
 * @param params 参数
 */
export const reject = (params) => new Api('/pas/qualityStep/reject').fetch(params, '', 'POST');

/**
 * 删除（批量）
 * @param params 参数
 */
export const remove = (params) => new Api('/pas/qualityStep/remove').fetch(params, '', 'DELETE');

/**
 * 详情
 * @param id 参数
 * @param pageCode 权限编码
 */
export const qualityStepGet = (id, pageCode = '') => new Api(`/pas/qualityStep/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');

/**
 * 删除（单个）
 * @param id 参数
 */
export const qualityStepDelete = (id) => new Api(`/pas/qualityStep/${id}`).fetch('', '', 'DELETE');
