<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>4</version>
  </parent>
  <groupId>org.sonatype.spice</groupId>
  <artifactId>spice-parent</artifactId>
  <version>12</version>
  <packaging>pom</packaging>
  <name>Sonatype Spice Components</name>

  <scm>
    <connection>scm:svn:http://svn.sonatype.org/spice/tags/spice-parent-12</connection>
    <url>http://svn.sonatype.org/spice/tags/spice-parent-12</url>
    <developerConnection>scm:svn:https://svn.sonatype.org/spice/tags/spice-parent-12</developerConnection>
  </scm>

  <licenses>
    <license>
      <name>Apache Public License 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  
	<ciManagement>
	  <system>Hudson</system>
	  <url>https://grid.sonatype.org/ci/view/Spice/</url>
	</ciManagement>
	
  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.sonatype.org/browse/SPICE</url>
  </issueManagement>
  
  <properties>
    <!--
      2008.12.07 Oleg: reverted to 6.1.12 as http transport only works with that version | Please always sync with
      mercury-http-transport !!
    -->
    <jetty.version>6.1.12</jetty.version>
    <plexus.version>1.0-beta-3.0.5</plexus.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>${plexus.version}</version>
        <scope>provided</scope>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>${plexus.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>1.5.5</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>jetty</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.5</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>${plexus.version}</version>
          <executions>
            <execution>
              <id>process-classes</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
            <execution>
              <id>process-test-classes</id>
              <goals>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-maven-plugin</artifactId>
          <version>1.3.8</version>
          <executions>
            <execution>
              <goals>
                <goal>descriptor</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  
	<reporting>
	  <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.2</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>1.2</version>
        <configuration>
          <omitVisitors>UnreadFields</omitVisitors>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <targetJdk>1.5</targetJdk>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>2.5</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <tagletArtifacts>
            <tagletArtifact>
              <groupId>org.apache.maven.plugin-tools</groupId>
              <artifactId>maven-plugin-tools-javadoc</artifactId>
              <version>2.5</version>
            </tagletArtifact>
            <tagletArtifact>
              <groupId>org.codehaus.plexus</groupId>
              <artifactId>plexus-javadoc</artifactId>
              <version>1.0</version>
            </tagletArtifact>
          </tagletArtifacts>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.1.1</version>
        <reportSets>
          <reportSet>
            <reports>
              <!-- Broken, don't know why. -->
              <report>dependencies</report>
              <report>project-team</report>
              <report>mailing-list</report>
              <report>cim</report>
              <report>issue-tracking</report>
              <report>license</report>
              <report>scm</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
	  </plugins>
	</reporting>
	
</project>