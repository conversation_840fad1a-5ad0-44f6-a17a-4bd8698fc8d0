package com.chinasie.orion.handler.status;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.MarketContractMilestoneEnum;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.constant.MarketContractStatusEnum;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.entity.ContractSupplierSignedSubject;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.constant.RequirementMscNodeEnum;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.ContractOurSignedSubjectService;
import com.chinasie.orion.service.ContractSupplierSignedSubjectService;
import com.chinasie.orion.service.MarketContractService;
import com.rabbitmq.client.Channel;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目采购订单状态变更
 */
@Component
public class MarketContractChangeStatusReceiver extends AbstractChangeStatusReceiver {

    private final Logger logger = LoggerFactory.getLogger(MarketContractChangeStatusReceiver.class);


    private static final String CURRENT_CLASS = "MarketContract";

    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private ContractSupplierSignedSubjectService contractSupplierSignedSubjectService;
    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Autowired
    protected PmsMQProducer mqProducer;

    @Autowired
    private ContractMilestoneService contractMilestoneService;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        logger.info("市场合同状态更改消息消费：{}", msg);
//        if (ObjectUtil.isNotEmpty(msg)) {
//            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
//            if (Objects.nonNull(classVO)) {
//                if (CURRENT_CLASS.equals(classVO.getClassName())) {
//                    msg.setClassName(classVO.getClassName());
//                    ThreadUtil.execAsync(() -> {
//                        try {
        try {
            consumerCreateMessage(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
//                        } catch (Exception e) {
//                            processError(msg, channel, message, e);
//                        }
//                    });
//                }
//            }
//        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        logger.error("项市场合同订单状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */

    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        //项目采购订单 状态变更
        Integer status = message.getStatus();
        String businessId = message.getBusinessId();
        MarketContract marketContract = marketContractService.getById(businessId);
        marketContract.setStatus(status);
        if (Objects.equals(MarketContractStatusEnum.AUDITING.getStatus(), status)) {
            //合同状态从编制中变成审核中时 合同下的约定里程碑的状态也要从编制中变成审核中
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, businessId);
            //contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getAmmountType, MarketContractMilestoneEnum.MILESTONEAMT.getCode());
            List<ContractMilestone> contractMilestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(contractMilestones)) {
                //获取所有的父里程碑
                List<ContractMilestone> parent = contractMilestones.stream().filter(obj -> Objects.isNull(obj.getParentId())).collect(Collectors.toList());
                List<ContractMilestone> child = contractMilestones.stream().filter(obj -> !Objects.isNull(obj.getParentId())).collect(Collectors.toList());
                Map<String, List<ContractMilestone>> childMap = child.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
                //遍历所有的里程碑
                Integer sequenceNumber = findMax(parent);//获取当前合同所有的父里程碑序号的最大值
                parent.sort(Comparator.comparing(ContractMilestone::getCreateTime));//把父里程碑按照创建时间排序
                ContractMilestone contractMilestone1 = parent.get(0);
                String number = marketContract.getNumber();
                for (ContractMilestone contractMilestone : contractMilestones) {
                    Integer milestoneStatus = contractMilestone.getStatus();
                    if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.CREATED.getStatus())) {
                        contractMilestone.setStatus(MarketContractMilestoneStatusEnum.APPROVAL.getStatus());
                        if (ObjectUtil.isEmpty(sequenceNumber)) {
                            contractMilestone1.setSequenceNumber(1);
                        }
                    }
                }

                //设置父里程碑序号
                for (ContractMilestone contractMilestone : parent) {
                    if (!contractMilestone.getId().equals(contractMilestone1.getId())) {
                        sequenceNumber = findMax(parent);
                        contractMilestone.setSequenceNumber(sequenceNumber + 1);
                    }
                }
                //设置里程碑的编码
                for (ContractMilestone contractMilestone : parent) {
                    StringBuffer milestoneNumber = new StringBuffer();
                    if (ObjectUtil.isNotEmpty(number)) {
                        String substring = number.substring(9);
                        milestoneNumber.append(substring).append("-").append(contractMilestone.getSequenceNumber());
                        String milestoneNumberStr = milestoneNumber.toString();
                        contractMilestone.setNumber(milestoneNumberStr);
                    }
                    List<ContractMilestone> milestones = childMap.get(contractMilestone.getId());
                    if (ObjectUtil.isNotEmpty(milestones)) {
                        milestones.sort(Comparator.comparing(ContractMilestone::getCreateTime));//把子里程碑按照创建时间排序
                        for (ContractMilestone milestone : milestones) {
                            sequenceNumber = findMax(milestones);
                            if (ObjectUtil.isEmpty(sequenceNumber)) {
                                ContractMilestone contractMilestoneSon1 = milestones.get(0);
                                contractMilestoneSon1.setSequenceNumber(1);
                            } else {
                                sequenceNumber = findMax(milestones);
                                milestone.setSequenceNumber(sequenceNumber + 1);
                            }
                        }

                        for (ContractMilestone milestone : milestones) {
                            StringBuffer stringBuffer = new StringBuffer();
                            stringBuffer.append(contractMilestone.getNumber()).append("-").append(milestone.getSequenceNumber());
                            String string = stringBuffer.toString();
                            milestone.setNumber(string);
                        }
                    }

                }


                contractMilestoneService.updateBatchById(contractMilestones);
            }
            //合同状态变成履行中或待签署时 合同下的约定里程碑的状态也要从审批变成进行中

            // 如果是审核中的时候，重新生成合同的编码。主要是为了防止在这个过程中，合同有变更
            final LambdaQueryWrapper<ContractSupplierSignedSubject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ContractSupplierSignedSubject::getContractId, businessId);
            final List<ContractSupplierSignedSubject> supplierSignedList =
                    contractSupplierSignedSubjectService.list(queryWrapper);
//            if (CollectionUtils.isNotEmpty(supplierSignedList)) {
//                // 审批的时候不能报错，合同编码生成失败也不去处理
//                try {
//                    final String number = marketContractService.generateApplyNo(marketContract, supplierSignedList);
////                    final LambdaUpdateWrapper<MarketContract> contractUpdateWrapper = new LambdaUpdateWrapper<>();
////                    contractUpdateWrapper.eq(MarketContract::getId, businessId).set(MarketContract::getNumber, number);
////                    marketContractService.update(contractUpdateWrapper);
//                    marketContract.setNumber(number);
//                    final LambdaUpdateWrapper<ContractSupplierSignedSubject> supplierUpdateWrapper = new LambdaUpdateWrapper<>();
//                    supplierUpdateWrapper.set(ContractSupplierSignedSubject::getContractNumber, number)
//                            .eq(ContractSupplierSignedSubject::getContractId, businessId);
//                    contractSupplierSignedSubjectService.update(supplierUpdateWrapper);
//
//                    final LambdaUpdateWrapper<ContractOurSignedSubject> ourUpdateWrapper = new LambdaUpdateWrapper<>();
//                    ourUpdateWrapper.set(ContractOurSignedSubject::getContractNumber, number)
//                            .eq(ContractOurSignedSubject::getContractId, businessId);
//                    contractOurSignedSubjectService.update(ourUpdateWrapper);
//                } catch (Exception e) {
//                    logger.error("生成合同编号失败", e);
//                }
//            }
        } else if (Objects.equals(MarketContractStatusEnum.CREATED.getStatus(), status)) {
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX2 = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getContractId, businessId);
            contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getAmmountType, MarketContractMilestoneEnum.MILESTONEAMT.getCode());
            List<ContractMilestone> contractMilestones2 = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX2);
            if (ObjectUtil.isNotEmpty(contractMilestones2)) {
                for (ContractMilestone contractMilestone : contractMilestones2) {
                    Integer milestoneStatus = contractMilestone.getStatus();
                    if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.APPROVAL.getStatus())) {
                        contractMilestone.setStatus(MarketContractMilestoneStatusEnum.CREATED.getStatus());
                    }
                }
                contractMilestoneService.updateBatchById(contractMilestones2);
            }

        } else if (Objects.equals(MarketContractStatusEnum.FULFIL.getStatus(), status)) {
            String contractType = marketContract.getContractType();
            if (ObjectUtil.isNotEmpty(contractType) ) {
                //判断是不是子合同或框架子订单 如果是 判断下属子订单类型是不是框架 如果是框架 不执行里程碑状态变更
                boolean flag = true;
//                if (contractType.equals("sonContract") || contractType.equals("subOrderContract")) {
//                    String subOrderType = marketContract.getSubOrderType();
//                    if (subOrderType.equals("frame")) {
//                        flag = false;
//                    }
//                }
                //if (flag) {
                    LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX2 = new LambdaQueryWrapperX<>();
                    contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getContractId, businessId);
                    //contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getAmmountType, MarketContractMilestoneEnum.MILESTONEAMT.getCode());
                    List<ContractMilestone> contractMilestones2 = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX2);
                    if (ObjectUtil.isNotEmpty(contractMilestones2)) {
                        for (ContractMilestone contractMilestone : contractMilestones2) {
                            Integer milestoneStatus = contractMilestone.getStatus();
                            contractMilestone.setStatus(MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
                            if (ObjectUtil.isNotEmpty(contractMilestone.getPlanAcceptDate())) {
                                contractMilestone.setPlannedAcceptanceDate(contractMilestone.getPlanAcceptDate());
                            } else if (ObjectUtil.isNotEmpty(contractMilestone.getExpectAcceptDate())) {
                                contractMilestone.setPlannedAcceptanceDate(contractMilestone.getExpectAcceptDate());
                            }

                            if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt()) && !(contractMilestone.getMilestoneAmt().compareTo(BigDecimal.ZERO) == 0)) {
                                contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getMilestoneAmt());
                            } else if (ObjectUtil.isNotEmpty(contractMilestone.getExceptAcceptanceAmt()) && !(contractMilestone.getExceptAcceptanceAmt().compareTo(BigDecimal.ZERO) == 0)) {
                                contractMilestone.setPlannedAcceptanceAmount(contractMilestone.getExceptAcceptanceAmt());
                            }
                            if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.APPROVAL.getStatus())&& StrUtil.equals(contractMilestone.getAmmountType(),MarketContractMilestoneEnum.MILESTONEAMT.getCode())) {
                                contractMilestone.setExceptAcceptanceAmt(contractMilestone.getMilestoneAmt());
                                contractMilestone.setExpectAcceptDate(contractMilestone.getPlanAcceptDate());
                            }
                        }
                        contractMilestoneService.updateBatchById(contractMilestones2);
                    }
                //}
            }

        } else if (121 == status) {

//            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX2 = new LambdaQueryWrapperX<>();
//            contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getContractId, businessId);
//            contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getAmmountType, MarketContractMilestoneEnum.MILESTONEAMT.getCode());
//            List<ContractMilestone> contractMilestones2 = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX2);
//            if (ObjectUtil.isNotEmpty(contractMilestones2)) {
//                for (ContractMilestone contractMilestone : contractMilestones2) {
//                    Integer milestoneStatus = contractMilestone.getStatus();
//                    if (milestoneStatus.equals(MarketContractMilestoneStatusEnum.APPROVAL.getStatus())) {
//                        contractMilestone.setStatus(MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
//                        contractMilestone.setExceptAcceptanceAmt(contractMilestone.getMilestoneAmt());
//                        contractMilestone.setExpectAcceptDate(contractMilestone.getPlanAcceptDate());
//
//                    }
//                }
//                contractMilestoneService.updateBatchById(contractMilestones2);
//            }
            // 发送消息通知
            // 编制中 -> 待签署
            sendMessage(message.getBusinessId(),
                    "/pas/contract-mangeDetail?id=" + message.getBusinessId() + "&htNum=" + marketContract.getNumber() + "&query=" + new Date().getTime(),
                    marketContract.getName() + "，审批完成，等待返回签署结果；",
                    Arrays.asList(marketContract.getCommerceRspUser(), marketContract.getTechRspUser()),
                    marketContract.getPlatformId(),
                    marketContract.getOrgId(),
                    RequirementMscNodeEnum.NODE_CONTRACT_DQS.getCode(),
                    marketContract.getCreatorId()
            );
        }
        boolean result = marketContractService.updateById(marketContract);

        logger.info("市场合同状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }



    private Integer findMax(List<ContractMilestone> contractMilestones) {
        Optional<Integer> maxOpt = contractMilestones.stream().map(ContractMilestone::getSequenceNumber).filter(Objects::nonNull).max(Integer::compareTo);
        return maxOpt.orElse(null);
    }

    /**
     * 发送消息通知
     */
    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String platformId, String orgId, String code, String userId) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$desc$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(userId)
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

}
