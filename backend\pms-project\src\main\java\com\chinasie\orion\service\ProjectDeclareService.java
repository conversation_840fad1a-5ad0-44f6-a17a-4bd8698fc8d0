package com.chinasie.orion.service;



import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectDeclareDTO;
import com.chinasie.orion.domain.entity.ProjectDeclare;
import com.chinasie.orion.domain.vo.ProjectDeclareVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectDeclare 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
public interface ProjectDeclareService  extends OrionBaseService<ProjectDeclare> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectDeclareVO detail(String id, String pageCode)  throws Exception;

    /**
     *  详情
     *
     * * @param id
     */
    ProjectDeclareVO getByProjectId(String projectId)  throws Exception;

    /**
     *  新增
     *
     * * @param projectDeclareDTO
     */
    ProjectDeclareVO create(ProjectDeclareDTO projectDeclareDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectDeclareDTO
     */
    ProjectDeclareVO edit(ProjectDeclareDTO projectDeclareDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectDeclareVO> pages(Page<ProjectDeclareDTO> pageRequest) throws Exception;

    /**
     * 待发起申报项目分页
     * @return
     * @throws Exception
     */
    Page<ProjectVO> projectPages(Page<ProjectDTO> pageRequest) throws Exception;


    Page<ProjectDeclareVO> userPages(Page<ProjectDeclareDTO> pageRequest) throws Exception;
}
