<template>
  <OrionTable
    ref="tableRef"
    :isSpacing="false"
    :isTableHeader="false"
    :options="tableOptions"
  />
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, h, defineProps,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';

const props = defineProps({
  userId: {
    type: String,
    default: '',
  },
});
const addOrEditRef = ref(null);
const state = reactive({});
const tableOptions = reactive({
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  isTreeTable: false,
  api: (params) => new Api(`/pms/projectScheme/userId/Pages/${props.userId}`).fetch(params, '', 'POST').then((res) => {
    if (res?.content) {
      res.content.forEach((item) => {
        delete item.children;
      });
    }
    return res;
  }),
  columns: [
    {
      title: '所在项目',
      dataIndex: 'projectName',
      width: 250,
    },
    {
      title: '负责/参与计划',
      dataIndex: 'name',
    },
    {
      title: '开始时间',
      dataIndex: 'beginTime',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
    },
    {
      title: '累计填报',
      dataIndex: 'reportingDays',
    },
  ],
});

</script>
