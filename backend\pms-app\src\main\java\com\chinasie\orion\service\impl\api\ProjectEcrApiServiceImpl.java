package com.chinasie.orion.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.EcrStatusChangeEnum;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.EcrExecuteDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatement;
import com.chinasie.orion.domain.entity.reporting.ProjectWeekly;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.vo.EcrExecuteVO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.quality.QualityItemService;
import com.chinasie.orion.service.reporting.ProjectDailyStatementService;
import com.chinasie.orion.service.reporting.ProjectWeeklyService;
import com.chinasie.orion.service.review.ReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinasie.orion.conts.MsgBusinessTypeEnum.*;

/**
 * @author: lsy
 * @date: 2024/6/12
 * @description:
 */
@RestController
public class ProjectEcrApiServiceImpl implements ProjectEcrApiService {

    @Autowired
    private MessageCenterApi messageCenterApi;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ProjectSchemeService projectSchemeService;
    @Autowired
    private YearInvestmentSchemeService yearInvestmentSchemeService;
    @Autowired
    private YearInvestmentSchemeMonthFeedbackService yearInvestmentSchemeMonthFeedbackService;
    @Autowired
    private GoodsServicePlanService goodsServicePlanService;
    @Autowired
    private RiskManagementService riskManagementService;
    @Autowired
    private QuestionManagementService questionManagementService;
    @Autowired
    private QualityItemService qualityItemService;
    @Autowired
    private DeliverableService deliverableService;
    @Autowired
    private InterfaceManagementService interfaceManagementService;
    @Autowired
    private BudgetExpendFormService budgetExpendFormService;
    @Autowired
    private BudgetApplicationFormService budgetApplicationFormService;
    @Autowired
    private BudgetAdjustmentFormService budgetAdjustmentFormService;
    @Autowired
    private ProjectPurchaseOrderInfoService projectPurchaseOrderInfoService;
    @Autowired
    private ProjectContractService projectContractService;
    @Autowired
    private GoodsServiceStoreService goodsServiceStoreService;
    @Autowired
    private ReviewService reviewService;
    @Autowired
    private ContractPayNodeConfirmService contractPayNodeConfirmService;
    @Autowired
    private WorkHourFillService workHourFillService;
    @Autowired
    private IdeaFormService ideaFormService;
    @Autowired
    private ProjectDailyStatementService projectDailyStatementService;
    @Autowired
    private ProjectWeeklyService projectWeeklyService;




    public Boolean projectStatusEcrExecuting(EcrExecuteDTO ecrExecuteDTO) throws Exception {
        // todo 项目状态变更执行暂时注释
//        String projectId = ecrExecuteDTO.getProjectId();
//        Project project = projectService.getOne(new LambdaQueryWrapperX<>(Project.class)
//                .select(Project::getId, Project::getStatus, Project::getLastStatus)
//                .eq(Project::getId, projectId));
//        if (ObjectUtil.isNotEmpty(project)) {
//            List<ProjectScheme> projectSchemeList = new ArrayList<>();
//            EcrExecuteVO variousDataTypesByProjectId = this.getVariousDataTypesByProjectId(ecrExecuteDTO, projectSchemeList);
//
//            String ecrStatus = ecrExecuteDTO.getEcrStatus();
//            project.setEcrTime(new Date());
//            if (ObjectUtil.equal(EcrStatusChangeEnum.STOP.getCode(), ecrStatus)) {
//                project.setLastStatus(project.getStatus());
//                project.setStatus(ProjectStatusEnum.PAUSED.getStatus());
//                //计划状态变更 非已完成 ->已暂停
//                List<ProjectScheme> projectSchemeList1 = projectSchemeList.stream().filter(f -> !Status.SUSPEND.getCode().equals(f.getStatus())
//                        && !Status.TERMINATION.getCode().equals(f.getStatus())).collect(Collectors.toList());
//                if (CollectionUtil.isNotEmpty(projectSchemeList1)) {
//                    projectSchemeList1.forEach(f -> {
//                        f.setLastStatus(f.getStatus());
//                        f.setStatus(Status.SUSPEND.getCode());
//                    });
//                    projectSchemeService.updateBatchById(projectSchemeList1);
//                }
//                // todo 调用流程接口批量挂起
//                List<String> dataIdsForWorkflow = variousDataTypesByProjectId.getDataIdsForWorkflow();
//
//            } else if (ObjectUtil.equal(EcrStatusChangeEnum.END.getCode(), ecrStatus)) {
//                project.setLastStatus(project.getStatus());
//                project.setStatus(ProjectStatusEnum.TERMINATED.getStatus());
//                //计划状态变更 非已完成 ->已终止
//                if (CollectionUtil.isNotEmpty(projectSchemeList)) {
//                    projectSchemeList.forEach(f -> {
//                        f.setStatus(Status.TERMINATION.getCode());
//                    });
//                    projectSchemeService.updateBatchById(projectSchemeList);
//                }
//
//                // todo 调用流程接口批量终止
//                variousDataTypesByProjectId.getDataIdsForWorkflow();
//                // 调用消息接口批量取消待办
//                List<String> dataIdsForMsc = variousDataTypesByProjectId.getDataIdsForMsc();
//                if (CollectionUtil.isNotEmpty(dataIdsForMsc)){
//                    messageCenterApi.todoMessageChangeStatusByBusinessIds(dataIdsForMsc);
//                }
//            } else {
//                project.setStatus(project.getLastStatus());
//                //计划状态恢复
//                int pausedCycle;
//                if (ecrExecuteDTO.getStopStartTime() != null) {
//                    pausedCycle = (int) DateUtil.between(ecrExecuteDTO.getStopStartTime(), new Date(), DateUnit.DAY);
//                } else {
//                    pausedCycle = 0;
//                }
//
//                List<ProjectScheme> projectSchemeList1 = projectSchemeList.stream()
//                        .filter(f -> Status.SUSPEND.getCode().equals(f.getStatus())).collect(Collectors.toList());
//                if (CollectionUtil.isNotEmpty(projectSchemeList1)) {
//                    projectSchemeList1.forEach(f -> {
//                        f.setStatus(f.getLastStatus());
//                        f.setPausedCycle(pausedCycle);
//                    });
//                    projectSchemeService.updateBatchById(projectSchemeList1);
//                }
//
//            }
//            projectService.updateById(project);
//            // todo 调用流程接口批量激活
//            variousDataTypesByProjectId.getDataIdsForWorkflow();
//
//        }
        return true;
    }

    public EcrExecuteVO getVariousDataTypesByProjectId (EcrExecuteDTO ecrExecuteDTO, List<ProjectScheme> projectSchemeList) throws Exception{
        String projectId = ecrExecuteDTO.getProjectId();

        List<String> dataIdsForWorkflow = new ArrayList<>();
        List<String> dataIdsForMsc = new ArrayList<>();

        // 年度投资计划
        List<YearInvestmentScheme> yearInvestmentList = yearInvestmentSchemeService.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class)
                .select(YearInvestmentScheme::getId)
                .eq(YearInvestmentScheme::getProjectId, projectId)
                .eq(YearInvestmentScheme::getStatus, 107)
                .or()
                .eq(YearInvestmentScheme::getStatus, 121));
        if (CollectionUtil.isNotEmpty(yearInvestmentList)){
            dataIdsForWorkflow.addAll(yearInvestmentList.stream().map(YearInvestmentScheme::getId).collect(Collectors.toList()));
        }

        // 月度投资计划反馈
        List<YearInvestmentSchemeMonthFeedback> yearInvestmentSchemeMonthFeedbackList = yearInvestmentSchemeMonthFeedbackService.list(new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class)
                .select(YearInvestmentSchemeMonthFeedback::getId)
                .eq(YearInvestmentScheme::getProjectId, projectId)
                .eq(YearInvestmentScheme::getStatus, 110));
        if (CollectionUtil.isNotEmpty(yearInvestmentSchemeMonthFeedbackList)){
            dataIdsForWorkflow.addAll(yearInvestmentSchemeMonthFeedbackList.stream().map(YearInvestmentSchemeMonthFeedback::getId).collect(Collectors.toList()));
        }

        // 物质计划
        List<GoodsServicePlan> goodsServicePlanList = goodsServicePlanService.list(new LambdaQueryWrapperX<>(GoodsServicePlan.class)
                .select(GoodsServicePlan::getId)
                .eq(GoodsServicePlan::getProjectId, projectId)
                .eq(GoodsServicePlan::getStatus, 110));
        if (CollectionUtil.isNotEmpty(goodsServicePlanList)){
            dataIdsForWorkflow.addAll(goodsServicePlanList.stream().map(GoodsServicePlan::getId).collect(Collectors.toList()));
        }

        // 风险管理
        List<RiskManagement> riskManagementList = riskManagementService.list(new LambdaQueryWrapperX<>(RiskManagement.class)
                .select(RiskManagement::getId)
                .eq(RiskManagement::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(riskManagementList)){
            dataIdsForWorkflow.addAll(riskManagementList.stream().filter(f -> ObjectUtil.equal(110, f.getStatus()) ||  ObjectUtil.equal(121, f.getStatus())).map(RiskManagement::getId).collect(Collectors.toList()));
            dataIdsForMsc.addAll(riskManagementList.stream().map(RiskManagement::getId).collect(Collectors.toList()));
        }

        // 问题管理
        List<QuestionManagement> questionManagementList = questionManagementService.list(new LambdaQueryWrapperX<>(QuestionManagement.class)
                .select(QuestionManagement::getId)
                .eq(QuestionManagement::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(questionManagementList)){
            dataIdsForWorkflow.addAll(questionManagementList.stream().filter(f -> ObjectUtil.equal(110, f.getStatus())).map(QuestionManagement::getId).collect(Collectors.toList()));
            dataIdsForMsc.addAll(questionManagementList.stream().map(QuestionManagement::getId).collect(Collectors.toList()));
        }

        // 质量管控项
        List<QualityItem> qualityItemList = qualityItemService.list(new LambdaQueryWrapperX<>(QualityItem.class)
                .select(QualityItem::getId)
                .eq(QualityItem::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(qualityItemList)){
            dataIdsForMsc.addAll(qualityItemList.stream().map(QualityItem::getId).collect(Collectors.toList()));
        }

        // 任务交付物
        List<Deliverable> deliverableList = deliverableService.list(new LambdaQueryWrapperX<>(Deliverable.class)
                .select(Deliverable::getId)
                .eq(Deliverable::getProjectId, projectId)
                .eq(Deliverable::getStatus, 110));
        if (CollectionUtil.isNotEmpty(deliverableList)){
            dataIdsForWorkflow.addAll(deliverableList.stream().map(Deliverable::getId).collect(Collectors.toList()));
        }

        // 协同接口
        List<InterfaceManagement> interfaceManagementList = interfaceManagementService.list(new LambdaQueryWrapperX<>(InterfaceManagement.class)
                .select(InterfaceManagement::getId, InterfaceManagement::getStatus)
                .eq(InterfaceManagement::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(interfaceManagementList)){
            dataIdsForWorkflow.addAll(interfaceManagementList.stream().filter(f -> ObjectUtil.equal(110, f.getStatus())).map(InterfaceManagement::getId).collect(Collectors.toList()));
            dataIdsForMsc.addAll(interfaceManagementList.stream().map(InterfaceManagement::getId).collect(Collectors.toList()));
        }

        // 预算支出表单
        List<BudgetExpendForm> budgetExpendFormList = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .select(BudgetExpendForm::getId)
                .eq(BudgetExpendForm::getProjectId, projectId)
                .eq(BudgetExpendForm::getStatus, 110));
        if (CollectionUtil.isNotEmpty(budgetExpendFormList)){
            dataIdsForWorkflow.addAll(budgetExpendFormList.stream().map(BudgetExpendForm::getId).collect(Collectors.toList()));
        }

        //预算申请
        List<BudgetApplicationForm> budgetApplicationFormList = budgetApplicationFormService.list(new LambdaQueryWrapperX<>(BudgetApplicationForm.class)
                .select(BudgetApplicationForm::getId)
                .eq(BudgetApplicationForm::getProjectId, projectId).eq(BudgetApplicationForm::getStatus, 110));
        if (CollectionUtil.isNotEmpty(budgetApplicationFormList)) {
            dataIdsForWorkflow.addAll(budgetApplicationFormList.stream().map(BudgetApplicationForm::getId).collect(Collectors.toList()));
        }
        //预算调整
        List<BudgetAdjustmentForm> budgetAdjustmentFormList = budgetAdjustmentFormService.list(new LambdaQueryWrapperX<>(BudgetAdjustmentForm.class)
                .select(BudgetAdjustmentForm::getId)
                .eq(BudgetAdjustmentForm::getProjectId, projectId).eq(BudgetAdjustmentForm::getStatus, 110));
        if (CollectionUtil.isNotEmpty(budgetAdjustmentFormList)) {
            dataIdsForWorkflow.addAll(budgetAdjustmentFormList.stream().map(BudgetAdjustmentForm::getId).collect(Collectors.toList()));
        }
        //采购订单
        List<ProjectPurchaseOrderInfo> projectPurchaseOrderInfoList = projectPurchaseOrderInfoService.list(new LambdaQueryWrapperX<>(ProjectPurchaseOrderInfo.class)
                .select(ProjectPurchaseOrderInfo::getId)
                .eq(ProjectPurchaseOrderInfo::getProjectId, projectId).eq(ProjectPurchaseOrderInfo::getStatus, 110));
        if (CollectionUtil.isNotEmpty(projectPurchaseOrderInfoList)) {
            dataIdsForWorkflow.addAll(projectPurchaseOrderInfoList.stream().map(ProjectPurchaseOrderInfo::getId).collect(Collectors.toList()));
        }
        //采购合同
        List<ProjectContract> projectContractList = projectContractService.list(new LambdaQueryWrapperX<>(ProjectContract.class)
                .select(ProjectContract::getId, ProjectContract::getStatus)
                .eq(ProjectContract::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(projectContractList)) {
            dataIdsForWorkflow.addAll(projectContractList.stream()
                    .filter(f -> ObjectUtil.equal(110, f.getStatus())).map(ProjectContract::getId).collect(Collectors.toList()));

            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmService.list(new LambdaQueryWrapperX<>(ContractPayNodeConfirm.class)
                    .select(ContractPayNodeConfirm::getId)
                    .in(ContractPayNodeConfirm::getContractId, projectContractList.stream().map(ProjectContract::getId).collect(Collectors.toList())));
            if (CollectionUtil.isNotEmpty(contractPayNodeConfirmList)) {
                dataIdsForMsc.addAll(contractPayNodeConfirmList.stream().map(ContractPayNodeConfirm::getId).collect(Collectors.toList()));
            }
        }


        //物资入库
        List<GoodsServiceStore> goodsServiceStoreList = goodsServiceStoreService.list(new LambdaQueryWrapperX<>(GoodsServiceStore.class)
                .select(GoodsServiceStore::getId)
                .eq(GoodsServiceStore::getProjectId, projectId).eq(GoodsServiceStore::getStatus, 130));
        if (CollectionUtil.isNotEmpty(goodsServiceStoreList)) {
            dataIdsForWorkflow.addAll(goodsServiceStoreList.stream().map(GoodsServiceStore::getId).collect(Collectors.toList()));
        }
        //评审管理
        List<Review> reviewList = reviewService.list(new LambdaQueryWrapperX<>(Review.class)
                .select(Review::getId)
                .eq(Review::getProjectId, projectId).eq(Review::getStatus, 130));
        if (CollectionUtil.isNotEmpty(reviewList)) {
            dataIdsForWorkflow.addAll(reviewList.stream().map(Review::getId).collect(Collectors.toList()));
        }

        //消息
        //工时填报
        List<WorkHourFill> workHourFillList = workHourFillService.list(new LambdaQueryWrapperX<>(WorkHourFill.class)
                .select(WorkHourFill::getId).eq(WorkHourFill::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(workHourFillList)) {
            dataIdsForMsc.addAll(workHourFillList.stream().map(WorkHourFill::getId).collect(Collectors.toList()));
        }
        //意见单
        List<IdeaForm> ideaFormList = ideaFormService.list(new LambdaQueryWrapperX<>(IdeaForm.class).select(IdeaForm::getId).eq(IdeaForm::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(ideaFormList)) {
            dataIdsForMsc.addAll(ideaFormList.stream().map(IdeaForm::getId).collect(Collectors.toList()));
        }
        //计划日报
        List<ProjectDailyStatement> projectDailyStatementList = projectDailyStatementService.list(new LambdaQueryWrapperX<>(ProjectDailyStatement.class).select(ProjectDailyStatement::getId).eq(ProjectDailyStatement::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(projectDailyStatementList)) {
            dataIdsForMsc.addAll(projectDailyStatementList.stream().map(ProjectDailyStatement::getId).collect(Collectors.toList()));
        }
        //项目周报表
        List<ProjectWeekly> projectWeeklyList = projectWeeklyService.list(new LambdaQueryWrapperX<>(ProjectWeekly.class).select(ProjectWeekly::getId).eq(ProjectWeekly::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(projectWeeklyList)) {
            dataIdsForMsc.addAll(projectWeeklyList.stream().map(ProjectWeekly::getId).collect(Collectors.toList()));
        }
        //项目计划
        projectSchemeList.addAll(projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                .select(ProjectScheme::getId, ProjectScheme::getStatus, ProjectScheme::getLastStatus, ProjectScheme::getIdentification)
                .eq(ProjectScheme::getProjectId, projectId)));
        if (CollectionUtil.isNotEmpty(projectSchemeList)) {
            dataIdsForMsc.addAll(projectSchemeList.stream().map(m -> CollUtil.toList(m.getId(), String.format("%s_%s", SEND_DOWN, m.getId()),
                    String.format("%s_%s", MODIFY_APPROVE, m.getId()), String.format("%s_%s", PRE_FINISH, m.getId()),
                    String.format("feedback_%s", m.getId()))).flatMap(Collection::stream).collect(Collectors.toList()));
           List<String> approvalStatusList = CollUtil.toList(Status.ISSUE_APPROVAL.getName(), Status.AFFIRM_APPROVAL.getName(), Status.SUSPEND_APPROVAL.getName(),
                   Status.TERMINATION_APPROVAL.getName(), Status.START_APPROVAL.getName());
           dataIdsForWorkflow.addAll(projectSchemeList.stream().filter(f -> approvalStatusList.contains(f.getIdentification()))
                   .map(ProjectScheme::getId).collect(Collectors.toList()));
        }

        EcrExecuteVO ecrExecuteVO = new EcrExecuteVO();
        ecrExecuteVO.setDataIdsForWorkflow(dataIdsForWorkflow);
        ecrExecuteVO.setDataIdsForMsc(dataIdsForMsc);
        return ecrExecuteVO;
    }
}
