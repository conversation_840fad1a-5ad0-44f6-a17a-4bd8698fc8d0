package com.chinasie.orion.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomePlanExecutionTrackDTO;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InTransactionPreReconciliationMapper  extends OrionBaseMapper<Object>{

    Page<InTransactionPreReconciliationVO> getDatas(@Param("param") InTransactionPreReconciliationDTO param, Page page);

    List<InTransactionPreReconciliationVO> getExportDatas(@Param("param") InTransactionPreReconciliationDTO param);

}
