package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.FixedAssets;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.FixedAssetsMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component

public class FixedAssetsXxljob {

    @Autowired
    private FixedAssetsMapper fixedAssetsMapper;
    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("fixedAssetsNotifyJob")
    public void fixedAssetsJob() {
        List<FixedAssets> fixedAssets = fixedAssetsMapper.selectTwoMonth();
        fixedAssets.forEach(item -> {
            // 发送通知
            mscBuildHandlerManager.send(item, MessageNodeDict.NODE_FIXED_NOTIFY);
        });
    }

}
