package com.chinasie.orion.domain.vo.review;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ReviewLibrary VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewLibraryVO对象", description = "评审要点库")
@Data
public class ReviewLibraryVO extends ObjectVO implements Serializable {

    /**
     * 维护部门
     */
    @ApiModelProperty(value = "维护部门")
    private String maintainDept;
    @ApiModelProperty(value = "名称id对象数组(维护部门)")
    private List<IdAndNameVO> maintainDeptList;


    /**
     * 评审专家
     */
    @ApiModelProperty(value = "评审专家")
    private String experts;
    @ApiModelProperty(value = "名称id对象数组(评审专家)")
    private List<IdAndNameVO> expertList;


    /**
     * 评审要点库名称
     */
    @ApiModelProperty(value = "评审要点库名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 要点数量
     */
    @ApiModelProperty(value = "要点数量")
    private Integer essentialsNumber;

}
