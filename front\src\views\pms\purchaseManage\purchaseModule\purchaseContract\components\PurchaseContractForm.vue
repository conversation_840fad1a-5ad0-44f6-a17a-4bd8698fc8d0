<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/src/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'actualStartTime',
    label: '实际合同开始日期',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'actualEndTime',
    label: '实际合同结束日期',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'actualAcceptanceTimes',
    label: '实际验收日期',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'isCalculation',
    label: '是否参与计算',
    colProps: { span: 12 },
    componentProps: {
    },
    component: 'Switch',
  },
  {
    field: 'acceptanceResults',
    label: '验收结果',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },

];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;

  try {
    await setFieldsValue({
      ...props.record,
      isCalculation: props.record.isCalculation === '是',
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
      isCalculation: formValues.isCalculation ? '是' : '否',
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/contractInfo').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
