package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PlanToMember;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/19/18:04
 * @description:
 */
public interface PlanToMemberService extends OrionBaseService<PlanToMember> {

    PlanToMember saveParam(String id, String principalId, int i) throws Exception;

    List<PlanToMember> listByFormIdAndType(String id, int i) throws Exception;
}
