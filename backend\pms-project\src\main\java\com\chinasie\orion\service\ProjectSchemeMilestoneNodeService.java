package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodeDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNode;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneNodeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * ProjectSchemeMilestoneNode 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
public interface ProjectSchemeMilestoneNodeService extends OrionBaseService<ProjectSchemeMilestoneNode> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectSchemeMilestoneNodeVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectSchemeMilestoneNodeDTO
     */
    ProjectSchemeMilestoneNodeVO create(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectSchemeMilestoneNodeDTO
     */
    Boolean edit(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectSchemeMilestoneNodeVO> pages(Page<ProjectSchemeMilestoneNodeDTO> pageRequest) throws Exception;

    /**
     * 树列表
     *
     * @param tplId
     * @return
     */
    List<ProjectSchemeMilestoneNodeVO> tree(String tplId);

    /**
     * 新增(批量)
     *
     * @param projectSchemeMilestoneNodeDTOS
     * @return
     */
    List<String> createBatch(List<ProjectSchemeMilestoneNodeDTO> projectSchemeMilestoneNodeDTOS);



    /**
     * 导入里程碑节点
     *
     * @param file
     * @throws Exception
     */
    ImportExcelCheckResultVO importByExcelCheck(MultipartFile file,String tplId) throws Exception;


    /**
     * 计划确认导入
     *
     * @param importKey
     */
    void importByExcelDo(String importKey);

    /**
     * 计划取消导入
     *
     * @param importKey
     */
    void importByExcelCancel(String importKey);


    List<ProjectSchemeMilestoneNodeVO> getSchemeList(String templateId) throws Exception;

    /**
     * 查询里程碑计划模板列表
     * @param templateId 计划模版id
     * @return
     * @throws Exception
     */
    List<ProjectSchemeMilestoneNodeVO> getProjectSchemeMilestoneList(String templateId) throws Exception;

    ProjectSchemeMilestoneNodeVO createSchemeMilestone(ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO);
}
