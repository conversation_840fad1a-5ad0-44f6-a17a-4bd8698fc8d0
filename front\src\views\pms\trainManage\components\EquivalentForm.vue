<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm, OrionTable, BasicButton, UploadList, randomString,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';
import { openTrainTableSelect } from '/@/views/pms/trainManage/utils';
import { useUserStore } from '/@/store/modules/user';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  record: any
}>();

const userInfo: Record<string, any> = useUserStore().getUserInfo;
const tableRef: Ref = ref();
const trainList: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  dataSource: trainList,
  isSpacing: false,
  pagination: false,
  columns: [
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
      width: 100,
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 60,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      modalTitle: '移除提示',
      modalContent: '确认移除该数据？',
      modal(record: { id: string }) {
        trainList.value = trainList.value.filter((item) => item?.id !== record?.id);
        return Promise.resolve('');
      },
    },
  ],
};

function setTrainList(data: any[]) {
  trainList.value = data || [];
}

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '基础信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'personNumber',
    component: 'Input',
    label: '员工号',
    defaultValue: userInfo?.code,
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'fullName',
    component: 'Input',
    label: '姓名',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'nowPosition',
    component: 'Input',
    label: '现任职务',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'equivalentBaseCode',
    component: 'ApiSelect',
    label: '等效申请基地',
    required: true,
    componentProps: {
      api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'code',
      onChange(_value: string, option: { label: string }) {
        setFieldsValue({
          equivalentBaseName: option?.label,
        });
        trainList.value = [];
      },
    },
  },
  {
    field: 'equivalentBaseName',
    component: 'Input',
    label: '',
    show: false,
    componentProps: {},
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    ifShow({ model }) {
      return model?.equivalentBaseCode;
    },
    renderColContent({ model }) {
      return h(BasicCard, {
        title: '等效培训信息',
        isSpacing: false,
        isBorder: false,
      }, h('div', {
        style: 'height:240px;overflow:hidden',
      }, h(OrionTable, {
        ref: tableRef,
        options: tableOptions,
      }, {
        toolbarLeft: () => h(BasicButton, {
          type: 'primary',
          icon: 'sie-icon-tianjiaxinzeng',
          onClick: () => openTrainTableSelect(model.equivalentBaseCode, [], setTrainList),
        }, '添加培训'),
      })));
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '等效材料',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  if (props?.record?.id) {
    getFormData();
  } else {
    getUserInfo();
  }
});

const loading: Ref<boolean> = ref(false);

async function getUserInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/basic-user/detail/userCode').fetch({
      userCode: userInfo.code,
    }, '', 'GET');
    setFieldsValue({
      fullName: result?.fullName,
      nowPosition: result?.nowPosition,
    });
  } finally {
    loading.value = false;
  }
}

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/train-equivalent').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
    trainList.value = [
      {
        id: result?.ukKey,
        baseName: result?.baseName,
        baseCode: result?.baseCode,
        lessonHour: result?.lessonHour,
        endDate: result?.endDate ? dayjs(result?.endDate).format('YYYY-MM-DD') : '',
        expireTime: result?.expireTime ? dayjs(result?.expireTime).format('YYYY-MM-DD') : '',
        trainCenterId: result?.trainCenterId,
        trainName: result?.trainName,
        trainNumber: result?.trainNumber,
      },
    ];
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      if (trainList.value.length === 0) {
        message.error('请添加培训信息');
        return reject();
      }
      const {
        trainCenterId, trainName, trainNumber, baseCode, baseName, id: ukKey,
      } = trainList.value[0];
      const params = {
        ...formValues,
        trainCenterId,
        trainName,
        trainNumber,
        baseCode,
        baseName,
        ukKey,
      };
      new Api('/pms/train-equivalent').fetch({
        ...formValues,
        ...params,
        id: props?.record?.id,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
