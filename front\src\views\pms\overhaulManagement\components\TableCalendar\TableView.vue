<template>
    <div ref="tableWrapRef" class="table-wrap">
        <!-- <button @click="sendData">发送消息到父组件</button> -->
        <STable :columns="getColumns()" bordered sticky :data-source="dataSource" :animateRows="false" size="small"
            :defaultExpandAllRows="true" :paginaexpandIcontion="false" :ignoreCellKey="true" 
            :scroll="{ x: 'max-content', y: tableWrapHeight }" :pagination="false" :customCell="customCell"
            :expand-row-by-click="false" :expandIcon="expandIcon" :custom-header-cell="customHeaderCell" >
            <!--右键菜单-->
            <template v-if="isEdit" #contextmenuPopup="args">
                <Menu v-if="isMenuVisible(args)" style="width: 80px" @click="menuClick($event, args)">
                    <MenuItem key="del">
                    <DeleteOutlined />
                    删除
                    </MenuItem>
                </Menu>
            </template>
        </STable>
        <!-- :expanded-row-keys="expandedRowKeys" -->
        <!-- :expandIcon="expandIcon" -->
    </div>
</template>
<script setup lang="ts">
import { STable } from '@surely-vue/table';
import {
    computed, nextTick, onMounted, reactive, Ref, ref, watch, watchEffect, getCurrentInstance, defineEmits
} from 'vue';
import { Menu, MenuItem, message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import _ from 'lodash-es';
import dayjs from 'dayjs';
import { getDaysBySection } from './resourceallocation.util';
import router from '/@/router';
import { addResizeListener } from '/@/utils/event';
import { getYearTimestamps } from './utils';
import { h } from 'vue';
import { right } from '@antv/x6/lib/registry/port-layout/line';
import {
    Button as AButton,
    Drawer as ADrawer,
    Modal as AModal,
    Table as ATable,
    InputSearch as AInputSearch,
    Pagination as APagination
} from 'ant-design-vue';
import Api from '/@/api';
const internalInstance = getCurrentInstance()
const componentKey = ref(0);
interface AntVTableProps {
    quarterKey: number
    data: any[]
    type: string
    year: string
    repairRound: string
    isEdit: boolean
    minDate: number
    legend: any[]
}


function isTimestampInRange(timestamp, startTimeStr, endTimeStr) {
    const startTime = new Date(`${startTimeStr}T00:00:00`); // 添加时间部分以确保一致性
    const endTime = new Date(`${endTimeStr}T23:59:59`); // 添加时间部分以覆盖整天
    const timestampDate = new Date(timestamp);
    return timestampDate >= startTime && timestampDate <= endTime;
}



const customHeaderCell = column => {
      return isTimestampInRange(column.dataIndex, planBeginTime.value, planEndTime.value) ? { style: { background: '#BBE6C7' } } : {};
    };

const props = defineProps<AntVTableProps>();
// const emits = defineEmits<{ (e: 'close') }>();

const dataSource: Ref<any[]> = ref([]);
const startTime: Ref<any> = ref('')
const endTime: Ref<any> = ref('')
const planBeginTime: Ref<any> = ref('')
const planEndTime: Ref<any> = ref('')


const formattedDates = reactive([]);

const expandedRowKeys = reactive([])

watch([
    () => props.data,
    () => props.quarterKey,
    () => props.legend,
    () => props.isEdit,
], () => {
    dataSource.value = props.data;

    startTime.value = props.data[0]?.beginTime?.split(' ')[0]
    endTime.value = props.data[0]?.endTime?.split(' ')[0]
    planBeginTime.value = props.data[0]?.planBeginTime?.split(' ')[0]
    planEndTime.value = props.data[0]?.planEndTime?.split(' ')[0]

    const dateList = getDatesBetween(startTime.value, endTime.value);
    formattedDates.push(formatDates(dateList));
    expandedRowKeys.push(dataSource?.value[0]?.key);

}, {
    deep: true,
});


watch(dataSource, (newValue) => {
    dataSource.value = newValue;
    return dataSource.value
}, {
    deep: true
});





const isMenuVisible = computed(() => ({ record, column }) => {
    return true
    //   const cellData = dataCellMap.value.get(`${record.uniqueId}-${column.dataIndex}`);
    //   switch (record.dataType) {
    //     case '1':
    //       return !!cellData?.unLeaderData?.length;
    //     case '3':
    //       return !!cellData?.showMenu;
    //     default:
    //       return false;
    //   }
});



const expandIcon = (props) => {
    if (props.record.children) {
        const expandedImage = require('../../../../../assets/imgs/zujian/bottom.svg');
        const collapsedImage = require('../../../../../assets/imgs/zujian/right.svg');
        if (props.expanded) {
            return h('img', {
                src: expandedImage,
                alt: 'Collapse',
                style: {
                    cursor: 'pointer',
                    width: '15px',
                    height: '15px',
                    position: 'relative',
                    top: '-3px',
                    left: '-5px',
                },
                onClick: () => {
                    props.onExpand(props.record, false);
                },
            });
        } else {
            return h('img', {
                src: collapsedImage,
                alt: 'Expand',
                style: {
                    cursor: 'pointer',
                    width: '15px',
                    height: '15px',
                    position: 'relative',
                    top: '-3px',
                    left: '-5px',
                },
                onClick() {
                    props.onExpand(props.record, true);
                }
            });
        }
    } else {
        return null;
    }
}

function getDateRangeArrayAsObjects(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const result = [];
    let currentDate = new Date(start);
    while (currentDate <= end) {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1;
        const day = currentDate.getDate();
        const formattedDate = day === 1 ? `${month}.${day}` : String(day);
        result.push({
            title: day === 1 ? `${month}.${day}` : String(day),
            dataIndex: dayjs(year + '-' + month + '-' + day).valueOf(),
            width: day === 1 ? 30 : 18,
            key: 'sb' + month + '-' + day,
            align: 'center',
            type: 'day',
            identPosition: 0,
            ident: '',
            identText: '',
            year: year,
            month: month.toString().length == 1 ? '0' + month.toString() : month.toString(),
            dayvalue: day,
            customRender
        });

        currentDate.setDate(currentDate.getDate() + 1);
    }

    return result;
}



function getColumns() {
    const quarterColumns = getDateRangeArrayAsObjects(startTime.value, endTime.value);
    return [
        ...(props.type.includes('person') ? [
            {
                title: '大修组织机构',
                dataIndex: 'name',
                fixed: 'left',
                align: 'center',
                width: 130,
                slot: 'slotname',
                ellipsis: true,
                resizable: true,
                // customRender: ({text, record, index, }) => {
                //     const obj = {
                //         props: {} as any,
                //     };
                //     if (record.hasOwnProperty('rowSpan') && index===0) {
                //         obj.props.rowSpan = record.rowSpan;
                //     }else{
                //         obj.props.rowSpan = 1;
                //     }
                //     return obj;
                // },
            },
            {
                title: '常驻',
                dataIndex: 'isBasePermanent',
                fixed: 'left',
                align: 'center',
                width: 50,
                resizable: true,
            },
            {
                title: '轮次',
                dataIndex: 'repairRound',
                fixed: 'left',
                align: 'center',
                width: 70,
                resizable: true,
            },
            {
                title: '班组',
                dataIndex: 'teamName',
                fixed: 'left',
                align: 'center',
                width: 100,
                resizable: true,
            },
            {
                title: '重叠',
                dataIndex: 'overNumbers',
                fixed: 'left',
                align: 'center',
                width: 60,
                resizable: true,
            },
        ] : []),
        ...(props.type.includes('material') ? [
            {
                title: '大修组织架构',
                dataIndex: 'name',
                fixed: 'left',
                align: 'center',
                width: 125,
                slot: 'slotname',
                ellipsis: true,
                resizable: true,
                // customRender: ({ text, record, index, }) => {
                //     const obj = {
                //         props: {} as any,
                //     };
                //     if (record.hasOwnProperty('rowSpan') && index === 0) {
                //         obj.props.rowSpan = record.rowSpan;
                //     } else {
                //         obj.props.rowSpan = 1;
                //     }
                //     return obj;
                // },
            },
            {
                title: '轮次',
                dataIndex: 'repairRound',
                fixed: 'left',
                align: 'center',
                width: 70,
                resizable: true,
            },
            {
                title: '班组',
                dataIndex: 'teamName',
                fixed: 'left',
                align: 'center',
                width: 100,
                resizable: true,
            },
            {
                title: '重叠',
                dataIndex: 'overNumbers',
                fixed: 'left',
                align: 'center',
                width: 60,
                resizable: true,
            },
        ] : []),
        ...quarterColumns
    ];
}



function getDatesBetween(startDate, endDate) {
    let dates = [];
    let currentDate = new Date(startDate);
    let endDateold = new Date(endDate);
    while (currentDate <= endDateold) {
        let formattedDate = currentDate?.toISOString().split('T')[0];
        dates.push(formattedDate);
        currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
}


function convertDateToMD(dateString) {
    const date = new Date(dateString);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}.${day}`;
}
function formatDates(arr) {
    let satrt = ref()
    let end = ref()
    let newarr = ref([])
    satrt.value = arr[0]
    end.value = arr[arr.length - 1]
    satrt.value = convertDateToMD(startTime.value)
    end.value = convertDateToMD(endTime.value)
    arr.forEach((item, index, arr) => {
        if (index == 0) {
            newarr.value.push(satrt.value)
        } else if (index == arr.length - 1) {
            newarr.value.push(end.value)
        } else {
            newarr.value.push(item?.split('-')[2])
        }
    })
    return newarr.value
}
///////
const customRender = ({ column, record }) => {
    if (column.identPosition == record.id) {
        return column.ident
    }
}


const saveList = ref([])
//单元格事件
const flag = ref(0)
const starttime = ref()
const endtime = ref()
const rowId = ref('')
const idFlag = ref(0)
function customCell({ column, record }) {
    if (column.type === 'day') {
        let { positions, overlapIds, repairNames } = checkDatePosition(column, column.dataIndex, record);
        let cellClassName = [];
        if (positions && positions.length) {
            cellClassName.push(...repairNames);
        }
        return {
            class: positions.concat(cellClassName).join(' '),
            onClick(event) {
                let id = ''
                if (props.isEdit == true) {
                    if (record.repairRound == dataSource.value[0]?.repairRound) {
                        flag.value++;
                        if (column.type === 'day') {
                            if (flag.value == 1) {
                                rowId.value = record.id
                                if (column.dayvalue.length == 1) {
                                    column.dayvalue = '0' + column.dayvalue;
                                    if (column.month.length == 1) {
                                        column.month = '0' + column.month;
                                    }
                                    starttime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                    endtime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                } else {

                                    starttime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                    endtime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                }
                            } else if (flag.value == 2) {
                                if (rowId.value == record.id) {
                                    let dateObj1 = new Date(starttime.value);
                                    if (column.dayvalue.length == 1) {
                                        if (column.month.length == 1) {
                                            column.month = '0' + column.month;
                                        }
                                        column.dayvalue = '0' + column.dayvalue;
                                        endtime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                    }
                                    let dateObj2 = new Date(column.year + '-' + column.month + '-' + column.dayvalue);
                                    if (dateObj1 < dateObj2) {
                                        endtime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                    } else {
                                        endtime.value = starttime.value;
                                        starttime.value = column.year + '-' + column.month + '-' + column.dayvalue
                                    }
                                    flag.value = 0
                                } else {
                                    message.error('请先选择结束时间')
                                    flag.value = 1
                                    return;
                                }

                            }
                            const obj = {
                                id: record?.sectionTime[0].id,
                                relationId: record.sectionTime[0].relationId,
                                number: record?.sectionTime[0].number,
                                realStartDate: starttime.value,
                                realEndDate: endtime.value,
                                repairName: record?.sectionTime[0].repairName,
                                teamId: record?.sectionTime[0].teamId,
                                isBasePermanent: record?.sectionTime[0].isBasePermanent == '是' ? true : false,
                            };

                            record.sectionTime.length = 0;
                            record.sectionTime.push(obj);
                            saveList.value.push(obj);
                            clickThis()
                        } else {
                            // flag.value=0
                            // message.error('请先删除再添加!')
                        }

                    } else {
                        message.error("仅可修改当前大修轮次！")
                    }
                } else {
                    message.error("请打开编辑状态修改！")
                }

            },

        };
    }
    return {};
}




async function menuClick({ domEvent }, { record, column }) {
    let obj = {
        id: record.sectionTime[0]?.id,
        relationId: record.sectionTime[0]?.relationId,
        number: record.sectionTime[0]?.number,
        realStartDate: null,
        realEndDate: null,
        repairName: record.sectionTime[0]?.repairName,
        teamId: record.sectionTime[0]?.teamId,
        isBasePermanent: record.sectionTime[0]?.isBasePermanent == '是' ? true : false,
    };
    if (record.repairRound == dataSource.value[0]?.repairRound) {
        saveList.value.push(obj)
        record.sectionTime.length = 0;
        record.sectionTime.push(obj)
        flag.value = 0;
        endtime.value = '';
        starttime.value = '';
        clickThis()
    } else {
        message.error('仅可修改当前大修轮次！');
    }
}


const emit = defineEmits(['clickThis'])
const clickThis = () => {
    emit('clickThis', saveList)
}






var i = 0;
function checkDatePosition(column_, targetDate: string, record: Record<string, any>): {

    positions: string[]
    overlapIds: string[]
    repairName: string[]
} {
    const dateRanges = record?.sectionTime || [];
    const cacheKey = `${record?.number}_${record?.id}_${column_.title}__${record?.dataType}_${targetDate}_${dateRanges.map((item) => `${item.realStartDate}-${item.realEndDate}`)}`;


    if (datePositionCache.value.has(cacheKey)) {
        return datePositionCache.value.get(cacheKey)!;

    }

    const positions = [];
    const overlapIds = [];
    const repairNames = [];
    const baseOverlaps = new Map<string, string>();
    for (let i = 0; i < dateRanges.length; i++) {

        const {
            number, realStartDate, realEndDate, repairName
        } = dateRanges[i];



        const targetDateStr = Number(targetDate);
        const inDateStr = dayjs(realStartDate).valueOf();
        const outDateStr = dayjs(realEndDate).valueOf();

        if (targetDateStr >= inDateStr && targetDateStr <= outDateStr && number == -1) {
            positions.push('redColor');
            continue;
        }

        if (inDateStr === outDateStr) {
        }
        if (targetDateStr === inDateStr && targetDateStr === outDateStr && number != -1) {
            positions.push('unit');
            continue;
        }

        if (targetDateStr === inDateStr) {
            positions.push('start');
            continue;
        }

        if (targetDateStr > inDateStr && targetDateStr < outDateStr && number != -1) {
            positions.push('middle');
            continue;
        }

        if (targetDateStr === outDateStr && number != -1) {
            positions.push('end');
        }

    }
    const arr = [...new Set(positions)];
    const result = {
        positions: arr.length > 1 ? ['data-cell', 'middle', 'redColor'] : arr.length ? ['data-cell', ...arr] : arr,
        overlapIds,
        repairNames
    };
    datePositionCache.value.set(cacheKey, result);
    // if (positions.length > 0 && (positions[0] === 'start')) {
    //     const cacheKeyPosition = cacheKey + '_start';
    //     datePositionCache.value.set(cacheKeyPosition, result);
    //     column_.identPosition = record.id;
    //     column_.identText = 'start';
    //     column_.ident = repairNames[0];
    // }
    return result;
}



// 时间标记数据
const datePositionCache = computed(() => new Map<string, {
    positions: string[]
    overlapIds: string[]
}>());

const getLikeDaysBySection = (sections) => {

    const dayLikes = [];

    sections.forEach((item) => {
        const { realStartDate, realEndDate, repairName } = item;

        let currentDate = dayjs(realStartDate);
        while (currentDate.isBefore(realEndDate) || currentDate.isSame(realEndDate, 'day')) {
            let position_ = '';
            if (currentDate.isSame(realStartDate)) {
                position_ = 'start';
            } else if (currentDate.isSame(realEndDate)) {
                position_ = 'end';
            } else {
                position_ = 'middle';
            }
            dayLikes.push({
                realStartDate: currentDate.format('YYYY/MM/DD'),
                repairName: repairName,
                position: position_
            });
            currentDate = currentDate.add(1, 'day');
        }
    });

    return dayLikes;
}


// 用于存储默认展开的行的 keys  
// const getExpandedKeys = (data) => {
//   const keys = [];
//   const addKeys = (items) => {
//     items.forEach(item => {
//       keys.push(item.key);
//       if (item.children) {
//         addKeys(item.children);
//       }
//     });
//   };
//   addKeys(data);
//   return keys;
// };
const tableWrapRef: Ref = ref();
const tableWrapHeight: Ref = ref(0);




onMounted(() => {

    addResizeListener(tableWrapRef.value, nodeResize);
})
function nodeResize() {
    const height = (tableWrapRef.value?.clientHeight || 0) - 60;
    tableWrapHeight.value = height < 0 ? 0 : height;
}
</script>



<style scoped lang="less">
:deep(.surely-table-cell-box) {
    padding: 0px 0px;
}

:deep(.surely-table-cell-box) {
    padding: 0px !important;
}

:deep(.testClass) {
    color: #AFE8C5 !important;
}

:deep(.surely-table) {
    // height: 95% !important;
}

.table-wrap {
    height: calc(100% - 115px);
    // height: 90%;
    overflow: auto;
    min-height: 400px;
}

:deep(.data-cell) {
    .surely-table-cell-inner {
        position: absolute;
        content: '';
        top: 50%;
        left: 0;
        transform: translateY(-1%);
        width: 100%;
        height: 50% !important;
        overflow: hidden;
        z-index: 1;
    }

    &.unit {
        .surely-table-cell-inner {
            border-radius: 4px;
        }
    }

    &.start {
        border-right: none !important;

        .surely-table-cell-inner {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
    }

    &.end {
        .surely-table-cell-inner {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }

    &.middle {
        border-right: none !important;

        .surely-table-cell-inner {
            border-radius: 0;
        }
    }

    &.in-out {
        .surely-table-cell-inner {
            background-color: #B5C2DD !important;
        }
    }

    &.job {
        .surely-table-cell-inner {
            background-color: #AFE8C5;

            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #60D38D;
            }
        }
    }

    &.task {
        .surely-table-cell-inner {
            background-color: #60D38D;
        }
    }

    &.overlap {
        .surely-table-cell-inner {
            background-color: #FF928A;
        }
    }

    &.redColor {
        .surely-table-cell-inner {
            background-color: #FF928A;
        }
    }


    .surely-table-cell-inner {
        background-color: #A2E5BC;

        // &::before {
        //     position: absolute;
        //     content: '';
        //     top: 0;
        //     left: 0;
        //     width: 100%;
        //     height: 2px;
        //     background-color: #60D38D;
        // }

    }

}

&.redColor {
    .surely-table-cell-inner {
        background-color: #FF928A;
    }
}

&.H602 {
    // .surely-table-cell-inner {
    //     background-color: #A2E5BC;

    //     // &::before {
    //     //     position: absolute;
    //     //     content: '';
    //     //     top: 0;
    //     //     left: 0;
    //     //     width: 100%;
    //     //     height: 2px;
    //     //     background-color: #60D38D;
    //     // }
    // }
    // }

    // &.D316 {
    //     .surely-table-cell-inner {
    //         background-color: #9DE0EE;
    //     }
    // }

    // &.Y209 {
    //     .surely-table-cell-inner {
    //         background-color: #C3BBFF;
    //     }
    // }
}

.tooltip-title {
    display: grid;
    gap: 10px 20px;
}

:deep(.job-name-row) {
    .surely-table-cell-inner {
        overflow: unset;

    }

    .surely-table-cell-content {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        color: ~`getPrefixVar('primary-color')`;
        cursor: pointer;
        overflow: hidden;
        max-height: 75px;
        display: flex;
    }

    &-1 {
        .surely-table-cell-content {
            -webkit-line-clamp: 1;
        }
    }

    &-3 {
        .surely-table-cell-content {
            -webkit-line-clamp: 3;
        }
    }
}

:deep(.ant-menu-item) {
    line-height: 24px;
    height: 24px;
}

:deep(.cp) {
    cursor: pointer;
}

:deep(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
}

:deep(.surely-table-cell-content) {
    display: flex;
}
</style>