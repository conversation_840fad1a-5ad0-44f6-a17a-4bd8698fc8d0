package com.chinasie.orion.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobManageTreeBO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String number;

    /**
     * 作业名
     */
    @ApiModelProperty(value = "作业名")
    private String name;


    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    private String nOrO;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspDept;


    @ApiModelProperty(value = "是否高风险")
    private String highRiskName;


    /**
     * 是否高风险
     */
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;

    /**
     * 是否重要作业
     */
    @ApiModelProperty(value = "是否重要作业")
    private Boolean isImportant;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenter;

    /**
     * 作业基地
     */
    @ApiModelProperty(value = "作业基地")
    private String jobBaseName;


    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    private String phase;


    /**
     * 是否匹配（0未匹配  1匹配）
     */
    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    private Integer matchUp;

    @ApiModelProperty("责任人id")
    String rspUserId;

    @ApiModelProperty("责任人姓名")
    String rspUserName;


    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    private String firstExecute;

    /**
     * 防异物等级
     */
    @ApiModelProperty(value = "防异物等级")
    private String antiForfeignLevel;

    /**
     * 是否已选择(0未选择   1已选择)
     */
    @ApiModelProperty("是否已选择(0未选择   1已选择)")
    Integer selected;

    @ApiModelProperty("状态")
    Integer status;

    @ApiModelProperty("工期")
    Integer workDuration;

    @ApiModelProperty("实际开始")
    Date actualBeginTime;

    @ApiModelProperty("实际结束")
    Date actualEndTime;

    @ApiModelProperty("计划开始")
    Date beginTime;

    @ApiModelProperty("计划结束")
    Date endTime;

    @ApiModelProperty("负责人code")
    String rspUserCode;

    @ApiModelProperty(value = "重要项目")
    private String importantProject;

    @ApiModelProperty(value = "大修所属部门")
    private String repairOrgId;
}
