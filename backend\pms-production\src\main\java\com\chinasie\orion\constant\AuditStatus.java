package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/24/15:46
 * @description:
 */
public enum AuditStatus {
    no_push(101,"待审查")
    ,executiong(110,"流程中")
    ,FINISH(130,"已审查")
    ;


    private Integer status;

    private String desc;

    AuditStatus(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> getMap (){
        Map<String,String> map =new HashMap<>();
        AuditStatus[] auditStatuses = AuditStatus.values();
        for (AuditStatus auditStatus : auditStatuses) {
            map.put(auditStatus.getStatus().toString(),auditStatus.getDesc());
        }
        return  map;
    }
}
