package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPurchaseSupplierInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:11:33
 */
@ApiModel(value = "ProjectPurchaseSupplierInfoDTO对象", description = "项目采购供应商信息")
@Data
public class ProjectPurchaseSupplierInfoDTO extends ObjectDTO implements Serializable{

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @NotBlank(message = "供应商不能为空")
    @Size(max = 100, message = "供应商过长，建议控制在100字符以内")
    private String supplierName;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @NotBlank(message = "联系人不能为空")
    @Size(max = 100, message = "联系人过长，建议控制在100字符以内")
    private String contactPerson;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    @Size(max = 100, message = "联系人过长，建议控制在100字符以内")
    @Pattern(regexp = "^([0-9][0-9]*)$", message = "联系人电话格式错误")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    @Size(max = 100, message = "联系人邮箱过长，建议控制在100字符以内")
    private String contactEmail;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    private String purchaseId;

}
