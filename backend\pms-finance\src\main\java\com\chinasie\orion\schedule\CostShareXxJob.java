package com.chinasie.orion.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.conts.IncomePlanDataStatusEnum;
import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.entity.ProjectFullSizeReport;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.CostShareMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.CostShareService;
import com.chinasie.orion.service.IncomePlanDataService;
import com.chinasie.orion.service.IncomePlanService;
import com.chinasie.orion.service.ProjectFullSizeReportService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class CostShareXxJob {
    @Resource
    private CostShareMapper costShareMapper;
    @Autowired
    private ProjectFullSizeReportService projectFullSizeReportService;

    @Autowired
    private ClassRedisHelper classRedisHelper;


    @XxlJob("costShareTotal")
    public void costShareTotal(Integer year) throws JsonProcessingException {
        if(ObjectUtil.isEmpty(year)){
            year = Integer.parseInt(DateUtil.format(new Date(), "yyyy"));
        }
        projectFullSizeReportService.remove(new LambdaQueryWrapper<ProjectFullSizeReport>().eq(ProjectFullSizeReport::getYear,year));
        List<ProjectFullSizeReport> reportList = costShareMapper.getTotal(year);
        Map<String,ProjectFullSizeReport> companyMap = new HashMap<>();
        Map<String,ProjectFullSizeReport> wbsExpertiseCenterMap = new HashMap<>();
        Map<String,ProjectFullSizeReport> BusinessClassificationMap = new HashMap<>();
        String orgId = CurrentUserHelper.getOrgId();
        for(ProjectFullSizeReport projectFullSizeReport:reportList){
            projectFullSizeReport.setType("4");
            projectFullSizeReport.setYear(year);
            ProjectFullSizeReport projectFullSizeReport1 = companyMap.get(projectFullSizeReport.getCompanyId());
            if(ObjectUtil.isNotEmpty(projectFullSizeReport1)){
                sumProjectFullSizeReport(projectFullSizeReport1,projectFullSizeReport);
            }else{
                projectFullSizeReport1 = new ProjectFullSizeReport();
                projectFullSizeReport1.setCompanyId(projectFullSizeReport.getCompanyId());
                projectFullSizeReport1.setType("1");
                projectFullSizeReport1.setYear(year);
                projectFullSizeReport1.setId(classRedisHelper.getUUID(ProjectFullSizeReport.class.getSimpleName()));
                sumProjectFullSizeReport(projectFullSizeReport1,projectFullSizeReport);
                companyMap.put(projectFullSizeReport1.getCompanyId(),projectFullSizeReport1);
            }
            ProjectFullSizeReport projectFullSizeReport2 = wbsExpertiseCenterMap.get(projectFullSizeReport.getCompanyId()
                    +projectFullSizeReport.getWbsExpertiseCenter());
            if(ObjectUtil.isNotEmpty(projectFullSizeReport2)){
                sumProjectFullSizeReport(projectFullSizeReport2,projectFullSizeReport);
            }else{
                projectFullSizeReport2 = new ProjectFullSizeReport();
                projectFullSizeReport2.setId(classRedisHelper.getUUID(ProjectFullSizeReport.class.getSimpleName()));
                projectFullSizeReport2.setCompanyId(projectFullSizeReport.getId());
                projectFullSizeReport2.setType("2");
                projectFullSizeReport2.setYear(year);
                projectFullSizeReport2.setWbsExpertiseCenter(projectFullSizeReport.getWbsExpertiseCenter());
                projectFullSizeReport2.setParentId(projectFullSizeReport1.getId());
                sumProjectFullSizeReport(projectFullSizeReport2,projectFullSizeReport);
                wbsExpertiseCenterMap.put(projectFullSizeReport.getCompanyId()
                        +projectFullSizeReport.getWbsExpertiseCenter(),projectFullSizeReport2);
            }
            ProjectFullSizeReport projectFullSizeReport3 = BusinessClassificationMap.get(projectFullSizeReport.getCompanyId()
                    +projectFullSizeReport.getWbsExpertiseCenter()+projectFullSizeReport.getBusinessClassification());
            if(ObjectUtil.isNotEmpty(projectFullSizeReport3)){
                sumProjectFullSizeReport(projectFullSizeReport3,projectFullSizeReport);
            }else{
                projectFullSizeReport3 = new ProjectFullSizeReport();
                projectFullSizeReport3.setId(classRedisHelper.getUUID(ProjectFullSizeReport.class.getSimpleName()));
                projectFullSizeReport3.setCompanyId(projectFullSizeReport.getCompanyId());
                projectFullSizeReport3.setType("3");
                projectFullSizeReport3.setYear(year);
                projectFullSizeReport3.setWbsExpertiseCenter(projectFullSizeReport.getWbsExpertiseCenter());
                projectFullSizeReport3.setParentId(projectFullSizeReport2.getId());
                projectFullSizeReport3.setBusinessClassification(projectFullSizeReport.getBusinessClassification());
                sumProjectFullSizeReport(projectFullSizeReport3,projectFullSizeReport);
                BusinessClassificationMap.put(projectFullSizeReport.getCompanyId()
                        +projectFullSizeReport.getWbsExpertiseCenter()+projectFullSizeReport.getBusinessClassification(),projectFullSizeReport3);
            }
            projectFullSizeReport.setParentId(projectFullSizeReport3.getId());
        }
        List<ProjectFullSizeReport> keysList = Stream.of(wbsExpertiseCenterMap, BusinessClassificationMap,companyMap)
                .flatMap(map -> map.values().stream())
                .collect(Collectors.toList());
        for(ProjectFullSizeReport projectFullSizeReport:keysList){
            statics(projectFullSizeReport);
        }
        reportList.addAll(keysList);
        projectFullSizeReportService.saveBatch(reportList);
    }


    public void sumProjectFullSizeReport(ProjectFullSizeReport projectFullSizeReport1,ProjectFullSizeReport projectFullSizeReport2){
        projectFullSizeReport1.setOperatingIncome(projectFullSizeReport1.getOperatingIncome().add(projectFullSizeReport2.getOperatingIncome()));
        projectFullSizeReport1.setDirectPurchaseCost(projectFullSizeReport1.getDirectPurchaseCost().add(projectFullSizeReport2.getDirectPurchaseCost()));
        projectFullSizeReport1.setDirectTravelCost(projectFullSizeReport1.getDirectTravelCost().add(projectFullSizeReport2.getDirectTravelCost()));
        projectFullSizeReport1.setLaborCost(projectFullSizeReport1.getLaborCost().add(projectFullSizeReport2.getLaborCost()));
        projectFullSizeReport1.setTechnicalConfiguration(projectFullSizeReport1.getTechnicalConfiguration().add(projectFullSizeReport2.getTechnicalConfiguration()));
        projectFullSizeReport1.setDailyAdministrativeExpenses(projectFullSizeReport1.getDailyAdministrativeExpenses().add(projectFullSizeReport2.getDailyAdministrativeExpenses()));
        projectFullSizeReport1.setSoftwareUsageFee(projectFullSizeReport1.getSoftwareUsageFee().add(projectFullSizeReport2.getSoftwareUsageFee()));
        projectFullSizeReport1.setTaxeSurcharge(projectFullSizeReport1.getTaxeSurcharge().add(projectFullSizeReport2.getTaxeSurcharge()));
        projectFullSizeReport1.setManagementFee(projectFullSizeReport1.getManagementFee().add(projectFullSizeReport2.getManagementFee()));
    }

    public void statics(ProjectFullSizeReport projectFullSizeReport){
        projectFullSizeReport.setProjectDirectCostGross(projectFullSizeReport.getOperatingIncome()
                .subtract(projectFullSizeReport.getDirectPurchaseCost())
                .subtract(projectFullSizeReport.getDirectTravelCost())
                .subtract(projectFullSizeReport.getLaborCost())
                .subtract(projectFullSizeReport.getTechnicalConfiguration())
        );
        if(projectFullSizeReport.getOperatingIncome().compareTo(BigDecimal.ZERO)==0) {
            projectFullSizeReport.setProjectDirectCostGrossMargin(BigDecimal.ZERO);
        }else {
            projectFullSizeReport.setProjectDirectCostGrossMargin(projectFullSizeReport.getProjectDirectCostGross()
                    .divide(projectFullSizeReport.getOperatingIncome(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }

        projectFullSizeReport.setProjectGrossProfit(projectFullSizeReport.getProjectDirectCostGross()
                .subtract(projectFullSizeReport.getDailyAdministrativeExpenses())
                .subtract(projectFullSizeReport.getSoftwareUsageFee())
                .subtract(projectFullSizeReport.getTaxeSurcharge())
        );
        if(projectFullSizeReport.getOperatingIncome().compareTo(BigDecimal.ZERO)==0) {
            projectFullSizeReport.setProjectDirectCostGrossMargin(BigDecimal.ZERO);
        }else {
            projectFullSizeReport.setProjectGrossMargin(projectFullSizeReport.getProjectGrossProfit()
                    .divide(projectFullSizeReport.getOperatingIncome(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }
        projectFullSizeReport.setProjectProfit(projectFullSizeReport.getProjectGrossProfit().subtract(projectFullSizeReport.getManagementFee()));

        if(projectFullSizeReport.getOperatingIncome().compareTo(BigDecimal.ZERO)==0) {
            projectFullSizeReport.setProjectDirectCostGrossMargin(BigDecimal.ZERO);
        }else {
            projectFullSizeReport.setProjectProfitMargin(projectFullSizeReport.getProjectProfit()
                    .divide(projectFullSizeReport.getOperatingIncome(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }
    }
}
