package org.jeecg.modules.demo.dkm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.demo.dkm.dto.DtcPersonnelsDTO;
import org.jeecg.modules.demo.dkm.entity.DtcPersonnels;

import java.util.List;

/**
 * @Description: DtcPersonnels Mapper
 * @Author: tancheng
 * @Date: 2025-4-16
 */
@Mapper
public interface DtcPersonnelsMapper extends BaseMapper<DtcPersonnels> {
    
    /**
     * 查询数据完整性问题的记录
     * 一次性查询所有字段缺失的记录
     * @return 存在数据完整性问题的记录列表
     */
    List<DtcPersonnelsDTO> findDataIntegrityIssues();
} 