package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ContractMilestonesStatisticDTO对象
 *
 */
@ApiModel(value = "ContractMilestonesStatisticDTO对象", description = "合同里程碑统计")
@Data
@ExcelIgnoreUnannotated
public class ContractMilestonesStatisticDTO {

    // 非核框架合同数量
    @ApiModelProperty(value = "非核框架合同数量")
    private Integer nonNuclearFrameworkContractCount;

    // 非核框架合同金额
    @ApiModelProperty(value = "非核框架合同金额")
    private BigDecimal nonNuclearFrameworkContractAmount;

    // 非核总价合同数量
    @ApiModelProperty(value = "非核总价合同数量")
    private Integer nonNuclearTotalPriceContractCount;

    // 非核总价合同金额
    @ApiModelProperty(value = "非核总价合同金额")
    private BigDecimal nonNuclearTotalPriceContractAmount;

    // 非核复合合同数量
    @ApiModelProperty(value = "非核复合合同数量")
    private Integer nonNuclearCompositeContractCount;

    // 非核复合合同金额
    @ApiModelProperty(value = "非核复合合同金额")
    private BigDecimal nonNuclearCompositeContractAmount;

    // 非核已签合同数量
    @ApiModelProperty(value = "非核已签合同数量")
    private Integer nonNuclearSignedContractCount;

    // 非核已签合同金额
    @ApiModelProperty(value = "非核已签合同金额")
    private BigDecimal nonNuclearSignedContractAmount;

    // 非核待签合同数量
    @ApiModelProperty(value = "非核待签合同数量")
    private Integer nonNuclearPendingContractCount;

    // 非核待签合同金额
    @ApiModelProperty(value = "非核待签合同金额")
    private BigDecimal nonNuclearPendingContractAmount;


    @ApiModelProperty(value = "核框架合同数量")
    private Integer nuclearFrameworkContractCount;


    @ApiModelProperty(value = "核框架合同金额")
    private BigDecimal nuclearFrameworkContractAmount;

    // 核总价合同数量
    @ApiModelProperty(value = "核总价合同数量")
    private Integer nuclearTotalPriceContractCount;

    // 核总价合同金额
    @ApiModelProperty(value = "核总价合同金额")
    private BigDecimal nuclearTotalPriceContractAmount;

    // 核复合合同数量
    @ApiModelProperty(value = "核复合合同数量")
    private Integer nuclearCompositeContractCount;

    // 核复合合同金额
    @ApiModelProperty(value = "核复合合同金额")
    private BigDecimal nuclearCompositeContractAmount;

    // 核已签合同数量
    @ApiModelProperty(value = "核已签合同数量")
    private Integer nuclearSignedContractCount;

    // 核已签合同金额
    @ApiModelProperty(value = "核已签合同金额")
    private BigDecimal nuclearSignedContractAmount;

    // 核待签合同数量
    @ApiModelProperty(value = "非核待签合同数量")
    private Integer nuclearPendingContractCount;

    // 核待签合同金额
    @ApiModelProperty(value = "非核待签合同金额")
    private BigDecimal nuclearPendingContractAmount;


}
