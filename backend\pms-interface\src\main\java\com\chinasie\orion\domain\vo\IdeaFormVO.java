package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.util.List;

/**
 * IdeaForm VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "IdeaFormVO对象", description = "意见单")
@Data
public class IdeaFormVO extends ObjectVO implements Serializable {

    /**
     * 接口类型
     */
    @ApiModelProperty(value = "单据类型")
    private String formType;
    @ApiModelProperty(value = "单据类型名称")
    private String formTypeName;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    private String publishDeptId;
    @ApiModelProperty(value = "发布部门名称")
    private String publishDeptName;

    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    private String reviewDeptIds;
    @ApiModelProperty(value = "接受部门ids")
    private List<String> reviewDeptIdList;
    @ApiModelProperty(value = "接受部门名称")
    private String reviewDeptName;

    /**
     * 当前责任方
     */
    @ApiModelProperty(value = "当前责任方")
    private String currentUserNames;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    private String manUser;
    @ApiModelProperty(value = "主办人名称")
    private String manUserName;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    private String thirdVerify;
    @ApiModelProperty(value = "第三方检查备案名称")
    private String thirdVerifyName;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    private String specialtyCode;

    /**
     * 回复意见描述
     */
    @ApiModelProperty(value = "回复意见描述")
    private String desc;

    /**
     * 回复意见
     */
    @ApiModelProperty(value = "回复意见")
    private String replySuggest;
    @ApiModelProperty(value = "回复意见名称")
    private String replySuggestName;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 接口id
     */
    @ApiModelProperty(value = "接口id")
    private String interfaceId;
    @ApiModelProperty(value = "接口名称")
    private String interfaceName;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "流水号")
    private String projectId;
    /**
     * 协办
     */
    @ApiModelProperty(value = "协办人")
    private String cooperationUsers;
    @ApiModelProperty(value = "协办人名称")
    private String cooperationUserNames;


    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    private String dataClassName;

}
