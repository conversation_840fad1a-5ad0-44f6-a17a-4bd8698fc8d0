package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.SupplierBankDTO;
import com.chinasie.orion.domain.entity.SupplierBank;
import com.chinasie.orion.domain.vo.SupplierBankVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * SupplierBank 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
public interface SupplierBankService extends OrionBaseService<SupplierBank> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SupplierBankVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param supplierBankDTO
     */
    String create(SupplierBankDTO supplierBankDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param supplierBankDTO
     */
    Boolean edit(SupplierBankDTO supplierBankDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<SupplierBankVO> pages(String mainTableId, Page<SupplierBankDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<SupplierBankVO> vos) throws Exception;

    /**
     * 根据供应商编码查询list
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Page<SupplierBankVO> getByCode(Page<SupplierBankDTO> pageRequest) throws Exception;
}
