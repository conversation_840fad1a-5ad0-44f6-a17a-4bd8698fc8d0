package com.chinasie.orion.constant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同预警日期
 */

public enum ContractWarningDayEnum {

    NORMAL("正常","10"),
    HALF_YEAR("剩余半年","100"),
    LESS_HALF_YEAR("临期半年","101"),
    THREE_MONTH("剩余三个月","102"),
    LESS_THREE_MONTH("临期三个月","103"),
    TWO_MONTH("剩余两个月","104"),
    LESS_TWO_MONTH("临期两个月","105"),
    ONE_MONTH("剩余一个月","106"),
    LESS_ONE_MONTH("临期一个月","107"),
    FIFTEEN_DAY("剩余15天","108"),
    LESS_FIFTEEN_DAY("临期15天","109"),
    ZERO_DAY("已到期","110");

    private String name;
    private String desc;

    ContractWarningDayEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ContractWarningDayEnum lt : ContractWarningDayEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

    public static Map<String, String> getContractWarningDayMap() {
        return Arrays.stream(ContractWarningDayEnum.values())
                .collect(Collectors.toMap(ContractWarningDayEnum::getDesc, ContractWarningDayEnum::getName));
    }


}