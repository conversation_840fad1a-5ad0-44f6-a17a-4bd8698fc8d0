<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="typeName+'计划模板'"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm" />
      </div>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import { computed, defineEmits, ref } from 'vue';
import {
  BasicButton, BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import {
  Input, message, Textarea, InputNumber,
} from 'ant-design-vue';

import dayjs from 'dayjs';
import { projectSchemeMilestoneTemplate, putProjectSchemeMilestoneNode } from '../api';
import Api from '/@/api';
const rspDeptOptions = ref([]);
const submitLoading = ref(false);
const templateId = ref('');
const [modalRegister, { closeDrawer }] = useDrawerInner((drawerData) => {
  typeName.value = '编辑';
  formData.value = drawerData;
  templateId.value = drawerData.templateId;

  setFieldsValue(drawerData);
});
const emit = defineEmits(['editSuccess']);

const drawerName = ref<string>('');
const typeName = ref<string>('添加');
const formData = ref<any>({});

const handleClose = () => {
  closeDrawer();
};

const [
  registerForm,
  {
    setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
  },
] = useForm({
  labelWidth: 120,
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      colProps: {
        span: 24,
      },
      label: '计划名称:',
      rules: [
        {
          required: true,
          trigger: 'blur',
          type: 'string',
        },
      ],
    },
    {
      field: 'nodeType',
      component: 'Select',
      label: '计划类型:',
      rules: [
        {
          required: true,
          trigger: 'blur',
          type: 'string',
        },
      ],
      colProps: {
        span: 12,
      },
      componentProps: {
        options: [
          {
            label: '计划',
            value: 'plan',
          },
          {
            label: '里程碑',
            value: 'milestone',
          },
        ],
      },
    },
    {
      field: 'rspDeptId',
      component: 'Select',
      label: '责任部门:',
      colProps: {
        span: 12,

      },
      componentProps: {
        options: computed(() => rspDeptOptions.value),
        fieldNames: {
          key: 'id',
          value: 'id',
          label: 'name',
        },
      },
    },
    {
      field: 'durationDays',
      component: 'InputNumber',
      label: '工期:',
      colProps: {
        span: 12,
      },
      rules: [{ type: 'number' }],
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'delayDays',
      component: 'InputNumber',
      label: '项目从N天开始:',
      colProps: {
        span: 12,
      },
      rules: [{ type: 'number' }],
      componentProps: {
        min: 0,
      },
    },
    {
      field: 'processFlag',
      component: 'Select',
      label: '是否关联流程:',
      colProps: { span: 24 },
      componentProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '描述:',
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 250,
      },
    },
  ],
});

function getOrganization() {
  new Api('/pmi')
    .fetch({}, 'organization/org-type', 'GET')
    .then((res) => {
      rspDeptOptions.value = res;
    });
}
getOrganization();

async function handleSubmit() {
  const formObj = await validateFields();
  formObj.templateId = templateId.value;
  formObj.id = formData.value.id;
  formObj.nodeName = formObj.name;
  const result = await new Api('/pms/projectSchemeMilestoneNode').fetch(
    formObj,
    '',
    'PUT',
  );
  if (result) {
    closeDrawer();
    formData.value = null;
    message.success('操作成功');
    emit('editSuccess', result);
  }
}
</script>

<style lang="less" scoped>
.flex-right {
  display: flex;
  justify-content: right;
}

.formContent {
  display: flex;
  height: 100%;
  flex-direction: column;

  .formContent_content {
    padding: 0px 24px;
    flex: 1 1 auto;
  }
}
</style>
