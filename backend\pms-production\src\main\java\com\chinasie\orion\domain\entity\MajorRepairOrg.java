package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;

/**
 * MajorRepairOrg Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@TableName(value = "pmsx_major_repair_org")
@ApiModel(value = "MajorRepairOrgEntity对象", description = "大修组织")
@Data

public class MajorRepairOrg extends  ObjectEntity  implements Serializable{

    /**
     * 责任人编码
     */
    @ApiModelProperty(value = "责任人编码")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;

//    @ApiModelProperty(value = "逻辑删除状态, 1代表正常")
//    @TableField(value = "logic_status")
//    private Integer logicStatus;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(value = "rsp_user_name")
    private String rspUserName;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 全路径
     */
    @ApiModelProperty(value = "全路径")
    @TableField(value = "chain_path")
    private String chainPath;

    /**
     * 大修伦次
     */
    @ApiModelProperty(value = "大修伦次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 组织类型：
     */
    @ApiModelProperty(value = "组织类型：")
    @TableField(value = "type")
    private String type;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @TableField(value = "name")
    private String name;

    /**
     * 组织层级
     */
    @ApiModelProperty(value = "组织层级")
    @TableField(value = "level")
    private Integer level;


    @ApiModelProperty(value = "组织code")
    @TableField(value = "code")
    private String code;
    @ApiModelProperty(value = "责任人ID")
    @TableField(value = "rsp_user_id")
    private String rspUserId;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;


    @ApiModelProperty(value = "层级类型")
    @TableField(value = "level_type")
    @ExcelProperty(value = "层级类型：repairRole(大修指挥部角色)，executionSpecialty(执行专业) ,managementRole(管理组-角色)，specialtyTeam(专业班组) ", index = 9)
    private String levelType;
//    @ApiModelProperty("大修组织类型:  role:角色,major:专业,major_manage:专业管理组,major_group:专业班组")
//    private String repairOrgType;

    @ApiModelProperty(value = "基地")
    @TableField(exist = false)
    private String baseCode;



    @ApiModelProperty(value = "大修计划开始时间")
    @TableField(exist = false)
    private Date majorBeginTime;


    @ApiModelProperty(value = "大修计划结束时间")
    @TableField(exist = false)
    private Date majorEndTime;
}
