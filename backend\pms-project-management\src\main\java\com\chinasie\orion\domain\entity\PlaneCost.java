package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * PlaneCost Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:59:04
 */
@TableName(value = "pmsx_plane_cost")
@ApiModel(value = "PlaneCostEntity对象", description = "机票费用")
@Data

public class PlaneCost extends  ObjectEntity  implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "data_year")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @TableField(value = "data_month")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "data_quarter")
    private Integer dataQuarter;

    /**
     * 机票单号
     */
    @ApiModelProperty(value = "机票单号")
    @TableField(value = "plane_no")
    private String planeNo;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_no")
    private String deptNo;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    @TableField(value = "task_no")
    private String taskNo;

    /**
     * 出发日期
     */
    @ApiModelProperty(value = "出发日期")
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    @TableField(value = "from_city")
    private String fromCity;

    /**
     * 到达地
     */
    @ApiModelProperty(value = "到达地")
    @TableField(value = "to_city")
    private String toCity;

    /**
     * 折扣机票金额
     */
    @ApiModelProperty(value = "折扣机票金额")
    @TableField(value = "discount_price")
    private BigDecimal discountPrice;

    /**
     * 全价机票金额
     */
    @ApiModelProperty(value = "全价机票金额")
    @TableField(value = "full_price")
    private BigDecimal fullPrice;

    /**
     * 机票折数
     */
    @ApiModelProperty(value = "机票折数")
    @TableField(value = "discount_count")
    private BigDecimal discountCount;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @TableField(value = "flow_status")
    private String flowStatus;

}
