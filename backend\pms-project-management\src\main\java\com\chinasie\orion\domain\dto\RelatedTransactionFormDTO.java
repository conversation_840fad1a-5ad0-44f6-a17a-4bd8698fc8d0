package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * RelatedTransactionForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:07:31
 */
@ApiModel(value = "RelatedTransactionFormDTO对象", description = "关联交易表单")
@Data
@ExcelIgnoreUnannotated
public class RelatedTransactionFormDTO extends ObjectDTO implements Serializable{

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTitle;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @ExcelProperty(value = "发起人 ", index = 1)
    private String startUser;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @ExcelProperty(value = "发起时间 ", index = 2)
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @ExcelProperty(value = "结束日期 ", index = 3)
    private Date endDate;

    /**
     * 表单状态
     */
    @ApiModelProperty(value = "表单状态")
    @ExcelProperty(value = "表单状态 ", index = 4)
    private String formStatus;




}
