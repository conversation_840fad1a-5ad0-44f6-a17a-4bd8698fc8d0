package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.JobManageDTO;
import com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO;
import com.chinasie.orion.domain.entity.JobHeightRiskCopy;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.vo.MajorRepairOrgSimpleVO;
import com.chinasie.orion.domain.vo.ProjectJobIdsVO;
import com.chinasie.orion.domain.vo.ProjectScheduleVO;
import com.chinasie.orion.domain.vo.count.ProjectJobCountVO;
import com.chinasie.orion.domain.vo.count.ProjectPackageCountVO;
import com.chinasie.orion.domain.vo.job.JobPackageStatusVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hpsf.Decimal;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:49
 * @description:
 */
@Mapper
public interface JobManageMapper extends OrionBaseMapper<JobManage> {

    @Select({
            "SELECT",
            "    jm.number AS number,",
            "    jm.phase AS phase",
            "FROM",
            "    pmsx_job_manage jm",
            "WHERE",
            "    jm.repair_round = #{repairRound}",
            "    AND jm.plan_scheme_id = #{planSchemeId}",
            "    AND jm.is_high_risk = 1"
    })
    List<JobManage> selectJobManageStatisticByConditions(
            @Param("repairRound") String repairRound,
            @Param("planSchemeId") String planSchemeId
    );

    long count(@Param("ids") List<String> ids);


    /**
     * 通过重大项目分组查询相对应的作业数量
     * @param ids ids
     * @return 结果
     */
    List<ProjectJobCountVO> countJobNum(@Param("ids") List<String> ids);

    /**
     * 通过重大项目分组查询相对应的作业包数量
     * @param projectIds ids
     * @return 结果
     */
    List<ProjectPackageCountVO> countJobPackageByProjectIds(@Param("projectIds") List<String> projectIds);

    /**
     * 通过重大项目分组查询相对应的作业包数量(已完成的)
     * @param projectIds ids
     * @return 结果
     */
    List<ProjectPackageCountVO> countJobPackageByProjectIdsFinish(@Param("projectIds") List<String> projectIds);

    /**
     * 根据重大项目id获取相对应的最新的进度
     * @param projectIds ids
     * @return 结果
     */
    List<ProjectScheduleVO> getNewestSchedule(@Param("projectIds") List<String> projectIds);

    /**
     * 批量插入关系表
     * @param projectId 项目id
     * @param jobIds 作业id列表
     * @return 结果
     */
    boolean insertBatch(@Param(value = "projectId") String projectId,@Param(value = "jobIds") List<String> jobIds);

    /**
     * 根据项目id删除关系
     * @param projectId 项目id
     * @return 结果
     */
    boolean deleteRelations(@Param("projectId") String projectId);

    /**
     * 根据项目id 删除进展关系以及进展
     */
    void deleteProgressByProjectId(@Param("projectIds") List<String> projectIds);

    /**
     * 根据项目id查询进展数据
     * @param ids ids
     * @return 结果
     */
    List<String> selectProgressByProjectId(@Param("projectIds") List<String> ids);

    List<String> getMaterialIdList(@Param("projectId") String projectId,@Param("keyword") String keyword);

    List<String> getPersonIdList(@Param("projectId") String projectId,@Param("keyword") String keyword);

    void updateJobManage(@Param("jobHeightRiskCopyList") List<JobHeightRiskCopy> jobHeightRiskCopyList);

    /**
     * 通过大修轮次查询相对应的重大项目关联的作业id
     * @param repairRound 大修轮次
     * @return 结果
     */
    List<String> selectJobIdsByRepairRound(@Param("repairRound") String repairRound,@Param("projectId") String projectId);


    /**
     * 通过重大项目id获取重大项目关联作业
     * @param id id
     * @return 结果
     */
    List<String> selectProjectJobIdsByProjectId(@Param("id") String id);

    /**
     * 通过项目id
     * @param projectId 参数
     * @return 结果
     */
    Integer selectCountProgressDate(@Param("projectId") String projectId, @Param("workDate") Date workDate);

    Integer selectCountProgressDateByRepairOrgId(@Param("repairOrgId") String repairOrgId, @Param("workDate") Date workDate);

    /**
     * 通过项目id和日期查询进度条数
     * @param projectId 项目id
     * @param workDate 工作日期
     * @param id 进度id
     * @return
     */
    Integer selectCountProgressDateJobId(@Param("projectId") String projectId,@Param("workDate") Date workDate,@Param("id") String id);

    Integer selectCountProgressDateJobIdByRepairOrgId(@Param("repairOrgId") String repairOrgId,@Param("workDate") Date workDate,@Param("id") String id);

    /**
     * 通过进展id删除进展
     * @param ids ids
     * @return 结果
     */
    Boolean deleteProgressByProgressId(@Param("ids") List<String> ids);


    /**
     * 通过进展id删除进展
     * @param ids ids
     * @return 结果
     */
    Boolean deleteOrgProgressByProgressId(@Param("ids") List<String> ids);

    /**
     * 获取今日没有填写进展的重大项目
     * @return
     */
    List<String> getNoProgressProject();


    boolean deleteProjectJobRelationByJobId(@Param("jobId") String jobId);


    /**
     * 通过jobId获取作业状态
     * @param jobIds 作业id
     * @return 结果
     */
    List<JobPackageStatusVO> selectProjectPackageStatusByJobIds(@Param("jobIds") List<String> jobIds);

    /**
     * 通过jobId查询关联的重大项目管理的负责人
     * @param jobId jobId
     * @return 结果
     */
    String selectImportantProjectRspUserByJobId(@Param("jobId") String jobId);

    /**
     * 通过作业id获取到相关重大项目管理下的所有锁业id
     * @param id 作业id
     * @return ids
     */
    List<ProjectJobIdsVO> selectIdsByProjectIdAndJobId(@Param("jobId") String id);

    /**
     * 通过班组获取相关作业
     * @param orgId 班组id
     * @return 结果
     */
    List<MajorRepairOrgJobSimpleDTO> getMajorRepairOrgSimpleVO(@Param("orgId") String orgId);


    List<JobManage> listByNumberList(@Param("jobNumberList") List<String> jobNumberList);


    void updateNumberById(@Param("number") String number,@Param("id") String id
            ,@Param("name") String name,@Param("nOrO") String nOrO,@Param("workCenter") String workCenter
            ,@Param("jobBase") String jobBase,@Param("beginTime") Date beginTime,@Param("endTime") Date endTime
            ,@Param("actualBeginTime") Date actualBeginTime,@Param("actualEndTime") Date actualEndTime,@Param("workduration") Integer workduration
            ,@Param("repairRound") String repairRound,@Param("phase") String phase,@Param("modifyId") String modifyId
        ,@Param("modifyTime") Date modifyTime,@Param("status") Integer status,@Param("logicStatus") Integer logicStatus
        ,@Param("rspUserId") String rspUserId,@Param("rspUserCode") String rspUserCode,@Param("rspDept") String rspDept
            , @Param("firstExecute")  String firstExecute,@Param("antiForfeignLevel") String antiForfeignLevel,@Param("isHighRisk") Boolean isHighRisk);
}


