package com.chinasie.orion.domain.dto.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/06/17:09
 * @description:
 */
@Data
public class SimpleSearchDTO implements Serializable {

    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("大修轮次")
    @NotEmpty(message = "所属大修伦次不能为空")
    private String majorRepairTurn;
    @ApiModelProperty("top数量")
    private long topSize;
    
    private List<String> idList;
}
