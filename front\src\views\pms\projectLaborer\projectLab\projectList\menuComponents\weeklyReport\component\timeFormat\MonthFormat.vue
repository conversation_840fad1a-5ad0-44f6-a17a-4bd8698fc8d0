<template>
  <div style="color: #7e7d7d">
    <Icon
      icon="fa-angle-double-left"
      size="12"
      class="mr10 cursorPointer action-btn"
      @click="subtractOneMonth(state.currentTime)"
    />
    <Icon
      icon="fa-angle-left"
      size="12"
      class="mr20 cursorPointer action-btn"
      @click="subtractOneYear(state.currentTime)"
    />
    <span class="mr20">
      {{ dayjs(state.currentTime).format('YYYY-MM') }}
    </span>
    <Icon
      icon="fa-angle-right"
      size="12"
      class="mr10 cursorPointer action-btn"
      @click="addOneYear(state.currentTime)"
    />
    <Icon
      icon="fa-angle-double-right"
      size="12"
      class="mr10 cursorPointer action-btn"
      @click="addOneMonth(state.currentTime)"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType, watch } from 'vue';
import { Button } from 'ant-design-vue';
import Icon from '/@/components/Icon/src/Icon.vue';
import dayjs from 'dayjs';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
defineExpose({ setTime });
const emits = defineEmits<{
  (e: 'timeChange', hah: string): void;
}>();
const props = defineProps({
  // trigger: {
  //   type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
  //   default: () => ['contextmenu'],
  // },
  // selectedKeys: {
  //   type: Array as PropType<string[]>,
  //   default: () => [],
  // },
});

const state = reactive({
  currentTime: dayjs().format('YYYY-MM'),
});
function setTime(val) {
  state.currentTime = val;
}

function addOneYear(inputDate) {
  let dateObject = dayjs(inputDate);
  let nextDay = dateObject.add(1, 'year');
  state.currentTime = nextDay.format('YYYY-MM');
}

function subtractOneYear(inputDate) {
  let dateObject = dayjs(inputDate);
  let prevMonth = dateObject.subtract(1, 'year');
  state.currentTime = prevMonth.format('YYYY-MM');
}

function addOneMonth(inputDate) {
  let dateObject = dayjs(inputDate);
  let nextMonth = dateObject.add(1, 'month');
  state.currentTime = nextMonth.format('YYYY-MM');
}

function subtractOneMonth(inputDate) {
  let dateObject = dayjs(inputDate);
  let prevMonth = dateObject.subtract(1, 'month');
  state.currentTime = prevMonth.format('YYYY-MM-DD');
}

watch(() => state.currentTime, () => {
  emits('timeChange', state.currentTime);
});
</script>

<style scoped lang="less">
.cursorPointer {
  cursor: pointer;
}
</style>
