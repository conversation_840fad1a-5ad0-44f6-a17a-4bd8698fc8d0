<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.github.xiaoymin</groupId>
    <artifactId>knife4j</artifactId>
    <version>4.5.0</version>
  </parent>
  <groupId>com.github.xiaoymin</groupId>
  <artifactId>knife4j-openapi2-ui</artifactId>
  <version>4.5.0</version>
  <name>knife4j-openapi2-ui</name>
  <description>前端Swagger文档ui模块-20221202-2350</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <properties>
    <destDir>${project.build.outputDirectory}/META-INF/resources/</destDir>
  </properties>
  <build>
    <resources>
      <resource>
        <targetPath>META-INF/resources/</targetPath>
        <directory>${project.basedir}/src/main/resources</directory>
      </resource>
    </resources>
  </build>
</project>
