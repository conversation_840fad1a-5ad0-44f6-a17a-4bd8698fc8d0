<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.sonatype.spice</groupId>
    <artifactId>spice-parent</artifactId>
    <version>12</version>
  </parent>
  
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.sonatype.plexus</groupId>
  <artifactId>plexus-cipher</artifactId>
  <url>http://spice.sonatype.org/${project.artifactId}</url>
  
  <name>Plexus Cipher: encryption/decryption Component</name>
  <version>1.4</version>
  
  <distributionManagement>
    <site>
      <id>sonatype.org-sites</id>
      <url>${spiceSiteBaseUrl}/${project.artifactId}</url>
    </site>
  </distributionManagement>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-maven-plugin</artifactId>
        <version>1.3.5</version>
        <executions>
          <execution>
            <goals>
              <goal>descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.4</source>
          <target>1.4</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  
  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-9-stable-1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
    </dependency>
  </dependencies>

  <scm>
    <connection>scm:svn:http://svn.sonatype.org/spice/tags/plexus-cipher-1.4</connection>
    <developerConnection>scm:svn:https://svn.sonatype.org/spice/tags/plexus-cipher-1.4</developerConnection>
    <url>http://svn.sonatype.org/spice/tags/plexus-cipher-1.4</url>
  </scm>
</project>
