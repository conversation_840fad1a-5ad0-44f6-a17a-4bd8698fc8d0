<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-build-configuration-parent</artifactId>
        <version>12.1.12.Final</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>infinispan-bom</artifactId>
    <packaging>pom</packaging>

    <name>Infinispan BOM</name>
    <description>Infinispan BOM module</description>
    <url>http://www.infinispan.org</url>
    <organization>
        <name>JBoss, a division of Red Hat</name>
        <url>http://www.jboss.org</url>
    </organization>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>placeholder</id>
            <name>See http://www.infinispan.org for a complete list of contributors</name>
        </developer>
    </developers>
    <mailingLists>
        <mailingList>
            <name>Infinispan Issues</name>
            <subscribe>https://lists.jboss.org/mailman/listinfo/infinispan-issues</subscribe>
            <unsubscribe>https://lists.jboss.org/mailman/listinfo/infinispan-issues</unsubscribe>
            <post><EMAIL></post>
            <archive>http://lists.jboss.org/pipermail/infinispan-issues/</archive>
        </mailingList>
        <mailingList>
            <name>Infinispan Developers</name>
            <subscribe>https://lists.jboss.org/mailman/listinfo/infinispan-dev</subscribe>
            <unsubscribe>https://lists.jboss.org/mailman/listinfo/infinispan-dev</unsubscribe>
            <post><EMAIL></post>
            <archive>http://lists.jboss.org/pipermail/infinispan-dev/</archive>
        </mailingList>
    </mailingLists>
    <scm>
        <connection>scm:git:**************:infinispan/infinispan.git</connection>
        <developerConnection>scm:git:**************:infinispan/infinispan.git</developerConnection>
        <url>https://github.com/infinispan/infinispan</url>
    </scm>
    <issueManagement>
        <system>jira</system>
        <url>https://issues.jboss.org/browse/ISPN</url>
    </issueManagement>

    <distributionManagement>
        <repository>
            <id>${jboss.releases.repo.id}</id>
            <name>JBoss Release Repository</name>
            <url>${jboss.releases.repo.url}</url>
        </repository>
        <snapshotRepository>
            <id>${jboss.snapshots.repo.id}</id>
            <name>JBoss Snapshot Repository</name>
            <url>${jboss.snapshots.repo.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <!-- Java source/target version -->
        <version.java>1.8</version.java>

        <versionx.org.infinispan.infinispan-console>${version.console}</versionx.org.infinispan.infinispan-console>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-api</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-jdbc</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-jpa</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-remote</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-rocksdb</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-common</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-embedded</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-remote</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-checkstyle</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cli-client</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-client-hotrod</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-client-rest</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-key-value-store-client</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-clustered-counter</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-clustered-lock</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-commons</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-commons-test</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-component-annotations</artifactId>
                <version>${version.infinispan}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-component-processor</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-core</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jboss-marshalling</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-extended-statistics</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-hibernate-cache-commons</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-hibernate-cache-spi</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-hibernate-cache-v51</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-hibernate-cache-v53</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache-commons</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache-remote</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-console</artifactId>
                <version>${versionx.org.infinispan.infinispan-console}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-marshaller-kryo</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-marshaller-kryo-bundle</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-marshaller-protostuff</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-marshaller-protostuff-bundle</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-multimap</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-objectfilter</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-persistence-soft-index</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-query-core</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-query</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-query-dsl</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-remote-query-client</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-remote-query-server</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-scripting</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-core</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-hotrod</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-memcached</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-rest</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <!-- TODO should be here? -->
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-router</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-runtime</artifactId>
                <version>${version.infinispan}</version>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-testdriver-core</artifactId>
                <version>${version.infinispan}</version>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-testdriver-junit4</artifactId>
                <version>${version.infinispan}</version>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-testdriver-junit5</artifactId>
                <version>${version.infinispan}</version>
            </dependency>

            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring5-common</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring5-embedded</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring5-remote</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring-boot-starter-embedded</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring-boot-starter-remote</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tasks</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tasks-api</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tools</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-anchored-keys</artifactId>
                <version>${version.infinispan}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan.protostream</groupId>
                <artifactId>protostream</artifactId>
                <version>${version.protostream}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan.protostream</groupId>
                <artifactId>protostream-types</artifactId>
                <version>${version.protostream}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan.protostream</groupId>
                <artifactId>protostream-processor</artifactId>
                <version>${version.protostream}</version>
                <!-- compile-only dependency -->
                <scope>provided</scope>
            </dependency>
           <dependency>
              <groupId>org.infinispan</groupId>
              <artifactId>infinispan-cloudevents-integration</artifactId>
              <version>${version.infinispan}</version>
           </dependency>
        </dependencies>
    </dependencyManagement>
</project>
