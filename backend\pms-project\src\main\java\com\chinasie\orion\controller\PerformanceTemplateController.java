package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.PerformanceTemplateDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.entity.PerformanceTemplate;
import com.chinasie.orion.domain.vo.PerformanceTemplateVO;
import com.chinasie.orion.service.PerformanceTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * PerformanceTemplate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@RestController
@RequestMapping("/performanceTemplate")
@Api(tags = "项目绩效模版")
public class PerformanceTemplateController {

    @Autowired
    private PerformanceTemplateService performanceTemplateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情，传主键，会加载某个绩效类型下面的指标数据信息")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)

    public ResponseDTO<PerformanceTemplateVO> detail(@PathVariable(value = "id") String id) throws Exception {
        PerformanceTemplateVO rsp = performanceTemplateService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param performanceTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<PerformanceTemplateVO> create(@RequestBody PerformanceTemplateDTO performanceTemplateDTO) throws Exception {
        PerformanceTemplateVO rsp = performanceTemplateService.create(performanceTemplateDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<List<PerformanceTemplate>> listAll(@RequestBody PerformanceTemplateDTO performanceTemplateDTO) throws Exception {
        List<PerformanceTemplate> rsp = performanceTemplateService.listAll(performanceTemplateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param performanceTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> edit(@RequestBody PerformanceTemplateDTO performanceTemplateDTO) throws Exception {
        Boolean rsp = performanceTemplateService.edit(performanceTemplateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = performanceTemplateService.remove(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 分页
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加指标时 过滤已经保存再库中的指标")
    @RequestMapping(value = "/filterIndicatorLibrary/templateId/{templateId}", method = RequestMethod.POST)
    public ResponseDTO<List<IndicatorLibrary>> filterIndicatorLibrary(@PathVariable(value = "templateId") String templateId) throws Exception {
        List<IndicatorLibrary> rsp = performanceTemplateService.filterIndicatorLibrary(templateId);
        return new ResponseDTO<>(rsp);
    }

}
