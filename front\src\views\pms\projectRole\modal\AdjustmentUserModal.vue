<template>
  <BasicModal
    v-bind="$attrs"
    :width="500"
    :min-height="80"
    :use-wrapper="false"
    :can-fullscreen="false"
    title="调整角色"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <div class="flex flex-ac p20">
      <div>所属角色：</div>
      <div class="flex-f1">
        <Cascader
          v-model:value="selectValue"
          :options="roleList"
          :field-names="{ label: 'name', value: 'id' }"
          :show-search="showSearch"
          :autofocus="true"
          size="large"
          change-on-select
          expand-trigger="hover"
          class="w-full"
          placeholder="请选择"
        />
      </div>
    </div>
    <!--footer-->
    <template #footer>
      <ModalFooterButtons
        :button-loading="buttonLoading"
        :is-continue="false"
        :ok-disabled="okDisabledStatus"
        @reset="reset"
        @cancel="cancel"
        @ok="ok"
      />
    </template>
  </BasicModal>
</template>

<script lang="ts">
import {
  computed, defineComponent, reactive, ref, toRefs,
} from 'vue';
import { BasicModal, useModalInner, ModalFooterButtons } from 'lyra-component-vue3';
import { message, Cascader } from 'ant-design-vue';

export default defineComponent({
  name: 'AdjustmentUserModal',
  components: {
    BasicModal,
    ModalFooterButtons,
    Cascader,
  },
  props: {
    roleList: {
      type: Array,
      default: () => [],
    },
    onOk: {
      type: Function,
      default: null,
    },
  },
  setup(props) {
    const state = reactive({
      selectValue: [],
    });

    const buttonLoading = ref(false);
    // useModal
    const [modalRegister, { closeModal }] = useModalInner((data) => {});

    return {
      ...toRefs(state),
      modalRegister,
      okDisabledStatus: computed(() => {
        if (state.selectValue.length) {
          return false;
        }
        return true;
      }),
      showSearch: {
        filter(inputValue, path) {
          return path.some((option) => option.name.indexOf(inputValue) > -1);
        },
      },
      buttonLoading,
      visibleChange(visible) {
        if (!visible) {
          state.selectValue = [];
        }
      },
      cancel() {
        closeModal();
      },
      reset() {},
      ok() {
        buttonLoading.value = true;
        props.onOk
            && props
              .onOk(state.selectValue[state.selectValue.length - 1])
              .then(() => {
                message.success('调整成功');
                closeModal();
              })
              .finally(() => {
                buttonLoading.value = false;
              });
      },
    };
  },
});
</script>

<style scoped lang="less"></style>
