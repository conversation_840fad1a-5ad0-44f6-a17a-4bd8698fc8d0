package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.YearInvestmentSchemeStatus;
import com.chinasie.orion.domain.dto.investmentschemeReport.SearchReportDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.YearInvestmentScheme;
import com.chinasie.orion.domain.entity.YearInvestmentSchemeMonthFeedback;
import com.chinasie.orion.domain.vo.MonthInvestmentSchemeVO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeMonthFeedbackVO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import com.chinasie.orion.domain.vo.investmentschemeReport.*;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.YearInvestmentSchemeMonthFeedbackRepository;
import com.chinasie.orion.repository.YearInvestmentSchemeRepository;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.OrgLeaderVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.service.InvestmentSchemeReportService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.YearInvestmentSchemeMonthFeedbackService;
import com.chinasie.orion.service.YearInvestmentSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InvestmentSchemeReportServiceImpl implements InvestmentSchemeReportService {

    @Resource
    private YearInvestmentSchemeService yearInvestmentSchemeService;

    @Resource
    private YearInvestmentSchemeRepository yearInvestmentSchemeRepository;

    @Autowired
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Resource
    private YearInvestmentSchemeMonthFeedbackService yearInvestmentSchemeMonthFeedbackService;

    @Resource
    private YearInvestmentSchemeMonthFeedbackRepository yearInvestmentSchemeMonthFeedbackRepository;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private ProjectService projectService;

    @Resource
    private DeptRedisHelper deptRedisHelper;

    private Boolean defaultCondition(YearInvestmentSchemeReportVO query, LambdaQueryWrapperX<YearInvestmentScheme> condition) throws Exception {
        if (Objects.isNull(query)) {
            query=new YearInvestmentSchemeReportVO();
        }
        //String companyName = query.getCompanyName();
        LambdaQueryWrapperX<Project> projectCondition = new LambdaQueryWrapperX<>(Project.class);
//        if (StrUtil.isNotBlank(companyName)) {
//            if (companyName.contains(",")) {
//                projectCondition.and(projectOrionWrapper -> {
//                    String[] companyArray = companyName.split(",");
//                    List<String> companyList = Arrays.asList(companyArray);
//                    projectOrionWrapper.like(Project::getResOrg, companyList.get(0));
//                    for (int i = 1; i < companyList.size(); i++) {
//                        projectOrionWrapper.or().like(Project::getResOrg, companyList.get(i));
//                    }
//                });
//            } else {
//                projectCondition.like(Project::getResOrg, companyName);
//            }
//        }
        String projectNumber = query.getProjectNumber();
        if (StrUtil.isNotBlank(projectNumber)) {
            projectCondition.like(Project::getNumber, projectNumber);
        }
        String projectName = query.getProjectName();
        if (StrUtil.isNotBlank(projectName)) {
            projectCondition.like(Project::getName, projectName);
        }
        String rspDeptId = query.getRspDeptId();
        if (StrUtil.isNotBlank(rspDeptId)) {
            projectCondition.like(Project::getResDept, rspDeptId);
        }
        Integer projectStatusCode = query.getProjectStatusCode();
        if (Objects.nonNull(projectStatusCode)) {
            projectCondition.eq(Project::getStatus, projectStatusCode);
        }
        String name = query.getName();
        if (StrUtil.isNotBlank(name)) {
            projectCondition.and(sub -> {
                sub.like(Project::getName, name).or().like(Project::getNumber, name);
            });
        }
        List<Project> projects = projectService.list(projectCondition);
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIds)) {
            return false;
        }
        condition.in(YearInvestmentScheme::getProjectId, projectIds);
        String yearName = query.getYearName();
        if (StrUtil.isNotBlank(yearName)) {
            if (yearName.contains("-")) {
                String[] split = yearName.split("-");
                condition.between(YearInvestmentScheme::getYearName, split[0], split[1]);
            }
        }
        Integer status = query.getStatus();
        if (Objects.nonNull(status)) {
            condition.eq(YearInvestmentScheme::getStatus, status);
        }

        if (StrUtil.isNotBlank(name)) {
            condition.and(sub -> {
                sub.like(YearInvestmentScheme::getName, name).or().like(YearInvestmentScheme::getNumber, name);
            });
        }
        return true;
    }


    private Boolean defaultCondition2(SearchReportDTO query, LambdaQueryWrapperX<YearInvestmentScheme> condition) throws Exception {
        if (Objects.isNull(query)) {
            return false;
        }
        String companyName = query.getCompanyName();
        LambdaQueryWrapperX<Project> projectCondition = new LambdaQueryWrapperX<>(Project.class);
//        if (StrUtil.isNotBlank(companyName)) {
//            if (companyName.contains(",")) {
//                projectCondition.and(projectOrionWrapper -> {
//                    String[] companyArray = companyName.split(",");
//                    List<String> companyList = Arrays.asList(companyArray);
//                    projectOrionWrapper.like(Project::getResOrg, companyList.get(0));
//                    for (int i = 1; i < companyList.size(); i++) {
//                        projectOrionWrapper.or().like(Project::getResOrg, companyList.get(i));
//                    }
//                });
//            } else {
//                projectCondition.like(Project::getResOrg, companyName);
//            }
//        }
        String projectNumber = query.getProjectNumber();
        if (StrUtil.isNotBlank(projectNumber)) {
            projectCondition.like(Project::getNumber, projectNumber);
        }
        String projectName = query.getProjectName();
        if (StrUtil.isNotBlank(projectName)) {
            projectCondition.like(Project::getName, projectName);
        }
        String rspDeptId = query.getRspDeptId();
        if (StrUtil.isNotBlank(rspDeptId)) {
            projectCondition.like(Project::getResDept, rspDeptId);
        }
        Integer projectStatusCode = query.getProjectStatusCode();
        if (Objects.nonNull(projectStatusCode)) {
            projectCondition.eq(Project::getStatus, projectStatusCode);
        }
        String name = query.getName();
        if (StrUtil.isNotBlank(name)) {
            projectCondition.and(sub -> {
                sub.like(Project::getName, name).or().like(Project::getNumber, name);
            });
        }
        List<Project> projects = projectService.list(projectCondition);
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIds)) {
            return false;
        }
        condition.in(YearInvestmentScheme::getProjectId, projectIds);
        String yearName = query.getYearName();
        if (StrUtil.isNotBlank(yearName)) {
            if (yearName.contains("-")) {
                String[] split = yearName.split("-");
                condition.between(YearInvestmentScheme::getYearName, split[0], split[1]);
            }
        }
        Integer status = query.getStatus();
        if (Objects.nonNull(status)) {
            condition.eq(YearInvestmentScheme::getStatus, status);
        }

        if (StrUtil.isNotBlank(name)) {
            condition.and(sub -> {
                sub.like(YearInvestmentScheme::getName, name).or().like(YearInvestmentScheme::getNumber, name);
            });
        }
        return true;
    }


    @Override
    public Page<YearInvestmentSchemeReportVO> pageCreate(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        YearInvestmentSchemeReportVO query = pageRequest.getQuery();
        Page<YearInvestmentScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        condition.isNull(YearInvestmentScheme::getOldId);
        Boolean aBoolean = defaultCondition(query, condition);
        if (!aBoolean) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        condition.orderByDesc(YearInvestmentScheme::getModifyTime);
        dataPermissionForYearInvestmentScheme(condition);
        PageResult<YearInvestmentScheme> page = yearInvestmentSchemeRepository.selectPage(realPageRequest, condition);
        List<YearInvestmentScheme> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return new Page<>(page.getPageNum(), page.getPageSize(), 0, new ArrayList<>());
        }
        //处理数据
        List<YearInvestmentSchemeReportVO> resultContent = new ArrayList<>();
        for (YearInvestmentScheme c : content) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(c.getId(), null);
            YearInvestmentSchemeReportVO tmp = BeanCopyUtils.convertTo(detail, YearInvestmentSchemeReportVO::new);
            resultContent.add(tmp);
        }

        return new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize(), resultContent);
    }

    @Override
    public Page<YearInvestmentSchemeReportVO> pageChange(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        Page<YearInvestmentScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        condition.isNotNull(YearInvestmentScheme::getOldId);
        Boolean aBoolean = defaultCondition(pageRequest.getQuery(), condition);
        if (!aBoolean) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        condition.orderByDesc(YearInvestmentScheme::getModifyTime);
        dataPermissionForYearInvestmentScheme(condition);
        PageResult<YearInvestmentScheme> page = yearInvestmentSchemeRepository.selectPage(realPageRequest, condition);
        List<YearInvestmentScheme> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return new Page<>(page.getPageNum(), page.getPageSize(), 0, new ArrayList<>());
        }

        //处理数据
        List<YearInvestmentSchemeReportVO> resultContent = new ArrayList<>();
        for (YearInvestmentScheme c : content) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(c.getId(), null);
            YearInvestmentSchemeVO oldDetail = yearInvestmentSchemeService.detail(c.getOldId(), null);
            //构造数据
            YearInvestmentSchemeReportVO tmp = BeanCopyUtils.convertTo(detail, YearInvestmentSchemeReportVO::new);
            tmp.setOld(oldDetail);
            resultContent.add(tmp);
        }

        return new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize(), resultContent);
    }

    @Override
    public Page<YearInvestmentSchemeMonthFeedbackReportVO> pageMonthFeedback(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        YearInvestmentSchemeReportVO query = pageRequest.getQuery();
        if (Objects.isNull(query)) {
            query = new YearInvestmentSchemeReportVO();
        }
        String companyName = query.getCompanyName();
        LambdaQueryWrapperX<Project> projectCondition = new LambdaQueryWrapperX<>(Project.class);
//        if (StrUtil.isNotBlank(companyName)) {
//            if (companyName.contains(",")) {
//                projectCondition.and(projectOrionWrapper -> {
//                    String[] companyArray = companyName.split(",");
//                    List<String> companyList = Arrays.asList(companyArray);
//                    projectOrionWrapper.like(Project::getResOrg, companyList.get(0));
//                    for (int i = 1; i < companyList.size(); i++) {
//                        projectOrionWrapper.or().like(Project::getResOrg, companyList.get(i));
//                    }
//                });
//            } else {
//                projectCondition.like(Project::getResOrg, companyName);
//            }
//        }
        String projectNumber = query.getProjectNumber();
        if (StrUtil.isNotBlank(projectNumber)) {
            projectCondition.like(Project::getNumber, projectNumber);
        }
        String projectName = query.getProjectName();
        if (StrUtil.isNotBlank(projectName)) {
            projectCondition.like(Project::getName, projectName);
        }
        String rspDeptId = query.getRspDeptId();
        if (StrUtil.isNotBlank(rspDeptId)) {
            projectCondition.like(Project::getResDept, rspDeptId);
        }
        List<String> rspDeptIdList = query.getRspDeptIdList();
        if (!CollectionUtils.isEmpty(rspDeptIdList)) {
            projectCondition.in(Project::getResDept, rspDeptIdList);
        }
        Integer projectStatusCode = query.getProjectStatusCode();
        if (Objects.nonNull(projectStatusCode)) {
            projectCondition.eq(Project::getStatus, projectStatusCode);
        }

        String name = query.getName();
        if (StrUtil.isNotBlank(name)) {
            projectCondition.and(sub -> {
                sub.like(Project::getName, name).or().like(Project::getNumber, name);
            });
        }

        List<Project> projects = projectService.list(projectCondition);
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());

        LambdaQueryWrapperX<YearInvestmentScheme> yearCondition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        if (CollectionUtils.isEmpty(projectIds)) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        yearCondition.in(YearInvestmentScheme::getProjectId, projectIds);
        String yearName = query.getYearName();
        if (StrUtil.isNotBlank(yearName)) {
            if (yearName.contains("-")) {
                String[] split = yearName.split("-");
                yearCondition.between(YearInvestmentScheme::getYearName, split[0], split[1]);
            }
        }

        List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(yearCondition);
        if(CollectionUtils.isEmpty(yearInvestmentSchemes)){
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        List<String> yearIds = yearInvestmentSchemes.stream().map(YearInvestmentScheme::getId).collect(Collectors.toList());

        LambdaQueryWrapperX<YearInvestmentSchemeMonthFeedback> condition = new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class);
        if (!CollectionUtils.isEmpty(yearIds)) {
            condition.in(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearIds);
        }


        if (StrUtil.isNotBlank(name)) {
            condition.and(sub -> {
                sub.like(YearInvestmentSchemeMonthFeedback::getName, name).or().like(YearInvestmentSchemeMonthFeedback::getNumber, name);
            });
        }

        Integer status = query.getStatus();
        if (Objects.nonNull(status)) {
            condition.eq(YearInvestmentSchemeMonthFeedback::getStatus, status);
        }

        String month = query.getMonth();
        if (StrUtil.isNotBlank(month)) {
            condition.eq(YearInvestmentSchemeMonthFeedback::getMonth, month + "月");
        }
        Page<YearInvestmentSchemeMonthFeedback> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        dataPermissionForYearInvestmentSchemeMonthFeedback(condition);
        condition.orderByDesc(YearInvestmentSchemeMonthFeedback::getModifyTime);
        PageResult<YearInvestmentSchemeMonthFeedback> page = yearInvestmentSchemeMonthFeedbackRepository.selectPage(realPageRequest, condition);
        List<YearInvestmentSchemeMonthFeedback> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return new Page<>(page.getPageNum(), page.getPageSize(), 0, new ArrayList<>());
        }

        List<String> yearInvestmentIds = content.stream().map(YearInvestmentSchemeMonthFeedback::getYearInvestmentId).distinct().collect(Collectors.toList());

        Map<String, YearInvestmentSchemeVO> yearInvestmentSchemeVOMap = new HashMap<>();

        for (String yId : yearInvestmentIds) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(yId, null);
            yearInvestmentSchemeVOMap.put(yId, detail);
        }


        //处理数据
        List<YearInvestmentSchemeMonthFeedbackReportVO> resultContent = new ArrayList<>();
        for (YearInvestmentSchemeMonthFeedback c : content) {
            YearInvestmentSchemeMonthFeedbackVO detail = yearInvestmentSchemeMonthFeedbackService.detail(c.getId(), null);
            //构造数据
            YearInvestmentSchemeMonthFeedbackReportVO tmp = BeanCopyUtils.convertTo(detail, YearInvestmentSchemeMonthFeedbackReportVO::new);
            tmp.setYearInvestmentSchemeVO(yearInvestmentSchemeVOMap.get(c.getYearInvestmentId()));
            resultContent.add(tmp);
        }

        return new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize(), resultContent);
    }

    @Override
    public Page<YearInvestmentSchemeReportVO> pageTotalDo(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        String sql = "SELECT t1.id  FROM pms_year_investment_scheme t1 INNER JOIN ( SELECT MAX( create_time ) AS create_time, investment_id, year_name FROM pms_year_investment_scheme WHERE logic_status = 1 GROUP BY investment_id, year_name ) t2 ON t1.create_time = t2.create_time AND t1.year_name = t2.year_name AND t1.investment_id = t2.investment_id WHERE logic_status = 1";
        YearInvestmentSchemeReportVO query = pageRequest.getQuery();
        if(!ObjectUtils.isEmpty(query) && query.isPointProject()){
            sql ="SELECT\n" +
                    "\tt1.id \n" +
                    "FROM\n" +
                    "\t(select year_name,investment_id,id,logic_status,create_time from pms_year_investment_scheme y where (y.installation + y.device+y.architecture+y.other) > 1000 and logic_status = 1) as t1\n" +
                    "\tINNER JOIN ( SELECT MAX( create_time ) AS create_time, investment_id, year_name FROM pms_year_investment_scheme WHERE logic_status = 1 GROUP BY investment_id, year_name ) t2 ON t1.create_time = t2.create_time \n" +
                    "\tAND t1.year_name = t2.year_name \n" +
                    "\tAND t1.investment_id = t2.investment_id \n" +
                    "WHERE\n" +
                    "\tt1.logic_status = 1;\n" +
                    "\t";
        }
        List<String> yearInvestmentSchemeIds = namedParameterJdbcTemplate.queryForList(sql, new HashMap<>(), String.class);
        if (CollectionUtils.isEmpty(yearInvestmentSchemeIds)) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        Page<YearInvestmentScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        Boolean aBoolean = defaultCondition(pageRequest.getQuery(), condition);
        if (!aBoolean) {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        }
        condition.eq(YearInvestmentScheme::getStatus, YearInvestmentSchemeStatus.GIVE.getCode());
        condition.in(YearInvestmentScheme::getId, yearInvestmentSchemeIds);
        condition.orderByDesc(YearInvestmentScheme::getSerialNumber).orderByAsc(YearInvestmentScheme::getYearName);
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());

        dataPermissionForYearInvestmentScheme(condition);
        PageResult<YearInvestmentScheme> page = yearInvestmentSchemeRepository.selectPage(realPageRequest, condition);
        List<YearInvestmentScheme> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {
            return new Page<>(page.getPageNum(), page.getPageSize(), 0, new ArrayList<>());
        }
        List<YearInvestmentSchemeReportVO> resultContent = new ArrayList<>();
        for (YearInvestmentScheme c : content) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(c.getId(), null);
            YearInvestmentSchemeReportVO tmp = BeanCopyUtils.convertTo(detail, YearInvestmentSchemeReportVO::new);
            resultContent.add(tmp);
        }
        return new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize(), resultContent);
    }

    @Override
    public void exportCreateByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        String excelFileName = "投资计划申报";
        /*
         * 当isEmpty=true，表示导出一个空数据的Excel
         * */
        Boolean isEmpty = searchDTO.getIsEmpty();
        if (isEmpty) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportCreateByExcelVO.class, new ArrayList<>());
            return;
        }

        List<YearInvestmentScheme> db;
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            condition.in(YearInvestmentScheme::getId, ids);
            condition.orderByDesc(YearInvestmentScheme::getSerialNumber).orderByAsc(YearInvestmentScheme::getYearName);
            db = yearInvestmentSchemeService.list(condition);
        } else {
            LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            condition.isNull(YearInvestmentScheme::getOldId);
            Boolean aBoolean = defaultCondition2(searchDTO, condition);
            if (!aBoolean) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_EXCEL, "数据不存在");
            }
            dataPermissionForYearInvestmentScheme(condition);
            condition.orderByDesc(YearInvestmentScheme::getSerialNumber).orderByAsc(YearInvestmentScheme::getYearName);
            db = yearInvestmentSchemeService.list(condition);
        }

        if (CollectionUtil.isEmpty(db)) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportCreateByExcelVO.class, new ArrayList<>());
            return;
        }

        List<ExportCreateByExcelVO> result = new ArrayList<>();
        AtomicInteger order = new AtomicInteger(1);
        for (YearInvestmentScheme yis : db) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(yis.getId(), null);
            ExportCreateByExcelVO tmp = BeanCopyUtils.convertTo(detail, ExportCreateByExcelVO::new);
            tmp.setStatusName(detail.getDataStatus().getName());
            tmp.setCloseFlag(detail.getCloseFlag() ? "否":"是");
            tmp.setLastYearComplete(NumberUtil.roundStr(detail.getLastYearComplete().toString(), 2));
            tmp.setOrder(String.valueOf(order.getAndIncrement()));
            tmp.setArchitecture(NumberUtil.roundStr(detail.getArchitecture().toString(), 2));
            tmp.setInstallation(NumberUtil.roundStr(detail.getInstallation().toString(), 2));
            tmp.setDevice(NumberUtil.roundStr(detail.getDevice().toString(), 2));
            tmp.setOther(NumberUtil.roundStr(detail.getOther().toString(), 2));
            Map<String, BigDecimal> monthMap = detail.getMonthInvestmentSchemes().stream().collect(Collectors.toMap(MonthInvestmentSchemeVO::getName, MonthInvestmentSchemeVO::getPredicate));
            if(ObjectUtil.isNotEmpty(monthMap)) {
                tmp.setMonth1(NumberUtil.roundStr(monthMap.get("1月").toString(), 2));
                tmp.setMonth2(NumberUtil.roundStr(monthMap.get("1-2月").toString(), 2));
                tmp.setMonth3(NumberUtil.roundStr(monthMap.get("1-3月").toString(), 2));
                tmp.setMonth4(NumberUtil.roundStr(monthMap.get("1-4月").toString(), 2));
                tmp.setMonth5(NumberUtil.roundStr(monthMap.get("1-5月").toString(), 2));
                tmp.setMonth6(NumberUtil.roundStr(monthMap.get("1-6月").toString(), 2));
                tmp.setMonth7(NumberUtil.roundStr(monthMap.get("1-7月").toString(), 2));
                tmp.setMonth8(NumberUtil.roundStr(monthMap.get("1-8月").toString(), 2));
                tmp.setMonth9(NumberUtil.roundStr(monthMap.get("1-9月").toString(), 2));
                tmp.setMonth10(NumberUtil.roundStr(monthMap.get("1-10月").toString(), 2));
                tmp.setMonth11(NumberUtil.roundStr(monthMap.get("1-11月").toString(), 2));
                tmp.setMonth12(NumberUtil.roundStr(monthMap.get("1-12月").toString(), 2));
            }
            tmp.setNextOneYear(NumberUtil.roundStr(detail.getNextOneYear().toString(), 2));
            tmp.setNextTwoYear(NumberUtil.roundStr(detail.getNextTwoYear().toString(), 2));
            tmp.setNextThreeYear(NumberUtil.roundStr(detail.getNextThreeYear().toString(), 2));
            tmp.setNextFourYear(NumberUtil.roundStr(detail.getNextFourYear().toString(), 2));
            tmp.setNextFiveYear(NumberUtil.roundStr(detail.getNextFiveYear().toString(), 2));

            tmp.setOverallBudget(new BigDecimal(StringUtils.hasText(tmp.getOverallBudget())?tmp.getOverallBudget() : "0").divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setOverallReality(new BigDecimal(StringUtils.hasText(tmp.getOverallReality())?tmp.getOverallReality() : "0").divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setProjectAmount(new BigDecimal(StringUtils.hasText(tmp.getProjectAmount())?tmp.getProjectAmount() : "0").divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setContractAmount(new BigDecimal(StringUtils.hasText(tmp.getContractAmount())?tmp.getContractAmount() : "0").divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            result.add(tmp);
        }
        //转换
        String fileName = String.format("%s.xlsx", excelFileName);
        ExcelUtils.write(response, fileName, excelFileName, ExportCreateByExcelVO.class, result);
    }

    @Override
    public void exportChangeByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        String excelFileName = "投资计划调整";
        Boolean isEmpty = searchDTO.getIsEmpty();
        if (isEmpty) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportChangeByExcelVO.class, new ArrayList<>());
            return;
        }

        List<YearInvestmentScheme> db = new ArrayList<>();
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            condition.in(YearInvestmentScheme::getId, ids);
            condition.orderByDesc(YearInvestmentScheme::getModifyTime);
            db = yearInvestmentSchemeService.list(condition);
        } else {
            LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            condition.isNotNull(YearInvestmentScheme::getOldId);
            Boolean aBoolean = defaultCondition2(searchDTO, condition);
            if (!aBoolean) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_EXCEL, "数据不存在");
            }
            dataPermissionForYearInvestmentScheme(condition);
            condition.orderByDesc(YearInvestmentScheme::getModifyTime);
            db = yearInvestmentSchemeService.list(condition);
        }

        if (CollectionUtil.isEmpty(db)) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportChangeByExcelVO.class, new ArrayList<>());
            return;
        }

        //处理数据
        List<ExportChangeByExcelVO> result = new ArrayList<>();
        AtomicInteger order = new AtomicInteger(1);
        for (YearInvestmentScheme c : db) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(c.getId(), null);
            YearInvestmentSchemeVO oldDetail = yearInvestmentSchemeService.detail(c.getOldId(), null);
            //构造数据
            ExportChangeByExcelVO tmp = BeanCopyUtils.convertTo(detail, ExportChangeByExcelVO::new);
            tmp.setOrder(String.valueOf(order.getAndIncrement()));
            tmp.setStatusName(detail.getDataStatus().getName());

            tmp.setArchitecture(NumberUtil.roundStr(oldDetail.getArchitecture().toString(), 2));
            tmp.setInstallation(NumberUtil.roundStr(oldDetail.getInstallation().toString(), 2));
            tmp.setDevice(NumberUtil.roundStr(oldDetail.getDevice().toString(), 2));
            tmp.setOther(NumberUtil.roundStr(oldDetail.getOther().toString(), 2));

            tmp.setArchitectureChange(NumberUtil.roundStr(detail.getArchitecture().toString(), 2));
            tmp.setInstallationChange(NumberUtil.roundStr(detail.getInstallation().toString(), 2));
            tmp.setDeviceChange(NumberUtil.roundStr(detail.getDevice().toString(), 2));
            tmp.setOtherChange(NumberUtil.roundStr(detail.getOther().toString(), 2));

            BigDecimal changeMoney = new BigDecimal(tmp.getTotalChange()).subtract(new BigDecimal(tmp.getTotal()));
            tmp.setChangeMoney(NumberUtil.roundStr(changeMoney.toString(), 2));

            Map<String, BigDecimal> monthOldMap = oldDetail.getMonthInvestmentSchemes().stream().collect(Collectors.toMap(MonthInvestmentSchemeVO::getName, MonthInvestmentSchemeVO::getPredicate));
            tmp.setMonth1(NumberUtil.roundStr(monthOldMap.get("1月").toString(), 2));
            tmp.setMonth2(NumberUtil.roundStr(monthOldMap.get("1-2月").toString(), 2));
            tmp.setMonth3(NumberUtil.roundStr(monthOldMap.get("1-3月").toString(), 2));
            tmp.setMonth4(NumberUtil.roundStr(monthOldMap.get("1-4月").toString(), 2));
            tmp.setMonth5(NumberUtil.roundStr(monthOldMap.get("1-5月").toString(), 2));
            tmp.setMonth6(NumberUtil.roundStr(monthOldMap.get("1-6月").toString(), 2));
            tmp.setMonth7(NumberUtil.roundStr(monthOldMap.get("1-7月").toString(), 2));
            tmp.setMonth8(NumberUtil.roundStr(monthOldMap.get("1-8月").toString(), 2));
            tmp.setMonth9(NumberUtil.roundStr(monthOldMap.get("1-9月").toString(), 2));
            tmp.setMonth10(NumberUtil.roundStr(monthOldMap.get("1-10月").toString(), 2));
            tmp.setMonth11(NumberUtil.roundStr(monthOldMap.get("1-11月").toString(), 2));
            tmp.setMonth12(NumberUtil.roundStr(monthOldMap.get("1-12月").toString(), 2));


            Map<String, BigDecimal> monthMap = detail.getMonthInvestmentSchemes().stream().collect(Collectors.toMap(MonthInvestmentSchemeVO::getName, MonthInvestmentSchemeVO::getPredicate));
            tmp.setMonth1Change(NumberUtil.roundStr(monthMap.get("1月").toString(), 2));
            tmp.setMonth2Change(NumberUtil.roundStr(monthMap.get("1-2月").toString(), 2));
            tmp.setMonth3Change(NumberUtil.roundStr(monthMap.get("1-3月").toString(), 2));
            tmp.setMonth4Change(NumberUtil.roundStr(monthMap.get("1-4月").toString(), 2));
            tmp.setMonth5Change(NumberUtil.roundStr(monthMap.get("1-5月").toString(), 2));
            tmp.setMonth6Change(NumberUtil.roundStr(monthMap.get("1-6月").toString(), 2));
            tmp.setMonth7Change(NumberUtil.roundStr(monthMap.get("1-7月").toString(), 2));
            tmp.setMonth8Change(NumberUtil.roundStr(monthMap.get("1-8月").toString(), 2));
            tmp.setMonth9Change(NumberUtil.roundStr(monthMap.get("1-9月").toString(), 2));
            tmp.setMonth10Change(NumberUtil.roundStr(monthMap.get("1-10月").toString(), 2));
            tmp.setMonth11Change(NumberUtil.roundStr(monthMap.get("1-11月").toString(), 2));
            tmp.setMonth12Change(NumberUtil.roundStr(monthMap.get("1-12月").toString(), 2));


            tmp.setNextOneYear(NumberUtil.roundStr(detail.getNextOneYear().toString(), 2));
            tmp.setNextTwoYear(NumberUtil.roundStr(detail.getNextTwoYear().toString(), 2));
            tmp.setNextThreeYear(NumberUtil.roundStr(detail.getNextThreeYear().toString(), 2));
            tmp.setNextFourYear(NumberUtil.roundStr(detail.getNextFourYear().toString(), 2));
            tmp.setNextFiveYear(NumberUtil.roundStr(detail.getNextFiveYear().toString(), 2));

            tmp.setOverallBudget(new BigDecimal(tmp.getOverallBudget()).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setOverallReality(new BigDecimal(tmp.getOverallReality()).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setProjectAmount(new BigDecimal(tmp.getProjectAmount()).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            tmp.setContractAmount(new BigDecimal(tmp.getContractAmount()).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP).toString());
            result.add(tmp);
        }


        String fileName = String.format("%s.xlsx", excelFileName);
        ExcelUtils.write(response, fileName, excelFileName, ExportChangeByExcelVO.class, result);
    }

    @Override
    public void exportMonthFeedbackByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        String excelFileName = "投资计划执行月报";
        Boolean isEmpty = searchDTO.getIsEmpty();
        if (isEmpty) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportMonthFeedbackByExcelVO.class, new ArrayList<>());
            return;
        }

        List<YearInvestmentSchemeMonthFeedback> db = new ArrayList<>();
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapperX<YearInvestmentSchemeMonthFeedback> condition = new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class);
            condition.in(YearInvestmentSchemeMonthFeedback::getId, ids);
            condition.orderByDesc(YearInvestmentSchemeMonthFeedback::getModifyTime);
            db = yearInvestmentSchemeMonthFeedbackService.listByIds(ids);
        } else {
            LambdaQueryWrapperX<YearInvestmentSchemeMonthFeedback> condition = new LambdaQueryWrapperX<>(YearInvestmentSchemeMonthFeedback.class);
            if (Objects.isNull(searchDTO)) {
                searchDTO = new SearchReportDTO();
            }
            String companyName = searchDTO.getCompanyName();
            LambdaQueryWrapperX<Project> projectCondition = new LambdaQueryWrapperX<>(Project.class);
//            if (StrUtil.isNotBlank(companyName)) {
//                if (companyName.contains(",")) {
//                    projectCondition.and(projectOrionWrapper -> {
//                        String[] companyArray = companyName.split(",");
//                        List<String> companyList = Arrays.asList(companyArray);
//                        projectOrionWrapper.like(Project::getResOrg, companyList.get(0));
//                        for (int i = 1; i < companyList.size(); i++) {
//                            projectOrionWrapper.or().like(Project::getResOrg, companyList.get(i));
//                        }
//                    });
//                } else {
//                    projectCondition.like(Project::getResOrg, companyName);
//                }
//            }
            String projectNumber = searchDTO.getProjectNumber();
            if (StrUtil.isNotBlank(projectNumber)) {
                projectCondition.like(Project::getNumber, projectNumber);
            }
            String projectName = searchDTO.getProjectName();
            if (StrUtil.isNotBlank(projectName)) {
                projectCondition.like(Project::getName, projectName);
            }
            String rspDeptId = searchDTO.getRspDeptId();
            if (StrUtil.isNotBlank(rspDeptId)) {
                projectCondition.like(Project::getResDept, rspDeptId);
            }
            Integer projectStatusCode = searchDTO.getProjectStatusCode();
            if (Objects.nonNull(projectStatusCode)) {
                projectCondition.eq(Project::getStatus, projectStatusCode);
            }

            String name = searchDTO.getName();
            if (StrUtil.isNotBlank(name)) {
                projectCondition.and(sub -> {
                    sub.like(Project::getName, name).or().like(Project::getNumber, name);
                });
            }

            List<Project> projects = projectService.list(projectCondition);
            List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());

            LambdaQueryWrapperX<YearInvestmentScheme> yearCondition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            if (CollectionUtils.isEmpty(projectIds)) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_EXCEL, "数据不存在");
            }
            yearCondition.in(YearInvestmentScheme::getProjectId, projectIds);
            String yearName = searchDTO.getYearName();
            if (StrUtil.isNotBlank(yearName)) {
                if (yearName.contains("-")) {
                    String[] split = yearName.split("-");
                    yearCondition.between(YearInvestmentScheme::getYearName, split[0], split[1]);
                }
            }

            List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(yearCondition);
            List<String> yearIds = yearInvestmentSchemes.stream().map(YearInvestmentScheme::getId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(yearInvestmentSchemes)){
                throw new BaseException(PMSErrorCode.PMS_ERROR_EXCEL, "空数据不允许导出");
            }

            if (!CollectionUtils.isEmpty(yearIds)) {
                condition.in(YearInvestmentSchemeMonthFeedback::getYearInvestmentId, yearIds);
            }


            if (StrUtil.isNotBlank(name)) {
                condition.and(sub -> {
                    sub.like(YearInvestmentSchemeMonthFeedback::getName, name).or().like(YearInvestmentSchemeMonthFeedback::getNumber, name);
                });
            }

            Integer status = searchDTO.getStatus();
            if (Objects.nonNull(status)) {
                condition.eq(YearInvestmentSchemeMonthFeedback::getStatus, status);
            }

            String month = searchDTO.getMonth();
            if (StrUtil.isNotBlank(month)) {
                condition.eq(YearInvestmentSchemeMonthFeedback::getMonth, month + "月");
            }
            dataPermissionForYearInvestmentSchemeMonthFeedback(condition);
            condition.orderByDesc(YearInvestmentSchemeMonthFeedback::getModifyTime);
            db = yearInvestmentSchemeMonthFeedbackService.list(condition);
        }

        if (CollectionUtil.isEmpty(db)) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportChangeByExcelVO.class, new ArrayList<>());
            return;
        }

        List<String> yearInvestmentIds = db.stream().map(YearInvestmentSchemeMonthFeedback::getYearInvestmentId).distinct().collect(Collectors.toList());

        Map<String, YearInvestmentSchemeVO> yearInvestmentSchemeVOMap = new HashMap<>();

        for (String yId : yearInvestmentIds) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(yId, null);
            yearInvestmentSchemeVOMap.put(yId, detail);
        }


        //处理数据
        List<ExportMonthFeedbackByExcelVO> result = new ArrayList<>();
        AtomicInteger order = new AtomicInteger(1);
        for (YearInvestmentSchemeMonthFeedback c : db) {
            //构造数据

            YearInvestmentSchemeMonthFeedbackVO detail = yearInvestmentSchemeMonthFeedbackService.detail(c.getId(), null);

            ExportMonthFeedbackByExcelVO tmp = BeanCopyUtils.convertTo(detail, ExportMonthFeedbackByExcelVO::new);
            tmp.setOrder(String.valueOf(order.getAndIncrement()));
            tmp.setStatusName(detail.getDataStatus().getName());


            YearInvestmentSchemeVO yearInvestmentSchemeVO = yearInvestmentSchemeVOMap.get(detail.getYearInvestmentId());
            tmp.setProjectName(yearInvestmentSchemeVO.getProjectName());
            tmp.setProjectNumber(yearInvestmentSchemeVO.getProjectNumber());
            tmp.setProjectStatusName(yearInvestmentSchemeVO.getProjectStatusName());
            tmp.setRspDeptName(yearInvestmentSchemeVO.getRspDeptName());
            tmp.setRspUserName(yearInvestmentSchemeVO.getRspUserName());
            tmp.setYearProcess(yearInvestmentSchemeVO.getYearProcess());
           // tmp.setCompanyName(yearInvestmentSchemeVO.getCompanyName());

            Map<String, String> doStatusMap = new HashMap<>() {{
                put("0", "有偏差");
                put("1", "无偏差");
            }};
            tmp.setMonthDoStatus(doStatusMap.get(String.valueOf(c.getMonthDoStatus())));
            tmp.setLastPracticeDo(NumberUtil.roundStr(detail.getLastPracticeDo().toString(), 2));
            tmp.setMplanDo(NumberUtil.roundStr(detail.getMplanDo().toString(), 2));
            tmp.setMpracticeDo(NumberUtil.roundStr(detail.getMpracticeDo().toString(), 2));
            DecimalFormat df = new DecimalFormat("0.00%");//设置百分比格式，保留两位小数
            if (Objects.equals(detail.getMplanDo().compareTo(BigDecimal.ZERO), 0)) {
                if (!Objects.equals(detail.getMpracticeDo().compareTo(BigDecimal.ZERO), 0)) {
                    tmp.setMpracticeDoRate("100%");
                } else {
                    tmp.setMpracticeDoRate("0.00%");
                }
            } else {
                BigDecimal monthRate = detail.getMpracticeDo().divide(detail.getMplanDo(), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                tmp.setMpracticeDoRate(df.format(monthRate.doubleValue()));
            }
            tmp.setYinvestmentPlan(NumberUtil.roundStr(detail.getYinvestmentPlan().toString(), 2));
            if (Objects.equals(detail.getYinvestmentPlan().compareTo(BigDecimal.ZERO), 0)) {
                if (!Objects.equals(detail.getMpracticeDo().compareTo(BigDecimal.ZERO), 0)) {
                    tmp.setYearCompleteRate("100%");
                } else {
                    tmp.setYearCompleteRate("0.00%");
                }
            } else {
                BigDecimal yearRate = detail.getMpracticeDo().divide(detail.getYinvestmentPlan(), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                tmp.setYearCompleteRate(df.format(yearRate.doubleValue()));
            }

            result.add(tmp);
        }

        String fileName = String.format("%s.xlsx", excelFileName);
        ExcelUtils.write(response, fileName, excelFileName, ExportMonthFeedbackByExcelVO.class, result);
    }

    @Override
    public void exportTotalDoByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        String excelFileName = "投资计划总体执行";
        Boolean isEmpty = searchDTO.getIsEmpty();
        if (isEmpty) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportTotalDoByExcelVO.class, new ArrayList<>());
            return;
        }

        List<YearInvestmentScheme> db = new ArrayList<>();
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
            condition.in(YearInvestmentScheme::getId, ids);
            condition.eq(YearInvestmentScheme::getStatus, YearInvestmentSchemeStatus.GIVE.getCode());
            condition.orderByDesc(YearInvestmentScheme::getModifyTime);
            db = yearInvestmentSchemeService.list(condition);
        } else {
            String sql = "SELECT t1.id  FROM pms_year_investment_scheme t1 INNER JOIN ( SELECT MAX( create_time ) AS create_time, investment_id, year_name FROM pms_year_investment_scheme WHERE logic_status = 1 GROUP BY investment_id, year_name ) t2 ON t1.create_time = t2.create_time AND t1.year_name = t2.year_name AND t1.investment_id = t2.investment_id WHERE logic_status = 1";
            if(!ObjectUtils.isEmpty(searchDTO) && searchDTO.isPointProject()){
                sql ="SELECT\n" +
                        "\tt1.id \n" +
                        "FROM\n" +
                        "\t(select year_name,investment_id,id,logic_status,create_time from pms_year_investment_scheme y where (y.installation + y.device+y.architecture+y.other) > 1000 and logic_status = 1) as t1\n" +
                        "\tINNER JOIN ( SELECT MAX( create_time ) AS create_time, investment_id, year_name FROM pms_year_investment_scheme WHERE logic_status = 1 GROUP BY investment_id, year_name ) t2 ON t1.create_time = t2.create_time \n" +
                        "\tAND t1.year_name = t2.year_name \n" +
                        "\tAND t1.investment_id = t2.investment_id \n" +
                        "WHERE\n" +
                        "\tt1.logic_status = 1;\n" +
                        "\t";
            }
            List<String> yearInvestmentSchemeIds = namedParameterJdbcTemplate.queryForList(sql, new HashMap<>(), String.class);
            if (!CollectionUtils.isEmpty(yearInvestmentSchemeIds)) {
                LambdaQueryWrapperX<YearInvestmentScheme> condition = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
                condition.in(YearInvestmentScheme::getId, yearInvestmentSchemeIds);
                Boolean aBoolean = defaultCondition2(searchDTO, condition);
                if (!aBoolean) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_EXCEL, "数据不存在");
                }
                dataPermissionForYearInvestmentScheme(condition);
                condition.eq(YearInvestmentScheme::getStatus, YearInvestmentSchemeStatus.GIVE.getCode());
//                condition.in(YearInvestmentScheme::getId, yearInvestmentSchemeIds);
                condition.orderByDesc(YearInvestmentScheme::getModifyTime);
                db = yearInvestmentSchemeService.list(condition);
            }
        }

        if (CollectionUtil.isEmpty(db)) {
            String fileName = String.format("%s.xlsx", excelFileName);
            ExcelUtils.write(response, fileName, excelFileName, ExportChangeByExcelVO.class, new ArrayList<>());
            return;
        }

        List<ExportTotalDoByExcelVO> result = new ArrayList<>();

        AtomicInteger order = new AtomicInteger(1);
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        for (YearInvestmentScheme c : db) {
            YearInvestmentSchemeVO detail = yearInvestmentSchemeService.detail(c.getId(), null);
            ExportTotalDoByExcelVO tmp = BeanCopyUtils.convertTo(detail, ExportTotalDoByExcelVO::new);
            tmp.setOrder(String.valueOf(order.getAndIncrement()));
            DecimalFormat df = new DecimalFormat("0.00%");//设置百分比格式，保留两位小数

            if (StrUtil.isNotBlank(detail.getOldId())) {
                if (Objects.equals(new BigDecimal(detail.getTotalChange()).compareTo(BigDecimal.ZERO), 0)) {
                    if (!Objects.equals(new BigDecimal(detail.getTotalDo()).compareTo(BigDecimal.ZERO), 0)) {
                        tmp.setTotalDoPercent("100%");
                    } else {
                        tmp.setTotalDoPercent("0.00%");
                    }
                } else {
                    BigDecimal monthRate = new BigDecimal(detail.getTotalDo()).divide(new BigDecimal(detail.getTotalChange()), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                    tmp.setTotalDoPercent(df.format(monthRate.doubleValue()));
                }
                BigDecimal changeMoney = new BigDecimal(tmp.getTotalDo()).subtract(new BigDecimal(tmp.getTotalChange()));
                tmp.setBalance(NumberUtil.roundStr(changeMoney.toString(), 2));

            } else {
                if (Objects.equals(new BigDecimal(detail.getTotal()).compareTo(BigDecimal.ZERO), 0)) {
                    if (!Objects.equals(new BigDecimal(detail.getTotalDo()).compareTo(BigDecimal.ZERO), 0)) {
                        tmp.setTotalDoPercent("100%");
                    } else {
                        tmp.setTotalDoPercent("0.00%");
                    }
                } else {
                    BigDecimal monthRate = new BigDecimal(detail.getTotalDo()).divide(new BigDecimal(detail.getTotal()), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
                    tmp.setTotalDoPercent(df.format(monthRate.doubleValue()));
                }
                BigDecimal changeMoney = new BigDecimal(tmp.getTotalDo()).subtract(new BigDecimal(tmp.getTotal()));
                tmp.setBalance(NumberUtil.roundStr(changeMoney.toString(), 2));
            }

            tmp.setCutOffGiveY(detail.getCutOffGiveY());
            tmp.setCutOffCompleteY(detail.getCutOffCompleteY());
            BigDecimal estimate = new BigDecimal(detail.getEstimate());
            BigDecimal complete = new BigDecimal(detail.getCutOffCompleteY());

            BigDecimal multiply = complete.divide(estimate, 4,RoundingMode.HALF_UP);
            tmp.setFinishRate(decimalFormat.format(multiply));
            tmp.setNotExecuteGive(estimate.subtract(complete).toString());
            result.add(tmp);
        }

        String fileName = String.format("%s.xlsx", excelFileName);
        ExcelUtils.write(response, fileName, excelFileName, ExportTotalDoByExcelVO.class, result);
    }


    private void dataPermissionForYearInvestmentScheme(LambdaQueryWrapperX<YearInvestmentScheme> condition) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser currentSimpleUser = userRedisHelper.getSimpleUserById(currentUserId);
        String orgCode = currentSimpleUser.getOrgCode();
        if (StrUtil.equals("32010000", orgCode)) {
            return;
        }
        if (StrUtil.equals("32420000", orgCode)) {
            SimpleDeptVO simpleDeptVO = deptRedisHelper.getSimpleDeptById(currentSimpleUser.getOrgId());
            List<String> orgLeaders = simpleDeptVO.getGroupLeader().stream().map(OrgLeaderVO::getUserId).collect(Collectors.toList());
            if (orgLeaders.contains(currentUserId)) {
                return;
            }
        }
        UserVO userById = userRedisHelper.getUserById(currentUserId);
        List<RoleVO> roles = userById.getRoles();
        if (!CollectionUtils.isEmpty(roles)) {
            for (RoleVO role : roles) {
                if ("company_budget_manager".equals(role.getCode())) {
                    return;
                }
            }
        }

        List<Project> projects = projectService.list(new LambdaQueryWrapperX<>(Project.class).eq(Project::getResDept, currentSimpleUser.getOrgId()));
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(projectIds)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "当前用户所属部门无项目数据，不能导出");
        }
        condition.in(YearInvestmentScheme::getProjectId, projectIds);
    }


    private void dataPermissionForYearInvestmentSchemeMonthFeedback(LambdaQueryWrapperX<YearInvestmentSchemeMonthFeedback> condition) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser currentSimpleUser = userRedisHelper.getSimpleUserById(currentUserId);
        String orgCode = currentSimpleUser.getOrgCode();
        if (StrUtil.equals("32010000", orgCode)) {
            return;
        }
        if (StrUtil.equals("32420000", orgCode)) {
            SimpleDeptVO simpleOrganization = deptRedisHelper.getSimpleDeptById(currentSimpleUser.getOrgId());
            List<String> orgLeaders = simpleOrganization.getGroupLeader().stream().map(OrgLeaderVO::getUserId).collect(Collectors.toList());
            if (orgLeaders.contains(currentUserId)) {
                return;
            }
        }
        UserVO userById = userRedisHelper.getUserById(currentUserId);
        List<RoleVO> roles = userById.getRoles();
        if(!CollectionUtils.isEmpty(roles)){
            for (RoleVO role : roles) {
                if("company_budget_manager".equals(role.getCode())){
                    return;
                }
            }
        }
        List<Project> projects = projectService.list(new LambdaQueryWrapperX<>(Project.class).eq(Project::getResDept, currentSimpleUser.getOrgId()));
        List<String> projectIds = projects.stream().map(Project::getId).collect(Collectors.toList());
        condition.in(YearInvestmentSchemeMonthFeedback::getProjectId, projectIds);
    }

}