package com.chinasie.orion.manager;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.feign.request.FlowTemplateBusinessBatchDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDeleteDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDetailBatchDTO;
import com.chinasie.orion.feign.response.FlowTemplateVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WfInstanceProjectMaterialPreparationManage {

    private static final String SCHEME_DETAIL_URL = "/pms/question-management-details/%s";
    @Resource
    private WorkflowFeignService processInstanceService;
    @Resource
    private UserRedisHelper userRedisHelper;

    public void process(String materialPreparationId, String orgId)  {
        String templateId = getTemplateId();
        getBusinessKey(materialPreparationId, templateId, orgId);
    }

    private String getTemplateId() {
        ResponseDTO<List<FlowTemplateVO>> listResponseDTO = null;
        try {
            listResponseDTO = processInstanceService.byDataType("ProjectMaterialPreparation");
        } catch (Exception e) {
            log.error("调用流程服务，获取模板ID异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR, "request workflow Exception!");
        }
        List<FlowTemplateVO> result = listResponseDTO.getResult();
        List<FlowTemplateVO> majorTemplate = result.stream().filter(FlowTemplateVO::getIsMajor).collect(Collectors.toList());
        if (CollUtil.isEmpty(majorTemplate) ) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "workflow template does not exist!");
        }
        return result.get(0).getId();
    }

    private String getBusinessKey(String materialPreparationId, String templateId, String orgId) {

        FlowTemplateBusinessBatchDTO flowTemplateBusinessBatchDTO = new FlowTemplateBusinessBatchDTO();
        FlowTemplateBusinessDetailBatchDTO flowTemplateBusinessDetailBatchDTO = new FlowTemplateBusinessDetailBatchDTO();
        flowTemplateBusinessDetailBatchDTO.setBusinessId(materialPreparationId);
        flowTemplateBusinessDetailBatchDTO.setBusinessName(buildTitle());
        flowTemplateBusinessDetailBatchDTO.setMessageUrl(String.format(SCHEME_DETAIL_URL, materialPreparationId));
        flowTemplateBusinessDetailBatchDTO.setDataTypeCode("ProjectMaterialPreparation");
        flowTemplateBusinessBatchDTO.setTemplateId(templateId);
        flowTemplateBusinessBatchDTO.setBusinessList(Collections.singletonList(flowTemplateBusinessDetailBatchDTO));
        flowTemplateBusinessBatchDTO.setOrgId(orgId);
        FlowTemplateBusinessDTO businessDTO = new FlowTemplateBusinessDTO();

        businessDTO.setTemplateId(templateId);
        try {
            log.info("调用流程服务，建立业务与模板管理关系，请求参数：{}", JSON.toJSONString(businessDTO));
            log.info("flowTemplateBusinessBatchDTO data {}", flowTemplateBusinessBatchDTO);
            ResponseDTO<FlowTemplateBusinessVO> responseDTO = processInstanceService.batchCreate(flowTemplateBusinessBatchDTO);
            log.info("调用流程服务，建立业务与模板管理关系，响应结果：{}", JSON.toJSONString(responseDTO));
            return responseDTO.getResult().getId();
        } catch (Exception e) {
            log.error("调用流程服务，建立业务与模板管理关系异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR,"调用流程服务异常");
        }
    }

    private String buildTitle() {
        SimpleUser user = userRedisHelper.getSimpleUserById(CurrentUserHelper.getCurrentUserId());
        if (Objects.nonNull(user)) {
            return "【" + user.getName() + "】" + "发起备料与加工申请流程";
        }
        return "";
    }


    public void removeProcess(List<ProjectScheme> schemes) throws Exception {
        List<String> ids = schemes.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        FlowTemplateBusinessDeleteDTO flowTemplateBusinessDeleteDTO = new FlowTemplateBusinessDeleteDTO();
        flowTemplateBusinessDeleteDTO.setBusinessIds(ids);
        processInstanceService.batchDelete(flowTemplateBusinessDeleteDTO);
    }
}
