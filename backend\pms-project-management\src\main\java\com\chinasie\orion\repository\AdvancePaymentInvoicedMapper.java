package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.AdvancePaymentInvoiced;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.domain.vo.ProvisionalIncomeAccountingVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * AdvancePaymentInvoiced Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 15:21:06
 */
@Mapper
public interface AdvancePaymentInvoicedMapper extends  OrionBaseMapper  <AdvancePaymentInvoiced> {
    AdvancePaymentInvoicedVO getTotal(@Param("contractId") String contractId);
    List<AdvancePaymentInvoicedVO> getMilestoneTotal(@Param("milestoneIds") List<String> milestoneIds);

}

