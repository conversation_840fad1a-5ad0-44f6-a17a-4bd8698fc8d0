<template>
  <div class="page">
    <div class="title">
      <div class="title-left">
        <Select
          v-model:value="state.baseName"
          Select
          size="small"
          style="width: 120px;"
          :options="safetyOptions"
          @change="getMajorRepairsData"
        />
      </div>
      <div class="title-cneter">
        <div class="title-cneter-item">
          <div class="title-cneter-item-color color-s" />
          <div>已完成的大修</div>
        </div>
        <div class="title-cneter-item">
          <div class="title-cneter-item-color color-i" />
          <div>正在进行的大修</div>
        </div>
        <div class="title-cneter-item">
          <div class="title-cneter-item-color color-y" />
          <div>正在准备的大修</div>
        </div>
      </div>
      <div class="title-right">
        <a-range-picker
          v-model:value="state.data"
          picker="year"
          @change="getMajorRepairsData"
        />
      </div>
    </div>
    <div class="content">
      <div class="calendar">
        <div class="calendar-yue">
          <!-- 1 到 12 月 -->
          <div class="calendar-yue-item">
            1月
          </div>
          <div class="calendar-yue-item">
            2月
          </div>
          <div class="calendar-yue-item">
            3月
          </div>
          <div class="calendar-yue-item">
            4月
          </div>
          <div class="calendar-yue-item">
            5月
          </div>
          <div class="calendar-yue-item">
            6月
          </div>
          <div class="calendar-yue-item">
            7月
          </div>
          <div class="calendar-yue-item">
            8月
          </div>
          <div class="calendar-yue-item">
            9月
          </div>
          <div class="calendar-yue-item">
            10月
          </div>
          <div class="calendar-yue-item">
            11月
          </div>
          <div class="calendar-yue-item">
            12月
          </div>
        </div>
        <div class="calendar-com">
          <div
            v-for="item in state.MajorRepairsData"
            :key="item.id"
            class="calendar-com-item"
          >
            <div class="calendar-com-item-tit">
              {{ item.baseName }}
            </div>
            <div

              class="calendar-com-item-tr"
            >
              <div
                v-for="item1 in item.overhaulDetails"
                :key="item1.id"
                class="calendar-com-item-con"
              >
                <div
                  v-for="n in 12"
                  :key="n"
                  class="calendar-com-item-con-item"
                >
                  <!-- dayjs(item1.actualBeginTime)的月份 -->

                  <!-- <div
                    v-if="dayjs(item1.actualBeginTime).month() === n - 1"
                    class="calendar-com-item-con-item-tips"
                    :style="{ width: ((dayjs(item1.actualEndTime).month()+1) - dayjs(item1.actualBeginTime).month()) * 100 + '%',background:(item1.haulStatus=='正在准备的大修'? '#f5a623' : item1.haulStatus=='已完成的大修'? '#59bd56': '#f50') }"
                  >

                    {{ item1.repairRound }}
                  </div> -->

                  <Popover
                    placement="top"
                    trigger="hover"
                  >
                    <template #content>
                      <div>大修轮次：{{ item1.repairRound }}</div>
                      <div>大修状态：{{ item1.haulStatus }}</div>
                      <div>开始时间：{{ item1.actualBeginTime }}</div>
                      <div>完成时间：{{ item1.actualEndTime }}</div>
                      <div>大修工期：{{ item1.workDuration }}</div>
                      <div>大修经理：{{ item1.ownerName }}</div>
                      <div>大修类别：{{ item1.type }}</div>
                    </template>
                    <div
                      v-if="dayjs(item1.actualBeginTime).month() === n - 1"
                      class="calendar-com-item-con-item-tips"
                      :style="{ width: ((dayjs(item1.actualEndTime).month()+1) - dayjs(item1.actualBeginTime).month()) * 100 + '%', background: (item1.haulStatus == '正在准备的大修' ? '#f50' : item1.haulStatus == '已完成的大修' ? '#59bd56' : '#f5a623') }"
                    >
                      {{ item1.repairRound }}
                    </div>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  DatePicker,
  Popover,
} from 'ant-design-vue';

import dayjs from 'dayjs';
import {
  Select,
} from 'lyra-component-vue3';
import {
  defineProps,
  onMounted,
  reactive,
  ref,
} from 'vue';
import Api from '/@/api';
onMounted(() => {
  getMajorRepairsData();
  getSafetyOptions();
});
const state: any = reactive({
  loading: false,
  baseName: '',
  startDate: '2023-01-01',
  endDate: '2024-12-31',
  MajorRepairsData: [],
  data: [dayjs().startOf('year'), dayjs().endOf('year')],

});

let safetyOptions = ref([]);
async function getSafetyOptions() {
  try {
    const result = await new Api('/pms/productionDashboard/getLocation').fetch({
    }, '', 'POST');
    safetyOptions.value = result.map((item: any) => ({
      label: item.name,
      value: item.code,
    }));
    safetyOptions.value = [
      {
        label: '全部',
        value: '',
      },
      ...safetyOptions.value,
    ];
  } finally {
    state.loading = false;
  }
}
// 多基地大修准备及实施状态
async function getMajorRepairsData() {
  try {
    const result = await new Api('/pms/productionDashboard/getOverhaulDetail').fetch({
      // statedata: state.data,
      baseName: state.baseName,
      startDate: dayjs(state.data[0]).format('YYYY-MM-DD'),
      // endDate: dayjs(state.data[1]).format('YYYY-MM-DD'),
      // 当前年最后一天: dayjs(state.data[1]).format('YYYY-MM-DD'),
      endDate: dayjs(state.data[1]).endOf('year').format('YYYY-MM-DD'),
    }, '', 'POST');
    state.MajorRepairsData = result || [];
  } finally {
    state.loading = false;
  }
}
// const MajorRepairsDatas = ref(props.MajorRepairsData);
// console.log('🚀 ~ MajorRepairsDatas:', MajorRepairsDatas.value);
const ARangePicker = DatePicker.RangePicker;
// props.MajorRepairsData

</script>

<style scoped lang="less">
.page{
  padding: 20px;
}
.title{
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding: 0 20px;
  &-cneter{
    display: flex;
    justify-content: center;
    flex: 1;
    &-item{
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      &-color{
        width: 40px;
        height: 20px;
        border-radius: 5px;
        margin-right: 5px;
      }
      &-color.color-s{
        background: #59bd56;
      }
      &-color.color-i{
        background: #f5a623;
      }
      &-color.color-y{
        background: #f50;
      }
    }

  }
}
.calendar{
  &-yue{
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    padding-left: 200px;
    &-item{
      width: calc(100% / 12);
      text-align: left;
      padding: 10px 0;
    }
  }
  &-com-item{
    display: flex;
    justify-content: space-between;

    padding: 0 20px;
    &-tit{
      width: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #000;
      border-right: 0;

    }
    &-tr{
      flex: 1;
      border-bottom: 1px solid #000;
    }

    &-con{
      display: flex;
      justify-content: space-between;
      &-item{
        width: calc(100% / 12);
        height: 40px;
        line-height: 40px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #000;
        border-right: 0;
        border-bottom: 0;
        position: relative;
        &-tips{
          width:  200%;
          height: 30px;
          background: #59bd56;
          position: absolute;
          left: 0;
          top: 5px;
          text-align: left;
          padding-left: 5px;
          line-height: 30px;
          z-index: 1000;
          // 鼠标手
          cursor: pointer;

        }

      }
      &-item:last-child{
        border-right:1px solid #000;
      }
    }
  }

}

</style>
