<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>easy-trans</artifactId>
  <version>2.3.1</version>
  <packaging>pom</packaging>
  <url>https://gitee.com/fhs-opensource/easy_trans</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>jackwong</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <modules>
    <module>easy-trans-anno</module>
    <module>easy-trans-service</module>
    <module>easy-trans-spring-boot-starter</module>
    <module>easy-trans-mybatis-plus-extend</module>
    <module>easy-trans-mybatis-flex-extend</module>
    <module>easy-trans-jpa-extend</module>
    <module>easy-trans-tk-extend</module>
    <module>easy-trans-easy-query-extend</module>
    <module>easy-trans-beetl-sql-extend</module>
    <module>easy-trans-untrans-driver</module>
    <module>easy-trans-dependencies</module>
  </modules>
  <scm>
    <connection>scm:*************:fhs-opensource/easy_trans.git</connection>
    <developerConnection>scm:*************:fhs-opensource/easy_trans.git</developerConnection>
    <url>*************:fhs-opensource/easy_trans.git</url>
  </scm>
  <issueManagement>
    <system>gitee Issue</system>
    <url>https://gitee.com/fhs-opensource/easy_trans</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>oss</id>
      <name>OSS Staging Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>oss</id>
      <name>OSS Snapshots Repository</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <fhs.release.version>2.2.4</fhs.release.version>
    <fhs.spring.version>5.2.6.RELEASE</fhs.spring.version>
    <maven.compiler.target>1.8</maven.compiler.target>
    <fhs.mybatis.version>3.5.5</fhs.mybatis.version>
    <fhs.servlet-api.version>3.1.0</fhs.servlet-api.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <fhs.easy-query.version>2.0.42</fhs.easy-query.version>
    <revision>2.3.1</revision>
    <fhs.mp.version>3.5.3.2</fhs.mp.version>
    <fhs.springboot.version>2.2.5.RELEASE</fhs.springboot.version>
    <fhs.hibernate.version>5.4.12.Final</fhs.hibernate.version>
    <maven.compiler.source>1.8</maven.compiler.source>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>easy-trans-anno</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>easy-trans-service</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-annotation</artifactId>
        <version>${fhs.mp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-core</artifactId>
        <version>${fhs.mp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-extension</artifactId>
        <version>${fhs.mp.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${fhs.mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.10.3</version>
      </dependency>
      <dependency>
        <groupId>com.easy-query</groupId>
        <artifactId>sql-api4j</artifactId>
        <version>${fhs.easy-query.version}</version>
      </dependency>
      <dependency>
        <groupId>com.easy-query</groupId>
        <artifactId>sql-core</artifactId>
        <version>${fhs.easy-query.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>1.12.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>${fhs.springboot.version}</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
        <version>${fhs.springboot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>${fhs.spring.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>${fhs.springboot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>${fhs.spring.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-commons</artifactId>
        <version>2.2.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.24</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>22.0</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.10.2</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>1.9.5</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>${fhs.hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-schema</artifactId>
        <version>2.9.2</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>2.9.3</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <resources>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>${project.basedir}/src/main/resources/META-INF</directory>
      </resource>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>${project.basedir}</directory>
        <includes>
          <include>LICENSE</include>
          <include>NOTICE</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.build.sourceDirectory}</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>${project.build.testSourceDirectory}</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.5.1</version>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <skip>true</skip>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.10.4</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <phase>package</phase>
              <goals>
                <goal>jar</goal>
              </goals>
              <configuration>
                <additionalparam>-Xdoclint:none</additionalparam>
              </configuration>
            </execution>
          </executions>
          <configuration>
            <encoding>UTF-8</encoding>
            <aggregate>true</aggregate>
            <charset>UTF-8</charset>
            <docencoding>UTF-8</docencoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.7</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>true</autoReleaseAfterClose>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.2.7</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
