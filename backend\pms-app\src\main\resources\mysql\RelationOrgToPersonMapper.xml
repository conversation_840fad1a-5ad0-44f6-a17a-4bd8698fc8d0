<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.RelationOrgToPersonMapper">


    <update id="updatePersonTime">
        update
            pmsx_relation_org_to_person t1
                inner join pmsx_person_mange t2 on t1.person_id = t2.id
        set t1.plan_begin_time = #{date.beginTime},
            t1.plan_end_time   = #{date.endTime}
        where t2.is_base_permanent = true
          and t2.logic_status = 1
          and t1.logic_status = 1
    </update>
    <update id="updatePersonTimeByList">
        update
        pmsx_relation_org_to_person t1
        inner join pmsx_major_repair_org t2 on t1.repair_org_id = t2.id  and t2.id in
            <foreach collection="list" item="t2" open="(" separator="," close=")">
                #{t2}
            </foreach>
        set t1.plan_begin_time = t2.begin_time,
        t1.plan_end_time   = t2.end_time
        where t1.is_base_permanent = true
        and t2.logic_status = 1
        and t1.logic_status = 1;

    </update>
    <select id="getPersonManageTreeDataStrategy" resultType="com.chinasie.orion.domain.vo.PersonTmpVO">
        select
        distinct rop.id as 'personId',
        rop.newcomer,
        rop.plan_begin_time as 'planInDate',
        rop.plan_end_time as 'planOutDate',
        rop.user_code as 'number',
        rop.act_in_date as 'actInDate',
        rop.act_out_date as 'actOutDate',
        ud.sex,
        ud.name as 'userName',
        rop.is_base_permanent as 'isBasePermanent',
        rop.newcomer_match_person as 'newcomerMatchPerson',
        rop.in_days as 'inDays',
        rop.status,
        rop.leave_reason as 'leaveReason',
        rop.is_finish_out_handover as 'isFinishOutHandover',
        rop.is_again_in as 'isAgainIn',
        pbu.id as 'basicUserId',
        rop.base_code as 'baseCode',
        rop.repair_org_id as 'repairOrgId',
        pmro.rsp_user_id as 'rspUserId',
        pmro.chain_path as 'chainPath',
        pmro.parent_id as 'parentId',
        rop.id as 'id',
        rop.id as 'relationId'
        from
        pmsx_relation_org_to_person rop
        left join pmi_user ud on  ud.code = rop.user_code and ud.logic_status = 1
        inner join pmsx_basic_user pbu on pbu.user_code = ud.code and pbu.logic_status = 1
        inner join pmsx_major_repair_org pmro on pmro.id = rop.repair_org_id  and pmro.logic_status = 1
        inner join pmsx_major_repair_plan mrp on mrp.repair_round = pmro.repair_round  and mrp.repair_round = #{repairRound}
        WHERE  rop.repair_round = #{repairRound}  and rop.repair_org_id IN
        <foreach item="id" index="index" collection="orgIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rop.logic_status = 1
        <if test="sql != null and sql != ''">AND ${sql}</if>
        <if test="keyword != null and keyword != ''">
            AND (rop.user_code LIKE CONCAT('%', #{keyword}, '%')
            OR pbu.name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>
    <select id="getPersonManageTreeData" resultType="com.chinasie.orion.domain.vo.PersonTmpVO">
        SELECT DISTINCT rop.id,rop.newcomer,rop.plan_begin_time as 'planInDate',rop.plan_end_time as 'planOutDate'
        ,rop.user_code as 'number',pbu.full_name as 'name',rop.name as 'userName',
        rop.act_in_date as 'actInDate',rop.act_out_date as 'actOutDate',ud.sex,
        rop.is_base_permanent as 'isBasePermanent',rop.newcomer_match_person as 'newcomerMatchPerson',rop.in_days as 'inDays',
        rop.status,rop.leave_reason as 'leaveReason',rop.is_finish_out_handover as 'isFinishOutHandover',
        rop.in_days as inDays,
        rop.is_again_in as 'isAgainIn',pbu.id as 'basicUserId',rop.base_code as 'baseCode',rop.base_name as 'baseName',rop.repair_org_id as 'repairOrgId',rop.create_time as 'createTime',
        pmro.begin_time as 'majorOrgBeginTime',pmro.end_time as 'majorOrgEndTime',mrp.begin_time as 'majorBeginTime',mrp.end_time as 'majorEndTime'
        FROM pmsx_relation_org_to_person rop
        left  JOIN pmi_user ud ON ud.code = rop.user_code and ud.logic_status = 1
        INNER JOIN pmsx_basic_user pbu ON pbu.user_code = ud.code and pbu.logic_status = 1
        inner join pmsx_major_repair_org pmro on pmro.id = rop.repair_org_id  and pmro.logic_status = 1
        inner join pmsx_major_repair_plan mrp on mrp.repair_round = pmro.repair_round  and mrp.repair_round = #{repairRound}
        WHERE  rop.repair_round = #{repairRound}  and  rop.repair_org_id IN
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rop.logic_status = 1
        <if test="keyword != null and keyword != ''">
            AND (rop.user_code LIKE CONCAT('%', #{keyword}, '%')
            or pbu.full_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>
    <select id="getJobPostNames" resultType="com.chinasie.orion.domain.entity.PersonMange">
        select
        ppm.user_code as 'number',
        ppjpa.job_post_name as 'jobPostName'
        from
        pmsx_relation_org_to_person ppm
        inner join pmsx_person_job_post_authorize ppjpa on ppm.`user_code` = ppjpa.user_code and ppjpa.logic_status = 1
        where ppjpa.status = 1 and ppjpa.authorize_status = 130 and (ppm.base_code = #{baseCode} or ppm.base_code = 'SNPI')
        and ppm.user_code in
        <foreach collection="codeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
</mapper>
