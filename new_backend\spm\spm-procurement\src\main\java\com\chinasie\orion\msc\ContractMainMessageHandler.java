package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.domain.entity.ContractMain;
import com.chinasie.orion.constant.MsgHandlerConstant;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ContractMainMessageHandler implements MscBuildHandler<ContractMain> {

    @Override
    public SendMessageDTO buildMsc(ContractMain contractMain, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$contractName$", contractMain.getContractName());
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(contractMain.getContractNumber()+"_"+contractMain.getDeptId())
                // .messageUrl("/pas/MarketDemandManagementDetails/" + projectOrderDTO.getOrderNumber())
                .messageUrl("/pms/techCfgContractManage")
                .messageUrlName("技术配置合同管理")
                .senderTime(new Date())
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList((List<String>) objects[0])
                .platformId(contractMain.getPlatformId())
                .orgId(CurrentUserHelper.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgHandlerConstant.CONTRACT_MAIN;
    }
}
