package com.chinasie.orion.controller.assetApply;

import com.chinasie.orion.domain.dto.assetApply.AssetSyncDTO;
import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailAssetsDTO;
import com.chinasie.orion.domain.vo.applyAsset.AssetSyncVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailAssetsService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProjectAssetApplyDetailAssets 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04 09:17:33
 */
@RestController
@RequestMapping("/projectAssetApplyDetailAssets")
@Api(tags = "资产转固申请详情表-Asset")
public class ProjectAssetApplyDetailAssetsController {

    @Autowired
    private ProjectAssetApplyDetailAssetsService projectAssetApplyDetailAssetsService;


    /**
     * 新增
     *
     * @param projectAssetApplyDetailAssetsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【资产转固申请详情表-Asset】数据【{{#projectAssetApplyDetailAssetsDTO.name}}】", type = "ProjectAssetApplyDetailAssets", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectAssetApplyDetailAssetsDTO projectAssetApplyDetailAssetsDTO) throws Exception {
        String rsp =  projectAssetApplyDetailAssetsService.create(projectAssetApplyDetailAssetsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

//    /**
//     * 编辑
//     *
//     * @param projectAssetApplyDetailAssetsDTO
//     * @return
//     * @throws Exception
//     */
//    @ApiOperation(value = "编辑")
//    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectAssetApplyDetailAssetsDTO.name}}】", type = "ProjectAssetApplyDetailAssets", subType = "编辑", bizNo = "{{#projectAssetApplyDetailAssetsDTO.id}}")
//    public ResponseDTO<Boolean> edit(@RequestBody  ProjectAssetApplyDetailAssetsDTO projectAssetApplyDetailAssetsDTO) throws Exception {
//        Boolean rsp = projectAssetApplyDetailAssetsService.edit(projectAssetApplyDetailAssetsDTO);
//        return new ResponseDTO<>(rsp);
//    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【资产转固申请详情表-Asset】数据", type = "ProjectAssetApplyDetailAssets", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectAssetApplyDetailAssetsService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【资产转固申请详情表-Asset】数据", type = "ProjectAssetApplyDetailAssets", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectAssetApplyDetailAssetsService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【资产转固申请详情表-Asset】分页数据", type = "ProjectAssetApplyDetailAssets", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<AssetSyncVO>> pages(@RequestBody Page<AssetSyncDTO> pageRequest) throws Exception {
        Page<AssetSyncVO> rsp =  projectAssetApplyDetailAssetsService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
