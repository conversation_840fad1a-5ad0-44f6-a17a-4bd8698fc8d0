<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import { ref, Ref, inject } from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import { parseBooleanToRender } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const supplierInfo: Record<string, any> = inject('supplierInfo');

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
    },
    {
      title: '申请编号',
      dataIndex: 'applicationNumber',
    },
    {
      title: '受限类型',
      dataIndex: 'restrictedType',
    },
    {
      title: '整改状态',
      dataIndex: 'rectificationStatus',
    },
    {
      title: '黑名单类型',
      dataIndex: 'blacklistType',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '申报日期',
      dataIndex: 'declarationDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
    },
    {
      title: '申报公司',
      dataIndex: 'declaringCompany',
    },
    {
      title: '内容描述',
      dataIndex: 'contentDescription',
    },
    {
      title: '审批完成时间',
      dataIndex: 'approvalCompletionTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否解冻',
      dataIndex: 'whetherThawed',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '受限范围',
      dataIndex: 'restrictedScope',
    },
    {
      title: '集团发送SAP',
      dataIndex: 'groupSendsSap',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/supplierRestrictedRecord/getRestrictedRecordByCode').fetch({
    ...params,
    query: {
      supplierCode: supplierInfo.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
