package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * NonContractProc Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-12 11:04:24
 */
@TableName(value = "pms_non_contract_proc")
@ApiModel(value = "NonContractProcEntity对象", description = "无合同采购表")
@Data

public class NonContractProc extends ObjectEntity implements Serializable {

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @TableField(value = "work_topic")
    private String workTopic;

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @TableField(value = "process_name")
    private String processName;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @TableField(value = "initiation_time")
    private Date initiationTime;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @TableField(value = "initiator")
    private String initiator;

    /**
     * 报销人
     */
    @ApiModelProperty(value = "报销人")
    @TableField(value = "claimant")
    private String claimant;

    /**
     * 申请公司编码
     */
    @ApiModelProperty(value = "申请公司编码")
    @TableField(value = "apply_company_code")
    private String applyCompanyCode;

    /**
     * 申请公司名称
     */
    @ApiModelProperty(value = "申请公司名称")
    @TableField(value = "apply_company_name")
    private String applyCompanyName;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @TableField(value = "apply_dept")
    private String applyDept;

    /**
     * 费用归属公司编码
     */
    @ApiModelProperty(value = "费用归属公司编码")
    @TableField(value = "expense_company_code")
    private String expenseCompanyCode;

    /**
     * 费用归属公司名称
     */
    @ApiModelProperty(value = "费用归属公司名称")
    @TableField(value = "xpense_company_name")
    private String xpenseCompanyName;

    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    @TableField(value = "reimbursed_amount")
    private String reimbursedAmount;

    /**
     * 要求付款时间
     */
    @ApiModelProperty(value = "要求付款时间")
    @TableField(value = "req_payment_time")
    private Date reqPaymentTime;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 折合人民币
     */
    @ApiModelProperty(value = "折合人民币")
    @TableField(value = "in_rmb")
    private String inRmb;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    @TableField(value = "apply_reason")
    private String applyReason;

    /**
     * 费用信息
     */
    @ApiModelProperty(value = "费用信息")
    @TableField(value = "expense_info")
    private String expenseInfo;

    /**
     * 供应商信息
     */
    @ApiModelProperty(value = "供应商信息")
    @TableField(value = "supplier_info")
    private String supplierInfo;

    /**
     * 是否内部交易
     */
    @ApiModelProperty(value = "是否内部交易")
    @TableField(value = "is_internal_tx")
    private Boolean isInternalTx;

    /**
     * 内部交易号
     */
    @ApiModelProperty(value = "内部交易号")
    @TableField(value = "internal_tx_number")
    private String internalTxNumber;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @TableField(value = "process_status")
    private String processStatus;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @TableField(value = "payment_way")
    private String paymentWay;

    /**
     * 立项类别
     */
    @ApiModelProperty(value = "立项类别")
    @TableField(value = "project_category")
    private String projectCategory;

    /**
     * 立项号
     */
    @ApiModelProperty(value = "立项号")
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    @TableField(value = "bk_dept")
    private String bkDept;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @TableField(value = "payment_amount")
    private String paymentAmount;

    /**
     * 交易金额
     */
    @ApiModelProperty(value = "交易金额")
    @TableField(value = "transaction_amount")
    private String transactionAmount;

    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额")
    @TableField(value = "actual_payment_amount")
    private String actualPaymentAmount;

    /**
     * 申请笔数
     */
    @ApiModelProperty(value = "申请笔数")
    @TableField(value = "apply_number")
    private String applyNumber;

    /**
     * 报销金额
     */
    @ApiModelProperty(value = "报销金额")
    @TableField(value = "reimbursement_amount")
    private String reimbursementAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
