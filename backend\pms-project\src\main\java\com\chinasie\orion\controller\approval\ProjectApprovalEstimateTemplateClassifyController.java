package com.chinasie.orion.controller.approval;

import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateClassifyDTO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateClassifyVO;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectApprovalEstimateTemplateClassify 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
@RestController
@RequestMapping("/projectApprovalEstimateTemplateClassify")
@Api(tags = "概算模板分类")
public class ProjectApprovalEstimateTemplateClassifyController {

    @Autowired
    private ProjectApprovalEstimateTemplateClassifyService projectApprovalEstimateTemplateClassifyService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【概算模板分类】【{{#name}}】详情", type = "ProjectApprovalEstimateTemplateClassify", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalEstimateTemplateClassifyVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectApprovalEstimateTemplateClassifyVO rsp = projectApprovalEstimateTemplateClassifyService.detail(id,pageCode);
        LogRecordContext.putVariable("name",rsp.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalEstimateTemplateClassifyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【概算模板分类】数据【{{#projectApprovalEstimateTemplateClassifyDTO.name}}】", type = "ProjectApprovalEstimateTemplateClassify", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO create(@RequestBody ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO) throws Exception {
        ProjectApprovalEstimateTemplateClassifyVO projectApprovalEstimateTemplateClassifyVO = projectApprovalEstimateTemplateClassifyService.create(projectApprovalEstimateTemplateClassifyDTO);
        LogRecordContext.putVariable("id", projectApprovalEstimateTemplateClassifyVO);
        return new ResponseDTO<>(projectApprovalEstimateTemplateClassifyVO);
    }

    /**
     * 编辑
     *
     * @param projectApprovalEstimateTemplateClassifyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【概算模板分类】数据【{{#projectApprovalEstimateTemplateClassifyDTO.name}}】", type = "ProjectApprovalEstimateTemplateClassify", subType = "编辑", bizNo = "{{#projectApprovalEstimateTemplateClassifyDTO.id}}")
    public ResponseDTO edit(@RequestBody  ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO) throws Exception {
        ProjectApprovalEstimateTemplateClassifyVO rsp = projectApprovalEstimateTemplateClassifyService.edit(projectApprovalEstimateTemplateClassifyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【概算模板分类】数据", type = "ProjectApprovalEstimateTemplateClassify", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateClassifyService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【概算模板分类】数据", type = "ProjectApprovalEstimateTemplateClassify", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateClassifyService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【概算模板分类】分页数据", type = "ProjectApprovalEstimateTemplateClassify", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectApprovalEstimateTemplateClassifyVO>> pages(@RequestBody Page<ProjectApprovalEstimateTemplateClassifyDTO> pageRequest) throws Exception {
        Page<ProjectApprovalEstimateTemplateClassifyVO> rsp =  projectApprovalEstimateTemplateClassifyService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 详情
     *
     * @param keyWord
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【概算模板分类】列表数据", type = "ProjectApprovalEstimateTemplateClassify", subType = "列表查询", bizNo = "")
    public ResponseDTO list(@RequestParam(required = false)String keyWord) throws Exception {
        return new ResponseDTO<>(projectApprovalEstimateTemplateClassifyService.list(keyWord));
    }

}
