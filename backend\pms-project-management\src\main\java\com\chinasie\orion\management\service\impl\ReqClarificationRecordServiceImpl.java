package com.chinasie.orion.management.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.domain.dto.ReqClarificationRecordDTO;
import com.chinasie.orion.management.domain.entity.ReqClarificationRecord;
import com.chinasie.orion.management.domain.vo.ReqClarificationRecordVO;
import com.chinasie.orion.management.repository.ReqClarificationRecordMapper;
import com.chinasie.orion.management.service.ReqClarificationRecordService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * ReqClarificationRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27 09:31:59
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ReqClarificationRecordServiceImpl extends  OrionBaseServiceImpl<ReqClarificationRecordMapper, ReqClarificationRecord>   implements ReqClarificationRecordService {

    private final OrionJ2CacheService orionJ2CacheService;
    private final FileApiService fileApi;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReqClarificationRecordVO detail(String id, String pageCode) throws Exception {
        ReqClarificationRecord reqClarificationRecord = this.getById(id);
        ReqClarificationRecordVO result = BeanCopyUtils.convertTo(reqClarificationRecord, ReqClarificationRecordVO::new);
        setEveryName(Collections.singletonList(result));

        List<FileVO> fileVOList = fileApi.getFilesByDataId(id);
        result.setFileList(fileVOList);

        return result;
    }

    /**
     *  新增
     *
     * * @param reqClarificationRecordDTO
     */
    @Override
    public  String create(ReqClarificationRecordDTO reqClarificationRecordDTO) throws Exception {
        ReqClarificationRecord reqClarificationRecord =BeanCopyUtils.convertTo(reqClarificationRecordDTO,ReqClarificationRecord::new);
        this.save(reqClarificationRecord);

        String rsp=reqClarificationRecord.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param reqClarificationRecordDTO
     */
    @Override
    public Boolean edit(ReqClarificationRecordDTO reqClarificationRecordDTO) throws Exception {
        ReqClarificationRecord reqClarificationRecord =BeanCopyUtils.convertTo(reqClarificationRecordDTO,ReqClarificationRecord::new);

        this.updateById(reqClarificationRecord);

        String rsp=reqClarificationRecord.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReqClarificationRecordVO> pages( Page<ReqClarificationRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ReqClarificationRecord> condition = new LambdaQueryWrapperX<>( ReqClarificationRecord. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ReqClarificationRecord::getCreateTime);


        Page<ReqClarificationRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ReqClarificationRecord::new));

        PageResult<ReqClarificationRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReqClarificationRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReqClarificationRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReqClarificationRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "需求澄清记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ReqClarificationRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ReqClarificationRecordExcelListener excelReadListener = new ReqClarificationRecordExcelListener();
        EasyExcel.read(inputStream,ReqClarificationRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ReqClarificationRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("需求澄清记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ReqClarificationRecord> reqClarificationRecordes =BeanCopyUtils.convertListTo(dtoS,ReqClarificationRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ReqClarificationRecord-import::id", importId, reqClarificationRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ReqClarificationRecord> reqClarificationRecordes = (List<ReqClarificationRecord>) orionJ2CacheService.get("pmsx::ReqClarificationRecord-import::id", importId);
        log.info("需求澄清记录导入的入库数据={}", JSONUtil.toJsonStr(reqClarificationRecordes));

        this.saveBatch(reqClarificationRecordes);
        orionJ2CacheService.delete("pmsx::ReqClarificationRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ReqClarificationRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ReqClarificationRecord> condition = new LambdaQueryWrapperX<>( ReqClarificationRecord. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ReqClarificationRecord::getCreateTime);
        List<ReqClarificationRecord> reqClarificationRecordes =   this.list(condition);

        List<ReqClarificationRecordDTO> dtos = BeanCopyUtils.convertListTo(reqClarificationRecordes, ReqClarificationRecordDTO::new);

        String fileName = "需求澄清记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ReqClarificationRecordDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ReqClarificationRecordVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ReqClarificationRecordExcelListener extends AnalysisEventListener<ReqClarificationRecordDTO> {

        private final List<ReqClarificationRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReqClarificationRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReqClarificationRecordDTO> getData() {
            return data;
        }
    }


}
