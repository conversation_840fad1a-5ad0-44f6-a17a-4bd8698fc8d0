import { ref } from 'vue';
import {
  cloneDeep, concat, forEach, get, map, reduce, set,
} from 'lodash-es';
import { createGlobalState } from '@vueuse/shared';
import { parsePriceByNumber } from '/@/views/spm/utils/utils';
import Api from '/@/api';

export const useContractViewTableHeader = createGlobalState(() => {
  const tableDynamicTheadHeaders = ref([]);
  const tableFixedTheadHeaders = ref([
    {
      minWidth: 150,
      align: 'right',
      title: '人力成本预算总额',
      dataIndex: 'personTotalBudget',
      customRender({ text }) {
        return text ? parsePriceByNumber(text) : '0.00';
      },
    },
    {
      minWidth: 150,
      align: 'right',
      title: '人力预算执行总额',
      dataIndex: 'personActualTotalMoney',
      customRender({ text }) {
        return text ? parsePriceByNumber(text) : '0.00';
      },
    },
    {
      minWidth: 140,
      align: 'right',
      title: '岗级计划总人数',
      dataIndex: 'personTotalCount',
      customRender({ text }) {
        return text ? parsePriceByNumber(text) : '0.00';
      },
    },
    {
      minWidth: 140,
      align: 'right',
      title: '岗级实际总人数',
      dataIndex: 'personActualPerson',
      customRender({ text }) {
        return text ? parsePriceByNumber(text) : '0.00';
      },
    },
  ]);
  const generateTableTheadHeaderCfg = (result) => reduce(result, (prev, cur) => {
    const basicColumn = {
      minWidth: 130,
      align: 'right',
      customRender({ text }) {
        return text ? parsePriceByNumber(text) : '0.00';
      },
    };
    const columns = map(cur, (value, prop) => {
      const newColumn = cloneDeep(basicColumn);
      set(newColumn, 'minWidth', prop.length * 20);
      set(newColumn, 'title', prop);
      set(newColumn, 'dataIndex', value);
      return newColumn;
    });
    return concat(prev, columns);
  }, []);
  async function getTableDynamicTheadHeaders() {
    try {
      const result = await new Api('/spm/contractMain/sheet/heads').fetch('', '', 'GET');
      tableDynamicTheadHeaders.value = generateTableTheadHeaderCfg(result);
    } catch (e) {
    }
  }

  function parseTableTree(result, currYear, setUuid) {
    const afterResult = map(result, (row) => {
      const beforeRow = cloneDeep(row);
      forEach(get(beforeRow, 'sheetHeadsMap', {}), (value, prop) => {
        set(beforeRow, prop, value);
      });
      setUuid?.(beforeRow, 'first');
      set(beforeRow, 'year', currYear);

      // 处理中心
      set(beforeRow, 'children', map(get(beforeRow, 'planList', []), (childRow) => {
        const beforeChildRow = cloneDeep(childRow);
        forEach(get(beforeChildRow, 'sheetHeadsMap', {}), (childValue, childProp) => {
          set(beforeChildRow, childProp, childValue);
        });
        set(beforeChildRow, 'year', currYear);
        setUuid?.(beforeChildRow, 'second');
        return beforeChildRow;
      }));
      return beforeRow;
    });
    return afterResult;
  }

  return {
    tableDynamicTheadHeaders,
    tableFixedTheadHeaders,
    getTableDynamicTheadHeaders,
    parseTableTree,
  };
});