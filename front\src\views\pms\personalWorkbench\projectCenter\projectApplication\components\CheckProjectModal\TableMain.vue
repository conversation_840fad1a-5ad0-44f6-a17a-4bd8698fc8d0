<script setup lang="ts">
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const tableRef:Ref = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  rowSelection: {
    type: 'radio',
  },
  api: (params) => new Api('/pms/projectDeclare/project').fetch({
    ...params,
  }, 'page', 'POST'),
  columns: [
    {
      title: '项目编号',
      dataIndex: 'number',
    },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '开始日期',
      dataIndex: 'projectStartTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '结束日期',
      dataIndex: 'projectEndTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
      width: 100,
    },
    {
      title: '子类型',
      dataIndex: 'projectSubTypeName',
      width: 100,
    },
    {
      title: '项目预估金额',
      dataIndex: 'estimateMoney',
      width: 100,
    },
  ],
};

defineExpose({
  getSelectRows: computed(() => tableRef.value.getSelectRows),
});
</script>

<template>
  <div style="height: 500px;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
