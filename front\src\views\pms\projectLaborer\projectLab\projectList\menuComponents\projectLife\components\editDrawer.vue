<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="'修改'+drawerName"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div class="drawar-content">
      <div class="require-title red-star">
        {{ drawerName }}说明：
      </div>
      <Textarea
        v-model:value="drawerContent"
        class="content-textarea"
        :autosize="{ minRows: 6, maxRows: 10 }"
        :maxlength="1000"
      />
      <div>
        <div>
          <Checkbox v-model:checked="isAttachment">
            流程节点模板
          </Checkbox>
        </div>
        <UploadList
          :listData="uploadFileData"
          type="modal"
          :on-change="onChange"
        />
        <!--        <OrionTable-->
        <!--          ref="tableRef"-->
        <!--          :options="tableOptions"-->
        <!--          class="pay-node-table"-->
        <!--          @buttonClick="onButtonClick"-->
        <!--        >-->
        <!--          <template #toolbarLeft>-->
        <!--            <div>-->
        <!--              <BasicUpload-->
        <!--                v-if="isAttachment"-->
        <!--                :max-number="100"-->
        <!--                :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"-->
        <!--                :isClassification="false"-->
        <!--                :isToolRequired="false"-->
        <!--                zIndex="1001"-->
        <!--                button-text="文件上传"-->
        <!--                @saveChange="saveChange"-->
        <!--              />-->
        <!--            </div>-->
        <!--          </template>-->
        <!--          <template #name="{ record }">-->
        <!--            <span-->
        <!--              class="action-btn"-->
        <!--              @click="onOpenFile(record)"-->
        <!--            >{{ record.name+'.'+record.filePostfix }}</span>-->
        <!--          </template>-->
        <!--        </OrionTable>-->

        <!--        <div class="source-table-slots">-->
        <!--          <div-->
        <!--            v-for="(item,index) in drawerAttachments"-->
        <!--            :key="index"-->
        <!--            class="link-area flex-pj flex"-->
        <!--          >-->
        <!--            <div>-->
        <!--              <Icon-->
        <!--                :size="14"-->
        <!--                icon="orion-icon-link"-->
        <!--              />-->

        <!--              <span class="link-text action-btn">{{ item.name }}</span>-->
        <!--            </div>-->

        <!--            <Icon-->
        <!--              :size="14"-->
        <!--              icon="orion-icon-close"-->
        <!--              @click="deleteFile(item.id,index)"-->
        <!--            />-->
        <!--          </div>-->

        <!--          <div-->
        <!--            v-for="(item,index) in uploadFileData"-->
        <!--            :key="index"-->
        <!--            class="link-area flex-pj flex"-->
        <!--          >-->
        <!--            <div>-->
        <!--              <Icon-->
        <!--                :size="14"-->
        <!--                icon="orion-icon-link"-->
        <!--              />-->

        <!--              <span class="link-text action-btn">{{ item.name }}</span>-->
        <!--            </div>-->

        <!--            <Icon-->
        <!--              class="action-btn"-->
        <!--              :size="14"-->
        <!--              icon="orion-icon-close"-->
        <!--              @click="deleteFile(item.id,index)"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </div>

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup>
import {
  computed, defineEmits, ref, unref,
} from 'vue';
import {
  BasicButton,
  BasicDrawer,
  BasicUpload,
  openFile,
  OrionTable,
  randomString, UploadList,
  useDrawerInner,
} from 'lyra-component-vue3';
import { Checkbox, message, Textarea } from 'ant-design-vue';
import Api from '/@/api';

const submitLoading = ref(false);
const [modalRegister, { closeDrawer }] = useDrawerInner(
  (drawerData) => {
    drawerContent.value = drawerData.data.value.content;
    uploadFileData.value = (drawerData.data.value?.attachments ?? []).map((item) => ({
      ...item,
      tableId: randomString(10),
    }));
    drawerAction.value = drawerData.data.value.actions;
    drawerNodeKey.value = drawerData.data.value.nodeKey;
    drawerName.value = drawerData.data.value.name;
    projectNodeId.value = drawerData.data.value.id;
  },
);
const emit = defineEmits(['editSuccess']);
const isAttachment = ref(false);
const drawerContent = ref<string>('');
const drawerAttachments = ref<any>([]);
const drawerAction = ref<any>([]);
const drawerNodeKey = ref<string>('');
const projectId = ref<string>('');
const projectNodeId = ref<string>('');
const drawerName = ref<string>('');
// const allFileDatas = ref<any>([]);

const uploadFileData = ref<any>([]);

const handleClose = () => {
  // relationList.value = [];
  // values.value = [];
  closeDrawer();
  //   context.emit('close');
};

async function saveChange(data) {
  let item = data[0];
  let reader = new FileReader();
  reader.readAsDataURL(item.file);

  let fileData = [];
  for (let i in data) {
    fileData.push({
      tableId: randomString(10),
      editDrawer: i,
      name: data[i].name,
      filePath: data[i].result.filePath,
      filePostfix: data[i].result.filePostfix,
      fileSize: data[i].size,
    });
  }
  uploadFileData.value = [...uploadFileData.value, ...fileData];

  // updateTable();
}

const deleteImgArray = ref<any>([]);
function deleteFile(recordId) {
  if (recordId) {
    // 使用filter方法过滤掉id不等于要删除的id的元素
    uploadFileData.value = uploadFileData.value.filter((item) => item.tableId !== recordId);
    message.success('删除成功');
  }
}
function onOpenFile(record) {
  openFile({
    ...record,
    type: 'page',
  });
}
function onChange(listData) {
  for (let i = 0; i < listData.length; i++) {
    const obj = listData[i];

    // 如果对象的id属性为空，则添加editDrawer:1属性
    if (obj.id === null) {
      obj.editDrawer = i;
      obj.tableId = randomString(10);
      obj.Id = randomString(10);
    }
  }

  uploadFileData.value = listData;
}
const tableOptions = {
  showSmallSearch: false,
  deleteToolButton: 'add|enable|disable',
  rowSelection: {},
  pagination: false,
  rowKey: 'tableId',
  dataSource: computed(() => uploadFileData.value),
  actions: [
    {
      text: '查看',
      onClick(record) {
        openFile(record);
      },
    },
    {
      text: '删除',
      onClick(record) {
        deleteFile(record.tableId);
      },
    },
    // {
    //   text: '删除',
    //   modal(record) {
    //     return batchDelete([record.id]).then(() => {
    //       tableReload();
    //     });
    //   },
    // },
  ],
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
      key: 'name',
      minWidth: 700,
      align: 'left',
      slots: { customRender: 'name' },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
};
function onButtonClick({ type, selectColumns }) {
  if (type === 'delete') {
    uploadFileData.value = uploadFileData.value.filter((item) => !selectColumns.keys.some((key) => item.tableId === key));
    message.success('删除成功');
  }
}
async function handleSubmit() {
  if (drawerContent.value === '') {
    message.success('说明内容不能为空');
    return;
  }

  const result = await new Api('/pms/project-life-cycle/node').fetch({
    nodeKey: drawerNodeKey.value,
    content: drawerContent.value,
    attachments: uploadFileData.value,
    isAttachment: isAttachment.value,

    id: projectNodeId.value,
  }, '', 'PUT');

  closeDrawer();

  drawerAttachments.value = [];
  uploadFileData.value = [];

  message.success('修改成功');
  emit('editSuccess', result);
}

</script>

<style lang="less" scoped>
.drawar-content {
  padding:~`getPrefixVar('content-margin')`;
  .require-title {
    margin:0 0 ~`getPrefixVar('button-margin')` 0;
    font-weight: 700;
  }
  .red-star::before {
    display: inline-block;
    margin-right: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun,sans-serif;
    line-height: 1;
    content: "*";

  }
  .content-textarea {
    margin:0 0 ~`getPrefixVar('button-margin')` 0;
  }
  .link-area {
    width:600px;
    margin: ~`getPrefixVar('button-margin')` 0 0 0;
  }

}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      box-sizing: border-box;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      height: 32px;
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
