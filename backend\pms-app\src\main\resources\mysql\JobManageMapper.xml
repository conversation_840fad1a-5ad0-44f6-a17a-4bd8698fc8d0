<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.JobManageMapper">
    <insert id="insertBatch">
        insert into pmsx_project_job(project_id,job_id) values
        <foreach collection="jobIds" item="jobId" separator=",">
            (#{projectId},#{jobId})
        </foreach>
    </insert>

    <delete id="deleteRelations">
        delete from pmsx_project_job where project_id = #{projectId}
    </delete>

    <delete id="deleteProgressByProjectId">
        delete from pmsx_project_progress where project_id in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
    </delete>
    <delete id="deleteProgressByProgressId">
        delete from pmsx_org_progress where progress_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </delete>
    <delete id="deleteProjectJobRelationByJobId">
        delete from pmsx_project_job where job_id = #{jobId}
    </delete>
    <delete id="deleteOrgProgressByProgressId">
        delete from pmsx_org_progress where progress_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


    <select id="count" resultType="java.lang.Long">
        select sum(b.a) from
        (
                select count(1) as a from pmsx_job_material where job_id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                and logic_status = 1
                union all
                select count(1) as a from pmsx_job_post_authorize where job_id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                and logic_status = 1
        )b
    </select>


    <update id="updateJobManage" parameterType="java.util.List">
        UPDATE pmsx_job_manage
        SET
        supervisory_staff_name = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.checkName}
        </foreach>
        END,
        job_dept_code = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDept}
        </foreach>
        END,
        job_dept_name = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDeptName}
        </foreach>
        END,
        job_dept_code = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDept}
        </foreach>
        END,
        job_dept_name = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDeptName}
        </foreach>
        END,
        job_dept_code = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDept}
        </foreach>
        END,
        job_dept_name = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.operatingDeptName}
        </foreach>
        END,
        work_place = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.jobAddrName}
        </foreach>
        END,
        supervisory_staff_code = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.checkId}
        </foreach>
        END,
        manage_person_name = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.managerName}
        </foreach>
        END,
        manage_person_code = CASE number
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="" separator=" " close="">
            WHEN #{detail.workOrderNo} THEN #{detail.managerId}
        </foreach>
        END
        WHERE number IN
        <foreach collection="jobHeightRiskCopyList" item="detail" index="index" open="(" separator="," close=")">
            #{detail.workOrderNo}
        </foreach>
    </update>
    <update id="updateNumberById">
        UPDATE pmsx_job_manage  SET number=#{number},
                                    name=#{name},
                                    n_or_o=#{nOrO},
                                    work_center=#{workCenter},
                                    job_base=#{jobBase},
                                    begin_time=#{beginTime},
                                    end_time=#{endTime},
                                    actual_begin_time=#{actualBeginTime},
                                    actual_end_time=#{actualEndTime},
                                    work_duration=#{workduration},
                                    repair_round=#{repairRound},
                                    phase=#{phase},
                                    modify_id=#{modifyId},
                                    modify_time=#{modifyTime},
                                    status=#{status} ,
                                    logic_status=#{logicStatus}
                                    ,rsp_user_id=#{rspUserId},
                                    rsp_user_code=#{rspUserCode},
                                    rsp_dept=#{rspDept},
                                    first_execute=#{firstExecute},
                                    anti_forfeign_level=#{antiForfeignLevel},
                                    is_high_risk=#{isHighRisk}
                                WHERE id=#{id}


    </update>
    <select id="countJobNum" resultType="com.chinasie.orion.domain.vo.count.ProjectJobCountVO">
        select project_id as projectId,count(job_id) as jobCount
        from pmsx_project_job where project_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by project_id
    </select>
    <select id="countJobPackageByProjectIds"
            resultType="com.chinasie.orion.domain.vo.count.ProjectPackageCountVO">
        select ppj.project_id, count(pjp.job_id) as packageCount
        from pmsx_project_job ppj
                 INNER JOIN pmsx_job_manage pjm on ppj.job_id = pjm.id
                 INNER JOIN pmsx_job_package pjp on pjm.id = pjp.job_id
        where pjp.logic_status = 1
        GROUP BY ppj.project_id
        having ppj.project_id in
        <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="countJobPackageByProjectIdsFinish"
            resultType="com.chinasie.orion.domain.vo.count.ProjectPackageCountVO">
        select ppj.project_id, count(pjp.job_id) as packageCount
        from pmsx_project_job ppj
        INNER JOIN pmsx_job_manage pjm on ppj.job_id = pjm.id
        INNER JOIN pmsx_job_package pjp on pjm.id = pjp.job_id
        where pjp.status = 130 and pjm.logic_status = 1
        GROUP BY ppj.project_id
        having ppj.project_id in
        <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getNewestSchedule" resultType="com.chinasie.orion.domain.vo.ProjectScheduleVO">
        SELECT
        newest.project_id as 'id',
        pjp.progress_schedule as 'schedule'
        FROM
        (
            SELECT
                ppp.project_id,
                max( pjp.work_date ) as date
            FROM
                pmsx_project_progress ppp
            INNER JOIN pmsx_job_progress pjp ON ppp.progress_id = pjp.id
            WHERE
                pjp.logic_status = 1
            AND ppp.project_id IN
            <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            GROUP BY ppp.project_id
        ) newest
        INNER JOIN pmsx_project_progress ppp on newest.project_id = ppp.project_id
        INNER JOIN pmsx_job_progress pjp on pjp.id = ppp.progress_id and pjp.work_date = newest.date
    </select>

    <select id="selectProgressByProjectId" resultType="java.lang.String">
        select progress_id from pmsx_project_progress where project_id in
        <foreach collection="projectIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
--         and logic_status = 1
    </select>
    <select id="selectJobIdsByRepairRound" resultType="java.lang.String">
        SELECT DISTINCT
            ( ppj.job_id )
        FROM
            pmsx_project_job ppj
                INNER JOIN pmsx_important_project pip ON ppj.project_id = pip.id
        where pip.repair_round = #{repairRound} and ppj.project_id != #{projectId} and pip.logic_status = 1
    </select>

    <select id="selectProjectJobIdsByProjectId" resultType="java.lang.String">
        select job_id from pmsx_project_job where project_id = #{id}
    </select>
    <select id="selectCountProgressDate" resultType="java.lang.Integer">
        SELECT
            count(ppp.progress_id)
        FROM
            pmsx_project_progress ppp
                INNER JOIN pmsx_job_progress pjp ON ppp.progress_id = pjp.id
        where ppp.project_id = #{projectId} and pjp.work_date = #{workDate}
    </select>


    <select id="selectCountProgressDateJobId" resultType="java.lang.Integer">
        SELECT
            count(ppp.progress_id)
        FROM
            pmsx_project_progress ppp
                INNER JOIN pmsx_job_progress pjp ON ppp.progress_id = pjp.id
        where ppp.project_id = #{projectId} and pjp.work_date = #{workDate} and pjp.id != #{id}
    </select>
    <select id="getNoProgressProject" resultType="java.lang.String">
        SELECT DISTINCT
            ( ppj.project_id )
        FROM
            pmsx_project_job ppj
                LEFT JOIN pmsx_project_progress ppp ON ppj.project_id = ppp.project_id
                INNER JOIN pmsx_important_project pip ON pip.id = ppj.project_id
                LEFT JOIN pmsx_job_progress pjp ON pjp.id = ppp.progress_id
                AND DATE( pjp.work_date ) = CURDATE()
        WHERE
            acture_start IS NOT NULL
          AND (
                (NOW() BETWEEN pip.acture_start AND pip.acture_end )
                OR
                ( NOW() > pip.acture_start AND pip.acture_end IS NULL )
              )
          AND pip.logic_status = 1
    </select>
    <select id="selectProjectPackageStatusByJobIds"
            resultType="com.chinasie.orion.domain.vo.job.JobPackageStatusVO">
        select job_id as jobId, status
        from pmsx_job_package
        where job_id in
        <foreach collection="jobIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

<!-- and ppj.logic_status = 1 -->
    <select id="getMaterialIdList" resultType="java.lang.String">
        select pjm.material_id from pmsx_project_job ppj
        INNER JOIN pmsx_job_material pjm on ppj.job_id = pjm.job_id and pjm.logic_status = 1
        where ppj.project_id = #{projectId} and pjm.material_id is not null
        <if test=" null != keyword  and keyword !=''">
            and (
                   pjm.`number` like concat('%',#{keyword},'%')
                or pjm.`asset_code` like concat('%',#{keyword},'%')
                or pjm.`asset_name` like concat('%',#{keyword},'%')
                 or pjm.`product_code` like concat('%',#{keyword},'%')
            )
        </if>
    </select>

<!-- and ppj.logic_status = 1 -->
    <select id="getPersonIdList" resultType="java.lang.String">
        select pkpa.person_manage_id  from pmsx_project_job ppj
        INNER JOIN pmsx_job_post_authorize pkpa on ppj.job_id = pkpa.job_id and pkpa.logic_status = 1
        where ppj.project_id = #{projectId} and pkpa.person_id is not null
        <if test=" null != keyword  and keyword !=''">
            and  pkpa.`person_code` like concat('%',#{keyword},'%')
        </if>
    </select>

    <select id="selectImportantProjectRspUserByJobId" resultType="java.lang.String">
        select rsp_user_id
        from pmsx_important_project pip
                 INNER JOIN pmsx_project_job ppj on pip.id = ppj.project_id
        where job_id = #{jobId}
    </select>
    <select id="selectCountProgressDateByRepairOrgId" resultType="java.lang.Integer">
        SELECT count(ppp.progress_id)
        FROM pmsx_project_progress ppp
                 INNER JOIN pmsx_job_progress pjp ON ppp.progress_id = pjp.id
        where ppp.project_id = #{repairOrgId}
          and pjp.work_date = #{workDate}
    </select>
    <select id="selectCountProgressDateJobIdByRepairOrgId" resultType="java.lang.Integer">
        SELECT count(ppp.progress_id)
        FROM pmsx_project_progress ppp
                 INNER JOIN pmsx_job_progress pjp ON ppp.progress_id = pjp.id
        where ppp.project_id = #{repairOrgId}
          and pjp.work_date = #{workDate}
          and pjp.id != #{id}
    </select>


    <select id="selectIdsByProjectIdAndJobId" resultType="com.chinasie.orion.domain.vo.ProjectJobIdsVO">
        SELECT
            project_id AS 'projectId',
            job_id AS 'jobId'
        FROM
            pmsx_project_job
        WHERE
                project_id = (
                SELECT DISTINCT
                    ( project_id )
                FROM
                    pmsx_project_job
                WHERE
                    job_id = #{jobId})
    </select>

    <select id="getMajorRepairOrgSimpleVO" resultType="com.chinasie.orion.domain.dto.MajorRepairOrgJobSimpleDTO">
        select
            t2.id,
            t2.begin_time as 'beginTime',
            t2.end_time as 'endTime',
            t2.actual_begin_time as 'actualBeginTime',
            t2.actual_end_time as 'actualEndTime'
        from pmsx_relation_org_to_job t1
                 inner join pmsx_job_manage t2 on t1.job_number = t2.`number`
        where t1.logic_status = 1 and t2.logic_status = 1
        AND t1.repair_org_id = #{orgId}
    </select>
    <select id="listByNumberList" resultType="com.chinasie.orion.domain.entity.JobManage">
        SELECT
            jm.id,
            jm.number,
            jm.phase
        FROM
            pmsx_job_manage jm
        WHERE
            jm.number IN
        <foreach collection="jobNumberList" item="number" open="(" close=")" separator=",">
            #{number}
        </foreach>
    </select>
</mapper>
