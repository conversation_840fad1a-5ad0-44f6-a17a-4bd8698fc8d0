<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, openBasicSelectModal, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import { useMaterialSearch } from '/@/views/pms/materialManage/components/hooks';
import { message } from 'ant-design-vue';
import { MaterialDrawerRecord } from '../utils';

const props = defineProps<{
  record: MaterialDrawerRecord
}>();

const assetType: Ref<string> = ref('pms_fixed_assets');// 830特殊处理
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'assetType',
    component: 'SelectDictVal',
    label: '资产类型',
    required: true,
    defaultValue: 'pms_fixed_assets', // 830特殊处理
    componentProps: {
      // disabled: props.record.id,
      disabled: true, // 830特殊处理
      dictNumber: 'pms_supplies_type',
      async onChange(value: string) {
        assetType.value = value;
        await resetFields();
        await setFieldsValue({
          assetType: value,
        });
      },
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '资产编码/条码',
    required: computed(() => assetType.value === 'pms_fixed_assets'),
    componentProps({ formModel }) {
      return {
        readonly: true,
        disabled: ['out', 'in'].includes(props.record.operationType),
        onClick() {
          if (!formModel.assetType) {
            message.info('请先选择资产类型');
            return;
          }
          openBasicSelectModal({
            width: 1200,
            title: '请选择',
            ...getNumberProps(formModel.assetType, props.record),
            onOk(records) {
              const numberItem = records?.[0];
              if (numberItem) {
                setValueByMaterial(numberItem, formModel);
                if (props?.record?.operationType !== 'plan-pick') {
                  formModel.baseCode = numberItem?.storageLocation;
                  formModel.baseName = numberItem?.storageLocationName;
                }
                formModel.costCenterName = numberItem?.costCenterName;
                formModel.materialNumber = numberItem?.number || numberItem?.code;
                formModel.materialName = numberItem?.name;
              }
            },
          } as any);
        },
      };
    },
  },
  {
    field: 'productCode',
    component: 'Input',
    label: '产品编码',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'toolStatus',
    component: 'Select',
    label: '工具状态',
    componentProps() {
      return {
        disabled: true,
        options: [
          {
            label: '报废',
            value: 'scrap',
          },
          {
            label: '待报废',
            value: 'pending_scrap',
          },
          {
            label: '封存',
            name: 'frozen',
          },
          {
            label: '检定',
            name: 'validation',
          },
          {
            label: '可用',
            value: 'available',
          },
          {
            label: '损坏',
            value: 'damaged',
          },
          {
            label: '污损',
            value: 'soiled',
          },
        ],
        placeholder: ' ',
      };
    },
  },
  {
    field: 'maintenanceCycle',
    component: 'Input',
    label: '检定维护周期',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'assetCode',
    component: 'Input',
    label: '资产代码',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'assetName',
    component: 'Input',
    label: '资产名称',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'costCenter',
    component: 'TreeSelectOrg',
    label: '成本中心名称',
    componentProps() {
      return {
        fieldNames: { value: 'deptCode' },
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'specificationModel',
    component: 'Input',
    label: '规格型号',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'stockNum',
    component: 'InputNumber',
    label: '库存数量',
    componentProps() {
      return {
        disabled: true,
        min: 0,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'demandNum',
    component: 'InputNumber',
    label: '需求数量',
    required: true,
    defaultValue: 1,
    ifShow() {
      return props.record.operationType !== 'out';
    },
    componentProps() {
      return {
        min: 1,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'isVerification',
    component: 'Select',
    label: '是否需要检定',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'nextVerificationDate',
    component: 'DatePicker',
    label: '下次检定日期',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      disabled: true,
    },
  },
  {
    field: 'baseCode',
    component: 'ApiSelect',
    label: '物资所在地',
    ifShow() {
      return props.record.operationType !== 'out';
    },
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
        api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
      };
    },
  },
  // TODO 按发版要求暂时隐藏该字段
  // {
  //   field: 'isMetering',
  //   component: 'Select',
  //   label: '是否计量器具',
  //   ifShow() {
  //     return props.record.operationType !== 'out';
  //   },
  //   componentProps: {
  //     disabled: true,
  //     options: [
  //       {
  //         label: '是',
  //         value: true,
  //       },
  //       {
  //         label: '否',
  //         value: false,
  //       },
  //     ],
  //   },
  // },
];

const inSchemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资入库信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'actInDate',
    component: 'DatePicker',
    label: '实际入场日期',
    required: true,
    componentProps() {
      return {
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'inputStockNum',
    component: 'InputNumber',
    label: '实际入场数量',
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps() {
      return {
        precision: 0,
        min: 0,
        max: 999999,
      };
    },
  },
  {
    field: 'rspUserNo',
    component: 'SelectUser',
    label: '责任人工号',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps() {
      return {
        showField: 'code',
        selectUserModalProps: {
          selectType: 'radio',
        },
        onChange(user: any[]) {
          setFieldsValue({
            rspUserName: user[0]?.name,
          });
        },
      };
    },
  },
  {
    field: 'rspUserName',
    component: 'Input',
    label: '责任人姓名',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'isReport',
    component: 'Select',
    label: '是否向电厂报备',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps() {
      return {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'isOverdue',
    component: 'Select',
    label: '检定是否超期',
    componentProps() {
      return {
        disabled: true,
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
];

const outSchemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资入库信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'outNum',
    component: 'InputNumber',
    label: '实际离场数量',
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps({ formModel }) {
      return {
        precision: 0,
        min: 0,
        max: formModel.stockNum,
      };
    },
  },
  {
    field: 'actOutDate',
    component: 'DatePicker',
    label: '实际离场日期',
    required: true,
    componentProps() {
      return {
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'outReason',
    component: 'SelectDictVal',
    label: '出库原因',
    required: true,
    componentProps: {
      dictNumber: 'pms_out_reason',
    },
  },
  {
    field: 'materialDestination',
    component: 'Input',
    label: '物资去向',
    componentProps() {
      return {
        maxlength: 200,
      };
    },
  },
  {
    field: 'isAgainIn',
    component: 'Select',
    label: '是否再次入场',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps({ formModel }) {
      return {
        onChange() {
          formModel.inAndOutDate = [];
        },
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'inAndOutDateList',
    component: 'RangePicker',
    label: '计划入场离场时间',
    required: true,
    ifShow({ model }) {
      return model.isAgainIn;
    },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, resetFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [...schemas, ...(props.record.operationType === 'in' ? inSchemas : props.record.operationType === 'out' ? outSchemas : [])],
});
const {
  setValueByMaterial, getNumberProps,
} = useMaterialSearch(setFieldsValue);

onMounted(() => {
  if (props?.record?.id) {
    getFormData();
  } else {
    setFieldsValue(props.record);
  }
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/material-manage').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      rspUserNo: (result?.rspUserId && result?.rspUserName) ? [
        {
          id: result?.rspUserId,
          name: result?.rspUserName,
          code: result?.rspUserNo,
        },
      ] : undefined,
    });
    if (props.record.operationType === 'out') {
      await setFieldsValue({
        outNum: result.stockNum,
      });
    }
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      rspUserNo: formValues.rspUserNo?.[0]?.code,
      id: props?.record?.id,
      jobId: props?.record?.jobId,
    };

    return new Promise((resolve, reject) => {
      switch (props.record.operationType) {
        case 'pick':
          new Api('/pms/job-material').fetch(params, 'add', 'POST').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
        case 'plan-pick':
          new Api('/pms/schemeToMaterial').fetch({
            ...formValues,
            ...props.record,
          }, 'add', 'POST').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
        case 'in':
          new Api('/pms/material-manage/edit').fetch(params, '', 'PUT').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
        case 'out':
          new Api(`/pms/material-manage/${props.record.id}/out/bound`).fetch(params, '', 'PUT').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
      }
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
