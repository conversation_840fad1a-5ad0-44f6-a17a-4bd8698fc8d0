<script setup lang="ts">
import { useRouter } from 'vue-router';
import {
  reactive, Ref, ref, h, watchEffect, computed,
} from 'vue';
import Api from '/@/api';
import {
  Modal, RangePicker, Space, Select,
} from 'ant-design-vue';
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, downloadByData, isPower, openDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  get as loadGet, isEmpty, isBoolean, cloneDeep,
} from 'lodash-es';
import { openFormDrawer, parseBooleanToRender, parsePriceByNumber } from '../utils';

const router = useRouter();
const tableRef = ref();
const rowMoney = reactive([
  {
    key: 'total',
    title: '申请条数',
    value: '',
    suffix: '条',
  },
  {
    key: 'allMoney',
    title: '申请金额',
    value: '',
    suffix: '元',
  },
]);
const tableOptions = {
  showToolButton: false,
  filterConfigName: 'purchaseProjectApplication_filter001',
  isSpacing: true,
  pagination: {},
  rowSelection: {
  },
  smallSearchField: ['name'],
  filterConfig: {
    fields: [
      {
        field: 't1.name',
        fieldName: '采购立项申请名称',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't1.project_code',
        fieldName: '采购立项申请编号',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't2.project_name',
        fieldName: '项目名称',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't2.contract_type',
        fieldName: '合同类型',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't2.apply_department',
        fieldName: '需求部门',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't2.tech_respons',
        fieldName: '技术负责人',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't2.biz_respons',
        fieldName: '商务负责人',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
      {
        field: 't.already_time',
        fieldName: '截至本周已经耗时',
        fieldType: 'String',
        component: 'InputNumber',
        hidden: false,
      },
      {
        field: 't.contract_status',
        fieldName: '截至本周合同状态',
        fieldType: 'String',
        component: 'Input',
        hidden: false,
      },
    ],
  },
  columns: [

    {
      title: '采购计划编号',
      dataIndex: 'purchasePlanCode',
      width: 150,
    },
    {
      title: '采购立项申请名称',
      dataIndex: 'purchaseName',
      width: 180,
    },
    {
      title: '采购立项申请编号',
      dataIndex: 'purchReqDocCode',
      width: 180,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 180,
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      width: 120,
    },
    {
      title: '需求部门',
      dataIndex: 'applyDepartment',
      width: 150,
    },
    {
      title: '技术负责人',
      dataIndex: 'techRespons',
      width: 120,
    },
    {
      title: '商务负责人',
      dataIndex: 'bizRespons',
      width: 120,
    },
    {
      title: '采购立项审批完成时间',
      dataIndex: 'purchReqEndTime',
      width: 150,
      customRender: ({ record }) => (record.projectEndTime ? dayjs(record.projectEndTime).format('YYYY-MM-DD') : ''),
    },
    {
      title: '采购立项申请金额',
      dataIndex: 'purchReqAmount',
      width: 150,
    },
    {
      title: '时间',
      dataIndex: 'week',
      width: 120,
      customRender: ({ record }) => `${record.year}年第${record.week}周`,
    },
    {
      title: '截至本周已经耗时',
      dataIndex: 'alreadyTime',
      width: 120,
    },
    {
      title: '截至本周合同状态',
      dataIndex: 'contractStatus',
      width: 120,
    },
    {
      title: '上周工作安排',
      dataIndex: 'lastWorkPlan',
      width: 180,
    },
    {
      title: '上周工作完成情况',
      dataIndex: 'lastWorkContent',
      width: 180,
    },
    {
      title: '下周工作安排',
      dataIndex: 'nextWorkPlan',
      width: 180,
    },
    {
      title: '需技术部门领导关注内容',
      dataIndex: 'techLeaderAttentionContent',
      width: 180,
    },
    {
      title: '需商务部门领导关注内容',
      dataIndex: 'busLeaderAttentionContent',
      width: 180,
    },
    {
      title: '预计下周是否能完成',
      dataIndex: 'isNextCompleteName',
      width: 130,
    },
    {
      title: '是否已完成合同签章',
      dataIndex: 'isSignName',
      width: 130,
    },
    {
      title: '上周工作是否按计划完成',
      dataIndex: 'isNextCompleteName',
      width: 130,

    },
  ],
  api: (params: Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['wbsId', 'projectEndTime'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'wbsId') {
            Object.assign(query, {
              wbsId: first,
            });
          }
          if (filedProp === 'projectEndTime') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    pageSearchConditions.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/spm/purchProjectWeekly/pageExcel').fetch({
      ...params,

      // power: {
      //   pageCode: 'projectApplication001',
      //   containerCode: 'PMS_CGLXSQ_container_01',
      // },
      ...newSearchConditions,
      query: {
        ...getQuery(),
        keyword: keyword.value,
      },
    }, '', 'POST');
  },
};
const pageSearchConditions = ref(null);
const loadStatus: Ref<boolean> = ref(false);
const powerData = ref();

const selectRows: Ref<any[]> = ref([]);
const selectKeys: Ref<string[]> = ref([]);

// const showImportBtn = computed(() => isPower('PMS_CGLXSQ_container_01_button_04', powerData.value));
const showImportBtn = true;
const actions: IOrionTableActionItem[] = [];

// 表格多选回调
function selectionChange({ rows, keys }) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}
function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/spm/ncfFormpurchaseRequest').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const exportTable = () => {
  const myParams = {
    searchConditions: pageSearchConditions.value?.searchConditions || [],
    query: {
      ...pageSearchConditions.value?.query,
      ...getQuery(),
    },
  };

  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      let res = await downloadByData('/spm/purchProjectWeekly/export/excel', {
        ...(selectKeys.value.length > 0
          ? {
            query: {
              ids: selectKeys.value,
            },
          } : (myParams || {})),
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
};

const getPowerDataHandle = (data) => {
  powerData.value = data;
};

const yearOptions = Array.from({ length: 5 }, (_, i) => {
  const now = new Date().getFullYear();
  return {
    label: `${now - i}年`,
    value: now - i,
  };
});
const weekOptions = Array.from({ length: 53 }, (_, i) => ({
  label: `第${i + 1}周`,
  value: i + 1,
}));
const selectedYear = ref(yearOptions[0].value);
const selectedWeek = ref();
function handleYearChange(val) {
  selectedYear.value = val;
  tableRef.value?.reload();
}
function handleWeekChange(val) {
  selectedWeek.value = val;
  tableRef.value?.reload();
}
function getQuery() {
  const query: Record<string, any> = {};
  if (selectedYear.value) query.year = selectedYear.value;
  if (selectedWeek.value) query.week = selectedWeek.value;
  return query;
}
const keyword = ref('');
function keywordSearch(val: string) {
  keyword.value = val;
  tableRef.value.reload();
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
      @smallSearch="keywordSearch"
    >
      <template #toolbarLeft>
        <Space :size="12">
          <Select
            v-model:value="selectedYear"
            :options="yearOptions"
            style="width: 120px"
            placeholder="请选择年份"
            allowClear
            @change="handleYearChange"
          />
          <Select
            v-model:value="selectedWeek"
            :options="weekOptions"
            style="width: 120px"
            placeholder="请选择周"
            allowClear
            @change="handleWeekChange"
          />
          <BasicButton
            v-if="showImportBtn"
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

:deep(.ant-input-group-wrapper) {
  display: flex;
  align-items: center;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
