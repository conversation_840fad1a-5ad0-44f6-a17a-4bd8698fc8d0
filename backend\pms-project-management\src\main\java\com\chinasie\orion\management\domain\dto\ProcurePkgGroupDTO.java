package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProcurePkgGroup DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:49:40
 */
@ApiModel(value = "ProcurePkgGroupDTO对象", description = "采购项目包组")
@Data
@ExcelIgnoreUnannotated
public class ProcurePkgGroupDTO extends  ObjectDTO   implements Serializable{

    /**
     * 包组编号
     */
    @ApiModelProperty(value = "包组编号")
    @ExcelProperty(value = "包组编号 ", index = 0)
    private String groupId;

    /**
     * 包组名称
     */
    @ApiModelProperty(value = "包组名称")
    @ExcelProperty(value = "包组名称 ", index = 1)
    private String groupName;

    /**
     * 报价轮次
     */
    @ApiModelProperty(value = "报价轮次")
    @ExcelProperty(value = "报价轮次 ", index = 2)
    private Integer biddingRounds;

    /**
     * 报价开始时间
     */
    @ApiModelProperty(value = "报价开始时间")
    @ExcelProperty(value = "报价开始时间 ", index = 3)
    private Date BiddingBeginTime;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @ExcelProperty(value = "报价截止时间 ", index = 4)
    private Date BiddingEndTime;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    @ExcelProperty(value = "报价状态 ", index = 5)
    private String BiddingStatus;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @ExcelProperty(value = "需求编号 ", index = 6)
    private String requirementId;



}
