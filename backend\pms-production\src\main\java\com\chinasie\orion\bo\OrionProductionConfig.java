package com.chinasie.orion.bo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/08/11:34
 * @description:
 */
@Data
@Component
@ConfigurationProperties(prefix = "orion.production.data-json")
public class OrionProductionConfig {

    @ApiModelProperty("作业生命周期节点JSON")
    private String jobLifecycle;
}
