package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectInitiationWBSDTO;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.domain.vo.ProjectInitiationWBSVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectInitiationWBS 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
public interface ProjectInitiationWBSService extends OrionBaseService<ProjectInitiationWBS> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectInitiationWBSVO detail(String id, String pageCode) throws Exception;

    /**
     * 根据立项编号查询
     * <p>
     * * @param projectNumber
     */
    List<ProjectInitiationWBSVO> getByProjectNumber(String projectNumber) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectInitiationWBSDTO
     */
    String create(ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectInitiationWBSDTO
     */
    Boolean edit(ProjectInitiationWBSDTO projectInitiationWBSDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectInitiationWBSVO> pages(Page<ProjectInitiationWBSDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectInitiationWBSVO> vos) throws Exception;
}
