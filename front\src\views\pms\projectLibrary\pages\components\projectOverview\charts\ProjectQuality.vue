<!--项目计划模块开发-->
<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch,
} from 'vue';
import SpinView from '/@/views/pms/components/SpinView.vue';
import { useChart } from './useChart';
import Api from '/@/api';

const projectId:string = inject('projectId');
const loading:Ref<boolean> = ref(false);
const qualityInfo:Ref<Record<string, any>> = ref({});
const dataOptions:ComputedRef<any[]> = computed(() => [
  {
    name: '项目成果',
    value: qualityInfo.value.total || 0,
  },
  {
    color: '#60C057',
    name: '已交付项目成果',
    value: qualityInfo.value.done || 0,
  },
  {
    color: '#52C9F5',
    name: '未交付项目成果',
    value: qualityInfo.value.doing || 0,
  },
]);
const legendOptions = computed(() => dataOptions.value.filter((item) => item.color));
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: legendOptions.value.map((item) => item.color),
  series: [
    {
      name: '项目质量',
      type: 'pie',
      radius: ['60%', '75%'],
      center: ['45%', '50%'],
      label: {
        show: false,
      },
      data: legendOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
  chartInstance.value.hideLoading();
});

onMounted(() => {
  getQuality();
});

// 获取项目质量数据
async function getQuality() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/qualityCount/${projectId}`).fetch({
    }, '', 'GET');
    qualityInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="container-question">
    <!--Echarts圆环-->
    <div class="chart-question-circle">
      <div
        v-for="(item,index) in legendOptions"
        :key="index"
        class="custom-legend-item"
      />
    </div>
    <spin-view
      v-if="loading"
      class="chart-question"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="chart-question"
    />
    <!--数据显示-->
    <div class="data-show">
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
        class="custom-legend-title"
      >
        <span>{{ item.name }}：</span>
        <span class="value">{{ item.value || 0 }}个</span>
      </div>
    </div>
  </div>
  <div class="content-show-parent">
    <div class="content-show">
      <span>项目过程的规范性{{ qualityInfo.rate }}%</span>
      <span>效率评级：{{ qualityInfo.level }}</span>
    </div>
  </div>
</template>

<style scoped lang="less">
.container-question {
  display: flex;
  align-items: center;
}

.custom-legend-title + .custom-legend-title {
  margin-top: 10px;
}

.chart-question {
  width: 0;
  flex-grow: 1;
  height: 150px;
  margin: 0 12px;
}

.data-show{
  padding-right: 50px;
}

.content-show-parent{
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .content-show{
    width: 85%;
    border-top:1px dashed ~`getPrefixVar('border-color-base')`;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 14px;
  }
}

</style>
