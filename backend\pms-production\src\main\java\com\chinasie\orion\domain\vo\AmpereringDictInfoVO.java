package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * AmpereringDictInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-09-30 00:02:31
 */
@ApiModel(value = "AmpereringDictInfoVO对象", description = "业务字典表（明细表）")
@Data
public class AmpereringDictInfoVO extends  ObjectVO   implements Serializable{

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String typeId;


    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String parentId;


    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
    private String code;


    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    private String name;


    /**
     * 是否启用禁用【启用1，禁用0表示】
     */
    @ApiModelProperty(value = "是否启用禁用【启用1，禁用0表示】")
    private Boolean isEnabled;


    /**
     * 排序,默认0，升序排序
     */
    @ApiModelProperty(value = "排序,默认0，升序排序")
    private Integer sort;




}
