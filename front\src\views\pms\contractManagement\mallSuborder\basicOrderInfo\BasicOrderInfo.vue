<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { formatRenderRes } from '../token/util';

interface Props {
  dataProps: any
}

const props = withDefaults(defineProps<Props>(), {
  dataProps: {},
});
const formatDateTime = (dateStr: string) => (dateStr ? dayjs(dateStr).format('YYYY-MM-DD') : '');

</script>

<template>
  <BasicCard title="基本信息">
    <div class="layout-info">
      <div class="li-row">
        <div class="l-property">
          下单人
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
          <span>框架合同名称{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
          <span>框架合同A{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          下单企业
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.orderBusiness) }}</span>
          <span>框架合同编号{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
          <span>SNK_20213450{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          下单人电话
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.orderTel) }}</span>
        </div>
      </div>
    </div>
  </BasicCard>
  <BasicCard title="收货信息">
    <div class="layout-info">
      <div class="li-row">
        <div class="l-property">
          收货人
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.receivePerson) }}</span>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          收货人联系电话
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.receiveTel) }}</span>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          收货地址
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.receive?.receiveAddress) }}</span>
        </div>
      </div>
    </div>
  </BasicCard>
  <BasicCard title="发票信息">
    <div class="layout-info">
      <div class="li-row">
        <div class="li-column">
          <div class="l-property">
            发票类型
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.invoice?.invoiceType) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            地址、电话
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.invoice?.invoiceAddress) }}  {{ formatRenderRes(dataProps.invoice?.invoiceTel) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="li-column">
          <div class="l-property">
            发票抬头
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.invoice?.invoiceHead) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            开户行及账号
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.invoice?.invoiceBank) }} {{ formatRenderRes(dataProps.invoice?.invoiceAccount) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          纳税人识别号
        </div>
        <div class="l-value">
          <span>{{ formatRenderRes(dataProps.invoice?.invoiceAIdentifier) }}</span>
        </div>
      </div>
    </div>
  </BasicCard>
  <BasicCard title="流程信息">
    <div class="layout-info">
      <div class="li-row">
        <div class="li-column">
          <div class="l-property">
            支付申请人
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.flow.flowPayPerson) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            收货申请人
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.flow.flowReceivePerson) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property" />
          <div class="l-value">
            <span />
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="li-column">
          <div class="l-property">
            商务接口人
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.order?.businessPersonName) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            技术接口人
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.order?.technicalPersonName) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            承担部门
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.order?.bearOrgName) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="l-property">
          纳税人识别号
        </div>
        <div class="l-value">
          <span>****************</span>
        </div>
      </div>
    </div>
  </BasicCard>
  <BasicCard title="其他信息">
    <div class="layout-info">
      <div class="li-row">
        <div class="li-column">
          <div class="l-property">
            要求到货时间
          </div>
          <div class="l-value">
            <span>{{ formatDateTime(dataProps.other?.receiveDate) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            特殊送货要求
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.receiveDemand) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="li-column">
          <div class="l-property">
            采购组织编码
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.buyOrgCode) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            采购组织名称
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.buyOrgName) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="li-column">
          <div class="l-property">
            工厂
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.shop) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            确认控制
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.confirmControl) }}</span>
          </div>
        </div>
      </div>
      <div class="li-row mt-row">
        <div class="li-column">
          <div class="l-property">
            结算方式
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.settlementmethod) }}</span>
          </div>
        </div>
        <div class="li-column">
          <div class="l-property">
            买方留言
          </div>
          <div class="l-value">
            <span>{{ formatRenderRes(dataProps.other?.leaveWord) }}</span>
          </div>
        </div>
      </div>
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.layout-info {
  .li-row {
    display: flex;
    flex-wrap: nowrap;

    &.mt-row {
      margin-top: ~`getPrefixVar('content-padding-top')`;
    }
  }

  .l-property {
    width: 200px;
    color: ~`getPrefixVar('primary-10')`;
    font-weight: 400;
  }

  .li-column {
    display: flex;
    flex-wrap: nowrap;
    flex: 1;
  }

  .l-value {
    display: flex;
    flex: 1;
    flex-wrap: nowrap;

    span {
      flex: 1;
      min-width: 200px;
      color: ~`getPrefixVar('primary-10')`;
    }
  }
}
</style>