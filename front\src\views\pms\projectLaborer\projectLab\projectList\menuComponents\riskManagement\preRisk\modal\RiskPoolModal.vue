<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],
  api: (params) => {
    params.query = {
      status: 130,
    };
    return new Api('/pas').fetch(params, 'riskLibrary/page', 'POST');
  },
  columns: [
    {
      title: '风险编号',
      dataIndex: 'number',
      width: 100,
    },
    {
      title: '风险名称',
      dataIndex: 'name',
      minWidth: 200,

    },
    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      width: 100,
      align: 'left',
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
      width: 100,
      align: 'left',
    },
    {
      title: '影响程度',
      dataIndex: 'riskInfluenceName',
      width: 100,
      align: 'left',
    },
    {
      title: '预估发生时间',
      dataIndex: 'predictStartTimeName',
      width: 150,
      align: 'left',
    },
    {
      title: '应对策略',
      dataIndex: 'copingStrategyName',
      width: 100,
      align: 'left',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      width: 150,
      slots: { customRender: 'status' },
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: 100,
    },
    {
      title: '修改日期',

      dataIndex: 'modifyTime',
      customRender: ({
        text, record, index, column,
      }) => (record.modifyTime && record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd') : ''),
      width: 180,
    },
  ],
  //  beforeFetch,
});
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  getSelectData,
});
</script>

<style lang="less" scoped>
</style>
