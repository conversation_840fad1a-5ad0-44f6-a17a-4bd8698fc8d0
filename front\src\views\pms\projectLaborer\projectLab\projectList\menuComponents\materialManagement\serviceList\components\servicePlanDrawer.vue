<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1000"
    :showFooter="true"
    :title="state.drawerName"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="confirmDrawer"
  >
    <ServicePlanDrawerFrom
      v-if="state.visibleStatus"
      ref="formRef"
      :fromData="state.fromData"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  ref, defineEmits,
} from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import ServicePlanDrawerFrom from './servicePlanDrawerFrom.vue';
import Api from '/@/api';
const emits = defineEmits(['upTableDate']);
const formRef = ref();
const state = ref({
  visibleStatus: false,
  type: '',
  drawerName: '',
  id: '',
  projectId: '',
  fromData: {},
});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, itemData, projectId}) => {
    state.value.projectId = openProps.projectId;
    state.value.drawerName = openProps.type === 'edit' ? '编辑物资/服务计划' : '新增物资/服务计划';
    state.value.type = openProps.type;
    state.value.id = openProps.itemData?.id;
    state.value.fromData = {
      ...openProps.itemData,
      projectId: openProps.projectId,
      typeSelection: openProps.type === 'edit',
    };
    // 设置为已打开状态
    state.value.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.value.visibleStatus = visible);
}
async function confirmDrawer() {
  const formValues = await formRef.value.validate();
  changeOkLoading(true);
  // const {
  //   number, typeCode, goodsServiceNumber, description, normsModel, serviceTerm, unitCode, demandAmount, demandTime, remark,
  // } = formValues;
  // const {
  //   number, typeCode, goodsServiceNumber, description, normsModel, serviceTerm, unitCode, demandAmount, demandTime, remark,
  // } = formValues;

  // const data = {
  //   projectId: state.value.projectId,
  //   id: state.value.id,
  //   typeCode,
  //   goodsServiceNumber,
  //   description,
  //   normsModel,
  //   serviceTerm,
  //   unitCode,
  //   demandAmount,
  //   demandTime,
  //   remark,
  //
  // };
  if (state.value.type === 'edit') {
    const { number, ...data } = formValues;
    data.projectId = state.value.projectId;
    data.id = state.value.id;
    data.demandTime = data.demandTime ? dayjs(data.demandTime).format('YYYY-MM-DD') : '';
    await new Api('/pms/goods-service-plan').fetch(data, '', 'PUT').then(() => {

    }).finally(() => {
      changeOkLoading(false);
    });
  } else {
    const { number, ...data } = formValues;
    data.projectId = state.value.projectId;
    data.demandTime = data.demandTime ? dayjs(data.demandTime).format('YYYY-MM-DD') : '';
    await new Api('/pms/goods-service-plan').fetch(data, '', 'POST').then(() => {

    }).finally(() => {
      changeOkLoading(false);
    });
  }
  closeDrawer();
  emits('upTableDate');// 更新父组件数据
}

</script>
<style scoped lang="less">
</style>
