<script setup lang="ts">
import {
  BasicButton,
  Layout,
  OrionTable,
  isPower,
  DataStatusTag, openDrawer, BasicBusinessTree,
} from 'lyra-component-vue3';

import {
  computed, h, provide, ref,
} from 'vue';

import { message } from 'ant-design-vue';

import { useRouter } from 'vue-router';
import DrawerAdd from './components/DrawerAdd.vue';
import {
  add, disable, edit, enable, page, remove,
} from '/@/views/pms/api/documentModelLibrary';
import { jumpName } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
const router = useRouter();

const headAuthList = ref([]);
const treeCode = {
  addCode: 'DocTmpLib_container_01_button_01',
  editCode: 'DocTmpLib_container_01_button_02',
  deleteCode: 'DocTmpLib_container_01_button_03',
};
const powerCode = {
  pageCode: 'PMS_WDMBK',
  headCode: 'DocTmpLib_container_01',
  containerCode: 'DocTmpLib_container_02',
  headAdd: 'DocTmpLib_container_01_button_04',
  headDelete: 'DocTmpLib_container_01_button_05',
  headEnable: 'DocTmpLib_container_01_button_06',
  headDisable: 'DocTmpLib_container_01_button_07',
  listDelete: 'DocTmpLib_container_02_button_01',
  listEnable: 'DocTmpLib_container_02_button_02',
  listDisable: 'DocTmpLib_container_02_button_03',
  listEdit: 'DocTmpLib_container_02_button_04',
  listView: 'DocTmpLib_container_02_button_05',
};

// 权限分发
provide(
  'powerData',
  computed(() => headAuthList.value),
);
const tableRef = ref(null);
const formId = ref('');
const columns = [
  {
    title: '编号',
    dataIndex: 'number',
  },
  {
    title: '文档模板名称',
    dataIndex: 'name',
    customRender({
      text, record,
    }) {
      if (isPower(powerCode.listView, record.rdAuthList)) {
        return jumpName(text, () => {
          router.push({
            name: 'DocumentTemplateLibraryDetails',
            params: {
              id: record.id,
            },
          });
        });
      }
      return text;
    },
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '版本',
    dataIndex: 'revId',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record) => isPower(powerCode.listEdit, record.rdAuthList),
    async onClick(record) {
      handleEdit(record.id);
    },
  },
  {
    text: (record) => (record.status === 101 ? '启用' : '禁用'),
    isShow: (record) => isPower(powerCode.listEnable, record.rdAuthList) || isPower(powerCode.listDisable, record.rdAuthList),
    async onClick(record) {
      record.status === 101 ? await enable([record.id]) : await disable([record.id]);
      tableRef.value.reload();
    },
  },
  {
    text: '删除',
    isShow: (record) => isPower(powerCode.listDelete, record.rdAuthList),
    async modal(record) {
      await remove([record.id]);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },

];
const searchConditions = ref([]);
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: true,
  immediate: false,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  // 权限配置使用计算属性
  deleteToolButton: computed(() => {
    let str = 'add';
    if (!isPower(powerCode.headDelete, headAuthList.value)) str += '|delete';
    if (!isPower(powerCode.headEnable, headAuthList.value)) str += '|enable';
    if (!isPower(powerCode.headDisable, headAuthList.value)) str += '|disable';
    return str;
  }),
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['name'],
  rowSelection: {},
  columns,
  actions,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headCode,
      containerCode: powerCode.containerCode,
    };
    searchConditions.value = params?.searchConditions || [];
    return page(formId.value, params).then((res) => {
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  // 批量自定义启动禁用
  batchEnableDisableApi: async ({ ids, type }) => {
    type ? await enable(ids) : await disable(ids);
    tableRef.value.reload();
  },
  // 批量删除自定义api，有特殊情况才使用, data={ids:'选中的id组','选中的原始数据'}
  batchDeleteApi: async ({ ids }) => {
    await remove(ids);
  },
};

const selectNode = ({ id }) => {
  formId.value = id;
  tableRef.value.reload({ page: 1 });
};

// 新增
const handleAdd = () => {
  const refDrawer = ref();
  openDrawer({
    title: '新增文档',
    width: 1100,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
      });
    },
    async onOk() {
      const values = await refDrawer.value.formMethods.validate();
      const { isUseAllObject } = values;
      const data = {
        ...values,
        useScope: !isUseAllObject ? refDrawer.value.searchStringId : null,
        fileDtoList: refDrawer.value.fileDtoList,
        mainTableId: formId.value,
      };
      await add(data);
      tableRef.value.reload();
    },
  });
};
// 编辑
const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑文档',
    width: 1100,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const values = await refDrawer.value.formMethods.validate();
      const { isUseAllObject } = values;
      const data = {
        ...values,
        useScope: !isUseAllObject ? refDrawer.value.searchStringId : null,
        fileDtoList: refDrawer.value.fileDtoList,
        id,
        mainTableId: formId.value,
      };
      await edit(data);
      tableRef.value.reload();
    },
  });
};

</script>

<template>
  <Layout
    v-get-power="{powerData:headAuthList}"
    :options="{ body: { scroll: true } }"
  >
    <template #left>
      <BasicBusinessTree
        title="文档模板库"
        appName="pms"
        className="documentModelLibraryDir"
        :powerCode="treeCode"
        @select="selectNode"
      />
    </template>
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd, headAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新建文档
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
