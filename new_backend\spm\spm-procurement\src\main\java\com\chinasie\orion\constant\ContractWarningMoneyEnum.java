package com.chinasie.orion.constant;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同预警金额
 */

public enum ContractWarningMoneyEnum {

    NORMAL("正常","10"),
    TWENTY_PERCENT("=20%", "100"),
    LESS_TWENTY_PERCENT("<20%", "101"),
    FIFTEEN_PERCENT("=15%", "102"),
    LESS_FIFTEEN_PERCENT("<15%", "103"),
    TEN_PERCENT("=10%", "104"),
    LESS_TEN_PERCENT("<10%", "105"),
    FIVE_PERCENT("=5%", "106"),
    LESS_FIVE_PERCENT("<5%", "107"),
    ZERO_PERCENT("<=0%", "108");

    private String name;
    private String desc;

    ContractWarningMoneyEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {

        for (ContractWarningMoneyEnum lt : ContractWarningMoneyEnum.values()) {
            if (lt.name.equals(value)) {
                return lt.desc;
            }
        }
        return null;
    }

    public static Map<String, String> getContractWarningMoneyMap() {
        return Arrays.stream(ContractWarningMoneyEnum.values())
                .collect(Collectors.toMap(ContractWarningMoneyEnum::getDesc, ContractWarningMoneyEnum::getName));
    }

}