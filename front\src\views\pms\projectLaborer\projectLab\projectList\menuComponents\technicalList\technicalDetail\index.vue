<script setup lang="ts">
import type { Ref } from 'vue';
import { useRoute } from 'vue-router';
import {
  computed, onMounted, ref, h,
} from 'vue';
import {
  BasicTableAction, ITableActionItem, Layout3, openDrawer,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';
import dayjs from 'dayjs';
import { WorkflowAction } from 'lyra-workflow-component-vue3';
import BasicInformation from './BasicInformation.vue';
import Deliverable from './Deliverable.vue';
import RelatedAppendix from './RelatedAppendix.vue';
import ApprovalProcess from './ApprovalProcess.vue';
import CreateForm from '../components/CreateForm.vue';

const route = useRoute();
// 项目计划id
const technicalchemeId = route.params.id;
const projectData:Ref = ref({});
const tabsId:Ref<string> = ref('basic_information');
const layoutLoading:Ref<boolean> = ref(false);
const processRef: Ref = ref();
const processViewRef: Ref = ref();
const menuData = computed(() => [
  {
    name: '基本信息',
    id: 'basic_information',
    isShow: true,
  },
  {
    name: '交付物',
    id: 'deliverable',
    isShow: true,
  },
  {
    name: '相关附件',
    id: 'related_appendix',
    isShow: true,
  },
  {
    name: '审批流程',
    id: 'approval_process',
    isShow: true,
  },

].filter((item) => item.isShow));
const workflowProps = computed(() => ({
  Api,
  businessData: projectData.value,
  afterEvent: (type, props) => {
    processViewRef.value?.init();
    tabsId.value = '';
    setTimeout(async () => {
      tabsId.value = 'approval_process';
      await getTechnicalDetail();
    }, 20);
  },
}));
const getTechnicalDetail = async () => {
  try {
    layoutLoading.value = true;
    const url = `/pms/deliverGoals/${technicalchemeId}`;
    const res = await new Api(url).fetch('', '', 'GET');
    setTitleByRootTabsKey(route.query.rootTabsKey, res.name);
    // res.ownerName = res?.rspUserName;
    processRef.value?.setProps({
      businessData: res,
    });
    res.projectCode = res?.number;
    projectData.value = res;
  } finally {
    layoutLoading.value = false;
  }
};
const menuChange = ({ id }) => {
  tabsId.value = id;
};
const actionsBtn:ITableActionItem[] = [
  {
    isShow: computed(() => true),
    text: '编辑',
    icon: 'sie-icon-bianji',
    onClick() {
      const formRef = ref();
      openDrawer({
        title: '编辑IED',
        width: 800,
        content: (h) => h(CreateForm, {
          ref: formRef,
          projectId: projectData.value.projectId,
          id: technicalchemeId,
        }),
        async onOk() {
          const formMethods = formRef.value.formMethods;
          const formData = await formMethods.validate();
          const planSubmitTime = dayjs(formData.planSubmitTime).format('YYYY-MM-DD');
          const data = {
            ...formData,
            planSubmitTime,
            projectId: projectData.value.projectId,
            id: technicalchemeId,
          };
          const url = '/pms/deliverGoals';
          await new Api(url).fetch(data, '', 'PUT');
          await getTechnicalDetail();
        },
      });
    },
  },

  {
    isShow: computed(() => !!processRef.value?.isAdd),
    text: '添加流程',
    icon: 'sie-icon-tianjiaxinzeng',
    onClick() {
      processRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
];

onMounted(() => {
  getTechnicalDetail();
});
</script>

<template>
  <Layout3
    v-loading="layoutLoading"
    :projectData="projectData"
    :menuData="menuData"
    :type="2"
    :defaultActionId="tabsId"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectData.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <BasicInformation
      v-if="tabsId==='basic_information'"
      :data="projectData"
    />
    <Deliverable
      v-if="tabsId==='deliverable'"
      :id="technicalchemeId"
      :projectId="projectData.projectId"
    />
    <RelatedAppendix
      v-if="tabsId==='related_appendix'"
      :id="technicalchemeId"
      pageType="modal"
    />
    <ApprovalProcess
      v-if="tabsId==='approval_process'"
      :deliverDetailsInfo="projectData"
    />
  </Layout3>
</template>

<style scoped lang="less">

</style>
