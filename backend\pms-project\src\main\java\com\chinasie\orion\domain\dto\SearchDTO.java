package com.chinasie.orion.domain.dto;


import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "SearchDTO对象", description = "搜索")
public class SearchDTO extends RevisionClassDTO {
    private List<List<SearchCondition>> searchConditions;

    @ApiModelProperty(value = "ids")
    private List<String> ids;

    @ApiModelProperty(value = "objIds")
    private List<String> objIds;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "变更申请方式")
    private Integer changeWay;

    @ApiModelProperty(value = "项目Id")
    private String projectId;


    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getChangeWay() {
        return changeWay;
    }

    public void setChangeWay(Integer changeWay) {
        this.changeWay = changeWay;
    }

    public List<String> getObjIds() {
        return objIds;
    }

    public void setObjIds(List<String> objIds) {
        this.objIds = objIds;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public List<List<SearchCondition>> getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(List<List<SearchCondition>> searchConditions) {
        this.searchConditions = searchConditions;
    }
}
