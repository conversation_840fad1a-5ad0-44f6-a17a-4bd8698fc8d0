package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectLifeCycleTemplate VO对象
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@ApiModel(value = "ProjectLifeCycleTemplateVO对象", description = "全生命周期模板")
@Data
public class ProjectLifeCycleTemplateVO extends ObjectVO implements Serializable{
        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;


        /**
         * 内容
         */
        @ApiModelProperty(value = "内容")
        private String content;


        /**
         * 文件数
         */
        @ApiModelProperty(value = "文件数")
        private Integer fileNum;
}
