package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ImToParameter VO对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@ApiModel(value = "ImToParameterVO对象", description = "接口和参数的关系")
@Data
public class ImToParameterVO extends ObjectVO implements Serializable {

    /**
     * 接口ID
     */
    @ApiModelProperty(value = "接口ID")
    private String imId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    private String paramId;

    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
    private String modelId;

//    @ApiModelProperty(value = "参数对象")
//    private ParameterPoolVO parameterPoolVO;


    /**
     * 名称
     */
    @ApiModelProperty(value = "参数名称")
    private String paramName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "参数编码")
    private String paramNumber;

    /**
     * 提供部门名称
     */
    @ApiModelProperty(value = "参数提供部门")
    private String paramProviderDeptNames;

    /**
     * 名称
     */
    @ApiModelProperty(value = "模板名称")
    private String modelName;




}
