package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDetailDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.ContractLineInfoVO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestDetailVO;
import com.chinasie.orion.management.repository.ContractLineInfoMapper;
import com.chinasie.orion.management.repository.NcfFormpurchaseRequestDetailMapper;
import com.chinasie.orion.management.repository.NcfPurchProjectImplementationMapper;
import com.chinasie.orion.management.service.ContractInfoService;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestDetailService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfFormpurchaseRequestDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@Service
@Slf4j
public class NcfFormpurchaseRequestDetailServiceImpl extends OrionBaseServiceImpl<NcfFormpurchaseRequestDetailMapper, NcfFormpurchaseRequestDetail> implements NcfFormpurchaseRequestDetailService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private ContractInfoService contractInfoService;

    @Autowired
    private ContractLineInfoMapper contractLineInfoMapper;

    @Autowired
    private NcfPurchProjectImplementationMapper ncfPurchProjectImplementationMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormpurchaseRequestDetailVO detail(String id, String pageCode) throws Exception {
        NcfFormpurchaseRequestDetail ncfFormpurchaseRequestDetail = this.getById(id);
        NcfFormpurchaseRequestDetailVO result = BeanCopyUtils.convertTo(ncfFormpurchaseRequestDetail, NcfFormpurchaseRequestDetailVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<NcfFormpurchaseRequestDetailVO> getDetailsByCode(String projectCode) {
        List<NcfFormpurchaseRequestDetail> ncfFormpurchaseRequestDetails = this.getBaseMapper().selectList(NcfFormpurchaseRequestDetail::getProjectCode, projectCode);
        return BeanCopyUtils.convertListTo(ncfFormpurchaseRequestDetails, NcfFormpurchaseRequestDetailVO::new);
    }

    @Override
    public Page<NcfFormpurchaseRequestDetailVO> getPages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.in(NcfFormpurchaseRequestDetail::getProjectNumber, pageRequest.getQuery().getProjectNumber());
        condition.orderByDesc(NcfFormpurchaseRequestDetail::getCreateTime);

        Page<NcfFormpurchaseRequestDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequestDetail::new));

        PageResult<NcfFormpurchaseRequestDetail> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormpurchaseRequestDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestDetailVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }


    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestDetailDTO
     */
    @Override
    public String create(NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception {
        NcfFormpurchaseRequestDetail ncfFormpurchaseRequestDetail = BeanCopyUtils.convertTo(ncfFormpurchaseRequestDetailDTO, NcfFormpurchaseRequestDetail::new);
        this.save(ncfFormpurchaseRequestDetail);

        String rsp = ncfFormpurchaseRequestDetail.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestDetailDTO
     */
    @Override
    public Boolean edit(NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception {
        NcfFormpurchaseRequestDetail ncfFormpurchaseRequestDetail = BeanCopyUtils.convertTo(ncfFormpurchaseRequestDetailDTO, NcfFormpurchaseRequestDetail::new);

        this.updateById(ncfFormpurchaseRequestDetail);

        String rsp = ncfFormpurchaseRequestDetail.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormpurchaseRequestDetailVO> pages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormpurchaseRequestDetail::getCreateTime);

        if (pageRequest.getQuery() == null || pageRequest.getQuery().getContractNumber() == null) {
            throw new Exception("合同编号为空，请输入");
        }
        condition.isNotNull(NcfFormpurchaseRequestDetail::getWbsId);
        //根据合同编码查对应的采购申请编码，根据查询的采购申请编码，查询对应的采购申请数据
        LambdaQueryWrapperX<ContractInfo> infoQueryWrapper = new LambdaQueryWrapperX<>(ContractInfo.class);
        infoQueryWrapper.eq(ContractInfo::getContractNumber, pageRequest.getQuery().getContractNumber());
        List<ContractInfo> contractInfos = contractInfoService.list(infoQueryWrapper);
        List<String> codeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractInfos)) {
            codeList = contractInfos.stream().map(ContractInfo::getPurchaseApplicant).filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(codeList)) {
            condition.in(NcfFormpurchaseRequestDetail::getProjectCode, codeList);
        } else {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        }


        Page<NcfFormpurchaseRequestDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequestDetail::new));

        PageResult<NcfFormpurchaseRequestDetail> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormpurchaseRequestDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestDetailVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<NcfFormpurchaseRequestDetailVO> wbsPages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest) {
        LambdaQueryWrapperX<ContractLineInfo> condition = new LambdaQueryWrapperX<>(ContractLineInfo.class);
        condition.eq(ContractLineInfo::getContractNumber,pageRequest.getQuery().getContractNumber());
        List<ContractLineInfo> contractLineInfos = contractLineInfoMapper.selectList(condition);
        if (ObjectUtil.isEmpty(contractLineInfos)){
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        }
        List<String> collectOne = contractLineInfos.stream().map(ContractLineInfo::getProcurementApplicantNumber).distinct().collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(collectOne)){
            List<NcfPurchProjectImplementation> projectImplementations = ncfPurchProjectImplementationMapper.selectList(NcfPurchProjectImplementation::getEcpPurchaseAppNo,collectOne);
            if(!CollectionUtils.isEmpty(projectImplementations)){
                List<String> purchReqDocCodes = projectImplementations.stream().map(NcfPurchProjectImplementation::getPurchReqDocCode).filter(Objects::nonNull).collect(Collectors.toList());
                collectOne.addAll(purchReqDocCodes);
            }
        }

        List<String> collectTwo = contractLineInfos.stream().map(ContractLineInfo::getProcurementApplicantLineNumber).distinct().collect(Collectors.toList());


        LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> conditionX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (ObjectUtil.isNotEmpty(collectOne)){
            conditionX.in(NcfFormpurchaseRequestDetail::getProjectCode,collectOne);
            if (ObjectUtil.isNotEmpty(collectTwo)){
                conditionX.in(NcfFormpurchaseRequestDetail::getProjectId,collectTwo);
            }
        }
        Page<NcfFormpurchaseRequestDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequestDetail::new));

        PageResult<NcfFormpurchaseRequestDetail> page = this.getBaseMapper().selectPage(realPageRequest, conditionX);

        Page<NcfFormpurchaseRequestDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestDetailVO::new);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购申请行项目表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestDetailDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormpurchaseRequestDetailExcelListener excelReadListener = new NcfFormpurchaseRequestDetailExcelListener();
        EasyExcel.read(inputStream, NcfFormpurchaseRequestDetailDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormpurchaseRequestDetailDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购申请行项目表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormpurchaseRequestDetail> ncfFormpurchaseRequestDetailes = BeanCopyUtils.convertListTo(dtoS, NcfFormpurchaseRequestDetail::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::NcfFormpurchaseRequestDetail-import::id", importId, ncfFormpurchaseRequestDetailes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormpurchaseRequestDetail> ncfFormpurchaseRequestDetailes = (List<NcfFormpurchaseRequestDetail>) orionJ2CacheService.get("ncf::NcfFormpurchaseRequestDetail-import::id", importId);
        log.info("采购申请行项目表导入的入库数据={}", JSONUtil.toJsonStr(ncfFormpurchaseRequestDetailes));

        this.saveBatch(ncfFormpurchaseRequestDetailes);
        orionJ2CacheService.delete("ncf::NcfFormpurchaseRequestDetail-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::NcfFormpurchaseRequestDetail-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(NcfFormpurchaseRequestDetail::getCreateTime);
        List<NcfFormpurchaseRequestDetail> ncfFormpurchaseRequestDetailes = this.list(condition);

        List<NcfFormpurchaseRequestDetailDTO> dtos = BeanCopyUtils.convertListTo(ncfFormpurchaseRequestDetailes, NcfFormpurchaseRequestDetailDTO::new);

        String fileName = "采购申请行项目表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestDetailDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormpurchaseRequestDetailVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class NcfFormpurchaseRequestDetailExcelListener extends AnalysisEventListener<NcfFormpurchaseRequestDetailDTO> {

        private final List<NcfFormpurchaseRequestDetailDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormpurchaseRequestDetailDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormpurchaseRequestDetailDTO> getData() {
            return data;
        }
    }


}
