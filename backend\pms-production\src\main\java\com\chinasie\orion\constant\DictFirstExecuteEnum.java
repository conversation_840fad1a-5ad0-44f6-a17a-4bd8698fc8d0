package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/16:46
 * @description:
 */
public enum DictFirstExecuteEnum {
    base_firsh("pms_base_firsh","本基地首次执行"),
    qun_base_first ("pms_qun_base_first","群厂基地首次执行") ,
    BASE_NA ("pms_base_na","NA")  ;

    private  String key;

    private String desc;


    DictFirstExecuteEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<String,String> map(){
        Map<String,String> map=new HashMap<>();
        for (DictFirstExecuteEnum dictFirstExecuteEnum : DictFirstExecuteEnum.values()) {
            map.put(dictFirstExecuteEnum.getKey(),dictFirstExecuteEnum.getDesc());
        }
        return map;
    }
}
