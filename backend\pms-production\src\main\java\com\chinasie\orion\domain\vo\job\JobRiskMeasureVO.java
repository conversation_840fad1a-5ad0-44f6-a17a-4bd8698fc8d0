package com.chinasie.orion.domain.vo.job;

import com.chinasie.orion.domain.vo.JobRiskVO;
import com.chinasie.orion.domain.vo.JobSecurityMeasureVO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/16:26
 * @description:
 */
@Data
public class JobRiskMeasureVO  implements Serializable {

    @ApiModelProperty(value = "作业ID")
    private String jobId;

    /**
     * 是否高风险
     */
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;

    /**
     * 高风险（字典）
     */
    @ApiModelProperty(value = "高风险（字典）")
    private String heightRisk;
    /**
     * 高风险（字典）
     */
    @ApiModelProperty(value = "高风险名称")
    private String heightRiskName;
    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    private String firstExecute;
    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行名称")
    private String firstExecuteName;

    /**
     * 新人参与
     */
    @ApiModelProperty(value = "新人参与")
    private Boolean newParticipants;

    /**
     * 重要项目
     */
    @ApiModelProperty(value = "重要项目")
    private String importantProject;


    @ApiModelProperty(value = "重要项目名称")
    private String importantProjectName;

    @ApiModelProperty(value = "风险信息")
    private List<JobRiskVO> riskVOList;


    @ApiModelProperty(value = "证明文件")
    private List<FileTreeVO> fileVOList;
}
