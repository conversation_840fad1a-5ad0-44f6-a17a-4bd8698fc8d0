<script setup lang="ts">
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import { Icon, BasicScrollbar } from 'lyra-component-vue3';

const emits = defineEmits<{(e: 'updateTableSelect', rows?: Record<string, any>[]): void
}>();

const attrs: Record<string, any> = inject('attrs', {});
const selectedRows: Ref<Record<string, any>[]> = ref([]);

onMounted(() => {
  initSelected();
});

// 初始化选中项
function initSelected() {
  if (attrs.selectedData?.length) {
    setData(attrs.selectedData);
  }
}

// 删除全部
function handleDelAll() {
  selectedRows.value = [];
  emits('updateTableSelect', selectedRows.value);
}

// 单条删除
function handleDelItem(index: number) {
  selectedRows.value.splice(index, 1);
  emits('updateTableSelect', selectedRows.value);
}

// 设置已选择的数据
function setData(rows: Record<string, any>[]) {
  selectedRows.value = rows;
}

defineExpose({
  setData,
  getData() {
    return selectedRows.value;
  },
});
</script>

<template>
  <div class="selected-list">
    <div class="title">
      <h3>已选择({{ selectedRows.length }})</h3>
      <div
        class="fz12 action-btn"
        @click="handleDelAll"
      >
        删除全部
      </div>
    </div>
    <BasicScrollbar class="scroll">
      <div
        v-for="(item,index) in selectedRows"
        :key="index"
        class="list-item"
      >
        <span class="name flex-te">{{ item.name }}</span>
        <div
          class="icon-wrap"
          @click="handleDelItem(index as number)"
        >
          <Icon
            class="del-icon"
            icon="fa-remove"
          />
        </div>
      </div>
    </BasicScrollbar>
  </div>
</template>

<style scoped lang="less">
.selected-list {
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
  height: 100%;

  .title {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #eef2f3;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
    color: #535353;

    h3 {
      font-size: 12px;
      font-weight: 700;
      margin-bottom: 0;
    }
  }

  .scroll {
    height: 0;
    flex-grow: 1;

    .list-item {
      height: 36px;
      line-height: 36px;
      position: relative;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:hover {
        background-color: #fff;

        .icon-wrap {
          opacity: 1;
        }
      }

      .icon-wrap {
        padding: 0 10px;
        cursor: pointer;
        margin-left: 10px;
        opacity: 0;

        &:hover {
          .del-icon {
            color: ~`getPrefixVar('primary-color')`;
          }
        }
      }
    }
  }
}
</style>
