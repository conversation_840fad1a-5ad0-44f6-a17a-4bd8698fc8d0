<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2012, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>jakarta.annotation</groupId>
    <artifactId>ca-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.3.5</version>

    <inceptionYear>2004</inceptionYear>

    <name>Jakarta Annotations Parent POM</name>
    <description>Jakarta Annotations Parent POM</description>

    <url>https://projects.eclipse.org/projects/ee4j.ca</url>

    <scm>
        <connection>scm:git:https://github.com/eclipse-ee4j/common-annotations-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/common-annotations-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/common-annotations-api</url>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <non.final>false</non.final>
        <spec.version>1.3</spec.version>
        <extension.name>jakarta.annotation</extension.name>
        <vendor.name>Oracle Corporation</vendor.name>
        <implementation.vendor.id>org.glassfish</implementation.vendor.id>
        <findbugs.version>3.0.5</findbugs.version>
        <findbugs.exclude>exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
    </properties>

    <modules>
        <module>api</module>
        <module>spec</module>
    </modules>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
</project>
