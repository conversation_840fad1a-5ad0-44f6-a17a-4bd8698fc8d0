package com.chinasie.orion.service.impl;





import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.CopyEntityDTO;
import com.chinasie.orion.domain.dto.pdm.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.dto.PlanToParamDTO;
import com.chinasie.orion.domain.vo.PlanToParamVO;


import com.chinasie.orion.domain.vo.RelationParamInsVO;
import com.chinasie.orion.domain.vo.RelationParamVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.feign.dto.ParamInsCopyDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.service.PlanToParamService;
import com.chinasie.orion.repository.PlanToParamMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * PlanToParam 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@Service
public class PlanToParamServiceImpl extends OrionBaseServiceImpl<PlanToParamMapper, PlanToParam> implements PlanToParamService {

    @Autowired
    private PlanToParamMapper planToParamMapper;

    @Autowired
    private ComponentFeignService componentFeignService;

    @Autowired
    private ProjectServiceImpl projectService;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  PlanToParamVO detail(String id) throws Exception {
        PlanToParam planToParam =planToParamMapper.selectById(id);
        PlanToParamVO result = BeanCopyUtils.convertTo(planToParam,PlanToParamVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param planToParamDTO
     */
    @Override
    public  PlanToParamVO create(PlanToParamDTO planToParamDTO) throws Exception {
        PlanToParam planToParam =BeanCopyUtils.convertTo(planToParamDTO,PlanToParam::new);
        int insert = planToParamMapper.insert(planToParam);
        PlanToParamVO rsp = BeanCopyUtils.convertTo(planToParam,PlanToParamVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param planToParamDTO
     */
    @Override
    public Boolean edit(PlanToParamDTO planToParamDTO) throws Exception {
        PlanToParam planToParam =BeanCopyUtils.convertTo(planToParamDTO,PlanToParam::new);
        int update =  planToParamMapper.updateById(planToParam);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = planToParamMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<PlanToParamVO> pages(Page<PlanToParamDTO> pageRequest) throws Exception {
        Page<PlanToParam> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PlanToParam::new));

        PageResult<PlanToParam> page = planToParamMapper.selectPage(realPageRequest,null);

        Page<PlanToParamVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PlanToParamVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PlanToParamVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Boolean relationToParam(String planId, List<String> imIdList) {
        if (!CollectionUtils.isEmpty(this.list(new LambdaQueryWrapper<>(PlanToParam.class)
                .in(PlanToParam::getFromId, imIdList)
                .eq(PlanToParam::getToId, planId)))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToParam> planToDeliverGoalsList = new ArrayList<>();
        for (String fromId : imIdList) {
            PlanToParam planToDeliverGoals = new PlanToParam();
            planToDeliverGoals.setFromId(fromId);
            planToDeliverGoals.setToId(planId);
            planToDeliverGoalsList.add(planToDeliverGoals);
        }
        this.saveBatch(planToDeliverGoalsList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelation(String planId, List<String> paramIdList) {
        this.remove(new LambdaQueryWrapper<>(PlanToParam.class)
                .eq(PlanToParam::getToId, planId)
                .in(PlanToParam::getFromId, paramIdList));
        return true;
    }

    @Override
    public List<RelationParamVO> relationList(String planId) throws Exception {
        LambdaQueryWrapperX<PlanToParam> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(PlanToParam::getToId,planId);
        lambdaQueryWrapperX.orderByDesc(PlanToParam::getCreateTime);
        List<PlanToParam> list = this.list(lambdaQueryWrapperX);
        if(com.chinasie.orion.util.CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }
        List<String> paramIdList = list.stream().map(PlanToParam::getFromId).distinct().collect(Collectors.toList());
        List<String> modelIdList = list.stream().map(PlanToParam::getModelId).distinct().collect(Collectors.toList());
        List<String> insIdList = list.stream().map(PlanToParam::getInsId).distinct().collect(Collectors.toList());

        SearchDTO build = SearchDTO.builder().ids(paramIdList).build();
        Map<String, ParameterPoolVO> idToParameterPoo= new HashMap<>();
        ResponseDTO<List<ParameterPoolVO>> paramListResponse = componentFeignService.parameterPoolLists(build);
        if(ResponseUtils.success(paramListResponse)){
            List<ParameterPoolVO> result = paramListResponse.getResult();
            if(!com.chinasie.orion.util.CollectionUtils.isBlank(result)){
                idToParameterPoo = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
            }
        }
        Map<String,ParameterPoolModuleVO> idToPoolModule= new HashMap<>();
        if(!com.chinasie.orion.util.CollectionUtils.isBlank(modelIdList)){
            build.setIds(modelIdList);
            ResponseDTO<List<ParameterPoolModuleVO>> search = componentFeignService.parameterPoolModuleSearch(build);
            if(ResponseUtils.success(search)){
                List<ParameterPoolModuleVO> result = search.getResult();
                if(!com.chinasie.orion.util.CollectionUtils.isBlank(result)){
                    idToPoolModule = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
                }
            }
        }

        Map<String, ParameterPoolInsVO> idToIns= new HashMap<>();
        if(!com.chinasie.orion.util.CollectionUtils.isBlank(insIdList)){
            build.setIds(insIdList);
            ResponseDTO<List<ParameterPoolInsVO>> search = componentFeignService.parameterPoolInsSearch(build);
            if(ResponseUtils.success(search)){
                List<ParameterPoolInsVO> result = search.getResult();
                if(!com.chinasie.orion.util.CollectionUtils.isBlank(result)){
                    idToIns = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
                }
            }
        }

        List<RelationParamVO> imToParameterVOS= new ArrayList<>();
        for (PlanToParam planToParam : list) {
            RelationParamVO relationParamVO = BeanCopyUtils.convertTo(planToParam, RelationParamVO::new);
            relationParamVO.setParamId(planToParam.getFromId());
            relationParamVO.setPlanId(planToParam.getToId());
            relationParamVO.setInsId(planToParam.getInsId());
            imToParameterVOS.add(relationParamVO);
        }
        Map<String, ParameterPoolVO> finalIdToParameterPoo = idToParameterPoo;
        Map<String, ParameterPoolModuleVO> finalIdToPoolModule = idToPoolModule;
        Map<String, ParameterPoolInsVO> finalIdToIns = idToIns;
        imToParameterVOS.forEach(i->{

            ParameterPoolModuleVO orDefault = finalIdToPoolModule.getOrDefault(i.getModelId(), new ParameterPoolModuleVO());
            i.setModelName(orDefault.getName());

            ParameterPoolInsVO inst = finalIdToIns.getOrDefault(i.getInsId(), new ParameterPoolInsVO());
            i.setInsName(inst.getName());
            i.setInsCreateTime(inst.getCreateTime());

            ParameterPoolVO orDefault1 = finalIdToParameterPoo.getOrDefault(i.getParamId(), new ParameterPoolVO());
            i.setPlanId(planId);
            i.setParamName(orDefault1.getName());
            i.setParamNumber(orDefault1.getNumber());
            i.setParamProviderDeptNames(orDefault1.getProviderDeptNames());
            i.setAliases(orDefault1.getAliases());
        });
        return imToParameterVOS;
    }

    @Override
    public Boolean settingModel(String planId, String paramId, String modelId) {
        List<PlanToParam> list = this.list(new LambdaQueryWrapper<>(PlanToParam.class)
                .eq(PlanToParam::getToId, planId)
                .eq(PlanToParam::getFromId, paramId));
        if(CollectionUtils.isEmpty(list)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "当前计划下不存改参数，请刷新后重试");
        }
        PlanToParam planToParam = list.get(0);
        planToParam.setModelId(modelId);
        return this.updateById(planToParam);
    }

    @Override
    public Boolean addValue(ParameterPoolInsDTO parameterPoolInsDTO) throws Exception {
        String relationId = parameterPoolInsDTO.getRelationId();
        String id = parameterPoolInsDTO.getId();
        if(StringUtils.hasText(id)){
            //调用 pdm 参数实列修改
            ResponseDTO<Boolean> responseDTO = componentFeignService.edit(parameterPoolInsDTO);
            if(ResponseUtils.success(responseDTO)){
                Boolean result = responseDTO.getResult();
                if(ObjectUtils.isEmpty(result)){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm修改参数实列异常："+responseDTO.getMessage());
                }
                if(!result){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm修改参数实列失败："+responseDTO.getMessage());
                }
            }
        }else{
            PlanToParam planToParam = this.getById(relationId);
            if(ObjectUtils.isEmpty(planToParam)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "当前关系不存在，请刷新后重试");
            }
            // 调用新增
            String dataClassName = parameterPoolInsDTO.getDataClassName();
            if(Objects.equals("EdmDocument",dataClassName)){
                parameterPoolInsDTO.setProjectId(parameterPoolInsDTO.getDataId());
                parameterPoolInsDTO.setProjectName(parameterPoolInsDTO.getDataName());
                parameterPoolInsDTO.setDataName(parameterPoolInsDTO.getDataName());
                parameterPoolInsDTO.setDataHref("/edm/edm-figureWork-details/"+parameterPoolInsDTO.getDataId());
            }else if(StringUtils.hasText(parameterPoolInsDTO.getProjectId())){
                // 调用新增
                Project byId = projectService.getById(parameterPoolInsDTO.getProjectId());
                parameterPoolInsDTO.setProjectName(byId== null?"":byId.getName());
                ProjectScheme scheme = projectSchemeService.getById(parameterPoolInsDTO.getDataId());
                parameterPoolInsDTO.setDataName(scheme== null? "":scheme.getName());
                parameterPoolInsDTO.setDataHref("/pms/ProPlanDetails/"+parameterPoolInsDTO.getDataId());
            }else {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "当前关系不存在，请刷新后重试");
            }
            ResponseDTO<ParameterPoolInsVO> parameterPoolInsVOResponseDTO = componentFeignService.create(parameterPoolInsDTO);
            if(ResponseUtils.success(parameterPoolInsVOResponseDTO)){
                ParameterPoolInsVO result = parameterPoolInsVOResponseDTO.getResult();
                if(ObjectUtils.isEmpty(result)){
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm创建参数实列异常："+parameterPoolInsVOResponseDTO.getMessage());
                }
                String moduleId = result.getModuleId();
                planToParam.setModelId(moduleId);
                planToParam.setInsId(result.getId());
                this.updateById(planToParam);
            }else{
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm创建参数实列异常："+parameterPoolInsVOResponseDTO.getMessage());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean copySourceIdToTargetId(String sourceId, String targetId, CopyEntityDTO copyEntityDTO) throws Exception {
        List<PlanToParam> list = this.list(new LambdaQueryWrapper<>(PlanToParam.class)
                .eq(PlanToParam::getToId, sourceId));
        if(CollectionUtils.isEmpty(list)){
            return  Boolean.TRUE;
        }

        List<String> insIdList = new ArrayList<>();
        list.forEach(item->{
            item.setId(null);
            item.setCreateTime(null);
            item.setModifyTime(null);
            item.setToId(targetId);
            String insId = item.getInsId();
            if(StringUtils.hasText(insId)){
                insIdList.add(insId);
            }
        });
        String newName = copyEntityDTO.getNewName();
        String newRevId = copyEntityDTO.getNewRevId();
        String name = String.format(newName+"_"+ newRevId);

        Map<String,String>  paramgIdToInsIdMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(insIdList)){
            ParamInsCopyDTO paramInsCopyDTO = new ParamInsCopyDTO();
            paramInsCopyDTO.setInsIdList(insIdList);
            paramInsCopyDTO.setDataId(targetId);
            paramInsCopyDTO.setDataName(name);
            paramInsCopyDTO.setDataHref("/edm/edm-figureWork-details/"+targetId);
            ResponseDTO<List<ParameterPoolInsVO>> responseDTO = componentFeignService.copyByIdList(paramInsCopyDTO);
            if(HttpStatus.OK.value() != responseDTO.getCode()){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "调用pdm复制数实列异常："+responseDTO.getMessage());
            }
            List<ParameterPoolInsVO> result = responseDTO.getResult();

            paramgIdToInsIdMap= result.stream().collect(Collectors.toMap(ParameterPoolInsVO::getParameterId,ParameterPoolInsVO::getId));
        }

        // 关系复制完成后 需要复制参数实列
        if(!MapUtil.isEmpty(paramgIdToInsIdMap)){
            Map<String, String> finalParamgIdToInsIdMap = paramgIdToInsIdMap;
            list.forEach(i->{
                i.setInsId(finalParamgIdToInsIdMap.getOrDefault(i.getFromId(),""));
            });
        }
        return this.saveBatch(list);
    }

    @Override
    public List<RelationParamInsVO> relationListByToIdList(List<String> fromIdList) throws Exception {

        LambdaQueryWrapperX<PlanToParam> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PlanToParam.class);
        lambdaQueryWrapperX.in(PlanToParam::getToId,fromIdList);
        lambdaQueryWrapperX.orderByDesc(PlanToParam::getCreateTime);
        List<PlanToParam> list = this.list(lambdaQueryWrapperX);
        if(com.chinasie.orion.util.CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }
        List<String> insIdList = list.stream().map(PlanToParam::getInsId).distinct().collect(Collectors.toList());
        List<String> paramIdList = list.stream().map(PlanToParam::getFromId).distinct().collect(Collectors.toList());
        SearchDTO build = SearchDTO.builder().ids(paramIdList).build();
        Map<String, ParameterPoolVO> idToParameterPoo= new HashMap<>();
        ResponseDTO<List<ParameterPoolVO>> paramListResponse = componentFeignService.parameterPoolLists(build);
        if(ResponseUtils.success(paramListResponse)){
            List<ParameterPoolVO> result = paramListResponse.getResult();
            if(!com.chinasie.orion.util.CollectionUtils.isBlank(result)){
                idToParameterPoo = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
            }
        }
        Map<String, ParameterPoolInsVO> idToIns= null;
        if(!CollectionUtils.isEmpty(insIdList)){
            build.setIds(insIdList);
            ResponseDTO<List<ParameterPoolInsVO>> search = componentFeignService.parameterPoolInsSearch(build);
            if(ResponseUtils.success(search)){
                List<ParameterPoolInsVO> result = search.getResult();
                if(!com.chinasie.orion.util.CollectionUtils.isBlank(result)){
                    idToIns = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
                }
            }
        }
        List<RelationParamInsVO> imToParameterVOS= new ArrayList<>();
        for (PlanToParam planToParam : list) {
            RelationParamInsVO relationParamVO = new RelationParamInsVO();
            relationParamVO.setToId(planToParam.getToId());
            relationParamVO.setFromId(planToParam.getFromId());
            relationParamVO.setInsId(planToParam.getInsId());
            relationParamVO.setModelId(planToParam.getModelId());
            imToParameterVOS.add(relationParamVO);
        }
        Map<String, ParameterPoolInsVO> finalIdToIns = idToIns;
        Map<String, ParameterPoolVO> finalIdToParameterPoo = idToParameterPoo;
        imToParameterVOS.forEach(i->{
            i.setInsEntity(finalIdToIns.get(i.getInsId()));
            ParameterPoolVO orDefault = finalIdToParameterPoo.getOrDefault(i.getFromId(), new ParameterPoolVO());
            i.setParamName(orDefault.getName());
        });
        return imToParameterVOS;
    }
}
