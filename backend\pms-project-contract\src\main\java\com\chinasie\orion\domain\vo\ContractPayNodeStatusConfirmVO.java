package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractPayNodeStatusConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-28 10:43:16
 */
@ApiModel(value = "ContractPayNodeStatusConfirmVO对象", description = "项目合同支付节点状态确认")
@Data
public class ContractPayNodeStatusConfirmVO extends ObjectVO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    private String nodeId;

    /**
     * 确认id
     */
    @ApiModelProperty(value = "确认id")
    private String confirmId;

}
