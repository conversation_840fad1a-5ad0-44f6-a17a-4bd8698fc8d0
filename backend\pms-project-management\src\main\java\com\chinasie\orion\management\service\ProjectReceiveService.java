package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectReceiveDTO;
import com.chinasie.orion.management.domain.entity.ProjectReceive;
import com.chinasie.orion.management.domain.vo.ProjectReceiveVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectReceive 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
public interface ProjectReceiveService extends OrionBaseService<ProjectReceive> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectReceiveVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectReceiveDTO
     */
    String create(ProjectReceiveDTO projectReceiveDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectReceiveDTO
     */
    Boolean edit(ProjectReceiveDTO projectReceiveDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectReceiveVO> pages(Page<ProjectReceiveDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectReceiveVO> vos) throws Exception;

    /**
     * 根据number查询收货信息
     * <p>
     * * @param number
     */
    ProjectReceiveVO getByNumber(String number) throws Exception;
}
