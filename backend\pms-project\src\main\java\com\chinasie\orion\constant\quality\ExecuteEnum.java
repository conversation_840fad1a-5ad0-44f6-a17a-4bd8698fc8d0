package com.chinasie.orion.constant.quality;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.constant.ProjectSchemeEnum;
import com.chinasie.orion.domain.entity.ProjectScheme;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum ExecuteEnum {
    COMPLETE(2, "已完成"),
    UNDERWAY(1, "正在执行"),
    READY(0, "待执行"),
    ;

    private Integer code;
    private String name;


    ExecuteEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }

    public static ExecuteEnum getExecute(List<ProjectScheme> projectSchemes){
        List<ProjectScheme> completed = projectSchemes.stream().filter(p -> ProjectSchemeEnum.COMPLETED.getValue().equals(p.getStatus().toString())).collect(Collectors.toList());
        List<ProjectScheme> execution = projectSchemes.stream()
                .filter(p -> ProjectSchemeEnum.EXECUTING.getValue().equals(p.getStatus().toString()) || ProjectSchemeEnum.CHANGE.getValue().equals(p.getStatus().toString()))
                .collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(completed)){
            if (completed.size() == projectSchemes.size()){
                return COMPLETE;
            }

            if (completed.size() != 0 || !CollectionUtil.isEmpty(execution)){
                return COMPLETE;
            }
        }
        return READY;
    }
}
