package com.chinasie.orion.domain.vo.job;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/11/11:18
 * @description:
 */
@Data
public class JobPostAuthorizeInfoVO   implements Serializable {


    @ApiModelProperty(value = "授权Id")
    private String id;
    @ApiModelProperty(value = "基地编码")
    private String basePlaceCode;
    @ApiModelProperty(value = "基地名称")
    private String basePlaceName;

    @ApiModelProperty(value = "岗位编号")
    private String jobPostCode;
    @ApiModelProperty(value = "岗位名称")
    private String jobPostName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    private Date endDate;
    /**
     * 授权状态（100-未授权，111-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    private Integer authorizeStatus;

    /**
     * 授权状态（100-未授权，111-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）名称")
    private String authorizeStatusName;


    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    private Boolean isEquivalent;
    @ApiModelProperty(value = "等效认定基地")
    private List<EquBasePlaceVO> equBasePlaceVOList;


    @ApiModelProperty(value = "授权记录")
    private List<FileVO> fileVOList;
    @ApiModelProperty(value = "所属作业的基地编码")
    private String baseCode;
    @ApiModelProperty(value = "所属作业的基地名称")
    private String baseName;
}
