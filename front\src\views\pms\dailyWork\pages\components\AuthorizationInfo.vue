<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  DataStatusTag,
  IOrionTableActionItem,
  isPower,
  Layout,
  openSelectUserModal,
  OrionTable,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue/lib/components';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { authApi } from '../utils';
import { useAuthPageConfig } from './hooks';

const route = useRoute();
const router = useRouter();
const routeName = route.name as string;
const powerCodePrefix: Ref = inject('powerCodePrefix');
const { detailRouteName } = useAuthPageConfig(routeName);
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '员工号',
      dataIndex: 'userCode',
      customRender({ text, record }) {
        if (isPower(`${powerCodePrefix.value}_container_02_02_button_01`, record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
    },
    {
      title: '性别',
      dataIndex: 'sex',
    },
    {
      title: '现任职位',
      dataIndex: 'nowPosition',
    },
    {
      title: '作业岗位',
      dataIndex: 'jobPostName',
      customRender({ text, record }) {
        if (isPower(`${powerCodePrefix.value}_container_02_02_button_03`, record?.rdAuthList)) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text,
            componentValue: record?.jobPostCode,
            componentProps: {
              fieldNames: {
                label: 'name',
                value: 'number',
              },
              api: () => new Api('/pms/job-post-library/list').fetch({
                baseCode: detailsData.jobBase,
              }, '', 'POST'),
            },
            onSubmit(value: any, resolve: (value: any) => void) {
              new Api('/pms/job-post-authorize/edit/job/post').fetch({
                id: record?.id,
                jobPostCode: value?.number,
                jobPostId: value?.id,
              }, '', 'PUT').then(() => {
                message.success('操作成功');
                updateTable();
              }).finally(() => {
                resolve(true);
              });
            },
          });
        }
        return h('div', { class: 'flex-te' }, text);
      },
    },
    {
      title: '是否满足授权',
      dataIndex: 'isAuthorizationName',
    },
    {
      title: '授权状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '授权到期时间',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '人员所在基地',
      dataIndex: 'basePlaceName',
    },
    {
      title: '进入基地时间',
      dataIndex: 'enterBaseDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-post-authorize').fetch({
    ...params,
    query: {
      jobId: detailsData?.id,
    },
    power: {
      containerCode: route.name,
      pageCode: `${powerCodePrefix.value}_container_02_01`,
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加人员',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: `${powerCodePrefix.value}_container_02_01_button_01`,
  },
  {
    event: 'auth',
    text: '授权校验',
    icon: 'sie-icon-gouxuan',
    disabled: selectedRows.value.length === 0,
    powerCode: `${powerCodePrefix.value}_container_02_01_button_02`,
  },
  {
    event: 'batchDelete',
    text: '移除',
    icon: 'sie-icon-shanchu',
    powerCode: `${powerCodePrefix.value}_container_02_01_button_03`,
    disabled: selectedRows.value.length === 0,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openSelectUserModal([], {
        okHandle(user: any[]) {
          new Api('/pms/job-post-authorize/add/person/list').fetch({
            jobId: detailsData?.id,
            personIdList: user.map((item) => item.id),
            planSchemeId: detailsData?.planSchemeId,
            repairRound: detailsData?.repairRound,
          }, '', 'POST').then(() => {
            message.success('操作成功');
            updateTable();
          });
          return Promise.resolve(true);
        },
      });
      break;
    case 'auth':
      Modal.confirm({
        title: '核验提示！',
        content: '确认核验已选数据?',
        onOk: () => authApi(detailsData?.id, selectedRows.value.map((item) => item.id), updateTable),
      });
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower(`${powerCodePrefix.value}_container_02_02_button_01`, record?.rdAuthList),
  },
  {
    text: '移除',
    event: 'delete',
    isShow: (record) => isPower(`${powerCodePrefix.value}_container_02_02_button_02`, record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record?.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: detailRouteName.value,
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-post-authorize').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
</script>

<template>
  <Layout
    v-get-power="{powerData}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
