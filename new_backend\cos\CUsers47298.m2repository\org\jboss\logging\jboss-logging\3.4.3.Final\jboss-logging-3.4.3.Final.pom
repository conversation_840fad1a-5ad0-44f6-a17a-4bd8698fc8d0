
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.jboss.logging</groupId>
    <artifactId>jboss-logging</artifactId>
    <version>3.4.3.Final</version>
    <packaging>jar</packaging>
    <name>JBoss Logging 3</name>
    <url>http://www.jboss.org</url>
    <description>The JBoss Logging Framework</description>
    <scm>
        <connection>scm:git:git://github.com/jboss-logging/jboss-logging.git</connection>
        <url>https://github.com/jboss-logging/jboss-logging</url>
    </scm>

    <parent>
        <groupId>org.jboss</groupId>
        <artifactId>jboss-parent</artifactId>
        <version>39</version>
    </parent>

    <licenses>
        <license>
            <name>Apache License, version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <!-- Dependency versions -->
        <version.ch.qos.logback>1.2.8</version.ch.qos.logback>
        <version.org.apache.log4j>1.2.17</version.org.apache.log4j>
        <version.org.apache.logging.log4j>2.17.1</version.org.apache.logging.log4j>
        <version.org.jboss.logmanager>2.1.18.Final</version.org.jboss.logmanager>
        <version.org.junit>5.8.2</version.org.junit>
        <version.org.sfl4j>1.7.32</version.org.sfl4j>

        <!-- Override the parent version to compile with Java 17 -->
        <version.bundle.plugin>5.1.3</version.bundle.plugin>

        <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
        <cp.test.classes.dir>${project.build.directory}${file.separator}cp-test-classes</cp.test.classes.dir>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${version.org.junit}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.jboss.logmanager</groupId>
            <artifactId>jboss-logmanager</artifactId>
            <version>${version.org.jboss.logmanager}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${version.org.apache.log4j}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.jdmk</groupId>
                    <artifactId>jmxtools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jmx</groupId>
                    <artifactId>jmxri</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${version.org.apache.logging.log4j}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${version.org.sfl4j}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${version.ch.qos.logback}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${version.org.apache.logging.log4j}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-test-compile</id>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <phase>test-compile</phase>
                        <configuration>
                            <testExcludes>
                                <testExclude>**/*ClassPathTestCase.java</testExclude>
                            </testExcludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cp-test-compile</id>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <phase>test-compile</phase>
                        <configuration>
                            <outputDirectory>${cp.test.classes.dir}</outputDirectory>
                            <skip>${skip.cp.tests}</skip>
                            <testIncludes>
                                <testInclude>**/*ClassPathTestCase.java</testInclude>
                            </testIncludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <redirectTestOutputToFile>${maven.test.redirectTestOutputToFile}</redirectTestOutputToFile>
                    <!-- Required so we can test the various providers -->
                    <reuseForks>false</reuseForks>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>default</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <excludes>
                                <exclude>**/*ClassPathTestCase.java</exclude>
                            </excludes>
                            <systemPropertyVariables>
                                <!-- This is required as JUnit 5 initializes a logger too early for it to be set
                                     in the JBossLogManagerTestCase. -->
                                <java.util.logging.manager>org.jboss.logmanager.LogManager</java.util.logging.manager>
                            </systemPropertyVariables>
                        </configuration>
                    </execution>
                    <execution>
                        <id>jboss-logmanager-cp-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <testClassesDirectory>${cp.test.classes.dir}</testClassesDirectory>
                            <includes>
                                <include>**/JBossLogManagerClassPathTestCase.java</include>
                            </includes>
                            <classpathDependencyExcludes>
                                <classpathDependencyExclude>org.apache.logging.log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.slf4j</classpathDependencyExclude>
                                <classpathDependencyExclude>ch.qos.logback</classpathDependencyExclude>
                            </classpathDependencyExcludes>
                            <systemPropertyVariables>
                                <java.util.logging.manager>org.jboss.logmanager.LogManager</java.util.logging.manager>
                            </systemPropertyVariables>
                        </configuration>
                    </execution>
                    <execution>
                        <id>log4j2-cp-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>test</phase>
                        <configuration>
                            <skip>false</skip>
                            <testClassesDirectory>${cp.test.classes.dir}</testClassesDirectory>
                            <includes>
                                <include>**/Log4j2ClassPathTestCase.java</include>
                            </includes>
                            <classpathDependencyExcludes>
                                <classpathDependencyExclude>org.jboss.logmanager</classpathDependencyExclude>
                                <classpathDependencyExclude>log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.slf4j</classpathDependencyExclude>
                                <classpathDependencyExclude>ch.qos.logback</classpathDependencyExclude>
                            </classpathDependencyExcludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>log4j-cp-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>test</phase>
                        <configuration>
                            <skip>false</skip>
                            <testClassesDirectory>${cp.test.classes.dir}</testClassesDirectory>
                            <includes>
                                <include>**/Log4jClassPathTestCase.java</include>
                            </includes>
                            <classpathDependencyExcludes>
                                <classpathDependencyExclude>org.apache.logging.log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.jboss.logmanager</classpathDependencyExclude>
                                <classpathDependencyExclude>org.slf4j</classpathDependencyExclude>
                                <classpathDependencyExclude>ch.qos.logback</classpathDependencyExclude>
                            </classpathDependencyExcludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>slf4j-cp-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>test</phase>
                        <configuration>
                            <skip>false</skip>
                            <testClassesDirectory>${cp.test.classes.dir}</testClassesDirectory>
                            <includes>
                                <include>**/Slf4jClassPathTestCase.java</include>
                            </includes>
                            <classpathDependencyExcludes>
                                <classpathDependencyExclude>org.apache.logging.log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.jboss.logmanager</classpathDependencyExclude>
                                <classpathDependencyExclude>log4j</classpathDependencyExclude>
                            </classpathDependencyExcludes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>jul-cp-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <testClassesDirectory>${cp.test.classes.dir}</testClassesDirectory>
                            <includes>
                                <include>**/JulClassPathTestCase.java</include>
                            </includes>
                            <classpathDependencyExcludes>
                                <classpathDependencyExclude>org.apache.logging.log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.jboss.logmanager</classpathDependencyExclude>
                                <classpathDependencyExclude>log4j</classpathDependencyExclude>
                                <classpathDependencyExclude>org.slf4j</classpathDependencyExclude>
                                <classpathDependencyExclude>ch.qos.logback</classpathDependencyExclude>
                            </classpathDependencyExcludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Adding OSGI metadata to the JAR without changing the packaging type. -->
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultSpecificationEntries>false</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestEntries>
                            <Automatic-Module-Name>org.jboss.logging</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                    <instructions>
                        <Export-Package>
                            ${project.groupId}.*;version=${project.version};-split-package:=error
                        </Export-Package>
                        <Import-Package>
                            org.apache.log4j.config;resolution:=optional,
                            *;resolution:=optional
                        </Import-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
