package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewOpinion DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@ApiModel(value = "ReviewOpinionDTO对象", description = "评审意见")
@Data
public class ReviewOpinionDTO extends ObjectDTO implements Serializable {

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点id")
    private String reviewEssentialsId;
    @ApiModelProperty(value = "评审要点")
    private String essentials;
    @ApiModelProperty(value = "具体描述")
    private String description;
    /**
     * 关联问题
     */
    @ApiModelProperty(value = "关联问题")
    private String questionId;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String presentedUser;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    private String opinion;

    /**
     * 意见类型
     */
    @ApiModelProperty(value = "意见类型")
    private Integer type;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
