package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectDemandStatisticsVO对象", description = "需求统计表")
public class ProjectDemandStatisticsVO {
    @ApiModelProperty(value = "项目id")
    private String id;
    @ApiModelProperty(value = "负责人名称")
    private String rspuserName;
    @ApiModelProperty(value = "负责人")
    private String rspuser;
    @ApiModelProperty(value = "展示时间")
    private String showTime;
    @ApiModelProperty(value = "时间")
    private Date timeValue;
    @ApiModelProperty(value = "未开始数量")
    private Integer noStartCount=0;
    @ApiModelProperty(value = "进行中数量")
    private Integer underwayCount=0;
    @ApiModelProperty(value = "已完成数量")
    private Integer completeCount=0;
}
