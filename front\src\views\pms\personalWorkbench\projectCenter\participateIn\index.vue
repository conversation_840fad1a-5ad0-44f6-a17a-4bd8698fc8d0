<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="isTable"
      class="card-list-table"
    >
      <template #projectApproveTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectStartTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectEndTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>

      <template #schedule="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>

      <template #statusIdName="{ record }">
        <DataStatusTag :status-data="record?.dataStatus" />
      </template>
    </OrionTable>
  </Layout>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  getCurrentInstance,
  h,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  Ref,
  toRefs,
  unref, watch,
} from 'vue';
import {
  BasicTitle1,
  DataStatusTag,
  isPower,
  Layout,
  OrionTable,
  useDrawer,
  useProjectPower,
} from 'lyra-component-vue3';
import {
  Empty, message, Progress, Modal,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';

const tableRef = ref(null);
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    // BasicTitle1,
    Layout,
    Progress,
    DataStatusTag,
    OrionTable,
  },
  setup() {
    let ids: string[] = [];
    try {
      ids = JSON.parse(sessionStorage.getItem('ids'));
    } finally {
      sessionStorage.removeItem('ids');
    }

    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    const [registerModal, { openDrawer: openDrawerModal }] = useDrawer();
    const isTable: Ref<boolean> = ref(true);
    const state = reactive({
      showVisible: false,
      message: '',
      powerData: [],
      nodeData: [],
      // 搜索弹窗
      searchModalVisible: false,
      searchData: {},
      params: {},
      queryCondition: [],
      searchStatus: '',
    });

    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    const internalInstance = getCurrentInstance();

    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0003' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }

    onMounted(async () => {
      state.powerData = await getProjectPower();
      onResize();
      window.addEventListener('resize', onResize);
    });
    const router = useRouter();
    const zkKeys = () => getTableAction().getSelectRowKeys();
    const searchTable = (params) => {
      state.searchStatus = 'eveSearch';
      state.queryCondition = params.queryCondition;
      state.params = params.params;
      successSave();
    };
    // 跳转详情
    const toDetails = (data) => {
      router.push({
        name: 'MenuComponents',
        query: {
          id: data.id,
        },
      });
    };
    const addNode = () => {
      openDrawerAdd(true, { type: 'add' });
    };
    const onSearch = () => {
      state.searchStatus = 'oddSearch';
      successSave();
    };
    const successSave = () => {
      getTableAction().reload({
        page: 1,
      });
      getTableAction().clearSelectedRowKeys();
      state.searchStatus = '';
    };

    function getListParams(params) {
      if (ids?.length) {
        params.query = {
          ids,
          jumpFlag: true,
        };
      }
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    function reloadTable() {
      tableRef.value?.reload();
    }

    function deleteRecords(ids: string[]) {
      return new Api('/pms')
        .fetch(ids, 'project/removeBatch/', 'DELETE')
        .then(() => {
          message.success('删除成功');
        });
    }

    const tableOptions = {
      rowSelection: {},
      // smallSearchField: ['name', 'number'],
      deleteToolButton: 'add|delete|enable|disable',
      isFilter2: true,
      filterConfigName: 'PMS_PERSONALWORKBENCH_PROJECTCENTER_PARTICIPATEIN_INDEX',
      api: (params) => new Api('/pms/project/workBenchPage/2').fetch(getListParams(params), '', 'POST'),
      batchDeleteApi({ ids }) {
        return deleteRecords(ids);
      },
      columns: [
        {
          title: '项目编号',
          dataIndex: 'number',
          width: '100px',
          slots: { customRender: 'number' },
        },
        {
          title: '项目名称',
          dataIndex: 'name',
          minWidth: 220,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('XMX_list_button_03', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('XMX_list_button_03', state.powerData)) {
                    toDetails(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
        },
        {
          title: '项目类型',
          dataIndex: 'projectTypeName',
          width: '120px',
        },
        {
          title: '是否需要申报',
          dataIndex: 'isDeclare',
          width: '100px',
          customRender({ text }) {
            return text ? '是' : text === false ? '否' : '--';
          },
        },
        {
          title: '项目进度',
          dataIndex: 'schedule',
          width: '170px',
          slots: { customRender: 'schedule' },
        },
        {
          title: '状态',
          dataIndex: 'statusIdName',
          width: '100px',
          slots: { customRender: 'statusIdName' },
        },
        {
          title: '项目经理',
          dataIndex: 'pm',
          width: '120px',
          slots: { customRender: 'pm' },
        },
        {
          title: '我的项目角色',
          dataIndex: 'roleName',
          width: '170px',
          slots: { customRender: 'roleName' },
        },
        {
          title: '立项日期',
          dataIndex: 'projectApproveTime',
          width: '120px',
          slots: { customRender: 'projectApproveTime' },
        },
        {
          title: '开始日期',
          dataIndex: 'projectStartTime',
          width: '120px',
          slots: { customRender: 'projectStartTime' },
        },
        {
          title: '结束日期',
          dataIndex: 'projectEndTime',
          width: '120px',
          slots: { customRender: 'projectEndTime' },
        },
      ],
    };
    onUnmounted(() => {
      window.removeEventListener('resize', onResize);
    });

    const gridNum: Ref<number> = ref();

    watch(() => tableRef.value, () => {
      onResize();
    });
    function onResize() {
      if (!tableRef.value) {
        return;
      }
      const tableWidth = tableRef.value.$el.clientWidth - 60;
      let num = parseInt(tableWidth / 340);
      gridNum.value = parseInt(tableWidth / (340 + Math.ceil((num - 1 < 0 ? 0 : num - 1) * 20) / num));
    }

    /**
     * 用户收藏项目
     * @param params
     */
    async function postUserLike(params: { projectId: string }) {
      return new Api('/pms/user-like-project').fetch(params, '', 'post');
    }

    /**
     * 用户取消收藏项目
     * @param params
     */
    async function deleteUserLike(params: Array<string>) {
      return new Api('/pms/user-like-project').fetch(params, '', 'delete');
    }
    return {
      ...toRefs(state),
      isTable,
      addNode,
      dayjs,
      onSearch,
      successSave,
      searchTable,
      tableRef,
      isPower,
      registerModal,
      registerAdd,
      tableOptions,
      Empty,
      onResize,
      gridNum,
    };
  },
});
</script>
<style lang="less" scoped>
.title{
  margin-left: 20px;
}
.card-grid {
  display: grid;
  gap: 16px 20px;
}

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
</style>
