package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RankingVO
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "OverhaulDetailVO", description = "多基地大修准备及实施状态")
@Data
public class OverhaulDetailVO extends ObjectVO implements Serializable {

    /**
     * 所属基地名称
     */
    @ApiModelProperty(value = "所属基地名称")
    private String baseName;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    /**
     * 大修状态
     */
    @ApiModelProperty(value = "大修状态")
    private String haulStatus;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String actualBeginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String actualEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private String beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private String endTime;


    /**
     * 大修工期
     */
    @ApiModelProperty(value = "大修工期")
    private int workDuration;


    /**
     * 大修经理
     */
    @ApiModelProperty(value = "大修经理")
    private String repairManager;

    /**
     * 大修类别
     */
    @ApiModelProperty(value = "大修类别")
    private String type;
}
