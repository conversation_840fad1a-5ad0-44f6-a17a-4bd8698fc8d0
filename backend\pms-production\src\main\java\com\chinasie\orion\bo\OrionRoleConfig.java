package com.chinasie.orion.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/08/11:34
 * @description:
 */
@Data
@Component
@ConfigurationProperties(prefix = "orion.production.role")
public class OrionRoleConfig {

    @ApiModelProperty("培训工程师code")
    private String trainEngineerCode;
    @ApiModelProperty("安全质量经理code")
    private String envPmCode;
    @ApiModelProperty("联络人Code")
    private String contactCode;
    @ApiModelProperty("第一层级")
    private List<String> firstLevel;
    @ApiModelProperty("第二层级")
    private List<String> twoLevel;
    @ApiModelProperty("第三层级")
    private List<String> threeLevel;
}
