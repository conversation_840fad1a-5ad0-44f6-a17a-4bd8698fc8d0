<template>
  <a-modal
    v-model:visible="father.visible"
    width="100%"
    wrap-class-name="full-modal"
    :footer="null"
    :closable="false"
  >
    <template #title>
      <div class="fl pt10">
        <b>{{ father.title }}</b>
      </div>
      <div class="fr">
        <a-button @click="father.visible = false">
          关闭
        </a-button>
      </div>
    </template>
    <Layout :options="{ body: { scroll: true }, right: { width: 400 } }">
      <template #right>
        <div class="content-title-icon">
          <h3>基本信息</h3>
        </div>
        <div class="pl20">
          <p class="mb-4 mt-4">
            作者：{{ father.form.author }}
          </p>
          <p class="mb-4 mt-4">
            创建人：{{ father.form.creatorName }}
          </p>
          <p class="mb-4 mt-4">
            所属部门：{{ father.form.deptName }}
          </p>
          <p class="mb-4 mt-4">
            创建时间：{{ formatDate(father.form.createTime) }}
          </p>
          <p class="mb-4 mt-4">
            所属分类：{{ father.form.classifyName }}
          </p>
          <p class="mb-4 mt-4">
            知识状态：{{ father.form.statusName }}
          </p>
          <p class="mb-4 mt-4">
            知识版本：{{ father.form.revision }}
          </p>
          <p class="mb-4 mt-4">
            更新人：{{ father.form.modifyName }}
          </p>
          <p class="mb-4 mt-4">
            更新时间：{{ formatDate(father.form.modifyTime) }}
          </p>
        </div>
        <div class="content-title-icon">
          <h3>扩展信息</h3>
        </div>
        <div class="pl20">
          <p class="mb-4 mt-4">
            知识项目：
          </p>
          <p class="mb-4 mt-4">
            知识来源：
          </p>
          <p class="mb-4 mt-4">
            知识标签：
          </p>
          <a-tag
            v-for="(s, i) in father.form.labelDtoList"
            :key="i"
            style="margin-bottom: 10px; padding: 5px 10px"
          >
            {{ s }}
          </a-tag>
        </div>
      </template>
      <div class="edit-content pl60 pr60 pt20 pb20">
        <div class="title">
          <h1>{{ father.form.name }}</h1>
          <a-space
            :size="30"
            class="sub-title"
          >
            <span>作者: {{ father.form.author }}</span>
            <span>{{ formatDate(father.form.modifyTime) }}</span>
          </a-space>
          <a-divider class="mt10 mb20" />
          <div class="detail">
            {{ father.form.summary }}
          </div>
          <Tinymce
            v-model="father.form.content"
            :show-image-upload="false"
            :height="500"
          />
          <div class="detail">
            <p class="mb5">
              附件列表：
            </p>
            <a-row
              v-for="v in fileDataSource"
              :key="v.id"
              type="flex"
            >
              <a-col flex="auto">
                <Icon
                  :icon="getFileObj(v.filePostfix).icon"
                  :style="{ color: getFileObj(v.filePostfix).color }"
                />
                <span
                  class="text-link"
                  @click="preview(v.filePath, v.filePostfix)"
                >
                  {{ v.name + v.filePostfix }}
                </span>
              </a-col>
              <a-col
                flex="100px"
                class="tx-c"
              >
                {{ v.fileSize ? (v.fileSize / 1024 / 1024).toFixed(2) + ' MB' : '' }}
              </a-col>
              <a-col
                flex="100px"
                class="tx-c"
              >
                <DownloadOutlined
                  class="text-link mr20"
                  @click="downLoadController(v.id)"
                />
              </a-col>
            </a-row>
          </div>
        </div>
        <a-tabs>
          <a-tab-pane
            key="2"
            tab="流程"
          >
            <div class="content-title-icon">
              <h3>流程详情</h3>
            </div>
            <a-table
              bordered
              row-key="id"
              :data-source="journalList"
              :columns="columnProcess"
              :pagination="false"
            />
            <div class="content-title-icon">
              <h3>流程图</h3>
            </div>
            <div class="bpmn-containers">
              <div
                ref="canvas"
                class="canvas"
              />
            </div>
          </a-tab-pane>
          <a-tab-pane
            key="3"
            tab="权限"
          >
            <a-table
              row-key="type"
              :show-index-column="false"
              :max-height="300"
              :bordered="true"
              :pagination="false"
              :data-source="father.form.permissionJsonDtoList"
              :columns="columnsSetting"
            >
              <template #type="{ text }">
                <span v-if="text === 1">可阅读</span>
                <span v-if="text === 2">可编辑</span>
                <span v-if="text === 3">可下载</span>
              </template>
              <template #projectList="{ record }">
                <span
                  v-for="(s, i) in record.projectList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #organizationList="{ record }">
                <span
                  v-for="(s, i) in record.organizationList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #personList="{ record }">
                <span
                  v-for="(s, i) in record.personList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #isPublic="{ record }">
                <a-checkbox
                  v-model:checked="record.isPublic"
                  disabled
                >
                  公开
                </a-checkbox>
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
        <br>
      </div>
    </Layout>
  </a-modal>
</template>

<script>
import {
  defineComponent, toRefs, reactive, ref,
} from 'vue';
import {
  Modal, Space, Divider, Row, Col, Table, Button, Tag, Tabs,
} from 'ant-design-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
import { Tinymce } from '/@/views/pms/projectLaborer/components/Tinymce';
import { Icon } from '/@/components/Icon';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { getFileObj, edit } from './data';
import Api from '/@/api';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import { useUserStore } from '/@/store/modules/user';
import Viewer from 'bpmn-js/lib/Viewer';
import CustomRenderer from '/@/views/pms/projectLaborer/flowcenter/detail/extra';
import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
import { formatDate, preview, roleController } from '/@/views/pms/projectLaborer/utils';

export default defineComponent({
  name: 'Details',
  components: {
    AButton: Button,
    ATable: Table,
    DownloadOutlined,
    Icon,
    ARow: Row,
    ACol: Col,
    Tinymce,
    Layout,
    AModal: Modal,
    ASpace: Space,
    ADivider: Divider,
    ATag: Tag,
    ATabPane: Tabs.TabPane,
    ATabs: Tabs,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props, { emit }) {
    const userStore = useUserStore();
    const state = reactive({
      father: props.data,
      canvas: ref(),
      columnsSetting: edit.columns1,
      columnProcess: edit.columns2,
      journalList: [],
      fileDataSource: [],
    });

    async function getProcess() {
      const userId = userStore.getUserInfo.id;
      const query = {
        pageNum: 1,
        pageSize: 1,
        query: {
          deliveries: [{ deliveryId: state.father.form.id }],
          userId,
        },
      };
      const url = '/workflow/act-prearranged/by_delivery/page';
      const data = await new Api(url).fetch(query, '', 'POST');
      if (data && data.length) {
        const row = data[0];
        const url1 = `/workflow/act-inst-detail/journal?userId=${userId}&procInstId=${row.id}`;
        const url2 = `/workflow/act-flow/bpmn?userId=${userId}&uid=${row.flowInfoId}`;
        const data1 = await new Api(url1).fetch('', '', 'GET');
        state.journalList = data1
          .filter((s) => s.activityType === 'userTask')
          .map((s, v) => ({
            ...s,
            key: v + 1,
          }));
        const data2 = await new Api(url2).fetch('', '', 'GET');
        renderFlow(data2);
      }
    }
    function renderFlow(xml) {
      const bpmnModeler = new Viewer({
        container: state.canvas,
        additionalModules: [CustomRenderer, MoveCanvasModule],
      });
      bpmnModeler.importXML(xml, (err) => {
        if (err) return;
        bpmnModeler.get('canvas').zoom('fit-viewport', 'auto'); // 自动调整流程在画布的位置
      });
    }
    async function downLoadController(id) {
      if (await roleController(state.father.form.id, 'download')) {
        downLoadById(id);
      }
    }

    function init() {
      getProcess();
      new Api('/res/file/all').fetch('', state.father.form.id, 'GET').then((res) => {
        state.fileDataSource = res;
      });
    }
    init();

    return {
      ...toRefs(state),
      downLoadController,
      preview,
      formatDate,
      getFileObj,
    };
  },
});
</script>

<style lang="less" scoped>
  .edit-content {
    height: calc(100vh - 110px);

    ::v-deep(.ant-col) {
      width: 1px !important;
    }

    .title {
      h1 {
        font-size: 36px;
        font-weight: 650;
        color: #393f4d;
      }

      .sub-title {
        color: #9ea3b5;
        .rate {
          color: #ffb614;
          font-size: 26px;
          font-weight: 650;
        }
      }

      .detail {
        padding: 20px;
        background-color: rgba(244, 245, 248, 1);
      }
    }
  }

  .text-link {
    cursor: pointer;
    &:hover {
      color: #0960bd;
    }
  }

  :deep(.bpmn-containers) {
    width: 100%;
    height: 40vh;

    .canvas {
      height: 100%;
      overflow: scroll;
    }

    img {
      display: none;
    }
  }
</style>
