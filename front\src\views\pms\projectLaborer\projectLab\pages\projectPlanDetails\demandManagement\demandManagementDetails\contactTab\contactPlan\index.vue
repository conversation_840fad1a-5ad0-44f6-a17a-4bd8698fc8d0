<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="add"
        @click="clickType('add')"
      >
        新增
      </BasicButton>
      <BasicButton
        icon="delete"
        @click="clickType('delete')"
      >
        删除
      </BasicButton>
    </template>

    <template #planPredictStartTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>
    <template #planPredictEndTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
    </template>

    <template #action="{record}">
      <BasicTableAction :actions="actionsBtn(record)" />
    </template>
  </OrionTable>
  <!-- 从系统添加 -->
  <AddSystemRole
    :id="id"
    :data="addSystemModalData"
    @success="successSave"
  />
  <PlanManagementModal @register="registerView" />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, reactive, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, DataStatusTag, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import dayjs from 'dayjs';
import AddSystemRole from './modal/addSystemRole.vue';
import Api from '/@/api';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import PlanManagementModal from './modal/PlanManagementModal.vue';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicTableAction,
    OrionTable,
    AddSystemRole,
    PlanManagementModal,
    BasicButton,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const { mainRouter } = useQiankun();
    const [registerView, { openDrawer: openDrawerView }] = useDrawer();
    const state = reactive({
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},
      /* 简易弹窗提醒消息 */
      message: '',
      searchData: {},
      params: {},
      powerData: [],
      tableRef: null,
    });
    state.powerData = inject('powerData');
    const formData = inject('formData');
    const state6 = reactive({
      btnList: [
        'check',
        'open',
        'add',
        'delete',
      ],

    });
    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    /* 页数变化cb */
    /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'check':
          checkData();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          openPlan();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
      }
    };
    const openPlan = () => {
      if (lengthCheckHandle()) return;
      mainRouter.push({
        name: 'PlanDetails',
        query: {
          id: state.selectedRowKeys[0],
          projectId: formData?.value?.projectId,
        },
      });
    };
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };

    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      openDrawerView(true, { id: state.selectedRowKeys[0] });
    };
    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 新建项目 */
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      Modal.confirm({
        title: '请确认是否对当前选中数据进行删除?',
        onOk() {
          const newArr = {
            id: formData?.value?.id,
            planIds: state.selectedRowKeys,
          };
          new Api('/pas/demand-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
            .then((res) => {
              message.success('删除成功');
              state.tableRef.reload();
            })
            .catch(() => {
            });
        },
      });
    };
    /* 搜索右上 */
    /* 新建项目成功回调 */
    const successSave = () => {
      state.selectedRowKeys = [];
      state.selectedRows = [];
      state.tableRef.reload();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };

    const tableOptions = {
      showIndexColumn: false,
      pagination: false,
      bordered: false,
      showSmallSearch: false,
      deleteToolButton: 'add|delete|enable|disable',
      rowClick: clickRow,
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onChange: onSelectChange,
      },
      api() {
        return new Api(`/pas/demand-management/relation/plan/${formData?.value?.id}`).fetch('', '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: 120,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: 'action-btn',
                title: text,
                onClick(e) {
                  let routerName = record.planType !== '0' ? 'PlanDetails' : 'MilestoneDetails';
                  mainRouter.push({
                    name: routerName,
                    query: {
                      id: record.id,
                      projectId: formData?.value?.projectId,
                    },
                  });
                  // e.stopPropagation();
                },
              },
              text,
            );
          },
          minWidth: 240,
          align: 'left',
          slots: { customRender: 'name' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '计划类型',
          dataIndex: 'planTypeName',
          key: 'planType',
          width: 100,
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'planTypeName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',

          width: 80,
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        // {
        //   title: '计划进度',
        //   dataIndex: 'scheduleName',
        //   key: 'schedule',
        //   width: 90,
        //   align: 'left',
        //   slots: { customRender: 'scheduleName' },
        //   // sorter: true,
        //   ellipsis: true,
        // },
        // {
        //   title: '优先级',
        //   dataIndex: 'priorityLevelName',
        //   key: 'priorityLevel',
        //   width: 80,
        //   align: 'left',
        //   slots: { customRender: 'priorityLevelName' },
        //   // sorter: true,
        //   ellipsis: true,
        // },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'status',
          width: 80,
          align: 'left',
          sorter: true,
          ellipsis: true,
          customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
        },
        {
          title: '开始日期',
          dataIndex: 'planPredictStartTime',
          key: 'planPredictStartTime',
          width: 130,
          align: 'left',
          slots: { customRender: 'planPredictStartTime' },
          sorter: true,
          ellipsis: true,
        },

        {
          title: '结束日期',
          dataIndex: 'planPredictEndTime',
          key: 'planPredictEndTime',
          width: 150,
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'planPredictEndTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn = (record) => {
      const actions: ITableActionItem[] = [
        {
          text: '删除',
          modal() {
            const newArr = {
              id: formData?.value?.id,
              planIds: [record.id],
            };
            return new Api('/pas/demand-management/relation/plan/batch').fetch(newArr, '', 'DELETE')
              .then((res) => {
                message.success('删除成功');
                state.tableRef.reload();
              })
              .catch(() => {
              });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      onSelectChange,
      formatterTime,
      dayjs,
      successSave,
      addSystemRoleHandle,
      registerView,
      tableOptions,
      actionsBtn,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
