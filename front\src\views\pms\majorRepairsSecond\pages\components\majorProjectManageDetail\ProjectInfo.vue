<script setup lang="ts">

import {
  computed, inject, reactive, watchEffect,
} from 'vue';
import { BasicCard } from 'lyra-component-vue3';

const detailsData: Record<string, any> = computed(() => inject('MajorProjectDetailData'));
const projectInfo = reactive({
  list: [
    {
      label: '项目名称',
      field: 'projectName',
    },
    {
      label: '项目负责人',
      field: 'rspUserName',
    },
    {
      label: '负责人工号',
      field: 'rspUserCode',
    },
    {
      label: '负责人所在中心',
      field: 'deptName',
    },
    {
      label: '计划开始日期',
      field: 'planStart',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划结束日期',
      field: 'planEnd',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际开始日期',
      field: 'actureStart',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际结束日期',
      field: 'actureEnd',
      formatTime: 'YYYY-MM-DD',
    },
  ],
  dataSource: {},
});

watchEffect(() => {
  projectInfo.dataSource = detailsData.value ?? {};
});
</script>

<template>
  <BasicCard
    :is-border="false"
    :grid-content-props="projectInfo"
  />
</template>

<style scoped lang="less">

</style>