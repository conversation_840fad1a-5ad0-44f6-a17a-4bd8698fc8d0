package com.chinasie.orion.domain.dto.assetApply;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectAssetApplyDetailAssets Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
@ApiModel(value = "ProjectAssetApplyDetailAssetsEntity对象", description = "资产同步表")
@Data

public class AssetSyncDTO extends ObjectEntity implements Serializable {

    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    private String procurementApplicantLineNumber;

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    private String projectIdName;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    private String generalLedgerSubject;

    /**
     * 资产
     */
    @ApiModelProperty(value = "资产")
    private String asset;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private String requiredQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private String unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private String totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    private BigDecimal localCurrencyAmount;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    private String item;

    /**
     * 物料组
     */
    @ApiModelProperty(value = "物料组")
    private String itemGroup;

    /**
     * 内部订单
     */
    @ApiModelProperty(value = "内部订单")
    private String internalOrder;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    private String wbsId;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectId;
}
