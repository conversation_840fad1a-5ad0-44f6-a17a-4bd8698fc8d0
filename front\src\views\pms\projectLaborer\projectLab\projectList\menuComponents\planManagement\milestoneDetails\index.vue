<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange"
  >
    <div class="content_tabs">
      <Summarize
        v-if="actionId===1212122"
        :formId="formId"
      />
      <FrontPlan
        v-if="actionId===1212123"
        :formId="formId"
        :projectId="projectInfo.projectId"
        @checkDetails="checkData($event,'plan')"
      />
      <Deliverable
        v-if="actionId===1212124"
        :formId="formId"
        :projectId="projectInfo.projectId"
        @checkDetails="checkData($event,'deliverale')"
      />
      <RelatedContent
        v-if="actionId===1212125"
        :formId="formId"
        :projectId="projectInfo.projectId"
      />
      <!--    <Log v-if="tabsIndex === 4" />-->
      <PushModel />
    </div>
    <PlanManagementModal @register="registerDetails" />
    <DeliverableModal @register="registerDetailsDeliverable" />
  </Layout3>
</template>

<script>
import {
  isPower,
  Layout3, useDrawer, useProjectPower,
} from 'lyra-component-vue3';
// import { Layout2 } from '/@/components/Layout2.0';
import {
  computed, getCurrentInstance, onMounted, provide, reactive, toRefs,
} from 'vue';
import { useRoute } from 'vue-router';
import Summarize from '../planDetails/summarize2/index.vue';
import FrontPlan from '../planDetails/frontPlan2/index.vue';
import Deliverable from '../planDetails/deliverable2/index.vue';
import RelatedContent from '../planDetails/relatedContent/index2.vue';
import PlanManagementModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/PlanManagementModal.vue';
import DeliverableModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/DeliverableModal.vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';

export default {
  name: 'Index',
  components: {
    Layout3,
    PushModel,
    Summarize,
    FrontPlan,
    Deliverable,
    PlanManagementModal,
    DeliverableModal,
    RelatedContent,
    // Log,
  },
  setup() {
    const [registerDetails, { openDrawer: openDetailsDrawer }] = useDrawer();
    const [registerDetailsDeliverable, { openDrawer: openDetailsDeliverable }] = useDrawer();
    const route = useRoute();
    const state = reactive({
      tabsIndex: 0,
      formId: route.query.id,
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '前置计划' }, // 1
      //   { name: '交付物' }, // 2
      //   { name: '关联内容' }, // 3
      //   // { name: '日志' }, // 4
      // ],
      actionId: 1212122,
      projectInfo: {},
      powerData: [],
    });
    const state6 = reactive({
      tabsOption: [],
    });
    const internalInstance = getCurrentInstance();
    // 获取权限
    async function getProjectPower() {
      return new Promise((resolve, reject) => {
        useProjectPower(
          { pageCode: 'PMS0006' },
          (powerList) => {
            resolve(powerList || []);
          },
          internalInstance,
        );
      });
    }
    function getForm() {
      const love = {
        id: state.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-里程碑详情', // 模块名称
        type: 'GET', // 操作类型
        remark: `打开查看了里程碑【${state.formId}】详情`,
      };
      new Api('/pms', love)
        .fetch('', `plan/${state.formId}`, 'GET')
        .then((res) => {
          state.projectInfo = res;
          setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
        })
        .catch((_) => {
        });
    }
    onMounted(async () => {
      state.powerData = await getProjectPower();
      isPower('LCB_container_02', state.powerData) && state6.tabsOption.push({
        name: '概述',
        id: 1212122,
      });
      isPower('LCB_container_03', state.powerData) && state6.tabsOption.push({
        name: '前置计划',
        id: 1212123,
      });
      isPower('LCB_container_04', state.powerData) && state6.tabsOption.push({
        name: '交付物',
        id: 1212124,
      });
      isPower('LCB_container_05', state.powerData) && state6.tabsOption.push({
        name: '关联内容',
        id: 1212125,
      });
      await getForm();
    });
    function contentTabsChange(index) {
      state.actionId = index.id;
    }
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    const checkData = (id, type) => {
      if (type === 'plan') {
        openDetailsDrawer(true, { id });
      } else {
        openDetailsDeliverable(true, { id });
      }
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange,
      isPower,
      registerDetails,
      checkData,
      registerDetailsDeliverable,
    };
  },
};
</script>

<style scoped>
.content_tabs{
  display: flex;
  height: 100%;
}
</style>
