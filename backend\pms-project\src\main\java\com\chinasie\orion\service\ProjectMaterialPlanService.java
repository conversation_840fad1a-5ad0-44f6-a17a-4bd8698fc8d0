package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectMaterialPlanDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPlanPreparationDTO;
import com.chinasie.orion.domain.entity.ProjectMaterialPlan;
import com.chinasie.orion.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanPreparationVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.pdm.api.domain.dto.BasicMaterialsDTO;
import com.chinasie.orion.pdm.api.domain.vo.BasicMaterialsVO;
import com.chinasie.orion.sdk.metadata.page.Page;


/**
 * <p>
 * ProjectMaterialPlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
public interface ProjectMaterialPlanService  extends  OrionBaseService<ProjectMaterialPlan>  {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectMaterialPlanVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectMaterialPlanDTO
     */
    String create(ProjectMaterialPlanDTO projectMaterialPlanDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectMaterialPlanDTO
     */
    Boolean edit(ProjectMaterialPlanDTO projectMaterialPlanDTO)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectMaterialPlanVO> pages( Page<ProjectMaterialPlanDTO> pageRequest)throws Exception;

    /**
     * 物资追踪分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectMaterialPlanVO> tracePages( Page<ProjectMaterialPlanDTO> pageRequest) throws Exception;

    /**
     * 研发物料父级分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProductToMaterialVO> materialParentPage(Page<ProjectMaterialPlanDTO> pageRequest) throws Exception;

    /**
     * 物料分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<BasicMaterialsVO> materialPage(Page<BasicMaterialsDTO> pageRequest) throws Exception;

    /**
     * 采购申请
     * @param pageRequest
     * @return
     * @throws Exception
     */
    ResponseDTO<Page<Object>> materialApplyVOPages(Page<Object> pageRequest) throws Exception;
//    ResponseDTO<Page<MaterialApplyVO>> materialApplyVOPages(Page<MaterialDTO> pageRequest) throws Exception;

    /**
     * 采购单分页查询
     * @param pageRequest
     * @return
     * @throws Exception
     */
    ResponseDTO<Page<Object>> materialPurchasePages(Page<Object> pageRequest) throws Exception;
//    ResponseDTO<Page<MaterialPurchaseVO>> materialPurchasePages(Page<MaterialDTO> pageRequest) throws Exception;

    /**
     * 质检单分页查询
     * @param pageRequest
     * @return
     * @throws Exception
     */
    ResponseDTO<Page<Object>> materialQualityCheckPages(Page<Object> pageRequest) throws Exception;
//    ResponseDTO<Page<MaterialQualityCheckVO>> materialQualityCheckPages(Page<MaterialDTO> pageRequest) throws Exception;

    /**
     * 领料单分页查询
     * @param pageRequest
     * @return
     * @throws Exception
     */
    ResponseDTO<Page<Object>> materialOutWarehousePages(Page<Object> pageRequest) throws Exception;
//    ResponseDTO<Page<MaterialOutWarehouseVO>> materialOutWarehousePages(Page<MaterialDTO> pageRequest) throws Exception;

    /**
     * 备料单查询
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectMaterialPlanPreparationVO> preparationPage(Page<ProjectMaterialPlanPreparationDTO> pageRequest) throws Exception;
}
