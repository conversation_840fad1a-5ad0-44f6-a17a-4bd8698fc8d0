package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * RequirementManageCustContact VO对象
 *
 * <AUTHOR>
 * @since 2024-09-06 11:00:57
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementManageCustContactVO对象", description = "需求管理-客户-联系人")
@Data
public class RequirementManageCustContactVO extends ObjectVO implements Serializable {

    /**
     * 需求管理id，pms_requirement_mangement id
     */
    @ApiModelProperty(value = "需求管理id，pms_requirement_mangement id")
    private String requirementId;


    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    private String custContactId;


    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactName;


    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    private String contactPhone;


    /**
     * 联系人类型；business.商务联系人；technology.技术负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人")
    private String contactType;

    private String contactTypeName;

}
