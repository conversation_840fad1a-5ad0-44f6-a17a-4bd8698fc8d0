// ************************************************ //
// ******************年度预算管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';
import src from '/@/views/pms/projectLaborer/components/ContextMenu/src';

/**
 * 年度预算列表
 * @param params
 */
export async function postAnnualBudgetGroupList(params?: {
  keyword?: string
}) {
  return new Api('/pms/annualBudgetGroup/pages').fetch(params, '', 'POST');
}

/**
 * 年度预算列表新增
 * @param params
 */
export async function postAnnualBudgetGroup(params: {
  name: string,
  annual: number,
  [propName: string]: any
}) {
  return new Api('/pms/annualBudgetGroup').fetch(params, '', 'POST');
}

/**
 * 年度预算列表编辑
 * @param params
 */
export async function putAnnualBudgetGroup(params: {
  id: string,
  name: string,
  annual: number,
  [propName: string]: any
}) {
  return new Api('/pms/annualBudgetGroup').fetch(params, '', 'PUT');
}

/**
 * 年度预算列表详情
 * @param id
 */
export async function getAnnualBudgetGroup(id: string) {
  return new Api(`/pms/annualBudgetGroup/${id}`).fetch('', '', 'GET');
}

/**
 * 年度预算列表删除
 * @param ids
 */
export async function delAnnualBudgetGroup(ids: string[]) {
  return new Api('/pms/annualBudgetGroup').fetch(ids, '', 'DELETE');
}

/**
 * 年度预算数据分页列表
 * @param params
 */
export async function postAnnualBudgetDataPages(params:any) {
  return new Api('/pms/annualBudgetData/pages').fetch(params, '', 'POST');
}

/**
 * 年度预算数据新增
 * @param params
 */
export async function postAnnualBudgetData(params:any) {
  return new Api('/pms/annualBudgetData').fetch(params, '', 'POST');
}

/**
 * 年度预算数据编辑
 * @param params
 */
export async function putAnnualBudgetData(params:any) {
  return new Api('/pms/annualBudgetData').fetch(params, '', 'PUT');
}

/**
 * 年度预算数据删除
 * @param ids
 */
export async function delAnnualBudgetData(ids:string[]) {
  return new Api('/pms/annualBudgetData').fetch(ids, '', 'DELETE');
}

/**
 * 年度预算数据详情
 * @param id
 */
export async function getAnnualBudgetData(id:string) {
  return new Api(`/pms/annualBudgetData/${id}`).fetch('', '', 'GET');
}
