package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ProjectBudget Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 14:48:31
 */
@ApiModel(value = "ProjectBudgetVO对象", description = "项目预算表")
@Data
public class ProjectBudgetVO extends ObjectVO implements Serializable{

            /**
         * 排序
         */
        @ApiModelProperty(value = "排序")
        private Integer sort;

        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 预算名称
         */
        @ApiModelProperty(value = "预算名称")
        private String name;

        /**
         * 成本中心ID
         */
        @ApiModelProperty(value = "成本中心ID")
        private String costCenterId;

        /**
         * 成本中心名字
         */
        @ApiModelProperty(value = "成本中心名字")
        private String costCenterName;

        /**
         * 费用科目ID
         */
        @ApiModelProperty(value = "费用科目ID")
        private String expenseAccountId;

        /**
         * 费用科目名
         */
        @ApiModelProperty(value = "费用科目名")
        private String expenseAccountName;

        /**
         * 年度
         */
        @ApiModelProperty(value = "年度")
        private String year;

        /**
         * 年度预算总费用
         */
        @ApiModelProperty(value = "年度预算总费用")
        private BigDecimal yearExpense;

        /**
         * 费用预算月度表ID
         */
        @ApiModelProperty(value = "费用预算月度表ID")
        private String monthBudgetId;

        /**
         * 实际总成本
         */
        @ApiModelProperty(value = "实际总成本")
        private BigDecimal totalCost;

        /**
         * 差价
         */
        @ApiModelProperty(value = "差价")
        private BigDecimal priceDifference;

        /**
         * 是否超出预算 0为未超预算，1为超出预算
         */
        @ApiModelProperty(value = "是否超出预算 0为未超预算，1为超出预算")
        private Integer isOut;

        /**
         * 执行进度
         */
        @ApiModelProperty(value = "执行进度")
        private Integer executionSchedule;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 月份预算
         */
        @ApiModelProperty(value = "月份预算")
        private BudgetMonthVO budgetMonthVO;
    }
