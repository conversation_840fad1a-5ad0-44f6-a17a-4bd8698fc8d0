package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/03/30/17:30
 * @description:
 */

/**
 * 已创建；待立项；已立项；已验收；已关闭；
 *
 * <AUTHOR>
 */
public enum NewProjectStatusEnum {
    PROJECT_CREATED(101, "已创建"),
    PROJECT_APPROVALING(110, "待立项"),
    PROJECT_APPROVALED(120, "已立项"),
    PROJECT_CHECK_AND_ACCEPT(130, "已验收"),
    PROJECT_CLOSE(140, "已关闭");


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    NewProjectStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
