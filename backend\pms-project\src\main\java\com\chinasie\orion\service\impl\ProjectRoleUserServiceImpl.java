package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.ProjectRoleUserRepository;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.RoleRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:47
 * @description:
 */
@Service
@Slf4j
public class ProjectRoleUserServiceImpl extends OrionBaseServiceImpl<ProjectRoleUserRepository, ProjectRoleUser> implements ProjectRoleUserService {

    @Resource
    private UserBo userBo;

    @Lazy
    @Resource
    private QuestionManagementService questionManagementService;
    @Lazy
    @Resource
    private RiskManagementService riskManagementService;
    @Resource
    private ProjectRoleService projectRoleService;
    @Resource
    private ProjectService projectService;
    @Lazy
    @Resource
    private DemandManagementService demandManagementService;
    @Lazy
    @Resource
    private PostProjectService postProjectService;
    //    @Resource
//    private StakeholderService stakeholderService;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectRoleService roleService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private ProjectProperties projectProperties;

    @Autowired
    private RoleRedisHelper roleRedisHelper;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private UserBaseApiService userBaseApiService;

    @Resource
    private ProjectRoleUserRepository projectRoleUserRepository;

    @Resource
    private DeptDOMapper deptDOMapper;

    @Override
    public List<String> saveBatchProRoleUser(List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception {
        if (CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectRole projectRoleDTO = projectRoleService.getById(projectRoleUserDTOList.get(0).getProjectRoleId());
        if (ObjectUtils.isEmpty(projectRoleDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该角色不存在，请刷新后重试");
        }
        String roleDTOId = projectRoleDTO.getId();
        List<ProjectRoleUser> query = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getProjectRoleId, roleDTOId)
                .eq(ProjectRoleUser::getProjectId, projectRoleUserDTOList.get(0).getProjectId()).
                in(ProjectRoleUser::getUserId, projectRoleUserDTOList.stream().map(ProjectRoleUserDTO::getUserId).toArray()));
        if (!CollectionUtils.isEmpty(query)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "用户已存在该项目的该角色");
        }
        if (Objects.equals("pm", projectRoleDTO.getCode())) {
            Project projectDTO = projectService.getById(projectRoleDTO.getProjectId());
            if (Objects.isNull(projectDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
            }
            projectDTO.setPm(projectRoleUserDTOList.get(0).getName());
            Project project = BeanCopyUtils.convertTo(projectDTO, Project::new);
            projectService.updateById(project);
        }

        List<ProjectRoleUser> projectRoleUsers = BeanCopyUtils.convertListTo(projectRoleUserDTOList, ProjectRoleUser::new);
        projectRoleUsers.forEach(pru -> {
            pru.setDefaultFlag(1);
        });
        this.saveBatch(projectRoleUsers);
        List<String> userIdList = projectRoleUserDTOList.stream().map(ProjectRoleUserDTO::getUserId).collect(Collectors.toList());
        this.updateUserInfo(userIdList);

        return projectRoleUsers.stream().map(ProjectRoleUser::getId).collect(Collectors.toList());
    }

    public void updateUserInfo(List<String> userIdList) throws Exception {
        List<ProjectRoleUser> projectRoleUserDTOList;
        if (!CollectionUtils.isEmpty(userIdList)) {
            List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
            if (CollectionUtils.isEmpty(userVOList)) {
                return;
            }
            projectRoleUserDTOList = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                    .in(ProjectRoleUser::getUserId, userIdList.toArray()));
        } else {
            projectRoleUserDTOList = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class));
            userIdList = projectRoleUserDTOList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
        }
        List<UserVO> userVOList = userRedisHelper.getUserByIds(userIdList);
        Map<String, UserVO> idToEntityMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
        projectRoleUserDTOList.forEach(projectRoleUserDTO -> {
            String userId = projectRoleUserDTO.getUserId();
            UserVO userVO = idToEntityMap.get(userId);
            if (!ObjectUtils.isEmpty(userVO)) {
                projectRoleUserDTO.setName(userVO.getName());
                projectRoleUserDTO.setCode(userVO.getCode());
                projectRoleUserDTO.setEmail(userVO.getEmail());
            }
        });

        List<ProjectRoleUser> projectRoleUsers = BeanCopyUtils.convertListTo(projectRoleUserDTOList, ProjectRoleUser::new);
        if(!CollectionUtils.isEmpty(projectRoleUsers)){
            this.updateBatchById(projectRoleUsers);
        }


    }


    @Override
    public List<String> saveBatchApprovalProjectRoleUser(List<ProjectRoleUserDTO> projectRoleUserDTOList) throws Exception {
        if (CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectRole projectRoleDTO = projectRoleService.getById(projectRoleUserDTOList.get(0).getProjectRoleId());
        if (ObjectUtils.isEmpty(projectRoleDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "该角色不存在，请刷新后重试");
        }
        String roleDTOId = projectRoleDTO.getId();
        List<ProjectRoleUser> query = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getProjectRoleId, roleDTOId)
                .eq(ProjectRoleUser::getProjectId, projectRoleUserDTOList.get(0).getProjectId()).
                in(ProjectRoleUser::getUserId, projectRoleUserDTOList.stream().map(ProjectRoleUserDTO::getUserId).toArray()));
        if (!CollectionUtils.isEmpty(query)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "用户已存在该项目的该角色");
        }
        if (Objects.equals("pm", projectRoleDTO.getCode())) {
            Project projectDTO = projectService.getById(projectRoleDTO.getProjectId());
            if (Objects.isNull(projectDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
            }
            projectDTO.setPm(projectRoleUserDTOList.get(0).getName());
            Project project = BeanCopyUtils.convertTo(projectDTO, Project::new);
            projectService.updateById(project);
        }

        List<ProjectRoleUser> projectRoleUsers = BeanCopyUtils.convertListTo(projectRoleUserDTOList, ProjectRoleUser::new);
        projectRoleUsers.forEach(pru -> {
            pru.setDefaultFlag(0);
        });
        this.saveBatch(projectRoleUsers);
        List<String> userIdList = projectRoleUserDTOList.stream().map(ProjectRoleUserDTO::getUserId).collect(Collectors.toList());
        this.updateUserInfo(userIdList);

        return projectRoleUsers.stream().map(ProjectRoleUser::getId).collect(Collectors.toList());
    }


    @Override
    public List<SimpleVo> getProjectRoleUserList(String projectId, String name) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        LambdaQueryWrapperX<ProjectRoleUser> queryWrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        queryWrapper.eq(ProjectRoleUser::getProjectId, projectId);
        queryWrapper.innerJoin(UserDO.class, UserDO::getId, ProjectRoleUser::getUserId);
        if(StringUtils.hasText(name)){
            queryWrapper.like(UserDO::getName, name);
        }
        queryWrapper.select(ProjectRoleUser::getUserId);
        queryWrapper.selectAs(UserDO::getName, ProjectRoleUser::getName);
        List<ProjectRoleUser> projectRoleUserDTOList = this.list(queryWrapper);
        return projectRoleUserDTOList.stream().map(p -> {
            SimpleVo simpleVo = new SimpleVo();
            simpleVo.setId(p.getUserId());
            simpleVo.setName(p.getName());
            return simpleVo;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SimpleVo::getId))), ArrayList::new));
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectRoleUserVO> getProjectRoleUserPage(com.chinasie.orion.sdk.metadata.page.Page<UserQueryDTO> pageRequest) throws Exception {

        com.chinasie.orion.sdk.metadata.page.Page<ProjectRoleUserVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        UserQueryDTO query = pageRequest.getQuery();
        if (Objects.isNull(query) || !StringUtils.hasText(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id不能为空");
        }
        IPage<ProjectRoleUser> userPageRequest = new Page<>();
        userPageRequest.setCurrent(pageRequest.getPageNum());
        userPageRequest.setSize(pageRequest.getPageSize());

        LambdaQueryWrapperX<ProjectRoleUser> projectRoleUserDTO = new LambdaQueryWrapperX<>();
        projectRoleUserDTO.eq(ProjectRoleUser::getProjectId, query.getProjectId());
        Project project = projectService.getById(query.getProjectId());
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
//        if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
//            projectRoleUserDTO.eq(ProjectRoleUser::getDefaultFlag, 1);
//        }

        String name = query.getName();
        if (StringUtils.hasText(name)) {
            projectRoleUserDTO.eq(ProjectRoleUser::getName, name);
        }
        String number = query.getNumber();
        if (StringUtils.hasText(number)) {
            projectRoleUserDTO.like(ProjectRoleUser::getNumber, number);
        }
        String mobile = query.getMobile();
        if (StringUtils.hasText(mobile)) {
            projectRoleUserDTO.like(ProjectRoleUser::getMobile, mobile);
        }
        String email = query.getEmail();
        if (StringUtils.hasText(email)) {
            projectRoleUserDTO.like(ProjectRoleUser::getEmail, email);
        }

        String projectRoleId = query.getProjectRoleId();
        if (StringUtils.hasText(projectRoleId)) {
            projectRoleUserDTO.eq(ProjectRoleUser::getProjectRoleId, projectRoleId);
        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, projectRoleUserDTO);
        }
        IPage<ProjectRoleUser> projectRoleUserDTOList = this.page(userPageRequest, projectRoleUserDTO);
        if (ObjectUtils.isEmpty(projectRoleUserDTOList)) {
            return resultPage;
        }
        List<ProjectRoleUser> content = projectRoleUserDTOList.getRecords();
        if (CollectionUtils.isEmpty(content)) {
            return resultPage;
        }
        List<ProjectRoleUserVO> projectRoleUserVOs = new ArrayList<>();
        for (ProjectRoleUser c : content) {
            SimpleUser user = userRedisHelper.getSimpleUserById(c.getUserId());
            if (user == null) {
                continue;
            }
            ProjectRoleUserVO projectRoleUserVO = BeanCopyUtils.convertTo(c, ProjectRoleUserVO::new);
            projectRoleUserVO.setMobile(user.getMobile());
            projectRoleUserVO.setEmail(user.getEmail());
            projectRoleUserVO.setOrgName(user.getOrgName());
            projectRoleUserVO.setDeptName(user.getDeptName());
            UserVO userById = userRedisHelper.getUserById(c.getUserId());
            if (!ObjectUtils.isEmpty(userById)) {
                projectRoleUserVO.setStatus(userById.getStatus());
            }
            projectRoleUserVOs.add(projectRoleUserVO);
        }
//
//        PageResult<ProjectRoleUserVO> userVOPageRequest = new PageResult<>();
//        userVOPageRequest.setPageNum(pageRequest.getPageNum());
//        userVOPageRequest.setPageSize(pageRequest.getPageSize());
//        userVOPageRequest.setTotalSize(projectRoleUserDTOList.getTotal());
//        userVOPageRequest.setContent(projectRoleUserVOs);

        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectRoleUserVOs);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectRoleUserVOs, ProjectRoleUserVO::getId, ProjectRoleUserVO::getDataStatus, ProjectRoleUserVO::setRdAuthList,
                ProjectRoleUserVO::getCreatorId,
                ProjectRoleUserVO::getModifyId,
                ProjectRoleUserVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(projectRoleUserDTOList.getTotal());
        resultPage.setContent(projectRoleUserVOs);
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectRoleUserVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectRoleUserVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<ProjectRoleUserVO> getProjectApprovalRoleUserPage(com.chinasie.orion.sdk.metadata.page.Page<UserQueryDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ProjectRoleUserVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        UserQueryDTO query = pageRequest.getQuery();
        if (Objects.isNull(query) || !StringUtils.hasText(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id不能为空");
        }
        IPage<ProjectRoleUser> userPageRequest = new Page<>();
        userPageRequest.setCurrent(pageRequest.getPageNum());
        userPageRequest.setSize(pageRequest.getPageSize());

        LambdaQueryWrapperX<ProjectRoleUser> projectRoleUserDTO = new LambdaQueryWrapperX<>();
        projectRoleUserDTO.eq(ProjectRoleUser::getDefaultFlag, 0);
        String name = query.getName();
        if (StringUtils.hasText(name)) {
            projectRoleUserDTO.eq(ProjectRoleUser::getName, name);
        }
        String number = query.getNumber();
        if (StringUtils.hasText(number)) {
            projectRoleUserDTO.like(ProjectRoleUser::getNumber, number);
        }
        String mobile = query.getMobile();
        if (StringUtils.hasText(mobile)) {
            projectRoleUserDTO.like(ProjectRoleUser::getMobile, mobile);
        }
        String email = query.getEmail();
        if (StringUtils.hasText(email)) {
            projectRoleUserDTO.like(ProjectRoleUser::getEmail, email);
        }
        projectRoleUserDTO.eq(ProjectRoleUser::getProjectId, query.getProjectId());

        String projectRoleId = query.getProjectRoleId();
        if (StringUtils.hasText(projectRoleId)) {
            projectRoleUserDTO.eq(ProjectRoleUser::getProjectRoleId, projectRoleId);
        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, projectRoleUserDTO);
        }
        IPage<ProjectRoleUser> projectRoleUserDTOList = this.page(userPageRequest, projectRoleUserDTO);
        if (ObjectUtils.isEmpty(projectRoleUserDTOList)) {
            return resultPage;
        }
        List<ProjectRoleUser> content = projectRoleUserDTOList.getRecords();
        if (CollectionUtils.isEmpty(content)) {
            return resultPage;
        }

        List<ProjectRoleUserVO> projectRoleUserVOs = new ArrayList<>();
        content.forEach(c -> {
            SimpleUser user = userRedisHelper.getSimpleUserById(c.getUserId());
            ProjectRoleUserVO projectRoleUserVO = BeanCopyUtils.convertTo(c, ProjectRoleUserVO::new);
            projectRoleUserVO.setMobile(user.getMobile());
            projectRoleUserVO.setEmail(user.getEmail());
            projectRoleUserVO.setOrgName(user.getOrgName());
            projectRoleUserVO.setDeptName(user.getDeptName());
            projectRoleUserVOs.add(projectRoleUserVO);
        });

//        PageResult<ProjectRoleUserVO> userVOPageRequest = new PageResult<>();
//        userVOPageRequest.setPageNum(pageRequest.getPageNum());
//        userVOPageRequest.setPageSize(pageRequest.getPageSize());
//        userVOPageRequest.setTotalSize(projectRoleUserDTOList.getTotal());
//        userVOPageRequest.setContent(projectRoleUserVOs);
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectRoleUserVOs);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectRoleUserVOs, ProjectRoleUserVO::getId, ProjectRoleUserVO::getDataStatus, ProjectRoleUserVO::setRdAuthList,
                ProjectRoleUserVO::getCreatorId,
                ProjectRoleUserVO::getModifyId,
                ProjectRoleUserVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(projectRoleUserDTOList.getTotal());
        resultPage.setContent(projectRoleUserVOs);
        return resultPage;
    }


    @Override
    public ProjectRoleUserVO getProjectRoleUserDetail(String id, String pageCode) throws Exception {
        ProjectRoleUser projectRoleUserDTO = this.getById(id);
        if (Objects.isNull(projectRoleUserDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectRoleUserVO projectRoleUserVO = new ProjectRoleUserVO();
        BeanCopyUtils.copyProperties(projectRoleUserDTO, projectRoleUserVO);

        List<UserVO> userVOList = userBo.getUserDetailByUserIdList(Collections.singletonList(projectRoleUserVO.getUserId()));
        List<String> userIdList = new ArrayList<>();
        userIdList.add(projectRoleUserVO.getModifyId());
        userIdList.add(projectRoleUserVO.getCreatorId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(userVOList)) {
            if (!CollectionUtils.isEmpty(userVOList.get(0).getOrganizations())) {
                projectRoleUserVO.setDept(userVOList.get(0).getOrganizations().get(0).getId());
                projectRoleUserVO.setDeptName(userVOList.get(0).getOrganizations().get(0).getName());
            }
            projectRoleUserVO.setMobile(userVOList.get(0).getMobile());
            projectRoleUserVO.setEmail(userVOList.get(0).getEmail());
            projectRoleUserVO.setStatus(userVOList.get(0).getStatus());
            projectRoleUserVO.setStatusName(projectRoleUserVO.getStatus() == 1 ? "正常" : (projectRoleUserVO.getStatus() == 0 ? "锁定" : "离职"));
        }
        projectRoleUserVO.setModifyName(userVOIdAndNameMap.get(projectRoleUserVO.getModifyId()));
        projectRoleUserVO.setCreatorName(userVOIdAndNameMap.get(projectRoleUserVO.getCreatorId()));

        // 权限设置
        if (org.springframework.util.StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(id, currentUserId);
            pmsAuthUtil.setDetailAuths(projectRoleUserVO, currentUserId, pageCode, projectRoleUserVO.getDataStatus(), ProjectRoleUserVO::setDetailAuthList, projectRoleUserVO.getCreatorId(), projectRoleUserVO.getModifyId(), projectRoleUserVO.getOwnerId(), roleCodeList);
        }
        return projectRoleUserVO;
    }

    @Override
    public Boolean removeProjectRoleUser(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        List<ProjectRoleUser> projectRoleUserDTOList = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .in(ProjectRoleUser::getId, idList.toArray()));
        if (CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            return Boolean.TRUE;
        }
        List<String> userIdList = projectRoleUserDTOList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
        List<ProjectRoleUser> projectRoleUserDTOList1 = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .notIn(ProjectRoleUser::getId, idList.toArray())
                .in(ProjectRoleUser::getUserId, userIdList.toArray()));
        if (!CollectionUtils.isEmpty(projectRoleUserDTOList1)) {
            List<String> filterUserIdList = projectRoleUserDTOList1.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
            userIdList = userIdList.stream().filter(f -> !filterUserIdList.contains(f)).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(userIdList)) {
            List<QuestionManagement> questionManagementDTOList = questionManagementService.list(
                    new LambdaQueryWrapper<>(QuestionManagement.class)
                            .in(QuestionManagement::getRecipient, userIdList.toArray())
                            .in(QuestionManagement::getPrincipalId, userIdList.toArray())
            );
            if (!CollectionUtils.isEmpty(questionManagementDTOList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
            List<RiskManagement> riskManagementDTOList = riskManagementService.list(new LambdaQueryWrapper<>(RiskManagement.class).
                    in(RiskManagement::getDiscernPerson, userIdList.toArray())
                    .in(RiskManagement::getPrincipalId, userIdList.toArray()));
            if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
            List<DemandManagement> demandManagementDTOList = demandManagementService.list(new LambdaQueryWrapper<>(DemandManagement.class).
                    in(DemandManagement::getPrincipalId, userIdList.toArray()).
                    in(DemandManagement::getRecipientId, userIdList.toArray()));
            if (!CollectionUtils.isEmpty(demandManagementDTOList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
            List<PostProject> postProjectDTOList = postProjectService.list(new LambdaQueryWrapper<>(PostProject.class).
                    in(PostProject::getPrincipalId, userIdList.toArray()));
            if (!CollectionUtils.isEmpty(postProjectDTOList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
        }
        this.removeBatchByIds(idList);
        //修改项目的项目经理
        List<ProjectRole> projectRoleDTOList = projectRoleService.list(new LambdaQueryWrapper<>(ProjectRole.class)
                .eq(ProjectRole::getCode, "pm").
                in(ProjectRole::getId, projectRoleUserDTOList.stream().map(ProjectRoleUser::getProjectRoleId).distinct().toArray()));
        if (!CollectionUtils.isEmpty(projectRoleDTOList)) {
            List<ProjectRoleUser> pm = this.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                    .eq(ProjectRoleUser::getProjectRoleId, projectRoleDTOList.get(0).getId()));
            Project projectDTO = projectService.getById(projectRoleDTOList.get(0).getProjectId());
            if (Objects.isNull(projectDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
            }
            projectDTO.setPm("");
            if (!CollectionUtils.isEmpty(pm)) {
                projectDTO.setPm(pm.get(0).getName());
            }
            projectService.updateById(projectDTO);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<ProjectRoleUserSearchVO> searchProjectUser(KeywordDto keywordDto) throws Exception {
        LambdaQueryWrapper<ProjectRoleUser> LambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        LambdaQueryWrapper.eq(ProjectRoleUser::getProjectId, keywordDto.getProjectId());
        Project project = projectService.getById(keywordDto.getProjectId());
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
            LambdaQueryWrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }

        if (StringUtils.hasText(keywordDto.getKeyword())) {
            LambdaQueryWrapper.like(ProjectRoleUser::getName, keywordDto.getKeyword());
            LambdaQueryWrapper.like(ProjectRoleUser::getNumber, keywordDto.getKeyword());
        }
        List<ProjectRoleUser> projectRoleUserDTOList = this.list(LambdaQueryWrapper);
        if (CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            return new ArrayList<>();
        }
        List<ProjectRoleUserSearchVO> projectRoleUserSearchVOList = BeanCopyUtils.convertListTo(projectRoleUserDTOList, ProjectRoleUserSearchVO::new);
        setContent(projectRoleUserSearchVOList);
        return projectRoleUserSearchVOList;
    }

    @Override
    public List<ProjectRoleUserSearchVO> getProjectRoleUserByIds(String projectId, List<String> userIds) throws Exception {
        LambdaQueryWrapper<ProjectRoleUser> LambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        if (StringUtils.hasText(projectId)) {
            LambdaQueryWrapper.eq(ProjectRoleUser::getProjectId, projectId);
            Project project = projectService.getById(projectId);
            // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
            if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
                LambdaQueryWrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
            }
        }
        if (!CollectionUtils.isEmpty(userIds)) {
            LambdaQueryWrapper.in(ProjectRoleUser::getUserId, userIds.toArray());
        }
        List<ProjectRoleUser> projectRoleUserDTOList = this.list(LambdaQueryWrapper);
        if (CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            return new ArrayList<>();
        }
        List<ProjectRoleUserSearchVO> projectRoleUserSearchVOList = BeanCopyUtils.convertListTo(projectRoleUserDTOList, ProjectRoleUserSearchVO::new);
        setContent(projectRoleUserSearchVOList);

        return projectRoleUserSearchVOList;
    }

    @Override
    public List<ProjectRoleUserSearchVO> getListByRoleIdAndProjectId(List<String> businessIdList, String projectId) throws Exception {

        LambdaQueryWrapper<ProjectRoleUser> LambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        if (StringUtils.hasText(projectId)) {
            LambdaQueryWrapper.eq(ProjectRoleUser::getProjectId, projectId);
            Project project = projectService.getById(projectId);
            // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
            if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
                LambdaQueryWrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
            }
        }
        if (!CollectionUtils.isEmpty(businessIdList)) {
            LambdaQueryWrapper.in(ProjectRoleUser::getProjectRoleId, businessIdList);
        }
        List<ProjectRoleUser> projectRoleUserDTOS = this.list(LambdaQueryWrapper);
        List<ProjectRoleUserSearchVO> projectRoleUserSearchVOList = BeanCopyUtils.convertListTo(projectRoleUserDTOS, ProjectRoleUserSearchVO::new);
        setContent(projectRoleUserSearchVOList);

        return projectRoleUserSearchVOList;
    }

    @Override
    public List<UserVO> getSimpleUserList(String projectId) throws Exception {
        return this.getUserListByProjectId(projectId);
    }

    @Override
    public List<SimpleUserVO> getSimpleUserByProjectId(String projectId) throws Exception {
        List<String> userIdList = this.getUserIdListByProjectId(projectId);
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        long endTime8 = System.currentTimeMillis();
        List<SimpleUserVO> users = userBaseApiService.getUserByIds(userIdList);
        long endTime9 = System.currentTimeMillis();
        log.debug("start9: 九阶段查询任务执行耗时: {} 毫秒",(endTime9 - endTime8));
        return users;
    }

    @Override
    public List<UserVO> getAllSimpleUserList(String projectId) throws Exception {
        List<String> userIdList = this.getAllUserListByProjectId(projectId);
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<UserVO> users = userRedisHelper.getUserByIds(userIdList);
        return users.stream().sorted(Comparator.comparing(UserVO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }



    public List<String> getUserIdListByProjectId(String projectId){
        Integer status = projectService.getStatus(projectId);
        LambdaQueryWrapperX<ProjectRoleUser> wrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        wrapper.eq(ProjectRoleUser::getProjectId, projectId);
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(120, 130, 140).contains(status)) {
            wrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }
        wrapper.select(ProjectRoleUser::getUserId);
        List<ProjectRoleUser> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return  list.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
    }


    @Override
    public List<UserVO> getUserListByProjectId(String projectId) throws Exception {
                long startTime = System.currentTimeMillis();
        long endTime00 = System.currentTimeMillis();
        Integer status = projectService.getStatus(projectId);
        LambdaQueryWrapperX<ProjectRoleUser> wrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        wrapper.eq(ProjectRoleUser::getProjectId, projectId);
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(120, 130, 140).contains(status)) {
            wrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }
        wrapper.innerJoin(UserDO.class, UserDO::getId, ProjectRoleUser::getUserId);
        wrapper.innerJoin(DeptUserDO.class, DeptUserDO::getUserId, UserDO::getId);
        wrapper.innerJoin(DeptDO.class, DeptDO::getId, DeptUserDO::getDeptId);
        wrapper.select(ProjectRoleUser::getUserId);
        wrapper.selectAs(UserDO::getName,ProjectRoleUser::getName)
                .selectAs(UserDO::getCode, ProjectRoleUser::getCode)
                .selectAs(DeptDO::getParentId, ProjectRoleUser::getParentId)
                .selectAs(DeptDO::getName, ProjectRoleUser::getDeptName)
                .selectAs(DeptDO::getDeptCode, ProjectRoleUser::getDeptCode)
                .selectAs(DeptDO::getType, ProjectRoleUser::getType)
                .selectAs(DeptDO::getId, ProjectRoleUser::getDeptId);
        List<ProjectRoleUser> list = this.list(wrapper);
        log.debug("start00-001: 获取用户链表列表: {} 毫秒", (endTime00 - startTime));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<String,UserVO> userMap = new HashMap<>();
        Map<String,List<DeptVO>> userTodeptMap = new HashMap<>();
        for (ProjectRoleUser projectRoleUser : list) {
            List<DeptVO> deptList = userTodeptMap.get(projectRoleUser.getUserId());
            if (CollectionUtil.isEmpty(deptList)) {
                deptList = new ArrayList<>();
            }
            DeptVO deptVO = new DeptVO();
            deptVO.setId(projectRoleUser.getDeptId());
            deptVO.setName(projectRoleUser.getDeptName());
            deptVO.setDeptCode(projectRoleUser.getDeptCode());
            deptVO.setType(projectRoleUser.getType());
            deptVO.setParentId(projectRoleUser.getParentId());
            deptList.add(deptVO);
            userTodeptMap.put(projectRoleUser.getUserId(),deptList);
            UserVO userVO =  new UserVO();
            userVO.setId(projectRoleUser.getUserId());
            userVO.setName(projectRoleUser.getName());
            userVO.setCode(projectRoleUser.getCode());
            userMap.put(projectRoleUser.getUserId(),userVO);
        }
        List<UserVO> userList = new ArrayList<>();
        for (Map.Entry<String, UserVO> stringUserVOEntry : userMap.entrySet()) {
            UserVO userVO =  stringUserVOEntry.getValue();
            List<DeptVO> deptList = userTodeptMap.get(stringUserVOEntry.getKey());
            userVO.setOrganizations(deptList);
            userList.add(userVO);
        }
        return userList;
    }

    @Override
    public List<String> getAllUserListByProjectId(String projectId) throws Exception {
        LambdaQueryWrapper<ProjectRoleUser> wrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        wrapper.eq(ProjectRoleUser::getProjectId, projectId);
        Project project = projectService.getById(projectId);
        List<ProjectRoleUser> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream()
                .map(ProjectRoleUser::getUserId)
                .distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public List<ProjectRoleUser> findUserListByCode(String projectId, String ruleCode) throws Exception {
        LambdaQueryWrapper<ProjectRole> roleWrapper = new LambdaQueryWrapper<>(ProjectRole.class);
        roleWrapper.eq(ProjectRole::getProjectId, projectId);
        roleWrapper.eq(ProjectRole::getRoleCode, ruleCode);
        List<ProjectRole> projectRoles = roleService.list(roleWrapper);
        List<String> projectRoleId = projectRoles.stream().map(ProjectRole::getId).collect(Collectors.toList());
        LambdaQueryWrapper<ProjectRoleUser> wrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        wrapper.eq(ProjectRoleUser::getProjectId, projectId);
        Project project = projectService.getById(projectId);
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
            wrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }
        wrapper.in(ProjectRoleUser::getProjectRoleId, projectRoleId);
        List<ProjectRoleUser> roleUsers = this.list(wrapper);
        return BeanCopyUtils.convertListTo(roleUsers, ProjectRoleUser::new);
    }

    @Override
    public List<SimpleRoleVO> getCurrentUserRoleByProjectId(String projectId) throws Exception {
        //设置当前用户
        RoleByUserParamDTO roleByUserParamDTO = new RoleByUserParamDTO();
        roleByUserParamDTO.setProjectId(projectId);
        roleByUserParamDTO.setUserId(CurrentUserHelper.getCurrentUserId());
        List<SimpleRoleVO> simpleRoleVOS = getRoleInfoByProjectAndUser(roleByUserParamDTO);
        return simpleRoleVOS;
    }

    @Override
    public List<SimpleRoleVO> getRoleInfoByProjectAndUser(RoleByUserParamDTO roleByUserParamDTO) throws Exception {
        // 获取缓存的用户项目角色信息
        List<SimpleRoleVO> roles = getCachedUserProjectRole(roleByUserParamDTO.getProjectId(), roleByUserParamDTO.getUserId());
        if (CollectionUtil.isNotEmpty(roles)) {
            return roles;
        }

        String userId = roleByUserParamDTO.getUserId();
        String projectId = roleByUserParamDTO.getProjectId();
        LambdaQueryWrapper<ProjectRoleUser> wrapper = this.getWrapper(projectId, userId);

        Project project = projectService.getById(projectId);
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(120, 130, 140).contains(project.getStatus())) {
            wrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }

        List<ProjectRoleUser> roleUsers = this.list(wrapper);
        if (CollectionUtils.isEmpty(roleUsers)) {
            return new ArrayList<>();
        }
        List<String> businessRoleIdList = roleUsers.stream()
                .map(ProjectRoleUser::getProjectRoleId)
                .collect(Collectors.toList());
        LambdaQueryWrapper<ProjectRole> orionWrapper = new LambdaQueryWrapper<>(ProjectRole.class);
        orionWrapper.in(ProjectRole::getId, businessRoleIdList);
        List<ProjectRole> projectRoles = projectRoleService.list(orionWrapper);
        List<SimpleRoleVO> simpleRoleVOS = new ArrayList<>();
        for (ProjectRole projectRole : projectRoles) {
            if (StringUtils.hasText(projectRole.getRoleCode())) {
                simpleRoleVOS.add(new SimpleRoleVO(projectRole.getRoleCode(), projectRole.getBusinessId()));
            } else if (StringUtils.hasText(projectRole.getCode())) {
                simpleRoleVOS.add(new SimpleRoleVO(projectRole.getCode(), projectRole.getBusinessId()));
            }
        }

        // 缓存用户的项目角色信息
        cacheUserProjectRole(roleByUserParamDTO.getProjectId(), roleByUserParamDTO.getUserId(), simpleRoleVOS);

        return simpleRoleVOS;
    }

    @Override
    public List<ProjectUserInfoVO> listProjectUser(String projectId) throws Exception {
        long startTime = System.currentTimeMillis();
        List<ProjectUserInfoVO> userInfoVOS = CollUtil.toList();
        List<UserVO> userList = this.getSimpleUserList(projectId);
        long endTime00 = System.currentTimeMillis();
        log.debug("start00: 项目库获取用户列表: {} 毫秒", (endTime00 - startTime));
        if (CollUtil.isEmpty(userList)) {
            return userInfoVOS;
        }
        List<DeptVO> orgList = userList.stream().map(UserVO::getOrganizations).filter(CollUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
        Map<String, DeptVO> idToDeptMap = orgList.stream().collect(Collectors.toMap(DeptVO::getId, deptVO -> deptVO, (key1, key2) -> key1));

        //强制获取所有部门信息
        LambdaQueryWrapper<DeptDO> deptWrapper = new LambdaQueryWrapper<>(DeptDO.class);
        deptWrapper.select(DeptDO::getId,DeptDO::getParentId,DeptDO::getType,DeptDO::getTypeCode,DeptDO::getName);
        List<DeptDO> deptDOS = deptDOMapper.selectList(deptWrapper);
        for (DeptDO deptDO : deptDOS) {
            DeptVO  deptVO = new DeptVO();
            deptVO.setId(deptDO.getId());
            deptVO.setType(deptDO.getType());
            deptVO.setTypeCode(deptDO.getTypeCode());
            deptVO.setName(deptDO.getName());
            idToDeptMap.put(deptDO.getId(),deptVO);
        }
        userList.forEach(item -> {
            ProjectUserInfoVO userInfoVO = ProjectUserInfoVO.builder()
                    .id(item.getId())
                    .name(item.getName())
                    .code(item.getCode())
                    .build();
            setOrgInfo1(userInfoVO, item.getOrganizations(),idToDeptMap);
            userInfoVOS.add(userInfoVO);
        });
        return userInfoVOS;
    }

    @Override
    public List<ProjectUserInfoVO> listAllProjectUser(String projectId) throws Exception {
        List<ProjectUserInfoVO> userInfoVOS = CollUtil.toList();
        List<UserVO> userList = this.getAllSimpleUserList(projectId);
        if (CollUtil.isEmpty(userList)) {
            return userInfoVOS;
        }
        userList.forEach(item -> {
            ProjectUserInfoVO userInfoVO = ProjectUserInfoVO.builder()
                    .id(item.getId())
                    .name(item.getName())
                    .code(item.getCode())
                    .build();
            setOrgInfo(userInfoVO, item.getOrganizations());
            userInfoVOS.add(userInfoVO);
        });



        return userInfoVOS;
    }

    @Override
    public ProjectUserInfoVO getUserInfoByProjectId(String projectId, String userCode) throws Exception {
        List<ProjectUserInfoVO> userInfoVOS = listProjectUser(projectId);
        return userInfoVOS.stream()
                .filter(item -> item.getCode().equals(userCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 通过项目信息获取所有项目成员
     *
     * @param projectId
     * @return
     */
    @Override
    public List<SimpleVO> getAllUserByProjectId(String projectId) throws Exception {
        List<UserVO> userVOS = Optional.ofNullable(this.getAllSimpleUserList(projectId)).orElse(new ArrayList<>());
        List<SimpleVO> simpleVOS = BeanCopyUtils.convertListTo(userVOS, SimpleVO::new);
        List<SimpleVO> result = simpleVOS.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(SimpleVO::getId))), ArrayList::new));
        return result;
    }

    @Override
    public boolean isPmRoleUser(String projectId) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> userList = projectRoleUserRepository.getUserListByProjectPm(projectId, projectProperties.getInitManagerRoleCode(),currentUserId);
        if(!CollectionUtils.isEmpty(userList)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Override
    public List<String> getAllUserIdListByProjectIdAndRoleId(String projectId, String roleId) throws Exception {
        LambdaQueryWrapperX<ProjectRoleUser> newProjectRoleUserOrionWrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        newProjectRoleUserOrionWrapper.select(ProjectRoleUser::getUserId);
        newProjectRoleUserOrionWrapper.distinct();
        newProjectRoleUserOrionWrapper.select(ProjectRoleUser::getUserId);
        newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getProjectId, projectId);
        if (StringUtils.hasText(roleId)) {
            newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getProjectRoleId, roleId);
        }
        //TODO 这块儿逻辑之前是否有用？
        Project project = projectService.getById(projectId);
        // 如果项目不是在 执行中，已验收，已关闭，只能看到自己的
        if (!Arrays.asList(
                ProjectStatusEnum.APPROVED.getStatus(),
                ProjectStatusEnum.ACCEPTED.getStatus(),
                ProjectStatusEnum.CLOSE.getStatus()).contains(project.getStatus())) {
            newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getDefaultFlag, 1);
        }

        List<ProjectRoleUser> userIdList = this.list(newProjectRoleUserOrionWrapper);
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return userIdList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
    }


    @Override
    public List<String> getRoleInfoByProjectAndUserId(String projectId, String userId) throws Exception {
        List<SimpleRoleVO> simpleRoleVOS = new ArrayList<>();
        List<RoleVO> roleVOS = getRoleByProjectAndUserId(projectId, userId);
        if (!CollectionUtil.isEmpty(roleVOS)) {
            roleVOS.forEach(p -> {
                simpleRoleVOS.add(new SimpleRoleVO(p.getId(), p.getCode()));
            });
        }
        // 缓存用户的项目角色信息
        return simpleRoleVOS.stream().map(SimpleRoleVO::getRoleCode).collect(Collectors.toList());
    }

    @Override
    public List<RoleVO> getRoleByProjectAndUserId(String projectId, String userId) throws Exception {
        LambdaQueryWrapperX<ProjectRoleUser> wrapper = this.getProjectRoleUserWrapper(projectId, userId);
        wrapper.innerJoin(ProjectRole.class,ProjectRole::getId, ProjectRoleUser::getProjectRoleId);
        wrapper.selectAs(ProjectRole::getBusinessId, ProjectRoleUser::getProjectRoleId);
        List<ProjectRoleUser> roleUsers = this.list(wrapper);
        if (CollectionUtils.isEmpty(roleUsers)) {
            return new ArrayList<>();
        }
        List<String> roleIds = roleUsers.stream().map(ProjectRoleUser::getProjectRoleId).collect(Collectors.toList());
        List<RoleVO> roleVOS = roleRedisHelper.getRoleByIds(roleIds);
        List<SimpleRoleVO> simpleRoleVOS = new ArrayList<>();
        if (!CollectionUtil.isEmpty(roleVOS)) {
            roleVOS.forEach(p -> {
                simpleRoleVOS.add(new SimpleRoleVO(p.getId(), p.getCode()));
            });
            cacheUserProjectRole(projectId, userId, simpleRoleVOS);
            return roleVOS;
        }else{
            return new ArrayList<>();
        }
    }

    public LambdaQueryWrapperX<ProjectRoleUser> getProjectRoleUserWrapper(String projectId, String userId) {
        LambdaQueryWrapperX<ProjectRoleUser> newProjectRoleUserOrionWrapper = new LambdaQueryWrapperX<>();
        newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getProjectId, projectId);
        newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getLogicStatus, StatusEnum.ENABLE.getIndex());
        newProjectRoleUserOrionWrapper.select(ProjectRoleUser::getProjectRoleId);
        if (StringUtils.hasText(userId)) {
            newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getUserId, userId);
        }
        return newProjectRoleUserOrionWrapper;
    }

    private void setOrgInfo1(ProjectUserInfoVO userInfoVO, List<DeptVO> orgs,Map<String,DeptVO> deptMap) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        DeptVO deptVO = orgs.get(0);
        if ("20".equals(deptVO.getType())) {
            userInfoVO.setDeptId(deptVO.getId());
            userInfoVO.setDeptName(deptVO.getName());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = deptMap.get(deptVO.getParentId());
            if(Objects.nonNull(deptOrg)){
                userInfoVO.setDeptId(deptOrg.getId());
                userInfoVO.setDeptName(deptOrg.getName());
                userInfoVO.setSectionId(deptVO.getId());
                userInfoVO.setSectionName(deptVO.getName());
            }
        }else{
            userInfoVO.setDeptId(deptVO.getId());
            userInfoVO.setDeptName(deptVO.getName());
        }

    }


    private void setOrgInfo(ProjectUserInfoVO userInfoVO, List<DeptVO> orgs) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        DeptVO deptVO = orgs.get(0);
        if ("20".equals(deptVO.getType())) {
            userInfoVO.setDeptId(deptVO.getId());
            userInfoVO.setDeptName(deptVO.getName());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = getParentOrgId(deptVO);
            userInfoVO.setDeptId(deptOrg.getId());
            userInfoVO.setDeptName(deptOrg.getName());
            userInfoVO.setSectionId(deptVO.getId());
            userInfoVO.setSectionName(deptVO.getName());
        } else if ("40".equals(deptVO.getType())) {
            DeptVO sectionOrg = getParentOrgId(deptVO);
            DeptVO deptOrg = getParentOrgId(sectionOrg);
            userInfoVO.setDeptId(deptOrg.getId());
            userInfoVO.setDeptName(deptOrg.getName());
            userInfoVO.setSectionId(sectionOrg.getId());
            userInfoVO.setSectionName(sectionOrg.getName());
            userInfoVO.setTeamsId(deptVO.getId());
            userInfoVO.setTeamsName(deptVO.getName());
        }

    }

    private DeptVO getParentOrgId(DeptVO org) {
        String parentId = org.getParentId();
        DeptVO organization = deptRedisHelper.getDeptById(parentId);
        return Objects.nonNull(organization) ? organization : new DeptVO();
    }

    /**
     * 获取缓存的用户项目角色信息.
     *
     * @param projectId
     * @param userId
     * @return
     */
    private List<SimpleRoleVO> getCachedUserProjectRole(String projectId, String userId) {
        String key = userProjectRoleKey(projectId);
        List<SimpleRoleVO> list = (List<SimpleRoleVO>) redisTemplate.opsForHash().get(key, userId);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    private String userProjectRoleKey(String projectId) {
        return String.format("pms::user-project-role::%s", projectId);
    }

    private void setContent(List<ProjectRoleUserSearchVO> projectRoleUserSearchVOList) throws Exception {
        List<String> userIdList = projectRoleUserSearchVOList.stream().map(ProjectRoleUserSearchVO::getUserId).collect(Collectors.toList());
        List<UserVO> userVOList = userBo.getUserDetailByUserIdList(userIdList);
        Map<String, UserVO> userVOMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
        projectRoleUserSearchVOList.forEach(o -> {
            if (Objects.nonNull(userVOMap.get(o.getUserId()))) {
                if (!CollectionUtils.isEmpty(userVOMap.get(o.getUserId()).getOrganizations())) {
                    o.setDept(userVOMap.get(o.getUserId()).getOrganizations().get(0).getId());
                    o.setDeptName(userVOMap.get(o.getUserId()).getOrganizations().get(0).getName());
                }
                o.setNumber(userVOMap.get(o.getUserId()).getCode());
            }
        });
    }

//    @PostConstruct
    public void initProjectRoleUser() {
        try {
            updateUserInfo(null);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public LambdaQueryWrapper<ProjectRoleUser> getWrapper(String projectId, String userId) {
        LambdaQueryWrapper<ProjectRoleUser> newProjectRoleUserOrionWrapper = new LambdaQueryWrapper<>(ProjectRoleUser.class);
        newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getProjectId, projectId);
        if (StringUtils.hasText(userId)) {
            newProjectRoleUserOrionWrapper.eq(ProjectRoleUser::getUserId, userId);
        }
        return newProjectRoleUserOrionWrapper;
    }

    /**
     * 缓存用户在项目下的角色信息.
     *
     * @param projectId
     * @param userId
     * @param roles
     */
    private void cacheUserProjectRole(String projectId, String userId, List<SimpleRoleVO> roles) {
        String key = userProjectRoleKey(projectId);
        redisTemplate.opsForHash().put(key, userId, roles);
        // 缓存失效时间为 7天 + 0 - 30 之间的随机小时数, 随机数是避免数据集体时效
        long expireTime = 24 * 60 * 7 + 60 * (new Random().nextInt(30));
        redisTemplate.expire(key, expireTime, TimeUnit.MINUTES);
    }


    @Override
    public List<SimpleVO> getDeptListByProject(String projectId) throws Exception {
        // 获取当前项目下所有的用户信息列表
        List<UserVO> userList = this.getSimpleUserList(projectId);
        List<SimpleVO> simpleVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(userList)) {
            return new ArrayList<>();
        }
        List<DeptVO> orgList = userList.stream().map(UserVO::getOrganizations).filter(item -> CollUtil.isNotEmpty(item)).flatMap(List::stream).collect(Collectors.toList());
        // 通过用户信息中的部门ID 分组 然后去重
        Map<String, SimpleVO> map = orgList.stream().filter(simpleUser -> StringUtils.hasText(simpleUser.getId())).collect(Collectors.toMap(DeptVO::getId, org -> {
            SimpleVO simpleVO = new SimpleVO();
            simpleVO.setId(org.getId());
            simpleVO.setName(org.getName());
            return simpleVO;
        }, (k1, k2) -> k2));
        // 组装数据返回
        for (Map.Entry<String, SimpleVO> stringSimpleVOEntry : map.entrySet()) {
            simpleVOList.add(stringSimpleVOEntry.getValue());
        }
        return simpleVOList;
    }


    @Override
    public Boolean projectExistCurrentUser(String projectId) throws Exception {
        List<ProjectUserInfoVO> projectUserInfoVOS = this.listProjectUser(projectId);
        if (CollectionUtils.isEmpty(projectUserInfoVOS)) {
            return false;
        }
        List<String> userIds = projectUserInfoVOS.stream().map(ProjectUserInfoVO::getId).collect(Collectors.toList());
        return userIds.contains(CurrentUserHelper.getCurrentUserId());
    }

    @Override
    public Map<String, List<String>> getAllUserIdListByProjectIdListAndRoleIdList(List<String> projectIdList, List<String> roleIdList) throws Exception {
        LambdaQueryWrapperX<ProjectRoleUser> newProjectRoleUserOrionWrapper = new LambdaQueryWrapperX<>();
        newProjectRoleUserOrionWrapper.distinct();
        newProjectRoleUserOrionWrapper.select(ProjectRoleUser::getUserId);
        newProjectRoleUserOrionWrapper.in(ProjectRoleUser::getProjectId, projectIdList);
        newProjectRoleUserOrionWrapper.in(ProjectRoleUser::getProjectRoleId, roleIdList);
        newProjectRoleUserOrionWrapper.select(ProjectRoleUser::getUserId, ProjectRoleUser::getProjectId);
        List<ProjectRoleUser> list = this.list(newProjectRoleUserOrionWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectId, Collectors.mapping(ProjectRoleUser::getUserId, Collectors.toList())));
    }

    @Override
    public List<String> getUserIdToProjectIdsAndRoleIds(RoleUserParamDTO roleUserParamDTO) {
        List<String> roleIdList = roleUserParamDTO.getRoleIdList();
        List<String> projectIdList = roleUserParamDTO.getProjectIdList();
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleUserLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        projectRoleUserLambdaQueryWrapper.rightJoin(ProjectRole.class, ProjectRole::getId, ProjectRoleUser::getProjectRoleId);
        projectRoleUserLambdaQueryWrapper.in(ProjectRole::getBusinessId, roleIdList);
        projectRoleUserLambdaQueryWrapper.in(ProjectRole::getProjectId, projectIdList);
//        projectRoleUserLambdaQueryWrapper.eq(ProjectRoleUser::getUserId,Objects::isNull);
        projectRoleUserLambdaQueryWrapper.select(ProjectRoleUser::getUserId);
        List<ProjectRoleUser> list = this.list(projectRoleUserLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<ProjectRoleUserVO> getUserByProjectIds(List<String> projectIds) {
        List<ProjectRole> projectRoleDTOList = projectRoleService.list(new LambdaQueryWrapper<>(ProjectRole.class)
                .eq(ProjectRole::getCode, "pm").in(ProjectRole::getProjectId, projectIds));
        if (CollectionUtil.isEmpty(projectRoleDTOList)) {
            return new ArrayList<>();
        }
        List<String> ids = projectRoleDTOList.stream().map(ProjectRole::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleUserLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        projectRoleUserLambdaQueryWrapper.in(ProjectRoleUser::getProjectId, projectIds);
        projectRoleUserLambdaQueryWrapper.in(ProjectRoleUser::getProjectRoleId, ids);
        List<ProjectRoleUser> projectRoleUsers = this.list(projectRoleUserLambdaQueryWrapper);
        List<ProjectRoleUserVO> list = BeanCopyUtils.convertListTo(projectRoleUsers, ProjectRoleUserVO::new);
        List<ProjectRoleUserVO> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            result = list.stream()
                    .collect(Collectors.toMap(ProjectRoleUserVO::getUserId, u -> u, (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public Map<String, List<String>> getProjectIdToListByProjectRoleIdList(List<String> projectRoleIdList) {
        if(CollectionUtils.isEmpty(projectRoleIdList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<ProjectRoleUser> projectRoleUserLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        projectRoleUserLambdaQueryWrapper.in(ProjectRoleUser::getProjectRoleId, projectRoleIdList);
        projectRoleUserLambdaQueryWrapper.select(ProjectRoleUser::getProjectRoleId,ProjectRoleUser::getProjectId,ProjectRoleUser::getUserId);
        List<ProjectRoleUser> projectRoleUsers = this.list(projectRoleUserLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(projectRoleUsers)){
            return  new HashMap<>();
        }
        return projectRoleUsers.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectRoleId,Collectors.mapping(ProjectRoleUser::getUserId,Collectors.toList())));
    }
}
