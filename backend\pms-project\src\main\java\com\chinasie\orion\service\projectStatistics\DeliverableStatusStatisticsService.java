package com.chinasie.orion.service.projectStatistics;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.DeliverableStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.DeliverableStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.DeliverableStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * DeliverableStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:57:00
 */
public interface DeliverableStatusStatisticsService  extends OrionBaseService<DeliverableStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    DeliverableStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param deliverableStatusStatisticsDTO
     */
    DeliverableStatusStatisticsVO create(DeliverableStatusStatisticsDTO deliverableStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param deliverableStatusStatisticsDTO
     */
    Boolean edit(DeliverableStatusStatisticsDTO deliverableStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<DeliverableStatusStatisticsVO> pages(Page<DeliverableStatusStatisticsDTO> pageRequest) throws Exception;

}

