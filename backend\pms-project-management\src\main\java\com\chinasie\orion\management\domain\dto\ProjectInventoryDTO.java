package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectInventory DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
@ApiModel(value = "ProjectInventoryDTO对象", description = "商品清单")
@Data
@ExcelIgnoreUnannotated
public class ProjectInventoryDTO extends  ObjectDTO   implements Serializable{

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 0)
    private String orderNumber;

    /**
     * 商品图
     */
    @ApiModelProperty(value = "商品图")
    @ExcelProperty(value = "商品图 ", index = 1)
    private String inventoryImg;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    @ExcelProperty(value = "商品名 ", index = 2)
    private String inventoryName;

    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    @ExcelProperty(value = "单品名称 ", index = 3)
    private String itemName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 4)
    private BigDecimal univalence;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量 ", index = 5)
    private BigDecimal quantity;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @ExcelProperty(value = "金额 ", index = 6)
    private BigDecimal amount;

    /**
     * 采购备注
     */
    @ApiModelProperty(value = "采购备注")
    @ExcelProperty(value = "采购备注 ", index = 7)
    private String notes;

    /**
     * PR信息
     */
    @ApiModelProperty(value = "PR信息")
    @ExcelProperty(value = "PR信息 ", index = 8)
    private String prMessage;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    @ExcelProperty(value = "增值税率 ", index = 8)
    private double rate;
    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    @ExcelProperty(value = "PR行项目 ", index = 9)
    private String planLineId;


    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    @ExcelProperty(value = "单品编码 ", index = 10)
    private String skuCode;


    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    @ExcelProperty(value = "商品类型 ", index = 11)
    private String orderProperty;




    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 9)
    private BigDecimal nakedprice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @ExcelProperty(value = "计量单位 ", index = 10)
    private String unit;

}
