import type { Router, RouteRecordRaw } from 'vue-router';
import { Modal } from 'ant-design-vue';

import { usePermissionStoreWidthOut } from '/@/store/modules/permission';

import { PageEnum } from '/@/enums/pageEnum';
import { useUserStore, useUserStoreWidthOut } from '/@/store/modules/user';

import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { useRolePermissionsStore } from '/@/store/modules/rolePermissions';
import { getAuthCache, lockLogoutTask } from '/@/utils/auth';
import { usePanshiUrl } from '/@/store/modules/panshiUrl';
import { USER_ORGANIZATION_KEY } from '/@/enums/cacheEnum';
import { selectOrganization } from '/@/components/SwitchOrganization';
import { guardQueue } from '/@/router';

const LOGIN_PATH = PageEnum.BASE_LOGIN;
const PAGE_404 = PageEnum.PAGE_404;
const LOGIN_ADMIN = PageEnum.LOGIN_ADMIN;

const whitePathList: PageEnum[] = [
  LOGIN_PATH,
  PAGE_404,
  LOGIN_ADMIN,
];

export function createPermissionGuard(router: Router) {
  const permissionStore = usePermissionStoreWidthOut();
  const beforeGuard = router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore();
    // Jump to the 404 page after processing the login
    if (from.path === LOGIN_PATH && to.name === PAGE_NOT_FOUND_ROUTE.name) {
      next(PageEnum.BASE_HOME);
      return;
    }

    // Whitelist can be directly entered
    if (whitePathList.includes(to.path as PageEnum)) {
      next();
      return;
    }

    // 通过地址栏获取token
    if (to?.query?.token) {
      const { token } = to.query;
      userStore.setToken(token as string);
      await userStore.getUserInfoAction();
      delete to.query.token;

      next({
        // @ts-ignore
        path: to.path,
        redirect: to.path,
        // replace: true,
        query: {
          ...to.query,
        },
      });
      return;
    }

    // 获取管理权限
    const rolePermissionsStore = useRolePermissionsStore();
    // await rolePermissionsStore.loadRolePermissions();

    // 获取磐石地址
    await usePanshiUrl().getLoginUrl();

    const token = userStore.getToken;

    // token does not exist
    if (!token) {
      // redirect login page
      if (usePanshiUrl().getPanshiUrl === '/#/login') {
        const redirectData: { path: string; replace: boolean; query?: Recordable<string> } = {
          path: LOGIN_PATH,
          replace: true,
        };
        if (to.path) {
          redirectData.query = {
            ...redirectData.query,
          };
        }
        next(redirectData);
        return;
      }
      // 跳转到登录
      useUserStore().goLogin();
      return false;
    }

    // 启动系统设定退出时间任务
    // if (import.meta.env?.VITE_GLOB_IS_DEV !== 'true') {
    //   await lockLogoutTask();
    // }

    if (permissionStore.getIsDynamicAddedRoute) {
      next();
      return;
    }

    // 选择组织
    // const { orgId } = userStore.getOrgIdPId;
    // if (!orgId) {
    //   await selectOrganization();
    // }

    const routes = await permissionStore.buildRoutesAction();

    routes.forEach((route) => {
      router.addRoute(route as unknown as RouteRecordRaw);
    });

    const redirectPath = (from.query.redirect || to.path) as string;
    const redirect = decodeURIComponent(redirectPath);
    const nextData = to.path === redirect ? {
      ...to,
      replace: true,
    } : { path: redirect };
    permissionStore.setDynamicAddedRoute(true);
    next(nextData);
  });

  guardQueue.push(beforeGuard);
}
