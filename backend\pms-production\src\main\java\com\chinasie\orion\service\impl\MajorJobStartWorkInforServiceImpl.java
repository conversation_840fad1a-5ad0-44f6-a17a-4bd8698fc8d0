package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.MajorJobStartWorkInfor;
import com.chinasie.orion.domain.dto.MajorJobStartWorkInforDTO;
import com.chinasie.orion.domain.vo.MajorJobStartWorkInforVO;
import com.chinasie.orion.service.MajorJobStartWorkInforService;
import com.chinasie.orion.repository.MajorJobStartWorkInforMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;


import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorJobStartWorkInfor 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 09:46:56
 */
@Service
@Slf4j
public class MajorJobStartWorkInforServiceImpl extends  OrionBaseServiceImpl<MajorJobStartWorkInforMapper, MajorJobStartWorkInfor>   implements MajorJobStartWorkInforService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MajorJobStartWorkInforVO detail(String id,String pageCode) throws Exception {
        MajorJobStartWorkInfor majorJobStartWorkInfor =this.getById(id);
        MajorJobStartWorkInforVO result = BeanCopyUtils.convertTo(majorJobStartWorkInfor,MajorJobStartWorkInforVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param majorJobStartWorkInforDTO
     */
    @Override
    public  String create(MajorJobStartWorkInforDTO majorJobStartWorkInforDTO) throws Exception {
        MajorJobStartWorkInfor majorJobStartWorkInfor =BeanCopyUtils.convertTo(majorJobStartWorkInforDTO,MajorJobStartWorkInfor::new);
        this.save(majorJobStartWorkInfor);

        String rsp=majorJobStartWorkInfor.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorJobStartWorkInforDTO
     */
    @Override
    public Boolean edit(MajorJobStartWorkInforDTO majorJobStartWorkInforDTO) throws Exception {
        MajorJobStartWorkInfor majorJobStartWorkInfor =BeanCopyUtils.convertTo(majorJobStartWorkInforDTO,MajorJobStartWorkInfor::new);

        this.updateById(majorJobStartWorkInfor);

        String rsp=majorJobStartWorkInfor.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorJobStartWorkInforVO> pages( Page<MajorJobStartWorkInforDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorJobStartWorkInfor> condition = new LambdaQueryWrapperX<>( MajorJobStartWorkInfor. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorJobStartWorkInfor::getCreateTime);


        Page<MajorJobStartWorkInfor> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorJobStartWorkInfor::new));

        PageResult<MajorJobStartWorkInfor> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorJobStartWorkInforVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorJobStartWorkInforVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorJobStartWorkInforVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<MajorJobStartWorkInforVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

}
