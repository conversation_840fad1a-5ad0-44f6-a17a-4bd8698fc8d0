<template>
  <Layout2 class="boxs">
    <template #left>
      <LeftTree
        :reloadAll="reloadAll"
        @select-change="selectChange"
      />
    </template>

    <Layout2
      v-if="isPower('LXSZ_container_04',powerData)"
      v-model:tabsIndex="tabsIndex"
      :tabs="tabsOption"
      @tabs-change="contentTabsChange2"
    >
      <BasicInfo
        v-if="tabsId===66661 &&isPower('LXSZ_container_04_01',powerData)"
        :selectChangeData="selectChangeData"
      />
      <TypeAttribute
        v-if="tabsId===66662 &&isPower('LXSZ_container_04_02',powerData)"
        :selectChangeData="selectChangeData"
      />
      <ApprovalProcess
        v-if="tabsId===66663 &&isPower('LXSZ_container_04_03',powerData)"
        :selectChangeData="selectChangeData"
      />
      <TypeRule
        v-if="tabsId===66664"
        :selectChangeData="selectChangeData"
      />
      <RelatedObjects
        v-if="tabsId==66668"
        style="margin-top: 10px"
        :form-id="selectChangeData.id"
        pageName="RiskManagement"
      />
    </Layout2>

    <div
      v-else
      class="table-content"
    >
      <Empty

        :type="403"
      />
    </div>
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, inject, provide, reactive, toRefs,
} from 'vue';
import { isPower, Layout2 } from 'lyra-component-vue3';
// import BasicInfo from './basicInfo/index.vue';
import { Empty } from 'ant-design-vue';
import BasicInfo from './riskMenu/basicInfo/index.vue';
import TypeAttribute from './riskMenu/typeAttribute/index.vue';
import TypeRule from './riskMenu/typeRule/index.vue';
import ApprovalProcess from './riskMenu/approvalProcess/index.vue';
import LeftTree from './model/LeftTree.vue';
import RelatedObjects from './RelatedObjectsType.vue';
// import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({

  name: 'Index',
  components: {
    Empty,
    Layout2,
    BasicInfo,
    TypeAttribute,
    ApprovalProcess,
    TypeRule,
    LeftTree,
    RelatedObjects,
  },
  props: {},
  emits: [],
  setup() {
    const powerData: any = inject('powerData', {});
    const state = reactive({
      tabsOption: [
        {
          name: '基本信息',
          id: 66661,
        },
        {
          name: '类型属性',
          id: 66662,
        },
        // { name: '审批流程', id: 66663 },
        // { name: '相关对象', id: 66668 },
        // { name: '类型规则', id: 66664 },
      ],
      tabsIndex: 0,
      tabsId: 66661,
      selectChangeData: {},
      reloadAll: new Date(),
    });
    const contentTabsChange2 = (index, item) => {
      state.tabsIndex = index;
      state.tabsId = item.id;
    };

    function selectChange(selectChangeData) {
      state.selectChangeData = selectChangeData;
    }

    function reflashAll() {
      state.reloadAll = new Date();
    }

    provide('reloadAll', reflashAll);
    return {
      ...toRefs(state),
      contentTabsChange2,
      selectChange,
      powerData,
      isPower,
    };
  },
});
</script>

<style lang="less" scoped>
.table-content {
  .ant-empty {
    top: 50%;
    position: absolute;
    width: 100%;
  }
}
</style>
