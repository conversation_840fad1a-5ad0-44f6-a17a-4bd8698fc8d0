<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import { defineExpose } from 'vue';
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';

const schemas: FormSchema[] = [
  {
    field: '汇报时间',
    component: 'DatePicker',
    label: '汇报时间',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      picker: 'week',
    },
  },
  {
    field: '整体进度',
    component: 'ApiSelect',
    label: '整体进度',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: '提交人',
    component: 'ApiSelect',
    label: '提交人',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: '抄送人',
    component: 'ApiSelect',
    label: '抄送人',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },

];
const [registerForm, formMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});
defineExpose({
  formMethods,
});
</script>

<style scoped lang="less">

</style>
