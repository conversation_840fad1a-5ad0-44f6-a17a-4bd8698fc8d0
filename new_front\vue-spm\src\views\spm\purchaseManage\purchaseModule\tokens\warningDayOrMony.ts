import { h } from 'vue';
import { Tag } from 'ant-design-vue';

const waringMoneyMap = new Map();
waringMoneyMap.set('10', '正常');
waringMoneyMap.set('100', '余额=20%');
waringMoneyMap.set('101', '余额<20%');
waringMoneyMap.set('102', '余额=15%');
waringMoneyMap.set('103', '余额<15%');
waringMoneyMap.set('104', '余额=15%');
waringMoneyMap.set('105', '余额<10%');
waringMoneyMap.set('106', '余额=10%');
waringMoneyMap.set('107', '余额<5%');
waringMoneyMap.set('108', '余额=0%');
waringMoneyMap.set('109', '余额<0%');
export const useWaringMoney = () => {
  const options = [];
  waringMoneyMap.forEach((val, key) => {
    options.push({
      label: val,
      name: val,
      value: key,
    });
  });

  return options;
};
export const useWaringMoneyByProp = (prop) => {
  const content = (waringMoneyMap.has(`${prop}`) ? waringMoneyMap.get(`${prop}`) : prop);
  return [
    '103',
    '105',
    '107',
    '109',
    '108',
  ].includes(`${prop}`)
    ? h(Tag, {
      color: 'red',
    }, content)
    : content;
};

const waringDayMap = new Map();
waringDayMap.set('10', '正常');
waringDayMap.set('100', '剩余半年');
waringDayMap.set('101', '临期半年');
waringDayMap.set('102', '剩余三个月');
waringDayMap.set('103', '临期三个月');
waringDayMap.set('104', '剩余两个月');
waringDayMap.set('105', '临期两个月');
waringDayMap.set('106', '剩余一个月');
waringDayMap.set('107', '临期一个月');
waringDayMap.set('108', '剩余15天');
waringDayMap.set('109', '临期15天');
waringDayMap.set('110', '已到期');
export const useWaringDay = () => {
  const options = [];
  waringDayMap.forEach((val, key) => {
    options.push({
      label: val,
      name: val,
      value: key,
    });
  });

  return options;
};
export const useWaringDayByProp = (prop) => {
  const content = waringDayMap.has(`${prop}`) ? waringDayMap.get(`${prop}`) : prop;
  return [
    '103',
    '107',
    '109',
    '108',
    '110',
  ].includes(`${prop}`)
    ? h(Tag, {
      color: 'red',
    }, content)
    : content;
};
