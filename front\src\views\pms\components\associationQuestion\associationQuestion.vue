<template>
  <div class="plan-tab">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        v-if="['risk','question'].includes(props.pageType)"
        #toolbarLeft
      >
        <div class="button-margin-right">
          <BasicButton
            v-if="isPower(powerCode?.addCode, powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="addTableNode"
          >
            转为问题
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.quoteCode, powerData)"
            icon="sie-icon-tianjiaxinzeng"
            @click="quoteRisk"
          >
            关联问题
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.removeCode, powerData)"
            icon="sie-icon-shanchu"
            :disabled="disabledDelete"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicButton, OrionTable, DataStatusTag, openDrawer, openModal, isPower,
} from 'lyra-component-vue3';
import {
  inject, ref, h, Ref, computed,
} from 'vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import AddTableModal from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/components/AddTableModal.vue';
import dayjs from 'dayjs';
import AssociationQuestionModal from './components/AssociationQuestionModal.vue';

const props = withDefaults(defineProps<{
    pageType:string,
    getQuestionTableDataApi:any,
    deleteQuestionBatchApi:any,
    addQuestionTableApi:any
    powerCode?:object
}>(), {
  pageType: 'risk',
  getQuestionTableDataApi: null,
  deleteQuestionBatchApi: null,
  powerCode: () => ({}),
  addQuestionTableApi: null,
});
const tableRef = ref();
const formData:Record<any, any> = inject('formData', {});
const powerData = inject('powerData', {});
const router = useRouter();
const selectedRowKeys:Ref<string[]> = ref([]);
const selectedRows:Ref<any[]> = ref([]);
const disabledDelete = computed(() => {
  if (selectedRows.value.length === 0) return true;
  return !selectedRows.value.every((item) => !item.isCurrentCreate);
});

function selectionChange(data) {
  selectedRowKeys.value = data.keys;
  selectedRows.value = data.rows;
}
function addTableNode() {
  openFormDrawer(AddTableModal, {
    projectId: formData?.value?.projectId,
    fromObjName: formData?.value?.projectName,
    modelName: 'pms',
  }, updateData);
}
function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void, type:string = 'modal'): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑问题' : '新增问题',
    width: type === 'modal' ? 1000 : 1200,
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
        formType: record?.id ? 'edit' : 'add',
        drawerData: record,
        addQuestionTableApi: props.addQuestionTableApi,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

function quoteRisk() {
  const quoteRiskPoolRef = ref();
  openModal({
    title: '关联问题',
    width: 1100,
    height: 700,
    content(h) {
      return h(AssociationQuestionModal, {
        ref: quoteRiskPoolRef,
        showLeftTree: true,
        questionId: formData.value.id,
      });
    },
    async onOk() {
      await quoteRiskPoolRef.value.saveData();
      updateData();
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '移除提示',
    content: type === 'batch' ? '是否移除选中的数据？' : '是否移除该条数据？',
    async onOk() {
      await props.deleteQuestionBatchApi(params);
      message.success('移除成功');
      updateData();
    },
  });
}
function updateData() {
  tableRef.value.reload();
}
const tableOptions = {
  showIndexColumn: false,
  bordered: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  api(params) {
    if (!formData.value.id) {
      return Promise.resolve([]);
    }
    return props.getQuestionTableDataApi(params);
  },
  columns: initColumns(),
  actions: [
    {

      isShow: (record) => record.isCurrentCreate && isPower(props.powerCode?.editCode, powerData),
      text: '编辑',
      onClick(record) {
        openFormDrawer(AddTableModal, record, updateData);
      },
    },
    {
      text: '激活',
      isShow: (record) => record.isCurrentCreate && record.status === 101 && isPower(props.powerCode?.activateCode, powerData),
      onClick(record) {
        Modal.confirm({
          title: '激活提示',
          content: '是否激活当前的数据',
          onOk() {
            new Api('/pms').fetch([record.id], 'question-management/open', 'PUT').then((res) => {
              message.success('激活成功。');
              updateData();
            });
          },
        });
      },
    },
    {
      text: '关闭',
      isShow: (record) => record.isCurrentCreate && record.status === 101 && isPower(props.powerCode?.closeCode, powerData),
      onClick(record) {
        Modal.confirm({
          title: '关闭提示',
          content: '是否关闭当前的数据',
          onOk() {
            new Api('/pms').fetch([record.id], 'question-management/close', 'PUT').then((res) => {
              message.success('关闭成功。');
              updateData();
            });
          },
        });
      },
    },
    {
      text: '删除',
      isShow: (record) => record.isCurrentCreate && isPower(props.powerCode?.deleteCode, powerData),
      onClick(record) {
        Modal.confirm({
          title: '删除提示',
          content: '是否删除当前的数据',
          onOk() {
            new Api('/pms').fetch([record.id], 'question-management/removeBatch', 'DELETE').then((res) => {
              message.success('删除成功。');
              updateData();
            });
          },
        });
      },
    },
    {
      text: '移除',
      isShow: (record) => !record.isCurrentCreate && isPower(props.powerCode?.removeCode, powerData),
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
  ],
};
function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      key: 'number',

      width: '120px',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      customRender({ record, text }) {
        return isPower(props.powerCode?.checkCode, powerData) ? h(
          'span',
          {
            class: 'action-btn',
            // class: computed(()=>isPower('FX_container_button_08', state.powerData)) ?'action-btn':'',
            title: text,
            onClick(e) {
              router.push({
                name: 'PMSQuestionManagementDetails',
                params: {
                  id: record.id,
                },
              });
            },
          },
          text,
        ) : text;
      },

      align: 'left',
      // slots: { customRender: 'name' },
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '问题内容',
      dataIndex: 'content',
      key: 'content',

      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      key: 'dataStatus',
      width: '80px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    // {
    //   title: '问题来源',
    //   dataIndex: 'questionSourceName',
    //   key: 'questionSourceName',
    //   align: 'left',
    //   // sorter: true,
    //   ellipsis: true,
    // },
    {
      title: '问题类型',
      dataIndex: 'questionTypeName',
      key: 'questionTypeName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '问题负责人',
      dataIndex: 'principalName',
      key: 'principalName',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'principalName' },
    },
    {
      title: '采纳情况',
      dataIndex: 'adoptionSituationName',
      width: 200,
    },
    {
      title: '期望完成日期',
      dataIndex: 'predictEndTime',
      key: 'predictEndTime',
      align: 'left',
      width: '180px',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      ellipsis: true,
    },

    {
      title: '提出人',
      dataIndex: 'exhibitorName',
      key: 'exhibitorName',
      width: '70px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '提出日期',
      dataIndex: 'proposedTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
      width: '180px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'proposedTime' },
    },
  ];
  if (['risk', 'question'].includes(props.pageType)) {
    columns.push({
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    });
  }
  return columns;
}

</script>