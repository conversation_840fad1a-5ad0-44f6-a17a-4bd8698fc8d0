package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractCenter DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:31:28
 */
@ApiModel(value = "ContractCenterDTO对象", description = "用人中心")
@Data
@ExcelIgnoreUnannotated
public class ContractCenterDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    @ExcelProperty(value = "用人单位代号 ", index = 1)
    private String centerCode;

    /**
     * 用人中心名称
     */
    @ApiModelProperty(value = "用人中心名称")
    @ExcelProperty(value = "用人中心名称 ", index = 2)
    private String centerName;




}
