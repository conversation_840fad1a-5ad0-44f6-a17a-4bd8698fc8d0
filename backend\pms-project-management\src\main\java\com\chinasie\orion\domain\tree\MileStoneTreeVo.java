package com.chinasie.orion.domain.tree;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import com.chinasie.orion.domain.dto.ContractMilestoneTreeDTO;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class MileStoneTreeVo extends ObjectEntity implements Serializable {

    @ApiModelProperty(value = "自定义查询参数")
    ContractMilestoneTreeDTO query;

    @ApiModelProperty(value = "高级搜索条件")
    List<List<SearchCondition>> searchConditions;

    /**
     * 是否是统计字段
     */
    @StatisticField("isStatistics")
    @ApiModelProperty(value = "是否是统计字段")
    private Boolean isStatistics = false;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 合同约定验收金额
     */
    @ApiModelProperty(value = "合同约定验收金额")
    private BigDecimal milestoneAmt = BigDecimal.ZERO;
    /**
     * 初始预估验收金额
     */
    @ApiModelProperty(value = "初始预估验收金额")
    private BigDecimal exceptAcceptanceAmt = BigDecimal.ZERO;

    /**
     * 金额类型
     */
    @ApiModelProperty(value = "金额类型")
    private String ammountType;

    /**
     * 合同约定验收日期
     */
    @ApiModelProperty(value = "合同约定验收日期")
    private Date planAcceptDate;

    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    private Date expectAcceptDate;

    /**
     * 日期类型
     */
    @ApiModelProperty(value = "日期类型")
    private String dateType;

    /**
     * 收入类型
     */
    @ApiModelProperty(value = "收入类型")
    private String mileIncomeType;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String busRspUser;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String busRspUserName;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private String costBusType;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名")
    private String cusPersonName;


    @ApiModelProperty(value = "所级部门，pmi_dept id")
    private String officeDept;


    @ApiModelProperty(value = "所级部门名称")
    private String officeDeptName;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    private String undertDeptName;


    /**
     * 是否暂估
     */
    @ApiModelProperty(value = "是否暂估0：否 1：是")
    private Integer isProvisionalEstimate;

    /**
     * 里程碑已暂估金额
     */
    @StatisticField(value = "milestoneProvisionalEstimateAmt", type = StatisticType.SUM)
    @ApiModelProperty(value = "里程碑已暂估金额")
    private BigDecimal milestoneProvisionalEstimateAmt = BigDecimal.ZERO;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String businessIncomeTypeName;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 统计是否是子里程碑
     */
    @StatisticField(value = "childernCount", type = StatisticType.SUM)
    @ApiModelProperty(value = "统计是否是里程碑 0-子里程碑 1-里程碑")
    private Integer childernCount = 0;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 综合税率
     */
    @ApiModelProperty(value = "综合税率")
    private String rate;

    /**
     * 税率Id
     */
    @ApiModelProperty(value = "税率Id")
    private String rateId;

    /**
     * 税率Id
     */
    @ApiModelProperty(value = "税率值")
    private String rateName;


    /**
     * 金额类型-id
     */
    @ApiModelProperty(value = "金额类型-id ")
    private String moneyTypeId;

    /**
     * 金额类型名称
     */
    @ApiModelProperty(value = "金额类型名称")
    private String moneyTypeName;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amt;

    /**
     * 验收日期
     */
    @ApiModelProperty(value = "验收日期")
    private Date checkDate;
    /**
     * 日期类型-id
     */
    @ApiModelProperty(value = "日期类型-id ")
    private String dateTypeId;

    /**
     * 日期类型名称
     */
    @ApiModelProperty(value = "日期类型名称")
    private String dateTypeName;

    /**
     * 收入类型-id
     */
    @ApiModelProperty(value = "收入类型-id")
    private String incomeTypeId;

    /**
     * 日期类型名称
     */
    @ApiModelProperty(value = "收入类型名称")
    private String incomeTypeName;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String businessIncomeType;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


    /**
     * 成本业务分类名称
     */
    @ApiModelProperty(value = "成本业务分类名称")
    private String costBusTypeName;


    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @TableField(value = "actual_accept_date")
    private Date actualAcceptDate;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    @TableField(value = "actual_milestone_amt")
    private BigDecimal actualMilestoneAmt;


    /**
     * 累计验收比例
     */
    @ApiModelProperty(value = "累计验收比例")
    @TableField(value = "total_accept_rate")
    private BigDecimal totalAcceptRate;


    /**
     * 是否创建计划数据 0：否 1：是
     */
    @ApiModelProperty(value = "是否创建计划数据 0：否 1：是")
    @TableField(value = "is_plan")
    private Integer isPlan;
    /**
     * 主合同id
     */
    @TableField(exist = false)
    private String frameContractId;


    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    private String departmental;

    /**
     * 技术接口人--所级名称
     */
    @ApiModelProperty(value = "技术接口人--所级名称")
    private String departmentalName;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeType;


    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    private BigDecimal milestoneAdvanceAmt;

    /**
     * 开票主体名称
     */
    @ApiModelProperty(value = "开票主体名称")
    private BigDecimal signedMainName;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    private Date exceptInvoiceDate;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal amtTax;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;


    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanCode;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectCode;


    /**
     * wbs编号
     */
    @ApiModelProperty(value = "wbs编号")
    private String wbsCode;


    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    private String voucherNum;

    /**
     * 过帐日期
     */
    @ApiModelProperty(value = "过帐日期")
    private Date passAccountDate;

    /**
     * 确认收入金额-暂估收入
     */
    @ApiModelProperty(value = "确认收入金额-暂估收入")
    private BigDecimal confirmIncomeProvisionalEstimate;

    /**
     * 确认收入金额-开票收入
     */
    @ApiModelProperty(value = "确认收入金额-开票收入")
    private BigDecimal confirmIncomeInvoicing;

    /**
     * 确认收入金额-合计值
     */
    @ApiModelProperty(value = "确认收入金额-合计值")
    private BigDecimal confirmIncomeSum;

    /**
     * 是否被跟踪确认
     */
    @ApiModelProperty(value = "是否被跟踪确认")
    private String isTrackConfirm;
    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型 1-同级 2-子级")
    private Integer nodeType;
    /**
     * 节点级别
     */
    @ApiModelProperty(value = "节点级别")
    private Integer level;

    /**
     * 合同约定验收金额
     */
    @StatisticField(value = "milestoneAmtTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "合同约定验收金额 -- 统计用")
    private BigDecimal milestoneAmtTJ = BigDecimal.ZERO;

    /**
     * 初始预估验收金额
     */
    @StatisticField(value = "exceptAcceptanceAmtTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "初始预估验收金额 -- 统计用")
    private BigDecimal exceptAcceptanceAmtTJ = BigDecimal.ZERO;


    /**
     * 实际验收金额(不含税)
     */
    @StatisticField(value = "exceptAcceptanceAmtNoRateTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "实际验收金额(不含税) -- 统计用")
    private BigDecimal exceptAcceptanceAmtNoRateTJ = BigDecimal.ZERO;

    /**
     * 已确认开票收入
     */
    @StatisticField(value = "confirmIncomeInvoicingTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "已确认开票收入 -- 统计用")
    private BigDecimal confirmIncomeInvoicingTJ = BigDecimal.ZERO;

    /**
     * 已确认暂估收入
     */
    @StatisticField(value = "confirmIncomeProvisionalEstimateTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "已确认暂估收入 -- 统计用")
    private BigDecimal confirmIncomeProvisionalEstimateTJ = BigDecimal.ZERO;

    /**
     * 已预收款开票金额
     */
    @StatisticField(value = "milestoneAdvanceAmtTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "已预收款开票金额 -- 统计用")
    private BigDecimal milestoneAdvanceAmtTJ = BigDecimal.ZERO;

    /**
     * 剩余未确认收入
     */
    @StatisticField(value = "remaineUnconfiremIncomeAmtTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "剩余未确认收入 -- 统计用")
    private BigDecimal remaineUnconfiremIncomeAmtTJ = BigDecimal.ZERO;

    /**
     * 确认收入金额-合计值
     */
    @StatisticField(value = "confirmIncomeSumTJ", type = StatisticType.SUM)
    @ApiModelProperty(value = "确认收入金额-合计值 -- 统计用")
    private BigDecimal confirmIncomeSumTJ = BigDecimal.ZERO;
    /**
     * 业务收入类型-id
     */
    @ApiModelProperty(value = "业务收入类型-id")
    private String businessIncomeTypeId;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    @ApiModelProperty(value = "确认分配状态 1:待分配,2:已分配")
    private String confirmAllocationStatus;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "立项时间")
    private Date initiationTime;

}
