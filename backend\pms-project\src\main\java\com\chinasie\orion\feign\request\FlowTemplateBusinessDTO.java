package com.chinasie.orion.feign.request;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * FlowTemplateBusiness Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-26 17:01:52
 */
@ApiModel(value = "FlowTemplateBusinessDTO对象", description = "流程模版与业务关联表")
@Data
public class FlowTemplateBusinessDTO extends ObjectDTO implements Serializable {

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    /**
     * 流程模版id
     */
    @ApiModelProperty(value = "流程模版id")
    @NotBlank(message = "流程模版id不能为空")
    private String templateId;

    /**
     * 业务Id
     */
    @ApiModelProperty(value = "业务Id")
    @NotBlank(message = "业务Id不能为空")
    private String businessId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String processStatus;


    /**
     * 发起人Id
     */
    @ApiModelProperty(value = "发起人Id")
    private String applyUserId;

    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    private String applyUserName;

    /**
     * 数据类型编号
     */
    @ApiModelProperty(value = "数据类型编号")
    private String dataTypeCode;


    /**
     * 数据类型名称
     */
    @ApiModelProperty(value = "数据类型名称")
    private String dataTypeName;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
   // @NotBlank(message = "业务名称不能为空")
    private String businessName;

    /**
     * 消息url
     */
    @ApiModelProperty(value = "消息url")
    private String messageUrl;

}
