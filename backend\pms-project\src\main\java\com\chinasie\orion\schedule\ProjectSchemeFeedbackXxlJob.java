package com.chinasie.orion.schedule;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.conts.ProjectSchemeNodeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeFeedback;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ProjectSchemeFeedbackService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lsy
 * @date: 2024/4/19
 * @description:
 */
@Slf4j
@Component
public class ProjectSchemeFeedbackXxlJob {

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private ProjectSchemeFeedbackService projectSchemeFeedbackService;

    @Autowired
    private PmsMQProducer mqProducer;

    @XxlJob("projectSchemeFeedback")
    public void projectSchemeFeedbackXxl() {
        Date now = DateUtil.parse(DateUtil.formatDateTime(new Date()), DatePattern.NORM_DATETIME_MINUTE_FORMAT);
        List<ProjectScheme> list = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                .eq(ProjectScheme::getStatus, Status.PUBLISHED.getCode()));
        Map<String, List<ProjectSchemeFeedback>> feedbackMap = projectSchemeFeedbackService.list(new LambdaQueryWrapperX<>(ProjectSchemeFeedback.class)
                .eq(ProjectSchemeFeedback::getIsExecute, false))
                .stream().collect(Collectors.groupingBy(ProjectSchemeFeedback::getProjectSchemeId));
        for (ProjectScheme projectScheme : list) {
            try {
                if (StrUtil.isNotBlank(projectScheme.getRspUser())
                        && StrUtil.isNotBlank(projectScheme.getIssueRemindIntervalUnit())
                        && projectScheme.getIssueRemindInterval() != null
                        && projectScheme.getIssueRemindInterval() != 0) {
                    if (feedbackMap.containsKey(projectScheme.getId())) {
                        ProjectSchemeFeedback projectSchemeFeedback = feedbackMap.get(projectScheme.getId())
                                .stream().max(Comparator.comparing(ProjectSchemeFeedback::getNextExecuteTime)).get();
                        if (projectSchemeFeedback.getNextExecuteTime().getTime() == now.getTime()) {
                            sendMscAndSaveNewProjectSchemeFeedback(now, projectScheme);
                            projectSchemeFeedback.setIsExecute(true);
                            projectSchemeFeedbackService.updateById(projectSchemeFeedback);
                        } else if (projectSchemeFeedback.getNextExecuteTime().getTime() < now.getTime()) {
                            Calendar oldNextExecuteTime = Calendar.getInstance();
                            oldNextExecuteTime.setTime(projectSchemeFeedback.getNextExecuteTime());
                            sendMscAndSaveNewProjectSchemeFeedback(now, projectScheme, oldNextExecuteTime);
                        }
                    } else {
                        Calendar oldNextExecuteTime = Calendar.getInstance();
                        oldNextExecuteTime.setTime(DateUtil.parse(DateUtil.formatDateTime(projectScheme.getIssueTime()), DatePattern.NORM_DATETIME_MINUTE_FORMAT));
                        sendMscAndSaveNewProjectSchemeFeedback(now, projectScheme, oldNextExecuteTime);
                    }
                }
            } catch (Exception e) {
                log.error("项目计划[{}]下发后反馈提醒报错:{}" , projectScheme.getId(), e.getMessage());
            }
        }
    }

    private void sendMscAndSaveNewProjectSchemeFeedback(Date now, ProjectScheme projectScheme, Calendar oldNextExecuteTime) {
        Calendar nextExecuteTime = getNextExecuteTime(oldNextExecuteTime, projectScheme, now);
        ProjectSchemeFeedback newFeedback = new ProjectSchemeFeedback();
        newFeedback.setProjectSchemeId(projectScheme.getId());
        newFeedback.setNextExecuteTime(nextExecuteTime.getTime());
        if (nextExecuteTime.getTimeInMillis() == now.getTime()) {
            sendMscAndSaveNewProjectSchemeFeedback(now, projectScheme);
            newFeedback.setIsExecute(true);
        } else {
            newFeedback.setIsExecute(false);
        }
        projectSchemeFeedbackService.save(newFeedback);
    }

    private void sendMscAndSaveNewProjectSchemeFeedback(Date now, ProjectScheme projectScheme) {
        packageMessage(projectScheme.getId(), projectScheme.getName(), Collections.singletonList(projectScheme.getRspUser()),
                projectScheme.getCreatorId(), projectScheme.getOrgId(), projectScheme.getPlatformId(), projectScheme.getProjectId());
        ProjectSchemeFeedback newFeedback = new ProjectSchemeFeedback();
        newFeedback.setProjectSchemeId(projectScheme.getId());
        newFeedback.setIsExecute(false);
        Calendar nextExecuteTime = Calendar.getInstance();
        nextExecuteTime.setTime(now);
        issueRemindIntervalUnit(projectScheme, nextExecuteTime);
        newFeedback.setNextExecuteTime(nextExecuteTime.getTime());
        projectSchemeFeedbackService.save(newFeedback);
    }

    private void issueRemindIntervalUnit(ProjectScheme projectScheme, Calendar nextExecuteTime) {
        if ("时".equals(projectScheme.getIssueRemindIntervalUnit())) {
            nextExecuteTime.add(Calendar.HOUR_OF_DAY, projectScheme.getIssueRemindInterval());
        } else if ("天".equals(projectScheme.getIssueRemindIntervalUnit())) {
            nextExecuteTime.add(Calendar.DAY_OF_MONTH, projectScheme.getIssueRemindInterval());
        } else if ("周".equals(projectScheme.getIssueRemindIntervalUnit())) {
            nextExecuteTime.add(Calendar.DAY_OF_MONTH, projectScheme.getIssueRemindInterval() * 7);
        } else {
            nextExecuteTime.add(Calendar.MONTH, projectScheme.getIssueRemindInterval());
        }
    }

    private Calendar getNextExecuteTime(Calendar nextExecuteTime, ProjectScheme projectScheme, Date now) {
        issueRemindIntervalUnit(projectScheme, nextExecuteTime);
        if (nextExecuteTime.getTimeInMillis() < now.getTime()) {
            getNextExecuteTime(nextExecuteTime, projectScheme, now);
        }
        return nextExecuteTime;
    }


    private void packageMessage(String businessId,
                                String name,
                                List<String> recipientIdList,
                                String sendId,
                                String orgId,
                                String platformId,
                                String projectId) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(String.format("feedback_%s", businessId))
                .todoStatus(0)
                .businessNodeCode(ProjectSchemeNodeEnum.Node_Project_Scheme_Feedback.getCode())
                .messageUrl(String.format("/pms/menuComponents?id=%stabType=project_plan", projectId))
                .messageUrlName("项目计划列表页")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(recipientIdList)
//                .messageMap(MapUtil.builder(new HashMap<String, Object>())
//                        .put("name", name)
//                        .build())
//                .titleMap(MapUtil.builder(new HashMap<String, Object>())
//                        .put("name", name)
//                        .build())
                .senderTime(new Date())
                .senderId(sendId)
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }
}
