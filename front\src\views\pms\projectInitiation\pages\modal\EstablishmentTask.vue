<template>
  <div class="establishment-task">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :expandIconColumnIndex="3"
      :expandedRowKeys="defaultExpandedRowKeys"
      @expand="expandRows"
    >
      <template #toolbarLeft>
        <div class="toolbar-left">
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_01',powerData)"
            type="primary"
            icon="sie-icon-jihuaxiafa"
            ghost
            @click="quoteDocument"
          >
            引用文档模板库
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_02',powerData)"
            icon="sie-icon-jihuaxiafa"
            type="primary"
            ghost
            :disabled="distributeTaskDisabled"
            @click="distributeTaskClick"
          >
            任务下发
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_03',powerData)"
            icon="sie-icon-jihuafenjie"
            type="primary"
            ghost
            :disabled="setRelationshipDisabled"
            @click="setRelationshipRow"
          >
            设置前后置关系
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_04',powerData)"
            icon="orion-icon-desktop"
            type="primary"
            ghost
            @click="checkView('all')"
          >
            全局预览
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_05',powerData)"
            icon="orion-icon-desktop"
            type="primary"
            :disabled="selectedRows.length!==1"
            ghost
            @click="checkView('one')"
          >
            预览我的
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMLX_container_03_head_button_06',powerData)"
            type="primary"
            icon="delete"
            ghost
            :disabled="selectedRows.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
          <div class="task-status">
            <span style="padding-right: 5px">任务状态：</span>
            <ASelect
              v-model:value="taskStatus"
              style="width: 120px"
              :options="statusOptions"
              :fieldNames="{label:'name',value:'statusValue'}"
              :allowClear="true"
              @change="changeStatus"
            />
          </div>
        </div>
      </template>
      <template #toolbarRight>
        <slot name="slotRight" />
      </template>
    </OrionTable>
  </div>
</template>

<script lang="ts" setup>
import {
  BasicButton, OrionTable, openModal, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, inject, onMounted, provide, Ref, ref,
} from 'vue';
import { useUserStore } from '/@/store/modules/user';
import {
  Select as ASelect,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import {
  initEstablishmentTableData, initActions,
  initColumns, quoteDocumentTemplate,
  distributeTaskRow, relationshipRow, deleteBatchData,
} from '../../index';
import { SelectListTable } from '/@/views/pms/components';
import Api from '/@/api';
import { declarationData } from '/@/views/pms/projectInitiation/pages/keys';
import CheckModalView from '../components/CheckModalView.vue';

const emit = defineEmits(['changeTabs']);
const tableRef = ref();
const powerData = inject('powerData', []);
const detailsData = inject(declarationData);
const taskStatus:Ref<string|number> = ref('');
const userInfo = useUserStore().getUserInfo;
const defaultExpandedRowKeys = ref<string[]>([]); // 展开的key
const indexData: Ref<any[]> = ref([]);// 将树表格更改为列表形式
const statusOptions: Ref<any[]> = ref([]);// 将树表格更改为列表形式
const router = useRouter();
const distributeTaskDisabled:ComputedRef = computed(() => !(selectedRows.value.length === 1 && selectedRows.value[0].status === 101));
const setRelationshipDisabled:ComputedRef = computed(() => selectedRows.value.length !== 1);
// 表格勾选数据
const selectedRows = computed(() => indexData.value.filter((item) => selectedRowKeys.value.filter((item1) => !item1.endsWith('_Top')).includes(item.id)));
const selectedRowKeys = ref([]);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    selectedRowKeys,
    onSelect: onSelectChange,
    onSelectAll,
  },
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: false,
  showTableSetting: false,
  rowKey: 'key',
  rowClassName: (record) =>
    (record.topSort ? 'table-striped' : null),
  api: async (tableParams) => {
    tableParams.approvalId = detailsData.value.id;
    tableParams.power = {
      pageCode: 'PMS1026',
      containerCode: 'PMS_XMLX_container_03_table',
    };
    if (taskStatus.value) {
      tableParams.status = taskStatus.value;
    }
    let result = await new Api('/pms').fetch(tableParams, 'collaborativeCompilationTask/list', 'POST');
    indexData.value = [];
    result = initEstablishmentTableData(result, '', indexData, 1, []);
    defaultExpandedRowKeys.value = result.expandedRowKeys;
    return result.tableData;
  },
  columns: initColumns(indexData, router, userInfo),
  actions: initActions(userInfo, upDateTable, indexData),
});
function deleteBatch() {
  deleteBatchData(selectedRows.value.map((item) => item.id), 'batch', upDateTable);
}
function quoteDocument() {
  quoteDocumentTemplate(SelectListTable, upDateTable, detailsData);
}
function distributeTaskClick() {
  distributeTaskRow({
    taskId: selectedRows.value[0].id,
    approvalId: detailsData.value.id,
  }, upDateTable);
}
function setRelationshipRow() {
  let optionsList = indexData.value.filter((item) => item.id !== selectedRows.value[0].id && [101, 130].includes(item.status));
  relationshipRow({
    optionsList,
    approvalId: detailsData.value.id,
    taskId: selectedRows.value[0].id,
    record: selectedRows.value[0],
  }, upDateTable);
}
function onSelectChange(record, selected) {
  if (selected) {
    if (record.key.endsWith('_Top')) {
      selectedRowKeys.value.push(record.id);
      selectedRowKeys.value.push(record.key);
    } else {
      selectedRowKeys.value.push(record.id);
      if (record.topSort !== 0) {
        selectedRowKeys.value.push(`${record.id}_Top`);
      }
    }
  }
  if (!selected) {
    if (record.topSort !== 0) {
      selectedRowKeys.value = selectedRowKeys.value.filter((item) => ![`${record.id}_Top`, record.id].includes(item));
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter((item) => record.id !== item);
    }
  }
}
function onSelectAll(selected, rows) {
  if (selected) {
    selectedRowKeys.value = rows.filter((item) => item)
      .map((item) => item.key);
  } else {
    selectedRowKeys.value = [];
  }
}
function checkView(type) {
  let leftList = [];
  if (type === 'all') {
    leftList = indexData.value.filter((item) => item.id.indexOf('_Top') < 0);
  } else {
    leftList = selectedRows.value;
  }
  openModal({
    title: '预览',
    width: '98%',
    height: 800,
    content(h) {
      return h(CheckModalView, {
        leftList,
        showLeft: type === 'all',
      });
    },
  });
}

function expandRows(expanded, record) {
  if (expanded) {
    defaultExpandedRowKeys.value.push(record.id);
  } else {
    defaultExpandedRowKeys.value = defaultExpandedRowKeys.value.filter((item) => item !== record.id);
  }
}
function upDateTable(type = 'update', record = {}) {
  if (type === 'update') {
    tableRef.value.reload();
  }
  if (type === 'changeTabs') {
    emit('changeTabs', record);
  }
}
onMounted(() => {
  getStatusOptions();
});
function getStatusOptions() {
  new Api('/pms').fetch('', 'collaborativeCompilationTask/getStatus', 'GET').then((res) => {
    statusOptions.value = res;
  });
}
function changeStatus() {
  upDateTable();
}
</script>
<style lang="less" scoped>
.establishment-task{
  height: 100%;
}
.toolbar-left{
  display: flex;
  align-items: center;
  flex-flow: wrap;
  width: calc(100% - 220px);
  gap: 10px 0;
}
</style>