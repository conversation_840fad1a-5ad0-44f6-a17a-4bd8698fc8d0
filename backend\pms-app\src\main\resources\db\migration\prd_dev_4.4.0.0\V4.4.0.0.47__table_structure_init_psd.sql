DROP TABLE IF EXISTS pmsx_job_height_risk_copy;

CREATE TABLE `pmsx_job_height_risk_copy` (
                                             `id` varchar(64) NOT NULL COMMENT '主键',
                                             `class_name` varchar(64) DEFAULT 'JobHeightRisk' COMMENT '创建人',
                                             `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                             `modify_time` datetime DEFAULT NULL COMMENT '修改时间',
                                             `owner_id` varchar(64) DEFAULT 'user00000000000000000100000000000000' COMMENT '拥有者',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                             `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                             `platform_id` varchar(64) DEFAULT 'ykovb40e9fb1061b46fb96c4d0d3333dcc13' COMMENT '平台ID',
                                             `org_id` varchar(64) DEFAULT 'rxlm1789200490531569664' COMMENT '业务组织Id',
                                             `status` int(11) DEFAULT '1' COMMENT '状态',
                                             `logic_status` int(11) DEFAULT '1' COMMENT '逻辑删除字段',
                                             `operational_risks_json` text NOT NULL COMMENT '作业风险及判断标准',
                                             `job_number` varchar(64) DEFAULT NULL COMMENT '作业编号',
                                             `is_height_risk` bit(1) DEFAULT NULL COMMENT '是否高风险',
                                             `work_topics` varchar(200) DEFAULT NULL COMMENT '工作主题',
                                             `work_order_no` varchar(64) DEFAULT NULL COMMENT '工单号',
                                             `job_address` varchar(200) DEFAULT NULL COMMENT '作业地点',
                                             `plan_commencement_date` datetime DEFAULT NULL COMMENT '计划开工时间',
                                             `job_content` varchar(255) DEFAULT NULL COMMENT '工作描述',
                                             `operating_dept` varchar(64) DEFAULT NULL COMMENT '作业部门',
                                             `work_owner_name` varchar(64) DEFAULT NULL COMMENT '项目负责人',
                                             `work_owner_phone` varchar(20) DEFAULT NULL COMMENT '负责人电话',
                                             `manager_name` varchar(64) DEFAULT NULL COMMENT '管理人',
                                             `manager_phone` varchar(20) DEFAULT NULL COMMENT '管理人电话',
                                             `job_status` varchar(64) DEFAULT NULL COMMENT '作业过程状态',
                                             `current_link` varchar(64) DEFAULT NULL COMMENT '当前环节',
                                             `process_status` varchar(64) DEFAULT NULL COMMENT '作业过程状态',
                                             `current_phase` varchar(255) DEFAULT NULL COMMENT '当前环节',
                                             `check_name` varchar(64) DEFAULT NULL COMMENT '监督人员姓名',
                                             `check_id` varchar(64) DEFAULT NULL COMMENT '监督人员id',
                                             `manager_id` varchar(64) DEFAULT NULL COMMENT '管理人员id',
                                             `job_address_name` varchar(64) DEFAULT NULL COMMENT '作业地点名称',
                                             `operating_dept_name` varchar(128) DEFAULT NULL COMMENT '作业部门名称',
                                             `check_phone` varchar(64) DEFAULT NULL COMMENT '监督人电话',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='作业高风险副本';

ALTER TABLE `pmsx_job_height_risk` ADD COLUMN `check_id` varchar(64)  COMMENT '监督人员id';
ALTER TABLE `pmsx_job_height_risk` ADD COLUMN `manager_id` varchar(64)  COMMENT '管理人员id';