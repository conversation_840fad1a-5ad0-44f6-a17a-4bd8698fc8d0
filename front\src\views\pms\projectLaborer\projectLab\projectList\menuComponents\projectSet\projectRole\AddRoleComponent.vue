<!--业务组件 AddRoleComponent-->
<template>
  <OrionTable
    :options="baseTableOption"
    :dataSource="state.dataSource"
    @smallSearch="smallSearch"
    @selectionChange="selectionChange"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { projectRoleListApi } from '/@/views/pms/projectLaborer/api/projectList';

const props = defineProps<{projectId:string}>();
const emit = defineEmits(['change']);

const state = reactive({
  dataSource: [],
});
const columns = [
  {
    title: '角色编号',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '角色名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    key: 'creatorName',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
    key: 'modifyName',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];

const dataSource = [];
const baseTableOption = {
  showToolButton: false,
  rowSelection: {},
  columns,
};

function smallSearch(name) {
  const data = {
    name,
    projectId: props.projectId,
  };
  projectRoleListApi(data).then((res) => {
    state.dataSource = res;
  });
}
function selectionChange(rows) {
  emit('change', rows);
}
onMounted(() => {
  smallSearch(null);
});
</script>
