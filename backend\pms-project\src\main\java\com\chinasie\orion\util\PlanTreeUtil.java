package com.chinasie.orion.util;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.vo.PlanTreeVo;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/17/11:39
 * @description:
 */
public class PlanTreeUtil {
    public static List<PlanTreeVo> assembleTree(List<PlanTreeVo> listNodes) {
        List<PlanTreeVo> newTreeNodes = new ArrayList();
        newTreeNodes.addAll(listNodes.stream().filter((tx) ->  StrUtil.isBlank(tx.getParentId()) || "null".equals(tx.getParentId()) || "0".equals(tx.getParentId())).collect(Collectors.toList()));
        Iterator var2 = newTreeNodes.iterator();
        while (var2.hasNext()) {
            PlanTreeVo t = (PlanTreeVo) var2.next();
            assembleTree(t, listNodes);
        }
        return newTreeNodes;
    }

    public static void assembleTree(PlanTreeVo node, List<PlanTreeVo> listNodes) {
        if (node != null && !CollectionUtils.isEmpty(listNodes)) {
            Stream<PlanTreeVo> var10000 = listNodes.stream().filter((tx) -> Objects.equals(tx.getParentId(), node.getId()));
            Objects.requireNonNull(node);
            var10000.forEachOrdered(node::addChildren);
            if (!CollectionUtils.isEmpty(node.getChildren())) {
                Collections.sort(node.getChildren(), Comparator.comparing(PlanTreeVo::getSort, Comparator.nullsFirst(Comparator.naturalOrder())));
                Iterator var2 = node.getChildren().iterator();
                while (var2.hasNext()) {
                    try {
                        PlanTreeVo t = (PlanTreeVo) var2.next();
                        assembleTree(t, listNodes);
                    } catch (Exception e) {

                    }
                }
            }
        }
    }
}
