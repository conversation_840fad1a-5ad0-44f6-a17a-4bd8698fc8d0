package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Review DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewDTO对象", description = "项目评审")
@Data
@ExcelIgnoreUnannotated
public class ReviewDTO extends ObjectDTO implements Serializable {

    /**
     * 内容备注
     */
    @ApiModelProperty(value = "内容备注")
    @ExcelProperty(value = "内容备注 ", index = 0)
    @Length(max = 1000 ,message = "内容备注不能超过1000！")
    private String content;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 会议纪要号
     */
    @ApiModelProperty(value = "会议纪要号")
    private String meetingNo;

    /**
     * 问题整改报告文件号
     */
    @ApiModelProperty(value = "问题整改报告文件号")
    private String questionFileNo;

    /**
     * 评审报告文件号
     */
    @ApiModelProperty(value = "评审报告文件号")
    private String reviewFileNo;


    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    private String reviewAddress;

    /**
     * 会议召开时间
     */
    @ApiModelProperty(value = "会议召开时间")
    private Date reviewTime;

    /**
     * 文档齐套检查
     */
    @ApiModelProperty(value = "文档齐套检查")
    private Integer examineState;

    /**
     * 阶段遗留问题已关闭
     */
    @ApiModelProperty(value = "阶段遗留问题已关闭")
    private Integer phaseLegacy;

    /**
     * 满足产品开发流程的要求
     */
    @ApiModelProperty(value = "研制过程文档签审完成并齐套，满足产品开发流程的要求")
    private Integer requestState;

    /**
     * 项目管理专员
     */
    @ApiModelProperty(value = "项目管理专员")
    @NotBlank(message = "项目管理专员不能为空！")
    private String manageUser;

    /**
     * 评审名称
     */
    @ApiModelProperty(value = "评审名称")
    private String name;

    /**
     * 关联任务计划
     */
    @ApiModelProperty(value = "关联任务计划")
    @NotBlank(message = "关联任务计划不能为空！")
    private String planId;

    /**
     * 评审类型
     */
    @ApiModelProperty(value = "评审类型")
    private String reviewType;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    //=======================交付物数据啊========================
    /**
     * 关联交付物
     */
    @ApiModelProperty(value = "关联交付物")
    private String deliverId;

    /**
     * 交付物名称
     */
    @ApiModelProperty(value = "交付物名称")
    @NotBlank(message = "交付物名称不能为空！")
    private String deliverName;

    /**
     * 评审文件PLM编号
     */
    @ApiModelProperty(value = "评审文件PLM编号")
    private String plmNo;

    /**
     * 交付物文件
     */
    @ApiModelProperty(value = "交付物文件数据")
    private List<FileInfoDTO> files;
}
