<template>
  <div
    v-loading="loading"
    class="formContent"
  >
    <div class="formContent_content">
      <!--        借用一下样式-->
      <BasicTitle1
        title="基本信息"
        class="ant-basic-form"
      />
      <BasicForm @register="registerForm">
        <template #number="{ model, field }">
          <div style="display: flex;">
            <a-input
              v-model:value="model[field]"
              style="width: 100%"
              disabled
              placeholder="变更创建完成后自动生成编号"
            />
          </div>
        </template>
      </BasicForm>

      <BasicTitle1
        title="变更内容"
        class="ant-basic-form"
      />
      <!--        changeContent-->
      <AddTableProjectBudget
        v-if="type==='project_budget'"
        ref="addTableProjectBudgetFormRef"
        :type="type"
      />
      <AddTableProjectDeliver
        v-else-if="type === 'project_deliver'"
        ref="addTableProjectDeliverFormRef"
        :type="type"
      />
      <AddTableProjectManager
        v-else-if="type === 'project_manager'"
        ref="addTableProjectManagerFormRef"
        :type="type"
      />
      <AddTableProjectMilestone
        v-else-if="type === 'project_milestone'"
        ref="addTableProjectMilestoneFormRef"
        :type="type"
      />
      <AddTableProjectScheme
        v-else-if="type==='project_scheme'"
        ref="addTableProjectSchemeFormRef"
        :schemeData="schemeData"
      />
      <AddTableProjectStatus
        v-else-if="type === 'project_status'"
        ref="addTableProjectStatusFormRef"
        :type="type"
      />
      <AddTableProjectUser
        v-else-if="type === 'project_user'"
        ref="addTableProjectUserFormRef"
        :type="type"
      />
    </div>
  </div>
  <SelectUserModal
    selectType="radio"
    @register="selectUserRegister"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, defineExpose, h, nextTick, onMounted, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicForm, BasicTitle1, getDictByNumber, SelectUserModal, useForm, useModal,
} from 'lyra-component-vue3';
import { Input, message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import { appendFrom } from '/@/views/pms/utils/utils';
import {
  AddTableProjectBudget, AddTableProjectScheme,
  AddTableProjectDeliver, AddTableProjectManager, AddTableProjectMilestone, AddTableProjectStatus,
  AddTableProjectUser,
} from '../index';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicTitle1,
    BasicForm,
    AInput: Input,
    SelectUserModal,
    AddTableProjectScheme,
    AddTableProjectBudget,
    AddTableProjectDeliver,
    AddTableProjectManager,
    AddTableProjectMilestone,
    AddTableProjectStatus,
    AddTableProjectUser,
  },
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
    drawerData: {
      type: Object,
      default: () => {},
    },
    addAlterTableApi: {
      type: Function,
      default: () => {},
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    // 项目计划需要的值
    const schemeData:Ref<Record<any, any>> = ref({});
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const route = useRoute();
    const formFieldList:Ref<any[]> = ref([]);
    const AddTableNodeBottomFormRef = ref();

    const addTableProjectUserFormRef = ref();
    const addTableProjectStatusFormRef = ref();
    const addTableProjectFileFormRef = ref();
    const addTableProjectDeliverFormRef = ref();
    const addTableProjectBudgetFormRef = ref();
    const addTableProjectSchemeFormRef = ref();
    const addTableProjectMilestoneFormRef = ref();
    const addTableProjectManagerFormRef = ref();
    const state = reactive({
      loading: false,
      loadingBtn: false,
      checked: false,
      formType: 'add',
      ecrTypeOptions: [],
      fieldList: [],
      formId: '',
      departmentData: [],
      centerOptions: [],
      responsiblerId: '',
    });
    const tableRef = ref();
    // const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
    //
    // });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    const detailsData = ref();
    async function getItemData(id) {
      state.loading = true;
      try {
        let changeType = getChangeTypeUrl();
        if (type.value === 'project_scheme' || type.value === 'project_milestone') {
          changeType += (type.value === 'project_scheme') ? '/detail/list' : '/detailList';
        }
        const res = await new Api(`/pas/${changeType}/${id}`).fetch({}, '', 'GET');
        detailsData.value = res;
        let formRef;
        let formData;
        switch (type.value) {
          case 'project_budget':
            formRef = addTableProjectBudgetFormRef.value;
            formData = res;
            break;
          case 'project_user':
            formRef = addTableProjectUserFormRef.value;
            formData = res;
            break;
          case 'project_manager':
            formRef = addTableProjectManagerFormRef.value;
            formData = res;
            break;
          case 'project_status':
            formRef = addTableProjectStatusFormRef.value;
            formData = res.ecrVO;
            break;
          case 'project_milestone':
            formRef = addTableProjectMilestoneFormRef.value;
            formData = res.ecrVO;
            break;
          case 'project_deliver':
            formRef = addTableProjectDeliverFormRef.value;
            formData = res.ecrVO;
            break;
          case 'project_scheme':
            formRef = addTableProjectSchemeFormRef.value;
            formData = res.ecrVOS;
            schemeData.value = {
              projectId: formData.projectId,
              projectName: formData.projectName,
            };
            break;
          default:
            throw new Error('Unsupported type.value');
        }

        await formRef.setFormData(formData, res);
        getTypeList(formData.ecrType, 'init', formData);
        state.loading = false;
      } catch (e) {
        state.loading = false;
        throw new Error(e);
      }
    }
    const type = ref('');
    const [
      registerForm,
      {
        setFieldsValue, validateFields, appendSchemaByField, removeSchemaByFiled,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '标题',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入标题',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 12,
          },
          slot: 'number',
        },

        {
          field: 'changeWay',
          component: 'Select',
          label: '变更方式',
          colProps: {
            span: 12,
          },
          rules: [
            {
              required: true,
              message: '请选择变更方式',
              trigger: 'blur',
              type: 'number',
            },
          ],
          componentProps: {
            placeholder: '请选择变更方式',
            options: [
              {
                label: '快速变更',
                value: 1,
              },
              {
                label: '工程变更',
                value: 2,
              },
            ],
          },
        },
        {
          field: 'responsiblerName',
          component: 'Input',
          label: '责任人',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入责任人',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ responsiblerName: data[0].name });
                  state.responsiblerId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ responsiblerName: data[0].name });
                      state.responsiblerId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.responsiblerId = '';
            },
          },
        },
        {
          field: 'type',
          label: '变更类型',
          required: true,
          colProps: { span: 12 },
          componentProps: {
            placeholder: '请选择',
            api: () => getDictByNumber('ecr_type'),
            onChange: (val) => {
              type.value = val;
            },
            // disabled: true,
            labelField: 'name',
            valueField: 'number',
          },
          component: 'ApiSelect',
        },
        {
          field: 'ecrType',
          component: 'TreeSelect',
          label: '变更类型拓展属性',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择类型',
            treeData: computed(() => state.ecrTypeOptions),
            fieldNames: {
              label: 'name',
              key: 'id',
              value: 'id',
              children: 'children',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'applyTime',
          component: 'DatePicker',
          label: '申请时间',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择申请时间',
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'description',
          component: 'InputTextArea',
          label: '变更内容描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },

        {
          field: 'reason',
          component: 'InputTextArea',
          label: '变更原因',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });

    // const cancel = () => {
    //   closeDrawer();
    // };
    //
    function getChangeTypeUrl() {
      if (type.value === 'project_manager') {
        return 'ecrProjectManager';
      }
      if (type.value === 'project_status') {
        return 'ecrProjectStatus';
      }
      if (type.value === 'project_budget') {
        return 'ecrProjectBudget';
      }
      if (type.value === 'project_deliver') {
        return 'ecrProjectDeliver';
      }
      if (type.value === 'project_user') {
        return 'ecrProjectUser';
      }
      if (type.value === 'project_milestone') {
        return 'ecrProjectMilestone';
      }
      if (type.value === 'project_scheme') {
        return 'ecrProjectScheme';
      }
    }
    async function saveData() {
      let formData = await validateFields();

      let attrValues = [];
      if (formFieldList.value.length > 0) {
        formFieldList.value.forEach((item) => {
          attrValues.push({
            attrId: item.id,
            value: Array.isArray(formData[item.id]) ? formData[item.id].join(';') : formData[item.id],
          });
          delete formData[item.id];
        });
      }
      formData.responsiblerId = state.responsiblerId;
      formData.attrValues = attrValues;
      formData.type = type.value;
      let bottomFormParams = {};
      if (type.value === 'project_scheme') {
        bottomFormParams = await addTableProjectSchemeFormRef.value.getFormData();
      } else if (type.value === 'project_budget') {
        bottomFormParams = await addTableProjectBudgetFormRef.value.getFormData();
      } else if (type.value === 'project_deliver') {
        bottomFormParams = await addTableProjectDeliverFormRef.value.getFormData();
      } else if (type.value === 'project_manager') {
        bottomFormParams = await addTableProjectManagerFormRef.value.getFormData();
      } else if (type.value === 'project_milestone') {
        bottomFormParams = await addTableProjectMilestoneFormRef.value.getFormData();
      } else if (type.value === 'project_status') {
        bottomFormParams = await addTableProjectStatusFormRef.value.getFormData();
      } else if (type.value === 'project_user') {
        bottomFormParams = await addTableProjectUserFormRef.value.getFormData();
      } else {
        bottomFormParams = await AddTableNodeBottomFormRef.value.getFormData();
      }

      if (bottomFormParams) {
        const newFormData = Object.assign(formData, bottomFormParams);
        state.loadingBtn = true;
        let str = state.formType === 'edit' ? 'edit' : 'add';
        const changeType = getChangeTypeUrl();
        if (str === 'edit') {
          newFormData.id = state.formId;
          if (type.value === 'project_status') {
            newFormData.ecrId = detailsData.value.ecrId;
          }
          if (type.value === 'project_scheme') {
            newFormData.ecrId = detailsData.value.ecrVOS.id;
          }
          // if (type.value === 'project_status' || type.value === 'project_deliver') {
          //
          //   newFormData.ecrId = detailsData.value.ecrId;
          // }
        }
        if (state.formType === 'add') {
          await props.addAlterTableApi(newFormData);
        } else {
          await new Api(`/pas/${changeType}`).fetch(newFormData, str, 'PUT');
        }
        message.success(`${state.formType === 'edit' ? '编辑' : '新增'}成功`);
      }
    }
    onMounted(() => {
      state.loading = false;
      state.formType = props.drawerData.type;
      if (props.drawerData.type === 'add') {
        setFieldsValue(props.drawerData.data);
        type.value = props.drawerData.data.type;
        schemeData.value = props.drawerData.schemeData;
      } else {
        state.formId = props.drawerData.data.id;
        // setDrawerProps({ title: '编辑变更' });
        type.value = props.drawerData.data.type;
        getItemData(state.formId);
      }
      getEcrTypeTree();
    });
    function getEcrTypeTree() {
      new Api('/pas').fetch({ status: 1 }, 'ecr-type/tree', 'GET').then((res) => {
        state.ecrTypeOptions = res;
      });
    }
    const BasicFormFlag = ref(true);
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.id);
        });
      }
      nextTick(() => {
        getTypeList(val, 'change');
      });
    }
    function getTypeList(value, type = 'change', res:any = {}) {
      new Api('/pas').fetch({ status: 1 }, `ecr-type-to-ecr-attr/list/${value}`, 'get').then((res1) => {
        formFieldList.value = res1;
        appendFrom(formFieldList.value, appendSchemaByField, 'applyTime');
        if (type === 'init') {
          if (Array.isArray(res.attrValues) && res1.length > 0) {
            res.attrValues.forEach((item) => {
              let fileItem = res1.find((item1) => item1.id === item.attrId);
              res[item.attrId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
            });
          }
          state.responsiblerId = res.responsiblerId || '';
          setFieldsValue(res);
        }
      });
    }

    return {
      ...toRefs(state),
      schemeData,
      // modalRegister,
      registerForm,
      // cancel,
      saveData,
      tableRef,
      selectUserRegister,
      BasicFormFlag,
      type,
      AddTableNodeBottomFormRef,
      addTableProjectUserFormRef,
      addTableProjectStatusFormRef,
      addTableProjectFileFormRef,
      addTableProjectDeliverFormRef,
      addTableProjectBudgetFormRef,
      addTableProjectSchemeFormRef,
      addTableProjectMilestoneFormRef,
      addTableProjectManagerFormRef,
    };
  },
});

</script>
<style lang="less" scoped>

.formContent{
  display: flex;
  height: 100%;
  flex-direction: column;
  .formContent_content{
    flex: 1 1 auto;
  }
  .moreMessage{
    color: #5976d6;
    cursor: pointer;
  }
  .actions{
    span{
      color: ~`getPrefixVar('primary-color')`;
      padding:0px 10px;
      cursor: pointer;
    }
    .actions1{
      border-right: 1px solid ~`getPrefixVar('primary-color')`;
    }
  }
}
:deep(.ant-form-item){
  display: block;
}
:deep(.ant-form-item-control){
  width: 100% !important;
}
:deep(.edit-btn){
  color: #0960bd;;
}
</style>
