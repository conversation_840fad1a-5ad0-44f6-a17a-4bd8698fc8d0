package com.chinasie.orion.msc.handler;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.msc.api.MscBuildHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class GoodsServiceWillExpireBuildHandler implements MscBuildHandler<GoodsServicePlan> {

    @Override
    public SendMessageDTO buildMsc(GoodsServicePlan plan, Object... params) {
        // $assign$将$name$$action$至$deptName$的$rspUser$
        Map<String, Object> messageMap = new HashMap<>();

        messageMap.put("$description$", plan.getDescription());
        messageMap.put("$goodsServiceNumber$", plan.getGoodsServiceNumber());


        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
//                .messageUrl("/plan/task-plan-details/" + plan.getId())
//                .messageUrl("/pms/materialWarehousingDetails?"+plan.getId()+"&projectId="+plan.getProjectId())
                .messageUrl("/pms/menuComponents?id="+plan.getProjectId())
                .messageUrlName("详情")
                .recipientIdList(Arrays.asList(plan.getOwnerId()))
                .senderId(plan.getOwnerId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(plan.getId())
                .platformId(plan.getPlatformId())
                .orgId(plan.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeNumberDict.GOODS_SERVICE_WILL_EXPIRE_NODE;
    }
}
