package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractChange VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractChangeVO对象", description = "合同变更信息表")
@Data
public class ContractChangeVO extends ObjectVO implements Serializable {

    /**
     * 变更编号
     */
    @ApiModelProperty(value = "变更编号")
    private String changeId;


    /**
     * 变更标题
     */
    @ApiModelProperty(value = "变更标题")
    private String changeTitle;


    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型")
    private String changeType;


    /**
     * 变更申请日期
     */
    @ApiModelProperty(value = "变更申请日期")
    private Date changeRequestDate;


    /**
     * 本次变更金额
     */
    @ApiModelProperty(value = "本次变更金额")
    private BigDecimal thisChangeAmount;


    /**
     * 累计变更金额
     */
    @ApiModelProperty(value = "累计变更金额")
    private BigDecimal cumulativeChangeAmount;


    /**
     * 累计变更比率
     */
    @ApiModelProperty(value = "累计变更比率")
    private String cumulativeChangeRate;


    /**
     * 变更后合同承诺总价（总目标值）
     */
    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    private BigDecimal contactAmountAfterChange;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String businessRspUserName;
}
