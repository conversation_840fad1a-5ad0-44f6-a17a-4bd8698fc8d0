package com.chinasie.orion.service.projectStatistics;


import com.chinasie.orion.domain.dto.projectStatistics.PlanStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.PlanStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * PlanStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 11:28:29
 */
public interface PlanStatusStatisticsService  extends OrionBaseService<PlanStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    PlanStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param planStatusStatisticsDTO
     */
    PlanStatusStatisticsVO create(PlanStatusStatisticsDTO planStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param planStatusStatisticsDTO
     */
    Boolean edit(PlanStatusStatisticsDTO planStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<PlanStatusStatisticsVO> pages(Page<PlanStatusStatisticsDTO> pageRequest) throws Exception;

}
