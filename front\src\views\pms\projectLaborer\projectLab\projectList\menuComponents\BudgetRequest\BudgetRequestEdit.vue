<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import {
  computed, reactive, onMounted, Ref, ref, provide, readonly, inject,
} from 'vue';
import { getRules, setFormFieldsValues } from './utils';
import Api from '/@/api';
false;

const props = defineProps<{
  formId: string | undefined,
  projectId: string | undefined
}>();

// 对于自定义规则进行处理
const schemas = [
  // {
  //   field: 'approvalId',
  //   label: '立项编码',
  //   component: 'Input',
  //   colProps: {
  //     span: 12,
  //   },
  //   type: 'input',
  //   componentProps: {
  //     allowClear: true,
  //     placeholder: '请输入',
  //   },
  //   rules: [
  //     {
  //       required: true,
  //       message: '该内容为必填项',
  //       trigger: 'change',
  //     },
  //   ],
  // },
  // {
  //   field: 'approvalName',
  //   label: '立项名称',
  //   component: 'Input',
  //   colProps: {
  //     span: 12,
  //   },
  //   type: 'input',
  //   componentProps: {
  //     allowClear: true,
  //     placeholder: '请输入',
  //   },
  //   rules: [
  //     {
  //       required: true,
  //       message: '该内容为必填项',
  //       trigger: 'change',
  //     },
  //   ],
  // },
  {
    field: 'name',
    label: '申请标题',
    component: 'Input',
    colProps: {
      span: 24,
    },
    type: 'input',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'remark',
    label: '申请说明',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    type: 'textarea',
    componentProps: {
      showCount: true,
      maxlength: 200,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
];
const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props.formId && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/budgetApplicationFrom').fetch('', props.formId, 'GET');

    await setFieldsValue({ ...result });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.formId,
      projectId: props?.projectId,
      ...formValues,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/budgetApplicationFrom').fetch(params, props.formId ? 'edit' : 'add', props.formId ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
