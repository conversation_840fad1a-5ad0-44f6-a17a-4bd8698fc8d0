<template>
  <BasicDrawer
    v-bind="$attrs"
    title="搜索问题"
    width="400"
    showFooter
    :showCancelBtn="false"
    :showOkBtn="false"
    @register="drawerRegister"
    @visibleChange="visibleChange"
  >
    <Content
      v-if="visible"
      @init="formInit"
    />

    <template #centerFooter>
      <a-button
        style="width: 100px; background: #5172dc19;color: #5172DC;"
        size="large"
        @click="cancelClick('close')"
      >
        取消
      </a-button>
      <span class="mr10" />
    </template>

    <template #appendFooter>
      <a-button
        size="large"
        style="width: 100px; background: #5172dc;color: #ffffff;"
        @click="okClick"
      >
        确认
      </a-button>
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import { Button } from 'ant-design-vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { reactive, toRefs } from 'vue';
import Content from './Content.vue';
export default {
  name: 'Index',
  components: {
    BasicDrawer,
    Content,
    AButton: Button,
  },
  setup(props, { emit }) {
    const state = reactive({
      visible: false,
      formMethods: null,
      onOk: null,
    });
    const [drawerRegister, { closeDrawer }] = useDrawerInner((props) => {
      props.onOk && (state.onOk = props.onOk);
    });

    function formInit({ formMethods }) {
      state.formMethods = formMethods;
    }

    function cancelClick() {
      closeDrawer();
    }

    function visibleChange(visible) {
      state.visible = visible;
    }

    function okClick() {
      const formParams = state.formMethods?.getFieldsValue();
      formParams.queryPredictStartTime = formParams?.predictEndTime?.[0];
      formParams.queryPredictEndTime = formParams?.predictEndTime?.[1];
      delete formParams.predictEndTime;
      emit('search', formParams);
      closeDrawer();
    }
    return {
      ...toRefs(state),
      drawerRegister,
      cancelClick,
      okClick,
      formInit,
      visibleChange,
    };
  },
};
</script>

<style scoped>

</style>
