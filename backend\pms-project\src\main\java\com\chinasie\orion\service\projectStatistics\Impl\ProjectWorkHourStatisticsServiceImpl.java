package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectWorkHourStatisticsDTO;
import com.chinasie.orion.domain.entity.WorkHourEstimate;
import com.chinasie.orion.domain.entity.WorkHourFill;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectWorkHourStatisticsVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.WorkHourEstimateService;
import com.chinasie.orion.service.WorkHourFillService;
import com.chinasie.orion.service.projectStatistics.ProjectWorkHourStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ProjectWorkHourStatisticsServiceImpl implements ProjectWorkHourStatisticsService {
    @Resource
    private WorkHourEstimateService workHourEstimateService;
    @Resource
    private WorkHourFillService workHourFillService;
    @Autowired
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Resource
    private UserBo userBo;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Override
    public ProjectWorkHourStatisticsVO getProjectWorkHourTotalStatistics(ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO) {
        ProjectWorkHourStatisticsVO projectWorkHourStatisticsVO = new ProjectWorkHourStatisticsVO();
        LambdaQueryWrapperX<WorkHourEstimate> projectWorkHourLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectWorkHourLambdaQueryWrapperX.select(" ifnull(sum(work_hour),0) estimateWorkHour");
        projectWorkHourLambdaQueryWrapperX.eq(WorkHourEstimate::getProjectId, projectWorkHourStatisticsDTO.getProjectId());
        projectWorkHourLambdaQueryWrapperX.eq(WorkHourEstimate::getStatus, 130);
        Map<String, Object> map = workHourEstimateService.getMap(projectWorkHourLambdaQueryWrapperX);
        projectWorkHourStatisticsVO.setEstimateWorkHour(Integer.parseInt(map.get("estimateWorkHour").toString()));
        LambdaQueryWrapperX<WorkHourFill> workHourFillLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        workHourFillLambdaQueryWrapperX.select(" ifnull(sum(work_hour),0) fillWorkHour");
        workHourFillLambdaQueryWrapperX.eq(WorkHourEstimate::getProjectId, projectWorkHourStatisticsDTO.getProjectId());
        workHourFillLambdaQueryWrapperX.eq(WorkHourEstimate::getStatus, 130);
        Map<String, Object> fillMap = workHourFillService.getMap(workHourFillLambdaQueryWrapperX);
        projectWorkHourStatisticsVO.setFillWorkHour(Integer.parseInt(fillMap.get("fillWorkHour").toString()));
        projectWorkHourStatisticsVO.setSurplusWorkHour(projectWorkHourStatisticsVO.getEstimateWorkHour() - projectWorkHourStatisticsVO.getFillWorkHour());
        projectWorkHourStatisticsVO.setEstimateDeviation(projectWorkHourStatisticsVO.getFillWorkHour() - projectWorkHourStatisticsVO.getEstimateWorkHour());

        if(projectWorkHourStatisticsVO.getEstimateWorkHour()>0){
            String  fillSchedule = String.format("%.0f",(double)projectWorkHourStatisticsVO.getFillWorkHour() / projectWorkHourStatisticsVO.getEstimateWorkHour()*100);
            String  deviationRatio = String.format("%.0f",(double)(projectWorkHourStatisticsVO.getFillWorkHour() - projectWorkHourStatisticsVO.getEstimateWorkHour()) / projectWorkHourStatisticsVO.getEstimateWorkHour()*100);
            projectWorkHourStatisticsVO.setFillSchedule(new BigDecimal(fillSchedule));
            projectWorkHourStatisticsVO.setDeviationRatio(new BigDecimal(deviationRatio));
        }

        return projectWorkHourStatisticsVO;

    }

    @Override
    public List<ProjectWorkHourStatisticsVO> getProjectWorkHourTimeStatistics(ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO) {
        List<ProjectWorkHourStatisticsVO> projectWorkHourStatisticsVOs = new ArrayList<>();
        String estimateSql = "select ";
        String fillSql = "select ";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectWorkHourStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }else if (projectWorkHourStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }else if (projectWorkHourStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }else{
                return new ArrayList<ProjectWorkHourStatisticsVO>();
            }
            SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMM");
            String start=simpleDateFormat.format(startDate);
            String end=simpleDateFormat.format(endDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");

            estimateSql = estimateSql + "ifnull(sum( CASE  WHEN CONCAT(SUBSTRING(d.work_month,1,4),SUBSTRING(d.work_month,6,2))  >= '"+start+"' and CONCAT(SUBSTRING(d.work_month,1,4),SUBSTRING(d.work_month,6,2))  <= '"+end+"' THEN d.work_hour ELSE 0 END ),0) estimate"+i;
            fillSql = fillSql + "ifnull(sum( CASE  WHEN d.work_date >= '"+sdf.format(startDate)+"' and d.work_date <= '"+sdf.format(endDate)+"' THEN d.work_hour ELSE 0 END ),0) fill"+i;
            if(i<9){
                estimateSql=estimateSql+"," ;
                fillSql=fillSql+"," ;
            }
        }
        estimateSql=estimateSql+ " FROM " +
                " pms_work_hour_estimate e " +
                " LEFT JOIN pms_work_hour_estimate_detail d ON e.id = d.work_hour_id " +
                " WHERE" +
                " e.logic_status = 1  " +
                " AND d.logic_status = 1  and e.project_id= '"+projectWorkHourStatisticsDTO.getProjectId()+"' and e.`status`=130"+
                " GROUP BY" +
                " e.project_id";

        fillSql=fillSql+ " FROM" +
                " pms_work_hour_fill f " +
                " LEFT JOIN pms_work_hour_fill_day d ON f.id = d.fill_id " +
                " WHERE " +
                " f.logic_status = 1 " +
                " AND d.logic_status = 1  and f.project_id='"+projectWorkHourStatisticsDTO.getProjectId()+"' and f.`status`=130"+
                " GROUP BY" +
                " f.project_id";

        List<Map<String,Object>> estimateList = namedParameterJdbcTemplate.queryForList(estimateSql, new HashMap<>());
        List<Map<String,Object>> fillList = namedParameterJdbcTemplate.queryForList(fillSql, new HashMap<>());
        Map<String, Object> map = new HashMap<>();
        if(CollectionUtil.isNotEmpty(estimateList)){
            map=estimateList.get(0);
        }
        Map<String, Object> fillMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(fillList)){
            fillMap=fillList.get(0);
        }
        for (int i = 0; i < 10; i++) {
            ProjectWorkHourStatisticsVO projectWorkHourStatisticsVO = new ProjectWorkHourStatisticsVO();
            if(CollectionUtil.isNotEmpty(estimateList)) {
                projectWorkHourStatisticsVO.setEstimateWorkHour(Integer.parseInt(map.get("estimate" + i).toString()));
            }else{
                projectWorkHourStatisticsVO.setEstimateWorkHour(0);
            }
            if(CollectionUtil.isNotEmpty(fillList)) {
                projectWorkHourStatisticsVO.setFillWorkHour(Integer.parseInt(fillMap.get("fill" + i).toString()));
            }else{
                projectWorkHourStatisticsVO.setFillWorkHour(0);
            }
            projectWorkHourStatisticsVO.setId(String.valueOf(i+1));
            projectWorkHourStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectWorkHourStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectWorkHourStatisticsVOs.add(projectWorkHourStatisticsVO);
        }
        Collections.reverse(projectWorkHourStatisticsVOs);
        return projectWorkHourStatisticsVOs;
    }

    @Override
    public Page<ProjectWorkHourStatisticsVO> getProjectWorkHourPages(Page<ProjectWorkHourStatisticsDTO> pageRequest) throws Exception {

        List<ProjectWorkHourStatisticsVO> projectWorkHourStatisticsVOS = new ArrayList<>();
        ProjectWorkHourStatisticsDTO projectSchemeStatisticsDTO = pageRequest.getQuery();
        Long pageSize = pageRequest.getPageSize();
        Long pageNum = pageRequest.getPageNum();
        String count = "select a.member_id,ifnull(a.estimate,0) estimateWorkHour,ifnull(b.fill,0) fillWorkHour from  " +
                "(select member_id,sum(work_hour)estimate from pms_work_hour_estimate where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)a " +
                "left join " +
                "(select member_id,sum(work_hour)fill from pms_work_hour_fill where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)b on a.member_id = b.member_id"+
                " union "+
                "select b.member_id,ifnull(a.estimate,0) estimateWorkHour,ifnull(b.fill,0) fillWorkHour from  " +
                "(select member_id,sum(work_hour)estimate from pms_work_hour_estimate where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)a " +
                "right join " +
                "(select member_id,sum(work_hour)fill from pms_work_hour_fill where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)b on a.member_id = b.member_id";
        List<ProjectWorkHourStatisticsVO> countList = namedParameterJdbcTemplate.query(count,new HashMap<>(), new BeanPropertyRowMapper(ProjectWorkHourStatisticsVO.class));

        String sql = "select a.member_id,ifnull(a.estimate,0) estimateWorkHour,ifnull(b.fill,0) fillWorkHour from  " +
                "(select member_id,sum(work_hour)estimate from pms_work_hour_estimate where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)a " +
                "left join " +
                "(select member_id,sum(work_hour)fill from pms_work_hour_fill where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)b on a.member_id = b.member_id"+
                " union "+
                "select b.member_id,ifnull(a.estimate,0) estimateWorkHour,ifnull(b.fill,0) fillWorkHour from  " +
                "(select member_id,sum(work_hour)estimate from pms_work_hour_estimate where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)a " +
                "right join " +
                "(select member_id,sum(work_hour)fill from pms_work_hour_fill where project_id = '" + projectSchemeStatisticsDTO.getProjectId() + "' and `status`=130 and logic_status = 1 group by member_id)b on a.member_id = b.member_id"+
                " limit " + (pageNum - 1) * pageSize + "," + pageNum * pageSize;
        List<ProjectWorkHourStatisticsVO> list =namedParameterJdbcTemplate.query(sql,new HashMap<>(), new BeanPropertyRowMapper(ProjectWorkHourStatisticsVO.class));
        for (ProjectWorkHourStatisticsVO map : list) {
            ProjectWorkHourStatisticsVO projectWorkHourStatisticsVO = new ProjectWorkHourStatisticsVO();
            projectWorkHourStatisticsVO.setEstimateWorkHour(map.getEstimateWorkHour());
            projectWorkHourStatisticsVO.setFillWorkHour(map.getFillWorkHour());
            projectWorkHourStatisticsVO.setSurplusWorkHour(projectWorkHourStatisticsVO.getEstimateWorkHour() - projectWorkHourStatisticsVO.getFillWorkHour());
            projectWorkHourStatisticsVO.setEstimateDeviation(projectWorkHourStatisticsVO.getFillWorkHour() - projectWorkHourStatisticsVO.getEstimateWorkHour());
            if (projectWorkHourStatisticsVO.getEstimateWorkHour() > 0) {
                String  fillSchedule = String.format("%.0f",(double)projectWorkHourStatisticsVO.getFillWorkHour() / projectWorkHourStatisticsVO.getEstimateWorkHour()*100);
                String  deviationRatio = String.format("%.0f",(double)(projectWorkHourStatisticsVO.getFillWorkHour() - projectWorkHourStatisticsVO.getEstimateWorkHour()) / projectWorkHourStatisticsVO.getEstimateWorkHour()*100);
                projectWorkHourStatisticsVO.setFillSchedule(new BigDecimal(fillSchedule));
                projectWorkHourStatisticsVO.setDeviationRatio(new BigDecimal(deviationRatio));
            }
            projectWorkHourStatisticsVO.setMemberId(map.getMemberId());
            SimpleUser simpleUser=userRedisHelper.getSimpleUserById(projectWorkHourStatisticsVO.getMemberId());
            projectWorkHourStatisticsVO.setMemberName(simpleUser.getName());
            projectWorkHourStatisticsVO.setDeptName(simpleUser.getOrgName());
            projectWorkHourStatisticsVO.setDeptId(simpleUser.getOrgId());
            projectWorkHourStatisticsVOS.add(projectWorkHourStatisticsVO);
        }
        Page<ProjectWorkHourStatisticsVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), countList.size());
        pageResult.setContent(projectWorkHourStatisticsVOS);
        return pageResult;
    }
}
