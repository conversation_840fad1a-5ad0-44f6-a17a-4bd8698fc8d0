package org.jeecg.modules.demo.dkm.entity;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Description: 报价管理实体类
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Data
@TableName("pmsx_quotation_management")
public class Quotation {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    /**
     * 报价名称
     */
    private String quotationName;
    
    /**
     * 报价编号
     */
    private String quotationId;
    
    /**
     * 需求ID(外键)
     */
    private String requirementId;
    
    /**
     * 需求名称(非数据库字段，用于显示)
     */
    @TableField(exist = false)
    private String requirementName;
} 