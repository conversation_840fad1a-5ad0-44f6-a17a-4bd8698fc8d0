package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.util.List;

/**
 * InterfaceManagement VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "InterfaceManagementVO对象", description = "接口管理")
@Data
public class InterfaceManagementVO extends ObjectVO implements Serializable {

    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    private String publishOrgName;

    /**
     * 接受单位
     */
    @ApiModelProperty(value = "接受单位")
    private String receiveOrgName;

    /**
     * 接口类型
     */
    @ApiModelProperty(value = "接口类型")
    private String type;
    @ApiModelProperty(value = "接口类型名称")
    private String typeName;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号/编码")
    private String number;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    private String publishDeptId;
    @ApiModelProperty(value = "发布方部门名称")
    private String publishDeptName;
    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    private String reviewDeptIds;
    @ApiModelProperty(value = "接受方部门名称拼接")
    private String reviewDeptNames;
    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    private String manUser;
    @ApiModelProperty(value = "主办人名称")
    private String manUserName;

    /**
     * 协办
     */
    @ApiModelProperty(value = "协办")
    private String cooperationUsers;
    @ApiModelProperty(value = "协办人名称")
    private String cooperationUserNames;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    private String thirdVerify;
    @ApiModelProperty(value = "第三方检查备案名称")
    private String thirdVerifyName;

    @ApiModelProperty(value = "当前责任方")
    private String nowRspDeptName;
    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    private String specialtyCode;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    private String desc;


    @ApiModelProperty(value = "单据类型")
    private String formType;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "协办人列表")
    private List<String> cooperationUserIdList;
    @ApiModelProperty(value = "创建人部门名称")
    private String creatorDeptName;
    @ApiModelProperty(value = "创建人部门ID")
    private String creatorDeptId;

    @ApiModelProperty(value = "接口状态")
    private String interfaceState;


    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    private String dataClassName;
}
