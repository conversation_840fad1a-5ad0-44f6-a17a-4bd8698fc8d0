package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * AttendanceSign DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@ApiModel(value = "AttendanceSignDTO对象", description = "出勤签到")
@Data
@ExcelIgnoreUnannotated
public class AttendanceSignDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer attandanceYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer attandanceMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer attandanceQuarter;

    /**
     * 中心部门编码
     */
    @ApiModelProperty(value = "中心部门编码")
    @ExcelProperty(value = "中心部门编码 ", index = 1)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 2)
    private String orgName;

    /**
     * 所编号
     */
    @ApiModelProperty(value = "所编号")
    @ExcelProperty(value = "所编号 ", index = 3)
    private String deptCode;

    /**
     * 所名称
     */
    @ApiModelProperty(value = "所名称")
    @ExcelProperty(value = "所名称 ", index = 4)
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 5)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 6)
    private String contractName;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @ExcelProperty(value = "人员岗级 ", index = 7)
    private String jobGrade;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 8)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 9)
    private String supplierName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @ExcelProperty(value = "用户名称 ", index = 10)
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 11)
    private String userCode;

    /**
     * 本月应签到天数
     */
    @ApiModelProperty(value = "本月应签到天数")
    @ExcelProperty(value = "本月应签到天数 ", index = 12)
    private Integer shouldDays;

    /**
     * 实际出勤天数
     */
    @ApiModelProperty(value = "实际出勤天数")
    @ExcelProperty(value = "实际出勤天数 ", index = 13)
    private Integer actualDays;

    /**
     * 已换休天数
     */
    @ApiModelProperty(value = "已换休天数")
    @ExcelProperty(value = "已换休天数 ", index = 14)
    private Integer offDays;

    /**
     * 出勤率
     */
    @ApiModelProperty(value = "出勤率")
    @ExcelProperty(value = "出勤率 ", index = 15)
    private BigDecimal attandanceRate;




}
