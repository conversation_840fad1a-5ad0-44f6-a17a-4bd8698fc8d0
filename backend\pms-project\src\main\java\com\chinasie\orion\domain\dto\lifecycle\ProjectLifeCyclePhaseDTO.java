package com.chinasie.orion.domain.dto.lifecycle;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectLifeCycle DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@ApiModel(value = "ProjectLifeCycleDTO对象", description = "全生命周期")
@Data
@ExcelIgnoreUnannotated
public class ProjectLifeCyclePhaseDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 0)
    private String projectId;

    /**
     * 项目计划id（里程碑id）
     */
    @ApiModelProperty(value = "项目计划id（里程碑id）")
    @ExcelProperty(value = "项目计划id（里程碑id） ", index = 1)
    private String projectSchemeId;

    /**
     * 全生命周期模板id
     */
    @ApiModelProperty(value = "全生命周期模板id")
    @ExcelProperty(value = "全生命周期模板id ", index = 2)
    private String templateId;

    /**
     * 阶段描述
     */
    @ApiModelProperty(value = "阶段描述")
    @ExcelProperty(value = "阶段描述 ", index = 3)
    private String phaseDescription;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序 ", index = 4)
    private Integer sort;


    @ApiModelProperty(value = "文件")
    private List<FileVO> fileDtoList;

}
