<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '指标名称',
      field: 'indexName',
    },
    {
      label: '上月',
      field: 'lastMonth',
    },
    {
      label: '本月',
      field: 'currentMonth',
    },
    {
      label: '目标',
      field: 'goal',
    },
    {
      label: '环比',
      field: 'chainRatio',
    },
    {
      label: '同比',
      field: 'yearBasis',
    },
    {
      label: '备注说明',
      field: 'remarks',
    },
  ],
  column: 2,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
