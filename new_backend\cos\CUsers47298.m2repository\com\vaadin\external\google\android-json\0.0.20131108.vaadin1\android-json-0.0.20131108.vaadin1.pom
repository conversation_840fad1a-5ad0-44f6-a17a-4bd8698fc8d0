<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.vaadin.external.google</groupId>
    <artifactId>android-json</artifactId>
    <version>0.0.20131108.vaadin1</version>
    <name>JSON library from Android SDK</name>
    <description>
      JSON (JavaScript Object Notation) is a lightweight data-interchange format.
      This is the org.json compatible Android implementation extracted from the Android SDK
    </description>
    <url>http://developer.android.com/sdk</url>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <distribution>repo</distribution>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>
    <scm>
        <connection>scm:http:http://developer.android.com/sdk/</connection>
        <developerConnection>scm:http:http://developer.android.com/sdk/</developerConnection>
        <url>http://developer.android.com/sdk/</url>
    </scm>
    <developers>
        <developer>
            <id>id</id>
            <name>Android Dev</name>
            <email>androiddev</email>
            <url>http://developer.android.com/sdk</url>
            <organization>Google</organization>
            <organizationUrl>http://www.google.com</organizationUrl>
            <timezone>0</timezone>
        </developer>
    </developers>
    <distributionManagement>
        <repository>
            <id>vaadin-releases</id>
            <name>Vaadin release repository</name>
            <url>http://oss.sonatype.org/content/repositories/vaadin-releases/</url>
        </repository>
        <snapshotRepository>
            <id>vaadin-snapshots</id>
            <name>Vaadin snapshot repository</name>
            <url>http://oss.sonatype.org/content/repositories/vaadin-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>vaadin-snapshots</id>
            <url>http://oss.sonatype.org/content/repositories/vaadin-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>vaadin-releases</id>
            <url>http://oss.sonatype.org/content/repositories/vaadin-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>