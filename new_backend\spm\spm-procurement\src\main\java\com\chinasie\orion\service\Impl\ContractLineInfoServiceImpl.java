package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractLineInfoDTO;
import com.chinasie.orion.domain.entity.ContractLineInfo;
import com.chinasie.orion.domain.vo.ContractLineInfoVO;
import com.chinasie.orion.repository.ContractLineInfoMapper;
import com.chinasie.orion.service.ContractLineInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractLineInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractLineInfoServiceImpl extends OrionBaseServiceImpl<ContractLineInfoMapper, ContractLineInfo> implements ContractLineInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractLineInfoVO detail(String id, String pageCode) throws Exception {
        ContractLineInfo contractLineInfo = this.getById(id);
        ContractLineInfoVO result = BeanCopyUtils.convertTo(contractLineInfo, ContractLineInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractLineInfoDTO
     */
    @Override
    public String create(ContractLineInfoDTO contractLineInfoDTO) throws Exception {
        ContractLineInfo contractLineInfo = BeanCopyUtils.convertTo(contractLineInfoDTO, ContractLineInfo::new);
        this.save(contractLineInfo);

        String rsp = contractLineInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractLineInfoDTO
     */
    @Override
    public Boolean edit(ContractLineInfoDTO contractLineInfoDTO) throws Exception {
        ContractLineInfo contractLineInfo = BeanCopyUtils.convertTo(contractLineInfoDTO, ContractLineInfo::new);

        this.updateById(contractLineInfo);

        String rsp = contractLineInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractLineInfoVO> pages(String mainTableId, Page<ContractLineInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractLineInfo> condition = new LambdaQueryWrapperX<>(ContractLineInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractLineInfo::getCreateTime);

        condition.eq(ContractLineInfo::getMainTableId, mainTableId);

        Page<ContractLineInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractLineInfo::new));

        PageResult<ContractLineInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractLineInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractLineInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractLineInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同行项目信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractLineInfoDTO.class, new ArrayList<>());

    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractLineInfoVO> getByCode(Page<ContractLineInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractLineInfo> condition = new LambdaQueryWrapperX<>(ContractLineInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractLineInfo::getContractNumber,pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractLineInfo::getCreateTime);
        Page<ContractLineInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractLineInfo::new));

        PageResult<ContractLineInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractLineInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractLineInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractLineInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractLineInfoExcelListener excelReadListener = new ContractLineInfoExcelListener();
        EasyExcel.read(inputStream, ContractLineInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractLineInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同行项目信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractLineInfo> contractLineInfoes = BeanCopyUtils.convertListTo(dtoS, ContractLineInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractLineInfo-import::id", importId, contractLineInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractLineInfo> contractLineInfoes = (List<ContractLineInfo>) orionJ2CacheService.get("ncf::ContractLineInfo-import::id", importId);
        log.info("合同行项目信息导入的入库数据={}", JSONUtil.toJsonStr(contractLineInfoes));

        this.saveBatch(contractLineInfoes);
        orionJ2CacheService.delete("ncf::ContractLineInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractLineInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractLineInfo> condition = new LambdaQueryWrapperX<>(ContractLineInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractLineInfo::getCreateTime);
        List<ContractLineInfo> contractLineInfoes = this.list(condition);

        List<ContractLineInfoDTO> dtos = BeanCopyUtils.convertListTo(contractLineInfoes, ContractLineInfoDTO::new);

        String fileName = "合同行项目信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractLineInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractLineInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractLineInfoExcelListener extends AnalysisEventListener<ContractLineInfoDTO> {

        private final List<ContractLineInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractLineInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractLineInfoDTO> getData() {
            return data;
        }
    }


}
