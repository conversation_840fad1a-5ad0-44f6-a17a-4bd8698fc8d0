package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomeAccountConfirm VO对象
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@ApiModel(value = "IncomeAccountConfirmVO对象", description = "收入记账明细确认统计表")
@Data
public class IncomeAccountConfirmStatisticsVO implements Serializable {
    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanNum;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeVerifyType;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String certificateSerialNumber;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    private Date documentDate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    private BigDecimal tax;

    /**
     * 含税金额   【税额】+【收入净额】
     */
    @ApiModelProperty(value = "含税金额   【税额】+【收入净额】")
    private BigDecimal amtTax;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String conText;


    @ApiModelProperty(value = "确认状态")
    private String confirmStatus;

    @ApiModelProperty(value = "凭证类型")
    private String voucherType;


}
