import { h } from 'vue';
import { Modal } from 'ant-design-vue';
import { DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';

// 操作栏
export function getActionsList({ state }) {
  return [
    {
      text: '编辑',
      ifShow: true,
      onClick: (record) => {
        // console.log('state', record);
        state.addOrEditRef.openDrawer({
          action: 'edit',
          info: record,
        });
      },
    },
    {
      text: '删除',
      ifShow: true,
      onClick: (record) => {
        Modal.confirm({
          title: '删除确认提示',
          content: '请确认是否删除该条信息？',
          onOk() {
            return new Api('').fetch('', '', 'DELETE');
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
  ];
}

// 列数据
export function getColumns() {
  return [

    {
      title: '工作内容',
      dataIndex: 'content',
      // slots: { customRender: 'name' },
    },
    {
      title: '时间',
      dataIndex: 'taskTime',
      width: 250,
      customRender: ({ record }) => (record.taskTime ? `${record.taskTime}h` : '0h'),
    },

    {
      title: '是否计划内',
      dataIndex: 'thePlan',
      width: 120,
      customRender: ({ record }) => (record.thePlan ? '计划内' : '计划外'),

    },
    {
      title: '关联对象',
      dataIndex: 'relationshipName',
    },

  ];
}
