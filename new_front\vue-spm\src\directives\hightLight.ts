import type { Directive, App } from 'vue';
import { isNull, isUnDef } from 'lyra-component-vue3';
// 搜索关键字高亮显示
const highlight: Directive = (el, binding) => {
  const { value } = binding;
  if (value && typeof value === 'object') {
    let { key, word } = value;
    if (key?.length) {
      // 正则匹配中文、英文关键字高亮，高亮后的关键字和之前的大小写保持一致
      key = `(${key.replace(/([\+\.\*\|\?\-\(\[\^\$])/g, '\\$1').replace(/\s+/g, '|')})`; // 把匹配关键字中的正则符转义
      const regKey = new RegExp(key, 'igm'); // 传  igm  可避免关键词后面的空格造成文字不匹配问题

      // 判断值为null 和 undefined 的情况
      if (isUnDef(word) || isNull(word)) {
        word = '';
      }
      el.innerHTML = word.replace(regKey, "<span style='color:red;'>$1</span>");
    } else {
      if (isUnDef(word) || isNull(word)) {
        word = '';
      }
      el.innerHTML = word;
    }
  }
};

export function setupHighlightDirective(app: App) {
  app.directive('highlight', highlight);
}

export default highlight;
