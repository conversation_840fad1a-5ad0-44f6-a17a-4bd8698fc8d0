package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.zhxu.bs.util.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.ContractPayNodeStatusConfirmDTO;
import com.chinasie.orion.domain.dto.ProjectReceivableDTO;
import com.chinasie.orion.domain.entity.ProjectFundsReceived;
import com.chinasie.orion.domain.entity.ProjectReceivable;
import com.chinasie.orion.domain.entity.Stakeholder;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectReceivableMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ProjectFundsReceivedService;
import com.chinasie.orion.service.ProjectReceivableService;
import com.chinasie.orion.service.StakeholderService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectReceivable 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@Service
public class ProjectReceivableServiceImpl extends OrionBaseServiceImpl<ProjectReceivableMapper, ProjectReceivable> implements ProjectReceivableService {

    @Autowired
    private ProjectReceivableMapper projectReceivableMapper;
    @Autowired
    private ProjectFundsReceivedService projectFundsReceivedService;
    @Autowired
    private StakeholderService stakeholderService;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private LyraFileBO fileBo;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private SearchHelper searchHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectReceivableVO detail(String id, String pageCode) throws Exception {
        ProjectReceivable projectReceivable = projectReceivableMapper.selectById(id);
        ProjectReceivableVO result = BeanCopyUtils.convertTo(projectReceivable, ProjectReceivableVO::new);
        LambdaQueryWrapperX<ProjectFundsReceived> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.in(ProjectFundsReceived::getReceivableId, id);
        List<ProjectFundsReceived> projectFundsReceiveds = projectFundsReceivedService.list(lambdaQueryWrapperX);
        if (StringUtils.isNotBlank(result.getStakeholderId())) {
            Stakeholder stakeholders = stakeholderService.getById(result.getStakeholderId());
            if (ObjectUtil.isNotEmpty(stakeholders)) {
                result.setStakeholderName(stakeholders.getName());
            }
        }
        if (!CollectionUtil.isEmpty(projectFundsReceiveds)) {
            List<String> numbers = projectFundsReceiveds.stream().filter(a -> StrUtil.isNotBlank(a.getInvoiceNumber())).map(ProjectFundsReceived::getInvoiceNumber).collect(Collectors.toList());
            String invoiceNumber = numbers.stream().collect(Collectors.joining(";"));
            result.setInvoiceNumber(invoiceNumber);
        }
        if (StrUtil.isNotBlank(result.getContractNumber()) && StrUtil.isNotBlank(result.getCollectionPoint())) {
            List<ContractPayNodeVO> contractPayNodeVOs = pasFeignService.getContractPayNodeVO(Arrays.asList(result.getCollectionPoint())).getResult();
            Map<String, String> contractPayNodeVOMap = contractPayNodeVOs.stream().collect(Collectors.toMap(ContractPayNodeVO::getId, ContractPayNodeVO::getPayTypeName));

            List<ProjectContractVO> contractVOS = pasFeignService.listByIds(Arrays.asList(result.getContractId())).getResult();
            Map<String, String> contractVOMap = contractVOS.stream().collect(Collectors.toMap(ProjectContractVO::getId, ProjectContractVO::getNumber));

            if (StrUtil.isNotBlank(contractPayNodeVOMap.get(result.getCollectionPoint()))) {
                result.setCollectionPoint(contractPayNodeVOMap.get(result.getCollectionPoint()));
            }
            if (StrUtil.isNotBlank(contractVOMap.get(result.getContractId()))) {
                result.setContractNumber(contractVOMap.get(result.getContractId()));
            }
        }

        if (StrUtil.isNotBlank(result.getCreatorId())) {
            UserVO userVO = userRedisHelper.getUserById(result.getCreatorId());
            if (ObjectUtil.isNotEmpty(userVO)) {
                result.setCreatorName(userVO.getName());
            }
        }
        // 权限设置
        if (StrUtil.isNotBlank(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), ProjectReceivableVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        }
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectReceivableDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectReceivableVO create(ProjectReceivableDTO projectReceivableDTO) throws Exception {
        projectReceivableDTO.setNoAmountReceived(projectReceivableDTO.getAmountReceivable());
        projectReceivableDTO.setFundsReceived(BigDecimal.ZERO);
        ProjectReceivable projectReceivable = BeanCopyUtils.convertTo(projectReceivableDTO, ProjectReceivable::new);
        String code = "";
        if (StrUtil.isNotBlank(projectReceivableDTO.getNumber())) {
            code = codeBo.createCode(ClassNameConstant.PROJECT_RECEIVABLE, ClassNameConstant.NUMBER, true, projectReceivableDTO.getNumber());
        } else {
            code = codeBo.createCode(ClassNameConstant.PROJECT_RECEIVABLE, ClassNameConstant.NUMBER, false, null);
        }
        if (StringUtils.isNotBlank(projectReceivableDTO.getContractId()) && StringUtils.isBlank(projectReceivableDTO.getContractNumber())) {
            ResponseDTO<List<ProjectContractVO>> projectContractR = pasFeignService.listByIds(Arrays.asList(projectReceivableDTO.getContractId()));
            if (ResponseUtils.success(projectContractR) && CollectionUtil.isNotEmpty(projectContractR.getResult())) {
                ProjectContractVO projectContractVO = projectContractR.getResult().get(0);
                projectReceivable.setContractNumber(projectContractVO.getNumber());
            }
        }
        projectReceivable.setNumber(code);
        int insert = projectReceivableMapper.insert(projectReceivable);
        ProjectReceivableVO rsp = BeanCopyUtils.convertTo(projectReceivable, ProjectReceivableVO::new);
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectReceivableDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(rsp.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTRECEIVABLE_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
            searchHelper.sendDataChangeMessage(rsp.getId());
        }
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectReceivableDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectReceivableDTO projectReceivableDTO) throws Exception {
        ProjectReceivable projectReceivable = this.getById(projectReceivableDTO);
        if (ObjectUtil.isEmpty(projectReceivable)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在，请仔细检查");
        }
        //TODO 整改 提取到枚举
        if (projectReceivable.getAmountReceivable().compareTo(projectReceivableDTO.getAmountReceivable()) != 0) {
            BigDecimal bigDecimal = projectReceivableDTO.getAmountReceivable().subtract(projectReceivable.getFundsReceived());
            if (projectReceivable.getFundsReceived().compareTo(BigDecimal.ZERO) == 0) {
                projectReceivableDTO.setStatus(101);
            } else if (bigDecimal.compareTo(BigDecimal.ZERO) == 1) {
                projectReceivableDTO.setStatus(110);
            } else if (bigDecimal.compareTo(BigDecimal.ZERO) == 0 || bigDecimal.compareTo(BigDecimal.ZERO) == -1) {
                projectReceivableDTO.setStatus(111);
                if (StringUtils.isNotBlank(projectReceivable.getCollectionPoint())) {
                    ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO = new ContractPayNodeStatusConfirmDTO();
                    contractPayNodeStatusConfirmDTO.setNodeIdList(Arrays.asList(projectReceivable.getCollectionPoint()));
                    contractPayNodeStatusConfirmDTO.setContractNumber(projectReceivable.getContractNumber());
                    pasFeignService.statusChange(contractPayNodeStatusConfirmDTO);
                }
            }
            projectReceivableDTO.setNoAmountReceived(bigDecimal);
        }
        ProjectReceivable projectReceivable1 = BeanCopyUtils.convertTo(projectReceivableDTO, ProjectReceivable::new);
        if (StringUtils.isNotBlank(projectReceivableDTO.getContractId())) {
            ResponseDTO<List<ProjectContractVO>> projectContractR = pasFeignService.listByIds(Arrays.asList(projectReceivableDTO.getContractId()));
            if (ResponseUtils.success(projectContractR) && CollectionUtil.isNotEmpty(projectContractR.getResult())) {
                ProjectContractVO projectContractVO = projectContractR.getResult().get(0);
                projectReceivable.setContractNumber(projectContractVO.getNumber());
            }
        }
        int update = projectReceivableMapper.updateById(projectReceivable1);
        List<FileVO> oldFiles = fileBo.getFilesByDataId(projectReceivable1.getId());
        List<FileDTO> attachments = Optional.ofNullable(projectReceivableDTO.getAttachments()).orElse(new ArrayList<>());
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        List<String> result = new ArrayList<>();
        attachments.forEach(f -> {
            f.setDataId(projectReceivable1.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTRECEIVABLE_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        fileBo.addBatch(attachments);
        searchHelper.sendDataChangeMessage(projectReceivable1.getId());
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<ProjectFundsReceived> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.in(ProjectFundsReceived::getReceivableId, ids);
        List<ProjectFundsReceived> projectFundsReceiveds = projectFundsReceivedService.list(lambdaQueryWrapperX);
        if (!CollectionUtil.isEmpty(projectFundsReceiveds)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据存在实收，请仔细检查");
        }
        int delete = projectReceivableMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectReceivableVO> pages(Page<ProjectReceivableDTO> pageRequest) throws Exception {
        Page<ProjectReceivableVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), Page::setHeadAuthList, new ArrayList<>());

        Page<ProjectReceivable> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectReceivable::new));
        ProjectReceivableDTO projectReceivableDTO = pageRequest.getQuery();
        LambdaQueryWrapperX<ProjectReceivable> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        if (ObjectUtil.isNotEmpty(projectReceivableDTO)) {
            lambdaQueryWrapperX.eqIfPresent(ProjectReceivable::getProjectId, projectReceivableDTO.getProjectId());
//            if(StrUtil.isNotBlank(projectReceivableDTO.getName())){
//                lambdaQueryWrapperX.like(ProjectReceivable::getName,projectReceivableDTO.getName()).or().like(ProjectReceivable::getNumber,projectReceivableDTO.getName());
//            }
        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, lambdaQueryWrapperX);
        }
        PageResult<ProjectReceivable> page = projectReceivableMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        //Page<ProjectReceivableVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectReceivableVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectReceivableVO::new);
        if (CollectionUtil.isEmpty(vos)) {
            return resultPage;
        }
        //获取客户名称
        List<String> stakeholderIds = vos.stream().map(ProjectReceivableVO::getStakeholderId).collect(Collectors.toList());
        List<Stakeholder> stakeholders = stakeholderService.listByIds(stakeholderIds);
        Map<String, String> stakeholderMap = stakeholders.stream().collect(Collectors.toMap(Stakeholder::getId, Stakeholder::getName));
        //获取发票编码
        List<String> ids = vos.stream().map(ProjectReceivableVO::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectFundsReceived> fundsReceivedLambdaQueryWrapperX = new LambdaQueryWrapperX();
        fundsReceivedLambdaQueryWrapperX.in(ProjectFundsReceived::getReceivableId, ids);
        List<ProjectFundsReceived> projectFundsReceiveds = projectFundsReceivedService.list(fundsReceivedLambdaQueryWrapperX);
        Map<String, List<ProjectFundsReceived>> map = projectFundsReceiveds.stream().filter(a -> StringUtils.isNotBlank(a.getReceivableId())).collect(Collectors.groupingBy(ProjectFundsReceived::getReceivableId));
        List<String> stringList = vos.stream().map(ProjectReceivableVO::getCollectionPoint).collect(Collectors.toList());
        List<ContractPayNodeVO> contractPayNodeVOs = pasFeignService.getContractPayNodeVO(stringList).getResult();
        Map<String, String> contractPayNodeVOMap = contractPayNodeVOs.stream().collect(Collectors.toMap(ContractPayNodeVO::getId, ContractPayNodeVO::getPayTypeName));

        List<String> contractIds = vos.stream().map(ProjectReceivableVO::getContractNumber).collect(Collectors.toList());
        List<ProjectContractVO> contractVOS = pasFeignService.listByIds(contractIds).getResult();
        Map<String, String> contractVOMap = contractVOS.stream().collect(Collectors.toMap(ProjectContractVO::getId, ProjectContractVO::getNumber));

        for (ProjectReceivableVO projectReceivableVO : vos) {
            List<ProjectFundsReceived> list = map.get(projectReceivableVO.getId());
            if (!CollectionUtil.isEmpty(list)) {
                List<String> numbers = list.stream().filter(a -> StrUtil.isNotBlank(a.getInvoiceNumber())).map(ProjectFundsReceived::getInvoiceNumber).collect(Collectors.toList());
                String invoiceNumber = numbers.stream().collect(Collectors.joining(";"));
                projectReceivableVO.setInvoiceNumber(invoiceNumber);
            }
            if (StrUtil.isNotBlank(contractPayNodeVOMap.get(projectReceivableVO.getCollectionPoint()))) {
                projectReceivableVO.setCollectionPoint(contractPayNodeVOMap.get(projectReceivableVO.getCollectionPoint()));
            }
            if (StrUtil.isNotBlank(contractVOMap.get(projectReceivableVO.getContractNumber()))) {
                projectReceivableVO.setContractNumber(contractVOMap.get(projectReceivableVO.getContractNumber()));
            }
            projectReceivableVO.setStakeholderName(stakeholderMap.get(projectReceivableVO.getStakeholderId()));
        }
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(vos);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), vos, ProjectReceivableVO::getId, ProjectReceivableVO::getDataStatus, ProjectReceivableVO::setRdAuthList,
                ProjectReceivableVO::getCreatorId,
                ProjectReceivableVO::getModifyId,
                ProjectReceivableVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setContent(vos);
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectReceivableVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectReceivableVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public List<ProjectReceivableVO> getList(ProjectReceivableDTO projectReceivableDTO) throws Exception {
        LambdaQueryWrapperX<ProjectReceivable> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eqIfPresent(ProjectReceivable::getProjectId, projectReceivableDTO.getProjectId());
        List<ProjectReceivable> projectReceivables = this.list(lambdaQueryWrapperX);

        List<ProjectReceivableVO> projectReceivableVOS = BeanCopyUtils.convertListTo(projectReceivables, ProjectReceivableVO::new);
        if (!CollectionUtils.isBlank(projectReceivableVOS)) {
            List<String> stringList = projectReceivableVOS.stream().map(ProjectReceivableVO::getCollectionPoint).collect(Collectors.toList());
            List<ContractPayNodeVO> contractPayNodeVOs = pasFeignService.getContractPayNodeVO(stringList).getResult();
            Map<String, String> contractPayNodeVOMap = contractPayNodeVOs.stream().collect(Collectors.toMap(ContractPayNodeVO::getId, ContractPayNodeVO::getPayTypeName));
            for (ProjectReceivableVO projectReceivableVO : projectReceivableVOS) {
                projectReceivableVO.setCollectionPointName(contractPayNodeVOMap.get(projectReceivableVO.getCollectionPoint()));
            }
        }

        return projectReceivableVOS;
    }


    @Override
    public List<String> importFiles(String id, List<FileDTO> files) throws Exception {
        List<FileVO> oldFiles = fileBo.getFilesByDataId(id);
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        List<String> result = new ArrayList<>();
        files.forEach(f -> {
            f.setDataId(id);
            f.setDataType(FileConstant.FILETYPE_PROJECTRECEIVABLE_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        fileBo.addBatch(files);
        return result;
    }

    /**
     * 列表
     * <p>
     * * @param pageRequest
     */
    public List<ProjectReceivableValueVO> getProjectReceivableVOList(String id) throws Exception {
        LambdaQueryWrapperX<ProjectReceivable> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectReceivable.class);
        lambdaQueryWrapperX.eqIfPresent(ProjectReceivable::getCollectionPoint, id);
        List<ProjectReceivable> projectReceivables = this.list(lambdaQueryWrapperX);
        List<ProjectReceivableValueVO> projectReceivableVOS = BeanCopyUtils.convertListTo(projectReceivables, ProjectReceivableValueVO::new);
        return projectReceivableVOS;
    }
}
