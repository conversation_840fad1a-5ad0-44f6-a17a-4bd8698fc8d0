package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectPlaceDTO;
import com.chinasie.orion.domain.entity.ProjectPlace;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.vo.ProjectPlaceVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

/**
 * <p>
 * ProjectPlace 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
public interface ProjectPlaceService extends OrionBaseService<ProjectPlace> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectPlaceVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectPlaceDTO
     */
    String create(ProjectPlaceDTO projectPlaceDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectPlaceDTO
     */
    Boolean edit(ProjectPlaceDTO projectPlaceDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectPlaceVO> pages(Page<ProjectPlaceDTO> pageRequest) throws Exception;


    /**
     * 根据项目id获取地址
     * <p>
     * * @param id
     */
    List<ProjectPlace> getPlaceByProjectId(String id);

    /**
     * 根据项目ids获取地址
     * <p>
     * * @param ids
     */
    List<ProjectPlace> getPlaceByProjectIds(List<String> ids);
}
