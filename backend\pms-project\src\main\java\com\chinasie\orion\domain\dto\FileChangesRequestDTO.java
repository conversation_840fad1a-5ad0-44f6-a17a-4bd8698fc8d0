package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 文件变动数据获取请求DTO.
 *
 * <AUTHOR>
 */
public class FileChangesRequestDTO implements Serializable {
    /**
     * 获取变动信息的baseTime.
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date baseTime;

    /**
     * 文件类型.
     */
    private List<String> dataTypes;

    /**
     * 获取的文件数量.
     */
    private Integer count = 30;

    public Date getBaseTime() {
        return baseTime;
    }

    public void setBaseTime(Date baseTime) {
        this.baseTime = baseTime;
    }

    public List<String> getDataTypes() {
        return dataTypes;
    }

    public void setDataTypes(List<String> dataTypes) {
        this.dataTypes = dataTypes;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
