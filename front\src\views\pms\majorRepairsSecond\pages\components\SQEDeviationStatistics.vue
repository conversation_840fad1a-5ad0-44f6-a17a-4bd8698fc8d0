<script setup lang="ts">
import { STable } from '@surely-vue/table';
import {
  inject, onMounted, ref, Ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import { getDictByNumber } from 'lyra-component-vue3';

const detailsData: Record<string, any> = inject('detailsData');

const columns: Ref<any[]> = ref([]);

function customRender(item, { column, record }) {
  return record?.rowStatisticsVOList?.find((data) => data.dictNumber === item.number)?.[column.dataIndex] || 0;
}

async function getTableColumns() {
  const result = await getDictByNumber('pms_pyramid_category');
  const arr: any[] = result?.map((item: any) => ({
    title: item.name,
    dataIndex: item.number,
    children: [
      {
        title: '当日安全偏差',
        dataIndex: 'safetyDayCount',
        align: 'center',
        width: 110,
        customRender: (args) => customRender(item, args),
      },
      {
        title: '当日质量偏差',
        dataIndex: 'qualityDayCount',
        align: 'center',
        width: 110,
        customRender: (args) => customRender(item, args),
      },
      {
        title: '累计安全偏差',
        dataIndex: 'safetyCount',
        align: 'center',
        width: 110,
        customRender: (args) => customRender(item, args),
      },
      {
        title: '累计质量偏差',
        dataIndex: 'qualityCount',
        align: 'center',
        width: 110,
        customRender: (args) => customRender(item, args),
      },
    ],
  })) || [];
  columns.value = [
    {
      title: '项目',
      dataIndex: 'name',
      width: 150,
      align: 'center',
    },
  ].concat(arr);
}

watchEffect(() => {
  getTableColumns();
});

const data: Ref<any[]> = ref([]);
const loading: Ref<boolean> = ref(false);

async function getData() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/safety-quality-env/deviation/statistics?majorRepairTurn=${detailsData?.repairRound}`).fetch('', '', 'POST');
    data.value = result || [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getData();
});
</script>

<template>
  <s-table
    bordered
    rowKey="uniqueId"
    size="small"
    :loading="loading"
    :columns="columns"
    :scroll="{ y: 300 }"
    :pagination="false"
    :data-source="data"
  />
</template>

<style scoped lang="less">

</style>
