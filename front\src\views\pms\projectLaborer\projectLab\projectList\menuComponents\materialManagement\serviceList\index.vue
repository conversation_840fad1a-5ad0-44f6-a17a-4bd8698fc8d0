<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_06_03_01_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openServicePlanDrawer(true, { type: 'add', itemData:{}, projectId: props.id })"
        >
          创建计划
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_06_03_01_button_03',powerData)"
          type="primary"
          icon="sie-icon-chongzhi"
          :disabled="disabledBtn"
          @click="onRefreshData"
        >
          刷新
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_06_03_01_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <ServicePlanDrawer
      @upTableDate="upTableDate"
      @register="modalServicePlanRegister"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  ref, toRefs, nextTick, onMounted, h, computed, inject, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
import { getServicelist } from '/@/views/pms/api';
import dayjs from 'dayjs';
import Tags from './components/Tags.vue';
import ServicePlanDrawer from './components/servicePlanDrawer.vue';
const [modalServicePlanRegister, { openDrawer: openServicePlanDrawer }] = useDrawer();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const tableRef = ref(null);
const selectRows = ref([]);
const disabledBtn = ref(false);
const powerData:Ref<any[]> = ref([]);
const baseTableOption = {
  api: async (params) => {
    const result:Record<string, any> = await getServicelist({
      ...params,
      query: { projectId: props.id },
      power: {
        pageCode: 'PMS0004',
        containerCode: 'PMS_XMXQ_container_06_03_02',
        headContainerCode: 'PMS_XMXQ_container_06_03_01',
      },
    });
    powerData.value = result.headAuthList;
    return result;
  },
  rowSelection: {},
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_MATERIALMANAGEMENT_SERVICELIST_INDEX',
  smallSearchField: [
    'number',
    'goods_service_number',
    'description',
  ],
  columns: [
    {
      title: '物资/服务计划编号',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 50,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 80,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '物资/服务编码',
      dataIndex: 'goodsServiceNumber',
      width: 150,
    },
    {
      title: '物资/服务描述',
      dataIndex: 'description',
      width: 150,
    },
    {
      title: '规格型号',
      dataIndex: 'normsModel',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '服务期限',
      dataIndex: 'serviceTerm',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '需求数量',
      dataIndex: 'demandAmount',
      width: 80,
    },
    {
      title: '入库数量',
      dataIndex: 'totalStoreAmount',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '需求日期',
      dataIndex: 'demandTime',
      width: 200,
      customRender({ text, record }) {
        return h('div', { class: 'flex flex-ac' }, [h('span', { class: 'date m-b-r' }, dayjs(text).format('YYYY-MM-DD')), h(Tags, { data: dateStatus.value(record) })]);
      },
    },
    {
      title: '需求人',
      dataIndex: 'demandPersonName',
      width: 80,

    },
    {
      title: '采购计划编号',
      dataIndex: 'buyPlanId',
      width: 150,
      // customRender({ text }) {
      //   return text || '--';
      // },
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => text) ? 'action-btn' : '',
            title: text,
            onClick(record) {
              // console.log('点击的编码', record);
            },
          },
          text,
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      minWidth: 200,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_03_02_button_01', record.rdAuthList),
      onClick(record) {
        router.push({
          name: 'MaterialServicesDetails',
          query: {
            id: record.id,
            projectId: props.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_03_02_button_02', record.rdAuthList),
      onClick(record) {
        openServicePlanDrawer(true, {
          type: 'edit',
          itemData: record,
          projectId: props.id,
        });
      },
    },
    {
      text: '删除',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_03_02_button_03', record.rdAuthList),
      modal: (record) => batchDelete([record.id]),
    },
  ],
};
// 刷新数据
async function onRefreshData() {
  upTableDate();
}
// 判断是否已逾期(如果已经入库则不限时预警)
const dateStatus = computed(() => (record) => {
  const isStore = Number(record?.dataStatus?.statusValue) === 160;
  const { demandTime } = record;// 需求日期
  if (isStore || !demandTime) {
    return [];
  }
  if (dayjs(demandTime).format('YYYY-MM-DD') < dayjs().format('YYYY-MM-DD')) {
    return [
      {
        color: 'error',
        label: `逾期${dayjs().diff(dayjs(demandTime), 'day')}天`,
      },
    ];
  }
  if (dayjs(demandTime).subtract(15, 'day') <= dayjs()) {
    return [
      {
        color: 'warning',
        label: '逾期预警',
      },
    ];
  }
});

function selectionChange({ rows }) {
  selectRows.value = rows;
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的评价？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/goods-service-plan').fetch(ids, '', 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function upTableDate() {
  tableRef.value?.reload();
}
</script>
<style scoped lang="less">
</style>
