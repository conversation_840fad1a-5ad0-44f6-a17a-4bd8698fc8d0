package com.chinasie.orion.domain.dto.relationOrgToMaterial;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.domain.entity.MaterialManage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * JobMaterial DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@ApiModel(value = "OrgMaterialRelationDTO对象", description = "组织相关的物资")
@Data
@ExcelIgnoreUnannotated
public class OrgMaterialRelationDTO extends MROLogDTO implements Serializable {

    @ApiModelProperty(value = "大修组织id")
    private String repairOrgId;

    @ApiModelProperty(value = "大修基地")
    private String baseCode;

    private List<MaterialManage> materialManageList;
}
