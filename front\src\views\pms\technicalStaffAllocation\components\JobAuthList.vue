<script setup lang="ts">
import {
  OrionTable, openFile, isPower, IOrionTableActionItem, BasicTableAction, BasicButton,
} from 'lyra-component-vue3';
import {
  computed,
  ComputedRef,
  h, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { Modal, Popover } from 'ant-design-vue';
import { openFormDrawer } from '../utils';
import { AuthorizationEdit } from './index';

const powerCodePrefix: string = inject('powerCodePrefix');
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  height: 300,
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '所属基地',
      dataIndex: 'baseName',
    },
    {
      title: '作业岗位',
      dataIndex: 'jobPostName',
    },
    {
      title: '授权起始日期',
      dataIndex: 'startData',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '授权到期日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '授权记录',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        if (isPower(`${powerCodePrefix}_container_02_03_button_04`, powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/personJobPostAuthorize').fetch({
    ...params,
    query: {
      userCode: detailsData?.userCode,
    },
  }, 'page', 'POST'),
};
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加授权',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: `${powerCodePrefix}_container_02_04_button_01`,
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: `${powerCodePrefix}_container_02_04_button_02`,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(AuthorizationEdit, { userCode: detailsData?.userCode }, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower(`${powerCodePrefix}_container_02_03_button_01`, powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower(`${powerCodePrefix}_container_02_03_button_02`, powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(AuthorizationEdit, record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/personJobPostAuthorize').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}

function updateTable() {
  tableRef.value?.reload();
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      v-if="detailsData.userStatus === '在职'"
      #toolbarLeft
    >
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.powerCode]"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template
      v-if="detailsData.userStatus === '在职'"
      #actions="{record}"
    >
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
