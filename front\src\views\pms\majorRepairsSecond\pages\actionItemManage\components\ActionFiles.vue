<script setup lang="ts">
import { BasicCard, UploadList } from 'lyra-component-vue3';
import {
  inject, reactive, readonly, Ref,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', readonly({}));
const powerData: Ref = inject('powerData');

const uploadPowerCode = {
  download: 'PMS_ZJHTLBXQ_container_02_HB_01_button_02',
  preview: 'PMS_ZJHTLBXQ_container_02_HB_01_button_01',
};
</script>

<template>
  <BasicCard
    title="行动项管理附件"
    :is-border="false"
  >
    <UploadList
      :height="300"
      :edit="false"
      :is-file-edit="false"
      :is-spacing="false"
      :powerData="powerData"
      :powerCode="uploadPowerCode"
      :listData="detailsData.fileList"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
