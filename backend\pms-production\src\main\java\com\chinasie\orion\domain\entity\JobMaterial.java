package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * JobMaterial Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@TableName(value = "pmsx_job_material")
@ApiModel(value = "JobMaterialEntity对象", description = "作业相关的物资")
@Data

public class JobMaterial extends  ObjectEntity  implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 存放地编码
     */
    @ApiModelProperty(value = "存放地编码")
    @TableField(value = "storage_place_code")
    private String storagePlaceCode;

    /**
     * 存放地名称
     */
    @ApiModelProperty(value = "存放地名称")
    @TableField(value = "storage_place_name")
    private String storagePlaceName;

    /**
     * 资产编码/条形码
     */
    @ApiModelProperty(value = "资产编码/条形码")
    @TableField(value = "number")
    private String number;

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    @TableField(value = "asset_type")
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @TableField(value = "asset_code")
    private String assetCode;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @TableField(value = "asset_name")
    private String assetName;

    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center_name")
    private String costCenterName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "specification_model")
    private String specificationModel;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    @TableField(value = "stock_num")
    private Integer stockNum;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "demand_num")
    private Integer demandNum;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @TableField(value = "next_verification_date")
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @TableField(value = "is_verification")
    private Boolean isVerification;

    /**
     * 物质所在基地编码
     */
    @ApiModelProperty(value = "物质所在基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 物质所在基地名称
     */
    @ApiModelProperty(value = "物质所在基地名称")
    @TableField(value = "base_name")
    private String baseName;

    @ApiModelProperty(value = "物资管理id")
    @TableField(value = "material_id")
    private String materialId;

    @ApiModelProperty(value = "物资管理台账id")
    @TableField(value = "material_ledger_id")
    private String materialLedgerId;

    @ApiModelProperty(value = "计划起始时间")
    @TableField(value = "plan_begin_date")
    private Date planBeginDate;
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "plan_end_date")
    private Date planEndDate;

    @ApiModelProperty(value = "实际起始时间")
    @TableField(value = "act_begin_date")
    private Date actBeginDate;
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "act_end_date")
    private Date actEndDate;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    @TableField(value = "tool_status")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    @TableField(value = "maintenance_cycle")
    private Integer maintenanceCycle;
}
