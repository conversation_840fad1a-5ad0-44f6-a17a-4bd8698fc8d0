DROP TABLE IF EXISTS `pmsx_procure_pkg_group`;
CREATE TABLE `pmsx_procure_pkg_group`  (
                                           `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                           `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                           `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                           `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
                                           `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                           `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                           `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                           `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '平台ID',
                                           `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务组织Id',
                                           `status` int(11) NULL DEFAULT NULL COMMENT '状态',
                                           `logic_status` int(11) NULL DEFAULT NULL COMMENT '逻辑删除字段',
                                           `group_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '包组编号',
                                           `group_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '包组名称',
                                           `bidding_rounds` int(11) NULL DEFAULT NULL COMMENT '报价轮次',
                                           `bidding_begin_time` datetime NULL DEFAULT NULL COMMENT '报价开始时间',
                                           `bidding_end_time` datetime NULL DEFAULT NULL COMMENT '报价截止时间',
                                           `bidding_status` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报价状态',
                                           `requirement_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '需求编号',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '采购项目包组' ROW_FORMAT = Dynamic;