package com.chinasie.orion.management.service.impl;


import com.chinasie.orion.management.domain.entity.ReqDetail;
import com.chinasie.orion.management.service.ReqDetailService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.management.repository.ReqDetailMapper;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ReqDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 17:18:32
 */
@Service
@Slf4j
public class ReqDetailServiceImpl extends OrionBaseServiceImpl<ReqDetailMapper, ReqDetail> implements ReqDetailService {



}
