package com.chinasie.orion.constant;


public enum InvestmentSchemeStatus {
    WRITE(100, "编制中"),
    ClOSE(111, "已关闭"),
    PROCESS(107, "进行中");
    private final Integer code;
    private final String value;

    InvestmentSchemeStatus(Integer code, String value) {
        this.value = value;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
