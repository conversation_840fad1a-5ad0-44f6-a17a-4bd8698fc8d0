package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.QuestionReplyManagementDTO;
import com.chinasie.orion.domain.entity.QuestionReplyManagement;
import com.chinasie.orion.domain.vo.QuestionReplyManagementVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * QuestionReplyManagement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
public interface QuestionReplyManagementService extends OrionBaseService<QuestionReplyManagement> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    QuestionReplyManagementVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param questionReplyManagementDTO
     */
    String create(QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception;

    /**
     * 批量新增
     * <p>
     * * @param questionReplyManagementDTOS
     */
    boolean addList(List<QuestionReplyManagementDTO> questionReplyManagementDTOS);
    /**
     * 根据问题编码查询
     * <p>
     * * @param questionReplyManagementDTO
     */
    List<QuestionReplyManagementVO> getListByNumber(String number) throws Exception;


    /**
     * 编辑
     * <p>
     * * @param questionReplyManagementDTO
     */
    Boolean edit(QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<QuestionReplyManagementVO> pages(Page<QuestionReplyManagementDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<QuestionReplyManagementVO> vos) throws Exception;
}
