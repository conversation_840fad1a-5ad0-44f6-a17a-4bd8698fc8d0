package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectPurchaseStatisticsVO对象", description = "采购统计表")
public class ProjectPurchaseStatisticsVO {
    @ApiModelProperty(value = "项目id")
    private String id;
    @ApiModelProperty(value = "负责人名称")
    private String rspuserName;
    @ApiModelProperty(value = "负责人")
    private String rspuser;
    @ApiModelProperty(value = "展示时间")
    private String showTime;
    @ApiModelProperty(value = "时间")
    private Date timeValue;
    @ApiModelProperty(value = "未审核数量")
    private Integer noAuditCount=0;
    @ApiModelProperty(value = "审核中数量")
    private Integer underReviewCount=0;
    @ApiModelProperty(value = "已审核数量")
    private Integer reviewedCount=0;
    @ApiModelProperty(value = "已关闭数量")
    private Integer closeCount=0;
}
