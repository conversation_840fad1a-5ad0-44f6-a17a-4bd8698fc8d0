<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm, BasicCard, OrionTable, openFile,
} from 'lyra-component-vue3';
import { h } from 'vue';
import { Popover } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  data: any[]
}>();

const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
  resizeHeightOffset: 65,
};

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '培训等效认定信息',
        isSpacing: false,
        isBorder: false,
      }, h(OrionTable, {
        options: tableOptions,
        dataSource: props?.data,
        columns: [
          {
            title: '基地名称',
            dataIndex: 'equivalentBaseName',
          },
          {
            title: '等效认定时间',
            dataIndex: 'equivalentDate',
            customRender({ text }) {
              const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
              return h('div', {
                class: 'flex-te',
                title: str,
              }, str);
            },
          },
          {
            title: '等效认定书',
            dataIndex: 'fileVOList',
            customRender({ text }) {
              return h(Popover, { title: '附件' }, {
                default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
                content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
                  class: 'action-btn',
                  onClick() {
                    openFile(item);
                  },
                }, item.name)),
              });
            },
          },
        ],
      }));
    },
  },
];

const [register, { validate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
