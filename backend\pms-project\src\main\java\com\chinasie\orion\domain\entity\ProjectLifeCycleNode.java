package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生命周期
 */
@Data
@TableName(value = "pms_project_lifecycle_node")
public class ProjectLifeCycleNode implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creatorId;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    private String ownerId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyId;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;
    @ApiModelProperty("备注")

    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    private String orgId;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;


    @ApiModelProperty("是否需要流程模版")
    @TableField("is_attachment")
    private Boolean isAttachment;

    /**
     * NodeKey
     */
    @TableField("node_key")
    private String nodeKey;

    /**
     * 节点名.
     */
    @TableField("name")
    private String name;

    /**
     * 节点名.
     */
    @TableField("project_type")
    private String projectType;

    /**
     * 节点内容.
     */
    @TableField("content")
    private String content;

    /**
     * 节点附件.
     */
    @TableField("attachments")
    private String attachments;

    /**
     * 节点操作.
     */
    @TableField("actions")
    private String actions;

    /**
     * 节点类型
     */
    @TableField("node_type")
    private String nodeType;

    /**
     * 纵坐标
     */
    @TableField("ordinate")
    private Integer ordinate;

    /**
     * 横坐标
     */
    @TableField("abscissa")
    private Integer abscissa;
}
