<script setup lang="ts">

import { get } from 'lodash-es';

const props = withDefaults(defineProps<{
  colCfg:any
  currIdx: number,
  idx:number
}>(), {
  colCfg: () => ({}),
  currIdx: 0,
  idx: 0,
});
const emits = defineEmits(['onSelect']);

function handleSelect() {
  emits('onSelect');
}
</script>

<template>
  <div
    class="tr-col"
    @click.stop.prevent="handleSelect"
  >
    <div class="col-name">
      {{ colCfg.rspUserNames }}
    </div>
    <div class="col-feed-status">
      {{ get(colCfg,'dataStatus.name') }}
    </div>
    <!--    <div class="col-status">-->
    <!--      {{ colCfg.status }}-->
    <!--    </div>-->
  </div>
</template>

<style scoped lang="less">
.tr-col{
  min-width: 70px;
  flex-shrink: 0;
  padding: 6px;
  background: rgba(231,237,252,1);
  border-radius: 4px;
  height: 100%;
  cursor: pointer;
  margin-right: 6px;
  &.tr-col-active{
    background: rgba(255,255,255,1);
  }
  &:last-child{
    margin-right: 0;
  }
  .col-name{
    text-align: center;
    font-size: 14px;
    color:#333;
    font-weight: bold;
  }
  .col-feed-status,.col-status{
    text-align: center;
    font-size: 13px;
    color:#666;
  }
}
</style>