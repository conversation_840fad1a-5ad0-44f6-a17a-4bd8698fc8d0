package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *  项目个人中心统计
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/23
 */
@Data
@ApiModel(value = "ProjectCenterStatVO对象", description = "项目个人中心统计")
public class ProjectCenterStatVO implements Serializable {
    /**
     * 待立项项目数
     */
    @ApiModelProperty(value = "待立项里程碑数")
    private Integer pendingProjectInitiationCount = 0;
    /**
     * 已立项项目数
     */
    @ApiModelProperty(value = "已立项项目数")
    private Integer alreadyProjectInitiationCount = 0;
    /**
     * 已关闭项目数
     */
    @ApiModelProperty(value = "已关闭项目数")
    private Integer closeProjectInitiationCount = 0;

    /**
     * 执行中里程碑个数
     */
    @ApiModelProperty(value = "执行中里程碑个数")
    private Integer progressMilestoneCount = 0;
    /**
     * 执行中里程碑
     */
    @ApiModelProperty(value = "执行中里程碑")
    private MilestoneData progressMilestone;

    /**
     * 临期里程碑个数
     */
    @ApiModelProperty(value = "临期里程碑个数")
    private Integer adventMilestoneCount = 0;
    /**
     * 临期里程碑
     */
    @ApiModelProperty(value = "临期里程碑")
    private MilestoneData adventMilestoneData;

    /**
     * 逾期里程碑个数
     */
    @ApiModelProperty(value = "逾期里程碑个数")
    private Integer overdueMilestoneCount = 0;
    /**
     * 逾期里程碑
     */
    @ApiModelProperty(value = "逾期里程碑")
    private MilestoneData overdueMilestoneData;
    /**
     * 金额完成率
     */
    @ApiModelProperty(value = "金额完成率")
    private BigDecimal amountCompletionRate;
    /**
     * 个数完成率
     */
    @ApiModelProperty(value = "个数完成率")
    private BigDecimal countCompletionRate;

    @ApiModelProperty(value = "头部里程碑数据")
    private PlanMilestoneData planMilestoneData;

    @Data
    @ApiModel(value = "MilestoneData对象", description = "里程碑数据")
    public static class MilestoneData implements Serializable{
        /**
         * 风险个数
         */
        @ApiModelProperty(value = "风险个数")
        private Integer riskCount = 0;
        /**
         * 问题个数
         */
        @ApiModelProperty(value = "问题个数")
        private Integer problemCount = 0;
        /**
         * 问题转计划个数
         */
        @ApiModelProperty(value = "问题转计划个数")
        private Integer problemToPlanCount = 0;
        /**
         * 未完成个数
         */
        @ApiModelProperty(value = "未完成个数")
        private Integer unfinishedCount = 0;
        /**
         * 临期个数
         */
        @ApiModelProperty(value = "临期个数")
        private Integer adventCount = 0;
        /**
         * 逾期个数
         */
        @ApiModelProperty(value = "逾期个数")
        private Integer overdueCount = 0;
    }

    @Data
    @ApiModel(value = "PlanMilestoneData", description = "计划里程碑")
    public static class PlanMilestoneData implements Serializable{
        @ApiModelProperty(value = "已完成里程碑数")
        private Integer completeMilestoneCount = 0;

        @ApiModelProperty(value = "未完成里程碑数")
        private Integer noCompleteMilestoneCount = 0;

        @ApiModelProperty(value = "执行中里程碑数")
        private Integer progressMilestoneCount = 0;

        @ApiModelProperty(value = "临期里程碑数")
        private Integer adventMilestoneCount = 0;

        @ApiModelProperty(value = "逾期里程碑数")
        private Integer overdueMilestoneCount = 0;

    }
}
