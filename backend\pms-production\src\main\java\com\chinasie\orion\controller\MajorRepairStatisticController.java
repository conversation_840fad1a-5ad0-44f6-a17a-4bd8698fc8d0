package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.JobManageDTO;
import com.chinasie.orion.domain.dto.MajorPersonStatisticDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanBoardStatisticDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanDTO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.*;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/majorRepairStatistic")
@Api(tags = "大修统计")
public class MajorRepairStatisticController {

    @Autowired
    private MajorRepairStatisticService majorRepairStatisticService;

    /**
     * 大修人员统计
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员统计")
    @RequestMapping(value = "/getPerson", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【大修统计】-【人员统计】【{{#jobManageDTO.repairRound}}】", type = "MajorPersonStatistic", subType = "大修统计", bizNo = "{{#jobManageDTO.repairRound}}")
    public ResponseDTO<MajorPersonStatisticDTO> getPerson(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        MajorPersonStatisticDTO rsp = majorRepairStatisticService.getPersonStatistic(jobManageDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 大修物资统计
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资统计")
    @RequestMapping(value = "/getMaterial", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【大修统计】-【物资统计】【{{#jobManageDTO.repairRound}}】", type = "MajorPersonStatistic", subType = "大修统计", bizNo = "{{#jobManageDTO.repairRound}}")
    public ResponseDTO<MajorPersonStatisticDTO> getMaterial(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        MajorPersonStatisticDTO rsp = majorRepairStatisticService.getMaterialStatistic(jobManageDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "大修指挥部——角色管理")
    @RequestMapping(value = "/roleManager", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【大修统计】-【大修信息统计】【{{#jobManageDTO.repairRound}}】", type = "MajorPersonStatistic", subType = "大修统计", bizNo = "{{#jobManageDTO.repairRound}}")
    public ResponseDTO<MajorPersonStatisticDTO> roleManager(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        MajorPersonStatisticDTO rsp = majorRepairStatisticService.getPersonStatistic(jobManageDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "大修统计日历概况")
    @RequestMapping(value = "/getMajorRepair", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【大修统计】-【大修统计日历概况】【{{#jobManageDTO.repairRound}}】", type = "MajorPersonStatistic", subType = "大修统计", bizNo = "{{#jobManageDTO.repairRound}}")
    public ResponseDTO<MajorRepairPlanBoardStatisticDTO> getMajorStatistic(@RequestBody MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        MajorRepairPlanBoardStatisticDTO rsp = majorRepairStatisticService.getMajorStatistic(majorRepairPlanDTO);
        return new ResponseDTO<>(rsp);
    }

}
