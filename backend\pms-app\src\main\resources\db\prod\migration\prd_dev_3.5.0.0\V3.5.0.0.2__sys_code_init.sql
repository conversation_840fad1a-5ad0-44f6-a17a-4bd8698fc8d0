INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11795337077320314880', 'LXFX', NULL, '项目立项风险策划编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:11:30', '314j1000000000000000000', '2024-05-28 15:10:48', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'a62afdc4c78b43f4952258391af8bd77', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1795339229967151104', 'LXFX', '0', '9hi11795337077320314880', '', 'fixedValue', '1', 'LXFX', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:20:03', '314j1000000000000000000', '2024-05-28 14:20:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1795340253259886592', '年份', '0', '9hi11795337077320314880', '', 'DATE_YYYY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:24:07', '314j1000000000000000000', '2024-05-28 14:24:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1795340393471275008', '固定符二', '0', '9hi11795337077320314880', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:24:40', '314j1000000000000000000', '2024-05-28 14:24:40', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1795342519714967552', '流水号', '0', '9hi11795337077320314880', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:33:07', '314j1000000000000000000', '2024-05-28 14:33:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1795342701240250368', '固定符一', '0', '9hi11795337077320314880', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 14:33:50', '314j1000000000000000000', '2024-05-28 14:33:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);

INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1795369609982111744', 'number', '9hi11795337077320314880', 'ProjectApprovalRiskPlan', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-28 16:20:46', '314j1000000000000000000', '2024-05-28 16:20:46', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');


INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11796541840028295168', 'MARKETCONTRACT', NULL, '市场合同编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 21:58:47', '314j1000000000000000000', '2024-05-31 21:58:59', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'fb00ab4ede794f51b7add9023042991c', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');

INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1796542501491007488', '前缀', '0', '9hi11796541840028295168', '', 'fixedValue', '1', 'MC', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 22:01:25', '314j1000000000000000000', '2024-05-31 22:01:25', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1796542605815930880', '前缀分隔符', '0', '9hi11796541840028295168', '', 'fixedValue', '1', '-', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 22:01:50', '314j1000000000000000000', '2024-05-31 22:01:50', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1796542686132658176', '日期', '0', '9hi11796541840028295168', '', 'DATE_YYYY', '1', '', '0', 3, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 22:02:09', '314j1000000000000000000', '2024-05-31 22:02:09', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1796542784434561024', '日期分隔符', '0', '9hi11796541840028295168', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 22:02:33', '314j1000000000000000000', '2024-05-31 22:02:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1796542927976226816', '序号', '0', '9hi11796541840028295168', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '0', 5, '', '0', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 22:03:07', '314j1000000000000000000', '2024-05-31 22:03:07', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);


INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1796542091875278848', 'number', '9hi11796541840028295168', 'MarketContract', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-31 21:59:47', '314j1000000000000000000', '2024-05-31 21:59:47', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
