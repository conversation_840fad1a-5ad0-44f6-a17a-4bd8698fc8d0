<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const detailsData: Record<string, any> = inject('detailsData');
const basicInfo = reactive({
  list: [
    {
      label: '隐患编号',
      field: 'hiddenDangerCode',
    },
    {
      label: '事件主题',
      field: 'eventTopic',
    },
    {
      label: '事件等级',
      field: 'eventLevel',
    },
    {
      label: '事件地点',
      field: 'eventLocation',
    },
    {
      label: '检查人',
      field: 'reviewerName',
    },
    {
      label: '检查人所在部门',
      field: 'deptName',
    },
    {
      label: '直接责任部门',
      field: 'rspDeptName',
    },
    // {
    //   label: '事件位置',
    //   field: 'eventPosition',
    // },
    // {
    //   label: '分类类型',
    //   field: 'classificationType',
    // },
    // {
    //   label: '隐患类型',
    //   field: 'hiddenDangerType',
    // },
    {
      label: '事发日期',
      field: 'occurrenceDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '是否大修',
      field: 'isMajorRepair',
      isBoolean: true,
    },
    {
      label: '隐患/事件领域',
      field: 'hiddenEvent',
    },
    {
      label: '事件类型',
      field: 'eventType',
    },
    {
      label: '是否已关闭',
      field: 'isClosed',
      isBoolean: true,
    },
    {
      label: '当前流程',
      field: 'currentProcess',
    },
    {
      label: '所属基地',
      field: 'eventLocationCodeName',
    },
    {
      label: '金字塔类别',
      field: 'pyramidCategoryName',
    },
    {
      label: '大修轮次',
      field: 'majorRepairTurn',
    },
    {
      label: '是否考核',
      field: 'isAssessed',
      isBoolean: true,
    },
    {
      label: '考核级别',
      field: 'assessmentLevelName',
    },
    {
      label: '事件描述',
      field: 'eventDesc',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="隐患信息"
    :is-border="false"
    :grid-content-props="basicInfo"
  />
</template>

<style scoped lang="less">

</style>
