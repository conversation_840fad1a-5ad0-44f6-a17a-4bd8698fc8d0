package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * LeadManagement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-21 18:45:58
 */
@ApiModel(value = "LeadManagementVO对象", description = "线索管理")
@Data
public class LeadManagementVO extends ObjectVO implements Serializable {
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 线索名称
     */
    @ApiModelProperty(value = "线索名称")
    private String name;

    /**
     * 目录id
     */
    @ApiModelProperty(value = "目录id")
    private String dirId;


    /**
     * 目录名称
     */
    @ApiModelProperty(value = "目录名称")
    private String dirName;

    /**
     * 线索来源
     */
    @ApiModelProperty(value = "线索来源")
    private String sourceId;

    /**
     * 线索来源名称
     */
    @ApiModelProperty(value = "线索来源名称")
    private String sourceName;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String proposeId;

    /**
     * 提出人名称
     */
    @ApiModelProperty(value = "提出人名称")
    private String proposeName;

    /**
     * 提出日期
     */
    @ApiModelProperty(value = "提出日期")
    private Date proposeDate;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 优先级名称
     */
    @ApiModelProperty(value = "优先级名称")
    private String priorityName;
    /**
     * 线索内容
     */
    @ApiModelProperty(value = "线索内容")
    private String content;

    /**
     * 是否子线索
     */
    @ApiModelProperty(value = "是否子线索")
    private Integer childFlag;

    /**
     * 父级线索
     */
    @ApiModelProperty(value = "父级线索")
    private String parentId;

    /**
     * 父级线索名称
     */
    @ApiModelProperty(value = "父级线索名称")
    private String parentName;


    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型")
    private String leadModel;

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型")
    private String leadModelName;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspCenter;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspCenterName;

    /**
     * 客户经理
     */
    @ApiModelProperty(value = "客户经理")
    private String accountManagerId;
    /**
     * 客户经理名称
     */
    @ApiModelProperty(value = "客户经理名称")
    private String accountManagerName;

    /**
     * 第一次分配说明
     */
    @ApiModelProperty(value = "第一次分配说明")
    private String firstRemark;

    /**
     * 第一次分配日期
     */
    @ApiModelProperty(value = "第一次分配日期")
    private Date firstDate;

    /**
     * 配合中心
     */
    @ApiModelProperty(value = "配合中心")
    private String cooperateCenter;

    /**
     * 配合中心名称
     */
    @ApiModelProperty(value = "配合中心名称")
    private String cooperateCenterName;

    /**
     * 责任工程师
     */
    @ApiModelProperty(value = "责任工程师")
    private String rspEngineer;

    /**
     * 责任工程师名称
     */
    @ApiModelProperty(value = "责任工程师名称")
    private String rspEngineerName;

    /**
     * 第二次分配说明
     */
    @ApiModelProperty(value = "第二次分配说明")
    private String secondRemark;

    /**
     * 第二次分配时间
     */
    @ApiModelProperty(value = "第二次分配时间")
    private Date secondDate;

    /**
     * 作废说明
     */
    @ApiModelProperty(value = "作废说明")
    private String invalidRemark;
    /**
     * 子线索
     */
    @ApiModelProperty(value = "子线索")
    private List<LeadManagementVO> children;

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "主/子线索")
    private String leadType;

}
