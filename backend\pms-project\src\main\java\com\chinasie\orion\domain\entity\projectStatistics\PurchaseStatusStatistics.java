package com.chinasie.orion.domain.entity.projectStatistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * PurchaseStatusStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2023-12-21 14:22:48
 */
@TableName(value = "pmsx_purchase_status_statistics")
@ApiModel(value = "PurchaseStatusStatisticsEntity对象", description = "采购状态趋势统计表")
@Data
public class PurchaseStatusStatistics  implements Serializable{

    /**
     * 统计ID
     */
    @ApiModelProperty(value = "统计ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "date_str")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    @TableField(value = "uk")
    private String uk;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 未审核数量
     */
    @ApiModelProperty(value = "未审核数量")
    @TableField(value = "no_audit_count")
    private Integer noAuditCount;

    /**
     * 审核中数量
     */
    @ApiModelProperty(value = "审核中数量")
    @TableField(value = "under_review_count")
    private Integer underReviewCount;

    /**
     * 已审核数量
     */
    @ApiModelProperty(value = "已审核数量")
    @TableField(value = "reviewed_count")
    private Integer reviewedCount;

    /**
     * 已关闭数量
     */
    @ApiModelProperty(value = "已关闭数量")
    @TableField(value = "close_count")
    private Integer closeCount;

}

