package com.chinasie.orion.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.InvestmentSchemeStatus;
import com.chinasie.orion.constant.YearInvestmentSchemeStatus;
import com.chinasie.orion.domain.dto.InvestmentSchemeDTO;
import com.chinasie.orion.domain.entity.InvestmentScheme;
import com.chinasie.orion.domain.entity.InvestmentSchemeEstimate;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.YearInvestmentScheme;
import com.chinasie.orion.domain.vo.InvestmentSchemeVO;
import com.chinasie.orion.domain.vo.MoneyStatisticsVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.InvestmentSchemeRepository;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * InvestmentScheme 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@Service
public class InvestmentSchemeServiceImpl extends OrionBaseServiceImpl<InvestmentSchemeRepository,InvestmentScheme> implements InvestmentSchemeService {

    @Resource
    private ProjectService projectService;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private InvestmentSchemeEstimateService investmentSchemeEstimateService;

    @Autowired
    private YearInvestmentSchemeService yearInvestmentSchemeService;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    private MoneyStatisticsService moneyStatisticsService;




    @Override
    public InvestmentSchemeVO createByProjectId(String projectId, String pageCode) throws Exception {
        LambdaQueryWrapperX<InvestmentScheme> investmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentScheme.class);
        investmentSchemeLambdaQueryWrapperX.eq(InvestmentScheme::getProjectId, projectId);
        List<InvestmentScheme> investmentSchemes = this.list(investmentSchemeLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(investmentSchemes)) {
            return this.detail(investmentSchemes.get(0).getId(), null);
        } else {
            InvestmentSchemeVO investmentSchemeVO = this.initValue(projectId);
            InvestmentSchemeDTO investmentSchemeDTO = BeanCopyUtils.convertTo(investmentSchemeVO, InvestmentSchemeDTO::new);
            investmentSchemeDTO.setProjectId(projectId);
            String id = this.create(investmentSchemeDTO);
            return this.detail(id, pageCode);
        }
    }

    @Override
    public synchronized InvestmentSchemeVO initValue(String projectId) throws Exception {
        InvestmentSchemeVO result = new InvestmentSchemeVO();
        Project project = projectService.getById(projectId);
        List<InvestmentScheme> investmentSchemes = this.list();
        result.setNumber("TZJH" + "-" + String.format("%04d", investmentSchemes.size() + 1));
        result.setName(project.getName() + "投资计划");
        result.setProjectName(project.getName());
        result.setProjectNumber(project.getNumber());
        result.setProjectId(projectId);

//        List<SimpleVO> orgList = projectService.getProductList();

//        String resOrg = project.getResOrg();
//        String companyName = "";
//        if (StrUtil.isNotBlank(resOrg)) {
//            if (resOrg.contains(",")) {
//                String[] split = resOrg.split(",");
//                List<String> resorgs = Arrays.asList(split);
//                companyName = orgList.stream().filter(o -> resorgs.contains(o.getId())).map(SimpleVO::getName).collect(Collectors.joining(","));
//            }else{
////                companyName=  orgList.stream().filter(o -> resOrg.equals(o.getId())).map(SimpleVO::getName).collect(Collectors.toList()).get(0);
//                for (SimpleVO simpleVO : orgList) {
//                    String id = simpleVO.getId();
//                    if(id.equals(resOrg)){
//                        companyName = simpleVO.getName();
//                        break;
//                    }
//                }
//            }
//        }

     //   result.setCompanyName(companyName);
        if (Objects.nonNull(project.getDataStatus())) {
            result.setProjectStatusName(project.getDataStatus().getName());
        }
        if (Objects.nonNull(project.getResPerson())) {
            result.setRspUserName(userRedisHelper.getUserById(project.getResPerson()).getName());
        }
        if (Objects.nonNull(project.getResDept())) {
            result.setRspDeptName(deptRedisHelper.getDeptById(project.getResDept()).getName());
        }

        MoneyStatisticsVO moneyStatisticsVO = moneyStatisticsService.getStaticsByProjectNumber(project.getId(),project.getNumber());

        result.setOverallBudget(NumberUtil.roundStr(moneyStatisticsVO.getAllBudgeMoney().toString(), 2));
        /**
         * 总体实际
         */
        result.setOverallReality(NumberUtil.roundStr(moneyStatisticsVO.getAllPracticalMoney().toString(), 2));
        /**
         * 立项金额
         */
        result.setProjectAmount(NumberUtil.roundStr(moneyStatisticsVO.getAllProjectApprovalMoney().toString(), 2));
        /**
         * 合同金额
         */
        result.setContractAmount(NumberUtil.roundStr(moneyStatisticsVO.getAllContactMoney().toString(), 2));


        return result;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public InvestmentSchemeVO detail(String id, String pageCode) throws Exception {
        InvestmentScheme investmentScheme = this.getById(id);
        InvestmentSchemeVO investmentSchemeVO = initValue(investmentScheme.getProjectId());
        InvestmentSchemeVO result = BeanCopyUtils.convertTo(investmentScheme, InvestmentSchemeVO::new);
        result.setProjectName(investmentSchemeVO.getProjectName());
        result.setProjectNumber(investmentSchemeVO.getProjectNumber());
        result.setRspUserName(investmentSchemeVO.getRspUserName());
        result.setRspDeptName(investmentSchemeVO.getRspDeptName());
        result.setProjectStatusName(investmentSchemeVO.getProjectStatusName());
        result.setCompanyName(investmentSchemeVO.getCompanyName());
        /**
         * 总体预算
         */
        result.setOverallBudget(investmentSchemeVO.getOverallBudget());
        /**
         * 总体实际
         */
        result.setOverallReality(investmentSchemeVO.getOverallReality());
        /**
         * 立项金额
         */
        result.setProjectAmount(investmentSchemeVO.getProjectAmount());
        /**
         * 合同金额
         */
        result.setContractAmount(investmentSchemeVO.getContractAmount());

        /**
         * 概算
         */
        List<InvestmentSchemeEstimate> investmentSchemeEstimates = investmentSchemeEstimateService.list(new LambdaQueryWrapperX<>(InvestmentSchemeEstimate.class).eq(InvestmentSchemeEstimate::getInvestmentSchemeId, id));
        if (!CollectionUtils.isEmpty(investmentSchemeEstimates)) {
            //新增获取最大层级概算
            //{ label: '项目建议书', value: '1' },
            //{ label: '可行性研究报告', value: '2' },
            //{ label: '初步设计', value: '3' },
            InvestmentSchemeEstimate use = new InvestmentSchemeEstimate();
            Map<String, InvestmentSchemeEstimate> sourceEstimateMap = investmentSchemeEstimates.stream().collect(Collectors.toMap(InvestmentSchemeEstimate::getSource, Function.identity(), (v1, v2) -> v2));
            if (sourceEstimateMap.containsKey("1")) {
                use = sourceEstimateMap.get("1");
            }
            if (sourceEstimateMap.containsKey("2")) {
                use = sourceEstimateMap.get("2");
            }
            if (sourceEstimateMap.containsKey("3")) {
                use = sourceEstimateMap.get("3");
            }
            String estimateStr = NumberUtil.roundStr((use.getArchitecture().add(use.getDevice()).add(use.getInstallation()).add(use.getOther())).toString(), 2);
            result.setEstimate(estimateStr);
        } else {
            result.setEstimate("0");
        }


        /**
         * 累计投资
         */
        List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(new LambdaQueryWrapperX<>(YearInvestmentScheme.class).eq(YearInvestmentScheme::getStatus, YearInvestmentSchemeStatus.GIVE.getCode()).eq(YearInvestmentScheme::getInvestmentId, id));
        Map<String, List<YearInvestmentScheme>> groupByYearNameMap = yearInvestmentSchemes.stream().collect(Collectors.groupingBy(YearInvestmentScheme::getYearName));
        List<YearInvestmentScheme> y_2 = new ArrayList<>();
        groupByYearNameMap.forEach((k, v) -> {
            if (v.size() == 2) {
                y_2.addAll(v.stream().filter(o -> StrUtil.isNotBlank(o.getOldId())).collect(Collectors.toList()));
            } else {
                y_2.addAll(v);
            }

        });
        BigDecimal totalInvestmentPlan = y_2.stream().map(o -> o.getArchitecture().add(o.getDevice()).add(o.getOther()).add(o.getInstallation())).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalInvestmentPlan(NumberUtil.roundStr(totalInvestmentPlan.toString(), 2));

        /**
         * 累计完成
         */
        BigDecimal totalDo = y_2.stream().map(YearInvestmentScheme::getTotalDo).reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalInvestmentCompletePlan(NumberUtil.roundStr(totalDo.toString(), 2));


        /**
         * 概算执行率
         */
        DecimalFormat df = new DecimalFormat("0.00%");//设置百分比格式，保留两位小数

        //概算
        BigDecimal estimate = new BigDecimal(result.getEstimate());

        if (Objects.equals(estimate.compareTo(BigDecimal.ZERO), 0)) {
            if (!Objects.equals(totalDo.compareTo(BigDecimal.ZERO), 0)) {
                result.setEstimatePercent("100%");
            } else {
                result.setEstimatePercent("0.00%");
            }
        } else {
            BigDecimal yearRate = totalDo.divide(estimate, 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
            result.setEstimatePercent(df.format(yearRate.doubleValue()));
        }

        /**
         * 本年投资计划
         */
        y_2.stream().filter(o -> Objects.equals(o.getYearName(), DateUtil.format(new Date(), "yyyy"))).findFirst().ifPresentOrElse(o -> {
            result.setCurrentYearInvest(NumberUtil.roundStr(String.valueOf(o.getArchitecture().add(o.getInstallation()).add(o.getOther()).add(o.getDevice())), 2));
        }, () -> {
            result.setCurrentYearInvest("0");
        });


        /**
         * 本年完成投资计划
         */
        y_2.stream().filter(o -> Objects.equals(o.getYearName(), DateUtil.format(new Date(), "yyyy"))).findFirst().ifPresentOrElse(o -> {
            result.setCurrentYearCompleteInvest(NumberUtil.roundStr(o.getTotalDo().toString(), 2));
        }, () -> {
            result.setCurrentYearCompleteInvest("0");
        });

        /**
         * 本年投资计划执行率
         */
        if (Objects.equals(new BigDecimal(result.getCurrentYearInvest()).compareTo(BigDecimal.ZERO), 0)) {
            if (!Objects.equals(new BigDecimal(result.getCurrentYearCompleteInvest()).compareTo(BigDecimal.ZERO), 0)) {
                result.setCurrentYearCompleteInvestPercent("100%");
            } else {
                result.setCurrentYearCompleteInvestPercent("0.00%");
            }
        } else {
            BigDecimal yearRate = new BigDecimal(result.getCurrentYearCompleteInvest()).divide(new BigDecimal(result.getCurrentYearInvest()), 4, RoundingMode.HALF_UP);// 除法运算并设置保留小数点后四位
            result.setCurrentYearCompleteInvestPercent(df.format(yearRate.doubleValue()));
        }


        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), CurrentUserHelper.getCurrentUserId());

        pmsAuthUtil.setDetailAuths(result, CurrentUserHelper.getCurrentUserId(), pageCode, result.getDataStatus(), InvestmentSchemeVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param investmentSchemeDTO
     */
    @Override
    public String create(InvestmentSchemeDTO investmentSchemeDTO) throws Exception {
        //判断是否已经存在该项目的投资计划
        LambdaQueryWrapperX<InvestmentScheme> investmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentScheme.class);
        investmentSchemeLambdaQueryWrapperX.eq(InvestmentScheme::getProjectId, investmentSchemeDTO.getProjectId());
        List<InvestmentScheme> investmentSchemes = this.list(investmentSchemeLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(investmentSchemes)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目投资计划已存在");
        }
        InvestmentScheme investmentScheme = BeanCopyUtils.convertTo(investmentSchemeDTO, InvestmentScheme::new);
        investmentScheme.setCloseFlag(false);
        this.save(investmentScheme);
        return investmentScheme.getId();
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<YearInvestmentScheme> investmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(YearInvestmentScheme.class);
        investmentSchemeLambdaQueryWrapperX.in(YearInvestmentScheme::getInvestmentId, ids);
        List<YearInvestmentScheme> yearInvestmentSchemes = yearInvestmentSchemeService.list(investmentSchemeLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(yearInvestmentSchemes)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该投资计划已产生年度投资计划");
        }
        Boolean result = this.removeBatchByIds(ids);
        return result;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public List<InvestmentSchemeVO> list(String projectId) throws Exception {
        LambdaQueryWrapperX<InvestmentScheme> investmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentScheme.class);
        investmentSchemeLambdaQueryWrapperX.eq(InvestmentScheme::getProjectId, projectId);
        List<InvestmentScheme> investmentSchemes = this.list(investmentSchemeLambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(investmentSchemes)) {
            return new ArrayList<>();
        }
        InvestmentScheme investmentScheme = investmentSchemes.get(0);
        InvestmentSchemeVO detail = this.detail(investmentScheme.getId(), null);
        List<InvestmentSchemeVO> result = new ArrayList<>(Collections.singletonList(detail));
        return result;
    }

    @Override
    public Boolean close() throws Exception {
        LambdaQueryWrapperX<InvestmentScheme> investmentSchemeLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InvestmentScheme.class);
        investmentSchemeLambdaQueryWrapperX.eq(InvestmentScheme::getCloseFlag, true);
        List<InvestmentScheme> investmentSchemes = this.list(investmentSchemeLambdaQueryWrapperX);
        if (!CollectionUtils.isEmpty(investmentSchemes)) {
            investmentSchemes.forEach(e -> e.setStatus(InvestmentSchemeStatus.ClOSE.getCode()));
            return this.updateBatchById(investmentSchemes);
        }

        return true;
    }
}
