package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProjectPayNormalPurchase VO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:31
 */
@ApiModel(value = "ProjectPayNormalPurchaseVO对象", description = "正常采购金额")
@Data
public class ProjectPayNormalPurchaseVO extends  ObjectVO   implements Serializable{

            /**
         * 参考凭证编码
         */
        @ApiModelProperty(value = "参考凭证编码")
        private String refbn;


        /**
         * WBS元素
         */
        @ApiModelProperty(value = "WBS元素")
        private String posid;


        /**
         * 项目定义
         */
        @ApiModelProperty(value = "项目定义")
        private String pspid;


        /**
         * 借方日期
         */
        @ApiModelProperty(value = "借方日期")
        private Date budat;


        /**
         * 成本要素
         */
        @ApiModelProperty(value = "成本要素")
        private String kstar;


        /**
         * 成本要素名称
         */
        @ApiModelProperty(value = "成本要素名称")
        private String txtTwo;


        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String sgtxt;


        /**
         * 币种
         */
        @ApiModelProperty(value = "币种")
        private String currency;


        /**
         * CO对象名称
         */
        @ApiModelProperty(value = "CO对象名称")
        private String postOne;


        /**
         * 业务货币值
         */
        @ApiModelProperty(value = "业务货币值")
        private String wtgbtr;


        /**
         * 数据更新时间
         */
        @ApiModelProperty(value = "数据更新时间")
        private Date insertTime;


        /**
         * 本次数据更新时间
         */
        @ApiModelProperty(value = "本次数据更新时间")
        private Date updateTime;


    

}
