package com.chinasie.orion.service.review.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.review.ReviewLibraryDTO;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.entity.review.ReviewEssentials;
import com.chinasie.orion.domain.entity.review.ReviewLibrary;
import com.chinasie.orion.domain.vo.review.IdAndNameVO;
import com.chinasie.orion.domain.vo.review.ReviewLibraryVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.review.ReviewEssentialsMapper;
import com.chinasie.orion.repository.review.ReviewLibraryMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewLibraryService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * ReviewLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@Service
@Slf4j
public class ReviewLibraryServiceImpl extends OrionBaseServiceImpl<ReviewLibraryMapper, ReviewLibrary> implements ReviewLibraryService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private UserBaseApiService userBaseApiService;

    @Resource
    private DeptBaseApiService deptBaseApiService;

    @Resource
    private ReviewEssentialsMapper essentialsMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReviewLibraryVO detail(String id, String pageCode) throws Exception {
        ReviewLibrary reviewLibrary = this.getById(id);
        ReviewLibraryVO result = BeanCopyUtils.convertTo(reviewLibrary, ReviewLibraryVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param reviewLibraryDTO
     */
    @Override
    public String create(ReviewLibraryDTO reviewLibraryDTO) throws Exception {
//        LambdaQueryWrapperX<ReviewLibrary> wrapperX = new LambdaQueryWrapperX<>(ReviewLibrary.class);
//        wrapperX.eq(ReviewLibrary::getName,reviewLibraryDTO.getName());
//        if (this.exists(wrapperX)){
//            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "评审名称已存在！");
//        }
        ReviewLibrary reviewLibrary = BeanCopyUtils.convertTo(reviewLibraryDTO, ReviewLibrary::new);
        List<String> expertList = reviewLibraryDTO.getExpertList();
        if (CollectionUtil.isEmpty(expertList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "评审专家不能为空！");
        }
        String experts = String.join(",", expertList);
        reviewLibrary.setExperts(experts);
        this.save(reviewLibrary);
        String rsp = reviewLibrary.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param reviewLibraryDTO
     */
    @Override
    public Boolean edit(ReviewLibraryDTO reviewLibraryDTO) throws Exception {
//        LambdaQueryWrapperX<ReviewLibrary> wrapperX = new LambdaQueryWrapperX<>(ReviewLibrary.class);
//        wrapperX.eq(ReviewLibrary::getName,reviewLibraryDTO.getName())
//                .ne(ReviewLibrary::getId,reviewLibraryDTO.getId());
//        if (this.exists(wrapperX)){
//            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "评审名称已存在！");
//        }
        ReviewLibrary reviewLibrary = BeanCopyUtils.convertTo(reviewLibraryDTO, ReviewLibrary::new);
        List<String> expertList = reviewLibraryDTO.getExpertList();
        if (CollectionUtil.isEmpty(expertList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "评审专家不能为空！");
        }
        String experts = String.join(",", expertList);
        reviewLibrary.setExperts(experts);
        this.updateById(reviewLibrary);
        String rsp = reviewLibrary.getId();

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "删除id不能为空！");
        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReviewLibraryVO> pages(Page<ReviewLibraryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ReviewLibrary> condition = new LambdaQueryWrapperX<>(ReviewLibrary.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ReviewLibraryDTO query = pageRequest.getQuery();
        if (!ObjectUtil.isNull(query)) {
            if (StrUtil.isBlank(query.getName())) {
                condition.like(ReviewLibrary::getName, query.getName());
            }
        }
        condition.orderByDesc(ReviewLibrary::getCreateTime);


        Page<ReviewLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ReviewLibrary::new));

        PageResult<ReviewLibrary> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReviewLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReviewLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReviewLibraryVO::new);
        if (!CollectionUtil.isEmpty(vos)) {
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<ReviewLibraryVO> vos) throws Exception {
        List<String> resultList = vos.stream()
                .map(obj -> obj.getExperts().split(","))
                .flatMap(Arrays::stream)
                .collect(Collectors.toList());
        List<SimpleUserVO> userByIds = userBaseApiService.getUserByIds(resultList);
        Map<String, String> userMap = userByIds.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));

        List<String> deptIds = vos.stream().map(ReviewLibraryVO::getMaintainDept).collect(Collectors.toList());
        List<DeptVO> deptByIds = deptBaseApiService.getDeptByIds(deptIds);
        Map<String, String> deptMap = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        setEssentialsCount(vos);
        vos.forEach(vo -> {
            List<String> list = Arrays.asList(vo.getExperts().split(","));
            List<IdAndNameVO> experts = new ArrayList<>();
            list.forEach(id -> {
                if (StrUtil.isNotBlank(userMap.get(id))){
                    IdAndNameVO idAndNameVO = new IdAndNameVO();
                    idAndNameVO.setId(id);
                    idAndNameVO.setName(userMap.get(id));
                    experts.add(idAndNameVO);
                }
            });
            vo.setExpertList(experts);

            List<IdAndNameVO> dept = new ArrayList<>();
            if (!CollectionUtil.isEmpty(deptMap)){
                String maintainDept = vo.getMaintainDept();
                String s = deptMap.get(maintainDept);
                IdAndNameVO idAndNameVO = new IdAndNameVO();
                idAndNameVO.setId(maintainDept);
                idAndNameVO.setName(s);
                dept.add(idAndNameVO);
            }
            vo.setMaintainDeptList(dept);
        });
    }

    @Override
    public List<ReviewLibraryVO> getList() {
        List<ReviewLibrary> list = this.list();
        if (CollectionUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<ReviewLibraryVO> vos = BeanCopyUtils.convertListTo(list, ReviewLibraryVO::new);
        return vos;
    }


    public static class ReviewLibraryExcelListener extends AnalysisEventListener<ReviewLibraryDTO> {

        private final List<ReviewLibraryDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReviewLibraryDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReviewLibraryDTO> getData() {
            return data;
        }
    }

    private void setEssentialsCount(List<ReviewLibraryVO> vos){
        List<String> ids = vos.stream().map(ReviewLibraryVO::getId).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<ReviewEssentials> wrapperX = new LambdaQueryWrapperX<>(ReviewEssentials.class);
        wrapperX.in(ReviewEssentials::getMainTableId,ids);
        List<ReviewEssentials> reviewEssentials = essentialsMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(reviewEssentials)){
            return;
        }
        Map<String, List<ReviewEssentials>> map = reviewEssentials.stream().filter(e -> StrUtil.isNotBlank(e.getMainTableId())).collect(Collectors.groupingBy(ReviewEssentials::getMainTableId));
        vos.forEach(v -> {
            String id = v.getId();
            List<ReviewEssentials> reviewEssentialsList = map.get(id);
            if (!CollectionUtil.isEmpty(reviewEssentialsList)){
                v.setEssentialsNumber(reviewEssentialsList.size());
            }

        });
    }

}
