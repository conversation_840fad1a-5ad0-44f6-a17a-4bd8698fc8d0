package com.chinasie.orion.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.vo.BudgetExpendDetailVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BudgetExpendStatisticsMapper  extends OrionBaseMapper<Object> {

    Page<BudgetExpendDetailVO> getBudgetExpendDetailVOPage(@Param("ids") List<String> ids,@Param("projectId") String projectId ,Page page);

    List<BudgetExpendDetailVO> getList(@Param("ids") List<String> ids,@Param("projectId") String projectId );
}
