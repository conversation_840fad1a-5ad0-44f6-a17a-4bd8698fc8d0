package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectMajorDeed Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@TableName(value = "pms_project_major_deed")
@ApiModel(value = "ProjectMajorDeedEntity对象", description = "项目主要事迹")
@Data

public class ProjectMajorDeed extends  ObjectEntity  implements Serializable{

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "begin_time", updateStrategy = FieldStrategy.ALWAYS)
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField(value = "end_time", updateStrategy = FieldStrategy.ALWAYS)
    private Date endTime;

    /**
     * 主要事迹
     */
    @ApiModelProperty(value = "主要事迹")
    @TableField(value = "major_deed")
    private String majorDeed;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 起止时间
     */
    @ApiModelProperty(value = "起止时间")
    @TableField(value = "begin_end_time")
    private String beginEndTime;
}
