package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/1/29
 * @description:
 */
@Data
public class DeliverGoalsVO extends ObjectVO implements Serializable {

    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 计划提交时间
     */
    @ApiModelProperty(value = "计划提交时间")
    private Date planSubmitTime;

    /**
     * 编写人
     */
    @ApiModelProperty(value = "编写人")
    private String writer;

    /**
     * 编写人名称
     */
    @ApiModelProperty(value = "编写人名称")
    private String writerName;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String resPersonName;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeName;

    /**
     * 文件状态
     */
    @ApiModelProperty(value = "文件状态")
    private String fileStatus;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String revId;

    @ApiModelProperty(value = "是否载挂技术文件")
    private String existDeliverable;

    @ApiModelProperty(value = "项目id")
    private String projectId;
}
