package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectCollectionDTO;
import com.chinasie.orion.domain.entity.ProjectCollection;
import com.chinasie.orion.domain.vo.ProjectCollectionStatisticsVO;
import com.chinasie.orion.domain.vo.ProjectCollectionTreeVO;
import com.chinasie.orion.domain.vo.ProjectCollectionVO;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneNodeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectCollection 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
public interface ProjectCollectionService extends OrionBaseService<ProjectCollection> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectCollectionVO detail(String id) throws Exception;

    ProjectCollectionVO detailPerson(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectCollectionDTO
     */
    ProjectCollectionVO create(ProjectCollectionDTO projectCollectionDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectCollectionDTO
     */
    Boolean edit(ProjectCollectionDTO projectCollectionDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectCollectionStatisticsVO> pages(Page<ProjectCollectionDTO> pageRequest) throws Exception;

    Page<ProjectCollectionVO> getPages(Page<ProjectCollectionDTO> pageRequest) throws Exception;


    /**
     * 树列表
     *
     * @param id
     * @return
     */
    List<ProjectCollectionTreeVO> tree(String id,ProjectCollectionDTO projectCollectionDTO) throws Exception;

    /**
     * 统计
     *
     * @param id
     * @return
     */
    List<ProjectCollectionTreeVO> treeStatistics(String id,ProjectCollectionDTO projectCollectionDTO) throws Exception;

    Boolean createProjectCollection(ProjectCollectionDTO projectCollectionDTO) throws Exception;

    Boolean removeProjectCollection(String parentId,String id) throws Exception;
}
