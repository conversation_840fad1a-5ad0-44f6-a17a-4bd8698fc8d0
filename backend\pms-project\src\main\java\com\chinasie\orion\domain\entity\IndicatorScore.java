package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.math.BigDecimal;

/**
 * IndicatorScore Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@TableName(value = "pmsx_indicator_score")
@ApiModel(value = "IndicatorScoreEntity对象", description = "指标评分表")
@Data
public class IndicatorScore extends ObjectEntity implements Serializable {

    /**
     * 项目和指标关联外键
     */
    @ApiModelProperty(value = "项目和指标关联外键")
    @TableField(value = "performance_in_id")
    private String PerformanceInId;

    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 分数
     */
    @ApiModelProperty(value = "项目绩效ID")
    @TableField(value = "performance_id")
    private String performanceId;

}
