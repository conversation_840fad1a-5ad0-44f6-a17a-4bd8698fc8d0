<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :isTable="isTable"
      class="card-list-table"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_04_01_02_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addBudget"
        >
          新增预算
        </BasicButton>
        <BasicButtonGroup>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_04_01_01',powerData)"
            :type="isTable?'':'primary'"
            :ghost="!isTable"
            @click="emits('budgetType', true)"
          >
            预算编制
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_04_01_02',powerData)"
            :type="isTable?'primary':''"
            :ghost="isTable"
            @click="emits('budgetType', false)"
          >
            预算执行
          </BasicButton>
        </BasicButtonGroup>
      </template>

      <template #details="{record}">
        <span
          v-if="isPower('PMS_XMXQ_container_04_01_02_button_01',powerData)"
          class="action-btn"
          @click="openDetails(record)"
        >详情</span>
      </template>
      <template #budgetExecute="{record}">
        <div
          v-if="budgetExecute(record)<=100"
          class="budgetExecute"
        >
          <div class="progress">
            <Progress
              strokeColor="#52C41A"
              :percent="budgetExecute(record).toFixed(2)"
              status="active"
              :show-info="false"
            />
          </div>
          <span
            class="percentage"
          >{{ budgetExecute(record).toFixed(2) }}%</span>
        </div>
        <div
          v-else
          class="budgetExecute"
        >
          <div class="progress">
            <Progress
              :percent="budgetExecute(record).toFixed(2)"
              status="exception"
              :show-info="false"
            />
          </div>
          <span
            class="percentage"
          >{{ budgetExecute(record).toFixed(2) }}%</span>
        </div>
      </template>
      <template #footer>
        <div class="footer">
          <div class="footer_lf">
            汇总
          </div>
          <div class="footer_cont">
            <div class="footer_cont_money">
              {{ state.totalBudget.toFixed(2) }}
            </div>
            <div class="footer_cont_money">
              {{ state.totalCost.toFixed(2) }}
            </div>
            <div :class="[state.isOut ? 'redStyle' : 'greenStyle','footer_cont_money']">
              {{ state.totalDifference.toFixed(2) }}
            </div>
          </div>
          <div class="footer_rf">
            <div
              v-if="budgetExecuteSummary()>=100"
              class="footer_Progress"
            >
              <Progress
                strokeColor="#FF4D4F"
                :percent="budgetExecuteSummary().toFixed(2)"
                :show-info="false"
              />
              <!--              <span class="percentage">{{ budgetExecuteSummary().toFixed(2) }}%</span>-->
            </div>
            <div
              v-else
              class="footer_Progress"
            >
              <Progress
                strokeColor="#52C41A"
                :percent="budgetExecuteSummary().toFixed(2)"
                :show-info="false"
                status="active"
              />
              <!--              <span class="percentage">{{ budgetExecuteSummary().toFixed(2) }}%</span>-->
            </div>
            <span class="percentage">{{ budgetExecuteSummary().toFixed(2) }}%</span>
          </div>
        </div>
      </template>
    </oriontable>
    <!--详情抽屉-->
    <BudgetExecuteDrawer
      @updatePage="updatePage"
      @register="registerDetailsDrawer"
    />
    <!--    新增抽屉-->
    <BudgetingDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  nextTick, ref, reactive, computed, onMounted, h, inject, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton,
  BasicButtonGroup, isPower,
  Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import {
  Modal, message, Progress,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { getBudgetExecute } from '/@/views/pms/api/costManage';
import BudgetExecuteDrawer from './components/budgetExecteDrawer.vue';
import BudgetingDrawer from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectCostManage/budget/components/budgeting/components/budgetingDrawer.vue';
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer }] = useDrawer();
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const emits = defineEmits(['budgetType']);
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
function addBudget() {
  openEditDrawer(true, {
    type: 'add',
    projectId: props.id,
  });
}
// 获取汇总
onMounted(() => {
  new Api(`/pms/project-budget/total-cost/${props.id}`).fetch('', '', 'GET').then((res) => {
    state.value.totalBudget = res.totalBudget;
    state.value.totalCost = res.totalCost;
    state.value.totalDifference = res.totalDifference;
    state.value.isOut = res.isOut;
  });
});
const router = useRouter();
const isTable = ref(true);
const tableRef = ref(null);
const footerDom = ref(null);
const powerData = inject('powerData');
const state = ref({
  totalBudget: 0,
  totalCost: 0,
  totalDifference: 0,
  isOut: 0,
});
const columns = [
  {
    title: '预算编码',
    dataIndex: 'number',
    width: 120,
  },
  {
    title: '预算名称',
    dataIndex: 'name',
    minWidth: 140,
  },
  {
    title: '年度',
    dataIndex: 'year',
    width: 70,
    customRender({ record, text }) {
      return dayjs(record.year).format('YYYY');
    },
  },
  {
    title: '详情',
    dataIndex: 'details',
    width: 70,
    slots: { customRender: 'details' },
  },
  {
    title: '年度预算金额',
    width: 160,
    dataIndex: 'yearExpense',
  },
  {
    title: '总成本',
    width: 160,
    dataIndex: 'totalCost',
  },
  {

    title: '差异',
    width: 160,
    dataIndex: 'priceDifference',
    customRender({ text, record }) {
      if (record.isOut) {
        return h('span', { class: 'redStyle' }, `-${record.priceDifference}`);
      }
      return h('span', { class: 'greenStyle' }, `${record.priceDifference}`);
    },
  },
  {
    title: '执行进度',
    dataIndex: 'budgetExecute',
    minWidth: 330,
    slots: { customRender: 'budgetExecute' },
    fixed: 'right',
  },
];

const baseTableOption = {
  resizeHeightOffset: 100,
  rowSelection: {},
  columns,
  api: (params) => getBudgetExecute({
    ...params,
    query: { projectId: props.id },
  }),
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_PROJECTCOSTMANAGE_BUDGET_COMPONENTS_BUDGETEXECUTE_INDEX',

};
function updatePage() {
  nextTick();
  tableRef.value.reload();
}

function budgetExecute(record) {
  if (Number(record.totalCost) === 0 || Number(record.yearExpense) === 0) {
    return 0;
  }
  return (((Number(record.totalCost) / Number(record.yearExpense)) * 100)) * 1;
}
function budgetExecuteSummary() {
  if (Number(state.value.totalCost) === 0 || Number(state.value.totalBudget) === 0) {
    return 0;
  }
  return (((Number(state.value.totalCost) / Number(state.value.totalBudget)) * 100)) * 1;
}
function openDetails(record) {
  openDetailsDrawer(true, {
    type: 'details',
    formData: record,
    projectId: props.id,
  });
}
</script>
<style scoped lang="less">
:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
:deep( .redStyle){
  color: #FF4D4F ;
}
:deep( .greenStyle){
  color:#52C41A;
}
:deep(.ant-progress-inner){
  border: 1px solid #ccc;
}
.percentage{
  color: rgba(0,0,0,.85);
  display: inline-block;
  font-size: 1em;
  line-height: 1;
  margin-left: 8px;
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
  width: 2em;
  word-break: normal;
}
.red{
  color: red;
}
.footer{
  display: flex;
  align-items: center;
  font-weight: bold;
  overflow: auto;
  .footer_lf{
    display: flex;
    width: 200px;
  }
  .footer_cont{
    display: flex;
    justify-content:space-around;
    margin-left: 25em;
    width: 35em;
    .footer_cont_money{
      flex: 1;
      padding-left: 8px;
    }
  }
  .footer_rf{
    display: flex;
    padding-left: 10px;
    flex: 1;
    align-items: center;
    .footer_Progress{
      width:180px ;
      display: flex;
       align-items: center;
    }
  }
}
.budgetExecute{
  display: flex;
  .progress{
    width: 180px;
  }
}

</style>
