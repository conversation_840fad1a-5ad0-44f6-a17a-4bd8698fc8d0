<template>
  <Layout class="ui-2-0">
    <BasicTable
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
      @rowClick="rowClick"
    />
  </Layout>
  <NewButtonModal
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
</template>

<script>
import {
  Layout, BasicTable,
} from 'lyra-component-vue3';

import { message, Modal } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import { onMounted, reactive, toRefs } from 'vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
export default {
  name: 'Index',
  components: {
    NewButtonModal,
    Layout,
    BasicTable,
  },
  setup() {
    const state = reactive({
      selectedRowKeys: [],
      columns: [
        {
          title: '资源名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '材料分类',
          dataIndex: 'name1',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '成本',
          dataIndex: 'predictDeliverTime',
        },
        {
          title: '数量',
          dataIndex: 'deliveryTime',
        },
        {
          title: '描述',
          dataIndex: 'planName',
          align: 'left',
          minWidth: 200,
        },
      ],
      dataSource: [],
      loading: false,
      btnConfig: {
        check: { show: true },
        open: { show: true },
        add: { show: true },
        remove: { show: true },
        search: { show: true },
      },
    });
    function getPage() {
      state.loading = true;
      const url = `deliverable/list?planId=${parseURL().id}`;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.dataSource = res;
          state.selectedRowKeys = [];
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    }
    function onSelectChange(selectedRowKeys) {
      state.selectedRowKeys = selectedRowKeys;
    }
    function rowClick(val) {
      if (state.selectedRowKeys.includes(val.id)) {
        state.selectedRowKeys = state.selectedRowKeys.filter((s) => s !== val.id);
      } else {
        state.selectedRowKeys.push(val.id);
      }
    }
    function isSelectCheck(type) {
      if (state.selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (state.selectedRowKeys.length === 1) {
        return true;
      }
      if (state.selectedRowKeys.length > 1) {
        if (type === 'remove') {
          return true;
        }
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleCheck(id) {
      new Api('/pms').fetch('', `deliverable/${id}`).then((res) => {
        state.viewDetails = {
          visible: true,
          title: '查看信息',
          form: res,
        };
      });
    }
    function clickType(type) {
      if (type === 'open') {
        const bool = isSelectCheck();
        if (bool) {
        }
      }
      if (type === 'search') {
      }
      if (type === 'check') {
        const bool = isSelectCheck();
        if (bool) {
          handleCheck(state.selectedRowKeys[0]);
        }
      }
      if (type === 'remove') {
        const bool = isSelectCheck(type);
        if (bool) {
          Modal.confirm({
            title: '确认提示',
            content: '请确认是否移除此选中数据？',
            onOk() {
              isConfirm();
            },
          });
        }
      }
      if (type === 'add') {
        state.edit = {
          visible: true,
          title: '添加信息',
          type,
          form: {},
        };
      }
    }
    function isConfirm() {
      new Api('/pms').fetch(state.selectedRowKeys, 'deliverable/batch', 'DELETE').then(() => {
        message.success('操作成功');
        getPage();
      });
    }

    function submitEdit(val) {
      state.edit.visible = false;
      if (val) {
        getPage();
      }
    }

    onMounted(() => {
      // getPage();
    });
    return {
      ...toRefs(state),
      clickType,
      getPage,
      rowClick,
      onSelectChange,
      submitEdit,
    };
  },
};
</script>

<style scoped>
  .ui-2-0 {
    width: calc(100% - 60px);
    float: left;
  }
</style>
