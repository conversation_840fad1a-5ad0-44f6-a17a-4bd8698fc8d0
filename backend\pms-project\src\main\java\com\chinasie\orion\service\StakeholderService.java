package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.StakeholderDTO;
import com.chinasie.orion.domain.entity.Stakeholder;
import com.chinasie.orion.domain.vo.StakeholderVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:42
 * @description:
 */
public interface StakeholderService extends OrionBaseService<Stakeholder> {

    /**
     * 新增干系人
     * @param stakeholderDTO
     * @return
     * @throws Exception
     */
    String saveStakeholder(StakeholderDTO stakeholderDTO) throws Exception;

    /**
     * 获取干系人分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<StakeholderVO> getStakeholderPage(Page<StakeholderDTO> pageRequest) throws Exception;

    /**
     * 获取干系人详情
     * @param id
     * @return
     * @throws Exception
     */
    StakeholderVO getStakeholderDetail(String id,String pageCode) throws Exception;

    /**
     * 编辑干系人
     * @param stakeholderDTO
     * @return
     * @throws Exception
     */
    Boolean editStakeholder(StakeholderDTO stakeholderDTO) throws Exception;

    /**
     * 批量删除干系人
     * @param idList
     * @return
     * @throws Exception
     */
    Boolean removeStakeholder(List<String> idList) throws Exception;

    /**
     * 导入干系人
     * @param file
     * @return
     * @throws Exception
     */
    Boolean saveStakeholderByExcel(MultipartFile file) throws Exception;

    /**
     * 获取干系人列表
     * @param
     * @return
     * @throws Exception
     */
    List<StakeholderVO> getStakeholderList(StakeholderDTO stakeholderDTO) throws Exception;
}
