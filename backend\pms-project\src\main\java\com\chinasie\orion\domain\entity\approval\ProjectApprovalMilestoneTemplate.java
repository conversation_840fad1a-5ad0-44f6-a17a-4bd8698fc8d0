package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectApprovalMilestoneTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:59
 */
@TableName(value = "pmsx_project_approval_milestone_template")
@ApiModel(value = "ProjectApprovalMilestoneTemplateEntity对象", description = "项目立项里程碑模板")
@Data

public class ProjectApprovalMilestoneTemplate extends  ObjectEntity  implements Serializable{

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    @TableField(value = "name")
    private String name;

}
