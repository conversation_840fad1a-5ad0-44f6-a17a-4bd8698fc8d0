<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chinasie.orion</groupId>
    <artifactId>orion-framework</artifactId>
    <version>4.1.0.0-LYRA</version>
  </parent>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>orion-spring-boot-starter-mybatis</artifactId>
  <version>4.1.0.0-LYRA</version>
  <dependencies>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.yulichang</groupId>
      <artifactId>mybatis-plus-join-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>easy-trans-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>easy-trans-mybatis-plus-extend</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.shardingsphere</groupId>
      <artifactId>shardingsphere-jdbc-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>transmittable-thread-local</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-nacos</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc8</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.dameng</groupId>
      <artifactId>DmJdbcDriver18</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>cn.com.kingbase</groupId>
      <artifactId>kingbase8</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.opengauss</groupId>
      <artifactId>opengauss-jdbc</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-license-verify</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
