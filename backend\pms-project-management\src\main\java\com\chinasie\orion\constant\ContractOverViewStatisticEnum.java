package com.chinasie.orion.constant;

public enum ContractOverViewStatisticEnum {
    NEWBIDPENDING("newBidPending", "新中标待签署"),
    NEWBID("newBid", "新签署"),
    HISTORYBID("historyBid", "历史签署"),
    COMPLETEDBID("completedBid", "已签署")
    ;


    private String key;

    private String desc;


    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    ContractOverViewStatisticEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
