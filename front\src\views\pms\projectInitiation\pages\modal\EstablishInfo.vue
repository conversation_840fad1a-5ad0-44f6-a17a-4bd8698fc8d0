<script setup lang="ts">
import {
  h,
  inject, onMounted, reactive, Ref, ref,
} from 'vue';
import {
  BasicCard, BasicEditor, DataStatusTag, BasicButton,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import RelationshipTable from './RelationshipTable.vue';
import { message, Tag } from 'ant-design-vue';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import Api from '/@/api';
const content:Ref<string> = ref('');
// 立项详情数据
const detailsData = inject('formData', {});
// 立项基本信息
const baseInfoProps = reactive({
  list: [
    {
      label: '任务名称',
      field: 'name',
    },
    {
      label: '任务父级',
      field: 'parentName',
    },
    {
      label: '任务属性',
      field: 'sourceName',
    },
    {
      label: '任务状态',
      field: 'status',
      valueRender: ({ record }) => (record.dataStatus ? h(DataStatusTag, {
        statusData: record.dataStatus,
      }) : ''),
    },
    {
      label: '执行情况',
      field: 'circumstance',
      valueRender: ({ record }) => h(Tag, {
        color: statusColor[record.circumstance],
      }, record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')),
    },
    {
      label: '完成是否需要多级审批',
      field: 'val',
      valueRender: ({ record }) => (record.val ? '是' : '否'),
    },

    {
      label: '任务开始日期',
      field: 'beginTime',
      valueRender: ({ record }) => (record.beginTime ? dayjs(record.beginTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '任务结束日期',
      field: 'endTime',
      valueRender: ({ record }) => (record.endTime ? dayjs(record.endTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '实际开始日期',
      field: 'actualBeginTime',
      valueRender: ({ record }) => (record.actualBeginTime ? dayjs(record.actualBeginTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '实际结束日期',
      field: 'actualEndTime',
      valueRender: ({ record }) => (record.actualEndTime ? dayjs(record.actualEndTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '下发时间',
      field: 'issueTime',
      valueRender: ({ record }) => (record.issueTime ? dayjs(record.issueTime).format('YYYY-MM-DD HH:mm:ss') : '---'),
    },
    {
      label: '下发人',
      field: 'issuedUserName',
    },
    {
      label: '责任人',
      field: 'rspUserName',
    },
    {
      label: '责任部门',
      field: 'rspDeptName',
    },

    {
      label: '任务描述',
      field: 'remark',
      gridColumn: '1/5',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
const basicEditorRef = ref();
onMounted(() => {
  if (detailsData.value?.processObject !== 'taskDecomposition_1') {
    basicEditorRef.value.editor.disable();
  }
  getContent();
});
function getContent() {
  new Api('/pms').fetch('', `collaborativeCompilationTask/getContent?id=${detailsData.value.id}`, 'GET').then((res) => {
    content.value = res || '';
  });
}
function saveContent() {
  new Api('/pms').fetch({
    id: detailsData.value.id,
    content: content.value,
  }, 'collaborativeCompilationTask/updateContent', 'PUT').then((res) => {
    message.success('保存成功');
    getContent();
  });
}
</script>

<template>
  <div class="basic-info-content">
    <BasicCard
      title="需求评审信息"
      :grid-content-props="baseInfoProps"
      :isBorder="false"
    />
    <BasicCard
      title="任务编制内容"
      class="basic-editor"
    >
      <div class="basic-editor-content">
        <div class="basic-editor-content-top">
          <BasicButton
            type="primary"
            @click="saveContent"
          >
            保存
          </BasicButton>
        </div>
        <BasicEditor
          ref="basicEditorRef"
          v-model:value="content"
          :disabled="true"
        />
      </div>
    </BasicCard>

    <BasicCard
      title="前后置任务关系"
      class="basic-editor"
    >
      <RelationshipTable :dataSource="detailsData?.taskPrePostVOS||[]" />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
:deep(.basic-title){
  border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
  padding-bottom: 15px;
}
:deep(.card-content){
  margin-top: 0 !important;
}
.basic-info-content{
  height: 100%;
  padding-top: 1px;
}
.basic-editor-content-top{
  padding: 5px 0 ;
  text-align: right;
  .ant-btn{
    margin-right: 0 !important;
  }
}
</style>
