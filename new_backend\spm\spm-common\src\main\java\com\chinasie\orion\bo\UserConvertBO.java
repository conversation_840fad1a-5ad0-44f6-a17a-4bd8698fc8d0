package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员公共类转换
 **/
@Slf4j
@Component
public class UserConvertBO {

    @Resource
    private UserRedisHelper userRedisHelperTemp;
    private static UserRedisHelper userRedisHelper;

    @Resource
    private UserBaseApiService userBaseApiServiceTemp;
    private static UserBaseApiService userBaseApiService;

    @Resource
    private UserDOMapper userDOMapperTemp;
    private static UserDOMapper userDOMapper;

    @PostConstruct
    public void init() {
        userRedisHelper = userRedisHelperTemp;
        userBaseApiService = userBaseApiServiceTemp;
        userDOMapper = userDOMapperTemp;
    }

    //根据id使用Redis获取用户名称
    public static String getNameByIdForRedis(String id) {
        return getDataByIdForRedis(id, true);
    }

    //根据id使用Redis获取用户编号
    public static String getCodeByIdForRedis(String id) {
        return getDataByIdForRedis(id, false);
    }

    //根据id使用Redis获取用户数据
    public static String getDataByIdForRedis(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            UserBaseCacheVO user = userRedisHelper.getUserBaseCacheById(id);
            if (ObjectUtil.isNotNull(user))
                return flag ? user.getName() : user.getCode();
        }
        return null;
    }

    //根据id使用Api获取用户名称
    public static String getNameByIdForApi(String id) {
        return getDataByIdForApi(id, true);
    }

    //根据id使用Api获取用户编号
    public static String getCodeByIdForApi(String id) {
        return getDataByIdForApi(id, false);
    }

    //根据id使用Api获取用户数据
    public static String getDataByIdForApi(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            SimpleUserVO user = userBaseApiService.getUserById(id);
            if (ObjectUtil.isNotNull(user))
                return flag ? user.getName() : user.getCode();
        }
        return null;
    }

    //根据id使用Mapper获取用户名称
    public static String getNameByIdForMapper(String id) {
        return getDataByIdForMapper(id, true);
    }

    //根据id使用Mapper获取用户编号
    public static String getCodeByIdForMapper(String id) {
        return getDataByIdForMapper(id, false);
    }

    //根据id使用Mapper获取用户名称
    public static String getDataByIdForMapper(String id, boolean flag) {
        if (StrUtil.isNotBlank(id)) {
            UserDO user = userDOMapper.selectById(id);
            if (ObjectUtil.isNotNull(user))
                return flag ? user.getName() : user.getCode();
        }
        return null;
    }

    //根据code使用Redis获取用户名称
    public static String getNameByCodeForRedis(String code) {
        return getDataByCodeForRedis(code, true);
    }

    //根据code使用Redis获取用户id
    public static String getIdByCodeForRedis(String code) {
        return getDataByCodeForRedis(code, false);
    }

    //根据code使用Redis获取用户数据
    public static String getDataByCodeForRedis(String code, boolean flag) {
        if (StrUtil.isNotBlank(code)) {
            Map<String, String> map = getMapByCodesForRedis(Collections.singletonList(code), flag);
            if (ObjectUtil.isNotNull(map))
                return map.get(code);
        }
        return null;
    }

    //根据code使用Api获取用户名称
    public static String getNameByCodeForApi(String code) {
        return getDataByCodeForApi(code, true);
    }

    //根据code使用Api获取用户id
    public static String getIdByCodeForApi(String code) {
        return getDataByCodeForApi(code, false);
    }

    //根据code使用Api获取用户数据
    public static String getDataByCodeForApi(String code, boolean flag) {
        if (StrUtil.isNotBlank(code)) {
            SimpleUserVO user = userBaseApiService.getUserByCode(code);
            if (ObjectUtil.isNotNull(user))
                return flag ? user.getName() : user.getId();
        }
        return null;
    }

    //根据code使用Mapper获取用户名称
    public static String getNameByCodeForMapper(String code) {
        return getDataByCodeForMapper(code, true);
    }

    //根据code使用Mapper获取用户id
    public static String getIdByCodeForMapper(String code) {
        return getDataByCodeForMapper(code, false);
    }

    //根据code使用Mapper获取用户数据
    public static String getDataByCodeForMapper(String code, boolean flag) {
        if (StrUtil.isNotBlank(code)) {
            LambdaQueryWrapperX<UserDO> lqw = new LambdaQueryWrapperX<>(UserDO.class);
            lqw.select(UserDO::getName);
            lqw.eq(UserDO::getCode, code);
            lqw.last("limit 1");
            UserDO user = userDOMapper.selectOne(lqw);
            if (ObjectUtil.isNotNull(user))
                return flag ? user.getName() : user.getId();
        }
        return null;
    }

    //根据id使用Redis获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForRedis(List<String> ids) {
        return getMapByIdsForRedis(ids, true);
    }

    //根据id使用Redis获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForRedis(List<String> ids) {
        return getMapByIdsForRedis(ids, false);
    }

    //根据id使用Redis获取用户数据（批量）
    public static Map<String, String> getMapByIdsForRedis(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<UserBaseCacheVO> userList = userRedisHelper.getUserBaseCacheByIds(ids);
        return generateMap(userList,
                flag ? UserBaseCacheVO::getId : UserBaseCacheVO::getCode,
                flag ? UserBaseCacheVO::getName : UserBaseCacheVO::getCode);
    }

    //根据id使用Api获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForApi(List<String> ids) {
        return getMapByIdsForApi(ids, true);
    }

    //根据id使用Api获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForApi(List<String> ids) {
        return getMapByIdsForApi(ids, false);
    }

    //根据id使用Api获取用户数据（批量）
    public static Map<String, String> getMapByIdsForApi(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<SimpleUserVO> userList = userBaseApiService.getUserByIds(ids);
        return generateMap(userList,
                flag ? SimpleUserVO::getId : SimpleUserVO::getCode,
                flag ? SimpleUserVO::getName : SimpleUserVO::getCode);
    }

    //根据id使用Mapper获取用户名称（批量）
    public static Map<String, String> getId2NameByIdsForMapper(List<String> ids) {
        return getMapByIdsForMapper(ids, true);
    }

    //根据id使用Mapper获取用户编号（批量）
    public static Map<String, String> getId2CodeByIdsForMapper(List<String> ids) {
        return getMapByIdsForMapper(ids, false);
    }

    //根据id使用Mapper获取用户数据（批量）
    public static Map<String, String> getMapByIdsForMapper(List<String> ids, boolean flag) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<UserDO> userList = userDOMapper.selectBatchIds(ids);
        return generateMap(userList,
                flag ? UserDO::getId : UserDO::getCode,
                flag ? UserDO::getName : UserDO::getCode);
    }

    //根据code使用Redis获取用户名称（批量）
    public static Map<String, String> getCode2NameByCodesForRedis(List<String> codeList) {
        return getMapByCodesForRedis(codeList, true);
    }

    //根据code使用Redis获取用户编号（批量）
    public static Map<String, String> getCode2IdByCodesForRedis(List<String> codeList) {
        return getMapByCodesForRedis(codeList, false);
    }

    //根据code使用Redis获取用户数据（批量）
    public static Map<String, String> getMapByCodesForRedis(List<String> codeList, boolean flag) {
        if (CollUtil.isEmpty(codeList)) {
            return Collections.emptyMap();
        }
        List<UserBaseCacheVO> userList = userRedisHelper.getUserBaseCacheByCode(TenantContextHolder.getTenantId(), codeList);
        return generateMap(userList,
                UserBaseCacheVO::getCode,
                flag ? UserBaseCacheVO::getName : UserBaseCacheVO::getId);
    }

    //根据code使用Api获取用户名称（批量）
    public static Map<String, String> getCode2NameByCodesForApi(List<String> codeList) {
        return getMapByCodesForApi(codeList, true);
    }

    //根据code使用Api获取用户编号（批量）
    public static Map<String, String> getCode2IdByCodesForApi(List<String> codeList) {
        return getMapByCodesForApi(codeList, false);
    }

    //根据code使用Api获取用户数据（批量）
    public static Map<String, String> getMapByCodesForApi(List<String> codeList, boolean flag) {
        if (CollUtil.isEmpty(codeList)) {
            return Collections.emptyMap();
        }
        List<SimpleUserVO> userList = userBaseApiService.getUserByCode(codeList);
        return generateMap(userList,
                SimpleUserVO::getCode,
                flag ? SimpleUserVO::getName : SimpleUserVO::getId);
    }

    //根据code使用Mapper获取用户名称（批量）
    public static Map<String, String> getCode2NameByCodesForMapper(List<String> codeList) {
        return getMapByCodesForMapper(codeList, true);
    }

    //根据code使用Mapper获取用户编号（批量）
    public static Map<String, String> getCode2IdByCodesForMapper(List<String> codeList) {
        return getMapByCodesForMapper(codeList, false);
    }

    //根据code使用Mapper获取用户数据（批量）
    public static Map<String, String> getMapByCodesForMapper(List<String> codeList, boolean flag) {
        if (CollUtil.isEmpty(codeList)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapperX<UserDO> lqw = new LambdaQueryWrapperX<>(UserDO.class);
        lqw.select(UserDO::getCode, flag ? UserDO::getName : UserDO::getId);
        lqw.in(UserDO::getCode, codeList);
        List<UserDO> userList = userDOMapper.selectList(lqw);
        return generateMap(userList,
                UserDO::getCode,
                flag ? UserDO::getName : UserDO::getId);
    }

    //使用Redis获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForRedis() {
        return allForRedis(1);
    }

    //使用Redis获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForRedis() {
        return allForRedis(2);
    }

    //使用Redis获取所有的code-name用户数据
    public static Map<String, String> getCode2NameAllForRedis() {
        return allForRedis(3);
    }

    //使用Redis获取所有的code-id用户数据
    public static Map<String, String> getCode2IdAllForRedis() {
        return allForRedis(4);
    }

    //根据code使用Redis获取用户数据（批量）
    public static Map<String, String> allForRedis(int type) {
        List<UserBaseCacheVO> userList = userRedisHelper.getAllUserBaseCache(TenantContextHolder.getTenantId());
        return getAllData(userList, type,
                UserBaseCacheVO::getId, UserBaseCacheVO::getName,
                UserBaseCacheVO::getCode, UserBaseCacheVO::getId);
    }

    //使用Api获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForApi() {
        return allForApi(1);
    }

    //使用Api获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForApi() {
        return allForApi(2);
    }

    //使用Api获取所有的code-name用户数据
    public static Map<String, String> getCode2NameAllForApi() {
        return allForApi(3);
    }

    //使用Api获取所有的code-id用户数据
    public static Map<String, String> getCode2IdAllForApi() {
        return allForApi(4);
    }

    //根据code使用Api获取用户数据（批量）
    public static Map<String, String> allForApi(int type) {
        List<SimpleUserVO> userList = userBaseApiService.getAllUserByOrgId();
        return getAllData(userList, type,
                SimpleUserVO::getId, SimpleUserVO::getName,
                SimpleUserVO::getCode, SimpleUserVO::getId);
    }

    //使用Mapper获取所有的id-name用户数据
    public static Map<String, String> getId2NameAllForMapper() {
        return allForMapper(1);
    }

    //使用Mapper获取所有的id-code用户数据
    public static Map<String, String> getId2CodeAllForMapper() {
        return allForMapper(2);
    }

    //使用Mapper获取所有的code-name用户数据
    public static Map<String, String> getCode2NameAllForMapper() {
        return allForMapper(3);
    }

    //使用Mapper获取所有的code-id用户数据
    public static Map<String, String> getCode2IdAllForMapper() {
        return allForMapper(4);
    }

    //根据code使用Mapper获取用户数据（批量）
    public static Map<String, String> allForMapper(int type) {
        LambdaQueryWrapperX<UserDO> lqw = new LambdaQueryWrapperX<>(UserDO.class);
        if (type == 1 || type == 2) {
            lqw.select(UserDO::getId, type == 1 ? UserDO::getName : UserDO::getCode);
        } else {
            lqw.select(UserDO::getCode, type == 3 ? UserDO::getName : UserDO::getId);
        }
        List<UserDO> userList = userDOMapper.selectList(lqw);
        return getAllData(userList, type,
                UserDO::getId, UserDO::getName,
                UserDO::getCode, UserDO::getId);
    }

    // 提取公共方法：根据类型和字段生成映射
    private static <T> Map<String, String> generateMap(List<T> list, Function<T, String> keyExtractor, Function<T, String> valueExtractor) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap(); // 返回空集合，避免返回 null
        }
        return list.stream()
                .collect(Collectors.toMap(keyExtractor, valueExtractor, (k1, k2) -> k1)); // 处理键冲突
    }

    // 提取公共方法：根据类型生成所有用户数据
    private static <T> Map<String, String> getAllData(List<T> list, int type, Function<T, String> keyExtractor1, Function<T, String> valueExtractor1,
                                                      Function<T, String> keyExtractor2, Function<T, String> valueExtractor2) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        switch (type) {
            case 1:
                return generateMap(list, keyExtractor1, valueExtractor1);
            case 2:
                return generateMap(list, keyExtractor1, valueExtractor2);
            case 3:
                return generateMap(list, keyExtractor2, valueExtractor1);
            case 4:
                return generateMap(list, keyExtractor2, valueExtractor2);
            default:
                return Collections.emptyMap();
        }
    }

}
