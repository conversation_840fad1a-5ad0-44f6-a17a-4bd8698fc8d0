package com.chinasie.orion.domain.vo.tree;

import com.chinasie.orion.domain.vo.job.BeforeAndAfterFourDay;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

@Data
public class JobWorkImplEditVO implements Serializable {

    @ApiModelProperty(value = "前后四天")
    public Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap;
    @ApiModelProperty(value = "工单号")
    @NotEmpty(message = "工单编号不能为空")
    private String  jobNumber;
}
