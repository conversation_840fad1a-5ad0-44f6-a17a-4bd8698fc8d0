<template>
  <Layout
    v-get-power="{pageCode:'PMS2901',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
    class="boxs"
    :content-title="templateClassifyName"
  >
    <template #left>
      <BusinessTree
        ref="leftTree"
        :get-tree-api="getTreeApi"
        :delete-tree-api="deleteTreeApi"
        title-name="概算模版"
        :add-api="addTreeApi"
        :edit-api="editTreeApi"
        :show-btn="true"
        :power-code="powerCode"
        @select-node="selectTreeNode"
        @init-data="initTreeData"
      />
    </template>
    <div class="table-content">
      <OrionTable
        v-if="selectTreeId"
        ref="tableRef"
        :options="tableOptions"
        @selectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="isPower('GSMB_container_02_button_01',powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="addTableNode"
          >
            新增模版
          </BasicButton>
          <BasicButton
            v-if="isPower('GSMB_container_02_button_03',powerData)"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
        </template>
        >
      </OrionTable>
      <Empty
        v-else
      />
    </div>
  </Layout>
</template>

<script lang="ts" setup>
import {
  h, provide, ref, Ref, computed,
} from 'vue';
import {
  BasicButton,
  BusinessTree,
  Layout,
  OrionTable,
  Empty, isPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import {
  getTableData,
  deleteBatchData,
  getTreeApi,
  deleteTreeApi,
  addTreeApi,
  editTreeApi,
  initNode,
} from './index';
import Api from '/@/api';

const treeData:Ref<Record<any, any>[]> = ref([]);
const selectTreeId:Ref<string> = ref('');
const selectRowKeys:Ref<string[]> = ref([]);
const router = useRouter();
const templateClassifyName:Ref<string> = ref('');
const powerData:Ref<any[]> = ref([]);
const powerCode = {
  addCode: 'GSMB_container_01_button_01',
  editCode: 'GSMB_container_01_button_02',
  deleteCode: 'GSMB_container_01_button_03',
};

provide('powerData', computed(() => powerData.value));
function selectTreeNode(id:string, node:any) {
  templateClassifyName.value = node.name;
  if (!selectTreeId.value) {
    selectTreeId.value = id;
  } else {
    selectTreeId.value = id;
    update();
  }
}
function initTreeData(data) {
  treeData.value = data;
}
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],
  api: (params) => {
    params.query = {
      templateClassifyId: selectTreeId.value,
    };

    return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplate/page', 'POST');
  },
  columns: [
    {
      title: '模版名称',
      dataIndex: 'name',
      minWidth: 200,
      ellipsis: true,
      customRender({ record, text }) {
        return isPower('GSMB_container_02_button_04', powerData) ? h('span', {
          title: text,
          onClick: () => {
            router.push({
              name: 'PMSEstimateTemplateDetails',
              params: {
                id: record.id,
              },
            });
          },
          class: 'action-btn',
        }, text) : text;
      },
    },
    {
      title: '包含科目数量',
      dataIndex: 'subjectNumber',
      width: 150,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
    },
    {
      title: '概算说明',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '负责人',
      dataIndex: 'modifyName',
      width: 120,
    },
    {
      title: '修改日期',
      ellipsis: true,
      dataIndex: 'modifyTime',
      width: 180,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => record.status !== 130 && isPower('GSMB_container_02_button_02', powerData),
      onClick(record) {
        initNode({
          drawerData: {
            type: 'edit',
            record,
          },
          treeData: treeData.value,
          update,
        });
      },
    },
    {
      text: '删除',
      isShow: (record) => record.status !== 130 && isPower('GSMB_container_02_button_02', powerData),
      onClick(record) {
        deleteBatchData([record.id], 'one', tableRef);
      },
    },
  ],
  //  beforeFetch,
});
function deleteBatch() {
  deleteBatchData(selectRowKeys.value, 'batch', tableRef);
}
function addTableNode() {
  initNode({
    drawerData: {
      type: 'add',
    },
    update,
    treeData: treeData.value,
    selectTreeId: selectTreeId.value,

  });
}

function update() {
  tableRef.value.reload({ page: 1 });
}
function getPowerDataHandle(data) {
  powerData.value = data;
}
</script>

<style lang="less" scoped>
.content {
}

.table-content {
  position: relative;
  height: 100%;
  .ant-empty {
    top: 50%;
    position: absolute;
    width: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.boxs {
  &.layout2-wrap {
    :deep(.left) {
      overflow: hidden !important;
      //background-color: red !important;
      box-sizing: border-box !important;
      width: 280px !important;
      margin-right: 0 !important;
      border-right: 1px solid #e5e7eb;

      .left-wrap {
        overflow: hidden !important;
        padding: 0 !important;
      }
    }
  }

  .demandTableTitle {
    display: flex;
    justify-content: space-between;
  }

  //:deep(.layout2-wrap){
  //  background-color: red !important;
  //  &>.left{
  //    box-sizing: content-box;
  //    width: 350px !important;
  //    margin-right: 0 !important;
  //    border-right: 1px solid #e5e7eb;
  //    .left-warp,.flex,.flex-ver{
  //      padding:0 !important;
  //    }
  //  }
  //}
}

</style>
