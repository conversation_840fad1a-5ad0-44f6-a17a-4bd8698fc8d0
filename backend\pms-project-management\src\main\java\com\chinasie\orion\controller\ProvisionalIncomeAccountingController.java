package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.ProvisionalIncomeAccountingDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInvoiced;
import com.chinasie.orion.domain.entity.ProvisionalIncomeAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.domain.vo.ProvisionalIncomeAccountingVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProvisionalIncomeAccountingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;



import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProvisionalIncomeAccounting 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:52:56
 */
@RestController
@RequestMapping("/provisionalIncomeAccounting")
@Api(tags = "暂估收入核算信息")
public class  ProvisionalIncomeAccountingController  {

    @Autowired
    private ProvisionalIncomeAccountingService provisionalIncomeAccountingService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "暂估收入核算信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProvisionalIncomeAccountingVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProvisionalIncomeAccountingVO rsp = provisionalIncomeAccountingService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 根据关联合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据关联合同id获取详情")
    @RequestMapping(value = "/detailByContractId/{contractId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】根据关联合同id获取详情了数据【{{#contractId}}】", type = "暂估收入核算信息", subType = "根据关联合同id获取详情", bizNo = "{{#contractId}}")
    public ResponseDTO<List<ProvisionalIncomeAccounting>> detailByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ProvisionalIncomeAccounting> rsp = provisionalIncomeAccountingService.detailByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param provisionalIncomeAccountingDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#provisionalIncomeAccountingDTO.name}}】", type = "暂估收入核算信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProvisionalIncomeAccountingDTO provisionalIncomeAccountingDTO) throws Exception {
        String rsp =  provisionalIncomeAccountingService.create(provisionalIncomeAccountingDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param provisionalIncomeAccountingDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#provisionalIncomeAccountingDTO.name}}】", type = "暂估收入核算信息", subType = "编辑", bizNo = "{{#provisionalIncomeAccountingDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProvisionalIncomeAccountingDTO provisionalIncomeAccountingDTO) throws Exception {
        Boolean rsp = provisionalIncomeAccountingService.edit(provisionalIncomeAccountingDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "暂估收入核算信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = provisionalIncomeAccountingService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "暂估收入核算信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = provisionalIncomeAccountingService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "暂估收入核算信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProvisionalIncomeAccountingVO>> pages(@RequestBody Page<ProvisionalIncomeAccountingDTO> pageRequest) throws Exception {
        Page<ProvisionalIncomeAccountingVO> rsp =  provisionalIncomeAccountingService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("暂估收入核算信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "暂估收入核算信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        provisionalIncomeAccountingService.downloadExcelTpl(response);
    }

    @ApiOperation("暂估收入核算信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "暂估收入核算信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = provisionalIncomeAccountingService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("暂估收入核算信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "暂估收入核算信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  provisionalIncomeAccountingService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消暂估收入核算信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "暂估收入核算信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  provisionalIncomeAccountingService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("暂估收入核算信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "暂估收入核算信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        provisionalIncomeAccountingService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("暂估收入核算信息统计")
    @GetMapping(value = "/getTotal")
    @LogRecord(success = "【{USER{#logUserId}}】统计数据", type = "InvoicingRevenueAccounting", subType = "统计数据", bizNo = "")
    public ResponseDTO<ProvisionalIncomeAccountingVO> getTotal(@RequestParam String contractId) throws Exception {
        ProvisionalIncomeAccountingVO rsp  = provisionalIncomeAccountingService.getTotal(contractId);
        return  new ResponseDTO<>(rsp);
    }
}
