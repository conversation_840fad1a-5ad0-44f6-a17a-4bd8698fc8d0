package com.chinasie.orion.service.reporting;


import java.lang.String;
import java.util.List;
import java.util.Map;

import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementContentDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatementContent;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementContentVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectDailyStatementContent 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
public interface ProjectDailyStatementContentService  extends OrionBaseService<ProjectDailyStatementContent> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectDailyStatementContentVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectDailyStatementContentDTO
     */
    ProjectDailyStatementContentVO create(ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectDailyStatementContentDTO
     */
    Boolean edit(ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectDailyStatementContentVO> pages(Page<ProjectDailyStatementContentDTO> pageRequest) throws Exception;

    /**
     * @param contentVOList
     *  批量创建内容
     */
    void createBatch(List<ProjectDailyStatementContentDTO> contentVOList) throws Exception;

    /**
     *  通过 日报ID列表获取 日报内容数据
     * @param idList
     * @return
     */
    Map<String,List<ProjectDailyStatementContentVO>> getMapByDataIdList(List<String> idList) throws Exception;

    /**
     * 删除数据日报数据列表
     * @param ids
     * @return
     */
    int deleteByDataIdList(List<String> ids);

    /**
     * 根据日报id获取内容
     * @param dailyId 日报id
     * @return
     */
    List<ProjectDailyStatementContent> getListByDailyId(String dailyId);

    /**
     * 根据日期获取明日日报内容
     * @param dailyIds 日期
     * @return
     */
    List<ProjectDailyStatementContentVO> getListByDailyIds(List<String> dailyIds);

    /**
     * 添加关联对象名称
     * @param contentVOList 日报内容数组
     * @return
     */
    void setRelationshipName(List<ProjectDailyStatementContentVO> contentVOList) throws Exception;

    /**
     * 添加关联对象类型
     * @param contentVOList 日报内容数组
     * @return
     */
    void setDTORelationshipType(List<ProjectDailyStatementContentDTO> contentVOList) throws Exception;

}
