<script setup lang="ts">

import { BasicCard, BasicSteps } from 'lyra-component-vue3';
import { computed, ref } from 'vue';

const currentStep = ref(0);
const defSteps = [
  {
    title: 'ECP公招',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '需求分发',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '需求确认',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '项目报价',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '投标确认',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '关联交易审查流程',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '合同签署',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
  {
    title: '项目立项',
    subTitle: '状态：已响应',
    disabled: true,
    description: '2024-01-23 12:34:56',
  },
];
const stepsConf = computed(() => defSteps);
</script>

<template>
  <BasicCard
    title="立项生命周期"
    :isBorder="false"
  >
    <BasicSteps
      v-model:current="currentStep"
      layout="horizontal"
      :steps="stepsConf"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>