package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.Size;

/**
 * ProjectApprovalEstimateTemplate DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateDTO对象", description = "概算模板")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateTemplateDTO extends ObjectDTO implements Serializable{

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 0)
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 100, message = "名称过长，建议控制在100字符以内")
    private String name;

    /**
     * 科目数量
     */
    @ApiModelProperty(value = "科目数量")
    private Integer subjectNumber;

    /**
     * 所属分类ID
     */
    @ApiModelProperty(value = "所属分类ID")
    private String templateClassifyId;



}
