<template>
  <div class="subject-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
      @initData="initData"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['GSMBXQ_container_01_button_01']"
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          添加科目
        </BasicButton>
        <BasicButton
          v-is-power="['GSMBXQ_container_01_button_02']"
          icon="delete"
          :disabled="selectRowKeys.length===0"
          @click="deleteBatch"
        >
          删除
        </BasicButton>
      </template>
      <template #required="{record}">
        <a-radio-group
          v-if="!Array.isArray(record.children) || record.children.length === 0"
          v-model:value="record.required"
          :options="requiredOptions"
          @change="changeValue($event,record)"
        />
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts" setup>
import {
  h, inject, Ref, ref, watch,
} from 'vue';
import {
  BasicButton, OrionTable, openModal, BasicTree, isPower,
} from 'lyra-component-vue3';
import { SelectListTable } from '/@/views/pms/components';
import { message, Modal, RadioGroup as ARadioGroup } from 'ant-design-vue';
import { Formula, SubjectTreeModal, setFormula } from '../index';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string
    subjectTree:any[]
}>(), {
  formId: '',
  subjectTree: () => [],
});
const requiredOptions = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];
watch(() => props.subjectTree, () => {
  tableRef.value.reload();
});
const tableRef = ref();
const selectRowKeys:Ref<string[]> = ref([]);
const getFormData = inject('getFormData', () => {});
const powerData = inject('powerData', []);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  expandIconColumnIndex: 4,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  api: (params) => Promise.resolve(props.subjectTree),
  columns: [
    {
      title: '科目序号',
      dataIndex: 'index',
      align: 'left',
      width: 100,
    },
    {
      title: '科目编码',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '科目名称',
      dataIndex: 'name',
      minWidth: 150,
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      width: 200,
      slots: { customRender: 'required' },
    },
    {
      title: '公式',
      dataIndex: 'formula',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [

    // {
    //   text: '查看',
    //   // isShow: (record) => record.status !== 130,
    //   onClick(record) {
    //   },
    // },
    {
      text: '设置公式',
      isShow: (record) => (!Array.isArray(record.children) || record.children.length === 0) && (record.formula === null || !record.formula) && isPower('GSMBXQ_container_01_button_03', powerData),
      onClick(record) {
        const formulaRef = ref();
        openModal({
          title: '设置公式',
          width: 780,
          height: 460,
          content(h) {
            return h(Formula, {
              ref: formulaRef,
              estimateTemplateId: props.formId,
            });
          },
          async onOk() {
            let formulaData = formulaRef.value.getFormValue();
            await setFormula({
              id: record.id,
              formulaName: formulaData.formulaValue.join(''),
              formula: formulaData.formulaCode.join(''),
            });
            message.success('设置公式成功');
            getFormData();
          },
        });
      },
    },
    {
      text: '取消公式',
      isShow: (record) => (!Array.isArray(record.children) || record.children.length === 0) && record.formula && isPower('GSMBXQ_container_01_button_02', powerData),
      // isShow: (record) => record.status !== 130,
      onClick(record) {
        Modal.confirm({
          title: '取消公式提示',
          content: '是否取消该行数据的公式？',
          onOk() {
            new Api('/pms').fetch('', `projectApprovalEstimateTemplateExpenseSubject/cancelFormula?id=${record.id}`, 'PUT').then((res) => {
              message.success('取消公式成功。');
              getFormData();
            });
          },
        });
      },
    },
  ],
  //  beforeFetch,
});
function addTableNode() {
  //
  const selectListTableRef = ref();
  openModal({
    title: '添加科目',
    width: 1100,
    height: 700,
    content(h) {
      return h(SubjectTreeModal, {
        ref: selectListTableRef,
      });
    },
    async onOk() {
      let selectData = await selectListTableRef.value.getSelectData();
      if (selectData.length === 0) {
        message.warning('请选择科目');
        return Promise.reject('');
      }
      let params = selectData.map((item) => ({
        estimateTemplateId: props.formId,
        expenseSubjectId: item.id,
        id: item.id,
        number: item.number,
        parentId: item.parentId,
        name: item.name,
      }));
      new Api('/pms').fetch(params, 'projectApprovalEstimateTemplateExpenseSubject/add/batch', 'POST').then((res) => {
        message.success('添加科目成功');
        getFormData();
      });
    },
  });
}
function deleteBatch() {
  Modal.confirm({
    title: '删除提示',
    content: '是否删除选中的数据',
    onOk() {
      new Api('/pms').fetch(selectRowKeys.value, 'projectApprovalEstimateTemplateExpenseSubject/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        getFormData();
      });
    },
  });
}
function initData(data:any[], indexColumns = '') {
  data.forEach((item, index) => {
    if (indexColumns) {
      item.index = `${indexColumns}.${index + 1}`;
    } else {
      item.index = index + 1;
    }
    if (Array.isArray(item.children)) {
      initData(item.children, item.index);
    }
  });
}
function changeValue(val, record) {
  new Api('/pms').fetch({
    id: record.id,
    required: val.target.value,
  }, 'projectApprovalEstimateTemplateExpenseSubject/edit', 'PUT').then((res) => {
    message.success('修改必填项成功');
  });
}
</script>
