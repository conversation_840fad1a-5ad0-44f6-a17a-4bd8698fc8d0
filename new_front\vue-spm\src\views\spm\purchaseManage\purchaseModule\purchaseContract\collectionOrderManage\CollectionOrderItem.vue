<script setup lang="ts">

import { Spin } from 'ant-design-vue';
import { Layout3, Layout3Content, BasicCard } from 'lyra-component-vue3';
import {
  computed, onMounted, reactive, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { setBasicInfo } from '../../utils';

interface MenuItem {
  id: string,
  name: string,
  children?: MenuItem[]
}

const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({
  name: '关于第八届科研成果日开展方案的第三次会议',
  ownerName: '张珊珊',
  projectCode: '申请单编码：3101202400101',
}));
const defaultActionId: Ref<string> = ref('jBXX');
const menuData: Ref<MenuItem[]> = ref([]);
const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '订单编号',
      field: 'orderUmber',
    },
    {
      label: '供应商名称',
      field: 'contractName',
    },
    {
      label: '退货金额',
      field: 'returnAmount',
    },
    {
      label: '企业名称',
      field: 'enterpriseName',
    },
    {
      label: '下单人',
      field: 'orderPlacer',
    },
    {
      label: '下单时间',
      field: 'orderTime',
    },
    {
      label: '订单最后一次交货时间',
      field: 'timeOfDelivery',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '订单最后一次确认收货时间',
      field: 'timeOfLastReceipt',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '发货耗时',
      field: 'usedTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '对账申请时间',
      field: 'reconciliationApplicationTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '对账确认时间',
      field: 'reconciliationConfirmationTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '申请开票时间',
      field: 'applicationForInvoicingTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '开票时间',
      field: 'invoicingTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '支付时间',
      field: 'paidTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: 'PO订单号',
      field: 'poOrderUmber',
    },
    {
      label: '部门',
      field: 'department',
    },
    {
      label: '供应商编码',
      field: 'contractId',
    },
    {
      label: '支付负责人',
      field: 'paymentManager',
    },
    {
      label: '验收方式',
      field: 'acceptanceMethod',
    },
    {
      label: '订单总金额（含税含费）',
      field: 'orderTotalAmount',
    },
    {
      label: '采购申请号',
      field: 'purchReqDocCode',
    },
    {
      label: 'PR行项目',
      field: 'prProjectId',
    },
    {
      label: '订单状态',
      field: 'orderState',
    },
    {
      label: '商品后台类目',
      field: 'commodityBackgroundCategory',
    },
    {
      label: '单品编码',
      field: 'itemCoding',
    },
    {
      label: '单品名称',
      field: 'itemName',
    },
    {
      label: '电商订单编号',
      field: 'eCommerceOrderNumber',
    },
    {
      label: '子订单号',
      field: 'suborderNumber',
    },
    {
      label: '订单金额',
      field: 'orderAmount',
    },
    {
      label: '应付金额',
      field: 'amountPayable',
    },
    {
      label: '结算状态',
      field: 'settlementStatus',
    },
    {
      label: '订单确认时间',
      field: 'orderConfirmationTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '订单审批时间',
      field: 'orderApprovalTime',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: 'PR公司名称',
      field: 'prCompanyName',
    },
    {
      label: '电商渠道订单号',
      field: 'commerceChannelOrderNumber',
    },
    {
      label: '对账人',
      field: 'reconciler',
    },
    {
      label: '收货负责人',
      field: 'consignee',
    },
    {
      label: '收货审核人',
      field: 'receiptReviewer',
    },
    {
      label: '结算方式',
      field: 'settlementMethod',
    },
    {
      label: '要求到货日期',
      field: 'requestDeliveryDate',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    },
    {
      label: '下单人电话',
      field: 'orderPhoneNumber',
    },
  ]),
  column: 2,
  dataSource: {},
});
const getPowerDataHandle = async (data: any) => {
  menuData.value = menuData.value.concat([
    {
      id: 'jBXX',
      name: '基本信息',
    },
  ]);
};
function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}
const getDetail = async () => {
  try {
    const res = await new Api('/spm/ncfFormPurchOrder').fetch('', unref(projectId), 'GET');
    baseInfoProps.dataSource = res;
  } catch (e) {

  }
};
onMounted(async () => {
  await getPowerDataHandle();
  await getDetail();
});
</script>

<template>
  <Layout3
    :projectData="layoutData"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicCard
        title="基本信息"
        :grid-content-props="baseInfoProps"
        :isBorder="false"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">

</style>