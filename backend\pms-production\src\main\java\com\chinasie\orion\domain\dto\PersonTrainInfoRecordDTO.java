package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PersonTrainInfoRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
@ApiModel(value = "PersonTrainInfoRecordDTO对象", description = "用户培训信息落地")
@Data
@ExcelIgnoreUnannotated
public class PersonTrainInfoRecordDTO extends  ObjectDTO   implements Serializable{

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @ExcelProperty(value = "培训编码 ", index = 0)
    private String trainNumber;

    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    @ExcelProperty(value = "培训名称 ", index = 1)
    private String trainName;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    @ExcelProperty(value = "培训基地名称 ", index = 2)
    private String baseName;

    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    @ExcelProperty(value = "培训基地编码 ", index = 3)
    private String baseCode;

    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    @ExcelProperty(value = "培训课时 ", index = 4)
    private BigDecimal lessonHour;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @ExcelProperty(value = "完成时间 ", index = 5)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @ExcelProperty(value = "是否等效 ", index = 6)
    private Boolean isEquivalent;

    /**
     * 到期时间时间
     */
    @ApiModelProperty(value = "到期时间时间")
    @ExcelProperty(value = "到期时间时间 ", index = 7)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    @ExcelProperty(value = "培训讲师 ", index = 8)
    private String trainLecturer;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    @ExcelProperty(value = "培训内容 ", index = 9)
    private String content;


    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @ApiModelProperty(value = "来源ID - 岗位授权id")
    private String sourceId;
}
