import { defineStore } from 'pinia';

export const statisticType = defineStore({
  id: 'statisticType',
  state: (): any => ({
    statisticTypeId: '',
    statisticTableName: '',
    projectid: '',
    tableandechart: <any>{},
  }),
  getters: {
    getstatisticTypeId() {
      return this.statisticTypeId;
    },
    getTableName() {
      return this.statisticTableName;
    },
    getprojectid() {
      return this.projectid;
    },
    gettableandechart() {
      return this.tableandechart;
    },
  },
  actions: {
    setstatisticTypeId(val) {
      this.statisticTypeId = val;
    },
    setTableName(val) {
      this.statisticTableName = val;
    },
    setprojectid(val) {
      this.projectid = val;
    },
    settableandechart(val) {
      this.tableandechart = val;
    },
    clear() {
      this.statisticTypeId = '';
      this.statisticTableName = '';
      this.projectid = '';
      this.tableandechart = {};
    },
  },
});
