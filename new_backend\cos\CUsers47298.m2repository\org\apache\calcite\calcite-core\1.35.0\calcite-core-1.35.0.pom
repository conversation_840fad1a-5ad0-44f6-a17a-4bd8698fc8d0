<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.calcite</groupId>
  <artifactId>calcite-core</artifactId>
  <version>1.35.0</version>
  <name>Calcite Core</name>
  <description>Core Calcite APIs and engine</description>
  <url>https://calcite.apache.org</url>
  <inceptionYear>2012</inceptionYear>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <mailingLists>
    <mailingList>
      <name>Apache Calcite developers list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/calcite.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/calcite.git</developerConnection>
    <url>https://github.com/apache/calcite</url>
  </scm>
  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/CALCITE</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>org.apache.calcite</groupId>
      <artifactId>calcite-linq4j</artifactId>
      <version>1.35.0</version>
    </dependency>
    <dependency>
      <groupId>org.locationtech.jts</groupId>
      <artifactId>jts-core</artifactId>
      <version>1.19.0</version>
    </dependency>
    <dependency>
      <groupId>org.locationtech.jts.io</groupId>
      <artifactId>jts-io-common</artifactId>
      <version>1.19.0</version>
    </dependency>
    <dependency>
      <groupId>org.locationtech.proj4j</groupId>
      <artifactId>proj4j</artifactId>
      <version>1.2.2</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.15.0</version>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.5.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>19.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.calcite.avatica</groupId>
      <artifactId>avatica-core</artifactId>
      <version>1.23.0</version>
    </dependency>
    <dependency>
      <groupId>org.apiguardian</groupId>
      <artifactId>apiguardian-api</artifactId>
      <version>1.1.2</version>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.10.0</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.33</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.15.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.15.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
      <version>2.15.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.uzaygezen</groupId>
      <artifactId>uzaygezen-core</artifactId>
      <version>0.2</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.jayway.jsonpath</groupId>
      <artifactId>json-path</artifactId>
      <version>2.7.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.yahoo.datasketches</groupId>
      <artifactId>sketches-core</artifactId>
      <version>0.9.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.13</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>net.hydromatic</groupId>
      <artifactId>aggdesigner-algorithm</artifactId>
      <version>6.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-dbcp2</artifactId>
      <version>2.6.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.8</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-math3</artifactId>
      <version>3.6.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.codehaus.janino</groupId>
      <artifactId>commons-compiler</artifactId>
      <version>3.1.9</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.codehaus.janino</groupId>
      <artifactId>janino</artifactId>
      <version>3.1.9</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
