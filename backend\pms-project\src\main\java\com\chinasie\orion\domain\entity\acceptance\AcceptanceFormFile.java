package com.chinasie.orion.domain.entity.acceptance;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;

import java.io.Serializable;

/**
 * 验收文件.
 *
 * <AUTHOR>
 */
@TableName(value = "pms_acceptance_form_file")//, code = "07m4",  type = "common", defaultIdValue = false)
public class AcceptanceFormFile extends ObjectEntity implements Serializable {

    // 验收单Id
    @TableField("acceptance_form_id")
    private String acceptanceFormId;

    // res文件Id
    @TableField("file_id")
    private String fileId;

    public String getAcceptanceFormId() {
        return acceptanceFormId;
    }

    public void setAcceptanceFormId(String acceptanceFormId) {
        this.acceptanceFormId = acceptanceFormId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }
}
