package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ProjectMaterialPreparationInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:53:32
 */
@ApiModel(value = "ProjectMaterialPreparationInfoDTO对象", description = "备料信息")
@Data
@ExcelIgnoreUnannotated
public class ProjectMaterialPreparationInfoDTO extends  ObjectDTO   implements Serializable{

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    @NotBlank(message = "任务类型不能为空")
    @ExcelProperty(value = "任务类型 ", index = 0)
    private String taskType;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @NotBlank(message = "物料编码不能为空")
    @ExcelProperty(value = "编码 ", index = 1)
    private String materialNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @NotNull(message = "数量不能为空")
    @ExcelProperty(value = "数量 ", index = 2)
    private Integer num;

    /**
     * 替代料组
     */
    @ApiModelProperty(value = "替代料组")
    @ExcelProperty(value = "替代组 ", index = 3)
    private Integer replaceGroup;

    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    private Date requireCompleteTime;

    @ExcelProperty(value = "要求完成日期 ", index = 4)
    private String requireCompleteTimeStr;

    /**
     * 最短采购周期
     */
    @ApiModelProperty(value = "最短采购周期")
    private BigDecimal minProcurementCycle;

    /**
     * 标准采购周期
     */
    @ApiModelProperty(value = "标准采购周期")
    private BigDecimal procurementCycle;

    /**
     * 备料id
     */
    @ApiModelProperty(value = "备料id")
    private String preparationId;

    @ApiModelProperty("备注")
    @Size(max = 512)
    @ExcelProperty(value = "备注 ", index = 5)
    private String remark;



    /**
     * 源
     */
    @ApiModelProperty(value = "源")
    private String source;


    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String basicUnit;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String materialName;


    /**
     * 上版本数量
     */
    @ApiModelProperty(value = "上版本数量")
    private Integer preRevNum;

    /**
     * 差异原因
     */
    @ApiModelProperty(value = "差异原因")
    private String diffReason;
}
