package com.chinasie.orion.exp;

import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 14 日
 **/
@Component
@Slf4j
public class LeaderFindDepartmentByDeptCodeExp implements IExp {

    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    @Autowired
    private DeptUserDOMapper deptUserDOMapper;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;


    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "领导所属的机构及其下属机构";
    }

    /**
     * 查找当前用户是否是 领导 =》通过领导组  或者主管领导
     * 如果是领导组的化，就查找其所属的机构及其下属机构
     * @param s
     * @return
     */
    @Override
    public List<String> exp(String s) {
        log.info("查找当前领导用户的机构及其下属机构");

        String currentUserId = CurrentUserHelper.getCurrentUserId();

        //看当前用户是否是领导
        LambdaQueryWrapperX<DeptLeaderDO> queryLeaderWrapper=new LambdaQueryWrapperX<>(DeptLeaderDO.class);
        queryLeaderWrapper.eq(DeptLeaderDO::getUserId,currentUserId);
        queryLeaderWrapper.select(DeptLeaderDO::getDeptId);
        boolean exists = deptLeaderDORepository.exists(queryLeaderWrapper);
        if(exists){
            //查找当前领导所属的机构及其下属机构的所有的成员的
            List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(queryLeaderWrapper);
            List<String> leaderDeptIds = deptLeaderDOS.stream().map(DeptLeaderDO::getDeptId).distinct().collect(Collectors.toList());
            //查找下属的部门

            Map<String, DeptVO> deptVOMap = deptRedisHelper.mapAllDept();

            List<DeptVO> deptVOS= Lists.newArrayList();

            deptVOMap.forEach((k,vo)->deptVOS.add(vo));

            Map<String, List<DeptVO>> parentDeptVoMap = deptVOS.stream().collect(Collectors.groupingBy(DeptVO::getParentId));

            List<String> deptCodes=Lists.newArrayList();

            leaderDeptIds.forEach(parntId->{
                findSubDept(parntId,deptCodes,parentDeptVoMap,deptVOMap);
            });

            if(CollectionUtils.isEmpty(deptCodes)){
                log.info("当前领导用户的机构为空");
                return Arrays.asList("");
            }
            return deptCodes;

        }else {
            log.info("当前用户不是领导");
        }
        return Arrays.asList("");
    }

    /**
     * 查找下级
     *
     * @param parntId
     * @param deptCodes
     * @param parentDeptVoMap
     * @param deptVOMap
     */
    private void findSubDept(String parntId, List<String> deptCodes, Map<String, List<DeptVO>> parentDeptVoMap, Map<String, DeptVO> deptVOMap) {
        if(deptVOMap.containsKey(parntId)){
            DeptVO deptVO = deptVOMap.get(parntId);
            deptCodes.add(deptVO.getDeptCode());
            if(parentDeptVoMap.containsKey(parntId)){
                List<DeptVO> deptVOS = parentDeptVoMap.get(parntId);
                List<String> tempDeptIds = deptVOS.stream().map(DeptVO::getId).collect(Collectors.toList());
                tempDeptIds.forEach(tempDeptId->{
                    findSubDept(tempDeptId,deptCodes,parentDeptVoMap, deptVOMap);
                });
            }
        }

    }

    @Override
    public Boolean apply() {
        return true;
    }
}
