package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * customerInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@ApiModel(value = "customerInfoVO对象", description = "客户管理")
@Data
public class CustomerInfoVO extends ObjectVO implements Serializable {

    /**
     * 冗余字段
     */
    @ApiModelProperty(value = "冗余字段")
    private String comnumber;


    /**
     * 工商注册号
     */
    @ApiModelProperty(value = "工商注册号")
    private String busRegisterCode;


    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    private String taxIdCode;


    /**
     * 企业全称
     */
    @ApiModelProperty(value = "企业全称")
    private String cusFullName;


    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别")
    private String cusLevel;

    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    private String cusLevelName;


    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;


    /**
     * 组织机构代码
     */
    @ApiModelProperty(value = "组织机构代码")
    private String organizatioinCode;


    /**
     * 营业期限
     */
    @ApiModelProperty(value = "营业期限")
    private String bizPeriod;


    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    private String registeredCapital;


    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String uniformCreditCode;


    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    private String cusCategory;


    /**
     * 成立日期
     */
    @ApiModelProperty(value = "成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;


    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    private String businessScope;


    /**
     * 税务登记证号
     */
    @ApiModelProperty(value = "税务登记证号")
    private String comtaxnumber;


    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String registeredAddress;


    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalRepr;


    /**
     * 创建日期
     */
    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cusCeateTime;


    /**
     * 公众号信息
     */
    @ApiModelProperty(value = "公众号信息")
    private String publicAccountInfo;


    /**
     * 员工人数
     */
    @ApiModelProperty(value = "员工人数")
    private String cusNumCount;


    /**
     * 客户联系地址
     */
    @ApiModelProperty(value = "客户联系地址")
    private String cusAddress;


    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String ywsrlx;

    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    private String ywsrlxName;


    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    private String industry;

    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;


    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String busScope;

    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    private String busScopeName;


    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 客户关系名称(集团内外)
     */
    @ApiModelProperty(value = "客户关系名称(集团内外)")
    private String groupInOutName;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String cusRemark;


    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    private String cusStatus;

    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    private String cusStatusName;


    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    private String customerdepartmentent;


    /**
     * 所属集团
     */
    @ApiModelProperty(value = "所属集团")
    private String groupInfo;


    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String cusNumber;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String cusName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 公司类型
     */
    @ApiModelProperty(value = "公司类型")
    private String comtpye;

    /**
     * 资质信息
     */
    @ApiModelProperty(value = "资质信息")
    private String zzxx;

    /**
     * 登记状态
     */
    @ApiModelProperty(value = "登记状态")
    private String registStatus;

    /**
     * 实缴资本
     */
    @ApiModelProperty(value = "实缴资本")
    private String paidInCapital;

    /**
     * 核准日期
     */
    @ApiModelProperty(value = "核准日期")
    private Date approvedDate;

    /**
     * 所属省份
     */
    @ApiModelProperty(value = "所属省份")
    private String province;

    /**
     * 所属城市
     */
    @ApiModelProperty(value = "所属城市")
    private String city;

    /**
     * 所属区县
     */
    @ApiModelProperty(value = "所属区县")
    private String county;

    /**
     * 国标行业门类
     */
    @ApiModelProperty(value = "国标行业门类")
    private String category;

    /**
     * 国标行业大类
     */
    @ApiModelProperty(value = "国标行业大类")
    private String largeCategory;

    /**
     * 国标行业中类
     */
    @ApiModelProperty(value = "国标行业中类")
    private String middleCategory;

    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String englishName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String tel;

    /**
     * 其他电话
     */
    @ApiModelProperty(value = "其他电话")
    private String otherTel;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 是否被使用
     */
    @ApiModelProperty(value = "是否被使用0-没有被使用,1-被使用")
    private String isUsed;

    /**
     * 是否关联人士
     */
    @ApiModelProperty(value = "是否关联人士")
    private String isPerson;

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地")
    private String homeBase;

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地名称")
    private String homeBaseName;

    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    private String salesClass;

    /**
     * 销售业务分类名称
     */
    @ApiModelProperty(value = "销售业务分类名称")
    private String salesClassName;

}
