package com.chinasie.orion.bo;

import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.DocumentFeignService;
import com.chinasie.orion.util.ResponseUtils;
import com.google.common.collect.Maps;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/01/16:29
 * @description:
 */
@Component
public class DocumentBo {

    @Resource
    private DocumentFeignService documentFeignService;

    public String insertDocument(DocumentDTO documentDTO) throws Exception {
        ResponseDTO<String> stringResponseDTO = documentFeignService.addDocument(DocumentBo.changeMap(documentDTO));
        if (ResponseUtils.fail(stringResponseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, stringResponseDTO.getMessage());
        }
        return  stringResponseDTO.getResult();
    }

    public void updateDocument(DocumentDTO documentDTO) throws Exception {
        ResponseDTO<String> stringResponseDTO = documentFeignService.editDocument(DocumentBo.changeMap(documentDTO));
        if (ResponseUtils.fail(stringResponseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, stringResponseDTO.getMessage());
        }
    }

    public List<DocumentDTO> getListByIdList(List<String> ids) throws Exception {
        ResponseDTO<List<DocumentDTO>> stringResponseDTO = documentFeignService.queryDocumentListByIdList(ids);
        if (ResponseUtils.fail(stringResponseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, stringResponseDTO.getMessage());
        }
        return stringResponseDTO.getResult();
    }

    public void delByIdList(List<String> ids) throws Exception {
        ResponseDTO<Boolean> stringResponseDTO = documentFeignService.delByIdList(ids);
        if (ResponseUtils.fail(stringResponseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, stringResponseDTO.getMessage());
        }
    }

    public static Map<String, Object> changeMap(Object obj) {
        Map<String, Object> map = Maps.newHashMap();
        if (obj != null) {
            BeanMap beanMap = BeanMap.create(obj);
            for (Object key : beanMap.keySet()) {
                map.put(key + "", beanMap.get(key));
            }
        }
        return map;
    }

}
