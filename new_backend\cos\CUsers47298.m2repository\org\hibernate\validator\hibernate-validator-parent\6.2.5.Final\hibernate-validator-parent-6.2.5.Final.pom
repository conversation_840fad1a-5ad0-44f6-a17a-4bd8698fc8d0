<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Hibernate Validator, declare and validate application constraints
  ~
  ~ License: Apache License, Version 2.0
  ~ See the license.txt file in the root directory or <http://www.apache.org/licenses/LICENSE-2.0>.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.hibernate.validator</groupId>
    <artifactId>hibernate-validator-parent</artifactId>
    <version>6.2.5.Final</version>
    <packaging>pom</packaging>

    <name>Hibernate Validator Aggregator</name>
    <url>http://hibernate.org/validator</url>
    <description>Aggregator of the Hibernate Validator modules.</description>

    <developers>
        <developer>
            <id>epbernard</id>
            <name><PERSON></name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/emmanuel-bernard/</url>
        </developer>
        <developer>
            <id>hardy.ferentschik</id>
            <name>Hardy Ferentschik</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/hardy-ferentschik/</url>
        </developer>
        <developer>
            <id>gunnar.morling</id>
            <name>Gunnar Morling</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/gunnar-morling/</url>
        </developer>
        <developer>
            <id>kevinpollet</id>
            <name>Kevin Pollet</name>
            <email><EMAIL></email>
            <organization>SERLI</organization>
            <url>http://www.serli.com/</url>
        </developer>
        <developer>
            <id>davide.dalto</id>
            <name>Davide D'Alto</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/davide-dalto/</url>
        </developer>
        <developer>
            <id>guillaume.smet</id>
            <name>Guillaume Smet</name>
            <email><EMAIL></email>
            <organization>Red Hat, Inc.</organization>
            <url>http://in.relation.to/guillaume-smet/</url>
        </developer>
        <developer>
            <id>marko.bekhta</id>
            <name>Marko Bekhta</name>
            <email><EMAIL></email>
            <url>http://in.relation.to/marko-bekhta/</url>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>George Gastaldi</name>
            <email><EMAIL></email>
        </contributor>
    </contributors>

    <mailingLists>
        <mailingList>
            <name>hibernate-dev</name>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>

    <modules>
        <module>test-utils</module>
        <module>build-config</module>
        <module>engine</module>
        <module>tck-runner</module>
        <module>annotation-processor</module>
        <module>performance</module>
        <module>cdi</module>
        <module>modules</module>
        <module>integration</module>
    </modules>

    <properties>
        <!-- Version to be used as baseline for API/SPI change reports -->

        <previous.stable>6.0.10.Final</previous.stable>

        <!-- URLs used in javadoc and documentation generation -->

        <bv.spec.url>http://beanvalidation.org/2.0/spec/</bv.spec.url>
        <java.api-docs.base-url>http://docs.oracle.com/javase/8/docs/api</java.api-docs.base-url>
        <java.technotes.base-url>http://docs.oracle.com/javase/8/docs/technotes</java.technotes.base-url>
        <javaee.api-docs.base-url>http://docs.oracle.com/javaee/7/api</javaee.api-docs.base-url>
        <javafx.docs.url>http://docs.oracle.com/javase/8/javase-clienttechnologies.htm</javafx.docs.url>
        <bv.api-docs.base-url>http://docs.jboss.org/hibernate/beanvalidation/spec/2.0/api</bv.api-docs.base-url>
        <javamoney.api-docs.base-url>http://javamoney.github.io/apidocs</javamoney.api-docs.base-url>

        <!-- Module names used for Java 9 modules and OSGi bundles -->

        <hibernate-validator.module-name>org.hibernate.validator</hibernate-validator.module-name>
        <hibernate-validator-cdi.module-name>org.hibernate.validator.cdi</hibernate-validator-cdi.module-name>

        <!-- Dependencies versions -->

        <version.jakarta.validation-api>2.0.2</version.jakarta.validation-api>
        <version.org.hibernate.beanvalidation.tck>2.0.6</version.org.hibernate.beanvalidation.tck>

        <version.com.thoughtworks.paranamer>2.8</version.com.thoughtworks.paranamer>
        <version.org.glassfish.jakarta.el>3.0.3</version.org.glassfish.jakarta.el>
        <version.org.jboss.logging.jboss-logging>3.4.1.Final</version.org.jboss.logging.jboss-logging>
        <version.org.jboss.logging.jboss-logging-tools>2.2.1.Final</version.org.jboss.logging.jboss-logging-tools>

        <!-- Currently supported version of WildFly -->
        <version.wildfly>22.0.0.Final</version.wildfly>
        <!-- Used to create a patch file for the second version of WildFly we support -->
        <!--<version.wildfly.secondary>18.0.1.Final</version.wildfly.secondary>-->
        <!-- Version used to run the TCK in incontainer mode -->
        <version.wildfly.tck>${version.wildfly}</version.wildfly.tck>

        <!--
            These dependencies should be aligned with the ones from the WildFly version we support
            See http://search.maven.org/#search|gav|1|g%3A"org.wildfly"%20AND%20a%3A"wildfly-parent"
        -->
        <version.com.fasterxml.classmate>1.5.1</version.com.fasterxml.classmate>
        <version.joda-time>2.9.7</version.joda-time>
        <version.org.slf4j>1.7.22</version.org.slf4j>
        <version.org.apache.logging.log4j>2.17.1</version.org.apache.logging.log4j>
        <version.jakarta.persistence-api>2.2.3</version.jakarta.persistence-api>

        <!--
            These dependencies are used for integration tests with WildFly.
            They should be aligned with the ones from the Wildfly version we support
            See http://search.maven.org/#search|gav|1|g%3A"org.wildfly"%20AND%20a%3A"wildfly-parent"
        -->
        <version.jakarta.enterprise.cdi-api>2.0.1</version.jakarta.enterprise.cdi-api>
        <version.org.jboss.weld.weld>3.1.1.Final</version.org.jboss.weld.weld>
        <version.org.wildfly.arquillian>2.2.0.Final</version.org.wildfly.arquillian>
        <version.jakarta.ejb-api>3.2.5</version.jakarta.ejb-api>
        <version.jakarta.interceptor-api>1.2.4</version.jakarta.interceptor-api>
        <version.jakarta.annotation-api>1.3.5</version.jakarta.annotation-api>
        <version.jakarta.ws.rs-api>2.1.6</version.jakarta.ws.rs-api>

        <!-- JavaMoney dependencies -->
        <version.javax.money>1.0.1</version.javax.money>
        <version.org.javamoney.moneta>1.1</version.org.javamoney.moneta>

        <!-- Used in the Karaf features file: it is a dependency of Moneta, and for the documentation -->
        <version.javax.annotation>1.2</version.javax.annotation>

        <!-- JavaFX dependencies (from JDK 11) -->
        <version.org.openjfx>11.0.2</version.org.openjfx>

        <!-- Test dependencies -->
        <version.org.jboss.arquillian>1.6.0.Final</version.org.jboss.arquillian>
        <version.org.testng>6.14.3</version.org.testng>
        <!-- it must be the exact same version than the one used in the Bean Validation TCK -->
        <version.org.assertj.assertj-core>3.8.0</version.org.assertj.assertj-core>
        <version.junit>4.13.1</version.junit>
        <version.org.easymock>3.4</version.org.easymock>
        <version.io.rest-assured>4.1.2</version.io.rest-assured>
        <version.org.codehaus.groovy>2.4.12</version.org.codehaus.groovy>
        <version.com.google.guava>30.1.1-jre</version.com.google.guava>
        <version.org.springframework.spring-expression>5.3.22</version.org.springframework.spring-expression>
        <version.org.jboss.arquillian.container.arquillian-weld-se-embedded-1.1>1.0.0.Final</version.org.jboss.arquillian.container.arquillian-weld-se-embedded-1.1>
        <version.com.fasterxml.jackson.core.jackson-databind>2.13.2.2</version.com.fasterxml.jackson.core.jackson-databind>
        <version.com.fasterxml.jackson.core.jackson-annotations>2.13.2</version.com.fasterxml.jackson.core.jackson-annotations>
        <version.net.bytebuddy.byte-buddy>1.10.22</version.net.bytebuddy.byte-buddy>

        <!-- OSGi dependencies -->
        <version.org.apache.karaf>4.2.0</version.org.apache.karaf>
        <version.org.ops4j.pax.exam>4.12.0</version.org.ops4j.pax.exam>
        <version.org.ops4j.pax.url>2.5.4</version.org.ops4j.pax.url>
        <version.org.osgi.core>6.0.0</version.org.osgi.core>
        <version.fish.payara>5.2020.2</version.fish.payara>
        <version.fish.payara.arquillian>2.3.1</version.fish.payara.arquillian>

        <!--
            Please don't change the name of this property, it may be used and
            overridden by a CI job of the Checkstyle project so that they can
            check for regressions. Which is obviously good for us, too.
         -->
        <puppycrawl.checkstyle.version>8.38</puppycrawl.checkstyle.version>

        <!-- Asciidoctor -->

        <version.asciidoctor.plugin>1.5.6</version.asciidoctor.plugin>
        <version.org.hibernate.infra.hibernate-asciidoctor-theme>1.0.3.Final</version.org.hibernate.infra.hibernate-asciidoctor-theme>
        <version.org.hibernate.infra.hibernate-asciidoctor-extensions>1.0.3.Final</version.org.hibernate.infra.hibernate-asciidoctor-extensions>
        <version.org.jruby>9.1.15.0</version.org.jruby>
        <version.org.asciidoctor.asciidoctorj>1.6.0-alpha.5</version.org.asciidoctor.asciidoctorj>
        <version.org.asciidoctor.asciidoctorj-pdf>1.5.0-alpha.16</version.org.asciidoctor.asciidoctorj-pdf>

        <!-- Maven plugins versions -->

        <version.antrun.plugin>1.8</version.antrun.plugin>
        <version.assembly.plugin>3.4.2</version.assembly.plugin>
        <version.buildhelper.plugin>3.0.0</version.buildhelper.plugin>
        <version.bundle.plugin>3.5.0</version.bundle.plugin>
        <version.checkstyle.plugin>3.1.1</version.checkstyle.plugin>
        <version.clean.plugin>3.0.0</version.clean.plugin>
        <version.compiler.plugin>3.8.1</version.compiler.plugin>
        <version.copy.plugin>0.0.6</version.copy.plugin>
        <version.dependency.plugin>3.0.2</version.dependency.plugin>
        <version.depends.plugin>1.4.0</version.depends.plugin>
        <version.deploy.plugin>2.8.2</version.deploy.plugin>
        <version.enforcer.plugin>3.0.0-M1</version.enforcer.plugin>
        <version.forbiddenapis.plugin>3.2</version.forbiddenapis.plugin>
        <version.gmavenplus.plugin>1.6</version.gmavenplus.plugin>
        <version.gpg.plugin>3.0.1</version.gpg.plugin>
        <version.install.plugin>2.5.2</version.install.plugin>
        <version.japicmp.plugin>0.11.0</version.japicmp.plugin>
        <version.jar.plugin>3.0.2</version.jar.plugin>
        <version.jqassistant.plugin>1.11.1</version.jqassistant.plugin>
        <version.javadoc.plugin>3.0.1</version.javadoc.plugin>
        <version.license.plugin>3.0</version.license.plugin>
        <version.release.plugin>2.5.3</version.release.plugin>
        <version.resources.plugin>3.0.2</version.resources.plugin>
        <version.shade.plugin>3.1.0</version.shade.plugin>
        <version.sigtest.plugin>1.2</version.sigtest.plugin>
        <version.source.plugin>3.0.1</version.source.plugin>
        <version.surefire.plugin>2.21.0</version.surefire.plugin>
        <version.surefire.plugin.java-version.asm>9.2</version.surefire.plugin.java-version.asm>
        <version.failsafe.plugin>${version.surefire.plugin}</version.failsafe.plugin>
        <version.wildfly.plugin>1.2.1.Final</version.wildfly.plugin>
        <version.org.wildfly.core>14.0.0.Final</version.org.wildfly.core>
        <version.wildfly-patch-gen.plugin>2.1.0.Final</version.wildfly-patch-gen.plugin>
        <version.wildfly-patch-gen.plugin.woodstox>5.0.3</version.wildfly-patch-gen.plugin.woodstox>
        <version.nexus-staging-maven-plugin>1.6.8</version.nexus-staging-maven-plugin>

        <!-- Forbidden API related properties -->
        <forbiddenapis-junit.path>forbidden-junit.txt</forbiddenapis-junit.path>

        <!-- Repository Deployment URLs -->

        <ossrh.releases.repo.id>ossrh</ossrh.releases.repo.id>
        <ossrh.releases.repo.url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</ossrh.releases.repo.url>
        <ossrh.releases.repo.baseUrl>https://oss.sonatype.org/</ossrh.releases.repo.baseUrl>
        <ossrh.snapshots.repo.id>ossrh</ossrh.snapshots.repo.id>
        <ossrh.snapshots.repo.url>https://oss.sonatype.org/content/repositories/snapshots</ossrh.snapshots.repo.url>

        <!-- Maven Central repository -->

        <mavencentral.repo.url>https://repo.maven.apache.org/maven2/</mavencentral.repo.url>

        <!-- Build settings -->

        <hibernate-validator-parent.path>.</hibernate-validator-parent.path>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- The lowest supported version of Java for applications using Hibernate Validator -->
        <!-- Set statically, independently of the current JDK: we want our code to comply with this version -->
        <java-version.main.release>8</java-version.main.release>
        <java-version.main.compiler.java_home>${java.home}</java-version.main.compiler.java_home>
        <java-version.main.compiler>${java-version.main.compiler.java_home}/bin/javac</java-version.main.compiler>
        <!-- The version of test bytecode, useful for testing compatibility with newer JDKs -->
        <!-- Set to the version of the JDK running Maven by default, but overridden on CI -->
        <!-- 'java.specification.version' is only correct from Java 9 onwards, but we require Java 17 to run the build anyway -->
        <java-version.test.release>${java.specification.version}</java-version.test.release>
        <java-version.test.compiler.java_home>${java.home}</java-version.test.compiler.java_home>
        <java-version.test.compiler>${java-version.test.compiler.java_home}/bin/javac</java-version.test.compiler>
        <!-- IMPORTANT: For Java 8, this must be the path to the JDK, not to the JRE -->
        <java-version.test.launcher.java_home>${java-version.test.compiler.java_home}</java-version.test.launcher.java_home>
        <java-version.test.launcher>${java-version.test.launcher.java_home}/bin/java</java-version.test.launcher>
        <java-version.test.java17.add-test-source-phase>generate-test-sources</java-version.test.java17.add-test-source-phase>

        <!-- No need to build the javadocs per module. Aggregated javadocs are build in the distribution module. See also HV-894 -->
        <maven.javadoc.skip>true</maven.javadoc.skip>

        <!-- maven.compiler.release and maven.compiler.testRelease are set in a profile; see below -->
        <!-- Also set source/target, because several other plugins rely on this and don't understand release -->
        <maven.compiler.source>${java-version.main.release}</maven.compiler.source>
        <maven.compiler.target>${java-version.main.release}</maven.compiler.target>
        <maven.compiler.testSource>${java-version.test.release}</maven.compiler.testSource>
        <maven.compiler.testTarget>${java-version.test.release}</maven.compiler.testTarget>

        <forbiddenapis.jdk.target>10</forbiddenapis.jdk.target>

        <!-- Set empty default values to avoid Maven leaving property references (${...}) when it doesn't find a value -->
        <surefire.jvm.args.additional></surefire.jvm.args.additional>
        <surefire.jvm.args.illegal-access></surefire.jvm.args.illegal-access>
        <surefire.jvm.args.add-opens></surefire.jvm.args.add-opens>
        <!--
          The arguments below are Shrinkwrap settings taken from
          https://github.com/shrinkwrap/resolver/blob/788a3c1148af3a7ebdfdaf817393273f5f5ee17b/impl-maven/src/main/java/org/jboss/shrinkwrap/resolver/impl/maven/bootstrap/MavenSettingsBuilder.java#L80
          - maven.repo.local: necessary for the Shrinkwrap artifact retrieval to work when using a non-default local repository
         -->
        <surefire.jvm.args.shrinkwrap>
            -Dmaven.repo.local=${settings.localRepository}
        </surefire.jvm.args.shrinkwrap>
        <surefire.jvm.args>${surefire.jvm.args.additional} ${surefire.jvm.args.add-opens} ${surefire.jvm.args.illegal-access} ${surefire.jvm.args.shrinkwrap}</surefire.jvm.args>
        <failsafe.jvm.args>${surefire.jvm.args.additional} ${surefire.jvm.args.add-opens} ${surefire.jvm.args.illegal-access} ${surefire.jvm.args.shrinkwrap}</failsafe.jvm.args>

        <!--
            Should be set from the command line.
            Used by CI jobs that execute tests in multiple environments.
            Allows distinguishing between multiple executions of the same test in test reports.
         -->
        <surefire.environment>default</surefire.environment>

        <arquillian.wildfly.jvm.args.add-opens></arquillian.wildfly.jvm.args.add-opens>
        <arquillian.wildfly.jvm.args.add-modules></arquillian.wildfly.jvm.args.add-modules>
        <arquillian.wildfly.jvm.args>-Duser.language=en ${arquillian.wildfly.jvm.args.add-opens} ${arquillian.wildfly.jvm.args.add-modules}</arquillian.wildfly.jvm.args>

        <!-- JDK version required for the build -->
        <jdk.min.version>17</jdk.min.version>
        <maven.min.version>3.3.1</maven.min.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator-test-utils</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator-cdi</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator-annotation-processor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator-modules</artifactId>
                <version>${project.version}</version>
                <classifier>wildfly-${version.wildfly}-patch</classifier>
                <type>zip</type>
            </dependency>
            <!--
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>hibernate-validator-modules</artifactId>
                <version>${project.version}</version>
                <classifier>wildfly-${version.wildfly.secondary}-patch</classifier>
                <type>zip</type>
            </dependency>
            -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${version.jakarta.validation-api}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${version.org.jboss.logging.jboss-logging}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-processor</artifactId>
                <version>${version.org.jboss.logging.jboss-logging-tools}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging-annotations</artifactId>
                <version>${version.org.jboss.logging.jboss-logging-tools}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.el</artifactId>
                <version>${version.org.glassfish.jakarta.el}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${version.com.fasterxml.classmate}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${version.joda-time}</version>
            </dependency>
            <dependency>
                <groupId>javax.money</groupId>
                <artifactId>money-api</artifactId>
                <version>${version.javax.money}</version>
            </dependency>
            <dependency>
                <groupId>org.javamoney</groupId>
                <artifactId>moneta</artifactId>
                <version>${version.org.javamoney.moneta}</version>
            </dependency>
            <dependency>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-base</artifactId>
                <version>${version.org.openjfx}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${version.net.bytebuddy.byte-buddy}</version>
            </dependency>
            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>org.osgi.core</artifactId>
                <version>${version.org.osgi.core}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${version.org.apache.logging.log4j}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <type>test-jar</type>
                <version>${version.org.apache.logging.log4j}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${version.org.slf4j}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${version.org.apache.logging.log4j}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>${version.jakarta.persistence-api}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${version.junit}</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${version.org.testng}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-jsr223</artifactId>
                <version>${version.org.codehaus.groovy}</version>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>${version.org.easymock}</version>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${version.org.assertj.assertj-core}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${version.io.rest-assured}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.arquillian</groupId>
                <artifactId>arquillian-bom</artifactId>
                <version>${version.org.jboss.arquillian}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${version.jakarta.annotation-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${version.jakarta.ws.rs-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.interceptor</groupId>
                <artifactId>jakarta.interceptor-api</artifactId>
                <version>${version.jakarta.interceptor-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ejb</groupId>
                <artifactId>jakarta.ejb-api</artifactId>
                <version>${version.jakarta.ejb-api}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.enterprise</groupId>
                <artifactId>jakarta.enterprise.cdi-api</artifactId>
                <version>${version.jakarta.enterprise.cdi-api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>jakarta.interceptor</groupId>
                        <artifactId>jakarta.interceptor-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.el</groupId>
                        <artifactId>jakarta.el-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jboss.weld</groupId>
                <artifactId>weld-core-impl</artifactId>
                <version>${version.org.jboss.weld.weld}</version>
            </dependency>
            <dependency>
                <groupId>org.wildfly.arquillian</groupId>
                <artifactId>wildfly-arquillian-container-managed</artifactId>
                <version>${version.org.wildfly.arquillian}</version>
                <exclusions>
                    <exclusion>
                        <groupId>sun.jdk</groupId>
                        <artifactId>jconsole</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jboss.arquillian.container</groupId>
                <artifactId>arquillian-weld-se-embedded-1.1</artifactId>
                <version>${version.org.jboss.arquillian.container.arquillian-weld-se-embedded-1.1}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.paranamer</groupId>
                <artifactId>paranamer</artifactId>
                <version>${version.com.thoughtworks.paranamer}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${version.com.google.guava}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${version.org.springframework.spring-expression}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${version.com.fasterxml.jackson.core.jackson-databind}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${version.com.fasterxml.jackson.core.jackson-annotations}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${version.enforcer.plugin}</version>
                <executions>
                    <execution>
                        <id>enforce-java</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[${jdk.min.version},)</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>${maven.min.version}</version>
                                </requireMavenVersion>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>javax.validation:validation-api</exclude>
                                        <exclude>org.glassfish:javax.el</exclude>
                                        <exclude>javax.annotation:javax.annotation-api</exclude>
                                        <exclude>org.jboss.spec.javax.interceptor:jboss-interceptors-api_1.2_spec</exclude>
                                        <exclude>javax.enterprise:cdi-api</exclude>
                                        <exclude>javax.persistence:javax.persistence-api</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${version.antrun.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${version.clean.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${version.jar.plugin}</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Implementation-Title>${project.artifactId}</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>${project.parent.groupId}</Implementation-Vendor>
                                <Implementation-Vendor-Id>${project.parent.groupId}</Implementation-Vendor-Id>
                                <Implementation-URL>http://hibernate.org/validator/</Implementation-URL>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${version.compiler.plugin}</version>
                    <configuration>
                        <compilerArgs>
                            <compilerArg>-Aorg.jboss.logging.tools.addGeneratedAnnotation=false</compilerArg>
                        </compilerArgs>
                        <testCompilerArgument>-parameters</testCompilerArgument>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-compile</id>
                            <configuration>
                                <fork>true</fork>
                                <executable>${java-version.main.compiler}</executable>
                            </configuration>
                        </execution>
                        <execution>
                            <id>default-testCompile</id>
                            <configuration>
                                <fork>true</fork>
                                <executable>${java-version.test.compiler}</executable>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${version.checkstyle.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>hibernate-validator-build-config</artifactId>
                            <version>${project.version}</version>
                        </dependency>
                        <!--
                             force SLF4J dependency to align
                             Maven internals and CheckStyle's
                             see https://github.com/jcgay/maven-color/wiki/Problems
                             If that causes problem, that can be removed
                             but maven-color won't work -->
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${puppycrawl.checkstyle.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.slf4j</groupId>
                            <artifactId>jcl-over-slf4j</artifactId>
                            <version>${version.org.slf4j}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.slf4j</groupId>
                            <artifactId>slf4j-jdk14</artifactId>
                            <version>${version.org.slf4j}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <configLocation>checkstyle.xml</configLocation>
                        <consoleOutput>true</consoleOutput>
                        <failsOnError>true</failsOnError>
                        <violationSeverity>error</violationSeverity>
                        <includeResources>true</includeResources>
                        <includeTestResources>false</includeTestResources>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                        <resourceIncludes>**/*.xml,**/*.properties</resourceIncludes>
                        <!-- These classes are either imported from other sources and re-formatted
                        or generated or present significant reasons to not follow the rules. -->
                        <excludes>
                            **/org/hibernate/validator/internal/xml/binding/*.java,
                            **/Log_$logger.java,
                            **/Messages_$bundle.java,
                            **/ConcurrentReferenceHashMap.java,
                            **/TypeHelper*.java,
                            **/TckRunner.java
                        </excludes>
                    </configuration>
                    <executions>
                        <execution>
                            <id>check-style</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>de.thetaphi</groupId>
                    <artifactId>forbiddenapis</artifactId>
                    <version>${version.forbiddenapis.plugin}</version>
                    <configuration>
                        <targetVersion>${forbiddenapis.jdk.target}</targetVersion>
                        <!-- if the Java version used is too new, don't fail, just do nothing -->
                        <failOnUnsupportedJava>false</failOnUnsupportedJava>
                        <suppressAnnotations>
                            <annotation>**.IgnoreForbiddenApisErrors</annotation>
                        </suppressAnnotations>
                        <signaturesArtifacts>
                            <signaturesArtifact>
                                <groupId>org.hibernate.validator</groupId>
                                <artifactId>hibernate-validator-build-config</artifactId>
                                <version>${project.version}</version>
                                <type>jar</type>
                                <path>forbidden-common.txt</path>
                            </signaturesArtifact>
                            <signaturesArtifact>
                                <groupId>org.hibernate.validator</groupId>
                                <artifactId>hibernate-validator-build-config</artifactId>
                                <version>${project.version}</version>
                                <type>jar</type>
                                <path>${forbiddenapis-junit.path}</path>
                            </signaturesArtifact>
                        </signaturesArtifacts>
                    </configuration>
                    <executions>
                        <execution>
                            <id>check-main</id>
                            <goals>
                                <goal>check</goal>
                            </goals>
                            <phase>verify</phase>
                            <configuration>
                                <bundledSignatures>
                                    <bundledSignature>jdk-unsafe</bundledSignature>
                                    <bundledSignature>jdk-deprecated</bundledSignature>
                                    <bundledSignature>jdk-system-out</bundledSignature>
                                    <bundledSignature>jdk-non-portable</bundledSignature>
                                    <bundledSignature>jdk-internal</bundledSignature>
                                </bundledSignatures>
                            </configuration>
                        </execution>
                        <execution>
                            <id>check-test</id>
                            <goals>
                                <goal>testCheck</goal>
                            </goals>
                            <phase>verify</phase>
                            <configuration>
                                <bundledSignatures>
                                    <bundledSignature>jdk-deprecated</bundledSignature>
                                </bundledSignatures>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${version.license.plugin}</version>
                    <configuration>
                        <header>${hibernate-validator-parent.path}/build-config/src/main/resources/license.header</header>
                        <strictCheck>true</strictCheck>
                        <headerDefinitions>
                            <headerDefinition>${hibernate-validator-parent.path}/build-config/src/main/resources/java-header-style.xml</headerDefinition>
                            <headerDefinition>${hibernate-validator-parent.path}/build-config/src/main/resources/xml-header-style.xml</headerDefinition>
                        </headerDefinitions>
                        <mapping>
                            <java>JAVA_CLASS_STYLE</java>
                            <xml>XML_FILE_STYLE</xml>
                        </mapping>
                        <excludes>
                            <!-- Next two classes are copied from other project and have different headers -->
                            <exclude>**/org/hibernate/validator/internal/util/TypeHelper.java</exclude>
                            <exclude>**/org/hibernate/validator/test/internal/util/TypeHelperTest.java</exclude>
                            <exclude>**/settings-example.xml</exclude>
                            <exclude>**/src/test/resources/org/hibernate/validator/referenceguide/**/*.*</exclude>
                            <exclude>**/org/hibernate/validator/referenceguide/**/*.*</exclude>
                            <exclude>**/src/test/resources/org/hibernate/validator/test/internal/xml/**/*.xml</exclude>
                            <exclude>.mvn/**</exclude>
                        </excludes>
                        <includes>
                            <include>**/*.java</include>
                            <include>**/*.xml</include>
                        </includes>
                    </configuration>
                    <executions>
                        <execution>
                            <id>license-headers</id>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${version.surefire.plugin}</version>
                    <configuration>
                        <forkMode>once</forkMode>
                        <redirectTestOutputToFile>true</redirectTestOutputToFile>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                        <jvm>${java-version.test.launcher}</jvm>
                        <argLine>${surefire.jvm.args}</argLine>
                        <reportNameSuffix>${surefire.environment}</reportNameSuffix>
                        <environmentVariables>
                            <!-- Ensure Java programs launched in tests (Payara, WildFly)
                                 use the test Java home, not the Maven Java home -->
                            <JAVA_HOME>${java-version.test.launcher.java_home}</JAVA_HOME>
                        </environmentVariables>
                    </configuration>
                    <dependencies>
                        <!--
                             maven-surefire-plugin and maven-failsafe-plugin use an older version of ASM
                             that cannot handle Java 15+ bytecode.
                             Let's upgrade that dependency and hope for the best;
                             if it doesn't work, the build is very likely to fail and we'll know about it.
                         -->
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${version.surefire.plugin.java-version.asm}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>${version.surefire.plugin}</version>
                    <executions>
                        <execution>
                            <id>generate-test-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report-only</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <outputDirectory>${project.build.directory}/surefire-reports</outputDirectory>
                        <outputName>test-report</outputName>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${version.failsafe.plugin}</version>
                    <configuration>
                        <jvm>${java-version.test.launcher}</jvm>
                        <argLine>${failsafe.jvm.args}</argLine>
                        <reportNameSuffix>${surefire.environment}</reportNameSuffix>
                        <environmentVariables>
                            <!-- Ensure Java programs launched in tests (Payara, WildFly)
                                 use the test Java home, not the Maven Java home -->
                            <JAVA_HOME>${java-version.test.launcher.java_home}</JAVA_HOME>
                        </environmentVariables>
                    </configuration>
                    <dependencies>
                        <!--
                             maven-surefire-plugin and maven-failsafe-plugin use an older version of ASM
                             that cannot handle Java 15+ bytecode.
                             Let's upgrade that dependency and hope for the best;
                             if it doesn't work, the build is very likely to fail and we'll know about it.
                         -->
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${version.surefire.plugin.java-version.asm}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${version.dependency.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${version.install.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${version.assembly.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${version.release.plugin}</version>
                    <configuration>
                        <preparationGoals>clean install</preparationGoals>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <allowTimestampedSnapshots>true</allowTimestampedSnapshots>
                        <pushChanges>false</pushChanges>
                        <localCheckout>true</localCheckout>
                        <tagNameFormat>@{project.version}</tagNameFormat>
                        <releaseProfiles>documentation-pdf</releaseProfiles>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.asciidoctor</groupId>
                    <artifactId>asciidoctor-maven-plugin</artifactId>
                    <version>${version.asciidoctor.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.jruby</groupId>
                            <artifactId>jruby-complete</artifactId>
                            <version>${version.org.jruby}</version>
                        </dependency>
                         <dependency>
                            <groupId>org.asciidoctor</groupId>
                            <artifactId>asciidoctorj</artifactId>
                            <version>${version.org.asciidoctor.asciidoctorj}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.asciidoctor</groupId>
                            <artifactId>asciidoctorj-pdf</artifactId>
                            <version>${version.org.asciidoctor.asciidoctorj-pdf}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.hibernate.infra</groupId>
                            <artifactId>hibernate-asciidoctor-extensions</artifactId>
                            <version>${version.org.hibernate.infra.hibernate-asciidoctor-extensions}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>ch.mfrey.maven.plugin</groupId>
                    <artifactId>copy-maven-plugin</artifactId>
                    <version>${version.copy.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>${version.bundle.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${version.source.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${version.javadoc.plugin}</version>
                    <configuration>
                        <quiet>true</quiet>
                        <docfilessubdirs>true</docfilessubdirs>
                        <bottom>
                            <![CDATA[Copyright &copy; ${project.inceptionYear}-{currentYear} <a href="http://redhat.com">Red Hat, Inc.</a> All Rights Reserved]]></bottom>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${version.deploy.plugin}</version>
                    <configuration>
                        <!-- we use the nexus-staging plugin -->
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>${version.gpg.plugin}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${version.resources.plugin}</version>
                    <configuration>
                        <!-- By default the "@" character is a delimiter, causing files containing
                             that character to not be filtered properly -->
                        <useDefaultDelimiters>false</useDefaultDelimiters>
                        <delimiters>
                            <delimiter>${*}</delimiter>
                        </delimiters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.gmavenplus</groupId>
                    <artifactId>gmavenplus-plugin</artifactId>
                    <version>${version.gmavenplus.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.codehaus.groovy</groupId>
                            <artifactId>groovy-all</artifactId>
                            <version>${version.org.codehaus.groovy}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.servicemix.tooling</groupId>
                    <artifactId>depends-maven-plugin</artifactId>
                    <version>${version.depends.plugin}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${version.buildhelper.plugin}</version>
                </plugin>
                <plugin>
                    <!--
                        Creates a report by running "mvn japicmp:cmp"
                        Note: you need to install the jars before running the japicmp command.
                    -->
                    <groupId>com.github.siom79.japicmp</groupId>
                    <artifactId>japicmp-maven-plugin</artifactId>
                    <version>${version.japicmp.plugin}</version>
                    <configuration>
                        <oldVersion>
                            <dependency>
                                <groupId>org.hibernate.validator</groupId>
                                <artifactId>${project.artifactId}</artifactId>
                                <version>${previous.stable}</version>
                                <type>${project.packaging}</type>
                            </dependency>
                        </oldVersion>
                        <skip>true</skip>
                        <newVersion>
                            <file>
                                <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
                            </file>
                        </newVersion>
                        <parameter>
                            <onlyModified>true</onlyModified>
                            <excludes>
                                <exclude>org.hibernate.validator.internal.*</exclude>
                            </excludes>
                        </parameter>
                    </configuration>
                </plugin>
                <!-- Used to build the JMH jars -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${version.shade.plugin}</version>
                </plugin>
                <!-- WildFly patching infrastructure plugins -->
                <plugin>
                    <groupId>org.jboss.as</groupId>
                    <artifactId>patch-gen-maven-plugin</artifactId>
                    <version>${version.wildfly-patch-gen.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.fasterxml.woodstox</groupId>
                            <artifactId>woodstox-core</artifactId>
                            <version>${version.wildfly-patch-gen.plugin.woodstox}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.wildfly.plugins</groupId>
                    <artifactId>wildfly-maven-plugin</artifactId>
                    <version>${version.wildfly.plugin}</version>
                    <dependencies>
                        <!-- Contains the patch command -->
                        <dependency>
                            <groupId>org.wildfly.core</groupId>
                            <artifactId>wildfly-patching</artifactId>
                            <version>${version.org.wildfly.core}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.wildfly.core</groupId>
                            <artifactId>wildfly-cli</artifactId>
                            <version>${version.org.wildfly.core}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.netbeans.tools</groupId>
                    <artifactId>sigtest-maven-plugin</artifactId>
                    <version>${version.sigtest.plugin}</version>
                </plugin>
                <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.asciidoctor
                                        </groupId>
                                        <artifactId>
                                            asciidoctor-maven-plugin
                                        </artifactId>
                                        <versionRange>
                                            [0.1.4,)
                                        </versionRange>
                                        <goals>
                                            <goal>
                                                process-asciidoc
                                            </goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.jboss.maven.plugins
                                        </groupId>
                                        <artifactId>
                                            maven-injection-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.0.2,)
                                        </versionRange>
                                        <goals>
                                            <goal>bytecode</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.codehaus.gmavenplus
                                        </groupId>
                                        <artifactId>
                                            gmavenplus-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.5,)
                                        </versionRange>
                                        <goals>
                                            <goal>execute</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.servicemix.tooling</groupId>
                                        <artifactId>depends-maven-plugin</artifactId>
                                        <versionRange>[1.2,)</versionRange>
                                        <goals>
                                            <goal>generate-depends-file</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-dependency-plugin</artifactId>
                                        <versionRange>[2.0,)</versionRange>
                                        <goals>
                                            <goal>copy-dependencies</goal>
                                            <goal>copy</goal>
                                            <goal>unpack</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <ciManagement>
        <system>Jenkins</system>
        <url>http://ci.hibernate.org/view/Validator/</url>
    </ciManagement>

    <issueManagement>
        <system>JIRA</system>
        <url>https://hibernate.atlassian.net/projects/HV/summary</url>
    </issueManagement>

    <inceptionYear>2007</inceptionYear>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/hibernate/hibernate-validator.git</connection>
        <developerConnection>scm:git:**************:hibernate/hibernate-validator.git</developerConnection>
        <url>http://github.com/hibernate/hibernate-validator</url>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <repository>
            <id>${ossrh.releases.repo.id}</id>
            <name>OSSRH Releases Repository</name>
            <url>${ossrh.releases.repo.url}</url>
        </repository>
        <snapshotRepository>
            <id>${ossrh.snapshots.repo.id}</id>
            <name>OSSRH Snapshots Repository</name>
            <url>${ossrh.snapshots.repo.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>docs</id>
            <activation>
                <property>
                    <name>disableDocumentationBuild</name>
                    <value>!true</value>
                </property>
            </activation>
            <modules>
                <module>documentation</module>
            </modules>
        </profile>
        <profile>
            <id>dist</id>
            <activation>
                <property>
                    <name>disableDistributionBuild</name>
                    <value>!true</value>
                </property>
            </activation>
            <modules>
                <module>distribution</module>
            </modules>
        </profile>
        <profile>
            <id>relocation</id>
            <modules>
                <module>relocation</module>
            </modules>
        </profile>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>enforce-release-rules</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireReleaseDeps />
                                        <requireJavaVersion>
                                            <version>[17,18)</version>
                                        </requireJavaVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>${version.nexus-staging-maven-plugin}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <nexusUrl>${ossrh.releases.repo.baseUrl}</nexusUrl>
                            <serverId>${ossrh.releases.repo.id}</serverId>
                            <stagingProgressTimeoutMinutes>60</stagingProgressTimeoutMinutes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <homedir>${env.RELEASE_GPG_HOMEDIR}</homedir>
                                    <passphrase>${env.RELEASE_GPG_PASSPHRASE}</passphrase>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- Running the build with JDK 8 is not recommended and will require disabling the enforcer plugin,
                 but it's the only way to run the signature test. -->
            <id>runningMavenWithJdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <!-- With JDK 8, java.home is the JRE -->
                <java-version.main.compiler.java_home>${java.home}/../</java-version.main.compiler.java_home>
                <java-version.test.compiler.java_home>${java.home}/../</java-version.test.compiler.java_home>
                <!-- Cannot use -release on JDK 8; we'll default to -source/-target -->
                <!-- That's why we don't define maven.compiler.release nor maven.compiler.testRelease here -->
            </properties>
        </profile>
        <profile>
            <id>runningMavenWithJdk9+</id>
            <activation>
                <jdk>[9,)</jdk>
            </activation>
            <properties>
                <maven.compiler.release>${java-version.main.release}</maven.compiler.release>
                <maven.compiler.testRelease>${java-version.test.release}</maven.compiler.testRelease>
            </properties>
        </profile>
        <profile>
            <id>testWithJdk8</id>
            <activation>
                <property>
                    <name>java-version.test.release</name>
                    <value>8</value>
                </property>
            </activation>
            <properties>
                <!-- Tests specific to Java 17+ won't work with JDK 8, so we disable them. -->
                <java-version.test.java17.add-test-source-phase>none</java-version.test.java17.add-test-source-phase>
            </properties>
            <modules>
                <!-- Only run the OSGi tests for JDK 8 -->
                <module>osgi</module>
            </modules>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <configuration>
                                <classpathDependencyExcludes>
                                    <!-- JavaFX is already provided by OracleJDK8 -->
                                    <classpathDependencyExclude>org.openjfx:javafx-base</classpathDependencyExclude>
                                </classpathDependencyExcludes>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <configuration>
                                <classpathDependencyExcludes>
                                    <!-- JavaFX is already provided by OracleJDK8 -->
                                    <classpathDependencyExclude>org.openjfx:javafx-base</classpathDependencyExclude>
                                </classpathDependencyExcludes>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>testWithJdk11</id>
            <activation>
                <property>
                    <name>java-version.test.release</name>
                    <value>11</value>
                </property>
            </activation>
            <properties>
                <!-- Tests specific to Java 17+ won't work with JDK 11, so we disable them. -->
                <java-version.test.java17.add-test-source-phase>none</java-version.test.java17.add-test-source-phase>
            </properties>
        </profile>
        <profile>
            <id>testWithJdk11+</id>
            <activation>
                <property>
                    <name>java-version.test.release</name>
                    <value>!8</value>
                </property>
            </activation>
            <properties>
                <arquillian.wildfly.jvm.args.add-modules>
                    --add-modules=java.se
                </arquillian.wildfly.jvm.args.add-modules>
                <!--
                All these add-opens are probably not necessary for our WildFly usage but add the ones defined in
                https://github.com/ctomc/wildfly/commit/d8c4f55a1f900d931c224049c8a118ba4a5bcb45 to be on the safe side.
                -->
                <arquillian.wildfly.jvm.args.add-opens>
                    --add-opens=java.base/java.lang=ALL-UNNAMED
                    --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                    --add-opens=java.base/java.security=ALL-UNNAMED
                    --add-opens=java.base/java.math=ALL-UNNAMED
                    --add-opens=java.base/java.io=ALL-UNNAMED
                    --add-opens=java.base/java.net=ALL-UNNAMED
                    --add-opens=java.base/java.util=ALL-UNNAMED
                    --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
                    --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED
                    --add-opens=java.base/java.util.concurrent.locks=ALL-UNNAMED
                    --add-opens=java.base/jdk.internal.reflect=ALL-UNNAMED
                    --add-opens=java.management/javax.management=ALL-UNNAMED
                    --add-opens=java.management/javax.management.openmbean=ALL-UNNAMED
                    --add-opens=java.naming/javax.naming=ALL-UNNAMED
                </arquillian.wildfly.jvm.args.add-opens>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.wildfly.plugins</groupId>
                            <artifactId>wildfly-maven-plugin</artifactId>
                            <configuration>
                                <javaOpts>
                                    --add-opens=java.base/java.lang=ALL-UNNAMED
                                    --add-opens=java.base/java.security=ALL-UNNAMED
                                    --add-opens=java.base/java.io=ALL-UNNAMED
                                    --add-modules=java.se
                                </javaOpts>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.jboss.as</groupId>
                            <artifactId>patch-gen-maven-plugin</artifactId>
                            <configuration>
                                <argLine>
                                    --add-modules=java.se
                                </argLine>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>jqassistant</id>
            <!--
            To run the analysis on the engine module and launch a Neo4j server accessible at http://localhost:7474/, run:
                mvn -pl engine jqassistant:scan jqassistant:server -Pjqassistant
            To run the analysis on the engine module:
                mvn clean install -pl engine -Pjqassistant -DskipTests=true
            Our rules are in jqassistant/rules.xml.
            -->
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.buschmais.jqassistant</groupId>
                        <artifactId>jqassistant-maven-plugin</artifactId>
                        <version>${version.jqassistant.plugin}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>scan</goal>
                                    <goal>analyze</goal>
                                </goals>
                                <configuration>
                                    <failOnViolations>true</failOnViolations>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>IDEA</id>
            <activation>
                <!-- This is a trick to have the profile automatically activated by IDEA -->
                <property>
                    <name>idea.maven.embedder.version</name>
                </property>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <artifactId>maven-compiler-plugin</artifactId>
                            <version>${version.compiler.plugin}</version>
                            <configuration>
                                <compilerArgs>
                                    <compilerArg>-Aorg.jboss.logging.tools.addGeneratedAnnotation=false</compilerArg>
                                    <!-- Make sure IDEA uses the -parameters for all code;
                                         IDEA ignores the "testCompilerArguments" option... -->
                                    <compilerArg>-parameters</compilerArg>
                                </compilerArgs>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
</project>
