<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright 2016-2020 The OpenZipkin Authors

    Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
    in compliance with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software distributed under the License
    is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
    or implied. See the License for the specific language governing permissions and limitations under
    the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.zipkin.reporter2</groupId>
  <artifactId>zipkin-reporter-bom</artifactId>
  <name>Zipkin Reporter BOM</name>
  <version>2.16.3</version>
  <packaging>pom</packaging>
  <description>Bill Of Materials POM for all Zipkin reporter artifacts</description>

  <properties>
    <main.basedir>${project.basedir}/..</main.basedir>
    <!-- use the same value in ../pom.xml -->
    <zipkin.version>2.23.2</zipkin.version>
    <zipkin-proto3.version>1.0.0</zipkin-proto3.version>

    <nexus-staging-maven-plugin.version>1.6.8</nexus-staging-maven-plugin.version>
  </properties>

  <url>https://github.com/openzipkin/zipkin-reporter-java</url>
  <inceptionYear>2016</inceptionYear>

  <organization>
    <name>OpenZipkin</name>
    <url>https://zipkin.io/</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/openzipkin/zipkin-reporter-java</url>
    <connection>scm:git:https://github.com/openzipkin/zipkin-reporter-java.git</connection>
    <developerConnection>scm:git:https://github.com/openzipkin/zipkin-reporter-java.git</developerConnection>
    <tag>2.16.3</tag>
  </scm>

  <!-- Developer section is needed for Maven Central, but doesn't need to include each person -->
  <developers>
    <developer>
      <id>openzipkin</id>
      <name>OpenZipkin Gitter</name>
      <url>https://gitter.im/openzipkin/zipkin</url>
    </developer>
  </developers>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <issueManagement>
    <system>Github</system>
    <url>https://github.com/openzipkin/zipkin-reporter-java/issues</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin</artifactId>
        <version>${zipkin.version}</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.proto3</groupId>
        <artifactId>zipkin-proto3</artifactId>
        <version>${zipkin-proto3.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-reporter</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-okhttp3</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-libthrift</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-urlconnection</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-kafka08</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-kafka</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-amqp-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-sender-activemq-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-reporter-spring-beans</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-reporter-brave</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>zipkin-reporter-metrics-micrometer</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>${nexus-staging-maven-plugin.version}</version>
            <extensions>true</extensions>
            <configuration>
              <serverId>ossrh</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
              <autoReleaseAfterClose>true</autoReleaseAfterClose>
            </configuration>
          </plugin>

          <plugin>
            <artifactId>maven-deploy-plugin</artifactId>
            <version>3.0.0-M1</version>
          </plugin>

          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
