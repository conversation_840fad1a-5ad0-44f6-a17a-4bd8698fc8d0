package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * CollaborativeCompilationTask Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:04:08
 */
@TableName(value = "pmsx_collaborative_compilation_task")
@ApiModel(value = "CollaborativeCompilationTaskEntity对象", description = "协同编制任务表")
@Data

public class CollaborativeCompilationTask extends  ObjectEntity  implements Serializable{

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    @TableField(value = "approval_id")
    private String approvalId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "rsp_dept")
    private String rspDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    private String rspUser;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 计划情况
     */
    @ApiModelProperty(value = "计划情况")
    @TableField(value = "circumstance")
    private Integer circumstance;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    /**
     * 父级链
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "parent_chain")
    private String parentChain;

    /**
     * 任务描述
     */
    @ApiModelProperty(value = "任务描述")
    @TableField(value = "task_desc")
    private String taskDesc;

    /**
     * 置顶序号（0：取消置顶）
     */
    @ApiModelProperty(value = "置顶序号（0：取消置顶）")
    @TableField(value = "top_sort")
    private Integer topSort;

    /**
     * 执行情况说明
     */
    @ApiModelProperty(value = "执行情况说明")
    @TableField(value = "execute_desc")
    private String executeDesc;

    /**
     * 下发计划时间
     */
    @ApiModelProperty(value = "下发计划时间")
    @TableField(value = "issue_time")
    private Date issueTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 下达人
     */
    @ApiModelProperty(value = "下达人")
    @TableField(value = "issued_user")
    private String issuedUser;

    /**
     * 确认理由
     */
    @ApiModelProperty(value = "确认理由")
    @TableField(value = "reason_confirmation")
    private String reasonConfirmation;

    /**
     * 上一个状态
     */
    @ApiModelProperty(value = "上一个状态")
    @TableField(value = "last_status")
    private Integer lastStatus;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    @TableField(value = "content")
    private String content;

    /**
     * 处理实例
     */
    @ApiModelProperty(value = "处理实例")
    @TableField(value = "process_instances")
    private String processInstances;

    /**
     * 处理对象
     */
    @ApiModelProperty(value = "处理对象")
    @FieldBind(dataBind = DictDataBind.class, type = "process_object_type", target = "processObjectName")
    @TableField(value = "process_object")
    private String processObject;

    @ApiModelProperty(value = "处理对象名称")
    @TableField(exist = false)
    private String processObjectName;


    @ApiModelProperty(value = "处理对象")
    @TableField(exist = false)
    private String modelParentId;

    @ApiModelProperty(value = "转办理由")
    @TableField(value = "reason_transfer")
    private String  reasonTransfer;

}
