package com.chinasie.orion.util;
import cn.hutool.core.util.IdUtil;
import com.chinasie.orion.constant.Permission;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class TreeInfoProcessor<T extends NodeVO> {
    private String currentUser;
    private Map<String, Set<String>> currentPermissions;
    private boolean filterNoReadNodes;
    private boolean isCount = Boolean.FALSE;
    private TreeNodeVO<T> root;

    private Map<String, List<T>> homeworkMap;
    private Set<String> parentIdSet ;

    private Integer level;

    private boolean delimiter = Boolean.FALSE;

    private Set<String> expandSet  = new HashSet<>();

    private Set<String> oneOrTwoIdSet = new HashSet<>();


    public TreeInfoProcessor(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor, Function<T, String> responsiblePersonExtractor,
                             Function<T, Boolean> idBoolExtractor,String currentUser, Map<String, Set<String>> currentPermissions, boolean filterNoReadNodes, Map<String, List<T>> homeworkMap) {
        this.currentUser = currentUser;
        this.currentPermissions = currentPermissions;
        this.filterNoReadNodes = filterNoReadNodes;
        this.homeworkMap = homeworkMap;
        this.parentIdSet = new HashSet<>();
        this.oneOrTwoIdSet = new HashSet<>();
        this.root = buildTree(data, idExtractor, parentIdExtractor,idBoolExtractor, responsiblePersonExtractor);
    }

    // Function<T, String> parentIdExtractor,
    public TreeInfoProcessor(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor, Function<T, String> responsiblePersonExtractor,String currentUser,Function<T, Integer> sortExtractor
            , Map<String, Set<String>> currentPermissions, boolean filterNoReadNodes,boolean isCount, Map<String, List<T>> homeworkMap) {
        this.currentUser = currentUser;
        this.currentPermissions = currentPermissions;
        this.filterNoReadNodes = filterNoReadNodes;
        this.homeworkMap = homeworkMap;
        this.parentIdSet = new HashSet<>();
        this.isCount = isCount;
        this.oneOrTwoIdSet = new HashSet<>();
        this.root =  buildTreeAndSort(data, idExtractor, parentIdExtractor, responsiblePersonExtractor,sortExtractor);
    }


    private TreeNodeVO<T> buildTreeAndSort(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor,Function<T, String> responsiblePersonExtractor,Function<T, Integer> sortExtractor) {
        Map<String, TreeNodeVO<T>> nodeMap = new LinkedHashMap<>();
        Set<String> existingIds = new HashSet<>();
        for (T item : data) {
            String id = idExtractor.apply(item);
            Integer sort = sortExtractor.apply(item);
            TreeNodeVO<T> node = new TreeNodeVO<>(item,sort);
            node.setRoleList(new HashSet<>());
            nodeMap.put(id, node);
            existingIds.add(id);
        }
        for (T item : data) {
            String id = idExtractor.apply(item);
            List<T> dataList = homeworkMap.getOrDefault(id, new ArrayList<>());
            String parentId = parentIdExtractor.apply(item);
            TreeNodeVO<T> node = nodeMap.get(id);
            if (parentId == null || parentId.isEmpty() || !existingIds.contains(parentId)) {
                this.root = node;
                node.setBusinessDataList(dataList);
            } else {
                TreeNodeVO<T> parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    expandSet.add(parentNode.getData().getId());
                    expandSet.add(node.getData().getId());
                    node.setBusinessDataList(dataList);
                    parentNode.addChild(node);
                }
            }
        }
        this.setTopParentId(root);
        root.setLevel(1);
        List<String> existId = new ArrayList<>();
        setDefaultPermissions(root, null, new ArrayList<>(),Boolean.TRUE,existId);
        setParentPermissions(root);

        // 添加到 一二层的父级Id
        oneOrTwoIdSet.add(root.getData().getId());
        List<TreeNodeVO<T>> children = root.getChildren();
        if(null != children && !children.isEmpty()){
            for (TreeNodeVO<T> child : children) {
                oneOrTwoIdSet.add(child.getData().getId());
            }
        }
        // 是否过滤权限
        if (filterNoReadNodes) {
            filterNoReadNodes(root);
        }
        // 是否统计
        if(isCount) {
            StatisticCalculator.calculateStatistics(Collections.singletonList(root));
        }
        return root;
    }



    public TreeInfoProcessor(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor, Function<T, String> responsiblePersonExtractor,
                             String currentUser, Map<String, Set<String>> currentPermissions, boolean filterNoReadNodes, Map<String, List<T>> homeworkMap) {
        this.currentUser = currentUser;
        this.currentPermissions = currentPermissions;
        this.filterNoReadNodes = filterNoReadNodes;
        this.homeworkMap = homeworkMap;
        this.parentIdSet = new HashSet<>();
        this.oneOrTwoIdSet = new HashSet<>();
        this.root = buildTree(data, idExtractor, parentIdExtractor, responsiblePersonExtractor);
    }


    private TreeNodeVO<T> buildTree(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor,Function<T, String> responsiblePersonExtractor) {
        Map<String, TreeNodeVO<T>> nodeMap = new LinkedHashMap<>();
        Set<String> existingIds = new HashSet<>();
        for (T item : data) {
            String id = idExtractor.apply(item);
            TreeNodeVO<T> node = new TreeNodeVO<>(item);
            node.setRoleList(new HashSet<>());
            nodeMap.put(id, node);
            existingIds.add(id);
        }
        for (T item : data) {
            String id = idExtractor.apply(item);
            List<T> dataList = homeworkMap.getOrDefault(id, new ArrayList<>());
            String parentId = parentIdExtractor.apply(item);
            TreeNodeVO<T> node = nodeMap.get(id);
            if (parentId == null || parentId.isEmpty() || !existingIds.contains(parentId)) {
                this.root = node;
                node.setBusinessDataList(dataList);
            } else {
                TreeNodeVO<T> parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    expandSet.add(parentNode.getData().getId());
                    expandSet.add(node.getData().getId());
                    node.setBusinessDataList(dataList);
                    parentNode.addChild(node);
                }
            }
        }
        this.setTopParentId(root);
        root.setLevel(1);
        List<String> existId = new ArrayList<>();
        setDefaultPermissions(root, null, new ArrayList<>(),Boolean.FALSE,existId);
        setParentPermissions(root);
        if (filterNoReadNodes) {
            filterNoReadNodes(root);
        }
        StatisticCalculator.calculateStatistics(Collections.singletonList(root));
        return root;
    }


    private TreeNodeVO<T> buildTree(List<T> data, Function<T, String> idExtractor, Function<T, String> parentIdExtractor, Function<T, Boolean> idBoolExtractor,Function<T, String> responsiblePersonExtractor) {
        Map<String, TreeNodeVO<T>> nodeMap = new LinkedHashMap<>();
        Set<String> existingIds = new HashSet<>();
        for (T item : data) {
            String id = idExtractor.apply(item);
            String rspUserId = responsiblePersonExtractor.apply(item);
            TreeNodeVO<T> node = new TreeNodeVO<>(item);
            node.setRoleList(new HashSet<>());
            nodeMap.put(id, node);
            existingIds.add(id);
        }
        for (T item : data) {
            String id = idExtractor.apply(item);
            List<T> dataList = homeworkMap.getOrDefault(id, new ArrayList<>());
            String parentId = parentIdExtractor.apply(item);
            TreeNodeVO<T> node = nodeMap.get(id);
            Boolean idBool = idBoolExtractor.apply(item);
            if (parentId == null || parentId.isEmpty() || !existingIds.contains(parentId)) {
                this.root = node;
            } else {
                TreeNodeVO<T> parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    expandSet.add(parentNode.getData().getId());
                    expandSet.add(node.getData().getId());
                    node.setBusinessDataList(dataList);
                    parentNode.addChild(node);
                    if(Objects.equals(idBool,Boolean.TRUE)){
                        TreeNodeVO<T> node1 = new TreeNodeVO<>(Boolean.TRUE);
                        NodeVO nodeVO= new NodeVO<>();
                        nodeVO.setId(IdUtil.nanoId(10));
                        node1.setData((T) nodeVO);
                        // 默认构建空的对象作为分割
                        parentNode.addChild(node1);
                    }
                }
            }
        }
        this.level =1;
        root.setLevel(1);

        this.setTopParentId(root);
        List<String> existId = new ArrayList<>();
        setDefaultPermissions(root, null, new ArrayList<>(),Boolean.FALSE,existId);
        setParentPermissions(root);
        if (filterNoReadNodes) {
            filterNoReadNodes(root);
        }
        StatisticCalculator.calculateStatistics(Collections.singletonList(root));
        return root;
    }

    private void setTopParentId(TreeNodeVO<T> root) {
        if(!CollectionUtils.isBlank(root.getChildren())){
            parentIdSet.add(root.getData().getId());
        };
    }

    private void setDefaultPermissions(TreeNodeVO<T> node, Set<String> parentPermissions, List<String> dataIdList,boolean isSort,List<String> existId) {
        T data = node.getData();
        if(Objects.equals(node.getDelimiter(),Boolean.TRUE&&Objects.isNull(data))){
            return;
        }
        String nodeId = data.getId();
        if(existId.contains(nodeId)){
            log.error("----------------------循环依赖了---:"+nodeId);
        }
        existId.add(nodeId);
        Set<String> nodePermissions = currentPermissions.getOrDefault(nodeId, new HashSet<>());
        if (parentPermissions != null && !parentPermissions.isEmpty()) {
            nodePermissions.addAll(parentPermissions);
        }
        if (Objects.equals(data.getRspUserId(), currentUser)) {
            nodePermissions.add(Permission.WRITE.name());
            nodePermissions.add(Permission.READ.name());
        } else if (nodePermissions.isEmpty()) {
            nodePermissions.clear();
        }
        node.setRoleList(nodePermissions);
        Set<String> parentRoleSet = node.getRoleList();
        List<T> businessDataList = node.getBusinessDataList();
        if(!CollectionUtils.isBlank(businessDataList)){
            parentIdSet.add(node.getData().getId());
            updateBusinessDataPermissions(businessDataList, parentRoleSet);
            node.setBusinessDataList(businessDataList);
            boolean hasNonEmptyRoles = businessDataList.stream().anyMatch(item -> !item.getRoleList().isEmpty());
            if (hasNonEmptyRoles) {
                Set<String> updatedPermissions = new HashSet<>(nodePermissions);
                updatedPermissions.add(Permission.READ.name());
                node.setRoleList(updatedPermissions);
                dataIdList.add(node.getData().getId());
            }
        }
        for (TreeNodeVO<T> child : node.getChildren()) {
            Integer level= node.getLevel();
            child.setLevel(level+1);
            setDefaultPermissions(child, nodePermissions, dataIdList,isSort,existId);
        }
        if(isSort){
            List<TreeNodeVO<T> > children = node.getChildren();
            if (!CollectionUtils.isBlank(children)) {
                children.sort(Comparator.comparing(TreeNodeVO::getSort));
                node.setChildren(children);
            }
        }
        if (Objects.nonNull(node.getData()) && Objects.nonNull(node.getData().getParentId())) {
            parentIdSet.add(node.getData().getParentId());
        }
    }

    private void updateBusinessDataPermissions(List<T> businessDataList, Set<String> parentRoleSet) {
        businessDataList.forEach(item -> {
            Set<String> businessDataSet = currentPermissions.getOrDefault(item.getId(), new HashSet<>());
            if (!parentRoleSet.isEmpty()) {
                businessDataSet.addAll(parentRoleSet);
            }
            if (Objects.equals(item.getRspUserId(), currentUser)) {
                businessDataSet.add(Permission.WRITE.name());
            }
            item.setRoleList(businessDataSet);
        });
    }

    private void setParentPermissions(TreeNodeVO<T> node) {

        if(Objects.equals(node.getDelimiter(),Boolean.TRUE&&Objects.isNull(node.getData()))){
            return;
        }
        if (parentIdSet.contains(node.getData().getId())) {
            node.setAddRole(Permission.READ.name());
        }
        for (TreeNodeVO<T> child : node.getChildren()) {
            setParentPermissions(child);
        }
    }

    private void filterNoReadNodes(TreeNodeVO<T> node) {
        List<TreeNodeVO<T>> childrenToRemove = new ArrayList<>();
        for (TreeNodeVO<T> child : node.getChildren()) {
            if (!hasReadPermission(child)) {
                childrenToRemove.add(child);
            } else {
                filterNoReadNodes(child);
            }
        }
        node.getChildren().removeAll(childrenToRemove);
    }

    private boolean hasReadPermission(TreeNodeVO<T> node) {
        return node.getRoleList().contains(Permission.READ.name()) || node.getRoleList().contains(Permission.WRITE.name());
    }

    private TreeNodeVO<T> findNodeById(TreeNodeVO<T> node, String id) {
        if (node == null) {
            return null;
        }
        if (node.getData().getId().equals(id)) {
            return node;
        }
        for (TreeNodeVO<T> child : node.getChildren()) {
            TreeNodeVO<T> foundNode = findNodeById(child, id);
            if (foundNode != null) {
                return foundNode;
            }
        }
        return null;
    }

    public List<TreeNodeVO<T>> getRootList() {
        return Collections.singletonList(root);
    }

    public TreeNodeVO<T> getRoot() {
        return root;
    }

    public List<String> getParenIdList(){
        return new ArrayList<>(parentIdSet);
    }

    public List<String> getOneOrTwoIdList(){
        return new ArrayList<>(oneOrTwoIdSet);
    }
}