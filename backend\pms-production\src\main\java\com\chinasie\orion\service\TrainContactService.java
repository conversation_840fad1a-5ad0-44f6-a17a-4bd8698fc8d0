package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.BusinessParamDTO;
import com.chinasie.orion.domain.dto.TrainContactDTO;
import com.chinasie.orion.domain.entity.TrainContact;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.domain.vo.TrainContactVO;
import com.chinasie.orion.domain.vo.train.RolePermission;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:08
 * @description:
 */

public interface TrainContactService  extends OrionBaseService<TrainContact> {


    /**
     *  详情
     *
     * * @param id
     */
    TrainContactVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param trainContractDTO
     */
    String create(TrainContactDTO trainContractDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param trainContractDTO
     */
    Boolean edit(TrainContactDTO trainContractDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<TrainContactVO> pages(Page<TrainContactDTO> pageRequest)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<TrainContactVO> vos)throws Exception;


    Map<String, TrainContact> codeToEntity(List<String> traincenterCodes);

    /**
     *  获取当前人是否拥有权限
     * @return
     */
    RolePermission currentPermission();

    /**
     *  获取当前用户 在当前基地下的权限
     * @param currentUserId
     * @return
     */
    Map<String, String> listByCurrentUser(String currentUserId);

    /**
     *  获取当用户 能够访问的基地列表
     * @return
     */
    List<BasePlaceVO> currentBasePlaceList();

    List<String> getCodeMap(String userId, String code);

    List<String> getUserIdListByRoleCodeList(BusinessParamDTO businessParamDTO);

    /**
     *  获取联络人角色列表
     * @return 结果
     */
    Map<String,String> getRoleList();
}
