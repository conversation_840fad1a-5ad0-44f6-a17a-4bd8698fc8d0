FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/java:21-anolis

LABEL maintainer="<EMAIL>"
ENV LANG=C.UTF-8
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# Set environment
RUN mkdir -p /mpf-dkm
WORKDIR /mpf-dkm

EXPOSE 7011

# 激活生产环境 Profile
ENV SPRING_PROFILES_ACTIVE=prod

# 将应用程序的 jar 包复制到镜像中
COPY mpf-dkm-3.7.0.jar app.jar
RUN ls -l /mpf-dkm/

CMD ["sh", "-c", "java -Dfile.encoding=utf-8 -jar app.jar"]