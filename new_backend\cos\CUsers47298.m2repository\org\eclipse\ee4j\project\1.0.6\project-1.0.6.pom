<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.eclipse.ee4j</groupId>
    <artifactId>project</artifactId>
    <version>1.0.6</version>
    <packaging>pom</packaging>

    <name>EE4J Project</name>
    <url>https://projects.eclipse.org/projects/ee4j</url>
    <description>
        Eclipse Enterprise for Java (EE4J) is an open source initiative to create standard APIs,
        implementations of those APIs, and technology compatibility kits for Java runtimes
        that enable development, deployment, and management of server-side and cloud-native applications.
    </description>

    <organization>
        <name>Eclipse Foundation</name>
        <url>https://www.eclipse.org</url>
    </organization>
    <inceptionYear>2017</inceptionYear>

    <developers>
        <developer>
            <id>eclipseee4j</id>
            <name>Eclipse EE4J Developers</name>
            <organization>Eclipse Foundation</organization>
            <email><EMAIL></email>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Eclipse Public License v. 2.0</name>
            <url>https://www.eclipse.org/org/documents/epl-2.0/EPL-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GNU General Public License, version 2 with the GNU Classpath Exception</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/eclipse-ee4j/ee4j/issues</url>
    </issueManagement>

    <scm>
        <connection>scm:git:**************:eclipse-ee4j/ee4j.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/ee4j.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/ee4j</url>
    </scm>

    <mailingLists>
        <mailingList>
            <name>Community discussions</name>
            <post><EMAIL></post>
            <subscribe>https://accounts.eclipse.org/mailing-list/jakarta.ee-community</subscribe>
            <unsubscribe>https://accounts.eclipse.org/mailing-list/jakarta.ee-community</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jakarta.ee-community/</archive>
            <otherArchives>
                <otherArchive>http://dev.eclipse.org/mhonarc/lists/jakarta.ee-community/maillist.rss</otherArchive>
            </otherArchives>
        </mailingList>
        <mailingList>
            <name>PMC discussions</name>
            <post><EMAIL></post>
            <subscribe>https://accounts.eclipse.org/mailing-list/ee4j-pmc</subscribe>
            <unsubscribe>https://accounts.eclipse.org/mailing-list/ee4j-pmc</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/ee4j-pmc/</archive>
            <otherArchives>
                <otherArchive>http://dev.eclipse.org/mhonarc/lists/ee4j-pmc/maillist.rss</otherArchive>
            </otherArchives>
        </mailingList>
    </mailingLists>

    <properties>
        <sonatypeOssDistMgmtNexusUrl>https://jakarta.oss.sonatype.org/</sonatypeOssDistMgmtNexusUrl>
        <sonatypeOssDistMgmtSnapshotsUrl>${sonatypeOssDistMgmtNexusUrl}content/repositories/snapshots/</sonatypeOssDistMgmtSnapshotsUrl>
        <sonatypeOssDistMgmtStagingUrl>${sonatypeOssDistMgmtNexusUrl}content/repositories/staging/</sonatypeOssDistMgmtStagingUrl>
        <sonatypeOssDistMgmtReleasesUrl>${sonatypeOssDistMgmtNexusUrl}service/local/staging/deploy/maven2/</sonatypeOssDistMgmtReleasesUrl>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--
          Initialize release.arguments to empty, otherwise if not defined
          it can fail the release plugin.
        -->
        <release.arguments></release.arguments>
    </properties>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>${sonatypeOssDistMgmtSnapshotsUrl}</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <name>Sonatype Nexus Releases</name>
            <url>${sonatypeOssDistMgmtReleasesUrl}</url>
        </repository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                    <configuration>
                        <mavenExecutorId>forked-path</mavenExecutorId>
                        <useReleaseProfile>false</useReleaseProfile>
                        <arguments>-Poss-release ${release.arguments}</arguments>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>1.6.8</version>
                    <configuration>
                        <serverId>ossrh</serverId>
                        <nexusUrl>${sonatypeOssDistMgmtNexusUrl}</nexusUrl>
                        <autoReleaseAfterClose>false</autoReleaseAfterClose>
                        <!-- Skip based on the maven.deploy.skip property -->
                        <skipNexusStagingDeployMojo>${maven.deploy.skip}</skipNexusStagingDeployMojo>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <!--
            This profile provides configuration for the plugins that are required are in
            order to deploy non SNAPSHOT artifacts.
            SNAPSHOT artifacts can be deployed without using any profile.
        -->
        <profile>
            <id>oss-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>enforce-maven</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireMavenVersion>
                                            <version>[3.0.4,)</version>
                                            <message>Maven 3.0 through 3.0.3 inclusive does not pass
                                                correct settings.xml to Maven Release Plugin.</message>
                                        </requireMavenVersion>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <!-- Older versions have issues with the gpg passphrase -->
                        <version>1.6</version>
                        <configuration>
                            <gpgArguments>
                                <arg>--pinentry-mode</arg>
                                <arg>loopback</arg>
                            </gpgArguments>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <extensions>true</extensions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!--
            This profile enables consuming snapshot artifacts from the ossrh
            snapshots repository.
        -->
        <profile>
            <id>snapshots</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>sonatype-nexus-snapshots</id>
                    <name>Sonatype Nexus Snapshots</name>
                    <url>${sonatypeOssDistMgmtSnapshotsUrl}</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>sonatype-nexus-snapshots</id>
                    <name>Sonatype Nexus Snapshots</name>
                    <url>${sonatypeOssDistMgmtSnapshotsUrl}</url>
                    <releases>
                        <enabled>false</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!--
            This profile enables consuming artifacts from the ossrh staging
            repository group.
        -->
        <profile>
            <id>staging</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>sonatype-nexus-staging</id>
                    <name>Sonatype Nexus Staging</name>
                    <url>${sonatypeOssDistMgmtStagingUrl}</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>sonatype-nexus-staging</id>
                    <name>Sonatype Nexus Staging</name>
                    <url>${sonatypeOssDistMgmtStagingUrl}</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
</project>
