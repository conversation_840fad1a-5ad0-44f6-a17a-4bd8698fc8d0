package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractTypeDTO;
import com.chinasie.orion.domain.entity.ContractType;
import com.chinasie.orion.domain.vo.ContractTypeVO;
import com.chinasie.orion.repository.ContractTypeMapper;
import com.chinasie.orion.service.ContractTypeService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * ContractType 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10 03:27:16
 */
@Service
@Slf4j
public class ContractTypeServiceImpl extends  OrionBaseServiceImpl<ContractTypeMapper, ContractType>   implements ContractTypeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractTypeVO detail(String id, String pageCode) throws Exception {
        ContractType contractType =this.getById(id);
        ContractTypeVO result = BeanCopyUtils.convertTo(contractType,ContractTypeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractTypeDTO
     */
    @Override
    public  String create(ContractTypeDTO contractTypeDTO) throws Exception {
        ContractType contractType =BeanCopyUtils.convertTo(contractTypeDTO,ContractType::new);
        this.save(contractType);

        String rsp=contractType.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractTypeDTO
     */
    @Override
    public Boolean edit(ContractTypeDTO contractTypeDTO) throws Exception {
        ContractType contractType =BeanCopyUtils.convertTo(contractTypeDTO,ContractType::new);

        this.updateById(contractType);

        String rsp=contractType.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractTypeVO> pages( Page<ContractTypeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractType> condition = new LambdaQueryWrapperX<>( ContractType. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractType::getCreateTime);


        Page<ContractType> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractType::new));

        PageResult<ContractType> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractTypeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractTypeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractTypeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购合同标的类别导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractTypeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ContractTypeExcelListener excelReadListener = new ContractTypeExcelListener();
        EasyExcel.read(inputStream,ContractTypeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractTypeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购合同标的类别导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractType> contractTypees =BeanCopyUtils.convertListTo(dtoS,ContractType::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractType-import::id", importId, contractTypees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractType> contractTypees = (List<ContractType>) orionJ2CacheService.get("pmsx::ContractType-import::id", importId);
        log.info("采购合同标的类别导入的入库数据={}", JSONUtil.toJsonStr(contractTypees));

        this.saveBatch(contractTypees);
        orionJ2CacheService.delete("pmsx::ContractType-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractType-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractType> condition = new LambdaQueryWrapperX<>( ContractType. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractType::getCreateTime);
        List<ContractType> contractTypees =   this.list(condition);

        List<ContractTypeDTO> dtos = BeanCopyUtils.convertListTo(contractTypees, ContractTypeDTO::new);

        String fileName = "采购合同标的类别数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractTypeDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ContractTypeVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractTypeExcelListener extends AnalysisEventListener<ContractTypeDTO> {

        private final List<ContractTypeDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractTypeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractTypeDTO> getData() {
            return data;
        }
    }

    @Override
    public List<ContractTypeVO> listByNumber(ContractTypeDTO param) throws Exception {
        LambdaQueryWrapperX<ContractType> condition = new LambdaQueryWrapperX<>( ContractType. class);
        condition.eq(ContractType::getContractNumber,param.getContractNumber());
        List<ContractType> list = this.list(condition);
        return BeanCopyUtils.convertListTo(list,ContractTypeVO::new);

    }
}
