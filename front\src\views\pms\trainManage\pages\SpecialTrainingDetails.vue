<script setup lang="ts">
import {
  BasicTableAction, IDataStatus, Layout3, randomString,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, readonly, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import BasicInfo from './components/BasicInfo.vue';
import UserInfo from './components/UserInfo.vue';
import ProveInfo from './components/ProveInfo.vue';
import { useCompletion } from '/@/views/pms/trainManage/pages/hooks';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const pageType: string = 'special';
provide('pageType', pageType);
const powerPrefix: string = 'PMS_DXRCZXPXXQ_container';
provide('powerPrefix', powerPrefix);
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.name,
  projectCode: detailsData.typeName,
  dataStatus: detailsData.dataStatus,
  ownerName: detailsData.ownerName,
}));

const menuData = computed(() => [
  {
    id: 'info',
    name: '培训详情',
    powerCode: `${powerPrefix}_02`,
  },
  {
    id: 'user',
    name: '参培人员',
    powerCode: `${powerPrefix}_03`,
  },
  {
    id: 'prove',
    name: '培训证明',
    powerCode: `${powerPrefix}_04`,
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/train-manage').fetch({
      pageCode: `PMS${route.name as string}`,
    }, dataId.value, 'GET');
    Object.assign(detailsData, result);
    powerData.value = result?.detailAuthList;
    if (detailsData?.isCheck) {
      trainCenterId.value = detailsData?.trainCenterId;
    }
    updateKey();
  } finally {
    loading.value = false;
  }
}

provide('updateDetails', readonly(getDetails));

const trainCenterId: Ref<string> = ref('');
const actions = computed(() => [
  {
    text: '办结',
    icon: 'sie-icon-xiangmuyanshou',
    powerCode: `${powerPrefix}_01_button_01`,
    modalTitle: '办结提示！',
    modalContent: '是否确认办结？',
    isShow: () => detailsData?.status !== 1 && detailsData?.isCheck === true,
    modal: () => completionApi(unref(trainCenterId)),
  },
]);

const { completionApi } = useCompletion({
  updateFn: getDetails,
});

const layoutKey: Ref<string> = ref();

function updateKey() {
  layoutKey.value = randomString();
}

</script>

<template>
  <Layout3
    :key="layoutKey"
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :powerData="powerData"
        :actions="actions"
        type="button"
      />
    </template>
    <template
      v-if="detailsData?.id"
    >
      <BasicInfo
        v-if="actionId==='info'"
      />
      <UserInfo
        v-if="actionId==='user'"
      />
      <ProveInfo
        v-if="actionId==='prove'"
      />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
