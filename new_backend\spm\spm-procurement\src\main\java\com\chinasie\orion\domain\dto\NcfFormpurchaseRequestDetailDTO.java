package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormpurchaseRequestDetail DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@ApiModel(value = "NcfFormpurchaseRequestDetailDTO对象", description = "采购申请行项目表")
@Data
@ExcelIgnoreUnannotated
public class NcfFormpurchaseRequestDetailDTO extends ObjectDTO implements Serializable {

    /**
     * 行项目id
     */
    @ApiModelProperty(value = "行项目id")
    @ExcelProperty(value = "行项目id ", index = 0)
    private String projectId;

    /**
     * 内部订单
     */
    @ApiModelProperty(value = "内部订单")
    @ExcelProperty(value = "内部订单 ", index = 1)
    private String internalOrder;

    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    @ExcelProperty(value = "物料 ", index = 2)
    private String item;

    /**
     * 物料组
     */
    @ApiModelProperty(value = "物料组")
    @ExcelProperty(value = "物料组 ", index = 3)
    private String itemGroup;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    @ExcelProperty(value = "总账科目 ", index = 4)
    private String generalLedgerSubject;

    /**
     * 资产
     */
    @ApiModelProperty(value = "资产")
    @ExcelProperty(value = "资产 ", index = 5)
    private String asset;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @ExcelProperty(value = "需求数量 ", index = 6)
    private String requiredQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位 ", index = 7)
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    @ExcelProperty(value = "交货时间 ", index = 8)
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 9)
    private String unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @ExcelProperty(value = "总价 ", index = 10)
    private String totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    @ExcelProperty(value = "本位币金额 ", index = 11)
    private BigDecimal localCurrencyAmount;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 12)
    private String costCenter;

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    @ExcelProperty(value = "项目编号/名称 ", index = 13)
    private String projectIdName;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    @ExcelProperty(value = "WBS编号 ", index = 14)
    private String wbsId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 15)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 16)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ")
    @ExcelIgnore
    private String contractNumber;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "主表ID ", index = 17)
    private String projectCode;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号 ", index = 17)
    private String projectNumber;
}
