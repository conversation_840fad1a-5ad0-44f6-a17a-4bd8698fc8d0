package com.chinasie.orion.domain.dto;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Data
@ApiModel(value = "ProjectSchemeContentDTO对象", description = "项目计划记录内容")
public class ProjectSchemeContentBatchDTO {
    List<ProjectSchemeContentDTO> contentDTOS;

    List<FileDTO> attachments;
}
