package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.tree.OrionTreeNodeVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * DocumentModelLibraryDir VO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@ApiModel(value = "DocumentModelLibraryDirVO对象", description = "文档模板库文件夹")
@Data
public class DocumentModelLibraryDirVO extends OrionTreeNodeVO<DocumentModelLibraryDirVO> implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


}
