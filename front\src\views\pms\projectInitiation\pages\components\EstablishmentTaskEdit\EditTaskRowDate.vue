<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch, onUnmounted,
} from 'vue';
import { DatePicker as ADatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';
const props = withDefaults(defineProps<{
    record:object,
    fieldName:string
}>(), {
  record: () => ({}),
  fieldName: '',
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const isShowPanel:Ref<boolean> = ref(false);
const dateValue:Ref<string> = ref('');
// 下拉框展开操作
function handleMouseleave() {
  if (!isShowPanel.value) {
    isEdit.value = false;
  }
  //
}

const handleClick = (event) => {
  if (props.record.status !== 101) return;
  isEdit.value = true;
  dateValue.value = props.record[props.fieldName];
};
onMounted(() => {
  dateValue.value = props.record[props.fieldName];
});
function pressEnter(value: []) {
}
function changeDate(value) {
  emit('change', dateValue.value, changeItemType);
}
function changeItemType() {
  isEdit.value = false;
}
// 面板打开的回调
function openChange(val) {
  isShowPanel.value = val;
}
defineExpose({
  changeItemType: () => {
    isEdit.value = false;
  },
});
</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
    @mouseenter="handleClick"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="dayjs(record[fieldName]).format('YYYY-MM-DD')"
      @click="handleClick"
    >
      {{ dayjs(record[fieldName]).format('YYYY-MM-DD') }}
    </div>
    <div
      v-else
      class="row-name-value"
    >
      <ADatePicker
        v-model:value="dateValue"
        value-format="YYYY-MM-DD"
        @change="changeDate"
        @openChange="openChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
