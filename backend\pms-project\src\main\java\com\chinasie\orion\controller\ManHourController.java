package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ManHourDTO;
import com.chinasie.orion.domain.entity.ManHour;
import com.chinasie.orion.domain.vo.ManHourVo;
import com.chinasie.orion.domain.vo.PlanManHourVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ManHourService;
import com.chinasie.orion.util.BeanCopyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:55
 * @description:
 */
@RestController
@RequestMapping("/man-hour")
@Api(tags = "工时表（是否还在使用？）")
@Deprecated
public class ManHourController {

    @Resource
    private ManHourService manHourService;

    @ApiOperation("新增工时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "manHourDTO", dataType = "ManHourDTO")
    })
    @PostMapping(value = "")
    public ResponseDTO<String> saveManHour(@RequestBody ManHourDTO manHourDTO) throws Exception {
        return new ResponseDTO(manHourService.saveManHour(manHourDTO));
    }


    @ApiOperation("修改工时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "manHourDTO", dataType = "ManHourDTO")
    })
    @PutMapping(value = "")
    public ResponseDTO<Boolean> updateManHour(@RequestBody ManHourDTO manHourDTO) throws Exception {
        return new ResponseDTO(manHourService.updateManHour(manHourDTO));
    }


    @ApiOperation("获取工时详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/{id}")
    public ResponseDTO<ManHourDTO> getById(@PathVariable("id") String id) throws Exception {
        ManHour manHour = manHourService.getById(id);
        return new ResponseDTO<>(BeanCopyUtils.convertTo(manHour, ManHourDTO::new));
    }


    @ApiOperation("删除工时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @DeleteMapping(value = "/{id}")
    public ResponseDTO<Boolean> del(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(manHourService.delByIdList(Collections.singletonList(id)));
    }

    @ApiOperation("批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @DeleteMapping(value = "/batch")
    public ResponseDTO<Boolean> delByList(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO(manHourService.delByIdList(idList));
    }

    @ApiOperation("获取工时列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/page")
    public ResponseDTO<PageResult<ManHourVo>> savePlan(@RequestBody PageRequest<ManHourDTO> pageRequest) throws Exception {
        return new ResponseDTO(manHourService.pageList(pageRequest));
    }


    @ApiOperation("获取当前计划下的所有工时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", dataType = "String")
    })
    @GetMapping(value = "/list")
    public ResponseDTO<PlanManHourVo> listByPlanId(@RequestParam("planId") String planId) throws Exception {
        return new ResponseDTO(manHourService.listByPlanId(planId));
    }
}
