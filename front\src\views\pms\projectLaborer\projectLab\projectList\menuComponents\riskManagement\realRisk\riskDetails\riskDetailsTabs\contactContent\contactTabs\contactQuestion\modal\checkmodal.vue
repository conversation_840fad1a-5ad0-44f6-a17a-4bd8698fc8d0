<template>
  <div class="checkDetails">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="checkDetailsDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane
          key="1"
          tab="概述"
          class="tabPaneStyle"
        />
      </a-tabs>
      <!--      <basicTitle :title="'预览'">-->
      <!--        <div style="margin: 5px 5px 0 8px">-->
      <!--          <img :src="datavalue[0].imageId ? pictureBase + datavalue[0].imageId : emptyString">-->
      <!--        </div>-->
      <!--      </basicTitle>-->

      <basicTitle
        :title="'基本信息'"
        class="checkDetailsMessage"
      >
        <div class="messageContent">
          <template
            v-for="(item, index) in valueList"
            :key="index"
          >
            <div class="messageContent_row">
              <span class="messageContent_row_label">{{ item.label }}</span>
              <span class="messageContent_row_value">{{
                ['modifyTime', 'createTime', 'proposedTime', 'predictEndTime'].includes(
                  item.fieldName
                )
                  ? datavalue[0][item.fieldName]
                    ? dayjs(datavalue[0][item.fieldName]).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                  : [].includes(item.fieldName)
                    ? datavalue[0][item.fieldName]
                      ? dayjs(datavalue[0][item.fieldName]).format('YYYY-MM-DD')
                      : ''
                    : datavalue[0][item.fieldName]
              }}</span>
            </div>
          </template>
        </div>
      </basicTitle>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch,
} from 'vue';
import { Drawer, Tabs } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import { pictureBase, emptyString } from '/@/views/pms/projectLaborer/api/picture';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aTabs: Tabs,
    aTabPane: Tabs.TabPane,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      visible: false,
      title: '查看详情',
      nextCheck: false,
      activeKey: '1',
      valueList: [
        {
          label: '编号',
          fieldName: 'number',
        },
        {
          label: '名称',
          fieldName: 'name',
        },
        //   { label: '类型', fieldName: 'classifyName' },
        {
          label: '所属项目',
          fieldName: 'projectName',
        },
        {
          label: '问题内容',
          fieldName: 'content',
        },
        {
          label: '问题类型',
          fieldName: 'questionTypeName',
        },
        {
          label: '严重程度',
          fieldName: 'seriousLevelName',
        },
        {
          label: '提出人',
          fieldName: 'exhibitor',
        },
        {
          label: '提出时间',
          fieldName: 'proposedTime',
        },
        {
          label: '期望完成时间',
          fieldName: 'predictEndTime',
        },
        {
          label: '接收人',
          fieldName: 'recipientName',
        },
        {
          label: '负责人',
          fieldName: 'principalName',
        },
        {
          label: '状态',
          fieldName: 'statusName',
        },
        {
          label: '优先级',
          fieldName: 'priorityLevelName',
        },
        {
          label: '进度',
          fieldName: 'scheduleName',
        },
        {
          label: '修改人',
          fieldName: 'modifyName',
        },
        {
          label: '修改时间',
          fieldName: 'modifyTime',
        },
        {
          label: '创建人',
          fieldName: 'creatorName',
        },
        {
          label: '创建时间',
          fieldName: 'createTime',
        },
      ],
      datavalue: {},
    });
    const formRef = ref();
    const formState = reactive({
      // parentId: '',
      // name: '',
      // designation: '',
      // mark: ''
    });

    watch(
      () => props.data,
      (newVal) => {
        state.visible = true;
        state.datavalue = { ...newVal };
      },
    );

    return {
      ...toRefs(state),
      formRef,
      pictureBase,
      emptyString,
      formState,
      close() {
        state.visible = false;
        emit('close', false);
      },
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  //.checkDetailsDrawer {
    .ant-drawer-body {
      padding: 60px 10px 0px 10px !important;
    }
    img {
      height: 262px;
      width: 100%;
    }
    .checkDetailsMessage {
      margin-top: 10px;
    }
  //}
  .messageContent {
    padding-left: 10px;
    padding-bottom: 20px;
    .messageContent_row {
      display: flex;
      padding: 10px 0px;
      span {
        color: #444b5e;
      }
      .messageContent_row_label {
        display: inline-block;
        width: 120px;
        vertical-align: middle;
      }
      .messageContent_row_value {
        display: inline-block;
        vertical-align: middle;
        width: calc(~'100% - 120px');
      }
    }
  }
</style>
