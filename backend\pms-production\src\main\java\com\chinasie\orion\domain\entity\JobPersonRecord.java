package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobPersonRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@TableName(value = "pmsx_job_person_record")
@ApiModel(value = "JobPersonRecordEntity对象", description = "作业人员记录表")
@Data

public class JobPersonRecord extends  ObjectEntity  implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 人员code
     */
    @ApiModelProperty(value = "人员code")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 人员管理ID
     */
    @ApiModelProperty(value = "人员管理ID")
    @TableField(value = "person_manage_id")
    private String personManageId;

}
