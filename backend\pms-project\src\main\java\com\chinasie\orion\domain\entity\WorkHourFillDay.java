package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourFillDay Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 13:38:23
 */
@TableName(value = "pms_work_hour_fill_day")
@ApiModel(value = "WorkHourFillDay对象", description = "工时填报每天信息")
@Data
public class WorkHourFillDay extends ObjectEntity implements Serializable{

    /**
     * 工时填报id
     */
    @ApiModelProperty(value = "工时填报id")
    @TableField(value = "fill_id" )
    private String fillId;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    @TableField(value = "member_id" )
    private String memberId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    @TableField(value = "work_date" )
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "work_hour" )
    private Integer workHour;

}
