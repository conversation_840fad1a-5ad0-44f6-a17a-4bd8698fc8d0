package com.chinasie.orion.domain.vo;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.ArrayList;
import java.util.List;
/**
 * TaskDecomposition VO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@ApiModel(value = "TaskDecompositionVO对象", description = "任务分解")
@Data
public class TaskDecompositionVO extends ObjectVO implements TreeUtils.TreeNode<String, TaskDecompositionVO> ,Serializable{

        /**
         * 父级id
         */
        @ApiModelProperty(value = "父级id")
        private String parentId;


        /**
         * 责任部门
         */
        @ApiModelProperty(value = "责任部门")
        private String rspDept;
        @ApiModelProperty(value = "责任部门名称")
        private String rspDeptName;


        /**
         * 责任人
         */
        @ApiModelProperty(value = "责任人")
        private String rspUser;
        @ApiModelProperty(value = "责任人名称")
        private String rspUserName;


        /**
         * 工期
         */
        @ApiModelProperty(value = "工期")
        private Integer workTime;


        /**
         * 处理实例
         */
        @ApiModelProperty(value = "处理实例")
        private String processInstances;
        @ApiModelProperty(value = "处理实例名称")
        private String processInstancesName;


        /**
         * 处理对象
         */
        @ApiModelProperty(value = "处理对象")
        private String processObject;
        @ApiModelProperty(value = "处理对象名称")
        private String processObjectName;


        /**
         *  任务名称
         */
        @ApiModelProperty(value = " 任务名称")
        private String name;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


        /**
         * 主表ID
         */
        @ApiModelProperty(value = "主表ID")
        private String mainTableId;


        /**
         *  文档分解ID
         */
        @ApiModelProperty(value = "文档分解ID")
        private String documentDecompositionId;


        /**
         * 子项
         */
        @ApiModelProperty(value = "子项")
        private List<TaskDecompositionVO> children;


        /**
         * 是否前置关系
         */
        @ApiModelProperty(value = "是否前置关系")
        private Boolean isPrePost;

        @ApiModelProperty(value = "项目计划前置关系")
        private List<TaskDecompositionPrePostVO> schemePrePostVOList = new ArrayList<>();

        @ApiModelProperty(value = "项目计划后置关系")
        private List<TaskDecompositionPrePostVO> schemePostVOList = new ArrayList<>();

        @ApiModelProperty(value = "项目计划前后置关系")
        private List<TaskDecompositionPrePostVO>  projectSchemePrePostVOS = new ArrayList<>();
}
