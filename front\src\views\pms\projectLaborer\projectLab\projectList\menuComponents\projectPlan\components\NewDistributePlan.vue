<template>
  <div class="dis-body">
    <p class="dis-body-message">
      计划将通过待办通知下发给责任人，计划下发后无法撤回，请悉知
    </p>
    <a-radio-group v-model:value="distributesState.needNote">
      <a-radio :value="false">
        不需要抄送
      </a-radio>
      <a-radio :value="true">
        需要抄送
      </a-radio>
    </a-radio-group>

    <div
      v-if="distributesState.needNote"
      class="flex-box"
    >
      <span>请选择抄送人：</span>
      <a-select
        v-model:value="distributesState.beNotifiedPersons"
        placeholder="请选择抄送人"
        style="width: 400px"
        mode="multiple"
        :options="userList"
      >
        <a-select-option value="private">
          Private
        </a-select-option>
        <a-select-option value="public">
          Public
        </a-select-option>
      </a-select>
    </div>
    <div class="workProcess">
      是否需要审核
    </div>
    <a-radio-group v-model:value="distributesState.isProcess">
      <a-radio :value="false">
        不需要审核
      </a-radio>
      <a-radio :value="true">
        需要审核
      </a-radio>
    </a-radio-group>
    <div class="p10">
      反馈提醒：
      <a-switch
        v-model:checked="checked"
        checked-children="开"
        un-checked-children="关"
      />
    </div>
    <div
      v-if="checked"
      class="p10 plan-time"
    >
      每隔
      <div>
        <a-input-number
          v-model:value="issueRemindInterval"
          :min="0"
          :max="500"
          placeholder="请输入"
          class="plan-time-number"
        />
      </div>

      <a-select
        v-model:value="issueRemindIntervalUnit"
        style="width:60px;"
        placeholder="请选择"
        :options="[
          {value:'时'},
          {value:'天'},
          {value:'周'},
          {value:'月'},
        ]"
      />
      在
      <ATimePicker
        v-model:value="issueRemindTime"
        value-format="HH:mm:ss"
      />
      需计划负责人反馈一次执行进度
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  defineComponent, ref, reactive, Ref, onMounted,
} from 'vue';
import {
  message,
  RadioGroup as ARadioGroup,
  Select as ASelect,
  SelectOption as ASelectOption,
  Radio as ARadio, Switch as ASwitch, InputNumber as AInputNumber, TimePicker as ATimePicker,
} from 'ant-design-vue';
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import Api from '/@/api';
const props = withDefaults(defineProps<{
   modalData:object
}>(), {
  modalData: () => ({}),
});
const checked = ref(false);
const issueRemindInterval:Ref<any> = ref(undefined);
const issueRemindIntervalUnit = ref('时');
const issueRemindTime:Ref<string> = ref('');
const userList = ref([]);
const distributesState:Ref<Record<any, any>> = ref({
  needNote: false,
  beNotifiedPersons: [],
  isProcess: false,
});

onMounted(() => {
  getUserList();
});

const getUserList = async () => {
  const res = await new Api('/pms').fetch(
    '',
    `project-role-user/${props.modalData.projectId}/allUser`,
    'POST',
  );
  userList.value = res.map((item) => ({
    value: item.id,
    label: item.name,
  }));
};

// 计划下发确认

defineExpose({
  async onSubmit() {
    if (checked.value && !issueRemindInterval.value) {
      message.error('请输入每隔的时间段');
      return;
    }
    const params = {
      ...props.modalData,
      ...distributesState.value,
      issueRemindInterval: checked.value ? issueRemindInterval.value : null, // 值 0 ~ 500
      issueRemindIntervalUnit: checked.value ? issueRemindIntervalUnit.value : null, // 时 天 周 月
      issueRemindTime: checked.value ? issueRemindTime.value : null, // 时 天 周 月

    };
    await new Api('/pms').fetch(params, 'projectScheme/issue', 'PUT');
    message.success('下发成功');
  },
});
</script>
<style lang="less" scoped>
.dis-body {
  padding: 22px 22px 30px;
  .dis-body-message{
    font-size: 15px;
    font-weight: 500;
  }
  .workProcess{
    font-weight: 500;
    padding: 20px 0 10px 0;
  }
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > span {
      margin-right: 10px;
    }
  }
}
.plan-time{
  display: flex;
  align-items: center;
  gap: 0 10px;
  .plan-time-number{
    width: 80px !important;
  }
}
</style>
