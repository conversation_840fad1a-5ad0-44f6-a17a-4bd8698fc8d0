package com.chinasie.orion.domain.dto.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/7
 * @description:
 */
@Data
public class ChangeMaterialDTO {

    @NotBlank(message = "物料id不能为空")
    private String id;

    @ApiModelProperty(value = "物料")
    @NotEmpty(message = "未选择改配后的物料")
    private List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialVOList;

}
