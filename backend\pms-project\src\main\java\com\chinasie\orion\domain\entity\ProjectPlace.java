package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPlace Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
@TableName(value = "pmsx_project_place")
@ApiModel(value = "ProjectPlaceEntity对象", description = "项目地点")
@Data
public class ProjectPlace extends ObjectEntity implements Serializable {

    /**
     * 地区id
     */
    @ApiModelProperty(value = "地区id")
    @TableField(value = "area_id")
    private String areaId;

    /**
     * 项目数据主键
     */
    @ApiModelProperty(value = "项目数据主键")
    @TableField(value = "project_id")
    private String projectId;

}
