package com.chinasie.orion.service.quality.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.quality.QualityItemMessageDTO;
import com.chinasie.orion.domain.entity.quality.QualityItemMessage;
import com.chinasie.orion.domain.vo.quality.QualityItemMessageVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.quality.QualityItemMessageMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.quality.QualityItemMessageService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * QualityItemMessage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@Service
@Slf4j
public class QualityItemMessageServiceImpl extends OrionBaseServiceImpl<QualityItemMessageMapper, QualityItemMessage> implements QualityItemMessageService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QualityItemMessageVO detail(String id, String pageCode) throws Exception {
        QualityItemMessage qualityItemMessage = this.getById(id);
        QualityItemMessageVO result = BeanCopyUtils.convertTo(qualityItemMessage, QualityItemMessageVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param qualityItemMessageDTO
     */
    @Override
    public String create(QualityItemMessageDTO qualityItemMessageDTO) throws Exception {
        QualityItemMessage qualityItemMessage = BeanCopyUtils.convertTo(qualityItemMessageDTO, QualityItemMessage::new);
        this.save(qualityItemMessage);

        String rsp = qualityItemMessage.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param qualityItemMessageDTO
     */
    @Override
    public Boolean edit(QualityItemMessageDTO qualityItemMessageDTO) throws Exception {
        QualityItemMessage qualityItemMessage = BeanCopyUtils.convertTo(qualityItemMessageDTO, QualityItemMessage::new);

        this.updateById(qualityItemMessage);

        String rsp = qualityItemMessage.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QualityItemMessageVO> pages(Page<QualityItemMessageDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QualityItemMessage> condition = new LambdaQueryWrapperX<>(QualityItemMessage.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QualityItemMessage::getCreateTime);


        Page<QualityItemMessage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QualityItemMessage::new));

        PageResult<QualityItemMessage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QualityItemMessageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QualityItemMessageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QualityItemMessageVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "质量管控项和消息关联关系导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemMessageDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        QualityItemMessageExcelListener excelReadListener = new QualityItemMessageExcelListener();
        EasyExcel.read(inputStream, QualityItemMessageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QualityItemMessageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("质量管控项和消息关联关系导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<QualityItemMessage> qualityItemMessagees = BeanCopyUtils.convertListTo(dtoS, QualityItemMessage::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::QualityItemMessage-import::id", importId, qualityItemMessagees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QualityItemMessage> qualityItemMessagees = (List<QualityItemMessage>) orionJ2CacheService.get("pmsx::QualityItemMessage-import::id", importId);
        log.info("质量管控项和消息关联关系导入的入库数据={}", JSONUtil.toJsonStr(qualityItemMessagees));

        this.saveBatch(qualityItemMessagees);
        orionJ2CacheService.delete("pmsx::QualityItemMessage-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::QualityItemMessage-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QualityItemMessage> condition = new LambdaQueryWrapperX<>(QualityItemMessage.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QualityItemMessage::getCreateTime);
        List<QualityItemMessage> qualityItemMessagees = this.list(condition);

        List<QualityItemMessageDTO> dtos = BeanCopyUtils.convertListTo(qualityItemMessagees, QualityItemMessageDTO::new);

        String fileName = "质量管控项和消息关联关系数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemMessageDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<QualityItemMessageVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class QualityItemMessageExcelListener extends AnalysisEventListener<QualityItemMessageDTO> {

        private final List<QualityItemMessageDTO> data = new ArrayList<>();

        @Override
        public void invoke(QualityItemMessageDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QualityItemMessageDTO> getData() {
            return data;
        }
    }


}
