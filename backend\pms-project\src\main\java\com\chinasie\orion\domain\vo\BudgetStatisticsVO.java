package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BudgetStatisticsVO {

    @ApiModelProperty(value = "预算余额")
    private BigDecimal residueMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "总成本额")
    private BigDecimal expendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "使用比")
    private BigDecimal useRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "余额比")
    private BigDecimal residueRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "超支金额")
    private BigDecimal overspendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "超支比")
    private BigDecimal overspendRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "超支余额比")
    private BigDecimal overspendResidueRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "材料费余额")
    private BigDecimal materialsResidueMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "材料费")
    private BigDecimal materialsExpendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "材料使用率")
    private BigDecimal materialsUseRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "工资及劳务费余额")
    private BigDecimal wageResidueMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "工资及劳务费")
    private BigDecimal wageExpendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "工资使用率")
    private BigDecimal wageUseRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "占用材料费")
    private BigDecimal materialsOccupationMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "领用材料费")
    private BigDecimal materialsReceiveMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "占用比")
    private BigDecimal materialsOccupationRatio = BigDecimal.ZERO;

    @ApiModelProperty(value = "领用比")
    private BigDecimal materialsReceiveRatio = BigDecimal.ZERO;


}
