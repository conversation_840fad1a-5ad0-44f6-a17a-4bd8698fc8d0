<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>com.squareup.okhttp</groupId>
  <artifactId>parent</artifactId>
  <version>2.7.5</version>
  <packaging>pom</packaging>

  <name>OkHttp (Parent)</name>
  <description>An HTTP+SPDY client for Android and Java applications</description>
  <url>https://github.com/square/okhttp</url>

  <modules>
    <module>okhttp</module>
    <module>okhttp-tests</module>

    <module>okhttp-android-support</module>

    <module>okhttp-apache</module>
    <module>okhttp-testing-support</module>
    <module>okhttp-urlconnection</module>

    <module>okhttp-ws</module>
    <module>okhttp-ws-tests</module>

    <module>okhttp-logging-interceptor</module>

    <module>okcurl</module>
    <module>mockwebserver</module>
    <module>samples</module>
    <module>benchmarks</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- Compilation -->
    <java.version>1.7</java.version>
    <okio.version>1.6.0</okio.version>
    <!-- ALPN library targeted to Java 7 -->
    <alpn.jdk7.version>7.1.2.v20141202</alpn.jdk7.version>
    <!-- ALPN library targeted to Java 8 update 25. -->
    <alpn.jdk8.version>8.1.2.v20141202</alpn.jdk8.version>
    <bouncycastle.version>1.50</bouncycastle.version>
    <gson.version>2.2.3</gson.version>
    <apache.http.version>4.2.2</apache.http.version>
    <airlift.version>0.6</airlift.version>
    <guava.version>16.0</guava.version>
    <android.version>4.1.1.4</android.version>

    <!-- Test Dependencies -->
    <junit.version>4.11</junit.version>
  </properties>

  <scm>
    <url>https://github.com/square/okhttp/</url>
    <connection>scm:git:https://github.com/square/okhttp.git</connection>
    <developerConnection>scm:git:**************:square/okhttp.git</developerConnection>
    <tag>parent-2.7.5</tag>
  </scm>

  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/square/okhttp/issues</url>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.squareup.okio</groupId>
        <artifactId>okio</artifactId>
        <version>${okio.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${apache.http.version}</version>
      </dependency>
      <dependency>
        <groupId>io.airlift</groupId>
        <artifactId>airline</artifactId>
        <version>${airlift.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.android</groupId>
        <artifactId>android</artifactId>
        <version>${android.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.0</version>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.17</version>
          <configuration>
            <properties>
              <!--
                Configure a listener for enforcing that no uncaught exceptions issue from OkHttp
                tests. Every test must have a <scope>test</scope> dependency on
                okhttp-testing-support.
                -->
              <property>
                <name>listener</name>
                <value>com.squareup.okhttp.testing.InstallUncaughtExceptionHandlerListener</value>
              </property>
            </properties>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>2.17</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.9</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.4.2</version>
        <dependencies>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-provider-gitexe</artifactId>
            <version>1.9</version>
          </dependency>
        </dependencies>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.10</version>
        <configuration>
          <failsOnError>true</failsOnError>
          <configLocation>checkstyle.xml</configLocation>
          <consoleOutput>true</consoleOutput>
          <excludes>**/CipherSuite.java</excludes>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>checkstyle</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.11</version>
        <executions>
          <execution>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.1</version>
          </signature>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>alpn-when-jdk7</id>
      <activation>
        <jdk>1.7</jdk>
      </activation>
      <properties>
        <bootclasspathPrefix>${settings.localRepository}/org/mortbay/jetty/alpn/alpn-boot/${alpn.jdk7.version}/alpn-boot-${alpn.jdk7.version}.jar</bootclasspathPrefix>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <argLine>-Xbootclasspath/p:${bootclasspathPrefix}</argLine>
              </configuration>
              <dependencies>
                <dependency>
                  <groupId>org.mortbay.jetty.alpn</groupId>
                  <artifactId>alpn-boot</artifactId>
                  <version>${alpn.jdk7.version}</version>
                </dependency>
              </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>alpn-when-jdk8</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <properties>
        <bootclasspathPrefix>${settings.localRepository}/org/mortbay/jetty/alpn/alpn-boot/${alpn.jdk8.version}/alpn-boot-${alpn.jdk8.version}.jar</bootclasspathPrefix>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <argLine>-Xbootclasspath/p:${bootclasspathPrefix}</argLine>
              </configuration>
              <dependencies>
                <dependency>
                  <groupId>org.mortbay.jetty.alpn</groupId>
                  <artifactId>alpn-boot</artifactId>
                  <version>${alpn.jdk8.version}</version>
                </dependency>
              </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>

