<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'actualAcceptanceTime',
    label: '实际验收时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'isAcceptanceQualified',
    label: '是否一次验收合格',
    colProps: { span: 12 },
    componentProps: {},
    component: 'Switch',
  },
  {
    field: 'actualDeliveryTime',
    label: '实际交付时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'reason',
    label: '原因',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
  {
    field: 'isDeliverOnTime',
    label: '是否按时交付',
    colProps: { span: 12 },
    componentProps: {},
    component: 'Switch',
  },
  {
    field: 'reasonOfUndeliver',
    label: '未按时交付验收原因',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    await setFieldsValue({
      ...props.record,
      isAcceptanceQualified: props.record.isAcceptanceQualified === '是',
      isDeliverOnTime: props.record.isDeliverOnTime === '是',
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
      isAcceptanceQualified: formValues.isAcceptanceQualified ? '是' : '否',
      isDeliverOnTime: formValues.isDeliverOnTime ? '是' : '否',
      mainTableId: props?.record?.mainTableId,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/actualPayMilestone').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
