package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/18 19:12
 * @description:
 */
public enum ProjectPurchaseOrderSourceEnum {
    MANUAL("manual", "手动新增"),
    ;

    private String code;

    private String description;

    ProjectPurchaseOrderSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
