package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobMaterialRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:37
 */
@ApiModel(value = "JobMaterialRecordDTO对象", description = "作业物资记录表")
@Data
@ExcelIgnoreUnannotated
public class JobMaterialRecordDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobId;

    /**
     * 物资code
     */
    @ApiModelProperty(value = "物资code")
    @ExcelProperty(value = "物资code ", index = 1)
    private String materiaCode;

    /**
     * 物资管理ID
     */
    @ApiModelProperty(value = "物资管理ID")
    @ExcelProperty(value = "物资管理ID ", index = 2)
    private String materiaManageId;




}
