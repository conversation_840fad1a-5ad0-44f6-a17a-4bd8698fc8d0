<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch, onUnmounted,
} from 'vue';
import { Select as ASelect, Tag as ATag } from 'ant-design-vue';
import { objectColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import { useUserStore } from '/@/store/modules/user';

const props = withDefaults(defineProps<{
    record:object,
    options:any
}>(), {
  record: () => ({}),
  options: null,
});
const emit = defineEmits(['change']);
const userInfo = useUserStore().getUserInfo;
const isEdit:Ref<boolean> = ref(false);
const dropdownVisible:Ref<boolean> = ref(false); // 是否展开下拉
const selectValue:Ref<string> = ref('');
// 下拉框展开操作
function handleMouseleave() {
  if (dropdownVisible.value) return;
  isEdit.value = false;
}

const mouseenter = (event) => {
  if (!isCreator()) return;
  if (props.record.status !== 101) return;
  isEdit.value = true;
  selectValue.value = props.record.processObject;
};
onMounted(async () => {
  selectValue.value = props.record.processObject;
});
function isCreator() {
  return [props.record.creator, props.record.issuedUser].includes(userInfo.id);
}
function dropdownVisibleChange(val:boolean) {
  dropdownVisible.value = val;
}
function changeSelect(val) {
  let itemData = props.options.find((item) => item.number === val);
  emit('change', itemData, changeItemType);
}
function changeItemType() {
  isEdit.value = false;
}
</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
    @mouseenter="mouseenter"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="record.selectValue"
    >
      <ATag :color="objectColor[selectValue]">
        {{ record.processObjectName }}
      </ATag>
    </div>
    <div
      v-else
      class="row-name-value"
    >
      <ASelect
        v-model:value="selectValue"
        style="width:100%"
        :options="options"
        :fieldNames="{label:'description',value:'number'}"
        @change="changeSelect"
        @dropdownVisibleChange="dropdownVisibleChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
