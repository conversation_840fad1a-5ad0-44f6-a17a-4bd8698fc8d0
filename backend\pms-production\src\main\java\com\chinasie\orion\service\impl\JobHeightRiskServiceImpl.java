package com.chinasie.orion.service.impl;





import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import com.chinasie.orion.constant.MajorRepairStatusEnum;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigJob;
import com.chinasie.orion.domain.entity.JobHeightRisk;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;


import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.repository.AmpereRingBoardConfigJobMapper;
import com.chinasie.orion.service.JobHeightRiskService;
import com.chinasie.orion.repository.JobHeightRiskMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobHeightRisk 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
@Service
@Slf4j
public class JobHeightRiskServiceImpl extends  OrionBaseServiceImpl<JobHeightRiskMapper, JobHeightRisk>   implements JobHeightRiskService {


    @Autowired
    private AmpereRingBoardConfigJobMapper ampereRingBoardConfigJobMapper;



    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobHeightRiskVO detail(String id,String pageCode) throws Exception {
        JobHeightRisk jobHeightRisk =this.getById(id);
        JobHeightRiskVO result = BeanCopyUtils.convertTo(jobHeightRisk,JobHeightRiskVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobHeightRiskDTO
     */
    @Override
    public  String create(JobHeightRiskDTO jobHeightRiskDTO) throws Exception {
        JobHeightRisk jobHeightRisk =BeanCopyUtils.convertTo(jobHeightRiskDTO,JobHeightRisk::new);
        this.save(jobHeightRisk);

        String rsp=jobHeightRisk.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobHeightRiskDTO
     */
    @Override
    public Boolean edit(JobHeightRiskDTO jobHeightRiskDTO) throws Exception {
        JobHeightRisk jobHeightRisk =BeanCopyUtils.convertTo(jobHeightRiskDTO,JobHeightRisk::new);

        this.updateById(jobHeightRisk);

        String rsp=jobHeightRisk.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobHeightRiskVO> pages( Page<JobHeightRiskDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobHeightRisk> condition = new LambdaQueryWrapperX<>( JobHeightRisk. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobHeightRisk::getCreateTime);


        Page<JobHeightRisk> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobHeightRisk::new));

        PageResult<JobHeightRisk> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobHeightRiskVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobHeightRiskVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobHeightRiskVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业高风险导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobHeightRiskDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobHeightRiskExcelListener excelReadListener = new JobHeightRiskExcelListener();
        EasyExcel.read(inputStream,JobHeightRiskDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobHeightRiskDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业高风险导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobHeightRisk> jobHeightRiskes =BeanCopyUtils.convertListTo(dtoS,JobHeightRisk::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobHeightRisk-import::id", importId, jobHeightRiskes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobHeightRisk> jobHeightRiskes = (List<JobHeightRisk>) orionJ2CacheService.get("pmsx::JobHeightRisk-import::id", importId);
        log.info("作业高风险导入的入库数据={}", JSONUtil.toJsonStr(jobHeightRiskes));

        this.saveBatch(jobHeightRiskes);
        orionJ2CacheService.delete("pmsx::JobHeightRisk-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobHeightRisk-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobHeightRisk> condition = new LambdaQueryWrapperX<>( JobHeightRisk. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobHeightRisk::getCreateTime);
        List<JobHeightRisk> jobHeightRiskes =   this.list(condition);

        List<JobHeightRiskDTO> dtos = BeanCopyUtils.convertListTo(jobHeightRiskes, JobHeightRiskDTO::new);

        String fileName = "作业高风险数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobHeightRiskDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobHeightRiskVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<JobHeightRiskVO> listByEntity(Page<JobHeightRiskDTO> jobHeightRiskDTO) {
        LambdaQueryWrapperX<JobHeightRisk> condition = new LambdaQueryWrapperX<>( JobHeightRisk. class);
        JobHeightRiskDTO jobHeightRiskDTO1 = jobHeightRiskDTO.getQuery();
        if(Objects.isNull(jobHeightRiskDTO1)){
            throw  new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB);
        }
        String  jobNumber =jobHeightRiskDTO1.getJobNumber();
        Assert.isTrue(StringUtils.hasText(jobNumber),()-> new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB));
        condition.eq(JobHeightRisk::getJobNumber,jobNumber);
        condition.in(JobHeightRisk::getProcessStatus,Arrays.asList("90","92"));
        List<JobHeightRisk> majorRepairPlanList = this.list(condition);
        if (CollectionUtils.isEmpty(majorRepairPlanList)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(majorRepairPlanList, JobHeightRiskVO::new);

    }

    @Override
    public List<JobHeightRiskVO> listByJobNumber(List<String> numberList) {
        if(CollectionUtils.isEmpty(numberList)){
            return new ArrayList<>();
        }
        //获取作业相关的 高风险数据
        LambdaQueryWrapperX<JobHeightRisk> condition = new LambdaQueryWrapperX<>( JobHeightRisk. class);
        condition.in(JobHeightRisk::getJobNumber,numberList);
        condition.select(JobHeightRisk::getJobNumber,JobHeightRisk::getRiskLevel
                ,JobHeightRisk::getRiskTypeName,JobHeightRisk::getJudgmentStandards);
        List<JobHeightRisk> jobHeightRiskList =this.list(condition);
        if(CollectionUtils.isEmpty(jobHeightRiskList)){
           return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(jobHeightRiskList, JobHeightRiskVO::new);
    }

    /**
     * 未完工详情
     *分级统计
     * 当前环节:070_现场作业
     * 作业状态：不是99，98
     * 按“当前环节”字段进行统计，统计“作业过程”状态的所有工单且开工日期在当天及之前的所有工单的数量。
     * @param riskVOPage
     * @return
     */
    @Override
    public Page<JobHeightRiskVO> jobUndoneDetails(Page<JobHeightRiskVO> riskVOPage) {
        //查询需要展示的作业
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> jobLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        jobLambdaQueryWrapperX.eq(AmpereRingBoardConfigJob::getIsShow,true);
        List<AmpereRingBoardConfigJob> ringBoardConfigJobs = ampereRingBoardConfigJobMapper.selectList(jobLambdaQueryWrapperX);
        List<String> jobNames = ringBoardConfigJobs.stream().map(o -> o.getJobName()).distinct().collect(Collectors.toList());

        LambdaQueryWrapperX<JobHeightRisk> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.le(JobHeightRisk::getPlanCommencementDate, DateTime.now());
//        lambdaQueryWrapperX.eq(JobHeightRisk::getCurrentPhase,"070_现场作业");
//        lambdaQueryWrapperX.notIn(JobHeightRisk::getProcessStatus, Arrays.asList(99,98));
//        List<String> currentPhases = Arrays.asList("010_发起流程","021_管控一/二级作业部门安全员审查","022_管控三级作业所安全员审查","030_所或模块经理批准","040_部门负责人或授权人批准","050_公司安质环部审查","060_总经理部批准","070_现场作业");
//        lambdaQueryWrapperX.in(JobHeightRisk ::getCurrentPhase,currentPhases);
        lambdaQueryWrapperX.eq(JobHeightRisk ::getCurrentPhase,"070_现场作业");
        lambdaQueryWrapperX.notIn(JobHeightRisk :: getProcessStatus,Arrays.asList(10,98,99));
        if(!CollectionUtils.isEmpty(jobNames)){
            lambdaQueryWrapperX.in(JobHeightRisk::getJudgmentStandards,jobNames);
        }
        JobHeightRiskVO query = riskVOPage.getQuery();
        if(Objects.nonNull(query)){
            if(StringUtils.hasText(query.getRiskLevel()) && Objects.equals("一级",query.getRiskLevel())){
                lambdaQueryWrapperX.eq(JobHeightRisk::getRiskLevel,query.getRiskLevel());
            }else{
                // 获取所有 非三级作业 包含
//                lambdaQueryWrapperX.eq(JobHeightRisk::getRiskLevel,query.getRiskLevel());
               if(StringUtils.hasText(query.getRiskLevel()) && !Objects.equals("一级",query.getRiskLevel())){
                   lambdaQueryWrapperX.select(JobHeightRisk::getCopyId,JobHeightRisk::getRiskLevel);
                   List<JobHeightRisk> jobHeightRiskList= this.list(lambdaQueryWrapperX);
                   lambdaQueryWrapperX.clear();
                   // TODO 因赶工临时处理
                   if(CollectionUtils.isEmpty(jobHeightRiskList)){
                       Page<JobHeightRiskVO> resultPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize(),0l);
                       return  resultPage;
                   }else{
                       Map<String, Set<String> > copyIdToRiskSetMap =jobHeightRiskList.stream()
                               .collect(Collectors.groupingBy(JobHeightRisk::getCopyId
                                       ,Collectors.mapping(JobHeightRisk::getRiskLevel,Collectors.toSet())));
                       List<String> copyIds = new ArrayList<>();
                       log.info("查询后的数据：{}",JSONUtil.toJsonStr(copyIdToRiskSetMap));
                       for (Map.Entry<String, Set<String>> stringSetEntry : copyIdToRiskSetMap.entrySet()) {
                           Set<String> riskLeveLSet = stringSetEntry.getValue();
                           if(!riskLeveLSet.contains("一级") && Objects.equals("二级",query.getRiskLevel())){
                               if(riskLeveLSet.contains("二级") ){
                                   copyIds.add(stringSetEntry.getKey());
                               }
                           }
                           if(!riskLeveLSet.contains("一级") && !riskLeveLSet.contains("二级") && Objects.equals("三级",query.getRiskLevel())){
                               Set<String> values= stringSetEntry.getValue();
                               if(values.size() == 1 && values.contains("三级")){
                                   copyIds.add(stringSetEntry.getKey());
                               }
                           }
                       }
                       if(CollectionUtils.isEmpty(copyIds)){
                           Page<JobHeightRiskVO> resultPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize(),0l);
                           return  resultPage;
                       }else{
                           lambdaQueryWrapperX.in(JobHeightRisk::getCopyId,copyIds);
                       }
                   }
                   if(!CollectionUtils.isEmpty(jobNames)){
                       lambdaQueryWrapperX.in(JobHeightRisk::getJudgmentStandards,jobNames);
                   }
//                   lambdaQueryWrapperX.in(JobHeightRisk ::getCurrentPhase,currentPhases);
                   lambdaQueryWrapperX.eq(JobHeightRisk ::getCurrentPhase,"070_现场作业");
                   lambdaQueryWrapperX.notIn(JobHeightRisk :: getProcessStatus,Arrays.asList(10,98,99));
               }
            }
            if(StringUtils.hasText(query.getJobAddressName())){
                lambdaQueryWrapperX.eq(JobHeightRisk::getJobAddressName,query.getJobAddressName());
            }
            if(StringUtils.hasText(query.getJudgmentStandards())){
                lambdaQueryWrapperX.eq(JobHeightRisk::getJudgmentStandards,query.getJudgmentStandards());
            }
        }
        lambdaQueryWrapperX.select(JobHeightRisk::getCopyId);
        lambdaQueryWrapperX.distinct();

        Page<JobHeightRisk> mPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize());

     //   lambdaQueryWrapperX.groupBy(JobHeightRisk::getJobNumber);
        PageResult<JobHeightRisk> result = this.baseMapper.selectPage(mPage, lambdaQueryWrapperX);
        log.info("执行到了------------------------------------------------------");
        //查询相关的job 的数据，进行数据处理
        if(CollectionUtil.isNotEmpty(result.getContent())){
            List<JobHeightRisk> jobHeightRiskList = result.getContent();
            List<JobHeightRisk> heightRisks = this.baseMapper.selectList(
                    JobHeightRisk::getCopyId, jobHeightRiskList.stream().map(JobHeightRisk::getCopyId).collect(Collectors.toList()));
            Map<String, List<JobHeightRisk>> jobNumberMap = heightRisks.stream().collect(Collectors.groupingBy(JobHeightRisk::getCopyId));

            result.getContent().forEach(o->{
                if(jobNumberMap.containsKey(o.getCopyId())){
                    List<JobHeightRisk> temps = jobNumberMap.get(o.getCopyId());
                    StringBuilder riskLevel=new StringBuilder("");
                    StringBuilder jd=new StringBuilder("");
                    for(int i=0;i<temps.size();i++){
                        JobHeightRisk jobHeightRisk = temps.get(i);
                        riskLevel.append(jobHeightRisk.getRiskLevel()+",");
                        jd.append(jobHeightRisk.getJudgmentStandards()+",");
                    }
                    BeanCopyUtils.copyProperties(temps.get(0),o);
                    o.setRiskLevel(riskLevel.toString());
                    o.setJudgmentStandards(jd.toString());
                }
            });
        }

        Page<JobHeightRiskVO> resultPage=new Page<>(result.getPageNum(), result.getPageSize(),result.getTotalSize());

        resultPage.setContent(BeanCopyUtils.convertListTo(result.getContent(),JobHeightRiskVO::new));
        return resultPage;
    }

    /**
     * 查询计划开工的查询详情
     *
     * @param riskVOPage
     * @return
     */
    @Override
    public Page<JobHeightRiskVO> queryPlanStartWorkDetails(Page<JobHeightRiskDTO> riskVOPage) {
        //查询需要展示的作业
        LambdaQueryWrapperX<AmpereRingBoardConfigJob> jobLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        jobLambdaQueryWrapperX.eq(AmpereRingBoardConfigJob::getIsShow,true);
        List<AmpereRingBoardConfigJob> ringBoardConfigJobs = ampereRingBoardConfigJobMapper.selectList(jobLambdaQueryWrapperX);
        List<String> jobNames = ringBoardConfigJobs.stream().map(o -> o.getJobName()).distinct().collect(Collectors.toList());

        LambdaQueryWrapperX<JobHeightRisk> lambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        JobHeightRiskDTO query = riskVOPage.getQuery();
        if(Objects.nonNull(query)){
            if(Objects.nonNull(query.getPlanStartTime())){
                lambdaQueryWrapperX.ge(JobHeightRisk::getPlanCommencementDate,query.getPlanStartTime());
            }
            if(Objects.nonNull(query.getPlanEndTime())){
                lambdaQueryWrapperX.le(JobHeightRisk::getPlanCommencementDate,query.getPlanEndTime());
            }
            if(StringUtils.hasText(query.getRiskLevel())){
                if(StringUtils.hasText(query.getRiskLevel()) && Objects.equals("一级",query.getRiskLevel())){
                    lambdaQueryWrapperX.eq(JobHeightRisk::getRiskLevel,query.getRiskLevel());
                }else{
                    // 获取所有 非三级作业 包含
//                lambdaQueryWrapperX.eq(JobHeightRisk::getRiskLevel,query.getRiskLevel());
                    if(StringUtils.hasText(query.getRiskLevel()) && !Objects.equals("一级",query.getRiskLevel())){
                        lambdaQueryWrapperX.select(JobHeightRisk::getCopyId,JobHeightRisk::getRiskLevel);
                        List<JobHeightRisk> jobHeightRiskList= this.list(lambdaQueryWrapperX);
                        lambdaQueryWrapperX.clear();
                        if(CollectionUtils.isEmpty(jobHeightRiskList)){
                            Page<JobHeightRiskVO> resultPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize(),0l);
                            return  resultPage;
                        }else{
                            Map<String, Set<String> > copyIdToRiskSetMap =jobHeightRiskList.stream()
                                    .collect(Collectors.groupingBy(JobHeightRisk::getCopyId
                                            ,Collectors.mapping(JobHeightRisk::getRiskLevel,Collectors.toSet())));
                            List<String> copyIds = new ArrayList<>();
                            log.info("查询后的数据：{}",JSONUtil.toJsonStr(copyIdToRiskSetMap));
                            for (Map.Entry<String, Set<String>> stringSetEntry : copyIdToRiskSetMap.entrySet()) {
                                Set<String> riskLeveLSet = stringSetEntry.getValue();
                                if(!riskLeveLSet.contains("一级") && Objects.equals("二级",query.getRiskLevel())){
                                    if(riskLeveLSet.contains("二级") ){
                                        copyIds.add(stringSetEntry.getKey());
                                    }
                                }
                                if(!riskLeveLSet.contains("一级") && !riskLeveLSet.contains("二级") && Objects.equals("三级",query.getRiskLevel())){
                                    Set<String> values= stringSetEntry.getValue();
                                    if(values.size() == 1 && values.contains("三级")){
                                        copyIds.add(stringSetEntry.getKey());
                                    }
                                }
                            }
                            if(CollectionUtils.isEmpty(copyIds)){
                                Page<JobHeightRiskVO> resultPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize(),0l);
                                return  resultPage;
                            }else{
                                lambdaQueryWrapperX.in(JobHeightRisk::getCopyId,copyIds);
                            }
                        }
                    }
                }
            }
            if(StringUtils.hasText(query.getJobAddressName())){
                lambdaQueryWrapperX.eq(JobHeightRisk::getJobAddressName,query.getJobAddressName());
            }
            if(StringUtils.hasText(query.getJudgmentStandards())){
                lambdaQueryWrapperX.eq(JobHeightRisk::getJudgmentStandards,query.getJudgmentStandards());
            }
        }
        if(!CollectionUtils.isEmpty(jobNames)){
            lambdaQueryWrapperX.in(JobHeightRisk::getJudgmentStandards,jobNames);
        }
//        List<String> currentPhases = Arrays.asList("010_发起流程","021_管控一/二级作业部门安全员审查","022_管控三级作业所安全员审查","030_所或模块经理批准","040_部门负责人或授权人批准","050_公司安质环部审查","060_总经理部批准","070_现场作业");
//        lambdaQueryWrapperX.in(JobHeightRisk ::getCurrentPhase,currentPhases);
        lambdaQueryWrapperX.eq(JobHeightRisk ::getCurrentPhase,"070_现场作业");
        lambdaQueryWrapperX.notIn(JobHeightRisk :: getProcessStatus,Arrays.asList(98,99));
//        lambdaQueryWrapperX.select(JobHeightRisk::getJobNumber);
        lambdaQueryWrapperX.select(JobHeightRisk::getCopyId);
        lambdaQueryWrapperX.distinct();
     //   lambdaQueryWrapperX.groupBy(JobHeightRisk::getJobNumber);
        Page<JobHeightRisk> mPage=new Page<>(riskVOPage.getPageNum(), riskVOPage.getPageSize());
        PageResult<JobHeightRisk> result = this.baseMapper.selectPage(mPage, lambdaQueryWrapperX);
        if(CollectionUtil.isNotEmpty(result.getContent())){
            List<JobHeightRisk> jobHeightRiskList = result.getContent();
            List<JobHeightRisk> heightRisks = this.baseMapper.selectList(JobHeightRisk::getCopyId, jobHeightRiskList.stream().map(JobHeightRisk::getCopyId).collect(Collectors.toList()));
            Map<String, List<JobHeightRisk>> jobNumberMap = heightRisks.stream().collect(Collectors.groupingBy(JobHeightRisk::getCopyId));
            result.getContent().forEach(o->{
                if(jobNumberMap.containsKey(o.getCopyId())){
                    List<JobHeightRisk> heightRiskList = jobNumberMap.get(o.getCopyId());
                    StringBuilder riskLevel=new StringBuilder("");
                    StringBuilder jd=new StringBuilder("");
                    for(int i=0;i<heightRiskList.size();i++){
                        JobHeightRisk jobHeightRisk = heightRiskList.get(i);
                        riskLevel.append(jobHeightRisk.getRiskLevel()+",");
                        jd.append(jobHeightRisk.getJudgmentStandards()+",");
                    }
                    BeanCopyUtils.copyProperties(heightRiskList.get(0),o);
                    o.setJudgmentStandards(jd.toString());
                    o.setRiskLevel(riskLevel.toString());
                }
            });
        }
        Page<JobHeightRiskVO> resultPage=new Page<>(result.getPageNum(), result.getPageSize(),result.getTotalSize());
        resultPage.setContent(BeanCopyUtils.convertListTo(result.getContent(),JobHeightRiskVO::new));
        return resultPage;
    }


    public static class JobHeightRiskExcelListener extends AnalysisEventListener<JobHeightRiskDTO> {

        private final List<JobHeightRiskDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobHeightRiskDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobHeightRiskDTO> getData() {
            return data;
        }
    }


}
