<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  DataStatusTag,
  IOrionTableActionItem,
  isPower,
  OrionTable,
  randomString,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import {
  computed, h, inject, onActivated, Ref, ref, unref, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { openOverhaulDailyForm } from '/@/views/pms/overhaulManagement/utils';
import { useUserStore } from '/@/store/modules/user';
import { processSearchConditions } from '/@/views/pms/majorRepairsSecond/pages/utils/util';
import { useJobExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';
import { useFilter } from '/@/views/pms/majorRepairsSecond/pages/components/Filter/useFilter';
import { getFilterOptions } from '/@/views/pms/majorRepairsSecond/pages/components/Filter/options/workDetail';

const props = defineProps<{
  progressRef: any
}>();

const refreshUpdateWorkKey = inject('refreshUpdateWorkKey');
const updateProgressKey = inject('updateProgressKey');
const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const showSelfWorkList = ref(false);
const userStore = useUserStore();

// 筛选条件
const sqlConditionList: Ref<string[]> = ref([]);

const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  smallSearchField: ['name'],
  api: (params: any) => new Api('/pms/job-manage/page').fetch({
    ...params,
    searchConditions: [],
    query: {
      norO: 'O', // O T P 归为大修 N M 归为日常 （T 日常转大修 M大修转日常)
      repairRound: detailsData?.repairRound,
      rspUserId: showSelfWorkList.value ? userStore.getUserInfo.id : undefined,
      keyword: processSearchConditions(params),
      sqlConditionList: unref(sqlConditionList),
    },
    power: {
      pageCode: 'PMSMajorRepairsSecondDetail',
      containerCode: 'PMS_DXXQEC_container_02',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 240,
    },
    {
      title: '项目计划',
      dataIndex: 'planSchemeName',
      width: 240,
    },
    {
      title: '作业名称',
      dataIndex: 'name',
      minWidth: 240,
      customRender({ text, record }) {
        if (isPower('PMS_DXXQEC_container_02_button_02', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 130,
    },
    {
      title: '作业负责人',
      width: 130,
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 130,
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      width: 130,
      customRender({ text, record }) {
        if ([isPower('PMS_DXXQEC_container_02_button_01', record?.rdAuthList), computed(() => record.isMajorProjectEdit).value].every(Boolean)) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text: text ? '是' : text === false ? '否' : '',
            componentValue: text,
            componentProps: {
              options: [
                {
                  label: '是',
                  value: true,
                },
                {
                  label: '否',
                  value: false,
                },
              ],
            },
            onSubmit(option: { label: string, value: boolean }, resolve: (value: any) => void) {
              new Api('/pms/job-manage/is/important/project').fetch({
                id: record?.id,
                isMajorProject: option.value,
              }, '', 'PUT').then(() => {
                resolve(true);
                message.success('操作成功');
                updateTable();
              });
            },
          });
        }
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '是否高风险',
      dataIndex: 'isHighRisk',
      width: 120,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险等级',
      width: 130,
      dataIndex: 'heightRiskName',
    },
    {
      title: '进展状态',
      width: 130,
      dataIndex: 'busDataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '作业状态',
      dataIndex: 'phase',
      width: 80,
    },
    {
      title: '工作包审查状态',
      dataIndex: 'workPackageStatusName',
      width: 130,
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
        return '';
      },
    },
    {
      title: '首次执行',
      width: 130,
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      width: 130,
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    // {
    //   title: '是否自带工器具',
    //   width: 130,
    //   dataIndex: 'isCarryTool',
    //   customRender({ text }) {
    //     return text ? '是' : text === false ? '否' : '';
    //   },
    // },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 130,
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
      },
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_DXXQEC_container_02_button_01', record?.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_DXXQEC_container_02_button_02', record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openOverhaulDailyForm(record, () => {
        updateProgressKey.value = randomString(32);
        updateTable();
      });
      break;
    case 'view':
      navDetails(record?.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'OverhaulOperationDetails',
    params: {
      id,
    },
    query: {
      id: detailsData?.id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function handleSwitchShowBtn(status) {
  showSelfWorkList.value = status;
  tableRef.value?.reload?.();
}

watch(() => refreshUpdateWorkKey.value, (val) => {
  if (val !== 'update') {
    updateTable();
  }
});

onActivated(() => {
  updateTable();
});

const { importApi, OrderImportRender, exportApi } = useJobExcel();

function importCallBack() {
  props?.progressRef?.update();
  updateTable();
}

const { tableWrapRef, FilterButtonRender } = useFilter();

// 筛选回调
function searchCb(sqlConditions: string[]) {
  sqlConditionList.value = sqlConditions;
  tableRef.value.reload();
}
</script>

<template>
  <div
    ref="tableWrapRef"
    class="work-detail"
  >
    <OrionTable
      ref="tableRef"
      class="radio-button-table"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-daoru"
          @click="importApi"
        >
          导入
        </BasicButton>
        <BasicButton
          icon="sie-icon-daochu"
          @click="exportApi({
            repairRound:detailsData?.repairRound
          })"
        >
          导出
        </BasicButton>
        <div class="user-self-row">
          <div
            v-if="!showSelfWorkList"
            class="work-btn"
            @click="handleSwitchShowBtn(true)"
          />
          <div
            v-else
            class="not-work-btn"
            @click="handleSwitchShowBtn(false)"
          />
          <FilterButtonRender
            :filterOptions="getFilterOptions({
              norO:'O',
              repairRound: detailsData?.repairRound,
            })"
            @search="searchCb"
          />
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
    <OrderImportRender
      :repairRound="detailsData?.repairRound"
      :cb="importCallBack"
    />
  </div>
</template>

<style scoped lang="less">
.work-detail {
  height: 600px;
  overflow: hidden;
  padding-top: 5px;
}

.user-self-row {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-left: 16px;
  margin-left: auto;

  .work-btn {
    width: 84px;
    height: 31px;
    background: url("/@/assets/images/work_self.png") center no-repeat;
    background-size: cover;
    cursor: pointer;
  }

  .not-work-btn {
    width: 84px;
    height: 31px;
    background: url("/@/assets/images/all_work.png") center no-repeat;
    background-size: cover;
    cursor: pointer;
  }
}

:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}
</style>
