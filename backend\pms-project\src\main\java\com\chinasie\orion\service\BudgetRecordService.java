package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetRecordDTO;
import com.chinasie.orion.domain.entity.BudgetRecord;
import com.chinasie.orion.domain.vo.BudgetRecordVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
public interface BudgetRecordService extends OrionBaseService<BudgetRecord> {

    List<BudgetRecordVO> getList(String budgetId, String type);


}
