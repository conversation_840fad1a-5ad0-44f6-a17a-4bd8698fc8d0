<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas: FormSchema[] = [
  {
    field: 'certificateType',
    label: '类型',
    componentProps: {
      dictNumber: 'pms_certificate_type',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'name',
    label: '名称',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'level',
    label: '证书等级',
    componentProps: {
      dictNumber: 'pms_certificate_level',
    },
    component: 'SelectDictVal',
  },
  {
    field: 'issuingAuthority',
    label: '发证机构',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'isNeedRenewal',
    label: '是否需要复审',
    componentProps: {
      options: [
        {
          label: '是',
          value: '是',
        },
        {
          label: '否',
          value: '否',
        },
      ],
    },
    rules: [{ required: true }],
    component: 'Select',
  },
  {
    field: 'renewalYearNum',
    label: '复审年限',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
      min: 0,
      max: 99999,
      precision: 0,
    },
    ifShow({ model }) {
      return model.isNeedRenewal === '是';
    },
    component: 'InputNumber',
  },

];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/certificate-info').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,

    };

    return new Promise((resolve, reject) => {
      new Api('/pms/certificate-info').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
