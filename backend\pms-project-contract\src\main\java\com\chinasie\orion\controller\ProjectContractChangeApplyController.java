package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectContractChangeApplyAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectContractChangeApplyDTO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyCreateVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyMainInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectContractChangeApplyService;
import com.chinasie.orion.util.CollectionUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProjectContractChangeApply 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@RestController
@RequestMapping("/projectContractChangeApply")
@Api(tags = "项目合同变更申请信息")
public class ProjectContractChangeApplyController {

    @Autowired
    private ProjectContractChangeApplyService projectContractChangeApplyService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看项目合同变更申请详情，业务编号：{#id}",
            type = "ProjectContractChangeApply",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractChangeApplyVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractChangeApplyVO rsp = projectContractChangeApplyService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取发起变更信息
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取发起变更信息")
    @RequestMapping(value = "/apply/{contractId}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取合同编号为{#contractId}的发起变更信息",
            type = "ProjectContractChangeApply",
            subType = "获取发起变更信息",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<ProjectContractChangeApplyCreateVO> apply(@PathVariable(value = "contractId") String contractId) throws Exception {
        ProjectContractChangeApplyCreateVO rsp = projectContractChangeApplyService.apply(contractId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取更改反显变更信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取更改反显变更信息")
    @LogRecord(
            success = "【{USER{#logUserId}}】获取合同变更申请编号为{#id}的更改反显变更信息",
            type = "ProjectContractChangeApply",
            subType = "获取更改反显变更信息",
            bizNo = "{#id}"
    )
    @RequestMapping(value = "/editInfo/{id}", method = RequestMethod.GET)
    public ResponseDTO<ProjectContractChangeApplyCreateVO> editInfoById(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractChangeApplyCreateVO rsp = projectContractChangeApplyService.editInfoById(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 变更申请所有信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "变更申请所有信息")
    @RequestMapping(value = "/all/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取合同变更申请编号为{#id}的所有信息",
            type = "ProjectContractChangeApply",
            subType = "变更申请所有信息",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractChangeApplyAllInfoVO> allInfo(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractChangeApplyAllInfoVO rsp = projectContractChangeApplyService.allInfo(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 变更申请主要信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "变更申请主要信息")
    @RequestMapping(value = "/main/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】获取合同变更申请编号为{#id}的主要信息",
            type = "ProjectContractChangeApply",
            subType = "变更申请主要信息",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractChangeApplyMainInfoVO> mainInfo(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractChangeApplyMainInfoVO rsp = projectContractChangeApplyService.mainInfo(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectContractChangeAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增项目合同变更申请",
            type = "ProjectContractChangeApply",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<ProjectContractChangeApplyVO> create(@RequestBody @Validated ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception {
        ProjectContractChangeApplyVO rsp = projectContractChangeApplyService.create(projectContractChangeAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectContractChangeAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑项目合同变更申请，业务编号：{#projectContractChangeApplyDTO.id}",
            type = "ProjectContractChangeApply",
            subType = "编辑",
            bizNo = "{#projectContractChangeApplyDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception {
        ProjectContractChangeApplyDTO projectContractChangeApplyDTO = projectContractChangeAllInfoDTO.getProjectContractChangeApplyDTO();
        if (!StringUtils.hasText(projectContractChangeApplyDTO.getId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "变更申请id不能为空!");
        }
        Boolean rsp = projectContractChangeApplyService.edit(projectContractChangeAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除项目合同变更申请，业务编号：{ID_LIST{#ids}}",
            type = "ProjectContractChangeApply",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectContractChangeApplyService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行项目合同变更申请分页查询",
            type = "ProjectContractChangeApply",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ProjectContractChangeApplyVO>> pages(@RequestBody Page<ProjectContractChangeApplyDTO> pageRequest) throws Exception {
        Page<ProjectContractChangeApplyVO> rsp = projectContractChangeApplyService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 根据合同编号获取变更列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号获取变更列表")
    @RequestMapping(value = "/list/{contractId}", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】根据合同编号{#contractId}获取变更列表",
            type = "ProjectContractChangeApply",
            subType = "根据合同获取列表",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<List<ProjectContractChangeApplyVO>> listByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ProjectContractChangeApplyVO> rsp = projectContractChangeApplyService.listByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }
}
