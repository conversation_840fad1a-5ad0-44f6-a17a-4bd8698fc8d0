package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@ApiModel(value = "ProjectContractDTO对象", description = "项目合同信息")
@Data
public class ProjectContractDTO extends ObjectDTO implements Serializable {

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @NotBlank(message = "合同名称不能为空")
    @Size(max = 100, message = "合同名称过长，建议控制在100字符以内")
    private String name;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 合同类别
     */
    @ApiModelProperty(value = "合同类别")
    @NotBlank(message = "合同类别不能为空")
    private String contractCategory;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @NotBlank(message = "合同类型不能为空")
    private String contractType;

    /**
     * 合同负责人id
     */
    @ApiModelProperty(value = "合同负责人id")
    @NotBlank(message = "合同负责人id不能为空")
    private String principalId;

    /**
     * 责任部门id
     */
    @ApiModelProperty(value = "责任部门id")
    private String rspDeptId;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @NotNull(message = "合同金额不能为空")
    @DecimalMin(value = "0.01", message = "进度值应大于等于0")
    private BigDecimal contractMoney;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currency;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @NotNull(message = "合同开始日期不能为空")
    private Date startDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @NotNull(message = "合同结束日期不能为空")
    private Date endDate;

    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    private Date signDate;

    /**
     * 是否具有质保期
     */
    @ApiModelProperty(value = "是否具有质保期")
    @NotNull(message = "是否具有质保期不能为空")
    private Boolean isGuaranteePeriod;

    /**
     * 是否具有质保金
     */
    @ApiModelProperty(value = "是否具有质保金")
    @NotNull(message = "是否具有质保金不能为空")
    private Boolean isGuaranteeMoney;

    /**
     * 预计质保期到期日期
     */
    @ApiModelProperty(value = "预计质保期到期日期")
    private Date guaranteeEndDate;

    /**
     * 质保金额
     */
    @ApiModelProperty(value = "质保金额")
    private BigDecimal guaranteeAmt;

    /**
     * 合同其他信息
     */
    @ApiModelProperty(value = "合同其他信息")
    @Size(max = 1000, message = "合同其他信息过长，建议控制在1000字符以内")
    private String contractOtherInfo;

}
