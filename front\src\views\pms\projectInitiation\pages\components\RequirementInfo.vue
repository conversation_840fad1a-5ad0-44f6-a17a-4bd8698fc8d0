<script setup lang="ts">
import {
  inject, onMounted, reactive, ref, Ref, watch,
} from 'vue';
import { BasicCard } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { declarationData } from '../keys';
import { formatMoney } from '../../index';
import Api from '/@/api';
// 立项详情数据
const detailsData = inject(declarationData);
const requireReviewData:Ref<Record<any, any>> = ref({});
const loading:Ref<boolean> = ref(false);
// 立项基本信息
const baseInfoProps = reactive({
  list: [
    {
      label: '需求评审标识',
      field: 'requireReviewLogo',
    },
    {
      label: '军兵种',
      field: 'armyArms',
    },
    {
      label: '应用场景',
      field: 'applicationScenarios',
    },
    {
      label: '是否定型',
      field: 'isCaseHardened',
    },
    {
      label: '是否军检',
      field: 'idExamine',
    },
    {
      label: '产品线',
      field: 'productLine',
      // valueRender: ({ record }) => (record.projectEndTime ? dayjs(record.projectEndTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '公司预计签订金额-共计',
      field: 'totalSignAmount',
      valueRender: ({ record }) => (record.totalSignAmount ? formatMoney(record.totalSignAmount) : '---'),
    },
    {
      label: '公司预计签订金额-本次',
      field: 'signAmount',
      valueRender: ({ record }) => (record.signAmount ? formatMoney(record.signAmount) : '---'),
    },
    {
      label: '商机标识',
      field: 'creatorName',
      width: 120,
    },
    {
      label: '商机名称',
      field: 'createTime',
      valueRender: ({ record }) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '签单客户',
      field: 'approvalReason',
      gridColumn: '3/5',
    },
    {
      label: '市场需求容量',
      field: 'demandCapacity',
      gridColumn: '1/3',
    },
    {
      label: '产品竞争情况',
      field: 'competitionSituation',
      gridColumn: '3/5',
    },
  ],
  column: 4,
  dataSource: requireReviewData,
});
watch(() => detailsData.value, (newDetailsData) => {
  if (newDetailsData.value.requireReviewId) {
    getRequireReviewData();
  }
});
onMounted(() => {
  if (detailsData.value.requireReviewId) {
    getRequireReviewData();
  }
});
function getRequireReviewData() {
  loading.value = true;
  new Api('/pms').fetch('', `requireReviewForm/${detailsData.value.requireReviewId}`, 'GET').then((res) => {
    requireReviewData.value = res;
    loading.value = false;
  });
}
</script>

<template>
  <div
    v-loading="loading"
    class="basic-info-content"
  >
    <BasicCard
      title="需求评审信息"
      :grid-content-props="baseInfoProps"
      :isBorder="false"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.basic-title){
  border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
  padding-bottom: 15px;
}
:deep(.card-content){
  margin-top: 0 !important;
}
.basic-info-content{
  height: 100%;
  padding-top: 1px;
}
</style>
