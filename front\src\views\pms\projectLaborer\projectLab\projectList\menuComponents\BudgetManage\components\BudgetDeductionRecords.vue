<script setup lang="ts">
import {
  BasicButton, BasicCard, BasicTableAction, OrionTable,
} from 'lyra-component-vue3';
import {
  h,
  inject, reactive, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const tableOptions = {

  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: false,
  columns: [
    {
      title: '类型',
      dataIndex: 'budgetChangeType',
    },
    {
      title: '表单编号',
      dataIndex: 'budgetChangeNumber',
      customRender({ record, text }) {
        return h('span', {
          onClick: () => {
            router.push({
              name: 'ExpenseManagementDetails',
              query: {
                id: record.budgetChangeId,
              },
            });
          },
          class: 'action-btn',
        }, text);
      },
    },
    {
      title: '标题',
      dataIndex: 'budgetChangeName',
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作人',
      dataIndex: 'operationPersonName',
    },
    {
      title: '支出金额（元）',
      dataIndex: 'changeMoney',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/budgetRecord/getList').fetch({

    // power: {
    //   containerCode: 'table-list-container-demo111-KcBsejQf',
    //   pageCode: 'list-container-demo111',
    // },
  }, `?budgetId=${detailsData?.id}&type=2`, 'GET'),

};

</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
