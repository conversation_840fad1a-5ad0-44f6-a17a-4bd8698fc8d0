package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.dto.train.SettingTrainPersonScoreDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterUserDTO;
import com.chinasie.orion.domain.entity.view.PersonTrainInfo;
import com.chinasie.orion.domain.vo.train.PersonTrainEffectVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.service.PersonTrainInfoService;
import com.chinasie.orion.xxljob.RepairTrainingXxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.TrainPerson;
import com.chinasie.orion.domain.dto.TrainPersonDTO;
import com.chinasie.orion.domain.vo.TrainPersonVO;
import com.chinasie.orion.service.TrainPersonService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * TrainPerson 前端控制器
 * </p>LL
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:14
 */
@RestController
@RequestMapping("/train-person")
@Api(tags = "参培人员")
public class  TrainPersonController  {

    @Autowired
    private TrainPersonService trainPersonService;

    @Autowired
    private PersonTrainInfoService trainInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【参培人员】【{{#number}}】的信息", type = "TrainManage", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TrainPersonVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        TrainPersonVO rsp = trainPersonService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getUserCode());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【参培人员】数据【{{#trainPersonDTO.userCode}}】", type = "TrainPerson", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TrainPersonDTO trainPersonDTO) throws Exception {
        String rsp =  trainPersonService.create(trainPersonDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【参培人员】数据【{{#trainPersonDTO.userCode}}】", type = "TrainPerson", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> createBatch(@RequestBody TrainCenterUserDTO trainPersonDTO) throws Exception {
        return new ResponseDTO<>(trainPersonService.createBatch(trainPersonDTO));
    }


    /**
     * 编辑
     *
     * @param trainPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【参培人员】数据【{{#trainPersonDTO.userCode}}】", type = "TrainPerson", subType = "编辑", bizNo = "{{#trainPersonDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  TrainPersonDTO trainPersonDTO) throws Exception {
        Boolean rsp = trainPersonService.edit(trainPersonDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【参培人员】数据", type = "TrainPerson", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = trainPersonService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【参培人员】数据", type = "TrainPerson", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = trainPersonService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【参培人员】分页数据", type = "TrainPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TrainPersonVO>> pages(@RequestBody Page<TrainPersonDTO> pageRequest) throws Exception {
        Page<TrainPersonVO> rsp =  trainPersonService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取人员下的培训信息")
    @LogRecord(success = "【{USER{#logUserId}}】查询【参培人员】人员下的培训信息", type = "TrainPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonTrainVO>> pagesList(@RequestBody Page<PersonTrainInfo> pageRequest) throws Exception {
        Page<PersonTrainVO> rsp =  trainInfoService.pagesList( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取培训中心下的参培人员列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【参培人员】培训中心下的参培人员列表", type = "TrainPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/train/person/list", method = RequestMethod.POST)
    public ResponseDTO<List<TrainPersonVO>> trainPersonList(@RequestBody TrainPersonDTO trainPersonDTO) throws Exception {
        List<TrainPersonVO> rsp =  trainPersonService.trainPersonList(trainPersonDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取未到期并且当前人已经通过培训的培训列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【参培人员】未到期并且当前人已经通过培训的培训列表", type = "TrainPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/train/current/list", method = RequestMethod.POST)
    public ResponseDTO<List<PersonTrainVO>> trainCurrentPersonList(@RequestBody EquivalentParamDTO equivalentParamDTO) throws Exception {
        List<PersonTrainVO> rsp =  trainPersonService.trainCurrentPersonList(equivalentParamDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "合格")
    @RequestMapping(value = "/setting/score/ok", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】设置【参培人员】【{{#trainNumber}}】下人员【{{#numbers}}】培训合格", type = "TrainPerson", subType = "编辑", bizNo = "{{#trainPersonDTO.id}}")
    public ResponseDTO<Boolean> scoreOK(@RequestBody  List<String> idList) throws Exception {
        Boolean rsp = trainPersonService.scoreOK(idList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     * @param idList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "不合格")
    @RequestMapping(value = "/setting/score/not/ok", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】设置【参培人员】【{{#trainNumber}}】人员【{{#numbers}}】培训不合格", type = "TrainPerson", subType = "编辑", bizNo = "{{#trainPersonDTO.id}}")
    public ResponseDTO<Boolean> scoreNotOK(@RequestBody  List<String> idList) throws Exception {
        Boolean rsp = trainPersonService.scoreNotOK(idList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     * @param scoreDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "设置分数")
    @RequestMapping(value = "/setting/score/value", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】设置【参培人员】人员【{{#number}}】分数【{{#score}}】", type = "TrainPerson", subType = "编辑", bizNo = "{{#trainPersonDTO.id}}")
    public ResponseDTO<Boolean> settingScore(@RequestBody SettingTrainPersonScoreDTO scoreDTO) throws Exception {
        Boolean rsp = trainPersonService.settingScore(scoreDTO);
        return new ResponseDTO<>(rsp);
    }





    /**
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取指定人已经通过培训的培训列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【参培人员】指定人员{{#userCode}}】的培训列表了数据", type = "TrainPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/person/train/list", method = RequestMethod.POST)
    public ResponseDTO<List<PersonTrainVO>> personTrainList(@RequestParam("userCode") String userCode) throws Exception {
//        List<PersonTrainVO> rsp =  trainInfoService.personTrainList(userCode);
        List<PersonTrainVO> rsp =  trainPersonService.getPersonTrainList(userCode,null);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("批量导出（Excel）")
    @PostMapping(value = "/export/excel/{centerId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【参培人员】数据", type = "TrainPerson", subType = "导出数据", bizNo = "")
    public void exportByExcel(@PathVariable("centerId") String centerId,@RequestBody List<List<SearchCondition>> pageRequest, HttpServletResponse response) throws Exception {
        trainPersonService.exportByExcel(pageRequest, response,centerId);
    }

    @Autowired
    private RepairTrainingXxlJob xxlJob;

    @GetMapping(value = "/test/xxjob")
    public void testXxjob() throws Exception {
//        xxlJob.repairTrainingNoticeJob();
        xxlJob.repairTrainingNoticeJobCheaked();
    }

}
