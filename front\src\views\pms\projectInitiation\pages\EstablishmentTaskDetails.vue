<script setup lang="ts">
import {
  BasicTableAction, isPower, Layout3, Layout3Content,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps } from 'lyra-workflow-component-vue3';
import {
  computed, ComputedRef, onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import Process from '/@/views/pms/components/Process.vue';
import { Spin } from 'ant-design-vue';
import EstablishInfo from './modal/EstablishInfo.vue';
import EstablishLog from './modal/EstablishLog.vue';
import { useUserStore } from '/@/store/modules/user';
import {
  completeTaskRow,
  delegateRowTask,
  expediteTaskRow,
  revocationChange,
  sendBackRow,
  startExecution,
  verifyTaskRow,
} from '../index';

const processViewRef = ref();
const route = useRoute();
const detailsData: Ref = ref();
const powerData: Ref = ref();
const userInfo = useUserStore().getUserInfo;
provide('powerData', computed(() => powerData));
provide('formData', detailsData);
const actionId: Ref<string> = ref('');
const workflowActionRef: Ref = ref();
const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: detailsData.value,
  afterEvent() {
    getDetailData();
    processViewRef.value?.init();
  },
}));
const loading: Ref<boolean> = ref(false);

const menuData: Ref<any[]> = ref([]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
function getDetailData() {
  loading.value = true;
  new Api('/pms').fetch('', `collaborativeCompilationTask/${route.params.id}`, 'GET').then((res:any) => {
    res.projectCode = res.number;
    res.ownerName = res.creatorName || res.modifyName;
    detailsData.value = res;
    actionId.value = 'basicInfo';
    loading.value = false;
  });
}

function menuChange({ id }) {
  actionId.value = id;
  // console.log(actionId.value);
}

// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}
const actionsBtn = computed(() => [
  {
    text: '催办',
    isShow: (record) => isCreator() && isPower('PMS_XTBZ_container_03_button_01', powerData),
    onClick: (record) => {
      expediteTaskRow(record, getDetailData);
    },
  },
  {
    text: '撤回',
    isShow: (record) => isCreator() && isPower('PMS_XTBZ_container_03_button_02', powerData),
    onClick: (record) => {
      // //actionClick('revocation', record);
      revocationChange(record, [], getDetailData);
    },
  },
  {
    text: '退回',
    isShow: (record) => isRespUser() && isPower('PMS_XTBZ_container_03_button_03', powerData),
    onClick: (record) => {
      sendBackRow(record, getDetailData);
    },
  },
  {
    text: '转办',
    isShow: (record) => isRespUser() && isPower('PMS_XTBZ_container_03_button_04', powerData),
    onClick: (record) => {
      delegateRowTask(record, getDetailData);
    },
  },
  {
    text: '开始执行',
    // 130 已下发 && 任务责任人
    isShow: (record) => isRespUser() && isPower('PMS_XTBZ_container_03_button_05', powerData),
    onClick: (record) => {
      startExecution(record, getDetailData);
    },
  },
  {
    text: '执行完成',
    isShow: (record) => isRespUser() && isPower('PMS_XTBZ_container_03_button_06', powerData),
    onClick: (record) => {
      completeTaskRow(record, getDetailData);
    },
  },
  {
    text: '完成确认',
    isShow: (record) => isCreator() && isPower('PMS_XTBZ_container_03_button_07', powerData),
    onClick: (record) => {
      verifyTaskRow(record, getDetailData);
    },
  },
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: workflowActionRef.value?.isAdd && isPower('PMS_XTBZ_container_03_button_08', powerData),
    onClick(record: any) {
      handleAddWorkflow();
    },
  },
]);
function isCreator() {
  return [detailsData.value.creator, detailsData.value.issuedUser].includes(userInfo.id);
}
function isRespUser() {
  return detailsData.value.rspUser === userInfo.id;
}

function getPowerDataHandle(data) {
  powerData.value = data;
  menuData.value = [
    {
      id: 'basicInfo',
      name: '任务详情',
      isShow: isPower('PMS_XTBZ_container_01', powerData),
    },
    // {
    //   id: 'basicLog',
    //   name: '日志',
    //   isShow: true,
    // },
    {
      id: 'basicProcess',
      name: '流程',
      isShow: isPower('PMS_XTBZ_container_02', powerData),
    },
  ].filter((item) => typeof item?.isShow === 'undefined' || item?.isShow);
}
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'PMS8028',getPowerDataHandle}"
    :defaultActionId="actionId"
    :projectData="detailsData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        :record="detailsData"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="detailsData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>

    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <EstablishInfo v-if="actionId==='basicInfo'" />
      <EstablishLog v-if="actionId==='basicLog'" />
      <Process
        v-if="actionId==='basicProcess'"
        ref="processViewRef"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">

</style>
