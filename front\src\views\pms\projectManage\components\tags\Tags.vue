<template>
  <div class="tags">
    <template
      v-for="item in list"
      :key="item.label"
    >
      <span
        v-if="!item.hidden && item.label"
        :class="['tag',item.color]"
      >
        {{ item.label }}
      </span>
    </template>
  </div>
</template>

<script setup lang="ts">

import { computed } from 'vue';

interface tagType {
    color: string,
    label: string,
    hidden?:boolean
}
const colorType = [
  'success',
  'error',
  'info',
  'warning',
];
const props = defineProps({
  data: {
    type: Array<tagType>,
    default: () => [],
  },
});

const list = computed(() => props.data.map((item) => ({
  ...item,
  color: colorType.includes(item.color) ? item.color : '',
})));

</script>

<style scoped lang="less">
.tags{
    display: flex;
    align-items: center;
    .tag{
        height: 22px;
        line-height: 20px;
        padding: 0 5px;
        border-radius: 4px;
        border: 1px solid ~`getPrefixVar('border-color-base')`;
        margin-right: ~`getPrefixVar('button-margin')`;

        &.warning{
            color: ~`getPrefixVar('warning-color')`;
            background-color: ~`getPrefixVar('warning-color-deprecated-bg')`;
            border-color: ~`getPrefixVar('warning-color-deprecated-border')`;
        }

        &.error{
            color: ~`getPrefixVar('error-color')`;
            background-color: ~`getPrefixVar('error-color-deprecated-bg')`;
            border-color: ~`getPrefixVar('error-color-deprecated-border')`;
        }
        &.info{
            color: ~`getPrefixVar('info-color')`;
            background-color: ~`getPrefixVar('info-color-deprecated-bg')`;
            border-color: ~`getPrefixVar('info-color-deprecated-border')`;
        }

        &.success{
            color: ~`getPrefixVar('success-color')`;
            background-color: ~`getPrefixVar('success-color-deprecated-bg')`;
            border-color: ~`getPrefixVar('success-color-deprecated-border')`;
        }
    }
}
</style>
