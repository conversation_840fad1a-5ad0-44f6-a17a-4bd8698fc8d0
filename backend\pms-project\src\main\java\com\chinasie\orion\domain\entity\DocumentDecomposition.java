package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * DocumentDecomposition Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
@TableName(value = "pms_document_decomposition")
@ApiModel(value = "DocumentDecompositionEntity对象", description = "文档分解")
@Data

public class DocumentDecomposition extends  ObjectEntity  implements Serializable{

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 关联任务
     */
    @ApiModelProperty(value = "关联任务")
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 条目标题
     */
    @ApiModelProperty(value = "条目标题")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 条目编制内容
     */
    @ApiModelProperty(value = "条目编制内容")
    @TableField(value = "content")
    private String content;

}
