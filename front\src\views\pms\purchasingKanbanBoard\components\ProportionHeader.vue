<template>
  <div>
    <div class="components-grid">
      <a-row class="flex-row">
        <a-col
          v-for="(card, index) in props.data"
          :key="index"
          :span="6"
          class="w25"
        >
          <a-card>
            <!-- 卡片内容 -->
            <div
              :class="card.type === 'A' ? 'sign-a' : (card.type === 'B' ? 'sign-b' : (card.type === 'C' ? 'sign-c' : (card.type === 'D' ? 'sign-d' : (card.type === 'E' ? 'sign-e' : 'sign-f'))))"
              class="grid-content bg"
            >
              <div
                :class="card.type === 'B' ? 'icon-img big-unit' : (card.type === 'F' ? 'icon-img big-unit' : '')"
              >
                <Icon
                  v-if="card.type === 'B' || card.type === 'F'"
                  :size="getSize(card.type)"
                  color="#fff"
                  icon="sie-icon-tongyongmokuaiguanli"
                />
              </div>
              <div
                :class="card.type === 'B' || card.type === 'F' ? 'mr' : ''"
                class="flex-column"
              >
                <div class="card-content">
                  <p class="p-title">
                    {{ card.title }}
                  </p>
                  <div class="price">
                    <h1>{{ card.present }}</h1>
                    <p>{{ card.priceUnit }}</p>
                  </div>
                </div>
                <div
                  :class="card.type === 'A' || card.type === 'D' ? 'bd' : 'pt'"
                  class="z-data"
                >
                  <ul>
                    <li
                      v-for="(section,idx) in card.child"
                      :key="idx"
                      :class="card.type === 'A' || card.type === 'D' ? 'fs13' : ''"
                    >
                      <span>{{ section.name }}</span>
                      <span
                        :style="{ background: section.bgColor}"
                        class="point"
                      />
                      <span>{{ section.num }}{{ card.type === 'E' ? '个': '' }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from 'lyra-component-vue3';

const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});
function getSize(card) {
  switch (card) {
    case 'B':
      return '24';
    default:
      return '28';
  }
}

</script>
<style lang="less" scoped>
.components-grid {
  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;

    .w25 {
      position: relative;
      min-width: 230px;
      width: 25%;
      height: 142px;
      border: 1px solid #e9e9e9;
      margin: 0 6px;
    }
  }
  .sign-a, .sign-c, .sign-d, .sign-e{
    .price{
      margin-top: 10px;
    }
  }
  .sign-b{
    .p-title{
      text-align: center;
    }
    .price{
      margin-top: 30px;
      padding-left: 20px;
    }
  }
  .sign-e{
    .price{
      justify-content: flex-start!important;
    }
  }
  .sign-f{
    .price{
      justify-content: flex-start!important;
      margin-top: 30px;
      padding-left: 93px;
    }
    .p-title{
      text-align: left;
      padding-left: 70px;
    }
  }
  .grid-content {
    position: relative;
    text-align: left;
    line-height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 22px 10px 0 20px;
    .icon-img {
      position: absolute;
      left: 13px;
      top: 42px;
      width: 50px;
      height: 50px;
      border-radius: 100%;
      background-color: #0a84ff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
    }
    .big-unit{
      width: 65px;
      height: 65px;
    }
    .health {
      display: flex;
    }

    .flex-column {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      flex: 1;

      .z-data {
        ul {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          padding: 0;
          margin-top: 12px;

          li {
            display: flex;
            flex-direction: row;
            align-items: center;
            list-style: none;

            span {
              display: inline-block;
              font-weight: 400;
              font-style: normal;
              font-size: 13px;
              color: rgba(0, 0, 0, 0.***************);
              line-height: 22px;
            }

            span:first-child {
              margin-right: 6px;
            }
            span:last-child{
              color: rgba(0, 0, 0, 0.42745098);
            }

            .point {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin: 3px;
            }
          }

          .fs13 {
            font-size: 13px;
          }
        }
      }

      .bd {
        margin-top: 5px;
      }

      .pt {
        margin-top: 5px;
      }
    }

    .position-img {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-b {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-c {
      position: absolute;
      left: 23px;
      bottom: 33px;
      width: 90%;
      /deep/.ant-progress-show-info .ant-progress-outer{
        padding-right: 0;
        margin-right: 0;
      }
      /deep/.ant-progress-bg{
        width:80%;
      }
      /deep/.ant-progress-outer{
        width: 75%;
      }
    }
    .position-img-d {
      position: absolute;
      right: 20px;
      bottom: 35px;
      /deep/.ant-progress-inner{
        width: 68px!important;
        height: 68px!important;
      }
    }
    .position-img-e {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-f {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }

    .card-content {
      h1, p {
        margin-bottom: 0;
      }

      h1 {
        font-size: 30px;
        font-weight: 400;
      }

      .p-title {
        font-size: 13px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.***************);
      }

      .price {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: baseline;

        p {
          margin-left: 10px;
          font-size: 16px;
        }
      }
    }
  }
}

@media screen and (max-width: 1650px) {
  .components-grid {
    .flex-row {
      flex-wrap: wrap;
      >.w25 {
        width: calc(33.33% - 12px)!important;

        &:nth-child(3) ~ .w25  {
          margin-top: 12px;
        }
      }
    }

  }
}

</style>
