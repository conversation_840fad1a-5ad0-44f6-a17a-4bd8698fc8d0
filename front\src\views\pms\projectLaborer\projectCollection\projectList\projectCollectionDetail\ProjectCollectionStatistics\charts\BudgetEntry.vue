<script setup lang="ts">
import {
  computed, ComputedRef, inject, onMounted, ref, Ref, watch, watchEffect,
} from 'vue';
import { DatePicker, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useChart } from './useChart';
import Api from '/@/api';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import SpinView from '/@/views/pms/components/SpinView.vue';

const projectId: string = inject('projectId');
const type: Ref<string> = inject('type');
const id: Ref<string> = ref();
const loading:Ref<boolean> = ref(false);
const year: Ref<string> = ref(dayjs().format('YYYY'));
const budgetInfo: Ref<Record<string, any>> = ref({});
const options: Ref<any[]> = ref([]);
const list:ComputedRef<any[]> = computed(() => budgetInfo.value.projectBudgetTotalVOS || []);
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
  },
  color: ['#3CB6E3'],
  grid: {
    top: 10,
    left: 20,
    right: 20,
    bottom: 20,
    containLabel: true,
  },
  dataZoom: [
    {
      show: list.value.length > 5,
      height: 0,
      moveHandleSize: 10,
      showDetail: false,
      maxValueSpan: 4,
      bottom: 10,
    },
  ],
  xAxis: {
    type: 'category',
    data: list.value.map((item) => item.budgetName),
    axisTick: {
      show: false,
    },
    axisLabel: {
      interval: 0,
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
      },
    },
  },
  series: [
    {
      barWidth: 20,
      data: list.value.map((item) => item.budgetMoney),
      type: 'bar',
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watchEffect(() => {
  if (id.value && year.value) {
    getBudgetInfo();
  } else {
    loading.value = false;
  }
});

watch(() => chartOption.value, (value) => {
  if (list.value.length) {
    chartInstance.value.setOption(value);
  }
  chartInstance.value.hideLoading();
  loading.value = false;
}, { deep: true });

onMounted(() => {
  getOptions();
});

// 获取预算信息
async function getBudgetInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectCollectionStatistics/projectBudgetTotal').fetch({
      projectId,
      type: type.value,
      id: id.value,
      year: year.value,
    }, '', 'GET');
    budgetInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}

// 获取费用科目
async function getOptions() {
  loading.value = true;
  try {
    const result = await new Api('/pms/expenseSubject/getFirstFloor').fetch('', '', 'GET');
    options.value = result?.map((item) => ({
      label: item.name,
      value: item.id,
    })) ?? [];
    id.value = result?.[0]?.id ?? '';
  } finally {
  }
}
</script>

<template>
  <div class="select-wrap">
    <Select
      v-model:value="id"
      style="width: 120px"
      :options="options"
    />
    <DatePicker
      v-model:value="year"
      class="ml15"
      style="width: 120px"
      picker="year"
      valueFormat="YYYY"
      :allow-clear="false"
    />
  </div>
  <spin-view
    v-show="loading"
    class="chart"
  />
  <div
    v-show="!loading && list.length"
    ref="chartRef"
    class="chart"
  />
  <empty-view
    v-show="!loading && list.length===0"
    class="chart"
  />
</template>

<style scoped lang="less">
.select-wrap {
  position: absolute;
  top: 10px;
  right: 245px;
}

.chart {
  margin-top: 20px;
  width: 100%;
  height: 232px;
}
</style>
