<script setup lang="ts">
import { BasicButton, openSelectUserModal, OrionTable } from 'lyra-component-vue3';
import {
  computed, inject, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { usePersonInOut } from '/@/views/pms/majorRepairsSecond/hooks/usePersonInOut';

const router = useRouter();
const planDetailsData: Ref<Record<string, any>> = inject('formData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  maxHeight: 300,
  showToolButton: false,
  smallSearchField: ['userCode', 'userName'],
  api: (params: object) => new Api('/pms/schemeToPerson').fetch({
    ...params,
    query: {
      planSchemeId: planDetailsData.value?.id,
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '员工号',
      dataIndex: 'userCode',
    },
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      modalTitle: '移除提示！',
      modalContent: '是否确认移除该数据？',
      modal: (record) => deleteApi([record.id], [record.personId]),
    },
  ],
};

const toolButtons = computed(() => [
  {
    label: '添加人员',
    event: 'add',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },
  {
    label: '移除',
    event: 'remove',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
  },
]);

const { addApi } = usePersonInOut();

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      addApi({
        baseCode: planDetailsData.value?.enforceBasePlace,
        planSchemeId: planDetailsData.value?.id,
        repairRound: planDetailsData.value?.repairRound,
        isHaveProject: true,
      }, updateTable);
      break;
    case 'remove':
      Modal.confirm({
        title: '移除提示！',
        content: '确认移除已选择的数据？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id), selectedRows.value.map((item) => item.personId)),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[], personIdList: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/schemeToPerson/remove/batch/new').fetch({
      ids,
      personIdList,
    }, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-bind="item"
          @click="handleToolButton(item.event)"
        >
          {{ item.label }}
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
