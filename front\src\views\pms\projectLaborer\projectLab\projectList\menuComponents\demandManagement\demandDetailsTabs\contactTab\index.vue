<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <RiskContactDoc
      v-if="contentTabs[contentTabsIndex]?.name === '关联文档' && isPower('XQ_container_03_02', powerData)"
      :id="id"
    />
    <ContactPlan
      v-if="contentTabs[contentTabsIndex]?.name === '关联计划' && isPower('XQ_container_03_01', powerData)"
      :id="id"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, inject,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, Layout2Content, isPower,
} from 'lyra-component-vue3';
import RiskContactDoc from './ContactDoc/index.vue';
import ContactPlan from './contactPlan/index.vue';
// import { Layout2Content } from '/@/components/Layout2.0';

export default defineComponent({
  // name: 'ProjectSet',
  components: {
    RiskContactDoc,
    ContactPlan,
    Layout2Content,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup() {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      // [{ name: '关联计划' }, { name: '关联文档' }]
      contentTabs: [],
    });
    onMounted(() => {
      isPower('XQ_container_03_01', state.powerData) && state6.contentTabs.push({ name: '关联计划' });
      isPower('XQ_container_03_02', state.powerData) && state6.contentTabs.push({ name: '关联文档' });
    });
    const initForm = (data) => {
      state.className = data.className;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      isPower,
    };
  },
});
</script>

<style scoped></style>
