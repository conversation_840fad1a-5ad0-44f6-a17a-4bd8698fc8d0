package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.ProjectContractCloseApplyDTO;
import com.chinasie.orion.domain.vo.ProjectContractCloseApplyVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectContractCloseApplyService;
import com.chinasie.orion.util.CollectionUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProjectContractCloseApply 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
@RestController
@RequestMapping("/projectContractCloseApply")
@Api(tags = "项目合同关闭申请")
public class ProjectContractCloseApplyController {

    @Autowired
    private ProjectContractCloseApplyService projectContractCloseApplyService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看项目合同关闭申请详情，业务编号：{#id}",
            type = "ProjectContractCloseApply",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ProjectContractCloseApplyVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectContractCloseApplyVO rsp = projectContractCloseApplyService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectContractCloseApplyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增项目合同关闭申请",
            type = "ProjectContractCloseApply",
            subType = "新增",
            bizNo = ""
    )
    public ResponseDTO<ProjectContractCloseApplyVO> create(@RequestBody @Validated ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception {
        ProjectContractCloseApplyVO rsp = projectContractCloseApplyService.create(projectContractCloseApplyDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectContractCloseApplyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑项目合同关闭申请，业务编号：{#projectContractCloseApplyDTO.id}",
            type = "ProjectContractCloseApply",
            subType = "编辑",
            bizNo = "{#projectContractCloseApplyDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception {
        if (!StringUtils.hasText(projectContractCloseApplyDTO.getId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectContractCloseApplyService.edit(projectContractCloseApplyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除项目合同关闭申请，业务编号：{ID_LIST{#ids}}",
            type = "ProjectContractCloseApply",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectContractCloseApplyService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行项目合同关闭申请分页查询",
            type = "ProjectContractCloseApply",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ProjectContractCloseApplyVO>> pages(@RequestBody Page<ProjectContractCloseApplyDTO> pageRequest) throws Exception {
        Page<ProjectContractCloseApplyVO> rsp = projectContractCloseApplyService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号获取变更列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号获取关闭列表")
    @RequestMapping(value = "/list/{contractId}", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】根据合同编号{#contractId}获取关闭列表",
            type = "ProjectContractCloseApply",
            subType = "根据合同获取列表",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<List<ProjectContractCloseApplyVO>> listByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ProjectContractCloseApplyVO> rsp = projectContractCloseApplyService.listByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }
}
