<template>
  <BasicTransfer
    v-bind="$attrs"
    ref="basicTransferRef"
    :module-title="moduleTitle"
  />
</template>
<script lang="ts">
import { defineComponent, ref, watchEffect } from 'vue';
import {
  BasicTransfer, useModal, BasicModal,
} from 'lyra-component-vue3';
// import BasicTransfer from '/@/components/BasicTransfer';
// import { BasicModal, useModal } from '/@/components/Modal';
import { message } from 'ant-design-vue';
export default defineComponent({
  name: 'Transfer',
  components: {
    BasicTransfer,
    // BasicModal,
  },
  props: {
    moduleTitle: {
      type: String,
      default: '',
    },
    setApi: {
      type: Function,
      default: null,
    },
    setParams: {
      type: Object,
      default: () => {},
    },
    setMethod: {
      type: String,
      default: 'POST',
    },
    confirmLoading: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const basicTransferRef: any = ref(null);
    const [register, methods] = useModal();
    const confirmLoadingNow = ref(props.confirmLoadingNow);

    watchEffect(() => {
      confirmLoadingNow.value = props.confirmLoading;
    });

    return {
      visible,
      register,
      ...methods,
      basicTransferRef,
      confirmLoadingNow,
      async getSelectData() {
        return basicTransferRef.value.getData();
      },
      visibleChange(visibleStatus) {
        visible.value = visibleStatus;
      },
    };
  },
});
</script>

<style scoped></style>
