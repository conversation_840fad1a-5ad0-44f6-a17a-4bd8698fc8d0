package com.chinasie.orion.service.impl.approval;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.constant.ProjectApprovalEstimateEnum;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateExpenseSubject;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateExpenseSubject;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateExpenseSubjectVO;
import com.chinasie.orion.domain.vo.approval.SubjectSummaryVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateExpenseSubjectMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.ProjectApprovalService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateExpenseSubjectService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateExpenseSubjectService;
import com.chinasie.orion.util.AviatorUtils;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import java.lang.String;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;




/**
 * <p>
 * ProjectApprovalEstimateExpenseSubject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06 10:05:25
 */
@Service
@Slf4j
public class ProjectApprovalEstimateExpenseSubjectServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateExpenseSubjectMapper, ProjectApprovalEstimateExpenseSubject> implements ProjectApprovalEstimateExpenseSubjectService {



    @Autowired
    private ProjectApprovalEstimateTemplateExpenseSubjectService projectApprovalEstimateTemplateExpenseSubjectService;

    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private AviatorUtils aviatorUtils;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private ProjectProperties projectProperties;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private ProjectApprovalEstimateService projectApprovalEstimateService;

    @Override
    public Boolean editAmountBatch(List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        if (CollectionUtil.isNotEmpty(list)) {
            List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList = new ArrayList<>();
            getListByRecursion(list, projectApprovalEstimateExpenseSubjectList);
            this.updateBatchById(projectApprovalEstimateExpenseSubjectList);
        }
        return true;
    }


    public void getListByRecursion(List<ProjectApprovalEstimateExpenseSubjectDTO> list,
                                   List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList) {
        for (ProjectApprovalEstimateExpenseSubjectDTO entity : list) {
            ProjectApprovalEstimateExpenseSubject projectApprovalEstimateExpenseSubject = new ProjectApprovalEstimateExpenseSubject();
            projectApprovalEstimateExpenseSubject.setId(entity.getId());
            projectApprovalEstimateExpenseSubject.setAmount(entity.getAmount());
            projectApprovalEstimateExpenseSubjectList.add(projectApprovalEstimateExpenseSubject);
            if (CollectionUtil.isNotEmpty(entity.getChildren())) {
                getListByRecursion(entity.getChildren(), projectApprovalEstimateExpenseSubjectList);
            }
        }
    }

    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectApprovalEstimateExpenseSubject approvalEstimateExpenseSubject = this.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .select(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId)
                .in(ProjectApprovalEstimateExpenseSubject::getId, ids).last("limit 1"));
        if (ObjectUtil.isEmpty(approvalEstimateExpenseSubject)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String projectApprovalId = approvalEstimateExpenseSubject.getProjectApprovalId();
        List<ProjectApprovalEstimateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .select(ProjectApprovalEstimateExpenseSubject::getNumber, ProjectApprovalEstimateExpenseSubject::getParentId, ProjectApprovalEstimateExpenseSubject::getId, ProjectApprovalEstimateExpenseSubject::getFormula)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, projectApprovalId));
        if (CollectionUtil.isEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        Map<String, List<String>> parentMap = list.stream().collect(Collectors.groupingBy(ProjectApprovalEstimateExpenseSubject::getParentId, Collectors.mapping(ProjectApprovalEstimateExpenseSubject::getId, Collectors.toList())));
        List<String> deleteIdList = new ArrayList<>();
        getChildIdList(parentMap, deleteIdList, ids);
        List<String> deleteNumberList = new ArrayList<>();
        List<String> unDeleteFormulaList = new ArrayList<>();
        for (ProjectApprovalEstimateExpenseSubject projectApprovalEstimateExpenseSubject : list) {
            if (deleteIdList.contains(projectApprovalEstimateExpenseSubject.getId())) {
                deleteNumberList.add(projectApprovalEstimateExpenseSubject.getNumber());
            } else {
                unDeleteFormulaList.add(projectApprovalEstimateExpenseSubject.getFormula());
            }
        }
        if (unDeleteFormulaList.stream().anyMatch(a -> deleteNumberList.stream().anyMatch(a::contains))) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "存在科目的公式与要删除的科目有关联，删除失败！");
        }
        this.removeBatchByIds(deleteIdList);
        return true;
    }

    /**
     * 获取自身及子级ids
     * @param parentMap
     * @param childIdList
     * @param parentIdList
     */
    private void getChildIdList(Map<String, List<String>> parentMap,
                                List<String> childIdList,
                                List<String> parentIdList) {
        for (String parentId : parentIdList) {
            childIdList.add(parentId);
            if (parentMap.containsKey(parentId)) {
                getChildIdList(parentMap, childIdList, parentMap.get(parentId));
            }
        }
    }

    /**
     * 引入模板
     * @param approvalId
     * @param estimateTemplateId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean addTemplate(String approvalId, String estimateTemplateId) throws Exception {
        ProjectApproval byId = projectApprovalService.getById(approvalId);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "立项论证数据不存在或者已经被删除！");
        }
        //先将该立项里所有科目删除
        this.remove(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, approvalId));
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = projectApprovalEstimateTemplateExpenseSubjectService.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplateExpenseSubject.class)
                .eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId, estimateTemplateId));
        if (CollectionUtil.isNotEmpty(list)) {
            List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList = new ArrayList<>();
            saveDataByRecursion(TreeUtils.tree(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateExpenseSubjectDTO::new)),
                    approvalId, "0", projectApprovalEstimateExpenseSubjectList);
            this.saveBatch(projectApprovalEstimateExpenseSubjectList);
        }

        return true;
    }

    @Override
    public Boolean addExpenseSubject(List<ProjectApprovalEstimateExpenseSubjectDTO> projectApprovalEstimateExpenseSubjectDTOList, String projectApprovalId) throws Exception {
        if (CollectionUtil.isEmpty(projectApprovalEstimateExpenseSubjectDTOList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "未选择科目");
        }
        //校验概算科目编码是否已存在
        List<String> numberList = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .select(ProjectApprovalEstimateExpenseSubject::getNumber)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, projectApprovalId))
                .stream().map(ProjectApprovalEstimateExpenseSubject::getNumber).collect(Collectors.toList());
        checkEstimateSubjectNumber(projectApprovalEstimateExpenseSubjectDTOList, numberList);

        List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList = new ArrayList<>();
        saveDataByRecursion(TreeUtils.tree(projectApprovalEstimateExpenseSubjectDTOList), projectApprovalId,
                "0", projectApprovalEstimateExpenseSubjectList);
        this.saveBatch(projectApprovalEstimateExpenseSubjectList);

        return true;
    }

    private void checkEstimateSubjectNumber(List<ProjectApprovalEstimateExpenseSubjectDTO> projectApprovalEstimateExpenseSubjectDTOList,
                    List<String> numberList) {
        for (ProjectApprovalEstimateExpenseSubjectDTO projectApprovalEstimateExpenseSubjectDTO : projectApprovalEstimateExpenseSubjectDTOList) {
            if (numberList.contains(projectApprovalEstimateExpenseSubjectDTO.getNumber())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "添加科目失败：科目编码已存在");
            }
        }
    }

    /**
     * 递归子级新增
     *
     * @param projectApprovalEstimateExpenseSubjectDTOList
     * @param projectApprovalId
     * @param parentId
     * @throws Exception
     */
    private void saveDataByRecursion(List<ProjectApprovalEstimateExpenseSubjectDTO> projectApprovalEstimateExpenseSubjectDTOList,
                                     String projectApprovalId,
                                     String parentId,
                                     List<ProjectApprovalEstimateExpenseSubject> projectApprovalEstimateExpenseSubjectList) throws Exception {
        for (ProjectApprovalEstimateExpenseSubjectDTO projectApprovalEstimateExpenseSubjectDTO : projectApprovalEstimateExpenseSubjectDTOList) {
            ProjectApprovalEstimateExpenseSubject projectApprovalEstimateExpenseSubject = BeanCopyUtils.convertTo(projectApprovalEstimateExpenseSubjectDTO, ProjectApprovalEstimateExpenseSubject::new);
            projectApprovalEstimateExpenseSubject.setProjectApprovalId(projectApprovalId);
            projectApprovalEstimateExpenseSubject.setParentId(parentId);
            projectApprovalEstimateExpenseSubject.setId(classRedisHelper.getUUID(ProjectApprovalEstimateExpenseSubject.class.getSimpleName()));
            projectApprovalEstimateExpenseSubjectList.add(projectApprovalEstimateExpenseSubject);
            if (CollectionUtil.isNotEmpty(projectApprovalEstimateExpenseSubjectDTO.getChildren())) {
                saveDataByRecursion(projectApprovalEstimateExpenseSubjectDTO.getChildren(), projectApprovalId,
                        projectApprovalEstimateExpenseSubject.getId(), projectApprovalEstimateExpenseSubjectList);
            }
        }
    }

    @Override
    public List<ProjectApprovalEstimateExpenseSubjectVO> getExpenseSubjectList(String approvalId) throws Exception {
        List<ProjectApprovalEstimateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, approvalId)
                .orderByDesc(ProjectApprovalEstimateExpenseSubject::getCreateTime));
        List<ProjectApprovalEstimateExpenseSubjectVO> tree = TreeUtils.tree(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateExpenseSubjectVO::new));
        return tree;
    }

    @Override
    public BigDecimal calculate(String id, List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        if (CollectionUtil.isEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectApprovalEstimateExpenseSubject byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        if (StrUtil.isBlank(byId.getFormula())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该科目没有公式，不能进行计算");
        }
        Map<String, List<ProjectApprovalEstimateExpenseSubjectDTO>> parentMap = list.stream()
                .filter(f -> StrUtil.isNotBlank(f.getParentId()))
                .collect(Collectors.groupingBy(ProjectApprovalEstimateExpenseSubjectDTO::getParentId));
        Map<String, Object> variableMap = getVariableMap(list, parentMap, byId.getProjectApprovalId() );
        return aviatorUtils.calculateFormula(variableMap, byId.getFormula());
    }

    /**
     *
     * 获取每个科目的值或公式
     * @param list
     * @param parentMap
     * @return
     */
    private Map<String, Object> getVariableMap(List<ProjectApprovalEstimateExpenseSubjectDTO> list,
                                               Map<String, List<ProjectApprovalEstimateExpenseSubjectDTO>> parentMap,
                                               String projectApprovalId) throws Exception{
        Map<String, Object> variableMap = new HashMap<>();
        for (ProjectApprovalEstimateExpenseSubjectDTO entity : list) {
            if (parentMap.containsKey(entity.getId())) {
                variableMap.put(entity.getNumber(), parentMap.get(entity.getId())
                        .stream().map(ProjectApprovalEstimateExpenseSubjectDTO::getNumber).collect(Collectors.joining("+")));
            } else if (ObjectUtil.isNotEmpty(entity.getAmount())) {
                variableMap.put(entity.getNumber(), entity.getAmount());
            } else if (StrUtil.isNotBlank(entity.getFormula())) {
                variableMap.put(entity.getNumber(), entity.getFormula());
            } else {
                variableMap.put(entity.getNumber(), null);
            }
        }
        ProjectApprovalEstimate entityByProjectApprovalId = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isNotEmpty(entityByProjectApprovalId) && ObjectUtil.isNotEmpty(entityByProjectApprovalId.getPeopleNum())) {
            variableMap.put(ProjectApprovalEstimateEnum.PEOPLE_NUM.getKey(), entityByProjectApprovalId.getPeopleNum());
        }

        return variableMap;
    }

    @Override
    public List<ProjectApprovalEstimateExpenseSubjectVO> calculateBatch(String approvalId, List<ProjectApprovalEstimateExpenseSubjectDTO> list) throws Exception {
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<String, List<ProjectApprovalEstimateExpenseSubjectDTO>> parentMap = list.stream()
                .filter(f -> StrUtil.isNotBlank(f.getParentId()))
                .collect(Collectors.groupingBy(ProjectApprovalEstimateExpenseSubjectDTO::getParentId));
        Map<String, Object> variableMap = getVariableMap(list, parentMap, approvalId);
        List<ProjectApprovalEstimateExpenseSubjectVO> treeList = new ArrayList<>();
        for (ProjectApprovalEstimateExpenseSubjectDTO projectApprovalEstimateExpenseSubject : list) {
            ProjectApprovalEstimateExpenseSubjectVO entity = BeanCopyUtils.convertTo(projectApprovalEstimateExpenseSubject, ProjectApprovalEstimateExpenseSubjectVO::new);
            if (!parentMap.containsKey(entity.getId()) && StrUtil.isNotBlank(entity.getFormula())) {
                try {
                    entity.setCalculateAmount(aviatorUtils.calculateFormula(variableMap, entity.getFormula()));
                } catch (Exception e) {
                    log.error("项目立项概算科目【{}】计算报错", entity.getNumber(), e);
                }
            }
            treeList.add(entity);
        }
        return TreeUtils.tree(treeList);
    }

    @Override
    public SubjectSummaryVO subjectSummary(String approvalId) throws Exception {
        List<ProjectApprovalEstimateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, approvalId));
        SubjectSummaryVO subjectSummaryVO = new SubjectSummaryVO();
        if (CollectionUtil.isEmpty(list)) {
            return subjectSummaryVO;
        }
        BigDecimal allAmount = list.stream().filter(f -> "0".equals(f.getParentId()) && ObjectUtil.isNotEmpty(f.getAmount()))
                .map(ProjectApprovalEstimateExpenseSubject::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        subjectSummaryVO.setAllAmount(allAmount);
        if (allAmount.compareTo(BigDecimal.ZERO) != 0) {
            DecimalFormat df = new DecimalFormat("0.00%");
            Map<String, BigDecimal> amountByNumberMap = list.stream().filter(f -> ObjectUtil.isNotEmpty(f.getAmount()))
                    .collect(Collectors.toMap(ProjectApprovalEstimateExpenseSubject::getNumber, ProjectApprovalEstimateExpenseSubject::getAmount));
            //材料费
            DictValueVO materialFeeInfo = dictRedisHelper.getDictValueInfoByCode(projectProperties.getDictValueMaterialFee());
            if (ObjectUtil.isNotEmpty(materialFeeInfo) && StrUtil.isNotBlank(materialFeeInfo.getValue())) {
                BigDecimal materialFee = Arrays.stream(materialFeeInfo.getValue().split(",")).map(amountByNumberMap::get)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                subjectSummaryVO.setMaterialFee(materialFee);
                subjectSummaryVO.setMaterialFeeRate(df.format(materialFee.divide(allAmount, 4, RoundingMode.HALF_UP)));
            }
            //专用费
            DictValueVO dedicatedFeeInfo = dictRedisHelper.getDictValueInfoByCode(projectProperties.getDictValueDedicatedFee());
            if (ObjectUtil.isNotEmpty(dedicatedFeeInfo) && StrUtil.isNotBlank(dedicatedFeeInfo.getValue())) {
                BigDecimal dedicatedFee = Arrays.stream(dedicatedFeeInfo.getValue().split(",")).map(amountByNumberMap::get)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                subjectSummaryVO.setDedicatedFee(dedicatedFee);
                subjectSummaryVO.setDedicatedFeeRate(df.format(dedicatedFee.divide(allAmount, 4, RoundingMode.HALF_UP)));
            }
            //间接费
            DictValueVO indirectFeeInfo = dictRedisHelper.getDictValueInfoByCode(projectProperties.getDictValueIndirectFee());
            if (ObjectUtil.isNotEmpty(indirectFeeInfo) && StrUtil.isNotBlank(indirectFeeInfo.getValue())) {
                Map<String, String> nameByNumberMap = list.stream()
                        .collect(Collectors.toMap(ProjectApprovalEstimateExpenseSubject::getNumber, ProjectApprovalEstimateExpenseSubject::getName));
                String[] indirectFees = indirectFeeInfo.getValue().split(",");
                Map<String, String> indirectFeeRateMap = new HashMap<>();
                for (String indirect : indirectFees) {
                    if (nameByNumberMap.containsKey(indirect)) {
                        indirectFeeRateMap.put(nameByNumberMap.get(indirect),
                                df.format(amountByNumberMap.getOrDefault(indirect, BigDecimal.ZERO).divide(allAmount, 4, RoundingMode.HALF_UP)));
                    }
                }
                BigDecimal indirectFee = Arrays.stream(indirectFeeInfo.getValue().split(",")).map(amountByNumberMap::get)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
                subjectSummaryVO.setIndirectFee(indirectFee);
                subjectSummaryVO.setIndirectFeeRateMap(indirectFeeRateMap);
                subjectSummaryVO.setIndirectFeeRate(df.format(indirectFee.divide(allAmount, 4, RoundingMode.HALF_UP)));
                subjectSummaryVO.setDirectFeeRate(df.format(BigDecimal.ONE.subtract(indirectFee.divide(allAmount, 4, RoundingMode.HALF_UP))));
            }
        }
        //工资及人天
        ProjectApprovalEstimate projectApprovalEstimate = projectApprovalEstimateService.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimate.class)
                .eq(ProjectApprovalEstimate::getProjectApprovalId, approvalId));
        if (ObjectUtil.isNotEmpty(projectApprovalEstimate)) {
            subjectSummaryVO.setLaborFee(projectApprovalEstimate.getLaborFee());
            subjectSummaryVO.setPeopleNum(projectApprovalEstimate.getPeopleNum());
        }

        return subjectSummaryVO;
    }

    @Override
    public List<ProjectApprovalEstimateExpenseSubjectVO> getExpenseSubjectListByApprovalId(String approvalId) throws Exception {
        List<ProjectApprovalEstimateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, approvalId)
                .orderByDesc(ProjectApprovalEstimateExpenseSubject::getCreateTime));
        List<ProjectApprovalEstimateExpenseSubjectVO> tree = BeanCopyUtils.convertListTo(list,ProjectApprovalEstimateExpenseSubjectVO::new);
        return tree;
    }
}
