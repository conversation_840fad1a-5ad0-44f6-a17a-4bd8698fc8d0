package com.chinasie.orion.service.impl.search;


import com.chinasie.orion.domain.entity.ProjectFundsReceived;
import com.chinasie.orion.domain.entity.ProjectReceivable;
import com.chinasie.orion.repository.ProjectFundsReceivedMapper;
import com.chinasie.orion.repository.ProjectReceivableMapper;
import com.chinasie.orion.search.client.ClientDataProvider;
import com.chinasie.orion.search.common.domain.IndexData;
import com.chinasie.orion.search.common.domain.IndexDataCategory;
import com.chinasie.orion.search.common.domain.IndexDataDetail;
import com.chinasie.orion.search.common.domain.IndexDataType;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 问题 索引数据提供者.
 *
 * <AUTHOR>
 */
@Component
public class ProjectFundsReceivedIndexDataProviderImpl implements ClientDataProvider {

    @Autowired
    private ProjectFundsReceivedMapper projectFundsReceivedMapper;

    @Override
    public String library() {
        return "PMS";
    }

    @Override
    public String module() {
        return "ProjectFundsReceived_DATA";
    }

    /**
     * 获取上次索引以来新增的数据.
     *
     * @param lastIndexTime 上次数据索引时间
     * @param limitSize     返回数据量大小
     * @return
     */
    @Override
    public List<IndexData> fetchIndexData(Date lastIndexTime, Integer limitSize) {
        return projectFundsReceivedMapper.fetchIndexData(lastIndexTime, limitSize);
    }

    /**
     * 获取被索引数据的详情.
     *
     * @param dataId 数据Id
     * @return
     */
    @Override
    public IndexDataDetail findDetailById(String dataId) {
        ProjectFundsReceived detail =  projectFundsReceivedMapper.selectById(dataId);
        if(detail == null){
            return null;
        }
        IndexDataDetail result = BeanCopyUtils.convertTo(detail, IndexDataDetail::new);
        result.setDeptId("");
        result.setTitle("");
        result.setContent(detail.getRemark());
        // 设置删除标志
        result.setDeleted(Objects.equals(detail.getLogicStatus(), -1));
        result.getProps().put("status", detail.getStatus());
        return result;
    }

    /**
     * 获取被索引数据的类别信息.
     *
     * @param categoryIds
     * @return
     */
    @Override
    public List<IndexDataCategory> fetchCategories(List<String> categoryIds) {
        return null;
    }

    /**
     * 获取别索引数据的类型信息.
     *
     * @param dataTypeIds
     * @return
     */
    @Override
    public List<IndexDataType> fetchDataTypes(List<String> dataTypeIds) {
        return null;
    }
}
