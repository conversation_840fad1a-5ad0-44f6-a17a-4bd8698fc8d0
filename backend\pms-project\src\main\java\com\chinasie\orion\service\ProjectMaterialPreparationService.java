package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import java.util.Map;


import com.chinasie.orion.domain.dto.ProjectMaterialPlanPreparationDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPreparationDTO;
import com.chinasie.orion.domain.entity.ProjectMaterialPreparation;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanPreparationVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectMaterialPreparation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
public interface ProjectMaterialPreparationService  extends  OrionBaseService<ProjectMaterialPreparation>  {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectMaterialPreparationVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectMaterialPreparationDTO
     */
    String create(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, <PERSON>olean submit)throws Exception;

    /**
     *  编辑
     *
     * * @param projectMaterialPreparationDTO
     */
    Boolean edit(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, Boolean submit)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectMaterialPreparationVO> pages( Page<ProjectMaterialPreparationDTO> pageRequest)throws Exception;

    /**
     * 变更
     * @param projectMaterialPreparationDTO
     * @param submit
     * @return
     * @throws Exception
     */
    String upgrade(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, Boolean submit) throws Exception;

    /**
     * 获取版本记录
     * @param revKey
     * @return
     * @throws Exception
     */
    List<ProjectMaterialPreparationVO> getAllRevList(String revKey) throws Exception;

    /**
     * 物资计划通过项目id和物料编码获取备料量
     * @param projectId
     * @param materialNumberList
     * @return
     */
    Map<String, Integer> getPreparationNum(String projectId, List<String> materialNumberList);

    /**
     * 物资计划备料单查询
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectMaterialPlanPreparationVO> pageByMaterialPlan(Page<ProjectMaterialPlanPreparationDTO> pageRequest) throws Exception;
}
