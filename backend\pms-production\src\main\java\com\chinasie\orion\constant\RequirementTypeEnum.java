package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/9:54
 * @description:
 */
public enum RequirementTypeEnum {
    PMS_REQ_CERTIFICATE("pms_req_certificate","取得证书"),
    PMS_REQ_TRAIN("pms_req_train","通过培训"),

    ;

    public void setKey(String key) {
        this.key = key;
    }


    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }


    RequirementTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    private String key;


    private String desc;



    public static Map<String,String> keyDesc(){
        Map<String,String> keyToDescMap = new HashMap<>();
        RequirementTypeEnum[] values = RequirementTypeEnum.values();
        for (RequirementTypeEnum value : values) {
            keyToDescMap.put(value.getKey(),value.getDesc());
        }

        return  keyToDescMap;
    }

}
