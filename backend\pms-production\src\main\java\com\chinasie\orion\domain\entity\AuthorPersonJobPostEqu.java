package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * AuthorPersonJobPostEqu Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:34
 */
@TableName(value = "pmsx_author_person_job_post_equ")
@ApiModel(value = "AuthorPersonJobPostEquEntity对象", description = "作业人员岗位授权等效")
@Data

public class AuthorPersonJobPostEqu extends  ObjectEntity  implements Serializable{

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    @TableField(value = "person_id")
    private String personId;

    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员编号/工号")
    @TableField(value = "person_code")
    private String userCode;

    /**
     * 作业岗位编码
     */
    @ApiModelProperty(value = "作业岗位编码")
    @TableField(value = "job_post_code")
    private String jobPostCode;

    /**
     * 历史岗位授权ID
     */
    @ApiModelProperty(value = "历史岗位授权ID")
    @TableField(value = "history_author_id")
    private String historyAuthorId;

    /**
     * 等效的现有授权ID
     */
    @ApiModelProperty(value = "等效的现有授权ID")
    @TableField(value = "author_id")
    private String authorId;

    /**
     * 授权管理ID
     */
    @ApiModelProperty(value = "授权管理ID")
    @TableField(value = "author_manage_id")
    private String authorManageId;

}
