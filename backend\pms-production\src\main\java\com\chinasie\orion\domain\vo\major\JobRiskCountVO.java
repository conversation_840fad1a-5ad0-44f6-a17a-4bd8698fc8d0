package com.chinasie.orion.domain.vo.major;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/18:54
 * @description:
 */
@Data
public class JobRiskCountVO implements Serializable {
    @ApiModelProperty(value = "作业总数")
    private Integer jobNum =0;
    @ApiModelProperty(value = "高风险一级统计数")
    private Integer heightOneNum =0;
    @ApiModelProperty(value = "高风险二级统计数")
    private Integer heightTwoNum =0;
    @ApiModelProperty(value = "高风险三级统计数")
    private Integer heightThreeNum =0;
    @ApiModelProperty(value = "准备完成率")
    private BigDecimal prepRate =BigDecimal.ZERO;

}
