<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PersonManageLedgerMapper">



    <select id="outNumberToDayByBaseCode" resultType="java.lang.Integer">
       select  count(id) from pmsx_person_manage_ledger where base_code=#{baseCode} and type ='out' AND DATE(act_out_date)= CURDATE();
    </select>


    <select id="listBySouceIdList" resultType="com.chinasie.orion.domain.entity.PersonManageLedger">

        select person_manage_id as personManageId ,`type` as `type`  from pmsx_person_manage_ledger
                                        <where>
                                             logic_status = '1'
                                            <if test="personManageIdList != null and personManageIdList.size() > 0">
                                                and person_manage_id in
                                                <foreach collection="personManageIdList" item="item" open="(" separator="," close=")">
                                                    #{item}
                                                </foreach>
                                            </if>
                                            AND (DATE(act_in_date) = CURDATE() or DATE(act_out_date) = CURDATE()) GROUP BY person_manage_id,`type`
                                        </where>

    </select>

</mapper>
