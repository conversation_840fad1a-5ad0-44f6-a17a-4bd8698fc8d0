package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectCollection Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@TableName(value = "pmsx_project_collection")
@ApiModel(value = "ProjectCollectionEntity对象", description = "项目集")
@Data
public class ProjectCollection extends ObjectEntity implements Serializable {

    /**
     * 项目集名称
     */
    @ApiModelProperty(value = "项目集名称")
    @TableField(value = "name")
    private String name;

    /**
     * 项目可见人员
     */
    @ApiModelProperty(value = "项目可见人员")
    @TableField(value = "related_person")
    private String relatedPerson;

    /**
     * 项目责任人
     */
    @ApiModelProperty(value = "项目责任人")
    @FieldBind(dataBind =UserDataBind.class, target = "resPersonName")
    @TableField(value = "res_person")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @FieldBind(dataBind = DeptDataBind.class, target = "resDeptName")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    @ApiModelProperty(value = "项目集级别")
    @TableField(value = "project_collection_level")
    private String projectCollectionLevel;

    @ApiModelProperty(value = "项目集类型")
    @TableField(value = "project_collection_type")
    private String projectCollectionType;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;



    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    @TableField(exist = false)
    private String resDeptName;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String resPersonName;
}
