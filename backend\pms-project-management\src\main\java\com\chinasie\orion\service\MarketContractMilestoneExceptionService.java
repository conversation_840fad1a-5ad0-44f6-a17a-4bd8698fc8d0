package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.MarketContractMilestoneException;
import com.chinasie.orion.domain.dto.MarketContractMilestoneExceptionDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneExceptionVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MarketContractMilestoneException 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
public interface MarketContractMilestoneExceptionService  extends  OrionBaseService<MarketContractMilestoneException>  {


    /**
     *  详情
     *
     * * @param id
     */
    MarketContractMilestoneExceptionVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param marketContractMilestoneExceptionDTO
     */
    String create(MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO)throws Exception;

    /**
     *  查询里程碑异常列表
     *
     * * @param id
     */
    List<MarketContractMilestoneExceptionVO> listByMilestoneId(String milestoneId)throws Exception;

    /**
     *  编辑
     *
     * * @param marketContractMilestoneExceptionDTO
     */
    Boolean edit(MarketContractMilestoneExceptionDTO marketContractMilestoneExceptionDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MarketContractMilestoneExceptionVO> pages( Page<MarketContractMilestoneExceptionDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<MarketContractMilestoneExceptionVO> vos)throws Exception;
}
