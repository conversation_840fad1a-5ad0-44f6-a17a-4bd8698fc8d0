<template>
  <div class="productLibraryDetails5920">
    <div
      class="productLibraryDetails_content5920"
    >
      <div class="productLibraryDetails_left">
        <basicTitle :title="'预览'">
          <pdmImage
            :img-url="formState.projectImage ? pictureBase + formState.projectImage : ''"
            :show-delete="formType == 'edit'"
            @deleteImgUrl="deleteImgUrl"
          />
        </basicTitle>
      </div>

      <div class="productLibraryDetails_right">
        <basicTitle :title="'基本信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :rules="formType == 'details' ? {} : rules"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item>
                <BasicButton
                  v-if="isPower('XMX_container_button_90', powerData)"
                  class="mr10"
                  icon="sie-icon-edit"
                  @click="clickType"
                >
                  <span class="labelSpan">编辑</span>
                </BasicButton>
              </a-form-item>
              <a-form-item
                label="项目编号"
                name="code"
              >
                <span>{{ formState.number }}</span>
              </a-form-item>
              <a-form-item
                label="项目名称"
                name="name"
              >
                <span>{{ formState.name }}</span>
              </a-form-item>
              <a-form-item
                label="项目经理"
                name="pm"
              >
                <span>{{ formState.pm }}</span>
              </a-form-item>
              <a-form-item
                label="立项时间"
                name="projectApproveTime"
              >
                <span>{{
                  formState.projectApproveTime
                    ? dayjs(formState.projectApproveTime).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item label="状态">
                <!-- <span>{{ formState.statusName }}</span> -->
                <DataStatusTag
                  :status-data="formState.dataStatus"
                />
              </a-form-item>
              <a-form-item label="关联产品">
                <span>{{ formState.productName }}</span>
              </a-form-item>
              <a-form-item
                label="开始时间"
                name="projectStartTime"
              >
                <span>{{
                  formState.projectStartTime
                    ? dayjs(dayjs(formState.projectStartTime)).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="结束时间"
                name="projectEndTime"
              >
                <span>{{
                  formState.projectEndTime
                    ? dayjs(dayjs(formState.projectEndTime)).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="关联计划"
              >
                <span
                  class="pt5"
                >{{ formMatter(formState.schemeList) }}</span>
              </a-form-item>
              <a-form-item
                label="描述"
              >
                <span
                  class="pt5"
                >{{ formState.remark }}</span>
              </a-form-item>

              <!--              <a-form-item-->
              <!--                v-if="formType == 'edit'"-->
              <!--                label="预览图"-->
              <!--              >-->
              <!--                <upload @successChange="successChange" />-->
              <!--              </a-form-item>-->
              <a-form-item label="修改人">
                <span>{{ formState.modifyName }}</span>
              </a-form-item>
              <a-form-item label="修改时间">
                <span>{{
                  formState.modifyTime
                    ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item label="创建人">
                <span>{{ formState.creatorName }}</span>
              </a-form-item>
              <a-form-item label="创建时间">
                <span>{{
                  formState.createTime
                    ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>
    </div>

    <AddTableNode
      @update="successSave"
      @register="registerAdd"
    />

    <!-- <checkDetails :data="dataRow" /> -->
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, onMounted, inject, computed, watch,
} from 'vue';
import { Form } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { pictureBase, emptyString } from '/@/views/pms/projectLaborer/api/picture';
/* api */
import dayjs from 'dayjs';
import {
  isPower, DataStatusTag, useDrawer, BasicButton,
} from 'lyra-component-vue3';
import upload from './upload/index.vue';
import pdmImage from '/@/views/pms/projectLaborer/componentsList/image/index.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import AddTableNode from '../../components/AddTableNode.vue';
import Api from '/@/api';
export default defineComponent({
  name: 'ProjectLabdetail',
  components: {
    BasicButton,
    aForm: Form,
    aFormItem: Form.Item,
    pdmImage,
    basicTitle,
    DataStatusTag,
    AddTableNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    //   const route = useRoute();
    //   const layoutModelStore = layoutModel();
    const formData = inject('formData', {});
    const getFormData = inject('getFormData', {});
    const formRef = ref();
    const state = reactive({
      formType: 'details',
      formState: formData.value || {},
      /* 编辑表单 */
      newformdata: <any>{},
      treeData: [],
      parentId: 1,
      oldFormState: <any>{},
      message: '',
      showVisible: false,
      btnType: '',
      dataRow: {},
      productModelIdOptions: [],
      projectIdOptions: [],
      secretLevelOption: [],
      status: [],
      productId: [],
      nowProjectId: '',
      contentHeight: 600,
      powerData: [],
    });

    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    state.powerData = inject('powerData');

    watch(
      () => formData?.value,
      (val) => {
        state.formState = val;
      },
    );

    const clickType = (type) => {
      openDrawerAdd(true, {
        type: 'edit',
        id: formData?.value.id,
      });
    };
    function successSave() {
      try {
        getFormData.value();
      } catch (err) {

      }
    }

    onMounted(async () => {
      state.contentHeight = document.body.clientHeight - 365;

      // nextTick( async()=>{
      // state.status = await getProductListApi(provideProjectId.value);
      // })
    });
    /* 获取详情 */
    const successChange = (data) => {
      state.formState.projectImage = data.imageId;
      state.newformdata.projectImage = data.imageId;
      // console.log('测试🚀🚀 ~~~ data.imageId', data.imageId);
      // console.log('测试🚀🚀 ~~~ state.formState.projectImage', state.formState.projectImage);
    };
    function formMatter(val) {
      if (Array.isArray(val)) {
        let name = val.map((item) => item.name);
        return name.join(',');
      }
      return '';
    }
    return {
      ...toRefs(state),
      formRef,
      successChange,
      clickType,
      dayjs,
      pictureBase,
      registerAdd,
      successSave,
      formMatter,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
.productLibraryDetails_content5920 {
  padding-right:  ~`getPrefixVar('content-margin-left')`;
  display: flex;
  >div {
    flex: 1;
    margin-left: ~`getPrefixVar('content-margin-left')`;
  }
}
</style>
