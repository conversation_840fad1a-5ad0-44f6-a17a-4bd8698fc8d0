package com.chinasie.orion.management.controller;

import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.RequirementManageCustContactDTO;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.RequirementManageCustContactVO;
import com.chinasie.orion.management.domain.dto.RequirementsManagementLogsDTO;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.management.service.RequirementManageCustContactService;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.management.service.RequirementsManagementLogsService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * RequirementMangement 需求管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@RestController
@RequestMapping("/requirementMangement")
@Api(tags = "经营管理-需求")
@RequiredArgsConstructor
public class  RequirementMangementController  {
    private final RequirementsManagementLogsService requirementsManagementLogsService;
    private final RequirementMangementService requirementMangementService;
    private final RequirementManageCustContactService requirementManageCustContactService;

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord( success = "【{USER{#logUserId}}】查询了数据", type = "经营管理-需求", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<RequirementMangementVO> detail(
            @PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        RequirementMangementVO rsp = requirementMangementService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#requirementMangementDTO.name}}】",
            type = "经营管理-需求", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody RequirementMangementDTO requirementMangementDTO) throws Exception {
        String rsp = requirementMangementService.save(requirementMangementDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#requirementMangementDTO.name}}】",
            type = "经营管理-需求", subType = "编辑", bizNo = "{{#requirementMangementDTO.id}}")
    public ResponseDTO<?> edit(@RequestBody RequirementMangementDTO requirementMangementDTO) throws Exception {
        requirementMangementService.save(requirementMangementDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "分发")
    @RequestMapping(value = "/distribute", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】分发了数据【{{#requirementMangementDTO.name}}】",
            type = "经营管理-需求", subType = "分发", bizNo = "{{#requirementMangementDTO.id}}")
    public ResponseDTO<?> distribute(@RequestBody RequirementMangementDTO requirementMangementDTO) {
        requirementMangementService.distribute(requirementMangementDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "分发确认")
    @RequestMapping(value = "/distribute/confirm", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】分发确认了数据【{{#requirementMangementDTO.name}}】",
            type = "经营管理-需求", subType = "分发确认", bizNo = "{{#requirementMangementDTO.id}}")
    public ResponseDTO<?> distributeConfirm(@RequestBody RequirementMangementDTO requirementMangementDTO) {
        requirementMangementService.distributeConfirm(requirementMangementDTO);
        return new ResponseDTO<>("");
    }


    @ApiOperation(value = "取消")
    @RequestMapping(value = "/cancel", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】取消了数据【{{#requirementMangementDTO.name}}】",
            type = "经营管理-需求", subType = "取消", bizNo = "{{#requirementMangementDTO.id}}")
    public ResponseDTO<?> cancel(@RequestBody RequirementMangement requirementMangement) {
        requirementMangementService.cancel(requirementMangement);
        return new ResponseDTO<>("");
    }


    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "经营管理-需求", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = requirementMangementService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "经营管理-需求", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = requirementMangementService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "经营管理-需求", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<RequirementMangementVO>> pages(@RequestBody Page<RequirementMangementDTO> pageRequest) throws Exception {
        Page<RequirementMangementVO> rsp = requirementMangementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("主数据状态的枚举")
    @GetMapping(value = "/status/list")
    @LogRecord(success = "【{USER{#logUserId}}】主数据状态的枚举", type = "经营管理-需求", subType = "主数据状态的枚举", bizNo = "")
    public ResponseDTO<List<DataStatusVO>> statusList() {
        return new ResponseDTO<>(requirementMangementService.listDataStatus());
    }

    @ApiOperation("中心数据")
    @GetMapping(value = "/dept/list")
    @LogRecord(success = "【{USER{#logUserId}}】中心数据", type = "经营管理-需求", subType = "中心数据", bizNo = "")
    public ResponseDTO<List<DeptVO>> getDeptList() {
        List<DeptVO> deptList = requirementMangementService.getDeptList();
        return new ResponseDTO<>(deptList);
    }


    @ApiOperation(value = "客户联系人分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "RequirementManageCustContact",
            subType = "客户联系人分页查询", bizNo = "")
    @RequestMapping(value = "/contact/page", method = RequestMethod.POST)
    public ResponseDTO<?> contactPage(@RequestBody Page<RequirementManageCustContactDTO> pageRequest) {
        Page<RequirementManageCustContactVO> rsp = requirementManageCustContactService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 报名
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "报名")
    @RequestMapping(value = "/applicant", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】报名了数据", type = "经营管理-需求", subType = "报名", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> applicant(@RequestBody @Validated List<String> ids) throws Exception {
        Boolean rsp = requirementMangementService.applicant(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 取消报名
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "取消报名")
    @RequestMapping(value = "/cancelApplicant", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消报名了数据", type = "经营管理-需求", subType = "取消报名", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> cancelApplicant(@RequestBody @Validated List<String> ids) throws Exception {
        Boolean rsp = requirementMangementService.cancelApplicant(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 关闭需求
     *
     * @param requirementMangementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关闭需求")
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】关闭了数据", type = "经营管理-需求", subType = "关闭需求", bizNo = "{{#requirementMangementDTO.id}}")
    public ResponseDTO<String> close(@RequestBody RequirementMangementDTO requirementMangementDTO) throws Exception {
        String rsp = requirementMangementService.close(requirementMangementDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 关闭需求
     *
     * @param requirementMangementDTOs
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量关闭需求")
    @RequestMapping(value = "/close/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量关闭了数据", type = "经营管理-需求", subType = "批量关闭需求", bizNo = "{{#requirementMangementDTOs.toString()}}")
    public ResponseDTO<String> closeBatch(@RequestBody List<RequirementMangementDTO> requirementMangementDTOs) throws Exception {
        String rsp = requirementMangementService.closeBatch(requirementMangementDTOs);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("明细导出")
    @PostMapping(value = "/export/excelData",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "RequirementMangement", subType = "明细导出", bizNo = "")
    public void exportExcelData(@RequestBody Page<RequirementMangementDTO> pageRequest, HttpServletResponse response) throws Exception {
        requirementMangementService.exportExcelData(pageRequest, response);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "菜单分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "RequirementMangement", subType = "菜单数据分页查询", bizNo = "")
    @RequestMapping(value = "/page/menu", method = RequestMethod.POST)
    public ResponseDTO<Page<RequirementMangementVO>> pagesMenu(@RequestBody Page<RequirementMangementDTO> pageRequest) throws Exception {
        Page<RequirementMangementVO> rsp = requirementMangementService.pagesMenu(pageRequest);
        return new ResponseDTO<>(rsp);
    }


}
