<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>52</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-io</groupId>
  <artifactId>commons-io</artifactId>
  <version>2.11.0</version>
  <name>Apache Commons IO</name>

  <inceptionYear>2002</inceptionYear>
  <description>
The Apache Commons IO library contains utility classes, stream implementations, file filters,
file comparators, endian transformation classes, and much more.
  </description>

  <url>https://commons.apache.org/proper/commons-io/</url>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/IO</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-io/</url>
    </site>
  </distributionManagement>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/commons-io.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-io.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=commons-io.git</url>
    <tag>rel/commons-io-2.11.0</tag>
  </scm>

  <developers>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>dIon Gillard</name>
      <!-- Note: first name is correctly capitalised above -->
      <id>dion</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Nicola Ken Barozzi</name>
      <id>nicolaken</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Henri Yandell</name>
      <id>bayard</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Stephen Colebourne</name>
      <id>scolebourne</id>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <name>Jeremias Maerki</name>
      <id>jeremias</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Matthew Hawthorne</name>
      <id>matth</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Martin Cooper</name>
      <id>martinc</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Rob Oxspring</name>
      <id>roxspring</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jochen Wiedmann</name>
      <id>jochen</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Niall Pemberton</name>
      <id>niallp</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jukka Zitting</name>
      <id>jukka</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email>ggregory at apache.org</email>
      <url>https://www.garygregory.com</url>
      <organization>The Apache Software Foundation</organization>
      <organizationUrl>https://www.apache.org/</organizationUrl>      
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New_York</timezone>
      <properties>
        <picUrl>https://people.apache.org/~ggregory/img/garydgregory80.png</picUrl>
      </properties>
    </developer>
    <developer>
      <name>Kristian Rosenvold</name>
      <id>krosenvold</id>
      <email><EMAIL></email>
      <timezone>+1</timezone>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Rahul Akolkar</name>
    </contributor>
    <contributor>
      <name>Jason Anderson</name>
    </contributor>
    <contributor>
      <name>Nathan Beyer</name>
    </contributor>
    <contributor>
      <name>Emmanuel Bourg</name>
    </contributor>
    <contributor>
      <name>Chris Eldredge</name>
    </contributor>
    <contributor>
      <name>Magnus Grimsell</name>
    </contributor>
    <contributor>
      <name>Jim Harrington</name>
    </contributor>
    <contributor>
      <name>Thomas Ledoux</name>
    </contributor>
    <contributor>
      <name>Andy Lehane</name>
    </contributor>
    <contributor>
      <name>Marcelo Liberato</name>
    </contributor>
    <contributor>
      <name>Alban Peignier</name>
      <email>alban.peignier at free.fr</email>
    </contributor>
    <contributor>
      <name>Adam Retter</name>
      <organization>Evolved Binary</organization>
    </contributor>
    <contributor>
      <name>Ian Springer</name>
    </contributor>
    <contributor>
      <name>Dominik Stadler</name>
    </contributor>
    <contributor>
      <name>Masato Tezuka</name>
    </contributor>
    <contributor>
      <name>James Urie</name>
    </contributor>
    <contributor>
      <name>Frank W. Zammetti</name>
    </contributor>
  </contributors>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.7.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  
  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit-pioneer</groupId>
      <artifactId>junit-pioneer</artifactId>
      <version>1.4.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <version>3.11.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.jimfs</groupId>
      <artifactId>jimfs</artifactId>
      <version>1.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>${jmh.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>${jmh.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <commons.componentid>io</commons.componentid>
    <commons.module.name>org.apache.commons.io</commons.module.name>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.bc.version>2.10.0</commons.bc.version>
    <commons.release.version>2.11.0</commons.release.version>
    <commons.release.desc>(requires Java 8)</commons.release.desc>
    <commons.jira.id>IO</commons.jira.id>
    <commons.jira.pid>12310477</commons.jira.pid>
    <commons.osgi.export>
        <!-- Explicit list of packages from IO 1.4 -->
        org.apache.commons.io;
        org.apache.commons.io.comparator;
        org.apache.commons.io.filefilter;
        org.apache.commons.io.input;
        org.apache.commons.io.output;version=1.4.9999;-noimport:=true,
        <!-- Same list plus * for new packages -->
        org.apache.commons.io;
        org.apache.commons.io.comparator;
        org.apache.commons.io.filefilter;
        org.apache.commons.io.input;
        org.apache.commons.io.output;
        org.apache.commons.io.*;version=${project.version};-noimport:=true
    </commons.osgi.export>
    <commons.osgi.import>
        <!-- IO-734 - Make the sun.* references from BufferedFileChannelInputStream optional -->
        sun.nio.ch;resolution:=optional,
        sun.misc;resolution:=optional,
        *
    </commons.osgi.import>
    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-io/</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>
    <checkstyle.plugin.version>3.1.2</checkstyle.plugin.version>
    <commons.jacoco.version>0.8.7</commons.jacoco.version>
    <commons.surefire.version>3.0.0-M5</commons.surefire.version>
    <commons.japicmp.version>0.15.3</commons.japicmp.version>
    <spotbugs.plugin.version>4.2.3</spotbugs.plugin.version>
    <spotbugs.impl.version>4.3.0</spotbugs.impl.version>
    <jmh.version>1.32</jmh.version>    
    <japicmp.skip>false</japicmp.skip>
    <jacoco.skip>${env.JACOCO_SKIP}</jacoco.skip>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.releaseManagerName>Gary Gregory</commons.releaseManagerName>    
    <commons.releaseManagerKey>86fdc7e2a11262cb</commons.releaseManagerKey>
  </properties>

  <build>
    <!-- japicmp:cmp needs package to work from a jar -->
    <defaultGoal>clean package apache-rat:check japicmp:cmp checkstyle:check javadoc:javadoc</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>0.13</version>
          <configuration>
            <excludes>
              <exclude>src/test/resources/**/*.bin</exclude>
              <exclude>src/test/resources/dir-equals-tests/**</exclude>
              <exclude>test/**</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${checkstyle.plugin.version}</version>
          <configuration>
            <configLocation>${basedir}/checkstyle.xml</configLocation>
            <enableRulesSummary>false</enableRulesSummary>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>8.44</version>
            </dependency>
          </dependencies>
        </plugin>      
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M3</version>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0.5</version>
                </requireMavenVersion>
              </rules>    
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <archive combine.children="append">
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <classpathDependencyExcludes>
            <classpathDependencyExclude>xerces:xercesImpl</classpathDependencyExclude>
          </classpathDependencyExcludes>
          <forkCount>1</forkCount>
          <reuseForks>false</reuseForks>
          <!-- limit memory size see IO-161 -->
          <argLine>${argLine} -Xmx25M</argLine>
          <includes>
            <!-- Only include test classes, not test data -->
            <include>**/*Test*.class</include>
          </includes>
          <excludes>
            <exclude>**/*AbstractTestCase*</exclude>
            <exclude>**/testtools/**</exclude>
            <!-- https://issues.apache.org/jira/browse/SUREFIRE-44 -->
            <exclude>**/*$*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <ignorePathsToDelete>
            <ignorePathToDelete>javadocs</ignorePathToDelete>
          </ignorePathsToDelete>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.plugin.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs</artifactId>
            <version>${spotbugs.impl.version}</version>
         </dependency>
        </dependencies>
        <configuration>
          <excludeFilterFile>${basedir}/spotbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.plugin.version}</version>
        <configuration>
          <excludeFilterFile>${basedir}/spotbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
      </plugin>    
    </plugins>
  </reporting>
  <profiles>
    <profile>
      <id>setup-checkout</id>
      <activation>
        <file>
          <missing>site-content</missing>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <version>3.0.0</version>
            <executions>
              <execution>
                <id>prepare-checkout</id>
                <phase>pre-site</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <exec executable="svn">
                      <arg line="checkout --depth immediates ${commons.scmPubUrl} ${commons.scmPubCheckoutDirectory}" />
                    </exec>

                    <exec executable="svn">
                      <arg line="update --set-depth exclude ${commons.scmPubCheckoutDirectory}/javadocs" />
                    </exec>

                    <pathconvert pathsep=" " property="dirs">
                      <dirset dir="${commons.scmPubCheckoutDirectory}" includes="*" />
                    </pathconvert>
                    <exec executable="svn">
                      <arg line="update --set-depth infinity ${dirs}" />
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>java9+</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <!-- coverall version 4.3.0 does not work with java 9, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
        <coveralls.skip>true</coveralls.skip>
      </properties>
    </profile>
    <profile>
      <id>benchmark</id>
      <properties>
        <skipTests>true</skipTests>
        <benchmark>org.apache</benchmark>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <version>3.0.0</version>
            <executions>
              <execution>
                <id>benchmark</id>
                <phase>test</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <classpathScope>test</classpathScope>
                  <executable>java</executable>
                  <arguments>
                    <argument>-classpath</argument>
                    <classpath/>
                    <argument>org.openjdk.jmh.Main</argument>
                    <argument>-rf</argument>
                    <argument>json</argument>
                    <argument>-rff</argument>
                    <argument>target/jmh-result.${benchmark}.json</argument>
                    <argument>${benchmark}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
