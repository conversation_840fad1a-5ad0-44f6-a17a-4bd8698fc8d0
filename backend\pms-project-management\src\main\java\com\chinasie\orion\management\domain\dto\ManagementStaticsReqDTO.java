package com.chinasie.orion.management.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(value = "ManagementStaticsReqDTO对象", description = "市场经营看板-里程碑完成情况")
@Data
public class ManagementStaticsReqDTO implements Serializable {
    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptId;

    /**
     * 筛选年份
     */
    @ApiModelProperty(value = "筛选年份")
    @NotNull
    private Integer filterYear;

    /**
     * 筛选月份
     */
    @ApiModelProperty(value = "筛选月份")
    private Integer filterMonth;
}

