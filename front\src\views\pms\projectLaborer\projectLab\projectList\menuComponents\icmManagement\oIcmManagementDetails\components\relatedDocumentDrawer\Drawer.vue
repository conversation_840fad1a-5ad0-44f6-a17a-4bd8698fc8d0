<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm
      ref="formModalRef"
      :action="state.action"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
// 抽屉套表单（新增，编辑操作）
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);
const UserStore = useUserStore();

function initData() {
  return {
    action: 'add',
    title: '',
    showContinue: false,
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
    // interfaceId creatorId
    new Api(`/pms/interface-management/${state.originData?.record.interfaceId}`).fetch('', '', 'GET').then((res) => {
      formModalRef.value.setOptions([
        {
          label: res.creatorName,
          value: res.creatorId,
        },
      ]);
      formModalRef.value.FormMethods.setFieldsValue({
        manUser: res.creatorId,
        reviewDeptIdList: res?.publishDeptId ? [res?.publishDeptId] : undefined,
        publishDeptId: UserStore.getUserInfo?.simpleUser?.orgId ?? '',
      });
    });
  }
  if (data.action === 'edit') {
    setTimeout(() => {
      formModalRef.value.setOptions([
        {
          label: state.originData.record.manUserName,
          value: state.originData.record.manUser,
        },
      ]);
      // state.originData.record.reviewDeptIdList = state.originData?.record?.reviewDeptIds;
      formModalRef.value.FormMethods.setFieldsValue(state.originData?.record);
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      if (!state?.isContinue) {
        modalMethods.openDrawer(false);
      } else {
        formModalRef.value && formModalRef.value.FormMethods.resetFields();
      }
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  params.projectId = state.originData?.projectId;
  params.id = state.originData?.id;
  params.currentFormType = state.originData?.record?.formType;
  if (state.action === 'add') {
    params.interfaceId = state.originData?.record?.interfaceId;
    return await new Api('/pms/idea-form').fetch(params, '', 'POST');
  }
  if (state.action === 'edit') {
    return await new Api('/pms/idea-form').fetch(params, '', 'PUT');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
