import { createGlobalState } from '@vueuse/shared';
import { ref } from 'vue';

export const useGlobalWorkState = createGlobalState(() => {
  const refreshUpdateWorkKey = ref('update');
  const updateWorkKey = () => {
    refreshUpdateWorkKey.value = Math.round(Math.random() * 100000000).toString(32);
  };

  return {
    refreshUpdateWorkKey,
    updateWorkKey,
  };
});

export const useGlobalProgressState = createGlobalState(() => {
  const refreshUpdateProgressKey = ref('update');
  const updateProgressKey = () => {
    refreshUpdateProgressKey.value = Math.round(Math.random() * 100000000).toString(32);
  };

  return {
    refreshUpdateProgressKey,
    updateProgressKey,
  };
});

export const useGlobalMemberState = createGlobalState(() => {
  const refreshUpdateMemberKey = ref('update');
  const updateMemberKey = () => {
    refreshUpdateMemberKey.value = Math.round(Math.random() * 100000000).toString(32);
  };

  return {
    refreshUpdateMemberKey,
    updateMemberKey,
  };
});