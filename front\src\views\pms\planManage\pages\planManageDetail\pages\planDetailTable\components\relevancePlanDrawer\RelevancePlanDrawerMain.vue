<template>
  <div class="links-wrap flex flex-ver">
    <div class="links-have-wrap flex flex-ver">
      <div class="fw-b">
        已添加关联
      </div>
      <div
        v-if="state.linksPlan.length"
        class="links-have-list-wrap"
      >
        <div
          v-for="(item, index) in state.linksPlan"
          :key="index"
          class="flex flex-ac links-have-list-item"
        >
          <div class="flex-f1 flex-te">
            {{ item.name }}
          </div>
          <div
            class="action-btn"
            @click="deleteLink(index, item)"
          >
            移除
          </div>
        </div>
      </div>
      <div
        v-else
        class="links-have-list-wrap flex flex-pac"
      >
        <Empty
          description="请从下方添加关联"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>
    </div>
    <div class="fw-b mt10">
      可选关联计划
    </div>
    <div class="links-table-wrap flex-f1">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { OrionTable, IOrionTableOptions } from 'lyra-component-vue3';
import { getPlanLinksPre, getPlanPrePage } from '/@/views/pms/api';
import {
  onMounted, ref, unref, h, reactive,
} from 'vue';
import { Empty } from 'ant-design-vue';

const props = defineProps<{
  superId: string;
  planId: string;
  onInit?: (methods: {getLinksPlan: ()=> void})=>void
}>();
const tableRef = ref();
const state = reactive({
  linksPlan: [] as any[],
});

onMounted(() => {
  init();
});

function init() {
  setLinksPlanData();
  props.onInit && props.onInit({
    getLinksPlan,
  });
}

function getLinksPlan() {
  return state.linksPlan;
}

async function setLinksPlanData() {
  state.linksPlan = await getPlanLinksPre(props.planId) as any[];
}

function deleteLink(index, item) {
  state.linksPlan.splice(index, 1);
}

const tableOptions:IOrionTableOptions | any = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  api(params) {
    return getPlanPrePage({
      ...params,
      query: {
        superId: props.superId,
        currentPlanId: props.planId,
      },
    });
  },
  columns: [
    {
      title: '计划名称',
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      customRender({
        record, index, column,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            if (!state.linksPlan.find((item) => item.id === record.id)) {
              state.linksPlan.push(record);
            }
          },
        }, '添加关联');
      },
    },
  ],
};
</script>

<style scoped lang="less">
.links-wrap {
  height: 100%;

  .links-have-list-wrap {
    height: 160px!important;
    overflow-y: auto;

    .links-have-list-item {
      padding: 10px 10px;
      border-bottom: 1px solid #e9e9e9;

      &:nth-child(2n-2) {
        background: rgba(0,0,0,.02);
      }

      &:hover {
        background: rgba(0,0,0,.03);
      }
    }
  }

  .links-table-wrap {
    overflow: hidden;
    position: relative;
    :deep(.ant-basic-table) {
      padding: 0!important;
    }
  }

}
</style>
