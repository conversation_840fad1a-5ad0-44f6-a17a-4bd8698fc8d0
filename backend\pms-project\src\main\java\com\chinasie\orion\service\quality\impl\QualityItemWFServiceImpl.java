package com.chinasie.orion.service.quality.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.base.api.domain.vo.DeptLeaderVO;
import com.chinasie.orion.base.api.service.DeptLeaderBaseApiService;
import com.chinasie.orion.constant.MessageNodeConstant;
import com.chinasie.orion.constant.quality.ExecuteEnum;
import com.chinasie.orion.constant.quality.FinishEnum;
import com.chinasie.orion.constant.quality.QualityItemStatusEnum;
import com.chinasie.orion.constant.quality.RelevanceSchemeEnum;
import com.chinasie.orion.domain.dto.quality.QualityItemAffirmDTO;
import com.chinasie.orion.domain.dto.quality.UpdateMsgUrlDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.domain.entity.quality.QualityItemMessage;
import com.chinasie.orion.domain.entity.quality.QualityItemScheme;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.feign.dto.MessageIdTodoStatusDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.ProjectSchemeRepository;
import com.chinasie.orion.repository.quality.QualityItemMapper;
import com.chinasie.orion.repository.quality.QualityItemMessageMapper;
import com.chinasie.orion.repository.quality.QualityItemSchemeMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.quality.QualityItemWFService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("QualityItemWFService")
@Slf4j
public class QualityItemWFServiceImpl implements QualityItemWFService {

    @Resource
    private QualityItemMapper mapper;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private MessageCenterApi mscFeignService;
    @Resource
    private DeptLeaderBaseApiService deptLeaderBaseApiService;
    @Resource
    private QualityItemMessageMapper messageMapper;
    @Resource
    private ProjectRepository projectRepository;
    @Resource
    private ProjectSchemeRepository schemeRepository;
    @Resource
    private QualityItemSchemeMapper itemSchemeMapper;

    @Override
    public Boolean commit(List<String> ids) {
        List<QualityItem> qualitySteps = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(qualitySteps)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"提交的数据已被删除或不存在！");
        }
        Project project = projectRepository.selectById(qualitySteps.get(0).getProjectId());
        List<QualityItem> notStatus = qualitySteps.stream().filter(q -> !QualityItemStatusEnum.CREATED.getCode().equals(q.getStatus())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(notStatus)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS,"提交的数据中存在不可提交状态数据请刷新重新提交！");
        }
        qualitySteps.forEach(q ->  q.setStatus(QualityItemStatusEnum.GOING.getCode()));
        mapper.updateBatch(qualitySteps,20);

        List<DeptLeaderVO> deptLeaderByDeptId = deptLeaderBaseApiService.getUserLeaderByUserId(CurrentUserHelper.getCurrentUserId());
        if (!CollectionUtil.isEmpty(deptLeaderByDeptId)){
            List<String> leaderIds = deptLeaderByDeptId.stream().map(DeptLeaderVO::getUserId).collect(Collectors.toList());
            Map<String,Object> messageMap = new HashMap<>();
            messageMap.put("$projectName$",project.getName());
            SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                    .titleMap(messageMap)
                    .messageMap(messageMap)
                    .recipientIdList(leaderIds)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .businessData(JSONUtil.toJsonStr(ids))
                    .businessId(ids.get(0))
                    .platformId(project.getPlatformId())
                    .orgId(CurrentUserHelper.getOrgId())
                    .businessNodeCode(MessageNodeConstant.QUALITY_ITEM_COMMIT)
                    .build();
            sendCommitMessage(ids, sendMessageDTO,project.getId());
        }
        return true;
    }


    @Override
    public Boolean affirm(List<String> ids){
        List<QualityItem> qualitySteps = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(qualitySteps)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"提交的数据已被删除或不存在！");
        }
        List<QualityItem> notStatus = qualitySteps.stream().filter(q -> !QualityItemStatusEnum.GOING.getCode().equals(q.getStatus())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(notStatus)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS,"提交的数据中存在不可提交状态数据请刷新重新提交！");
        }
        qualitySteps.forEach(q -> q.setStatus(QualityItemStatusEnum.COMPLETE.getCode()));
        mapper.updateBatch(qualitySteps,20);
        removeMessage(ids);
        // 发送待办
        completeSendMessage(ids,qualitySteps.get(0).getProjectId());
        return true;
    }

    @Override
    public Boolean reject(List<String> ids){
        List<QualityItem> qualitySteps = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(qualitySteps)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"提交的数据已被删除或不存在！");
        }
        Project project = projectRepository.selectById(qualitySteps.get(0).getProjectId());
        List<QualityItem> notStatus = qualitySteps.stream().filter(q -> !QualityItemStatusEnum.GOING.getCode().equals(q.getStatus())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(notStatus)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS,"提交的数据中存在不可提交状态数据请刷新重新提交！");
        }
        qualitySteps.forEach(q -> q.setStatus(QualityItemStatusEnum.CREATED.getCode()));
        mapper.updateBatch(qualitySteps,20);
        removeMessage(ids);

        Map<String,Object> messageMap = new HashMap<>();
        UserVO userById = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        messageMap.put("$projectName$", project.getName());
        if (ObjectUtil.isNotNull(userById)){
            messageMap.put("$userName$", userById.getName());
        }
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .recipientIdList(qualitySteps.stream().map(QualityItem::getCreatorId).distinct().collect(Collectors.toList()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessData(JSONUtil.toJsonStr(ids))
                .businessId(ids.get(0))
                .platformId(project.getPlatformId())
                .orgId(CurrentUserHelper.getOrgId())
                .businessNodeCode(MessageNodeConstant.QUALITY_ITEM_REJECT)
                .build();
        //发送消息
        sendMessage(ids, sendMessageDTO);
        return true;
    }

    @Override
    public Boolean complete(QualityItemAffirmDTO qualityItemAffirmDTO){
        List<String> ids = qualityItemAffirmDTO.getIds();
        String completionStatement = qualityItemAffirmDTO.getCompletionStatement();
        List<QualityItem> qualitySteps = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(qualitySteps)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"提交的数据已被删除或不存在！");
        }
        List<QualityItem> notStatus = qualitySteps.stream().filter(q -> !QualityItemStatusEnum.COMPLETE.getCode().equals(q.getStatus())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(notStatus)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS,"提交的数据中存在不可提交状态数据请刷新重新提交！");
        }
        qualitySteps.forEach(q -> {
            q.setStatus(QualityItemStatusEnum.AFFIRM.getCode());
            if (StrUtil.isNotBlank(completionStatement)){
                q.setCompletionStatement(completionStatement);
            }
        });
        mapper.updateBatch(qualitySteps,20);

        // todo 消除待办
        return true;
    }

    @Override
    public Boolean correlation(String id, List<String> schemeIds) {
        if (StrUtil.isBlank(id)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL,"id不能为空！");
        }
        QualityItem qualityItem = mapper.selectById(id);
        if (ObjectUtil.isNull(qualityItem)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"质量管控项数据不存在！");
        }
        if (CollectionUtil.isEmpty(schemeIds)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL,"计划id不能为空！");
        }
        List<QualityItemScheme> itemSchemes = new ArrayList<>();
        for (String schemeId : schemeIds) {
            QualityItemScheme qualityItemScheme = new QualityItemScheme();
            qualityItemScheme.setQualityItemId(id);
            qualityItemScheme.setProjectSchemeId(schemeId);
            itemSchemes.add(qualityItemScheme);
        }
        List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
        if (!CollectionUtil.isEmpty(projectSchemes)){
            ExecuteEnum execute = ExecuteEnum.getExecute(projectSchemes);
            qualityItem.setExecute(execute.getCode());
            qualityItem.setRelevanceScheme(RelevanceSchemeEnum.YES.getCode());
        }
        mapper.updateById(qualityItem);
        itemSchemeMapper.insertBatch(itemSchemes);

        List<QualityItem> byProjectId = getByProjectId(qualityItem.getProjectId(),id);
        if (CollectionUtil.isEmpty(byProjectId)){
            removeMessage(Collections.singletonList(id));
        }
        return true;
    }

    private void removeMessage(List<String> ids) {
        List<QualityItemMessage> messages = getQualityItemMessageList(ids);
        if (CollectionUtil.isEmpty(messages)){
            return;
        }
        List<String> messageIds = messages.stream().map(QualityItemMessage::getMessageId).distinct().collect(Collectors.toList());
        List<QualityItemMessage> byMessageIds = getQualityItemMessageListByMessageIds(messageIds);
        if (CollectionUtil.isEmpty(byMessageIds)){
            return;
        }
        messages.forEach(m -> m.setFinish(FinishEnum.OK.getCode()));
        messageMapper.updateBatch(messages,20);

        Map<String, List<QualityItemMessage>> allMap = byMessageIds.stream().collect(Collectors.groupingBy(QualityItemMessage::getMessageId));
        Map<String, List<QualityItemMessage>> affirmMap = messages.stream().collect(Collectors.groupingBy(QualityItemMessage::getMessageId));
        List<String> removeMessageIds = new ArrayList<>();
        for (String key : allMap.keySet()){
            Set<String> all = allMap.get(key).stream().map(QualityItemMessage::getId).collect(Collectors.toSet());
            Set<String> affirm = affirmMap.get(key).stream().map(QualityItemMessage::getId).collect(Collectors.toSet());
            if (all.equals(affirm)){
                removeMessageIds.add(key);
            }
        }
        if (!CollectionUtil.isEmpty(removeMessageIds)){
            MessageIdTodoStatusDTO messageIdTodoStatusDTO = new MessageIdTodoStatusDTO();
            messageIdTodoStatusDTO.setMessageIdList(removeMessageIds);
            messageIdTodoStatusDTO.setUserId(CurrentUserHelper.getCurrentUserId());
            try {
                mscFeignService.todoMessageChangeStatusAndCancelOtherRecipientMsgByMessageIds(messageIdTodoStatusDTO);
                log.info("待办消除成功 发起人：{}， 消息id{}", CurrentUserHelper.getCurrentUserId(),JSONUtil.toJsonStr(removeMessageIds));
            } catch (Exception e) {
                log.error("待办消除失败 发起人：{}， 消息id{}", CurrentUserHelper.getCurrentUserId(),JSONUtil.toJsonStr(removeMessageIds));
            }
        }
    }

    public List<QualityItemMessage> getQualityItemMessageList(List<String> stepIds){
        LambdaQueryWrapperX<QualityItemMessage> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.in(QualityItemMessage::getQualityItemId,stepIds)
                .eq(QualityItemMessage::getFinish,FinishEnum.NO.getCode());
        List<QualityItemMessage> messages = messageMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(messages)){
            return new ArrayList<>();
        }
        return messages;
    }

    public List<QualityItemMessage> getQualityItemMessageListByMessageIds(List<String> messageIds){
        LambdaQueryWrapperX<QualityItemMessage> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.in(QualityItemMessage::getMessageId,messageIds)
                .eq(QualityItemMessage::getFinish,FinishEnum.NO.getCode());
        List<QualityItemMessage> messages = messageMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(messages)){
            return new ArrayList<>();
        }
        return messages;
    }


    private void sendCommitMessage(List<String> ids, SendMessageDTO sendMessageDTO, String projectId) {
        ResponseDTO<String> responseDTO = new ResponseDTO<>();
        try {
            responseDTO = mscFeignService.createMessage(sendMessageDTO);
        } catch (Exception e) {
            log.error("指控措施提交待办发送失败发送人：{}，发送业务id：{}",CurrentUserHelper.getCurrentUserId(),JSONUtil.toJsonStr(ids));
        }
        String messageId = responseDTO.getResult();
        if (StrUtil.isNotBlank(messageId)){
            UpdateMsgUrlDTO updateMsgUrlDTO = new UpdateMsgUrlDTO();
            updateMsgUrlDTO.setMessageId(messageId);
            updateMsgUrlDTO.setUrl(String.format("/pms/menuComponents?id=%s=quality_control_item&qualityControlItemMessageId=%s",projectId,messageId));
            try {
                mscFeignService.batchUpdateMessageUrl(Collections.singletonList(updateMsgUrlDTO));
            } catch (Exception e) {
                log.error("指控措施提交待办Url修改失败参数 ：{}", JSONUtil.toJsonStr(updateMsgUrlDTO));
            }
            List<QualityItemMessage> messages = new ArrayList<>();
            ids.forEach(q -> {
                QualityItemMessage message = new QualityItemMessage();
                message.setMessageId(messageId);
                message.setFinish(FinishEnum.NO.getCode());
                message.setQualityItemId(q);
                messages.add(message);
            });
            messageMapper.insertBatch(messages);
        }
    }

    private void sendMessage(List<String> ids, SendMessageDTO sendMessageDTO) {
        ResponseDTO<String> responseDTO = new ResponseDTO<>();
        try {
            responseDTO = mscFeignService.createMessage(sendMessageDTO);
        } catch (Exception e) {
            log.error("指控措施提交待办发送失败发送人：{}，发送业务id：{}",CurrentUserHelper.getCurrentUserId(),JSONUtil.toJsonStr(ids));
        }
        String messageId = responseDTO.getResult();
        if (StrUtil.isNotBlank(messageId)){
            List<QualityItemMessage> messages = new ArrayList<>();
            ids.forEach(q -> {
                QualityItemMessage message = new QualityItemMessage();
                message.setMessageId(messageId);
                message.setFinish(FinishEnum.NO.getCode());
                message.setQualityItemId(q);
                messages.add(message);
            });
            messageMapper.insertBatch(messages);
        }
    }

    private void completeSendMessage(List<String> ids, String projectId){
        LambdaQueryWrapperX<QualityItem> wrapperX = new LambdaQueryWrapperX<>(QualityItem.class);
        wrapperX.eq(QualityItem::getProjectId, projectId)
                .notIn(QualityItem::getId,ids);
        List<QualityItem> qualityItems = mapper.selectList(wrapperX);
        Project project = projectRepository.selectById(projectId);

        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("$projectName$",project.getName());
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .recipientIdList(Collections.singletonList(project.getPm()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessData(JSONUtil.toJsonStr(ids))
                .businessId(ids.get(0))
                .platformId(project.getPlatformId())
                .orgId(CurrentUserHelper.getOrgId())
                .businessNodeCode(MessageNodeConstant.QUALITY_ITEM_SCHEME_COMPLETE)
                .build();
        if (CollectionUtil.isEmpty(qualityItems)){
            // 直接把所有的状态变为已完成了
            sendMessage(ids,sendMessageDTO);
            return;
        }
        List<QualityItem> noComplete = qualityItems.stream().filter(q -> !QualityItemStatusEnum.COMPLETE.getCode().equals(q.getStatus()) && !QualityItemStatusEnum.AFFIRM.getCode().equals(q.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(noComplete)){
            // 剩余的措施项已经已完成了的
            List<String> selectIds = qualityItems.stream().map(QualityItem::getId).collect(Collectors.toList());
            ids.addAll(selectIds);
            sendMessage(ids,sendMessageDTO);
        }
    }

    private List<QualityItem> getByProjectId(String projectId, String id){
        LambdaQueryWrapperX<QualityItem> wrapperX = new LambdaQueryWrapperX<>(QualityItem.class);
        wrapperX.eq(QualityItem::getProjectId,projectId);
        wrapperX.ne(QualityItem::getId,id);
        wrapperX.eq(QualityItem::getRelevanceScheme,RelevanceSchemeEnum.NO);
        List<QualityItem> qualityItems = mapper.selectList(wrapperX);
        return qualityItems;
    }
}
