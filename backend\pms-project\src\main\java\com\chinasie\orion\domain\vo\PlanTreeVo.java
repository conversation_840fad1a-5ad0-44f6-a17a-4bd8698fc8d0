package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/15:34
 * @description:
 */
@Data
public class PlanTreeVo implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "编码")
    private String number;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 预计开始时间
     */
    @ApiModelProperty(value = "预计开始时间")
    private Date planPredictStartTime;

    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private String scheduleName;


    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private BigDecimal schedule;

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划类型Id")
    private String planType;
    @ApiModelProperty(value = "计划类型名称")
    private String planTypeName;

    /**
     * 预计结束时间
     */
    @ApiModelProperty(value = "预计结束时间")
    private Date planPredictEndTime;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    @ApiModelProperty(value = "预期工时(小时)")
    private BigDecimal manHour;

    @ApiModelProperty(value = "前置任务名称")
    private String taskName;
    @ApiModelProperty(value = "前置任务id")
    private String taskId;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;
    @ApiModelProperty(value = "优先级名称")
    private String priorityLevelName;
    /**
     * 优先级别
     */
    @ApiModelProperty(value = "优先级别")
    private String priorityLevel;
    @ApiModelProperty("名字")
    private String name;
    @ApiModelProperty("是否有子节点")
    private boolean isLeaf = false;
    @ApiModelProperty("子节点集合")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PlanTreeVo> children;
    @ApiModelProperty("排序字段")
    private Long sort;
    private String prefix;
    @ApiModelProperty("类名称")
    private String className;
    @ApiModelProperty("创建人ID")
    private String creatorId;
    @ApiModelProperty("创建人名字")
    private String creatorName;
    @ApiModelProperty("拥有者Id")
    private String ownerId;
    @ApiModelProperty("拥有者名字")
    private String ownerName;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改人ID")
    private String modifyId;
    @ApiModelProperty("修改人名字")
    private String modifyName;
    @ApiModelProperty("修改时间")
    private Date modifyTime;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;


    public void addChildren(PlanTreeVo baseTreeNode) {
        if (this.children == null) {
            this.setChildren(new ArrayList());
        }
        this.getChildren().add(baseTreeNode);
    }


    /**
     * 管理节点
     */
    @ApiModelProperty(value = "管理节点")
    private String manageNode;

    /**
     * 风险项
     */
    @ApiModelProperty(value = "风险项")
    private String riskItem;

    /**
     * 进度状态
     */
    @ApiModelProperty(value = "进度状态")
    private Integer speedStatus;

    /**
     * 进度状态
     */
    @ApiModelProperty(value = "进度状态")
    private String speedStatusName;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String resOrg;
    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String resOrgName;

    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    private List<String> joinOrgs;

    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    private String joinOrgsName;
    private String joinOrg;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resDept;
    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resDeptName;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUser;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUserName;

    /**
     * 参与科室
     */
    @ApiModelProperty(value = "参与科室")
    private List<String> joinDepts;

    private String joinDeptsName;


    private String joinDept;

    /**
     * 状态id
     */
    @ApiModelProperty(value = "状态id")
    @TableField(value = "status_id")
    private String statusId;

    /**
     * 版本信息
     */
    @ApiModelProperty(value = "版本信息")
    private String version;

    /**
     * 基线ID
     */
    @ApiModelProperty(value = "基线ID")
    @TableField(value = "base_line_id")
    private String baseLineId;


    /**
     * 原计划Id
     */
    @ApiModelProperty(value = "原计划Id")
    @TableField(value = "old_id")
    private String oldId;


    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "scheme_number")
    private String schemeNumber;

    /**
     *
     */
    @ApiModelProperty(value = "icon")
    @TableField(value = "icon")
    private String icon;

    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;

    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "parent_chain")
    private String parentChain;


    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "node_type")
    private String nodeType;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    @TableField(value = "rsp_sub_dept")
    private String rspSubDept;

    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "rsp_section_id")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;
    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    @TableField(value = "circumstance")
    private Integer circumstance;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    @TableField(value = "scheme_desc")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    @TableField(value = "top_sort")
    private Integer topSort;

    @ApiModelProperty(value = "执行情况说明")
    @TableField(value = "execute_desc")
    private String executeDesc;

    @ApiModelProperty(value = "计划下发时间")
    @TableField(value = "issue_time")
    private Date issueTime;


    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    @TableField(value = "delay_end_Flag")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    @TableField(value = "delay_end_reason")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    @TableField(value = "process_flag")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "process_id")
    private String processId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;




    @ApiModelProperty(value = "下达人")
    private String issuedUser;
    @ApiModelProperty(value = "参与人（拼接）")
    private String participantUsers;
    @ApiModelProperty(value = "参与人集合")
    private List<String> participantUserList;

    @ApiModelProperty(value = "参与人名称（拼接）")
    private String participantUserNames;

}
