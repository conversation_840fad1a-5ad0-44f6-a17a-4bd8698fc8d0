package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QuestionReplyManagement Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
@TableName(value = "pmsx_question_reply_management")
@ApiModel(value = "QuestionReplyManagementEntity对象", description = "问题答复")
@Data

public class QuestionReplyManagement extends ObjectEntity implements Serializable {

    /**
     * 问题编号
     */
    @ApiModelProperty(value = "问题编号")
    @TableField(value = "question_number")
    private String questionNumber;

    /**
     * 答复详情
     */
    @ApiModelProperty(value = "答复详情")
    @TableField(value = "reply")
    private String reply;

}
