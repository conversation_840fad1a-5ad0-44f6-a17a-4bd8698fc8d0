package com.chinasie.orion.management.controller;

import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.management.domain.vo.PurchaseRequestNumVO;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * NcfFormpurchaseRequest 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@RestController
@RequestMapping("/ncfFormpurchaseRequest")
@Api(tags = "采购申请主表")
public class NcfFormpurchaseRequestController {

    @Autowired
    private NcfFormpurchaseRequestService ncfFormpurchaseRequestService;
    @Autowired
    private LyraFileBO fileBo;
    @Autowired
    private FileApiService fileApiService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请主表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormpurchaseRequestVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormpurchaseRequestVO rsp = ncfFormpurchaseRequestService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormpurchaseRequestDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormpurchaseRequestDTO.name}}】", type = "采购申请主表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception {
        String rsp = ncfFormpurchaseRequestService.create(ncfFormpurchaseRequestDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormpurchaseRequestDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormpurchaseRequestDTO.name}}】", type = "采购申请主表", subType = "编辑", bizNo = "{{#ncfFormpurchaseRequestDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestService.edit(ncfFormpurchaseRequestDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购申请主表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购申请主表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请主表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestVO>> pages(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestVO> rsp = ncfFormpurchaseRequestService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据采购立项号查询立项数据
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据采购立项号查询立项数据")
    @LogRecord(success = "【{USER{#logUserId}}】根据采购立项号查询立项数据", type = "采购申请主表", subType = "根据采购立项号查询立项数据", bizNo = "")
    @RequestMapping(value = "/getByCodePage", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestVO>> getByCodePage(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestVO> rsp = ncfFormpurchaseRequestService.getByCodePage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "采购立项立项基本信息")
    @LogRecord(success = "【{USER{#logUserId}}】采购立项立项基本信息", type = "采购申请主表", subType = "采购立项立项基本信息", bizNo = "")
    @RequestMapping(value = "/base/getByCodePage", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestVO>> getBaseByCodePage(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestVO> rsp = ncfFormpurchaseRequestService.getBaseByCodePage(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 根据申请单编号查询采购项目实施及采购合同执行
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据申请单编号查询采购项目实施及采购合同执行")
    @LogRecord(success = "【{USER{#logUserId}}】根据申请单编号查询采购项目实施及采购合同执行", type = "采购申请主表", subType = "根据申请单编号查询采购项目实施及采购合同执行", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<PurchaseRequestNumVO> getByCode(@RequestBody NcfFormpurchaseRequestDTO dto) throws Exception {
        PurchaseRequestNumVO rsp = ncfFormpurchaseRequestService.getByCode(dto);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询总数及金额
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询总数及金额")
    @LogRecord(success = "【{USER{#logUserId}}】查询总数及金额", type = "采购申请主表", subType = "查询总数及金额", bizNo = "")
    @RequestMapping(value = "/getNumMoney", method = RequestMethod.POST)
    public ResponseDTO<Map<String, Object>> getNumMoney(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {
        Map<String, Object> map = ncfFormpurchaseRequestService.getNumMoney(pageRequest);
        return new ResponseDTO<>(map);
    }


    @ApiOperation("采购申请主表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购申请主表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestService.downloadExcelTpl(response);
    }

    @ApiOperation("采购申请主表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购申请主表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormpurchaseRequestService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购申请主表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购申请主表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购申请主表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购申请主表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购申请主表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购申请主表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestService.exportByExcel(pageRequest, response);
    }


    /**
     * 上传附件
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "上传附件")
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】上传了附件", type = "采购申请主表", subType = "上传附件", bizNo = "")
    public void uploadFile(@RequestBody NcfFormpurchaseRequestDTO dto) throws Exception {
        ncfFormpurchaseRequestService.uploadFile(dto);
    }

    /**
     * 查询附件
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询附件")
    @LogRecord(success = "【{USER{#logUserId}}】查询附件", type = "采购申请主表", subType = "查询附件", bizNo = "")
    @RequestMapping(value = "/getFileList", method = RequestMethod.POST)
    public ResponseDTO<List<FileVO>> filePage(@RequestBody NcfFormpurchaseRequestDTO dto) throws Exception {
        List<FileVO> rsp = fileBo.getFilesByDataId(dto.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除附件
     *
     * @param dtos
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除附件")
    @LogRecord(success = "【{USER{#logUserId}}】删除附件", type = "采购申请主表", subType = "删除附件", bizNo = "")
    @RequestMapping(value = "/deleteFileList", method = RequestMethod.POST)
    public ResponseDTO<Boolean> deleteFileList(@RequestBody List<FileDTO> dtos) throws Exception {
        List<String> ids = dtos.stream().map(FileDTO::getId).collect(Collectors.toList());
        return new ResponseDTO<>(fileApiService.deleteByIds(ids));
    }
}
