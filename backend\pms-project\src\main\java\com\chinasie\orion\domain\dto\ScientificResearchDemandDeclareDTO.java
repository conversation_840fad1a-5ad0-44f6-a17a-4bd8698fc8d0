package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ScientificResearchDemandDeclare DTO对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:16:57
 */
@ApiModel(value = "ScientificResearchDemandDeclareDTO对象", description = "科研需求申报")
@Data
public class ScientificResearchDemandDeclareDTO extends ObjectDTO implements Serializable{

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索id")
    private String clueId;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 申报理由
     */
    @ApiModelProperty(value = "申报理由")
    private String declareReason;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = " 开始时间")
    private Date beginTime;

    /**
     * 期望结束时间
     */
    @ApiModelProperty(value = "期望结束时间")
    private Date endTime;

    /**
     * 申报申请单编码
     */
    @ApiModelProperty(value = "申请编码")
    private String number;

    /**
     * 申报名称
     */
    @ApiModelProperty(value = "申报名称")
    private String name;

    /**
     * 申报背景摘要
     */
    @ApiModelProperty(value = "申报背景摘要")
    private String declareBackground;

    /**
     * 申报目标
     */
    @ApiModelProperty(value = "申报目标")
    private String declareTarget;


    /**
     * 申报技术摘要
     */
    @ApiModelProperty(value = "申报技术摘要")
    private String declareTechnology;

    /**
     * 支持性材料
     */
    @ApiModelProperty(value = "申报支持性材料")
    List<FileInfoDTO> fileInfoDTOList;

}

