package com.chinasie.orion.service.quality;



import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.quality.QualityItemSchemeDTO;
import com.chinasie.orion.domain.entity.quality.QualityItemScheme;
import com.chinasie.orion.domain.vo.quality.QualityItemSchemeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * QualityItemScheme 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:47
 */
public interface QualityItemSchemeService extends OrionBaseService<QualityItemScheme> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    QualityItemSchemeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param qualityItemSchemeDTO
     */
    String create(QualityItemSchemeDTO qualityItemSchemeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param qualityItemSchemeDTO
     */
    Boolean edit(QualityItemSchemeDTO qualityItemSchemeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<QualityItemSchemeVO> pages(Page<QualityItemSchemeDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<QualityItemSchemeVO> vos) throws Exception;

}
