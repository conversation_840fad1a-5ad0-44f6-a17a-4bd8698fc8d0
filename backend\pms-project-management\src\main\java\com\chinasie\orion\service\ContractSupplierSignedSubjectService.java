package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.ContractSupplierSignedSubject;
import com.chinasie.orion.domain.dto.ContractSupplierSignedSubjectDTO;
import com.chinasie.orion.domain.vo.ContractSupplierSignedSubjectVO;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ContractSupplierSignedSubject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:50:47
 */
public interface ContractSupplierSignedSubjectService  extends OrionBaseService<ContractSupplierSignedSubject>{
    /**
     *  详情
     *
     * * @param id
     */
    ContractSupplierSignedSubjectVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param contractSupplierSignedSubjectDTO
     */
    String create(ContractSupplierSignedSubjectDTO contractSupplierSignedSubjectDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param contractSupplierSignedSubjectDTO
     */
    Boolean edit(ContractSupplierSignedSubjectDTO contractSupplierSignedSubjectDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ContractSupplierSignedSubjectVO> pages( Page<ContractSupplierSignedSubjectDTO> pageRequest)throws Exception;

    /**
     *  导入校验
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ContractSupplierSignedSubjectVO> vos)throws Exception;

}
