package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 市场合同，数据权限构成 - 创建人部门
 *
 * <AUTHOR>
 * @since 2024年8月28日
 */
@Component
@Slf4j
public class MarketContractCreatorExp implements IExp {

    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "市场合同的公司级过滤规则";
    }

    @Override
    public List<String> exp(String s) {
        final String userId = CurrentUserHelper.getCurrentUserId();
        if (StringUtils.isNotBlank(userId)) {
            return List.of(userId);
        }
        return List.of();
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
