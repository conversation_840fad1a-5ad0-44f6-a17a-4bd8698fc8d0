package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "OpenCostStatistics对象", description = "开口项费用")
@Data
public class OpenCostUserStatistics {
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 基地后勤费
     */
    @ApiModelProperty(value = "基地后勤费")
    @TableField(value = "logisticsAmt")
    private BigDecimal logisticsAmt;

    /**
     * RP体检费
     */
    @ApiModelProperty(value = "RP体检费")
    @TableField(value = "physicalExaminationAmt")
    private BigDecimal physicalExaminationAmt;

    /**
     * 劳保用品费
     */
    @ApiModelProperty(value = "劳保用品费")
    @TableField(value = "laborAmt")
    private BigDecimal laborAmt;

    /**
     * 餐厅管理费
     */
    @ApiModelProperty(value = "餐厅管理费")
    @TableField(value = "restaurantManagementAmt")
    private BigDecimal restaurantManagementAmt;


    /**
     * 其他费
     */
    @ApiModelProperty(value = "其他费")
    @TableField(value = "otherAmt")
    private BigDecimal otherAmt;
}
