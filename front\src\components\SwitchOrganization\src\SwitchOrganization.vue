<template>
  <div>
    <TipsBox>请选择的所属组织，以便于系统更好的为您服务！</TipsBox>
    <div class="pt20 flex flex-ac">
      <div> 所属组织: </div>
      <div class="flex-f1 ml10">
        <Cascader
          v-bind="$attrs"
          class="w-full"
          :field-names="fieldNames"
          placeholder="请选择组织"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { TipsBox } from '/@/components/TipsBox';
import { Cascader } from 'ant-design-vue';

export default defineComponent({
  name: 'SwitchOrganization',
  components: {
    TipsBox,
    Cascader,
  },
  setup() {
    return {
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'children',
      },
    };
  },
});
</script>

<style scoped></style>
