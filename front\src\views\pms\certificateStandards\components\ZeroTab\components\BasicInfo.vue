<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '类型',
      field: 'certificateTypeName',
    },
    {
      label: '名称',
      field: 'name',
    },
    {
      label: '等级',
      field: 'levelName',
    },
    {
      label: '发证机构',
      field: 'issuingAuthority',
    },
    {
      label: '是否需要复审',
      field: 'isNeedRenewal',
    },
    {
      label: '复审年限',
      field: 'renewalYearNum',
    },
    {
      label: '创建人',
      field: 'creatorName',
    },
    {
      label: '修改时间',
      field: 'modifyTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '数据所有者',
      field: 'ownerName',
    },
    {
      label: '创建时间',
      field: 'createTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '修改人',
      field: 'modifyName',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
