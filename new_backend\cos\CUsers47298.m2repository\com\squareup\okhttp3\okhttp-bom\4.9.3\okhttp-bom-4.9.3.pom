<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.squareup.okhttp3</groupId>
  <artifactId>okhttp-bom</artifactId>
  <version>4.9.3</version>
  <packaging>pom</packaging>
  <name>okhttp-bom</name>
  <description>Square’s meticulous HTTP client for Java and Kotlin.</description>
  <url>https://square.github.io/okhttp/</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Square, Inc.</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/square/okhttp.git</connection>
    <developerConnection>scm:git:ssh://**************/square/okhttp.git</developerConnection>
    <url>https://github.com/square/okhttp</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>mockwebserver</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okcurl</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-brotli</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-dnsoverhttps</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-sse</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-tls</artifactId>
        <version>4.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-urlconnection</artifactId>
        <version>4.9.3</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
