package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.RelationJobAssistToOrgDTO;
import com.chinasie.orion.domain.vo.RelationJobAssistToOrgVO;
import com.chinasie.orion.service.RelationJobAssistToOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * RelationJobAssistToOrg 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@RestController
@RequestMapping("/relationJobAssistToOrg")
@Api(tags = "作业专业协助关系表")
public class  RelationJobAssistToOrgController  {

    @Autowired
    private RelationJobAssistToOrgService relationJobAssistToOrgService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【专业协助】信息【{{#id}}】", type = "RelationJobAssistToOrg", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<RelationJobAssistToOrgVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        RelationJobAssistToOrgVO rsp = relationJobAssistToOrgService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param relationJobAssistToOrgDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【专业协助】数据【{{#id}}】", type = "RelationJobAssistToOrg", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody RelationJobAssistToOrgDTO relationJobAssistToOrgDTO) throws Exception {
        String rsp =  relationJobAssistToOrgService.create(relationJobAssistToOrgDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param relationJobAssistToOrgDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【专业协助】数据【{{#relationJobAssistToOrgDTO.id}}】", type = "RelationJobAssistToOrg", subType = "编辑", bizNo = "{{#relationJobAssistToOrgDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  RelationJobAssistToOrgDTO relationJobAssistToOrgDTO) throws Exception {
        Boolean rsp = relationJobAssistToOrgService.edit(relationJobAssistToOrgDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【专业协助】数据【{{#id}}】", type = "RelationJobAssistToOrg", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = relationJobAssistToOrgService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【专业协助】数据【{{#ids.toString()}}}】", type = "RelationJobAssistToOrg", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = relationJobAssistToOrgService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【专业协助】数据", type = "RelationJobAssistToOrg", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<RelationJobAssistToOrgVO>> pages(@RequestBody Page<RelationJobAssistToOrgDTO> pageRequest) throws Exception {
        Page<RelationJobAssistToOrgVO> rsp =  relationJobAssistToOrgService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
