<script lang="ts" setup>
import {
  computed, onMounted, Ref,
  ref,
} from 'vue';
import {
  BasicButton,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';
import { message, TabPane, Tabs } from 'ant-design-vue';
import Api from '/@/api';
import { getColumns } from '../columns';

const props = defineProps<{
  powerData: {
    type: Object,
    default: () => ({}),
  },
  inComePlanId?: () => string,
  isControl?: () => boolean,
  cb?: () => void
}>();

const centerRef = ref(null);
const managementRef = ref(null);
const activeKey = ref<string>('');
const selectionKeys = ref([]);
const selectedRows = ref([]);
const loadingUnlock: Ref<boolean> = ref(false);
const loadingLock: Ref<boolean> = ref(false);
const size = ref('small');
const powerData = props.powerData;

const panes = ref(computed(() => {
  if (props.isControl) {
    return [
      {
        key: 'center',
        name: '中心数据管理',
      },
      {
        key: 'management',
        name: '所级数据管理',
      },
    ];
  }
  return [
    {
      key: 'management',
      name: '所级数据管理',
    },
  ];
}));

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const valuesList = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition.values));

    // 如果需要将所有 values 合并成一个扁平的数组
    const flattenedValues = valuesList.flat();
    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: {
        incomePlanId: props.inComePlanId,
        searchValue: flattenedValues[0],
      },
    };
  }
  return {
    pageNum: params.pageNum,
    pageSize: params.pageSize,
    query: {
      incomePlanId: props.inComePlanId,
    },
  };
}

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectionKeys.value = _keys;
    },
    getCheckboxProps: (record: Record<string, any>) => ({
      disabled: record.isFlag === '0',
    }),
  },
  showSmallSearch: true,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  maxHeight: 300,
  smallSearchField: [
    't.lock_status',
    't1.name',
    't2.name',
  ],
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams || [], activeKey.value === 'center' ? 'incomePlanDataLock/expertiseCenterPages' : 'incomePlanDataLock/expertiseStationPages', 'POST');
  },
};

onMounted(() => {
  activeKey.value = props.isControl ? 'center' : 'management';
});

function onTabChange() {
  selectionKeys.value = [];
  selectedRows.value = [];
  updateTable();
}

function paramsRows(type) {
  return selectedRows.value.map((item) => ({
    id: item.id,
    expertiseCenter: item.expertiseCenter,
    expertiseStation: item.expertiseStation,
    lockStatus: type === '1' ? 'unlock' : 'lock_down',
    lockStatusName: type === '1' ? '未锁定' : '已锁定',
    lockType: item.lockType,
    incomePlanId: item.incomePlanId,
  }));
}

// 定义专业中心编辑-所函数
async function handleLock(type) {
  if (type === '2') {
    loadingLock.value = true;
  } else {
    loadingUnlock.value = true;
  }
  const params = paramsRows(type);
  if (activeKey.value === 'center') {
    try {
      const result = await new Api('/pms/incomePlanDataLock').fetch(params, 'editCenter', 'PUT');
      if (result) {
        loadingUnlock.value = false;
        loadingLock.value = false;
        message.success(type === '1' ? '解锁成功' : '锁定成功');
        props.cb?.();
        closeAll();
      }
    } catch (error) {
      loadingUnlock.value = false;
      loadingLock.value = false;
    }
  } else {
    try {
      const result = await new Api('/pms/incomePlanDataLock').fetch(params, 'editStation', 'PUT');
      if (result) {
        loadingUnlock.value = false;
        loadingLock.value = false;
        message.success(type === '1' ? '解锁成功' : '锁定成功');
        props.cb?.();
        closeAll();
      }
    } catch (error) {
      loadingUnlock.value = false;
      loadingLock.value = false;
    }
  }
}

// 定义 handleCancel 函数
function handleCancel() {
  closeAll();
}

// 定义 closeAll 函数
function closeAll() {
  openModal.closeAll();
}

function updateTable() {
  if (activeKey.value === 'center') {
    centerRef.value?.reload();
  } else {
    managementRef.value?.reload();
  }
}

</script>
<template>
  <div class="manage-box">
    <Tabs
      v-model:activeKey="activeKey"
      :animated="false"
      :size="size"
      class="act-end"
      @change="onTabChange"
    >
      <TabPane
        v-for="pane in panes"
        :key="pane.key"
        :tab="pane.name"
      >
        <OrionTable
          :ref="pane.key"
          :options="options"
          :columns="getColumns(activeKey)"
        />
      </TabPane>
    </Tabs>
    <div class="document-footer">
      <div class="btnStyle">
        <BasicButton @click="handleCancel">
          取消
        </BasicButton>
        <BasicButton
          :disabled="!selectedRows.length || selectedRows && selectedRows?.[0]?.lockStatus === 'unlock'"
          type="primary"
          icon="fa-unlock"
          :loading="loadingUnlock"
          @click="handleLock('1')"
        >
          取消锁定
        </BasicButton>
        <BasicButton
          :disabled="!selectedRows.length || selectedRows && selectedRows?.[0]?.lockStatus === 'lock_down'"
          type="primary"
          icon="fa-lock"
          :loading="loadingLock"
          @click="handleLock('2')"
        >
          锁定数据
        </BasicButton>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.manage-box{
  padding: 0 0 5px;
  position: fixed;
  top: 58px;
  width: 100%;
  z-index: 99;
  :deep(.ant-tabs-nav .ant-tabs-tab-active){
    font-size: 14px !important;
  }
  :deep(.ant-tabs-nav){
    width: 50%;
    margin-top: -47px;
    position: absolute;
    padding: 0 20px;
    border: none;
  }
  :deep(.ant-tabs-top>.ant-tabs-nav:before){
    border: none;
  }
}
.document-footer {
  position: fixed;
  bottom: 3px;
  left: 0;
  z-index: 9999;
  height: 44px;
  background: #fff;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
