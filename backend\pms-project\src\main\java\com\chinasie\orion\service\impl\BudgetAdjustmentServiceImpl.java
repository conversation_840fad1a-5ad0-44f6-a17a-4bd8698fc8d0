package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.domain.dto.BudgetAdjustmentDTO;
import com.chinasie.orion.domain.entity.BudgetAdjustment;
import com.chinasie.orion.domain.entity.BudgetApplication;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetAdjustmentMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetAdjustmentService;
import com.chinasie.orion.service.BudgetManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * BudgetAdjustment 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@Service
@Slf4j
public class BudgetAdjustmentServiceImpl extends OrionBaseServiceImpl<BudgetAdjustmentMapper, BudgetAdjustment> implements BudgetAdjustmentService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private UserRedisHelper userRedisHelper;


    @Autowired
    private BudgetManagementService budgetManagementService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetAdjustmentVO detail(String id, String pageCode) throws Exception {
        BudgetAdjustment budgetAdjustment = this.getById(id);
        BudgetAdjustmentVO result = BeanCopyUtils.convertTo(budgetAdjustment, BudgetAdjustmentVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param budgetAdjustmentDTO
     */
    @Override
    public String create(BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception {
        BudgetAdjustment budgetAdjustment = BeanCopyUtils.convertTo(budgetAdjustmentDTO, BudgetAdjustment::new);
        this.save(budgetAdjustment);
        String rsp = budgetAdjustment.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param budgetAdjustmentDTO
     */
    @Override
    public Boolean edit(BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception {
        BudgetAdjustment budgetAdjustment = BeanCopyUtils.convertTo(budgetAdjustmentDTO, BudgetAdjustment::new);

        this.updateById(budgetAdjustment);

        String rsp = budgetAdjustment.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetAdjustmentVO> pages(Page<BudgetAdjustmentDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetAdjustment> condition = new LambdaQueryWrapperX<>(BudgetAdjustment.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetAdjustment::getCreateTime);


        Page<BudgetAdjustment> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetAdjustment::new));

        PageResult<BudgetAdjustment> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BudgetAdjustmentVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetAdjustmentVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetAdjustmentVO::new);
        if (CollUtil.isEmpty(vos)) {
            return pageResult;
        }
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算调整表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetAdjustmentDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BudgetAdjustmentExcelListener excelReadListener = new BudgetAdjustmentExcelListener();
        EasyExcel.read(inputStream, BudgetAdjustmentDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetAdjustmentDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算调整表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetAdjustment> budgetAdjustmentes = BeanCopyUtils.convertListTo(dtoS, BudgetAdjustment::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetAdjustment-import::id", importId, budgetAdjustmentes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetAdjustment> budgetAdjustmentes = (List<BudgetAdjustment>) orionJ2CacheService.get("pmsx::BudgetAdjustment-import::id", importId);
        log.info("预算调整表导入的入库数据={}", JSONUtil.toJsonStr(budgetAdjustmentes));

        this.saveBatch(budgetAdjustmentes);
        orionJ2CacheService.delete("pmsx::BudgetAdjustment-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetAdjustment-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetAdjustment> condition = new LambdaQueryWrapperX<>(BudgetAdjustment.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetAdjustment::getCreateTime);
        List<BudgetAdjustment> budgetAdjustmentes = this.list(condition);

        List<BudgetAdjustmentDTO> dtos = BeanCopyUtils.convertListTo(budgetAdjustmentes, BudgetAdjustmentDTO::new);

        String fileName = "预算调整表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetAdjustmentDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<BudgetAdjustmentVO> vos) throws Exception {
        List<String> ids = vos.stream().map(BudgetAdjustmentVO::getBudgetId).collect(Collectors.toList());
        List<BudgetManagementVO> managementVOS = budgetManagementService.getudgetManagementVOList(ids);
        Map<String, BudgetManagementVO> map = managementVOS.stream().collect(Collectors.toMap(BudgetManagementVO::getId, e -> e));
        vos.forEach(vo -> {
            if (ObjectUtil.isNotEmpty(map.get(vo.getBudgetId()))) {
                BudgetManagementVO budgetManagementVO = map.get(vo.getBudgetId());
                if (vo.getIsChange().equals("0")) {
                    vo.setName(budgetManagementVO.getName());
                    vo.setNumber(budgetManagementVO.getNumber());
                    vo.setCostCenterId(budgetManagementVO.getCostCenterId());
                    vo.setCostCenterName(budgetManagementVO.getCostCenterName());
                    vo.setExpenseSubjectName(budgetManagementVO.getExpenseSubjectName());
                    vo.setExpenseSubjectNumber(budgetManagementVO.getExpenseSubjectNumber());
                    vo.setTimeTypeName(budgetManagementVO.getTimeTypeName());
                    vo.setCurrency(budgetManagementVO.getCurrency());
                    vo.setCurrencyName(budgetManagementVO.getCurrencyName());
                    vo.setBudgetTime(budgetManagementVO.getBudgetTime());
                    vo.setBudgetObjectId(budgetManagementVO.getBudgetObjectId());
                    vo.setBudgetObjectType(budgetManagementVO.getBudgetObjectType());
                    vo.setBudgetObjectTypeName(budgetManagementVO.getBudgetObjectTypeName());
                }
                vo.setTimeType(budgetManagementVO.getTimeType());
            }
        });

    }

    @Override
    public List<BudgetAdjustmentVO> getList(String formId) throws Exception {
        List<BudgetAdjustment> budgetAdjustments = this.list(new LambdaQueryWrapperX<>(BudgetAdjustment.class)
                .eq(BudgetAdjustment::getFormId, formId)
                .orderByAsc(BudgetAdjustment::getBudgetId).orderByAsc(BudgetAdjustment::getIsChange));
        List<BudgetAdjustmentVO> vos = BeanCopyUtils.convertListTo(budgetAdjustments, BudgetAdjustmentVO::new);
        setEveryName(vos);
        return vos;
    }


    @Override
    public List<BudgetAdjustmentVO> getDetailList(String formId) throws Exception {
        List<BudgetAdjustment> budgetAdjustments = this.list(new LambdaQueryWrapperX<>(BudgetAdjustment.class)
                .eq(BudgetAdjustment::getFormId, formId)
                .eq(BudgetAdjustment::getIsChange, 0)
                .orderByDesc(BudgetAdjustment::getBudgetId).orderByDesc(BudgetAdjustment::getCreateTime));
        List<BudgetAdjustmentVO> vos = BeanCopyUtils.convertListTo(budgetAdjustments, BudgetAdjustmentVO::new);
        if (CollUtil.isEmpty(vos)) {
            return vos;
        }
        setEveryName(vos);
        return vos;
    }

    @Override
    public Boolean saveBatchBudgetAdjustment(List<BudgetAdjustmentDTO> budgetAdjustmentDTOs) throws Exception {
        List<BudgetAdjustment> budgetAdjustments = BeanCopyUtils.convertListTo(budgetAdjustmentDTOs, BudgetAdjustment::new);
        Boolean result = this.updateBatchById(budgetAdjustments);
        return result;
    }

    public static class BudgetAdjustmentExcelListener extends AnalysisEventListener<BudgetAdjustmentDTO> {

        private final List<BudgetAdjustmentDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetAdjustmentDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BudgetAdjustmentDTO> getData() {
            return data;
        }
    }


}
