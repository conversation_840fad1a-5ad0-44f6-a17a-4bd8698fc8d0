<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, openFile, OrionTable, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { Popover } from 'ant-design-vue';
import { InAndOutSiteProps } from '/@/views/pms/overhaulManagement/utils';
import { get as _get } from 'lodash-es';

const props = defineProps<{
  record: InAndOutSiteProps
}>();

const emits = defineEmits<{
  (e: 'updateButton', isDisabled: boolean): void
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '人员信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'nature',
    component: 'Input',
    label: '人员性质',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '员工号',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '姓名',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'sex',
    component: 'Input',
    label: '性别',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'companyName',
    component: 'Input',
    label: '公司',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '部门',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'instituteName',
    component: 'Input',
    label: '研究所',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'nowPosition',
    component: 'Input',
    label: '现任职务',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'politicalAffiliation',
    component: 'Input',
    label: '政治面貌',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
];

const inScheme: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '入场管理信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  // {
  //   field: 'isBasePermanent',
  //   label: '是否常驻基地',
  //   component: 'Input',
  //   colProps: {
  //     span: 12,
  //   },
  //   componentProps: {
  //     disabled: true,
  //   },
  // },
  // {
  //   field: 'baseName',
  //   label: '常驻基地名称',
  //   component: 'Input',
  //   colProps: {
  //     span: 12,
  //   },
  //   componentProps: {
  //     disabled: true,
  //   },
  //   ifShow: ({ model }) => _get(model, 'isBasePermanent'),
  // },
  // {
  //   field: 'baseDate',
  //   label: '常驻基地日期',
  //   component: 'DatePicker',
  //   colProps: {
  //     span: 12,
  //   },
  //   componentProps: {
  //     placeholder: '请选择申请时间',
  //     valueFormat: 'YYYY-MM-DD',
  //     style: {
  //       width: '100%',
  //     },
  //   },
  //   ifShow: ({ model }) => _get(model, 'isBasePermanent'),
  // },
  {
    field: 'actInDate',
    component: 'DatePicker',
    label: '实际入场日期',
    required: computed(() => !isBasePermanent.value),
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'mainWorkCenter',
    component: 'Input',
    label: '主工作中心',
    componentProps({ formModel }) {
      return {
        disabled: true,
      };
    },
  },
  {
    field: 'designCtrlZoneOp',
    component: 'Select',
    label: '涉及控制区作业',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'heightStr',
    component: 'Input',
    label: '身高/米',
    required: true,
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'weightStr',
    component: 'Input',
    label: '体重/千克',
    required: true,
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'chemicalToxinUseJob',
    component: 'Select',
    label: '化学品/毒物使用或接触作业',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'isJoinYearMajorRepair',
    component: 'Select',
    label: '一年内参与过集团内大修',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'isHeightMeasurePerson',
    component: 'Select',
    label: '高剂量人员(年个人剂量>8mSv为高剂量人员)',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'newcomer',
    component: 'Select',
    label: '是否新人',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
      onChange: async (val) => {
        await updateSchema({
          field: 'contactUserName',
          componentProps: {
            disabled: !val,
          },
        });
      },
    },
  },
  {
    field: 'contactUserName',
    component: 'Input',
    label: '接口人',
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'isBasePermanent',
    component: 'Select',
    label: '是否基地常驻',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'certificate',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    colSlot: 'certificate',
  },
];

const outScheme: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '离场管理信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'actOutDate',
    component: 'DatePicker',
    label: '实际离场日期',
    required: computed(() => !isBasePermanent.value),
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'leaveReason',
    component: 'SelectDictVal',
    label: '离场原因',
    required: true,
    componentProps: {
      dictNumber: 'pms_out_factory_reason',
    },
  },
  {
    field: 'isFinishOutHandover',
    component: 'Select',
    label: '是否完成离场工作交接、离场WBC测量（必要时）',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps() {
      return {
        onChange(value: boolean) {
          emits('updateButton', !value);
        },
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'isAgainIn',
    component: 'Select',
    label: '是否再次入场',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps({ formModel }) {
      return {
        onChange() {
          formModel.inAndOutDate = [];
        },
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'inAndOutDateList',
    component: 'RangePicker',
    label: '计划入场离场时间',
    required: computed(() => !isBasePermanent.value),
    ifShow({ model }) {
      return model.isAgainIn;
    },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, clearValidate, updateSchema,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [...schemas, ...(props.record.operationType === 'in' ? inScheme : props.record.operationType === 'out' ? outScheme : [])],
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);
const isBasePermanent: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/person-mange').fetch('', props?.record?.id, 'GET');
    isBasePermanent.value = !!result?.isBasePermanent;
    if (props.record.operationType === 'in') {
      await updateSchema({
        field: 'contactUserName',
        componentProps: {
          disabled: !result?.newcomer,
        },
      });
    }
    await setFieldsValue({
      ...result,
    });
    await clearValidate();
  } finally {
    loading.value = false;
  }
}

// 获取指定人员的证书
const tableOptions = computed(() => ({
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  api: () => new Api(`/pms/basic-user-certificate/user/certificate/list?userCode=${props.record?.userCode}`).fetch('', '', 'POST'),
  columns: [
    {
      title: '证书名称',
      dataIndex: 'certificateName',
    },
    {
      title: '证书类型',
      dataIndex: 'certificateTypeName',
      width: 120,
    },
    {
      title: '证书等级',
      dataIndex: 'certificateLevelName',
      width: 80,
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '获取日期',
      dataIndex: 'obtainDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '复查日期',
      dataIndex: 'reviewDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '证书信息',
      dataIndex: 'fileVOList',
      width: 80,
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '查看'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
}));

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params: Record<string, any> = {
      ...formValues,
      id: props.record.id,
      jobId: props.record.jobId,
    };

    return new Promise((resolve, reject) => {
      switch (props.record.operationType) {
        case 'in':
          new Api('/pms/person-mange').fetch(params, 'edit', 'PUT').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
        case 'out':
          new Api('/pms/person-mange/leave').fetch({
            ...params,
          }, '', 'PUT').then(() => {
            resolve('');
          }).catch((e) => {
            reject(e);
          });
          break;
      }
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  >
    <template #certificate>
      <BasicCard
        :isBorder="false"
        :isSpacing="false"
        :isContentSpacing="false"
        title="证书信息"
      >
        <div style="height: 200px;overflow: hidden">
          <OrionTable
            ref="tableRef"
            :options="tableOptions"
          />
        </div>
      </BasicCard>
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
