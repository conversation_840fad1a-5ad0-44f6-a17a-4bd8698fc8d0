//package com.chinasie.orion.domain.dto.file;
//
//import io.swagger.annotations.ApiModelProperty;
//
//import javax.validation.constraints.NotEmpty;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2021/07/06/14:44
// * @description:
// */
//public class FileDto extends RevisionClassDTO {
//
//
//    /**
//     * ID
//     */
//    @ApiModelProperty(value = "ID")
//    private String id;
//
//    /**
//     * 数据Id
//     */
//    @NotEmpty(message = "数据Id不能为空")
//    @ApiModelProperty(value = "数据Id")
//    private String dataId;
//
//    /**
//     * 文件大小
//     */
//    @ApiModelProperty(value = "文件大小")
//    private Long fileSize;
//
//    /**
//     * 文件路径
//     */
//    @ApiModelProperty(value = "文件路径")
//    private String filePath;
//    /**
//     * pdfPath
//     */
//    @ApiModelProperty(value = "文件的pdf路径")
//    private String pfdPath;
//    /**
//     * 文件后缀
//     */
//    @ApiModelProperty(value = "文件后缀")
//    private String filePostfix;
//
//    /**
//     * 文件工具
//     */
//    @ApiModelProperty(value = "文件工具")
//    private String fileTool;
//
//    /**
//     * 文件类型
//     */
//    @ApiModelProperty(value = "文件类型")
//    private Integer type;
//
//    /**
//     * 文件父级
//     */
//    @ApiModelProperty(value = "文件父级")
//    private String parentId;
//
//    @ApiModelProperty(value = "保密期限")
//    private String securityLimitName;
//
//    /**
//     * 文件类型
//     */
//    @ApiModelProperty(value = "文件类型")
//    private String code;
//
//    @ApiModelProperty(value = "批注文件路径")
//    private String commentsPath;
//
//    @ApiModelProperty(value = "文件类型")
//    private String dataType;
//
//    @Override
//    public String getId() {
//        return id;
//    }
//
//    @Override
//    public void setId(String id) {
//        this.id = id;
//    }
//
//    public String getDataId() {
//        return dataId;
//    }
//
//    public void setDataId(String dataId) {
//        this.dataId = dataId;
//    }
//
//    public Long getFileSize() {
//        return fileSize;
//    }
//
//    public void setFileSize(Long fileSize) {
//        this.fileSize = fileSize;
//    }
//
//    public String getFilePath() {
//        return filePath;
//    }
//
//    public void setFilePath(String filePath) {
//        this.filePath = filePath;
//    }
//
//    public String getPfdPath() {
//        return pfdPath;
//    }
//
//    public void setPfdPath(String pfdPath) {
//        this.pfdPath = pfdPath;
//    }
//
//    public String getFilePostfix() {
//        return filePostfix;
//    }
//
//    public void setFilePostfix(String filePostfix) {
//        this.filePostfix = filePostfix;
//    }
//
//    public String getFileTool() {
//        return fileTool;
//    }
//
//    public void setFileTool(String fileTool) {
//        this.fileTool = fileTool;
//    }
//
//
//    public Integer getType() {
//        return type;
//    }
//
//    public void setType(Integer type) {
//        this.type = type;
//    }
//
//    public String getParentId() {
//        return parentId;
//    }
//
//    public void setParentId(String parentId) {
//        this.parentId = parentId;
//    }
//
//    public String getSecurityLimitName() {
//        return securityLimitName;
//    }
//
//    public void setSecurityLimitName(String securityLimitName) {
//        this.securityLimitName = securityLimitName;
//    }
//
//    public String getCode() {
//        return code;
//    }
//
//    public void setCode(String code) {
//        this.code = code;
//    }
//
//    public String getCommentsPath() {
//        return commentsPath;
//    }
//
//    public void setCommentsPath(String commentsPath) {
//        this.commentsPath = commentsPath;
//    }
//
//    public String getDataType() {
//        return dataType;
//    }
//
//    public void setDataType(String dataType) {
//        this.dataType = dataType;
//    }
//}
