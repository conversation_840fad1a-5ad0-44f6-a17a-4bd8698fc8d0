package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.constant.MaterialTypeEnum;
import com.chinasie.orion.domain.dto.MaterialManageDTO;
import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.material.OutBoundDTO;
import com.chinasie.orion.domain.entity.JobMaterial;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.entity.MaterialOutManage;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.domain.vo.major.BaseMaterialCountVO;
import com.chinasie.orion.domain.vo.major.BasePersonCountVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.MaterialManageMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.SUPPLIES_TYPE;


/**
 * <p>
 * MaterialManage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@Service
@Slf4j
public class MaterialManageServiceImpl extends OrionBaseServiceImpl<MaterialManageMapper, MaterialManage> implements MaterialManageService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private MaterialOutManageService materialOutManageService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private UserRedisHelper userRedisHelper;


    @Autowired
    private FixedAssetsService fixedAssetsService;

    @Autowired
    private MaterialManageMapper materialManageMapper;

    @Autowired
    private JobNodeStatusService jobNodeStatusService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private DataStatusNBO dataStatusBO;

    private JobMaterialService jobMaterialService;



    @Autowired
    public void setJobMaterialService(JobMaterialService jobMaterialService) {
        this.jobMaterialService = jobMaterialService;
    }
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MaterialManageVO detail(String id, String pageCode) throws Exception {
        MaterialManage materialManage =  materialManageMapper.getInfoById(id);
//        MaterialManage materialManage = this.getById(id);
        MaterialManageVO result = BeanCopyUtils.convertTo(materialManage, MaterialManageVO::new);
        setEveryName(Collections.singletonList(result));

        if(!StrUtil.isEmpty(materialManage.getRspUserNo())){
            SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(materialManage.getRspUserNo());
            result.setRspUserId(simpleUserByCode == null ?"":simpleUserByCode.getId());
        }
        String useUserId = result.getUseUserId();
        if(!StrUtil.isEmpty(materialManage.getUseUserNo())){
            SimpleUser userByCode = userRedisHelper.getSimpleUserByCode(materialManage.getUseUserNo());
            result.setUseUserId(userByCode==null?"":userByCode.getId());
        }
        String number = result.getNumber();
        //封装附件
        FixedAssetsVO fixedAssetsVO = fixedAssetsService.detailByNumber(number);
        if (Objects.nonNull(fixedAssetsVO)){
            List<FileVO> filesByDataId = fileApiService.getFilesByDataId(fixedAssetsVO.getId());
            result.setFixedAssetsFileList(filesByDataId);
        }

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param materialManageDTO
     */
    @Override
    public String create(MaterialManageDTO materialManageDTO) throws Exception {
        MaterialManage materialManage = BeanCopyUtils.convertTo(materialManageDTO, MaterialManage::new);
        if(StringUtils.hasText(materialManage.getRspUserNo())){
            SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(materialManage.getRspUserNo());
            materialManage.setRspUserId(simpleUserByCode == null ?"":simpleUserByCode.getId());
        }
        if(StringUtils.hasText(materialManage.getUseUserNo())){
            SimpleUser userByCode = userRedisHelper.getSimpleUserByCode(materialManage.getUseUserNo());
            materialManage.setUseUserId(userByCode==null?"":userByCode.getId());
        }
        materialManage.setStockNum(materialManageDTO.getInputStockNum());
        this.save(materialManage);

        String rsp = materialManage.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param materialManageDTO
     */
    @Override
    public Boolean edit(MaterialManageDTO materialManageDTO) throws Exception {
        String id =materialManageDTO.getId();
        MaterialManage currentData= materialManageMapper.getInfoById(id);
        if(Objects.isNull(currentData)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"数据不存在");
        }
        if(Objects.equals(currentData.getLogicStatus(),StatusEnum.DELETE.getIndex())
                || Objects.equals(currentData.getStatus(),StatusEnum.DRAFT.getIndex())){
            currentData.setLogicStatus(StatusEnum.ENABLE.getIndex());
            currentData.setActOutDate(null);
            currentData.setIsAgainIn(null);
        }
        if(StringUtils.hasText(materialManageDTO.getRspUserNo())){
            SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(materialManageDTO.getRspUserNo());
            currentData.setRspUserId(simpleUserByCode == null ?"":simpleUserByCode.getId());
        }
        if(StringUtils.hasText(materialManageDTO.getUseUserNo())){
            SimpleUser userByCode = userRedisHelper.getSimpleUserByCode(materialManageDTO.getUseUserNo());
            currentData.setUseUserId(userByCode==null?"":userByCode.getId());
        }
        currentData.setDemandNum(materialManageDTO.getDemandNum());
        currentData.setActOutDate(null);
        currentData.setIsOverdue(materialManageDTO.getIsOverdue());
        currentData.setIsReport(materialManageDTO.getIsReport());
        currentData.setActInDate(materialManageDTO.getActInDate());
        currentData.setRspUserNo(materialManageDTO.getRspUserNo());
        currentData.setRspUserName(materialManageDTO.getRspUserName());
        currentData.setIsReport(materialManageDTO.getIsReport());
        currentData.setStatus(StatusEnum.ENABLE.getIndex());
        currentData.setToolStatus(materialManageDTO.getToolStatus());
        currentData.setMaintenanceCycle(materialManageDTO.getMaintenanceCycle());
        currentData.setProductCode(materialManageDTO.getProductCode());
        currentData.setInDays(this.getInDate(currentData.getInDate()));
        this.updateById(currentData);
        // 检查是否超期
        if(Objects.equals(materialManageDTO.getAssetType(),"pms_fixed_assets")){
            // 检定超期
            fixedAssetsService.updateByNumber(currentData.getNumber(),currentData.getAssetCode()
                    ,currentData.getIsOverdue(),currentData.getIsOverdue(),currentData.getNextVerificationDate());
        }
        // 物资入场 台账数据
        MaterialOutManage materialDestination = new MaterialOutManage();
        BeanCopyUtils.copyProperties(currentData, materialDestination);
        materialDestination.setRspUserNo(materialManageDTO.getRspUserNo());
        materialDestination.setRspUserName(materialManageDTO.getRspUserName());
        materialDestination.setDemandNum(materialManageDTO.getDemandNum());
//        materialDestination.set
        materialDestination.setInDays(currentData.getInDays());
        materialDestination.setIsReport(materialManageDTO.getIsReport());
        materialDestination.setStockNum(materialManageDTO.getInputStockNum());
        materialDestination.setIsOverdue(currentData.getIsOverdue());
        materialDestination.setIsMetering(materialManageDTO.getIsMetering());
        materialDestination.setBaseCode(currentData.getBaseCode());
        Date date= materialManageDTO.getNextVerificationDate();
        if(Objects.nonNull(date)){
            materialDestination.setIsOverdue(date.compareTo(new Date())>0);
        }
        materialDestination.setIsPass(null == currentData.getIsOverdue()?null:!currentData.getIsOverdue());
        materialDestination.setCreatorId(null);
        materialDestination.setCreateTime(null);
        materialDestination.setModifyId(null);
        materialDestination.setModifyTime(null);
        materialDestination.setOwnerId(null);
        materialDestination.setId(null);
        // 插入物资入场记录
        materialDestination.setSourceId(currentData.getId());
        materialDestination.setType(MaterialTypeEnum.INPUT.getKey());
        materialOutManageService.save(materialDestination);

        String jobId= materialManageDTO.getJobId();
        if(StringUtils.hasText(jobId)){
            String baseCode = materialManageDTO.getBaseCode();
            String number=  materialManageDTO.getNumber();
            // 修改生命周期状态
            jobMaterialService.updateMaterialId(jobId,materialDestination.getId(), currentData.getId(),id,number);
            List<String> jobIdList =jobMaterialService.listByMaterialId(currentData.getId());
            if(!CollectionUtils.isEmpty(jobIdList)){
                jobNodeStatusService.setNodeStatusByIdList(jobIdList,"materialJoin");
            }
        }
        LogRecordContext.putVariable("baseCode",materialDestination.getBaseCode());
        return Boolean.TRUE;
    }

//    private void existsData(MaterialManageDTO materialManageDTO) {
//       String number= materialManageDTO.getNumber();
//       String jobId= materialManageDTO.getJobId();
//
//        LambdaQueryWrapperX<JobMaterial> wrapperX = new LambdaQueryWrapperX<>(JobMaterial.class);
//        wrapperX.eq(JobMaterial::getNumber,number);
//        if(StringUtils.hasText(number)){
//            wrapperX.eq(JobMaterial::getNumber,number);
//        }
//        if(StringUtils.hasText(jobId)){
//            wrapperX.eq(JobMaterial::getJobId,jobId);
//        }
//        wrapperX.select(JobMaterial::getJobId);
//
//        cn.hutool.core.lang.Assert.isTrue(jobMaterialService.count(wrapperX)<=0,()->  new BaseException(MyExceptionCode.ERROR_DATA_PARAM));
//
//    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MaterialManageVO> pages(Page<MaterialManageDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MaterialManage> condition = new LambdaQueryWrapperX<>(MaterialManage.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MaterialManage::getCreateTime);
        condition.in(PersonMange::getStatus,Arrays.asList(StatusEnum.ENABLE.getIndex(),StatusEnum.DISABLE.getIndex()));
        MaterialManageDTO  pageRequestQuery  = pageRequest.getQuery();
        if(!Objects.isNull(pageRequestQuery)){
            if(StringUtils.hasText(pageRequestQuery.getBaseCode())){
                condition.eq(MaterialManage::getBaseCode,pageRequestQuery.getBaseCode());
            }
        }
        Page<MaterialManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MaterialManage::new));

        PageResult<MaterialManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MaterialManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MaterialManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MaterialManageVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<MaterialManageVO> vos) throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(SUPPLIES_TYPE);
        Map<String, String> numberToDesc = dictListByCode.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));

        List<String> deptIdList = vos.stream().map(MaterialManageVO::getCostCenter).distinct().collect(Collectors.toList());
        // 这里通过ID获取
        List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(deptIdList);
        Map<String, String> idToName = deptVOList.stream().collect(Collectors.toMap(DeptVO::getDeptCode, DeptVO::getName, (k1, k2) -> k1));

        Map<String,String> numberToName = basePlaceService.allMapSimpleList();
        String orgId = CurrentUserHelper.getOrgId();

        List<DataStatusVO> dataStatusVOList = dataStatusBO.getDataStatusListByClassName(MaterialManage.class.getSimpleName());
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
        vos.forEach(vo -> {
            vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
            vo.setAssetTypeName(numberToDesc.getOrDefault(vo.getAssetType(), ""));
            vo.setBaseName(numberToName.getOrDefault(vo.getBaseCode(),""));
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            // 如果
            String name =  idToName.getOrDefault(vo.getCostCenter(),"");
            vo.setCostCenterName(name);
            if(StrUtil.isEmpty(name) && StringUtils.hasText(vo.getCostCenter())){
                DeptBaseInfoVO deptBaseInfoVO= deptRedisHelper.getDeptBaseInfoByDeptCode(orgId,vo.getCostCenter());
                vo.setCostCenterName(null == deptBaseInfoVO ? "":deptBaseInfoVO.getName());
            }

        });
    }

    @Override
    public Boolean outBound(String id, MaterialOutManageDTO materialOutManageDTO) {

        MaterialManage byId = this.getById(id);
        if (null == byId) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据为空，请刷新后重新填写");
        }
        MaterialOutManage materialDestination = new MaterialOutManage();

        if( null == byId.getStockNum()){
            byId.setStockNum(0);
        }
        Integer outNum = materialOutManageDTO.getOutNum();
        if(null == outNum){
            outNum = 0;
        }
        if(byId.getStockNum()  < outNum){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "出库数量大于库存数据量，请修改出库数量");
        }
        byId.setStockNum(outNum);
        byId.setIsAgainIn(materialOutManageDTO.getIsAgainIn());
        BeanCopyUtils.copyProperties(byId,materialDestination);
        int num=materialOutManageDTO.getOutNum();
        String outReason= materialOutManageDTO.getOutReason();
        String destination=materialOutManageDTO.getMaterialDestination();
        String remark =materialOutManageDTO.getRemark();
        materialDestination.setRspUserNo(byId.getRspUserNo());
        materialDestination.setRspUserName(byId.getRspUserName());
        if(StringUtils.isEmpty(byId.getRspUserNo()) && StringUtils.hasText(byId.getRspUserId())){
            UserVO userVO= userRedisHelper.getUserById(byId.getRspUserId());
            if(Objects.nonNull(userVO)){
                materialDestination.setRspUserNo(userVO.getCode());
                materialDestination.setRspUserName(userVO.getName());
            }
        }
        materialDestination.setDemandNum(byId.getDemandNum());
        materialDestination.setIsReport(byId.getIsReport());
        materialDestination.setIsOverdue(byId.getIsOverdue());
        materialDestination.setIsMetering(byId.getIsMetering());
        materialDestination.setOutDate(byId.getOutDate());
        materialDestination.setInDate(byId.getInDate());
        materialDestination.setIsAgainIn(materialOutManageDTO.getIsAgainIn());
        materialDestination.setType(MaterialTypeEnum.OUT.getKey());
        materialDestination.setCreatorId(null);
        materialDestination.setCreateTime(null);
        materialDestination.setModifyId(null);
        materialDestination.setModifyTime(null);
        materialDestination.setOwnerId(null);
        materialDestination.setId(null);
        materialDestination.setOutNum(num);
        materialDestination.setMaterialDestination(destination);
        materialDestination.setOutReason(outReason);
        materialDestination.setActOutDate(materialOutManageDTO.getActOutDate());
        materialDestination.setRemark(remark);
        materialDestination.setSourceId(id);
        Boolean isAgainIn = materialOutManageDTO.getIsAgainIn();
        if (Objects.nonNull(isAgainIn) && isAgainIn) {
            List<Date> dateList=  materialOutManageDTO.getInAndOutDateList();
            if(CollectionUtils.isEmpty(dateList)){
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "入场离场时间不能为空");
            }
            byId.setInDate(dateList.get(0));
            byId.setOutDate(dateList.get(1));
//            byId.setActInDate(null);
//            byId.setActOutDate(null);
        }else{
//            byId.setInDate(null);
//            byId.setOutDate(null);
//            byId.setActInDate(null);
//            byId.setActOutDate(null);
        }
        materialDestination.setDemandNum(byId.getDemandNum());
//        byId.setActOutDate(materialOutManageDTO.getActOutDate());
        this.insertBatch(materialDestination, byId);
        List<String> jobIdList =jobMaterialService.listByMaterialId(id);
        if(!CollectionUtils.isEmpty(jobIdList)){
            jobNodeStatusService.setNodeStatusByIdList(jobIdList,"materialOut");
        }
        LogRecordContext.putVariable("name",byId.getAssetName());
        LogRecordContext.putVariable("number",byId.getNumber());
        LogRecordContext.putVariable("baseCode",byId.getBaseCode());
        return Boolean.TRUE;
    }

    public void insertBatch(MaterialOutManage materialOutManages, MaterialManage materialManages) {
        // 获取锁
        final LockInfo lockInfo = lockTemplate.lock("MaterialOutManage", 30000L, 5000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        // 获取锁成功，处理业务
        try {
           Boolean isAgainIn = materialManages.getIsAgainIn();
            if (Objects.nonNull(isAgainIn) && isAgainIn) {
                materialManages.setStatus(StatusEnum.DISABLE.getIndex());
                this.updateById(materialManages);
            }else{
                materialManages.setStatus(StatusEnum.DRAFT.getIndex());
                this.updateById(materialManages);
//                this.removeById(materialManages.getId());
            }
            materialOutManageService.save(materialOutManages);
            jobMaterialService.updateMaterialLedgerId(materialManages.getId(), materialOutManages.getId());
        }catch (Exception e){
            log.error("物资数据插入失败：{}",e);
            throw  new BaseException(MyExceptionCode.ERROR_PARAM,"物资/台账数据异常");
        }finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
    }

    @Override
    public Boolean outBoundBatch(OutBoundDTO outBoundDTO) {
        List<String> idList = outBoundDTO.getIdList();
        List<MaterialManage> materialManages = this.listByIds(idList);

//        List<MaterialOutManage> materialOutManages = new ArrayList<>();
//        List<MaterialManage> materialManageList = new ArrayList<>();
//        for (MaterialManage materialManage : materialManages) {
//            MaterialOutManage materialDestination = new MaterialOutManage();
//            BeanCopyUtils.copyProperties(materialManage, materialDestination);
//            materialDestination.setOutReason(new Date());
//            materialDestination.setOutDate(outBoundDTO.getOutDate());
//            materialDestination.setMaterialDestination(outBoundDTO.getMaterialDestination());
//            materialDestination.setOutNum(materialManage.getStockNum());
//            materialDestination.setStockNum(0);
//            materialDestination.setCreatorId(null);
//            materialDestination.setCreateTime(null);
//            materialDestination.setModifyId(null);
//            materialDestination.setModifyTime(null);
//            materialDestination.setOwnerId(null);
//            materialDestination.setId(null);
//            materialDestination.setType(MaterialTypeEnum.OUT.getKey());
//            materialOutManages.add(materialDestination);
//            materialManage.setStockNum(0);
//            materialManageList.add(materialManage);
//        }
//        this.insertBatch(materialOutManages, materialManageList);
        return Boolean.TRUE;
    }

    @Override
    public void materialManageDateVerifyHandler() {
        Date date = new Date();
        LambdaQueryWrapperX<MaterialManage> condition = new LambdaQueryWrapperX<>(MaterialManage.class);
        condition.select(MaterialManage::getNumber,MaterialManage::getId,MaterialManage::getIsVerification,MaterialManage::getNextVerificationDate);
        condition.eq(MaterialManage::getIsVerification,Boolean.TRUE);
        List<MaterialManage> materialManages = this.list(condition);
        if(CollectionUtils.isEmpty(materialManages)){
            return;
        }
        materialManages.forEach(item->{
            if(DateUtil.compare(item.getNextVerificationDate(),date)>0){
                item.setIsOverdue(Boolean.FALSE);
            }else{
                item.setIsOverdue(Boolean.TRUE);
            }
        });

        //  只有两种情况 更新只会有两条sql
        // 优先更新 物资库 然后更新 固定资产库
        Map<Boolean, List<String>> map = materialManages.stream().collect(Collectors.groupingBy(MaterialManage::getIsOverdue, Collectors.mapping(MaterialManage::getId, Collectors.toList())));
        LambdaUpdateWrapper<MaterialManage> updateWrapper = new LambdaUpdateWrapper<>(MaterialManage.class);
        for (Map.Entry<Boolean, List<String>> booleanListEntry : map.entrySet()) {
            updateWrapper.clear();
            updateWrapper.set(MaterialManage::getIsOverdue,booleanListEntry.getKey());
            updateWrapper.in(MaterialManage::getId,booleanListEntry.getValue());
            this.update(updateWrapper);
        }
        // todo  1. 对于 固定资产库新增字段 是否超期 更新下次验证日期
        Map<Boolean, List<String>> overdueToList = materialManages.stream().filter(item-> StringUtils.hasText(item.getNumber())).collect(Collectors.groupingBy(MaterialManage::getIsOverdue
                , Collectors.mapping(MaterialManage::getNumber, Collectors.toList())));
        //  只有两种情况 更新只会有两条sql
        for (Map.Entry<Boolean, List<String>> booleanListEntry : overdueToList.entrySet()) {
            fixedAssetsService.updateByList(booleanListEntry.getKey(),booleanListEntry.getValue());
        }

    }

    @Override
    public List<MaterialManageVO> listByIdList(List<String> materialIdList) {
        if(CollectionUtils.isEmpty(materialIdList)){
            return  new ArrayList<>();
        }
        List<MaterialManage> materialManageList= materialManageMapper.listByIdList(materialIdList);
        if(CollectionUtils.isEmpty(materialIdList)){
            return  new ArrayList<>();
        }
        List<MaterialManageVO> manageVOList=  BeanCopyUtils.convertListTo(materialManageList,MaterialManageVO::new);
        manageVOList.forEach(item->{
            item.setOutDays(0);
            List<Date> allDate = new ArrayList<>();
            if(Objects.isNull(item.getInDate()) && Objects.isNull(item.getOutDate()) ){

            }else{
                allDate.add(item.getInDate());
                allDate.add(item.getOutDate());
            }
            if(Objects.equals(item.getStatus(),StatusEnum.DISABLE.getIndex())){
                item.setInDays(getInDate(item.getInDate()));
            }
            item.setInAndOutDateList(allDate);
            Boolean isOverdue=item.getIsOverdue();
            if(Objects.nonNull(isOverdue) && isOverdue){
                item.setIsPass(Boolean.FALSE);
            }else{
                item.setIsPass(Boolean.TRUE);
            }
        });
        return manageVOList;
    }


    public long getInDate(Date inDate) {
        if(!Objects.isNull(inDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(inDate));
            endCalendar.setTime(DateUtil.beginOfDay(new Date()));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return ((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000));

            }else{
                return ( startCalendar.getTimeInMillis() -endCalendar.getTimeInMillis() ) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    public long getOutDate(Date outDate) {
        if(!Objects.isNull(outDate)){
            Calendar startCalendar = Calendar.getInstance();
            Calendar endCalendar = Calendar.getInstance();

            startCalendar.setTime(DateUtil.beginOfDay(new Date()));
            endCalendar.setTime(DateUtil.beginOfDay(outDate));
            if(startCalendar.getTimeInMillis()>endCalendar.getTimeInMillis()){
                return 0;
            }else{
                return (endCalendar.getTimeInMillis() - startCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000);
            }
        }
        return 0;
    }

    @Override
    public Boolean editDate(InAndOutDTO inAndOutDTO) {
        String personManageId = inAndOutDTO.getId();
        LambdaUpdateWrapper<MaterialManage> wrapperX = new LambdaUpdateWrapper<>(MaterialManage.class);
        wrapperX.eq(MaterialManage::getId, personManageId);
        List<Date>  inAndOutDateList =  inAndOutDTO.getInAndOutDateList();
        wrapperX.set(MaterialManage::getInDate, inAndOutDateList.get(0));
        wrapperX.set(MaterialManage::getOutDate, inAndOutDateList.get(1));
        wrapperX.set(MaterialManage::getModifyTime, new Date());
        wrapperX.set(MaterialManage::getModifyId, CurrentUserHelper.getCurrentUserId());
        return this.update(wrapperX);
    }

    @Override
    public MaterialManage getMaterialManageId(String assetType, String assetCode, String number, String baseCode) {
        if(StrUtil.isEmpty(number) ||StrUtil.isEmpty(baseCode)  ){
            return null;
        }
        LambdaQueryWrapperX<MaterialManage> condition = new LambdaQueryWrapperX<>(MaterialManage.class);
        condition.eq(MaterialManage::getNumber,number);
        condition.eq(MaterialManage::getBaseCode,baseCode);
        if(StrUtil.isNotBlank(assetType)){
            condition.eq(MaterialManage::getAssetType,assetType);
        }
        if(StrUtil.isNotBlank(assetCode)){
            condition.eq(MaterialManage::getAssetCode,assetCode);
        }
        condition.select(MaterialManage::getId,MaterialManage::getStatus);
        List<MaterialManage> materialManageList =this.list(condition);
        if(CollectionUtils.isEmpty(materialManageList)){
            return null;
        }
        return   materialManageList.get(0);
    }

    @Override
    public BaseMaterialCountVO countByBaseCode(String baseCode) {
        LambdaQueryWrapperX<MaterialManage>  wrapperX = new LambdaQueryWrapperX<>(MaterialManage.class);
        wrapperX.eq(MaterialManage::getBaseCode,baseCode);
        wrapperX.eq(MaterialManage::getStatus,StatusEnum.ENABLE.getIndex());
        wrapperX.isNotNull(MaterialManage::getActInDate);
        wrapperX.select(MaterialManage::getNumber,MaterialManage::getActInDate,MaterialManage::getId, MaterialManage::getNextVerificationDate);
        List<MaterialManage> personMangeList =  this.list(wrapperX);
        BaseMaterialCountVO baseMaterialCountVO =new BaseMaterialCountVO();
        if(CollectionUtils.isEmpty(personMangeList)){
            return  baseMaterialCountVO;
        }
        baseMaterialCountVO.setMaterialTotal(personMangeList.size());
        Date nowDate = new Date();

        long notValidTotal = personMangeList.stream().filter(item-> Objects.nonNull(item.getNextVerificationDate()))
                .filter(item->DateUtil.compare(item.getNextVerificationDate(),nowDate)<0).count();
        baseMaterialCountVO.setNotValidTotal(Integer.valueOf(String.valueOf(notValidTotal)));
        return baseMaterialCountVO;
    }


}
