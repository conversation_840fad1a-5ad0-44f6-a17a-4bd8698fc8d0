package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BudgetExpendStatisticsVO {

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String expenseSubjectName;


    /**
     * 科目Id
     */
    @ApiModelProperty(value = "科目Id")
    private String expenseSubjectId;

    /**
     * 科目Id
     */
    @ApiModelProperty(value = "父科目Id")
    private String parentId;


    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本中心名称")
    private List<String> costCenterIdNames;


    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本支出编码")
    private List<String> expendNumbers;


    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "预算编码")
    private List<String> budgetNumbers;

    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "预算名称")
    private List<String> budgetNames;


    /**
     * 科目Id
     */
    @ApiModelProperty(value = "支出金额")
    private BigDecimal expendMoney;


    @ApiModelProperty(value = "支出金额")
    private List<BudgetExpendStatisticsVO> children;

    @ApiModelProperty(value = "成本Id")
    private String id;

}
