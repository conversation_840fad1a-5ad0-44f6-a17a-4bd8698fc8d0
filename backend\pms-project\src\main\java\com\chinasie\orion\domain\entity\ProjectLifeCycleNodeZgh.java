package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 生命周期
 */
@Data
@TableName(value = "pms_project_lifecycle_node_zgh")
public class ProjectLifeCycleNodeZgh extends ObjectEntity implements Serializable {

    /**
     * 节点内容.
     */
    @TableField("content")
    private String content;

    /**
     * 节点附件.
     */
    @TableField("project_id")
    private String projectId;
    /**
     * 节点附件.
     */
    @TableField("node_id")
    private String nodeId;


    /**
     * 节点是否有附件
     */
    @TableField("attachment_flag")
    private Boolean attachmentFlag;


}
