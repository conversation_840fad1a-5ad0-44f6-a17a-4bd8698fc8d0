<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <groupId>org.eclipse.jetty</groupId>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>jetty-bom</artifactId>
  <version>9.4.11.v20180605</version>
  <name>Jetty :: Bom</name>
  <description>Jetty BOM artifact</description>
  <url>http://www.eclipse.org/jetty</url>
  <inceptionYear>1995</inceptionYear>
  <packaging>pom</packaging>

  <issueManagement>
    <system>github</system>
    <url>https://github.com/eclipse/jetty.project/issues</url>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache Software License - Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
    <license>
      <name>Eclipse Public License - Version 1.0</name>
      <url>http://www.eclipse.org/org/documents/epl-v10.php</url>
    </license>
  </licenses>

  <properties>
    <jetty.url>http://www.eclipse.org/jetty</jetty.url>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <build-support-version>1.4</build-support-version>
  </properties>

  <scm>
    <connection>scm:git:https://github.com/eclipse/jetty.project.git</connection>
    <developerConnection>scm:git:**************:eclipse/jetty.project.git</developerConnection>
    <url>https://github.com/eclipse/jetty.project</url>
  </scm>

  <distributionManagement>
    <repository>
      <id>oss.sonatype.org</id>
      <name>Jetty Staging Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>oss.sonatype.org</id>
      <name>Jetty Snapshot Repository</name>
      <url>https://oss.sonatype.org/content/repositories/jetty-snapshots/</url>
    </snapshotRepository>
    <site>
      <id>jetty.eclipse.website</id>
      <url>scp://build.eclipse.org:/home/<USER>/httpd/download.eclipse.org/jetty/${project.version}/</url>
    </site>
  </distributionManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5.3</version>
          <configuration>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy</goals>
            <arguments>-Peclipse-release</arguments>
            <preparationGoals>clean install</preparationGoals>
            <mavenExecutorId>forked-path</mavenExecutorId>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.0.1</version>
          <configuration>
            <skipSource>true</skipSource>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.0.0</version>
          <configuration>
            <skip>true</skip>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jstl</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-annotations</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ant</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.cdi</groupId>
        <artifactId>cdi-core</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.cdi</groupId>
        <artifactId>cdi-servlet</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-continuation</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.11.v20180605</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.11.v20180605</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.gcloud</groupId>
        <artifactId>jetty-gcloud-session-manager</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.11.v20180605</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.11.v20180605</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-common</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-hpack</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-http-client-transport</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-spi</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-infinispan</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-hazelcast</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaas</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaspi</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jndi</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.memcached</groupId>
        <artifactId>jetty-memcached-sessions</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-nosql</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-jsp</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-warurl</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-httpservice</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-plus</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-proxy</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-quickstart</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlets</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-spring</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-unixsocket</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-client-impl</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-server-impl</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-api</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-client</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-common</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-server</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-servlet</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>9.4.11.v20180605</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <developers>
    <developer>
      <id>gregw</id>
      <name>Greg Wilkins</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>janb</id>
      <name>Jan Bartel</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>jesse</id>
      <name>Jesse McConnell</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>sbordet</id>
      <name>Simone Bordet</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>1</timezone>
    </developer>
    <developer>
      <id>djencks</id>
      <name>David Jencks</name>
      <email><EMAIL></email>
      <organization>IBM</organization>
      <timezone>-8</timezone>
    </developer>
  </developers>

  <profiles>
    <profile>
      <id>eclipse-release</id>
      <build>
        <plugins>
          <plugin>
            <inherited>true</inherited>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
