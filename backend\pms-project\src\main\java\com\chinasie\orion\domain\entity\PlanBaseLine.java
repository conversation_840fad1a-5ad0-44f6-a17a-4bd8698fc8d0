package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/15:54
 * @description:
 */
@Data
@TableName(value = "pms_plan_base_line")
@ApiModel(value = "BaseLine对象", description = "基线")
public class PlanBaseLine extends ObjectEntity {
    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 计划预计结束时间
     */
    @ApiModelProperty(value = "计划预计结束时间")
    @TableField(value = "plan_predict_end_time")
    private Date planPredictEndTime;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 计划类型名称
     */
    @ApiModelProperty(value = "计划类型名称")
    @TableField(value = "plan_type_name")
    private String planTypeName;

    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "plan_type")
    private String planType;

    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    @TableField(value = "status_name")
    private String statusName;


    /**
     * 责任人ID
     */
    @ApiModelProperty(value = "责任人ID")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "plan_end_time")
    private Date planEndTime;

    /**
     * 状态id
     */
    @ApiModelProperty(value = "状态id")
    @TableField(value = "status_id")
    private String statusId;

    /**
     * 优先级别
     */
    @ApiModelProperty(value = "优先级别")
    @TableField(value = "priority_level")
    private String priorityLevel;

    @ApiModelProperty(value = "优先级名称")
    @TableField(value = "priority_level_name")
    private String priorityLevelName;


    @ApiModelProperty(value = "预期工时(小时)")
    @TableField(value = "man_hour")
    private BigDecimal manHour;

    @ApiModelProperty(value = "前置任务名称（存在多个是‘，’拼接）")
    @TableField(value = "task_name")
    private String taskName;
    @ApiModelProperty(value = "前置任务id（存在多个是‘，’拼接）")
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 版本信息
     */
    @ApiModelProperty(value = "版本信息")
    private String version;

    /**
     * 基线ID
     */
    @ApiModelProperty(value = "基线ID")
    @TableField(value = "base_line_id")
    private String baseLineId;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private BigDecimal schedule;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "plan_start_time")
    private Date planStartTime;

    /**
     * 计划预计开始时间
     */
    @ApiModelProperty(value = "计划预计开始时间")
    @TableField(value = "plan_predict_start_time")
    private Date planPredictStartTime;


    /**
     * 原计划Id
     */
    @ApiModelProperty(value = "原计划Id")
    @TableField(value = "old_id")
    private String oldId;



    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "scheme_number")
    private String schemeNumber;

    /**
     *
     */
    @ApiModelProperty(value = "icon")
    @TableField(value = "icon")
    private String icon;

    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;

    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "parent_chain")
    private String parentChain;


    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "node_type")
    private String nodeType;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    @TableField(value = "rsp_sub_dept")
    private String rspSubDept;

    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "rsp_section_id")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;
    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    @TableField(value = "circumstance")
    private Integer circumstance;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    @TableField(value = "scheme_desc")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    @TableField(value = "top_sort")
    private Integer topSort;

    @ApiModelProperty(value = "执行情况说明")
    @TableField(value = "execute_desc")
    private String executeDesc;

    @ApiModelProperty(value = "计划下发时间")
    @TableField(value = "issue_time")
    private Date issueTime;


    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    @TableField(value = "delay_end_Flag")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    @TableField(value = "delay_end_reason")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    @TableField(value = "process_flag")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "process_id")
    private String processId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;

    @ApiModelProperty(value = "下达人")
    @TableField(value = "issued_user")
    private String issuedUser;
    @ApiModelProperty(value = "参与人")
    @TableField(value = "participant_users")
    private String participantUsers;
}
