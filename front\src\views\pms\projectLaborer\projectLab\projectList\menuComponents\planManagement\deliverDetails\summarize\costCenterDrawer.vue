<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    title="编辑基本信息"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSave(father.type, isGo)"
        >
          提交
        </BasicButton>
      </div>

      <!--      <DrawerFooterButtons-->
      <!--        v-model:checked="checked"-->
      <!--        :loading="loadingBtn"-->
      <!--        @cancel-click="cancel"-->
      <!--        @ok-click="handleSave(father.type, isGo)"-->
      <!--      />-->
    </template>
  </BasicDrawer>
</template>

<script>
import {
  computed,
  h, inject, onBeforeMount, onMounted, reactive, ref, toRefs, unref,
} from 'vue';
import {
  Button, Checkbox, Col, DatePicker, Drawer, Form, Input, message, Row, Select,
} from 'ant-design-vue';
import Api from '/@/api';

import { useRoute } from 'vue-router';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import {
  BasicButton,
  BasicDrawer, BasicForm, SelectUserModal, useDrawerInner, useForm, useModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

export default {
  name: 'Edit',
  components: {
    BasicButton,
    BasicDrawer,
    SelectUserModal,
    BasicForm,
    AInput: Input,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const route = useRoute();
    const [modalRegister, { closeDrawer }] = useDrawerInner((drawerData) => {
      state.father = drawerData.formData;
      init(drawerData.formData.projectId);
      setFieldsValue(drawerData.formData);
    });
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '名称',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 50,
          },
          required: true,
        },

        {
          field: 'principalId',
          component: 'Select',
          label: '责任人:',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
            },
          ],
          componentProps: {
            options: computed(() => state.namesList),
            fieldNames: {
              key: 'id',
              value: 'id',
              label: 'name',
            },
          },
        },

        {
          field: 'predictDeliverTime',
          component: 'DatePicker',
          label: '计划交付时间',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择计划交付时间',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });

    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },

      father: props.data,
      isGo: false,
      loading: false,
      formRef: ref(),
      namesList: [], // 负责人
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
      responsiblerId: '',
    });
    // 项目计划id

    function init(projectId) {
      const url1 = `project-role-user/getListByName/${projectId}?name=`;
      new Api('/pms').fetch('', url1, 'POST').then((res) => {
        state.namesList = res;
      });
    }
    function filterOption(inputValue, treeNode) {
      return treeNode.props.label.includes(inputValue);
    }
    const visibleChange = (val) => {
      if (!val) {
        closeDrawer();
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };

    async function handleSave() {
      let formData = await validateFields();
      let principalName = state.namesList.find((item) => item.id === formData.principalId).name;
      formData.id = state.father.id;
      formData.principalName = principalName;
      formData.remark = formData.remark ? formData.remark : '';
      formData.predictDeliverTime = formData.predictDeliverTime ? dayjs(formData.predictDeliverTime).format('YYYY-MM-DD HH:mm:ss  ') : '';
      new Api('/pms', '')
        .fetch(formData, 'deliverable', 'PUT')
        .then(() => {
          state.loading = false;
          message.success('操作成功');
          emit('updatePage');// 更新父组件数据
          handleClose();
          resetFields();
        })
        .catch(() => {
          state.loading = false;
          resetFields();
          visibleChange(false);
          handleClose();
        });
    }
    function handleClose() {
      closeDrawer();
      emit('submit', false);
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    return {
      ...toRefs(state),
      handleSave,
      handleClose,
      handlePrincipal,
      filterOption,
      modalRegister,
      registerForm,
      selectUserRegister,
      visibleChange,
      closeDrawer,
    };
  },
};
</script>

<style lang="less" scoped>
.drawer-footer {
  position: absolute;
  bottom: 10px;
  width: 88%;
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
