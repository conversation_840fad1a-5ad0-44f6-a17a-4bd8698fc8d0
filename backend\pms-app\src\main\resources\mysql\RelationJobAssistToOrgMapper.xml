<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.RelationJobAssistToOrgMapper">

	<select id="getListByRepairOrgIds" resultType="com.chinasie.orion.domain.vo.JobOrgVO">
		select  distinct po.id,po.job_number as jobNumber ,po.major_repair_org_id as repairOrgId,po2.name as repairOrgName,po2.code as repairOrgCode from   pmsx_relation_job_assist_to_org po
		    inner join   pmsx_major_repair_org  po2 on po.major_repair_org_id=po2.id
		where po2.logic_status =1 and po.logic_status =1 and  po2.id in
		<foreach collection="repairIdList" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</select>



	<select id="getListByJobNumber" resultType="com.chinasie.orion.domain.vo.JobOrgVO">
		select  distinct po.id,po.job_number as jobNumber ,po.major_repair_org_id as repairOrgId,po2.name as repairOrgName,po2.code as repairOrgCode from   pmsx_relation_job_assist_to_org po
		inner join   pmsx_major_repair_org  po2 on po.major_repair_org_id=po2.id
		where po2.logic_status =1 and po.logic_status =1 and  po.job_number = #{jobNumber}
	</select>
	<select id="getListByJobNumberList" resultType="com.chinasie.orion.domain.vo.JobOrgVO">
		select  distinct po.id,po.job_number as jobNumber ,po.major_repair_org_id as repairOrgId,po2.name as repairOrgName,po2.code as repairOrgCode from   pmsx_relation_job_assist_to_org po
																																								inner join   pmsx_major_repair_org  po2 on po.major_repair_org_id=po2.id
		where po2.logic_status =1 and po.logic_status =1 and  po.job_number in
		<foreach collection="jobNumberList" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</select>
</mapper>
