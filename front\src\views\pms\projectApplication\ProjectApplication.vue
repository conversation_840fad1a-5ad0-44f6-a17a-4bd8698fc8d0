<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import {
  computed, h, onMounted, provide, reactive, readonly, Ref, ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { CreateAndEditDrawer } from './components';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const state = reactive({
  userOrgList: [],
});

const route = useRoute();
const router = useRouter();
const tableRef: Ref = ref();
const selectRows: Ref = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  api: (params: Record<string, any>) => {
    let obj: any = {
      ...params,
      power: {
        pageCode: 'PMS0008',
        containerCode: 'PMS_XMSB_container_list',
      },
      query: {
        ...(params.searchConditions?.length && params.searchConditions[0]?.length ? { name: params.searchConditions[0][0].values[0] } : {}),
      },
    };
    if (obj?.searchConditions) {
      delete obj.searchConditions;
    }
    return new Api('/pms/projectDeclare').fetch(obj, 'page', 'POST');
  },
  columns: [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      width: 150,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      minWidth: 150,
      customRender({ record, text }) {
        if (isPower('PMS_XMSB_container_list_look', record?.rdAuthList)) {
          return h('span', {
            class: 'action-btn',
            onClick: () => handleDetail(record),
          }, text);
        }
        return text;
      },
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
      width: 120,
    },
    {
      title: '责任部门',
      dataIndex: 'rspDeptName',
      width: 120,
    },
    {
      title: '项目负责人',
      dataIndex: 'resUserName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 120,
    },
    {
      title: '申请发起日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMSB_container_list_look', record?.rdAuthList),
      onClick: handleDetail,
    },
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => record.status === 101 && isPower('PMS_XMSB_container_list_edit', record?.rdAuthList),
      onClick(record: Record<string, any>) {
        openCreateAndEdit(true, { id: record.id });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => record.status === 101 && isPower('PMS_XMSB_container_list_delete', record?.rdAuthList),
      modal: (record: Record<string, any>) => batchDelete([record.id]),
    },
  ],
};

onMounted(() => {
  reqUserProfile(useUserStore().getUserInfo.id);
});

function handleDetail(record: Record<string, any>) {
  router.push({
    name: 'ProjectApplicationDetail',
    params: {
      id: record.id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

// 批量删除
function batchDelete(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectDeclare').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

async function reqUserProfile(uid: string) {
  if (!uid) return;
  try {
    const {
      orgName, orgId, orgCode, deptName, deptId, deptCode, className, classId, code,
    } = await new Api(`/pmi/user/user-profile/${uid}`).fetch('', '', 'GET');
    state.userOrgList = [
      { // 部门
        label: orgName,
        value: orgId,
        code: orgCode,
      },
      { // 科室
        label: deptName,
        value: deptId,
        code: deptCode,
      },
      { // 班组
        label: className,
        value: classId,
        code,
      },
    ].filter((item) => item.label && item.value);
  } finally {
    // state.loading = false;
  }
}

provide('userOrgList', readonly(computed(() => state.userOrgList)));

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item: Record<string, any>) => item.id)),
  });
}

// 表格勾选回调
function selectionChange({ rows }) {
  selectRows.value = rows;
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMS0008'}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_XMSB_container_header_add']"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openCreateAndEdit(true,{})"
        >
          创建申报
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_XMSB_container_header_delete']"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <!--创建、编辑申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="updateTable"
      @register="registerCreateAndEdit"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
