package com.chinasie.orion.schedule;

import com.chinasie.orion.service.ContractInfoService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 定时修改合同预警日期，合同预警金额  框架合同剩余金额  框架合同已使用金额   支付金额  支付比例
 */
@Component
public class UpdateContractDataXxlJob {
    @Autowired
    private ContractInfoService contractInfoService;

    @XxlJob("UpdateContractDataJobHandler")
    public void changeStatus() throws Exception {
        contractInfoService.updateContractInfo();
    }
}
