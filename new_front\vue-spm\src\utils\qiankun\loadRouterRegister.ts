import { transformObjToRoute } from '/@/router/helper/routeHelper';

/**
 * 消息抄送，因没有菜单权限，访问推送的消息业务详情，会造成404页面业务处理
 * router添加一个方法，通过远程加载页面信息，进行动态路由注册
 * @param router
 */
export function loadRouterRegister(router) {
  /**
   * 加载的路由注册
   * @param routerData 页面路由信息
   */
  router.loadRouterRegister = function (routerData: any[]) {
    let rData = transformObjToRoute(routerData);

    rData.forEach((item) => {
      router.addRoute(item);
    });
  };
}
