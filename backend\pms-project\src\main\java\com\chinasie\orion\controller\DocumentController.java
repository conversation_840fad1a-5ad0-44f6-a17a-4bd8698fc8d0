package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/22 18:04
 * @description:
 */
@RestController
@RequestMapping("/document")
@Api(tags = "文档")
public class DocumentController {

    @Resource
    private DocumentService documentService;

    @ApiOperation("新增文档")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileInfoDTO", dataType = "FileInfoDTO")
    })
    @PostMapping(value = "/create")
    @LogRecord(success = "【{USER{#logUserId}}】新增文档", type = "文档", subType = "新增文档", bizNo = "#{{fileInfoDTO.dataId}}")
    public ResponseDTO<String> saveFileInfo(@RequestBody FileInfoDTO fileInfoDTO) throws Exception {
        return new ResponseDTO<>(documentService.saveFileInfo(fileInfoDTO));
    }

    @ApiOperation("批量新增文档")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileInfoDTOList", dataType = "List")
    })
    @PostMapping(value = "/saveBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量新增文档", type = "文档", subType = "批量新增文档", bizNo = "#{{fileInfoDTOList.dataId}}")
    public ResponseDTO<List<String>> saveBatchAdd(@RequestBody List<FileInfoDTO> fileInfoDTOList) throws Exception {
        return new ResponseDTO<>(documentService.saveBatchAdd(fileInfoDTOList));
    }

    @ApiOperation("文档分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】文档分页", type = "文档", subType = "文档分页", bizNo = "")
    public ResponseDTO<Page<DocumentVO>> getDocumentVOPage(@RequestBody Page<FileInfoDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(documentService.getDocumentVOPage(pageRequest));
    }

    @ApiOperation("文档分页--新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getDocumentVOPage/new")
    @LogRecord(success = "【{USER{#logUserId}}】文档分页--新", type = "文档", subType = "文档分页--新", bizNo = "")
    public ResponseDTO<PageResult<DocumentVO>> getDocumentVOPageNew(@RequestBody PageRequest<FileInfoDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(documentService.getDocumentVOPage1(pageRequest));
    }

    @ApiOperation("文档分页(没有文档类型)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getDocumentPage")
    @LogRecord(success = "【{USER{#logUserId}}】文档分页(没有文档类型)", type = "文档", subType = "文档分页(没有文档类型)", bizNo = "")
    public ResponseDTO<PageResult<DocumentVO>> getDocumentPage(@RequestBody Page<FileInfoDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(documentService.getDocumentPage(pageRequest));
    }


    @ApiOperation("文档列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataId", dataType = "String"),
            @ApiImplicitParam(name = "fileInfoQueryDTO", dataType = "FileInfoQueryDTO")
    })
    @PostMapping("/getList/{dataId}")
    @LogRecord(success = "【{USER{#logUserId}}】文档列表", type = "文档", subType = "文档列表", bizNo = "#{{dataId}}")
    public ResponseDTO<List<DocumentVO>> getDocumentList(@PathVariable("dataId") String dataId, @RequestBody(required = false) FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        return new ResponseDTO<>(documentService.getDocumentList(dataId, fileInfoQueryDTO));
    }

    @ApiOperation("批量编辑文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileDtoList", dataType = "List")
    })
    @PutMapping("/editBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量编辑文件", type = "文档", subType = "批量编辑文件", bizNo = "")
    public ResponseDTO<Boolean> updateBatchDocument(@RequestBody List<FileDTO> fileDtoList) throws Exception {
        return new ResponseDTO<>(documentService.updateBatchDocument(fileDtoList));
    }

    @ApiOperation("编辑文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileDto", dataType = "FileDto")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑文件", type = "文档", subType = "编辑文件", bizNo = "#{{fileDto.dataId}}")
    public ResponseDTO<Boolean> updateDocument(@RequestBody FileDTO fileDto) throws Exception {
        return new ResponseDTO<>(documentService.updateDocument(fileDto));
    }

    @ApiOperation("批量删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileIdList", dataType = "List")
    })
    @DeleteMapping("/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除文件", type = "文档", subType = "批量删除文件", bizNo = "#{{fileIdList}}")
    public ResponseDTO<Boolean> deleteBatchFile(@RequestBody List<String> fileIdList) throws Exception {
        return new ResponseDTO<>(documentService.deleteBatchFile(fileIdList));
    }


    @ApiOperation("文档列表通过数据ID列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", dataType = "List")
    })
    @PostMapping("/getListByIdList")
    @LogRecord(success = "【{USER{#logUserId}}】文档列表通过数据ID列表", type = "文档", subType = "文档列表通过数据ID列表", bizNo = "#{{idList}}")
    public ResponseDTO<List<DocumentVO>> getDocumentListByIdList(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(documentService.getDocumentListByIdList(idList));
    }

    @ApiOperation("批量删除文件通过数据id")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataIds", dataType = "List")
    })
    @DeleteMapping("/remove/byDataIds")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除文件通过数据id", type = "文档", subType = "批量删除文件通过数据id", bizNo = "#{{dataIds}}")
    public ResponseDTO<Boolean> deleteBatchFileByDataIds(@RequestBody List<String> dataIds) throws Exception {
        return new ResponseDTO<>(documentService.deleteBatchFileByDataIds(dataIds));
    }


    @ApiOperation("签入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileDto", dataType = "FileDto")
    })
    @PutMapping("/file/checkIn")
    @LogRecord(success = "【{USER{#logUserId}}】签入", type = "文档", subType = "签入", bizNo = "#{{fileDto.dataId}}")
    public ResponseDTO<Boolean> fileCheckIn(@RequestBody FileDTO fileDto) throws Exception {
        return new ResponseDTO(documentService.fileCheckIn(fileDto));
    }

    @ApiOperation("签出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileDto", dataType = "FileDto")
    })
    @PutMapping("/file/moveOut")
    @LogRecord(success = "【{USER{#logUserId}}】签出", type = "文档", subType = "签出", bizNo = "#{{fileDto.dataId}}")
    public ResponseDTO<Boolean> fileMoveOut(@RequestBody FileDTO fileDto) throws Exception {
        return new ResponseDTO(documentService.fileMoveOut(fileDto));
    }

    /**
     * 生成项目档案
     *
     * @param projectId
     * @param templateId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "生成项目档案")
    @RequestMapping(value = "/generateDocument", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】生成项目档案", type = "文档", subType = "生成项目档案", bizNo = "#{{projectId}}")
    public ResponseDTO<Boolean> generateDocument(@RequestParam String projectId, @RequestParam String templateId) throws Exception {
        Boolean rsp = documentService.generateDocument(projectId, templateId);
        return new ResponseDTO<>(rsp);
    }
}
