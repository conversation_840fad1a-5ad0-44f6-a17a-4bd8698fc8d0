package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectOrderOtherDTO;
import com.chinasie.orion.management.domain.entity.ProjectOrderOther;
import com.chinasie.orion.management.domain.vo.ProjectOrderOtherVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectOrderOther 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
public interface ProjectOrderOtherService extends OrionBaseService<ProjectOrderOther> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectOrderOtherVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectOrderOtherDTO
     */
    String create(ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectOrderOtherDTO
     */
    Boolean edit(ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectOrderOtherVO> pages(Page<ProjectOrderOtherDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectOrderOtherVO> vos) throws Exception;

    /**
     * 根据number查询订单其他信息
     * <p>
     * * @param number
     */
    ProjectOrderOtherVO getByNumber(String number) throws Exception;
}
