package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.dto.ContractMainDTO;
import com.chinasie.orion.domain.dto.EstablishmentContractPlanDTO;
import com.chinasie.orion.domain.dto.IssuedDTO;
import com.chinasie.orion.domain.entity.ContractMain;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;
import com.chinasie.orion.domain.vo.ContractInfoVO;
import com.chinasie.orion.domain.vo.ContractMainVO;
import com.chinasie.orion.domain.vo.LaborCostStatisticsVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * ContractMain 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
public interface ContractMainService  extends  OrionBaseService<ContractMain>  {


        /**
         *  详情
         *
         * * @param id
         */
    ContractMainVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param contractMainDTO
         */
        String create(ContractMainDTO contractMainDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param contractMainDTO
         */
        Boolean edit(ContractMainDTO contractMainDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<IssuedDTO> params)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ContractMainVO> pages( Page<ContractMainDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ContractMainVO> vos)throws Exception;

        /**
        * 下发合同计划
        * @param params 参数
        */
        void issuedByIds(List<IssuedDTO> params);

        /**
        *  判断是中心或者部门
        * @param id 部门id
        * @return 结果
        */
        String judge(String id);

        /**
        *  编制合同计划
        * @param establishmentContractPlanDTO 参数
        */
        void establishment(EstablishmentContractPlanDTO establishmentContractPlanDTO);

        /**
        * 获取合同信息
        * @param year 年份
        * @param contractNumber 合同编号
        * @return 结果
        */
        ContractInfoVO contractInfoDetail(Integer year, String contractNumber);


        /**
        * 获取中心用人计划 （中心分组）
        * @param contractCenterPlanListDTO 参数
        * @return 结果
        */
        Map<String, List<ContractCenterPlanVO>> groupCenterPlan(ContractCenterPlanListDTO contractCenterPlanListDTO);

        /**
        * 开启下年录入
        * @param contractMainIds 合同计划id
        */
        void beginNextYear(List<String> contractMainIds);

        /**
        * 开启调整
        * @param contractMainIds 参数
        */
        void beginEdit(List<String> contractMainIds);

        /**
        * 获取表头
        * @return 结果
        */
        List<Map<String,Object>> getSheetHeads();

    /**
     * 根据中心code和合同编号和年份获取计划详情
     * @param code 中心code
     * @param contractNumber 合同编号
     * @param year 年份
     * @return 结果
     */
    List<ContractCenterPlanVO> listByCode(String code, String contractNumber, Integer year);


    /**
     * 人力成本数据统计
     * @param contractNumber
     * @param year
     */
    LaborCostStatisticsVO laborCostStatistics(String contractNumber, Integer year);
}
