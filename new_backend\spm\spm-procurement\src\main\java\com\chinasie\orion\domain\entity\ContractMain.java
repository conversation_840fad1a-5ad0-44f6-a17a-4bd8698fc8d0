package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractMain Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@TableName(value = "pmsx_contract_main")
@ApiModel(value = "ContractMainEntity对象", description = "合同计划主表")
@Data

public class ContractMain extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private Date year;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    @TableField(value = "contract_satus")
    private Integer contractSatus;

    @ApiModelProperty(value = "是否开启了下年录入")
    @TableField(value = "is_has_next")
    private Integer hasNext;

    @ApiModelProperty(value = "部门id")
    @TableField(exist = false)
    private String deptId;



}
