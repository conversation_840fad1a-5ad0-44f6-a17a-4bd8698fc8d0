<script lang="ts">
import { defineComponent, ref } from 'vue';
import { Select } from 'ant-design-vue';

export default defineComponent({
  name: 'EditBarSelectMultiple',
  components: { Select },
  props: {
    labels: {
      type: Array,
      default: () => [],
    },
    values: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const visible = ref(false);
    const dropdownVisible = ref(false);
    const values_:any = ref(JSON.parse(JSON.stringify(props.values)));
    // 失去焦点的时回调
    function handleBlur() {
      visible.value = false;
    }
    function handleMouseleave() {
      if (!dropdownVisible.value) {
        visible.value = false;
      }
    }
    function dropdownVisibleChange(bool) {
      dropdownVisible.value = bool;
    }

    function handleChange(values) {
      const labels:any = props.options.filter((item) => values.some((item_) => item_ === item.value))
        .map((item) => item.label);
      emit('change', {
        values,
        labels,
      });
    }
    return {
      visible,
      values_,
      handleBlur,
      handleMouseleave,
      dropdownVisibleChange,
      handleChange,
    };
  },

});
</script>

<template>
  <div
    v-if="isEdit"
    style="width: 100%;min-height: 30px;"
    @mouseover="visible=true"
    @click.stop
  >
    <Select
      v-if="visible"
      v-model:value="values_"
      mode="multiple"
      :maxTagCount="1"
      :maxTagTextLength="8"
      placeholder="请选择"
      style="width: 100%"
      :options="options"
      @mouseleave="handleMouseleave"
      @blur="handleBlur"
      @dropdownVisibleChange="dropdownVisibleChange"
      @change="handleChange"
    />
    <div v-else>
      {{ labels.join(',') }}
    </div>
  </div>
  <div v-else>
    {{ labels.join(',') }}
  </div>
</template>

<style scoped lang="less">

</style>
