package com.chinasie.orion.constant;

/**
 * 验收单状态枚举.
 *
 * <AUTHOR>
 */
public enum AcceptanceFormStatusEnum {
    CREATED(101, "已创建"),
    CHECK_AND_ACCEPTED(140, "已验收");

    private Integer status;

    private String descr;

    AcceptanceFormStatusEnum(Integer status, String descr) {
        this.status = status;
        this.descr = descr;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescr() {
        return descr;
    }

    public static AcceptanceFormStatusEnum getStatus(String value) {
        try {
            return valueOf(value);
        } catch (Exception e) {
            return null;
        }
    }
}
