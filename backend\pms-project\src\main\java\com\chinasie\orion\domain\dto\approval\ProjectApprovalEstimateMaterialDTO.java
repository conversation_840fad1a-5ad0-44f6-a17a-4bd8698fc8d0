package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateMaterial DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateMaterialDTO对象", description = "概算物料")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateMaterialDTO extends ObjectDTO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateMaterialDTO> {

    /**
     * 物料数量
     */
    @ApiModelProperty(value = "物料数量")
    @ExcelProperty(value = "物料数量 ", index = 0)
    private Integer materialAmount;

    /**
     * 物料价格
     */
    @ApiModelProperty(value = "物料价格")
    @ExcelProperty(value = "物料价格 ", index = 1)
    private BigDecimal materialPrice;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @ExcelProperty(value = "父级id ", index = 2)
    private String parentId;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @ExcelProperty(value = "需求数量 ", index = 3)
    private Integer requiredNum;

    /**
     * 材料概算
     */
    @ApiModelProperty(value = "材料概算")
    @ExcelProperty(value = "材料概算 ", index = 4)
    private BigDecimal amount;


    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @ExcelProperty(value = "物料id ", index = 5)
    private String materialId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @ExcelProperty(value = "项目立项id ", index = 6)
    private String projectApprovalId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 7)
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 移动平均价格
     */
    @ApiModelProperty(value = "移动平均价格")
    private BigDecimal averagePrice;

    /**
     * 价格单位
     */
    @ApiModelProperty(value = "价格单位")
    private String priceUnit;

    /**
     * 子项
     */
    @ApiModelProperty(value = "子项")
    private List<ProjectApprovalEstimateMaterialDTO> children;

}
