package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectApprovalRiskPlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 16:10:46
 */
@TableName(value = "pms_project_approval_risk_plan")
@ApiModel(value = "ProjectApprovalRiskPlanEntity对象", description = "项目立项风险策划")
@Data

public class ProjectApprovalRiskPlan extends  ObjectEntity  implements Serializable{


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 立项ID
     */
    @ApiModelProperty(value = "立项ID")
    @TableField(value = "approve_id")
    private String approveId;

    /**
     * 风险ID
     */
    @ApiModelProperty(value = "风险ID")
    @TableField(value = "risk_id")
    private String riskId;

    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    @TableField(value = "risk_probability")
    private String riskProbability;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    @TableField(value = "risk_influence")
    private String riskInfluence;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    @TableField(value = "risk_type")
    private String riskType;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    @TableField(value = "solutions")
    private String solutions;

    /**
     * 文档ID
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;

}
