package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectSchemeApplyApprovalService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ProjectSchemeApplyApprovalController
 *
 * @author: yangFy
 * @date: 2023/4/20 15:01
 * @description
 * <p>
 *
 * </p>
 */
@RestController
@RequestMapping("/projectScheme")
@Api(tags = "项目计划申请审批")
public class ProjectSchemeApplyApprovalController {
    @Resource
    private ProjectSchemeApplyApprovalService schemeApplyApprovalService;

    @ApiOperation("计划调整")
    @PostMapping(value = "/modify")
    @LogRecord(success = "【{USER{#logUserId}}】计划调整", type = "项目计划申请审批", subType = "计划调整", bizNo = "")
    public ResponseDTO<Boolean> modify(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        Boolean rsp = schemeApplyApprovalService.modify(projectSchemeDTO);
        return ResponseDTO.success(rsp);
    }


    @ApiOperation("计划调整——同意")
    @PostMapping(value = "/agree/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】计划调整——同意", type = "项目计划申请审批", subType = "计划调整——同意", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> agree(@PathVariable("id") String id, @RequestBody ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception {
        Boolean rsp = schemeApplyApprovalService.agree(id, projectSchemeApplyApproval);
        return ResponseDTO.success(rsp);
    }

    @ApiOperation("计划调整——驳回")
    @PostMapping(value = "/reject/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】计划调整——驳回", type = "项目计划申请审批", subType = "计划调整——驳回", bizNo = "{{#id}}")
    public ResponseDTO<List<String>> reject(@PathVariable("id") String id, @RequestBody ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception {
        Boolean rsp = schemeApplyApprovalService.reject(id, projectSchemeApplyApproval);
        return ResponseDTO.success(rsp);
    }

    @ApiOperation("计划调整内容详情")
    @GetMapping(value = "/apply/getDetail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】计划调整内容详情", type = "项目计划申请审批", subType = "计划调整内容详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeVO> getDetail(@PathVariable("id") String id) throws Exception {
        ProjectSchemeVO schemeVO = schemeApplyApprovalService.getDetail(id);
        return ResponseDTO.success(schemeVO);
    }
}
