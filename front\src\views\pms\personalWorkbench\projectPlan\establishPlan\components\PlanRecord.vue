<template>
  <BasicDrawer
    v-model:visible="$props.visible"
    title="计划记录"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
    @close="() => handleClose()"
  >
    <div class="drawer-body">
      <p class="title">
        计划详情
      </p>
      <a-row
        v-for="(val, index) in planDetailRows"
        :key="index"
        :gutter="16"
      >
        <a-col
          v-for="item in val"
          :key="item.value"
          :span="item.span"
          class="col"
        >
          <div class="label">
            {{ item.label }}：
          </div>
          <div class="value">
            {{ detailData[item.value] }}
          </div>
        </a-col>
      </a-row>
      <BasicButton
        class="add-btn"
        type="primary"
        icon="add"
        @click="() => addNewData()"
      >
        添加记录
      </BasicButton>
      <a-form
        ref="formRef"
        :model="form"
        layout="vertical"
      >
        <a-form-item
          v-for="(val, index) in form.record"
          :key="val.key"
          label="计划记录:"
          :name="['record', index, 'value']"
          required
          :rule="recordRule"
        >
          <div class="flex">
            <a-textarea
              v-model:value="val.value"
              placeholder="请输入记录"
            />
            <span
              v-if="form.record.length > 1"
              class="action-btn"
              @click="() => deleteItem(index)"
            >删除</span>
          </div>
        </a-form-item>
      </a-form>
      <div
        v-for="val in detailData['schemeContentVOList'] || []"
        :key="val.id"
        class="record-content"
      >
        <div class="time">
          {{ dayjs(val['createTime']).format('YYYY-MM-DD HH:mm:ss') }}
        </div>
        <div class="content">
          {{ val.content }}
        </div>
        <BasicButton
          v-if="useUserStore()?.getUserInfo?.id===val?.creatorId"
          class="del-btn"
          type="link"
          @click="handleDelRecord(val)"
        >
          删除
        </BasicButton>
      </div>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  Col, Form, FormItem, message, Modal, Row, Textarea,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  components: {
    ARow: Row,
    ACol: Col,
    AForm: Form,
    AFormItem: FormItem,
    ATextarea: Textarea,
    BasicButton,
    BasicDrawer,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['close', 'update'],
  setup(props, context) {
    const submitLoading = ref(false);
    const data = ref({
      projectId: '',
      id: '',
    });
    const relationList = ref(['']);
    const values = ref([]);
    const detailData = ref({});

    const planDetailRows = reactive([
      [
        {
          label: '计划名称',
          value: 'name',
          span: '12',
        },
        {
          label: '计划父级',
          value: 'parentName',
          span: '12',
        },
      ],
      [
        {
          label: '开始时间',
          value: 'beginTime',
          span: '12',
        },
        {
          label: '结束时间',
          value: 'endTime',
          span: '12',
        },
      ],
      [
        {
          label: '责任部门',
          value: 'rspSubDeptName',
          span: '12',
        },
        {
          label: '责任人',
          value: 'rspUserName',
          span: '12',
        },
      ],
      [
        {
          label: '前置计划关系',
          value: 'hasBefore',
          span: '12',
        },
        {
          label: '前置关系',
          value: 'beforeRelation',
          span: '12',
        },
      ],
      [
        {
          label: '计划描述',
          value: 'schemeDesc',
          span: '24',
        },
      ],
    ]);
    const form = reactive({
      record: [
        {
          value: '',
          key: Date.now(),
        },
      ],
    });
    const recordRule = {
      required: true,
      message: '计划记录不能为空',
      trigger: 'change',
    };

    const formRef = ref(null);

    const [modalRegister, { closeDrawer }] = useDrawerInner(
      (drawerData) => {
        data.value = drawerData.data;
        detailData.value = {
          ...drawerData.data,
        };
        form.record = [
          {
            value: '',
            key: Date.now(),
          },
        ];

        submitLoading.value = false;
        getPlanDetail();
      },
    );
    const addNewData = () => {
      form.record.push({
        value: '',
        key: Date.now(),
      });
    };

    const deleteItem = (index) => {
      form.record.splice(index, 1);
    };

    const handleSubmit = async () => {
      if (submitLoading.value) return;

      const res = await formRef.value.validate();
      if (res) {
        submitLoading.value = true;
        const params = form.record.map((item) => ({
          content: item.value,
          projectId: data.value?.projectId,
          projectSchemeId: data.value.id,
        }));
        const addRes = await new Api('/pms/schemeContent/createBatch').fetch(
          params,
          '',
          'POST',
        );
        if (addRes) {
          message.success('添加成功');
          context.emit('update');
          handleClose();
        }
        submitLoading.value = false;
      }
    };

    const handleClose = () => {
      relationList.value = [];
      values.value = [];
      formRef.value.resetFields();
      closeDrawer();
      context.emit('close');
    };

    const getPlanDetail = async () => {
      const res = await new Api(`/pms/projectScheme/${data.value.id}`).fetch(
        '',
        '',
        'GET',
      );
      detailData.value = {
        ...res,
        parent: res.parentName || '',
        beginTime: dayjs(res.beginTime).format('YYYY-MM-DD'),
        endTime: dayjs(res.endTime).format('YYYY-MM-DD'),
        hasBefore: res.schemePrePostVOList?.length ? '是' : '否',
        beforeRelation: (res.schemePrePostVOList || [])
          .map((item) => item.preSchemeName)
          .join('，'),
      };
    };

    // 删除记录
    async function handleDelRecord(val) {
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除该项目，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            new Api('/pms/schemeContent').fetch([val.id], '', 'delete').then(() => {
              getPlanDetail();
              resolve(true);
            }).catch(() => {
              resolve('');
            });
          });
        },
      });
    }

    return {
      relationList,
      values,
      addNewData,
      deleteItem,
      handleSubmit,
      handleClose,
      planDetailRows,
      detailData,
      form,
      formRef,
      recordRule,
      dayjs,
      modalRegister,
      submitLoading,
      handleDelRecord,
      useUserStore,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-body {
  padding: 22px;
}

.title {
  font-size: 14px;
  font-weight: bold;
}

.add-btn {
  margin-top: 35px;
  bottom: 10px;
}

.col {
  display: flex;
  align-items: center;
  height: 40px;
}

.label {
  min-width: 100px;
}

.label,
.value {
  color: ~`getPrefixVar('primary-10') `;
}

.record-content {
  margin-bottom: 10px;
  display: flex;

  .time {
    flex-shrink: 0;
    width: 160px;
    color: ~`getPrefixVar('text-color-second') `;
  }

  .content {
    color: ~`getPrefixVar('text-color') `;
    line-height: 20px;
    flex-grow: 1;
    width: 0;
  }

  .del-btn{
    margin-left: 30px;
    flex-shrink: 0;
  }

}

.flex {
  display: flex;
  align-items: center;

  .ant-input {
    flex: 1;
  }

  .action-btn {
    width: 50px;
    margin-left: 6px;
    text-align: center;
  }
}

.flex-right {
  display: flex;
  justify-content: right;
}
</style>
