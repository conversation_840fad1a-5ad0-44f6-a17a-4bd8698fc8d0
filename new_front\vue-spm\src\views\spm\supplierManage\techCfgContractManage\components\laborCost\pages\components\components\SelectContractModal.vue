<script setup lang="ts">
import { IOrionTableOptions, OrionTable } from 'lyra-component-vue3';
import { ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  contractNumber: string
}>();

const tableRef = ref();
const selectedRowKeys = ref([]);
const tableOptions: IOrionTableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  rowSelection: {
    onChange(keys: string[]) {
      selectedRowKeys.value = keys;
    },
  },
  api: () => new Api('/spm/contractAssessmentStandard/list').fetch('', props.contractNumber, 'POST'),
  columns: [
    {
      title: '考核类别',
      dataIndex: 'assessmentType',
    },
    {
      dataIndex: 'assessmentContent',
      title: '考核内容',
    },
    {
      title: '评分标准',
      dataIndex: 'standard',
    },
  ],
};
defineExpose({
  getSelectedRowKeys() {
    return selectedRowKeys.value;
  },
});
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
