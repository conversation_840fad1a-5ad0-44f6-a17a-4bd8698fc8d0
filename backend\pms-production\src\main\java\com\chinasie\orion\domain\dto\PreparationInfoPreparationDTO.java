package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotEmpty;

/**
 * PreparationInfoPreparation DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
@ApiModel(value = "PreparationInfoPreparationDTO对象", description = "准备信息维护")
@Data
@ExcelIgnoreUnannotated
public class PreparationInfoPreparationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 组织机构
     */
    @ApiModelProperty(value = "组织机构")
    @ExcelProperty(value = "组织机构 ", index = 0)
    private String orgStructure;

    /**
     * 工单准备
     */
    @ApiModelProperty(value = "工单准备")
    @ExcelProperty(value = "工单准备 ", index = 1)
    private String jobPrepare;

    /**
     * 重大项目评审
     */
    @ApiModelProperty(value = "重大项目评审")
    @ExcelProperty(value = "重大项目评审 ", index = 2)
    private String importantProject;

    /**
     * 参修人员入场
     */
    @ApiModelProperty(value = "参修人员入场")
    @ExcelProperty(value = "参修人员入场 ", index = 3)
    private String partUserJoin;

    /**
     * 关注人员面谈
     */
    @ApiModelProperty(value = "关注人员面谈")
    @ExcelProperty(value = "关注人员面谈 ", index = 4)
    private String likePerson;

    /**
     * 工具入场
     */
    @ApiModelProperty(value = "工具入场")
    @ExcelProperty(value = "工具入场 ", index = 5)
    private String toolJoin;

    /**
     * 安全质量管理
     */
    @ApiModelProperty(value = "安全质量管理")
    @ExcelProperty(value = "安全质量管理 ", index = 6)
    private String safetyQualityEnv;

    /**
     * 工作包
     */
    @ApiModelProperty(value = "工作包")
    @ExcelProperty(value = "工作包 ", index = 7)
    private String jobPackage;

    /**
     * 大修前培训
     */
    @ApiModelProperty(value = "大修前培训")
    @ExcelProperty(value = "大修前培训 ", index = 8)
    private String majorTrain;

    /**
     * 交底演练
     */
    @ApiModelProperty(value = "交底演练")
    @ExcelProperty(value = "交底演练 ", index = 9)
    private String publishDrill;

    /**
     * 后期保障
     */
    @ApiModelProperty(value = "后期保障")
    @ExcelProperty(value = "后期保障 ", index = 10)
    private String rearSupport;

    /**
     * 大修动员会
     */
    @ApiModelProperty(value = "大修动员会")
    @ExcelProperty(value = "大修动员会 ", index = 11)
    private String majorRally;

    /**
     * 大修准备率
     */
    @ApiModelProperty(value = "大修准备率")
    @ExcelProperty(value = "大修准备率 ", index = 12)
    private String majorPrepareRate;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "大修轮次不能为空")
    private String repairRound;


}
