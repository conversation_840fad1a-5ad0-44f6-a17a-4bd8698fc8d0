package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/22/22:22
 * @description:
 */
public enum JobPostAuthorizeEnum {
    NOT_AUTHORIZE(101,"未授权")
    ,WORK_FLOW_ING(110,"流程中")
    ,FINISH(130,"已授权")
    ,EXPIRED(121,"已失效")        ;


    private Integer status;

    private String desc;

    JobPostAuthorizeEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
