<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.MaterialManageMapper">
    <update id="updateStatuByIdList"></update>


    <select id="listByIdList" resultType="com.chinasie.orion.domain.entity.MaterialManage">
       select  `id`, `class_name`,  `status`, `logic_status`, `asset_type`, `asset_code`,
        `stock_num`,`input_stock_num`,`number`, `asset_name`,  `in_date`, `act_out_date`,
        `out_date`,`in_days`,specification_model,`act_out_date`,`act_in_date`,`cost_center_name`,`cost_center`
            from  pmsx_material_manage
    <where>
        <if test="materialIdList != null and materialIdList.size() > 0">
            and `id` in
            <foreach collection="materialIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>


    </select>
    <select id="getInfoById" resultType="com.chinasie.orion.domain.entity.MaterialManage">
        select  `id`, `class_name`, `creator_id`
            , `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`
            , `org_id`, `status`, `logic_status`, `asset_type`, `asset_code`, `number`, `asset_name`
            , `cost_center_name`, `specification_model`, `stock_num`, `next_verification_date`
            , `is_verification`, `base_id`, `rsp_user_no`, `rsp_user_name`, `use_user_no`, `use_user_name`
            , `act_in_date`, `is_metering`, `is_report`, `is_overdue`, `job_no`, `job_name`, `cost_center`
            , `base_code`, `input_stock_num`, `rsp_user_id`, `use_user_id`, `in_date`, `act_out_date`, `out_date`
            , `is_again_in`, `is_available`,`product_code`,`tool_status`,maintenance_cycle from  pmsx_material_manage where  id = #{id}

    </select>
    <select id="getMaterialManageInAndOutDateList" resultType="com.chinasie.orion.domain.dto.source.MaterialInfoDTO" >
        <!--  获取 物资管理 类型标识为1 -->
        select p.id ,p.number,p.in_date,p.out_date,'1' type,status as inOrOut,CONCAT(p.id ,'_',COALESCE(p.in_date, 'in'),'_',COALESCE(p.out_date, 'out')) as 'unique_key'
        from pmsx_material_manage p
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        union all
        <!--  获取 物资管理台账 类型标识为0 -->
        select ml.source_id as id,ml.number,ml.in_date,ml.out_date,'0' type,
        case  ml.type
        when 'input' then 1
        when 'out' then 2
        else ''
        end as inOrOut,
        CONCAT(ml.source_id ,'_',COALESCE(ml.in_date, 'in'),'_',COALESCE(ml.out_date, 'out')) as 'unique_key'
        from pmsx_material_out_manage ml
        where in_date is not null and out_date is not  null  and ml.source_id is not null and ml.source_id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <update id="updateStatusByIdList" >
        update pmsx_material_manage set status = #{status} where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


</mapper>
