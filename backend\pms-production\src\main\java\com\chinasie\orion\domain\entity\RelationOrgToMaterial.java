package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * relationOrgToMaterial Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@TableName(value = "pmsx_relation_org_to_material")
@ApiModel(value = "RelationOrgToMaterialEntity对象", description = "大修组织和大修组织物资关系")
@Data
public class RelationOrgToMaterial extends  ObjectEntity  implements Serializable{

    /**
     * 大修组织id
     */
    @ApiModelProperty(value = "大修组织id")
    @TableField(value = "repair_org_id")
    private String repairOrgId;

    /**
     * 物质管理id
     */
    @ApiModelProperty(value = "物质管理id")
    @TableField(value = "material_id")
    private String materialId;

    /**
     * 物质编码
     */
    @ApiModelProperty(value = "物质编码")
    @TableField(value = "material_number")
    private String materialNumber;


}
