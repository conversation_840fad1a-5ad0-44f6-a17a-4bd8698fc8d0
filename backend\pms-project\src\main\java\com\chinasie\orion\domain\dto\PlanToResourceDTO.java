package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/18/10:56
 * @description:
 */
@ApiModel(value = "PlanToResourceDTO对象", description = "计划对应资源")
public class PlanToResourceDTO extends RelationDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer resourceCount;

    /**
     * 类型（成员/设备/资源）
     */
    @ApiModelProperty(value = "类型（成员/设备/资源）")
    private Integer type;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getResourceCount(){
        return resourceCount;
    }

    public void setResourceCount(Integer resourceCount) {
        this.resourceCount = resourceCount;
    }

    public Integer getType(){
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark(){
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
