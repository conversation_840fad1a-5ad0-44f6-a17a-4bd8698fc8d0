<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.micrometer</groupId>
  <artifactId>micrometer-bom</artifactId>
  <version>1.8.13</version>
  <packaging>pom</packaging>
  <name>micrometer-bom</name>
  <description>Micrometer BOM (Bill of Materials) for managing Micrometer artifact versions</description>
  <url>https://github.com/micrometer-metrics/micrometer</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>shakuzen</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <url>**************:micrometer-metrics/micrometer.git</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jersey2</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-appoptics</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-atlas</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-azure-monitor</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch2</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-datadog</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-dynatrace</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-elastic</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-ganglia</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-graphite</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-health</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-humio</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-influx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-jmx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-kairos</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-new-relic</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-opentsdb</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-signalfx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-statsd</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-wavefront</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-test</artifactId>
        <version>1.8.13</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>io.micrometer#micrometer-bom;1.8.13</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.8.13</nebula_Implementation_Version>
    <nebula_Built_Status>integration</nebula_Built_Status>
    <nebula_Built_By>circleci</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Timezone>Etc/UTC</nebula_Build_Timezone>
    <nebula_Build_Date_UTC>2023-01-10T14:38:44.635903418Z</nebula_Build_Date_UTC>
    <nebula_Build_Date>2023-01-10_14:38:44</nebula_Build_Date>
    <nebula_Gradle_Version>7.6</nebula_Gradle_Version>
    <nebula_Module_Source>/micrometer-bom</nebula_Module_Source>
    <nebula_Module_Origin>**************:micrometer-metrics/micrometer.git</nebula_Module_Origin>
    <nebula_Change>f750900</nebula_Change>
    <nebula_Full_Change>f750900e694124e263e40de7b3af3afbb8261546</nebula_Full_Change>
    <nebula_Branch>f750900e694124e263e40de7b3af3afbb8261546</nebula_Branch>
    <nebula_Build_Host>39cec25a9cbd</nebula_Build_Host>
    <nebula_Build_Job>deploy</nebula_Build_Job>
    <nebula_Build_Number>19644</nebula_Build_Number>
    <nebula_Build_Id>19644</nebula_Build_Id>
    <nebula_Build_Url>https://circleci.com/gh/micrometer-metrics/micrometer/19644</nebula_Build_Url>
    <nebula_Created_By>18.0.2+9 (Eclipse Adoptium)</nebula_Created_By>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
  </properties>
</project>
