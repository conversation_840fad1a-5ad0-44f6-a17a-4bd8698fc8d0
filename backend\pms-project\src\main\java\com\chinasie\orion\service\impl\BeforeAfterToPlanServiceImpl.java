//package com.chinasie.orion.service.impl;
//
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.chinasie.orion.bo.CachePlanBo;
//import com.chinasie.orion.bo.DictBo;
//import com.chinasie.orion.constant.DictConstant;
//import com.chinasie.orion.domain.dto.BeforeAfterParamDto;
//import com.chinasie.orion.domain.dto.BeforeAfterToPlanSimpleDto;
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.dto.PlanQueryDTO;
//import com.chinasie.orion.domain.dto.plan.BeforeParamDto;
//import com.chinasie.orion.domain.entity.BeforeAfterToPlan;
//import com.chinasie.orion.domain.entity.Plan;
//import com.chinasie.orion.domain.vo.*;
//import com.chinasie.orion.exception.PMSErrorCode;
//import com.chinasie.orion.exception.PMSException;
//import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
//import com.chinasie.orion.repository.BeforeAfterToPlanRepository;
//import com.chinasie.orion.repository.PlanRepository;
//import com.chinasie.orion.sdk.domain.vo.business.UserVO;
//import com.chinasie.orion.sdk.helper.UserRedisHelper;
//import com.chinasie.orion.service.BeforeAfterToPlanService;
//import com.chinasie.orion.service.PlanService;
//import com.chinasie.orion.util.BeanCopyUtils;
//import org.springframework.beans.BeanUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.ObjectUtils;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/18/14:31
// * @description:
// */
//@Service
//public class BeforeAfterToPlanServiceImpl extends OrionBaseServiceImpl<BeforeAfterToPlanRepository, BeforeAfterToPlan> implements BeforeAfterToPlanService {
//
//    @Resource
//    private PlanService planService;
//    @Resource
//    private DictBo dictBo;
//
//    @Resource
//    private CachePlanBo cachePlanBo;
//    @Resource
//    private UserRedisHelper userRedisHelper;
//
//
//    @Override
//    public List<BeforeAndAfterPlanVo> list(PlanParam planParam) throws Exception {
//        List<BeforeAndAfterPlanVo> beforeAndAfterPlanVos = new ArrayList<>();
//        String planId = planParam.getPlanId();
//        Integer type = planParam.getType();
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionChainWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        if (type == 1) {
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, planId);
//        } else {
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getFromId, planId);
//        }
//        List<BeforeAfterToPlan> beforeAfterToPlans = this.list(beforeAfterToPlanOrionChainWrapper);
//        if (CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            return beforeAndAfterPlanVos;
//        }
//        Map<String, String> dictValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.BEFORE_AND_AFTER_RELATION);
//        List<String> toIdList = new ArrayList<>();
//        List<String> formIdList = new ArrayList<>();
//        Map<String, Integer> planIdToTypeMap = new HashMap<>();
//        for (BeforeAfterToPlan beforeAfterToPlan : beforeAfterToPlans) {
//            String fromId = beforeAfterToPlan.getFromId();
//            String toId = beforeAfterToPlan.getToId();
//            if (type == 1) {
//                formIdList.add(fromId);
//            } else {
//                toIdList.add(toId);
//            }
//            planIdToTypeMap.put(fromId, beforeAfterToPlan.getType());
//        }
//        List<String> idList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(toIdList)) {
//            idList.addAll(toIdList);
//        }
//        if (!CollectionUtils.isEmpty(formIdList)) {
//            idList.addAll(formIdList);
//        }
//        if (CollectionUtils.isEmpty(idList)) {
//            return beforeAndAfterPlanVos;
//        }
//        PlanQueryDTO planQueryDTO = new PlanQueryDTO();
//        planQueryDTO.setPlanType(planParam.getPlanType());
//        planQueryDTO.setPriorityLevel(planParam.getPriorityLevel());
//        planQueryDTO.setKeyword(planParam.getKeyword());
//        planQueryDTO.setIds(idList);
//        List<PlanDetailVo> listByIdList = planService.getListByIdList(null, planQueryDTO);
//        if (CollectionUtils.isEmpty(listByIdList)) {
//            return beforeAndAfterPlanVos;
//        }
//
//        for (PlanDetailVo planDetailVo : listByIdList) {
//            BeforeAndAfterPlanVo beforeAndAfterPlanVo = new BeforeAndAfterPlanVo();
//            BeanUtils.copyProperties(planDetailVo, beforeAndAfterPlanVo);
//            String id = planDetailVo.getId();
//            Integer integer = planIdToTypeMap.get(id);
//            if (integer != null) {
//                beforeAndAfterPlanVo.setTypeName(dictValueToDesMap.get(integer.toString()));
//            }
//            beforeAndAfterPlanVos.add(beforeAndAfterPlanVo);
//        }
//        return beforeAndAfterPlanVos;
//    }
//
//    @Override
//    public boolean delByIdAndTypeAndFormId(String id, Integer type, String sourceId) throws Exception {
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionChainWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//
//        if (type == 1) {
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getFromId, id);
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, sourceId);
//
//        } else {
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getFromId, sourceId);
//            beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, id);
//        }
//        this.remove(beforeAfterToPlanOrionChainWrapper);
//        this.updateCacheBo(id);
//        return true;
//    }
//
//    @Override
//    public boolean deleteByParam(BeforeAfterParamDto beforeAfterParamDto) throws Exception {
//        List<String> idList = beforeAfterParamDto.getIdList();
//        String sourceId = beforeAfterParamDto.getSourceId();
//        Integer type = beforeAfterParamDto.getType();
//
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        if (type == 1) {
//            beforeAfterToPlanOrionWrapper.in(BeforeAfterToPlan::getFromId, idList);
//            beforeAfterToPlanOrionWrapper.eq(BeforeAfterToPlan::getToId, sourceId);
//        } else {
//            beforeAfterToPlanOrionWrapper.eq(BeforeAfterToPlan::getFromId, sourceId);
//            beforeAfterToPlanOrionWrapper.in(BeforeAfterToPlan::getToId, idList);
//        }
//        this.remove(beforeAfterToPlanOrionWrapper);
//        this.updateCacheBo(sourceId);
//        return true;
//    }
//
//    @Override
//    public List<SimpleVo> getDictList() {
//        return dictBo.getDictValueAndDesList(DictConstant.BEFORE_AND_AFTER_RELATION);
//    }
//
//    @Override
//    public boolean saveByEntity(BeforeAfterToPlanSimpleDto beforeAfterToPlan) throws Exception {
//        Integer type = beforeAfterToPlan.getType();
//        String id = beforeAfterToPlan.getToId();
//        List<String> fromIdList = beforeAfterToPlan.getFromIdList();
//        List<BeforeAfterToPlan> beforeAfterToPlanDTOS = new ArrayList<>();
//        for (String s : fromIdList) {
//            BeforeAfterToPlan beforeAfterToPlanDTO = new BeforeAfterToPlan();
//            beforeAfterToPlanDTO.setToId(id);
//            beforeAfterToPlanDTO.setFromId(s);
//            beforeAfterToPlanDTO.setType(type);
//            beforeAfterToPlanDTOS.add(beforeAfterToPlanDTO);
//        }
//        List<BeforeAfterToPlan> beforeAfterToPlans1 = BeanCopyUtils.convertListTo(beforeAfterToPlanDTOS, BeforeAfterToPlan::new);
//        this.saveBatch(beforeAfterToPlans1);
//        this.updateCacheBeforePlan(fromIdList, id);
//        if (beforeAfterToPlans1 != null && beforeAfterToPlans1.size() > 0) {
//            return true;
//        }
//
//        return false;
//    }
//
//
//    public void updateCacheBeforePlan(List<String> fromIdList, String toId) throws Exception {
//        Plan planDTO = planService.getById(toId);
//        if (ObjectUtils.isEmpty(planDTO)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
//        }
//        String projectId = planDTO.getProjectId();
//
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionChainWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, toId);
//        List<BeforeAfterToPlan> beforeAfterToPlans = this.list(beforeAfterToPlanOrionChainWrapper);
//        if (CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            return;
//        }
//        Set<String> formIdList = new HashSet<>();
//        for (BeforeAfterToPlan beforeAfterToPlan : beforeAfterToPlans) {
//            formIdList.add(beforeAfterToPlan.getFromId());
//        }
//        List<Plan> planList = planService.listByIds(new ArrayList<>(formIdList));
//        PlanTreeVo cachePlanTreeVo = cachePlanBo.getCachePlanTreeVo(projectId, toId);
//        if (ObjectUtils.isEmpty(cachePlanTreeVo)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
//        }
//        if (CollectionUtils.isEmpty(planList)) {
//            cachePlanTreeVo.setTaskId("");
//            cachePlanTreeVo.setTaskName("");
//            cachePlanBo.setEntity(projectId, toId, cachePlanTreeVo);
//            return;
//        }
//        List<String> nameList = new ArrayList<>();
//        List<String> idList = new ArrayList<>();
//        for (Plan plan : planList) {
//            nameList.add(plan.getName());
//            idList.add(plan.getId());
//        }
//        String names = nameList.stream().collect(Collectors.joining(","));
//        String ids = idList.stream().collect(Collectors.joining(","));
//        cachePlanTreeVo.setTaskId(ids);
//        cachePlanTreeVo.setTaskName(names);
//        cachePlanBo.setEntity(projectId, toId, cachePlanTreeVo);
//    }
//
//    public void updateCacheBo(String planId) throws Exception {
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionChainWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, planId);
//        List<BeforeAfterToPlan> beforeAfterToPlans = this.list(beforeAfterToPlanOrionChainWrapper);
//        List<String> idList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            for (BeforeAfterToPlan beforeAfterToPlan : beforeAfterToPlans) {
//                idList.add(beforeAfterToPlan.getFromId());
//            }
//        }
//        this.updateCacheBeforePlan(idList, planId);
//    }
//
//    @Override
//    public List<PlanSimpleVo> beforeList(BeforeParamDto beforeParamDto) throws Exception {
//        String keyword = beforeParamDto.getKeyword();
//        String planId = beforeParamDto.getPlanId();
//        List<PlanSimpleVo> beforeAndAfterPlanVos = new ArrayList<>();
//        LambdaQueryWrapper<BeforeAfterToPlan> beforeAfterToPlanOrionChainWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        beforeAfterToPlanOrionChainWrapper.eq(BeforeAfterToPlan::getToId, planId);
//        List<BeforeAfterToPlan> beforeAfterToPlans = this.list(beforeAfterToPlanOrionChainWrapper);
//        List<String> idList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            for (BeforeAfterToPlan beforeAfterToPlan : beforeAfterToPlans) {
//                idList.add(beforeAfterToPlan.getFromId());
//            }
//        }
//        List<PlanDetailVo> listNotInIdList = planService.getListNotInIdList(idList, keyword, planId);
//        if (CollectionUtils.isEmpty(listNotInIdList)) {
//            return beforeAndAfterPlanVos;
//        }
//        Set<String> principalIdList = new HashSet<>();
//        listNotInIdList.forEach(planDetailVo -> {
//            if (StringUtils.hasText(planDetailVo.getResUser())) {
//                principalIdList.add(planDetailVo.getResUser());
//            }
//        });
//        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(new ArrayList<>(principalIdList));
//        for (PlanDetailVo planDetailVo : listNotInIdList) {
//            PlanSimpleVo planSimpleVo = new PlanSimpleVo();
//            planSimpleVo.setId(planDetailVo.getId());
//            planSimpleVo.setName(planDetailVo.getName());
//            if (StringUtils.hasText(planDetailVo.getResUser())) {
//                UserVO userVO = userMapByUserIds.get(planDetailVo.getResUser());
//                planSimpleVo.setPrincipalId(planDetailVo.getResUser());
//                planSimpleVo.setPrincipalName(userVO == null ? "" : userVO.getName());
//            }
//            planSimpleVo.setNumber(planDetailVo.getNumber());
//            beforeAndAfterPlanVos.add(planSimpleVo);
//        }
//        return beforeAndAfterPlanVos;
//    }
//}
