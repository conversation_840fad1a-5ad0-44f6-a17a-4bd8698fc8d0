# 企业级集成开发Cursor规则

## 全栈项目架构设计

### 微服务架构设计
```
作为一名资深的企业级架构师，请为[项目名称]设计一套基于Spring Cloud Alibaba和Vue3的微服务架构，要求：

技术栈：
- 后端：Spring Cloud Alibaba, Spring Boot, Nacos, Gateway, Seata, RabbitMQ, Redis
- 前端：Vue3, TypeScript, Element Plus, Pinia, qiankun微前端
- 数据库：MySQL 8.0, Redis, Milvus(向量数据库)
- DevOps：Docker, K8S, Jenkins, Skywalking, Loki+Grafana

业务域划分：
[列出核心业务域]

架构设计要点：
1. 微服务边界划分原则
2. 服务发现与配置中心设计
3. API网关策略
4. 分布式事务处理
5. 消息队列应用场景
6. 数据库设计原则
7. 微前端架构策略
8. CI/CD流水线设计
9. 监控与可观测性方案
10. 安全架构考虑

请提供：
1. 整体架构图
2. 核心服务清单及职责
3. 技术关键点实现建议
4. 可扩展性设计
5. 容灾与高可用考虑
```

### CICD流水线设计
```
请设计一套企业级Java与Vue项目的CI/CD流水线，基于以下技术栈：
- Jenkins
- Docker
- Kubernetes
- Sonar
- Nexus/Harbor
- GitLab

流水线要求：
- 代码质量检查
- 单元测试与集成测试
- 镜像构建与推送
- 环境部署策略
- 版本管理与回滚机制
- 安全扫描集成
- 性能测试集成
- 审批流程

应用架构：
[描述应用架构]

环境规划：
[描述开发、测试、预发布、生产环境]

请提供：
1. Jenkinsfile设计
2. Docker镜像构建策略
3. Kubernetes部署配置
4. 环境隔离与提升策略
5. 监控与告警集成
```

### 数据库设计与集成
```
请为[项目名称]设计数据库架构，要求同时考虑关系型数据库MySQL和NoSQL数据库Redis的应用，要点：

关系型数据库(MySQL 8.0)：
- 核心业务表设计
- 数据库分库分表策略(基于Sharding-JDBC)
- 数据库索引优化
- 事务隔离级别
- 数据库连接池配置
- 读写分离策略

NoSQL数据库(Redis)：
- 缓存策略设计
- 数据结构选择
- 过期策略
- 持久化策略
- 集群配置

集成策略：
- 缓存一致性保障
- 数据同步机制
- 分布式锁应用
- 性能优化考虑

业务场景：
[描述核心业务场景]

数据量级：
[描述预期数据量级]

请提供：
1. ER图设计
2. 表结构定义
3. 索引策略
4. 缓存策略设计
5. 数据一致性解决方案
```

## 前后端集成最佳实践

### API接口规范
```
请设计一套企业级前后端API接口规范，用于Java后端和Vue前端团队协作，包含：

接口设计规范：
- RESTful资源命名规范
- URL路径设计规则
- HTTP方法使用原则
- 查询参数规范
- 请求/响应数据格式
- 状态码使用规范
- 版本控制策略
- 分页/排序/过滤规范

接口文档规范：
- OpenAPI/Swagger规范
- 接口说明要素
- 字段描述规范
- 示例代码要求
- 错误码规范

安全规范：
- 认证/授权规范
- 参数校验规范
- 敏感数据处理
- CSRF防护
- 限流策略

协作流程：
- 接口设计评审流程
- 接口变更管理
- Mock数据规范
- 联调测试规范

请提供：
1. 接口规范文档模板
2. 典型接口设计示例
3. 接口文档示例
4. 最佳实践建议
```

### 前端与后端集成测试
```
请设计一套前后端集成测试方案，用于验证Java后端和Vue前端的交互，包含：

测试范围：
- API接口测试
- 端到端流程测试
- 性能测试
- 兼容性测试
- 安全测试

测试工具链：
- 后端测试：JUnit, MockMvc, Testcontainers
- 前端测试：Jest, Vue Test Utils, Cypress
- API测试：Postman, RestAssured
- 性能测试：JMeter, Gatling
- 安全测试：OWASP ZAP

测试策略：
- 测试环境管理
- 测试数据准备
- 测试用例设计
- 自动化测试实现
- CI/CD集成
- 测试报告与度量

重点测试场景：
[描述核心业务场景]

请提供：
1. 测试计划模板
2. 测试用例设计示例
3. 自动化测试框架搭建指南
4. 集成测试最佳实践
```

### 权限与认证集成
```
请设计一套适用于微服务架构的企业级权限与认证方案，集成Spring Security和Vue前端，包含：

认证方案：
- JWT认证流程
- OAuth2/OIDC集成
- 单点登录实现
- 多因素认证
- 会话管理
- 令牌刷新机制

权限模型：
- RBAC模型实现
- 数据权限控制
- API权限控制
- 菜单/按钮权限控制
- 动态权限配置
- 权限缓存策略

前后端集成：
- 前端权限控制实现
- 后端权限校验实现
- 权限数据同步机制
- 用户体验考虑

安全加固：
- 密码策略
- 账户锁定策略
- 安全日志审计
- 异常检测

请提供：
1. 认证流程设计
2. 权限模型实现
3. 前后端集成方案
4. 安全最佳实践
```

## 企业级共享组件

### 统一响应处理
```
请设计并实现一套企业级统一响应处理机制，用于Java后端和Vue前端的数据交互，包含：

后端实现：
- 统一响应对象设计
- 全局异常处理
- 错误码体系
- 业务异常定义
- 参数校验异常处理
- 权限异常处理
- 系统异常处理
- 国际化支持

前端实现：
- API响应拦截器
- 统一错误处理
- 错误提示组件
- 业务错误处理策略
- 网络错误处理
- 重试机制
- 友好提示UI

请提供：
1. 后端统一响应实现代码
2. 前端响应处理实现代码
3. 错误码设计规范
4. 最佳实践示例
```

### 分布式日志系统
```
请设计一套适用于微服务架构的分布式日志解决方案，基于以下技术栈：
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Filebeat
- Loki + Grafana
- Spring Boot Actuator
- Micrometer
- SkyWalking

需求：
- 统一日志收集
- 分布式追踪
- 异常聚合分析
- 性能指标监控
- 告警机制
- 日志安全与审计
- 容量规划
- 日志保留策略

日志场景：
- 应用日志
- 访问日志
- 审计日志
- 性能日志
- 安全日志

请提供：
1. 日志架构设计
2. 日志格式规范
3. 集成实现方案
4. 查询与分析最佳实践
5. 运维考虑
```

### 数据导入导出组件
```
请设计并实现一套企业级数据导入导出组件，适用于Java后端和Vue前端集成，支持：

功能需求：
- Excel导入导出(EasyExcel)
- CSV导入导出
- PDF导出
- 大数据量异步处理
- 导入数据验证
- 导入错误处理
- 自定义模板
- 进度跟踪

技术实现：
- 后端实现技术选型
- 前端UI组件
- 文件上传下载处理
- 数据流处理
- 内存优化考虑
- 并发处理策略
- 安全控制

请提供：
1. 后端组件设计与实现
2. 前端组件设计与实现
3. 使用示例
4. 性能优化建议
```

## AI增强开发实践

### RAG系统集成
```
请设计一套企业级检索增强生成(RAG)系统，用于集成到现有Java后端和Vue前端系统中，基于以下技术：
- Spring AI
- LlamaIndex
- Milvus向量数据库
- Vue3前端
- MaxKB大型知识库

功能需求：
- 文档自动向量化
- 智能检索服务
- 上下文理解
- 自然语言查询
- 多源数据集成
- 知识库管理
- 用户反馈优化
- 权限控制

技术实现：
- 向量嵌入选型
- 向量数据库设计
- 检索算法实现
- 前端交互设计
- API设计

请提供：
1. 系统架构设计
2. 核心组件实现
3. API接口设计
4. 前端UI设计
5. 数据处理流程
```

### AI辅助编码规范
```
请为企业开发团队设计一套AI辅助编码实践指南，重点关注Cursor在企业开发中的应用，包含：

应用场景：
- 代码生成最佳实践
- 代码补全策略
- 代码重构指南
- 单元测试生成
- 文档生成
- Bug修复辅助
- 架构设计辅助
- 安全审计辅助

提示工程规范：
- 角色定义模板
- 上下文提供模板
- 代码生成模板
- 代码审查模板
- 优化请求模板
- 问题诊断模板

团队协作：
- 提示模板共享
- 代码审查集成
- 知识分享机制
- 质量控制措施
- 安全与合规考虑

请提供：
1. AI辅助编码指南
2. 提示工程模板集
3. 团队协作最佳实践
4. 效果度量与评估方法
```

### 代码质量与审查自动化
```
请设计一套代码质量与审查自动化方案，集成AI辅助能力，用于企业Java和Vue项目，包含：

静态代码分析：
- SonarQube集成
- ESLint/StyleLint配置
- PMD/SpotBugs应用
- 架构一致性检查
- 安全漏洞扫描
- 技术债务管理

AI辅助代码审查：
- Cursor代码审查流程
- 常见问题自动识别
- 最佳实践建议
- 性能优化建议
- 安全增强建议

自动化流程：
- CI/CD集成策略
- 质量门禁设置
- 审查报告生成
- 问题跟踪与修复
- 团队反馈机制

请提供：
1. 代码质量管理方案
2. 静态分析工具配置
3. AI辅助审查流程
4. 自动化集成实现
5. 质量度量与可视化
``` 