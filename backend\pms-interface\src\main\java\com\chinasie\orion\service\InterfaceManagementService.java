package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.InterfaceManagement;
import com.chinasie.orion.domain.dto.InterfaceManagementDTO;
import com.chinasie.orion.domain.vo.InterfaceManagementVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.vo.InterfacePageDataVO;
import com.chinasie.orion.domain.vo.SimpleDictVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * InterfaceManagement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
public interface InterfaceManagementService extends OrionBaseService<InterfaceManagement> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    InterfaceManagementVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param interfaceManagementDTO
     */
    InterfaceManagementVO create(InterfaceManagementDTO interfaceManagementDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param interfaceManagementDTO
     */
    Boolean edit(InterfaceManagementDTO interfaceManagementDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<InterfacePageDataVO> pages(Page<InterfaceManagementDTO> pageRequest) throws Exception;

    /**
     *  获取编码
     * @return
     * @throws Exception
     */
    String getInterfaceNumber() throws Exception;

    List<SimpleDictVO> getTypeDict();

    List<SimpleDictVO> thirdVerifyDict();

    List<SimpleDictVO> auditStatusDict();

    /**
     *  接口关闭
     * @param id
     * @return
     */
    Boolean close(String id) throws Exception;

    /**
     *  获取接口状态列表
     * @return
     */
    List<String> stateDictList();

    /**
     *  获取接口列表
     * @param imIdList
     * @return
     */
    List<InterfacePageDataVO> detailListByIdList(List<String> imIdList) throws Exception;

    /**
     *  获取接口列表
     * @param interfaceManagementDTO
     * @return
     */
    List<InterfacePageDataVO> list(InterfaceManagementDTO interfaceManagementDTO) throws Exception;
}
