package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.dto.MarketContractDivisionLeaderDTO;
import com.chinasie.orion.domain.dto.MarketContractFileDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractDetailMoneyVo;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.domain.vo.SubOrderContractTotalAmt;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * MarketContract 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@RestController
@RequestMapping("/marketContract")
@Api(tags = "市场合同")
@RequiredArgsConstructor
public class MarketContractController {

    private final MarketContractService marketContractService;

    /**
     * 详情
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情数据【{{#id}}】", type = "市场合同", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MarketContractVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        MarketContractVO rsp = marketContractService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 合同详情里程碑金额
     */
    @ApiOperation(value = "合同详情里程碑金额")
    @RequestMapping(value = "/money/{contractId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情数据【{{#contractId}}】", type = "市场合同", subType = "合同详情里程碑金额", bizNo = "{{#contractId}}")
    public ResponseDTO<MarketContractDetailMoneyVo> getMoney(@PathVariable(value = "contractId") String contractId) throws Exception {
        MarketContractDetailMoneyVo rsp = marketContractService.getMoney(contractId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 投入分析
     */
    @RequestMapping(value = "/inputAnalysis/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情数据【{{#id}}】", type = "市场合同", subType = "投入分析", bizNo = "{{#id}}")
    public ResponseDTO<QuotationManagementVO> inputAnalysis(@PathVariable(value = "id") String id) throws Exception {
        QuotationManagementVO rsp = marketContractService.inputAnalysis(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractDTO.name}}】", type = "市场合同", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated MarketContractDTO marketContractDTO) throws Exception {
        String contractType = marketContractDTO.getContractType();
//        if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.SUB_ORDER_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
//            BigDecimal contractAmt = marketContractDTO.getContractAmt();
//            if (contractAmt == null) {
//                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "合同金额不能为空！");
//            }
//        }
//        if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
//            BigDecimal frameContractAmt = marketContractDTO.getFrameContractAmt();
//            if (frameContractAmt == null) {
//                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "框架合同金额不能为空！");
//            }
//        }

        String rsp = marketContractService.create(marketContractDTO);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增框架下子订单
     */
    @ApiOperation(value = "新增框架下子订单")
    @RequestMapping(value = "/add/order", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractDTO.name}}】", type = "市场合同", subType = "新增框架下子订单", bizNo = "{{#id}}")
    public ResponseDTO<String> createOrder(@RequestBody @Validated MarketContractDTO marketContractDTO) throws Exception {
//        String contractType = marketContractDTO.getContractType();
//        if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.SUB_ORDER_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
//            BigDecimal contractAmt = marketContractDTO.getContractAmt();
//            if (contractAmt == null) {
//                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "合同金额不能为空！");
//            }
//        }
//        if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
//            BigDecimal frameContractAmt = marketContractDTO.getFrameContractAmt();
//            if (frameContractAmt == null) {
//                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "框架合同金额不能为空！");
//            }
//        }

        String rsp = marketContractService.createOrder(marketContractDTO);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增附件
     *
     * @param marketContractFileDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增附件")
    @RequestMapping(value = "/add/file", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增市场合同附件【{{#marketContractFileDTO.dataId}}】", type = "市场合同", subType = "新增附件", bizNo = "{{#marketContractFileDTO.dataId}}")
    public ResponseDTO<Boolean> createFile(@RequestBody @Validated MarketContractFileDTO marketContractFileDTO) throws Exception {
        Boolean rsp = marketContractService.createFile(marketContractFileDTO);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询附件
     */
    @ApiOperation(value = "查询附件")
    @RequestMapping(value = "/file/{dataId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询市场合同附件【{{#dataId}}】", type = "市场合同", subType = "查询附件", bizNo = "{{#dataId}}")
    public ResponseDTO<List<Map<String, Object>>> queryFile(@PathVariable(value = "dataId") String dataId) throws Exception {
        List<Map<String, Object>> rsp = marketContractService.queryFile(dataId);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除附件
     */
    @ApiOperation(value = "删除附件")
    @RequestMapping(value = "/file/{fileId}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除市场合同附件【{{#fileIds}}】", type = "市场合同", subType = "删除附件", bizNo = "{{#fileIds}}")
    public ResponseDTO<Boolean> deleteFile(@RequestBody List<String> fileIds) throws Exception {
        Boolean rsp = marketContractService.deleteFile(fileIds);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     */
    @ApiOperation(value = "合同的编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractDTO.name}}】", type = "市场合同", subType = "编辑", bizNo = "{{#marketContractDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated MarketContractDTO marketContractDTO) throws Exception {
        Boolean rsp = marketContractService.edit(marketContractDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 子订单和子合同的编辑
     */
    @ApiOperation(value = "子订单和子合同的编辑")
    @RequestMapping(value = "/edit/sub", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractDTO.name}}】", type = "市场合同", subType = "子订单和子合同的编辑", bizNo = "{{#marketContractDTO.id}}")
    public ResponseDTO<Boolean> editSub(@RequestBody @Validated MarketContractDTO marketContractDTO) throws Exception {
        Boolean rsp = marketContractService.editSub(marketContractDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 关闭
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "关闭")
    @RequestMapping(value = "/close/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractDTO.name}}】", type = "市场合同", subType = "关闭", bizNo = "{{#marketContractDTO.id}}")
    public ResponseDTO<Boolean> close(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = marketContractService.close(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "市场合同", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = marketContractService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "市场合同", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = marketContractService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractVO>> pages(@RequestBody Page<MarketContractDTO> pageRequest) throws Exception {
        Page<MarketContractVO> rsp = marketContractService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 子订单分页
     */
    @ApiOperation(value = "子订单分页")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "subOrder/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "子订单分页", bizNo = "")
    public ResponseDTO<Page<MarketContractVO>> subOrderPages(@RequestBody Page<MarketContractDTO> pageRequest) throws Exception {
        Page<MarketContractVO> rsp = marketContractService.subOrderPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询子订单总金额
     */
    @ApiOperation(value = "查询子订单总金额")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "查询子订单总金额", bizNo = "")
    @RequestMapping(value = "subOrder/totalAmt/{id}", method = RequestMethod.GET)
    public ResponseDTO<SubOrderContractTotalAmt> subOrderTotalAmt(@PathVariable("id") String id) {
        SubOrderContractTotalAmt rsp = marketContractService.subOrderTotalAmt(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询所有已完结框架合同
     */
    @ApiOperation(value = "查询所有已完结框架合同")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/complate/frame/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "查询所有已完结框架合同", bizNo = "")
    public ResponseDTO<Page<MarketContractVO>> complateFramePages(@RequestBody Page<MarketContractDTO> pageRequest) {
        Page<MarketContractVO> rsp = marketContractService.complateFramePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "市场合同", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        marketContractService.downloadExcelTpl(response);
    }

    @ApiOperation("市场合同导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "市场合同", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = marketContractService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "市场合同", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = marketContractService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消市场合同导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "市场合同", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = marketContractService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("市场合同导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        marketContractService.exportByExcel(searchConditions, response);
    }


    @ApiOperation("合同明细导出")
    @PostMapping(value = "/export/excelData", produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同", subType = "合同明细导出", bizNo = "")
    public void exportExcelData(@RequestBody Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws Exception {
        marketContractService.exportExcelData(pageRequest, response);
    }

    @ApiOperation("根据合同编号查询")
    @PostMapping(value = "/getByNumber")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询", type = "市场合同", subType = "根据合同编号查询", bizNo = "")
    public ResponseDTO<MarketContract> getByNumber(@RequestBody MarketContractDTO dto) throws Exception {
        return new ResponseDTO<>(marketContractService.getByNumber(dto.getNumber()));
    }


    @ApiOperation("根据合同编号list查询")
    @PostMapping(value = "/getByNumberList")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询", type = "市场合同", subType = "根据合同编号list查询", bizNo = "")
    public ResponseDTO<List<MarketContract>> getByNumberList(@RequestBody List<MarketContractDTO> dtos) throws Exception {
        return new ResponseDTO<>(marketContractService.getByNumberList(dtos));
    }

    @ApiOperation("发起流程前判断组合类型里程碑是否有子项")
    @GetMapping(value = "/judgeMilestone/{marketContractId}")
    @LogRecord(success = "【{USER{#logUserId}}】发起流程前判断组合类型里程碑是否有子项", type = "市场合同", subType = "发起流程前判断组合类型里程碑是否有子项", bizNo = "")
    public ResponseDTO<List<String>> judgeMilestone(@PathVariable("marketContractId") String marketContractId) {
        return new ResponseDTO<>(marketContractService.judgeMilestone(marketContractId));
    }

    @ApiOperation("主数据状态的枚举")
    @GetMapping(value = "/status/list")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "主数据状态的枚举", bizNo = "")
    public ResponseDTO<List<DataStatusVO>> statusList() {
        return new ResponseDTO<>(marketContractService.listDataStatus());
    }

    /**
     * 查询所级领导
     */
    @ApiOperation(value = "查询所级领导和所级")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "查询所级领导和所级", bizNo = "")
    @RequestMapping(value = "getDivisionLeaders", method = RequestMethod.POST)
    public ResponseDTO<MarketContractDivisionLeaderDTO> getDivisionLeaders(@RequestBody MarketContractDivisionLeaderDTO marketContractDivisionLeaderDTO) {
        MarketContractDivisionLeaderDTO rsp = marketContractService.getDivisionLeaders(marketContractDivisionLeaderDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划合同信息分页查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询了收入计划合同信息分页查询", type = "ContractInfo", subType = "收入计划合同信息分页查询", bizNo = "")
    @RequestMapping(value = "/getIncomeMarketContractPage", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractVO>> getIncomeMarketContractPage(@RequestBody Page<MarketContractDTO> pageRequest) throws Exception {
        Page<MarketContractVO> rsp = marketContractService.getIncomeMarketContractPage(pageRequest, 0);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("判断当前合同的业务类型是否和他的需求保持一致")
    @RequestMapping(value = "/businessType", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】判断当前合同的业务类型是否和他的需求保持一致", type = "市场合同", subType = "判断当前合同的业务类型是否和他的需求保持一致", bizNo = "")
    public ResponseDTO<Boolean> businessType(@RequestBody MarketContract marketContract) throws Exception {
        return new ResponseDTO<>(marketContractService.businessType(marketContract));
    }

    @ApiOperation("已回款金额更新")
    @RequestMapping(value = "/returnAmtUpdate", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】已回款金额更新", type = "市场合同", subType = "已回款金额更新", bizNo = "")
    public ResponseDTO<Boolean> returnAmtUpdate(@RequestBody String marketContractId) throws Exception {
        return new ResponseDTO<>(marketContractService.returnAmtUpdate(marketContractId));
    }

    @ApiOperation("合同明细导出新")
    @PostMapping(value = "/export/excelDataNew", produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】合同明细导出新", type = "市场合同", subType = "导出数据", bizNo = "")
    public void exportExcelDataNew(@RequestBody Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws Exception {
        marketContractService.exportExcelDataNew(pageRequest, response);
    }


    /**
     * 分页
     */
    @ApiOperation(value = "菜单分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同", subType = "菜单数据分页查询", bizNo = "")
    @RequestMapping(value = "/page/menu", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractVO>> pagesMenu(@RequestBody Page<MarketContractDTO> pageRequest) throws Exception {
        Page<MarketContractVO> rsp = marketContractService.pagesMenu(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * test
     */
    @ApiOperation(value = "test")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public ResponseDTO<String> test(@RequestBody MarketContract dto) throws Exception {
        return new ResponseDTO<>(marketContractService.generateApplyNo(dto, null));
    }
}
