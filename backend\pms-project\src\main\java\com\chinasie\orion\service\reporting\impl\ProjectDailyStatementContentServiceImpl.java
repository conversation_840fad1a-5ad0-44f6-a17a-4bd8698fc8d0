package com.chinasie.orion.service.reporting.impl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementContentDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatementContent;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementContentVO;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.reporting.ProjectDailyStatementContentMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.reporting.ProjectDailyStatementContentService;
import com.chinasie.orion.service.reporting.ProjectDailyStatementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectDailyStatementContent 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@Service
public class ProjectDailyStatementContentServiceImpl extends OrionBaseServiceImpl<ProjectDailyStatementContentMapper,ProjectDailyStatementContent> implements  ProjectDailyStatementContentService {

    @Autowired
    private ProjectDailyStatementContentMapper projectDailyStatementContentMapper;


    @Autowired
    private InternalAssociationRedisHelper internalAssociationRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public ProjectDailyStatementContentVO detail(String id) throws Exception {
        ProjectDailyStatementContent projectDailyStatementContent =projectDailyStatementContentMapper.selectById(id);
        ProjectDailyStatementContentVO result = BeanCopyUtils.convertTo(projectDailyStatementContent,ProjectDailyStatementContentVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param projectDailyStatementContentDTO
     */
    @Override
    public  ProjectDailyStatementContentVO create(ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception {
        ProjectDailyStatementContent projectDailyStatementContent =BeanCopyUtils.convertTo(projectDailyStatementContentDTO,ProjectDailyStatementContent::new);
        int insert = projectDailyStatementContentMapper.insert(projectDailyStatementContent);
        ProjectDailyStatementContentVO rsp = BeanCopyUtils.convertTo(projectDailyStatementContent,ProjectDailyStatementContentVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectDailyStatementContentDTO
     */
    @Override
    public Boolean edit(ProjectDailyStatementContentDTO projectDailyStatementContentDTO) throws Exception {
        ProjectDailyStatementContent projectDailyStatementContent =BeanCopyUtils.convertTo(projectDailyStatementContentDTO,ProjectDailyStatementContent::new);
        int update =  projectDailyStatementContentMapper.updateById(projectDailyStatementContent);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = projectDailyStatementContentMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProjectDailyStatementContentVO> pages(Page<ProjectDailyStatementContentDTO> pageRequest) throws Exception {
        Page<ProjectDailyStatementContent> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectDailyStatementContent::new));

        PageResult<ProjectDailyStatementContent> page = projectDailyStatementContentMapper.selectPage(realPageRequest,null);

        Page<ProjectDailyStatementContentVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectDailyStatementContentVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectDailyStatementContentVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void createBatch(List<ProjectDailyStatementContentDTO> contentVOList) throws Exception {
        if(CollectionUtils.isBlank(contentVOList)){
            return;
        }
        //添加关联对象类型
        setDTORelationshipType(contentVOList);
        List<ProjectDailyStatementContent> ts = BeanCopyUtils.convertListTo(contentVOList, ProjectDailyStatementContent::new);
        this.saveBatch(ts);
    }

    @Override
    public Map<String, List<ProjectDailyStatementContentVO>> getMapByDataIdList(List<String> idList) throws Exception {
        if (CollectionUtils.isBlank(idList)){
            return new HashMap<>();
        }
        LambdaQueryWrapperX<ProjectDailyStatementContent> projectDailyStatementContentLambdaQueryWrapper = new LambdaQueryWrapperX<>();
//        projectDailyStatementContentLambdaQueryWrapper.select(ProjectDailyStatementContent::getId
//                ,ProjectDailyStatementContent::getContent
//                ,ProjectDailyStatementContent::getDailyStatementId);
        projectDailyStatementContentLambdaQueryWrapper.in(ProjectDailyStatementContent::getDailyStatementId,idList);
        List<ProjectDailyStatementContent> projectDailyStatementContents = projectDailyStatementContentMapper.selectList(projectDailyStatementContentLambdaQueryWrapper);
        if(CollectionUtils.isBlank(projectDailyStatementContents)){
            return Collections.emptyMap();
        }
        List<ProjectDailyStatementContentVO> projectDailyStatementContentVOS = BeanCopyUtils.convertListTo(projectDailyStatementContents, ProjectDailyStatementContentVO::new);
        setRelationshipName(projectDailyStatementContentVOS);
        return projectDailyStatementContentVOS.stream().collect(Collectors.groupingBy(ProjectDailyStatementContentVO::getDailyStatementId));
    }

    @Override
    public int deleteByDataIdList(List<String> ids) {
        return  projectDailyStatementContentMapper.delete(new LambdaQueryWrapper<>(ProjectDailyStatementContent.class).in(ProjectDailyStatementContent::getDailyStatementId,ids));
    }

    @Override
    public List<ProjectDailyStatementContent> getListByDailyId(String dailyId) {
        LambdaQueryWrapperX<ProjectDailyStatementContent> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(ProjectDailyStatementContent::getDailyStatementId,dailyId);
        return list(wrapper);
    }
    @Override
    public List<ProjectDailyStatementContentVO> getListByDailyIds(List<String> dailyIds) {
        List<ProjectDailyStatementContentVO> voList = new ArrayList<>();
        LambdaQueryWrapperX<ProjectDailyStatementContent> wrapper = new LambdaQueryWrapperX<>();
        wrapper.in(ProjectDailyStatementContent::getDailyStatementId,dailyIds);
        List<ProjectDailyStatementContent> list = list(wrapper);
        if (CollectionUtils.isBlank(list)){
            List<ProjectDailyStatementContentVO> vos = BeanCopyUtils.convertListTo(list, ProjectDailyStatementContentVO::new);
            voList.addAll(vos);
        }
        return voList;
    }

    //添加关联对象名称
    public void setRelationshipName(List<ProjectDailyStatementContentVO> contentVOList) throws Exception {
        if (CollectionUtils.isBlank(contentVOList)){
            return;
        }
        List<String> entityByIdList = contentVOList.stream().map(ProjectDailyStatementContentVO::getRelationship).distinct().collect(Collectors.toList());
        List<ProjectInternalAssociationRedisVO> projectInternalAssociationRedisVOS = internalAssociationRedisHelper.queryForEntityByIds(entityByIdList);
        Map<String, ProjectInternalAssociationRedisVO> map = projectInternalAssociationRedisVOS.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO::getId, p -> p));
        contentVOList.forEach(c->{
            ProjectInternalAssociationRedisVO projectInternalAssociationRedisVO = map.get(c.getRelationship());
            if (projectInternalAssociationRedisVO != null){
                c.setRelationshipName("【"+projectInternalAssociationRedisVO.getName()+"】"+projectInternalAssociationRedisVO.getInnerName());
            }
        });
    }

    //添加关联对象名称
    public void setDTORelationshipType(List<ProjectDailyStatementContentDTO> contentVOList) throws Exception {
        if (CollectionUtils.isBlank(contentVOList)){
            return;
        }
        List<String> entityByIdList = contentVOList.stream().map(ProjectDailyStatementContentDTO::getRelationship).distinct().collect(Collectors.toList());
        List<ProjectInternalAssociationRedisVO> projectInternalAssociationRedisVOS = internalAssociationRedisHelper.queryForEntityByIds(entityByIdList);
        Map<String, ProjectInternalAssociationRedisVO> map = projectInternalAssociationRedisVOS.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO::getId, p -> p));
        contentVOList.forEach(c->{
            ProjectInternalAssociationRedisVO projectInternalAssociationRedisVO = map.get(c.getRelationship());
            if (projectInternalAssociationRedisVO != null){
                c.setRelationType(projectInternalAssociationRedisVO.getType());
            }
        });
    }
}
