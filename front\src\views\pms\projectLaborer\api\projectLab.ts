import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 关联产品 */
  getProductList = 'project/getProductList',
  /* 项目状态 */
  getProductStatus = 'project-task-status/list'
}

// 关联产品列表
export function getProductListApi() {
  return new Api(base).fetch('', `${zApi.getProductList}/`, 'GET');
}

export function getProductStatusApi(params) {
  return new Api(base).fetch('', `${zApi.getProductStatus}/${params}`, 'GET');
}
