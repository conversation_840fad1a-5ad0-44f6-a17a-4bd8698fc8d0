<script setup lang="ts">
import {
  BasicButton, getDict, Icon, InputMoney, OrionTable, useModal,
} from 'lyra-component-vue3';
import {
  onMounted, provide, ref, Ref,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import SelectOrderModal from '/src/views/pms/projectLibrary/pages/components/selectOrder/SelectOrderModal.vue';
import {
  DatePicker, FormItemRest, Input, InputNumber, message, Modal, Select,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps<{
  purchaseTypeValue: string
}>();

const [registerSelectOrder, { openModal: openSelectOrderModal }] = useModal();

const purchaseTypeTitle: Ref<string> = ref(props.purchaseTypeValue === 'goodsType' ? '物资' : '服务');
provide('purchaseTypeTitle', purchaseTypeTitle);
provide('purchaseTypeValue', props.purchaseTypeValue);
// 已触发校验
// const validated: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const obj = () => ({
  id: `custom${new Date().getTime().toString()}`,
  planNumber: '',
  goodsServiceNumber: '',
  description: '',
  normsModel: '',
  unitCode: '',
  purchaseAmount: props.purchaseTypeValue !== 'goodsType' ? 1 : null,
  demandDate: null,
  noTaxPrice: null,
  noTaxTotalAmt: null,
  taxRate: null,
  haveTaxPrice: null,
  haveTaxTotalAmt: null,
  remark: '',
});
const selectKeys: Ref<string[]> = ref([]);
const dataSource: Ref<any[]> = ref([obj()]);
const unitOptions: Ref<any[]> = ref([]);
const columns: Ref<any[]> = ref([
  {
    title: `${purchaseTypeTitle.value}计划编号`,
    dataIndex: 'planNumber',
    type: 'form',
    component: 'Input',
    disabled: true,
  },
  {
    title: `${purchaseTypeTitle.value}编码`,
    dataIndex: 'goodsServiceNumber',
    width: 200,
    required: true,
    type: 'form',
    component: 'Input',
    suffix: true,
    placeholder: `请选择或输入${purchaseTypeTitle.value}编码`,
  },
  {
    title: `${purchaseTypeTitle.value}描述`,
    dataIndex: 'description',
    type: 'form',
    component: 'Input',
    required: true,
    placeholder: `请输入${purchaseTypeTitle.value}描述`,
  },
  (props.purchaseTypeValue === 'goodsType' ? {
    title: '规格型号',
    dataIndex: 'normsModel',
    type: 'form',
    component: 'Input',
    required: true,
    placeholder: '请输入规格型号',
  } : {
    title: '服务期限',
    dataIndex: 'normsModel',
    type: 'form',
    component: 'InputNumber',
    required: true,
    placeholder: '请输入服务期限',
  }),
  {
    title: '计量单位',
    dataIndex: 'unitCode',
    type: 'form',
    component: 'Select',
    required: true,
  },
  {
    title: '采购数量',
    dataIndex: 'purchaseAmount',
    type: 'form',
    component: 'InputMoney',
    placeholder: '请输入采购数量',
    required: true,
    disabled: props.purchaseTypeValue !== 'goodsType',
  },
  {
    title: '需求日期',
    dataIndex: 'demandDate',
    type: 'form',
    component: 'DatePicker',
    required: true,
  },
  {
    title: '单价（不含税）',
    dataIndex: 'noTaxPrice',
    type: 'form',
    component: 'InputMoney',
    placeholder: '请输入预估单价',
    required: true,
  },
  {
    title: '总金额（不含税）',
    dataIndex: 'noTaxTotalAmt',
    type: 'form',
    component: 'InputMoney',
    disabled: true,
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    type: 'form',
    component: 'InputPercent',
    placeholder: '请输入税率',
    required: true,
  },
  {
    title: '单价（含税）',
    dataIndex: 'haveTaxPrice',
    type: 'form',
    component: 'InputMoney',
    disabled: true,
  },
  {
    title: '总金额（含税）',
    dataIndex: 'haveTaxTotalAmt',
    type: 'form',
    component: 'InputMoney',
    disabled: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    type: 'form',
    component: 'Input',
    placeholder: '请输入备注信息',
  },
]);

const tableOptions = {
  rowSelection: {},
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  dataSource,
  columns,
};

onMounted(() => {
  getUnitOptions();
});

// 获取计量单位字典
async function getUnitOptions() {
  const result = await getDict(props.purchaseTypeValue === 'goodsType' ? 'dict1717110774029287424' : props.purchaseTypeValue === 'serviceType' ? 'dict1717110913980628992' : '');
  unitOptions.value = result || [];
}

// 添加订单明细
function handleAddOrder() {
  const item = obj();
  dataSource.value.push(item);
}

// 自动计算
function autoCalculation() {
  dataSource.value = dataSource.value.map((item) => {
    const obj = {
      ...item,
      noTaxTotalAmt: Math.round(item.noTaxPrice * item.purchaseAmount * 100) / 100,
      haveTaxPrice: Math.round(item.noTaxPrice * (1 + item.taxRate * 0.01) * 100) / 100,
    };
    obj.haveTaxTotalAmt = Math.round(obj.haveTaxPrice * obj.purchaseAmount * 100) / 100;
    return obj;
  });
}

// 搜索选择
function handleSearchSelect(index: number) {
  openSelectOrderModal(true, { index });
}

// 表格多选
function selectionChange({ keys }) {
  selectKeys.value = keys;
}

// 批量移除
function handleRemove() {
  if (selectKeys.value.length === dataSource.value.length) {
    return message.error('至少保留一条订单明细');
  }
  Modal.confirm({
    title: '移除提示',
    content: '确认移除所选择的数据项？',
    onOk() {
      dataSource.value = dataSource.value.filter((item) => !selectKeys.value.includes(item.id));
    },
  });
}

// 选择物资/服务回调
function selectOk(data: any[], index: number) {
  dataSource.value.splice(index, 1, {
    ...dataSource.value[index],
    planNumber: data[0].number,
    goodsServiceNumber: data[0].goodsServiceNumber,
    description: data[0].description,
    normsModel: data[0].normsModel,
    unitCode: data[0].unitCode,
    purchaseAmount: data[0].demandAmount,
    demandDate: data[0].demandTime ? dayjs(data[0].demandTime) : '',
  });
  autoCalculation();
}

// 金额失去焦点
function onInputMoneyBlur(dataIndex: string) {
  const keys = [
    'noTaxPrice',
    'purchaseAmount',
    'taxRate',
    'haveTaxPrice',
  ];
  if (keys.includes(dataIndex)) {
    autoCalculation();
  }
}

function getData() {
  return JSON.parse(JSON.stringify(dataSource.value)).map((item:Record<string, any>) => {
    if (item.id?.includes('custom')) {
      delete item.id;
    }
    return item;
  });
}

defineExpose({
  setData: (data: any[]) => {
    dataSource.value = data.map((item) => ({
      ...item,
      demandDate: dayjs(item.demandDate),
    }));
  },
  getData,
  validate: () => {
    const flag = dataSource.value.every((item) => columns.value.every((column) => {
      if (column.required) {
        return !!item[column.dataIndex];
      }
      return true;
    }));
    if (flag) {
      return getData();
    }
    return [];
  },
});
</script>

<template>
  <DetailsLayout
    title="订单明细"
    is-form-item
    show-table-header
  >
    <div style="height: 300px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :onSelectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddOrder"
          >
            添加订单明细
          </BasicButton>
          <BasicButton
            :disabled="selectKeys.length===0"
            icon="sie-icon-del"
            @click="handleRemove"
          >
            移除
          </BasicButton>
        </template>
        <template #headerCell="{title,column}">
          <div :class="{required:column.required}">
            {{ title }}
          </div>
        </template>
        <template #bodyCell="{text,index,record,column}">
          <div
            v-if="column.type==='form'"
          >
            <FormItemRest>
              <Input
                v-if="column.component==='Input'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
              >
                <template
                  v-if="column.suffix"
                  #suffix
                >
                  <Icon
                    icon="sie-icon-sousuo"
                    @click="handleSearchSelect(index)"
                  />
                </template>
              </Input>
              <Select
                v-if="column.component==='Select'"
                v-model:value="record[column.dataIndex]"
                :fieldNames="{ label: 'description', value: 'value' }"
                :options="unitOptions"
                :disabled="column.disabled"
              />
              <InputNumber
                v-if="column.component==='InputNumber'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
                :precision="0"
              />
              <DatePicker
                v-if="column.component==='DatePicker'"
                v-model:value="record[column.dataIndex]"
              />
              <InputMoney
                v-if="column.component==='InputMoney'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :min="0"
                :placeholder="column.placeholder||''"
                @blur="onInputMoneyBlur(column.dataIndex)"
              />
              <InputMoney
                v-if="column.component==='InputPercent'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :min="0"
                :placeholder="column.placeholder||''"
                :formatter="value => `${value}%`"
                :parser="value => value.replace('%', '')"
                @blur="onInputMoneyBlur(column.dataIndex)"
              />
            </FormItemRest>
          </div>
          <div v-else-if="column.flag">
            {{ index + 1 }}
          </div>
          <div v-else>
            {{ text }}
          </div>
        </template>
      </OrionTable>
    </div>
  </DetailsLayout>
  <SelectOrderModal
    @modalOk="selectOk"
    @register="registerSelectOrder"
  />
</template>

<style scoped lang="less">
.validated {
  .ant-select-selector,
  .ant-input,
  .ant-input-number,
  .ant-picker,
  .ant-input-affix-wrapper {
    border-color: ~`getPrefixVar('error-color')`;
  }
}
</style>
