package com.chinasie.orion.conts;

public enum IncomePlanDataStatusEnum {
    NO_START(101,"未开始"),
    PRO_CENTER_SUMMARY (110, "专业中心汇总"),
    FINANCE_SUMMARY (130, "财务部汇总"),
    RECONCILIATION_PROCESS(121, "对账流程中"),
    BILLING_PROCESS(120, "开票流程中"),
    COMPLETED(160, "已完成"),
    CLOSED(111, "已关闭"),
    FAILURE_DECLARE(150, "不申报");
    private Integer code;

    private String description;

    IncomePlanDataStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
