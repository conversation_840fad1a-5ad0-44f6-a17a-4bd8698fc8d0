package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.dto.ScientificResearchDemandDeclareFileInfoDTO;
import com.chinasie.orion.domain.entity.ScientificResearchDemandDeclareFileInfo;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.ScientificResearchDemandDeclareFileInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ScientificResearchDemandDeclareFileInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 11:37:06
 */
public interface ScientificResearchDemandDeclareFileInfoService  extends OrionBaseService<ScientificResearchDemandDeclareFileInfo>{
    /**
     * 批量新增
     * @param fileInfoDTOList
     * @return
     * @throws Exception
     */
    List<String> saveBatchAdd(String type, List<FileInfoDTO> fileInfoDTOList) throws Exception;

    /**
     * 通过dataid获取文件列表
     * @param dataId
     * @return
     * @throws Exception
     */
    List<DocumentVO> getDocumentList(String type, String dataId, FileInfoQueryDTO fileInfoQueryDTO) throws Exception;


    /**
     * 获取文件分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<DocumentVO> getDocumentVOPage(String type, Page<FileInfoDTO> pageRequest) throws Exception;


    /**
     * 批量删除文件
     * @param fileIdList
     * @return
     * @throws Exception
     */
    Boolean deleteBatchFile(String type, List<String> fileIdList) throws Exception;

}
