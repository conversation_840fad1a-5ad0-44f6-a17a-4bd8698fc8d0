package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.constant.ProjectContractCloseApplyStatusEnum;
import com.chinasie.orion.constant.ProjectContractStatusEnum;
import com.chinasie.orion.domain.dto.ProjectContractCloseApplyDTO;
import com.chinasie.orion.domain.entity.ProjectContract;
import com.chinasie.orion.domain.entity.ProjectContractChangeApply;
import com.chinasie.orion.domain.entity.ProjectContractCloseApply;
import com.chinasie.orion.domain.entity.ProjectContractCloseApplyList;
import com.chinasie.orion.domain.vo.ProjectContractCloseApplyVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectContractChangeApplyRepository;
import com.chinasie.orion.repository.ProjectContractCloseApplyRepository;
import com.chinasie.orion.repository.ProjectContractRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectContractCloseApplyService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectContractCloseApply 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
@Service
public class ProjectContractCloseApplyServiceImpl extends OrionBaseServiceImpl<ProjectContractCloseApplyRepository, ProjectContractCloseApply> implements ProjectContractCloseApplyService {

    @Autowired
    private ProjectContractCloseApplyRepository projectContractCloseApplyRepository;

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Autowired
    private ProjectContractChangeApplyRepository projectContractChangeApplyRepository;


    @Autowired
    private UserRedisHelper userRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectContractCloseApplyVO detail(String id) throws Exception {
        ProjectContractCloseApply projectContractCloseApply = projectContractCloseApplyRepository.selectById(id);
        ProjectContractCloseApplyVO result = BeanCopyUtils.convertTo(projectContractCloseApply, ProjectContractCloseApplyVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectContractCloseApplyDTO
     */
    @Override
    public ProjectContractCloseApplyVO create(ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception {

        String contractId = projectContractCloseApplyDTO.getContractId();
        ProjectContract projectContract = projectContractRepository.selectOne(ProjectContract::getId, contractId);
        if (!ProjectContractStatusEnum.PERFORM.getStatus().equals(projectContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同状态不是合同履行,不能发起关闭申请!");
        }
        ProjectContractCloseApply projectContractCloseApply1 = projectContractCloseApplyRepository.selectOne(ProjectContractCloseApply::getContractId, contractId);
        if (projectContractCloseApply1 != null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "当前合同已存在关闭申请，不能再次发起!");
        }

        List<ProjectContractChangeApply> projectContractChangeApplyList = projectContractChangeApplyRepository.selectList(ProjectContractChangeApply::getContractId, contractId);
        if (!CollectionUtils.isBlank(projectContractChangeApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同已发起变更申请，不能发起关闭申请!");
        }
        ProjectContractCloseApply projectContractCloseApply = BeanCopyUtils.convertTo(projectContractCloseApplyDTO, ProjectContractCloseApply::new);
        int insert = projectContractCloseApplyRepository.insert(projectContractCloseApply);
        ProjectContractCloseApplyVO rsp = BeanCopyUtils.convertTo(projectContractCloseApply, ProjectContractCloseApplyVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectContractCloseApplyDTO
     */
    @Override
    public Boolean edit(ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception {

        ProjectContractCloseApply projectContractCloseApply1 = projectContractCloseApplyRepository.selectById(projectContractCloseApplyDTO.getId());
        if (projectContractCloseApply1 == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同关闭申请不存在!");
        }
        if (!ProjectContractCloseApplyStatusEnum.CREATED.getStatus().equals(projectContractCloseApply1.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同关闭申请记录状态不能修改!");
        }
        ProjectContractCloseApply projectContractCloseApply = BeanCopyUtils.convertTo(projectContractCloseApplyDTO, ProjectContractCloseApply::new);


        int update = projectContractCloseApplyRepository.updateById(projectContractCloseApply);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

        List<ProjectContractCloseApply> projectContractCloseApplyList = projectContractCloseApplyRepository.selectList(ProjectContractCloseApply::getId, ids);
        if (CollectionUtils.isBlank(projectContractCloseApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同关闭申请记录不存在!");
        }
        if (projectContractCloseApplyList.stream().filter(item -> !item.getStatus().equals(ProjectContractCloseApplyStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "合同关闭申请记录当前状态不能删除!");
        }
        int delete = projectContractCloseApplyRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectContractCloseApplyVO> pages(Page<ProjectContractCloseApplyDTO> pageRequest) throws Exception {
        Page<ProjectContractCloseApply> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectContractCloseApply::new));

        PageResult<ProjectContractCloseApply> page = projectContractCloseApplyRepository.selectPage(realPageRequest, null);

        Page<ProjectContractCloseApplyVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectContractCloseApplyVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectContractCloseApplyVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProjectContractCloseApplyVO> listByContractId(String contractId) throws Exception {
        LambdaQueryWrapperX<ProjectContractCloseApply> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.selectAll(ProjectContractCloseApplyList.class);
        lambdaQueryWrapper.leftJoin(ProjectContract.class, ProjectContract::getId, ProjectContractCloseApply::getContractId);
        lambdaQueryWrapper.eq(ProjectContractCloseApply::getContractId, contractId);
        List<ProjectContractCloseApplyList> projectContractCloseApplyListList = projectContractCloseApplyRepository.selectJoinList(ProjectContractCloseApplyList.class, lambdaQueryWrapper);
        List<ProjectContractCloseApplyVO> vos = BeanCopyUtils.convertListTo(projectContractCloseApplyListList, ProjectContractCloseApplyVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            List<String> applyUserIds = vos.stream().map(ProjectContractCloseApplyVO::getApplyUserId).collect(Collectors.toList());
            List<UserVO> userVOList = userRedisHelper.getUserByIds(applyUserIds);
            Map<String, String> userMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            vos.forEach(p -> {
                p.setApplyUserName(userMap.get(p.getApplyUserId()));
            });
        }

        return vos;
    }
}
