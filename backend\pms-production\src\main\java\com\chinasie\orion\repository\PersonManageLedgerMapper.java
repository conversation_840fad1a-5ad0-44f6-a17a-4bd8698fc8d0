package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.PersonManageLedger;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * PersonManageLedger Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:49
 */
@Mapper
public interface PersonManageLedgerMapper extends OrionBaseMapper<PersonManageLedger> {
    Integer outNumberToDayByBaseCode(@Param("baseCode") String baseCode);

    List<PersonManageLedger> listBySouceIdList(@Param("personManageIdList") List<String> personManageIdList);
}

