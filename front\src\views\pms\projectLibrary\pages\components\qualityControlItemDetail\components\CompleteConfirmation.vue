<script setup lang="ts">
import { message, Textarea } from 'ant-design-vue';
import { ref, defineExpose } from 'vue';
const remark = ref(undefined);
const getValue = () => new Promise((resolve, reject) => {
  const msg = '质量管控项的完成情况不能为空';
  if (!remark.value) {
    message.error(msg);
    reject(msg);
  }
  if (!remark.value.trim()) {
    message.error(msg);
    reject(msg);
  }
  resolve(remark.value.trim());
});
defineExpose({
  getValue,
});
</script>

<template>
  <div class="p20">
    <div class="mb20">
      完成情况说明:
    </div>
    <Textarea
      v-model:value="remark"
      placeholder="请输入质量管控项的完成情况"
      :rows="4"
    />
  </div>
</template>

<style scoped lang="less">

</style>
