<script setup lang="ts">

import {
  computed, onMounted, provide, reactive, ref, Ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { BarsOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import {
  Breadcrumb, BreadcrumbItem, Collapse as aCollapse, CollapsePanel as aCollapsePanel,
} from 'ant-design-vue';
import { isPower } from 'lyra-component-vue3';
import BasicInfo from './components/BasicInfo.vue';
import CertificateList from './components/CertificateList.vue';
import TrainList from './components/TrainList.vue';
import JobAuthList from './components/JobAuthList.vue';
import PersonnelLedger from './components/PersonnelLedger.vue';
import Api from '/@/api';

interface DetailsDataType {
  id: string,
  fullName: string,
  userCode: string,
  [propName: string]: any
}

const router = useRouter();
const route = useRoute();
const powerCodePrefix: string = 'PMS_JSZCRYKXQ';
provide('powerCodePrefix', powerCodePrefix);
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const dataType = computed(() => route.query.type);
const dataPositionType = computed(() => route.query.positionType);
const detailsPowerData: Ref = ref(null);
provide('detailsPowerData', detailsPowerData);
const detailsData: DetailsDataType = reactive({
  id: '',
  fullName: '',
  userCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.fullName,
  projectCode: detailsData.userCode,
}));

function menuChange({ id }) {
  actionId.value = id;
}

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);
const activeKey = ref([
  '1',
  '2',
  '3',
  '4',
  '5',
]);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/basic-user').fetch({
      pageCode: 'PMSTechnicalStaffAllocationDetails',
    }, dataId.value, 'GET');
    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}
const customStyle = 'background: #fff';

const routes: Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}> = [
  {
    breadcrumbName: dataType.value === 'configuration' ? '技术配置人员' : '临时辅助人员',
    to: {
      name: 'PMSTechnicalStaffAllocation',
      query: {
        type: dataType.value,
        positionType: dataPositionType.value,
      },
    },
  },
  {
    breadcrumbName: '人员详情',
  },
];

function handleRoute(to) {
  router.push(to);
}

</script>

<template>
  <div
    v-loading="loading"
    v-get-power="{pageCode: 'PMSTechnicalStaffAllocationDetails'}"
    class="major-repairs-detail"
  >
    <div class="header-top">
      <a-space
        :size="12"
        align="baseline"
        class="row"
      >
        <h2>{{ detailsData?.fullName }}</h2>
        <span class="repair-round">{{ detailsData?.userCode }}</span>
      </a-space>
      <Breadcrumb>
        <BreadcrumbItem
          v-for="(item,index) in routes"
          :key="index"
        >
          <bars-outlined v-if="index===0" />
          <span
            v-if="item.to"
            class="link"
            @click="handleRoute(item.to)"
          >{{ item.breadcrumbName }}</span>
          <span v-else>{{ item.breadcrumbName }}</span>
        </BreadcrumbItem>
      </Breadcrumb>
    </div>
    <div
      v-if="detailsData?.id"
      class="a-collapse-box"
    >
      <a-collapse
        v-model:activeKey="activeKey"
        :bordered="false"
        style="background: rgb(255, 255, 255)"
      >
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <a-collapse-panel
          v-if="isPower('PMS_JSZCRYKXQ_container_01', powerData)"
          key="1"
          header="人员基本信息"
          :style="customStyle"
        >
          <BasicInfo />
        </a-collapse-panel>
        <a-collapse-panel
          v-if="isPower('PMS_JSZCRYKXQ_container_02', powerData)"
          key="2"
          header="证书信息"
          :style="customStyle"
        >
          <div>
            <CertificateList />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          v-if="isPower('PMS_JSZCRYKXQ_container_03', powerData)"
          key="3"
          header="培训信息"
          :style="customStyle"
        >
          <div>
            <TrainList />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          v-if="isPower('PMS_JSZCRYKXQ_container_04', powerData)"
          key="4"
          header="岗位授权信息"
          :style="customStyle"
        >
          <div>
            <JobAuthList />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          v-if="isPower('PMS_JSZCRYKXQ_container_05', powerData)"
          key="5"
          header="人员台账信息"
          :style="customStyle"
        >
          <div>
            <PersonnelLedger />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<style scoped lang="less">
.major-repairs-detail {
  padding: ~`getPrefixVar('content-margin-top')` 14px;
  background: #fff;

  .header-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')`;
    position: relative;
    .row{
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    &:after {
      content: "";
      left: 16px;
      right: 16px;
      bottom: 0;
      position: absolute;
      pointer-events: none;
      background: #E8E8E8;
      height: 1px;
    }

    h2 {
      margin-bottom: 0;
      padding-bottom: 0;
      font-size: 18px;
      font-weight: 700;
    }

    .repair-round {
      font-weight: normal;
      margin-left: 10px;
    }
  }
  .a-collapse-box{
    :deep(.details-container){
      .details-container-title{
        display: none;
      }
    }
  }
  .ant-breadcrumb{
    span:first-child{
      cursor: pointer;
    }
  }
  :deep(.ant-collapse) {
    height: calc(100vh - 170px);
    overflow-x: auto;

    &.ant-collapse-borderless {
      & > .ant-collapse-item {
        border-bottom: 0;

        .basic-card-wrap {
          margin: 0 !important;
        }

        .card-content.spacing {
          margin: 0 !important;

          .details-grid {
            padding: 0;
          }
        }
      }

      .ant-collapse-header {
        display: inline-flex;
      }

      .ant-collapse-header, .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #000000;
      }

      & > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
        margin-right: 6px;
      }

    }
  }

}
</style>
