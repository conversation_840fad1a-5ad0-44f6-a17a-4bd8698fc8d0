package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.holidays.HolidaysApi;
import com.chinasie.orion.api.holidays.domain.dto.CalculationDateRangeHolidaysDTO;
import com.chinasie.orion.api.holidays.domain.dto.CalculationEndDateDTO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.conts.*;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.dict.SchemeDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.projectscheme.FallBackDTO;
import com.chinasie.orion.domain.dto.projectscheme.IssueDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceDurationVO;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceFeedbackVO;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeOperateLogVO;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PMIService;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.UserFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.manager.*;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.operatelog.domain.entity.OperateLog;
import com.chinasie.orion.operatelog.service.OperateLogService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pas.api.domain.dto.EcrDTO;
import com.chinasie.orion.pas.api.domain.dto.EcrProjectSchemeAddDTO;
import com.chinasie.orion.pas.api.domain.request.WorkHoursReportDetailForUserRequest;
import com.chinasie.orion.pas.api.domain.vo.EcrVO;
import com.chinasie.orion.pas.api.service.EcrSchemeApiService;
import com.chinasie.orion.pas.api.service.WorkHoursReportDetailForUserService;
import com.chinasie.orion.repository.ProjectSchemeRepository;
import com.chinasie.orion.repository.production.JobManageMapperBk;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.DeptParentChildLevelVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.service.quality.QualityItemService;
import com.chinasie.orion.service.redis.CacheHandler;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.HolidaysUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.chinasie.orion.util.ResponseUtils;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.ExpirationKeysConstant.EXPIRATION_KEYS_KEY;

/**
 * ProjectSchemeServiceImpl
 *
 * @author: yangFy
 * @date: 2023/4/19 16:10
 * @description: <p>
 *
 * </p>
 */
@Service
@Slf4j
public class ProjectSchemeServiceImpl extends OrionBaseServiceImpl<ProjectSchemeRepository, ProjectScheme> implements ProjectSchemeService {

    @Autowired
    private ProjectSchemePrePostService schemePrePostService;

    @Resource
    private LyraFileBO fileBo;

    @Resource
    private UserFeignService userFeignService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private ProjectService projectService;

    @Resource
    private DictBo dictBo;

    @Resource
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private StatusBo statusBo;

    @Autowired
    private DataUpdateMessageService dataUpdateMessageService;

    @Autowired
    private SchemeStatusProcessor statusProcessor;

    @Resource
    private SendMessageManager messageManager;

    @Resource
    private SchemeCommonManager commonManager;

    @Resource
    private ProjectSchemeAuthManager projectSchemeAuthManager;

    @Resource
    private UserRedisHelper userRedisHelper;
    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private ProjectSchemeContentService projectSchemeContentService;

    @Resource
    private ProjectSchemeApplyApprovalService projectSchemeApplyApprovalService;

    @Resource
    private PlanToDemandManagementService planToDemandManagementService;

    @Resource
    private DemandManagementService demandManagementService;

    @Resource
    private PlanToQuestionManagementService planToQuestionManagementService;

    @Resource
    private QuestionManagementService questionManagementService;

    @Resource
    private PlanToRiskManagementService planToRiskManagementService;

    @Resource
    private RiskManagementService riskManagementService;

    @Autowired
    private QualityItemService qualityItemService;

    @Resource
    private ProjectSetUpService projectSetUpService;
    @Resource
    private CurrentUserHelper currentUserHelper;

    @Resource
    private ProjectSchemeRepository projectSchemeRepository;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private ProjectSchemeMilestoneNodePrePostService projectSchemeMilestoneNodePrePostService;

    @Resource
    private SearchHelper searchHelper;

    @Autowired
    private PlanToDeliverGoalsService planToDeliverGoalsService;

    @Autowired
    private DeliverGoalsService deliverGoalsService;

    @Resource
    private ProjectPlaceService placeService;

    @Resource
    private PMIService pmiService;

    @Autowired
    private ProjectSchemeFallbackService projectSchemeFallbackService;

    @Autowired
    private ProjectSchemeFeedbackService projectSchemeFeedbackService;

    @Autowired
    private WorkHoursReportDetailForUserService workHoursReportDetailForUserService;

    @Resource
    private SysParamConfRedisHelper sysParamConfRedisHelper;

    @Resource
    private SysParamConfBO sysParamConfBO;


    @Autowired
    private HolidaysApi holidaysApi;

    @Autowired
    private PlanToQualityItemService planToQualityItemService;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private PlanToReviewService planToReviewService;

    @Autowired
    private OperateLogService operateLogService;

    @Resource
    private WfInstanceManage wfInstanceManage;

    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @Autowired
    protected PmsMQProducer mqProducer;

    private static final String ROOT_ID = "0";

    @Autowired
    private HolidaysUtils holidaysUtils;

    @Autowired
    private EcrSchemeApiService ecrSchemeApiService;

    @Autowired
    private JobManageMapperBk jobManageMapperBk;

    @Autowired
    private UserBaseApiService userBaseApiService;
    @Resource
    private ProjectInitiationApiService projectInitiationApiService;
    @Resource
    private ProjectContractMilestoneService projectContractMilestoneService;
    @Resource
    private DeptBaseApiService deptBaseApiService;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    private PlanToFixedAssetService planToFixedAssetService;
    @Autowired
    private FixedAssetsService fixedAssetService;
    @Autowired
    private PlanToNcFormpurchaseService planToNcFormpurchaseService;

    @Resource
    private CacheHandler cacheHandler;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private DataStatusNBO dataStatusNBO;

    /**
     * 创建项目计划（批量）
     *
     * @param projectSchemeDTOS
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> createBatch(String pid, List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception {
        vaildateProjectScheme(projectSchemeDTOS);
        Project project = projectService.getById(projectSchemeDTOS.get(0).getProjectId());
        List<ProjectScheme> projectSchemes = CollUtil.toList();
        for (int i = 0; i < projectSchemeDTOS.size(); i++) {
            ProjectScheme pScheme = getParentProjectScheme(pid);
            ProjectSchemeDTO projectSchemeDTO = projectSchemeDTOS.get(i);
            commonManager.checkTime(pScheme, projectSchemeDTO);
            ProjectScheme projectScheme = buildProjectScheme(pid, project, i, pScheme, projectSchemeDTO);
            projectSchemes.add(projectScheme);
        }
        this.saveBatch(projectSchemes);
        return projectSchemes.stream().map(ProjectScheme::getId).collect(Collectors.toList());
    }

    @Override
    public List<String> createTreeBatch(String projectId, String parentId, List<ProjectSchemeDTO> projectSchemeDTOS) {
        vaildateProjectScheme(projectSchemeDTOS);
        if (StrUtil.isBlank(parentId)) {
            parentId = "0";
        }
        Project project = projectService.getById(projectId);
        List<ProjectScheme> projectSchemes = CollUtil.toList();
        ClassVO classVO = classRedisHelper.getClassByClassName(ProjectScheme.class.getSimpleName());

        List<ProjectScheme> projectSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, project.getId()));

        Long maxSort = projectSchemeList.stream().map(ProjectScheme::getSort).max(Comparator.comparing(Long::longValue)).orElse(0L);


        AtomicLong sort = new AtomicLong(maxSort + 1);

        //使用一个map 去存储id的对应关系
        Map<String, String> relationIdMap = new ConcurrentHashMap<>();
        ProjectScheme parent = this.getById(parentId);
        String finalParentId = parentId;
        projectSchemeDTOS.forEach(o -> {
            recursionAndClapProjectScheme(classVO, project, parent, sort.getAndIncrement(), o, finalParentId, projectSchemes, relationIdMap);
        });
        projectSchemes.forEach(o -> {
            o.setModifyId(null);
            o.setModifyTime(null);
            o.setCreateTime(null);
            o.setCreatorId(null);
            o.setOwnerId(null);
            o.setClassName(null);
        });
        this.saveBatch(projectSchemes);
        //存储前后置关系
        Set<String> tplNodeIds = relationIdMap.keySet();
        List<ProjectSchemeMilestoneNodePrePost> projectSchemeMilestoneNodePrePosts = projectSchemeMilestoneNodePrePostService.list(new LambdaQueryWrapperX<>(ProjectSchemeMilestoneNodePrePost.class)
                .in(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, tplNodeIds));

        List<ProjectSchemePrePost> projectSchemePrePosts = new ArrayList<>();
        if (!CollectionUtils.isEmpty(projectSchemeMilestoneNodePrePosts)) {
            projectSchemeMilestoneNodePrePosts.forEach(r -> {
                ProjectSchemePrePost projectSchemePrePost = BeanCopyUtils.convertTo(r, ProjectSchemePrePost::new);
                projectSchemePrePost.setId(null);
                projectSchemePrePost.setCreatorId(null);
                projectSchemePrePost.setCreateTime(null);
                projectSchemePrePost.setModifyId(null);
                projectSchemePrePost.setModifyTime(null);
                projectSchemePrePost.setClassName("ProjectSchemePrePost");
                projectSchemePrePost.setProjectId(projectId);
                if (StrUtil.isNotBlank(r.getPreSchemeId())) {
                    projectSchemePrePost.setPreSchemeId(relationIdMap.get(r.getPreSchemeId()));
                } else {
                    projectSchemePrePost.setPostSchemeId(relationIdMap.get(r.getPostSchemeId()));
                }

                projectSchemePrePost.setProjectSchemeId(relationIdMap.get(r.getProjectSchemeId()));
                projectSchemePrePosts.add(projectSchemePrePost);

            });
            schemePrePostService.saveBatch(projectSchemePrePosts);
        }

        return projectSchemes.stream().map(ProjectScheme::getId).collect(Collectors.toList());
    }

    private void recursionAndClapProjectScheme(ClassVO classVO, Project project, ProjectScheme parent, long sort, ProjectSchemeDTO projectSchemeDTO, String parentId, List<ProjectScheme> projectSchemes, Map<String, String> relationIdMap) {
        String id = String.format("%s%s", Objects.isNull(classVO) ? "" : classVO.getCode(), IdUtil.getSnowflakeNextIdStr());
        ProjectScheme projectScheme = BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new);
        relationIdMap.put(projectSchemeDTO.getId(), id);
        projectScheme.setId(id);
        projectScheme.setParentId(parentId);
        projectScheme.setProjectId(project.getId());
        projectScheme.setTopSort(0);
        projectScheme.setProjectNumber(project.getNumber());
        projectScheme.setParentChain(Objects.isNull(parent) ? "0" : parent.getParentChain() + "," + parent.getId());
        projectScheme.setSchemeNumber(Objects.isNull(parent) ? project.getNumber() + "-" + sort : parent.getSchemeNumber() + "-" + sort);
        projectScheme.setNumber(projectScheme.getSchemeNumber());
        projectScheme.setSort(sort);
        projectScheme.setLevel(projectScheme.getParentChain().split(",").length);
        projectScheme.setCircumstance(0);
        projectScheme.setStatus(Status.PENDING.getCode());

        List<String> participantUserList = projectSchemeDTO.getParticipantUserList();
        if (!CollectionUtils.isEmpty(participantUserList)) {
            projectScheme.setParticipantUsers(participantUserList.stream().collect(Collectors.joining(",")));
        }
        projectSchemes.add(projectScheme);
        if (!CollectionUtils.isEmpty(projectSchemeDTO.getChildren())) {
            AtomicLong childSort = new AtomicLong(1);
            projectSchemeDTO.getChildren().forEach(child -> {
                recursionAndClapProjectScheme(classVO, project, projectScheme, childSort.getAndIncrement(), child, id, projectSchemes, relationIdMap);
            });
        }
    }

    @NotNull
    private ProjectScheme buildProjectScheme(String pid, Project project, int i, ProjectScheme pScheme, ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = new ProjectScheme();
        BeanCopyUtils.copyProperties(projectSchemeDTO, projectScheme);
        projectScheme.setId(null);
        projectScheme.setParentId(pid);
        projectScheme.setTopSort(0);
        projectScheme.setProjectNumber(project.getNumber());
        projectScheme.setParentChain(buildParentChain(pScheme));
        projectScheme.setSchemeNumber(buildSchemeNumber(project, i + 1));
        projectScheme.setNumber(projectScheme.getSchemeNumber());
        projectScheme.setSort(countSort(project.getId(), pid, i + 1));
        projectScheme.setLevel(projectScheme.getParentChain().split(",").length);
        projectScheme.setCircumstance(0);
        projectScheme.setStatus(Status.PENDING.getCode());

        List<String> participantUserList = projectSchemeDTO.getParticipantUserList();
        if (!CollectionUtils.isEmpty(participantUserList)) {
            projectScheme.setParticipantUsers(participantUserList.stream().collect(Collectors.joining(",")));
        }
        return projectScheme;
    }

    private Long countSort(String projectId, String pid, int num) throws Exception {
        List<ProjectScheme> projectSchemeList = list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, projectId).eq(ProjectScheme::getParentId, pid));
        return (long) (projectSchemeList.size() + num);
    }


    @Nullable
    private ProjectScheme getParentProjectScheme(String pid) throws Exception {
        if (!ROOT_ID.equals(pid)) {
            ProjectScheme scheme = getById(pid);
            if (Objects.nonNull(scheme)) {
                Assert.isFalse(Status.FINISHED.getCode().equals(scheme.getStatus()), () -> new PMSException(PMSErrorCode.PMS_ERROR_EXIST_FINISHED));
                return scheme;
            }
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "不存在父计划");
        }
        return null;
    }

    private Integer buildLevel(ProjectScheme parentProjectScheme) {
        if (Objects.isNull(parentProjectScheme)) {
            return Status.LEVEL_1.getCode();
        }
        return parentProjectScheme.getLevel() + 1;
    }

    private String buildSchemeNumber(Project project, int num) throws Exception {
        String projectNumber = project.getNumber();
        List<ProjectScheme> projectSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, project.getId()));
        StringBuffer numberBuffer = new StringBuffer(projectNumber);
        numberBuffer.append(StrUtil.DASHED);
        numberBuffer.append(projectSchemeList.size() + num);
        return numberBuffer.toString();
    }

    private String buildParentChain(ProjectScheme parentProjectScheme) {
        if (Objects.isNull(parentProjectScheme)) {
            return ROOT_ID;
        }
        String parentChain = StrUtil.isBlank(parentProjectScheme.getParentChain()) ? ROOT_ID : parentProjectScheme.getParentChain();
        return new StringBuffer(parentChain).append(StrUtil.COMMA).append(parentProjectScheme.getId()).toString();
    }

    @Override
    public boolean finishBatch(List<String> projectSchemeIds) throws Exception {
        List<ProjectScheme> projectSchemeList = listByIds(projectSchemeIds);
        if (CollUtil.isEmpty(projectSchemeList)) {
            return false;
        }
        List<ProjectSchemeDTO> projectSchemeDTOS = BeanCopyUtils.convertListTo(projectSchemeList, ProjectSchemeDTO::new);
        for (ProjectSchemeDTO projectSchemeDTO : projectSchemeDTOS) {
            finish(projectSchemeDTO);
        }
        return true;
    }

    @Override
    public void dataHandle() throws Exception {
        List<ProjectScheme> schemes = list();
        if (CollUtil.isEmpty(schemes)) {
            return;
        }
        List<ProjectScheme> data = CollUtil.toList();
        schemes.forEach(item -> {
            if (StrUtil.isNotBlank(item.getRspUser())
                    && (StrUtil.isBlank(item.getRspSectionId())
                    || StrUtil.isBlank(item.getRspUserCode()))) {
                item.setRspSubDept(getOrgId("20", item.getRspUser()));
                item.setRspSectionId(getOrgId("30", item.getRspUser()));
                item.setRspUserCode(userRedisHelper.getUserById(item.getRspUser()).getCode());
                data.add(item);
            }
        });
        updateBatchById(data);
    }

    private String getOrgId(String type, String userId) {
        UserVO user = userRedisHelper.getUserById(userId);
        if (Objects.isNull(user) || CollUtil.isEmpty(user.getOrganizations())) {
            return null;
        }
        List<DeptVO> orgs = user.getOrganizations();
        return orgs.stream().filter(item -> type.equals(item.getType()))
                .findFirst()
                .orElse(new DeptVO())
                .getId();
    }


    public static boolean isWithinRange(Date start1, Date end1, Date start2, Date end2) {
        // 如果 start1 或 end1 位于 start2 和 end2 之间（包括边界），则返回 true
        if ((start1.equals(start2) || start1.after(start2)) && (end1.equals(end2) || end1.before(end2))) {
            return true;
        }

        return false;
    }

    public static Date getnewDate(Date originalDate) {
        // 使用Calendar设置时分秒为00:00:00
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 获取修改后的Date对象
        Date midnightDate = calendar.getTime();
        return midnightDate;
    }

    /**
     * 编辑
     *
     * @param projectSchemeDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseVO edit(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ResponseVO responseVO = new ResponseVO();
        ProjectScheme oldScheme = getById(projectSchemeDTO.getId());
        //校验时间
        if(projectSchemeDTO.getBeginTime().after(projectSchemeDTO.getEndTime())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "开始时间不能大于截至时间");
        }
        //查询父计划
        String parentId = oldScheme.getParentId();
        ProjectScheme parentProjectScheme = this.getById(parentId);
        if (Objects.nonNull(parentProjectScheme)) {
            if (!isWithinRange(getnewDate(projectSchemeDTO.getBeginTime()),
                    getnewDate(projectSchemeDTO.getEndTime()),
                    getnewDate(parentProjectScheme.getBeginTime()),
                    getnewDate(parentProjectScheme.getEndTime()))) {
                responseVO.setMessage("子计划划时间范围不在父计划时间范围内,已修正");
                projectSchemeDTO.setBeginTime(parentProjectScheme.getBeginTime());
                projectSchemeDTO.setEndTime(parentProjectScheme.getEndTime());
            }
        }

        String projectId = oldScheme.getProjectId();

        ProjectSetUpVO detail = projectSetUpService.detail(projectId, ProjectSetUpService.prePlanTimeRule);
        if (Objects.nonNull(detail) && StrUtil.equals(detail.getValue(), "true") && ObjectUtil.isNotNull(oldScheme.getBeginTime())) {
            List<ProjectSchemePrePost> projectSchemePrePosts = schemePrePostService.list(new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                    .eq(ProjectSchemePrePost::getProjectSchemeId, oldScheme.getId()));
            if (!CollectionUtils.isEmpty(projectSchemePrePosts)) {
                //查询前置计划
                List<String> preSchemeIds = projectSchemePrePosts.stream().filter(f -> Status.SCHEME_PRE.getCode().equals(f.getType())).map(ProjectSchemePrePost::getPreSchemeId).collect(Collectors.toList());
                List<ProjectScheme> preSchemes = this.listByIds(preSchemeIds);
                preSchemes.forEach(pre -> {
                    //校验前置计划的结束时间是否在当前计划的前面
                    if (pre.getEndTime().after(projectSchemeDTO.getBeginTime())) {
                        throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您计划的开始时间在前置计划结束时间之前，请调整");
                    }
                });
                //查询后置计划
                List<String> postSchemeIds = projectSchemePrePosts.stream().filter(f -> Status.SCHEME_POST.getCode().equals(f.getType())).map(ProjectSchemePrePost::getPreSchemeId).collect(Collectors.toList());
                List<ProjectScheme> postSchemes = this.listByIds(postSchemeIds);
                postSchemes.forEach(post -> {
                    //校验前置计划的结束时间是否在当前计划的前面
                    if (projectSchemeDTO.getEndTime().after(post.getBeginTime())) {
                        throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您计划的开始时间在前置计划结束时间之前，请调整");
                    }
                });
            }


        }


        ProjectScheme projectScheme = BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new);
        if (!Status.PENDING.getCode().equals(projectScheme.getStatus())) {
            projectScheme.setOwnerId(projectScheme.getRspUser());
        }
        List<String> participantUserList = projectSchemeDTO.getParticipantUserList();
        if (!CollectionUtils.isEmpty(participantUserList)) {
            projectScheme.setParticipantUsers(participantUserList.stream().collect(Collectors.joining(",")));
        }

        Long durationDays = DateUtil.betweenDay(projectScheme.getBeginTime(), projectScheme.getEndTime(), true)+1;
        if (ObjectUtil.isNotEmpty(durationDays)) {
            projectScheme.setDurationDays(durationDays.intValue());
        }
        //计算逾期次数
        if((Objects.equals(oldScheme.getStatus(), Status.PUBLISHED.getCode()) || Objects.equals(oldScheme.getStatus(), Status.EXECUTING.getCode()))
                && Objects.equals(oldScheme.getCircumstance(), Status.CIRCUMSTANCE_OVERD.getCode())
                && oldScheme.getEndTime().before(projectSchemeDTO.getEndTime())){
            projectScheme.setOverdueCount(oldScheme.getOverdueCount() == null?1:oldScheme.getOverdueCount()+1);
        }
        //查找责任人code
        if(StringUtils.hasText(projectScheme.getRspUser())){
            projectScheme.setRspUserCode(userBaseApiService.getUserById(projectScheme.getRspUser()).getCode());
        }

        if (this.updateById(projectScheme)) {
            //更新后置计划的计划开始时间和计划完成时间
            updatePostSchemeTime(projectScheme, oldScheme);

            if (Status.PUBLISHED.getCode().equals(projectScheme.getStatus())) {
                messageManager.sendMsg(MsgBusinessTypeEnum.EDIT, SchemeMsgDTO.builder().projectSchemeList(CollUtil.toList(oldScheme)).build());
                if (!oldScheme.getRspUser().equals(projectScheme.getRspUser())) {
                    //发送待办到新责任人
                    messageManager.sendMsg(MsgBusinessTypeEnum.SEND_DOWN, SchemeMsgDTO.builder().projectSchemeList(CollUtil.toList(projectScheme)).build());
                    //消除原有责任人待办
                    messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, oldScheme.getId(), oldScheme.getRspUser());
                }
            }
            if (Status.ICON_MILESTONE.getCode().equals(projectSchemeDTO.getType())) {
                // 发送里程碑数据更新通知 - 关联project数据需处理
                dataUpdateMessageService.sendDataUpdatedMessage(Project.class.getSimpleName(), projectSchemeDTO.getProjectId());
            }
        }
        responseVO.setSuccess(Boolean.TRUE);
        return responseVO;
    }

    private void updatePostSchemeTime(ProjectScheme projectScheme, ProjectScheme oldProjectScheme) throws Exception {
        //从立项论证过来的数据计划开始结束时间可能会为空，编辑该计划时将前后置计划的计划时间为空的计划也更新
        List<ProjectScheme> updateBeginTimeDataList = new ArrayList<>();
        if (oldProjectScheme.getBeginTime() == null || oldProjectScheme.getEndTime() == null) {
            Map<String, Map<Integer, List<ProjectSchemePrePost>>> projectSchemePrePostMap = schemePrePostService.list(new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                            .eq(ProjectSchemePrePost::getProjectId, oldProjectScheme.getProjectId()))
                    .stream().collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId,
                            Collectors.groupingBy(ProjectSchemePrePost::getType)));
            Map<String, ProjectScheme> projectSchemeMap = this.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                            .select(ProjectScheme::getId, ProjectScheme::getBeginTime, ProjectScheme::getEndTime, ProjectScheme::getDurationDays)
                            .eq(ProjectScheme::getProjectId, oldProjectScheme.getProjectId())
                            .and(a -> a.isNull(ProjectScheme::getBeginTime).or().isNull(ProjectScheme::getEndTime))).stream()
                    .collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));
            Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap = projectSchemePrePostMap.get(projectScheme.getId());
            if (prePostTypeMap != null) {
                Project project = projectService.getById(oldProjectScheme.getProjectId());
                if (ObjectUtil.isEmpty(project)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在或者已经被删除！");
                }
                //计划的开始结束时间要在项目的开始结束时间范围内
                LocalDate maxEndTime = DateUtil.parseLocalDateTime(DateUtil.formatDate(project.getProjectEndTime()), DatePattern.NORM_DATE_PATTERN).toLocalDate();
                LocalDate minBeginTime = DateUtil.parseLocalDateTime(DateUtil.formatDate(project.getProjectStartTime()), DatePattern.NORM_DATE_PATTERN).toLocalDate();
                CalculationDateRangeHolidaysDTO calculationDateRangeHolidaysDTO = new CalculationDateRangeHolidaysDTO();
                calculationDateRangeHolidaysDTO.setStartDate(project.getProjectStartTime());
                calculationDateRangeHolidaysDTO.setEndDate(project.getProjectEndTime());
                List<String> holidayList = holidaysApi.calculationEndDateAndNextStartDate(calculationDateRangeHolidaysDTO)
                        .getResult().stream().filter(f -> ObjectUtil.isNotEmpty(f.getDate()))
                        .map(m -> DateUtil.formatDate(m.getDate()))
                        .collect(Collectors.toList());
                setPreProjectSchemeTime(projectScheme, prePostTypeMap, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
                setPostProjectSchemeTime(projectScheme, prePostTypeMap, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
            }


        }
        if (CollectionUtil.isNotEmpty(updateBeginTimeDataList)) {
            this.updateBatchById(updateBeginTimeDataList);
        }
    }

    /**
     * 通过前置关系设置时间
     *
     * @param projectScheme
     * @param prePostTypeMap
     * @param projectSchemePrePostMap
     * @param projectSchemeMap
     * @param updateBeginTimeDataList
     * @param minBeginTime
     * @param maxEndTime
     * @param holidayList
     * @throws Exception
     */
    private void setPreProjectSchemeTime(ProjectScheme projectScheme,
                                         Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap,
                                         Map<String, Map<Integer, List<ProjectSchemePrePost>>> projectSchemePrePostMap,
                                         Map<String, ProjectScheme> projectSchemeMap,
                                         List<ProjectScheme> updateBeginTimeDataList,
                                         LocalDate minBeginTime,
                                         LocalDate maxEndTime,
                                         List<String> holidayList) throws Exception {
        //前置
        if (prePostTypeMap.containsKey(Status.SCHEME_PRE.getCode())) {
            List<String> preIdList = prePostTypeMap.get(Status.SCHEME_PRE.getCode()).stream().map(ProjectSchemePrePost::getPreSchemeId).distinct().collect(Collectors.toList());
            for (String preId : preIdList) {
                ProjectScheme preProjectScheme = projectSchemeMap.get(preId);
                if (ObjectUtil.isNotEmpty(preProjectScheme) && ObjectUtil.isEmpty(preProjectScheme.getEndTime())) {
                    Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap1 = projectSchemePrePostMap.get(preProjectScheme.getId());
                    preProjectScheme.setEndTime(projectScheme.getBeginTime());
                    if (ObjectUtil.isNotEmpty(preProjectScheme.getDurationDays())) {
                        holidaysUtils.calculationStartDateOfWorkDay(preProjectScheme, preProjectScheme.getDurationDays(), holidayList, minBeginTime);
                        setPreProjectSchemeTime(preProjectScheme, prePostTypeMap1, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
                    }
                    setPostProjectSchemeTime(preProjectScheme, prePostTypeMap1, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);

                    updateBeginTimeDataList.add(preProjectScheme);
                }
            }
        }
    }

    /**
     * 通过后置关系设置时间
     *
     * @param projectScheme
     * @param prePostTypeMap
     * @param projectSchemePrePostMap
     * @param projectSchemeMap
     * @param updateBeginTimeDataList
     * @param minBeginTime
     * @param maxEndTime
     * @param holidayList
     * @throws Exception
     */
    private void setPostProjectSchemeTime(ProjectScheme projectScheme,
                                          Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap,
                                          Map<String, Map<Integer, List<ProjectSchemePrePost>>> projectSchemePrePostMap,
                                          Map<String, ProjectScheme> projectSchemeMap,
                                          List<ProjectScheme> updateBeginTimeDataList,
                                          LocalDate minBeginTime,
                                          LocalDate maxEndTime,
                                          List<String> holidayList) throws Exception {
        if (prePostTypeMap.containsKey(Status.SCHEME_POST.getCode())) {
            List<String> postIdList = prePostTypeMap.get(Status.SCHEME_POST.getCode()).stream().map(ProjectSchemePrePost::getPostSchemeId).distinct().collect(Collectors.toList());
            for (String postId : postIdList) {
                ProjectScheme postProjectScheme = projectSchemeMap.get(postId);
                if (ObjectUtil.isNotEmpty(postProjectScheme) && ObjectUtil.isEmpty(postProjectScheme.getBeginTime())) {
                    Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap1 = projectSchemePrePostMap.get(projectScheme.getId());

                    postProjectScheme.setBeginTime(projectScheme.getEndTime());
                    if (ObjectUtil.isNotEmpty(postProjectScheme.getDurationDays())) {
                        holidaysUtils.calculationEndDateOfWorkDay(postProjectScheme, postProjectScheme.getDurationDays(), holidayList, maxEndTime);
                        setPostProjectSchemeTime(postProjectScheme, prePostTypeMap1, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
                    }
                    setPreProjectSchemeTime(postProjectScheme, prePostTypeMap1, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
                    updateBeginTimeDataList.add(postProjectScheme);
                }
            }
        }
    }


    /**
     * 删除(批量)
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByIds(List<String> ids) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            return Boolean.TRUE;
        }
        List<ProjectScheme> childScheme = this.list(new LambdaQueryWrapper<>(ProjectScheme.class).in(ProjectScheme::getParentId, ids));
        List<String> childIds = childScheme.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        ids.addAll(childIds);
        List<String> delIds = ids.stream().distinct().collect(Collectors.toList());
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<ProjectScheme> schemes = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .in(ProjectScheme::getId, ids));

        Long count = jobManageMapperBk.exsitJobMangeBySchemeIdList(delIds.stream().collect(Collectors.joining(",")));
        if(count > 0){
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "计划下已有作业，处理后再移除");
        }
        if (CollUtil.isNotEmpty(schemes) && schemes.stream().map(ProjectScheme::getCreatorId).anyMatch(item -> !item.equals(currentUserId))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "存在非本人创建的计划，不能删除");
        }
        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        // TODO 需要判断当前节点是否发起节点或者当前节点处理人是否创建人
        wrapper.ne(ProjectScheme::getStatus, Status.PENDING.getCode()).ne(ProjectScheme::getStatus, Status.FALLBACK.getCode()).in(ProjectScheme::getId, delIds);
        List<ProjectScheme> nonPe = list(wrapper);
        if (CollUtil.isNotEmpty(nonPe)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "存在已发布或已完成的计划，不能删除");
        }
        schemePrePostService.remove(new LambdaQueryWrapper<>(ProjectSchemePrePost.class).and(sub -> sub.in(ProjectSchemePrePost::getProjectSchemeId, delIds).or().in(ProjectSchemePrePost::getPreSchemeId, delIds)));
        //代办消失
        messageManager.clearToDo(MsgBusinessTypeEnum.FALLBACK, delIds);
        return this.removeBatchByIds(delIds);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public ProjectSchemeVO getDetail(String id, String pageCode) throws Exception {
        statusProcessor.statusHandleById(CollUtil.toList(id));
        ProjectScheme projectScheme = this.getById(id);
        if(ObjectUtil.isNull(projectScheme)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "数据不存在或者已删除");
        }
        ProjectSchemeVO schemeVO = BeanCopyUtils.convertTo(projectScheme, ProjectSchemeVO::new);
        String parentId = projectScheme.getParentId();
        if (!ObjectUtil.equals("0", parentId) &&  StringUtils.hasText(parentId)) {
            ProjectScheme parent = this.getById(parentId);
            if(ObjectUtil.isNotEmpty(parent)){
                schemeVO.setParentName(parent.getName());
            }
        }
        List<ProjectSchemeContent> contents = projectSchemeContentService.list(new LambdaQueryWrapper<>(ProjectSchemeContent.class).eq(ProjectSchemeContent::getProjectSchemeId, id));
        if (CollectionUtil.isNotEmpty(contents)) {
            schemeVO.setSchemeContentVOList(BeanCopyUtils.convertListTo(contents, ProjectSchemeContentVO::new));
        }
        //获取前置计划
        LambdaQueryWrapper<ProjectSchemePrePost> prePostWrapper = new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                .eq(ProjectSchemePrePost::getProjectId, projectScheme.getProjectId())
                .eq(ProjectSchemePrePost::getProjectSchemeId, id);
        List<ProjectSchemePrePost> prePostList = schemePrePostService.list(prePostWrapper);
        if (CollectionUtil.isNotEmpty(prePostList)) {
            List<String> prePostIdList = new ArrayList<>();
            for (ProjectSchemePrePost projectSchemePrePost : prePostList) {
                if (Status.SCHEME_PRE.getCode().equals(projectSchemePrePost.getType())) {
                    prePostIdList.add(projectSchemePrePost.getPreSchemeId());
                } else {
                    prePostIdList.add(projectSchemePrePost.getPostSchemeId());
                }
            }
            List<ProjectSchemeVO> prePostSchemeVOS = BeanCopyUtils.convertListTo(this.listByIds(prePostIdList), ProjectSchemeVO::new);
            commonManager.codeMapping(prePostSchemeVOS);
            Map<String, ProjectSchemeVO> projectSchemeVOMap = prePostSchemeVOS.stream().peek(
                            p -> p.setTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(p.getNodeType())))
//                    .peek(prePostSchemeVO -> commonManager.codeMapping(prePostSchemeVO))
                    .collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));

            List<ProjectSchemePrePostVO> result = BeanCopyUtils.convertListTo(prePostList, ProjectSchemePrePostVO::new);
            Map<Integer, List<ProjectSchemePrePostVO>> prePostSchemeMap = result.stream().peek(prePost -> {
                ProjectSchemeVO vo;
                if (Status.SCHEME_PRE.getCode().equals(prePost.getType())) {
                    vo = projectSchemeVOMap.get(prePost.getPreSchemeId());
                } else {
                    vo = projectSchemeVOMap.get(prePost.getPostSchemeId());
                }
                if (null == vo) {
                    return;
                }
                prePost.setPreSchemeName(vo.getName());
                prePost.setRspSubDept(vo.getRspSubDept());
                prePost.setRspSubDeptName(vo.getRspSubDeptName());
                prePost.setRspUser(vo.getRspUser());
                prePost.setRspUserName(vo.getRspUserName());
                prePost.setSchemeBeginTime(vo.getBeginTime());
                prePost.setSchemeEndTime(vo.getEndTime());
                prePost.setSchemeDesc(vo.getSchemeDesc());
                prePost.setProjectSchemeName(projectScheme.getName());
                prePost.setTypeName(Status.codeMapping(prePost.getType()));
            }).collect(Collectors.groupingBy(ProjectSchemePrePostVO::getType, Collectors.toList()));
            schemeVO.setSchemePrePostVOList(prePostSchemeMap.getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
            schemeVO.setSchemePostVOList(prePostSchemeMap.getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
            schemeVO.setIsPrePost(prePostSchemeMap.containsKey(Status.SCHEME_PRE.getCode()));
            schemeVO.setProjectSchemePrePostVOS(prePostSchemeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        }

//        Map<String, ProjectSchemeApplyApproval> isApproveMap = isApprove(projectScheme.getProjectId(), Arrays.asList(projectScheme.getId()));
//        schemeVO.setApproveStatus(applyApprovalService.getApprovalStatus(projectScheme.getId()));


        /**
         * 映射名称
         */
        commonManager.codeMapping(ImmutableList.of(schemeVO));

        projectSchemeAuthManager.auth(schemeVO, pageCode, projectScheme.getProjectId());

        //获取超时原因附件
        List<FileVO> responseDTOByGetFiles = fileBo.getFilesByDataId(id);
        if (!Objects.isNull(responseDTOByGetFiles) && !CollectionUtils.isEmpty(responseDTOByGetFiles)) {
            List<FileVO> filesResult = responseDTOByGetFiles;
            Map<String, List<FileVO>> filesMap = filesResult.stream().collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getDataType()).orElse("")));
            schemeVO.setDelayEndReasonFiles(filesMap.getOrDefault(FileConstant.FILETYPE_PROJECT_SCHEME_DELAY, new ArrayList<>()));
            schemeVO.setCompleteFiles(filesMap.getOrDefault(FileConstant.FILETYPE_PROJECT_SCHEME, new ArrayList<>()));
            schemeVO.setContentFiles(filesMap.getOrDefault(FileConstant.FILETYPE_PROJECT_SCHEME_RECORD, new ArrayList<>()));
            schemeVO.setAttachments(filesResult);
        } else {
            schemeVO.setDelayEndReasonFiles(new ArrayList<>());
            schemeVO.setCompleteFiles(new ArrayList<>());
            schemeVO.setContentFiles(new ArrayList<>());
            schemeVO.setAttachments(new ArrayList<>());
        }
        String participantUsers = projectScheme.getParticipantUsers();
        if (StringUtils.hasText(participantUsers)) {
            List<String> participantUserIdList = Arrays.asList(participantUsers.split(","));
            Map<String, UserVO> idToEntityMap = userRedisHelper.getUserMapByUserIds(CurrentUserHelper.getOrgId(), participantUserIdList);
            List<String> names = new ArrayList<>();
            for (String s : participantUserIdList) {
                UserVO entityVo = idToEntityMap.get(s);
                if (ObjectUtil.isNotNull(entityVo)) {
                    names.add(entityVo.getName());
                }
            }
            schemeVO.setParticipantUserList(participantUserIdList);
            schemeVO.setParticipantUserNames(String.join(",", names));
        }
        String processType = "businessProcess";//sysParamConfBO.getProcessType();
        Boolean isManager = projectRoleUserService.isPmRoleUser(schemeVO.getProjectId());

        Map<String, DictValueVO> copeDict = dictRedisHelper.getDictMapByCode(SchemeDict.ENFORECE_SCORE);
        Map<String, DictValueVO> typeDict = dictRedisHelper.getDictMapByCode(SchemeDict.ENFORECE_TYPE);
        Map<String, DictValueVO> contentDict = dictRedisHelper.getDictMapByCode(SchemeDict.WORK_CONTENT);
        schemeVO.setWorkContentName(contentDict.getOrDefault(schemeVO.getWorkContent(),new DictValueVO()).getDescription());
        schemeVO.setEnforceScopeName(copeDict.getOrDefault(schemeVO.getEnforceScope(),new DictValueVO()).getDescription());
        schemeVO.setEnforceTypeName(typeDict.getOrDefault(schemeVO.getEnforceType(),new DictValueVO()).getDescription());
        schemeVO.setProcessType(processType);
        schemeVO.setIsManager(isManager);
        String rspSubDept = schemeVO.getRspSubDept();
        if(StringUtils.hasText(rspSubDept)){
            DeptVO deptVO= deptRedisHelper.getDeptById(rspSubDept);
            schemeVO.setRspSubDeptCode(null ==  deptVO?"":deptVO.getDeptCode() );
        }
        if (StrUtil.isNotBlank(schemeVO.getProjectId())) {
            Project project = projectService.getById(schemeVO.getProjectId());
            schemeVO.setProjectName(project.getName());
        }
        if(schemeVO.getIsWork() == null){
            schemeVO.setIsWork(0);
        }
        return schemeVO;
    }


    /**
     * 上移
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized Boolean up(String id) throws Exception {
        ProjectScheme scheme = this.getById(id);
        List<ProjectScheme> upSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, scheme.getProjectId())
                .eq(ProjectScheme::getParentId, scheme.getParentId())
                .lt(ProjectScheme::getSort, scheme.getSort())
                .orderByDesc(ProjectScheme::getSort));
        if (CollectionUtil.isEmpty(upSchemeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_MAX_SORT);
        }
        ProjectScheme upScheme = upSchemeList.get(0);
        long upSort = upScheme.getSort();
        upScheme.setSort(scheme.getSort());
        scheme.setSort(upSort);
        return this.updateBatchById(Arrays.asList(scheme, upScheme));
    }

    /**
     * 下移
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean down(String id) throws Exception {
        ProjectScheme scheme = this.getById(id);
        List<ProjectScheme> downSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, scheme.getProjectId())
                .eq(ProjectScheme::getParentId, scheme.getParentId())
                .gt(ProjectScheme::getSort, scheme.getSort())
                .orderByAsc(ProjectScheme::getSort));
        if (CollectionUtil.isEmpty(downSchemeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_MIN_SORT);
        }
        ProjectScheme downScheme = downSchemeList.get(0);
        long downSort = downScheme.getSort();
        downScheme.setSort(scheme.getSort());
        scheme.setSort(downSort);
        return this.updateBatchById(Arrays.asList(scheme, downScheme));
    }

    /**
     * 置顶
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public synchronized Boolean top(String id) throws Exception {
        ProjectScheme projectScheme = this.getById(id);
        /**
         * 0:取消置顶
         * topSort越大优先级越高
         */
        Integer maxTop = 0;
        List<ProjectScheme> dbs = this.list(new LambdaQueryWrapper<>(ProjectScheme.class).eq(ProjectScheme::getProjectId, projectScheme.getProjectId()).orderByDesc(ProjectScheme::getTopSort));
        if (CollectionUtils.isEmpty(dbs)) {
            maxTop = 0;
        } else {
            maxTop = dbs.get(0).getTopSort();
        }
        projectScheme.setTopSort(maxTop + 1);
        return this.updateById(projectScheme);
    }

    /**
     * 计划下发
     *
     * @param issueDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean issue(IssueDTO issueDTO) throws Exception {
        List<ProjectScheme> projectSchemes = getProjectSchemesByProjectId(issueDTO.getProjectId());
        ProjectScheme scheme = this.getById(issueDTO.getSchemeId());
        if (!scheme.getParentId().equals("0")) {
            List<ProjectScheme> view = new ArrayList<>();
            getParentScheme(view, scheme, projectSchemes);
            List<ProjectScheme> projectSchemeList = view.stream().filter(item -> item.getStatus().equals(Status.PENDING.getCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(projectSchemeList)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "请先下发父级计划");
            }
        }
        List<ProjectScheme> child = new ArrayList<>();
        //获取计划所有子项
        getSchemesAllChild(child, scheme, projectSchemes);

        List<ProjectScheme> pendingScheme = child.stream().filter(item -> item.getStatus().equals(Status.PENDING.getCode())
                && !item.getCircumstance().equals(Status.ISSUE_APPROVAL.getCode())).collect(Collectors.toList());
        if (scheme.getStatus().equals(Status.PENDING.getCode())) {
            pendingScheme.add(scheme);
        }
        if (CollUtil.isEmpty(pendingScheme)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "无可下发计划");
        }
        //责任人校验
        List<ProjectRoleUser> projectUserInfoVOS = projectRoleUserService.list(new LambdaQueryWrapper<ProjectRoleUser>()
                .eq(ProjectRoleUser::getProjectId,pendingScheme.get(0).getProjectId()));
        if(CollectionUtil.isEmpty(projectUserInfoVOS)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目未分配负责人");
        }
        Map<String,List<ProjectRoleUser>> map = projectUserInfoVOS.stream().collect(Collectors.groupingBy(ProjectRoleUser::getUserId));
        for(ProjectScheme projectScheme:pendingScheme){
            //校验时间
            if(projectScheme.getBeginTime().after(projectScheme.getEndTime())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "开始时间不能大于截至时间");
            }
            if(CollectionUtil.isEmpty(map.get(projectScheme.getRspUser()))){
                throw new PMSException(PMSErrorCode.PMS_ERR, "项目未分配该计划负责人，计划名称【"+projectScheme.getName()+"】");
            }
            //校验必填字段
            if(!Objects.isNull(projectScheme.getIsWork()) && projectScheme.getIsWork() == 1){
                checkScheme(projectScheme);
            }

        }
        issue(issueDTO, pendingScheme);
        ProjectVO projectVO = projectService.getProjectDetail(issueDTO.getProjectId(), "");
        if (Objects.isNull(projectVO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"计划相关项目数据不存在或者已删除");
        }
        pendingScheme.forEach(item->{
            cacheHandler.setKeyWithExpiration(EXPIRATION_KEYS_KEY+item.getId());
            redisTemplate.opsForSet().add(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, item.getId(),item.getId()); // 默认添加到 未处理的缓存集合中
            //下发后通知负责人
            mscBuildHandlerManager.send(item, MessageNodeNumberDict.PMS_PLAN_ALLOCATION_HEAD,projectVO.getName());
//            if (ProjectSchemeWorkContentType.REPAIR.equals(item.getWorkContent())){
//                //有大修作业下发后通知大修指挥部的人
//                mscBuildHandlerManager.send(item, MessageNodeNumberDict.PMS_PLAN_ALLOCATION_LEADER,projectVO.getName());
//            }
        });

        return true;
    }

    private void checkScheme(ProjectScheme scheme) {
        if(!StringUtils.hasText(scheme.getEnforceType())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "["+scheme.getName()+"]请选择实施类型");
        }
        if(!StringUtils.hasText(scheme.getEnforceBasePlace())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "["+scheme.getName()+"]请选择实施地点");
        }
        if(!StringUtils.hasText(scheme.getEnforceScope())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "["+scheme.getName()+"]请选择实施区域");
        }
        if(!StringUtils.hasText(scheme.getWorkContent())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "["+scheme.getName()+"]请选择工作内容");
        }

    }

    /**
     * 计划下发
     *
     * @param issueDTO
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public Boolean issueAgain(IssueDTO issueDTO) {
//        List<ProjectScheme> schemes = this.listByIds(issueDTO.getSchemeIds());
//        boolean nonPending = schemes.stream().anyMatch(scheme -> !Status.FALLBACK.getCode().equals(scheme.getStatus()));
//        if (nonPending) {
//            throw new BaseException(PMSErrorCode.PMS_ERROR_WITHOUT_FALLBACK);
//        }
//        issue(issueDTO, schemes);
//        return true;
//    }
    private void issue(IssueDTO issueDTO, List<ProjectScheme> schemes) {
        String processType = "businessProcess";//sysParamConfBO.getProcessType();
        List<String> userIdList  = new ArrayList<>();
        schemes.forEach(item->{
            if(StringUtils.hasText(issueDTO.getCreatorId())){
                userIdList.add(issueDTO.getCreatorId());
            }
            if(StringUtils.hasText(item.getRspUser())){
                userIdList.add(item.getRspUser());
            }
        });
        Map<String,String> idToName =new HashMap<>();
        if (CollectionUtil.isNotEmpty(userIdList)) {
            List<String> userIds = userIdList.stream().distinct().collect(Collectors.toList());
            List<SimpleUserVO> simpleUsers  =  userBaseApiService.getUserByIds(userIds);
            idToName =simpleUsers.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));
        }
        Map<String, String> finalIdToName = idToName;
        schemes.forEach(item->{
            if(StringUtils.hasText(issueDTO.getCreatorId())){
                item.setCreatorId(issueDTO.getCreatorId());
                item.setCreatorName(finalIdToName.get(issueDTO.getCreatorId()));
            }
            if(StringUtils.hasText(item.getRspUser())){
                item.setRspUserName(finalIdToName.get(item.getRspUser()));
            }
        });

        if (ProjectSchemeProcessEnum.WORK_PROCESS.getValue().equals(processType) && issueDTO.getIsProcess()) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<ProjectSchemeFeedback> projectSchemeFeedbackList = new ArrayList<>();
            wfInstanceManage.process(schemes, SchemeDict.ISSUE_PROCESS);
            for (ProjectScheme scheme : schemes) {
                scheme.setStatus(Status.PENDING.getCode());
                scheme.setCircumstance(Status.ISSUE_APPROVAL.getCode());
                //流程通过为准时间
                scheme.setIssueTime(new Date());
                scheme.setOwnerId(scheme.getRspUser());
                scheme.setIssuedUser(currentUserId);
                scheme.setIssueRemindInterval(issueDTO.getIssueRemindInterval());
                scheme.setIssueRemindIntervalUnit(issueDTO.getIssueRemindIntervalUnit());
                addProjectSchemeFeedback(scheme, projectSchemeFeedbackList);
            }
            this.updateBatchById(schemes);
            projectSchemeFeedbackService.saveBatch(projectSchemeFeedbackList);
            // messageManager.sendMsg(MsgBusinessTypeEnum.SEND_DOWN, SchemeMsgDTO.builder().projectSchemeList(schemes).recipientIds(issueDTO.getBeNotifiedPersons()).build());
        } else {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<ProjectSchemeFeedback> projectSchemeFeedbackList = new ArrayList<>();
            for (ProjectScheme scheme : schemes) {
                scheme.setStatus(Status.PUBLISHED.getCode());
                scheme.setCircumstance(Status.CIRCUMSTANCE_WAIT.getCode());
                scheme.setIssueTime(new Date());
                scheme.setOwnerId(scheme.getRspUser());
                scheme.setIssuedUser(currentUserId);
                scheme.setIssueRemindInterval(issueDTO.getIssueRemindInterval());
                scheme.setIssueRemindIntervalUnit(issueDTO.getIssueRemindIntervalUnit());
                scheme.setIssueRemindTime(issueDTO.getIssueRemindTime());
                addProjectSchemeFeedback(scheme, projectSchemeFeedbackList);
            }
            this.updateBatchById(schemes);
            projectSchemeFeedbackService.saveBatch(projectSchemeFeedbackList);



            messageManager.sendMsg(MsgBusinessTypeEnum.SEND_DOWN, SchemeMsgDTO.builder().projectSchemeList(schemes).recipientIds(issueDTO.getBeNotifiedPersons()).build());
            messageManager.clearToDo(MsgBusinessTypeEnum.FALLBACK, schemes.stream().map(ProjectScheme::getId).collect(Collectors.toList()));
        }
    }

    private void addProjectSchemeFeedback(ProjectScheme scheme, List<ProjectSchemeFeedback> projectSchemeFeedbackList) {
        if (scheme.getIssueRemindInterval() != null
                && scheme.getIssueRemindInterval() != 0
                && StrUtil.isNotBlank(scheme.getIssueRemindIntervalUnit())
                && StrUtil.isNotBlank(scheme.getRspUser())) {
            ProjectSchemeFeedback projectSchemeFeedback = new ProjectSchemeFeedback();
            projectSchemeFeedback.setProjectSchemeId(scheme.getId());
            projectSchemeFeedback.setIsExecute(false);
            Calendar nextExecuteTime = Calendar.getInstance();
            nextExecuteTime.setTime(scheme.getIssueTime());
            if ("时".equals(scheme.getIssueRemindIntervalUnit())) {
                nextExecuteTime.add(Calendar.HOUR_OF_DAY, scheme.getIssueRemindInterval());
            } else if ("天".equals(scheme.getIssueRemindIntervalUnit())) {
                nextExecuteTime.add(Calendar.DATE, scheme.getIssueRemindInterval());
            } else if ("周".equals(scheme.getIssueRemindIntervalUnit())) {
                nextExecuteTime.add(Calendar.HOUR_OF_DAY, scheme.getIssueRemindInterval() * 7);
            } else {
                nextExecuteTime.add(Calendar.MONTH, scheme.getIssueRemindInterval());
            }
            projectSchemeFeedback.setNextExecuteTime(DateUtil.parse(DateUtil.formatDateTime(nextExecuteTime.getTime()), DatePattern.NORM_DATETIME_MINUTE_FORMAT));
            projectSchemeFeedbackList.add(projectSchemeFeedback);
        }
    }

    /**
     * 取消置顶
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean unTop(String id) throws Exception {
        ProjectScheme projectScheme = this.getById(id);
        /**
         * 0:取消置顶
         * topSort越大优先级越高
         */
        projectScheme.setTopSort(0);
        return this.updateById(projectScheme);
    }

    /**
     * 树化
     *
     * @param source 源
     * @return List<E>
     */
    public static List<ProjectSchemeVO> toTree(List<ProjectSchemeVO> source) {
        if (Objects.isNull(source) || source.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, ProjectSchemeVO> map = source.stream().collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));
        Set<ProjectSchemeVO> root = new HashSet<>();

        source.forEach(d -> {
            String parentId = d.getParentId();
            if (map.containsKey(parentId)) {
                ProjectSchemeVO parent = map.get(parentId);
                List<ProjectSchemeVO> child = parent.getChildren();
                if (child == null) {
                    child = new ArrayList<>();
                }
                child.add(d);
                child.sort(Comparator.comparing(ProjectSchemeVO::getCreateTime, Comparator.nullsLast(Date::compareTo)));
                parent.setChildren(child);
                root.remove(d);
            } else {
                root.add(d);
            }
        });
        ArrayList<ProjectSchemeVO> result = new ArrayList<>(root);
        result.sort(Comparator.comparing(ProjectSchemeVO::getTopSort).reversed().thenComparing(ProjectSchemeVO::getSort).reversed());
        return result;
    }


    /**
     * 根据结束时间定时更新计划情况：正常、已临期、已逾期
     * 说明：根据已发布状态的的项目计划实行计划情况状态更新
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean refreshCircumstance() {
        /**
         * 已逾期计划
         * 说明：计划结束时间小于当前时间
         */
        LambdaQueryWrapper<ProjectScheme> overWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        overWrapper.eq(ProjectScheme::getStatus, Status.PUBLISHED).lt(ProjectScheme::getEndTime, new Date());
        /**
         * 已临期计划
         * 说明：计划时间与当前时间小于五天
         */
        LambdaQueryWrapper<ProjectScheme> nearWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        nearWrapper.eq(ProjectScheme::getStatus, Status.PUBLISHED).lt(ProjectScheme::getEndTime, DateUtil.offsetDay(new Date(), 5));
        //计划，获取count数量，再定时固定数量更新数据。。。
        return false;
    }

    @Override
    public ProjectSchemeVO parentTree(String id) {
        ProjectScheme projectScheme = this.getById(id);
        List<ProjectScheme> parentSchemes = getParentSchemes(projectScheme);
        if (CollectionUtil.isEmpty(parentSchemes)) {
            return null;
        }
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(parentSchemes, ProjectSchemeVO::new);

        codeMapping(schemeVOS);

        List<ProjectSchemeVO> projectSchemeVOS = toTree(schemeVOS);

        return projectSchemeVOS.get(0);
    }

    @Override
    public List<ProjectSchemeVO> tree(ProjectSchemeDTO schemeDTO) throws Exception {
        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        wrapper.eq(ProjectScheme::getProjectId, schemeDTO.getProjectId()).like(ProjectScheme::getParentChain, schemeDTO.getParentId()).orderByDesc(ProjectScheme::getTopSort).orderByAsc(ProjectScheme::getSort);
        List<ProjectScheme> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> ids = list.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        List<ProjectSchemePrePost> prePostList = schemePrePostService.list(new LambdaQueryWrapper<>(ProjectSchemePrePost.class).in(ProjectSchemePrePost::getProjectSchemeId, ids));
        Map<String, List<ProjectSchemePrePostVO>> prePostMap = new HashMap<>();
//        Map<String, List<ProjectSchemeContentVO>> contentMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(prePostList)) {
            prePostMap = prePostList.stream().collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId, Collectors.mapping(ProjectSchemePrePostVO::new, Collectors.toList())));
        }

//        List<ProjectSchemeContent> contents = projectSchemeContentService.queryForList(new LambdaQueryWrapper<>(ProjectSchemeContent.class).in(ProjectSchemeContent::getProjectSchemeId, ids));
//        if (CollectionUtil.isNotEmpty(contents)) {
//            contentMap = contents.stream().collect(Collectors.groupingBy(ProjectSchemeContent::getProjectSchemeId, Collectors.mapping(ProjectSchemeContentVO::new, Collectors.toList())));
//        }
        Map<String, List<ProjectSchemePrePostVO>> finalPrePostMap = prePostMap;
//        Map<String, List<ProjectSchemeContentVO>> finalContentMap = contentMap;
        String processType = sysParamConfBO.getProcessType();
        //基础属性赋值
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(list, ProjectSchemeVO::new);
        commonManager.codeMapping(schemeVOS);
        schemeVOS.forEach(scheme -> {
            scheme.setSchemePrePostVOList(null == finalPrePostMap.get(scheme.getId()) ? new ArrayList<>() : finalPrePostMap.get(scheme.getId()));
        });
        return toTree(schemeVOS);
    }

    /**
     * 计划完成确认
     *
     * @param projectSchemeDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finish(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        String processType ="businessProcess"; //sysParamConfBO.getProcessType();
        //查询其前置计划是否已经点击完成
        ProjectScheme oldScheme = this.getById(projectSchemeDTO.getId());
        String projectId = oldScheme.getProjectId();
        ProjectSetUpVO detail = projectSetUpService.detail(projectId, ProjectSetUpService.prePlanTimeRule);
        if (Objects.nonNull(detail) && StrUtil.equals(detail.getValue(), "true")) {
            List<ProjectSchemePrePost> preSchemes = schemePrePostService.list(new LambdaQueryWrapperX<>(ProjectSchemePrePost.class).eq(ProjectSchemePrePost::getProjectSchemeId, projectSchemeDTO.getId()));
            if (!CollectionUtils.isEmpty(preSchemes)) {
                List<String> preSchemeIds = preSchemes.stream().map(ProjectSchemePrePost::getPreSchemeId).collect(Collectors.toList());
                List<ProjectScheme> projectSchemes = this.list(new LambdaQueryWrapperX<>(ProjectScheme.class).in(ProjectScheme::getId, preSchemeIds).ne(ProjectScheme::getStatus, Status.FINISHED.getCode()));
                if (!CollectionUtils.isEmpty(projectSchemes)) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_EXIST_PRE_NON_FINISHED, "该计划存在未完成的前置计划");
                }
            }
        }


        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectSchemeDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectSchemeDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }

        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        boolean result = transactionTemplate.execute(transactionStatus -> {
            try {
                if (!Status.EXECUTING.getCode().equals(oldScheme.getStatus())) {
                    throw new BaseException(PMSErrorCode.PMS_ERROR_NON_PUBLISHED);
                }
                if (!Status.CIRCUMSTANCE_OVERD.getCode().equals(oldScheme.getCircumstance())) {
                    projectSchemeDTO.setCircumstance(null);
                }
                ProjectScheme scheme = BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new);
                if (StrUtil.isNotBlank(scheme.getParentId()) && !StrUtil.equals(ROOT_ID, scheme.getParentId())) {
                    if (StrUtil.isBlank(oldScheme.getPlanActive()) || !oldScheme.getPlanActive().contains("designPlan")) {
                        if (ProjectSchemeProcessEnum.WORK_PROCESS.getValue().equals(processType) && projectSchemeDTO.getIsProcess()) {
                            scheme.setCircumstance(Status.AFFIRM_APPROVAL.getCode());
                            scheme.setStatus(Status.EXECUTING.getCode());
                            //启动流程
                            wfInstanceManage.process(Arrays.asList(scheme), SchemeDict.COMPLETE_PROCESS);
                        } else {
                            scheme.setStatus(Status.CONFIRMED.getCode());
                        }
                    } else {
                        scheme.setStatus(Status.FINISHED.getCode());
                    }
                    return this.updateById(scheme);
                }
                LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
                wrapper.eq(ProjectScheme::getProjectId, scheme.getProjectId())
                        .like(ProjectScheme::getParentChain, scheme.getId())
                        .orderByDesc(ProjectScheme::getTopSort)
                        .orderByAsc(ProjectScheme::getSort);
                List<ProjectScheme> list = Optional.ofNullable(this.list(wrapper)).orElse(new ArrayList<>());
                boolean havNonFinish = list.stream().anyMatch(childScheme -> !Status.FINISHED.getCode().equals(childScheme.getStatus()));
                if (havNonFinish) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_HAVE_CHILD_NON_PUBLISHED);
                }
                if (StrUtil.isBlank(oldScheme.getPlanActive()) || !oldScheme.getPlanActive().contains("designPlan")) {
                    if (ProjectSchemeProcessEnum.WORK_PROCESS.getValue().equals(processType)&& projectSchemeDTO.getIsProcess()) {
                        scheme.setCircumstance(Status.AFFIRM_APPROVAL.getCode());
                        scheme.setStatus(Status.EXECUTING.getCode());
                        //TODO启动流程
                        wfInstanceManage.process(Arrays.asList(scheme), SchemeDict.COMPLETE_PROCESS);
                    } else {
                        scheme.setStatus(Status.CONFIRMED.getCode());
                    }
                } else {
                    scheme.setStatus(Status.FINISHED.getCode());
                }
                Boolean ret = this.updateById(scheme);

//                if (ProjectConsts.FROM_ACCEPTANCE_FORM.equals(projectSchemeDTO.getFrom())) {
//                    // 如果项目计划的执行完成是在验收单内发起，则同步附件信息到验收单.
//                    acceptanceFormService.saveRelatedFiles(projectSchemeDTO.getFromId(), attachments);
//                }
                return ret;
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        if (result) {
            messageManager.clearToDo(MsgBusinessTypeEnum.MODIFY_URGE, projectSchemeDTO.getId());
            messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, projectSchemeDTO.getId());
            messageManager.sendMsg(MsgBusinessTypeEnum.PRE_FINISH,
                    SchemeMsgDTO.builder().projectSchemeList(CollUtil.toList(BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new))).build());
            if (Status.ICON_MILESTONE.getCode().equals(projectSchemeDTO.getType())) {
                // 发送里程碑数据更新通知 - 关联project数据需处理
                dataUpdateMessageService.sendDataUpdatedMessage(Project.class.getSimpleName(), projectSchemeDTO.getProjectId());
            }
        }
        return result;
    }


    /**
     * @param projectId
     */
    @Override
    public List<ProjectSchemeVO> getSchemeList(String projectId) throws Exception {
        LambdaQueryWrapper<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        List<Integer> codes = Arrays.asList(Status.PUBLISHED.getCode(), Status.PENDING.getCode());
        schemeLambdaQueryWrapper.eq(ProjectScheme::getProjectId, projectId).in(ProjectScheme::getStatus, codes);
        List<ProjectScheme> projectSchemes = this.list(schemeLambdaQueryWrapper);
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeVO::new);
        if (schemeVOS == null) {
            return new ArrayList<>();
        }
        return schemeVOS;

    }

    /**
     * 获取项目所有里程碑
     *
     * @param projectId
     * @return
     */
    @Override
    public List<ProjectSchemeVO> getAllMilestoneByProjectId(String projectId) throws Exception {
        LambdaQueryWrapper<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        schemeLambdaQueryWrapper.eq(ProjectScheme::getProjectId, projectId)
                .eq(ProjectScheme::getNodeType, "milestone")
                .orderByAsc(ProjectScheme::getEndTime);
        List<ProjectScheme> schemes = this.list(schemeLambdaQueryWrapper);
        List<ProjectScheme> projectSchemes = commonManager.filterView(schemes, SchemeListTypeEnum.PROJECT_SCHEME);
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeVO::new);
        if (CollUtil.isEmpty(schemeVOS)) {
            return schemeVOS;
        }
        List<String> schemeIds = projectSchemes.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        /*
         * 判断是否存在前置关系
         */
        Map<String, Integer> approvalStatusMap = projectSchemeApplyApprovalService.getApprovalStatus(schemeIds);

        Map<String, List<ProjectSchemePrePostVO>> isPrePostMap = isPrePost(projectId, schemeIds);

        schemeVOS.stream().peek(schemeVO -> schemeVO.setIsPrePost(null != isPrePostMap.get(schemeVO.getId())))
                .peek(schemeVO -> schemeVO.setApproveStatus(approvalStatusMap.get(schemeVO.getId())));
//                .forEach(schemeVO -> commonManager.codeMapping(schemeVO));
        commonManager.codeMapping(schemeVOS);
        schemeVOS.sort(Comparator.comparing(ProjectSchemeVO::getEndTime));
        return schemeVOS;
    }

    @Override
    public boolean verifyUser(String userId, String projectId) throws Exception {
        LambdaQueryWrapper<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        schemeLambdaQueryWrapper.select(ProjectScheme::getId);
        schemeLambdaQueryWrapper.eq(ProjectScheme::getProjectId, projectId);
        schemeLambdaQueryWrapper.eq(ProjectScheme::getRspUser, userId);
        List<ProjectScheme> idList = this.list(schemeLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(idList)) {
            return Boolean.TRUE;
        }
        return false;
    }

    @Override
    public void importByExcelVerify(String pid, List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception {
        long startTime = System.currentTimeMillis();
        Project project = projectService.getById(projectSchemeDTOS.get(0).getProjectId());
        List<ProjectScheme> projectSchemes = CollUtil.toList();

        List<ComputeIntervalDayDTO> computeIntervalDayDTOS = new ArrayList<>();
        projectSchemeDTOS.forEach(p -> {
            computeIntervalDayDTOS.add(new ComputeIntervalDayDTO(p.getId(), p.getBeginTime(), p.getEndTime()));
        });

        ResponseDTO<List<ComputeIntervalDayVO>> responseDTO = userFeignService.computeIntervalDayBatch(computeIntervalDayDTOS);
        Map<String, Long> computeIntervalDayMap = new HashMap<>(projectSchemeDTOS.size());
        if (Objects.nonNull(responseDTO) && !CollectionUtils.isEmpty(responseDTO.getResult())) {
            responseDTO.getResult().stream().collect(Collectors.toMap(ComputeIntervalDayVO::getBusinessId, ComputeIntervalDayVO::getWorkdayNum)).forEach(computeIntervalDayMap::put);
        }

        ProjectScheme pScheme = getParentProjectScheme(pid);
        List<ProjectScheme> projectSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, project.getId()));
        Long maxSort = projectSchemeList.stream().map(ProjectScheme::getSort).max(Comparator.comparing(Long::longValue)).orElse(0L);
        long endTime1 = System.currentTimeMillis();
        log.debug("start1: 一阶段查询任务执行耗时: {} 毫秒", (endTime1 - startTime));
        Map<String,ProjectSchemeDTO>  map = projectSchemeDTOS.stream().collect(Collectors.toMap(ProjectSchemeDTO::getId,Function.identity()));
        for (int i = 0; i < projectSchemeDTOS.size(); i++) {
            ProjectSchemeDTO projectSchemeDTO = projectSchemeDTOS.get(i);
            commonManager.checkTime(pScheme, projectSchemeDTO);
            ProjectScheme projectScheme = new ProjectScheme();
            BeanCopyUtils.copyProperties(projectSchemeDTO, projectScheme);
            projectScheme.setId(projectSchemeDTO.getId());
            projectScheme.setTopSort(0);
            projectScheme.setProjectNumber(project.getNumber());
            if(ObjectUtil.isNotEmpty(map.get(projectSchemeDTO.getParentId()))){
                projectScheme.setSchemeNumber(""+(maxSort+i + 1));
            }
            if(projectSchemeDTO.getParentId().equals("0")||(ObjectUtil.isNotEmpty(pScheme)&&projectSchemeDTO.getParentId().equals(pScheme.getId()))) {
                projectScheme.setParentChain(buildParentChain(pScheme));
                projectScheme.setSchemeNumber(Objects.isNull(pScheme) ? project.getNumber() + "-" + (maxSort+i + 1) : pScheme.getSchemeNumber() + "-" + (maxSort+i + 1));
                projectScheme.setNumber(projectScheme.getSchemeNumber());
                projectScheme.setLevel(projectScheme.getParentChain().split(",").length);
            }
            projectScheme.setSort(maxSort+i + 1);
            projectScheme.setCircumstance(0);
            projectScheme.setStatus(Status.PENDING.getCode());
            projectScheme.setDurationDays(Integer.parseInt(String.valueOf(computeIntervalDayMap.getOrDefault(projectSchemeDTO.getId(), 0L))));
            projectSchemes.add(projectScheme);
        }
        long endTime2 = System.currentTimeMillis();
        log.debug("start2: 二阶段查询任务执行耗时: {} 毫秒", (endTime2 - endTime1));
        String parentId  = "0";
        if(ObjectUtil.isNotEmpty(pScheme)){
            parentId = pScheme.getId();
        }
        String finalParentId = parentId;
        List<ProjectScheme> schemeList = projectSchemes.stream().filter(item->item.getParentId().equals(finalParentId)).collect(Collectors.toList());
        Map<String,List<ProjectScheme>> parentIdMap = projectSchemes.stream().collect(Collectors.groupingBy(ProjectScheme::getParentId));
        for(ProjectScheme scheme:schemeList){
            setScheme(scheme,parentIdMap );
        }
        long endTime3 = System.currentTimeMillis();
        log.debug("start3: 三阶段查询任务执行耗时: {} 毫秒", (endTime3 - endTime2));
        this.saveBatch(projectSchemes);
    }
    public void setScheme(ProjectScheme projectScheme,Map<String,List<ProjectScheme>> map){
        List<ProjectScheme>  childs = map.get(projectScheme.getId());
        if(CollUtil.isEmpty(childs)){
            return;
        }
        for(ProjectScheme childScheme : childs){
            childScheme.setParentChain(buildParentChain(projectScheme));
            childScheme.setSchemeNumber(projectScheme.getSchemeNumber() + "-" + (childScheme.getSchemeNumber()));
            childScheme.setNumber(childScheme.getSchemeNumber());
            childScheme.setLevel(childScheme.getParentChain().split(",").length);
            if(CollUtil.isNotEmpty(map.get(childScheme.getId()))){
                setScheme(childScheme,map);
            }
        }
    }


    /**
     * 获取计划编号
     *
     * @return String
     */
    private String getSchemeNumber(ProjectSchemeVO parentSchemeVO) {
        Long sort = parentSchemeVO.getSort();
        List<ProjectSchemeVO> children = parentSchemeVO.getChildren();
        if (CollectionUtils.isEmpty(parentSchemeVO.getChildren())) {
            return StrUtil.toString(sort);
        }
        String childNumber = getSchemeNumber(children.stream().findFirst().orElse(new ProjectSchemeVO()));
        return StrUtil.format("{}.{}", sort, childNumber);
    }

    /**
     * 是否前置计划
     *
     * @param projectId 项目id
     * @param schemeIds 计划id列表
     * @return
     * @throws Exception
     */
    private Map<String, List<ProjectSchemePrePostVO>> isPrePost(String projectId, List<String> schemeIds) throws Exception {
        Map<String, List<ProjectSchemePrePostVO>> isPrePostMap = new HashMap<>();
        if (CollectionUtils.isEmpty(schemeIds)) {
            return isPrePostMap;
        }

        //获取前置计划
        LambdaQueryWrapper<ProjectSchemePrePost> prePostWrapper = new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                .eq(ProjectSchemePrePost::getProjectId, projectId)
                .eq(ProjectSchemePrePost::getType, Status.SCHEME_PRE.getCode())
                .in(ProjectSchemePrePost::getProjectSchemeId, schemeIds);
        List<ProjectSchemePrePost> prePostList = schemePrePostService.list(prePostWrapper);
        if (CollectionUtils.isEmpty(prePostList)) {
            return isPrePostMap;
        }
        List<String> prePostSchemeIds = prePostList.stream().map(ProjectSchemePrePost::getPreSchemeId).collect(Collectors.toList());

        List<ProjectSchemeVO> prePostSchemeVOS = BeanCopyUtils.convertListTo(this.listByIds(prePostSchemeIds), ProjectSchemeVO::new);

        commonManager.codeMapping(prePostSchemeVOS);
        Map<String, ProjectSchemeVO> prePostSchemeMap = prePostSchemeVOS.stream()
                .peek(p -> p.setTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(p.getNodeType())))
//                .peek(prePostSchemeVO -> commonManager.codeMapping(prePostSchemeVO))
                .collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));

        List<ProjectSchemePrePostVO> result = BeanCopyUtils.convertListTo(prePostList, ProjectSchemePrePostVO::new);
        isPrePostMap = result.stream().peek(prePost -> {
            ProjectSchemeVO vo = prePostSchemeMap.get(prePost.getPreSchemeId());
            if (null == vo) {
                return;
            }
            prePost.setPreSchemeName(vo.getName());
            prePost.setRspSubDept(vo.getRspSubDept());
            prePost.setRspSubDeptName(vo.getRspSubDeptName());
            prePost.setRspUser(vo.getRspUser());
            prePost.setRspUserName(vo.getRspUserName());
            prePost.setSchemeBeginTime(vo.getBeginTime());
            prePost.setSchemeEndTime(vo.getEndTime());
            prePost.setSchemeDesc(vo.getSchemeDesc());
        }).collect(Collectors.groupingBy(ProjectSchemePrePostVO::getProjectSchemeId, Collectors.toList()));
        return isPrePostMap;
    }


    /**
     * 是否后置计划
     *
     * @param projectId 项目id
     * @param schemeIds 计划id列表
     * @return
     * @throws Exception
     */
    private Map<String, List<ProjectSchemePrePostVO>> isPost(String projectId, List<String> schemeIds) throws Exception {

        Map<String, List<ProjectSchemePrePostVO>> isPrePostMap = new HashMap<>();

        //获取后置计划
        LambdaQueryWrapper<ProjectSchemePrePost> postWrapper = new LambdaQueryWrapper<>(ProjectSchemePrePost.class).eq(ProjectSchemePrePost::getProjectId, projectId)
                .in(ProjectSchemePrePost::getPreSchemeId, schemeIds);
        List<ProjectSchemePrePost> postList = schemePrePostService.list(postWrapper);
        if (CollectionUtils.isEmpty(postList)) {
            return isPrePostMap;
        }
        postList.forEach(t -> {
            t.setType(Status.SCHEME_POST.getCode());
        });
        List<String> postSchemeIdList = postList.stream().map(ProjectSchemePrePost::getProjectSchemeId).collect(Collectors.toList());


        List<ProjectSchemeVO> prePostSchemeVOS = BeanCopyUtils.convertListTo(this.listByIds(postSchemeIdList), ProjectSchemeVO::new);

        commonManager.codeMapping(prePostSchemeVOS);
        Map<String, ProjectSchemeVO> prePostSchemeMap = prePostSchemeVOS.stream()
                .peek(p -> p.setTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(p.getNodeType())))
//                .peek(prePostSchemeVO -> commonManager.codeMapping(prePostSchemeVO))
                .collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));

        List<ProjectSchemePrePostVO> result = BeanCopyUtils.convertListTo(postList, ProjectSchemePrePostVO::new);
        isPrePostMap = result.stream().peek(prePost -> {
            ProjectSchemeVO vo = prePostSchemeMap.get(prePost.getProjectSchemeId());
            if (null == vo) {
                return;
            }
            prePost.setPreSchemeName(vo.getName());
            prePost.setPreSchemeId(vo.getId());
            prePost.setRspSubDept(vo.getRspSubDept());
            prePost.setRspSubDeptName(vo.getRspSubDeptName());
            prePost.setRspUser(vo.getRspUser());
            prePost.setRspUserName(vo.getRspUserName());
            prePost.setSchemeBeginTime(vo.getBeginTime());
            prePost.setSchemeEndTime(vo.getEndTime());
            prePost.setSchemeDesc(vo.getSchemeDesc());
        }).collect(Collectors.groupingBy(ProjectSchemePrePostVO::getProjectSchemeId, Collectors.toList()));
        return isPrePostMap;
    }

    private void preCheck(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = this.getById(projectSchemeDTO.getId());
        if (Objects.isNull(projectScheme)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "计划已被删除");
        }
        if (Status.PENDING.getCode().equals(projectScheme.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "未下发的计划不能申请调整");
        }
        if (StrUtil.isNotBlank(projectSchemeDTO.getParentId()) && !ROOT_ID.equals(projectSchemeDTO.getParentId())) {
            ProjectScheme pScheme = this.getById(projectSchemeDTO.getParentId());
            if (Objects.isNull(pScheme)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "不存在父级计划");
            }
            commonManager.checkTime(pScheme, projectSchemeDTO);
        }
        //查询当前计划是否存在未审批调整申请
        LambdaQueryWrapper<ProjectSchemeApplyApproval> approveWrapper = new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class).eq(ProjectSchemeApplyApproval::getProjectId, projectSchemeDTO.getProjectId())
                .eq(ProjectSchemeApplyApproval::getProjectSchemeId, projectSchemeDTO.getId());
        List<ProjectSchemeApplyApproval> list = Optional.ofNullable(projectSchemeApplyApprovalService.list(approveWrapper)).orElse(new ArrayList<>());
        if (list.stream().anyMatch(approve -> ProjectSchemeConts.CO_IP.equals(approve.getStatus())
                || ProjectSchemeConts.CO_NA.equals(approve.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_EXIST_APPROVE);
        }
    }


    @Override
    public Boolean writeDelayReason(ProjectSchemeDTO projectSchemeDTO) {
        ProjectScheme db = this.getById(projectSchemeDTO.getId());
        db.setDelayEndReason(projectSchemeDTO.getDelayEndReason());

        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectSchemeDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectSchemeDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_DELAY);
        });

        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
            searchHelper.sendDataChangeMessage(projectSchemeDTO.getId());
        }
        this.updateById(db);
        return true;
    }

    @Override
    public Boolean urgePlan(UrgePlanRequestDTO urgePlanRequestDTO) {
        ProjectScheme db = this.getById(urgePlanRequestDTO.getProjectSchemeId());
        messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_URGE,
                SchemeMsgDTO.builder()
                        .projectSchemeList(Collections.singletonList(db))
                        .extParam(new HashMap<>() {{
                            put("remark", urgePlanRequestDTO.getRemark());
                            put("notifyUserId", JSONUtil.toJsonStr(urgePlanRequestDTO.getNotifyUserId()));
                        }})
                        .build());

        return true;
    }

    @Override
    public Page<ProjectSchemeVO> pageProjectSchemeByUserCode(String userCode, String projectCode, Page<ProjectSchemeDTO> pageRequest) {
        Project project = projectService.getOne(new LambdaQueryWrapper<>(Project.class).eq(Project::getNumber, projectCode));
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ProjectScheme> real = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapper<ProjectScheme> condition = new LambdaQueryWrapper<>();
        condition.eq(ProjectScheme::getProjectId, project.getId());
        condition.eq(ProjectScheme::getProcessFlag, true);

        ProjectSchemeDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            String name = query.getName();
            if (StrUtil.isNotBlank(name)) {
                condition.like(ProjectScheme::getName, name);
            }
        }


        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ProjectScheme> page = this.page(real, condition);
        return new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopyUtils.convertListTo(page.getRecords(), ProjectSchemeVO::new));
    }

    @Override
    public Boolean updateProjectSchemeByThird(ProjectSchemeDTO projectSchemeDTO) {
        ProjectScheme db = this.getOne(new LambdaQueryWrapper<>(ProjectScheme.class).eq(ProjectScheme::getNumber, projectSchemeDTO.getNumber()));
        db.setProcessId(projectSchemeDTO.getProcessId());
        db.setFormCodeId(projectSchemeDTO.getFormCodeId());
        db.setStatus(projectSchemeDTO.getStatus());
        boolean result = this.updateById(db);
        return result;
    }


    @Override
    public Boolean relationToDemand(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToDemandManagementService.list(new LambdaQueryWrapper<>(PlanToDemandManagement.class)
                .in(PlanToDemandManagement::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToDemandManagement::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToDemandManagement> planToDemandManagements = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToDemandManagement planToDemandManagement = new PlanToDemandManagement();
            planToDemandManagement.setClassName("PlanToDemandManagement");
            planToDemandManagement.setFromId(fromId);
            planToDemandManagement.setFromClass("DemandManagement");
            planToDemandManagement.setToId(fromIdsRelationToIdDTO.getToId());
            planToDemandManagement.setToClass("Plan");
            planToDemandManagements.add(planToDemandManagement);
        }
        planToDemandManagementService.saveBatch(planToDemandManagements);
        return Boolean.TRUE;
    }


    @Override
    public List<DemandManagementVO> relationToDemandLists(String id, PlanQueryDTO planQueryDTO) {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToDemandManagement> relationList = planToDemandManagementService.list(new LambdaQueryWrapper<>(PlanToDemandManagement.class)
                .eq(PlanToDemandManagement::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<DemandManagement> condition = new LambdaQueryWrapper<>();
        List<String> demandIdList = relationList.stream().map(PlanToDemandManagement::getFromId).collect(Collectors.toList());
        condition.in(DemandManagement::getId, demandIdList);
        if (Objects.nonNull(planQueryDTO)) {
            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
                condition.and(sub -> sub.like(DemandManagement::getName, planQueryDTO.getKeyword()).or().like(DemandManagement::getNumber, planQueryDTO.getKeyword()));
            }
        }
        List<DemandManagement> demandManagements = demandManagementService.list(condition);
        if (CollectionUtils.isEmpty(demandManagements)) {
            return new ArrayList<>();
        }
        List<DemandManagementVO> demandManagementVOList = BeanCopyUtils.convertListTo(demandManagements, DemandManagementVO::new);
        String projectName = "";

        List<DemandManagementVO> list = demandManagementVOList.stream().filter(item -> StringUtils.hasText(item.getProjectId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(list)) {
            Project projectDTO = projectService.getById(list.get(0).getProjectId());
            projectName = null == projectDTO ? "" : projectDTO.getName();
        }
        List<String> exhibitorIdLIST = new ArrayList<>();
        for (DemandManagementVO demandManagementVO : demandManagementVOList) {
            if (StringUtils.hasText(demandManagementVO.getPrincipalId())) {
                exhibitorIdLIST.add(demandManagementVO.getPrincipalId());
            }
            String recipientId = demandManagementVO.getRecipientId();
            if (StringUtils.hasText(recipientId)) {
                exhibitorIdLIST.add(recipientId);
            }

            String exhibitor = demandManagementVO.getExhibitor();
            if (StringUtils.hasText(exhibitor)) {
                exhibitorIdLIST.add(exhibitor);
            }
        }

        Map<String, String> sourceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.DEMAND_SOURCE);
        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.DEMAND_TYPE);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
        List<DemandManagement> demandManagementDTOS = demandManagementService.list(new LambdaQueryWrapper<>(DemandManagement.class)
                .in(DemandManagement::getId, demandManagementVOList.stream().map(DemandManagementVO::getParentId).distinct().toArray()));
        Map<String, String> demandManagementIdAndNameMap;
        if (!CollectionUtils.isEmpty(demandManagementDTOS)) {
            demandManagementIdAndNameMap = demandManagementDTOS.stream().collect(Collectors.toMap(DemandManagement::getId, DemandManagement::getName));
        } else {
            demandManagementIdAndNameMap = new HashMap<>();
        }
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.DEMAND_POLICY_ID);
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(exhibitorIdLIST);

        String finalProjectName = projectName;
        demandManagementVOList.forEach(o -> {
            String recipientId = o.getRecipientId();
            if (StringUtils.hasText(recipientId)) {
                UserVO userVO = userMapByUserIds.get(recipientId);
                o.setRecipientName(userVO != null ? userVO.getName() : "");
            }
            if (StringUtils.hasText(o.getPrincipalId())) {
                UserVO userVO = userMapByUserIds.get(o.getPrincipalId());
                o.setPrincipalName(userVO != null ? userVO.getName() : "");
            }
            o.setStatusName(statusValueToNameMap.get(o.getStatus()));
            o.setProjectName(finalProjectName);
            o.setSourceName(sourceValueToDesMap.get(o.getSource()));
            o.setTypeName(typeValueToDesMap.get(o.getType()));
            o.setPriorityLevelName(priorityLevelValueToDesMap.get(o.getPriorityLevel()));
            o.setParentName(demandManagementIdAndNameMap.get(o.getParentId()));
            String exhibitor = o.getExhibitor();
            if (StringUtils.hasText(exhibitor)) {
                UserVO userVO = userMapByUserIds.get(exhibitor);
                o.setExhibitorName(userVO != null ? userVO.getName() : "");
            }
            o.setScheduleName(ObjectUtil.isNotNull(o.getSchedule()) ? (o.getSchedule() + "%") : "0%");
        });

        return demandManagementVOList;
    }

    @Override
    public Boolean removeRelationToDemand(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        planToDemandManagementService.remove(new LambdaQueryWrapper<>(PlanToDemandManagement.class)
                .eq(PlanToDemandManagement::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToDemandManagement::getFromId, fromIdsRelationToIdDTO.getFromIds()));
        return true;
    }

    @Override
    public Boolean relationToQuestion(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToQuestionManagementService.list(new LambdaQueryWrapper<>(PlanToQuestionManagement.class)
                .in(PlanToQuestionManagement::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToQuestionManagement::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToQuestionManagement> planToQuestionManagements = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToQuestionManagement planToQuestionManagement = new PlanToQuestionManagement();
            planToQuestionManagement.setClassName("PlanToQuestionManagement");
            planToQuestionManagement.setFromId(fromId);
            planToQuestionManagement.setFromClass("QuestionManagement");
            planToQuestionManagement.setToId(fromIdsRelationToIdDTO.getToId());
            planToQuestionManagement.setToClass("Plan");
            planToQuestionManagements.add(planToQuestionManagement);
        }
        planToQuestionManagementService.saveBatch(planToQuestionManagements);
        return Boolean.TRUE;
    }


    @Override
    public Boolean removeRelationToQuestion(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        planToQuestionManagementService.remove(new LambdaQueryWrapper<>(PlanToQuestionManagement.class)
                .eq(PlanToQuestionManagement::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToQuestionManagement::getFromId, fromIdsRelationToIdDTO.getFromIds()));
        return true;
    }

    @Override
    public List<QuestionManagementVO> relationToQuestionLists(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToQuestionManagement> relationList = planToQuestionManagementService.list(new LambdaQueryWrapper<>(PlanToQuestionManagement.class)
                .eq(PlanToQuestionManagement::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<QuestionManagement> condition = new LambdaQueryWrapper<>();
        List<String> demandIdList = relationList.stream().map(PlanToQuestionManagement::getFromId).collect(Collectors.toList());
        condition.in(QuestionManagement::getId, demandIdList);
        if (Objects.nonNull(planQueryDTO)) {
            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
                condition.and(sub -> sub.like(QuestionManagement::getName, planQueryDTO.getKeyword()).or().like(QuestionManagement::getNumber, planQueryDTO.getKeyword()));
            }
        }
        List<QuestionManagement> questionManagements = questionManagementService.list(condition);
        if (CollectionUtils.isEmpty(questionManagements)) {
            return new ArrayList<>();
        }
        List<QuestionManagementVO> questionManagementVOList = BeanCopyUtils.convertListTo(questionManagements, QuestionManagementVO::new);
        questionManagementService.setContent(questionManagementVOList);
        return questionManagementVOList;

    }

    @Override
    public Boolean relationToRisk(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .in(PlanToRiskManagement::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToRiskManagement::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToRiskManagement> planToRiskManagements = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToRiskManagement planToRiskManagement = new PlanToRiskManagement();
            planToRiskManagement.setClassName("PlanToRiskManagement");
            planToRiskManagement.setFromId(fromId);
            planToRiskManagement.setFromClass("RiskManagement");
            planToRiskManagement.setToId(fromIdsRelationToIdDTO.getToId());
            planToRiskManagement.setToClass("Plan");
            planToRiskManagements.add(planToRiskManagement);
        }
        planToRiskManagementService.saveBatch(planToRiskManagements);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToRisk(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        planToRiskManagementService.remove(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .eq(PlanToRiskManagement::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToRiskManagement::getFromId, fromIdsRelationToIdDTO.getFromIds()));
        return true;
    }

    @Override
    public List<RiskManagementVO> relationToRiskLists(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToRiskManagement> relationList = planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .eq(PlanToRiskManagement::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<RiskManagement> condition = new LambdaQueryWrapper<>();
        List<String> riskIdList = relationList.stream().map(PlanToRiskManagement::getFromId).collect(Collectors.toList());
        condition.in(RiskManagement::getId, riskIdList);
        if (Objects.nonNull(planQueryDTO)) {
            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
                condition.and(sub -> sub.like(RiskManagement::getName, planQueryDTO.getKeyword()).or().like(RiskManagement::getNumber, planQueryDTO.getKeyword()));
            }
        }
        List<RiskManagement> riskManagements = riskManagementService.list(condition);
        if (CollectionUtils.isEmpty(riskManagements)) {
            return new ArrayList<>();
        }


        List<RiskManagementVO> riskManagementVOS = BeanCopyUtils.convertListTo(riskManagements, RiskManagementVO::new);
        riskManagementService.setContent(riskManagementVOS);
        return riskManagementVOS;
    }

    @Override
    public PlanSearchDataVo searchList(KeywordDto keywordDto) {

        PlanSearchDataVo planSearchDataVo = new PlanSearchDataVo();
        planSearchDataVo.setSize(0);
        planSearchDataVo.setPlanSearchVos(new ArrayList<>());
        String keyword = keywordDto.getKeyword();
        String projectId = keywordDto.getProjectId();
        List<Integer> status = keywordDto.getStatus();
        LambdaQueryWrapper<ProjectScheme> wrapper = new LambdaQueryWrapper<>(ProjectScheme.class);

        if (!StrUtil.isBlank(keyword)) {
            wrapper.and(sub -> sub.like(ProjectScheme::getName, keyword).or().like(ProjectScheme::getNumber, keyword));
        }

        if (!StrUtil.isBlank(projectId)) {
            wrapper.eq(ProjectScheme::getProjectId, projectId);
        }
        if (CollectionUtil.isNotEmpty(status)) {
            wrapper.in(ProjectScheme::getStatus, status);
        }
        List<ProjectScheme> plans = this.list(wrapper);
        if (CollectionUtils.isEmpty(plans)) {
            return planSearchDataVo;
        }
        List<PlanSearchVo> planSearchVos = new ArrayList<>();
        plans.forEach(p -> {
            PlanSearchVo tmp = new PlanSearchVo();
            tmp.setName(p.getName());
            tmp.setId(p.getId());
            tmp.setPrincipalId(p.getRspUser());
            if (StrUtil.isNotBlank(p.getRspUser())) {
                UserVO userVO = userRedisHelper.getUserById(p.getRspUser());
                tmp.setPrincipalName(userVO == null ? "" : userVO.getName());
            }
            tmp.setNumber(p.getNumber());
            planSearchVos.add(tmp);
        });

        planSearchDataVo.setSize(planSearchVos.size());
        planSearchDataVo.setPlanSearchVos(planSearchVos);
        return planSearchDataVo;
    }

    @Override
    public List<PlanDetailVo> search(SearchDTO searchDTO) throws Exception {
        LambdaQueryWrapperX<ProjectScheme> wrapper = new LambdaQueryWrapperX<>(ProjectScheme.class);
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            wrapper.in(ProjectScheme::getId, ids.toArray());
        }
        Integer status = searchDTO.getStatus();
        if (Objects.nonNull(status)) {
            wrapper.eq(ProjectScheme::getStatus, status);
        }
        if (!CollectionUtils.isEmpty(searchDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchDTO.getSearchConditions(), wrapper);
        }
        List<ProjectScheme> plans = this.list(wrapper);
        if (CollectionUtils.isEmpty(plans)) {
            return new ArrayList<>();
        }

        List<PlanDetailVo> planVOS = new ArrayList<>();


        List<String> projectIds = plans.stream().map(ProjectScheme::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectsMap = projects.stream().collect(Collectors.toMap(Project::getId, Function.identity()));


        plans.forEach(p -> {
            PlanDetailVo planDetailVo = BeanCopyUtils.convertTo(p, PlanDetailVo::new);
            Project project = projectsMap.getOrDefault(p.getProjectId(), new Project());
            planDetailVo.setProjectName(project.getName());
            planDetailVo.setPlanImage(p.getIcon());
            planDetailVo.setPlanPredictStartTime(p.getBeginTime());
            planDetailVo.setPlanPredictEndTime(p.getEndTime());
            planDetailVo.setSchedule(BigDecimal.ZERO);
            planDetailVo.setScheduleName("");
            planDetailVo.setPlanEndTime(p.getEndTime());
            planDetailVo.setPlanStartTime(p.getBeginTime());
            planDetailVo.setPlanType(p.getNodeType());
            planDetailVo.setPlanTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(p.getNodeType()));
            planDetailVo.setPrincipalId(p.getRspUser());

            if (StrUtil.isNotBlank(p.getRspUser())) {
                UserVO userVO = userRedisHelper.getUserById(p.getRspUser());
                planDetailVo.setPrincipalName(userVO == null ? "" : userVO.getName());
            }
            planDetailVo.setManHour(Objects.isNull(p.getDurationDays()) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(p.getDurationDays() * 8)));
            planDetailVo.setPriorityLevel("");
            planDetailVo.setPriorityLevelName("");
            planDetailVo.setTaskStatusId("");
            planDetailVo.setStatusName("");
            planDetailVo.setResDept(p.getRspSubDept());
            if (StrUtil.isNotBlank(p.getRspSubDept())) {
                DeptVO organization = deptRedisHelper.getDeptById(p.getRspSubDept());
                planDetailVo.setResDeptName(organization == null ? "" : organization.getName());
            }

            planDetailVo.setResUser(p.getRspUser());
            planDetailVo.setResUserName(planDetailVo.getPrincipalName());
            planDetailVo.setCircumstanceName(Status.codeMapping(planDetailVo.getCircumstance()));

            planVOS.add(planDetailVo);
        });


        return planVOS;
    }

    @Override
    public Page<ProjectSchemeVO> userPages(String type, Page<ProjectSchemeDTO> pageRequest) throws Exception {
        String userId = currentUserHelper.getUserId();
        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectScheme::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ProjectScheme> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        if (type.equals("1")) {
            objectLambdaQueryWrapperX.eq(ProjectScheme::getRspUser, userId);
            objectLambdaQueryWrapperX.in(ProjectScheme::getStatus, 111, 130);
        } else if (type.equals("2")) {
            objectLambdaQueryWrapperX.eq(ProjectScheme::getCreatorId, userId);
        }
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectSchemeDTO projectSchemeDTO = pageRequest.getQuery();
            if (StrUtil.isNotBlank(projectSchemeDTO.getName())) {
                LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(Project.class);
                lambdaQueryWrapperX.like(Project::getName, projectSchemeDTO.getName());
                List<Project> list = projectService.list(lambdaQueryWrapperX);
                if (CollectionUtil.isEmpty(list)) {
                    objectLambdaQueryWrapperX.like(ProjectScheme::getName, projectSchemeDTO.getName());
                } else {
                    List<String> projectIds = list.stream().map(Project::getId).collect(Collectors.toList());
                    objectLambdaQueryWrapperX.and(wrapper -> wrapper.in(ProjectScheme::getProjectId, projectIds).or()
                            .like(ProjectScheme::getName, projectSchemeDTO.getName()));
                }
            }
            if (StrUtil.isNotBlank(projectSchemeDTO.getProjectId())) {
                objectLambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectSchemeDTO.getProjectId());
            }
        }
        if (CollectionUtil.isNotEmpty(realPageRequest.getSearchConditions())) {
            objectLambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), objectLambdaQueryWrapperX);
        }
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        objectLambdaQueryWrapperX.inIfPresent(ProjectScheme::getProjectId, projectIdList);
        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        if (!CollectionUtil.isNotEmpty(schemeVOS)) {
            return pageResult;
        }
        // 状态策略
        Map<Integer, DataStatusVO> statusMap = dataStatusNBO.getDataStatusMapByClassName(ProjectScheme.class.getSimpleName());
        schemeVOS.forEach(o -> {
            UserVO creatorUserVO = userRedisHelper.getUserById(o.getCreatorId());
            if (Objects.nonNull(creatorUserVO)) {
                o.setCreatorName(creatorUserVO.getName());
            }
            if (Objects.nonNull(o.getStatus())){
                o.setDataStatus(statusMap.getOrDefault(o.getStatus(), new DataStatusVO()));
            }
        });
        codeMapping(schemeVOS);
        pageResult.setContent(schemeVOS);
        return pageResult;
    }

    public void codeMapping(List<ProjectSchemeVO> projectSchemeVOs) {
        List<String> projectIds = projectSchemeVOs.stream().map(ProjectSchemeVO::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, String> projectNameMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));
        Map<String, DictValueVO> levelMap = dictRedisHelper.getDictMapByCode(SchemeDict.LEVEL);
        for (ProjectSchemeVO projectSchemeVO : projectSchemeVOs) {
            if (null != levelMap) {
                projectSchemeVO.setLevelName(StrUtil.toString(levelMap.get(StrUtil.toString(projectSchemeVO.getLevel()))));
            }
            projectSchemeVO.setStatusName(Status.codeMapping(projectSchemeVO.getStatus()));
            // projectSchemeVO.setTypeName(Status.codeMapping(projectSchemeVO.getType()));
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
            projectSchemeVO.setRspSubDeptName("");
            projectSchemeVO.setRspUserName("");
            projectSchemeVO.setProjectName(projectNameMap.get(projectSchemeVO.getProjectId()));
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSubDept());
                projectSchemeVO.setRspSubDeptName(null == dept ? "" : dept.getName());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                UserVO rspUser = userRedisHelper.getUserById(projectSchemeVO.getRspUser());
                projectSchemeVO.setRspUserName(null == rspUser ? "" : rspUser.getName());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSectionId());
                projectSchemeVO.setRspSectionName(null == dept ? "" : dept.getName());
            }
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
        }
    }


    @Override
    public Boolean relationToDeliverGoals(String id, List<String> deliverGoalsIdList) {
        if (!CollectionUtils.isEmpty(planToDeliverGoalsService.list(new LambdaQueryWrapper<>(PlanToDeliverGoals.class)
                .in(PlanToDeliverGoals::getFromId, deliverGoalsIdList)
                .eq(PlanToDeliverGoals::getToId, id)))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToDeliverGoals> planToDeliverGoalsList = new ArrayList<>();

        for (String fromId : deliverGoalsIdList) {
            PlanToDeliverGoals planToDeliverGoals = new PlanToDeliverGoals();
            planToDeliverGoals.setFromId(fromId);
            planToDeliverGoals.setFromClass("DeliverGoals");
            planToDeliverGoals.setToId(id);
            planToDeliverGoals.setToClass("Plan");
            planToDeliverGoalsList.add(planToDeliverGoals);
        }
        planToDeliverGoalsService.saveBatch(planToDeliverGoalsList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToDeliverGoals(String id, List<String> deliverGoalsIdList) {
        planToDeliverGoalsService.remove(new LambdaQueryWrapper<>(PlanToDeliverGoals.class)
                .eq(PlanToDeliverGoals::getToId, id)
                .in(PlanToDeliverGoals::getFromId, deliverGoalsIdList));
        return true;
    }

    @Override
    public List<DeliverGoalsVO> relationToDeliverGoals(String id) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToDeliverGoals> relationList = planToDeliverGoalsService.list(new LambdaQueryWrapper<>(PlanToDeliverGoals.class)
                .eq(PlanToDeliverGoals::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<DeliverGoals> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeliverGoals::getId, relationList.stream().map(PlanToDeliverGoals::getFromId).collect(Collectors.toList()));

        List<DeliverGoals> deliverGoalsList = deliverGoalsService.list(wrapper);
        if (CollectionUtils.isEmpty(deliverGoalsList)) {
            return new ArrayList<>();
        }


        List<DeliverGoalsVO> deliverGoalsVOList = BeanCopyUtils.convertListTo(deliverGoalsList, DeliverGoalsVO::new);
        deliverGoalsService.setDeliverGoalsVO(deliverGoalsVOList);
        return deliverGoalsVOList;
    }

    @Override
    public List<ProjectSchemeVO> workHoursHainByRspUser(String userId, String start, String end) {
        LambdaQueryWrapperX<ProjectScheme> wrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        wrapperX.and(wrapper -> wrapper.eq(ProjectScheme::getRspUser, userId)
                        .or().like(ProjectScheme::getParticipantUsers, userId))
                .eq(ProjectScheme::getStatus, Status.PUBLISHED.getCode());
        if (StrUtil.isNotBlank(start) && StrUtil.isNotBlank(end)) {
            setDateCondition(wrapperX, start, end);
        }
        List<ProjectScheme> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> projectIds = list.stream().map(ProjectScheme::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, p -> p));
        List<ProjectSchemeVO> vos = BeanCopyUtils.convertListTo(list, ProjectSchemeVO::new);
        List<ProjectSchemeVO> newVos = new ArrayList<>();
        vos.forEach(p -> {
            String projectId = p.getProjectId();
            if (StrUtil.isNotBlank(projectId)) {
                Project project = projectMap.get(projectId);
                if (!ObjectUtil.isNull(project)) {
                    p.setProjectName(project.getName());
                    p.setProjectNumber(project.getNumber());
                    newVos.add(p);
                }
            }
        });
        setPlace(newVos);
        return newVos;
    }

    private void setDateCondition(LambdaQueryWrapperX<ProjectScheme> wrapperX, String start, String end) {
        wrapperX.and(wrapper -> wrapper
                .and(innerWrapper -> innerWrapper
                        .ge(ProjectScheme::getBeginTime, start)
                        .le(ProjectScheme::getEndTime, end))
                .or(innerWrapper -> innerWrapper
                        .le(ProjectScheme::getBeginTime, start)
                        .ge(ProjectScheme::getEndTime, end))
                .or(innerWrapper -> innerWrapper
                        .ge(ProjectScheme::getBeginTime, start)
                        .le(ProjectScheme::getEndTime, start))
                .or(innerWrapper -> innerWrapper
                        .le(ProjectScheme::getBeginTime, end)
                        .ge(ProjectScheme::getEndTime, end)));
    }

    private void setPlace(List<ProjectSchemeVO> newVos) {
        if (CollectionUtil.isEmpty(newVos)) {
            return;
        }
        List<String> projectIds = newVos.stream().map(ProjectSchemeVO::getProjectId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(projectIds)) {
            return;
        }
        List<ProjectPlace> placeByProjectIds = placeService.getPlaceByProjectIds(projectIds);
        if (CollectionUtil.isEmpty(placeByProjectIds)) {
            return;
        }
        List<String> areaIds = placeByProjectIds.stream().map(ProjectPlace::getAreaId).distinct().collect(Collectors.toList());
        ResponseDTO<List<BaseAreaVO>> byAreaIds;
        try {
            byAreaIds = pmiService.getByAreaIds(areaIds);
        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "获取base地数据失败！");
        }
        List<BaseAreaVO> result = byAreaIds.getResult();
        if (CollectionUtil.isEmpty(result)) {
            return;
        }
        Map<String, String> areaMap = result.stream().collect(Collectors.toMap(BaseAreaVO::getAreaId, BaseAreaVO::getArea));
        List<ProjectPlaceVO> projectPlaceVOS = BeanCopyUtils.convertListTo(placeByProjectIds, ProjectPlaceVO::new);
        projectPlaceVOS.forEach(projectPlaceVO -> {
            String areaId = projectPlaceVO.getAreaId();
            String areaName = areaMap.get(areaId);
            if (StrUtil.isNotBlank(areaName)) {
                projectPlaceVO.setArea(areaName);
            }
        });
        Map<String, List<ProjectPlaceVO>> projectPlaceMap = projectPlaceVOS.stream().collect(Collectors.groupingBy(ProjectPlaceVO::getProjectId));
        newVos.forEach(projectSchemeVO -> {
            String projectId = projectSchemeVO.getProjectId();
            List<ProjectPlaceVO> projectPlaceVOList = projectPlaceMap.get(projectId);
            if (!CollectionUtil.isEmpty(projectPlaceVOList)) {
                List<PlaceVO> placeVOS = BeanCopyUtils.convertListTo(projectPlaceVOList, PlaceVO::new);
                projectSchemeVO.setPlaceVOS(placeVOS);
            }
        });
    }

    @Override
    public List<ProjectSchemeVO> getListByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<ProjectScheme> projectSchemes = this.listByIds(ids);
        List<String> projectIds = projectSchemes.stream().map(ProjectScheme::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, p -> p));
        List<ProjectSchemeVO> vos = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeVO::new);
        vos.forEach(p -> {
            String projectId = p.getProjectId();
            Project project = projectMap.get(projectId);
            p.setProjectName(project.getName());
            p.setProjectNumber(project.getNumber());
        });
        return vos;
    }


    @Override
    public Page<ProjectSchemeVO> getSchemeByUsers(Page<ProjectSchemeDailyStatementDTO> pageRequest) {
        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(projectIdList)) {
            return new Page();
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectScheme::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        ProjectSchemeDailyStatementDTO projectSchemeDailyStatementDTO = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(projectSchemeDailyStatementDTO)) {
            return new Page();
        }
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX(ProjectScheme.class);
        if (CollectionUtil.isNotEmpty(realPageRequest.getSearchConditions())) {
            lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), lambdaQueryWrapperX);
        }

        Date currentDateTime = projectSchemeDailyStatementDTO.getDate();


        String userId = CurrentUserHelper.getCurrentUserId();
        lambdaQueryWrapperX.and(wrapper -> wrapper.eq(ProjectScheme::getRspUser, userId).or().like(ProjectScheme::getParticipantUsers, userId));
        lambdaQueryWrapperX.in(ProjectScheme::getProjectId, projectIdList);
        lambdaQueryWrapperX.apply("DATE(t.begin_time) <= DATE({0})", projectSchemeDailyStatementDTO.getDate());
        //lambdaQueryWrapperX.leIfPresent(ProjectScheme::getBeginTime,projectSchemeDailyStatementDTO.getDate());
        lambdaQueryWrapperX.geIfPresent(ProjectScheme::getEndTime, projectSchemeDailyStatementDTO.getDate());
        lambdaQueryWrapperX.likeIfPresent(ProjectScheme::getName, projectSchemeDailyStatementDTO.getName());
        lambdaQueryWrapperX.eq(ProjectScheme::getStatus, 130);
        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        if (CollectionUtil.isEmpty(schemeVOS)) {
            return pageResult;
        }
        List<String> projectIds = schemeVOS.stream().map(ProjectSchemeVO::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, p -> p));
        schemeVOS.forEach(p -> {
            String projectId = p.getProjectId();
            Project project = projectMap.get(projectId);
            if (ObjectUtil.isNotEmpty(project)) {
                p.setProjectName(project.getName());
                p.setProjectNumber(project.getNumber());
            }
            DataStatusVO dataStatus = p.getDataStatus();
            if (ObjectUtil.isNotNull(dataStatus)) {
                p.setStatusName(dataStatus.getName());
            }
        });
        pageResult.setContent(schemeVOS);
        return pageResult;
    }

    @Override
    public Page<ProjectSchemeVO> getSchemeByUser(String type, Page<ProjectSchemeDTO> pageRequest) {
        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(projectIdList)) {
            return new Page();
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectScheme::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        ProjectSchemeDTO projectSchemeDTO = pageRequest.getQuery();
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX(ProjectScheme.class);
        if (CollectionUtil.isNotEmpty(realPageRequest.getSearchConditions())) {
            lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), lambdaQueryWrapperX);
        }


        String userId = CurrentUserHelper.getCurrentUserId();
        lambdaQueryWrapperX.and(wrapper -> wrapper.eq(ProjectScheme::getRspUser, userId).or().like(ProjectScheme::getParticipantUsers, userId));
        lambdaQueryWrapperX.in(ProjectScheme::getProjectId, projectIdList);
        if ("1".equals(type)) {
            lambdaQueryWrapperX.eq(ProjectScheme::getStatus, ProjectSchemeEnum.COMPLETED.getValue());
        } else {
            lambdaQueryWrapperX.eq(ProjectScheme::getStatus, ProjectSchemeEnum.PUBLISHED.getValue());
        }
        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        if (CollectionUtil.isEmpty(schemeVOS)) {
            return pageResult;
        }

        List<String> userIdList = schemeVOS.stream().map(ProjectSchemeVO::getIssuedUser).collect(Collectors.toList());
        Map<String, UserVO> idToEntity = userRedisHelper.getUserMapByUserIds(CurrentUserHelper.getOrgId(), userIdList);
        Map<String, String> idToName = new HashMap();
        for (Map.Entry<String, UserVO> stringUserVOEntry : idToEntity.entrySet()) {
            idToName.put(stringUserVOEntry.getValue().getId(), stringUserVOEntry.getValue().getName());
        }
        schemeVOS.forEach(p -> {
            p.setIssuedUserName(idToName.getOrDefault(p.getIssuedUser(), ""));
            p.setCircumstanceName(Status.codeMapping(p.getCircumstance()));
            DataStatusVO dataStatus = p.getDataStatus();
            if (ObjectUtil.isNotNull(dataStatus)) {
                p.setStatusName(dataStatus.getName());
            }
        });
        pageResult.setContent(schemeVOS);
        return pageResult;
    }


    @Override
    public ProjectSchemeVO getSchemeDetail(String id) throws Exception {
        statusProcessor.statusHandleById(CollUtil.toList(id));
        ProjectScheme projectScheme = this.getById(id);
        Project project = projectService.getById(projectScheme.getProjectId());
        ProjectSchemeVO schemeVO = BeanCopyUtils.convertTo(projectScheme, ProjectSchemeVO::new);
        commonManager.codeMapping(ImmutableList.of(schemeVO));
        String participantUsers = projectScheme.getParticipantUsers();
        if (StrUtil.isNotBlank(schemeVO.getIssuedUser())) {
            SimpleUser simpleUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), schemeVO.getIssuedUser());
            List<DeptParentChildLevelVO> depts = simpleUser.getAllOrgParentChildLevelList();
            String deptName = depts.stream().map(DeptParentChildLevelVO::getOrgName).collect(Collectors.joining(","));
            schemeVO.setIssuedDeptName(deptName);
            schemeVO.setIssuedUserName(simpleUser.getName());
        }
        if (StringUtils.hasText(participantUsers)) {
            List<String> participantUserIdList = Arrays.asList(participantUsers.split(","));
            Map<String, UserVO> idToEntityMap = userRedisHelper.getUserMapByUserIds(CurrentUserHelper.getOrgId(), participantUserIdList);
            List<String> names = new ArrayList<>();
            for (String s : participantUserIdList) {
                UserVO entityVo = idToEntityMap.get(s);
                if (ObjectUtil.isNotNull(entityVo)) {
                    names.add(entityVo.getName());
                }
            }
            schemeVO.setParticipantUserList(participantUserIdList);
            schemeVO.setProjectName(project.getName());
            if (StrUtil.isNotBlank(schemeVO.getIssuedUser())) {
                SimpleUser simpleUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), schemeVO.getIssuedUser());
                List<DeptParentChildLevelVO> depts = simpleUser.getAllOrgParentChildLevelList();
                String deptName = depts.stream().map(DeptParentChildLevelVO::getOrgName).collect(Collectors.joining(","));
                schemeVO.setIssuedDeptName(deptName);
                schemeVO.setIssuedUserName(simpleUser.getName());
            }
            schemeVO.setParticipantUserNames(names.stream().collect(Collectors.joining(",")));
        }
        return schemeVO;
    }


    @Override
    public Page<ProjectSchemeVO> getPageByexamineUser(Page<ProjectSchemeDTO> pageRequest) {
        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(projectIdList)) {
            return new Page();
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectScheme::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        ProjectSchemeDTO projectSchemeDTO = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(projectSchemeDTO)) {
            return new Page();
        }
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX(ProjectScheme.class);
        if (CollectionUtil.isNotEmpty(realPageRequest.getSearchConditions())) {
            lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), lambdaQueryWrapperX);
        }
        String userId = CurrentUserHelper.getCurrentUserId();
        lambdaQueryWrapperX.eq(ProjectScheme::getExamineUser, userId);
        lambdaQueryWrapperX.eq(ProjectScheme::getExamineType, projectSchemeDTO.getExamineType());
        lambdaQueryWrapperX.eq(ProjectScheme::getStatus, 111);
        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        if (CollectionUtil.isEmpty(schemeVOS)) {
            return pageResult;
        }
        List<String> projectIds = schemeVOS.stream().map(ProjectSchemeVO::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, Project> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, p -> p));
        schemeVOS.forEach(p -> {
            String projectId = p.getProjectId();
            Project project = projectMap.get(projectId);
            if (ObjectUtil.isNotEmpty(project)) {
                p.setProjectName(project.getName());
                p.setProjectNumber(project.getNumber());
            }
            DataStatusVO dataStatus = p.getDataStatus();
            if (ObjectUtil.isNotNull(dataStatus)) {
                p.setStatusName(dataStatus.getName());
            }
            if (StrUtil.isNotBlank(p.getIssuedUser()) && ObjectUtil.isNotEmpty(userRedisHelper.getUserById(p.getIssuedUser()))) {
                p.setIssuedUserName(userRedisHelper.getUserById(p.getIssuedUser()).getName());
            }
        });

        codeMapping(schemeVOS);
        pageResult.setContent(schemeVOS);
        return pageResult;
    }

    @Override
    public Boolean examineTypeFinish(String id) throws Exception {
        ProjectScheme oldScheme = this.getById(id);
        oldScheme.setExamineType("130");
        return this.updateById(oldScheme);
    }

    @Override
    public ProjectSchemeVO getBySchemeId(String id) {
        if (StrUtil.isBlank(id)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "任务id不能为空！");
        }
        ProjectScheme byId = this.getById(id);
        if (ObjectUtil.isNull(byId)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "该计划不存在");
        }
        ProjectSchemeVO projectSchemeVO = BeanCopyUtils.convertTo(byId, ProjectSchemeVO::new);
        return projectSchemeVO;
    }

    @Override
    public Boolean relationToQualityItem(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToQualityItemService.list(new LambdaQueryWrapper<>(PlanToQualityItem.class)
                .in(PlanToQualityItem::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToQualityItem::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToQualityItem> planToQualityItems = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToQualityItem planToQualityItem = new PlanToQualityItem();
            planToQualityItem.setClassName("PlanToQualityItem");
            planToQualityItem.setFromId(fromId);
            planToQualityItem.setFromClass("QualityItem");
            planToQualityItem.setToId(fromIdsRelationToIdDTO.getToId());
            planToQualityItem.setToClass("Plan");
            planToQualityItems.add(planToQualityItem);
        }
        planToQualityItemService.saveBatch(planToQualityItems);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToQualityItem(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        planToQualityItemService.remove(new LambdaQueryWrapper<>(PlanToQualityItem.class)
                .eq(PlanToQualityItem::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToQualityItem::getFromId, fromIdsRelationToIdDTO.getFromIds()));
        return true;
    }

    @Override
    public List<QualityItemVO> relationToQualityItem(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToQualityItem> relationList = planToQualityItemService.list(new LambdaQueryWrapper<>(PlanToQualityItem.class)
                .eq(PlanToQualityItem::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<String> qualityIdList = relationList.stream().map(PlanToQualityItem::getFromId).collect(Collectors.toList()); // 质控ID列表
        // 通过质控ID列表获取 质控信息列表
        String keyword = "";
        if (Objects.nonNull(planQueryDTO)) {
            keyword = planQueryDTO.getKeyword();
        }
        ResponseDTO<List<QualityStepVO>> responseDTO = pasFeignService.list(qualityIdList,keyword);
        if(ResponseUtils.success(responseDTO)){
            List<QualityStepVO> qualityStepVOS =  responseDTO.getResult();
            if(CollectionUtils.isEmpty(qualityStepVOS)){
                new ArrayList<>();
            }
            List<QualityItemVO> qualityItemVOS = new ArrayList<>();
            for (QualityStepVO qualityStepVO : qualityStepVOS) {
                QualityItemVO qualityItemVO = new QualityItemVO();
                BeanCopyUtils.copyProperties(qualityStepVO, qualityItemVO);
                qualityItemVOS.add(qualityItemVO);
            }
            return qualityItemVOS;
        }
        return   new ArrayList<>();
//        LambdaQueryWrapper<QualityItem> condition = new LambdaQueryWrapper<>();
//        condition.in(QualityItem::getId, qualityIdList);
//        if (Objects.nonNull(planQueryDTO)) {
//            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
//                condition.and(sub -> sub.like(QualityItem::getPoint, planQueryDTO.getKeyword()).or().like(QualityItem::getNumber, planQueryDTO.getKeyword()));
//            }
//        }
//
//        List<QualityItem> qualityItemList = qualityItemService.list(condition);
//        if (CollectionUtils.isEmpty(qualityItemList)) {
//            return new ArrayList<>();
//        }
//
//        List<QualityItemVO> qualityItemVOS = BeanCopyUtils.convertListTo(qualityItemList, QualityItemVO::new);
//        qualityItemService.setEveryName(qualityItemVOS);
//        return qualityItemVOS;
    }

    @Override
    public List<SchemeOperateLogVO> logListById(String id) throws Exception {
        if (StrUtil.isBlank(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<OperateLog> operateLogByBizNoList = operateLogService.getOperateLogByBizNo(id);
        if (CollectionUtil.isEmpty(operateLogByBizNoList)) {
            return new ArrayList<>();
        }
        ProjectScheme projectScheme = this.getById(id);

        return operateLogByBizNoList.stream()
                .filter(f -> currentUserHelper.getUserId().equals(f.getUserId())
                        && StrUtil.isNotBlank(f.getType())
                        && StrUtil.isNotBlank(f.getSubType())
                        && ObjectUtil.isNotNull(f.getIsSuccess())
                        && f.getType().equals("项目计划")
                        && f.getSubType().contains("过程记录")
                        && f.getIsSuccess()).map(m -> {
                    SchemeOperateLogVO schemeOperateLogVO = new SchemeOperateLogVO();
                    schemeOperateLogVO.setRemark(m.getRemark());
                    schemeOperateLogVO.setCreateTime(m.getCreateTime());
                    schemeOperateLogVO.setProjectSchemeName(projectScheme.getName());
                    return schemeOperateLogVO;
                }).collect(Collectors.toList());

    }

    @Override
    public Page<ProjectSchemeVO> getSchemePage(Page<ProjectSchemeDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ProjectScheme> condition = new LambdaQueryWrapperX<>(ProjectScheme.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ProjectSchemeDTO query = pageRequest.getQuery();
        if (query != null) {
            String projectId = query.getProjectId();
            if (StrUtil.isNotBlank(projectId)) {
                condition.eq(ProjectScheme::getProjectId, projectId);
            }
        }
        condition.eq(ProjectScheme::getNodeType, "plan");
        condition.orderByDesc(ProjectScheme::getCreateTime);


        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectScheme> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        List<ProjectSchemeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        commonManager.codeMapping(vos);
//        List<ProjectSchemeVO> vos = page.getContent().stream().map(scheme -> {
//            ProjectSchemeVO projectSchemeVO = BeanCopyUtils.convertTo(scheme, ProjectSchemeVO::new);
//            commonManager.codeMapping(projectSchemeVO);
//            return projectSchemeVO;
//        }).collect(Collectors.toList());
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Boolean updateActualBeginTime(String id) {
        ProjectScheme projectScheme = this.getById(id);
        if (!projectScheme.getStatus().equals(Status.PUBLISHED.getCode())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "非已下发计划无法开始执行");
        }
        projectScheme.setId(id);
        projectScheme.setActualBeginTime(new Date());
        projectScheme.setStatus(Status.EXECUTING.getCode());
        messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, id);
        redisTemplate.opsForSet().remove(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, id);
        return this.updateById(projectScheme);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean schemeFallback(FallBackDTO fallBackDTO) {
        ProjectScheme byId = this.getById(fallBackDTO.getId());
        if (!Status.PUBLISHED.getCode().equals(byId.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只有已下发状态才能退回计划");
        }
        byId.setStatus(Status.PENDING.getCode());
        byId.setIssueRemindInterval(0);
        byId.setIssueRemindIntervalUnit("");
        byId.setCircumstance(Status.CIRCUMSTANCE_FALLBACK.getCode());
        this.updateById(byId);
        //计划退回提醒
        messageManager.sendMsg(MsgBusinessTypeEnum.FALLBACK, SchemeMsgDTO.builder().projectSchemeList(Collections.singletonList(byId)).recipientIds(Collections.singletonList(byId.getCreatorId())).build());
        //新增退回记录
        ProjectSchemeFallback projectSchemeFallback = new ProjectSchemeFallback();
        projectSchemeFallback.setProjectSchemeId(fallBackDTO.getId());
        projectSchemeFallback.setReason(fallBackDTO.getReason());
        projectSchemeFallbackService.save(projectSchemeFallback);
        messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, byId.getId());
        return true;
    }

    @Override
    public Map<String, List<ProjectScheme>> getSchemeByRspUsersForTime(List<String> userIds, Date beginTime, Date endTime) {
        if (CollectionUtil.isEmpty(userIds)) {
            return new HashMap<>();
        }
        if (ObjectUtil.isNull(beginTime)) {
            return new HashMap<>();
        }
        if (ObjectUtil.isNull(endTime)) {
            return new HashMap<>();
        }

        LambdaQueryWrapperX<ProjectScheme> wrapperX = new LambdaQueryWrapperX<>(ProjectScheme.class);
        wrapperX.and(wrapper -> wrapper.in(ProjectScheme::getRspUser, userIds)
                        .or().in(ProjectScheme::getParticipantUsers, userIds))
                // 产品
//                .eq(ProjectScheme::getStatus, Status.PUBLISHED.getCode())
                .ge(ProjectScheme::getBeginTime, beginTime)
                .le(ProjectScheme::getEndTime, endTime);

        List<ProjectScheme> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<String, List<ProjectScheme>> rspUserMap = new HashMap<>();

        list.stream().forEach(c -> {
            String participantUsers = c.getParticipantUsers();
            if (StrUtil.isNotBlank(participantUsers)) {
                List<String> participantUsersList = Arrays.stream(participantUsers.split(",")).collect(Collectors.toList());
                for (String userId : participantUsersList) {
                    if (rspUserMap.containsKey(userId)) {
                        List<ProjectScheme> projectSchemes = rspUserMap.get(userId);
                        if (!projectSchemes.contains(c)) {
                            projectSchemes.add(c);
                            rspUserMap.put(userId, projectSchemes);
                        }
                    } else {
                        List<ProjectScheme> schemeIds = new ArrayList<>();
                        schemeIds.add(c);
                        rspUserMap.put(userId, schemeIds);
                    }
                }
            }
            String rspUser = c.getRspUser();
            if (StrUtil.isNotBlank(rspUser)) {
                if (rspUserMap.containsKey(rspUser)) {
                    List<ProjectScheme> projectSchemes = rspUserMap.get(rspUser);
                    if (!projectSchemes.contains(c)) {
                        projectSchemes.add(c);
                        rspUserMap.put(rspUser, projectSchemes);
                    }
                } else {
                    List<ProjectScheme> schemeIds = new ArrayList<>();
                    schemeIds.add(c);
                    rspUserMap.put(rspUser, schemeIds);
                }
            }
        });
        return rspUserMap;
    }

    @Override
    public Page<ProjectSchemeVO> schemeByUserIdPages(String userId, Page<ProjectSchemeDTO> pageRequest) throws Exception {
        if (StrUtil.isBlank(userId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "用户id不能为空");
        }

        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX(ProjectScheme.class);
        lambdaQueryWrapperX.select(ProjectScheme::getId, ProjectScheme::getProjectId, ProjectScheme::getName, ProjectScheme::getBeginTime, ProjectScheme::getEndTime);
        lambdaQueryWrapperX.eq(ProjectScheme::getRspUser, userId)
                .or()
                .like(ProjectScheme::getParticipantUsers, userId);

        PageResult<ProjectScheme> page = projectSchemeRepository.selectPage(pageRequest, lambdaQueryWrapperX);

        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (CollectionUtil.isEmpty(page.getContent())) {
            return pageResult;
        }

        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        List<String> projectIds = schemeVOS.stream().map(ProjectSchemeVO::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, String> projectMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));

        WorkHoursReportDetailForUserRequest workHoursReportDetailForUserRequest = new WorkHoursReportDetailForUserRequest();
        workHoursReportDetailForUserRequest.setUserIds(Arrays.asList(userId));
        workHoursReportDetailForUserRequest.setSchemeIds(schemeVOS.stream().map(ProjectSchemeVO::getId).collect(Collectors.toList()));
        Map<String, Map<String, Double>> userWorkHoursBySchemeForTimeMap = workHoursReportDetailForUserService.getUserWorkHoursBySchemeForTime(workHoursReportDetailForUserRequest);

        return pageResult.setContent(schemeVOS.stream().map(m -> {
            m.setProjectName(projectMap.get(m.getProjectId()));
            Map<String, Double> stringDoubleMap = userWorkHoursBySchemeForTimeMap.get(userId);
            if (ObjectUtil.isNotNull(stringDoubleMap)) {
                Double day = stringDoubleMap.get(m.getId());
                m.setReportingDays(ObjectUtil.isNotNull(day) ? String.valueOf(day) : "0");
            }
            return m;
        }).collect(Collectors.toList()));

    }

    /**
     * 获取项目所有里程碑分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @Override
    public Page<ProjectSchemeVO> getMilestonePage(Page<ProjectSchemeDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ProjectScheme> condition = new LambdaQueryWrapperX<>(ProjectScheme.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ProjectSchemeDTO query = pageRequest.getQuery();
        if (query != null) {
            String projectId = query.getProjectId();
            if (StrUtil.isNotBlank(projectId)) {
                condition.eq(ProjectScheme::getProjectId, projectId);
            }
        }
        condition.eq(ProjectScheme::getNodeType, "milestone");
        condition.orderByDesc(ProjectScheme::getCreateTime);


        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectScheme> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeVO::new);
        commonManager.codeMapping(vos);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public String revocation(String id) {
        ProjectScheme projectScheme = this.getById(id);

        if (!Status.PUBLISHED.getCode().equals(projectScheme.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只有已下发状态才能撤回计划");
        }
        List<ProjectScheme> child = getSchemesChild(projectScheme);

        List<ProjectScheme> errProjectScheme = child.stream().filter(item -> !item.getStatus().equals(Status.PUBLISHED.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errProjectScheme)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "当前已有子级计划被执行，无法撤回");
        }
        child.add(projectScheme);
        for (ProjectScheme scheme : child) {
            scheme.setStatus(Status.PENDING.getCode());
            scheme.setIssueRemindInterval(0);
            scheme.setIssueRemindIntervalUnit("");
            scheme.setCircumstance(Status.CIRCUMSTANCE_FALLBACK.getCode());
        }
        this.updateBatchById(child);
        for (ProjectScheme scheme : child) {
            messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, scheme.getId());
            sendMessage(scheme.getId(), scheme.getName(), Collections.singletonList(projectScheme.getCreatorId()),
                    projectScheme.getCreatorId(), projectScheme.getOrgId(), projectScheme.getPlatformId());
        }

        String returnMsg = "已撤回" + child.size() + "条计划";
        return returnMsg;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeConfirmation(ProjectSchemeDTO projectSchemeDTO) throws Exception {

        ProjectScheme projectScheme = this.getById(projectSchemeDTO.getId());
        if (!projectScheme.getStatus().equals(Status.CONFIRMED.getCode())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "非待确认计划无法完成确认");
        }
        if (projectSchemeDTO.getConfirmResult().equals("agree")) {
            projectScheme.setId(projectSchemeDTO.getId());
            projectScheme.setExamineType(ProjectSchemeExamineTypeEnum.GRADED.getValue());
            projectScheme.setIdentification(projectSchemeDTO.getReasonConfirmation());
            projectScheme.setStatus(Status.FINISHED.getCode());
            if (projectScheme.getEndTime().compareTo(projectScheme.getActualEndTime()) >= 0) {
                projectScheme.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode());
            } else {
                projectScheme.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode());
            }
        }
        if (projectSchemeDTO.getConfirmResult().equals("reject")) {
            projectScheme.setId(projectSchemeDTO.getId());
            projectScheme.setIdentification(projectSchemeDTO.getReasonConfirmation());
            projectScheme.setStatus(Status.EXECUTING.getCode());
        }
        return this.updateById(projectScheme);
    }

    @Override
    public String suspendScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = this.getById(projectSchemeDTO.getId());
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectSchemeDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectSchemeDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_SUSPEND);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }

        List<ProjectScheme> child = getSchemesChild(projectScheme);

        List<ProjectScheme> suspendScheme = child.stream().filter(item ->
                item.getStatus().equals(Status.PUBLISHED.getCode()) ||
                        (item.getStatus().equals(Status.EXECUTING.getCode()) && !item.getCircumstance().equals(Status.SUSPEND_APPROVAL.getCode())
                                && !item.getCircumstance().equals(Status.TERMINATION_APPROVAL.getCode()))).collect(Collectors.toList());

        suspendScheme.add(projectScheme);
        if (projectSchemeDTO.getIsProcess()) {
            //TODO启动流程
            wfInstanceManage.process(suspendScheme, SchemeDict.SUSPEND_PROCESS);
            for (ProjectScheme scheme : suspendScheme) {
                scheme.setLastStatus(projectScheme.getStatus());
                scheme.setCircumstance(Status.SUSPEND_APPROVAL.getCode());
                scheme.setSuspendTime(projectSchemeDTO.getSuspendTime());
                scheme.setSuspendReason(projectSchemeDTO.getSuspendReason());
            }
        } else {
            for (ProjectScheme scheme : suspendScheme) {
                scheme.setLastStatus(projectScheme.getStatus());
                scheme.setStatus(Status.SUSPEND.getCode());
                scheme.setCircumstance(0);
                scheme.setSuspendTime(new Date());
                scheme.setSuspendReason(projectSchemeDTO.getSuspendReason());
            }
        }
        this.updateBatchById(suspendScheme);
        String returnMsg = "已暂停" + suspendScheme.size() + "条计划";
        return returnMsg;
    }

    @Override
    public String terminateScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = this.getById(projectSchemeDTO.getId());
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectSchemeDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectSchemeDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_TERMINATE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }
        List<ProjectScheme> child = getSchemesChild(projectScheme);
        //删除子项流程
        if (CollUtil.isNotEmpty(child)) {
            wfInstanceManage.removeProcess(child);
        }

        child.add(projectScheme);
        if (projectSchemeDTO.getIsProcess()) {
            //TODO启动流程
            wfInstanceManage.process(child, SchemeDict.TERMINATION_PROCESS);
            for (ProjectScheme scheme : child) {
                scheme.setCircumstance(Status.TERMINATION_APPROVAL.getCode());
                scheme.setTerminateTime(projectSchemeDTO.getTerminateTime());
                scheme.setTerminateReason(projectSchemeDTO.getTerminateReason());
            }
        } else {
            for (ProjectScheme scheme : child) {
                scheme.setStatus(Status.TERMINATION.getCode());
                scheme.setCircumstance(0);
                scheme.setTerminateTime(new Date());
                scheme.setTerminateReason(projectSchemeDTO.getTerminateReason());
            }
        }
        this.updateBatchById(child);
        String returnMsg = "已停止" + child.size() + "条计划";
        return returnMsg;
    }

    @Override
    public String startScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = this.getById(projectSchemeDTO.getId());
        if (!projectScheme.getStatus().equals(Status.SUSPEND.getCode())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "非暂停计划无法开始启动");
        }
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectSchemeDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectSchemeDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_SCHEME_START);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }
        List<ProjectScheme> parentSchemes = getParentSchemes(projectScheme);

        List<ProjectScheme> errSchemes = parentSchemes.stream().filter(item -> item.getStatus().equals(Status.SUSPEND.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errSchemes)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "顶级计划未启动，无法单独启动子级计划");
        }
        List<ProjectScheme> child = getSchemesChild(projectScheme);

        List<ProjectScheme> startScheme = child.stream().filter(item -> item.getStatus().equals(Status.SUSPEND.getCode())).collect(Collectors.toList());
        startScheme.add(projectScheme);
        if (projectSchemeDTO.getIsProcess()) {
            //TODO启动流程
            wfInstanceManage.process(startScheme, SchemeDict.START_PROCESS);
            for (ProjectScheme scheme : startScheme) {
                scheme.setCircumstance(Status.START_APPROVAL.getCode());
                scheme.setSuspendTime(projectSchemeDTO.getStartTime());
                scheme.setStartReason(projectSchemeDTO.getStartReason());
            }
        } else {
            for (ProjectScheme scheme : startScheme) {
                scheme.setStatus(scheme.getLastStatus());
                scheme.setCircumstance(0);
                scheme.setSuspendTime(new Date());
                scheme.setStartReason(projectSchemeDTO.getStartReason());
            }
        }
        this.updateBatchById(startScheme);
        String returnMsg = "已启动" + startScheme.size() + "条计划";
        return returnMsg;
    }

    @Override
    public Boolean getApprovalScheme(String projectId) {
        return true;
    }

    @Override
    public String createScheme(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new);
        projectScheme.setDurationDays(1);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        ProjectScheme parent = this.getById(projectSchemeDTO.getParentId());
        //存在父级
        if(StringUtils.hasText(projectSchemeDTO.getParentId()) && !"0".equals(projectSchemeDTO.getParentId())){
            projectScheme.setBeginTime(parent.getBeginTime());
            projectScheme.setEndTime(parent.getEndTime());
        }else{
            if (ObjectUtil.isEmpty(projectScheme.getStartTime())) {
                projectScheme.setBeginTime(dateFormat.parse(dateFormat.format(new Date())));
            } else {
                projectScheme.setBeginTime(dateFormat.parse(dateFormat.format(projectScheme.getStartTime())));
            }
            projectScheme.setParentId("0"); //顶级计划
            CalculationEndDateDTO calculationEndDateDTO = new CalculationEndDateDTO();
            calculationEndDateDTO.setStart(projectScheme.getBeginTime());
            calculationEndDateDTO.setWorkdayNum(1);
            Date endTime = holidaysApi.calculationEndDate(calculationEndDateDTO).getResult();
            projectScheme.setEndTime(endTime);
        }

        projectScheme.setNodeType(ProjectNodeTypeEnum.PLAN.getCode());
        UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), currentUserHelper.getUserId());
        setOrgInfo(projectScheme, userVO.getOrganizations());
        projectScheme.setRspUser(userVO.getId());
        projectScheme.setRspUserCode(userVO.getCode());
        projectScheme.setRspUserName(userVO.getName());
        Project project = projectService.getById(projectSchemeDTO.getProjectId());
        ClassVO classVO = classRedisHelper.getClassByClassName(ProjectScheme.class.getSimpleName());
        List<ProjectScheme> projectSchemeList = this.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, project.getId())
                .eq(ProjectScheme::getParentId,projectSchemeDTO.getParentId())
                .eq(ProjectScheme::getLogicStatus,1));
        Long maxSort = projectSchemeList.stream().map(ProjectScheme::getSort).max(Comparator.comparing(Long::longValue)).orElse(0L);
        AtomicLong sort = new AtomicLong(maxSort + 1);
        String id = String.format("%s%s", Objects.isNull(classVO) ? "" : classVO.getCode(), IdUtil.getSnowflakeNextIdStr());
        projectScheme.setId(id);
        projectScheme.setName("新建计划1");
        projectScheme.setParentId(projectSchemeDTO.getParentId());
        projectScheme.setProjectId(project.getId());
        projectScheme.setTopSort(0);
        projectScheme.setProjectNumber(project.getNumber());

        projectScheme.setParentChain(Objects.isNull(parent) ? "0" : parent.getParentChain() + "," + parent.getId());
        projectScheme.setSchemeNumber(Objects.isNull(parent) ? project.getNumber() + "-" + sort : parent.getSchemeNumber() + "-" + sort);
        projectScheme.setNumber(projectScheme.getSchemeNumber());
        if(projectSchemeDTO.getSort() == null || projectSchemeDTO.getSort() == 0){
            projectScheme.setSort(maxSort + 1);
        }else{
            projectScheme.setSort(projectSchemeDTO.getSort()+1);
        }
        projectScheme.setLevel(projectScheme.getParentChain().split(",").length);
        projectScheme.setCircumstance(0);
        projectScheme.setStatus(Status.PENDING.getCode());
        //计算工时
        Long durationDays = DateUtil.betweenDay(projectScheme.getBeginTime(), projectScheme.getEndTime(), true)+1;
        if (ObjectUtil.isNotEmpty(durationDays)) {
            projectScheme.setDurationDays(durationDays.intValue());
        }
        boolean save = this.save(projectScheme);
        if(projectSchemeDTO.getSort() != null && projectSchemeDTO.getSort() > 0){
            //重新排序
            //获取放置的坐标
            long dropIndex = projectSchemeDTO.getSort();
            //插入对象
            projectSchemeList.add((int) dropIndex,projectScheme);
            for (long i = 0; i < projectSchemeList.size(); i++) {
                projectSchemeList.get((int) i).setSort(i + 1);
            }
            this.updateBatchById(projectSchemeList);
        }
        return id;
    }

    private void setOrgInfo(ProjectScheme p, List<DeptVO> orgs) {
        if (CollUtil.isEmpty(orgs)) {
            return;
        }
        DeptVO deptVO = orgs.get(0);
        if ("20".equals(deptVO.getType())) {
            p.setRspSubDept(deptVO.getId());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = getParentOrgId(deptVO);
            p.setRspSubDept(deptOrg.getId());
        } else if ("40".equals(deptVO.getType())) {
            DeptVO sectionOrg = getParentOrgId(deptVO);
            DeptVO deptOrg = getParentOrgId(sectionOrg);
            p.setRspSubDept(deptOrg.getId());
            p.setRspSectionId(sectionOrg.getId());
        }else{
            p.setRspSubDept(deptVO.getId());
        }

    }

    private DeptVO getParentOrgId(DeptVO org) {
        String parentId = org.getParentId();
        DeptVO organization = deptRedisHelper.getDeptById(parentId);
        return Objects.nonNull(organization) ? organization : new DeptVO();
    }


    //根据项目获取父级项目
    private List<ProjectScheme> getParentSchemes(ProjectScheme projectScheme) {
        List<ProjectScheme> viewList = new ArrayList<>();
        List<ProjectScheme> projectSchemes = getProjectSchemesByProjectId(projectScheme.getProjectId());
        getParentScheme(viewList, projectScheme, projectSchemes);
        return viewList;
    }

    private void getParentScheme(List<ProjectScheme> viewList, ProjectScheme projectScheme, List<ProjectScheme> sourceList) {
        List<ProjectScheme> collect = sourceList.stream().filter(item -> projectScheme.getParentId().equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.add(collect.get(0));
            getParentScheme(viewList, collect.get(0), sourceList);
        }
    }

    private List<ProjectScheme> getSchemesChild(ProjectScheme projectScheme) {
        List<ProjectScheme> viewList = new ArrayList<>();
        List<ProjectScheme> projectSchemes = getProjectSchemesByProjectId(projectScheme.getProjectId());
        getSchemesAllChild(viewList, projectScheme, projectSchemes);
        return viewList;
    }


    private void getSchemesAllChild(List<ProjectScheme> viewList, ProjectScheme projectScheme, List<ProjectScheme> sourceList) {
        List<ProjectScheme> collect = sourceList.stream().filter(item -> projectScheme.getId().equals(item.getParentId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.addAll(collect);
            for (ProjectScheme projectScheme1 : collect) {
                getSchemesAllChild(viewList, projectScheme1, sourceList);
            }
        }
    }

    private List<ProjectScheme> getProjectSchemesByProjectId(String projectId) {
        List<ProjectScheme> projectSchemes = this.list(new LambdaQueryWrapper<>(ProjectScheme.class).eq(ProjectScheme::getProjectId, projectId));
        return projectSchemes;
    }

    //销毁流程实例
    public void deleteProcess(List<String> ids) {

    }

    //取消代办
    public void getChildScheme(List<String> ids) {

    }

    private void sendMessage(String businessId,
                             String name,
                             List<String> recipientIdList,
                             String sendId,
                             String orgId,
                             String platformId) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(String.format("feedback_%s", businessId))
                .todoStatus(0)
                .businessTypeCode("ProjectScheme")
                .businessNodeCode(ProjectSchemeNodeEnum.scheme_revocation.getCode())
                .messageUrl(String.format("/pms/ProPlanDetails/%s", businessId))
                .messageUrlName("项目计划详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(recipientIdList)
                .messageMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("name", name)
                        .build())
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("name", name)
                        .build())
                .senderTime(new Date())
                .senderId(sendId)
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    @Override
    public Page<ProjectSchemePerformanceVO> pageProjectSchemeByUserId(Page<ProjectSchemeReportDTO> pageRequest) throws Exception {
        ProjectSchemeReportDTO query = pageRequest.getQuery();
        LambdaQueryWrapperX<ProjectScheme> condition = getProjectSchemeConditionByPerformance(query);


        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectScheme> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectSchemePerformanceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectScheme> projectSchemeList = page.getContent();
        List<String> projectIdList = projectSchemeList.stream().map(ProjectScheme::getProjectId).distinct().collect(Collectors.toList());
        Map<String, String> projectMap;
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class)
                            .select(Project::getId, Project::getName).in(Project::getId, projectIdList))
                    .stream().collect(Collectors.toMap(Project::getId, Project::getName));
        } else {
            projectMap = new HashMap<>();
        }
        Map<String, Long> projectSchemeContentMap = new HashMap<>();
        if (query.getTaskFeedback()) {
            projectSchemeContentMap.putAll(projectSchemeContentService.list(new LambdaQueryWrapperX<>(ProjectSchemeContent.class)
                            .select(ProjectSchemeContent::getId, ProjectSchemeContent::getProjectSchemeId)
                            .in(ProjectSchemeContent::getProjectSchemeId, projectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(ProjectSchemeContent::getProjectSchemeId, Collectors.counting())));
        }
        List<ProjectSchemePerformanceVO> vos = projectSchemeList.stream().map(scheme -> {
            ProjectSchemePerformanceVO projectSchemePerformanceVO = BeanCopyUtils.convertTo(scheme, ProjectSchemePerformanceVO::new);
            projectSchemePerformanceVO.setNodeTypeName(ProjectSchemeNodeTypeEnum.getNameByValue(projectSchemePerformanceVO.getNodeType()));
            projectSchemePerformanceVO.setProjectName(projectMap.get(projectSchemePerformanceVO.getProjectId()));
            projectSchemePerformanceVO.setFeedbackNum(projectSchemeContentMap.getOrDefault(scheme.getId(), 0L));
            return projectSchemePerformanceVO;
        }).collect(Collectors.toList());
        pageResult.setContent(vos);
        return pageResult;
    }

    private LambdaQueryWrapperX<ProjectScheme> getProjectSchemeConditionByPerformance(ProjectSchemeReportDTO query) throws Exception {
        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getUserId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未传用户id");
        }
        LambdaQueryWrapperX<ProjectScheme> condition = new LambdaQueryWrapperX<>(ProjectScheme.class);
        condition.eq(ProjectScheme::getRspUser, query.getUserId());
        if (StrUtil.isNotBlank(query.getProjectId())) {
            condition.eq(ProjectScheme::getProjectId, query.getProjectId());
        }
        if (query.getCompleteTask()) {
            condition.eq(ProjectScheme::getStatus, Status.FINISHED.getCode());
        }
        if (query.getOverdueTask()) {
            condition.in(ProjectScheme::getCircumstance, Arrays.asList(Status.CIRCUMSTANCE_OVERD.getCode(), Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode()));
        }
        //todo 变更

        condition.orderByDesc(ProjectScheme::getCreateTime);
        return condition;
    }

    @Override
    public void exportExcelByUserId(ProjectSchemeReportDTO projectSchemeReportDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectScheme> condition = getProjectSchemeConditionByPerformance(projectSchemeReportDTO);
        List<ProjectScheme> projectSchemeList = this.list(condition);
        int i = 1;
        List<String> projectIdList = projectSchemeList.stream().map(ProjectScheme::getProjectId).distinct().collect(Collectors.toList());
        Map<String, String> projectMap;
        if (CollectionUtil.isNotEmpty(projectIdList)) {
            projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class)
                            .select(Project::getId, Project::getName).in(Project::getId, projectIdList))
                    .stream().collect(Collectors.toMap(Project::getId, Project::getName));
        } else {
            projectMap = new HashMap<>();
        }
        if (projectSchemeReportDTO.getCompleteTask()) {
            String fileName = "已完成计划导出.xlsx";
            exportExcelByUserId(response, projectSchemeList, i, projectMap, fileName);
        } else if (projectSchemeReportDTO.getChangeTask()) {
            String fileName = "变更计划导出.xlsx";
            exportExcelByUserId(response, projectSchemeList, i, projectMap, fileName);
        } else if (projectSchemeReportDTO.getOverdueTask()) {
            String fileName = "逾期计划导出.xlsx";
            exportExcelByUserId(response, projectSchemeList, i, projectMap, fileName);
        } else if (projectSchemeReportDTO.getTaskFeedback()) {
            List<ProjectSchemePerformanceFeedbackVO> vos = new ArrayList<>();
            Map<String, Long> projectSchemeContentMap = projectSchemeContentService.list(new LambdaQueryWrapperX<>(ProjectSchemeContent.class)
                            .select(ProjectSchemeContent::getId, ProjectSchemeContent::getProjectSchemeId)
                            .in(ProjectSchemeContent::getProjectSchemeId, projectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(ProjectSchemeContent::getProjectSchemeId, Collectors.counting()));
            for (ProjectScheme projectScheme : projectSchemeList) {
                ProjectSchemePerformanceFeedbackVO projectSchemePerformanceVO = BeanCopyUtils.convertTo(projectScheme, ProjectSchemePerformanceFeedbackVO::new);
                projectSchemePerformanceVO.setNodeTypeName(ProjectSchemeNodeTypeEnum.getNameByValue(projectSchemePerformanceVO.getNodeType()));
                projectSchemePerformanceVO.setSort(i++);
                projectSchemePerformanceVO.setStatusName(projectSchemePerformanceVO.getDataStatus() != null ? projectSchemePerformanceVO.getDataStatus().getName() : "");
                projectSchemePerformanceVO.setProjectName(projectMap.get(projectSchemePerformanceVO.getProjectId()));
                projectSchemePerformanceVO.setFeedbackNum(projectSchemeContentMap.getOrDefault(projectScheme.getId(), 0L));
                vos.add(projectSchemePerformanceVO);
            }
            String fileName = "计划反馈导出.xlsx";
            ExcelUtils.write(response, fileName, "sheet1", ProjectSchemePerformanceFeedbackVO.class, vos);
        } else {
            List<ProjectSchemePerformanceDurationVO> vos = new ArrayList<>();
            for (ProjectScheme projectScheme : projectSchemeList) {
                ProjectSchemePerformanceDurationVO projectSchemePerformanceVO = BeanCopyUtils.convertTo(projectScheme, ProjectSchemePerformanceDurationVO::new);
                projectSchemePerformanceVO.setNodeTypeName(ProjectSchemeNodeTypeEnum.getNameByValue(projectSchemePerformanceVO.getNodeType()));
                projectSchemePerformanceVO.setSort(i++);
                projectSchemePerformanceVO.setStatusName(projectSchemePerformanceVO.getDataStatus() != null ? projectSchemePerformanceVO.getDataStatus().getName() : "");
                projectSchemePerformanceVO.setProjectName(projectMap.get(projectSchemePerformanceVO.getProjectId()));
                vos.add(projectSchemePerformanceVO);
            }
            String fileName = "负责计划导出.xlsx";
            ExcelUtils.write(response, fileName, "sheet1", ProjectSchemePerformanceDurationVO.class, vos);
        }

    }

    private void exportExcelByUserId(HttpServletResponse response, List<ProjectScheme> projectSchemeList, int i, Map<String, String> projectMap, String fileName) throws Exception {
        List<ProjectSchemePerformanceVO> vos = new ArrayList<>();
        for (ProjectScheme projectScheme : projectSchemeList) {
            ProjectSchemePerformanceVO projectSchemePerformanceVO = BeanCopyUtils.convertTo(projectScheme, ProjectSchemePerformanceVO::new);
            projectSchemePerformanceVO.setNodeTypeName(ProjectSchemeNodeTypeEnum.getNameByValue(projectSchemePerformanceVO.getNodeType()));
            projectSchemePerformanceVO.setSort(i++);
            projectSchemePerformanceVO.setStatusName(projectSchemePerformanceVO.getDataStatus() != null ? projectSchemePerformanceVO.getDataStatus().getName() : "");
            projectSchemePerformanceVO.setProjectName(projectMap.get(projectSchemePerformanceVO.getProjectId()));
            vos.add(projectSchemePerformanceVO);
        }
        ExcelUtils.write(response, fileName, "sheet1", ProjectSchemePerformanceVO.class, vos);
    }

    public List<ProjectSchemeContentVO> getProjectSchemeContentList(String id) throws Exception {
        ProjectScheme byId = this.getById(id);
        List<ProjectSchemeContentVO> list;
        if (ObjectUtil.isNotEmpty(byId)) {
            list = projectSchemeContentService.getList(id);
            list.forEach(f -> f.setProjectSchemeName(byId.getName()));
        } else {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<ProjectSchemeTemplateEditVO> getTimeByPrePostRelation(ProjectSchemeTemplateEditDTO projectSchemeTemplateEditDTO) throws Exception {
        String projectId = projectSchemeTemplateEditDTO.getProjectId();
        Project project = projectService.getById(projectId);
        if (ObjectUtil.isEmpty(project)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在或者已经被删除！");
        }
        Map<String, Map<Integer, List<ProjectSchemePrePost>>> projectSchemePrePostMap = projectSchemeMilestoneNodePrePostService.list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class)
                        .eq(ProjectSchemeMilestoneNodePrePost::getTemplateId, projectSchemeTemplateEditDTO.getTemplateId()))
                .stream().map(m -> BeanCopyUtils.convertTo(m, ProjectSchemePrePost::new))
                .collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId,
                        Collectors.groupingBy(ProjectSchemePrePost::getType)));
        Map<String, ProjectScheme> projectSchemeMap = projectSchemeTemplateEditDTO.getProjectSchemeDTOList()
                .stream().map(m -> BeanCopyUtils.convertTo(m, ProjectScheme::new))
                .collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));
        ProjectScheme projectScheme = projectSchemeMap.get(projectSchemeTemplateEditDTO.getId());
        if (ObjectUtil.isEmpty(projectScheme)) {
            projectScheme = new ProjectScheme();
            projectScheme.setId(projectSchemeTemplateEditDTO.getId());
        }
        List<ProjectScheme> updateBeginTimeDataList = new ArrayList<>();
        //计划的开始结束时间要在项目的开始结束时间范围内
        LocalDate maxEndTime = DateUtil.parseLocalDateTime(DateUtil.formatDate(project.getProjectEndTime()), DatePattern.NORM_DATE_PATTERN).toLocalDate();
        LocalDate minBeginTime = DateUtil.parseLocalDateTime(DateUtil.formatDate(project.getProjectStartTime()), DatePattern.NORM_DATE_PATTERN).toLocalDate();

        Map<Integer, List<ProjectSchemePrePost>> prePostTypeMap = projectSchemePrePostMap.get(projectSchemeTemplateEditDTO.getId());
        if (prePostTypeMap != null) {
            CalculationDateRangeHolidaysDTO calculationDateRangeHolidaysDTO = new CalculationDateRangeHolidaysDTO();
            calculationDateRangeHolidaysDTO.setStartDate(project.getProjectStartTime());
            calculationDateRangeHolidaysDTO.setEndDate(project.getProjectEndTime());
            List<String> holidayList = holidaysApi.calculationEndDateAndNextStartDate(calculationDateRangeHolidaysDTO)
                    .getResult().stream().filter(f -> ObjectUtil.isNotEmpty(f.getDate()))
                    .map(m -> DateUtil.formatDate(m.getDate()))
                    .collect(Collectors.toList());

            if (ObjectUtil.isNotEmpty(projectSchemeTemplateEditDTO.getBeginTime())) {
                projectScheme.setBeginTime(projectSchemeTemplateEditDTO.getBeginTime());
                if (ObjectUtil.isNotEmpty(projectScheme.getDurationDays())) {
                    holidaysUtils.calculationEndDateOfWorkDay(projectScheme, projectScheme.getDurationDays(), holidayList, maxEndTime);
                    updateBeginTimeDataList.add(projectScheme);
                }
            } else if (ObjectUtil.isNotEmpty(projectSchemeTemplateEditDTO.getEndTime())) {
                projectScheme.setEndTime(projectSchemeTemplateEditDTO.getEndTime());
                if (ObjectUtil.isNotEmpty(projectScheme.getDurationDays())) {
                    holidaysUtils.calculationStartDateOfWorkDay(projectScheme, projectScheme.getDurationDays(), holidayList, minBeginTime);
                    updateBeginTimeDataList.add(projectScheme);
                }
            }

            if (ObjectUtil.isNotEmpty(projectScheme.getBeginTime())) {
                setPreProjectSchemeTime(projectScheme, prePostTypeMap, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
            }
            if (ObjectUtil.isNotEmpty(projectScheme.getEndTime())) {
                setPostProjectSchemeTime(projectScheme, prePostTypeMap, projectSchemePrePostMap, projectSchemeMap, updateBeginTimeDataList, minBeginTime, maxEndTime, holidayList);
            }
        }
        return BeanCopyUtils.convertListTo(updateBeginTimeDataList, ProjectSchemeTemplateEditVO::new);

    }

    @Override
    public Page<ProjectSchemeVO> getPlanOrMilestone(Page<SearchDTO> pageRequest, String type) {
        if (StrUtil.isBlank(type)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划类型不能为空！");
        }
        LambdaQueryWrapperX<ProjectScheme> condition = new LambdaQueryWrapperX<>(ProjectScheme.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        SearchDTO query = pageRequest.getQuery();
        if (query != null) {
            String projectId = query.getProjectId();
            if (StrUtil.isNotBlank(projectId)) {
                condition.eq(ProjectScheme::getProjectId, projectId);
            }
        }
        condition.eq(ProjectScheme::getNodeType, type);
        condition.orderByDesc(ProjectScheme::getCreateTime);


        Page<ProjectScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectScheme> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectScheme> content = page.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            return pageResult;
        }

        List<ProjectSchemeVO> vos = BeanCopyUtils.convertListTo(content, ProjectSchemeVO::new);
        List<String> userIds = content.stream().map(ProjectScheme::getRspUser).distinct().collect(Collectors.toList());
        List<String> deptIds = content.stream().map(ProjectScheme::getRspSubDept).distinct().collect(Collectors.toList());
        Map<String, UserVO> userVOMap = userRedisHelper.getUserMapByUserIds(userIds);
        Map<String, SimpleDeptVO> deptVOMap = deptRedisHelper.getSimpleDeptMapByDeptIds(deptIds);
        for (ProjectSchemeVO vo : vos) {
            String rspUser = vo.getRspUser();
            String rspSubDept = vo.getRspSubDept();
            vo.setRspSubDeptName(ObjectUtil.isNotNull(deptVOMap.get(rspSubDept)) ? deptVOMap.get(rspSubDept).getName() : "");
            vo.setRspUserName(ObjectUtil.isNotNull(userVOMap.get(rspUser)) ? userVOMap.get(rspUser).getName() : "");
        }
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Boolean transfer(ProjectSchemeDTO projectSchemeDTO) {
        String id = projectSchemeDTO.getId();
        ProjectScheme projectScheme = this.getById(id);
        String userId = projectScheme.getRspUser();
        projectScheme.setRspSubDept(projectSchemeDTO.getRspSubDept());
        projectScheme.setRspUser(projectSchemeDTO.getRspUser());
        projectScheme.setRspSectionId(projectSchemeDTO.getRspSectionId());
        projectScheme.setRspUserCode(projectSchemeDTO.getRspUserCode());
        projectScheme.setReasonTransfer(projectSchemeDTO.getReasonTransfer());
        if (!userId.equals(projectScheme.getRspUser())) {
            //发送待办到新责任人
            messageManager.sendMsg(MsgBusinessTypeEnum.SEND_DOWN, SchemeMsgDTO.builder().projectSchemeList(CollUtil.toList(projectScheme)).build());
            //消除原有责任人待办
            messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, projectScheme.getId(), projectScheme.getRspUser());
        }
        return this.updateById(projectScheme);
    }

    @Override
    public String createEcrScheme(EcrProjectSchemeAddDTO ecrProjectSchemeAddDTO) throws Exception {
        return ecrSchemeApiService.create(ecrProjectSchemeAddDTO);
    }

    @Override
    public Page<EcrVO> getEcrSchemePage(Page<EcrDTO> page) throws Exception {
        return ecrSchemeApiService.pages(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean drag(ProjectSchemeDragDTO projectSchemeDTO) {
        if(projectSchemeDTO.getSort() == null){
            projectSchemeDTO.setSort(0L);
        }
        ProjectScheme oldProjectScheme = this.getById(projectSchemeDTO.getId());
        //取消置顶
        if(oldProjectScheme.getTopSort() != null && oldProjectScheme.getTopSort() != 0){
            oldProjectScheme.setTopSort(0);
            this.updateById(oldProjectScheme);
        }
        List<ProjectScheme> projectSchemes = this.list(new LambdaQueryWrapper<ProjectScheme>()
                .eq(ProjectScheme::getParentId,projectSchemeDTO.getMoveParentId())
                .eq(ProjectScheme::getLogicStatus,1)
                .eq(ProjectScheme::getProjectId,projectSchemeDTO.getProjectId())
                .orderByAsc(ProjectScheme::getSort));
        long dropIndex =0;
        //跳级先修改数据再排序
        if(!projectSchemeDTO.getMoveParentId().equals(projectSchemeDTO.getParentId())){
            if(!"0".equals(projectSchemeDTO.getMoveParentId())){
                if(StringUtils.isEmpty(projectSchemeDTO.getUpLevelId())){
                    throw new PMSException(PMSErrorCode.PMS_ERR, "请输入上级id");
                }
                ProjectScheme parentScheme = this.getById(projectSchemeDTO.getMoveParentId());
                //表示子级第一个
                if(projectSchemeDTO.getSort().equals(parentScheme.getSort()) &&
                        parentScheme.getId().equals(projectSchemeDTO.getUpLevelId())){
                    dropIndex = 0;
                }else{
                    dropIndex = projectSchemeDTO.getSort();
                }
            }else{
                dropIndex = projectSchemeDTO.getSort();
            }
            //原级数据重新排序
            List<ProjectScheme> parentSchemeList = this.list(new LambdaQueryWrapper<ProjectScheme>()
                    .eq(ProjectScheme::getParentId,oldProjectScheme.getParentId())
                    .eq(ProjectScheme::getLogicStatus,1)
                    .eq(ProjectScheme::getProjectId,oldProjectScheme.getProjectId())
                    .ne(ProjectScheme::getId,oldProjectScheme.getId())
                    .orderByAsc(ProjectScheme::getSort));
            for (long i = 0; i < parentSchemeList.size(); i++) {
                parentSchemeList.get((int) i).setSort(i + 1);
            }
            this.updateBatchById(parentSchemeList);
            //修改现级数据
            oldProjectScheme.setParentId(projectSchemeDTO.getMoveParentId());
            oldProjectScheme.setLevel(projectSchemes.get(0).getLevel());
            oldProjectScheme.setParentChain(projectSchemes.get(0).getParentChain());
            this.updateById(oldProjectScheme);
            projectSchemes.add(oldProjectScheme);
        }else {
            //获取放置的坐标 上移
            if(oldProjectScheme.getSort() > projectSchemeDTO.getSort()){
                dropIndex = projectSchemeDTO.getSort();
            }else{//下移
                dropIndex = projectSchemeDTO.getSort()-1;
            }
        }

        //删除元素
        projectSchemes.remove(oldProjectScheme);
        //插入对象
        projectSchemes.add((int) dropIndex,oldProjectScheme);
        for (long i = 0; i < projectSchemes.size(); i++) {
            projectSchemes.get((int) i).setSort(i + 1);
        }
        this.updateBatchById(projectSchemes);
        return true;
    }

    @Override
    public ProjectSchemeApiVO getBySchemeIdApi(String projectSchemeId) {
        ProjectSchemeVO projectSchemeVO = this.getBySchemeId(projectSchemeId);
        Project project = projectService.getOne(new LambdaQueryWrapper<Project>()
                .eq(Project::getId,projectSchemeVO.getProjectId()));
        if(ObjectUtil.isNotNull(project)){
            projectSchemeVO.setProjectName(project.getName());
        }
        return BeanCopyUtils.convertTo(projectSchemeVO, ProjectSchemeApiVO::new);
    }

    @Override
    public ProjectCenterStatVO getProjectStat(String year, Integer type) {
        ProjectCenterStatVO vo = new ProjectCenterStatVO();
        //待立项里程碑数 合同里程碑未转计划数量
        Integer pendingProjectInitiationCount = projectContractMilestoneService.findPendingCount(year,type);
        vo.setPendingProjectInitiationCount(pendingProjectInitiationCount);
        //已立项项目数
        Integer alreadyProjectInitiationCount =projectInitiationApiService.findInitiationCount(year,type);
        vo.setAlreadyProjectInitiationCount(alreadyProjectInitiationCount);
        //已关闭项目数
        Integer closeProjectInitiationCount = projectInitiationApiService.findCloseCount(year,type);
        vo.setCloseProjectInitiationCount(closeProjectInitiationCount);

        //查询今年所有计划数据
        LambdaQueryWrapper<ProjectScheme> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.apply("YEAR(create_time) = {0}", year);
        if(type == 1){
            lambdaQueryWrapper.eq(ProjectScheme::getRspUser,CurrentUserHelper.getCurrentUserId());
        }
        List<ProjectScheme> allProjectSchemes = this.list(lambdaQueryWrapper);

        //查询今年合同里程碑转计划的数据
        LambdaQueryWrapper<ProjectScheme> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        if(type == 1){
            queryWrapper.eq(ProjectScheme::getRspUser,CurrentUserHelper.getCurrentUserId());
        }
        queryWrapper.and(item ->item.isNotNull(ProjectScheme::getContractMilestoneId).or().ne(ProjectScheme::getContractMilestoneId,""));
        queryWrapper.eq(ProjectScheme::getNodeType,ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_NAME_MAP.get("里程碑"));

        List<ProjectScheme> projectSchemes = this.list(queryWrapper);

        //筛选状态是执行中的合同里程碑数据
        List<ProjectScheme> projectSchemeList = projectSchemes.stream().filter(item -> Objects.equals(Status.EXECUTING.getCode(), item.getStatus())).collect(Collectors.toList());
        List<ProjectScheme> processMilestoneList = projectSchemes.stream().filter(
                item -> !Objects.equals(Status.CIRCUMSTANCE_NEAR.getCode(), item.getCircumstance())
                        && !Objects.equals(Status.CIRCUMSTANCE_OVERD.getCode(), item.getCircumstance()))
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(processMilestoneList)){
            vo.setProgressMilestoneCount(processMilestoneList.size());
            ProjectCenterStatVO.MilestoneData milestoneData = computeProjectScheme(allProjectSchemes, processMilestoneList);
            vo.setProgressMilestone(milestoneData);
        }else{
            vo.setProgressMilestoneCount(0);
            vo.setProgressMilestone(new ProjectCenterStatVO.MilestoneData());
        }
        //临期里程碑
        List<ProjectScheme> projectSchemes1 = projectSchemes.stream().filter(item -> Objects.equals(Status.CIRCUMSTANCE_NEAR.getCode(), item.getCircumstance())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(projectSchemes1)){
            vo.setAdventMilestoneCount(projectSchemes1.size());
            ProjectCenterStatVO.MilestoneData milestoneData = computeProjectScheme(allProjectSchemes, projectSchemes1);
            vo.setAdventMilestoneData(milestoneData);
        }else{
            vo.setAdventMilestoneCount(0);
            vo.setAdventMilestoneData(new ProjectCenterStatVO.MilestoneData());
        }
        //逾期
        List<ProjectScheme> projectSchemes2 = projectSchemes.stream().filter(item -> Objects.equals(Status.CIRCUMSTANCE_OVERD.getCode(), item.getCircumstance())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(projectSchemes2)){
            vo.setOverdueMilestoneCount(projectSchemes2.size());
            ProjectCenterStatVO.MilestoneData milestoneData = computeProjectScheme(allProjectSchemes, projectSchemes2);
            vo.setOverdueMilestoneData(milestoneData);
        }else{
            vo.setOverdueMilestoneCount(0);
            vo.setOverdueMilestoneData(new ProjectCenterStatVO.MilestoneData());
        }
        //金额完成率 个数完成率
        JSONObject completionRate = projectContractMilestoneService.findCompletionRate(year,type);
        vo.setAmountCompletionRate(completionRate.getBigDecimal("moneyCompeteRate"));
        vo.setCountCompletionRate(completionRate.getBigDecimal("countCompeteRate"));
        return vo;
    }

    @Override
    public ProjectCenterStatVO.PlanMilestoneData getProjectStatHead(String year, Integer type) {
        //查询今年合同里程碑转计划的数据
        LambdaQueryWrapper<ProjectScheme> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        if(type == 1){
            queryWrapper.eq(ProjectScheme::getRspUser,CurrentUserHelper.getCurrentUserId());
        }
        queryWrapper.eq(ProjectScheme::getNodeType,ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_NAME_MAP.get("里程碑"));

        List<ProjectScheme> milestoneSchemeList = this.list(queryWrapper);

        ProjectCenterStatVO.PlanMilestoneData planMilestoneData = new ProjectCenterStatVO.PlanMilestoneData();

        //已完成状态数据
        List<ProjectScheme> completeSchemeList = milestoneSchemeList.stream().filter(item -> Objects.equals(Status.FINISHED.getCode(), item.getStatus())).collect(Collectors.toList());
        planMilestoneData.setCompleteMilestoneCount(CollectionUtil.isEmpty(completeSchemeList)?0:completeSchemeList.size());
        //未完成数据
        List<ProjectScheme> noCompleteSchemeList = milestoneSchemeList.stream().filter(item -> !Objects.equals(Status.FINISHED.getCode(), item.getStatus()) && !Objects.equals(Status.PENDING.getCode(), item.getStatus())).collect(Collectors.toList());
        planMilestoneData.setNoCompleteMilestoneCount(CollectionUtil.isEmpty(noCompleteSchemeList)?0:noCompleteSchemeList.size());
        //执行中数据
        List<ProjectScheme> progressMilestoneList = milestoneSchemeList.stream().filter(item -> Objects.equals(Status.EXECUTING.getCode(), item.getStatus())).collect(Collectors.toList());
        planMilestoneData.setProgressMilestoneCount(CollectionUtil.isEmpty(progressMilestoneList)?0:progressMilestoneList.size());
        if(CollectionUtils.isEmpty(progressMilestoneList)){
            //临期
            List<ProjectScheme> adventMilestoneList = milestoneSchemeList.stream().filter(item -> Objects.equals(Status.CIRCUMSTANCE_NEAR.getCode(), item.getCircumstance())).collect(Collectors.toList());
            planMilestoneData.setAdventMilestoneCount(CollectionUtil.isEmpty(adventMilestoneList)?0:adventMilestoneList.size());
            //逾期
            List<ProjectScheme> overdueMilestoneList = milestoneSchemeList.stream().filter(item -> Objects.equals(Status.CIRCUMSTANCE_OVERD.getCode(), item.getCircumstance())).collect(Collectors.toList());
            planMilestoneData.setOverdueMilestoneCount(CollectionUtil.isEmpty(overdueMilestoneList)?0:overdueMilestoneList.size());
        }
        return planMilestoneData;
    }
    @Autowired
    private ProjectContractMilestoneService contractMilestoneService;
    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Override
    public Object test() {
        //查询未生成计划的合同里程碑数据
        List<ContractMilestoneApiVO> contractMilestoneVOS = contractMilestoneService.findnoChangePlanData();

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(contractMilestoneVOS)) {
            //查找对应项目信息
            List<String> projectIds = contractMilestoneVOS.stream().map(ContractMilestoneApiVO::getProjectId).distinct().collect(Collectors.toList());
            Map<String, List<ContractMilestoneApiVO>> contractMilestoneMap = contractMilestoneVOS.stream().collect(Collectors.groupingBy(ContractMilestoneApiVO::getProjectId));

            List<Project> projectList = projectService.list(new LambdaQueryWrapper<Project>().in(Project::getId, projectIds));
            Map<String, List<Project>> projectMap = projectList.stream().collect(Collectors.groupingBy(Project::getId));

            List<ContractMilestoneApiVO> updateContractMilestoneVOS = Lists.newArrayList();
            for (String key : contractMilestoneMap.keySet()) {
                List<ProjectScheme> projectSchemes = Lists.newArrayList();
                List<ProjectScheme> childrenSchemes = Lists.newArrayList();
                List<ContractMilestoneApiVO> contractMilestoneApiVOS = contractMilestoneMap.get(key);
                if (projectMap.get(key) == null) {
                    continue;
                }
                Project project = projectMap.get(key).get(0);

                LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
                lambdaQueryWrapper.select("max(sort) as sort");
                lambdaQueryWrapper.eq(ProjectScheme::getProjectId, project.getId());
                ProjectScheme one = this.getOne(lambdaQueryWrapper);
                Long maxSort = 0L;
                if(Objects.nonNull(one)){
                    maxSort = one.getSort();
                }

                long count = 1;
                for (int i = 0; i < contractMilestoneApiVOS.size(); i++) {
                    ProjectScheme projectScheme = new ProjectScheme();

                    projectScheme.setName(contractMilestoneApiVOS.get(i).getMilestoneName());
                    projectScheme.setProjectId(key);
                    projectScheme.setProjectNumber(project.getNumber());
                    projectScheme.setLevel(1);
                    projectScheme.setParentId("0");
                    projectScheme.setParentChain("0");
                    projectScheme.setNodeType(ProjectNodeTypeEnum.MILESTONE.getCode());
                    projectScheme.setCircumstance(0);
                    projectScheme.setRspUser(contractMilestoneApiVOS.get(i).getTechRspUser());
                    if(org.apache.commons.lang3.StringUtils.isNotEmpty(contractMilestoneApiVOS.get(i).getTechRspUser())){
                        UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), contractMilestoneApiVOS.get(i).getTechRspUser());
                        setOrgInfo(projectScheme, userVO.getOrganizations());
                        projectScheme.setRspUserCode(userVO.getCode());
                    }
                    projectScheme.setBeginTime(contractMilestoneApiVOS.get(i).getCreateTime());
                    projectScheme.setEndTime(contractMilestoneApiVOS.get(i).getPlanAcceptDate());
                    projectScheme.setSchemeDesc(contractMilestoneApiVOS.get(i).getDescription());
                    projectScheme.setTopSort(0);
                    projectScheme.setIsWork(0);
                    projectScheme.setClassName("ProjectScheme");
                    projectScheme.setCreatorId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setOwnerId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setCreateTime(new Date());
                    projectScheme.setModifyId(contractMilestoneApiVOS.get(i).getTechRspUser());
                    projectScheme.setModifyTime(new Date());
                    projectScheme.setRemark(contractMilestoneApiVOS.get(i).getRemark());
                    projectScheme.setPlatformId(contractMilestoneApiVOS.get(i).getPlatformId());
                    projectScheme.setOrgId(contractMilestoneApiVOS.get(i).getOrgId());
                    projectScheme.setStatus(Status.PENDING.getCode());
                    projectScheme.setNumber(projectScheme.getSchemeNumber());
                    projectScheme.setContractMilestoneId(contractMilestoneApiVOS.get(i).getId());
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(contractMilestoneApiVOS.get(i).getParentId())) {
                        projectScheme.setParentId(contractMilestoneApiVOS.get(i).getParentId());
                        childrenSchemes.add(projectScheme);
                    } else {
                        projectScheme.setSort(maxSort + count);
                        count++;
                        projectScheme.setSchemeNumber(project.getNumber() + "-" + projectScheme.getSort());
                        projectSchemes.add(projectScheme);
                    }
                }
                this.saveBatch(projectSchemes);
                if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(childrenSchemes) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(projectSchemes)) {
                    insertChildrenScheme(projectSchemes, childrenSchemes, 2, 1);
                }

                updateContractMilestoneVOS.addAll(contractMilestoneVOS);
            }


            //修改合同里程碑状态
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(updateContractMilestoneVOS)) {
                contractMilestoneService.updateContractMilestoneStatus(updateContractMilestoneVOS);
            }
        }
        return null;
    }

    @Override
    public List<SchemeVO> ganttDep(String projectId) {
        List<SchemeVO> schemeVOS = Lists.newArrayList();
        LambdaQueryWrapperX<ProjectScheme> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(ProjectScheme::getProjectId,projectId);
        queryWrapper.select(ProjectScheme :: getRspSubDept);
        queryWrapper.distinct();
        List<ProjectScheme> projectSchemes = this.list(queryWrapper);
        if(CollectionUtils.isEmpty(projectSchemes)){
            return schemeVOS;
        }

        List<String> rspSubDept = projectSchemes.stream().filter(Objects::nonNull).filter(p -> StringUtils.hasText(p.getRspSubDept())).map(ProjectScheme::getRspSubDept).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(rspSubDept)){
            return schemeVOS;
        }
        List<DeptVO> deptByIds = deptBaseApiService.getDeptByIds(rspSubDept);
        if(CollectionUtils.isEmpty(deptByIds)){
            return schemeVOS;
        }
        Map<String, String> deptNameMap = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));

        for(String key:deptNameMap.keySet()){
            SchemeVO schemeVO = new SchemeVO();
            schemeVO.setRspDept(key);
            schemeVO.setRspDeptName(deptNameMap.get(key));
            schemeVOS.add(schemeVO);
        }
        return schemeVOS;
    }

    @Override
    public List<SchemeVO> ganttUser(String projectId) {
//        List<ProjectScheme> projectSchemes = this.list(new LambdaQueryWrapper<ProjectScheme>()
//                .eq(ProjectScheme::getProjectId,projectId));
        LambdaQueryWrapperX<ProjectScheme> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(ProjectScheme::getProjectId,projectId);
        queryWrapper.select(ProjectScheme :: getRspUser);
        queryWrapper.distinct();
        List<ProjectScheme> projectSchemes = this.list(queryWrapper);
//        List<String> rspUserIds = projectSchemes.stream().map(ProjectScheme::getRspUser).distinct().collect(Collectors.toList());
//        List<UserBaseCacheVO>  userByIds = userRedisHelper.getUserBaseCacheByIds(rspUserIds);
//        Map<String, String> userNameMap = userByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        List<SchemeVO> schemeVOS = Lists.newArrayList();
        for(ProjectScheme projectScheme :projectSchemes){
            SchemeVO schemeVO = new SchemeVO();
            schemeVO.setRspUser(projectScheme.getRspUser());
            schemeVO.setRspUserName(projectScheme.getRspUserName());
            schemeVOS.add(schemeVO);
        }
        return schemeVOS;
    }

    @Override
    public Boolean relationToFixedAsset(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToFixedAssetService.list(new LambdaQueryWrapper<>(PlanToFixedAsset.class)
                .in(PlanToFixedAsset::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToFixedAsset::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToFixedAsset> list = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToFixedAsset planToFixedAsset = new PlanToFixedAsset();
            planToFixedAsset.setClassName("PlanToFixedAsset");
            planToFixedAsset.setFromId(fromId);
            planToFixedAsset.setFromClass("FixedAsset");
            planToFixedAsset.setToId(fromIdsRelationToIdDTO.getToId());
            planToFixedAsset.setToClass("Plan");
            list.add(planToFixedAsset);
        }
        planToFixedAssetService.saveBatch(list);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToFixedAsset(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
       return planToFixedAssetService.remove(new LambdaQueryWrapper<>(PlanToFixedAsset.class)
                .eq(PlanToFixedAsset::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToFixedAsset::getFromId, fromIdsRelationToIdDTO.getFromIds()));
    }

    @Override
    public List<FixedAssetsVO> relationToFixedAssetLists(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToFixedAsset> relationList = planToFixedAssetService.list(new LambdaQueryWrapper<>(PlanToFixedAsset.class)
                .eq(PlanToFixedAsset::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        //通过fromId查询FixedAssets固定资产能力表
        LambdaQueryWrapper<FixedAssets> condition = new LambdaQueryWrapper<>();
        List<String> fixedAssetsIdList = relationList.stream().map(PlanToFixedAsset::getFromId).collect(Collectors.toList());
        condition.in(FixedAssets::getId, fixedAssetsIdList);
        if (Objects.nonNull(planQueryDTO)) {
            if (StrUtil.isNotBlank(planQueryDTO.getKeyword())) {
                condition.and(sub -> sub.like(FixedAssets::getName, planQueryDTO.getKeyword()).or().like(FixedAssets::getNumber, planQueryDTO.getKeyword()));
            }
        }
        List<FixedAssets> fixedAssets = fixedAssetService.list(condition);
        if (CollectionUtils.isEmpty(fixedAssets)) {
            return new ArrayList<>();
        }


        List<FixedAssetsVO> fixedAssetsVOS = BeanCopyUtils.convertListTo(fixedAssets, FixedAssetsVO::new);
        fixedAssetService.setEveryName(fixedAssetsVOS);
        return fixedAssetsVOS;
    }

    @Override
    public Boolean relationToNcFormpurchase(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(planToNcFormpurchaseService.list(new LambdaQueryWrapper<>(PlanToNcFormpurchase.class)
                .in(PlanToNcFormpurchase::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(PlanToNcFormpurchase::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToNcFormpurchase> list = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            PlanToNcFormpurchase planToFixedAsset = new PlanToNcFormpurchase();
            planToFixedAsset.setClassName("PlanToNcFormpurchase");
            planToFixedAsset.setFromId(fromId);
            planToFixedAsset.setFromClass("NcfFormpurchaseRequestDetail");
            planToFixedAsset.setToId(fromIdsRelationToIdDTO.getToId());
            planToFixedAsset.setToClass("Plan");
            list.add(planToFixedAsset);
        }
        planToNcFormpurchaseService.saveBatch(list);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToNcFormpurchase(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        return planToNcFormpurchaseService.remove(new LambdaQueryWrapper<>(PlanToNcFormpurchase.class)
                .eq(PlanToNcFormpurchase::getToId, fromIdsRelationToIdDTO.getToId())
                .in(PlanToNcFormpurchase::getFromId, fromIdsRelationToIdDTO.getFromIds()));
    }

    @Override
    public List<NcfFormpurchaseRequestDetailAPIAsset> relationToNcFormpurchaseLists(String id, PlanQueryDTO planQueryDTO) {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToNcFormpurchase> relationList = planToNcFormpurchaseService.list(new LambdaQueryWrapper<>(PlanToNcFormpurchase.class)
                .eq(PlanToNcFormpurchase::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<String> fromIds = relationList.stream().map(PlanToNcFormpurchase::getFromId).collect(Collectors.toList());
        List<NcfFormpurchaseRequestDetailAPIAsset> ncfFormpurchaseRequestDetailAPIAsset = projectInitiationApiService.getNcfFormpurchaseRequestDetailAPIAsset(fromIds);
        return ncfFormpurchaseRequestDetailAPIAsset;
    }

    @Override
    public List<ProjectScheme> getSimpleListByIds(ArrayList<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.in(ProjectScheme::getId, ids);
        lambdaQueryWrapperX.select(ProjectScheme::getId, ProjectScheme::getStatus, ProjectScheme::getRspUser, ProjectScheme::getPlatformId, ProjectScheme::getOrgId);
        return this.list(lambdaQueryWrapperX);
    }


    static <T> java.util.function.Predicate<T> distinctByKey(java.util.function.Function<? super T, Object> keyExtractor) {
        java.util.Map<Object, Boolean> seen = new java.util.concurrent.ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    void insertChildrenScheme(List<ProjectScheme> projectSchemes,List<ProjectScheme> childrenSchemes,int level,long sort){
        List<ProjectScheme> resultSchemes = Lists.newArrayList();
        Map<String,List<ProjectScheme>> projectSchemeMap = projectSchemes.stream().collect(Collectors.groupingBy(ProjectScheme::getContractMilestoneId));
        List<ProjectScheme> insertChildSchemes = Lists.newArrayList();
        if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(childrenSchemes)){
            for(ProjectScheme child :childrenSchemes){
                if(!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(projectSchemeMap.get(child.getParentId()))){
                    String oldParentId = child.getParentId();
                    child.setParentId(projectSchemeMap.get(oldParentId).get(0).getId());
                    child.setParentChain(projectSchemeMap.get(oldParentId).get(0).getParentChain()+","+child.getParentId());
                    child.setLevel(level);
                    child.setSort(sort++);
                    child.setSchemeNumber(projectSchemeMap.get(oldParentId).get(0).getNumber()+"-"+child.getSort());
                    projectSchemeService.save(child);
                    insertChildSchemes.add(child);
                }else{
                    resultSchemes.add(child);
                }
            }
            if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(resultSchemes) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(insertChildSchemes)){
                insertChildrenScheme(insertChildSchemes,resultSchemes,level+1,1);
            }
        }
    }






    private ProjectCenterStatVO.MilestoneData computeProjectScheme(List<ProjectScheme> allProjectSchemes,List<ProjectScheme> projectSchemeList) {

        List<ProjectScheme> progressProjectSchemeList = Lists.newArrayList();

        for(ProjectScheme projectScheme:projectSchemeList){
            progressProjectSchemeList.add(projectScheme);
            List<ProjectScheme> projectSchemeStream = allProjectSchemes.stream()
                    .filter(item -> item.getParentChain().contains(projectScheme.getId()))
                    .collect(Collectors.toList());
            progressProjectSchemeList.addAll(projectSchemeStream);
        }
        ProjectCenterStatVO.MilestoneData progressMilestoneData = new ProjectCenterStatVO.MilestoneData();
        //查询风险 问题 问题转计划 未完成 临期 逾期
        List<String> ids = progressProjectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        //风险
        Integer riskCount = (int)planToRiskManagementService.count(new LambdaQueryWrapper<PlanToRiskManagement>()
                .in(PlanToRiskManagement::getToId,ids));
        progressMilestoneData.setRiskCount(riskCount);
        //问题
        Integer problemCount = (int)planToQuestionManagementService.count(new LambdaQueryWrapper<PlanToQuestionManagement>()
                .in(PlanToQuestionManagement::getToId,ids));
        progressMilestoneData.setProblemCount(problemCount);
        //转计划
        List<ProjectScheme> collect = projectSchemeList.stream().filter(item -> !StringUtils.hasText(item.getQuestionId())).collect(Collectors.toList());
        progressMilestoneData.setProblemToPlanCount(CollectionUtil.isNotEmpty(collect)?collect.size():0);
        //未完成
        List<ProjectScheme> projectSchemeStream = progressProjectSchemeList.stream()
                .filter(item -> Objects.equals(Status.EXECUTING.getCode(), item.getStatus()))
                .collect(Collectors.toList());
        progressMilestoneData.setUnfinishedCount(CollectionUtil.isNotEmpty(projectSchemeStream)?projectSchemeStream.size():0);
        //临期
        List<ProjectScheme> projectSchemeStream2 = progressProjectSchemeList.stream()
                .filter(item -> Objects.equals(Status.CIRCUMSTANCE_NEAR.getCode(), item.getCircumstance()))
                .collect(Collectors.toList());
        progressMilestoneData.setAdventCount(CollectionUtil.isNotEmpty(projectSchemeStream2)?projectSchemeStream2.size():0);
        //逾期
        List<ProjectScheme> projectSchemeStream3 = progressProjectSchemeList.stream()
                .filter(item -> Objects.equals(Status.OVERDUE.getCode(), item.getCircumstance()))
                .collect(Collectors.toList());
        progressMilestoneData.setOverdueCount(CollectionUtil.isNotEmpty(projectSchemeStream3)?projectSchemeStream3.size():0);

        return progressMilestoneData;
    }

    private void vaildateProjectScheme(List<ProjectSchemeDTO> projectSchemeDTOS) {
        for (ProjectSchemeDTO projectSchemeDTO : projectSchemeDTOS) {
            if (StrUtil.isEmpty(projectSchemeDTO.getName())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "计划名称不能为空");
            }
            if (projectSchemeDTO.getName().length() > 100) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "计划名称过长，建议控制在100字符以内");
            }
            if (StrUtil.isEmpty(projectSchemeDTO.getRspSubDept())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "责任部门不能为空");
            }
            if (StrUtil.isEmpty(projectSchemeDTO.getRspUser())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "责任人不能为空");
            }
            if (projectSchemeDTO.getBeginTime() == null) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "开始时间不能为空");
            }
            if (projectSchemeDTO.getEndTime() == null) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "结束时间不能为空");
            }
            if (StrUtil.isNotBlank(projectSchemeDTO.getSchemeDesc()) && projectSchemeDTO.getSchemeDesc().length() > 200) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "项目计划描述过长，建议控制在200字符以内");
            }
        }
    }
}
