<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_title">
          <div class="btnItem">
            <BasicButton
              v-if=" isPower('XMX_container_button_30', powerData) "
              class="mr10"
              @click="addNode"
            >
              <!-- <PlusCircleOutlined /> -->
              <PlusOutlined />

              <span class="labelSpan">创建问题</span>
            </BasicButton>
            <!--            <BasicButton-->
            <!--              class="mr10"-->
            <!--            >-->
            <!--              <ImportOutlined />-->
            <!--              <span class="labelSpan">导入数据</span>-->
            <!--            </BasicButton>-->
            <!--            <BasicButton-->
            <!--              class="mr10"-->
            <!--            >-->
            <!--              <ExportOutlined />-->
            <!--              <span class="labelSpan">导出数据</span>-->
            <!--            </BasicButton>-->

            <!-- <div class="addModel productLibraryIndex_btn">
              <ImportOutlined />
              <span class="labelSpan">导入数据</span>
            </div>
            <div class="addModel productLibraryIndex_btn">
              <ExportOutlined />
              <span class="labelSpan">导出数据</span>
            </div> -->
            <!-- <div class="productLibraryIndex_btn" @click="multiDelete">
              <DeleteOutlined />
              <span class="labelSpan">批量删除</span>
            </div> -->
          </div>
          <div
            v-if=" isPower('XMX_container_button_37', powerData) "
            class="btnItem searchcenter"
          >
            <a-input-search
              v-model:value="searchvlaue"
              placeholder="请输入名称或编号"
              style="width: 240px; margin-right: 8px"
              allow-clear
              @search="onSearch"
            />
          </div>
        </div>
        <div class="productLibraryIndex_table">
          <BasicTable
            class="pdmBasicTable"
            title=""
            title-help-message=""
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :show-index-column="false"
            :pagination="pagination"
            row-key="id"
            @register="registerTable"
            @change="handleChange"
            @rowClick="clickRow"
          >
            <template #proposedTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #predictEndTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #modifyTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
            <template #seriousLevelName="{ text }">
              <span
                v-if="text"
                :style="{ color: text == '严重' ? 'red' : '' }"
              >
                {{ text }}
              </span>
            </template>
            <template #schedule="{ text }">
              {{ text }}%
            </template>
            <template #seriousLevel="{ text }">
              <span
                v-if="text"
                :style="{ color: text == '最高' ? 'red' : '' }"
              >
                {{ text }}
              </span>
            </template>

            <template #statusName="{ text }">
              <span
                :style="{
                  color: text == '未完成' ? 'lightgray' : text == '处理中' ? 'blue' : 'green'
                }"
              >
                {{ text }}
              </span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <!-- 查看详情弹窗 -->
      <checkDetails :data="nodeData" />
      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <!-- 新建/编辑抽屉 -->
      <!-- <addProjectModal
        :data="addNodeModalData"
        @success="successSave"
        :listData="editdataSource"
        :Projectid="id"
      /> -->
      <ZkAddModal
        :form-item-arr="formItemArr"
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        :other-api="otherApi"
        @success="successSave"
      />

      <!-- 高级搜索抽屉 -->
      <searchModal
        :data="searchData"
        @search="searchTable"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject, h,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress, Button,
} from 'ant-design-vue';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons-vue';
import addProjectModal from './modal/addProjectModal.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import dayjs from 'dayjs';
import { questionPageApi, deleteQuestionApi } from '/@/views/pms/projectLaborer/api/questionManage';
import { formItemArr, otherApi } from './src/formItemArr';
import ZkAddModal from '/@/views/pms/projectLaborer/componentsList/ZkAddModal/index';
// import ZkAddModal from './ZkAddModal/index';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
const [registerTable, { setLoading }] = useTable();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    BasicTable,
    InfoCircleOutlined,
    messageModal,
    checkDetails,
    newButtonModal,
    searchModal,
    ImportOutlined,
    ExportOutlined,
    PlusOutlined,
    ZkAddModal,
    BasicButton: Button,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const state = reactive({
      searchvlaue: '',
      editdataSource: {},
      selectedRowKeys: [],
      dataSource: [],
      powerData: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        pageSize: 10,
        pageNum: 1,
        total: 0,
        queryCondition: [],
      },
      pageSize: 10,
      current: 1,
      total: 20,
      addNodeModalData: {},
      selectedRows: [],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
      // btnObjectData: {
      //   check: { show: true },
      //   open: { show: true },
      //   add: { show: true },
      //   delete: { show: true },
      //   edit: { show: true },
      //   search: { show: true },
      // },
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        check: { show: computed(() => isPower('XMX_container_button_33', state.powerData)) },
        open: { show: computed(() => isPower('XMX_container_button_34', state.powerData)) },
        add: { show: computed(() => isPower('XMX_container_button_30', state.powerData)) },
        delete: { show: computed(() => isPower('XMX_container_button_36', state.powerData)) },
        edit: { show: computed(() => isPower('XMX_container_button_35', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_37', state.powerData)) },
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          key: 'number',
          align: 'left',
          width: '150px',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          key: 'name',
          width: '300px',
          align: 'left',
          // slots: { customRender: 'name' },
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => isPower('XMX_container_button_33', state.powerData)) ? 'action-btn' : '',
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_33', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

          sorter: true,
          ellipsis: true,
        },

        {
          title: '提出人',
          dataIndex: 'exhibitor',
          key: 'exhibitor',
          width: '70px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'exhibitorId' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '提出时间',
          dataIndex: 'proposedTime',
          key: 'proposedTime',
          width: '100px',
          align: 'left',
          slots: { customRender: 'proposedTime' },

          sorter: true,
          ellipsis: true,
        },
        {
          title: '期望完成时间',
          dataIndex: 'predictEndTime',
          key: 'predictEndTime',
          width: '110px',
          align: 'left',
          slots: { customRender: 'predictEndTime' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '严重程度',
          dataIndex: 'seriousLevelName',
          key: 'seriousLevel',
          width: '70px',
          align: 'left',
          slots: { customRender: 'seriousLevelName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          key: 'priorityLevel',
          width: '70px',
          align: 'left',
          slots: { customRender: 'seriousLevel' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '进度',
          dataIndex: 'schedule',
          key: 'schedule',
          width: '70px',
          align: 'left',
          slots: { customRender: 'schedule' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'status',
          width: '70px',
          align: 'left',
          slots: { customRender: 'statusName' },
          sorter: true,
          ellipsis: true,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',
          width: '70px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          key: 'modifyTime',
          width: '150px',
          align: 'left',
          sorter: true,
          ellipsis: true,
          slots: { customRender: 'modifyTime' },
        },
      ],
    });
    /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      // showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // console.log('测试🚀 ~ file: index.vue ~ line 254 ~ pag', pag);
      // console.log('测试🚀 ~ file: index.vue ~ line 254 ~ sorter', sorter);
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      // state.tablehttp.orders[0].column = sorter.column?.key;
      state.tablehttp.orders[0].column = sorter.columnKey;
      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addNode();
          break;
        case 'open':
          openDetail();
          break;
        case 'delete':
          // deleteNode();
          multiDelete();
          break;
        case 'search':
          state.searchData = {};
          break;
      }
    };
    const router = useRouter();

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = {
        //   ...state.dataSource.filter((item) => {
        //     return item.id == state.selectedRowKeys[0];
        //   })
        ...state.selectedRows,
      };
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    const QuestionDetailsLocal = useIndex('QuestionDetailsLocal');

    onMounted(() => {
      QuestionDetailsLocal.value = parseInt(JSON.stringify(0));
      /* 高度变化 */
      // state.tableHeight = document.body.clientHeight - 365;
      state.tableHeight = document.body.clientHeight - 420;
      // console.log('测试🚀🚀 ~~~ state.tableHeight', state.tableHeight);

      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      // new Api('/pms')
      //   .fetch(state.selectedRowKeys, `project/removeBatch/`, 'DELETE')
      const love = {
        className: 'QuestionManagement',
        moduleName: '项目管理-问题管理',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      deleteQuestionApi(state.selectedRowKeys, love)
        .then(() => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      setLoading(true);

      state.tablehttp.query.projectId = props.id;
      state.tablehttp.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.id,
      });
      const love = {
        id: props.id,
        className: 'QuestionManagement',
        moduleName: '项目管理-问题管理', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${props.id}】问题管理列表`,
      };
      const res = await questionPageApi(state.tablehttp, love);
      state.dataSource = res.content;
      state.tablehttp.total = res.totalSize;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      setLoading(false);
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;
      for (const item in params.params) {
        if (params.params[item]) {
          state.tablehttp.queryCondition.push({
            column: item,
            type: 'eq',
            link: 'and',
            value: params.params[item],
          });
        }
      }
      getFormData();
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;
      toDetails(state.selectedRows[0]);
    };
    const toDetails = (data) => {
      router.push({
        name: 'QuestionDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      pagination,
      onSelectChange,
      handleChange,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      registerTable,
      setLoading,
      formItemArr,
      otherApi,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
