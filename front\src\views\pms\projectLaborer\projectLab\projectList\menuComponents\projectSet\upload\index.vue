<template>
  <div class="pdmUpload">
    <BasicUpload
      :accept="'.jpg,.gif,.png,.bmp'"
      :max-size="10"
      :max-number="1"
      :is-classification="true"
      button-text="上传图片"
      @saveChange="saveChange"
    />
  </div>
</template>
<script>
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  BasicUpload,
} from 'lyra-component-vue3';
// import BasicUpload from '/@/components/BasicUpload';

export default defineComponent({
  components: {
    BasicUpload,
  },
  setup(props, { emit }) {
    const saveChange = (data) => {
      let item = data[0];
      let reader = new FileReader();
      reader.readAsDataURL(item.file);
      reader.onload = function () {
        emit('successChange', {
          imgUrl: this.result,
          imageId: item.result.filePath,
        });
      };
    };
    return {
      saveChange,
    };
  },
});
</script>
<style lang="less" scoped>
  .pdmUpload {
    margin-bottom: 10px;
  }
</style>
