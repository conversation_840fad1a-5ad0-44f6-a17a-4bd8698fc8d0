package com.chinasie.orion.management.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.constant.caigouUtil.Sm4Util;
import com.chinasie.orion.management.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.management.domain.dto.SupplierInfoExcelDTO;
import com.chinasie.orion.management.domain.dto.SupplierInfoExcelOtherDTO;
import com.chinasie.orion.management.domain.dto.SupplierInfoExcelParkDTO;
import com.chinasie.orion.management.domain.entity.NcfFormSupplierReview;
import com.chinasie.orion.management.domain.entity.SupplierInfo;
import com.chinasie.orion.management.domain.vo.SupplierContactVO;
import com.chinasie.orion.management.domain.vo.SupplierInfoVO;
import com.chinasie.orion.management.repository.SupplierInfoMapper;
import com.chinasie.orion.management.service.NcfFormSupplierReviewService;
import com.chinasie.orion.management.service.SupplierContactService;
import com.chinasie.orion.management.service.SupplierInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * SupplierInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierInfoServiceImpl extends OrionBaseServiceImpl<SupplierInfoMapper, SupplierInfo> implements SupplierInfoService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private NcfFormSupplierReviewService ncfFormSupplierReviewService;

    @Autowired
    private SupplierContactService supplierContactService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierInfoVO detail(String id, String pageCode) throws Exception {
        SupplierInfo supplierInfo = this.getById(id);
        SupplierInfoVO result = BeanCopyUtils.convertTo(supplierInfo, SupplierInfoVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierInfoDTO
     */
    @Override
    public String create(SupplierInfoDTO supplierInfoDTO) throws Exception {
        SupplierInfo supplierInfo = BeanCopyUtils.convertTo(supplierInfoDTO, SupplierInfo::new);
        this.save(supplierInfo);
        String rsp = supplierInfo.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierInfoDTO
     */
    @Override
    public Boolean edit(SupplierInfoDTO supplierInfoDTO) throws Exception {
        SupplierInfo supplierInfo = BeanCopyUtils.convertTo(supplierInfoDTO, SupplierInfo::new);
        this.updateById(supplierInfo);
        String rsp = supplierInfo.getId();
        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierInfoVO> pages(Page<SupplierInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierInfo::getCreateTime);
        Page<SupplierInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierInfo::new));
        PageResult<SupplierInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Map<String, Object> getNum(Page<SupplierInfoDTO> pageRequest) {
        Map<String, Object> map = new HashMap<>();
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.gt(SupplierInfo::getQualValidity,new Date());
        condition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        condition.eq(SupplierInfo::getSupplierLevel,"公司级");
        condition.eq(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司");
        condition.select(SupplierInfo::getSupplierNumber);
        condition.distinct();
        List<SupplierInfo> list = this.list(condition);
        LambdaQueryWrapperX<SupplierInfo> yingruCondition = new LambdaQueryWrapperX<>(SupplierInfo.class);

        yingruCondition.gt(SupplierInfo::getQualValidity,new Date());
        yingruCondition.eq(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司");
        yingruCondition.eq(SupplierInfo::getSupplierLevel,"公司级");
        yingruCondition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        yingruCondition.innerJoin(NcfFormSupplierReview.class, NcfFormSupplierReview::getContractId, SupplierInfo::getSupplierNumber);
        yingruCondition.eq("YEAR(approval_completion_time)",  DateUtil.year(new Date()));
        yingruCondition.select(SupplierInfo::getSupplierNumber);
        yingruCondition.distinct();
        List<SupplierInfo> yingrulist = this.list(yingruCondition);

        map.put("allNum", list.size());
        map.put("yearNum", yingrulist.size());
        return map;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "供应商管理导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierInfoExcelListener excelReadListener = new SupplierInfoExcelListener();
        EasyExcel.read(inputStream, SupplierInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("供应商管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierInfo> supplierInfoes = BeanCopyUtils.convertListTo(dtoS, SupplierInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierInfo-import::id", importId, supplierInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierInfo> supplierInfoes = (List<SupplierInfo>) orionJ2CacheService.get("ncf::SupplierInfo-import::id", importId);
        log.info("供应商管理导入的入库数据={}", JSONUtil.toJsonStr(supplierInfoes));

        this.saveBatch(supplierInfoes);
        orionJ2CacheService.delete("ncf::SupplierInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierInfo::getCreateTime);
        List<SupplierInfo> supplierInfoes = this.list(condition);
        List<SupplierInfoExcelDTO> vos = BeanCopyUtils.convertListTo(supplierInfoes, SupplierInfoExcelDTO::new);
//        setEveryName(vos);
//        List<SupplierInfoDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoDTO::new);
        //手机号解码
        for (SupplierInfoExcelDTO vo : vos) {
            if (ObjectUtil.isNotEmpty(vo.getLandlinePhone())){
                vo.setLandlinePhone(Sm4Util.decoder(vo.getLandlinePhone()));
            }
        }

        String fileName = "潜在供应商管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierInfoExcelDTO.class,vos );

    }

    @Override
    public void setEveryName(List<SupplierInfoVO> vos) throws Exception {
    if(CollectionUtils.isEmpty(vos)){
            return;
        }
        //查询供应商联系人中默认联系人为是的联系人，若有多条就随便取一条
        List<String> supplierCodes = vos.stream().map(SupplierInfoVO::getSupplierNumber).collect(Collectors.toList());
        Map<String, SupplierContactVO> supplierContactVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(supplierCodes)) {
            List<SupplierContactVO> supplierContactVOS = supplierContactService.getBySupplierCode(supplierCodes);
            supplierContactVOMap = supplierContactVOS.stream().collect(Collectors.toMap(SupplierContactVO::getSupplierCode, Function.identity(), (k1, k2) -> k1));

        }
        for(SupplierInfoVO vo: vos){
            SupplierContactVO supplierContactVO = supplierContactVOMap.get(vo.getSupplierNumber());
            if(supplierContactVO != null){
                //设置默认联系人姓名，手机，电子邮箱
                vo.setContractName((StringUtils.isNotBlank(supplierContactVO.getContactLastname())?supplierContactVO.getContactLastname():"") + (StringUtils.isNotBlank(supplierContactVO.getContactFirstname())?supplierContactVO.getContactFirstname():""));
                vo.setContractTel(supplierContactVO.getMobile());
                vo.setContractEmail(supplierContactVO.getEmail());
            }
            vo.setContractEmail(Sm4Util.decoder(vo.getContractEmail()));
            vo.setContractTel(Sm4Util.decoder(vo.getContractTel()));
            vo.setLandlinePhone(Sm4Util.decoder(vo.getLandlinePhone()));
            vo.setLegalrep(Sm4Util.decoder(vo.getLegalrep()));
        }
        //根据类型进行对应字段解密
//
//            vos.forEach(x -> {
//                x.setContractEmail(Sm4Util.decoder(x.getContractEmail()));
//                x.setContractTel(Sm4Util.decoder(x.getContractTel()));
//                x.setLandlinePhone(Sm4Util.decoder(x.getLandlinePhone()));
//                x.setLegalrep(Sm4Util.decoder(x.getLegalrep()));
//            });

    }


    public static class SupplierInfoExcelListener extends AnalysisEventListener<SupplierInfoDTO> {

        private final List<SupplierInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierInfoDTO> getData() {
            return data;
        }
    }


    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierInfoVO> parkPages(Page<SupplierInfoDTO> pageRequest) throws Exception {
        //苏州院合格供应商菜单展示逻辑：1、供应商级别=公司级；2、在【公司级板块资审结果列表】中，二级公司名称=苏州热工研究院有限公司；3、资审有效期大于当前系统日期；

        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        condition.eq(SupplierInfo::getSupplierLevel,"公司级");
        condition.eq(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司");

        condition.gt(SupplierInfo::getQualValidity,new Date());
        condition.orderByDesc(SupplierInfo::getCreateTime);
        Page<SupplierInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierInfo::new));
        PageResult<SupplierInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void parkExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
//        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
//        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
//            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
//        }
//        condition.orderByDesc(SupplierInfo::getCreateTime);

        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        condition.eq(SupplierInfo::getSupplierLevel,"公司级");
        condition.eq(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司");
        condition.gt(SupplierInfo::getQualValidity,new Date());
        condition.orderByDesc(SupplierInfo::getCreateTime);
        List<SupplierInfo> supplierInfoes = this.list(condition);
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(supplierInfoes, SupplierInfoVO::new);
        setEveryName(vos);
        List<SupplierInfoExcelParkDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoExcelParkDTO::new);
//        setEveryName(vos);
//        List<SupplierInfoDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoDTO::new);
        //手机号解码
//        for (SupplierInfoExcelDTO vo : vos) {
//            if (ObjectUtil.isNotEmpty(vo.getLandlinePhone())){
//                vo.setLandlinePhone(Sm4Util.decoder(vo.getLandlinePhone()));
//            }
//        }
        String fileName = "公司级合格供应商数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierInfoExcelParkDTO.class,dtos );

    }


    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierInfoVO> otherPages(Page<SupplierInfoDTO> pageRequest) throws Exception {
        //条件过滤
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        //condition.and(e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").gt(SupplierInfo::getQualValidity,new Date());
        condition.and(e -> e.and(e2->e2.eq(SupplierInfo::getSupplierLevel,"集团级").gt(SupplierInfo::getQualValidity,new Date())).
               or(e1->e1.eq(SupplierInfo::getSupplierLevel,"公司级").ne(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司").gt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where supplier_level = '集团级' and qual_validity >=  now()"))
                );
//        condition.eq(SupplierInfo::getSupplierLevel,"集团级").gt(SupplierInfo::getQualValidity,new Date()).
//                or().eq(SupplierInfo::getSupplierLevel,"公司级").gt(SupplierInfo::getQualValidity,new Date())
//                .notIn(SupplierInfo::getId,collect);

        Page<SupplierInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierInfo::new));
        PageResult<SupplierInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void otherExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
//        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
//        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
//            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
//        }
//        condition.orderByDesc(SupplierInfo::getCreateTime);

        //条件过滤
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.ne(SupplierInfo::getSupplierCategory,"潜在供应商");
        //condition.and(e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").gt(SupplierInfo::getQualValidity,new Date());
        condition.and(e -> e.and(e2->e2.eq(SupplierInfo::getSupplierLevel,"集团级").gt(SupplierInfo::getQualValidity,new Date())).
                or(e1->e1.eq(SupplierInfo::getSupplierLevel,"公司级").ne(SupplierInfo::getSecondaryCompanyName,"苏州热工研究院有限公司").gt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where supplier_level = '集团级' and qual_validity >=  now()"))
        );
        List<SupplierInfo> supplierInfoes = this.list(condition);
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(supplierInfoes, SupplierInfoVO::new);
        setEveryName(vos);
        List<SupplierInfoExcelOtherDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoExcelOtherDTO::new);
//        setEveryName(vos);
//        List<SupplierInfoDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoDTO::new);
        //手机号解码
//        for (SupplierInfoExcelDTO vo : vos) {
//            if (ObjectUtil.isNotEmpty(vo.getLandlinePhone())){
//                vo.setLandlinePhone(Sm4Util.decoder(vo.getLandlinePhone()));
//            }
//        }
        String fileName = "其他合格供应商数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierInfoExcelOtherDTO.class,dtos );

    }


    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierInfoVO> latentPages(Page<SupplierInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.and(t -> {
            t.and(
                    e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").eq(SupplierInfo::getSupplierCategory,"潜在供应商")
            );
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").eq(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now()"));
            });
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo :: getSupplierLevel,"集团级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now()"));
            });
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo :: getSupplierLevel,"公司级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now() or supplier_level = '集团级'"));
            });

//            t.or(e -> e.eq(SupplierInfo :: getSupplierLevel,"公司级").eq(SupplierInfo::getSupplierCategory,"潜在供应商")
//                    .notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where supplier_category = '潜在供应商' and supplier_level = '集团级'"));
//            t.or(e -> e.eq(SupplierInfo :: getSupplierLevel,"集团级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
//                    .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now()"));
//            t.or(e -> e.eq(SupplierInfo :: getSupplierLevel,"公司级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
//                    .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now() or supplier_level = '集团级'"));
        });

        //condition.lt(SupplierInfo::getQualValidity,new Date());
        condition.orderByDesc(SupplierInfo::getCreateTime);
        Page<SupplierInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierInfo::new));
        PageResult<SupplierInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }




    @Override
    public void latentExportByExcel(Page<SupplierInfoDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierInfo> condition = new LambdaQueryWrapperX<>(SupplierInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.and(t -> {
            t.and(
                    e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").eq(SupplierInfo::getSupplierCategory,"潜在供应商")
            );
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo::getSupplierLevel,"集团级").eq(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now()"));
            });
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo :: getSupplierLevel,"集团级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now()"));
            });
            t.or(item->{
                item.and(e -> e.eq(SupplierInfo :: getSupplierLevel,"公司级").ne(SupplierInfo::getSupplierCategory,"潜在供应商")
                        .lt(SupplierInfo::getQualValidity,new Date()).notInSql("supplier_number","select supplier_number from ncf_form_supplier_info where qual_validity >=  now() or supplier_level = '集团级'"));
            });
        });

        condition.orderByDesc(SupplierInfo::getCreateTime);
        List<SupplierInfo> supplierInfoes = this.list(condition);
        List<SupplierInfoVO> vos = BeanCopyUtils.convertListTo(supplierInfoes, SupplierInfoVO::new);
        setEveryName(vos);
        List<SupplierInfoExcelDTO> dtos = BeanCopyUtils.convertListTo(vos, SupplierInfoExcelDTO::new);

        String fileName = "潜在合格供应商数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierInfoExcelDTO.class,dtos );

    }

}
