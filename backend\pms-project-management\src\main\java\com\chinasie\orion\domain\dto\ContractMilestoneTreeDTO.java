package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * ContractMilestoneTreeDTO DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneTreeDTO对象", description = "合同里程碑树形")
@Data
@ExcelIgnoreUnannotated
public class ContractMilestoneTreeDTO implements Serializable {
    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotEmpty(message = "合同id不能为空")
    private String contractId;

    @ApiModelProperty(value = "自定义查询参数")
    ContractMilestoneTreeDTO query;

    @ApiModelProperty(value = "高级搜索条件")
    List<List<SearchCondition>> searchConditions;
}
