--
CREATE TABLE `pmsx_prod_action_item` (
                                         `id` varchar(64) NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                         `modify_time` datetime NOT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                         `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                         `status` int(11) NOT NULL COMMENT '状态',
                                         `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                         `repair_round` varchar(64) DEFAULT NULL COMMENT '大修轮次',
                                         `rsp_user_ids` varchar(1000) DEFAULT NULL COMMENT '责任人ID拼接',
                                         `rsp_user_names` varchar(200) DEFAULT NULL COMMENT '责任人名称拼接',
                                         `finish_deadline` datetime DEFAULT NULL COMMENT '完成时限',
                                         `dimension_dict` varchar(64) DEFAULT NULL COMMENT '维度字典',
                                         `verifier_id` varchar(64) DEFAULT NULL COMMENT '验证人ID',
                                         `verifier_name` varchar(64) DEFAULT NULL COMMENT '验证人名称',
                                         `problem_desc` text COMMENT '问题描述',
                                         `parent_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '父级ID',
                                         `rsp_dept_ids` varchar(2000) DEFAULT NULL COMMENT '责任部门ID拼接',
                                         `rsp_dept_names` varchar(500) DEFAULT NULL COMMENT '责任部门名称拼接',
                                         PRIMARY KEY (`id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产大修行动项';

-- 富文本表
CREATE TABLE `pmsx_prod_action_rich_text` (
                                              `id` varchar(64) NOT NULL COMMENT '主键',
                                              `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                              `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                              `modify_time` datetime NOT NULL COMMENT '修改时间',
                                              `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                              `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                              `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                              `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                              `status` int(11) NOT NULL COMMENT '状态',
                                              `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                              `action_id` varchar(64) DEFAULT NULL COMMENT '行动项ID',
                                              `data_type` varchar(64) DEFAULT NULL COMMENT '数据类型',
                                              `rich_text` text COMMENT '富文本',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产大修行动项富文本';

