package com.chinasie.orion.constant;

public enum MaterialDownEnum {
    deviceNumber("设备数", " !isnull(t2.input_stock_num) "),
    toolNumber("计量工具数", " t2.is_metering = true"),
    noPlanIn("计划入场时间未报备", " isnull(t2.in_date)"),
    actInCountNo("实际入场时间未报备", " isnull(t2.act_in_date)"),
    planInCount("实际入场物资数", " t2.status = 1"),
    planOutCount("实际离场物资数", " t2.status = 2"),
    planInNotCount("实际离场未报备物资数", " isnull(t2.out_date)");

    private String name;

    private String sqlDesc;

    public String getName() {
        return name;
    }

    public String getSqlDesc() {
        return sqlDesc;
    }

    MaterialDownEnum(String name, String sqlDesc) {
        this.name = name;
        this.sqlDesc = sqlDesc;
    }

}
