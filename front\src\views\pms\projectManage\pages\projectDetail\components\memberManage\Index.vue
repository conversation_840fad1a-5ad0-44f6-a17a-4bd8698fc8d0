<template>
  <Layout2
    left-title="项目角色"
  >
    <template #left>
      <MenuList ref="menuRef" />
    </template>
    <TableList :menuId="menuId" />
  </Layout2>
</template>

<script setup lang="ts">
import { Layout2 } from 'lyra-component-vue3';
import { computed, ref } from 'vue';
import MenuList from './components/MenuList.vue';
import TableList from './components/TableList.vue';

const menuRef = ref();
const menuId = computed(() => (menuRef.value ? menuRef.value.menuId : ''));
</script>

<style scoped lang="less">

</style>
