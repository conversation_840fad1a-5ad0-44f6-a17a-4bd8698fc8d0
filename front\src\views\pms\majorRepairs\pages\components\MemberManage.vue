<script setup lang="ts">
import {
  BasicButton, BasicMenu, Empty, Layout, openSelectUserModal, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, inject, ref, Ref, unref, watch, watchEffect,
} from 'vue';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';

const detailsData: Record<string, any> = inject('detailsData');
const menuData: Ref<Record<string, any>[]> = ref([]);
const businessId: Ref<string> = ref('');
const roleCode = computed(() => menuData.value.find((item) => item.id === businessId.value)?.code);

// 获取角色列表
const menuLoading: Ref<boolean> = ref(false);

async function getRoleMenu() {
  menuLoading.value = true;
  try {
    const result = await new Api('/pms/majorRepairPlanRole/list').fetch({
      majorRepairTurn: detailsData.repairRound,
    }, '', 'POST');
    menuData.value = Array.isArray(result) ? result.map((item) => ({
      id: item.id,
      name: item.roleName,
      code: item.roleCode,
    })) : [];
  } finally {
    menuLoading.value = false;
  }
}

watchEffect(() => {
  if (!businessId.value || (businessId.value && menuData.value.every((item) => item.id !== businessId.value))) {
    businessId.value = menuData.value?.[0]?.id;
  }
});

watchEffect(() => {
  getRoleMenu();
});

function menuChange({ id }) {
  businessId.value = id;
}

const tableRef = ref();
const selectedKeys: Ref<string[]> = ref([]);
const keyword: Ref<string> = ref('');
const tableOptions = {
  rowSelection: {
    onChange(keys: string[]) {
      selectedKeys.value = keys;
    },
  },
  showSmallSearch: computed(() => !!businessId.value),
  immediate: false,
  api: (params: any) => {
    delete params.searchConditions;
    return new Api('/pms/majorRepairPlanMember/page').fetch({
      ...params,
      query: {
        userName: unref(keyword),
        businessId: businessId.value,
        majorRepairTurn: detailsData.repairRound,
      },
    }, '', 'POST');
  },
  showToolButton: false,
  columns: [
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '工号',
      dataIndex: 'userCode',
    },
    {
      title: '所在部门',
      dataIndex: 'deptName',
    },
    {
      title: '电话',
      dataIndex: 'mobile',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      modelTitle: '移除提示！',
      modelContent: '确认移除当前成员？',
      modal: ({ id }) => deleteApi([id]),
    },
  ],
};

watch(() => businessId.value, (value) => {
  if (value && tableRef.value) {
    tableRef.value.reload();
  }
});

function updateTable() {
  tableRef.value.reload();
}

const toolButton = computed(() => [
  {
    text: '新增',
    icon: 'sie-icon-tianjiaxinzeng',
    key: 'add',
    type: 'primary',
    disabled: !businessId.value,
  },
  {
    text: '移除',
    icon: 'delete',
    key: 'delete',
    disabled: selectedKeys.value.length === 0,
  },
]);

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openSelectUserModal([], {
        okHandle(user) {
          return new Promise((resolve) => {
            new Api('/pms/majorRepairPlanMember/batch').fetch({
              businessId: businessId.value,
              majorRepairTurn: detailsData.repairRound,
              roleCode: roleCode.value,
              userIdList: user.map((item) => item.id),
            }, '', 'POST').then(() => {
              message.success('操作成功');
              updateTable();
              resolve('');
            }).catch(() => {
              resolve('');
            });
          });
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除已选择的成员？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/majorRepairPlanMember/remove').fetch(ids, '', 'DELETE').then(() => {
      message.success('操作成功');
      updateTable();
      resolve(true);
    }).catch(() => {
      resolve(true);
    });
  });
}
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <template #left>
      <BasicMenu
        v-loading="menuLoading"
        :menuData="menuData"
        :defaultActionId="businessId"
        @menuChange="menuChange"
      >
        <template #header>
          <div class="menu-title">
            大修角色
          </div>
        </template>
        <div
          v-if="menuData.length===0"
          class="h-full w-full flex flex-pac"
        >
          <Empty />
        </div>
      </BasicMenu>
    </template>
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButton"
          :key="item.key"
          v-bind="item"
          @click="handleToolButton(item.key)"
        >
          {{ item.text }}
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.menu-title {
  font-size: 16px;
  line-height: 50px;
  padding-left: 20px;
}
</style>
