import dayjs from 'dayjs';
import { computed, h } from 'vue';
import { DatePicker, InputSearch as AInputSearch } from 'ant-design-vue';
import Api from '/@/api';
import {
  SelectUser,
  InputSelectUser, openTreeSelectModal,
} from 'lyra-component-vue3';

export const deliverIdsTableColumns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    minWidth: 200,
  },
  {
    title: '交付物名称',
    dataIndex: 'name',
    align: 'left',
    minWidth: 200,
  },
  {
    title: '计划交付物时间',
    dataIndex: 'predictDeliverTime',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    width: 100,
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    width: 150,
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    width: 100,
  },
];

export const ecrProjectBudgetListTableColumns = [
  {
    title: '预算申请编码',
    dataIndex: 'number',
  },
  {
    title: '预算名称',
    dataIndex: 'name',
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterId',
  },
  {
    title: '科目名称',
    dataIndex: 'expenseSubjectName',
  },
  {
    title: '科目编码',
    dataIndex: 'expenseSubjectNumber',
  },
  {
    title: '期间类型',
    dataIndex: 'timeTypeName',
    align: 'center',
  },
  {
    title: '预算期间',
    dataIndex: 'budgetTime',
  },
  {
    title: '预算对象类型',
    dataIndex: 'budgetObjectTypeName',
  },
  {
    title: '预算对象',
    dataIndex: 'budgetObjectName',
  },
  {
    title: '币种',
    dataIndex: 'currencyName',
  },
  {
    title: '预算申请总额（元）',
    dataIndex: 'budgetMoney',
  },
  {
    title: '1月',
    dataIndex: 'januaryMoney',
    align: 'center',
  },
  {
    title: '2月',
    dataIndex: 'februaryMoney',
    align: 'center',
  },
  {
    title: '3月',
    dataIndex: 'marchMoney',
    align: 'center',
  },
  {
    title: '第一季度',
    dataIndex: 'firstQuarterMoney',
    align: 'center',
  },
  {
    title: '4月',
    dataIndex: 'aprilMoney',
    align: 'center',
  },
  {
    title: '5月',
    dataIndex: 'mayMoney',
    align: 'center',
  },
  {
    title: '6月',
    dataIndex: 'juneMoney',
    align: 'center',
  },
  {
    title: '第二季度',
    dataIndex: 'secondQuarter',
    align: 'center',
  },
  {
    title: '7月',
    dataIndex: 'julyMoney',
    align: 'center',
  },
  {
    title: '8月',
    dataIndex: 'augustMoney',
    align: 'center',
  },
  {
    title: '9月',
    dataIndex: 'septemberMoney',
    align: 'center',
  },
  {
    title: '第三季度',
    dataIndex: 'thirdQuarter',
    align: 'center',
  },
  {
    title: '10月',
    dataIndex: 'octoberMoney',
    align: 'center',
  },
  {
    title: '11月',
    dataIndex: 'novemberMoney',
    align: 'center',
  },
  {
    title: '12月',
    dataIndex: 'decemberMoney',
    align: 'center',
  },
  {
    title: '第四季度',
    dataIndex: 'fourthQuarter',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 220,
    slots: { customRender: 'action' },
  },
];

export const schemeDTOSChangeTableColumns = [
  {
    title: '名称',
    dataIndex: 'name',
  },
  {
    title: '原计划开始时间',
    dataIndex: 'beginTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '原计划结束时间',
    dataIndex: 'endTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '新计划开始时间',
    dataIndex: 'newBeginTime',
    customRender({ record, index, column }) {
      return h(DatePicker, {
        value: computed(() => (record.newBeginTime ? dayjs(record.newBeginTime) : null)),
        disabledDate: (date) => (
          record.newEndTime && dayjs(date).isAfter(dayjs(record.newEndTime))
        ),
        onChange(val) {
          if (val) {
            record[column.dataIndex] = dayjs(val).format('YYYY-MM-DD');
          }
        },
        allowClear: false,
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '新计划结束时间',
    dataIndex: 'newEndTime',
    customRender({ record, index, column }) {
      return h(DatePicker, {
        value: computed(() => (record.newEndTime ? dayjs(record.newEndTime) : null)),
        disabledDate: (date) => (
          record.newBeginTime && dayjs(date).isBefore(dayjs(record.newBeginTime))
        ),
        onChange(val) {
          if (val) {
            record[column.dataIndex] = dayjs(val).format('YYYY-MM-DD');
          }
        },
        placeholder: '请选择',
        allowClear: false,
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '原责任人',
    dataIndex: 'rspUserName',
  },
  {
    title: '新责任人',
    dataIndex: 'newRspUserName',
    customRender({ record, index, column }) {
      return h(AInputSearch, {
        value: (record.newRspUserName || record.rspUserName),
        onChange(users) {

        },
        onClick() {
          openTreeSelectModal({
            title: '项目角色',
            width: '80%',
            selectType: 'radio',
            selectedData: [
              {
                id: (record.newRspUser || record.rspUser),
                name: (record.newRspUserName || record.rspUserName),
              },
            ],
            treeApi() {
              return new Api(`/pms/project-role/getList/${record.projectId}`).fetch('', '', 'GET');
            },
            columns: roleTableColumns,
            tableApi(option) {
              const params = {
                ...option,
                query: {
                  projectId: record.projectId,
                },
              };
              delete params.node;
              delete params.tableMethods;
              delete params.orders;
              return new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');
            },
            async onOk({ tableData }) {
              // 注意这里的字段不一样
              record.newRspUserName = tableData[0].name;
              record.newRspUser = tableData[0].userId;
            },
          });
        },

      });
    },
  },
  {
    title: '变更说明',
    dataIndex: 'explain',
    customRender({ record }) {
      return calculateRemark(record);
    },
  },
];

export const schemeDTOSTableColumns = [
  {
    title: '名称',
    dataIndex: 'name',
  },
  {
    title: '原计划开始时间',
    dataIndex: 'beginTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '原计划结束时间',
    dataIndex: 'endTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '原责任人',
    dataIndex: 'rspUserName',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 220,
    slots: { customRender: 'action' },
  },
];
export const roleTableColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    minWidth: 80,
  },
  {
    title: '工号',
    dataIndex: 'number',
    key: 'number',
    width: '110px',
    align: 'left',
    slots: { customRender: 'number' },
  },
  {
    title: '所在部门',
    dataIndex: 'orgName',
    width: '220px',
  },

  {
    title: '电话',
    dataIndex: 'mobile',
    width: '110px',
    slots: { customRender: 'mobile' },
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: '170px',
    slots: { customRender: 'email' },
  },
];

export const milestoneDTOSChangeTableColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    align: 'left',
    minWidth: 300,
    customRender({ record, text }) {
      let type = checkRecordType(record);
      if (type === 'update') {
        return (
          <span>
        <span class="edit-btn">【修改】</span>
            {text}
      </span>
        );
      }
      if (record.type === 'add') {
        return (
          <span>
        <span class="green">【新增】</span>
            {text}
      </span>
        );
      }
      if (record.type === 'deleted') {
        return (
          <span>
        <span class="red">【删除】</span>
            {text}
      </span>
        );
      }

      return (
          <span>
        <span class="edit-btn"></span>
            {text}
      </span>
      );
    },
  },

  {
    title: '类型',
    align: 'left',
    dataIndex: 'nodeType',
    width: 120,
    customRender({ text }) {
      return text === 'milestone' ? '里程碑节点' : '计划';
    },
  },

  {
    title: '原计划开始日期',
    align: 'left',
    dataIndex: 'beginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '原计划结束日期',
    align: 'left',
    dataIndex: 'endTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '新计划开始时间',
    dataIndex: 'newBeginTime',
    isShow(record) {
      return record.type !== 'deleted';
    },

    customRender({ record, index, column }) {
      if (record.type === 'deleted') {
        return '--';
      }

      return h(DatePicker, {
        value: computed(() => (record.newBeginTime ? dayjs(record.newBeginTime) : null)),
        disabledDate: (date) => {
          if (!record.newEndTime) {
            return false;
          }
          return dayjs(date).isAfter(dayjs(record.newEndTime));
        },
        onChange(val) {
          if (val) {
            record[column.dataIndex] = dayjs(val).format('YYYY-MM-DD');
          }
        },
        allowClear: false,
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '新计划结束时间',
    dataIndex: 'newEndTime',

    customRender({ record, index, column }) {
      if (record.type === 'deleted') {
        return '--';
      }
      return h(DatePicker, {
        value: computed(() => (record.newEndTime ? dayjs(record.newEndTime) : null)),
        disabledDate: (date) => {
          if (!record.newBeginTime) {
            return false;
          }
          return dayjs(date).isBefore(dayjs(record.newBeginTime));
        },
        // (
        //   record.newBeginTime && dayjs(date).isBefore(dayjs(record.newBeginTime))
        // ),
        onChange(val) {
          if (val) {
            record[column.dataIndex] = dayjs(val).format('YYYY-MM-DD');
          }
        },
        placeholder: '请选择',
        allowClear: false,
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '原责任人',
    dataIndex: 'rspUserName',
  },
  {
    title: '新责任人',
    dataIndex: 'newRspUserName',

    customRender({ record, index, column }) {
      if (record.type === 'deleted') {
        return '--';
      }
      return h(AInputSearch, {
        value: (record.newRspUserName || record.rspUserName),
        onChange(users) {

        },
        onClick() {
          openTreeSelectModal({
            title: '项目角色',
            width: '80%',
            selectType: 'radio',
            selectedData: [
              {
                id: (record.newRspUser || record.rspUser),
                name: (record.newRspUserName || record.rspUserName),
              },
            ],
            treeApi() {
              return new Api(`/pms/project-role/getList/${record.projectId}`).fetch('', '', 'GET');
            },
            columns: roleTableColumns,
            async tableApi(option) {
              const params = {
                ...option,
                query: {
                  projectId: record.projectId,
                },
              };
              delete params.node;
              delete params.tableMethods;
              delete params.orders;
              // return new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');

              const res = await new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');
              res.content = res.content.map((item) => ({
                ...item,
                id: item.userId,
              }));

              return res;
            },
            async onOk({ tableData }) {
              // 注意这里的字段不一样
              record.newRspUserName = tableData[0].name;
              record.newRspUser = tableData[0].userId;
            },
          });
        },

      });
    },

  },
  {
    title: '变更说明',
    align: 'left',
    dataIndex: 'explain',
    customRender({ record }) {
      return calculateRemark(record);
    },
  },
];

export const milestoneDTOSTableColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    align: 'left',
    minWidth: 300,
    slots: { customRender: 'name' },
  },

  {
    title: '计划类型',
    align: 'left',
    dataIndex: 'nodeTypeName',
    width: 120,
    customRender({ text }) {
      return text === 'milestone' ? '里程碑节点' : '计划';
    },
  },

  {
    title: '计划开始日期',
    align: 'left',
    dataIndex: 'beginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '计划结束日期',
    align: 'left',
    dataIndex: 'endTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '责任处室',
    align: 'left',
    dataIndex: 'rspSubDeptName',
    width: 120,
  },
  {
    title: '责任人',
    align: 'left',
    dataIndex: 'rspUserName',
    width: 120,
  },

];
export const milestoneDTOSAddTableColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    align: 'left',
    minWidth: 300,
    slots: { customRender: 'name' },
  },

  {
    title: '新计划开始时间',
    dataIndex: 'newBeginTime',
    customRender({ record, index, column }) {
      return h(DatePicker, {
        value: computed((date) => date),
        disabledDate: (date) => (
          dayjs(date).valueOf() > dayjs(record.newEndTime).valueOf()
        ),
        onChange(val) {
          record[column.dataIndex] = dayjs(val.valueOf()).format('YYYY-MM-DD');
        },
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '新计划结束时间',
    dataIndex: 'newEndTime',
    customRender({ record, index, column }) {
      return h(DatePicker, {
        value: computed((date) => date),
        disabledDate: (date) => (
          // dayjs(date).valueOf() > dayjs(record.NewBeginTime).valueOf()

          dayjs(date).valueOf() < dayjs(record.newBeginTime).valueOf()
        ),
        onChange(val) {
          record[column.dataIndex] = dayjs(val.valueOf()).format('YYYY-MM-DD');
        },
        placeholder: '请选择',
        style: {
          width: '100%',
        },
      });
    },
  },
  {
    title: '新责任人',
    align: 'left',
    dataIndex: 'rspUserName',
    width: 120,
    customRender({ record, index, column }) {
      return h(InputSelectUser, {
        // selectUserData: computed((value) => state.selectPrincipalUser),
        onChange(users) {
          // state.selectPrincipalUser = users;
        },
        selectUserModalProps: {
          selectType: 'radio',
          treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
            {
              orders: [
                {
                  asc: false,
                  column: '',
                },
              ],
              pageNum: 0,
              pageSize: 0,
              query: { status: 1 },
            },
            '',
            'POST',
          ),
        },
      });
    },
  },
];
export const simpleProjectTableColumns = [
  {
    title: '项目编号',
    dataIndex: 'number',
  },
  {
    title: '项目名称',
    dataIndex: 'name',
  },
  {
    title: '项目状态',
    dataIndex: 'status',
    align: 'left',
    width: 150,
    slots: { customRender: 'status' },
  },
  {
    title: '项目级别',
    dataIndex: 'levelName',
  },
  {
    title: '项目类型',
    dataIndex: 'projectTypeName',
  },
  {
    title: '项目经理',
    dataIndex: 'pm',
  },
  {
    title: '开始日期',
    dataIndex: 'projectStartTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '结束日期',
    dataIndex: 'projectEndTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text)
        .format('YYYY-MM-DD') : '';
    },
  },
];

export const projectFileTableColumns = [
  {
    title: '名称',
    dataIndex: 'fullName',
    minWidth: 380,
    slots: { customRender: 'name' },
  },
  {
    title: '文档来源',
    dataIndex: 'sourceName',
    minWidth: 380,
  },
  {
    title: '版本',
    dataIndex: 'revId',
    width: '80px',
    align: 'left',
    slots: { customRender: 'revId' },
  },
  {
    title: '状态',
    dataIndex: 'checkIn',
    width: '70px',
    slots: { customRender: 'checkIn' },
  },
  {
    title: '所有者',
    dataIndex: 'ownerName',
    width: '120px',
    slots: { customRender: 'ownerName' },
  },
  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    width: '180px',
    slots: { customRender: 'modifyTime' },
  },
];

function calculateRemark(record) {
  let explain = '';
  const beginTime = record.beginTime ? dayjs(record.beginTime) : null;
  const newBeginTime = record.newBeginTime ? dayjs(record.newBeginTime) : null;
  const endTime = record.endTime ? dayjs(record.endTime) : null;
  const newEndTime = record.newEndTime ? dayjs(record.newEndTime) : null;

  if (record.type === 'delete') {
    record.explain = '删除里程碑节点';
    return;
  }
  if (record.type === 'add') {
    record.explain = '新增里程碑节点';
    return;
  }
  // 计算开始时间变化
  if (newBeginTime && beginTime && !newBeginTime.isSame(beginTime)) {
    const daysDifference = newBeginTime.diff(beginTime, 'day');
    if (daysDifference > 0) {
      explain += `开始时间延后${daysDifference}天; `;
    } else {
      explain += `开始时间提前${Math.abs(daysDifference)}天; `;
    }
  }

  // 计算结束时间变化
  if (newEndTime && endTime && !newEndTime.isSame(endTime)) {
    const daysDifference = newEndTime.diff(endTime, 'day');
    if (daysDifference > 0) {
      explain += `结束时间延后${daysDifference}天; `;
    } else {
      explain += `结束时间提前${Math.abs(daysDifference)}天; `;
    }
  }

  // 计算工期变化
  if (newBeginTime && newEndTime && beginTime && endTime) {
    const originalDuration = endTime.diff(beginTime, 'day');
    const newDuration = newEndTime.diff(newBeginTime, 'day');
    const durationDifference = newDuration - originalDuration;

    if (durationDifference > 0) {
      explain += `工期延长${durationDifference}天`;
    } else if (durationDifference < 0) {
      explain += `工期缩短${Math.abs(durationDifference)}天`;
    }
  }

  // 计算责任人变化
  if (record.rspUserName && record.newRspUserName && record.rspUserName !== record.newRspUserName) {
    explain += `责任人由${record.rspUserName}变更为${record.newRspUserName}; `;
  }

  record.explain = explain;
}

function dateToNumber(dateString) {
  // 如果日期字符串为空，返回 -1，表示无效日期
  if (!dateString) {
    return -1;
  }
  // 使用 dayjs 将日期字符串解析为日期对象，然后返回其毫秒表示
  return dayjs(dateString).valueOf();
}

function checkRecordType(record) {
  // 获取旧的开始日期和结束日期的数字表示
  const oldBeginTime = dateToNumber(record.beginTime);
  const oldEndTime = dateToNumber(record.endTime);
  // 获取新的开始日期和结束日期的数字表示
  const newBeginTime = dateToNumber(record.newBeginTime);
  const newEndTime = dateToNumber(record.newEndTime);

  // 检查新旧开始日期或者新旧结束日期是否有变化
  const beginTimeChanged = oldBeginTime !== newBeginTime;
  const endTimeChanged = oldEndTime !== newEndTime;

  // 如果新旧开始日期或者新旧结束日期有任何一个发生了变化，则将记录的类型设置为 "edit"
  if (beginTimeChanged || endTimeChanged || (record.newRspUserName !== record.rspUserName)) {
    record.type = 'update';
    return 'update';
  }
  return '';
}