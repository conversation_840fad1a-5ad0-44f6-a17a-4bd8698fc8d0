package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PerformanceTemplate DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@ApiModel(value = "PerformanceTemplateDTO对象", description = "项目绩效模版")
@Data
public class PerformanceTemplateDTO extends ObjectDTO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    @ApiModelProperty(value = "code")
    private String code;

}
