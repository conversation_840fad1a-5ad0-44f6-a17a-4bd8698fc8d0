<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.projectreactor.addons</groupId>
  <artifactId>reactor-extra</artifactId>
  <version>3.4.10</version>
  <name>Reactor Extra</name>
  <description>Reactor Extra utilities, helpers and custom operators</description>
  <url>https://github.com/reactor/reactor-addons</url>
  <organization>
    <name>reactor</name>
    <url>https://github.com/reactor</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>simonbasle</id>
      <name>Simon Baslé</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/reactor/reactor-addons</connection>
    <developerConnection>scm:git:git://github.com/reactor/reactor-addons</developerConnection>
    <url>https://github.com/reactor/reactor-addons</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/reactor/reactor-addons/issues</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-core</artifactId>
      <version>3.4.28</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
