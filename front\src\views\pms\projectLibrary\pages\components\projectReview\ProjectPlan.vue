<script setup lang="ts">
import { ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { postProjectSchemeList } from '/@/views/pms/projectLaborer/projectLab/api';
import {
  getDefaultExpandedRowKeys,
} from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/utill';
import { message } from 'ant-design-vue';
const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'check',
  },
  columns: {
    type: Array,
    default: () => [
      {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        fixed: 'left',
        slots: { customRender: 'index' },
      },
      {
        title: '计划名称',
        dataIndex: 'name',
        fixed: 'left',
        minWidth: 250,
      },
      {
        title: '计划类型',
        dataIndex: 'nodeType',
        customRender: ({ text }) => (text === 'milestone' ? '里程碑节点' : '计划'),
      },
      {
        title: '责任部门',
        dataIndex: 'rspSubDeptName',
      },
      {
        title: '责任科室',
        dataIndex: 'resDeptName',
      },
      {
        title: '责任人',
        dataIndex: 'rspUserName',
      },
    ],
  },
});
const tableRef = ref(null);
const defaultExpandedRowKeys = ref<string[]>([]);

const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    type: props.type,
    defaultExpandedRowKeys: defaultExpandedRowKeys.value,
  },
  expandIconColumnIndex: 3,
  showIndexColumn: false,
  showSmallSearch: true,
  pagination: false,
  isFilter2: false,
  api: (params) => {
    params.projectId = props.projectId;
    params.typeEnum = 'PROJECT_SCHEME';
    return postProjectSchemeList(params);
  },
  columns: props.columns,
});

const initData = (data) => {
  defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
};

// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const keys = tableRef.value?.getSelectRows();
  if (keys.length > 0) {
    resolve(keys);
  } else {
    message.warning('请选择数据');
    reject();
  }
});
function convertToFormat(recordIndexes) {
  if (recordIndexes.length === 0) {
    return '--';
  }

  const incrementedIndexes = recordIndexes.map((item, index) => {
    if (index === 0) {
      return parseInt(item) + 1;
    }
    return parseInt(item) + 1;
  });
  const formattedIndexes = incrementedIndexes.join('.');
  return formattedIndexes;
}
defineExpose({
  isSelectedAndGetData,
});
</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      class="plan"
      :options="tableOptions"
      @initData="initData"
    >
      <template #index="{recordIndexs}">
        {{ convertToFormat(recordIndexs) }}
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
