<template>
  <BasicForm @register="register" />
</template>

<script setup lang="ts">
import {
  BasicForm, BasicTitle1, InputSelectUser, useForm,
} from 'lyra-component-vue3';
import {
  computed, defineExpose, h, reactive,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { getDict } from '../../../../api';

const state = reactive({
  // 已选合同责任人
  selectPrincipalUser: [],
  // 合同责任部门选项
  principalDeptOptions: [],
});

const [
  register,
  {
    setFieldsValue, getFieldsValue, validateFields, updateSchema, validate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'title1',
      component: 'Input',
      colProps: {
        span: 24,
      },
      renderColContent() {
        return h(BasicTitle1, {
          title: '合同基本信息',
          style: {
            marginBottom: '20px',
          },
        });
      },
    },
    {
      field: 'name',
      component: 'Input',
      colProps: {
        span: 24,
      },
      label: '合同名称',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        maxlength: 100,
        showCount: true,
        placeholder: '请输入合同名称',
      },
    },
    {
      field: 'contractCategory',
      component: 'ApiSelect',
      colProps: {
        span: 12,
      },
      label: '合同类别',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        api: () => getDict('dict1716408088866783232'),
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'contractType',
      component: 'ApiSelect',
      colProps: {
        span: 12,
      },
      label: '合同类型',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        api: () => getDict('dict1716409722757906432'),
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'principalId',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '合同负责人',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      render({ model, field }) {
        return h(InputSelectUser, {
          selectUserData: computed(() => state.selectPrincipalUser),
          onChange(users) {
            state.selectPrincipalUser = users;
            const userId = users?.[0]?.id;
            model[field] = userId ?? '';
            setPrincipalDept(userId);
            validateFields(['principalId']);
          },
          selectUserModalProps: {
            selectType: 'radio',
            treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
              {
                orders: [
                  {
                    asc: false,
                    column: '',
                  },
                ],
                pageNum: 0,
                pageSize: 0,
                query: { status: 1 },
              },
              '',
              'POST',
            ),
          },
        });
      },
    },
    {
      field: 'rspDeptId',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '合同责任部门',
      helpMessage: '根据合同负责人自动获取',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        disabled: true,
        options: computed(() => state.principalDeptOptions),
      },
    },
    {
      field: 'contractMoney',
      component: 'InputNumber',
      colProps: {
        span: 12,
      },
      label: '合同金额',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'number',
        },
      ],
      componentProps: {
        addonAfter: '元',
        min: 1,
        precision: 2,
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'currency',
      component: 'Input',
      colProps: {
        span: 12,
      },
      label: '币种',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        placeholder: '请输入合同交易币种，例如：RMB、USD',
        maxlength: 50,
      },
    },
    {
      field: 'startDate',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '合同开始日期',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        onChange() {
          validateFields(['endDate']);
        },
      },
    },
    {
      field: 'endDate',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '合同结束日期',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
          validator: validateEndTime,
        },
      ],
      componentProps: {
      },
    },
    {
      field: 'title2',
      component: 'Input',
      colProps: {
        span: 24,
      },
      renderColContent() {
        return h(BasicTitle1, {
          title: '合同质保信息',
          style: {
            marginBottom: '20px',
          },
        });
      },
    },
    {
      field: 'isGuaranteePeriod',
      component: 'Switch',
      colProps: {
        span: 24,
      },
      label: '是否具有质保期',
      defaultValue: false,
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'boolean',
        },
      ],
      componentProps: {
        onChange(status) {
          if (status) {
            updateSchema({
              field: 'isGuaranteePeriod',
              colProps: {
                span: 12,
              },
            });
          } else {
            updateSchema({
              field: 'isGuaranteePeriod',
              colProps: {
                span: 24,
              },
            });
          }
        },
      },
    },
    {
      field: 'guaranteeEndDate',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '预计质保期到期日期',
      ifShow({ model }) {
        return model.isGuaranteePeriod;
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {},
    },
    {
      field: 'isGuaranteeMoney',
      component: 'Switch',
      colProps: {
        span: 24,
      },
      label: '是否具有质保金',
      defaultValue: false,
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'boolean',
        },
      ],
      componentProps: {
        onChange(status) {
          if (status) {
            updateSchema({
              field: 'isGuaranteeMoney',
              colProps: {
                span: 12,
              },
            });
          } else {
            updateSchema({
              field: 'isGuaranteeMoney',
              colProps: {
                span: 24,
              },
            });
          }
        },
      },
    },
    {
      field: 'guaranteeAmt',
      component: 'InputNumber',
      colProps: {
        span: 12,
      },
      label: '质保金额',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'number',
        },
      ],
      ifShow({ model }) {
        return model.isGuaranteeMoney;
      },
      componentProps: {
        addonAfter: '元',
        min: 1,
        precision: 2,
        style: {
          width: '100%',
        },
      },
    },
  ],
});/**
 * 设置责任部门
 * @param principalId 合同负责人ID
 */
async function setPrincipalDept(principalId?: string) {
  if (!principalId) {
    setFieldsValue({
      rspDeptId: '',
    });
    return;
  }
  const userInfo = await new Api(`/pmi/user/get-user-info/${principalId}`).fetch('', '', 'GET');
  state.principalDeptOptions = (userInfo?.organizations ?? []).map((item) => ({
    value: item.id,
    label: item.name,
    key: item.id,
  }));
  setFieldsValue({
    rspDeptId: state.principalDeptOptions?.[0]?.value ?? '',
  });
}

/**
 * 验证结束日期
 * @param rule
 * @param value
 */
function validateEndTime(rule, value) {
  if (!value) {
    return Promise.reject('请选择结束日期');
  }

  const params = getFieldsValue();

  if (!params.startDate) {
    validateFields(['startDate']);
    return Promise.reject('请先选择合同开始日期');
  }

  if (params.startDate) {
    if (dayjs(params.startDate).valueOf() > dayjs(value).valueOf()) {
      return Promise.reject('结束日期必须大于开始日期');
    }
  }

  return Promise.resolve();
}

function getAllMoney() {
  return getFieldsValue()?.contractMoney ?? 0;
}

async function getValues() {
  const values = await validate();
  return values;
}

function setValues(values: any) {
  // 合同责任人回显
  state.selectPrincipalUser = [
    {
      id: values.principalId,
      name: values.principalName,
    },
  ];
  state.principalDeptOptions = [
    {
      value: values.rspDeptId,
      label: values.rspDeptName,
    },
  ];
  setFieldsValue(values);
}

defineExpose({
  getValues,
  getAllMoney,
  setValues,
});
</script>

<style scoped lang="less">

</style>
