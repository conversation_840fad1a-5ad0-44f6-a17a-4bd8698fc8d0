package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ConnectedMilestones DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@ApiModel(value = "ConnectedMilestonesDTO对象", description = "挂接里程碑")
@Data
@ExcelIgnoreUnannotated
public class ConnectedMilestonesExportDTO extends ObjectDTO implements Serializable {

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @ExcelProperty(value = "专业中心 ", index = 0)
    private String expertiseCenterName;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @ExcelProperty(value = "专业所 ", index = 1)
    private String expertiseStationName;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 2)
    private String number;

    /**
     * 合同编码(备注)
     */
    @ApiModelProperty(value = "合同编码(备注)")
    @ExcelProperty(value = "合同编码(备注) ", index = 3)
    private String contractNumRemark;

    /**
     * 收入计划月份
     */
    @ApiModelProperty(value = "收入计划月份")
    @ExcelProperty(value = "收入计划月份 ", index = 4)
    private Date estimateInvoiceDate;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型 ", index = 5)
    private String incomeConfirmTypeName;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 6)
    private String certificateSerialNumber;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    @ExcelProperty(value = "过账日期 ", index = 7)
    private Date postingDate;

    /**
     * 确认收入金额
     */
    @ApiModelProperty(value = "确认收入金额")
    @ExcelProperty(value = "确认收入金额 ", index = 8)
    private BigDecimal confirmRevenueAmount;

    /**
     * 冲销暂估金额
     */
    @ApiModelProperty(value = "冲销暂估金额")
    @ExcelProperty(value = "冲销暂估金额 ", index = 9)
    private BigDecimal reverseAmount;



//    /**
//     * 里程碑名称(备注)
//     */
//    @ApiModelProperty(value = "里程碑名称(备注)")
//    @ExcelProperty(value = "里程碑名称(备注) ", index = 10)
//    private String mileStoneRemark;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号", index = 10)
    private String contractNum;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称", index = 11)
    private String contractName;

    @ApiModelProperty(value = "关联合同（子订单）编号")
    @ExcelProperty(value = "关联合同（子订单）编号", index = 12)
    private String associateContractNumber;

    /**
     * 合同里程碑
     */
    @ApiModelProperty(value = "合同里程碑")
    @ExcelProperty(value = "合同里程碑", index = 13)
    private String milestoneName;

    /**
     * 甲方单位名称
     */
    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称", index = 14)
    private String partyADeptIdName;

    /**
     * 开票/收入确认公司名称
     */
    @ApiModelProperty(value = "开票/收入确认公司名称")
    @ExcelProperty(value = "开票/收入确认公司", index = 15)
    private String billingCompanyName;


}
