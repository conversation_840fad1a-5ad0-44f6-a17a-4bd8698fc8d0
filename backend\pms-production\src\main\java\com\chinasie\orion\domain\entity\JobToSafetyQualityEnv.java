package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * JobToSafetyQualityEnv Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-23 11:06:02
 */
@TableName(value = "pms_job_to_safety_quality_env")
@ApiModel(value = "JobToSafetyQualityEnv对象", description = "作业与隐患信息")
@Data
public class JobToSafetyQualityEnv extends ObjectEntity implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "project_id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "safety_quality_env_id")
    @TableField(value = "safety_quality_env_id")
    private String safetyQualityEnvId;
    /**
     * 主id
     */
    @ApiModelProperty(value = "job_manage_id")
    @TableField(value = "job_manage_id")
    private String jobManageId;
}
