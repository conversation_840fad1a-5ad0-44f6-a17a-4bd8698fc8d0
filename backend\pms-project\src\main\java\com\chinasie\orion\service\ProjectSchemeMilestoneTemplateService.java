package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneTemplateDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneTemplate;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneTemplateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
/**
 * <p>
 * ProjectSchemeMilestoneTemplate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
public interface ProjectSchemeMilestoneTemplateService extends OrionBaseService<ProjectSchemeMilestoneTemplate> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectSchemeMilestoneTemplateVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectSchemeMilestoneTemplateDTO
     */
    ProjectSchemeMilestoneTemplateVO create(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO)  throws Exception;


    /**
     * 保存或更新
     * @param templateName
     * @return
     * @throws Exception
     */
    ProjectSchemeMilestoneTemplateVO saveNoExist(String templateName)  throws Exception;


    /**
     *  编辑
     *
     * * @param projectSchemeMilestoneTemplateDTO
     */
    Boolean edit(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectSchemeMilestoneTemplateVO> pages(Page<ProjectSchemeMilestoneTemplateDTO> pageRequest) throws Exception;

    /**
     * 列表
     * @param projectSchemeMilestoneTemplateDTO
     * @return
     * @throws Exception
     */
    List<ProjectSchemeMilestoneTemplateVO> lists(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception;


    Boolean enable(String id)throws Exception;

    Boolean disEnable(String id);
}
