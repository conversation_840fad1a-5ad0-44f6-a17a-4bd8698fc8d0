<script setup lang="ts">
import Api from '/@/api';
import { OrionTable } from 'lyra-component-vue3';
import { Ref, ref, unref } from 'vue';
import dayjs from 'dayjs';

const selectionRows = ref({});
const tableRef:Ref = ref();
const getTableRef = () => tableRef.value;

const tableOptions = {
  showToolButton: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  columns: [
    {
      title: '线索编号',
      dataIndex: 'number',
      width: 120,
      slots: { customRender: 'name' },
    },
    {
      title: '线索名称',
      dataIndex: 'name',
      minWidth: 120,
    },
    {
      title: '状态',
      dataIndex: 'busStatus',
      maxWidth: 120,
    },
    {
      title: '线索来源',
      dataIndex: 'sourceId',
      width: 120,
    },
    {
      title: '线索客户',
      dataIndex: 'custName',
      width: 100,
    },
    {
      title: '提出人',
      dataIndex: 'projectPerson',
      width: 100,
    },
    {
      title: '提出时间',
      dataIndex: 'proposeDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
  ],
  rowKey: 'number',
  api: (params) => new Api('/pms/projectInitiation/getClues').fetch(params, '', 'POST'),
};
const handleSelectionChange = (rows) => {
  selectionRows.value = { ...rows };
};
defineExpose({
  getTableRef,
});
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    class="card-list-table"
    @selectionChange="handleSelectionChange"
  />
</template>

<style scoped lang="less">

</style>