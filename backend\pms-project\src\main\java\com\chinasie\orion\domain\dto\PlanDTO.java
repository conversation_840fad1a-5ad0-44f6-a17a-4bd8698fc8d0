package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/15:51
 * @description:
 */
@Data

@ApiModel(value = "PlanDTO对象", description = "计划")
public class PlanDTO extends ObjectDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "所选项目不能为空")
    private String projectId;

    @ApiModelProperty(value = "负责人ID")
    @NotEmpty(message = "负责人不能为空")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    @ApiModelProperty(value = "计划图片")
    private String planImage;
    /**
     * 预计开始时间
     */
    @ApiModelProperty(value = "预计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planPredictStartTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityLevel;
    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private BigDecimal schedule;

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTime;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划类型Id")
    private String planType;

    /**
     * 预计结束时间
     */
    @ApiModelProperty(value = "预计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planPredictEndTime;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;


    @ApiModelProperty(value = "内部状态ID")
    private String taskStatusId;


    /**
     * 计划壳Id
     */
    @ApiModelProperty(value = "计划壳Id")
    private String pmId;

    @ApiModelProperty(value = "预计工时")
    private BigDecimal manHour;

    @ApiModelProperty(value = "实际工时")
    private BigDecimal realityManHour;

    @ApiModelProperty(value = "剩余工时")
    private BigDecimal residueManHour;

    @ApiModelProperty(value = "偏差工时")
    private BigDecimal deviationManHour;

    @ApiModelProperty(value = "工时进度")
    private BigDecimal manHourSchedule;

    @ApiModelProperty(value = "工时进度描述（转为百分比）")
    private String manHourScheduleName;


    @ApiModelProperty(value = "参与人")
    private List<String> participant;

    /**
     * 文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;


    /**
     * 管理节点
     */
    @ApiModelProperty(value = "管理节点")
    private String manageNode;

    /**
     * 风险项
     */
    @ApiModelProperty(value = "风险项")
    private String riskItem;

    /**
     * 进度状态
     */
    @ApiModelProperty(value = "进度状态")
    private Integer speedStatus;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    private String resOrg;

    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    private List<String> joinOrgs;
    private String joinOrg;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUser;

    /**
     * 参与科室
     */
    @ApiModelProperty(value = "参与科室")
    private List<String> joinDepts;
    private String joinDept;


    private List<PlanDTO> children = new ArrayList<>();

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Integer level;
}
