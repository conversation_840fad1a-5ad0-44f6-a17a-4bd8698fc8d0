<script setup lang="ts">
import { IOrionTableOptions, OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { get } from 'lodash-es';
import {
  inject, onMounted, ref, watch, watchEffect,
} from 'vue';
import {
  useDynamicContractPlanLayout,
} from '../../statusStoreManage/useDynamicContractPlanLayout';
import { parsePriceByNumber } from '/@/views/spm/utils/utils';
import { STableSummaryCell, STableSummaryRow } from '@surely-vue/table';
import Template from '/@/views/spm/projectLaborer/knowledgeEditData/Template.vue';

const props = withDefaults(defineProps<{
  tabCode:string
}>(), {
  tabCode: '',
});
const { currLayoutCode } = useDynamicContractPlanLayout();
const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const tableOptions:IOrionTableOptions = {
  columns: [
    {
      title: '类型',
      dataIndex: 'costTypeName',
      width: 100,
    },
    {
      title: '名称',
      dataIndex: 'costName',
    },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
      width: 100,
    },
    {
      title: '数量',
      dataIndex: 'num',
      width: 160,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 100,
    },
    {
      title: '总计（元）',
      dataIndex: 'totalAmount',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
    },
  ],
  deleteToolButton: 'enable|disable|add|delete',
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  canResize: false,
};
const dataSource = ref([]);

async function getContractPlanByCode() {
  try {
    const result = await new Api('/spm/contractMain/listByCode').fetch({
      code: currLayoutCode.value,
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
      year: get(basicContractEmployerPlan, 'year'),
    }, '', 'GET');

    dataSource.value = result;
  } catch (e) {}
}

watchEffect(async () => {
  if (currLayoutCode.value) {
    await getContractPlanByCode();
  }
});
</script>

<template>
  <div class="contract-employer-plan">
    <OrionTable
      :dataSource="dataSource"
      :options="tableOptions"
    >
      <template
        v-if="dataSource.length"
        #summary
      >
        <s-table-summary-row>
          <s-table-summary-cell
            :index="0"
            align="center"
            :col-span="5"
          >
            合计
          </s-table-summary-cell>
          <s-table-summary-cell
            :index="5"
            :col-span="2"
          >
            <template #default="{ total }">
              {{ total }}
            </template>
          </s-table-summary-cell>
          <s-table-summary-cell
            :index="7"
            :col-span="2"
          >
            <template #default="{ total }">
              {{ parsePriceByNumber(total) }}
            </template>
          </s-table-summary-cell>
        </s-table-summary-row>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.contract-employer-plan{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>