<template>
  <OrionTable
    v-if="showTableSetting && checked"
    ref="tableRef"
    :options="tableOptions"
    :dataSource="tableSource"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="checked === 'contract' && isPower('JHGL_container_button_01',powerData)"
        type="primary"
        icon="add"
        @click="openAddContract(true,{})"
      >
        关联合同
      </BasicButton>
      <BasicUpload
        v-if="checked==='word' && isPower('JHGL_container_button_02',powerData)"
        button-text="上传文档"
        @saveChange="saveChange"
      />
    </template>
    <template #toolbarRight>
      <a-radio-group
        v-model:value="checked"
        class="select-btn"
        @change="onChangeType"
      >
        <a-radio-button
          v-for="val in radioList"
          :key="val?.value"
          :value="val?.value"
        >
          {{ val?.label }}
        </a-radio-button>
      </a-radio-group>
    </template>
  </OrionTable>
  <div
    v-if="radioList.length===0"
    class="w-full h-full flex flex-ac flex-pc"
  >
    <Empty
      description="暂无权限"
      :image="Empty.PRESENTED_IMAGE_SIMPLE"
    />
  </div>
  <AddContract
    :projectSchemeId="$props.projectSchemeId"
    @register="registerAddContract"
    @updateTable="updateTable"
  />
</template>
<script lang="ts">
import {
  defineComponent, ref, computed, nextTick, inject, unref, h,
} from 'vue';
import {
  RadioGroup, RadioButton, message, Empty,
} from 'ant-design-vue';
import {
  OrionTable, BasicButton, BasicUpload, useDrawer, isPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

import { useRouter } from 'vue-router';
import AddContract from './AddContract.vue';
import { deleteSchemeContract } from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';

export default defineComponent({
  components: {
    ARadioGroup: RadioGroup,
    ARadioButton: RadioButton,
    OrionTable,
    BasicButton,
    AddContract,
    BasicUpload,
    Empty,
  },
  props: {
    projectSchemeId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const powerData = inject('powerData');
    const radioList = ref([
      (isPower('JHGL_container_02_01', unref(powerData)) ? {
        label: '关联合同',
        value: 'contract',
      } : undefined),
      (isPower('JHGL_container_02_02', unref(powerData)) ? {
        label: '关联文档',
        value: 'word',
      } : undefined),
    ].filter((item) => item));
    const checked = ref(isPower('JHGL_container_02_01', unref(powerData)) ? 'contract' : isPower('JHGL_container_02_02', unref(powerData)) ? 'word' : '');
    const showTableSetting = ref(true);
    const tableSource = [];
    const tableRef = ref();
    const router = useRouter();
    const projectNumber = inject('projectNumber') as string;
    const [registerAddContract, { openDrawer: openAddContract }] = useDrawer();
    const tableColumns = computed(() => {
      if (checked.value === 'contract') {
        return [
          {
            title: '合同编号',
            dataIndex: 'number',
            width: 100,
          },
          {
            title: '合同名称',
            dataIndex: 'name',
            align: 'left',
            minWidth: 300,
            slots: { customRender: 'name' },
          },
          {
            title: '合同创建时间',
            align: 'left',
            dataIndex: 'signingDate',
            width: 120,
            customRender({ text }) {
              return text ? dayjs(text).format('YYYY-MM-DD') : '';
            },
          },
          {
            title: '合同交货时间',
            align: 'left',
            dataIndex: 'deliveryDate',
            width: 120,
            customRender({ text }) {
              return text ? dayjs(text).format('YYYY-MM-DD') : '';
            },
          },
          {
            title: '合同形式',
            align: 'left',
            dataIndex: 'shapeName',
            width: 150,
          },
          {
            title: '合同总金额',
            align: 'left',
            dataIndex: 'contractMoney',
            width: 150,
            customRender({ text }) {
              return h('span', Number(text).toLocaleString());
            },
          },
          {
            title: '采购员',
            align: 'left',
            width: 120,
            dataIndex: 'purchasePersonName',
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ];
      }
      return [
        {
          title: '合同名称',
          dataIndex: 'name',
          align: 'left',
          slots: { customRender: 'name' },
        },
        {
          title: '上传日期',
          align: 'left',
          dataIndex: 'createTime',
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '操作员',
          align: 'left',
          dataIndex: 'createUserName',
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ];
    });
    const requestApi = (params) => {
      if (checked.value === 'contract') {
        return new Api('/pms/projectSchemeContract/pages').fetch({
          ...params,
          query: {
            projectSchemeId: props.projectSchemeId,
          },
        }, '', 'POST');
      }
      return new Api(`/pms/projectSchemeDocument/${props.projectSchemeId}/files/pages`).fetch(params, '', 'POST');
    };
    const tableOptions = ref({
      showSmallSearch: false,
      showToolButton: false,
      pagination: false,
      api: requestApi,
      columns: tableColumns,
      actions: [
        {
          text: '查看',
          onClick: handleLook,
        },
        {
          text: '移除',
          modal: handleRemove,
        },
      ],
    });
    const showAddContractModal = ref(false);

    function handleLook(record) {
      if (checked.value === 'contract') {
        router.push({
          name: 'ContractDetail',
          params: {
            number: record.number,
          },
          query: {
            pn: unref(projectNumber),
          },
        });
      } else {
        checkField(record);
      }
    }

    // 移除关联合同
    async function handleRemove(record) {
      if (checked.value === 'contract') {
        await deleteSchemeContract([record.id]);
        updateTable();
      } else {
        return new Promise((resolve, reject) => {
          new Api(`/pms/projectSchemeDocument/${props.projectSchemeId}/files`).fetch([record.id], '', 'delete').then(() => {
            message.success('移除成功');
            updateTable();
            resolve('');
          }).catch(() => {
            reject();
          });
        });
      }
    }

    const onChangeType = (e) => {
      showTableSetting.value = false;
      checked.value = e?.target?.value;
      nextTick(() => {
        showTableSetting.value = true;
      });
    };

    // 保存文件
    async function saveChange(files) {
      await new Api(`/pms/projectSchemeDocument/${props?.projectSchemeId}/files`).fetch(files.map((item) => item.result), '', 'POST');
      message.success('上传成功');
      updateTable();
    }

    function updateTable() {
      nextTick(() => {
        tableRef.value.reload();
      });
    }

    // 文件预览
    function checkField(fileData) {
      window.open(`/api/document-platform/document/preview?fileId=${fileData.id}&fileName=${encodeURIComponent(fileData.name)}${fileData.filePostfix}&baseHost=${location.host}&fileExt=${fileData.filePostfix}`);
    }

    return {
      radioList,
      checked,
      tableOptions,
      tableSource,
      saveChange,
      tableRef,
      showAddContractModal,
      onChangeType,
      showTableSetting,
      registerAddContract,
      openAddContract,
      updateTable,
      isPower,
      powerData,
      Empty,
    };
  },
  computed: {
    roleList() {
      return roleList;
    },
  },
});
</script>
<style lang="less" scoped>

</style>
