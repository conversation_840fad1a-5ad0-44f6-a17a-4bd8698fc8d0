package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.domain.dto.MilestoneIncomeAllocationDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MileStoneLog;
import com.chinasie.orion.domain.entity.MilestoneIncomeAllocation;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.MilestoneIncomeAllocationVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.MilestoneIncomeAllocationMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MileStoneLogService;
import com.chinasie.orion.service.MilestoneIncomeAllocationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * MilestoneIncomeAllocation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07 10:50:47
 */
@Service
@Slf4j
public class MilestoneIncomeAllocationServiceImpl extends OrionBaseServiceImpl<MilestoneIncomeAllocationMapper, MilestoneIncomeAllocation> implements MilestoneIncomeAllocationService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private MileStoneLogService mileStoneLogService;
    @Resource
    private MessageCenterApi messageCenterApi;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MilestoneIncomeAllocationVO detail(String id, String pageCode) throws Exception {
        MilestoneIncomeAllocation milestoneIncomeAllocation = this.getOne(new LambdaQueryWrapperX<>(MilestoneIncomeAllocation.class).eq(MilestoneIncomeAllocation::getMilestoneId,id));
        MilestoneIncomeAllocationVO result = BeanCopyUtils.convertTo(milestoneIncomeAllocation, MilestoneIncomeAllocationVO::new);
        ContractMilestone contractMilestone = contractMilestoneService.getOne(new LambdaQueryWrapperX<>(ContractMilestone.class)
                .eq(ContractMilestone::getId,result.getMilestoneId()));
        if(ObjectUtil.isEmpty(contractMilestone)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "不存在里程碑");
        }
       List<ContractMilestone>  contractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class)
                .eq(ContractMilestone::getParentId,result.getMilestoneId()));
        contractMilestones.add(0,contractMilestone);
        if(CollUtil.isEmpty(contractMilestones)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "不存在子项里程碑");
        }
        List<ContractMilestoneVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(contractMilestones,ContractMilestoneVO::new);
        result.setContractMilestoneVOS(contractMilestoneVOS);
        setEveryName(Collections.singletonList(result));
        List<FileVO> files = fileApi.getFilesByDataId(id);
        result.setFileList(files);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param milestoneIncomeAllocationDTO
     */
    @Override
    public String create(MilestoneIncomeAllocationDTO milestoneIncomeAllocationDTO) throws Exception {
        MilestoneIncomeAllocation milestoneIncomeAllocation = BeanCopyUtils.convertTo(milestoneIncomeAllocationDTO, MilestoneIncomeAllocation::new);
        this.save(milestoneIncomeAllocation);

        String rsp = milestoneIncomeAllocation.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param milestoneIncomeAllocationDTO
     */
    @Override
    public Boolean edit(MilestoneIncomeAllocationDTO milestoneIncomeAllocationDTO) throws Exception {
        MilestoneIncomeAllocation milestoneIncomeAllocation = BeanCopyUtils.convertTo(milestoneIncomeAllocationDTO, MilestoneIncomeAllocation::new);

        this.updateById(milestoneIncomeAllocation);
        String rsp = milestoneIncomeAllocation.getId();

        List<ContractMilestone> contractMilestones = BeanCopyUtils.convertListTo(milestoneIncomeAllocationDTO.getContractMilestoneDTOs(),ContractMilestone::new);
        contractMilestoneService.updateBatchById(contractMilestones);

        //编辑附件
        List<FileDTO> fileDTOList = milestoneIncomeAllocationDTO.getFileInfoDTOList();
        List<FileVO> existFileList = fileApi.getFilesByDataId(rsp);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            List<String> filesIds = existFileList.stream().map(FileVO::getId).collect(Collectors.toList());
            fileApi.removeBatchByIds(filesIds);
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(rsp);
                item.setDataType("MilestoneIncomeAllocation");
            });
            fileApi.batchSaveFile(fileDTOList);
        }
        messageCenterApi.todoMessageChangeStatusByBusinessId("MilestoneIncomeAllocation_"+milestoneIncomeAllocation.getMilestoneId());
        MileStoneLog mileStoneLog=new MileStoneLog();
        mileStoneLog.setMilestoneId(milestoneIncomeAllocationDTO.getMilestoneId());
        mileStoneLog.setEditDesc("已确认金额分配");
        mileStoneLog.setEditMessage("分配已完成金额给二级里程碑");
        mileStoneLog.setFileCount(fileDTOList==null?0:fileDTOList.size());
        mileStoneLog.setEditPerson(CurrentUserHelper.getCurrentUserId());
        mileStoneLog.setEditTime(new Date());
        mileStoneLogService.save(mileStoneLog);
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MilestoneIncomeAllocationVO> pages(Page<MilestoneIncomeAllocationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MilestoneIncomeAllocation> condition = new LambdaQueryWrapperX<>(MilestoneIncomeAllocation.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MilestoneIncomeAllocation::getCreateTime);


        Page<MilestoneIncomeAllocation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MilestoneIncomeAllocation::new));

        PageResult<MilestoneIncomeAllocation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MilestoneIncomeAllocationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MilestoneIncomeAllocationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MilestoneIncomeAllocationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<MilestoneIncomeAllocationVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


}
