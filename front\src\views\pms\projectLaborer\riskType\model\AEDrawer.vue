<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="500"
    wrap-class-name="addTreeNode-riskType"
    @register="register"
    @close="cancelClick('close')"
  >
    <BasicForm
      class="oobbj"
      @register="registerForm"
    />

    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="cloneData[0]==='add'"
        :loading="loading"
        @cancelClick="cancelClick('close')"
        @okClick="okClick"
      />
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import { Checkbox, Button, message } from 'ant-design-vue';
import { DrawerFooterButtons } from '/@/views/components';
import Api from '/@/api';

export default defineComponent({
  name: '',
  components: {
    <PERSON>Drawer,
    BasicForm,
    DrawerFooterButtons,
  },
  emits: ['addSuccess'],
  setup(_, { emit }) {
    const state: any = reactive({
      cloneData: [],
      checked: undefined,
      loading: false,
      parentId: '',
    });

    function resetData() {
      state.cloneData = [];
      state.checked = undefined;
      state.loading = false;
    }

    const schemas: FormSchema[] = [
      {
        field: 'parentId',
        component: 'Input',
        label: '所属类型:',
        rules: [
          // { required: true, trigger: 'blur' },
        ],
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'number',
        component: 'Input',
        label: '编号:',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
        ],
        componentProps: {
          disabled: true,
          maxlength: 15,
          placeholder: '创建类型完成时自动生成编号',
        },
        defaultValue: '创建类型完成时自动生成编号',
      },
      {
        field: 'name',
        component: 'Input',
        label: '名称:',
        rules: [
          {
            required: true,
            trigger: 'blur',
          },
          {
            min: 1,
            max: 64,
            message: '名称1-64位',
            trigger: 'blur',
          },
        ],
        componentProps: {
          maxlength: 64,
        },
      },
      {
        field: 'remark',
        component: 'InputTextArea',
        label: '描述:',
        componentProps: {
          rows: 4,
          showCount: true,
          maxlength: 100,
        },
      },
      {
        field: 'icon',
        component: 'IconSelect',
        label: '类型图标',
        colProps: {
          span: 24,
        },
        componentProps: {
          class: 'IconSelect',
        },
        defaultValue: 'fa-th-large',
      },
    ];
    const [registerForm, F1] = useForm({
      layout: 'vertical',
      baseColProps: {
        span: 24,
      },
      schemas,
      showSubmitButton: false,
      showResetButton: false,
    });
    const [register, DrawerM] = useDrawerInner((data) => {
      state.cloneData = JSON.parse(JSON.stringify(data));
      F1.setFieldsValue({ parentId: state.cloneData[1].showType ? state.cloneData[1].showType : '全部' });
      if (state.cloneData[0] === 'edit') {
        // if (state.cloneData[1].fileToolList?.length > 0) {
        //   state.cloneData[1].fileToolList = state.cloneData[1].fileToolList.map((item) => item.id);
        // }
        // F1.setFieldsValue(state.cloneData[1]);
      }
    });

    /**
         * @description: 提交确定
         * */
    async function okClick() {
      await F1.validate();
      state.loading = true;
      const fd = F1.getFieldsValue();
      // console.log("----- fd -----", fd)
      // 如果格式不对,此处可以对请求参数进行拷贝并处理
      const params: any = JSON.parse(JSON.stringify(fd));
      if (!params?.parentId) {
        params.parentId = 0;
      } else {
        params.parentId = state.cloneData[1].data.id;
      }
      if (params?.number === '创建类型完成时自动生成编号') {
        delete params.number;
      }
      if (state.cloneData[0] === 'add') {
        new Api('/pms/projectPlan-type').fetch(params, '', 'POST').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      } else {
        // 如果格式不对或需要添加某些id,此处可以对请求参数进行处理
        // params.id = state.cloneData[1].id;
        new Api('/pms/projectPlan-type').fetch(fd, '', 'PUT').then(() => {
          message.success('操作成功');
          cancelClick('ok');
          emit('addSuccess');
        }).catch(() => {
          state.loading = false;
        });
      }
    }

    /**
         * @description: 取消
         * */
    function cancelClick(type) {
      state.loading = false;
      if (type === 'close') {
        resetData();
        F1.resetFields();
        DrawerM.closeDrawer();
      } else if (!state.checked) {
        F1.resetFields();
        DrawerM.closeDrawer();
      } else {
        const fd = F1.getFieldsValue();
        let parentId = fd.parentId;
        F1.setFieldsValue({ parentId });
        F1.resetFields();
        F1.setFieldsValue({ parentId: state.cloneData[1].showType });
      }
    }

    return {
      ...toRefs(state),
      register,
      registerForm,
      okClick,
      cancelClick,
    };
  },
});
</script>
<style lang="less" scoped>
//:deep(.bbj){
//  background-color: red !important;
//  &>.ant-row{
//    .ant-col{
//      .ant-form-item{
//        display: block !important;
//      }
//    }
//  }
//
//}
.addTreeNode-riskType {
  .oobbj {
    .ant-form-item {
      display: block !important;
    }
  }
}
</style>
