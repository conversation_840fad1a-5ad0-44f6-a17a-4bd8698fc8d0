<template>
  <BasicModal
    v-bind="$attrs"
    title="工时预估"
    :width="1200"
    :min-height="600"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <div class="table-wrap">
      <OrionTable
        ref="detailTableRef"
        :options="estimateOption"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="add"
            @click="addNewRow"
          >
            新增一行
          </BasicButton>
          <BasicButton
            icon="delete"
            @click="multiDelete"
          >
            删除
          </BasicButton>
        </template>
      </OrionTable>
    </div>

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="closeModal"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          @click="handleConfirm"
        >
          确定
        </BasicButton>

        <BasicButton
          type="primary"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicModal>
</template>
<script setup lang="ts">
import {
  BasicButton, BasicModal, InputSelectUser, OrionTable, randomString, useModalInner,
} from 'lyra-component-vue3';
import {
  computed, h, inject, reactive, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import { cloneDeep } from 'lodash-es';
import {
  DatePicker, InputNumber, message, Modal,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const updateNodePages: (() => void) = inject('updateNodePages');
const selectPrincipalUser = ref('');
const route = useRoute();
const state = reactive({
  payNodeTypeOptions: [],
  orderList: [],
  orderAndNodeParamDTOList: [],
  btnLoading: false,
  selectPrincipalUser: '',
  columns: [
    {
      title: '成员姓名',
      dataIndex: 'memberId',
      required: true,
      customRender({ record, index, column }) {
        return h(InputSelectUser, {
          selectUserData: computed((value) => record.selectUserData),
          onChange(users) {
            selectPrincipalUser.value = users;
            record[column.dataIndex] = users[0].id;
            record.memberName = users[0].name;
            record.selectUserData = users;
            setRoleInfo(users?.[0]?.id, column, record);
          },
          selectUserModalProps: {
            selectType: 'radio',
            treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
              {
                orders: [
                  {
                    asc: false,
                    column: '',
                  },
                ],
                pageNum: 0,
                pageSize: 0,
                query: { status: 1 },
              },
              '',
              'POST',
            ),
          },
        });
      },
    },
    {
      title: '成员角色',
      dataIndex: 'memberRoleName',
    },
    {
      title: '工时时长（小时）',
      dataIndex: 'workHour',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      align: 'left',
      width: 140,
      required: true,
      customRender({ record, index, column }) {
        return h(DatePicker, {
          allowClear: false,
          value: computed((date) => date),
          disabledDate: (date: Date) => (
            dayjs(date).valueOf() > dayjs(basicInfos.value.projectEndTime).valueOf() || dayjs(date).valueOf() < dayjs(basicInfos.value.projectStartTime).valueOf() || dayjs(date).subtract(1, 'day').valueOf() > dayjs(record.endDate).valueOf()
          ),
          onChange(val) {
            record[column.dataIndex] = dayjs(val.valueOf()).format('YYYY-MM-DD');
            record.allEffectYearMonth = getYearMonthArray(record.startDate, record.endDate);
            updateRecord(record);
          },
          placeholder: '请选择',
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      align: 'left',
      width: 140,
      required: true,
      customRender({ record, index, column }) {
        return h(DatePicker, {
          value: computed((date) => date),
          allowClear: false,
          disabledDate: (date: Date) => (
            dayjs(date).valueOf() > dayjs(basicInfos.value.projectEndTime).valueOf() || dayjs(date).valueOf() < dayjs(basicInfos.value.projectStartTime).valueOf() || dayjs(date).valueOf() < dayjs(record.startDate).valueOf()
          ),
          onChange(val) {
            record[column.dataIndex] = val ? dayjs(val.valueOf()).format('YYYY-MM-DD') : undefined;
            record.allEffectYearMonth = getYearMonthArray(record.startDate, record.endDate);
            updateRecord(record);
          },
          placeholder: '请选择',
          style: {
            width: '100%',
          },
        });
      },
    },
  ],
  obj: {
    id: randomString(),
    memberId: '',
    memberRoleName: '',
    workHour: '',
    startDate: '',
    endDate: '',
  },
  isShowTable: false,
  projectId: route.query.id,
});

function updateRecord(record: Record<string, any>) {
  const detailList = [];
  columns.value.forEach((item) => {
    if (!getYearMonthArray(record?.startDate, record?.endDate).includes(item?.value)) {
      record[item.dataIndex] = null;
    } else {
      detailList.push(record?.detailList?.find((v: Record<string, any>) => v?.workMonth === item?.dataIndex));
    }
  });
  record.detailList = detailList;
  record.workHour = detailList?.reduce((prev, next) => prev + (Number(next?.workHour) || 0), 0);
}

const obj = () => ({
  id: randomString(),
  memberId: '',
  memberRoleName: '',
  workHour: '',
  startDate: '',
  endDate: '',
  projectId: state.projectId,
});
const columns = ref([]);
const basicInfos: Ref<{
  [propName: string]: any
}> = ref({});
const detailTableRef: Ref = ref(null);
const dataSource: Ref<any[]> = ref([state.obj]);
const estimateOption = reactive({
  deleteToolButton: 'add|enable|disable|delete',
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'id',
  rowSelection: {},
  dataSource,
  columns: state.columns,
  customHeaderCell(column) {
    return {
      className: `surely-table-cell surely-table-header-cell${column.required ? ' required' : ''}`,
    };
  },
});

const emit = defineEmits<{
  (e: 'loadingChange', loading: boolean): void
}>();

function getYearMonthArray(startDate, endDate) {
  if (!startDate || !endDate) return [];

  const startYear = new Date(startDate).getFullYear();
  const startMonth = new Date(startDate).getMonth() + 1;
  const endYear = new Date(endDate).getFullYear();
  const endMonth = new Date(endDate).getMonth() + 1;
  const yearMonthArray = [];

  for (let year = startYear; year <= endYear; year++) {
    let start = 1;
    let end = 12;

    if (year === startYear) {
      start = startMonth;
    }

    if (year === endYear) {
      end = endMonth;
    }

    for (let month = start; month <= end; month++) {
      const formattedMonth = month.toString().padStart(2, '0');
      const yearMonth = `${year}-${formattedMonth}`;
      yearMonthArray.push(yearMonth);
    }
  }

  return yearMonthArray;
}

const [register, { closeModal }] = useModalInner((basicInfo) => {
  let detailList = [];
  basicInfos.value = basicInfo;
  const yearMonthArr = getYearMonthArray(basicInfo.projectStartTime, basicInfo.projectEndTime);
  columns.value = yearMonthArr.map((item) => ({
    title: dayjs(item).format('YYYY年MM月'),
    dataIndex: dayjs(item).format('YYYY年MM月'),
    value: dayjs(item).format('YYYY-MM'),
    customRender({
      record, index, column,
    }) {
      return h(InputNumber, {
        value: computed(() => record[column.dataIndex]),
        disabled: !(record?.allEffectYearMonth && record?.allEffectYearMonth.includes(column.value)),
        placeholder: '请输入工时',
        onChange(e: any) {
          record[column.dataIndex] = e;
          onInputBlur(index, record);
        },
      });
    },
  }));

  yearMonthArr.forEach((_item) => {
    detailList.push({
      [dayjs(_item).format('YYYY年MM月')]: null,
    });
  });

  let mergedObj = detailList.reduce((result, currentObj) => {
    Object.assign(result, currentObj);
    return result;
  }, {});
  let resultArr = [mergedObj];

  dataSource.value = [
    {
      ...state.obj,
      ...resultArr[0],
    },
  ];

  state.obj = {
    ...state.obj,
    ...resultArr[0],
  };
  detailTableRef.value.setColumns(state.columns.concat(columns.value));
  addNewRow();
  dataSource.value.splice(0, 1);
});

const onInputBlur = (index: number, record: Record<string, any>) => {
  let detailList = [];
  columns.value.forEach((item) => {
    if (getYearMonthArray(record.startDate, record.endDate).includes(item.value)) {
      detailList.push({
        workMonth: item.dataIndex,
        workHour: dataSource.value[index][item.dataIndex],
      });
    }
  });
  dataSource.value[index].workHour = detailList?.reduce((prev, next) => prev + (Number(next?.workHour) || 0), 0);
  dataSource.value[index].detailList = detailList;
  detailTableRef.value.setTableData(dataSource.value);
};

async function handleConfirm() {
  if (dataSource.value.some((item) => !item?.memberId || !item?.endDate || !item?.startDate)) {
    return message.error('请完善必填项');
  }

  if (dataSource.value.some((record) => !record?.detailList || record?.detailList?.some((item) => !item?.workHour))) {
    return message.error('存在未填写的工时');
  }
  for (let i in dataSource.value) {
    dataSource.value[i].detailList = dataSource.value[i].detailList?.filter((item) => item.workHour);
    dataSource.value[i].projectId = state.projectId;
  }
  new Api('/pms').fetch(dataSource.value, 'workHourEstimate', 'POST').then((res) => {
    message.success('新增成功');
    updateNodePages();
    closeModal();
  }).catch((err) => {
    closeModal();
  });
}

async function handleSubmit() {
  if (dataSource.value.some((item) => !item?.memberId || !item?.endDate || !item?.startDate)) {
    return message.error('请完善必填项');
  }

  if (dataSource.value.some((record) => !record?.detailList || record?.detailList?.some((item) => !item?.workHour))) {
    return message.error('存在未填写的工时');
  }

  for (let i in dataSource.value) {
    dataSource.value[i].detailList = dataSource.value[i].detailList.filter((item) => item.workHour);
    dataSource.value[i].projectId = state.projectId;
  }
  new Api('/pms').fetch(dataSource.value, 'workHourEstimate/submit', 'POST').then((res) => {
    message.success('提交成功');
    updateNodePages();
    closeModal();
  }).catch((err) => {
    closeModal();
  });
}

function visibleChange(visible: boolean) {
  if (visible === false) {
    columns.value = [];
    closeModal();
  }
}

const addNewRow = () => {
  // eslint-disable-next-line operator-assignment
  let obj = cloneDeep(state.obj);
  obj.id = randomString();
  dataSource.value.push(obj);
  detailTableRef.value.setTableData(dataSource.value);
};

async function setRoleInfo(id, columns, record) {
  new Api(`/pms/project-role-user/user/role/info/list/${state.projectId}`).fetch({
    userId: id,
  }, '', 'GET').then((res) => {
    if (res && res.length > 0) {
      record.memberRoleName = res.map((item) => item.name).join(',');
    }
  }).catch((err) => {
  });
}

function multiDelete() {
  // if (detailTableRef.value.getSelectRowKeys().length > 0) {
  //   for (let i in detailTableRef.value.getSelectRowKeys()) {
  //     for (let j in dataSource.value) {
  //       console.log(dataSource.value[i].id);
  //       if (dataSource.value[i].id == detailTableRef.value.getSelectRowKeys()[i]) {
  //         console.log(dataSource.value[i].id == detailTableRef.value.getSelectRowKeys()[i]);
  //         dataSource.value.splice(dataSource.value.indexOf(detailTableRef.value.getSelectRowKeys()[i]), 1);
  //       }
  //     }
  //   }
  // }
  if (detailTableRef.value.getSelectRowKeys().length === 0) {
    return message.error('请选择要删除的数据!');
  }
  Modal.confirm({
    title: '删除提示',
    content: '是否删除当前单据，删除后可重新添加？',
    onOk() {
      dataSource.value = dataSource.value.filter((item) =>
        !detailTableRef.value.getSelectRowKeys().includes(item.id));
      detailTableRef.value.setTableData(dataSource.value);
    },
  });
  // dataSource.value = dataSource.value.filter((item) => item.id !== detailTableRef.value.getSelectRowKeys().map((item) => item));
  // console.log(dataSource.value);
}

// 获取当前项目的用户的角色
// /project-role-user/user/role/info/list/{projectId}
</script>
<style scoped lang="less">
.flex-fs {
  align-items: flex-start;
}

.table-wrap {
  height: 600px;
  overflow: hidden;
}
</style>
