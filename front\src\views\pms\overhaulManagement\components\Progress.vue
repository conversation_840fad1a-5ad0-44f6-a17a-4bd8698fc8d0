<script setup lang="ts">
import { BasicButton, isPower, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, inject, ref, Ref,
} from 'vue';
import { openWorkProgressForm } from '/@/views/pms/dailyWork/pages/utils';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useJobProgressExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';

const detailsData: Record<string, any> = inject('detailsData');
const updateLife: Function = inject('updateLife');
const tableRef: Ref = ref();
const { exportApi } = useJobProgressExcel();

function updateTable() {
  tableRef.value.reload();
}

const tableOptions = {
  showToolButton: false,
  isSpacing: false,
  pagination: false,
  maxHeight: 300,
  smallSearchField: ['progressSchedule'],
  api: (params: object) => new Api('/pms/job-progress/page').fetch({
    ...params,
    query: {
      jobId: detailsData?.id,
    },
    power: {
      pageCode: 'PMSOverhaulOperationDetails',
      containerCode: 'PMS_DXZYXQNEW_container_01_05',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '日期',
      dataIndex: 'workDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '总体进展',
      dataIndex: 'progressSchedule',
      customRender({ text }) {
        return text ? `${text}%` : '0%';
      },
    },
    {
      title: '工作进展',
      dataIndex: 'progressDetail',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_DXZYXQNEW_container_01_05_button_02', record?.rdAuthList),
      onClick(record) {
        openWorkProgressForm(record, updateTable);
      },
    },
    {
      text: '删除',
      isShow: (record) => isPower('PMS_DXZYXQNEW_container_01_05_button_03', record?.rdAuthList),
      modal: ({ id }) => deleteProgressApi([id]),
    },
  ],
};

function deleteProgressApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/job-progress/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

const toolButtons = computed(() => [
  {
    text: '添加进展',
    event: 'add',
    icon: 'sie-icon-tianjiaxinzeng',
    type: 'primary',
    powerCode: 'PMS_DXZYXQNEW_container_01_05_button_01',
  },
  {
    text: '导出',
    event: 'export',
    icon: 'sie-icon-daochu',
    powerCode: 'PMS_DXZYXQNEW_container_01_05_button_01',
  },
]);

function handleTool(event: string) {
  switch (event) {
    case 'add':
      openWorkProgressForm({ jobId: detailsData?.id }, () => {
        updateTable();
        updateLife();
      });
      break;
    case 'export':
      exportApi({
        jobId: detailsData?.id,
        repairRound: detailsData?.repairRound,
      });
      break;
  }
}
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      style="position: relative"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-is-power="[item.powerCode]"
          v-bind="item"
          @click="handleTool(item.event)"
        >
          {{ item.text }}
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
