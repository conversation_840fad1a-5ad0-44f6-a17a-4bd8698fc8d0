package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@TableName(value = "pmsx_evaluation_project")
@ApiModel(value = "EvaluationProject对象", description = "项目评价")
@Data
public class EvaluationProject extends ObjectEntity implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 项目评价名称
     */
    @ApiModelProperty(value = "项目评价名称")
    @TableField(value = "name" )
    private String name;

    /**
     * 评价类型
     */
    @ApiModelProperty(value = "评价类型")
    @TableField(value = "evaluation_type" )
    private String evaluationType;

    /**
     * 项目评价责任人id
     */
    @ApiModelProperty(value = "项目评价责任人id")
    @TableField(value = "evaluation_person_id" )
    private String evaluationPersonId;

    /**
     * 发起项目评价时间
     */
    @ApiModelProperty(value = "发起项目评价时间")
    @TableField(value = "evaluation_time" )
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date evaluationTime;

    /**
     * 评价对象
     */
    @ApiModelProperty(value = "评价对象")
    @TableField(value = "evaluation_object" )
    private String evaluationObject;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 项目经理id
     */
    @ApiModelProperty(value = "项目经理id")
    @TableField(value = "project_manager_id" )
    private String projectManagerId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;



}