package com.chinasie.orion.domain.vo;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(value = "ProjectCollectionTreeVO对象", description = "项目集子项目树")
@Data
public class ProjectCollectionTreeVO extends ProjectCollectionVO implements TreeUtils.TreeNode<String, ProjectCollectionTreeVO> {

    /**
     * 里程碑总数
     */
    @ApiModelProperty(value = "里程碑总数")
    private Integer totalCount=0;

    /**
     * 里程碑完成数
     */
    @ApiModelProperty(value = "里程碑完成数")
    private Integer finishedCount=0;

    @ApiModelProperty(value = "里程碑未完成数")
    private Integer noFinishedCount=0;

    /**
     * 问题总数
     */
    @ApiModelProperty(value = "问题总数")
    private Integer questionTotalCount=0;

    /**
     * 问题完成数
     */
    @ApiModelProperty(value = "问题完成数")
    private Integer questionFinishedCount=0;

    @ApiModelProperty(value = "问题未完成数")
    private Integer questionNOFinishedCount=0;

    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    @ApiModelProperty(value = "项目结束时间")
    private Date projectEndTime;

    @ApiModelProperty(value = "项目进度")
    private String schedule;

    /**
     * 子项
     */
    @ApiModelProperty(value = "子项")
    private List<ProjectCollectionTreeVO> children;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级")
    private String parentId;

    @ApiModelProperty(value = "是否是组合")
    private String isCollection;
}
