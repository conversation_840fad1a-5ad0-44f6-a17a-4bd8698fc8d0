package com.chinasie.orion.service.impl;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.constant.MaterialTypeEnum;
import com.chinasie.orion.domain.entity.BasePlace;
import com.chinasie.orion.domain.entity.MaterialOutManage;
import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.domain.vo.MaterialOutManageVO;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.FixedAssetsService;
import com.chinasie.orion.service.MaterialOutManageService;
import com.chinasie.orion.repository.MaterialOutManageMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;

import static com.chinasie.orion.constant.DictConts.SUPPLIES_TYPE;


/**
 * <p>
 * MaterialOutManage 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:55
 */
@Service
@Slf4j
public class MaterialOutManageServiceImpl extends  OrionBaseServiceImpl<MaterialOutManageMapper, MaterialOutManage>   implements MaterialOutManageService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private FixedAssetsService fixedAssetsService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MaterialOutManageVO detail(String id,String pageCode) throws Exception {
        MaterialOutManage materialOutManage =this.getById(id);
        MaterialOutManageVO result = BeanCopyUtils.convertTo(materialOutManage,MaterialOutManageVO::new);
        setEveryName(Collections.singletonList(result));

        //封装附件
        String number = result.getNumber();
        FixedAssetsVO fixedAssetsVO = fixedAssetsService.detailByNumber(number);
        if (Objects.nonNull(fixedAssetsVO)){
            List<FileVO> filesByDataId = fileApiService.getFilesByDataId(fixedAssetsVO.getId());
            result.setFileVOList(filesByDataId);
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param materialOutManageDTO
     */
    @Override
    public  String create(MaterialOutManageDTO materialOutManageDTO) throws Exception {
        MaterialOutManage materialOutManage =BeanCopyUtils.convertTo(materialOutManageDTO,MaterialOutManage::new);
        this.save(materialOutManage);

        String rsp=materialOutManage.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param materialOutManageDTO
     */
    @Override
    public Boolean edit(MaterialOutManageDTO materialOutManageDTO) throws Exception {
        MaterialOutManage materialOutManage =BeanCopyUtils.convertTo(materialOutManageDTO,MaterialOutManage::new);

        this.updateById(materialOutManage);

        String rsp=materialOutManage.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MaterialOutManageVO> pages( Page<MaterialOutManageDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MaterialOutManage> condition = new LambdaQueryWrapperX<>( MaterialOutManage. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MaterialOutManage::getCreateTime);


        Page<MaterialOutManage> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MaterialOutManage::new));

        PageResult<MaterialOutManage> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MaterialOutManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MaterialOutManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MaterialOutManageVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "物质出库管理导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MaterialOutManageDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            MaterialOutManageExcelListener excelReadListener = new MaterialOutManageExcelListener();
        EasyExcel.read(inputStream,MaterialOutManageDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MaterialOutManageDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("物质出库管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MaterialOutManage> materialOutManagees =BeanCopyUtils.convertListTo(dtoS,MaterialOutManage::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MaterialOutManage-import::id", importId, materialOutManagees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MaterialOutManage> materialOutManagees = (List<MaterialOutManage>) orionJ2CacheService.get("pmsx::MaterialOutManage-import::id", importId);
        log.info("物质出库管理导入的入库数据={}", JSONUtil.toJsonStr(materialOutManagees));

        this.saveBatch(materialOutManagees);
        orionJ2CacheService.delete("pmsx::MaterialOutManage-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MaterialOutManage-import::id", importId);
        return true;
    }



    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MaterialOutManage> condition = new LambdaQueryWrapperX<>( MaterialOutManage. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MaterialOutManage::getCreateTime);
        List<MaterialOutManage> materialOutManagees =   this.list(condition);

        List<MaterialOutManageDTO> dtos = BeanCopyUtils.convertListTo(materialOutManagees, MaterialOutManageDTO::new);

        String fileName = "物质出库管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MaterialOutManageDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MaterialOutManageVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        Map<String, String> stringStringMap = MaterialTypeEnum.keyToDesc();
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(SUPPLIES_TYPE);
        Map<String, String> numberToDesc = dictListByCode.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));

        List<String> deptIdList = vos.stream().map(MaterialOutManageVO::getCostCenter).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 这里通过ID获取
        List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(deptIdList);
        Map<String, String> idToName = deptVOList.stream().collect(Collectors.toMap(DeptVO::getDeptCode, DeptVO::getName, (k1, k2) -> k1));

        Map<String, BasePlace> idToEntity= basePlaceService.mapEntityAll();

        String orgId = CurrentUserHelper.getOrgId();
        vos.forEach(vo->{
            vo.setTypeName(stringStringMap.getOrDefault(vo.getType(),""));
            vo.setAssetTypeName(numberToDesc.getOrDefault(vo.getAssetType(), ""));
            vo.setInputStockNum(vo.getStockNum());
            vo.setToolStatusName(MaterialToolTypeEnum.getDescByKey(vo.getToolStatus()));
            // 如果
            String name =  idToName.getOrDefault(vo.getCostCenter(),"");
            vo.setCostCenterName(name);
            if(StrUtil.isEmpty(name) && StringUtils.hasText(vo.getCostCenter())){
                DeptBaseInfoVO deptBaseInfoVO= deptRedisHelper.getDeptBaseInfoByDeptCode(orgId,vo.getCostCenter());
                vo.setCostCenterName(null == deptBaseInfoVO ? "":deptBaseInfoVO.getName());
            }
            if(StringUtils.hasText(vo.getBaseCode())){
                BasePlace basePlace=  idToEntity.get(vo.getBaseCode());
                if(Objects.nonNull(basePlace)){
                    vo.setBaseName(basePlace.getName());
                }
            }
        });
    }

    @Override
    public Map<String, MaterialOutManage> getMateriaIdToLastOutMaterial(List<String> materialIdList) {

        LambdaQueryWrapperX<MaterialOutManage> condition = new LambdaQueryWrapperX<>( MaterialOutManage. class);
        condition.eq(MaterialOutManage::getSourceId, materialIdList)
                .orderByDesc(MaterialOutManage::getCreateTime);
        List<MaterialOutManage> materialOutManages =   this.list(condition);

        return materialOutManages.stream()
                .collect(Collectors.toMap(
                        MaterialOutManage::getSourceId, // key是sourceId
                        materialOutManage -> materialOutManage, // value是MaterialOutManage对象
                        (existing, replacement) -> {
                            // 比较两个对象的createTime，保留最新的那个
                            return existing.getCreateTime().compareTo(replacement.getCreateTime()) > 0 ? existing : replacement;
                        }));
    }

    public static class MaterialOutManageExcelListener extends AnalysisEventListener<MaterialOutManageDTO> {

        private final List<MaterialOutManageDTO> data = new ArrayList<>();

        @Override
        public void invoke(MaterialOutManageDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MaterialOutManageDTO> getData() {
            return data;
        }
    }


}
