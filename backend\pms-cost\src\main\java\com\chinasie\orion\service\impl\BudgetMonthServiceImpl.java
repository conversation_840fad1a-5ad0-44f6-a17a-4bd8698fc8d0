package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.BudgetMonthDTO;
import com.chinasie.orion.domain.entity.BudgetMonth;
import com.chinasie.orion.domain.vo.BudgetMonthVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.BudgetMonthMapper;
import com.chinasie.orion.service.BudgetMonthService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * BudgetMonth 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:58:25
 */
@Service
public class BudgetMonthServiceImpl  extends OrionBaseServiceImpl<BudgetMonthMapper, BudgetMonth> implements BudgetMonthService {

    @Resource
    private BudgetMonthMapper budgetMonthMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public BudgetMonthVO detail(String id) throws Exception {
        BudgetMonth budgetMonth =budgetMonthMapper.selectById(id);
        BudgetMonthVO result = BeanCopyUtils.convertTo(budgetMonth,BudgetMonthVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param budgetMonthDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  BudgetMonthVO create(BudgetMonthDTO budgetMonthDTO) throws Exception {
        BudgetMonth budgetMonth =BeanCopyUtils.convertTo(budgetMonthDTO,BudgetMonth::new);
        int insert = budgetMonthMapper.insert(budgetMonth);
        BudgetMonthVO rsp = BeanCopyUtils.convertTo(budgetMonth,BudgetMonthVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param budgetMonthDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(BudgetMonthDTO budgetMonthDTO) throws Exception {
        BudgetMonth budgetMonth =BeanCopyUtils.convertTo(budgetMonthDTO,BudgetMonth::new);
        int update =  budgetMonthMapper.updateById(budgetMonth);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        int delete = budgetMonthMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


}
