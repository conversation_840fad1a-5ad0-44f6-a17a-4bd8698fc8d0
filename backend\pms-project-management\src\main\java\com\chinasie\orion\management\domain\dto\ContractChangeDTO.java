package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractChange DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractChangeDTO对象", description = "合同变更信息表")
@Data
@ExcelIgnoreUnannotated
public class ContractChangeDTO extends ObjectDTO implements Serializable {

    /**
     * 变更编号
     */
    @ApiModelProperty(value = "变更编号")
    @ExcelProperty(value = "变更编号 ", index = 0)
    private String changeId;

    /**
     * 变更标题
     */
    @ApiModelProperty(value = "变更标题")
    @ExcelProperty(value = "变更标题 ", index = 1)
    private String changeTitle;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型")
    @ExcelProperty(value = "变更类型 ", index = 2)
    private String changeType;

    /**
     * 变更申请日期
     */
    @ApiModelProperty(value = "变更申请日期")
    @ExcelProperty(value = "变更申请日期 ", index = 3)
    private Date changeRequestDate;

    /**
     * 本次变更金额
     */
    @ApiModelProperty(value = "本次变更金额")
    @ExcelProperty(value = "本次变更金额 ", index = 4)
    private BigDecimal thisChangeAmount;

    /**
     * 累计变更金额
     */
    @ApiModelProperty(value = "累计变更金额")
    @ExcelProperty(value = "累计变更金额 ", index = 5)
    private BigDecimal cumulativeChangeAmount;

    /**
     * 累计变更比率
     */
    @ApiModelProperty(value = "累计变更比率")
    @ExcelProperty(value = "累计变更比率 ", index = 6)
    private String cumulativeChangeRate;

    /**
     * 变更后合同承诺总价（总目标值）
     */
    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    @ExcelProperty(value = "变更后合同承诺总价（总目标值） ", index = 7)
    private BigDecimal contactAmountAfterChange;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 8)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 9)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 10)
    private String contractNumber;
    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 11)
    private String contractName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @ExcelIgnore
    private String startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelIgnore
    private String endDate;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @ExcelProperty(value = "采购组Id ", index = 12)
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @ExcelProperty(value = "采购组名称 ", index = 13)
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @ExcelProperty(value = "商务负责人ID ", index = 14)
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @ExcelProperty(value = "商务负责人名称 ", index = 15)
    private String businessRspUserName;
}
