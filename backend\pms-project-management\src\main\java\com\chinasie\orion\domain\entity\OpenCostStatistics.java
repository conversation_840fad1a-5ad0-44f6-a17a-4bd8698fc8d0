package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "OpenCostStatistics对象", description = "开口项费用")
@Data
public class OpenCostStatistics {

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "dataQuarter")
    private Integer dataQuarter;
    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;


    /**
     * 基地后勤费
     */
    @ApiModelProperty(value = "基地后勤费")
    @TableField(value = "logisticsAmt")
    private BigDecimal logisticsAmt;

    /**
     * RP体检费
     */
    @ApiModelProperty(value = "RP体检费")
    @TableField(value = "physicalExaminationAmt")
    private BigDecimal physicalExaminationAmt;

    /**
     * 劳保用品费
     */
    @ApiModelProperty(value = "劳保用品费")
    @TableField(value = "laborAmt")
    private BigDecimal laborAmt;

    /**
     * 餐厅管理费
     */
    @ApiModelProperty(value = "餐厅管理费")
    @TableField(value = "restaurantManagementAmt")
    private BigDecimal restaurantManagementAmt;


    /**
     * 其他费
     */
    @ApiModelProperty(value = "其他费")
    @TableField(value = "otherAmt")
    private BigDecimal otherAmt;
}
