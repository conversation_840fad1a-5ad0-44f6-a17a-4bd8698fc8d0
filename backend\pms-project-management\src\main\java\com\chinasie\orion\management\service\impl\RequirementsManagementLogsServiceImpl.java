package com.chinasie.orion.management.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.vo.RequirementsManagementLogsVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.constant.RemarkEnum;
import com.chinasie.orion.management.constant.RequirementManagementStatusEnum;
import com.chinasie.orion.management.constant.RequirementStatusEnum;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.dto.RequirementsManagementLogsDTO;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.entity.RequirementsManagementLogs;
import com.chinasie.orion.management.repository.RequirementsManagementLogsMapper;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.management.service.RequirementsManagementLogsService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * RequirementsManagementLogs 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 09:45:53
 */
@Service
@Slf4j
public class RequirementsManagementLogsServiceImpl extends OrionBaseServiceImpl<RequirementsManagementLogsMapper, RequirementsManagementLogs> implements RequirementsManagementLogsService {
    @Autowired
    CurrentUserHelper currentUserHelper;

    @Autowired
    UserRedisHelper userRedisHelper;

    @Autowired
    DeptRedisHelper deptRedisHelper;

    @Autowired
    RequirementsManagementLogsMapper requirementsManagementLogsMapper;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    private RequirementMangementService requirementMangementService;

    @Override
    public boolean create(RequirementsManagementLogsDTO requirementsManagementLogsDTO) throws Exception {
        RequirementsManagementLogs requirementsManagementLogs = new RequirementsManagementLogs();
        requirementsManagementLogs.setFeedbackTime(new Date());
        requirementsManagementLogs.setOpUser(currentUserHelper.getUserId());
        requirementsManagementLogs.setOpUserName(userRedisHelper.getUserById(currentUserHelper.getUserId()).getName());
        requirementsManagementLogs.setCustBsPerson(requirementsManagementLogsDTO.getCustBsPerson());
        requirementsManagementLogs.setCustBsPersonName(userRedisHelper.getUserById(requirementsManagementLogsDTO.getCustBsPerson()).getName());
        requirementsManagementLogs.setCustTecPerson(requirementsManagementLogsDTO.getCustTecPerson());
        requirementsManagementLogs.setCustTecPersonName(userRedisHelper.getUserById(requirementsManagementLogsDTO.getCustTecPerson()).getName());
        requirementsManagementLogs.setReqiurementsId(requirementsManagementLogsDTO.getReqiurementsId());
        requirementsManagementLogs.setReqOwnership(requirementsManagementLogsDTO.getReqOwnership());
        requirementsManagementLogs.setReqOwnershipName(deptRedisHelper.getDeptById(requirementsManagementLogsDTO.getReqOwnership()).getName());
        requirementsManagementLogs.setRemarkType(0);
        if (RequirementStatusEnum.DISTRIBUTION.getStatus().equals(requirementsManagementLogsDTO.getStatus())) {
            StringBuilder sb = new StringBuilder();
            sb.append(RemarkEnum.DISTRIBUTION.getDescription());
            sb.append(requirementsManagementLogs.getCustBsPersonName());
            sb.append("、");
            sb.append(requirementsManagementLogs.getCustTecPersonName());
            String result = sb.toString();
            requirementsManagementLogs.setRemark(result);
            return this.save(requirementsManagementLogs);

        } else if (RequirementStatusEnum.CONFIRMED.getStatus().equals(requirementsManagementLogsDTO.getStatus())) {
            requirementsManagementLogs.setRemark(RemarkEnum.CONFIRMED.getDescription());
            return this.save(requirementsManagementLogs);
        } else if (RequirementStatusEnum.REDISTRIBUTION.getStatus().equals(requirementsManagementLogsDTO.getStatus())) {
            requirementsManagementLogs.setRemark(RemarkEnum.REDISTRIBUTION.getDescription());
            return this.save(requirementsManagementLogs);
        } else {
            requirementsManagementLogs.setRemark(RemarkEnum.UNCONFIRMED.getDescription());
            return this.save(requirementsManagementLogs);
        }
    }

    @Override
    public Boolean remove(String id) throws Exception {
        requirementsManagementLogsMapper.deleteById(id);
        return true;
    }

    @Override
    public Page<RequirementsManagementLogsVO> pages(com.chinasie.orion.sdk.metadata.page.Page<RequirementsManagementLogsDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<RequirementsManagementLogs> requirementsWrapperX = new LambdaQueryWrapperX<>();
        requirementsWrapperX.eq(RequirementsManagementLogs::getReqiurementsId, pageRequest.getQuery().getReqiurementsId())
                .eq(RequirementsManagementLogs::getLogicStatus, 1);

        com.chinasie.orion.sdk.metadata.page.Page<RequirementsManagementLogs> realPageRequest =
                new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<RequirementsManagementLogs> page = this.getBaseMapper().selectPage(realPageRequest, requirementsWrapperX);
        List<RequirementsManagementLogs> list = page.getContent();
//        List<RequirementsManagementLogs> list = this.list(requirementsWrapperX);
        ArrayList<RequirementsManagementLogsVO> requirementsManagementLogsVOS = new ArrayList<>();

        String userId = currentUserHelper.getUserId();
        String code = userRedisHelper.getUserById(userId).getCode(); //工号
        list.forEach(item -> {
            RequirementsManagementLogsVO requirementsManagementLogsVO = new RequirementsManagementLogsVO();
            requirementsManagementLogsVO.setOperationUser(item.getOpUser());
            requirementsManagementLogsVO.setOperationUserName(item.getOpUserName());
            requirementsManagementLogsVO.setRemark(item.getRemark());
            requirementsManagementLogsVO.setFeedbackTime(item.getFeedbackTime());
            requirementsManagementLogsVO.setId(item.getId());
            requirementsManagementLogsVO.setReqOwnership(item.getReqOwnershipName());
            if (item.getOpUser() != null && item.getOpUser().equals(code)) {
                requirementsManagementLogsVO.setIsPerson(true);
            } else {
                requirementsManagementLogsVO.setIsPerson(false);
            }
            requirementsManagementLogsVO.setType(item.getRemarkType());
            requirementsManagementLogsVOS.add(requirementsManagementLogsVO);
        });
        com.chinasie.orion.sdk.metadata.page.Page<RequirementsManagementLogsVO> objectPage = new com.chinasie.orion.sdk.metadata.page.Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        objectPage.setContent(requirementsManagementLogsVOS);
        return objectPage;
    }


    @Override
    public boolean add(RequirementsManagementLogsDTO requirementsManagementLogsDTO) throws Exception {
        String reqiurementsId = requirementsManagementLogsDTO.getReqiurementsId();
        RequirementMangement requirementMangement = requirementMangementService.getById(reqiurementsId);
        String businessPerson = requirementMangement.getBusinessPerson();
        RequirementMangementDTO requirementMangementDTO = new RequirementMangementDTO();
        BeanCopyUtils.copyProperties(requirementMangement,requirementMangementDTO);
        if (ObjectUtil.isNotEmpty(businessPerson)) {
            mscBuildHandlerManager.send(requirementMangementDTO, RequirementNodeDict.NODE_REQUIREMENT_FEEDBACK, businessPerson);
        }
        RequirementsManagementLogs requirementsManagementLogs = new RequirementsManagementLogs();
        String userId = currentUserHelper.getUserId();
        requirementsManagementLogs.setOpUser(userRedisHelper.getUserById(userId).getCode());
        requirementsManagementLogs.setOpUserName(userRedisHelper.getUserById(userId).getName());
        requirementsManagementLogs.setRemark(requirementsManagementLogsDTO.getRemark());
        requirementsManagementLogs.setFeedbackTime(new Date());
        requirementsManagementLogs.setReqiurementsId(requirementsManagementLogsDTO.getReqiurementsId());
        String orgId = userRedisHelper.getSimpleUserById(userId).getOrgId();
        if (deptRedisHelper.getDeptById(orgId) == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "部门信息未找到！");
        } else {
            requirementsManagementLogs.setReqOwnership(deptRedisHelper.getDeptById(orgId).getOrgId());
        }
        requirementsManagementLogs.setReqOwnershipName(deptRedisHelper.getDeptById(orgId).getName());
        requirementsManagementLogs.setRemarkType(1);
        return this.save(requirementsManagementLogs);
    }
}
