<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const detailsData: Record<string, any> = inject('detailsData');
const planInfo = reactive({
  list: [
    {
      label: '大修轮次',
      field: 'repairRound',
    },
    {
      label: '大修类别',
      field: 'typeName',
    },
    {
      label: '所属基地',
      field: 'baseName',
    },
    {
      label: '计划开始时间',
      field: 'beginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划结束时间',
      field: 'endTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划工期',
      field: 'workDuration',
    },
    {
      label: '实际开始时间',
      field: 'actualBeginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际结束时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际工期',
      field: 'actualWorkDuration',
    },
    {
      label: '大修经理',
      field: 'repairManagerName',
    },
    {
      label: '计划状态',
      field: 'dataStatus',
    },
  ],
  dataSource: detailsData,
});

const progressInfo = reactive({
  list: [
    {
      label: '计划作业数',
      field: 'preJobNum',
    },
    {
      label: '新增作业数',
      field: 'saveJobNum',
    },
    {
      label: '总作业数',
      field: 'totalJobNum',
    },
    {
      label: '准备完成数',
      field: 'prepareFinishNum',
    },
    {
      label: '准备率',
      field: 'prepareRate',
    },
    {
      label: '已完成作业数',
      field: 'finishedNum',
    },
    {
      label: '完成率',
      field: 'finishRate',
    },
  ],
  dataSource: detailsData,
});

const personInfo = reactive({
  list: [
    {
      label: '计划入场总人数',
      field: 'preInputPersonNum',
    },
    {
      label: '实际入场人数',
      field: 'actInputPersonNum',
    },
    {
      label: '已离场人数',
      field: 'outPersonNum',
    },
  ],
  dataSource: detailsData,
});

const materialInfo = reactive({
  list: [
    {
      label: '工器具计划入场数',
      field: 'preToolInputNum',
    },
    {
      label: '工器具实际入场总数',
      field: 'actToolInputNum',
    },
    {
      label: '工器具已离场数',
      field: 'actToolOutNum',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="计划详情"
    :is-border="false"
    :grid-content-props="planInfo"
  />
  <BasicCard
    title="计划进展"
    :is-border="false"
    :grid-content-props="progressInfo"
  />
  <BasicCard
    title="大修人员信息"
    :is-border="false"
    :grid-content-props="personInfo"
  />
  <BasicCard
    title="大修物资信息"
    :is-border="false"
    :grid-content-props="materialInfo"
  />
</template>

<style scoped lang="less">

</style>
