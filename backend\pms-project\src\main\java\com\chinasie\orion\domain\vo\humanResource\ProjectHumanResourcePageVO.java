package com.chinasie.orion.domain.vo.humanResource;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * ProjectHumanResource VO对象
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@ApiModel(value = "ProjectHumanResourceVO对象", description = "人力资源统计统计分页")
@Data
public class ProjectHumanResourcePageVO extends ObjectVO implements Serializable{

        @ApiModelProperty(value = "人员ID")
        private String userId;

        @ApiModelProperty(value = "人员名称")
        private String userName;

        @ApiModelProperty(value = "部门")
        private String deptName;

        @ApiModelProperty(value = "岗位")
        private String jobName;

        @ApiModelProperty(value = "计划人天")
        private String planPersonDay;

        @ApiModelProperty(value = "实际人天")
        private String actualPersonDay;

        @ApiModelProperty(value = "当前实际饱和率")
        private String actualSaturationRate;

        @ApiModelProperty(value = "当前时间之前人员填报数数组")
        private Map<Date, Double> beforeTimePersonArray;

        @ApiModelProperty(value = "当前时间之后人员关联项目计划个数数组")
        private Map<Date, Integer> afterTimePersonArray;

}
