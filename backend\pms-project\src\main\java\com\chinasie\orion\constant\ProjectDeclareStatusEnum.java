package com.chinasie.orion.constant;



import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * ProjectDeclare 项目申报状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
public enum ProjectDeclareStatusEnum {
    CREATED(101,"已创建"),FLOWING(110,"流程中"),END(140,"已终止"),
    FINISH(130,"已生效"),REJECT(120,"已驳回");


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
    ProjectDeclareStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
