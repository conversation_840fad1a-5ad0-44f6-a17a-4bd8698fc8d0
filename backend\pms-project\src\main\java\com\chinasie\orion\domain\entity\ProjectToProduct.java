package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectToProduct Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
@TableName(value = "pmsx_project_to_product")
@ApiModel(value = "ProjectToProductEntity对象", description = "项目产品关联关系表")
@Data
public class ProjectToProduct extends ObjectEntity implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @TableField(value = "product_id")
    private String productId;

}
