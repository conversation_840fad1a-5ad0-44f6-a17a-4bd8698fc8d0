<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.eclipse.jetty</groupId>
    <artifactId>jetty-project</artifactId>
    <version>9.4.53.v20231009</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>jetty-util-ajax</artifactId>
  <name>Jetty :: Utilities :: Ajax(JSON)</name>
  <description>JSON/Ajax Utility classes for Jetty</description>
  <properties>
    <bundle-symbolic-name>${project.groupId}.util.ajax</bundle-symbolic-name>
    <spotbugs.onlyAnalyze>org.eclipse.jetty.util.ajax.*</spotbugs.onlyAnalyze>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.toolchain</groupId>
      <artifactId>jetty-test-helper</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
