package com.chinasie.orion.repository;


import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomePlanDataDTO;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanDataTotalVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * IncomePlanData Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@Mapper
public interface IncomePlanDataMapper extends  OrionBaseMapper  <IncomePlanData> {
    IncomePlanDataTotalVO getTotal(@Param("param") IncomePlanDataDTO param);

}

