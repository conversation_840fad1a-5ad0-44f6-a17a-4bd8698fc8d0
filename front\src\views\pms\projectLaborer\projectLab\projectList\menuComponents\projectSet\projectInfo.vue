<script setup lang="ts">
import {
  DataStatusTag, isPower, OrionTable, openModal, BasicButton,
} from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  computed, ComputedRef, h, inject, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { useRoute, useRouter } from 'vue-router';

import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import Locations from './Locations.vue';
import ProductInformation from './ProductInformation.vue';
import ModalAdd from './components/ModalAdd.vue';
import MainStory from './projectInfo/MainStory.vue';
import ProjectContribution from './projectInfo/ProjectContribution.vue';
import ProjectRewards from './projectInfo/ProjectRewards.vue';
import { relevanceProject, saveRelevanceProject } from '/@/views/pms/api/projectApproval';

const props = defineProps<{
    id: string
}>();
const route = useRoute();
const router = useRouter();
const projectId:any = route.query.id;
const projectData: ComputedRef<Record<string, any>> = inject('formData');
const detailAuthList = projectData.value?.detailAuthList || [];
const tableRef: Ref = ref();
const tableRefStand: Ref = ref();
const tableRefSource: Ref = ref();
const tableData: Ref<Record<string, any>[]> = ref([]);
const dataSource: ComputedRef<Record<string, any>> = computed(() => ({
  ...tableData.value?.[0] || {},
  ...projectData || {},
}));
const tableOptionsSource = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: true,
  api: async (params) => {
    params.query = {
      projectId,
    };
    const result = await new Api('/pms/new-project-to-base-plan/page').fetch(params, '', 'post');
    tableData.value = result || [];
    return tableData.value;
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'projectNumber',
    },
    {
      title: '名称',
      dataIndex: 'sourceName',
    },

    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '责任人',
      dataIndex: 'resUserName',
    },

    {
      title: '来源类型',
      dataIndex: 'sourceTypeName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_12_01_button_01', record.rdAuthList),
      onClick(record: Record<string, any>) {
        if (record.sourceType === 'plan') {
          router.push({
            name: 'ProjectApplicationDetail',
            params: {
              id: record?.basePlanId,
            },
          });
        } else if (record.sourceType === 'contract') {
          router.push({
            name: 'ContractManageDetail',
            query: {
              id: record?.basePlanId,
            },
          });
        } else if (record.sourceType === 'declare') {
          router.push({
            name: 'ScientificResearchDemandDeclarationDetail',
            query: {
              id: record?.basePlanId,
            },
          });
        } else if (record.sourceType === 'lead') {
          router.push({
            name: 'PASClueChangeDetails',
            params: {
              id: record?.basePlanId,
            },
          });
        } else if (record.sourceType === 'project') {
          router.push({
            name: 'MenuComponents',
            query: {
              id: record?.basePlanId,
            },
          });
        } else if (record.sourceType === 'businessOpportunity') {
          router.push({
            name: 'PASOpportunityDetails',
            params: {
              id: record.basePlanId,
            },
          });
        }
      },
    },
  ],
};
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  api: async () => {
    const result = await new Api('/pms/projectDeclare/getByProjectId').fetch({
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_12_01',
    }, props?.id, 'GET');
    if (result?.applyReason) {
      projectData.value.applyReason = result.applyReason;
    }
    tableData.value = result ? [result] : [];
    return tableData.value;
  },
  columns: [
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
    },
    {
      title: '责任部门',
      dataIndex: 'rspDeptName',
    },
    {
      title: '项目负责人',
      dataIndex: 'resUserName',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '申报发起日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_12_01_button_01', record.rdAuthList),
      onClick(record: Record<string, any>) {
        router.push({
          name: 'ProjectApplicationDetail',
          params: {
            id: record?.id,
          },
        });
      },
    },
  ],
};
const tableOptionsStand = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  api: async () => {
    const result = await new Api('/pms/projectInitiation/project/page').fetch({
      projectId: route.query.id,
      power: {
        pageCode: 'PMS0004',
        containerCode: 'PMS_XMXQ_container_12_01',
      },
    }, '', 'POST');
    tableData.value = result.content ? result.content : [];
    return tableData.value;
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'projectNumber',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_12_01_button_02', record.rdAuthList)) {
          return h('span', {
            class: 'action-btn',
            onClick: () => {
              router.push({
                name: 'ProjectInitiationDetail',
                params: {
                  id: record?.id,
                },
                query: {
                  projectId: record?.projectId,
                },
              });
            },
          }, text);
        }
        return text;
      },
    },
    {
      title: '名称',
      dataIndex: 'projectName',

    },
    {
      title: '类型',
      dataIndex: 'projectType',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_12_01_button_04', record.rdAuthList),
      onClick(record: Record<string, any>) {
        Modal.confirm({
          title: '删除提示',
          content: '是否删除该条数据？',
          onOk() {
            new Api('/pms').fetch('', `questionRelationRisk/relationRisk/deleteRisk/${record.id}`, 'DELETE').then(() => {
              message.success('删除成功');
              tableRefStand.value.reload();
            });
          },
        });
      },
    },
  ],
};
const basicInfo: Ref<any[]> = ref([
  {
    label: '项目编号',
    field: 'number',
  },
  {
    label: '项目名称',
    field: 'name',
  },
  {
    label: '项目开始日期',
    field: 'projectStartTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目结束日期',
    field: 'projectEndTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '责任部门',
    field: 'resDeptName',
  },
  {
    label: '项目负责人',
    field: 'resPersonName',
  },
  {
    label: '项目类型',
    field: 'projectTypeName',
  },
  {
    label: '项目子类型',
    field: 'projectSubTypeName',
    // 研发:project_type_research
    // 投资:invest_server
    hidden: computed(() => !['project_type_research', 'invest_server'].includes(dataSource.value?.projectSubType)),
  },
  {
    label: '业务方向',
    field: 'directionName',
    hidden: computed(() => !['project_type_research'].includes(dataSource.value?.projectSubType)),
  },
  {
    label: '产品类型',
    field: 'productTypeName',
    hidden: computed(() => !['project_type_research'].includes(dataSource.value?.projectSubType)),
  },
  {
    label: '研发类型',
    field: 'researchName',
    hidden: computed(() => !['project_type_research'].includes(dataSource.value?.projectSubType)),
  },
  {
    label: '项目级别',
    field: 'levelName',
    hidden: computed(() => !['project_type_research'].includes(dataSource.value?.projectSubType)),
  },
]);

const detailInfo: Ref<any[]> = ref([
  {
    label: '项目申报支持性材料',
    field: '',
    class: 'flex-ver',
    labelBold: true,
    slotName: 'fileTable',
  },
  {
    label: '项目背景摘要',
    field: 'projectBackground',
  },
  {
    label: '项目目标摘要',
    field: 'projectTarget',
  },
  {
    label: '技术方案摘要',
    field: 'technologyPlan',
  },
]);
const fileList: Ref<Array<{
    id: string,
    name: string,
    createUserName: string,
    createTime: string,
}>> = computed(() => tableData.value[0]?.materialList && tableData.value[0]?.materialList.map((item: Record<string, any>) => ({
  id: item.id,
  name: item.fullName,
  createUserName: item.ownerName,
  createTime: item.modifyTime,
})));

const handleRelevance = () => {
  const refDrawer = ref();
  openModal({
    title: '关联立项&策划',
    width: 1100,
    content(h) {
      return h(ModalAdd, {
        ref: refDrawer,
        projectId,
      });
    },
    async onOk() {
      const { isSelectedAndGetData } = refDrawer.value;
      const values = await isSelectedAndGetData();
      await saveRelevanceProject(values);
      tableRefStand.value.reload();
    },
  });
};
</script>

<template>
  <DetailsLayout
    border-bottom
    title="项目基本信息"
    :data-source="projectData"
    :list="basicInfo"
  />
  <!--  <DetailsLayout title="产品信息">-->
  <!--    <ProductInformation :project-id="projectId" />-->
  <!--  </DetailsLayout>-->
  <DetailsLayout title="项目地点">
    <Locations />
  </DetailsLayout>

  <DetailsLayout title="项目来源信息">
    <div style="height: 330px;overflow: hidden">
      <OrionTable
        ref="tableRefSource"
        :options="tableOptionsSource"
      />
    </div>
  </DetailsLayout>

  <!--  <DetailsLayout title="项目申报">-->
  <!--    <div style="height: 330px;overflow: hidden">-->
  <!--      <OrionTable-->
  <!--        ref="tableRef"-->
  <!--        :options="tableOptions"-->
  <!--      />-->
  <!--    </div>-->
  <!--  </DetailsLayout>-->
  <DetailsLayout title="项目立项/策划">
    <div style="height: 330px;overflow: hidden">
      <OrionTable
        ref="tableRefStand"
        :options="tableOptionsStand"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_12_01_button_03', detailAuthList)"
            type="primary"
            icon="add"
            @click="handleRelevance"
          >
            关联
          </BasicButton>
        </template>
      </OrionTable>
    </div>
  </DetailsLayout>
  <DetailsLayout title="主要事迹">
    <MainStory :project-id="projectId" />
  </DetailsLayout>
  <DetailsLayout title="项目贡献情况">
    <ProjectContribution :project-id="projectId" />
  </DetailsLayout>
  <DetailsLayout title="项目奖惩情况">
    <ProjectRewards :project-id="projectId" />
  </DetailsLayout>

  <!--  <DetailsLayout-->
  <!--    border-bottom-->
  <!--    title="项目详细信息"-->
  <!--    :column="1"-->
  <!--    :data-source="tableData[0]"-->
  <!--    :list="detailInfo"-->
  <!--  >-->
  <!--    <template #fileTable>-->
  <!--      <GridFileList-->
  <!--        :column="1"-->
  <!--        :list="fileList"-->
  <!--      />-->
  <!--    </template>-->
  <!--  </DetailsLayout>-->
</template>

<style scoped lang="less">

</style>
