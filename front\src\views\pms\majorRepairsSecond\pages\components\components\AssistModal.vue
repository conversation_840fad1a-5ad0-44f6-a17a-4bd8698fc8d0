<script setup lang="ts">
import { message, Table, Transfer } from 'ant-design-vue';
import {
  computed, h, onMounted, reactive, ref,
} from 'vue';
import Api from '/@/api';
import {
  BasicButton, Icon, treeMap, treeToList,
} from 'lyra-component-vue3';
import { filter } from '/@/utils/helper/treeHelper';
import { cloneDeep } from 'lodash-es';

const props = defineProps<{
  record: Record<string, any>
  repairRound: string
}>();

const leftDataSource = ref<any[]>([]);
const rightHasIds = ref<string[]>([]);
const expandedRowKeys = ref<string[]>([]);
const loading = ref<boolean>(false);
const rowKey = (row) => row?.data?.id;

const rightDataSource = computed(() => {
  const leftToRight = treeToList(cloneDeep(leftDataSource.value)).filter((item) => item.rightSelected && item?.data?.nodeType)?.map((item) => {
    delete item.children;
    return item;
  });
  const set = new Set() as any;
  return filter(leftToRight, (node) => !set.has(node?.data?.id) && set.add(node?.data?.id));
});

const rightOrderList = computed(() => treeToList(rightDataSource.value).filter((item) => ['executionSpecialty', 'specialtyTeam'].includes(item?.data?.nodeType)));

async function getTree() {
  loading.value = true;
  try {
    await getHasList();
    const result = await new Api('/pms/majorRepairOrg/major/tree').fetch({
      majorRepairOrgIds: props?.record?.parentId ? [props?.record?.parentId] : [],
      repairRound: props.repairRound,
    }, '', 'POST');
    expandedRowKeys.value = result?.parenIdList || [];
    leftDataSource.value = treeMap(result ? result?.treeNodeVOList : [], {
      conversion: (node) => {
        if (rightHasIds.value.includes(node?.data?.id)) {
          node.rightSelected = true;
        }
        return node;
      },
    });
  } finally {
    loading.value = false;
  }
}

async function getHasList() {
  const result = await new Api('/pms/majorRepairOrg/job/major/list').fetch({
    jobNumberList: [props?.record?.data?.jobNumber],
    repairRound: props.repairRound,
  }, '', 'POST');
  rightHasIds.value = result || [];
  selectedKeysAll.left = result || [];
}

onMounted(() => {
  getTree();
});

const columns = [
  {
    title: '组织架构',
    dataIndex: ['data', 'name'],
  },
  {
    title: '责任人',
    dataIndex: ['data', 'rspUserName'],
    width: 100,
  },
];

function expandIcon({ expanded, record, onExpand }) {
  if (!record?.children?.length) {
    return h('span', {
      style: {
        display: 'inline-block',
        marginRight: '8px',
        marginTop: '2px',
        width: '16px',
      },
    });
  }
  if (expanded) {
    return h(Icon, {
      onClick(e) {
        onExpand(record, e);
      },
      style: {
        cursor: 'pointer',
        width: '16px',
        height: '16px',
        marginRight: '8px',
        marginTop: '2px',
      },
      icon: 'fa-angle-down',
      size: 20,
    });
  }
  return h(Icon, {
    onClick(e) {
      onExpand(record, e);
    },
    style: {
      cursor: 'pointer',
      width: '16px',
      height: '16px',
      marginRight: '8px',
      marginTop: '2px',
    },
    icon: 'fa-angle-right',
    size: 20,
  });
}

const selectedKeysAll = reactive({
  left: [],
  right: [],
});

const getRowSelection = ({
  direction,
  onItemSelect,
}: Record<string, any>) => ({
  hideSelectAll: direction === 'left',
  getCheckboxProps: (item: any) => ({
    disabled: !item?.data?.nodeType || (direction === 'left' && item?.rightSelected),
  }),
  onSelect({ data }: any, selected: boolean, selectedNodes: any[]) {
    selectedKeysAll[direction] = selectedNodes.map((item) => item?.data?.id);
    onItemSelect(data?.id, selected);
  },
  onSelectAll(selected: boolean, selectedNodes: any[]) {
    selectedKeysAll[direction] = selectedNodes.map((item) => item?.data?.id);
    selectedKeysAll[direction].forEach((id) => {
      onItemSelect(id, selected);
    });
  },
  selectedRowKeys: selectedKeysAll[direction],
});

function handleChange() {
  treeMap(leftDataSource.value, {
    conversion: (node) => {
      if (selectedKeysAll.left.includes(node?.data?.id)) {
        node.rightSelected = true;
      }
      return node;
    },
  });
}

const leftKeyword = ref('');
const rightKeyword = ref('');

function handleSearch(direction: string, keyword: string) {
  if (direction === 'left') {
    leftKeyword.value = keyword;
  } else {
    rightKeyword.value = keyword;
  }
}

function format(direction: string) {
  switch (direction) {
    case 'left':
      return filter(leftDataSource.value, (item) => filterOption(leftKeyword.value, item));
    case 'right':
      return filter(rightDataSource.value, (item) => filterOption(rightKeyword.value, item));
  }
}

function handleBatchDelete() {
  if (selectedKeysAll.right.length === 0) {
    return message.info('请选择需要删除的数据');
  }
  treeMap(leftDataSource.value, {
    conversion: (node) => {
      if (selectedKeysAll.right.includes(node?.data?.id)) {
        delete node.rightSelected;
      }
      return node;
    },
  });
  selectedKeysAll.left = [];
  selectedKeysAll.right = [];
}

function filterOption(inputValue, option) {
  return option?.data?.name?.indexOf(inputValue) > -1;
}

defineExpose({
  confirm() {
    return new Promise((resolve, reject) => {
      const addMajorRepairOrgIds = rightDataSource.value.filter((item) => !rightHasIds.value.includes(item?.data?.id)).map((item) => item?.data?.id);
      const delMajorRepairOrgIds = rightHasIds.value.filter((id) => !rightDataSource.value.includes(id));
      if (addMajorRepairOrgIds.length === 0 && delMajorRepairOrgIds.length === 0) {
        message.info('暂无需要保存的数据');
        return reject();
      }
      const params = {
        addMajorRepairOrgIds,
        delMajorRepairOrgIds,
        jobNumber: props?.record?.data?.jobNumber,
      };
      new Api('/pms/relationOrgToJob/assist/add/batch').fetch(params, '', 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});
</script>

<template>
  <div
    class="transfer-wrapper"
  >
    <div class="custom-header left">
      待选择专业
    </div>
    <div class="custom-header right">
      已选择专业（{{ rightOrderList.length }}）
    </div>
    <div class="right-buttons">
      <BasicButton
        type="link"
        @click="handleBatchDelete"
      >
        批量删除
      </BasicButton>
    </div>

    <Transfer
      :data-source="[]"
      :show-search="true"
      :oneWay="true"
      :rowKey="rowKey"
      :filter-option="filterOption"
      :show-select-all="false"
      @change="handleChange"
      @search="handleSearch"
    >
      <template
        #children="{
          direction,
          onItemSelect,
        }"
      >
        <Table
          v-model:expandedRowKeys="expandedRowKeys"
          :row-selection="getRowSelection({
            direction,
            onItemSelect,
          })"
          bordered
          :rowKey="rowKey"
          :loading="loading"
          :scroll="{y:380}"
          :pagination="false"
          :expandIcon="expandIcon"
          :columns="columns"
          :data-source="format(direction)"
          size="small"
        />
      </template>
    </Transfer>
  </div>
</template>

<style scoped lang="less">
.transfer-wrapper {
  position: relative;
  padding: 12px 20px;

  .custom-header {
    position: absolute;
    top: 20px;
    left: 32px;
    z-index: 1;

    &.right {
      left: calc(50% + 32px);
    }
  }

  .right-buttons {
    position: absolute;
    top: 16px;
    right: 20px;
    z-index: 1;
  }
}

:deep(.ant-transfer-customize-list .ant-transfer-list) {
  width: 0;
}

:deep(.ant-transfer-list-header-selected) {
  display: none;
}
</style>
