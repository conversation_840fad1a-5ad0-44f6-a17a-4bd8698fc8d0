ALTER TABLE `pmsx_quotation_management`
    MODIFY COLUMN `quote_amt` decimal(20, 6) NULL DEFAULT NULL COMMENT '报价金额' ,
    MODIFY COLUMN `floor_price` decimal(20, 6) NULL DEFAULT NULL COMMENT '底线价格' ;

ALTER TABLE `pms_market_contract`
    MODIFY COLUMN `contract_amt` decimal(20, 6) NULL DEFAULT NULL COMMENT '合同金额' ,
    MODIFY COLUMN `quality_amt` decimal(20, 6) NULL DEFAULT NULL COMMENT '质保金额' ,
    MODIFY COLUMN `frame_contract_amt` decimal(23, 6) NULL DEFAULT NULL COMMENT '框架合同金额' ;

ALTER TABLE `pmsx_project_order`
    MODIFY COLUMN `order_surcharge` decimal(20, 6) NULL DEFAULT NULL COMMENT '附加费' ;

ALTER TABLE `pmsx_project_invoice`
    MODIFY COLUMN `total_order_amount_tax` decimal(20, 6) NULL DEFAULT NULL COMMENT '订单总金额（含税含费）',
    MODIFY COLUMN `total_order_amount` decimal(20, 6) NULL DEFAULT NULL COMMENT '订单不含税总金额';


ALTER TABLE `pms_requirement_mangement`
    MODIFY COLUMN `cust_person_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称' AFTER `cust_person`,
    ADD COLUMN `cust_con_person_name` varchar(64) NULL COMMENT '客户主要联系人名称' AFTER `cust_con_person`;
