package com.chinasie.orion.domain.vo.projectscheme;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * ImportExcelErrorNoteVO
 *
 * @author: yangFy
 * @date: 2023/4/20 15:00
 * @description:
 * <p>
 * 项目计划导入错误结果VO
 * </p>
 */
@Data
@ApiModel(value = "ImportExcelNoteVO对象", description = "项目计划导入错误结果VO")
public class ImportExcelNoteVO {
    @ApiModelProperty(value = "序号")
    private String order;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目计划名称")
    private String name;

    @ApiModelProperty(value = "错误信息列表")
    private List<String> errorNotes;

    public ImportExcelNoteVO() {
    }

    public ImportExcelNoteVO(List<String> errorNotes) {
        this.errorNotes = errorNotes;
    }

    public ImportExcelNoteVO(String order, String projectName, String name, List<String> errorNotes) {
        this.order = order;
        this.projectName = projectName;
        this.name = name;
        if (CollectionUtils.isEmpty(errorNotes)){
            errorNotes=Arrays.asList("导入成功");
        }
        this.errorNotes=errorNotes;
    }
}
