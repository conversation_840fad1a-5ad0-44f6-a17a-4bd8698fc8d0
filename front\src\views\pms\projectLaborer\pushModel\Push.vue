<template>
  <a-drawer
    v-if="visibleChild"
    v-model:visible="visibleChild"
    class="ui-2-0"
    title="推送知识"
    :width="450"
    :body-style="bodyStyle"
    :mask-closable="false"
    :mask="false"
  >
    <div
      v-show="!detail.visible"
      v-loading="loading"
      style="position: relative"
    >
      <a-tabs
        v-model:activeKey="form.pushType"
        @change="handleTabs"
      >
        <a-tab-pane
          v-for="s in paneList"
          :key="s.id"
          :tab="s.name"
        />
      </a-tabs>
      <div class="m-2">
        <a-input-search
          v-model:value="form.keyWord"
          placeholder="请输入内容"
          allow-clear
          @search="searchTableData"
        />
      </div>

      <div class="divide-y divide-current divide-dashed mb-2">
        <div
          v-for="s in dataSource.content"
          :key="s.id"
          class="p-4 cursor-pointer hover:bg-blue-100"
          @click="openDetail(s.id)"
        >
          <div class="mb-2 text-lg font-normal">
            {{ s.name }}
          </div>
          <div
            class="mb-2"
            style="color: #acb2bf"
          >
            <span>作者：{{ s.author }}</span>
            <span class="ml-2">发布时间：{{ formatDefault(s.createTime) }}</span>
          </div>
          <div
            class="flex-te2"
            style="color: #686f8b"
          >
            {{ s.summary || '暂无描述' }}
          </div>
        </div>
      </div>
      <Pagination
        v-model:current="form.pageNum"
        v-model:pageSize="form.pageSize"
        class="align-right"
        :total="dataSource.totalSize"
        @query="getPage"
      />
    </div>
    <Detail
      v-if="detail.visible"
      :data="detail"
    />
  </a-drawer>
</template>

<script>
import { computed, reactive, toRefs } from 'vue';
import { Drawer, Tabs, Input } from 'ant-design-vue';
import Pagination from '/@/views/pms/projectLaborer/knowledgeEditData/Pagination.vue';
import Api from '/@/api';
import { handleDetailController } from '/@/views/pms/projectLaborer/utils';
import dayjs from 'dayjs';
import Detail from './Detail.vue';

export default {
  name: 'Push',
  components: {
    Detail,
    Pagination,
    ATabs: Tabs,
    AInputSearch: Input.Search,
    ATabPane: Tabs.TabPane,
    ADrawer: Drawer,
  },
  props: {
    visible: Boolean,
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const state = reactive({
      loading: false,
      isShow: true,
      searchValue: undefined,
      detail: {
        visible: false,
        id: undefined,
        form: {},
      },
      dataSource: {
        content: [],
        totalSize: 0,
      },
      form: {
        keyWord: undefined,
        pageNum: 1,
        pageSize: 10,
        pushType: '',
      },
      paneList: [],
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 55px)',
        padding: '15px',
      },
      visibleChild: computed({
        get() {
          return props.visible;
        },
        set(val) {
          emit('update:visible', val);
        },
      }),
    });
    function searchTableData() {
      getPage();
    }
    function handleTabs() {
      state.form.keyWord = undefined;
      getPage();
    }
    function getPage() {
      // state.loading = true;
      // new Api('/kms')
      //   .fetch(state.form, 'policy/service/page', 'POST')
      //   .then((res) => {
      //     state.dataSource = res;
      //     state.loading = false;
      //   })
      //   .catch((_) => {
      //     state.loading = false;
      //   });
    }

    function formatDefault(val) {
      return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
    function openDetail(id) {
      state.loading = true;
      new Api('/kms/knowledgeInfo/detail')
        .fetch('', id, 'GET')
        .then((res) => {
          state.loading = false;
          state.detail = {
            visible: true,
            id,
            form: res,
          };
        })
        .catch((_) => {
          state.loading = false;
        });
    }

    function init() {
      // new Api('/kms').fetch('', 'policy/dict/service/list', 'GET').then((res) => {
      //   state.paneList = res || [];
      //   if (res && res.length) {
      //     state.form.pushType = res[0].id;
      //     getPage();
      //   }
      // });
    }
    init();

    return {
      ...toRefs(state),
      searchTableData,
      getPage,
      handleTabs,
      handleDetailController,
      openDetail,
      formatDefault,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.ant-tabs-nav) .ant-tabs-tab {
    margin: 0 10px 0 0;
  }
</style>
