//package com.chinasie.orion.domain.entity;
//
///**
// * @author: yk
// * @date: 2023/5/25 16:32
// * @description: 项目计划关联合同信息
// */
//
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import com.chinasie.orion.sdk.annotation.*;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.lang.String;
//
///**
// * ProjectSchemeContract Entity对象
// *
// * <AUTHOR>
// * @since 2023-05-25 16:26:04
// */
//@TableName(value = "pms_project_scheme_contract", code = "pmtr", type = "common", defaultIdValue = false)
//@ApiModel(value = "ProjectSchemeContract对象", description = "项目计划关联合同")
//@Data
//public class ProjectSchemeContract extends ObjectEntity implements Serializable{
//
//    /**
//     * 项目计划id
//     */
//    @ApiModelProperty(value = "项目计划id")
//    @TableField(value = "project_scheme_id" )
//    private String projectSchemeId;
//
//    /**
//     * 合同编号
//     */
//    @ApiModelProperty(value = "合同编号")
//    @TableField(value = "contract_id" )
//    private String contractId;
//
//}
//
