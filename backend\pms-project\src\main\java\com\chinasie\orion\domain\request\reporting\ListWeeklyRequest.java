package com.chinasie.orion.domain.request.reporting;

import com.chinasie.orion.constant.reporting.TimeSearchTypeEnums;
import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "周报列表参数", description = "周报列表请求参数")
public class ListWeeklyRequest extends ProjectWeeklyDTO implements Serializable {

    @ApiModelProperty(value = "时间类型", required = true, example = "ALL")
    private TimeSearchTypeEnums timeType;

    @ApiModelProperty(value = "开始时间", required = true, example = "2021-01-01")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", required = true, example = "2021-01-01")
    private Date endTime;

    @ApiModelProperty(value = "负责人", required = true, example = "jkdsafa1231231")
    private String resp;

    @ApiModelProperty(value = "项目id", required = true)
    private List<String> projectIds = new ArrayList<>();
    @ApiModelProperty(value = "页面类型 false- 普通页面 true-审核页面 默认为false", required = true, example = "")
    private Boolean pageType= Boolean.FALSE;

    @ApiModelProperty(value = "关键字", required = true, example = "关键词")
    private String keyword;

    private List<String> idList;
}
