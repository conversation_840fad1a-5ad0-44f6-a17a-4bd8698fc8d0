<script setup lang="ts">
import {
  BasicCard, OrionTable,
} from 'lyra-component-vue3';
import { h, reactive, ref } from 'vue';
import { STableSummaryRow, STableSummaryCell } from '@surely-vue/table';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const dataSource = ref([]);

const tableOptions = {
  rowSelection: {},
  showToolButton: false,
  isSpacing: false,
  isFilter2: false,
  api: null,
  showSmallSearch: false,
  showIndexColumn: false,
  pagination: false,
  showTableSetting: false,
  columns: [
    {
      title: '年份',
      dataIndex: 'year',
      width: 100,
    },
    {
      title: '凭证号',
      dataIndex: 'voucherNumber',
      width: 100,
    },
    {
      title: '挂账金额',
      dataIndex: 'accruedAmt',
      isMoney: true,
    },
    {
      title: '已冲销金额',
      dataIndex: 'clearedAmt',
      isMoney: true,
    },
    {
      title: '剩余未冲销金额',
      dataIndex: 'unAdvReceivableAmt',
      isMoney: true,
    },
    {
      title: '本次冲销金额',
      dataIndex: 'currentClearAmtExTax',
      isMoney: true,
    },
    {
      title: '本次冲销金额（不含税）',
      dataIndex: 'currentClearAmt',
      isMoney: true,
      width: 200,
    },
    {
      title: '不冲销原因',
      dataIndex: 'noClearReason',
    },
  ],
};

const balanceTableRef = ref();
const state = reactive({
  accruedAmt: 0,
  clearedAmt: 0,
  unAdvReceivableAmt: 0,
  currentClearAmtExTax: 0,
  currentClearAmt: 0,
});

/**
 * 计算并设置表格数据的总计
 * @param data - 表格数据
 */
function setFnTotal(data) {
  let accruedAmt = 0;
  let clearedAmt = 0;
  let unAdvReceivableAmt = 0;
  let currentClearAmtExTax = 0;
  let currentClearAmt = 0;

  data.forEach((item) => {
    // 计算总额
    accruedAmt += Number(item.accruedAmt || 0); // 确保转换为数字
    clearedAmt += Number(item.clearedAmt || 0);
    unAdvReceivableAmt += Number(item.unAdvReceivableAmt || 0);
    currentClearAmtExTax += Number(item.currentClearAmtExTax || 0);
    currentClearAmt += Number(item.currentClearAmt || 0);
  });
  state.accruedAmt = accruedAmt;
  state.clearedAmt = clearedAmt;
  state.unAdvReceivableAmt = unAdvReceivableAmt;
  state.currentClearAmtExTax = currentClearAmtExTax;
  state.currentClearAmt = currentClearAmt;
}

/**
 * 设置表格数据并计算总计
 * @param data - 表格数据
 */
function setValues(data) {
  if (data.length > 0) {
    setFnTotal(data);
    dataSource.value = data;
  }
}

defineExpose({
  setValues,
});
</script>

<template>
  <BasicCard title="收入计提信息：">
    <OrionTable
      ref="balanceTableRef"
      :dataSource="dataSource"
      :options="tableOptions"
      summary-fixed
    >
      <template #summary>
        <div class="fix-summary">
          <div>
            <span class="red">*</span> 合计栏：
          </div>
          <div class="rq1">
            {{ state.accruedAmt ? state.accruedAmt.toFixed(2) : '0.00' }}
          </div>
          <div class="rq2">
            {{ state.clearedAmt ? state.clearedAmt.toFixed(2) : '0.00' }}
          </div>
          <div class="rq3">
            {{ state.unAdvReceivableAmt ? state.unAdvReceivableAmt.toFixed(2) : '0.00' }}
          </div>
          <div class="rq4">
            {{ state.currentClearAmtExTax ? state.currentClearAmtExTax.toFixed(2) : '0.00' }}
          </div>
          <div class="rq5">
            {{ state.currentClearAmt ? state.currentClearAmt.toFixed(2) : '0.00' }}
          </div>
        </div>
      </template>
    </OrionTable>
  </BasicCard>
</template>

<style scoped lang="less">
.fix-summary{
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.rq1{
  position: absolute;
  left: 259px;
}
.rq2{
  position: absolute;
  left: 420px;
}
.rq3{
  position: absolute;
  left: 580px;
}
.rq4{
  position: absolute;
  left: 740px;
}
.rq5{
  position: absolute;
  left: 900px;
}
</style>
