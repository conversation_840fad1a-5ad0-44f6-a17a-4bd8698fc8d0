package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/11/15 18:10
 * @description:
 */
@ApiModel(value = "WorkHourFillInfoVO对象", description = "工时填报详细信息")
@Data
public class WorkHourFillInfoVO extends  WorkHourFillVO{
    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;

    /**
     * 所在部门名称
     */
    @ApiModelProperty(value = "所在部门名称")
    private String orgName;


    /**
     * 成员角色
     */
    @ApiModelProperty(value = "成员角色")
    private String memberRoleName;

    /**
     * 记录
     */
    @ApiModelProperty(value = "记录")
    List<WorkHourFillDayInfoVO> dayList;
}
