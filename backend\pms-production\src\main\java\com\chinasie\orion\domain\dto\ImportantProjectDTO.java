package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ImportantProject DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
@ApiModel(value = "ImportantProjectDTO对象", description = "重大项目")
@Data
@ExcelIgnoreUnannotated
public class ImportantProjectDTO extends  ObjectDTO   implements Serializable{

    /**
     * 重大项目名称
     */
    @ApiModelProperty(value = "重大项目名称")
    @ExcelProperty(value = "重大项目名称 ", index = 0)
    private String projectName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @ExcelProperty(value = "负责人id ", index = 1)
    private String rspUserId;

    @ApiModelProperty(value = "负责人工号")
    private String rspUserCode;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "负责人姓名")
    @ExcelProperty(value = "负责人姓名 ", index = 2)
    private String rspUserName;

    /**
     * 负责人所在部门
     */
    @ApiModelProperty(value = "负责人所在部门")
    @ExcelProperty(value = "负责人所在部门 ", index = 3)
    private String deptId;


    /**
     * 负责人所在部门名称
     */
    @ApiModelProperty(value = "负责人所在部门名称")
    private String deptName;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @ExcelProperty(value = "计划开始时间 ", index = 4)
    private Date planStart;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @ExcelProperty(value = "计划完成时间 ", index = 5)
    private Date planEnd;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @ExcelProperty(value = "实际开始时间 ", index = 6)
    private Date actureStart;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    @ExcelProperty(value = "实际完成时间 ", index = 7)
    private Date actureEnd;

    /**
     * 作业id列表
     */
    @ApiModelProperty(value = "作业id列表")
    private List<String> jobIdList;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

}
