package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import java.util.Map;

/**
 * ProjectCollection VO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@ApiModel(value = "ProjectCollectionVO对象", description = "项目集")
@Data
public class ProjectCollectionVO extends ObjectVO implements Serializable{

            /**
         * 项目集名称
         */
        @ApiModelProperty(value = "项目集名称")
        private String name;

        /**
         * 项目可见人员
         */
        @ApiModelProperty(value = "项目可见人员")
        private String relatedPerson;

        @ApiModelProperty(value = "项目可见人员名称")
        List<Map> relatedPersonList;

        /**
         * 项目责任人
         */
        @ApiModelProperty(value = "项目责任人")
        private String resPerson;

        @ApiModelProperty(value = "项目责任人")
        private String resPersonName;

        /**
         * 责任部门
         */
        @ApiModelProperty(value = "责任部门")
        private String resDept;

        @ApiModelProperty(value = "责任部门")
        private String resDeptName;

        @ApiModelProperty(value = "项目集级别")
        private String projectCollectionLevel;

        @ApiModelProperty(value = "项目集级别")
        private String projectCollectionLevelName;

        @ApiModelProperty(value = "项目集类型")
        private String projectCollectionType;

        @ApiModelProperty(value = "项目集类型")
        private String projectCollectionTypeName;

        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        @ApiModelProperty(value = "树id")
        private String treeId;

        /**
         * 排序
         */
        @ApiModelProperty(value = "排序")
        private Integer sort;

        @ApiModelProperty(value = "项目信息")
        List<ProjectVO> projectVOList;

    }
