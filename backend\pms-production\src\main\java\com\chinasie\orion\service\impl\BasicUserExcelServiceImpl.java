package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.dto.BasicUserTemplateDownloadExcelDTO;
import com.chinasie.orion.domain.dto.UserDTO;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.BasicUserLedger;
import com.chinasie.orion.domain.request.BasicUserExportRequest;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.BasicUserMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DeptUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.BasicUserExcelService;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.PmiUserFeignService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/9
 */
@Service
@Slf4j
public class BasicUserExcelServiceImpl implements BasicUserExcelService {

    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private BasicUserMapper basicUserMapper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private DeptUserDOMapper deptUserDOMapper;
    @Autowired
    private DeptUserHelper deptUserHelper;
    @Resource
    private PmiUserFeignService pmiUserFeignService;
    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Override
    public void exportExcel(BasicUserExportRequest request, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
        condition.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
        condition.eq(BasicUser::getUserType, 1);
        if (!CollectionUtils.isEmpty(request.getIds())) {
            condition.in(BasicUser::getId, request.getIds());
        }
        if(StringUtils.hasText(request.getState())){
            if("离岗".equals(request.getState())){
                condition.eq(BasicUser::getUserStatus,"离职");
            }else{
                condition.ne(BasicUser::getUserStatus,"离职");
            }

        }
        List<BasicUser> basicUserList = basicUserMapper.selectList(condition);
        List<BasicUserVO> vos = BeanCopyUtils.convertListTo(basicUserList, BasicUserVO::new);
        basicUserService.setEveryName(vos);

        /**
         * 导出
         * */
        ExcelUtils.write(response, "技术支持人员.xlsx", "技术支持人员", BasicUserVO.class, vos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importExcel(String importId) throws IOException {
        List<BasicUserTemplateDownloadExcelDTO> userTemplateDownloadExcelDTOS = (List<BasicUserTemplateDownloadExcelDTO>) orionJ2CacheService.get("pmsx::BasicUser-import::id", importId);
        log.info("技术支持人员导入的入库数据={}", JSONUtil.toJsonStr(userTemplateDownloadExcelDTOS));
        //保存数据
        String maxCode = "000000";
        try {
            LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
            condition.select("MAX(CAST(SUBSTRING(user_code, 2) AS UNSIGNED)) AS userCode");
            condition.eq(BasicUser::getUserType, 1);
            BasicUser one = basicUserService.getOne(condition);
            if(one != null){
                maxCode = String.format("%06d",Integer.parseInt(one.getUserCode()));
            }
        }catch (Exception e){
            log.error("获取最大编码失败",e);
        }

        List<BasicUser> basicUsers = Lists.newArrayList();
        for(int i = 0;i<userTemplateDownloadExcelDTOS.size();i++){
            BasicUser basicUser = new BasicUser();
            basicUser.setIdCard(userTemplateDownloadExcelDTOS.get(i).getIdCard());
            basicUser.setUserCode("F"+String.format("%06d",Integer.parseInt(maxCode)+i+1));
            basicUser.setFullName(userTemplateDownloadExcelDTOS.get(i).getFullName());
            basicUser.setCompanyCode(userTemplateDownloadExcelDTOS.get(i).getCompanyCode());
            basicUser.setCompanyName(userTemplateDownloadExcelDTOS.get(i).getCompanyName());
            basicUser.setSex(userTemplateDownloadExcelDTOS.get(i).getSex());
            basicUser.setNation(userTemplateDownloadExcelDTOS.get(i).getNation());
            if(StringUtils.hasText(userTemplateDownloadExcelDTOS.get(i).getDateOfBirth())){
                basicUser.setDateOfBirth(DateUtil.parse(userTemplateDownloadExcelDTOS.get(i).getDateOfBirth(),"yyyy/MM/dd"));
            }
            basicUser.setPoliticalAffiliation(userTemplateDownloadExcelDTOS.get(i).getPoliticalAffiliation());
            if(StringUtils.hasText(userTemplateDownloadExcelDTOS.get(i).getJoinWorkTime())){
                basicUser.setJoinWorkTime(DateUtil.parse(userTemplateDownloadExcelDTOS.get(i).getJoinWorkTime(),"yyyy/MM/dd"));
            }
            basicUser.setHomeTown(userTemplateDownloadExcelDTOS.get(i).getHomeTown());
            basicUser.setBirthPlace(userTemplateDownloadExcelDTOS.get(i).getBirthPlace());
            if(StringUtils.hasText(userTemplateDownloadExcelDTOS.get(i).getAddUnitTime())){
                basicUser.setAddUnitTime(DateUtil.parse(userTemplateDownloadExcelDTOS.get(i).getAddUnitTime(),"yyyy/MM/dd"));
            }
            basicUser.setDeptCode(userTemplateDownloadExcelDTOS.get(i).getDepartmentCode());
            basicUser.setDeptName(userTemplateDownloadExcelDTOS.get(i).getDepartmentName());
            basicUser.setDeptId(userTemplateDownloadExcelDTOS.get(i).getDepartmentId());
            basicUser.setInstituteCode(userTemplateDownloadExcelDTOS.get(i).getInstituteCode());
            basicUser.setInstituteName(userTemplateDownloadExcelDTOS.get(i).getInstituteName());
            basicUser.setPhone(userTemplateDownloadExcelDTOS.get(i).getPhone());
            if(StringUtils.hasText(userTemplateDownloadExcelDTOS.get(i).getAddWorkTime())){
                basicUser.setAddWorkTime(DateUtil.parse(userTemplateDownloadExcelDTOS.get(i).getAddWorkTime(),"yyyy/MM/dd"));
            }
            basicUser.setPersonType(BasicUserEnum.TECHNICAL.getType());
            basicUser.setPersonnelNature("在职");
            basicUser.setUserStatus("在职");
            basicUser.setUserType(1);
            basicUser.setStatus(1);
            basicUser.setPlatformId("ykovb40e9fb1061b46fb96c4d0d3333dcc13");
            basicUser.setOrgId(CurrentUserHelper.getOrgId());
            basicUsers.add(basicUser);
        }

        //人员关系
        this.personnelRelations(basicUsers);
        //缓存刷新
        List<String> userIds = basicUsers.stream().map(BasicUser::getUserId).collect(Collectors.toList());
        pmiUserFeignService.batchSysUser(userIds);

        basicUserService.saveBatch(basicUsers);

        orionJ2CacheService.delete("pmsx::BasicUser-import::id", importId);
        return true;
    }

    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws IOException {
        ImportExcelCheckResultVO resultVO = new ImportExcelCheckResultVO();
        InputStream inputStream = file.getInputStream();
        UserExcelReadListener taskExcelReadListener = new UserExcelReadListener();
        EasyExcel.read(inputStream, BasicUserTemplateDownloadExcelDTO.class, taskExcelReadListener).sheet().headRowNumber(1).doRead();
        List<BasicUserTemplateDownloadExcelDTO> userTemplateDownloadExcelDTOS = taskExcelReadListener.getUserExcelDTOS();
        if (CollectionUtils.isEmpty(userTemplateDownloadExcelDTOS)) {
            resultVO.setOom("非正确模板，无法解析数据");
            resultVO.setCode(4000);
            return resultVO;
        }
        if (userTemplateDownloadExcelDTOS.size() > 1000) {
            resultVO.setOom("最多导入1000条");
            resultVO.setCode(400);
            return resultVO;
        }
        //校验
        checkExcel(userTemplateDownloadExcelDTOS,resultVO);
        if(resultVO.getCode() != null){
            return resultVO;
        }
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BasicUser-import::id", importId, userTemplateDownloadExcelDTOS, 24 * 60 * 60);
        resultVO.setCode(200);
        resultVO.setSucc(importId);
        return resultVO;
    }

    @Override
    public Boolean importCancelByExcel(String importId) {
        orionJ2CacheService.delete("pmsx::BasicUser-import::id", importId);
        return true;
    }

    private void personnelRelations(List<BasicUser> basicUsers) {
        List<UserDTO> userDOList = Lists.newArrayList();
        for(BasicUser basicUser:basicUsers){
            UserDTO user = new UserDTO();
            user.setCode(basicUser.getUserCode());
            user.setName(basicUser.getFullName());
            user.setMobile(basicUser.getPhone());
            user.setUsername(basicUser.getUserCode());
            user.setCardNo(basicUser.getIdCard());
            user.setSex(basicUser.getSex());
            user.setStatus(1);
            user.setPassword("orion2023!@#$");
            user.setClassName("User");
            user.setUserType("A");
            userDOList.add(user);
        }
        ResponseDTO<List<UserDTO>> listResponseDTO = pmiUserFeignService.batchCreate(userDOList);
        Map<String,List<UserDTO>> userMap = listResponseDTO.getResult().stream().collect(Collectors.groupingBy(UserDTO::getCode));
        for(BasicUser basicUser:basicUsers){
            basicUser.setUserId(userMap.get(basicUser.getUserCode()).get(0).getId());
        }

        //组织关系
        List<DeptUserDO> deptUserDOList = Lists.newArrayList();
        List<DeptVO> deptVOS = deptRedisHelper.listAllDept();
        Map<String,List<DeptVO>> departmentMap = deptVOS.stream()
                .collect(Collectors.groupingBy(DeptVO::getDeptCode));
        for(BasicUser basicUser:basicUsers){
            DeptUserDO deptUserDO = new DeptUserDO();
            deptUserDO.setDeptId(departmentMap.get(basicUser.getDeptCode()).get(0).getId());
            deptUserDO.setUserId(basicUser.getUserId());
            deptUserDO.setPartTime(false);
            deptUserDOList.add(deptUserDO);
        }
        deptUserDOMapper.insertBatch(deptUserDOList);
    }

    private void checkExcel(List<BasicUserTemplateDownloadExcelDTO> excelDtoS,ImportExcelCheckResultVO resultVO) {
        List<ImportExcelErrorNoteVO> err = new ArrayList<>();
        //身份证号
        List<String> idCards = Lists.newArrayList();
        Map<String,String> idCardMap = Maps.newHashMap();
        //公司
        Set<String> companyNames = Sets.newHashSet();
        //部门
        Set<String> departmentSet = Sets.newHashSet();
        //所
        Set<String> placeSet = Sets.newHashSet();
        for(int i = 0;i<excelDtoS.size();i++){
            List<String> errorNotes = Lists.newArrayList();
            if(!StringUtils.hasText(excelDtoS.get(i).getIdCard())){
                errorNotes.add("身份证号不能为空");
            }
            idCards.add(excelDtoS.get(i).getIdCard());
            if(idCardMap.containsKey(excelDtoS.get(i).getIdCard())){
                errorNotes.add("身份证号重复");
            }
            idCardMap.put(excelDtoS.get(i).getIdCard(),excelDtoS.get(i).getIdCard());

            if(!StringUtils.hasText(excelDtoS.get(i).getFullName())){
                errorNotes.add("姓名不能为空");
            }
            if(!StringUtils.hasText(excelDtoS.get(i).getCompanyName())){
                errorNotes.add("公司名称不能为空");
            }
            companyNames.add(excelDtoS.get(i).getCompanyName());

            if(!StringUtils.hasText(excelDtoS.get(i).getSex())){
                errorNotes.add("性别不能为空");
            }
            if(!StringUtils.hasText(excelDtoS.get(i).getNation())){
                errorNotes.add("民族不能为空");
            }
            if(!StringUtils.hasText(excelDtoS.get(i).getDateOfBirth())){
                errorNotes.add("出生日期不能为空");
            }
            if(!StringUtils.hasText(excelDtoS.get(i).getDepartmentName())){
                errorNotes.add("用人部门不能为空");
            }
            departmentSet.add(excelDtoS.get(i).getDepartmentName());

            if(!StringUtils.hasText(excelDtoS.get(i).getAddWorkTime())){
                errorNotes.add("到岗时间不能为空");
            }
            if(StringUtils.hasText(excelDtoS.get(i).getInstituteName())){
                placeSet.add(excelDtoS.get(i).getInstituteName());
            }
            if(!CollectionUtils.isEmpty(errorNotes)){
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i+2)+"");
                importExcelErrorNoteVO.setErrorNotes(errorNotes);
                err.add(importExcelErrorNoteVO);
            }
        }
        if(!CollectionUtils.isEmpty(err)){
            resultVO.setErr(err);
            resultVO.setCode(4000);
            return;
        }
        resultVO.setErr(err);
        List<BasicUser> basicUsers = basicUserMapper.selectList(new LambdaQueryWrapperX<BasicUser>()
                .in(BasicUser::getIdCard, idCards));
        if(!CollectionUtils.isEmpty(basicUsers)){
            resultVO.setCode(4000);
            resultVO.setOom("身份证号已存在"+basicUsers.stream().map(BasicUser::getIdCard).collect(Collectors.joining(",")));
            return;
        }
        List<DeptVO> deptVOS = deptRedisHelper.listAllDept();
        if(!CollectionUtils.isEmpty(deptVOS)){
//            List<String> companyList = deptVOS.stream().filter(deptVO -> "10".equals(deptVO.getType())).map(DeptVO::getName).collect(Collectors.toList());
            List<String> departmentList = deptVOS.stream().filter(deptVO -> "20".equals(deptVO.getType())).map(DeptVO::getName).collect(Collectors.toList());
            List<String> placeList = deptVOS.stream().filter(deptVO -> "30".equals(deptVO.getType())).map(DeptVO::getName).collect(Collectors.toList());

//            if(!CollectionUtils.isEmpty(companyList) && !CollectionUtils.isEmpty(companyNames)){
//                List<String> companyNamesList = companyNames.stream().filter(companyName -> !companyList.contains(companyName)).collect(Collectors.toList());
//                if(!CollectionUtils.isEmpty(companyNamesList)){
//                    resultVO.setCode(4000);
//                    resultVO.setOom("公司名称不存在"+companyNamesList.stream().collect(Collectors.joining(",")));
//                    return;
//               }
//            }
            if(!CollectionUtils.isEmpty(departmentList) && !CollectionUtils.isEmpty(departmentSet)){
                List<String> departmentSetList = departmentSet.stream().filter(departmentName -> !departmentList.contains(departmentName)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(departmentSetList)){
                    resultVO.setCode(4000);
                    resultVO.setOom("用人部门不存在："+departmentSetList.stream().collect(Collectors.joining(",")));
                   return;
                }
            }
            if(!CollectionUtils.isEmpty(placeList) && !CollectionUtils.isEmpty(placeSet)){
                List<String> placeSetList = placeSet.stream().filter(instituteName -> !placeList.contains(instituteName)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(placeSetList)){
                    resultVO.setCode(4000);
                    resultVO.setOom("所不存在："+placeSetList.stream().collect(Collectors.joining(",")));
                    return;
                }
            }
//            Map<String,List<DeptVO>> commandMap =  deptVOS.stream().filter(deptVO -> "10".equals(deptVO.getType()))
//                    .collect(Collectors.groupingBy(DeptVO::getName));
            Map<String,List<DeptVO>> departmentMap = deptVOS.stream().filter(deptVO -> "20".equals(deptVO.getType()))
                    .collect(Collectors.groupingBy(DeptVO::getName));
            Map<String,List<DeptVO>> instituteMap = deptVOS.stream().filter(deptVO -> "30".equals(deptVO.getType()))
                    .collect(Collectors.groupingBy(DeptVO::getName));
            for(BasicUserTemplateDownloadExcelDTO excelDto:excelDtoS){
//                if(StringUtils.hasText(excelDto.getCompanyName())){
//                    excelDto.setCompanyCode(commandMap.get(excelDto.getCompanyName()).get(0).getDeptCode());
//                }
                if(StringUtils.hasText(excelDto.getDepartmentName())){
                    excelDto.setDepartmentCode(departmentMap.get(excelDto.getDepartmentName()).get(0).getDeptCode());
                    excelDto.setDepartmentId(departmentMap.get(excelDto.getDepartmentName()).get(0).getId());
                }
                if(StringUtils.hasText(excelDto.getInstituteName())){
                    excelDto.setInstituteCode(instituteMap.get(excelDto.getInstituteName()).get(0).getDeptCode());
                }
            }
        }
    }

    public static class UserExcelReadListener implements ReadListener<BasicUserTemplateDownloadExcelDTO> {
        private final List<BasicUserTemplateDownloadExcelDTO> userExcelDTOS = new ArrayList<>();


        public List<BasicUserTemplateDownloadExcelDTO> getUserExcelDTOS() {
            return userExcelDTOS;
        }

        @Override
        public void invoke(BasicUserTemplateDownloadExcelDTO data, AnalysisContext context) {
            userExcelDTOS.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        @Override
        public void extra(CellExtra extra, AnalysisContext context) {
        }
    }

}
