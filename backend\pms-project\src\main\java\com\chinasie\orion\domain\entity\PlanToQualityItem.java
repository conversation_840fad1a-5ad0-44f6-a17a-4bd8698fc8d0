package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * PlanToQualityItem Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-23 11:06:02
 */
@TableName(value = "pms_plan_to_quality_item")
@ApiModel(value = "PlanToQualityItemEntity对象", description = "计划与质量管控关系")
@Data
public class PlanToQualityItem extends ObjectEntity implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    @TableField(value = "to_class")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    @TableField(value = "from_class")
    private String fromClass;

}

