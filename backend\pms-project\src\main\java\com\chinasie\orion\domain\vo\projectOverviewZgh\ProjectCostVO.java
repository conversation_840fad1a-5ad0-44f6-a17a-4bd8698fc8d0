package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 支出实时概况
 */
@Data
@ApiModel(value = "ProjectIncomeVO", description = "支出实时概况")
public class ProjectCostVO {

    @ApiModelProperty("支出金额")
    private BigDecimal cost = BigDecimal.ZERO;

    @ApiModelProperty("计划总金额")
    private BigDecimal planIncome = BigDecimal.ZERO;

    @ApiModelProperty("实际总金额")
    private BigDecimal practiceIncome = BigDecimal.ZERO;

    @ApiModelProperty("支出列表")
    private List<ProjectCostItemVO> costs = new ArrayList<>();


    @Data
    @ApiModel(value = "ProjectCostItemVO", description = "支出条目")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProjectCostItemVO {
        @ApiModelProperty("科目主键")
        private String id;

        @ApiModelProperty("科目ID")
        private String subjectId;

        @ApiModelProperty("科目名称")
        private String name;

        @ApiModelProperty("计划金额")
        private BigDecimal planAmount = BigDecimal.ZERO;

        @ApiModelProperty("承诺金额")
        private BigDecimal promiseAmount = BigDecimal.ZERO;


        @ApiModelProperty("商场采购金额")
        private String shopMallPurchaseAmount ;

        @ApiModelProperty("正常采购金额")
        private String normalPurchaseAmount;


        @ApiModelProperty("实际金额")
        private BigDecimal actualAmount = BigDecimal.ZERO;

        @ApiModelProperty("结余金额")
        private BigDecimal balanceAmount = BigDecimal.ZERO;

        @ApiModelProperty("进度")
        private BigDecimal schedule = BigDecimal.ZERO;

    }

}
