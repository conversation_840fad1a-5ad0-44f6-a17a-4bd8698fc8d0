package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractPayNodeStatusConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-28 10:43:16
 */
@TableName(value = "pms_contract_pay_node_status_confirm")
@ApiModel(value = "ContractPayNodeStatusConfirm对象", description = "项目合同支付节点状态确认")
@Data
public class ContractPayNodeStatusConfirm extends ObjectEntity implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    @TableField(value = "node_id")
    private String nodeId;

    /**
     * 确认id
     */
    @ApiModelProperty(value = "确认id")
    @TableField(value = "confirm_id")
    private String confirmId;

}
