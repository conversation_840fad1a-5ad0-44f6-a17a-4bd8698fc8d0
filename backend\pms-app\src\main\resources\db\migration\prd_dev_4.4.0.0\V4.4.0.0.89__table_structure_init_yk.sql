CREATE TABLE `pmsx_attendance_sign` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `attandance_year` int(20) NOT NULL COMMENT '年',
                                        `org_code` varchar(20) DEFAULT NULL COMMENT '中心部门编码',
                                        `org_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                        `dept_code` varchar(64) DEFAULT NULL COMMENT '所编号',
                                        `dept_name` varchar(64) DEFAULT NULL COMMENT '所名称',
                                        `contract_no` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                        `contract_name` varchar(64) NOT NULL COMMENT '合同名称',
                                        `job_grade` varchar(64) DEFAULT NULL COMMENT '人员岗级',
                                        `supplier_no` varchar(64) NOT NULL COMMENT '供应商编号',
                                        `supplier_name` varchar(64) DEFAULT NULL COMMENT '供应商名称',
                                        `user_name` varchar(64) DEFAULT NULL COMMENT '用户名称',
                                        `user_code` varchar(64) DEFAULT NULL COMMENT '工号',
                                        `should_days` int(11) DEFAULT NULL COMMENT '本月应签到天数',
                                        `actual_days` int(11) DEFAULT NULL COMMENT '实际出勤天数',
                                        `off_days` int(11) DEFAULT NULL COMMENT '已换休天数',
                                        `attandance_rate` decimal(10,0) DEFAULT NULL COMMENT '出勤率',
                                        `attandance_month` int(20) NOT NULL COMMENT '月',
                                        `attandance_quarter` int(10) NOT NULL COMMENT '季度',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出勤签到';



CREATE TABLE `pmsx_contract_assessment_standard` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键',
                                                     `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                     `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                     `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                     `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                     `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                     `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                     `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                     `status` int(11) NOT NULL COMMENT '状态',
                                                     `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                     `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                                     `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                                     `assessment_type` varchar(20) DEFAULT NULL COMMENT '审核类别',
                                                     `assessment_content` varchar(1024) DEFAULT NULL COMMENT '考核内容',
                                                     `standard` decimal(10,0) DEFAULT NULL COMMENT '考核标准',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核标准表';



CREATE TABLE `pmsx_excessive_cost` (
                                       `id` varchar(64) NOT NULL COMMENT '主键',
                                       `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                       `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                       `modify_time` datetime NOT NULL COMMENT '修改时间',
                                       `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                       `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                       `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                       `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                       `status` int(11) NOT NULL COMMENT '状态',
                                       `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                       `flow_status` varchar(64) NOT NULL COMMENT '流程状态',
                                       `flow_no` varchar(255) NOT NULL COMMENT '流水号',
                                       `org_code` varchar(64) NOT NULL COMMENT '中心编号',
                                       `org_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                       `dept_code` varchar(64) NOT NULL COMMENT '部门编号',
                                       `dept_name` varchar(64) DEFAULT NULL COMMENT '部门名称',
                                       `supplier_no` varchar(64) NOT NULL COMMENT '供应商编号',
                                       `supplier_name` varchar(64) DEFAULT NULL COMMENT '供应商名称',
                                       `contract_no` varchar(64) NOT NULL COMMENT '合同编号',
                                       `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                       `user_code` varchar(64) NOT NULL COMMENT '工号',
                                       `user_name` varchar(64) DEFAULT NULL COMMENT '姓名',
                                       `excessive_type_no` varchar(64) NOT NULL COMMENT '超标类型编号',
                                       `excessive_type_name` varchar(64) DEFAULT NULL COMMENT '超标类型名称',
                                       `task_no` varchar(64) NOT NULL COMMENT '差旅任务编号',
                                       `start_time` datetime NOT NULL COMMENT '开始日期',
                                       `end_time` datetime DEFAULT NULL COMMENT '结束日期',
                                       `days` int(11) NOT NULL COMMENT '时长',
                                       `city` varchar(64) DEFAULT NULL COMMENT '城市',
                                       `type` varchar(64) DEFAULT NULL COMMENT '住宿类型',
                                       `actual_amount` decimal(10,0) NOT NULL COMMENT '实际住宿总金额',
                                       `excess_amount` decimal(10,0) DEFAULT NULL COMMENT '超出金额',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='超额';


CREATE TABLE `pmsx_labor_cost_acceptance` (
                                              `id` varchar(64) NOT NULL COMMENT '主键',
                                              `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                              `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                              `modify_time` datetime NOT NULL COMMENT '修改时间',
                                              `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                              `create_time` datetime NOT NULL COMMENT '创建时间',
                                              `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                              `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                              `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                              `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                              `status` int(11) NOT NULL COMMENT '状态',
                                              `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                              `name` varchar(255) NOT NULL COMMENT '申请单名称',
                                              `contract_no` varchar(255) NOT NULL COMMENT '合同编号',
                                              `acceptance_year` int(11) NOT NULL COMMENT '验收年份',
                                              `acceptance_quarter` int(11) NOT NULL COMMENT '验收季度',
                                              `employ_dept_code` varchar(64) NOT NULL COMMENT '验收用人单位',
                                              `project_reviewer` varchar(500) NOT NULL COMMENT '项目组审核人',
                                              `dept_reviewer` varchar(500) NOT NULL COMMENT '研究所审核人',
                                              `org_reviewer` varchar(500) NOT NULL COMMENT '中心/部门审核人',
                                              `belong_dept_reviewer` varchar(500) NOT NULL COMMENT '归口部门审核人',
                                              `acceptance_dept_code` varchar(64) NOT NULL COMMENT '验收中心',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人力成本验收单';


CREATE TABLE `pmsx_labor_cost_acceptance_assessment_standard` (
                                                                  `id` varchar(64) NOT NULL COMMENT '主键',
                                                                  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                                  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                                  `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                                  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                                  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                                  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                                  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                                  `status` int(11) NOT NULL COMMENT '状态',
                                                                  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                                  `acceptance_id` varchar(64) NOT NULL COMMENT '验收单id',
                                                                  `assessment_atandard_id` varchar(64) NOT NULL COMMENT '审核标准Id',
                                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验收人力成本与审核标准关联';



CREATE TABLE `pmsx_labor_cost_acceptance_statistics` (
                                                         `id` varchar(64) NOT NULL COMMENT '主键',
                                                         `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                         `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                         `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                         `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                                         `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                         `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                         `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                         `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                         `status` int(11) NOT NULL COMMENT '状态',
                                                         `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                         `acceptance_id` varchar(64) NOT NULL COMMENT '验收单id',
                                                         `job_grade` varchar(64) NOT NULL COMMENT '人员岗级',
                                                         `plan_user_count` int(11) NOT NULL COMMENT '计划需求人数',
                                                         `actual_user_count` int(11) NOT NULL COMMENT '实际人数',
                                                         `workload` decimal(10,0) NOT NULL COMMENT '工作量(人/月)',
                                                         `job_grade_amt` decimal(10,0) NOT NULL COMMENT '岗级成本',
                                                         `job_grade_total_amt` decimal(10,0) NOT NULL COMMENT '岗级总价 (元)',
                                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验收人力成本费用统计';


CREATE TABLE `pmsx_open_cost` (
                                  `id` varchar(64) NOT NULL COMMENT '主键',
                                  `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                  `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                  `modify_time` datetime NOT NULL COMMENT '修改时间',
                                  `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                  `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                  `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                  `status` int(11) NOT NULL COMMENT '状态',
                                  `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                  `org_code` varchar(64) NOT NULL COMMENT '中心编号',
                                  `org_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                  `dept_code` varchar(64) NOT NULL COMMENT '部门编号',
                                  `dept_name` varchar(64) DEFAULT NULL COMMENT '部门名称',
                                  `contract_no` varchar(64) NOT NULL COMMENT '合同编号',
                                  `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                  `user_code` varchar(64) DEFAULT NULL COMMENT '工号',
                                  `user_name` varchar(64) DEFAULT NULL COMMENT '姓名',
                                  `supplier_no` varchar(64) NOT NULL COMMENT '供应商编号',
                                  `supplier_name` varchar(64) DEFAULT NULL COMMENT '供应商名称',
                                  `pay_date` datetime NOT NULL COMMENT '日期',
                                  `pay_type_no` varchar(64) NOT NULL COMMENT '费用类别编号',
                                  `pay_type_name` varchar(64) DEFAULT NULL COMMENT '费用类别名称',
                                  `pay_amt` decimal(10,0) DEFAULT NULL COMMENT '费用金额',
                                  `data_year` int(10) NOT NULL COMMENT '年',
                                  `data_month` int(10) NOT NULL COMMENT '月',
                                  `data_quarter` int(10) NOT NULL COMMENT '季度',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开口项费用';


CREATE TABLE `pmsx_plane_cost` (
                                   `id` varchar(64) NOT NULL COMMENT '主键',
                                   `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                   `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                   `modify_time` datetime NOT NULL COMMENT '修改时间',
                                   `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                   `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                   `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                   `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                   `status` int(11) NOT NULL COMMENT '状态',
                                   `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                   `plan_no` varchar(64) NOT NULL COMMENT '机票单号',
                                   `org_code` varchar(64) NOT NULL COMMENT '中心编号',
                                   `org_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                   `dept_no` varchar(64) NOT NULL COMMENT '部门编号',
                                   `dept_name` varchar(64) DEFAULT NULL COMMENT '部门名称',
                                   `supplier_no` varchar(64) NOT NULL COMMENT '供应商编号',
                                   `supplier_name` varchar(64) DEFAULT NULL COMMENT '供应商名称',
                                   `contract_no` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                   `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                   `user_code` varchar(64) NOT NULL COMMENT '工号',
                                   `user_name` varchar(64) DEFAULT NULL COMMENT '姓名',
                                   `task_no` varchar(64) DEFAULT NULL COMMENT '差旅任务编号',
                                   `start_time` datetime DEFAULT NULL COMMENT '出发日期',
                                   `from_city` varchar(64) DEFAULT NULL COMMENT '出发地',
                                   `to_city` varchar(64) DEFAULT NULL COMMENT '到达地',
                                   `discount_price` decimal(10,0) DEFAULT NULL COMMENT '折扣机票金额',
                                   `full_price` decimal(10,0) DEFAULT NULL COMMENT '全价机票金额',
                                   `discount_count` decimal(10,0) DEFAULT NULL COMMENT '机票折数',
                                   `flow_status` varchar(20) DEFAULT NULL COMMENT '流程状态',
                                   `data_year` int(10) NOT NULL COMMENT '年',
                                   `data_month` int(10) NOT NULL COMMENT '月',
                                   `data_quarter` int(10) NOT NULL COMMENT '季度',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机票费用';



