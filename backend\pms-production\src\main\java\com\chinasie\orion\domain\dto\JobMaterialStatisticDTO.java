package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "JobMaterialStatisticDTO对象", description = "作业相关的物资")
@Data
@ExcelIgnoreUnannotated
public class JobMaterialStatisticDTO extends ObjectDTO implements Serializable {

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String repairRound;

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 9)
    private String specificationModel;


    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @ExcelProperty(value = "需求数量 ", index = 11)
    private Integer demandNum;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;


    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;




}
