package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectDeclareFileInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 18:02:59
 */
@ApiModel(value = "ProjectDeclareFileInfoDTO对象", description = "项目申报文件信息")
@Data
public class ProjectDeclareFileInfoDTO extends ObjectDTO implements Serializable{

    /**
     * 项目申报id
     */
    @ApiModelProperty(value = "项目申报id")
    private String projectDeclareId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 文件数据id
     */
    @ApiModelProperty(value = "文件数据id")
    private String fileDataId;

}
