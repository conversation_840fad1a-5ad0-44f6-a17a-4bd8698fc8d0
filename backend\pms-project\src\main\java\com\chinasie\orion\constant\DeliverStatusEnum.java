package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum  DeliverStatusEnum {
    UN_START(101, "未开始"),
    DEALING(110, "流程中"),
    DEAL(130, "已完成");
    private Integer status;
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    DeliverStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getStringByStatus(Integer status){
        if (UN_START.getStatus().equals(status)) {
            return UN_START.getDesc();
        }
        if (DEALING.getStatus().equals(status)) {
            return DEALING.getDesc();
        }
        if (DEAL.getStatus().equals(status)) {
            return DEAL.getDesc();
        }
        return "";
    }
}
