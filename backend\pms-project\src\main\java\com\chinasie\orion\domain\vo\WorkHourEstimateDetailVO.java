package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourEstimateDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 17:47:13
 */
@ApiModel(value = "WorkHourEstimateDetailVO对象", description = "工时预估明细")
@Data
public class WorkHourEstimateDetailVO extends ObjectVO implements Serializable{

    /**
     * 工时id
     */
    @ApiModelProperty(value = "工时id")
    private String workHourId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String workMonth;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;

}
