package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractClaim DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractClaimDTO对象", description = "合同索赔信息表")
@Data
@ExcelIgnoreUnannotated
public class ContractClaimDTO extends ObjectDTO implements Serializable {

    /**
     * 索赔编号
     */
    @ApiModelProperty(value = "索赔编号")
    @ExcelProperty(value = "索赔编号 ", index = 0)
    private String claimId;

    /**
     * 索赔标题
     */
    @ApiModelProperty(value = "索赔标题")
    @ExcelProperty(value = "索赔标题 ", index = 1)
    private String claimTitle;

    /**
     * 索赔状态
     */
    @ApiModelProperty(value = "索赔状态")
    @ExcelProperty(value = "索赔状态 ", index = 2)
    private String claimStatus;

    /**
     * 索赔方向
     */
    @ApiModelProperty(value = "索赔方向")
    @ExcelProperty(value = "索赔方向 ", index = 3)
    private String claimDirection;

    /**
     * 索赔处理时间
     */
    @ApiModelProperty(value = "索赔处理时间")
    @ExcelProperty(value = "索赔处理时间 ", index = 4)
    private Date claimProcessTime;

    /**
     * 索赔申请时间
     */
    @ApiModelProperty(value = "索赔申请时间")
    @ExcelProperty(value = "索赔申请时间 ", index = 5)
    private Date claimRequestTime;

    /**
     * 累计索赔金额（含本次）
     */
    @ApiModelProperty(value = "累计索赔金额（含本次）")
    @ExcelProperty(value = "累计索赔金额（含本次） ", index = 6)
    private BigDecimal cumulativeClaimAmount;

    /**
     * 总累计索赔占原合同价%
     */
    @ApiModelProperty(value = "总累计索赔占原合同价%")
    @ExcelProperty(value = "总累计索赔占原合同价% ", index = 7)
    private String totalClaimPctOfOrigPrice;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 8)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 9)
    private String mainTableId;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 10)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 11)
    private String contractName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @ExcelIgnore
    private String startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelIgnore
    private String endDate;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @ExcelProperty(value = "采购组Id ", index = 12)
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @ExcelProperty(value = "采购组名称 ", index = 13)
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @ExcelProperty(value = "商务负责人ID ", index = 14)
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @ExcelProperty(value = "商务负责人名称 ", index = 15)
    private String businessRspUserName;
}
