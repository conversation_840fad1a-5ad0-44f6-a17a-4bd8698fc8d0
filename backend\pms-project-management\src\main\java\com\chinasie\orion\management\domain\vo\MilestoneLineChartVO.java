package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.w3c.dom.stylesheets.LinkStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "MilestoneLineChartVO对象", description = "经营看板-里程碑折线图")
@Data
public class MilestoneLineChartVO implements Serializable {


    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private List<Integer> allMonth;


    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private List<BigDecimal> acceptAmount;

    /**
     * 计划验收金额
     */
    @ApiModelProperty(value = "计划验收金额")
    private List<BigDecimal> planAmount;

}

