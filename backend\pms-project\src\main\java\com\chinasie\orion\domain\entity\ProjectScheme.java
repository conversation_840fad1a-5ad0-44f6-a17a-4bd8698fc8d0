package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * ProjectScheme
 *
 * @author: yangFy
 * @date: 2023/4/17 17:36
 * @description TODO
 * <p>
 * 项目计划关系表entity
 * </p>
 */
@Data
@TableName(value = "pms_project_scheme")
@ApiModel(value = "ProjectScheme对象", description = "项目计划")
public class ProjectScheme extends ObjectEntity implements Serializable {


    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "scheme_number")
    private String schemeNumber;
    /**
     *
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Long sort;
    /**
     *
     */
    @ApiModelProperty(value = "计划名称")
    @TableField(value = "name")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "icon")
    @TableField(value = "icon")
    private String icon;
    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;
    /**
     *
     */
    @ApiModelProperty(value = "父id")
    @TableField(value = "parent_id")
    private String parentId;
    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    @TableField(value = "parent_chain")
    private String parentChain;
//    /**
//     *
//     */
//    @ApiModelProperty(value = "计划类型")
//    @TableField(value = "type")
//    private Integer type;


    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "node_type")
    private String nodeType;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    @TableField(value = "rsp_sub_dept")
    private String rspSubDept;

    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "rsp_section_id")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "rsp_user")
    @FieldBind(dataBind = UserDataBind.class, target = "rspUserName")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;
    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    @TableField(value = "circumstance")
    private Integer circumstance;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    @TableField(value = "scheme_desc")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    @TableField(value = "top_sort")
    private Integer topSort;

    @ApiModelProperty(value = "执行情况说明")
    @TableField(value = "execute_desc")
    private String executeDesc;

    @ApiModelProperty(value = "计划下发时间")
    @TableField(value = "issue_time")
    private Date issueTime;


    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    @TableField(value = "delay_end_Flag")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    @TableField(value = "delay_end_reason")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    @TableField(value = "process_flag")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "process_id")
    private String processId;


    @ApiModelProperty(value = "关联流程实例Id")
    @TableField(value = "form_code_id")
    private String formCodeId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;


    @ApiModelProperty(value = "下达人")
    @TableField(value = "issued_user")
    private String issuedUser;
    @ApiModelProperty(value = "参与人")
    @TableField(value = "participant_users")
    private String participantUsers;

    @ApiModelProperty(value = "审批人")
    @TableField(value = "examine_user")
    private String examineUser;

    @ApiModelProperty(value = "评分状态")
    @TableField(value = "examine_type")
    private String examineType;

    @ApiModelProperty(value = "下发提醒间隔")
    @TableField(value = "issue_remind_interval")
    private Integer issueRemindInterval;

    @ApiModelProperty(value = "下发提醒间隔单位")
    @TableField(value = "issue_remind_interval_unit")
    private String issueRemindIntervalUnit;

    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String rspUserName;

    @ApiModelProperty(value = "下发提醒间隔时间")
    @TableField(value = "issue_remind_time")
    private Time issueRemindTime;


    @ApiModelProperty(value = "计划活动项")
    @TableField(value = "plan_active")
    private String planActive;

    @ApiModelProperty(value = "选择基线")
    @TableField(value = "base_line")
    private String baseLine;
    @ApiModelProperty(value = "设计任务编码")
    @TableField(value = "design_task_number")
    private String designTaskNumber;

    @ApiModelProperty(value = "标识")
    @TableField(value = "identification")
    private String identification;

    @ApiModelProperty(value = "确认理由")
    @TableField(value = "reason_confirmation")
    private String reasonConfirmation;


    @ApiModelProperty(value = "暂停时间")
    @TableField(value = "suspend_time")
    private Date suspendTime;

    @ApiModelProperty(value = "终止时间")
    @TableField(value = "terminate_time")
    private Date terminateTime;

    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time")
    private Date startTime;

    @ApiModelProperty(value = "暂停理由")
    @TableField(value = "suspend_reason")
    private String suspendReason;

    @ApiModelProperty(value = "终止理由")
    @TableField(value = "terminate_reason")
    private String terminateReason;

    @ApiModelProperty(value = "启动理由")
    @TableField(value = "start_reason")
    private String startReason;

    @ApiModelProperty(value = "上个状态")
    @TableField(value = "last_status")
    private Integer lastStatus;
    @ApiModelProperty(value = "是否作业")
    @TableField(value = "is_work_job")
    private Boolean isWorkJob;

    @ApiModelProperty(value = "实施类型")
    @TableField(value = "enforce_type")
    private String enforceType;

    @ApiModelProperty(value = "实施地点")
    @TableField(value = "enforce_base_place")
    private String enforceBasePlace;
    @ApiModelProperty(value = "实施地点名称")
    @TableField(value = "enforce_base_place_name")
    private String enforceBasePlaceName;

    @ApiModelProperty(value = "实施区域/范围")
    @TableField(value = "enforce_scope")
    private String enforceScope;

    @ApiModelProperty(value = "工作内容")
    @TableField(value = "work_content")
    private String workContent;

    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    @ApiModelProperty(value = "是否携带物资")
    @TableField(value = "is_carry_materials")
    private Boolean isCarryMaterials;

    @ApiModelProperty(value = "转办理由")
    @TableField(value = "reason_transfer")
    private String  reasonTransfer;

    @ApiModelProperty(value = "转办理由")
    @TableField(value = "product_id")
    private String  productId;


    /**
     * 项目暂停周期
     */
    @ApiModelProperty(value = "项目暂停周期")
    @TableField(value = "paused_cycle")
    private Integer pausedCycle;

    @ApiModelProperty(value = "紧急程度")
    @TableField(value = "urgency")
    private String urgency;


    @ApiModelProperty(value = "是否作业 -1：否 1：是")
    @TableField(value = "is_work")
    private Integer isWork;

    @ApiModelProperty(value = "逾期次数")
    @TableField(value = "overdue_count")
    private Integer overdueCount;

    @ApiModelProperty(value = "合同里程碑id")
    @TableField(value = "contract_milestone_id")
    private String contractMilestoneId;

    @ApiModelProperty(value = "问题id")
    @TableField(value = "question_id")
    private String questionId;

    public ProjectScheme() {

    }

}
