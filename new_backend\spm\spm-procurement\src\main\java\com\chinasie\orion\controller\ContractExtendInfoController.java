package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.ContractExtendInfoDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.service.ContractExtendInfoService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ContractExtendInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/contractExtendInfo")
@Api(tags = "合同拓展信息")
public class ContractExtendInfoController {

    @Autowired
    private ContractExtendInfoService contractExtendInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同拓展信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractExtendInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ContractExtendInfoVO rsp = contractExtendInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询合同扩展信息
     *
     * @param contractExtendInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询合同扩展信息")
    @RequestMapping(value = "/getExtendInfo", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号查询合同扩展信息", type = "合同拓展信息", subType = "根据合同编号查询合同扩展信息", bizNo = "{{#contractExtendInfoDTO.contractNumber}}")
    public ResponseDTO<ContractExtendInfoVO> getExtendInfo(@RequestBody ContractExtendInfoDTO contractExtendInfoDTO) throws Exception {
        ContractExtendInfoVO contractExtendInfoVO = contractExtendInfoService.getExtendInfo(contractExtendInfoDTO.getContractNumber());
        return new ResponseDTO<>(contractExtendInfoVO);
    }


    /**
     * 新增
     *
     * @param contractExtendInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractExtendInfoDTO.name}}】", type = "ContractExtendInfo", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractExtendInfoDTO contractExtendInfoDTO) throws Exception {
        String rsp = contractExtendInfoService.create(contractExtendInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractExtendInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractExtendInfoDTO.name}}】", type = "ContractExtendInfo", subType = "编辑", bizNo = "{{#contractExtendInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ContractExtendInfoDTO contractExtendInfoDTO) throws Exception {
        Boolean rsp = contractExtendInfoService.edit(contractExtendInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "ContractExtendInfo", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractExtendInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "ContractExtendInfo", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractExtendInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ContractExtendInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractExtendInfoVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<ContractExtendInfoDTO> pageRequest) throws Exception {
        Page<ContractExtendInfoVO> rsp = contractExtendInfoService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询合同扩展信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询人员列表信息")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ContractLineInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractExtendInfoVO>> getByCode(@PathVariable("contractNumber") String contractNumber, @RequestBody Page<ContractExtendInfoDTO> pageRequest) throws Exception {
        Page<ContractExtendInfoVO> rsp = contractExtendInfoService.getByCode(contractNumber, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同拓展信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "ContractExtendInfo", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractExtendInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("合同拓展信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "ContractExtendInfo", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractExtendInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同拓展信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "ContractExtendInfo", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractExtendInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同拓展信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "ContractExtendInfo", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractExtendInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同拓展信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "ContractExtendInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractExtendInfoService.exportByExcel(searchConditions, response);
    }
}
