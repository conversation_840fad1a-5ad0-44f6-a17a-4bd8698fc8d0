package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * JobPersonRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@ApiModel(value = "JobPersonRecordVO对象", description = "作业人员记录表")
@Data
public class JobPersonRecordVO extends  ObjectVO   implements Serializable{

            /**
         * 作业ID
         */
        @ApiModelProperty(value = "作业ID")
        private String jobId;


        /**
         * 人员code
         */
        @ApiModelProperty(value = "人员code")
        private String userCode;


        /**
         * 人员管理ID
         */
        @ApiModelProperty(value = "人员管理ID")
        private String personManageId;


    

}
