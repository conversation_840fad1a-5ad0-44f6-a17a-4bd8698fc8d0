<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google</groupId>
  <artifactId>google</artifactId>
  <version>5</version>
  <name>Google</name>
  <description>Internally developed code released as open source.</description>
  <packaging>pom</packaging>
  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>
  <developers>
    <developer>
      <id>google</id>
    </developer>
  </developers>
  <url>http://code.google.com/</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <distributionManagement>
    <repository>
      <id>google-releases</id>
      <name>Google Maven Repository</name>
      <url>http://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>google-snapshots</id>
      <name>Google Maven Snapshot Repository</name>
      <url>http://oss.sonatype.org/content/repositories/google-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <scm>
    <connection>scm:svn:http://google-maven-repository.googlecode.com/svn/tags/google-5</connection>
    <developerConnection>scm:svn:https://google-maven-repository.googlecode.com/svn/tags/google-5</developerConnection>
    <url>http://code.google.com/p/google-maven-repository/source/browse/tags/google-5</url>
  </scm>
  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
