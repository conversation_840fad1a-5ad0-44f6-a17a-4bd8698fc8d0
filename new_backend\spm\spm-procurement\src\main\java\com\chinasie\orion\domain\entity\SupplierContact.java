package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierContact Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_contact")
@ApiModel(value = "SupplierContactEntity对象", description = "供应商联系人")
@Data

public class SupplierContact extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 联系人姓
     */
    @ApiModelProperty(value = "联系人姓")
    @TableField(value = "contact_lastname")
    private String contactLastname;

    /**
     * 联系人名
     */
    @ApiModelProperty(value = "联系人名")
    @TableField(value = "contact_firstname")
    private String contactFirstname;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门	")
    @TableField(value = "department")
    private String department;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    @TableField(value = "position")
    private String position;

    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    @TableField(value = "landline")
    private String landline;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机	")
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    @TableField(value = "extension")
    private String extension;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @TableField(value = "fax")
    private String fax;

    /**
     * 默认联系人
     */
    @ApiModelProperty(value = "默认联系人")
    @TableField(value = "default_contact")
    private String defaultContact;

    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    @TableField(value = "email")
    private String email;

    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    @TableField(value = "id_number")
    private String idNumber;

    /**
     * 负责区域/专业
     */
    @ApiModelProperty(value = "负责区域/专业")
    @TableField(value = "responsible_area")
    private String responsibleArea;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
