package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProcurementDashboardDTO;
import com.chinasie.orion.domain.entity.ProcurementDashboard;
import com.chinasie.orion.domain.vo.ProcurementBarDashboardVO;
import com.chinasie.orion.domain.vo.ProcurementDashboardVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;


/**
 * <p>
 * ProcurementDashboard 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
public interface ProcurementDashboardService extends OrionBaseService<ProcurementDashboard> {

    /**
     * 查询看板块图数据
     * <p>
     * * @param id
     */
    List<ProcurementDashboardVO> getLumpData(ProcurementDashboardDTO procurementDashboard);


    /**
     * 查询看板柱状图数据
     * <p>
     * * @param id
     */
    List<ProcurementBarDashboardVO> getColumnData(ProcurementDashboardDTO procurementDashboard);
}
