package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * RequirementManagementMark VO对象
 *
 * <AUTHOR>
 * @since 2024-09-05 20:04:54
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementManagementMarkVO对象", description = "需求管理标注")
@Data
public class RequirementManagementMarkVO extends ObjectVO implements Serializable {

    /**
     * 需求管理id，关联 pms_requirement_mangement id
     */
    @ApiModelProperty(value = "需求管理id，关联 pms_requirement_mangement id")
    private String requirementId;


}
