package com.chinasie.orion.domain.dto.resourceAllocation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class UpdateTimeDTO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "开始时间")
    private String realStartDate;

    @ApiModelProperty(value = "结束时间")
    private String realEndDate;

    private String relationId;

    private String number;

    private String repairName;

    private String teamId;

    @ApiModelProperty(value = "是否常驻")
    private Boolean isBasePermanent;

}
