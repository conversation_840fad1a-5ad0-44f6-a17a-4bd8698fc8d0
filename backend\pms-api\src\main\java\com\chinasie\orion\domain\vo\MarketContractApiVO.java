package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
/**
 * MarketContract VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@ApiModel(value = "MarketContractVO对象", description = "市场合同")
@Data
public class MarketContractApiVO extends ObjectVO implements Serializable{

    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String number;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String name;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 质保等级名称
     */
    @ApiModelProperty(value = "质保等级名称")
    private String qualityLevelName;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 报价单id
     */
    @ApiModelProperty(value = "报价单id")
    @NotEmpty(message = "请选择报价单")
    private String quoteId;

    /**
     * 报价编号
     */
    @ApiModelProperty(value = "报价编号")
    private String quoteNumber;



    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同id")
    private String frameContractId;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String ywsrlx;


    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String techRspDept;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmt;

    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmt;

    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "框架合同金额")
    private BigDecimal frameContractAmt;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 关联交易审批
     */
    @ApiModelProperty(value = "关联交易审批")
    private Boolean relTransAppr;


    /**
     * 交易审批单号
     */
    @ApiModelProperty(value = "交易审批单号")
    private String transApprNumber;

    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "交易审批id")
    private String transApprId;

    /**
     * 关联交易审批状态
     */
    @ApiModelProperty(value = "关联交易审批状态")
    private String transFormStatus;



    /**
     * 主要内容
     */
    @ApiModelProperty(value = "主要内容")
    private String content;


    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    private String qualityLevel;


    /**
     * 签订时间
     */
    @ApiModelProperty(value = "签订时间")
    private Date signTime;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String commerceRspUser;

    /**
     * 商务接口人电话
     */
    @ApiModelProperty(value = "商务接口人电话")
    private String commerceRspUserPhone;


    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    private String commerceRspUserName;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    private String techRspDeptName;

    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    private Date closeDate;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    private String closeUserId;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    private String closeUserName;

    /**
     * 关闭方式
     */
    @ApiModelProperty(value = "关闭方式")
    private String closeType;

    /**
     * 是否需要采购
     */
    @ApiModelProperty(value = "是否需要采购")
    private Boolean isPurchase;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求Id")
    private String requirementId;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String resSource;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNumber;

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    private String requirementName;

    /**
     * 关联框架合同编号
     */
    @ApiModelProperty(value = "关联框架合同编号")
    private String frameContractNumber;

    /**
     * 关联框架合同名称
     */
    @ApiModelProperty(value = "关联框架合同名称")
    private String frameContractName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String cusName;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String busScope;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    private Date signDate;


    /**
     * 合同完结日期
     */
    @ApiModelProperty(value = "合同完结日期")
    private Date completeDate;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    private Date effectDate;

    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    private String industry;
    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;


    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String cusNumber;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;

    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    private String custBsPerson;

    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    private String custTecPerson;

    /**
     * 客户技术接口部门
     */
    @ApiModelProperty(value = "客户技术接口部门")
    private String custTecDept;

    /**
     * 市场合同类型名称
     */
    @ApiModelProperty(value = "市场合同类型名称")
    private String contractTypeName;

    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    @ExcelProperty(value = "配合部门接口人 ", index = 16)
    private String cooperatePerson;

    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    @ExcelProperty(value = "配合部门 ", index = 17)
    private String cooperateDpt;

    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    private String cusLevelName;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;


    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    private String ywsrlxName;


    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    private String busScopeName;

    /**
     * 客户关系(集团内外)名称
     */
    @ApiModelProperty(value = "客户关系(集团内外)名称")
    private String groupInOutName;

    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    private String cusStatusName;

    /**
     * 合同签署人id
     */
    @ApiModelProperty(value = "合同签署人id")
    private String contractSignUserId;

    /**
     * 合同签署人名称
     */
    @ApiModelProperty(value = "合同签署人名称")
    private String contractSignUserName;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessTypeName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String custPersonId;

    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    private BigDecimal quoteAmt;

    /**
     * 底层价格
     */
    @ApiModelProperty(value = "底层价格")
    private BigDecimal floorPrice;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "成本业务分类")
    private String costBusType;

}
