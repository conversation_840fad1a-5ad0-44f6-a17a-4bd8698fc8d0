package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectFundsReceivedDTO;
import com.chinasie.orion.domain.dto.ProjectReceivableDTO;
import com.chinasie.orion.domain.vo.ProjectFundsReceivedVO;
import com.chinasie.orion.domain.vo.ProjectReceivableVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.service.ProjectFundsReceivedService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectFundsReceived 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:18:18
 */
@RestController
@RequestMapping("/projectFundsReceived")
@Api(tags = "项目实收表")
public class ProjectFundsReceivedController {

    @Autowired
    private ProjectFundsReceivedService projectFundsReceivedService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情数据【{{#id}}】", type = "项目实收表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectFundsReceivedVO> detail(@PathVariable(value = "id") String id,@RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        ProjectFundsReceivedVO rsp = projectFundsReceivedService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectFundsReceivedDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectFundsReceivedDTO}}】", type = "项目实收表", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectFundsReceivedVO> create(@RequestBody ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {
        ProjectFundsReceivedVO rsp =  projectFundsReceivedService.create(projectFundsReceivedDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectFundsReceivedDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectFundsReceivedDTO}}】", type = "项目实收表", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {
        Boolean rsp = projectFundsReceivedService.edit(projectFundsReceivedDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目实收表", subType = "删除", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectFundsReceivedService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页了数据【{{#pageRequest}}】", type = "项目实收表", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectFundsReceivedVO>> pages(@RequestBody Page<ProjectFundsReceivedDTO> pageRequest) throws Exception {
        Page<ProjectFundsReceivedVO> rsp =  projectFundsReceivedService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】列表的数据【{{#projectFundsReceivedDTO}}】", type = "项目实收表", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectFundsReceivedVO>> pages(@RequestBody ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {
        List<ProjectFundsReceivedVO> rsp =  projectFundsReceivedService.getList(projectFundsReceivedDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    @ApiOperation("编辑时导入文件")
    @RequestMapping(value = "importfiles/{id}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】导入了文件", type = "项目实收表", subType = "导入文件", bizNo = "{{#id}}")
    public ResponseDTO<List<String>> importFiles(@PathVariable("id") String id, @RequestBody List<FileDTO> files) throws Exception {
        List<String> rsp = projectFundsReceivedService.importFiles(id, files);
        return new ResponseDTO<>(rsp);
    }
}

