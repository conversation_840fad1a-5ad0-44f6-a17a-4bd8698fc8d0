export const nodeLists1 :any = {
  nodes: [
    {
      id: '1',
      nodeKey: 'START',
      name: '开始',
      actions: [],
      x: 40,
      y: 140,
      nodeType: 'START_END_NODE',
      nodeState: 'FINISHED',
    },
    {
      id: '2',
      nodeKey: 'EMPTY',
      name: '请设置项目阶段',
      actions: [],
      x: 200,
      y: 150,
      nodeType: 'NORMAL_NODE',
      nodeState: 'NOT_START',
    },
    {
      id: '3',
      nodeKey: 'END',
      name: '结束',
      actions: [],
      x: 490,
      y: 140,
      nodeType: 'START_END_NODE',
      nodeState: 'NOT_START',
    },
  ],
  edges: [
    {
      source: 'START',
      target: 'EMPTY',
      isHighlight: true,
    },
    {
      source: 'EMPTY',
      target: 'END',
      isHighlight: false,
    },
  ],
};

export const nodeLists2 :any = {
  nodes: [
    {
      id: '1',
      nodeKey: 'START',
      name: '开始',
      actions: [],
      x: 40,
      y: 140,
      // 开始结束 常用节点
      // START_END_NODE NORMAL_NODE
      nodeType: 'START_END_NODE',
      // 已完成 进行中 未开始
      // FINISHED UNDERWAY NOT_START
      nodeState: 'FINISHED',
    },
    {
      id: '2',
      nodeKey: 'CHANGE_MANAGEMENT',
      name: '立项阶段',
      actions: [
        {
          id: '1-1',
          name: '项目启动',
          isOver: true,
        },
        {
          id: '1-2',
          name: '启动阶段子计划',
          isOver: true,
        },
        {
          id: '1-3',
          name: '启动阶段子计划名称',
          isOver: true,
        },
      ],
      x: 200,
      y: 150,
      nodeType: 'NORMAL_NODE',
      nodeState: 'FINISHED',
    },
    {
      id: '3',
      nodeKey: 'PLAN_MANAGEMENT',
      name: '方案阶段',
      actions: [
        {
          id: '2-1',
          name: '方案论证',
          isOver: true,
        },
        {
          id: '2-2',
          name: '标准化大纲制定',
          isOver: true,
        },
        {
          id: '2-3',
          name: '技术状态管理计划制定',
          isOver: false,
        },
      ],
      x: 490,
      y: 150,
      nodeType: 'NORMAL_NODE',
      nodeState: 'UNDERWAY',
    },
    {
      id: '4',
      nodeKey: 'KNOW_MANAGEMENT',
      name: '初样阶段',
      actions: [
        {
          id: '3-1',
          name: '初样研制',
          isOver: false,
        },
        {
          id: '3-2',
          name: '初样机验证',
          isOver: false,
        },
        {
          id: '3-3',
          name: '初样评审条件及内容审',
          isOver: false,
        },
      ],
      x: 780,
      y: 150,
      nodeType: 'NORMAL_NODE',
      nodeState: 'NOT_START',
    },
    {
      id: '5',
      nodeKey: 'END',
      name: '结束',
      actions: [],
      x: 1070,
      y: 140,
      nodeType: 'START_END_NODE',
      nodeState: 'NOT_START',
    },
  ],
  edges: [
    {
      source: 'START',
      target: 'CHANGE_MANAGEMENT',
      isHighlight: true,
    },
    {
      source: 'CHANGE_MANAGEMENT',
      target: 'PLAN_MANAGEMENT',
      isHighlight: true,
    },
    {
      source: 'PLAN_MANAGEMENT',
      target: 'KNOW_MANAGEMENT',
      isHighlight: false,
    },
    {
      source: 'KNOW_MANAGEMENT',
      target: 'END',
      isHighlight: false,
    },
  ],
};
