package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.GantDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeDragDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeGanttHeadVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeGanttVO;
import com.chinasie.orion.manager.SchemeCommonManager;
import com.chinasie.orion.manager.SchemeStatusProcessor;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectSchemeListService;
import com.chinasie.orion.service.ProjectSchemePrePostService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("projectSchemeGantt")
@AllArgsConstructor
public class ProjectSchemeGanttServiceImpl implements ProjectSchemeListService {

    private final ProjectSchemeService projectSchemeService;
    private final SchemeStatusProcessor statusProcessor;
    private final DeptRedisHelper deptRedisHelper;
    private final UserRedisHelper userRedisHelper;
    private final ProjectSchemePrePostService schemePrePostService;
    private final SchemeCommonManager commonManager;

    @Override
    public List<SchemeGanttVO> ganttPic(String projectId, GantDTO gantDTO) throws Exception {
        long startTime = System.currentTimeMillis();
        if(!StringUtils.hasText(projectId)){
            throw new RuntimeException("项目id未知");
        }

        LambdaQueryWrapper<ProjectScheme> pageWrapper = new LambdaQueryWrapper<>(ProjectScheme.class);
        if(StringUtils.hasText(gantDTO.getRspSubDept())){
            pageWrapper.eq(ProjectScheme::getRspSubDept, gantDTO.getRspSubDept());
        }
        if(StringUtils.hasText(gantDTO.getRspUser())){
            pageWrapper.eq(ProjectScheme::getRspUser, gantDTO.getRspUser());
        }
        if(gantDTO.getBeginTime() != null){
            pageWrapper.ge(ProjectScheme::getBeginTime, gantDTO.getBeginTime());
        }
        if(gantDTO.getEndTime() != null){
            pageWrapper.le(ProjectScheme::getEndTime, gantDTO.getEndTime());
        }

        pageWrapper.eq(ProjectScheme::getProjectId, projectId)
                .ne(ProjectScheme::getStatus, Status.PENDING.getCode())
//                .orderByDesc(ProjectScheme::getTopSort)
                .orderByAsc(ProjectScheme::getSort);
        List<ProjectScheme> schemes = projectSchemeService.list(pageWrapper);
        long endTime00 = System.currentTimeMillis();
        log.debug("start00: 00-ganttPic阶段查询任务执行耗时: {} 毫秒", (endTime00 - startTime));
        //筛选状态不是待发布的或者创建人是当前人的数据
        List<ProjectScheme> projectSchemes = commonManager.filterView(schemes, SchemeListTypeEnum.PROJECT_SCHEME);
        long endTime0 = System.currentTimeMillis();
        log.debug("start0: 0-ganttPic阶段查询任务执行耗时: {} 毫秒", (endTime0 - endTime00));
        //更新状态
        statusProcessor.statusHandle(projectSchemes);
        long endTime1 = System.currentTimeMillis();
        log.debug("start1: 1-ganttPic一阶段查询任务执行耗时: {} 毫秒", (endTime1 - endTime0));

        List<SchemeGanttVO> result = CollUtil.toList();
        Map<String, Integer> indexMap = new HashMap<>(projectSchemes.size());
        initIndexMap(projectSchemes, indexMap);
        List<ProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(projectSchemes, ProjectSchemeVO::new);
        commonManager.codeMapping1(schemeVOS);
        Map<String, List<ProjectSchemeVO>> childMap = schemeVOS.stream().collect(Collectors.groupingBy(ProjectSchemeVO::getParentId));
        long endTime2 = System.currentTimeMillis();
        log.debug("start2: 2-ganttPic二阶段查询任务执行耗时: {} 毫秒", (endTime2 - endTime1));
        for (int i = 0; i < schemeVOS.size(); i++) {
            ProjectSchemeVO item = schemeVOS.get(i);
//            if (item.getParentId() == null || item.getParentId().equals("0")) {
                SchemeGanttVO ganttVO = buildSchemeGanttVO(schemeVOS, indexMap, item);
                result.add(ganttVO);
                addChild(childMap, indexMap, schemeVOS, item, result);
//            }
        }
        long endTime3 = System.currentTimeMillis();
        log.debug("start3: 3-ganttPic三阶段查询任务执行耗时: {} 毫秒", (endTime3 - endTime2));
        //未开始个数
        int noBegin = (int)projectSchemes.stream().filter(item -> item.getCircumstance().equals(0)).count();
        long begin1 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_NORMAL.getCode())).count();
        long begin2 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_NEAR.getCode())).count();
        long begin3 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_FALLBACK.getCode())).count();
        long begin4 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_WAIT.getCode())).count();
        //进行中个数
        int beginning = (int) (begin1+begin2+begin3+begin4);
        long complete1 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode())).count();
        long complete2 = projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode())).count();
        //完成个数
        int complete = (int) (complete1+complete2);
        //逾期个数
        int overdue = (int) projectSchemes.stream().filter(item -> item.getCircumstance().equals(Status.CIRCUMSTANCE_OVERD.getCode())).count();
        if(CollUtil.isNotEmpty(result)){
            for(SchemeGanttVO ganttVO :result){
                ganttVO.setNoBegin(noBegin);
                ganttVO.setBeginning(beginning);
                ganttVO.setComplete(complete);
                ganttVO.setOverdue(overdue);
            }
        }
        long endTime4 = System.currentTimeMillis();
        log.debug("start4: 4-ganttPic四阶段查询任务执行耗时: {} 毫秒", (endTime4 - endTime3));
        return result;
    }

    private void addChild(Map<String, List<ProjectSchemeVO>> childMap,
                          Map<String, Integer> indexMap,
                          List<ProjectSchemeVO> projectSchemes,
                          ProjectSchemeVO projectScheme,
                          List<SchemeGanttVO> result) {
        List<ProjectSchemeVO> child = childMap.get(projectScheme.getId());
        if (!CollectionUtils.isEmpty(child)) {
            for (ProjectSchemeVO item : child) {
                SchemeGanttVO ganttVO = buildSchemeGanttVO(projectSchemes, indexMap, item);
                result.add(ganttVO);
                addChild(childMap, indexMap, projectSchemes, item, result);
            }
        }
    }

    private SchemeGanttVO buildSchemeGanttVO(List<ProjectSchemeVO> projectSchemes, Map<String, Integer> indexMap, ProjectSchemeVO item) {
        return SchemeGanttVO.builder()
                .id(indexMap.get(item.getId()))
                .planId(item.getId())
                .parentId(item.getParentId())
                .name(item.getName())
                .progress(0F)
                .hasChild(hasChild(projectSchemes, item.getId()))
                .collapsed(false)
                .level(countLevel(projectSchemes, item, 0))
                .status(buidlStatus(item))
//                .depends(getPreId(item.getId(), indexMap))
                .start(item.getBeginTime().getTime())
                .end(item.getEndTime().getTime())
                .duration(workDay(item.getBeginTime(), item.getEndTime()))
                .warning(Status.CIRCUMSTANCE_OVERD.getCode().equals(item.getCircumstance()) ? "逾期预警" : null)
                .leftText(item.getRspSubDeptName()+"："+item.getRspUserName())
                .canWrite(false)
                .taskType(item.getNodeType())
                .createTime(item.getCreateTime())
                .build();
    }

    private Status buidlStatus(ProjectSchemeVO item) {
        if (!Status.FINISHED.getCode().equals(item.getStatus())) {
            if (Status.CIRCUMSTANCE_OVERD.getCode().equals(item.getCircumstance())) {
                return Status.getStatus(item.getCircumstance());
            }
        }
        return Status.getStatus(item.getStatus());
    }

    private long workDay(Date beginDate, Date endDate) {
        long count = 0;
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(beginDate);
        cal2.setTime(endDate);
        while (cal1.compareTo(cal2) <= 0) {
            if (cal1.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
                    && cal1.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
                count++;
            }
            cal1.add(Calendar.DAY_OF_MONTH, 1);
        }
        return count;
    }

    private void initIndexMap(List<ProjectScheme> projectSchemes, Map<String, Integer> indexMap) {
        for (int i = 0; i < projectSchemes.size(); i++) {
            indexMap.put(projectSchemes.get(i).getId(), i + 1);
        }
    }

    private Integer countLevel(List<ProjectSchemeVO> list, ProjectSchemeVO projectScheme, int level) {
        String parentId = projectScheme.getParentId();
        if ("0".equals(parentId)) {
            return level;
        }
        List<ProjectSchemeVO> parentIdList = list.stream().filter(item -> parentId.equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(parentIdList)) {
            level++;
            level = countLevel(list, parentIdList.get(0), level);
        }
        return level;
    }

    private String getLeftText(String resUserId, String rspSubDept) {
        UserVO user = userRedisHelper.getUserById(resUserId);
        if (Objects.isNull(user)) {
            return null;
        }
        String userName = user.getName();
        DeptVO deptVO = deptRedisHelper.getDeptById(rspSubDept);
        if (Objects.nonNull(deptVO)) {
            String deptName = deptVO.getName();
            if (StrUtil.isNotBlank(userName) && StrUtil.isNotBlank(deptName)) {
                return String.format("%s：%s", deptName, userName);
            } else if (StrUtil.isNotBlank(userName)) {
                return userName;
            } else if (StrUtil.isNotBlank(deptName)) {
                return deptName;
            }
        }
        return userName;
    }

    private String getPreId(String id, Map<String, Integer> indexMap) {
        try {
            List<ProjectSchemePrePost> projectSchemePrePosts = schemePrePostService.list(
                    new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                            .eq(ProjectSchemePrePost::getProjectSchemeId, id));
            if (CollUtil.isNotEmpty(projectSchemePrePosts)) {
                StringBuffer preIdBuffer = new StringBuffer();
                projectSchemePrePosts.stream().map(ProjectSchemePrePost::getPreSchemeId).filter(StrUtil::isNotBlank)
                        .forEach(item -> preIdBuffer.append(indexMap.get(item)).append(","));
                return preIdBuffer.toString().substring(0, preIdBuffer.toString().lastIndexOf(","));
            }
        } catch (Exception e) {
            log.error("mysql error: ", e);
        }
        return null;
    }

    private boolean hasChild(List<ProjectSchemeVO> projectSchemes, String id) {
        return projectSchemes.stream().anyMatch(item -> item.getParentId().equals(id));
    }

}
