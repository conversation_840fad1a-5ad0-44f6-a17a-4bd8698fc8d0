package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PersonExecuteDTO {

    @ApiModelProperty("人员id")
    @Deprecated
    String personId;
    @NotEmpty(message = "关系id不能为空")
    String relationId;
    @ApiModelProperty(value = "是否常驻")
    Boolean isBasePermanent;

    @ApiModelProperty(value = "实际离场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    Date actOutDate;

    @ApiModelProperty(value = "离厂原因")
    String leaveReason;

    @ApiModelProperty(value = "完成离场工作交接")
    Boolean isFinishOutHandover;

    @ApiModelProperty(value = "再次入场")
    Boolean isAgainIn;

    @ApiModelProperty(value = "状态")
    Integer status;

    @ApiModelProperty(value = "基地code")
    private String baseCode;
    @ApiModelProperty(value = "驻地名称")
    private String baseName;
}
