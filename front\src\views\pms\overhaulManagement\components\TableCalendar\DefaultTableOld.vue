<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import {
  computed, nextTick, Ref, ref, watch,
} from 'vue';
import { Menu, MenuItem, message } from 'ant-design-vue';
import { DeleteOutlined } from '@ant-design/icons-vue';
import _ from 'lodash-es';
import dayjs from 'dayjs';
import router from '/@/router';

interface AntVTableProps {
  quarterKey: number
  data: any[]
  type: string
  year: string
  repairRound: string
  isEdit: boolean
}

const props = defineProps<AntVTableProps>();
const emits = defineEmits<{
  (e: 'close')
}>();

function addRowSpan(data) {
  return data.map((item, index, array) => {
    // 如果dataType是'2'，并且下一个元素的dataType是'3'，则设置rowSpan为2
    if (item.dataType === '2' && index + 1 < array.length && array[index + 1].dataType === '3') {
      return {
        ...item,
        rowSpan: 2,
      };
    }
    // 如果dataType是'3'，并且前一个元素的dataType是'2'，则设置rowSpan为0
    if (item.dataType === '3' && index - 1 >= 0 && array[index - 1].dataType === '2') {
      return {
        ...item,
        rowSpan: 0,
      };
    }
    return item;
  });
}

const dataSource: Ref<any[]> = ref([]);
watch(() => props.data, (value) => {
  dataSource.value = _.cloneDeep(addRowSpan(value));
});

function customRender({ record, text, column }) {
  const obj = {
    props: {
      rowSpan: record?.rowSpan || 1,
    } as any,
    children: '' as any,
  };
  switch (column.dataIndex) {
    case 'userCode':
    case 'fullName':
    case 'materialCode':
    case 'materialName':
      if (record.dataType === '1') {
        obj.children = text;
      }
      break;
    case 'overlapCount':
      if (record.dataType !== '3') {
        obj.children = text || '';
      }
      break;
    case 'jobName':
      if (record.dataType === '2') {
        obj.children = text;
      }
      break;
    default:
      if (record.dataType !== '1') {
        obj.children = text;
      }
  }
  return obj;
}

function formatDateString(record: { inDate?: string, outDate?: string }) {
  let inDateString: string = '';
  let outDateString: string = '';
  if (record?.inDate) {
    inDateString = dayjs(record.inDate).format('YYYY.MM.DD');
  }
  if (record?.outDate) {
    outDateString = dayjs(record.outDate).format('YYYY.MM.DD');
  }
  return [inDateString, outDateString].filter((item) => item).join('-');
}

function getDateStrings(record) {
  return record?.inAndOutDateVOList?.filter((item) => true || item.currentQuarter)?.map((item) => formatDateString(item));
}

function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

function getDaysByQuarter(quarter, year: string | number) {
  const monthDays = [
    31,
    28,
    31,
    30,
    31,
    30,
    31,
    31,
    30,
    31,
    30,
    31,
  ];
  const quarterStartMonths = {
    1: 0, // January
    2: 3, // April
    3: 6, // July
    4: 9, // October
  };

  if (!quarterStartMonths[quarter] && quarterStartMonths[quarter] !== 0) {
    return 'Invalid quarter';
  }

  let startMonth = quarterStartMonths[quarter];
  let days = [];
  for (let month = startMonth; month < startMonth + 3; month++) {
    let daysInMonth = monthDays[month];
    if (month === 1 && isLeapYear(Number(year))) { // February in a leap year
      daysInMonth = 29;
    }
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        title: day === 1 ? `${month + 1}.${day}` : `${day}`,
        dataIndex: `${month + 1}-${day}`,
        width: 45,
        resizable: false,
        type: 'day',
      });
    }
  }
  return days;
}

const containerRef = ref();

function getColumns() {
  return [
    ...(props.type.includes('person') ? [
      {
        title: '员工号',
        dataIndex: 'userCode',
        fixed: 'left',
        align: 'center',
        width: 80,
        resizable: false,
        customRender,
      },
      {
        title: '姓名',
        dataIndex: 'fullName',
        fixed: 'left',
        align: 'center',
        width: 80,
        resizable: false,
        customRender,
      },
    ] : []),
    ...(props.type.includes('material') ? [
      {
        title: '物资编码',
        dataIndex: 'materialCode',
        fixed: 'left',
        align: 'center',
        width: 125,
        resizable: false,
        customRender,
      },
      {
        title: '物资名称',
        dataIndex: 'materialName',
        fixed: 'left',
        align: 'center',
        width: 80,
        resizable: false,
        customRender,
      },
    ] : []),
    ...(props.type.includes('Overlap') ? [
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
        fixed: 'left',
        align: 'center',
        width: 80,
        resizable: false,
        customRender,
      },
    ] : []),
    {
      title: '作业名称',
      dataIndex: 'jobName',
      fixed: 'left',
      width: 100,
      resizable: false,
      customRender,
    },
    ...(props.type.includes('Overlap') ? [
      {
        title: '重叠天数',
        dataIndex: 'overlapCount',
        fixed: 'left',
        align: 'center',
        width: 80,
        resizable: false,
        customRender,
      },
    ] : []),
    {
      title: '时间周期',
      dataIndex: 'dataType',
      width: 80,
      colSpan: 2,
      align: 'center',
      fixed: 'left',
      customRender({ text }) {
        switch (text) {
          case '1':
            return '计划进出';
          case '2':
            return '计划作业';
          case '3':
            return '计划任务';
        }
      },
    },
    {
      title: '',
      dataIndex: 'inAndOutDateVOList',
      width: 160,
      fixed: 'left',
      colSpan: 0,
      tooltip: {
        trigger: 'click',
        placement: 'topLeft',
        overlayStyle: {
          maxWidth: '475px',
        },
      },
      customRender({ record }) {
        return `${getDateStrings(record)?.[0] || ''}`;
      },
    },
    ...getDaysByQuarter(props.quarterKey + 1, props.year),
  ];
}

function customRow(record) {
  return {
    style: { background: record?.dataType === '1' ? '#F8F9FC !important' : record?.dataType === '2' ? '#FFFFFF !important' : '#F1FBF5 !important' },
  };
}

const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  rowKey: 'uniqueId',
  isSpacing: false,
  pagination: false,
  columns: getColumns(),
  canColDrag: false,
  customCell,
  customRow,
};

const datePositionCache = new Map<string, {
  positions: string[],
  idxArr: string[]
}>();

function checkDatePosition(targetDate: string, record: Record<string, any>): {
  positions: string[],
  idxArr: string[]
} {
  // 生成唯一的键值
  const dateRanges = record?.inAndOutDateVOList || [];
  const cacheKey = `${targetDate}_${dateRanges?.map((range) => range.inDate + range.outDate).join(',')}`;

  // 检查缓存中是否有结果
  if (datePositionCache.has(cacheKey)) {
    return datePositionCache.get(cacheKey)!;
  }

  // 计算并存储结果
  const positions = [];
  const idxArr = [];

  for (let i = 0; i < dateRanges.length; i++) {
    const {
      inDate, outDate, leaderData,
    } = dateRanges[i];

    const targetDateStr = dayjs(targetDate).valueOf();
    const inDateStr = dayjs(inDate).valueOf();
    const outDateStr = dayjs(outDate || inDate).valueOf();

    if (targetDateStr >= inDateStr && targetDateStr <= outDateStr && leaderData !== true) {
      idxArr.push(i);
    }

    if (targetDateStr === inDateStr) {
      positions.push('start');
      continue;
    }
    if (targetDateStr > inDateStr && targetDateStr < outDateStr) {
      positions.push('middle');
      continue;
    }
    if (targetDateStr === outDateStr) {
      positions.push('end');
    }
  }

  const arr = [...new Set(positions)];
  const result = {
    positions: arr.length > 1 ? ['middle'] : arr,
    idxArr,
  };
  datePositionCache.set(cacheKey, result);
  return result;
}

// 使用computed创建一个响应式的缓存
const cachedCounts = computed(() => new Map<string, number[]>());

// 任务重叠计算
function countMatchingDateRanges(targetDate: string | number, record: Record<string, any>, returnCount: boolean = true): number | number[] {
  const dateRanges = dataSource.value.filter((item) =>
    item.userCode === record.userCode && item.repairRound === props.repairRound && item.dataType === '3').reduce((prev, next) => {
    prev = prev.concat(next?.inAndOutDateVOList || []);
    return prev;
  }, []);

  // 创建一个唯一的键来区分不同的参数组合
  const key = `${record.userCode}-${props.repairRound}-${targetDate}-${dateRanges?.map((range) => range.inDate + range.outDate).join(',')}`;

  // 如果缓存中有结果，则直接返回
  if (cachedCounts.value.has(key)) {
    const result = cachedCounts.value.get(key);
    return returnCount ? result.length : result;
  }

  const targetDateObj = dayjs(targetDate).valueOf();
  let count = 0;
  let index = 0;
  const indexArr: number[] = [];

  for (const { inDate, outDate } of dateRanges) {
    const startDateObj = dayjs(inDate).valueOf();
    const endDateObj = dayjs(outDate || inDate).valueOf();
    if (targetDateObj >= startDateObj && targetDateObj <= endDateObj) {
      count++;
      indexArr.push(index);
    }
    index++;
  }

  // 存储结果到缓存中
  cachedCounts.value.set(key, indexArr);

  return returnCount ? count : indexArr;
}

// 使用computed创建一个响应式的缓存
const cachedOverlap = computed(() => new Map<string, boolean>());

function checkTargetDateOverlap(record, targetDate) {
  // 将目标日期转换为Date对象
  const target = new Date(targetDate).valueOf();
  const key = `${target}-${record.dataType}-${record?.inAndOutDateVOList?.map((item) => item.inDate + item.outDate + item.baseCode).join(',')}`;

  if (cachedOverlap.value.has(key)) {
    return cachedOverlap.value.get(key);
  }

  const overlaps = new Set();
  record?.inAndOutDateVOList?.forEach((item) => {
    const inDate = dayjs(item.inDate).valueOf();
    const outDate = dayjs(item.outDate).valueOf();
    const baseCode = item.baseCode;
    if (target >= inDate && target <= outDate) {
      if (!overlaps.has(baseCode)) {
        overlaps.add(baseCode);
      }
    }
  });
  cachedOverlap.value.set(key, overlaps.size > 1);
  return overlaps.size > 1;
}

// 选择任务单元格
function handleCell(record, column) {
  const date = dayjs(`${props.year}-${column.dataIndex}`).format('YYYY-MM-DD');
  if (record?.jobBeginDate && record?.jobEndDate) {
    if (dayjs(record.jobBeginDate).valueOf() > dayjs(date).valueOf() || dayjs(record.jobEndDate).valueOf() < dayjs(date).valueOf()) {
      return message.info('请选择计划作业时间周期内的日期');
    }
  }
  // record.inAndOutDateVOList = record?.inAndOutDateVOList?.filter((item) => (item.inDate && item.outDate) || item.edit);
  // if ((record?.inAndOutDateVOList.length > 0 && !record?.inAndOutDateVOList[0]?.edit)) {
  //   return message.info('任务计划时间已设置，请右键删除后操作');
  // }
  // if (!props.type.includes('Overlap') && (record?.inAndOutDateVOList.length > 0 && !record?.inAndOutDateVOList[0]?.edit)) {
  //   return message.info('任务计划时间已设置，请右键删除后操作');
  // }
  const editIndex = record?.inAndOutDateVOList?.findIndex((item) => item.statusText);

  if (editIndex !== -1) {
    const item = record.inAndOutDateVOList[editIndex];

    switch (item.statusText) {
      case 'add':
        item.inDate = date;
        item.statusText = 'edit';
        break;
      case 'edit':
        if (dayjs(date) < dayjs(item.inDate)) {
          item.outDate = _.cloneDeep(item.inDate);
          item.inDate = date;
        } else {
          item.outDate = date;
        }
        delete item.statusText;
        record.inAndOutDateVOList.splice(editIndex, 1, item);
        break;
    }
  } else {
    switch (record.dataType) {
      case '1':
        message.info('计划进出时间已设置，请右键删除后操作');
        break;
      case '3':
        message.info('计划任务时间已设置，请右键删除后操作');
        break;
    }
    // record.inAndOutDateVOList.push({
    //   type: record.dataType === '1' ? 0 : 1,
    //   statusText: 'edit',
    //   inDate: date,
    // });
  }
}

function getDateString(year, dataIndex) {
  return `${year}-${dataIndex}`;
}

function customCell({ column, record }) {
  if (column?.type === 'day') {
    try {
      const dateString = getDateString(props.year, column.dataIndex);

      const { positions } = checkDatePosition(`${props.year}-${column.dataIndex}`, record);

      const overlapTaskCount = countMatchingDateRanges(dateString, record);

      const overlapInOutFlag = checkTargetDateOverlap(record, dateString);

      if (positions && positions.length > 0) {
        const dataColorClass = record?.dataType === '1' ? 'in-out' : record?.dataType === '2' ? 'job' : 'task';
        let overlap = '';
        if ((overlapTaskCount > 0 && record?.dataType === '3' && record.repairRound !== props.repairRound) || (overlapInOutFlag && record?.dataType === '1')) {
          overlap = 'overlap';
        }

        return {
          class: `data-cell ${overlap} ${positions.join(' ')} ${dataColorClass}`,
        };
      }
      if (record?.dataType !== '2' && props.isEdit) {
        return {
          class: 'cp',
          onClick() {
            handleCell(record, column);
          },
        };
      }
      return {};
    } catch (error) {
      console.error('Error in day column rendering:', error);
      return {};
    }
  }
  if (column.dataIndex === 'jobName' && record?.dataType === '2') {
    return {
      class: `job-name-row ${record.rowSpan ? 'job-name-row-3' : 'job-name-row-1'}`,
      title: record.jobName,
      onClick() {
        if (record.jobId && props.repairRound) {
          emits('close');
          router.push({
            name: 'OverhaulOperationDetails',
            params: {
              id: record.jobId,
            },
            query: {
              id: props.repairRound,
            },
          });
        }
      },
    };
  }
}

function isShowMenu({ record, column }) {
  const { positions, idxArr } = checkDatePosition(`${props.year}-${column.dataIndex}`, record);
  switch (record.dataType) {
    case '1':
      const overlapInOutFlag = checkTargetDateOverlap(record.inAndOutDateVOList, `${props.year}-${column.dataIndex}`);
      const item = idxArr.length > 0 ? record?.inAndOutDateVOList[idxArr[idxArr.length - 1]] : null;
      return overlapInOutFlag;
    case '3':
      return record.dataType === '3' && positions && positions.length > 0;
  }
}

function menuClick({ record, column }) {
  const { idxArr } = checkDatePosition(`${props.year}-${column.dataIndex}`, record);
  if (idxArr.length > 0) {
    record.inAndOutDateVOList.splice(idxArr[idxArr.length - 1], 1, {
      type: record.dataType === '1' ? 0 : 1,
      statusText: 'add',
    });
  }
}

const tableRef = ref();

watch(() => props.quarterKey, () => {
  updateColumns();
});

async function updateColumns() {
  await nextTick();
  tableRef.value.setColumns(getColumns());
}

defineExpose({
  updateColumns,
  getData() {
    return dataSource.value;
  },
});
</script>

<template>
  <div
    ref="containerRef"
    style="flex-grow: 1;height:0;overflow: hidden;min-height: 400px"
  >
    <OrionTable
      ref="tableRef"
      bordered
      size="small"
      :data-source="dataSource"
      :options="tableOptions"
    >
      <!--tooltip-->
      <template #tooltipTitle="{ record }">
        <div
          class="tooltip-title"
          :style="{
            gridTemplateColumns: `repeat(${record?.inAndOutDateVOList?.length>=3?3:record?.inAndOutDateVOList?.length}, 140px)`
          }"
        >
          <span
            v-for="(item,index) in getDateStrings(record)"
            :key="index"
          >
            {{ item }}
          </span>
        </div>
      </template>

      <!--右键菜单-->
      <template
        v-if="isEdit"
        #contextmenuPopup="args"
      >
        <Menu
          v-if="isShowMenu(args)"
          style="width: 80px"
          @click="menuClick(args)"
        >
          <MenuItem key="del">
            <DeleteOutlined />
            删除
          </MenuItem>
        </Menu>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
:deep(.orion-table-header-wrap) {
  display: none;
}

:deep(.data-cell) {
  overflow: visible !important;

  .surely-table-cell-inner {
    position: absolute;
    content: '';
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 50% !important;
    background-color: #AFE8C5;
    z-index: 1;
    box-sizing: content-box;
  }

  &.start {
    .surely-table-cell-inner {
      padding-right: 1px !important;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
  }

  &.end {
    .surely-table-cell-inner {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }

  &.middle {
    .surely-table-cell-inner {
      padding-right: 1px !important;
      border-radius: 0;
    }
  }

  &.in-out {
    .surely-table-cell-inner {
      background-color: #B5C2DD;
    }
  }

  &.job {
    .surely-table-cell-inner {
      background-color: #AFE8C5;

      &::before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #60D38D;
      }
    }
  }

  &.task {
    .surely-table-cell-inner {
      background-color: #60D38D;
    }
  }

  &.overlap {
    .surely-table-cell-inner {
      background-color: #FF928A;
    }
  }
}

.tooltip-title {
  display: grid;
  gap: 10px 20px;
}

:deep(.job-name-row) {
  .surely-table-cell-inner {
    height: 100% !important;
  }

  .surely-table-cell-content {
    white-space: pre-wrap;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow-wrap: normal;
    word-break: normal;
    color: ~`getPrefixVar('primary-color')`;
    cursor: pointer;
    margin: 8px;
    max-height: 100%;
    padding: 0 !important;
    overflow: hidden;
  }

  &-1 {
    .surely-table-cell-content {
      -webkit-line-clamp: 1;
    }
  }

  &-3 {
    .surely-table-cell-content {
      -webkit-line-clamp: 3;
    }
  }
}

:deep(.ant-menu-item) {
  line-height: 24px;
  height: 24px;
}

:deep(.cp) {
  cursor: pointer;
}
</style>
