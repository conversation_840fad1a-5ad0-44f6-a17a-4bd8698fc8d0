import { h } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';

// 列数据
export function getColumns({ router }) {
  return [
    {
      title: '日期',
      dataIndex: 'daily',
      width: 250,
    },
    {
      title: '工作内容',
      dataIndex: 'content',
      customRender: ({ record }) => {
        let name = '';
        if (record && record?.projectDailyStatementContentVOList?.length) {
          name = record?.projectDailyStatementContentVOList.map((item) => item.content).join(', ');
        }
        return h('span', {
          title: name,
          class: 'action-btn',
          onClick: () => {
            router.push({
              name: 'DayReportDetails',
              query: {
                id: record?.projectId,
                curId: record?.id,
              },
            });
          },
        }, name);
      },
      // slots: { customRender: 'name' },
    },
    {
      title: '关联对象',
      dataIndex: 'relationshipName',
      // slots: { customRender: 'name' },
      customRender: ({ record }) => {
        if (record && record?.projectDailyStatementContentVOList?.length) {
          return record?.projectDailyStatementContentVOList.map((item) => item.relationshipName).join(', ');
        }
      },
    },

    {
      title: '责任人',
      dataIndex: 'creatorName',
      width: 120,
    },
    {
      title: '整体进度',
      dataIndex: 'statusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '状态',
      dataIndex: 'busStatusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.busStatusName }),
    },
    {
      title: '评分',
      dataIndex: 'score',
      width: 120,
    },
    {
      title: '评价',
      dataIndex: 'evaluate',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];
}
