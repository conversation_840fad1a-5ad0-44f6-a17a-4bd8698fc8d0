package com.chinasie.orion.domain.vo.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/18/16:05
 * @description:
 */
@Data
public class SearchVO implements Serializable {
    @ApiModelProperty(value = "关键词")
    private String keyword;
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "大修轮次不能为空")
    private String repairRound;
    @ApiModelProperty(value = "大修组织ID")
    private String repairOrgId;
}
