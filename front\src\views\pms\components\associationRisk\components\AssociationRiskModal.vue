<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    questionId:string
}>(), {
  questionId: '',
});
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],

  api: (params) => {
    params.searchConditions = [
      [
        {
          field: 'status',
          fieldType: 'Integer',
          values: ['130'],
          queryType: 'eq',
        },
      ],
      [
        {
          field: 'status',
          fieldType: 'Integer',
          values: ['160'],
          queryType: 'eq',
        },
      ],
    ];
    params.query = {
      questionId: props.questionId,
    };
    return new Api('/pas').fetch(params, 'risk-management/getPage', 'POST');
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      width: '120px',
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 220,
    },
    {
      title: '风险描述',
      dataIndex: 'remark',
      width: '120px',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: '120px',
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },

    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      width: '80px',
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
      width: '100px',
    },
    {
      title: '影响程度',
      dataIndex: 'riskInfluenceName',
      width: '120px',
    },
    {
      title: '预估发生时间',
      dataIndex: 'predictStartTimeName',
      width: '120px',
    },

    {
      title: '负责人',
      dataIndex: 'principalName',
      width: '120px',
    },
    {
      title: '期望完成时间',
      dataIndex: 'predictEndTime',
      width: '170px',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },

    {
      title: '应对策略',
      dataIndex: 'copingStrategyName',
      width: '120px',
    },
    {
      title: '应对措施',
      dataIndex: 'copingStrategyName',
      width: '120px',
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择风险');
    return Promise.reject('');
  }
  await new Api('/pas').fetch(
    selectRows.map((item) => item.id),
    `questionRelationRisk/relationRisk/${props.questionId}`,
    'POST',
  );
  message.success('关联风险成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
