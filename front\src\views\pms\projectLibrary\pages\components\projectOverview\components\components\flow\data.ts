import { computed, Ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { treeMap, treeToList } from 'lyra-component-vue3';

export const nodeLists2: any = {
  edges: [
    //  开始到线索与需求
    {
      source: {
        cell: '0',
        port: 'start-port-out-outRight',
      },
      target: {
        cell: '1',
        port: 'parent-port-in-left',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: { name: 'normal' }, // 线条样式
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 12, // 字体大小
    },
    {
      source: {
        cell: '4-3',
        port: 'children-port-interiorCenter',
      },
      target: {
        cell: '0',
        port: 'start-port-out-outStart',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      }, // 线条样式
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
      stroke: '#999', // 线条字体颜色
      strokeDasharray: '2', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '线索入库', // 文本
      fill: '#999999', // 颜色
      fontSize: 12, // 字体大小
      strokeWidth: 0,
      fontFamily: 'Arial Normal',
      fontWeight: '500',
      vertices: [
        {
          x: 1143,
          y: 574,
        },
        {
          x: 80,
          y: 574,
        },
      ],
    },
    //  线索与需求
    {
      source: {
        cell: '1',
        port: 'parent-port-out-outWfBottom',
      },
      target: {
        cell: '0-1',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#f2f2f2',
      strokeDasharray: '',
      modifiable: '',
      text: '',
      fill: '',
      fontSize: 12,
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '0-2',
        port: 'children-port-interiorPoint',
      },
      target: {
        cell: '0-1',
        port: 'children-port-interiorOutPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      targetMarker: '', // 可以设置为目标标记的配置
      stroke: '',
      strokeDasharray: '5', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '匹配',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
    },
    {
      source: {
        cell: '0-3',
        port: 'children-port-interiorPoint',
      },
      target: {
        cell: '0-2',
        port: 'children-port-interiorOutPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      targetMarker: '', // 可以设置为目标标记的配置
      stroke: '',
      strokeDasharray: '5', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '匹配',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      style: {
        animation: 'ant-line 30s infinite linear',
      },
    },
    // 收入/销售管理
    {
      source: {
        cell: '0-1',
        port: 'children-port-outPointRight',
      },
      target: {
        cell: '2',
        port: 'parent-port-in-left',
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      vertices: [
        {
          x: 286,
          y: 200,
        },
        {
          x: 286,
          y: 125,
        },
      ],
    },
    // 收入/销售管理
    {
      source: {
        cell: '2',
        port: 'parent-port-out-LeftBottom',
      },
      target: {
        cell: '1-1',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '1-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '1-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-3',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '1-4',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-4',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '1-5',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '单一或多业务合并',
      fill: '#999', // 可以设置为文字填充色
      fontSize: 12, // 文字字体大小
      strokeWidth: 0,
      fontFamily: 'Arial Normal',
      fontWeight: '500',
      position: {
        distance: 0.66,
        offset: {
          x: 60,
          y: -10,
        },
      },
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-5',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '1-6',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '1-6',
        port: 'children-port-interiorCenter',
      },
      target: {
        cell: '0-2',
        port: 'children-port-inLeft',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 393,
          y: 530,
        },
        {
          x: 70,
          y: 530,
        },
        {
          x: 70,
          y: 260,
        },
      ],
    },
    {
      source: {
        cell: '0-2',
        port: 'children-port-right-outside',
      },
      target: {
        cell: '1-6',
        port: 'parent-port-in-top',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '', // 线条颜色
      strokeDasharray: '2', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 303,
          y: 260,
        },
        {
          x: 303,
          y: 483,
        },
      ],
    },
    {
      source: {
        cell: '1-4',
        port: 'children-port-outRight',
      },
      target: {
        cell: '3',
        port: 'parent-port-in-top',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 523,
          y: 350,
        },
        {
          x: 523,
          y: 73,
        },
        {
          x: 670,
          y: 73,
        },
      ],
    },
    {
      source: {
        cell: '1-4',
        port: 'children-port-outRight',
      },
      target: {
        cell: '4',
        port: 'parent-port-in-top',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 523,
          y: 350,
        },
        {
          x: 523,
          y: 63,
        },
        {
          x: 960,
          y: 63,
        },
      ],
    },
    {
      source: {
        cell: '1-6',
        port: 'children-port-outRight',
      },
      target: {
        cell: '3',
        port: 'parent-port-in-left',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '2', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 539,
          y: 480,
        },
        {
          x: 539,
          y: 125,
        },
      ],
    },
    {
      source: {
        cell: '1-6',
        port: 'children-port-outRight',
      },
      target: {
        cell: '4',
        port: 'parent-port-in-left',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '2', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 539,
          y: 480,
        },
        {
          x: 539,
          y: 79,
        },
        {
          x: 848,
          y: 79,
        },
        {
          x: 848,
          y: 126,
        },
      ],
    },
    {
      source: {
        cell: '3',
        port: 'parent-port-out-outFrBottom',
      },
      target: {
        cell: '2-5',
        port: 'children-port-inLeft',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      targetMarker: '', // 箭头
      stroke: '#f2f2f2', // 线条颜色
      strokeDasharray: '2', // 虚线样式，5像素实线，5像素空白
      modifiable: '', // 是否可编辑
      text: '', // 文本
      fill: '', // 线条颜色
      fontSize: 10, // 字体大小
      vertices: [
        {
          x: 599,
          y: 410,
        },
      ],
    },
    // 采购管理
    {
      source: {
        cell: '3',
        port: 'parent-port-out-right',
      },
      target: {
        cell: '3-1',
        port: 'children-port-inTopCenter',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      strokeDasharray: '2', // 可以设置为虚线的配置
      targetMarker: '', // 箭头
      stroke: '', // 线条颜色
      fill: '#999',
      fontSize: 12,
      strokeWidth: 0,
      fontFamily: 'Arial Normal',
      fontWeight: '500',
      position: {
        distance: 0.66,
        offset: {
          x: -60,
          y: -20,
        },
      },
      vertices: [
        {
          x: 775,
          y: 124,
        },
        {
          x: 775,
          y: 165,
        },
        {
          x: 953,
          y: 165,
        },
      ],
      text: '采购履约\n'
        + '智能匹配',
    },
    // 采购管理子级
    {
      source: {
        cell: '2-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '2-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '2-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '2-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '2-3',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '2-4',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '2-4',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '2-5',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '2-4',
        port: 'children-port-outRight',
      },
      target: {
        cell: '2-6',
        port: 'children-port-outRightTop',
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#f2f2f2',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#f2f2f2', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
      vertices: [
        {
          x: 772,
          y: 350,
        },
        {
          x: 772,
          y: 472,
        },
      ],
    },
    {
      source: {
        cell: '2-5',
        port: 'children-port-outRight',
      },
      target: {
        cell: '2-6',
        port: 'children-port-inTopCenter',
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#f2f2f2',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#f2f2f2', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: '', // 箭头
      vertices: [
        {
          x: 772,
          y: 410,
        },
        {
          x: 772,
          y: 436,
        },
        {
          x: 695,
          y: 436,
        },
      ],
    },
    {
      source: {
        cell: '2-6',
        port: 'children-port-interiorCenter',
      },
      target: {
        cell: '2-7',
        port: 'children-port-inLeftCenter',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#f2f2f2',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: '',
    },
    {
      source: {
        cell: '2-6',
        port: 'children-port-outRight',
      },
      target: {
        cell: '3-1',
        port: 'children-port-inLeft',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      stroke: '#f2f2f2',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#929292', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: '', // 箭头
      vertices: [
        {
          x: 836,
          y: 480,
        },
        {
          x: 836,
          y: 201,
        },
      ],
    },
    // 履约执行
    {
      source: {
        cell: '4',
        port: 'parent-port-out-bottom',
      },
      target: {
        cell: '3-1',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-2-1',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-2-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-2-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-2-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-2-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-2-3',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-3',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '3-4',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '3-2-3',
        port: 'children-port-outLeftRight',
      },
      target: {
        cell: '4-1',
        port: 'children-port-inLeft',
      },
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: '',
      vertices: [
        {
          x: 1050,
          y: 352,
        },
        {
          x: 1050,
          y: 201,
        },
      ],
    },
    // 验收交付
    {
      source: {
        cell: '5',
        port: 'parent-port-out-bottom',
      },
      target: {
        cell: '4-1',
        port: 'children-port-interiorPoint',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '4-1',
        port: 'children-port-outPointLeRight',
      },
      target: {
        cell: '6',
        port: 'parent-port-in-left',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      vertices: [
        {
          x: 1248,
          y: 200,
        },
        {
          x: 1248,
          y: 125,
        },
      ],
    },
    {
      source: {
        cell: '4-1',
        port: 'children-port-outPointLeRight',
      },
      target: {
        cell: '4-1-1',
        port: 'parent-port-in-left',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      vertices: [
        {
          x: 1214,
          y: 200,
        },
      ],
    },
    {
      source: {
        cell: '4-1-1',
        port: 'children-port-interiorCenter',
      },
      target: {
        cell: '4-2',
        port: 'children-port-inTopCenter',
      },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      connector: {
        name: 'rounded',
        args: {
          radius: 5,
        },
      },
      vertices: [
        {
          x: 1213,
          y: 358,
        },
        {
          x: 1143,
          y: 358,
        },
      ],
    },
    {
      source: {
        cell: '4-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '4-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '4-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '4-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    // 财务结算
    {
      source: {
        cell: '6',
        port: 'parent-port-out-outLeftCwJsBottom',
      },
      target: {
        cell: '5-1',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '5-1',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '5-2',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '5-2',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '5-3',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    {
      source: {
        cell: '5-3',
        port: 'children-port-interiorOutPoint',
      },
      target: {
        cell: '5-4',
        port: 'children-port-interiorPoint',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
      targetMarker: {
        // 假设你的库支持这样的配置
        name: 'block', // 箭头样式
        filled: true, // 箭头是否填充
        fill: '#000000', // 箭头颜色
        size: 10, // 箭头大小
        // 将箭头放置在边的起始位置
      },
    },
    // 结束
    {
      source: {
        cell: '5-4',
        port: 'children-port-interiorCenter',
      },
      target: {
        cell: '7',
        port: 'end-port-in-top',
      },
      connector: { name: 'normal' },
      isHighlight: 0, // 是否高亮 0 灰色 1 橙色 2 蓝色
      stroke: '#ccc',
      strokeDasharray: '', // 可以设置为虚线的配置
      modifiable: '', // 可以设置为可修改的配置
      text: '',
      fill: '#666', // 可以设置为文字填充色
      fontSize: 10, // 文字字体大小
    },
  ],
};

export function useNodeLists(nodes: Ref<any[]>) {
  const edges = computed(() => cloneDeep(nodeLists2.edges).map((item) => {
    const actions = treeToList(cloneDeep(nodes.value), { children: 'actions' }) || [];
    // 根据目标节点的状态来更新 isHighlight
    const targetActions = actions.filter((action) => action.id === item.source?.cell);
    const hasFinish = targetActions.some((action) => action.isFinish);
    const hasOver = targetActions.some((action) => action.isOver);
    const hasNot = targetActions.some((action) => action.isNot);

    if (hasFinish) {
      item.isHighlight = 2; // 蓝色
    } else if (hasOver) {
      item.isHighlight = 1; // 橙色
    } else if (hasNot) {
      item.isHighlight = 0; // 灰色
    }

    return item;
  }));
  return {
    edges,
  };
}
