package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.excel.annotations.ExcelSelected;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yk
 * @date: 2023/6/5 15:28
 * @description:
 */

/**
 * ProjectSchemeMilestoneNodeExcelDTO Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@ApiModel(value = "ProjectSchemeMilestoneNodeExcelDTO对象", description = "项目计划里程碑节点")
@Data
public class ProjectSchemeMilestoneNodeExcelDTO implements Serializable {

    @ExcelIgnore
    private Integer rowIndex;

    @ExcelProperty(value = "父级计划名称*",index = 0)
    private String parentName;

    @ExcelProperty(value = "计划名称*",index = 1)
    private String name;

    @ExcelProperty(value = "计划类型*",index = 2)
    @ExcelSelected(source = {"计划", "里程碑"})
    private String planType;

    @ExcelProperty(value = "责任部门*",index = 3)
    private String rspDeptName;

    @ExcelProperty(value = "计划工期（天）",index = 4)
    private String durationDays;

    @ExcelProperty(value = "是否关联流程*",index = 5)
    @ExcelSelected(source = {"是", "否"})
    private String processFlagName;

    @ExcelProperty(value = "描述",index = 6)
    private String remark;

}
