package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.entity.PlanToQualityItem;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToQualityItemMapper;
import com.chinasie.orion.service.PlanToQualityItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.chinasie.orion.cache.OrionJ2CacheService;




/**
 * <p>
 * PlanToQualityItem 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23 11:06:02
 */
@Service
@Slf4j
public class PlanToQualityItemServiceImpl extends OrionBaseServiceImpl<PlanToQualityItemMapper, PlanToQualityItem> implements PlanToQualityItemService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;



}
