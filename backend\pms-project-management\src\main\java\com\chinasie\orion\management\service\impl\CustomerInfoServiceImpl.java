package com.chinasie.orion.management.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.CachePlanBo;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.constant.*;
import com.chinasie.orion.management.constant.caigouUtil.SalesClassEnum;
import com.chinasie.orion.management.domain.dto.CustomerInfoDTO;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.vo.CustomerInfoVO;
import com.chinasie.orion.management.repository.CustomerInfoMapper;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import static com.chinasie.orion.exception.PMSErrorCode.KMS_EFFECT_DATA;


/**
 * <p>
 * customerInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@Service
@Slf4j
public class CustomerInfoServiceImpl extends OrionBaseServiceImpl<CustomerInfoMapper, CustomerInfo> implements CustomerInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private BasePlaceService basePlaceService;
    @Autowired
    private UserRedisHelper userRedisHelper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CustomerInfoVO detail(String id, String pageCode) throws Exception {
        CustomerInfo customerInfo = this.getById(id);
        CustomerInfoVO result = BeanCopyUtils.convertTo(customerInfo, CustomerInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param customerInfoDTO
     */
    @Override
    public String create(CustomerInfoDTO customerInfoDTO) throws Exception {
        CustomerInfo customerInfo = BeanCopyUtils.convertTo(customerInfoDTO, CustomerInfo::new);
        //客户名称重复性校验
        String cusName = customerInfoDTO.getCusName();
        Boolean flag = custNameCheck(cusName);
        if (flag) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "客户名称有重复！");
        }

        this.save(customerInfo);

        String rsp = customerInfo.getId();


        return rsp;
    }

    /**
     * 客户名称重复性校验
     *
     * @param cusName
     * @return
     * @throws Exception
     */
    public Boolean custNameCheck(String cusName) throws Exception {
        LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        customerInfoLambdaQueryWrapperX.eq(CustomerInfo::getCusName, cusName);
        List<CustomerInfo> list = this.list(customerInfoLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(list)) {
            return true;
        }
        return false;
    }

    /**
     * 编辑
     * <p>
     * * @param customerInfoDTO
     */
    @Override
    public Boolean edit(CustomerInfoDTO customerInfoDTO) throws Exception {
        CustomerInfo customerInfo = BeanCopyUtils.convertTo(customerInfoDTO, CustomerInfo::new);
        this.updateById(customerInfo);
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

        for (String id : ids) {
            CustomerInfo customerInfo = this.getById(id);
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                String flag = customerInfo.getIsUsed();
                if (ObjectUtil.isNotEmpty(flag) && "1".equals(flag)) {
                    throw new PMSException(KMS_EFFECT_DATA, "存在已合作客户，不支持删除。");
                }
            }

        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CustomerInfoVO> pages(Page<CustomerInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CustomerInfo> condition = new LambdaQueryWrapperX<>(CustomerInfo.class);
        CustomerInfoDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            if (ObjectUtil.isNotEmpty(query.getCusNumber()) && ObjectUtil.isNotEmpty(query.getCusName())) {
                condition.and(item -> {
                    item.like(CustomerInfo::getCusNumber, query.getCusNumber()).or().like(CustomerInfo::getCusName, query.getCusName());
                });
            }
        }
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CustomerInfo::getCreateTime);


        Page<CustomerInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CustomerInfo::new));

        PageResult<CustomerInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CustomerInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CustomerInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CustomerInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "客户导入模板.xlsm";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("客户导入模板.xlsx", StandardCharsets.UTF_8));


        ExcelUtils.writeTemplate(response, CustomerInfoDTO.class, "客户导入模版.xlsx", "客户导入模板", "客户导入模板");

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        customerInfoExcelListener excelReadListener = new customerInfoExcelListener();
        EasyExcel.read(inputStream, CustomerInfoDTO.class, excelReadListener).sheet().headRowNumber(2).doRead();
        List<CustomerInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        // 判断客户是否重复
        Map<String, String> newCollect = dtoS.stream().collect(Collectors.toMap(CustomerInfoDTO::getId, CustomerInfoDTO::getCusName));
        Map<String, String> oldCollect = this.list().stream().collect(Collectors.toMap(CustomerInfo::getCusName, CustomerInfo::getId));
        for (Map.Entry<String, String> entry : newCollect.entrySet()) {
            String id = oldCollect.getOrDefault(entry.getValue(), "");
            if (StringUtils.hasText(id) && !id.equals(entry.getKey())) {
                result.setCode(400);
                result.setOom("客户名称重复，请检查");
                return result;
            }
        }
        log.info("客户管理导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<CustomerInfo> customerInfoes = BeanCopyUtils.convertListTo(dtoS, CustomerInfo::new);
        for (CustomerInfo customerInfoe : customerInfoes) {
            for (CustomerInfoDTO dto : dtoS) {
                if (customerInfoe.getId().equals(dto.getId())) {
                    customerInfoe.setCusNumber(dto.getId());
                    customerInfoe.setId(UUID.randomUUID().toString());
                }
            }
        }
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::customerInfo-import::id", importId, customerInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<CustomerInfo> customerInfoes = (List<CustomerInfo>) orionJ2CacheService.get("pms::customerInfo-import::id", importId);
        log.info("客户管理导入的入库数据={}", JSONUtil.toJsonStr(customerInfoes));
        //所属行业
        List<DictValueVO> industryList = dictRedisHelper.getDictList("dict1804441009536851968");
        Map<String, String> industryMap = industryList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v2));
        //客户关系
        List<DictValueVO> groupInOutList = dictRedisHelper.getDictList("dict1804392716872900608");
        Map<String, String> groupInOutMap = groupInOutList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v2));
        //业务收入类型
        List<DictValueVO> ywsrlxList = dictRedisHelper.getDictList("dict1804393678006050816");
        Map<String, String> ywsrlxMap = ywsrlxList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v2));
        //客户范围
        List<DictValueVO> busScopeList = dictRedisHelper.getDictList("dict1804392913271185408");
        Map<String, String> busScopeMap = busScopeList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v2));
        //客户状态
        List<DictValueVO> cusStatusList = dictRedisHelper.getDictList("dict1804393066195509248");
        Map<String, String> cusStatusMap = cusStatusList.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v2));

        for (CustomerInfo customerInfo : customerInfoes) {
            String industry = customerInfo.getIndustry();
            String groupInOut = customerInfo.getGroupInOut();
            String ywsrlx = customerInfo.getYwsrlx();
            String busScope = customerInfo.getBusScope();
            String cusStatus = customerInfo.getCusStatus();
            if (industryMap.containsKey(industry)) {
                customerInfo.setIndustry(industryMap.get(customerInfo.getIndustry()));
            }
            if (groupInOutMap.containsKey(groupInOut)) {
                customerInfo.setGroupInOut(groupInOutMap.get(customerInfo.getGroupInOut()));
            }
            if (ywsrlxMap.containsKey(ywsrlx)) {
                customerInfo.setYwsrlx(ywsrlxMap.get(customerInfo.getYwsrlx()));
            }
            if (busScopeMap.containsKey(busScope)) {
                customerInfo.setBusScope(busScopeMap.get(customerInfo.getBusScope()));
            }
            if (cusStatusMap.containsKey(cusStatus)) {
                customerInfo.setCusStatus(cusStatusMap.get(customerInfo.getCusStatus()));
            }
            customerInfo.setCreateTime(new Date());
            customerInfo.setCreatorId(CurrentUserHelper.getCurrentUserId());
        }
        for (CustomerInfo customerInfoe : customerInfoes) {
            String cusNumber = customerInfoe.getCusNumber();
            LambdaQueryWrapperX<CustomerInfo> wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.eq(CustomerInfo::getCusNumber, cusNumber);
            List<CustomerInfo> list = this.list(wrapperX);
            if (ObjectUtil.isNotEmpty(list)) {
                this.updateById(customerInfoe);
            } else {
                this.save(customerInfoe);
            }
        }
        // this.saveOrUpdateBatch(customerInfoes);
//        this.saveBatch(customerInfoes);
        orionJ2CacheService.delete("pms::customerInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::customerInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<String> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CustomerInfo> condition = new LambdaQueryWrapperX<>(CustomerInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            condition.in(CustomerInfo::getId, searchConditions);
        }
        condition.orderByDesc(CustomerInfo::getCreateTime);
        List<CustomerInfo> customerInfoes = this.list(condition);

        List<CustomerInfoDTO> dtos = BeanCopyUtils.convertListTo(customerInfoes, CustomerInfoDTO::new);

        dtos.forEach(dto -> {
            dto.setCusLevel(CustomerLevelEnum.getDesc(dto.getCusLevel()));
            dto.setIndustry(CustomerIndustryEnum.getDesc(dto.getIndustry()));
            dto.setYwsrlx(IncomeTypeEnum.getDesc(dto.getYwsrlx()));
            dto.setBusScope(CustomerScopeEnum.getDesc(dto.getBusScope()));
            dto.setGroupInOut(CustomerRelationshipEnum.getDesc(dto.getGroupInOut()));
            dto.setCusStatus(CustomerStatusEnum.getDesc(dto.getCusStatus()));
        });

        String fileName = "客户管理数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CustomerInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<CustomerInfoVO> vos) throws Exception {
        List<String> homeBases = vos.stream().map(CustomerInfoVO::getHomeBase).collect(Collectors.toList());
        List<String> userAll=new ArrayList<>();
        List<String> creatUserIds = vos.stream().map(CustomerInfoVO::getCreatorId).distinct().collect(Collectors.toList());
        List<String> modifyUserIds = vos.stream().map(CustomerInfoVO::getModifyId).distinct().collect(Collectors.toList());
        userAll.addAll(creatUserIds);
        userAll.addAll(modifyUserIds);
        List<String> collectAllUser = userAll.stream().filter(x -> ObjectUtil.isNotEmpty(x)).distinct().collect(Collectors.toList());
        Map<String, String> collectMap=new HashMap<>();
        if (ObjectUtil.isNotEmpty(collectAllUser)){
            List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(collectAllUser);
            //名称
            collectMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        }
        Map<String, String> nameByCode = basePlaceService.getNameByCode(homeBases);
        if (!CollectionUtils.isEmpty(vos)) {
            Map<String, String> finalCollectMap = collectMap;
            vos.forEach(vo -> {
                //传递过来的是pmsx_base_place的code
                String homeBase = vo.getHomeBase();
                if (ObjectUtil.isNotEmpty(homeBase)) {

                    if (ObjectUtil.isNotEmpty(nameByCode)) {
                        vo.setHomeBaseName(nameByCode.get(vo.getHomeBase()));
                    }
                }

                vo.setYwsrlxName(IncomeTypeEnum.getDesc(vo.getYwsrlx()));
                vo.setCusLevelName(CustomerLevelEnum.getDesc(vo.getCusLevel()));
                vo.setBusScopeName(CustomerScopeEnum.getDesc(vo.getBusScope()));
                vo.setGroupInOutName(CustomerRelationshipEnum.getDesc(vo.getGroupInOut()));
                vo.setCusStatusName(CustomerStatusEnum.getDesc(vo.getCusStatus()));
                vo.setIndustryName(CustomerIndustryEnum.getDesc(vo.getIndustry()));
                vo.setSalesClassName(SalesClassEnum.getDesc(vo.getSalesClass()));
                if (ObjectUtil.isNotEmpty(finalCollectMap)) {
                    vo.setCreatorName(finalCollectMap.getOrDefault(vo.getCreatorId(),""));
                    vo.setModifyName(finalCollectMap.getOrDefault(vo.getModifyId(),""));
                }
            });
        }


    }

    @Override
    public String open(String id, String pageCode) throws Exception {
        CustomerInfo customerInfo = this.getById(id);
        if (ObjectUtil.isNotEmpty(customerInfo)) {
            customerInfo.setCusStatus("enable");
            this.save(customerInfo);
        }
        return customerInfo.getId();
    }

    @Override
    public String close(String id, String pageCode) throws Exception {
        CustomerInfo customerInfo = this.getById(id);
        if (ObjectUtil.isNotEmpty(customerInfo)) {
            customerInfo.setCusStatus("disable");
            this.save(customerInfo);
        }
        return customerInfo.getId();
    }


    public static class customerInfoExcelListener extends AnalysisEventListener<CustomerInfoDTO> {

        private final List<CustomerInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(CustomerInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<CustomerInfoDTO> getData() {
            return data;
        }
    }


    @Override
    public Page<CustomerInfoVO> getIncomeCustomerInfoPages(Page<CustomerInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CustomerInfo> condition = new LambdaQueryWrapperX<>(CustomerInfo.class);
        CustomerInfoDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            if (ObjectUtil.isNotEmpty(query.getCusName())) {
                condition.and(item -> {
                    item.like(CustomerInfo::getCusNumber, query.getCusName()).or().like(CustomerInfo::getCusName, query.getCusName());
                });
            }
        }
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CustomerInfo::getCreateTime);


        Page<CustomerInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CustomerInfo::new));

        PageResult<CustomerInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CustomerInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CustomerInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CustomerInfoVO::new);
        setEveryName(vos);
        if (pageRequest.getPageNum() == 1) {
            CustomerInfoVO customerInfoVO = new CustomerInfoVO();
            customerInfoVO.setId("0");
            customerInfoVO.setCusName("临时客户");
            vos.add(0, customerInfoVO);
        }
        pageResult.setContent(vos);

        return pageResult;
    }

}
