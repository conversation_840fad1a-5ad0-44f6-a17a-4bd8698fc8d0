import { DataStatusTag, IOrionTableOptions } from 'lyra-component-vue3';
import { h } from 'vue';
import dayjs from 'dayjs';
import { ToolbarButtonEnums } from './types';
import { getPlanPersonPage } from '/@/views/pms/api';
import { ActionButtons } from '../../components';

interface IGetTableOptions {
  toolbarClick: (btnTypes: ToolbarButtonEnums, rows?:any[])=>void;
  nameClick: (record: any) => void
  actionClick: (key: string, record: any) => void
}

export function getTableOptions(props: IGetTableOptions):IOrionTableOptions {
  const { toolbarClick, actionClick } = props;

  const actions = [
    {
      label: '编辑',
      key: 'edit',
    },
    {
      label: '删除',
      key: 'delete',
    },
  ];

  return {
    // @ts-ignore
    rowSelection: {},
    showToolButton: false,
    api(params) {
      return getPlanPersonPage(params);
    },
    tool: [
      {
        type: 'button',
        position: 'before',
        buttonGroup: [
          [
            {
              name: '创建计划',
              enable: true,
              icon: 'fa-plus',
              componentProps: {
                type: 'primary',
              },
              cb(records) {
                toolbarClick(ToolbarButtonEnums.CREATE);
              },
            },
          ],
        ],
      },
    ],
    columns: [
      {
        title: '编号',
        dataIndex: 'number',
        width: 130,
      },
      {
        title: '计划名称',
        dataIndex: 'name',
        slots: { customRender: 'name' },
        minWidth: 300,
        customRender({
          text, record, index, column,
        }) {
          return h('span', {
            title: text,
            // class: 'flex flex-te',
          }, [
            h('div', {
              class: 'action-btn flex-te',
              onClick(e:Event) {
                e.stopPropagation();
                props?.nameClick(record);
              },
            }, text),
          ]);
        },
      },
      // {
      //   title: '密级',
      //   dataIndex: 'number',
      //   width: 200,
      // },
      {
        title: '计划类型',
        dataIndex: 'planTypeName',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'data4',
        width: 100,
        customRender({ record }) {
          return h(DataStatusTag, {
            statusData: record.dataStatus,
          });
        },
      },
      {
        title: '进度状态',
        dataIndex: 'speedStatus',
        width: 100,
      },
      {
        title: '责任单位',
        dataIndex: 'resOrgName',
        width: 100,
      },
      {
        title: '责任科室',
        dataIndex: 'resDeptName',
        width: 100,
      },
      {
        title: '责任人',
        dataIndex: 'resPersonName',
        width: 100,
      },
      {
        title: '计划开始日期',
        dataIndex: 'startTime',
        width: 120,
        resizable: true,
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },
      {
        title: '计划结束日期',
        dataIndex: 'endTime',
        width: 120,
        resizable: true,
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },

      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 120,
        customRender({
          record, index, column,
        }) {
          return h(ActionButtons, {
            record,
            actionClick,
            actions,
          });
        },
      },
    ],
  };
}
