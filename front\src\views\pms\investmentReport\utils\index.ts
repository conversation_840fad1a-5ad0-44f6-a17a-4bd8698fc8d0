export function calculatePercentage(numerator: number | string, denominator: number | string): string {
  // 第一个分子，第二个是分母
  const num = typeof numerator === 'number' ? numerator : parseFloat(numerator);
  const den = typeof denominator === 'number' ? denominator : parseFloat(denominator);
  if (num === 0 || !num) {
    return '0.00%';
  }
  if (den === 0 || !den) {
    return '100%';
  }

  return `${((num / den) * 100).toFixed(2)}%`;
}