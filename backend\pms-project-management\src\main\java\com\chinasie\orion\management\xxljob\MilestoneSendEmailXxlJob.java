package com.chinasie.orion.management.xxljob;


import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.entity.EmailRecord;
import com.chinasie.orion.management.domain.vo.ContractXxlJobVO;
import com.chinasie.orion.management.repository.ContractInfoMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 每隔三十天需要向里程碑的技术接口人进行消息和邮件通知
 */
@Component
public class MilestoneSendEmailXxlJob {

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Resource
    MscBuildHandlerManager mscBuildHandlerManager;


    @XxlJob("milestoneSendEmailJob")
    public void purchaseDateMountJobHandler() throws ParseException {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, - 30);
        Date start = c.getTime();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        //前30天日期
        SimpleDateFormat formatDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = simpleDateFormat.format(start);
        String startTime = format+ " 00:00:00";
        String endTime = format+ " 23:59:59";
        Date startNow = formatDate.parse(startTime);
        Date endNow = formatDate.parse(endTime);

        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( ContractMilestone. class);
        lambdaQueryWrapperX.between(ContractMilestone::getIsTrackConfirm,startNow,endNow);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(lambdaQueryWrapperX);

        Map<String,List<ContractMilestone>> map=new HashMap<>();
        for (ContractMilestone contractMilestone : contractMilestones) {
            if (!StringUtils.isEmpty(contractMilestone.getTechRspUser())){
                if (!map.containsKey(contractMilestone.getTechRspUser())){
                    List<ContractMilestone> temp=new ArrayList<>();
                    temp.add(contractMilestone);
                    map.put(contractMilestone.getTechRspUser(),temp);

                }else {
                    map.get(contractMilestone.getTechRspUser()).add(contractMilestone);
                }
            }
        }

        for (Map.Entry<String, List<ContractMilestone>> stringListEntry : map.entrySet()) {
            String key = stringListEntry.getKey();
            List<ContractMilestone> value = stringListEntry.getValue();
            mscBuildHandlerManager.send(value.get(0),MsgHandlerConstant.NOOD_ONE,key,value);
        }
    }
}
