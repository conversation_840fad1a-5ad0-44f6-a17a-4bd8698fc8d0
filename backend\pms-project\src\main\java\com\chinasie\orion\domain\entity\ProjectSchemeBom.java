package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectSchemeBom Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:50:50
 */
@TableName(value = "pmsx_project_scheme_bom")
@ApiModel(value = "ProjectSchemeBomEntity对象", description = "项目计划bom信息表")
@Data

public class ProjectSchemeBom extends  ObjectEntity  implements Serializable{

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @TableField(value = "materiel_name")
    private String materielName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @TableField(value = "materiel_code")
    private String materielCode;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "version")
    private String version;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @TableField(value = "materiel_type")
    private String materielType;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "amount")
    private String amount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "use_num")
    private String useNum;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    @TableField(value = "materiel_status")
    private String materielStatus;

    /**
     * plm创建人id
     */
    @ApiModelProperty(value = "plm创建人id")
    @TableField(value = "plm_creator")
    private String plmCreator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "plm_create_time")
    private String plmCreateTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "plm_modify")
    private String plmModify;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "plm_last_modify_time")
    private String plmLastModifyTime;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @TableField(value = "scheme_id")
    private String schemeId;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_code")
    private String parentCode;

}
