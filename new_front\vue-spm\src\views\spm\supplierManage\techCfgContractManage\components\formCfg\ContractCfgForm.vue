<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  computed, reactive, ref,
} from 'vue';
import { get, map } from 'lodash-es';
import ContractBasicInfo from '../common/ContractBasicInfo.vue';
import ContractEmployer from '../common/ContractEmployer.vue';
import ContractEmploymentPlan from '../common/ContractEmploymentPlan.vue';
import ContractAppraisalClause from '../common/ContractAppraisalClause.vue';
import Api from '/@/api';
import { useContractPlanDetail } from '../../hooks/useContractPlanDetail';

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
});
const isOperable = computed(() => get(props, 'record.type') === 'edit');
const { basicContractEmployerPlan } = useContractPlanDetail(get(props, 'record.contractNumber'));
const tableRef = ref();
const tableRef2 = ref();
const tableRef3 = ref();

defineExpose({
  onSubmit() {
    const contractEmployerData = tableRef.value?.exportTableData();
    const contractEmploymentPlanData = tableRef2.value?.exportTableData();
    const contractAppraisalClauseData = tableRef3.value?.exportTableData();

    const bodyParams = {
      contractAssessmentStandards: contractAppraisalClauseData,
      contractCenters: contractEmployerData,
      contractCostTypes: map(contractEmploymentPlanData, (item) => ({
        contractNumber: get(item, 'contractNumber'),
        costTypeNumber: get(item, 'costTypeNumber'),
        costName: get(item, 'costName'),
        costTypeName: get(item, 'costTypeName'),
        unitPrice: get(item, 'unitPrice'),
        unit: get(item, 'unit'),
      })),
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
    };
    return new Promise((resolve, reject) => {
      new Api('/spm/contractMain/establishment').fetch(bodyParams, '', 'PUT').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <ContractBasicInfo />
  <template v-if="basicContractEmployerPlan?.contractNumber">
    <BasicCard
      title="合同用人单位"
      :isBorder="false"
    >
      <ContractEmployer
        ref="tableRef"
        :is-operable="isOperable"
      />
    </BasicCard>
    <BasicCard
      title="合同用人计划"
      :isBorder="false"
    >
      <ContractEmploymentPlan
        ref="tableRef2"
        :show-summary-row="!isOperable"
        :is-operable="isOperable"
      />
    </BasicCard>
    <BasicCard
      title="合同考核条款"
      :isBorder="false"
    >
      <ContractAppraisalClause
        ref="tableRef3"
        :is-operable="isOperable"
      />
    </BasicCard>
  </template>
</template>

<style scoped lang="less">

</style>