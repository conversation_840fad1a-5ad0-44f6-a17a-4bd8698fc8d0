package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MajorRepairPlanRole VO对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@ApiModel(value = "MajorRepairPlanRoleVO对象", description = "大修计划角色")
@Data
public class MajorRepairPlanRoleVO extends  ObjectVO   implements Serializable{

            /**
         * 角色code
         */
        @ApiModelProperty(value = "角色code")
        private String roleCode;

        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String majorRepairTurn;
        @ApiModelProperty(value = "角色名称")
        private String roleName;

        @ApiModelProperty(value = "角色层级")
        private String roleLevel;


    

}
