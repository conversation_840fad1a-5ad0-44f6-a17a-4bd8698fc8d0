package com.chinasie.orion.bo;

import com.chinasie.orion.domain.dto.ComponentQueryDTO;
import com.chinasie.orion.domain.vo.ComponentVO;
import com.chinasie.orion.domain.vo.PlanAndComponentDocumentVo;
import com.chinasie.orion.domain.vo.ProductVO;
import com.chinasie.orion.feign.ComponentFeignService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/10 15:30
 * @description:
 */
@Component
public class ComponentBo {
    @Resource
    private ComponentFeignService componentFeignService;

    public List<ComponentVO> searchComponent(ComponentQueryDTO componentQueryDTO) throws Exception {
        return new ArrayList<>();
//        ResponseDTO<List<ComponentVO>> responseDTO = componentFeignService.searchComponent(componentQueryDTO);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        List<ComponentVO> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;
    }

    public ComponentVO componentDetail(String id) throws Exception {
//        ResponseDTO<ComponentVO> responseDTO = componentFeignService.componentDetail(id);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        ComponentVO result = responseDTO.getResult();
//        if (Objects.isNull(result)) {
//            return new ComponentVO();
//        }
//        return result;

        return new ComponentVO();
    }

    public List<ProductVO> productList() throws Exception {
//        ResponseDTO<List<ProductVO>> responseDTO = componentFeignService.productList();
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        List<ProductVO> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;

        return new ArrayList<>();
    }

    public List<ProductVO> searchProduct(ComponentQueryDTO componentQueryDTO) throws Exception {
//        ResponseDTO<List<ProductVO>> responseDTO = componentFeignService.searchProduct(componentQueryDTO);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        List<ProductVO> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
        return new ArrayList<>();
    }

    public ProductVO productVODetail(String id) throws Exception {
//        ResponseDTO<ProductVO> responseDTO = componentFeignService.productVODetail(id);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        ProductVO result = responseDTO.getResult();
//        if (Objects.isNull(result)) {
//            return new ProductVO();
//        }
        return new ProductVO();
    }


    public List<PlanAndComponentDocumentVo> getDocumentComponentByIdList(List<String> idList) throws Exception {
//        ComponentDocumentParamDto componentDocumentParamDto = new ComponentDocumentParamDto();
//        componentDocumentParamDto.setIds(idList);
//        ResponseDTO<List<PlanAndComponentDocumentVo>> responseDTO = componentFeignService.documentRelations(componentDocumentParamDto);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        List<PlanAndComponentDocumentVo> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;
        return new ArrayList<>();

    }


    public List<PlanAndComponentDocumentVo> getFileByDocumentId(String id) throws Exception {
//        ResponseDTO<List<PlanAndComponentDocumentVo>> responseDTO = componentFeignService.getFilesByDocumentId(id);
//        if (ResponseUtils.fail(responseDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
//        }
//        List<PlanAndComponentDocumentVo> result = responseDTO.getResult();
//        if (CollectionUtils.isEmpty(result)) {
//            return new ArrayList<>();
//        }
//        return result;

        return new ArrayList<>();
    }

}
