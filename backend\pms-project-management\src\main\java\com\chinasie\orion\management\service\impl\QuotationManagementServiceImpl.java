package com.chinasie.orion.management.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.repository.*;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.MarketContractBusinessTypeEnums;
import com.chinasie.orion.constant.ProcessStatusEnum;
import com.chinasie.orion.constant.RoleColeConstant;
import com.chinasie.orion.controller.CustSaleBusTypeEnum;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.vo.QuotationReturnVO;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.constant.*;
import com.chinasie.orion.management.domain.dto.QuotationManagementDTO;
import com.chinasie.orion.management.domain.dto.QuotationManagementExcelExportDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.QuotationManageCustContactVO;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.management.repository.CustomerInfoMapper;
import com.chinasie.orion.management.repository.QuotationManagementMapper;
import com.chinasie.orion.management.repository.QuotationRequirementViewMapper;
import com.chinasie.orion.management.repository.RequirementMangementMapper;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptLeaderRelationVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.DeptUserRelationVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DateTime.now;


/**
 * <p>
 * QuotationManagement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@Service
@Slf4j
public class QuotationManagementServiceImpl extends OrionBaseServiceImpl<QuotationManagementMapper, QuotationManagement> implements QuotationManagementService {

    @Autowired
    protected PmsMQProducer mqProducer;

    @Autowired
    private FileApiService fileApi;
    @Autowired
    private RequirementMangementService requirementMangementService;
    @Autowired
    private RequirementMangementMapper requirementMangementMapper;

    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    @Autowired
    private FileApiService fileApiService;
    @Autowired
    private DictRedisHelper dictRedisService;
    @Autowired
    private DeptLeaderHelper deptLeaderHelper;
    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    private StatusRedisHelper statusRedisHelper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private SysCodeApi sysCodeApi;
    @Autowired
    private RoleUserHelper roleUserHelper;
    @Autowired
    private RoleRedisHelper roleRedisHelper;

    @Autowired
    private QuotationRequirementViewMapper quotationRequirementViewMapper;
    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;
    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptUserDOMapper deptUserDOMapper;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private RoleUserDOMapper roleUserDOMapper;
    @Autowired
    private QuotationManageCustContactService quotationManageCustContactService;
    @Autowired
    private RequirementManageCustContactService requirementManageCustContactService;

    @Autowired
    private WorkflowFeignService workflowFeignService;

    @Autowired
    private DataStatusNBO dataStatusNBO;

    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private DeptUserHelper deptUserHelper;
    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private QuotationManagementMapper quotationManagementMapper;
    private final Logger logger = LoggerFactory.getLogger(QuotationManagementServiceImpl.class);
    @Autowired
    private QuotationManagementService quotationManagementService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QuotationManagementVO detail(String id, String pageCode) throws Exception {
        LambdaQueryWrapperX<QuotationManagement> condition = new LambdaQueryWrapperX<>(QuotationManagement.class);
        condition.leftJoin(CustomerInfo.class, "ci", CustomerInfo::getId,
                QuotationManagement::getQuoteAcceptCom);
        condition.selectAll(QuotationManagement.class);
        condition.eq(QuotationManagement::getId, id);
        condition.selectAs(CustomerInfo::getCusName, QuotationManagement::getQuoteAcceptComName);
        List<QuotationManagement> quotationManagementList = quotationManagementMapper.selectList(condition);
        QuotationManagement quotationManagement = quotationManagementList.get(0);
        QuotationManagementVO result = BeanCopyUtils.convertTo(quotationManagement, QuotationManagementVO::new);
        setEveryName(Collections.singletonList(result));
        Integer status = quotationManagement.getStatus();
        DataStatusVO dataStatus = quotationManagement.getDataStatus();
        RequirementMangementVO requirementMangementVO = requirementMangementService.detail(quotationManagement.getRequirementId(), null);
        BeanCopyUtils.copyProperties(requirementMangementVO, result);
//        BeanCopyUtils.copyProperties(quotationManagement, result);
        String techRes = requirementMangementVO.getTechRes();
        if (StringUtils.hasText(techRes)) {
            result.setTechRes(techRes);
        }
        String reqOwnership = requirementMangementVO.getReqOwnership();
        if (StringUtils.hasText(reqOwnership)) {
            result.setReqOwnership(reqOwnership);
        }
        String custConPerson = requirementMangementVO.getCustConPerson();
        if (StringUtils.hasText(custConPerson)) {
            result.setCustConPerson(custConPerson);
        }
        String custPerson = requirementMangementVO.getCustPerson();
        if (StringUtils.hasText(custPerson)) {
            result.setCustPerson(custPerson);
        }
        String custPersonName = requirementMangementVO.getCustPersonName();
        if (StringUtils.hasText(custPersonName)) {
            result.setCustPersonName(custPersonName);
        }
        String resSource = requirementMangementVO.getResSource();
        if (StringUtils.hasText(resSource)) {
            result.setResSource(resSource);
        }
        String businessPerson = requirementMangementVO.getBusinessPerson();
        if (StringUtils.hasText(businessPerson)) {
            result.setBusinessPerson(businessPerson);
        }
        Date signDeadlnTime = requirementMangementVO.getSignDeadlnTime();
        if (ObjectUtil.isNotEmpty(signDeadlnTime)) {
            result.setSignDeadlnTime(signDeadlnTime);
        }
        Date requireMentcreateTime = requirementMangementVO.getCreateTime();
        String requireMentcreatorId = requirementMangementVO.getCreatorId();
        result.setRequireCreatorId(requireMentcreatorId);
        result.setRequireCreatTime(requireMentcreateTime);
        List<String> userIds = new ArrayList<>();
        String officeLeader = result.getOfficeLeader();
        if (ObjectUtil.isNotEmpty(officeLeader)) {
            userIds.add(officeLeader);
        }
        if (ObjectUtil.isNotEmpty(result.getIssuer())) {
            userIds.add(result.getIssuer());
        }
        if (ObjectUtil.isNotEmpty(requireMentcreatorId)) {
            userIds.add(requireMentcreatorId);
        }
        if (ObjectUtil.isNotEmpty(result.getCreatorId())) {
            userIds.add(result.getCreatorId());
        }
        List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(userIds);
        Map<String, String> users = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        String requireMentName = users.get(requireMentcreatorId);
        if (ObjectUtil.isNotEmpty(requireMentName)) {
            result.setRequireCreatorName(requireMentName);
        }
        if (ObjectUtil.isNotEmpty(result.getCreatorId())) {
            result.setCreatorName(users.getOrDefault(result.getCreatorId(), ""));
        }
        result.setBusinessType(requirementMangementVO.getBusinessType());
        result.setRequirementMangementDataStatus(requirementMangementVO.getDataStatus());

        String issuer = users.get(result.getIssuer());
        result.setRequirementRemark(requirementMangementVO.getRemark());
        ArrayList<String> fileIds = new ArrayList<>();
        fileIds.add(requirementMangementVO.getId());
        fileIds.add(quotationManagement.getId());
        List<FileTreeVO> filesByDataIds = fileApiService.getFilesByDataIds(fileIds);
        Map<String, List<FileTreeVO>> filesByDataIdMap = filesByDataIds.stream()
                .collect(Collectors.groupingBy(FileTreeVO::getDataId));
        List<FileTreeVO> requirementFileList1 = filesByDataIdMap.get(requirementMangementVO.getId());
        List<FileTreeVO> quoteFileInfoDTOList1 = filesByDataIdMap.get(quotationManagement.getId());
        ArrayList<FileVO> quoteFileInfoDTOList = new ArrayList<>();
        ArrayList<FileVO> requirementFileList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(requirementFileList1)) {
            for (FileTreeVO fileTreeVO : requirementFileList1) {
                FileVO fileVO = new FileVO();
                BeanCopyUtils.copyProperties(fileTreeVO, fileVO);
                fileVO.setClassName("OrionFile");
                requirementFileList.add(fileVO);
            }
        }
        if (ObjectUtil.isNotEmpty(quoteFileInfoDTOList1)) {
            for (FileTreeVO fileTreeVO : quoteFileInfoDTOList1) {
                FileVO fileVO = new FileVO();
                BeanCopyUtils.copyProperties(fileTreeVO, fileVO);
                fileVO.setClassName("OrionFile");
                quoteFileInfoDTOList.add(fileVO);
            }
        }

        //上传人
        List<String> total = new ArrayList<>();
        List<String> collectFileUser = requirementFileList.stream().map(FileVO::getCreatorId).distinct().collect(Collectors.toList());
        List<String> collectFileUserOne = quoteFileInfoDTOList.stream().map(FileVO::getCreatorId).distinct().collect(Collectors.toList());
        total.addAll(collectFileUser);
        total.addAll(collectFileUserOne);
        List<UserBaseCacheVO> userFileByIds = userRedisHelper.getUserBaseCacheByIds(collectFileUser);
        Map<String, String> userFileMap = userFileByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        for (FileVO fileVO : requirementFileList) {
            fileVO.setCreatorName(userFileMap.getOrDefault(fileVO.getCreatorId(), ""));
        }
        for (FileVO fileVO : quoteFileInfoDTOList) {
            fileVO.setCreatorName(userFileMap.getOrDefault(fileVO.getCreatorId(), ""));
        }
        result.setRequirementFileInfoDTOList(requirementFileList);
        if (!CollectionUtils.isEmpty(quoteFileInfoDTOList)) {
            result.setQuoteFileInfoDTOList(quoteFileInfoDTOList.stream()
                    .filter(x -> null == x.getDataType()).collect(Collectors.toList()));
            result.setBidQuoteFileList(quoteFileInfoDTOList.stream()
                    .filter(x -> null != x.getDataType() && "bidQuoteFile".equals(x.getDataType())).collect(Collectors.toList()));
        }

        if (StringUtils.hasText(quotationManagement.getQuoteAcceptComName())) {
            result.setQuoteAcceptComName(quotationManagement.getQuoteAcceptComName());
        }


        if (StringUtils.hasText(officeLeader)) {
            String officeLeaderName = users.get(officeLeader);
            if (ObjectUtil.isNotEmpty(officeLeaderName)) {
                result.setOfficeLeaderName(officeLeaderName);
            }
        }
        result.setId(id);
        result.setResSourceName(RequirementResouceEnum.getDesc(result.getResSource()));
        result.setDataStatus(dataStatus);
        result.setStatus(status);
        result.setIssuerName(issuer);
        result.setIssueWay(RouteOfQuotationEnum.getDesc(result.getIssueWay()));
        // 2024-8-19，重新报价的单据，1.底线价格变动，需要重新审批 2.底线价格未变动，但是报价金额低于底线价格需要重新审批
        result.setSkipExamine(false);
        if (StringUtils.hasText(quotationManagement.getReQuotationId())) {
            LambdaQueryWrapper<QuotationManagement> quotationManagementLambdaQueryWrapper = new LambdaQueryWrapper<>();
            quotationManagementLambdaQueryWrapper.eq(QuotationManagement::getId, quotationManagement.getReQuotationId());
            quotationManagementLambdaQueryWrapper.select(QuotationManagement::getFloorPrice);
            List<QuotationManagement> quotationManagements = this.list(quotationManagementLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(quotationManagements)) {
                QuotationManagement relation = quotationManagements.get(0);
                if (null != relation) {
                    if (null != relation.getFloorPrice() && null != quotationManagement.getFloorPrice()
                            && relation.getFloorPrice().compareTo(quotationManagement.getFloorPrice()) == 0) {
                        if (null != quotationManagement.getQuoteAmt()
                                && quotationManagement.getQuoteAmt().compareTo(quotationManagement.getFloorPrice()) >= 0) {
                            result.setSkipExamine(true);
                        }
                    }
                }
            }
        }

        final LambdaQueryWrapper<QuotationManageCustContact> contactQuery = new LambdaQueryWrapper<>();
        contactQuery.eq(QuotationManageCustContact::getQuotationId, id);
        final List<QuotationManageCustContact> contacts = quotationManageCustContactService.list(contactQuery);
        if (!CollectionUtils.isEmpty(contacts)) {
            final List<QuotationManageCustContactVO> contactVos =
                    BeanCopyUtils.convertListTo(contacts, QuotationManageCustContactVO::new);
            contactVos.forEach(e -> {
                e.setContactTypeName(CustContactTypeEnum.getDesc(e.getContactType()));
            });
            result.setCustContacts(contactVos);
        }
        result.setClassName("QuotationManagement");
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param quotationManagementDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(QuotationManagementDTO quotationManagementDTO) throws Exception {
        if (!StringUtils.hasText(quotationManagementDTO.getRequirementId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "请关联需求");
        }
        // 获取 quotationName 的字节长度
        String quotationName = quotationManagementDTO.getQuotationName();
        if (quotationName != null && quotationName.getBytes().length > 500) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "报价名称过长");
        }


        // 判断报价单中，是否有“非重新报价”的报价单已占用需求
        LambdaQueryWrapper<QuotationManagement> wrapperX = new LambdaQueryWrapper<>();
        wrapperX.eq(QuotationManagement::getRequirementId, quotationManagementDTO.getRequirementId());
        wrapperX.eq(QuotationManagement::getLogicStatus, 1);
        wrapperX.ne(QuotationManagement::getStatus, QuotationManagementStatusEnum.RE_QUATATION.getStatus());
        List<QuotationManagement> quotationManagements = this.baseMapper.selectList(wrapperX);
        if (!CollectionUtils.isEmpty(quotationManagements)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "需求已存在报价单");
        }
        QuotationManagement quotationManagement = BeanCopyUtils.convertTo(quotationManagementDTO, QuotationManagement::new);
        RequirementMangement requirementMangement = requirementMangementMapper.selectById(quotationManagementDTO.getRequirementId());
        if (requirementMangement != null) {
            String officeLeader = requirementMangement.getOfficeLeader();
            if (!StringUtils.isEmpty(officeLeader)) {
                quotationManagement.setOfficeLeader(officeLeader);
            }
            //校验需求状态是否为已确认
            if (RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus().equals(requirementMangement.getStatus())) {

                // 报价单编号，用户可以手动填写。用户没填写，就自动生成。生成的单号不能重复
                if (StringUtils.hasText(quotationManagement.getQuotationId())) {
                    LambdaQueryWrapper<QuotationManagement> checkCode = new LambdaQueryWrapper<>();
                    checkCode.eq(QuotationManagement::getQuotationId, quotationManagement.getQuotationId());
                    if (!CollectionUtils.isEmpty(this.list(checkCode))) {
                        throw new PMSException(PMSErrorCode.PMS_ERR, "报价单编码已存在");
                    }
                } else {
                    String number = this.generateQuotationNo(requirementMangement);
                    if (!StringUtils.hasText(number)) {
                        throw new PMSException(PMSErrorCode.PMS_ERR, "生成报价单编码失败，重试或者联系管理员");
                    }
                    quotationManagement.setQuotationId(number);
                }
                // 报价单名称也不能重复
                LambdaQueryWrapper<QuotationManagement> checkName = new LambdaQueryWrapper<>();
                checkName.eq(QuotationManagement::getQuotationName, quotationManagement.getQuotationName());
                if (!CollectionUtils.isEmpty(this.list(checkName))) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "报价单名称已存在");
                }

                this.save(quotationManagement);
                quotationManagement.setQuotationStatus(QuotationManagementStatusEnum.IN_PREPARATION.getStatus().toString());
                String rsp = quotationManagement.getId();
                List<FileInfoDTO> fileInfoDTOList = quotationManagementDTO.getQuoteFileInfoDTOList();
                if (!com.chinasie.orion.util.CollectionUtils.isBlank(fileInfoDTOList)) {
                    fileInfoDTOList.forEach(p -> {
                        p.setOrgId(quotationManagementDTO.getOrgId());
                        p.setDataId(rsp);
                    });
                    List<FileInfoDTO> insertInfoDTOList = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());

                    List<FileInfoDTO> updateInfoDTOList = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                    if (!com.chinasie.orion.util.CollectionUtils.isBlank(insertInfoDTOList)) {
                        documentService.saveBatchAdd(fileInfoDTOList);
                    }

                    if (!com.chinasie.orion.util.CollectionUtils.isBlank(updateInfoDTOList)) {
                        List<FileDTO> updateFileDtoList = BeanCopyUtils.convertListTo(updateInfoDTOList, FileDTO::new);
                        documentService.updateBatchDocument(updateFileDtoList);
                    }

                }

                // 创建的时候，把需求单中的联系人带到报价单中
                final List<QuotationManageCustContact> contacts = Lists.newArrayList();

                final LambdaQueryWrapper<RequirementManageCustContact> reqContractQuery = new LambdaQueryWrapper<>();
                reqContractQuery.eq(RequirementManageCustContact::getRequirementId, requirementMangement.getId());
                final List<RequirementManageCustContact> reqContacts = requirementManageCustContactService.list(reqContractQuery);

                if (!CollectionUtils.isEmpty(reqContacts)) {
                    reqContacts.forEach(e -> {
                        QuotationManageCustContact item = new QuotationManageCustContact();
                        item.setContactType(e.getContactType());
                        item.setCustContactId(e.getCustContactId());
                        item.setContactName(e.getContactName());
                        item.setContactPhone(e.getContactPhone());

                        contacts.add(item);
                    });
                    quotationManageCustContactService.saveQuotationContacts(contacts, quotationManagement);
                }

                //根据需求单创建报价单后，把需求单状态改成 已生成报价单
                LambdaUpdateWrapper<RequirementMangement> updateRequirement = new LambdaUpdateWrapper<>(RequirementMangement.class);
                updateRequirement.set(RequirementMangement::getHadQuotation, 1).eq(RequirementMangement::getId, quotationManagementDTO.getRequirementId());
                requirementMangementService.update(updateRequirement);

                // 发送消息
                sendMessage(quotationManagement.getId(),
                        "/pas/QuotationManagementDetails/" + quotationManagement.getId() + "?query=" + new Date().getTime(),
                        "发生新报价，" + quotationManagement.getQuotationName() + "，等待编制报价方案",
                        Arrays.asList(requirementMangement.getBusinessPerson(), requirementMangement.getTechRes()),
                        requirementMangement.getPlatformId(),
                        requirementMangement.getOrgId(),
                        RequirementMscNodeEnum.NODE_REQUIREMENT_OFFER.getCode());
                return rsp;
            } else {
                throw new PMSException(PMSErrorCode.PMS_ERR, "需求状态不是已确认");
            }
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERR, "所选数据有误");
        }
    }

    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String platformId, String orgId, String code) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$desc$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    /**
     * 编辑
     * <p>
     * * @param quotationManagementDTO
     */
    @Override
    @Transactional
    public Boolean edit(QuotationManagementDTO quotationManagementDTO) throws Exception {
        QuotationManagement quotation = this.baseMapper.selectById(quotationManagementDTO.getId());
        RequirementMangement requirementMangement = requirementMangementService.getById(quotation.getRequirementId());
        String businessPerson = quotationManagementDTO.getBusinessPerson();
        String techRes = quotationManagementDTO.getTechRes();
        if (ObjectUtil.isNotEmpty(requirementMangement)) {
            HashSet<String> users = new HashSet<>();
            users.add(businessPerson);
            users.add(techRes);
            List<String> userList = new ArrayList<>(users);
            List<SimpleUser> simplerUsers = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(), userList);
            Map<String, SimpleUser> userMap = simplerUsers.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
            if (ObjectUtil.isNotEmpty(requirementMangement.getBusinessPerson()) && ObjectUtil.isNotEmpty(businessPerson) && !businessPerson.equals(requirementMangement.getBusinessPerson())) {
                requirementMangement.setBusinessPerson(businessPerson);
                SimpleUser simpleUser = userMap.get(businessPerson);
                if (ObjectUtil.isNotEmpty(simpleUser)) {
                    requirementMangement.setBusinessPersonName(simpleUser.getName());
                }
            }
            if (ObjectUtil.isNotEmpty(requirementMangement.getTechRes()) && ObjectUtil.isNotEmpty(techRes) && !techRes.equals(requirementMangement.getTechRes())) {
                requirementMangement.setTechRes(techRes);
                SimpleUser simpleUser = userMap.get(techRes);
                if (ObjectUtil.isNotEmpty(simpleUser)) {
                    requirementMangement.setTechResName(simpleUser.getName());
                    String orgId = simpleUser.getOrgId();//20级
                    requirementMangement.setReqOwnership(orgId);
//                    List<String> deptByUserId = deptUserHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), techRes);
//                    String deptId = deptByUserId.get(0);
//                    List<DeptLeaderRelationVO> deptLeaderRelationVOS = deptLeaderHelper.getDeptLeaderRelationByDeptId(CurrentUserHelper.getOrgId(), deptId);
//                    if (ObjectUtil.isNotEmpty(deptLeaderRelationVOS)) {
//                        String officeLeader = requirementMangement.getOfficeLeader();
//                        for (DeptLeaderRelationVO deptLeaderRelationVO : deptLeaderRelationVOS) {
//                            String type = deptLeaderRelationVO.getType();
//                            if ("main".equals(type)) {
//                                officeLeader = deptLeaderRelationVO.getUserId();
//                                break;
//                            }
//                        }
//                        quotationManagementDTO.setOfficeLeader(officeLeader);
//                        requirementMangement.setOfficeLeader(officeLeader);
//                    }
                }
            }
            requirementMangement.setOfficeLeader(quotationManagementDTO.getOfficeLeader());
            requirementMangementService.updateById(requirementMangement);
        }

        QuotationManagement quotationManagement = BeanCopyUtils.convertTo(quotationManagementDTO, QuotationManagement::new);
        List<FileInfoDTO> fileInfoDTOList = quotationManagementDTO.getQuoteFileInfoDTOList();

        // 附件保存逻辑
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            List<FileDTO> fileDTOS = BeanCopyUtils.convertListTo(fileInfoDTOList, FileDTO::new);
            fileDTOS.forEach(p -> {
                p.setOrgId(quotationManagementDTO.getOrgId());
                p.setDataId(quotationManagementDTO.getId());
            });
            List<FileVO> existFileList = fileApi.getFilesByDataId(quotationManagementDTO.getId());
            List<FileDTO> insertInfoDTOList = fileDTOS.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            // 优先移除
            if (Objects.nonNull(existFileList)) {
                // existFileList 中不包含 fileDTOList的删除
                List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileInfoDTOList.stream()
                                .map(FileInfoDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filesIds)) {
                    fileApi.removeBatchByIds(filesIds);
                }
            }
            List<FileDTO> updateInfoDTOList = fileDTOS.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(insertInfoDTOList)) {
                fileApiService.batchSaveFile(insertInfoDTOList);
            }

            if (!CollectionUtils.isEmpty(updateInfoDTOList)) {
                fileApiService.batchUpdateFile(updateInfoDTOList);
            }
        } else {
            if (quotationManagement.getStatus() == null
                    || quotationManagement.getStatus().equals(QuotationManagementStatusEnum.IN_PREPARATION.getStatus())) {
                // 编辑的时候附件列表为空，则需要清空现在的附件关系,重新报价的时候，不要清空附件
                fileApiService.deleteByDataId(quotationManagementDTO.getId());
            }
        }

        this.updateById(quotationManagement);

        if (quotationManagement.getStatus() != null) {
            if (quotationManagement.getStatus().equals(QuotationManagementStatusEnum.RE_QUATATION.getStatus())) {
                // 重新报价，克隆一个新对象保存
                QuotationManagement reQuotationManagement = new QuotationManagement();

                reQuotationManagement.setRequirementId(quotation.getRequirementId());
                reQuotationManagement.setRequirementNumber(quotation.getRequirementNumber());

                // 重新报价，生成新的报价单编码
                String number = this.generateQuotationNo(requirementMangement);
                if (!StringUtils.hasText(number)) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "生成报价单编码失败，重试或者联系管理员");
                }
                quotationManagement.setQuotationId(number);
                reQuotationManagement.setQuotationId(quotation.getQuotationId());
                // 重新报价的时候，关联原报价单id,不保留报价信息
                reQuotationManagement.setReQuotationId(quotation.getId());
                reQuotationManagement.setQuotationName(quotation.getQuotationName() + "-1");

                reQuotationManagement.setBusiGoal(quotation.getBusiGoal());
                reQuotationManagement.setBusiGoalCont(quotation.getBusiGoalCont());
                reQuotationManagement.setBusiInfo(quotation.getBusiInfo());

                reQuotationManagement.setCostEstRes(quotation.getCostEstRes());
                reQuotationManagement.setCostEstRrRes(quotation.getCostEstRrRes());
                reQuotationManagement.setCostEstResCont(quotation.getCostEstResCont());
                reQuotationManagement.setCostEstHrResCont(quotation.getCostEstHrResCont());

                reQuotationManagement.setRevAnal(quotation.getRevAnal());
                reQuotationManagement.setRevAnalCont(quotation.getRevAnalCont());
                reQuotationManagement.setOtherInfo(quotation.getOtherInfo());
                reQuotationManagement.setOtherInfoCont(quotation.getOtherInfoCont());
                reQuotationManagement.setStatus(QuotationManagementStatusEnum.IN_PREPARATION.getStatus());

                reQuotationManagement.setOrgId(quotation.getOrgId());
                reQuotationManagement.setCreatorId(quotation.getCreatorId());
                reQuotationManagement.setCreatorName(quotation.getCreatorName());
                reQuotationManagement.setCreateTime(now());

                this.save(reQuotationManagement);
            }
        }
        return true;
    }

    /**
     * 返回报价状态，中标、未中标
     *
     * @param quotationManagementDTO 定价状态
     */
    @Override
    @Transactional
    public void bidResult(QuotationManagementDTO quotationManagementDTO) {
        QuotationManagement quotation = BeanCopyUtils.convertTo(quotationManagementDTO, QuotationManagement::new);

        QuotationManagement check = this.baseMapper.selectById(quotationManagementDTO.getId());

        if (null == check) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "报价单不存在！");
        }
        if (!Objects.equals(QuotationManagementStatusEnum.ALREADY_QUOTATION.getStatus(), check.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅已投标状态可以做此操作！");
        }

        //更新中标状态
        this.updateById(quotation);

        final List<FileDTO> quoteFiles = quotationManagementDTO.getBidQuoteFileList();
        List<String> removeIds = new ArrayList<>();
        try {
            List<FileVO> files = fileApiService.getFilesByDataId(quotation.getId());
            if (!CollectionUtils.isEmpty(files)) {
                removeIds = files.stream().filter(x -> null != x.getDataType() && "bidQuoteFile".equals(x.getDataType()))
                        .map(FileVO::getId).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取文件失败", e);
        }

        if (!CollectionUtils.isEmpty(quoteFiles)) {
            // 默认全部删除，如果在quoteFiles中存在，则不删除
            removeIds.removeIf(x -> quoteFiles.stream().anyMatch(y -> y.getId().equals(x)));

            quoteFiles.forEach(p -> {
                p.setOrgId(check.getOrgId());
                p.setDataId(check.getId());
            });
            List<FileDTO> insertInfoDTOList = quoteFiles.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            List<FileDTO> updateInfoDTOList = quoteFiles.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            try {
                if (!CollectionUtils.isEmpty(insertInfoDTOList)) {
                    fileApiService.batchSaveFile(insertInfoDTOList);
                }
                if (!CollectionUtils.isEmpty(updateInfoDTOList)) {
                    fileApiService.batchUpdateFile(updateInfoDTOList);
                }
            } catch (Exception e) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "保存附件失败！");
            }
        }

        if (!CollectionUtils.isEmpty(removeIds)) {
            try {
                fileApiService.deleteByIds(removeIds);
            } catch (Exception e) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "保存附件失败！");
            }
        }

        String desc;
        List<String> toUser;
        String code;

        RequirementMangement requirement = requirementMangementService.getById(check.getRequirementId());
        if (Objects.equals(QuotationManagementStatusEnum.SUCCESS_QUOTATION.getStatus(), quotation.getStatus())) {
            // 已中标
            if (5000000 <= check.getQuoteAmt().intValue()) {
                // 根据报价金额  判断是否需要获取承担部门负责人
                List<DeptLeaderRelationVO> relationByDeptId = deptLeaderHelper
                        .getDeptLeaderRelationByDeptId(requirement.getOrgId(), requirement.getReqOwnership());

                List<String> collect = relationByDeptId.stream().filter(x -> "main".equals(x.getType()))
                        .map(DeptLeaderRelationVO::getUserId).collect(Collectors.toList());
                collect.add(requirement.getBusinessPerson());
                collect.add(requirement.getTechRes());
                toUser = collect;
            } else {
                toUser = Arrays.asList(requirement.getBusinessPerson(), requirement.getTechRes());
            }
            desc = check.getQuotationName() + "，已中标；";
            code = RequirementMscNodeEnum.NODE_REQUIREMENT_YZB.getCode();
        } else if (Objects.equals(QuotationManagementStatusEnum.FAILED_QUATATION.getStatus(), quotation.getStatus())) {
            // 未中标
            desc = check.getQuotationName() + "，未中标；";
            toUser = Arrays.asList(requirement.getBusinessPerson(), requirement.getTechRes());
            code = RequirementMscNodeEnum.NODE_REQUIREMENT_WZB.getCode();
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERR, "参数错误，请传是否中标状态！");
        }

        // 邮件发送，从edit方法移到这个地方处理
        sendMessage(check.getId(),
                "/pas/QuotationManagementDetails/" + check.getId() + "?query=" + new Date().getTime(),
                desc,
                toUser,
                quotation.getPlatformId(),
                quotation.getOrgId(),
                code
        );

        if (StringUtils.hasText(check.getRequirementId())) {
            QuotationReturnVO quotationReturnVO = new QuotationReturnVO();
            quotationReturnVO.setCustTecPerson(requirement.getCustTecPerson());
            quotationReturnVO.setCustBsPerson(requirement.getCustBsPerson());
            quotationReturnVO.setOrgId(requirement.getOrgId());
            quotationReturnVO.setPlatformId(requirement.getPlatformId());
            quotationReturnVO.setId(check.getId());
            quotationReturnVO.setName(check.getQuotationName());

            mscBuildHandlerManager.send(quotationReturnVO, MessageNodeNumberDict.QUOTATION_RETURN_NOTIFICATION);
        }

    }

    /**
     * 报价发出
     *
     * @param quotationManagementDTO 报价单信息
     */
    @Override
    public void sendOut(QuotationManagementDTO quotationManagementDTO) {
        QuotationManagement check = this.baseMapper.selectById(quotationManagementDTO.getId());
        if (!QuotationManagementStatusEnum.CAN_QUOTATION.getStatus().equals(check.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅可投标状态可以做此操作！");
        }

        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaUpdateWrapper<QuotationManagement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(QuotationManagement::getSendOutUser, userId);
        updateWrapper.set(QuotationManagement::getSendOutTime, now());
        updateWrapper.set(QuotationManagement::getIssueTime, quotationManagementDTO.getIssueTime());
        updateWrapper.set(QuotationManagement::getIssueWay, quotationManagementDTO.getIssueWay());
        updateWrapper.set(QuotationManagement::getIssuer, quotationManagementDTO.getIssuer());
        updateWrapper.set(QuotationManagement::getQuoteAcceptCom, quotationManagementDTO.getQuoteAcceptCom());
        updateWrapper.set(QuotationManagement::getQuoteAcceptPen, quotationManagementDTO.getQuoteAcceptPen());
        updateWrapper.set(QuotationManagement::getQuoteRemark, quotationManagementDTO.getQuoteRemark());
        updateWrapper.set(QuotationManagement::getStatus, QuotationManagementStatusEnum.ALREADY_QUOTATION.getStatus());

        updateWrapper.eq(QuotationManagement::getId, quotationManagementDTO.getId());

        this.update(updateWrapper);
    }

    /**
     * 报价单作废，作废的报价单相当于删除，之后不可再编辑，同时也需要释放需求单的状态
     *
     * @param quotation 报价单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abandon(QuotationManagementDTO quotation) {
        QuotationManagement check = this.baseMapper.selectById(quotation.getId());
        if (null == check) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "参数错误！未找到数据！");
        }
        if (QuotationManagementStatusEnum.ABANDON.getStatus().equals(check.getStatus())
                || QuotationManagementStatusEnum.RE_QUATATION.getStatus().equals(check.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "已作废和重新报价的数据，不可以做此操作！");
        }

        LambdaUpdateWrapper<QuotationManagement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(QuotationManagement::getId, check.getId())
                .set(QuotationManagement::getStatus, QuotationManagementStatusEnum.ABANDON.getStatus())
                .set(QuotationManagement::getObsoleteReason, quotation.getObsoleteReason());
        this.update(updateWrapper);

        //把需求单状态的已报价状态改成0
        if (StringUtils.hasText(check.getRequirementId())) {
            requirementMangementService.update(new LambdaUpdateWrapper<RequirementMangement>()
                    .set(RequirementMangement::getHadQuotation, 0)
                    .eq(RequirementMangement::getId, check.getRequirementId()));
        }
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<QuotationManagement> quotationManagements = this.listByIds(ids);
        List<QuotationManagement> collect = quotationManagements.stream()
                .filter(quotationManagement -> !QuotationManagementStatusEnum.IN_PREPARATION.getStatus().equals(quotationManagement.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            this.removeBatchByIds(ids);

            //把需求单状态的已报价状态改成0
            requirementMangementService.update(new LambdaUpdateWrapper<RequirementMangement>()
                    .set(RequirementMangement::getHadQuotation, 0)
                    .in(RequirementMangement::getId, quotationManagements.stream()
                            .map(QuotationManagement::getRequirementId).collect(Collectors.toList())));
            return true;
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该报价状态不允许删除！");
        }

    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QuotationManagementVO> pages(Page<QuotationManagementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QuotationRequirementView> condition = new LambdaQueryWrapperX<>(QuotationRequirementView.class);
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId,
                "t", QuotationManagement::getRequirementId);
        condition.selectAll(QuotationManagement.class);
        condition.selectAs(RequirementMangement::getId, QuotationManagement::getRequirementId);
        condition.selectAs(RequirementMangement::getSignDeadlnTime, QuotationManagement::getSignDeadlnTime);
        condition.selectAs(RequirementMangement::getTechRes, QuotationManagement::getTechRes);
        condition.selectAs(RequirementMangement::getBusinessPerson, QuotationManagement::getBusinessPerson);
        condition.selectAs(RequirementMangement::getCustPerson, QuotationManagement::getCustPerson);
        condition.selectAs(RequirementMangement::getCustPersonName, QuotationManagement::getCustPersonName);
        condition.selectAs(RequirementMangement::getResSource, QuotationManagement::getResSource);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
            List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
            for (List<SearchCondition> searchCondition : searchConditions) {
                for (SearchCondition searchCondition1 : searchCondition) {
                    String field = searchCondition1.getField();
                    if ("rm.res_source".equals(field)) {
                        condition.eq(RequirementMangement::getResSource, searchCondition1.getValues().get(0).toString());
                    }
                }
            }
        }

        QuotationManagementDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            if (ObjectUtil.isNotEmpty(query.getPriority())) {
                condition.eq(RequirementMangement::getPriority, query.getPriority());
            }
            if (ObjectUtil.isNotEmpty(query.getQuotationName())) {
                condition.like(QuotationManagement::getQuotationName, query.getQuotationName());
            }
            if (ObjectUtil.isNotEmpty(query.getPrioritySort())) {
                if ("0".equals(query.getPrioritySort())) {
                    condition.orderByAsc(RequirementMangement::getPriority);
                }
                if ("1".equals(query.getPrioritySort())) {
                    condition.orderByDesc(RequirementMangement::getPriority);
                }

            }

        }
        condition.orderByDesc(QuotationRequirementView::getCreateTime);


        Page<QuotationRequirementView> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuotationRequirementView::new));
        IPage<QuotationRequirementView> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<QuotationRequirementView> result = quotationRequirementViewMapper.selectDataPermissionPage(mPage, QuotationRequirementView.class, condition);

        Page<QuotationManagementVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        List<QuotationManagementVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), QuotationManagementVO::new);
        List<String> ids = vos.stream().map(QuotationManagementVO::getRequirementId).collect(Collectors.toList());
        Set<String> cooperatePersons = vos.stream()
                .map(QuotationManagementVO::getCooperatePerson)
                .filter(StringUtils::hasText) // 确保字段不为空
                .collect(Collectors.toSet());

        Set<String> businessAndTechPersons = vos.stream()
                .flatMap(vo -> Stream.of(vo.getBusinessPerson(), vo.getTechRes(), vo.getCreatorId()))
                .filter(StringUtils::hasText) // 确保字段不为空
                .collect(Collectors.toSet());

        Set<String> uniqueCollect = new HashSet<>(cooperatePersons);
        uniqueCollect.addAll(businessAndTechPersons);
        List<String> collect = new ArrayList<>(uniqueCollect);
        Map<String, String> userMap;
        if (!CollectionUtils.isEmpty(collect)) {
            List<UserBaseCacheVO> users = userRedisHelper.getUserBaseCacheByIds(collect);
            userMap = users.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        } else {
            userMap = Map.of();
        }

        List<String> customerIds = vos.stream()
                .map(QuotationManagementVO::getCustPerson)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> customerInfoMap;
        if (!CollectionUtils.isEmpty(customerIds)) {
            LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            customerInfoLambdaQueryWrapper.in(CustomerInfo::getId, customerIds);
            customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getCusName);
            List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapper);
            customerInfoMap = customerInfos.stream()
                    .collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
        } else {
            customerInfoMap = Map.of();
        }
        if (!CollectionUtils.isEmpty(ids)) {
            List<DataStatusVO> dataStatusVOS = statusRedisHelper.getStatusList(QuotationManagementConstant.QUOTATION_MANAGEMENT_STATUS_TX);
            Map<Integer, DataStatusVO> dataStatusVOMap = dataStatusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));

            for (QuotationManagementVO vo : vos) {
                String id = vo.getId();
                String remark = vo.getRemark();
                Integer status = vo.getStatus();
                if (StringUtils.hasText(vo.getRequirementId())) {
                    String priority = vo.getPriority();
                    vo.setPriority(priority);
                    vo.setBusinessPersonName(userMap.getOrDefault(vo.getBusinessPerson(), ""));
                    vo.setTechResName(userMap.getOrDefault(vo.getTechRes(), ""));
                    vo.setCreatorName(userMap.getOrDefault(vo.getCreatorId(), ""));

                    vo.setRemark(remark);
                    if (dataStatusVOMap.containsKey(status)) {
                        vo.setDataStatus(dataStatusVOMap.get(status));
                    }
                    if (StringUtils.hasText(vo.getCustPerson())) {
                        vo.setCustPersonName(customerInfoMap.get(vo.getCustPerson()));
                    }
                    vo.setId(id);
                    vo.setStatus(status);
                    vo.setResSourceName(RequirementResouceEnum.getDesc(vo.getResSource()));
                    if (StringUtils.hasText(vo.getCooperatePerson())) {
                        vo.setCooperatePerson(userMap.get(vo.getCooperatePerson()));
                    }
                }
            }
            setEveryNamePage(vos);
            pageResult.setContent(vos);

            return pageResult;
        } else {
            return null;
        }

    }

    @Override
    public Page<QuotationManagementVO> successPages(Page<QuotationManagementDTO> pageRequest) {
        QuotationManagementDTO query = pageRequest.getQuery();
        query.setStatus(QuotationManagementStatusEnum.SUCCESS_QUOTATION.getStatus());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<QuotationManagement> realPageRequest =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        // 查询权限。
        String userId = CurrentUserHelper.getCurrentUserId();
        UserVO currentUser = userRedisHelper.getUserById(userId);
        List<RoleVO> roleVOS = currentUser.getRoles();
        // 当前人 角色包含【公司级商务接口人】，或者是 计划经营部的领导组成员，不启用数据权限，查看所有数据；
        if (CollectionUtils.isEmpty(roleVOS) || roleVOS.stream().filter(e -> StringUtils.hasText(e.getCode()))
                .noneMatch(role -> role.getCode().equals(RoleColeConstant.COMPANY_BUSINESS_INTERFACE_ROLE_CODE))) {
            // 如果所有角色都不等于 PLAN_ENGINE，则执行 else 逻辑
            //获取计划经营部门
            DeptBaseInfoVO deptDO = deptRedisHelper.getDeptBaseInfoByDeptCode(currentUser.getOrgId(), "PBD");
            if (null != deptDO) {
                LambdaQueryWrapper<DeptLeaderDO> queryWrapperX = new LambdaQueryWrapper<>(DeptLeaderDO.class);
                queryWrapperX.eq(DeptLeaderDO::getType, "group");
                queryWrapperX.eq(DeptLeaderDO::getDeptId, deptDO.getId());
                queryWrapperX.eq(DeptLeaderDO::getUserId, userId);

                DeptLeaderDO deptLeaderDO = deptLeaderDORepository.selectOne(queryWrapperX);
                if (null == deptLeaderDO) {
                    // 不是计划组的领导，那么就需要启用权限控制
                    query.setUserId(userId);
                    query.setDeptIds(this.getDeptIds(userId));
                }
            } else {
                // 不是计划组的领导，那么就需要启用权限控制
                query.setUserId(userId);
                query.setDeptIds(this.getDeptIds(userId));
            }
        }

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<QuotationManagementVO> page =
                this.getBaseMapper().queryByPage(realPageRequest, query);

        Page<QuotationManagementVO> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<QuotationManagementVO> vos = page.getRecords();
        ArrayList<String> ids = new ArrayList<>();
        ArrayList<String> techUser = new ArrayList<>();
        //业务类型
        for (QuotationManagementVO vo : vos) {
            techUser.add(vo.getTechRes());
            ids.add(vo.getRequirementId());
        }
        List<SimpleUser> simplerUsers = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(), techUser);
        Map<String, SimpleUser> stringSimpleUserMap = simplerUsers.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity(), (k1, k2) -> k1));
        LambdaQueryWrapper<RequirementMangement> LambdaQueryWrapper = new LambdaQueryWrapper();
        LambdaQueryWrapper.in(!CollectionUtils.isEmpty(ids), RequirementMangement::getId, ids);
        List<RequirementMangement> requirementMangements = requirementMangementMapper.selectList((LambdaQueryWrapper));
        if (ObjectUtil.isNotEmpty(requirementMangements)) {
            Map<String, String> map = requirementMangements.stream()
                    .filter(obj -> ObjectUtil.isNotEmpty(obj.getBusinessType()))
                    .collect(Collectors.toMap(RequirementMangement::getId, RequirementMangement::getBusinessType, (k1, k2) -> k1));
            if (ObjectUtil.isNotEmpty(map)) {
                vos.forEach(vo -> {
                    if (map.containsKey(vo.getRequirementId())) {
                        vo.setBusinessType(map.get(vo.getRequirementId()));
                    }
                });
            }
            //业务收入类型
            List<String> custPersons = new ArrayList<>();
            for (RequirementMangement requirementMangement : requirementMangements) {
                String custPerson = requirementMangement.getCustPerson();
                custPersons.add(custPerson);
            }
            List<CustomerInfo> customerInfos = customerInfoService.listByIds(custPersons);
            Map<String, String> customerInfosMap = customerInfos.stream()
                    .filter(obj -> ObjectUtil.isNotEmpty(obj.getYwsrlx()))
                    .collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getYwsrlx, (k1, k2) -> k1));
            if (ObjectUtil.isNotEmpty(customerInfosMap)) {
                vos.forEach(vo -> {
                    if (customerInfosMap.containsKey(vo.getCustPerson())) {
                        vo.setYwsrlx(customerInfosMap.get(vo.getCustPerson()));
                    }
                });
            }
        }


        setEveryName(vos);

        final List<DataStatusVO> statueList = statusRedisHelper.getStatusInfoListByClassName(QuotationManagement.class.getSimpleName());
        final Map<Integer, DataStatusVO> dictValueMap = statueList.stream().collect(
                Collectors.toMap(DataStatusVO::getStatusValue, Function.identity(), (k1, k2) -> k1));
        vos.forEach(e -> {
            e.setDataStatus(dictValueMap.getOrDefault(e.getStatus(), null));
            String techRes = e.getTechRes();
            e.setReqOwnership(stringSimpleUserMap.get(techRes).getOrgId());
            e.setReqOwnershipName(stringSimpleUserMap.get(techRes).getOrgName());

        });

        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 重新报价的，不需要走审批，直接通过到待报价状态
     *
     * @param quotationDto 报价单
     */
    @Override
    public void confirmQuotation(QuotationManagementDTO quotationDto) {
        String requirementId = quotationDto.getRequirementId();
        if (ObjectUtil.isNotEmpty(requirementId)) {
            RequirementMangement requirementMangement = requirementMangementService.getById(requirementId);
            if (ObjectUtil.isEmpty(requirementMangement)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "未找到对应需求!");
            }
            String custPerson = requirementMangement.getCustPerson();
            CustomerInfo customerInfo = customerInfoService.getById(custPerson);
            if (ObjectUtil.isEmpty(customerInfo)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "未找到对应客户!");
            }
            String busScope = customerInfo.getBusScope();
            if (ObjectUtil.isEmpty(busScope)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "客户没有客户范围，请检查数据!");
            }
        }

        final QuotationManagement quotation = this.getById(quotationDto.getId());
        if (null == quotation) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未找到报价单!");
        }
        if (!QuotationManagementStatusEnum.IN_PREPARATION.getStatus().equals(quotation.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅编制中报价单可以确认!");
        }
        if (!StringUtils.hasText(quotation.getReQuotationId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅重新报价可以直接确认!");
        }
        this.update(new LambdaUpdateWrapper<QuotationManagement>().set(QuotationManagement::getStatus,
                QuotationManagementStatusEnum.CAN_QUOTATION.getStatus()).eq(QuotationManagement::getId, quotationDto.getId()));
    }

    /**
     * 查找字典，关联显示名称
     */
    @Override
    public void setEveryName(List<QuotationManagementVO> vos) {
        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> existRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        }
        List<String> finalExistRoleIds = existRoleIds;
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgId = simplerUser.getOrgId();//当前登陆人的20级部门
//        List<DictValueVO> currencyList = dictRedisService.getDictListByCode("currency_type");
//        Map<String, String> currencyMap = currencyList.stream().collect(
//                Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription, (k1, k2) -> k1));
        List<DictValueVO> currencyType = dictRedisService.getByDictNumber("currency_type", CurrentUserHelper.getOrgId());
        Map<String, String> currencyMap = currencyType.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription, (k1, k2) -> k1));

        List<String> busTypes = vos.stream().map(QuotationManagementVO::getBusinessType).filter(StringUtils::hasText)
                .distinct().collect(Collectors.toList());

        Map<String, String> busTypeMap = dictRedisService.getDictValueListByCode(busTypes).stream()
                .collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        boolean isCWFQ = false;
        logger.info("当前登陆人角色：{}", existRoleIds.toString());
        if (existRoleIds.contains("Business_100")) {
            //财务分权角色
            isCWFQ = true;
        }
        final boolean isCWFQ1 = isCWFQ;
        vos.forEach(e -> {
            e.setIsCWFQ(isCWFQ1);
            if (StringUtils.hasText(e.getCurrency())) {
                e.setCurrencyName(currencyMap.getOrDefault(e.getCurrency(), ""));
            }
            if (StringUtils.hasText(e.getBusinessType())) {
                e.setBusinessTypeName(busTypeMap.getOrDefault(e.getBusinessType(), ""));
            }
            e.setResSourceName(RequirementResouceEnum.getDesc(e.getResSource()));
            e.setYwsrlxName(IncomeTypeEnum.getDesc(e.getYwsrlx()));
            e.setIndustryName(CustomerIndustryEnum.getDesc(e.getIndustry()));
            e.setGroupInOutName(CustomerRelationshipEnum.getDesc(e.getGroupInOut()));
            e.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(e.getBusinessType()));
            //需要判断当前登陆人是否是单据的中心商务角色
            boolean isCenterBusiness = false; //是否中心商务
            if (!CollectionUtils.isEmpty(centerRoleDOS)) {
                List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                } else {
                    e.setIsCenterBusiness(false);
                }
            }
            if (isCenterBusiness) {
                String reqOwnership = e.getReqOwnership();
                if (Objects.equals(reqOwnership, orgId)) {
                    e.setIsCenterBusiness(true);
                } else {
                    e.setIsCenterBusiness(false);
                }
            }
        });

    }


    public void setEveryNamePage(List<QuotationManagementVO> vos) {
        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> existRoleIds;
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
        } else {
            existRoleIds = new ArrayList<>();
        }
        List<String> finalExistRoleIds = existRoleIds;
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgId = simplerUser.getOrgId();//当前登陆人的20级部门
        vos.forEach(e -> {
            e.setResSourceName(RequirementResouceEnum.getDesc(e.getResSource()));
            //需要判断当前登陆人是否是单据的中心商务角色
            boolean isCenterBusiness = false; //是否中心商务
            if (!CollectionUtils.isEmpty(centerRoleDOS)) {
                List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                } else {
                    e.setIsCenterBusiness(false);
                }
            }
            if (isCenterBusiness) {
                String reqOwnership = e.getReqOwnership();
                if (Objects.equals(reqOwnership, orgId)) {
                    e.setIsCenterBusiness(true);
                } else {
                    e.setIsCenterBusiness(false);
                }
            }
        });

    }

    /**
     * 返回合同的状态 字典
     *
     * @return list
     */
    @Override
    public List<DataStatusVO> listDataStatus() {
        return statusRedisHelper.getStatusInfoListByClassName(QuotationManagement.class.getSimpleName());
    }

    /**
     * 获取一个ECP公司商务角色用户
     *
     * @return 取第一个ecp用户
     */
    @Override
    public SimpleUser getIssuer() {
        RoleVO role = roleRedisHelper.getRole("Business_020", CurrentUserHelper.getOrgId());
        if (null != role) {
            List<String> userIds = roleUserHelper.getUserIdsOfRoleId(CurrentUserHelper.getOrgId(), role.getId());
            if (!CollectionUtils.isEmpty(userIds)) {
                return userRedisHelper.getSimpleUserById(userIds.get(0));
            }
        }
        return null;
    }

    /**
     * 生成报价单号
     * BJ + 系统日期 + 专业编码 + 三位流水号；
     *
     * @param requirement 对应需求单
     * @return 编号
     */
    private String generateQuotationNo(RequirementMangement requirement) {
        DeptVO dept = deptRedisHelper.getDeptById(requirement.getReqOwnership());

        try {
            // 因为编码可以用户自己输入，那么生成的流水号很可能重复，所以要去检查是否已存在,存在就重新生成，
            // 不要抛错，最多重试10次，一般来说不会重复这么多次
            for (int i = 0; i < 10; i++) {
                ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("QuotationManagement", "number",
                        false, "");
                String number = responseDTO.getResult();
                // 把流水号生成中的 $ 替换成承担部门编码
                number = number.replace("$", dept.getCodeName());

                LambdaQueryWrapper<QuotationManagement> checkCode = new LambdaQueryWrapper<>();
                checkCode.eq(QuotationManagement::getQuotationId, number);
                if (CollectionUtils.isEmpty(this.list(checkCode))) {
                    return number;
                }
            }

        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "生成报价单编码失败！");
        }
        return "";
    }

    private List<String> getDeptIds(String userId) {
        //获取当前用户的所在的部门
        LambdaQueryWrapperX<DeptUserDO> deptUserQuery = new LambdaQueryWrapperX<>();
        deptUserQuery.eq(DeptUserDO::getUserId, userId);
        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(deptUserQuery);
        if (CollectionUtils.isEmpty(deptUserDOS)) {
            log.info("当前用户没有归属的部门");
            return List.of("");
        }
        LambdaQueryWrapperX<DeptDO> deptQuery = new LambdaQueryWrapperX();
        deptQuery.in(DeptDO::getId, deptUserDOS.stream().map(DeptUserDO::getDeptId).collect(Collectors.toList()));
        List<DeptDO> deptDOS = deptDOMapper.selectList(deptQuery);
        if (CollectionUtils.isEmpty(deptDOS)) {
            log.info("当前用户查询的部门信息不存在");
            return List.of("");
        }

        //获取当前用户的上级组织
        Set<String> parentDept = Sets.newHashSet();
        deptDOS.forEach(dept -> {
            if (StringUtils.hasText(dept.getChain())) {
                parentDept.addAll(Arrays.asList(dept.getChain().split(",")));
            }
        });

        //获取部门、中心一级的组织机构

        LambdaQueryWrapperX<DeptDO> deptCenterQuery = new LambdaQueryWrapperX<>();
        deptCenterQuery.eq(DeptDO::getType, "20");
        deptCenterQuery.eq(DeptDO::getLogicStatus, 1);
        List<DeptDO> departCenterDepts = deptDOMapper.selectList(deptCenterQuery);

        List<String> returnLists = Lists.newArrayList();
        departCenterDepts.stream().forEach(deptDO -> {
            if (parentDept.contains(deptDO.getId())) {
                returnLists.add(deptDO.getId());
            }
        });

        if (CollectionUtils.isEmpty(returnLists)) {
            log.info("当前用户的用户所在的部门的上级机构中不存在 部门/中心一级的机构");
            return List.of("");
        }

        //用户是承担部门的 主要领导 或者 用户是中心商务接口人 ， 中心技术接口人 角色

        //如果是 中心商务接口人 或者 中心技术接口人 角色
        if (isInterfaceRole(userId)) {
            log.info("当前用户是【{}】中心/部门  的中心商务接口人,或者中心技术接口人", returnLists);
            return returnLists;
        }

        //找出上面的部门中心的部门中，那些部门中心，当前用户是主管领导
        LambdaQueryWrapperX<DeptLeaderDO> deptLeaderQueryWrapperx = new LambdaQueryWrapperX<>();
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getType, "main");//主管lingd
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getClassName, "OrganizationLeader");//组织管理领导
        deptLeaderQueryWrapperx.in(DeptLeaderDO::getDeptId, returnLists);//承担部门（部门、中心）
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getUserId, CurrentUserHelper.getCurrentUserId());//当前登录用户
        List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(deptLeaderQueryWrapperx);
        List<String> userMainLeaderDeptIds = new ArrayList<>();
        for (DeptLeaderDO deptLeaderDO : deptLeaderDOS) {
            String deptId = deptLeaderDO.getDeptId();
            userMainLeaderDeptIds.add(deptId);
        }
        if (CollectionUtils.isEmpty(userMainLeaderDeptIds)) {
            log.info("用户不存在部门/中心的主管领导的部门");
            return List.of("");
        }
        return userMainLeaderDeptIds;
    }

    /**
     * 当前用户是否是 中心商务接口人 或者 中心技术接口人 接口人
     */
    private boolean isInterfaceRole(String currentUserId) {
        List<RoleUserDO> roleUserDOS = roleUserDOMapper.selectList(RoleUserDO::getUserId, currentUserId);
        if (CollectionUtils.isEmpty(roleUserDOS)) {
            log.info("当前用户没有关联角色");
            return false;
        }
        List<String> roleUserRoleIDs = roleUserDOS.stream().map(RoleUserDO::getRoleId).collect(Collectors.toList());

        List<RoleDO> roleDOS = roleDOMapper.selectList(RoleDO::getCode,
                Arrays.asList(RoleColeConstant.CENTER_BUSINESS_INTERFACE_ROLE_CODE,
                        RoleColeConstant.CENTER_TECHNICAL_INTERFACE_ROLE_CODE));

        if (CollectionUtils.isEmpty(roleDOS)) {
            log.info("当前系统没有中心商务接口人/中心技术接口人 的角色");
            return false;
        }

        List<String> roleIds = roleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());

        for (String id : roleIds) {
            if (roleUserRoleIDs.contains(id)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean deleteFile(List<String> fileIds) throws Exception {
        return fileApiService.deleteByIds(fileIds);
    }


    @Override
    public void exportExcelData(Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuotationRequirementView> condition = new LambdaQueryWrapperX<>(QuotationRequirementView.class);
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId,
                "t", QuotationManagement::getRequirementId);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        QuotationManagementDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            if (ObjectUtil.isNotEmpty(query.getPriority())) {
                condition.eq(RequirementMangement::getPriority, query.getPriority());
            }
            if (ObjectUtil.isNotEmpty(query.getPrioritySort())) {
                if ("0".equals(query.getPrioritySort())) {
                    condition.orderByAsc(RequirementMangement::getPriority);
                }
                if ("1".equals(query.getPrioritySort())) {
                    condition.orderByDesc(RequirementMangement::getPriority);
                }
            }
        }
        List<Integer> integers = Arrays.asList(1, 121, 140, 150);
        condition.in(QuotationRequirementView::getStatus, integers);
        condition.orderByDesc(QuotationRequirementView::getCreateTime);
        List<QuotationRequirementView> quotationRequirementViews = quotationRequirementViewMapper.selectDataPermissionList(QuotationRequirementView.class, condition);
//        List<QuotationManagementVO> vos = BeanCopyUtils.convertListTo(quotationRequirementViews, QuotationManagementVO::new);
        List<QuotationManagementExcelExportDTO> excelExportDTOS = BeanCopyUtils.convertListTo(quotationRequirementViews, QuotationManagementExcelExportDTO::new);
        //需求id
        List<String> requirmentIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getRequirementId).distinct().collect(Collectors.toList());
        //承担部门
        Map<String, SimpleDeptVO> deptMap = new HashMap<>();
        List<String> deptIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getReqOwnership).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<SimpleDeptVO> simpleDeptByIds = deptRedisHelper.getSimpleDeptByIds(deptIds);
            for (SimpleDeptVO simpleDeptById : simpleDeptByIds) {
                deptMap.put(simpleDeptById.getId(), simpleDeptById);
            }

        }
        //需求id 需求表
        Map<String, RequirementMangement> requirementMangementMap = new HashMap<>();
        //业务类型
        List<String> businessTypes = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(requirmentIds)) {
            List<RequirementMangement> requirementMangements = requirementMangementService.listByIds(requirmentIds);
            for (RequirementMangement requirementMangement : requirementMangements) {
                requirementMangementMap.put(requirementMangement.getId(), requirementMangement);
            }
            businessTypes = requirementMangements.stream().map(RequirementMangement::getBusinessType).distinct().collect(Collectors.toList());
        }
        //业务类型去除空
        List<String> collect = businessTypes.stream().filter(x -> x != null && !x.isEmpty()).collect(Collectors.toList());
        //业务类型
        Map<String, String> businessTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(collect)) {
            List<DictValueVO> dictValueListByCode = dictRedisHelper.getDictValueListByCode(collect);
            businessTypeMap = dictValueListByCode.stream().collect(
                    Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        }
        List<String> TotalUserIds = new ArrayList<>();

        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程发起人
        List<String> collectFolwUserIds = new ArrayList<>();
        List<String> quotationIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(quotationIds)) {
            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
//            try {
//                listByBusinessIds = workflowFeignService.findListByBusinessIds(quotationIds);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }
        }
        //流程人
        TotalUserIds.addAll(collectFolwUserIds);
        //流程创建人工号
//        Map<String, String> uesrFolwMap = new HashMap<>();
//        if (ObjectUtil.isNotEmpty(collectFolwUserIds)) {
//            List<UserVO> userByIds = userRedisHelper.getUserByIds(collectFolwUserIds);
//            for (UserVO userById : userByIds) {
//                uesrFolwMap.put(userById.getId(), userById.getCode());
//            }
//        }
        //报价执行人
        List<String> icsuerIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getIssuer).distinct().collect(Collectors.toList());
//        Map<String, String> icsuerMap = new HashMap<>();
//        if (ObjectUtil.isNotEmpty(icsuerIds)) {
//            List<UserVO> userByIds = userRedisHelper.getUserByIds(icsuerIds);
//            for (UserVO userById : userByIds) {
//                icsuerMap.put(userById.getId(), userById.getName());
//            }
//        }
        TotalUserIds.addAll(icsuerIds);

        Map<String, UserVO> userVOMap = new HashMap<>();
        List<String> TotalUserListDist = TotalUserIds.stream().distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(TotalUserListDist)) {
            List<UserVO> userByIds = userRedisHelper.getUserByIds(TotalUserListDist);
            for (UserVO userById : userByIds) {
                userVOMap.put(userById.getId(), userById);
            }
        }

        //报价接收方
        Map<String, String> quoteMap = new HashMap<>();
        List<String> quoteIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getQuoteAcceptCom).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(quoteIds)) {
            List<CustomerInfo> customerInfos = customerInfoService.listByIds(quoteIds);
            for (CustomerInfo customerInfo : customerInfos) {
                quoteMap.put(customerInfo.getId(), customerInfo.getCusName());
            }

        }

        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        List<QuotationManagement> quotationManagements = this.list();
        ArrayList<QuotationManagementVO> quotationManagementVOS = new ArrayList<>();
        for (QuotationManagement quotationManagement : quotationManagements) {
            QuotationManagementVO result = BeanCopyUtils.convertTo(quotationManagement, QuotationManagementVO::new);
            quotationManagementVOS.add(result);
        }

//        List<UserBaseCacheVO> allUserBaseCache = userRedisHelper.getAllUserBaseCache(CurrentUserHelper.getOrgId());
        List<UserDO> userDOS = userDOMapper.selectList();
        Map<String, UserDO> userBaseCacheVOMap = userDOS.stream().collect(Collectors.toMap(UserDO::getId, Function.identity()));
//        Map<String, String> userBaseCacheVOMap = allUserBaseCache.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        List<CustomerInfo> customerInfos = customerInfoService.list();
        Map<String, CustomerInfo> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
        List<RequirementManageCustContact> requirementManageCustContacts = requirementManageCustContactService.list();
        Map<String, RequirementManageCustContact> requirementManageCustContactMap = requirementManageCustContacts.stream().collect(Collectors.toMap(RequirementManageCustContact::getRequirementId, Function.identity(), (k1, k2) -> k1));
        setEveryName(quotationManagementVOS);
        Map<String, QuotationManagementVO> quotationManagementMap = quotationManagementVOS.stream().collect(Collectors.toMap(QuotationManagementVO::getId, Function.identity()));
        //经营管理需求来源
        List<DictValueVO> resource = dictRedisHelper.getDictList("dict1795720778296483840");
        Map<String, String> resourceMap = resource.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        for (QuotationManagementExcelExportDTO excelExportDTO : excelExportDTOS) {
            //承担部门
            SimpleDeptVO simpleDeptVO = deptMap.get(excelExportDTO.getReqOwnership());
            if (ObjectUtil.isNotEmpty(simpleDeptVO)) {
                excelExportDTO.setUndertakeDeptName(simpleDeptVO.getName());
                if (ObjectUtil.isNotEmpty(simpleDeptVO.getMainLeader())) {
                    //中心负责人
                    if (ObjectUtil.isNotEmpty(simpleDeptVO.getMainLeader())) {
                        UserDO userById = userBaseCacheVOMap.get(simpleDeptVO.getMainLeader().get(0).getUserId());
                        excelExportDTO.setLeaderName(userById == null ? "" : "[" + userById.getCode() + "]" + userById.getName());
                    }

                }
            }
            //工作主题
            excelExportDTO.setWorkTopic(excelExportDTO.getUndertakeDeptName() + "-" + excelExportDTO.getQuotationName() + "- [报价管理流程]");
            excelExportDTO.setQuotationFlowName("报价管理流程");
            excelExportDTO.setCurrencyName(currencyeMap.get(excelExportDTO.getCurrency()));
            //流程状态都是已完成
            excelExportDTO.setStatusName("已完成");
            //报价发出途径
            excelExportDTO.setIssueWayName(RouteOfQuotationEnum.getDesc(excelExportDTO.getIssueWay()));
            //价格
            excelExportDTO.setQuoteAmtStr(excelExportDTO.getQuoteAmt().toString());
            //报价状态
            Integer status = excelExportDTO.getStatus();
            if (status == 1) {
                excelExportDTO.setQuotationStatus("已中标");
            }
            if (status == 121) {
                excelExportDTO.setQuotationStatus("已投标");
            }
            if (status == 140) {
                excelExportDTO.setQuotationStatus("未中标");
            }
            if (status == 150) {
                excelExportDTO.setQuotationStatus("可投标");
            }
            if (ObjectUtil.isNotEmpty(excelExportDTO.getFinTradeBus())) {
                if (excelExportDTO.getFinTradeBus().booleanValue()) {
                    excelExportDTO.setFinTradeBusName("是");
                } else {
                    excelExportDTO.setFinTradeBusName("否");
                }
            }
            if (ObjectUtil.isNotEmpty(excelExportDTO.getIsContion())) {
                if (excelExportDTO.getIsContion().booleanValue()) {
                    excelExportDTO.setIsContionName("是");
                } else {
                    excelExportDTO.setIsContionName("否");
                }
            }

            if (ObjectUtil.isNotEmpty(excelExportDTO.getIsContionTow())) {
                if (excelExportDTO.getIsContionTow().booleanValue()) {
                    excelExportDTO.setIsContionTowName("是");
                } else {
                    excelExportDTO.setIsContionTowName("否");
                }
            }
            //优先级
            String priority = excelExportDTO.getPriority();
            if (ObjectUtil.isNotEmpty(priority)) {
                if ("1".equals(priority)) {
                    excelExportDTO.setPriority("低");
                }
                if ("2".equals(priority)) {
                    excelExportDTO.setPriority("中");
                }
                if ("3".equals(priority)) {
                    excelExportDTO.setPriority("高");
                }
            }


            //流程设置
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(excelExportDTO.getId());
            if (ObjectUtil.isNotEmpty(flowTemplateBusinessVO)) {
                excelExportDTO.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                excelExportDTO.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                excelExportDTO.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                UserVO userVO = userVOMap.get(flowTemplateBusinessVO.getApplyUserId());
                excelExportDTO.setFlowCreatePersonNumber(userVO == null ? "" : userVO.getCode());
            }
            //报价执行人
            UserVO userVO = userVOMap.get(excelExportDTO.getIssuer());
            excelExportDTO.setIssuerName(userVO == null ? "" : "[" + userVO.getCode() + "]" + userVO.getName());
            //报价接收方
            excelExportDTO.setQuoteAcceptComName(quoteMap.getOrDefault(excelExportDTO.getQuoteAcceptCom(), ""));
            RequirementMangement requirementMangement = requirementMangementMap.get(excelExportDTO.getRequirementId());
            QuotationManagementVO quotationManagement = quotationManagementMap.get(excelExportDTO.getId());
            //报价结果
            String result1 = quotationManagement.getResult();
            if (ObjectUtil.isNotEmpty(result1)) {
                excelExportDTO.setResult(result1);
            }


            //所级负责人
            String officeLeader = quotationManagement.getOfficeLeader();
            if (ObjectUtil.isNotEmpty(officeLeader)) {
                UserDO userBaseCacheVO = userBaseCacheVOMap.get(officeLeader);
                excelExportDTO.setOfficeLeaderName("[" + userBaseCacheVO.getCode() + "]" + userBaseCacheVO.getName());
            }
            RequirementMangementVO result = new RequirementMangementVO();
            //获取客户名称
            if (StringUtils.hasText(requirementMangement.getCustPerson())) {
                CustomerInfo customerInfo = customerInfoMap.get(requirementMangement.getCustPerson());
                if (customerInfo != null) {
                    result.setCustPersonName(customerInfo.getCusName());
                    result.setSalesClassification(customerInfo.getYwsrlx() + customerInfo.getIndustry());
                    result.setYwsrlx(customerInfo.getYwsrlx());
                    result.setIndustry(customerInfo.getIndustry());
                    result.setGroupInOut(customerInfo.getGroupInOut());
                    result.setYwsrlxName(IncomeTypeEnum.getDesc(customerInfo.getYwsrlx()));
                    result.setIndustryName(CustomerIndustryEnum.getDesc(customerInfo.getIndustry()));
                    result.setGroupInOutName(CustomerRelationshipEnum.getDesc(customerInfo.getGroupInOut()));
                }
            }

            BeanCopyUtils.copyProperties(result, quotationManagement);
            //销售业务分类
            excelExportDTO.setSalesClassification(quotationManagement.getGroupInOutName() + quotationManagement.getIndustryName());
            RequirementManageCustContact contacts = requirementManageCustContactMap.get(requirementMangement.getId());
            if (ObjectUtil.isNotEmpty(contacts)) {
                //客户联系人
                excelExportDTO.setCustConPersonName(contacts.getContactName());
                //客户联系人电话
                excelExportDTO.setCustContactPh(contacts.getContactPhone());
            }
            if (ObjectUtil.isNotEmpty(requirementMangement)) {
                //客户名称
                excelExportDTO.setCustPersonName(requirementMangement.getCustPersonName());
                //业务类型
                excelExportDTO.setBusinessTypeName(businessTypeMap.getOrDefault(requirementMangement.getBusinessType(), ""));
            }
            //合同获取方式
            String resSource = requirementMangement.getResSource();
            if (ObjectUtil.isNotEmpty(resSource)) {
                String resSourceName = resourceMap.get(resSource);
                excelExportDTO.setResSource(resSourceName);
            }
        }

        String fileName = "报价报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
//        try {
        EasyExcel.write(response.getOutputStream(), QuotationManagementExcelExportDTO.class).sheet("sheet1").doWrite(excelExportDTOS);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
//        }

    }

    @Override
    public Boolean check(String id) {
        if (ObjectUtil.isEmpty(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "ID不能为空!");
        }


        QuotationManagement quotationManagement = this.getById(id);
        if (quotationManagement == null) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未找到对应的报价管理!");
        }

        String requirementId = quotationManagement.getRequirementId();
        if (ObjectUtil.isEmpty(requirementId)) {
            return true;
        }

        RequirementMangement requirementMangement = requirementMangementService.getById(requirementId);
        if (ObjectUtil.isEmpty(requirementMangement)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未找到对应需求!");
        }

        String custPerson = requirementMangement.getCustPerson();
        CustomerInfo customerInfo = customerInfoService.getById(custPerson);
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未找到对应客户!");
        }

        String busScope = customerInfo.getBusScope();
        if (ObjectUtil.isEmpty(busScope)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "客户没有客户范围，请检查数据!");
        }

        return true;

    }

    @Override
    public void exportExcelDataNew(Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuotationManagement> condition = new LambdaQueryWrapperX<>(QuotationManagement.class);
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId,
                "t", QuotationManagement::getRequirementId);
        condition.selectAll(QuotationManagement.class);
        condition.selectAs(RequirementMangement::getCustConPerson, QuotationManagement::getCustConPerson);
        condition.selectAs(RequirementMangement::getBusinessType, QuotationManagement::getRequireBusinessType);
        condition.selectAs(RequirementMangement::getReqOwnership, QuotationManagement::getReqOwnership);
        condition.selectAs(RequirementMangement::getId, QuotationManagement::getRequirementId);
        condition.selectAs(RequirementMangement::getTechRes, QuotationManagement::getTechRes);
        condition.selectAs(RequirementMangement::getCustPerson, QuotationManagement::getCustPerson);
        condition.selectAs(RequirementMangement::getCustPersonName, QuotationManagement::getCustPersonName);
        condition.selectAs(RequirementMangement::getResSource, QuotationManagement::getResSource);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QuotationManagement::getCreateTime);
        List<QuotationManagement> quotationRequirementViews = quotationManagementMapper.selectList(condition);
        List<QuotationManagementExcelExportDTO> excelExportDTOS = BeanCopyUtils.convertListTo(quotationRequirementViews, QuotationManagementExcelExportDTO::new);
        //承担部门
        List<String> requirementIds = excelExportDTOS.stream()
                .map(QuotationManagementExcelExportDTO::getRequirementId)
                .distinct()
                .collect(Collectors.toList());


        Map<String, SimpleDeptVO> deptMap = new HashMap<>();
        List<String> deptIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getReqOwnership).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<SimpleDeptVO> simpleDeptByIds = deptRedisHelper.getSimpleDeptByIds(deptIds);
            for (SimpleDeptVO simpleDeptById : simpleDeptByIds) {
                deptMap.put(simpleDeptById.getId(), simpleDeptById);
            }

        }
        //需求id 需求表
        Map<String, RequirementMangement> requirementMangementMap = new HashMap<>();
        //业务类型
        // 业务类型
        Map<String, String> businessTypeMap = new HashMap<>();
        List<DictValueVO> dictValueListByCode = dictRedisHelper.getDictListByCode("businessType");
        businessTypeMap = dictValueListByCode.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        List<String> TotalUserIds = new ArrayList<>();

        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程发起人
        List<String> collectFolwUserIds = new ArrayList<>();
        List<String> quotationIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(quotationIds)) {
            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(quotationIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }
        }
        //流程人
        TotalUserIds.addAll(collectFolwUserIds);
        //报价执行人
        List<String> icsuerIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getIssuer).distinct().collect(Collectors.toList());
        TotalUserIds.addAll(icsuerIds);

        Map<String, UserBaseCacheVO> userVOMap = new HashMap<>();
        List<String> TotalUserListDist = TotalUserIds.stream().distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(TotalUserListDist)) {
            List<UserBaseCacheVO> userByIds = userRedisHelper.getUserBaseCacheByIds(TotalUserListDist);
            for (UserBaseCacheVO userById : userByIds) {
                userVOMap.put(userById.getId(), userById);
            }
        }
        //报价接收方
        Map<String, String> quoteMap = new HashMap<>();
        LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getYwsrlx, CustomerInfo::getSalesClass);
        List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapper);
        for (CustomerInfo customerInfo : customerInfos) {
            quoteMap.put(customerInfo.getId(), customerInfo.getCusName());
        }
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        List<QuotationManagement> quotationManagements = this.list();
        ArrayList<QuotationManagementVO> quotationManagementVOS = new ArrayList<>();
        for (QuotationManagement quotationManagement : quotationManagements) {
            QuotationManagementVO result = BeanCopyUtils.convertTo(quotationManagement, QuotationManagementVO::new);
            quotationManagementVOS.add(result);
        }
        List<UserDO> allUserBaseCache = userDOMapper.selectList();
        Map<String, UserDO> userBaseCacheVOMap = allUserBaseCache.stream().collect(Collectors.toMap(UserDO::getId, Function.identity()));
        Map<String, CustomerInfo> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
        LambdaQueryWrapper<RequirementManageCustContact> requirementManageCustContactLambdaQueryWrapper = new LambdaQueryWrapper<>();
        requirementManageCustContactLambdaQueryWrapper.in(RequirementManageCustContact::getRequirementId, requirementIds);
        requirementManageCustContactLambdaQueryWrapper.select(RequirementManageCustContact::getRequirementId, RequirementManageCustContact::getContactName, RequirementManageCustContact::getContactPhone);
        Map<String, QuotationManagementVO> quotationManagementMap = quotationManagementVOS.stream().collect(Collectors.toMap(QuotationManagementVO::getId, Function.identity()));
        //经营管理需求来源
        Map<String, String> resourceMap = new HashMap<>();
        for (RequirementResouceEnum lt : RequirementResouceEnum.values()) {
            resourceMap.put(lt.name, lt.desc);
        }
        List<String> userIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getTechRes).distinct().collect(Collectors.toList());
        List<DeptUserRelationVO> deptByUserIds = deptUserHelper.getDeptByUserIds(CurrentUserHelper.getOrgId(), userIds);
        Map<String, String> deptIdMap = deptByUserIds.stream().collect(
                Collectors.toMap(DeptUserRelationVO::getUserId, DeptUserRelationVO::getOrgId, (v1, v2) -> v1));
        List<String> orgIdList = deptByUserIds.stream()
                .map(DeptUserRelationVO::getOrgId)
                .collect(Collectors.toList());
        List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(orgIdList);
        Map<String, String> allDeptByOrgId = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        extracted(excelExportDTOS, deptIdMap, deptMap, userBaseCacheVOMap, currencyeMap, flowMap, userVOMap, quoteMap, quotationManagementMap, customerInfoMap, businessTypeMap, resourceMap);

        String fileName = "报价报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        EasyExcel.write(response.getOutputStream(), QuotationManagementExcelExportDTO.class).sheet("sheet1").doWrite(excelExportDTOS);


    }

    @Override
    public Page<QuotationManagementVO> pagesMenu(Page<QuotationManagementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QuotationManagement> condition = new LambdaQueryWrapperX<>(QuotationManagement.class);
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId,
                "t", QuotationManagement::getRequirementId);
        condition.selectAll(QuotationManagement.class);
        condition.selectAs(RequirementMangement::getCustConPerson, QuotationManagement::getCustConPerson);
        condition.selectAs(RequirementMangement::getBusinessType, QuotationManagement::getRequireBusinessType);
        condition.selectAs(RequirementMangement::getReqOwnership, QuotationManagement::getReqOwnership);
        condition.selectAs(RequirementMangement::getId, QuotationManagement::getRequirementId);
        condition.selectAs(RequirementMangement::getTechRes, QuotationManagement::getTechRes);
        condition.selectAs(RequirementMangement::getCustPerson, QuotationManagement::getCustPerson);
        condition.selectAs(RequirementMangement::getCustPersonName, QuotationManagement::getCustPersonName);
        condition.selectAs(RequirementMangement::getResSource, QuotationManagement::getResSource);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        condition.orderByDesc(QuotationManagement::getCreateTime);
        Page<QuotationManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuotationManagement::new));
        IPage<QuotationManagement> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<QuotationManagement> quotationManagementIPage = quotationManagementMapper.selectPage(mPage, condition);
        List<QuotationManagement> records = quotationManagementIPage.getRecords();

        Page<QuotationManagementVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), quotationManagementIPage.getTotal());
        List<QuotationManagementExcelExportDTO> excelExportDTOS = BeanCopyUtils.convertListTo(records, QuotationManagementExcelExportDTO::new);
        //承担部门
        List<String> requirementIds = excelExportDTOS.stream()
                .map(QuotationManagementExcelExportDTO::getRequirementId)
                .distinct()
                .collect(Collectors.toList());


        //需求id 需求表
        Map<String, RequirementMangement> requirementMangementMap = new HashMap<>();
        //业务类型
        // 业务类型
        Map<String, String> businessTypeMap = new HashMap<>();
        List<DictValueVO> dictValueListByCode = dictRedisHelper.getDictListByCode("businessType");
        businessTypeMap = dictValueListByCode.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        List<String> TotalUserIds = new ArrayList<>();

        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程发起人
        List<String> collectFolwUserIds = new ArrayList<>();
        List<String> quotationIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(quotationIds)) {
            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(quotationIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }
        }
        //流程人
        TotalUserIds.addAll(collectFolwUserIds);
        //报价执行人
        List<String> icsuerIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getIssuer).distinct().collect(Collectors.toList());
        TotalUserIds.addAll(icsuerIds);

        Map<String, UserBaseCacheVO> userVOMap = new HashMap<>();
        List<String> TotalUserListDist = TotalUserIds.stream().distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(TotalUserListDist)) {
            List<UserBaseCacheVO> userByIds = userRedisHelper.getUserBaseCacheByIds(TotalUserListDist);
            for (UserBaseCacheVO userById : userByIds) {
                userVOMap.put(userById.getId(), userById);
            }
        }
        //报价接收方
        Map<String, String> quoteMap = new HashMap<>();
        LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getYwsrlx, CustomerInfo::getSalesClass);
        List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapper);
        for (CustomerInfo customerInfo : customerInfos) {
            quoteMap.put(customerInfo.getId(), customerInfo.getCusName());
        }
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        List<QuotationManagement> quotationManagements = this.list();
        ArrayList<QuotationManagementVO> quotationManagementVOS = new ArrayList<>();
        for (QuotationManagement quotationManagement : quotationManagements) {
            QuotationManagementVO result = BeanCopyUtils.convertTo(quotationManagement, QuotationManagementVO::new);
            quotationManagementVOS.add(result);
        }
        List<UserDO> allUserBaseCache = userDOMapper.selectList();
        Map<String, UserDO> userBaseCacheVOMap = allUserBaseCache.stream().collect(Collectors.toMap(UserDO::getId, Function.identity()));
        Map<String, CustomerInfo> customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
        LambdaQueryWrapper<RequirementManageCustContact> requirementManageCustContactLambdaQueryWrapper = new LambdaQueryWrapper<>();
        requirementManageCustContactLambdaQueryWrapper.in(RequirementManageCustContact::getRequirementId, requirementIds);
        requirementManageCustContactLambdaQueryWrapper.select(RequirementManageCustContact::getRequirementId, RequirementManageCustContact::getContactName, RequirementManageCustContact::getContactPhone);
        Map<String, QuotationManagementVO> quotationManagementMap = quotationManagementVOS.stream().collect(Collectors.toMap(QuotationManagementVO::getId, Function.identity()));
        //经营管理需求来源
        Map<String, String> resourceMap = new HashMap<>();
        for (RequirementResouceEnum lt : RequirementResouceEnum.values()) {
            resourceMap.put(lt.name, lt.desc);
        }
        List<String> userIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getTechRes).distinct().collect(Collectors.toList());
        List<DeptUserRelationVO> deptByUserIds = deptUserHelper.getDeptByUserIds(CurrentUserHelper.getOrgId(), userIds);
        Map<String, String> deptIdMap = deptByUserIds.stream().collect(
                Collectors.toMap(DeptUserRelationVO::getUserId, DeptUserRelationVO::getOrgId, (v1, v2) -> v1));
        List<String> orgIdList = deptByUserIds.stream()
                .map(DeptUserRelationVO::getOrgId)
                .collect(Collectors.toList());
        Map<String, SimpleDeptVO> deptMap = new HashMap<>();
        List<String> deptIds = excelExportDTOS.stream().map(QuotationManagementExcelExportDTO::getReqOwnership).distinct().collect(Collectors.toList());
        deptIds.addAll(orgIdList);
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<SimpleDeptVO> simpleDeptByIds = deptRedisHelper.getSimpleDeptByIds(deptIds);
            for (SimpleDeptVO simpleDeptById : simpleDeptByIds) {
                deptMap.put(simpleDeptById.getId(), simpleDeptById);
            }

        }

        extracted(excelExportDTOS, deptIdMap, deptMap, userBaseCacheVOMap, currencyeMap, flowMap, userVOMap, quoteMap, quotationManagementMap, customerInfoMap, businessTypeMap, resourceMap);


        List<QuotationManagementVO> quotationManagementVOS1 = BeanCopyUtils.convertListTo(excelExportDTOS, QuotationManagementVO::new);
        pageResult.setContent(quotationManagementVOS1);
        return pageResult;
    }

    private void extracted(List<QuotationManagementExcelExportDTO> excelExportDTOS, Map<String, String> deptIdMap
            , Map<String, SimpleDeptVO> deptMap, Map<String, UserDO> userBaseCacheVOMap
            , Map<String, String> currencyeMap, Map<String, FlowTemplateBusinessVO> flowMap
            , Map<String, UserBaseCacheVO> userVOMap, Map<String, String> quoteMap
            , Map<String, QuotationManagementVO> quotationManagementMap, Map<String, CustomerInfo> customerInfoMap
            , Map<String, String> businessTypeMap, Map<String, String> resourceMap) {

        //经营管理需求来源
        Map<String, String> processStatusMap = new HashMap<>();
        for (ProcessStatusEnum lt : ProcessStatusEnum.values()) {
            processStatusMap.put(lt.code, lt.description);
        }
        for (QuotationManagementExcelExportDTO excelExportDTO : excelExportDTOS) {
            if (ObjectUtil.isNotEmpty(excelExportDTO.getRequirementId())) {
                excelExportDTO.setIsHaveRequire("是");
            } else {
                excelExportDTO.setIsHaveRequire("否");
            }
            //所级
            String techRspUser = excelExportDTO.getTechRes();
            String deptId = deptIdMap.getOrDefault(techRspUser, "");
            if (ObjectUtil.isNotEmpty(deptId) && ObjectUtil.isNotEmpty(deptMap.get(deptId)) && ObjectUtil.isNotEmpty(deptMap.get(deptId).getName())) {
                excelExportDTO.setDeptName(deptMap.get(deptId).getName());
            }
            //承担部门
            SimpleDeptVO simpleDeptVO = deptMap.get(excelExportDTO.getReqOwnership());
            if (ObjectUtil.isNotEmpty(simpleDeptVO)) {
                excelExportDTO.setUndertakeDeptName(simpleDeptVO.getName());
                if (ObjectUtil.isNotEmpty(simpleDeptVO.getMainLeader())) {
                    //中心负责人
                    String userId = simpleDeptVO.getMainLeader().get(0).getUserId();
                    if (ObjectUtil.isNotEmpty(userId)) {
                        UserDO userDO = userBaseCacheVOMap.get(userId);
                        if (ObjectUtil.isNotEmpty(userDO)) {
                            String leaderName = userDO.getName();
                            excelExportDTO.setLeaderName(leaderName == null ? "" : "[" + userDO.getCode() + "]" + leaderName);
                        }
                    }
                }
            }
            //工作主题
            excelExportDTO.setWorkTopic(excelExportDTO.getUndertakeDeptName() + "-" + excelExportDTO.getQuotationName() + "- [报价管理流程]");
            excelExportDTO.setQuotationFlowName("报价管理流程");
            excelExportDTO.setCurrencyName(currencyeMap.get(excelExportDTO.getCurrency()));
//            //流程状态都是已完成
//            excelExportDTO.setStatusName("已完成");
            //报价发出途径
            excelExportDTO.setIssueWayName(RouteOfQuotationEnum.getDesc(excelExportDTO.getIssueWay()));
            //价格
            if (ObjectUtil.isNotEmpty(excelExportDTO.getQuoteAmt())) {
                excelExportDTO.setQuoteAmtStr(excelExportDTO.getQuoteAmt().toString());
            }
            //报价状态
            Integer status = excelExportDTO.getStatus();

            HashMap<Integer, String> statusMap = new HashMap<>();
            statusMap.put(1, "已中标");
            statusMap.put(121, "已投标");
            statusMap.put(140, "未中标");
            statusMap.put(150, "可投标");
            statusMap.put(160, "已终止");
            statusMap.put(111, "已作废");
            statusMap.put(120, "编制中");
            statusMap.put(110, "审核中");
            excelExportDTO.setQuotationStatus(statusMap.getOrDefault(status, ""));
            String techRes = excelExportDTO.getTechRes();
            if (ObjectUtil.isNotEmpty(techRes)) {
                UserDO userDO = userBaseCacheVOMap.get(techRes);
                if (ObjectUtil.isNotEmpty(userDO)) {
                    excelExportDTO.setContionThree("[" + userDO.getCode() + "]" + userDO.getName());

                }
            }

            if (ObjectUtil.isNotEmpty(excelExportDTO.getFinTradeBus())) {
                if (excelExportDTO.getFinTradeBus()) {
                    excelExportDTO.setFinTradeBusName("是");
                } else {
                    excelExportDTO.setFinTradeBusName("否");
                }
            }
            if (ObjectUtil.isNotEmpty(excelExportDTO.getIsContion())) {
                if (excelExportDTO.getIsContion()) {
                    excelExportDTO.setIsContionName("是");
                } else {
                    excelExportDTO.setIsContionName("否");
                }
            }
            if (ObjectUtil.isNotEmpty(excelExportDTO.getIsContionTow())) {
                if (excelExportDTO.getIsContionTow()) {
                    excelExportDTO.setIsContionTowName("是");
                } else {
                    excelExportDTO.setIsContionTowName("否");
                }
            }
            //优先级
            String priority = excelExportDTO.getPriority();
            if (ObjectUtil.isNotEmpty(priority)) {
                if ("1".equals(priority)) {
                    excelExportDTO.setPriority("低");
                }
                if ("2".equals(priority)) {
                    excelExportDTO.setPriority("中");
                }
                if ("3".equals(priority)) {
                    excelExportDTO.setPriority("高");
                }
            }

            //流程设置
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(excelExportDTO.getId());
            if (ObjectUtil.isNotEmpty(flowTemplateBusinessVO)) {
                excelExportDTO.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                excelExportDTO.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                excelExportDTO.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                excelExportDTO.setStatusName(processStatusMap.getOrDefault(flowTemplateBusinessVO.getProcessStatus(), ""));
                UserBaseCacheVO userVO = userVOMap.get(flowTemplateBusinessVO.getApplyUserId());
                excelExportDTO.setFlowCreatePersonNumber(userVO == null ? "" : userVO.getCode());
            }
            //报价执行人
            UserBaseCacheVO userVO = userVOMap.get(excelExportDTO.getIssuer());
            excelExportDTO.setIssuerName(userVO == null ? "" : "[" + userVO.getCode() + "]" + userVO.getName());
            //报价接收方
            excelExportDTO.setQuoteAcceptComName(quoteMap.getOrDefault(excelExportDTO.getQuoteAcceptCom(), ""));
            QuotationManagementVO quotationManagement = quotationManagementMap.get(excelExportDTO.getId());
            //报价结果
            if (ObjectUtil.isNotEmpty(quotationManagement)) {
                DataStatusVO dataStatus = quotationManagement.getDataStatus();
                if (ObjectUtil.isNotEmpty(dataStatus)) {

                    String result1 = quotationManagement.getDataStatus().getName();
                    if (ObjectUtil.isNotEmpty(result1)) {
                        if (!StrUtil.equals(result1, "已中标") && !StrUtil.equals(result1, "未中标")) {
                            result1 = "-";
                        }
                        excelExportDTO.setResult(result1);
                    }
                }
            } else {
                logger.info("excelExportDTO:{}", excelExportDTO.getId());
                continue;
            }

            //所级负责人
            String officeLeader = quotationManagement.getOfficeLeader();
            if (ObjectUtil.isNotEmpty(officeLeader)) {
                if (ObjectUtil.isNotEmpty(userBaseCacheVOMap.get(officeLeader))) {
                    String officeLeaderName = userBaseCacheVOMap.get(officeLeader).getName();
                    excelExportDTO.setOfficeLeaderName("[" + userBaseCacheVOMap.get(officeLeader).getCode() + "]" + officeLeaderName);
                }
            }
            RequirementMangementVO result = new RequirementMangementVO();
            //获取客户名称
            CustomerInfo customerInfo = customerInfoMap.get(excelExportDTO.getCustPerson());
            if (customerInfo != null) {
                String salesClass = customerInfo.getSalesClass();
                if (ObjectUtil.isNotEmpty(salesClass)) {
                    //经营管理需求来源
                    Map<String, String> saleClassMap = new HashMap<>();
                    for (CustSaleBusTypeEnum lt : CustSaleBusTypeEnum.values()) {
                        saleClassMap.put(lt.key, lt.desc);
                    }
                    excelExportDTO.setSalesClassification(saleClassMap.getOrDefault(salesClass, ""));
                }
                result.setCustPersonName(customerInfo.getCusName());
                result.setYwsrlx(customerInfo.getYwsrlx());
                result.setYwsrlxName(IncomeTypeEnum.getDesc(customerInfo.getYwsrlx()));
            }

            BeanCopyUtils.copyProperties(result, quotationManagement);
            //客户联系人
            String custConPerson = excelExportDTO.getCustConPerson();
            if (ObjectUtil.isNotEmpty(custConPerson)) {
                excelExportDTO.setCustConPersonName(custConPerson);
                List<UserDO> custConPersonList = userBaseCacheVOMap.values().stream()
                        .filter(user -> {
                            // 检查 user 和 getName() 是否为 null，避免 NullPointerException
                            return user != null && custConPerson.equals(user.getName());
                        })
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(custConPersonList)) {
                    excelExportDTO.setCustContactPh(custConPersonList.get(0).getMobile());
                }
            }

            //客户名称
            excelExportDTO.setCustPersonName(excelExportDTO.getCustPersonName());
            //业务类型
            String requireBusinessType = excelExportDTO.getRequireBusinessType();
            if (ObjectUtil.isNotEmpty(requireBusinessType)) {
                excelExportDTO.setBusinessTypeName(businessTypeMap.getOrDefault(requireBusinessType, ""));
            }

            //合同获取方式
            String resSource = excelExportDTO.getResSource();
            if (ObjectUtil.isNotEmpty(resSource)) {
                String resSourceName = resourceMap.get(resSource);
                excelExportDTO.setResSource(resSourceName);
            }
        }
    }

}
