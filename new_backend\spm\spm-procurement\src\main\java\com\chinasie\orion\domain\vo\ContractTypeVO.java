package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractType VO对象
 *
 * <AUTHOR>
 * @since 2025-01-10 03:27:16
 */
@ApiModel(value = "ContractTypeVO对象", description = "采购合同标的类别")
@Data
public class ContractTypeVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 标的类别
         */
        @ApiModelProperty(value = "标的类别")
        private String objectType;


        /**
         * 类的占比
         */
        @ApiModelProperty(value = "类的占比")
        private BigDecimal typePercent;


    

}
