// ************************************************ //
// ******************项目管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 获取项目管理分页列表
 * @param params
 */
export async function postPages(params: any) {
  return new Api('/pms/project/pages').fetch(params, '', 'POST');
}

/**
 * 获取项目责任单位
 */
export async function getOrganizationList() {
  return new Api('/pmi/business-organization/list').fetch('', '', 'get');
}

/**
 * 获取项目责任部门
 * @param params
 */
export async function postBusinessOrgList(params) {
  return new Api('/pmi/organization/business/org/list').fetch(params, '', 'post');
}

/**
 * 获取项目负责人
 * @param params
 */
export async function postUserOrgList(params) {
  return new Api('/pmi/user/org/ids').fetch(params, '', 'post');
}

/**
 * 创建项目
 * @param params
 */
export async function postNewProject(params) {
  return new Api('/pms/project').fetch(params, '', params?.id ? 'put' : 'post');
}

/**
 * 删除项目
 * @param params
 */
export async function deleteNewProject(params: string[]) {
  return new Api('/pms/project').fetch(params, '', 'delete');
}

/**
 * 获取项目详情
 * @param id
 * @param params
 */
export async function getNewProjectById(id: string, params:{
  pageCode:string
}) {
  return new Api('/pms/project').fetch(params, id, 'get');
}

/**
 * 获取项目角色列表
 * @param params
 */
export async function postProjectRoleList(params) {
  return new Api('/pms/project-role/list').fetch(params, '', 'post');
}

/**
 * 获取项目角色用户列表
 * @param params
 */
export async function postProjectRoleUserList(params) {
  return new Api('/pms/project-role-user/pages').fetch(params, '', 'post');
}

/**
 * 批量新增角色用户
 * @param params
 */
export async function postRoleUserSaveBatch(params) {
  return new Api('/pms/project-role-user/save/batch').fetch(params, '', 'post');
}

/**
 * 获取综合计划列表
 * @param params
 */
export async function postSchemePages(params) {
  return new Api('/plan/scheme/pages').fetch(params, '', 'post');
}

/**
 * 获取计划来源列表
 */
export async function postSourceSearch() {
  return new Api('/plan/source/search').fetch({}, '', 'post');
}

/**
 * 获取计划来源列表
 * @param params
 */
export async function postPlanSearch(params:{
    ids:string[]
}) {
  return new Api('/plan/scheme/search').fetch(params, '', 'post');
}

/**
 * 获取物料管理分页列表
 * @param params
 */
export async function postSupManagePages(params) {
  return new Api('/pms/project-supplies-management/pages').fetch(params, '', 'post');
}

/**
 * 刷新物料列表
 * @param params
 */
export async function putSupManageRefresh(params: {
    projectId: string,
    projectNumber: string
}) {
  return new Api('/pms/project-supplies-management/refresh').fetch(params, '', 'put');
}

/**
 * 移除角色用户
 * @param params
 */
export async function deleteRoleUser(params) {
  return new Api('/pms/project-role-user').fetch(params, '', 'delete');
}

/**
 * 用户收藏项目
 * @param params
 */
export async function postUserLike(params: { projectId: string }) {
  return new Api('/pms/user-like-project').fetch(params, '', 'post');
}

/**
 * 用户取消收藏项目
 * @param params
 */
export async function deleteUserLike(params: Array<string>) {
  return new Api('/pms/user-like-project').fetch(params, '', 'delete');
}

/**
 * 获取物资阶段状态列表
 */
export async function getPhaseList() {
  return new Api('/pms/project-supplies-management/phase/list').fetch('', '', 'get');
}

/**
 * 获取表头和预算编码总览
 * @param params
 */
export async function postOverviewList(params: {
    projectId: string,
    projectNumber: string
}) {
  return new Api('/pms/budget-info/overview/list').fetch(params, '', 'post');
}

/**
 * 获取预算编码执行情况
 * @param params
 */
export async function postConditionList(params: {
    projectId: string,
    projectNumber: string
}) {
  return new Api('/pms/budget-info/annual/executive/condition/list').fetch(params, '', 'post');
}

/**
 * 预算管理刷新
 * @param params
 */
export async function putBudgetInfoRefresh(params: {
    projectId: string,
    projectNumber: string
}) {
  return new Api('/pms/budget-info/refresh').fetch(params, '', 'put');
}

/**
 * 预算管理刷新
 */
export async function getBudgeList() {
  return new Api('/pms/project/budge/list').fetch('', '', 'get');
}

/**
 * 项目合同分页列表
 * @param params
 */
export async function postProContPages(params:{
    projectId: string,
    projectNumber: string
}) {
  return new Api('/pas/project-contract/pages').fetch(params, '', 'post');
}

/**
 * 获取简化的订单节点列表
 * @param params
 */
// export async function getPurOrderList(contractNumber:string) {
//   return new Api(`/pms/purchase-order/${contractNumber}/list`).fetch('', '', 'get');
// }
export async function getPurOrderList(params) {
  return new Api('/pms/projectPurchaseOrderInfo/getPurchaseOrderList').fetch(params, '', 'POST');
}

/**
 * 获取订单下的订单节点信息列表
 * @param params
 */
export async function postOrderNodeList(params:{
    orderNumber:string
}) {
  return new Api('/pms/order-node/list').fetch(params, '', 'post');
}

/**
 * 获取合同详情信息（详细）
 * @param contractNumber
 */
export async function getProContract(contractNumber:string) {
  return new Api(`/pms/project-contract/number/${contractNumber}`).fetch('', '', 'get');
}

/**
 * 获取合同详情信息(简化)
 * @param contractNumber
 * @param projectNumber
 */
export async function getProContractSimple(contractNumber:string, projectNumber:string) {
  return new Api(`/pms/project-contract/simple/detail/${contractNumber}`).fetch({
    projectNumber,
    pageCode: 'PMS90005',
  }, '', 'get');
}

/**
 * 获取项目计划分页列表
 * @param params
 */
export async function postProjectPlanPages(params) {
  return new Api('/pms/projectScheme/page').fetch(params, '', 'post');
}

/**
 * 获取采购订单详情
 * @param id
 */
export async function getPurchaseOrder(id) {
  return new Api(`/pms/projectPurchaseOrderInfo/${id}`).fetch('', '', 'get');
}

/**
 * 获取订单确认记录分页
 * @param params
 */
export async function postNotarizePages(params) {
  return new Api('/pms/order-node-notarize/pages').fetch(params, '', 'post');
}

/**
 * 订单节点确认新增
 * @param params
 */
export async function postNotarizeAddBatch(params) {
  return new Api('/pms/order-node-notarize/add/batch').fetch(params, '', 'post');
}

/**
 * 订单节点确认编辑
 * @param params
 */
export async function postNotarizeEditBatch(params) {
  return new Api('/pms/order-node-notarize/edit/batch').fetch(params, '', 'post');
}

/**
 * 订单节点确认重新提交
 * @param params
 */
export async function putAgainCommit(params) {
  return new Api('/pms/order-node-notarize/again/commit').fetch(params, '', 'put');
}

/**
 * 获取计划层级
 */
export async function getPlanSchemeLevel() {
  return new Api('/plan/dict/code/plan_scheme_level').fetch('', '', 'get');
}

/**
 * 获取框架协议框架合同信息
 * @param contractNumber
 */
export async function getAgreementById(contractNumber:string) {
  return new Api(`/pms/frame-agreement-contract/${contractNumber}`).fetch('', '', 'get');
}

/**
 * 获取框架协议、框架合同分页列表
 * @param params
 */
export async function postFramePages(params) {
  return new Api('/pms/frame-agreement-contract/pages').fetch(params, '', 'post');
}

/**
 * 获取审核历史分页列表
 * @param params
 */
export async function postHistoryPages(params) {
  return new Api('/pms/order-node-notarize-history/pages').fetch(params, '', 'post');
}

/**
 * 获取项目信息
 * @param id
 */
export async function getProjectDetail(id:string) {
  return new Api(`/pms/project/detail/${id}`).fetch('', '', 'get');
}

/**
 * 获取申报相关的文件列表
 * @param id
 */
export async function getDeclarationFileList(id:string) {
  return new Api(`/pms/project/declaration/file/list/${id}`).fetch('', '', 'get');
}

/**
 * 获取验收列表
 * @param
 *
 */

export async function getAcceptanceList(params) {
  return new Api('/pms/acceptance-form/page').fetch(params, '', 'post');
}

/**
 * 获取验收单详情
 * @param id
 * @param params
 */
export async function getAcceptanceForm(id: string, params:{
  pageCode:string
}) {
  return new Api('/pms/acceptance-form').fetch(params, id, 'get');
}

/**
 * 验收单创建- 项目验收单
 * @param params
 */
export async function postAcceptance(params: {
  projectId: string,
  type: string,
  itemIds?:Array<string>
}) {
  return new Api('/pms/acceptance-form').fetch(params, '', 'post');
}

/**
 * 验收单状态更改
 * @param id
 */
export async function putAcceptanceStatus(id) {
  return new Api(`/pms/acceptance-form/${id}/status?status=COMPLETE`).fetch('', '', 'put');
}

/**
 * 验收计划列表
 * @param from
 */
export async function getProjectSchemePage(params) {
  return new Api('/pms/projectScheme/page').fetch(params, '', 'get');
}

/**
 * 项目计划 - 执行完成
 * @param params
 */
export async function putProjectSchemeFinish(params: {
  from: string
}) {
  return new Api('/pms/projectScheme/finish').fetch(params, '', 'put');
}

/**
 * 验收文件列表
 * @param id
 * @param params
 */
export async function getAcceptanceFormFiles(params, id) {
  return new Api(`/pms/acceptance-form/${id}/files/page`).fetch(params, '', 'post');
}

/**
 * 文件删除状态变更
 * @param  params
 */
export async function deleteAcceptanceFormFiles(params: {
  id: string,
  filedsIds: string[],
}) {
  return new Api(`/pms/acceptance-form/${params.id}/files`).fetch(params.filedsIds, '', 'delete');
}

/**
 * 保存已上传文件
 * @param  params
 */
export async function postAcceptanceFormFiles(params: {
  id: string,
  fileData:Object //              name: "文件名",filePath: "文件路径filePostfix: "文件扩展名", fileSize: 111, // 文件大小, 字节为单位
}) {
  return new Api(`/pms/acceptance-form/${params.id}/files`).fetch(params.fileData, '', 'post');
}

/**
 * 验收相关项
 * @param  id
 */
export async function getProcurementPlanApproval(params, id) {
  return new Api(`/pms/acceptance-form/${id}/procurement-plan-approval`).fetch(params, '', 'post');
}

/**
 * 项目完工
 * @param  id
 */
export async function putProjectFinish(id) {
  return new Api(`/pms/project/${id}/finish`).fetch('', '', 'put');
}

/**
 * 项目完工状态监测
 * @param  params
 */
export async function getIncomplete(params) {
  return new Api('/pms/project-supplies-management/status-check/incomplete').fetch(params, '', 'get');
}

// ************************************************ //
// ******************项目生命周期相关***************** //
// ************************************************ //

/**
 * 获取项目生命周期所有节点信息
 * @param  projectId
 */
export async function getProjectNodes(projectId) {
  return new Api(`/pms/project-nodes/${projectId}`).fetch('', '', 'get');
}

/**
 * 修改节点信息
 * @param  params
 */
export async function putProjectNodes(projectId, params) {
  return new Api(`/pms/project-nodes/${projectId}/${params.nodeKey}`).fetch(params, '', 'put');
}

// ************************************************ //
// ******************项目概况***************** //
// ************************************************ //
/**
 * 项目基本信息
 * @param  projectId
 */
export async function getprojectStats(projectId) {
  return new Api(`/pms/project-stats/${projectId}/base`).fetch('', '', 'get');
}

/**
//  * 采购计划行阶段统计
//  * @param
//  */

/// 采购计划行阶段统计
// {
//     dataSourceId: "dataSourceId", // 数据源Id
//     queries: [
//         {
//             query: "SQL"
//         }
//     ],
//     filters: {
//         projectId: ["项目Id"]
//     }
// }

/// 临期提醒列表

// {
//     dataSourceId: "dataSourceId",
//     format: "table",
//     queries: [
//         {
//             query: "列表SQL，返回消息队列信息"
//         }
//     ],
//     cache: { // 缓存机制需要数据，与具体报表chart相关
//         type: "PROJECT_REMINDER",
//         dataId: "${projectId}"
//     },
//     filters: {
//         projectId: ["项目Id"]
//     }
// }

/// 项目相关文档列表

// {
//     dataSourceId: "dataSourceId",
//     format: "table", // 增加format = table类型
//     queries: [
//         {
//             query: "SQL"
//         }
//     ],
//     cache: { // 缓存机制需要数据，与具体报表chart相关
//         type: "PROJECT_DOCS",
//         dataId: "${projectId}"
//     },
//     filters: {
//         projectId: ["项目Id"]
//     }
// }
// 项目预算总览 - 全部

// {
//     dataSourceId: "dataSourceId",
//     queries: [
//         {
//             query: "年度预算SQL"
//         },
//         {
//             query: "预算承诺SQL"
//         },
//         {
//             query: "实际执行SQL"
//         },
//         {
//             query: "预算占比SQL"
//         }
//     ],
//     cache: {
//         type: "PROJECT_BUDGET_ALL",
//         dataId: "${projectId}"
//     },
//     filters: {
//         projectId: ["项目Id"]
//     }

// }

// 项目预算总览 - 明细

// {
//     dataSourceId: "dataSourceId",
//     queries: [
//         {
//             query: "年度预算SQL"
//         },
//         {
//             query: "预算承诺SQL"
//         },
//         {
//             query: "实际执行SQL"
//         },
//     ],
//     cache: { // 缓存机制用到的核数据
//         type: "PROJECT_BUDGET_DETAIL",
//         dataId: "${projectId}"
//     },
//     filters: {
//         projectId: ["项目Id"],
//         wbsCode: ["All"] // wbs编码
//     }

// }

// 项目预算总览 - 明细筛选条件获取

// {
//     dataSourceId: "dataSourceId",
//     format: "table",
//     queries: [
//         {
//             query: "SQL"
//         }
//     ],
//     cache: { // 缓存机制用到的核数据
//         type: "PROJECT_BUDGET_WBS_CODES",
//         dataId: "${projectId}"
//     },
//     filters: {
//         projectId: ["项目Id"],
//     }

// }

export async function getQueryCustom() {
  return new Api('/report/data/query/custom').fetch('', '', 'get');
}

/**
//  * 采购行状态
//  * @param
//  */
export async function getprojectStatsList(params, projectId) {
  return new Api(`/pms/project-stats/${projectId}/psm/list`).fetch(params, '', 'get');
}

/**
//  * 采购计划行阶段统计
//  * @param
//  */
export async function getQueryCustomData() {
  return new Api('/report/data/query/custom').fetch('', '', 'post');
}

/**
 * 完工确认
 * @param  params
 */
export async function getCompleteNotarize(params) {
  return new Api('/pms/project-supplies-management/complete/notarize').fetch(params, '', 'post');
}

/**
 * 查看采购立项详情
 * @param  id
 * @param params
 */
export async function getSuppliesManagement(id, params) {
  return new Api(`/pms/project-supplies-management/${id}`).fetch(params, '', 'get');
}

/**
 * 获取完工确认详情
 * @param  params
 */
export async function getCompleteNotarizeDetail(params) {
  return new Api('/pms/project-supplies-management/detail/complete/notarize').fetch(params, '', 'get');
}

/**
 * 获取wbs元素清单
 * @param  params
 */
export async function postWbsList(params) {
  return new Api('/icm/icm/wbs/list').fetch(params, '', 'post');
}

/**
 * 获取wbs元素详情
 * @param  params
 */
export async function postWbsDetail(params) {
  return new Api('/icm/icm/wbs/detal').fetch(params, '', 'post');
}

/**
 * 获取完工确认对应的文件列表
 * @param  params
 */
export async function getCompleteFileList(params) {
  return new Api('/pms/completeNotarize/file/list').fetch(params, '', 'get');
}

/**
 * 删除单个文件
 * @param  fileId
 */
export async function deleteFileById(fileId) {
  return new Api(`/res/manage/file/${fileId}`).fetch('', '', 'delete');
}

/**
 * 批量新增文件
 * @param  params
 */
export async function postFileBatch(params) {
  return new Api('/res/manage/file/batch').fetch(params, '', 'post');
}

/**
 * 获取合同关联信息
 * @param  params
 */
export async function postContractRelevancyInfo(params) {
  return new Api('/pms/contractRelevancyInfo/pages').fetch(params, '', 'post');
}

/**
 * 获取订单节点对应的确认记录
 * @param  params
 */
export async function getNotarizeSingle(params:{
  nodeId: string,
  orderNumber: string,
  serialNumber:string
}) {
  return new Api('/pms/order-node-notarize/notarize/single').fetch(params, '', 'post');
}

/**
 * 获取采购订单信息分页列表
 * @param  params
 */
export async function postOrderListInfoPages(params:{
 query:{
   orderNumber:string
 }
}) {
  return new Api('/pms/purchase-order-list-info/pages').fetch(params, '', 'post');
}

/**
 * 获取订单确认详情（查看）
 * @param  id
 */
export async function getOrderNodeDetail(id) {
  return new Api(`/pms/order-node-notarize/detail/${id}`).fetch('', '', 'get');
}

/**
 * 获取订单确认详情（编辑、重新提交）
 * @param  id
 */
export async function getOrderNodeNotarize(id) {
  return new Api(`/pms/order-node-notarize/${id}`).fetch('', '', 'get');
}

/**
 * V1-获取某个文档下所有的附件（附件所有的版本）
 * @param  dataId
 */
export async function getFileAllByDataId(dataId) {
  return new Api(`/res/manage/file/all/${dataId}`).fetch('', '', 'get');
}

/**
 * 批量删除节点确认记录
 * @param  ids
 */
export async function deleteNodeNotarize(ids) {
  return new Api('/pms/order-node-notarize').fetch(ids, '', 'delete');
}

/**
 * 审核通过
 * @param  params
 */
export async function putAuditOk(params) {
  return new Api('/pms/order-node-notarize/audit/ok').fetch(params, '', 'put');
}

/**
 * 驳回
 * @param  params
 */
export async function putReject(params) {
  return new Api('/pms/order-node-notarize/reject').fetch(params, '', 'put');
}

/**
 * 获取申报详情
 * @param  projectId
 */
export async function getDeclaration(projectId) {
  return new Api(`/pms/declaration/${projectId}`).fetch('', '', 'get');
}

/**
 * 项目转固详情
 * @param  projectId
 */
export async function getCapitalizeDetail(projectId) {
  return new Api(`/pms/assetTransfer/capitalize/${projectId}`).fetch({
    pageCode: 'PMS90006',
  }, '', 'get');
}

/**
 * 项目转资详情
 * @param  projectId
 */
export async function getAssetsDetail(projectId) {
  return new Api(`/pms/assetTransfer/assets/${projectId}`).fetch({
    pageCode: 'PMS90006',
  }, '', 'get');
}

/**
 * 项目转固完成
 * @param  params
 */
export async function putCompleteSubmit(params) {
  return new Api('/pms/assetTransfer/capitalize/completeSubmit').fetch(params, '', 'put');
}

/**
 * 项目评价
 * @param projectId
 * @returns
 */
// 关闭项目
export async function putCommentsClose(projectId) {
  return new Api(`/pms/comments/close/${projectId}`).fetch('', '', 'put');
}

// 项目后评价
export async function postCommentsPost(projectId) {
  return new Api(`/pms/comments/post/${projectId}`).fetch('', '', 'post');
}

// 是否可以触发项目评价
export async function getCommentsPost(projectId) {
  return new Api(`/pms/comments/post/${projectId}`).fetch('', '', 'get');
}

// 上传项目文件
export async function postReportUpload(params) {
  return new Api('/pms/comments/report/upload').fetch(params, '', 'post');
}

/**
 * 获取资产转固
 * @param  projectId
 */
export async function getAssetTransfer(projectId) {
  return new Api(`/pms/assetTransfer/${projectId}`).fetch('', '', 'get');
}

/**
 * 获取合同列表
 * @param  params
 */
export async function postContractList(params) {
  return new Api('/pms/project-contract/list').fetch(params, '', 'post');
}

/**
 * 批量新增关联合同
 * @param  params
 */
export async function postContractBatchAdd(params) {
  return new Api('/pms/projectSchemeContract/batchAdd').fetch(params, '', 'post');
}

/**
 * 批量移除关联合同
 * @param  params
 */
export async function deleteSchemeContract(params) {
  return new Api('/pms/projectSchemeContract').fetch(params, '', 'delete');
}

/**
 * 完工确认保存文件
 * @param  params
 */
export async function postCompleteSaveFiles(params) {
  return new Api('/pms/completeNotarize/save/files').fetch(params, '', 'post');
}

/**
 * 获取集成地址
 */
export async function getUrlList() {
  return new Api('/icm/declaration/url/list').fetch('', '', 'get');
}

/**
 * 创建申报
 */
export async function postDeclarationCreate(params:{
  projectId:string,
  projectNumber:string
}) {
  return new Api(`/pms/declaration/create/${params.projectId}`).fetch(params, '', 'post');
}

/**
 * 采购计划管理分页列表
 * @param params
 */
export async function postSupManagePlanPages(params) {
  return new Api('/pms/project-supplies-management/plan/pages').fetch(params, '', 'post');
}

/**
 * 获取用户概要信息
 * @param  userId
 */
export async function getUserProfile(userId) {
  return new Api(`/pmi/user/user-profile/${userId}`).fetch('', '', 'get');
}

/**
 * 合同管理刷新数据
 * @param  params
 */
export async function putContractRefresh(params:{
  projectId: string,
  projectNumber: string
}) {
  return new Api('/pms/project-contract/refresh').fetch(params, '', 'put');
}

/**
 * 项目计划里程碑模版--编辑
 * @param  params
 */
export async function putProjectSchemeMilestoneTemplate(params) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'put');
}

/**
 * 项目计划里程碑模版--新增
 * @param  params
 */
export async function postProjectSchemeMilestoneTemplate(params) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'post');
}

/**
 * 项目计划里程碑模版--删除（批量）
 * @param  params
 */
export async function deleteProjectSchemeMilestoneTemplate(params) {
  return new Api('/pms/projectSchemeMilestoneTemplate').fetch(params, '', 'delete');
}

/**
 * 项目计划里程碑模版--详情
 * @param  params
 */
export async function getProjectSchemeMilestoneTemplate(id) {
  return new Api(`/pms/projectSchemeMilestoneTemplate/${id}`).fetch('', '', 'get');
}

/**
 * 项目计划里程碑模版--分页
 * @param  params
 */
export async function postProjectSchemeMilestoneTemplatelist() {
  return new Api('/pms/projectSchemeMilestoneTemplate/list').fetch('', '', 'post');
}

/**
 * 项目计划里程碑节点--编辑
 * @param  params
 */
export async function putProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'put');
}

/**
 * 项目计划里程碑节点--新增
 * @param  params
 */
export async function postProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'post');
}
/**
 * 项目计划里程碑节点--删除
 * @param  params
 */
export async function deleteProjectSchemeMilestoneNode(params) {
  return new Api('/pms/projectSchemeMilestoneNode').fetch(params, '', 'delete');
}

/**
 * 项目计划里程碑节点--分页
 * @param  params
 */
export async function postProjectSchemeMilestoneNodePages(params) {
  return new Api('/pms/projectSchemeMilestoneNode/pages').fetch(params, '', 'post');
}

/**
 * 项目转固完成确认
 * @param  projectId
 */
export async function putCapCompleteSubmit(projectId) {
  return new Api(`/pms/assetTransfer/capitalize/isTransferAssets?projectId=${projectId}`).fetch('', '', 'post');
}

/**
 * 项目转资完成确认
 * @param  projectId
 */
export async function putAssCompleteSubmit(projectId) {
  return new Api(`/pms/assetTransfer/assets/isTransferCapital?projectId=${projectId}`).fetch('', '', 'post');
}

/**
 * 自动创建投资计划
 * @param  projectId
 * @param pageCode
 */
export async function getInvestmentSchemeCreate(projectId, pageCode) {
  return new Api(`/pms/investmentScheme/create/${projectId}`).fetch({ pageCode }, '', 'get');
}

/**
 * 项目计划列表（置顶）
 * @param  params
 */
export async function postProjectSchemeList(params:{
  projectId:string,
  typeEnum?:string,
  power:{
    pageCode:string,
    containerCode:string
  }
  searchConditions: any,
}) {
  return new Api('/pms/projectScheme/list').fetch(params, '', 'post');
}
/**
 * 我执行的项目计划列表
 */
export async function myPostProjectSchemeList(params:object) {
  return new Api('/pms/projectScheme/userPages').fetch(params, '1', 'post');
}

/**
 * 我创建的项目计划列表
 */
export async function myCreatePostProjectSchemeList(params:object) {
  return new Api('/pms/projectScheme/userPages').fetch(params, '2', 'post');
}

/**
 * 获取当前用户所在项目的角色信息
 * @param  projectId
 */
export async function getProjectRoleUser(projectId:string) {
  return new Api(`/pms/project-role-user/current/role/info/list/${projectId}`).fetch('', '', 'get');
}

/**
 * 获取当前用户所在项目的角色信息-通过项目编号
 * @param  projectNumber
 */
export async function getProjectRoleUserByNumber(projectNumber:string) {
  return new Api(`/pms/project-role-user/current/role/info/number/${projectNumber}`).fetch('', '', 'get');
}

/**
 * 项目来源批量新增综合计划
 * @param  projectId
 * @param  params
 */
export async function postProjectSourceBatch(projectId:string, params) {
  return new Api(`/pms/project/project/source/batch/${projectId}`).fetch(params, '', 'post');
}

/**
 * 项目来源批量删除综合计划
 * @param  projectId
 * @param  params
 */
export async function delProjectSourceBatch(projectId:string, params) {
  return new Api(`/pms/project/project/source/delete/batch/${projectId}`).fetch(params, '', 'delete');
}

/**
 * 获取当前项目成员用户列表
 * @param  projectId
 */
export async function getProjectUserList(projectId:string) {
  return new Api(`/pms/project-role-user/${projectId}/user/list`).fetch('', '', 'GET');
}

/**
 * 根据项目ID和用户编号获取用户信息
 * @param  projectId
 * @param userCode
 */
export async function getUserInfoByCode(projectId:string, userCode:string) {
  return new Api(`/pms/project-role-user/${projectId}/userInfo/${userCode}`).fetch('', '', 'GET');
}

/**
 * 根据合同ID获取合同详情
 * @param  id
 */
export async function getProjectContract(id:string) {
  return new Api(`/pas/projectContract/main/${id}`).fetch('', '', 'GET');
}

/**
 * 根据合同ID获取列表
 * @param  id
 */
export async function getContractPayNode(id:string) {
  return new Api(`/pas/contractPayNode/list/contractId/${id}`).fetch('', '', 'POST');
}

/**
 * 根据合同节点ID获取详情
 * @param  id
 */
export async function getContractPayNodeById(id:string) {
  return new Api(`/pas/contractPayNode/${id}`).fetch('', '', 'GET');
}

/**
 * 保存合同支付节点
 * @param params
 */
export async function postContractPayNodeConfirm(params) {
  return new Api('/pas/contractPayNodeConfirm').fetch(params, '', 'post');
}

/**
 * 编辑合同支付节点
 * @param params
 */
export async function putContractPayNodeConfirm(params) {
  return new Api('/pas/contractPayNodeConfirm').fetch(params, '', 'put');
}

/**
 * 提交合同支付节点
 * @param params
 */
export async function postContractPayNodeConfirmSubmit(params) {
  return new Api('/pas/contractPayNodeConfirm/submit').fetch(params, '', 'post');
}
/**
 * 获取节点确认记录
 * @param params
 */
export async function postContractPayNodeConfirmPage(params) {
  return new Api('/pas/contractPayNodeConfirm/page').fetch(params, '', 'post');
}

/**
 * 删除节点确认审核记录
 * @param params
 */
export async function deleteContractPayNodeConfirmAuditRecord(params) {
  return new Api('/pas/contractPayNodeConfirmAuditRecord').fetch(params, '', 'delete');
}

/**
 * 删除节点确认审核记录
 * @param params
 */
export async function deleteContractPayNodeConfirm(params) {
  return new Api('/pas/contractPayNodeConfirm').fetch(params, '', 'delete');
}

/**
 * 支付状态确认
 * @param params
 */
export async function postContractPayNodeStatusConfirm(params) {
  return new Api('/pas/contractPayNode/status/confirm').fetch(params, '', 'post');
}

/**
 * 项目合同支付节点确认的驳回
 * @param params
 */
export async function postContractPayNodeConfirmReject(params) {
  return new Api('/pas/contractPayNodeConfirm/reject').fetch(params, '', 'post');
}

/**
 * 项目合同支付节点确认的同意
 * @param params
 */
export async function postContractPayNodeConfirmAgree(params) {
  return new Api('/pas/contractPayNodeConfirm/agree').fetch(params, '', 'post');
}

/**
 * 获取项目合同支付节点详情
 * @param params
 */
export async function getContractPayNodeConfirm(params) {
  return new Api('/pas/contractPayNodeConfirm/').fetch(params, '', 'get');
}
