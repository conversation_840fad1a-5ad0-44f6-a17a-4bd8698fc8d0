<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <!--
  /**
   * Licensed to the Apache Software Foundation (ASF) under one
   * or more contributor license agreements.  See the NOTICE file
   * distributed with this work for additional information
   * regarding copyright ownership.  The ASF licenses this file
   * to you under the Apache License, Version 2.0 (the
   * "License"); you may not use this file except in compliance
   * with the License.  You may obtain a copy of the License at
   *
   *     http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
  -->
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.zookeeper</groupId>
    <artifactId>parent</artifactId>
    <version>3.6.2</version>
    <relativePath>..</relativePath>
  </parent>

  <artifactId>zookeeper-jute</artifactId>
  <packaging>jar</packaging>
  <name>Apache ZooKeeper - Jute</name>
  <description>ZooKeeper jute</description>

  <dependencies>
    <dependency>
      <groupId>org.apache.yetus</groupId>
      <artifactId>audience-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javacc-maven-plugin</artifactId>
        <version>2.6</version>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <id>javacc</id>
            <goals>
              <goal>javacc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/main/java/org/apache/jute/compiler/generated/</sourceDirectory>
              <includes>
                <include>rcc.jj</include>
              </includes>
              <lookAhead>2</lookAhead>
              <isStatic>false</isStatic>
              <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>pre-compile-jute</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-Java-Jute</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <workingDirectory>${project.build.directory}/generated-sources/java</workingDirectory>
              <executable>java</executable>
              <arguments>
                <argument>-classpath</argument>
                <classpath />
                <argument>org.apache.jute.compiler.generated.Rcc</argument>
                <argument>-l</argument>
                <argument>java</argument>
                <argument>${project.basedir}/src/main/resources/zookeeper.jute</argument>
              </arguments>
            </configuration>
          </execution>
          <execution>
            <id>generate-C-Jute</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <workingDirectory>${project.basedir}/../zookeeper-client/zookeeper-client-c/generated/</workingDirectory>
              <executable>java</executable>
              <arguments>
                <argument>-classpath</argument>
                <classpath />
                <argument>org.apache.jute.compiler.generated.Rcc</argument>
                <argument>-l</argument>
                <argument>c</argument>
                <argument>${project.basedir}/src/main/resources/zookeeper.jute</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>jute-as-dependency</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${basedir}/target/generated-sources/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <!-- spotbugs does not make sense for generated code -->
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <configuration>
            <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
