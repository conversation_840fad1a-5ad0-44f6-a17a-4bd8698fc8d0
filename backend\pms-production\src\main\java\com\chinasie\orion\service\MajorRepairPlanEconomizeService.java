package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.MajorRepairPlanEconomizeDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanEconomize;
import com.chinasie.orion.domain.vo.MajorRepairPlanEconomizeVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/14:55
 * @description:
 */
public interface MajorRepairPlanEconomizeService  extends OrionBaseService<MajorRepairPlanEconomize> {


    /**
     *  详情
     *
     * * @param id
     */
    MajorRepairPlanEconomizeVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param majorRepairPlanEconomizeDTO
     */
    String create(MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param majorRepairPlanEconomizeDTll
     */
    Boolean edit(MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MajorRepairPlanEconomizeVO> pages(Page<MajorRepairPlanEconomizeDTO> pageRequest)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<MajorRepairPlanEconomizeVO> vos)throws Exception;

    /**
     *  通过大修轮次 获取已经加入过的 作业工单号列表
     * @param repairRound
     * @return
     */
    List<String> listByRepairRound(String repairRound);

    void export(MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception;
}

