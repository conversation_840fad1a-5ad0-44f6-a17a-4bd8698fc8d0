<template>
  <BasicDrawer
    v-model:visible="$props.visible"
    title="计划调整申请审批"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
    @close="() => handleClose()"
  >
    <div class="drawer-content">
      <p class="title">
        申请内容
      </p>
      <div
        v-for="(val, index) in planDetailRows"
        :key="index"
        class="row"
        :gutter="16"
      >
        <div
          v-for="item in val"
          :key="item.value"
          :span="item.span"
          class="col"
        >
          <div class="label">
            {{ item.label }}：
          </div>
          <div class="value">
            {{ detailData[item.value] }}
          </div>
        </div>
      </div>
      <a-form
        ref="formRef"
        :model="form"
        layout="vertical"
      >
        <a-form-item
          name="feedBack"
          required
          :rules="[{required:true,message:'请输入审批意见',trigger:['blur','change']}]"
        >
          <template #label>
            <div class="title">
              审批意见:
            </div>
          </template>
          <a-textarea
            v-model:value="form.feedBack"
            placeholder="请输入审批意见"
          />
        </a-form-item>
        <a-form-item
          v-if="tableSource.length"
          name="needChange"
          required
          :rule="recordRule"
        >
          <template #label>
            <div class="title">
              该计划为前置关联计划，是否需要调整关联关系
            </div>
          </template>
          <a-radio-group
            v-model:value="form.needChange"
          >
            <a-radio :value="true">
              需要
            </a-radio>
            <a-radio :value="false">
              不需要
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
        style="margin-top: -10px"
      >
        <template #name="{ record }">
          <div
            class="action-btn flex-te"
            :title="record.preSchemeName"
            @click="handleToDetail(record)"
          >
            {{ record.preSchemeName }}
          </div>
        </template>
      </OrionTable>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleReject"
        >
          驳回
        </BasicButton>
        <BasicButton
          type="primary"
          @click="handleSubmit"
        >
          同意
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, onMounted,
} from 'vue';
import {
  BasicButton,
  OrionTable,
  BasicDrawer,
  useDrawerInner,
} from 'lyra-component-vue3';
import {
  Form,
  FormItem,
  Textarea,
  RadioGroup,
  Radio,
  message,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { throttle } from 'lodash-es';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';

export default defineComponent({
  components: {
    AForm: Form,
    AFormItem: FormItem,
    ATextarea: Textarea,
    ARadioGroup: RadioGroup,
    ARadio: Radio,
    BasicButton,
    OrionTable,
    BasicDrawer,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['close', 'update'],
  setup(props, context) {
    const data = ref(null);
    const router = useRouter();
    const relationMap = {
      100061: '前置关系',
      100062: '后置关系',
    };
    const relationList = ref(['']);
    const values = ref([]);
    const detailData = ref<any>({});
    const deptList = ref([]);
    const userList = reactive({});

    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(
      (drawerData) => {
        data.value = drawerData?.data || {};
        getPlanDetail();
        getDetail();
        getDeptList();
      },
    );
    const planDetailRows = reactive([
      [
        {
          label: '计划名称',
          value: 'name',
          span: '12',
        },
        {
          label: '计划父级',
          value: 'parent',
          span: '12',
        },
      ],
      [
        {
          label: '开始时间',
          value: 'beginTime',
          span: '12',
        },
        {
          label: '结束时间',
          value: 'endTime',
          span: '12',
        },
      ],
      [
        {
          label: '责任部门',
          value: 'rspSubDeptName',
          span: '12',
        },
        {
          label: '责任人',
          value: 'rspUserName',
          span: '12',
        },
      ],
      [
        {
          label: '前置计划关系',
          value: 'hasBefore',
          span: '12',
        },
        {
          label: '前置关系',
          value: 'beforeRelation',
          span: '12',
        },
      ],
      [
        {
          label: '计划描述',
          value: 'schemeDesc',
          span: '24',
        },
      ],
    ]);
    const form = reactive({
      feedBack: '',
      needChange: true,
    });
    const recordRule = {
      required: true,
      message: '计划记录不能为空',
      trigger: 'change',
    };

    const formRef = ref(null);

    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      isTreeTable: false,
      showSmallSearch: false,
      showIndexColumn: true,
      pagination: false,
      // api: (params) => new Api('/plan').fetch(params, 'scheme/tree', 'POST'),
      columns: [
        {
          title: '前后置关系',
          dataIndex: 'type',
          width: 120,
          customRender({ record }) {
            return `${relationMap[record.type]}`;
          },
          // slots: { customRender: 'name' },
        },
        {
          title: '计划名称',
          dataIndex: 'preSchemeName',
          width: 200,
          slots: { customRender: 'name' },
        },

        {
          title: '责任部门',
          align: 'left',
          dataIndex: 'rspSubDeptName',
          width: 120,
        },

        {
          title: '责任人',
          align: 'left',
          dataIndex: 'rspUserName',
          slots: { customRender: 'rspUser' },
          width: 120,
        },
        {
          title: '开始时间',
          align: 'left',
          dataIndex: 'schemeBeginTime',
          width: 140,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'schemeEndTime',
          width: 140,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    });
    const userStore = useUserStore();

    function visibleChange(val) {
      if (!val) {
      }
    }

    const handleToDetail = (row) => {
      const { href } = router.resolve({
        name: 'ProPlanDetails',
        params: {
          id: row.preSchemeId,
        },
      });
      window.open(href, '_blank');
    };

    // 获取部门列表
    const getDeptList = async () => {
      const res = await new Api(
        `/pms/project-role-user/dept/list?projectId=${data.value?.projectId}`,
      ).fetch({ projectId: data.value?.projectId }, '', 'post');
      if (res) {
        deptList.value = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } else {
        deptList.value = [];
      }
    };

    const handleSubmit = async () => {
      await formRef.value.validate();
      const params = {
        agreement: true,
        certifier: userStore.getUserInfo.id,
        feedBack: form.feedBack,
        projectId: detailData.value.projectId,
        projectSchemeId: detailData.value.id,
      };
      const res = await new Api(
        `/pms/projectScheme/agree/${detailData.value.id}`,
      ).fetch(params, '', 'POST');
      if (res) {
        context.emit('update');
        message.success('计划审批成功');
        handleClose();
      }
    };

    const handleReject = async () => {
      const params = {
        agreement: false,
        certifier: userStore.getUserInfo.id,
        feedBack: form.feedBack,
        projectId: detailData.value.projectId,
        projectSchemeId: detailData.value.id,
      };
      const res = await new Api(
        `/pms/projectScheme/reject/${detailData.value.id}`,
      ).fetch(params, '', 'POST');
      if (res) {
        context.emit('update');
        message.success('驳回成功');
        handleClose();
      }
    };

    const handleClose = () => {
      relationList.value = [];
      values.value = [];
      closeDrawer();
      context.emit('close');
    };
    const getPlanDetail = async () => {
      const res = await new Api(
        `/pms/projectScheme/apply/getDetail/${data.value.id}`,
      ).fetch('', '', 'GET');

      detailData.value = {
        ...res,
        parent: res.parentName || '',
        beginTime: dayjs(res.beginTime).format('YYYY-MM-DD'),
        endTime: dayjs(res.endTime).format('YYYY-MM-DD'),
        hasBefore: res.isPrePost ? '是' : '否',
        beforeRelation: (res.schemePrePostVOList || [])
          .map((item) => item.name)
          .filter((item) => item)
          .join(','),
      };
    };
    const getDetail = async () => {
      const res = await new Api(`/pms/projectScheme/${data.value.id}`).fetch(
        '',
        '',
        'GET',
      );

      // tableSource.value = res.schemePrePostVOList || [];
      tableSource.value = [...(res.schemePrePostVOList || []), ...(res.schemePostVOList || [])];
      detailData.value.hasBefore = res.isPrePost ? '是' : '否';
      detailData.value.beforeRelation = (res.schemePrePostVOList || [])
        .map((item) => item.preSchemeName)
        .filter((item) => item)
        .join(',');
    };

    return {
      visibleChange,
      relationList,
      values,
      tableSource,
      tableOptions,
      handleSubmit,
      handleClose,
      planDetailRows,
      detailData,
      form,
      formRef,
      recordRule,
      tableRef,
      getPlanDetail,
      handleReject,
      deptList,
      userList,
      modalRegister,
      handleToDetail,
      getDetail,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 22px 22px 88px;
}
.title {
  font-size: 14px;
  font-weight: bold;
  margin-top: 8px;
}
.add-btn {
  margin-top: 35px;
  bottom: 10px;
}
.row {
  display: flex;
  flex-direction: row;
}
.col {
  margin-right: 16px;
  flex: 2;
  display: flex;
  align-items: center;
  height: 40px;
}
.label {
  min-width: 100px;
}
.label,
.value {
  color: ~`getPrefixVar('primary-10') `;
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
