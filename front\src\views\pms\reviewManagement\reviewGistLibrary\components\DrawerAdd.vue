<script setup lang="ts">
import {
  BasicForm, useForm,
} from 'lyra-component-vue3';
import { onMounted, ref } from 'vue';
import { reviewLibraryGet } from '/@/views/pms/api/reviewLibrary';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const dataDetail = ref({});
const [register, formMethods] = useForm({
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '评审要点库名称',
      required: true,
      colProps: {
        span: 24,
      },
    },
    {
      field: 'expertList',
      label: '评审专家',
      colProps: { span: 12 },
      componentProps: {
        selectUserModalProps: {
          selectType: 'check',
          isRequired: true,
        },
      },
      rules: [
        {
          required: true,
          type: 'array',
        },
      ],
      component: 'SelectUser',
    },
    {
      field: 'maintainDept',
      label: '维护部门',
      colProps: { span: 12 },
      rules: [
        {
          required: true,
        },
      ],
      component: 'SelectOrg',
    },

    {
      field: 'remark',
      component: 'InputTextArea',
      label: '备注',
      required: false,
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 1000,
      },
    },

  ],
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});

const getDataDetail = () => dataDetail.value;

onMounted(async () => {
  if (props.id) {
    loading.value = true;
    const result = await reviewLibraryGet(props.id);
    loading.value = false;
    result.maintainDept = result.maintainDeptList;
    dataDetail.value = result;
    formMethods.setFieldsValue(dataDetail.value);
  }
});

defineExpose({
  formMethods,
  getDataDetail,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
