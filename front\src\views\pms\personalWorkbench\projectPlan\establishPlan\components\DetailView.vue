<template>
  <DetailsLayout
    title="基本信息"
    :list="basicInfoList"
    :dataSource="$props?.data"
    :column="4"
  >
    <template #schemePrePostVOList="{text}">
      {{ text?.length > 0 ? '有' : '无' }}
    </template>
    <template #preSchemeName="{text}">
      {{ text?.['map'](item => item?.['preSchemeName'])?.join('、') || '--' }}
    </template>
    <template #schemeContentVOList="{text}">
      <div class="scheme-wrap">
        <div
          v-for="val in text || []"
          :key="val.id"
          class="record-content"
        >
          <div class="content">
            {{ val.content }}
          </div>
          <div class="time">
            -{{ dayjs(val['createTime']).format('YYYY-MM-DD HH:mm:ss') }}
          </div>
        </div>
      </div>
    </template>
  </DetailsLayout>
  <DetailsLayout title="前置计划关系">
    <div style="height: 300px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      >
        <template #action="{ record }">
          <div>
            <span
              class="action-btn"
              @click="() => deleteItem(record.id)"
            >解除</span>
          </div>
        </template>
      </OrionTable>
    </div>
  </DetailsLayout>
</template>
<script lang="ts">
import {
  computed, defineComponent, onMounted, reactive, ref, Ref, toRefs,
} from 'vue';
import { message } from 'ant-design-vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import dayjs from 'dayjs';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { RelationEnum } from '/@/views/pms/projectLaborer/projectLab/enums';

export default defineComponent({
  components: {
    DetailsLayout,
    OrionTable,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['handleGetDetail'],
  setup(props, { emit }) {
    const state = reactive({
      basicInfoList: [
        {
          label: '计划名称',
          field: 'name',
        },
        {
          label: '计划父级',
          field: 'parentName',
        },
        {
          label: '责任部门',
          field: 'rspSubDeptName',
        },
        {
          label: '责任人',
          field: 'rspUserName',
        },
        {
          label: '开始时间',
          field: 'beginTime',
          formatTime: 'YYYY-MM-DD HH:mm:ss',
        },
        {
          label: '结束时间',
          field: 'endTime',
          formatTime: 'YYYY-MM-DD HH:mm:ss',
        },
        {
          label: '前置计划关系',
          field: 'schemePrePostVOList',
          slot: true,
          slotName: 'schemePrePostVOList',
        },
        {
          label: '前置计划',
          field: 'schemePrePostVOList',
          slot: true,
          slotName: 'preSchemeName',
        },
        {
          label: '计划描述',
          field: 'schemeDesc',
          gridColumn: '1/5',
        },
        {
          label: '计划记录',
          field: 'schemeContentVOList',
          gridColumn: '1/5',
          slot: true,
          slotName: 'schemeContentVOList',
        },
      ],
    });
    //     这里的代码首先检查 props?.data.schemePrePostVOList 和 props?.data.schemePostVOList 是否存在，如果存在，就直接使用这两个数组，如果不存在，就使用空数组 []。然后使用扩展运算符 ... 来合并这两个数组。
    // 这样，无论 schemePrePostVOList 和 schemePostVOList 是否存在， tableSource 都将始终是一个数组，可能是空数组或包含所有数据的数组。
    const tableSource = computed(() => [...(props?.data.schemePrePostVOList || []), ...(props?.data.schemePostVOList || [])]);

    const tableRef:Ref = ref();
    const tableOptions = {
      isTreeTable: false,
      showSmallSearch: false,
      showIndexColumn: true,
      showToolButton: false,
      pagination: false,
      columns: [
        {
          title: '前后置关系',
          dataIndex: 'type',
          width: 100,
          customRender({ record }) {
            return `${RelationEnum[record.type]}`;
          },
        },
        {
          title: '计划名称',
          dataIndex: 'preSchemeName',
          align: 'left',
          minWidth: 300,
          slots: { customRender: 'name' },
        },
        {
          title: '责任部门',
          align: 'left',
          dataIndex: 'rspSubDeptName',
          width: 120,
        },
        {
          title: '责任人',
          align: 'left',
          dataIndex: 'rspUserName',
          width: 120,
        },
        {
          title: '开始日期',
          align: 'left',
          dataIndex: 'schemeBeginTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '结束日期',
          align: 'left',
          dataIndex: 'schemeEndTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '描述',
          align: 'left',
          dataIndex: 'schemeDesc',
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 120,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };
    const deleteItem = async (id) => {
      const res = await new Api('/pms/schemePrePost').fetch([id], '', 'DELETE');
      if (res) {
        message.success('删除成功');
        emit('handleGetDetail');
      }
    };

    return {
      ...toRefs(state),
      tableSource,
      tableOptions,
      deleteItem,
      dayjs,
      tableRef,
    };
  },
});
</script>
<style lang="less" scoped>
.scheme-wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 0;

  .record-content {
    display: flex;
    align-items: flex-start;

    .content {
      color: ~`getPrefixVar('primary-10') `;
      line-height: 20px;
      flex-grow: 1;
      margin-right: 50px;
      width: 0;
    }

    .time {
      flex-shrink: 0;
      color: ~`getPrefixVar('primary-10') `;
    }
  }

  .record-content+.record-content{
    margin-top: 15px;
  }
}

.container-box {
  height: 400px;
  overflow: hidden;
}
</style>
