package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.domain.dto.PurchProjectWeeklyDTO;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyExcelVO;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyVO;
import com.chinasie.orion.service.PurchProjectWeeklyService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * PurchProjectWeekly 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@RestController
@RequestMapping("/purchProjectWeekly")
@Api(tags = "采购项目实施周报")
public class PurchProjectWeeklyController {

    @Autowired
    private PurchProjectWeeklyService purchProjectWeeklyService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<PurchProjectWeeklyVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        PurchProjectWeeklyVO rsp = purchProjectWeeklyService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增校验
     *
     * @param purchProjectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增校验")
    @RequestMapping(value = "/checkCreate", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#purchProjectWeeklyDTO.name}}】", type = "PurchProjectWeekly", subType = "编辑", bizNo = "{{#purchProjectWeeklyDTO.id}}")
    public ResponseDTO<Boolean> checkCreate(@RequestBody  PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        Boolean rsp = purchProjectWeeklyService.checkCreate(purchProjectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param purchProjectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#purchProjectWeeklyDTO.name}}】", type = "PurchProjectWeekly", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        String rsp =  purchProjectWeeklyService.create(purchProjectWeeklyDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询已填的周
     *
     * @param purchProjectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询已填的周")
    @RequestMapping(value = "/selectFinishWeek", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#purchProjectWeeklyDTO.name}}】", type = "PurchProjectWeekly", subType = "编辑", bizNo = "{{#purchProjectWeeklyDTO.id}}")
    public ResponseDTO<List<Integer>> selectFinishWeek(@RequestBody  PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        List<Integer> rsp = purchProjectWeeklyService.selectFinishWeek(purchProjectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询上周工作安排
     *
     * @param purchProjectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询上周工作安排")
    @RequestMapping(value = "/selectLastWeekPlan", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#purchProjectWeeklyDTO.name}}】", type = "PurchProjectWeekly", subType = "编辑", bizNo = "{{#purchProjectWeeklyDTO.id}}")
    public ResponseDTO<String> selectLastWeekPlan(@RequestBody  PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        String rsp = purchProjectWeeklyService.selectLastWeekPlan(purchProjectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param purchProjectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#purchProjectWeeklyDTO.name}}】", type = "PurchProjectWeekly", subType = "编辑", bizNo = "{{#purchProjectWeeklyDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        Boolean rsp = purchProjectWeeklyService.edit(purchProjectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "PurchProjectWeekly", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = purchProjectWeeklyService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "PurchProjectWeekly", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = purchProjectWeeklyService.remove(ids);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "PurchProjectWeekly", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PurchProjectWeeklyVO>> pages(@RequestBody Page<PurchProjectWeeklyDTO> pageRequest) throws Exception {
        Page<PurchProjectWeeklyVO> rsp =  purchProjectWeeklyService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页报表
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页报表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "PurchProjectWeekly", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/pageExcel", method = RequestMethod.POST)
    public ResponseDTO<Page<PurchProjectWeeklyExcelVO>> pageExcel(@RequestBody Page<PurchProjectWeeklyDTO> pageRequest) throws Exception {
        Page<PurchProjectWeeklyExcelVO> rsp =  purchProjectWeeklyService.pageExcel( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购项目实施周报导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "PurchProjectWeekly", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<PurchProjectWeeklyDTO> pageRequest, HttpServletResponse response) throws Exception {
        purchProjectWeeklyService.exportByExcel(pageRequest, response);
    }
}
