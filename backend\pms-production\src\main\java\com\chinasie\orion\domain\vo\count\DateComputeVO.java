package com.chinasie.orion.domain.vo.count;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DateComputeVO {

    @JsonFormat(pattern = "yyyy-MM-dd")
    Date planStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    Date planEnd;

    @JsonFormat(pattern = "yyyy-MM-dd")
    Date actureStart;

    @JsonFormat(pattern = "yyyy-MM-dd")
    Date actureEnd;

}
