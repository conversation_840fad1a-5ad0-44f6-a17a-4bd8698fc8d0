package com.chinasie.orion.domain.dto.reporting;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/14/23:22
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DailyExportDTO {
    @ExcelProperty("序号*")
    private String sort;
    @ExcelProperty("日期*")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date daily;
    @ExcelProperty("工作内容*")
    private String content;
    @ExcelProperty(value = "关联对象")
    private String relationObj;
    @ExcelProperty("责任人*")
    private String respName;
    @ExcelProperty("工时*")
    private BigDecimal taskTime;
    @ExcelProperty("整体进度*")
    private String statusName;
    @ExcelProperty("状态*")
    private String busStatusName;
    @ExcelProperty("审核人*")
    private String reviewedByName;
    @ExcelProperty(value = "评分")
    private BigDecimal score;
    @ExcelProperty(value = "评价")
    private String evaluate;

    public Date getDaily() {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+08"));
        ZoneId timeZone = ZoneId.systemDefault();
        return daily;
    }
}
