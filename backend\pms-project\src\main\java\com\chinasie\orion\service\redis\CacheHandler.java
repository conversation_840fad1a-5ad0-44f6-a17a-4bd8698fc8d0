package com.chinasie.orion.service.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Slf4j
@Component
public class CacheHandler {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    // 过期时间 @Value("${orion.pms.major.role.mrc.code:mrc_lead}")
    @Value("${orion.pms.redis.time-to-live:172800}")
    private long ttl;

    public void setKeyWithExpiration(String key) {
        log.info("key保持时长：{}秒",ttl);
        // 使用Lua脚本保证原子性
        String luaScript = "redis.call('SETEX', KEYS[1], ARGV[1], 'pending');" +
                "redis.call('SADD', 'unprocessed_keys', KEYS[1]);";
        RedisScript<Void> script = RedisScript.of(luaScript);
        redisTemplate.execute(script, Collections.singletonList(key), String.valueOf(ttl));
    }
}