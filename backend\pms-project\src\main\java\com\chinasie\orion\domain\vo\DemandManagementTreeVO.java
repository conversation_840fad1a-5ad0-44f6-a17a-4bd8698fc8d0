package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/18 17:54
 * @description:
 */
@Data
public class DemandManagementTreeVO implements Serializable {
    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String exhibitor;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    private Date proposedTime;

    /**
     * 期望完成日期
     */
    @ApiModelProperty(value = "期望完成日期")
    private Date predictEndTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityLevel;
    private String priorityLevelName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;
    private String principalName;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private BigDecimal schedule;
    private String scheduleName;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    private List<DemandManagementTreeVO> child;
}
