package com.chinasie.orion.domain.vo.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/11/11:03
 * @description:
 */
@Data
public class SimpleTrainVO  implements Serializable {
    @ApiModelProperty(value = "培训ID")
    private String id ;
    @ApiModelProperty(value = "培训编码")
    private String number;
    @ApiModelProperty(value = "培训名称")
    private String name;

    @ApiModelProperty(value = "trainkey")
    private String trainKey;

    @ApiModelProperty(value = "到期时间")
    private Date expireTime;
}
