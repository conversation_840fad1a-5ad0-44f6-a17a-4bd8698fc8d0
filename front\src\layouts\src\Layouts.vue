<template>
  <ConfigProvider v-bind="getConfigProviderProps()">
    nihaoyahahah234
  </ConfigProvider>
</template>

<script lang="ts">
import { ConfigProvider } from 'ant-design-vue';
import { getConfigProviderProps } from './methods';

export default {
  name: 'DefaultLayouts',
  components: {
    ConfigProvider,
  },
  setup() {
    return {
      getConfigProviderProps,
    };
  },
};
</script>

<style scoped>

</style>
