package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.SafetyPyramidParamDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvStatisticDTO;
import com.chinasie.orion.domain.dto.SimpleSqeDTO;
import com.chinasie.orion.domain.dto.train.SimpleSearchDTO;
import com.chinasie.orion.domain.vo.SafetyQualityEnvVO;
import com.chinasie.orion.domain.vo.count.SafetyPyramidCount;
import com.chinasie.orion.domain.vo.count.SafetyQualityEnvCountVO;
import com.chinasie.orion.domain.vo.env.DeviationStatisticsVO;
import com.chinasie.orion.domain.vo.env.DeviationTopVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.SafetyQualityEnvService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/15:00
 * @description:
 */

@RestController
@RequestMapping("/safety-quality-env")
@Api(tags = "安质环")
public class  SafetyQualityEnvController  {

    @Autowired
    private SafetyQualityEnvService safetyQualityEnvService;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【安质环}】--【{{#eventTopic}}】详情", type = "SafetyQualityEnv", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SafetyQualityEnvVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        SafetyQualityEnvVO rsp = safetyQualityEnvService.detail(id,pageCode);
        LogRecordContext.putVariable("eventTopic", rsp.getEventTopic());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param safetyQualityEnvDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【安质环}】数据【{{#safetyQualityEnvDTO.eventTopic}}】", type = "SafetyQualityEnv", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SafetyQualityEnvDTO safetyQualityEnvDTO) throws Exception {
        String rsp =  safetyQualityEnvService.create(safetyQualityEnvDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param simpleSqeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "简化编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【安质环}数据【{{#safetyQualityEnvDTO.eventTopic}}】", type = "SafetyQualityEnv", subType = "编辑", bizNo = "{{#safetyQualityEnvDTO.id}}")
    public ResponseDTO<Boolean> editSimple(@RequestBody SimpleSqeDTO simpleSqeDTO) throws Exception {
        Boolean rsp = safetyQualityEnvService.editSimple(simpleSqeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 一层安质环事件统计
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【安质环}数据", type = "SafetyQualityEnv", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SafetyQualityEnvVO>> pages(@RequestBody Page<SafetyQualityEnvDTO> pageRequest) throws Exception {
        Page<SafetyQualityEnvVO> rsp =  safetyQualityEnvService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【安质环}数据列表", type = "SafetyQualityEnv", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<SafetyQualityEnvStatisticDTO> listByMajorRepairTurn(@RequestBody SimpleSearchDTO searchDTO) throws Exception {
        SafetyQualityEnvStatisticDTO rsp =  safetyQualityEnvService.listByMajorRepairTurn( searchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 安质环事件统计
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "安质环事件统计")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【安质环】大修【{{#searchDTO.majorRepairTurn}}】安质环事件统计", type = "SafetyQualityEnv", subType = "安质环事件统计", bizNo = "")
    @RequestMapping(value = "/listByStatistic", method = RequestMethod.POST)
    public ResponseDTO<SafetyQualityEnvStatisticDTO> evenStatistic(@RequestBody SimpleSearchDTO searchDTO) throws Exception {
        SafetyQualityEnvStatisticDTO rsp =  safetyQualityEnvService.evenStatistic( searchDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "统计")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】统计【安质环】数据列表", type = "SafetyQualityEnv", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/pyramid/count/list", method = RequestMethod.POST)
    public ResponseDTO<SafetyQualityEnvCountVO> pyramidCountList(@RequestBody SafetyPyramidParamDTO searchDTO) throws Exception {
        SafetyQualityEnvCountVO rsp =  safetyQualityEnvService.pyramidCountList( searchDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取大修轮次")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【安质环】安质环中的所有大修轮次列表", type = "SafetyQualityEnv", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/repairRound/list", method = RequestMethod.GET)
    public ResponseDTO<List<String>> repairRoundList( ) throws Exception {
        List<String> rsp =  majorRepairPlanService.getRepairRoundList( );
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation("安质环导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【安质环】数据", type = "SafetyQualityEnv", subType = "导出数据", bizNo = "")
    public void exportByExcel(@Validated  SimpleSearchDTO searchDTO, HttpServletResponse response) throws Exception {
        safetyQualityEnvService.exportByExcel( searchDTO,response);
    }


    @ApiOperation("偏差统计")
    @PostMapping(value = "/deviation/statistics")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【安质环】偏差统计", type = "SafetyQualityEnv", subType = "查询统计", bizNo = "")
    public ResponseDTO<List<DeviationStatisticsVO>> deviationStatistics(@Validated  SimpleSearchDTO searchDTO) throws Exception {
        return new ResponseDTO<>(safetyQualityEnvService.deviationStatistics( searchDTO));
    }

    @ApiOperation("偏差top5")
    @PostMapping(value = "/deviation/top")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【安质环】偏差统计top5", type = "SafetyQualityEnv", subType = "查询统计", bizNo = "")
    public ResponseDTO<DeviationTopVO> deviationTop(@Validated  SimpleSearchDTO searchDTO) throws Exception {
        return new ResponseDTO<>(safetyQualityEnvService.deviationTop( searchDTO));
    }

}
