package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BudgetMonth Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:58:25
 */
@ApiModel(value = "BudgetMonthVO对象", description = "费用预算月度年度表")
@Data
public class BudgetMonthVO extends ObjectVO implements Serializable{

            /**
         * 项目预算表ID
         */
        @ApiModelProperty(value = "项目预算表ID")
        private String budgetProjectId;

        /**
         * 项目预算名
         */
        @ApiModelProperty(value = "项目预算名")
        private String budgetProjectName;

        /**
         * 年度预算支出
         */
        @ApiModelProperty(value = "年度预算支出")
        private BigDecimal yearExpense;

        /**
         * 年度实际支出
         */
        @ApiModelProperty(value = "年度实际支出")
        private BigDecimal yearFactExpense;

        /**
         * 1月预算
         */
        @ApiModelProperty(value = "1月预算")
        private BigDecimal januaryMoney;

        /**
         * 2月预算
         */
        @ApiModelProperty(value = "2月预算")
        private BigDecimal februaryMoney;

        /**
         * 3月预算
         */
        @ApiModelProperty(value = "3月预算")
        private BigDecimal marchMoney;

        /**
         * 4月预算
         */
        @ApiModelProperty(value = "4月预算")
        private BigDecimal aprilMoney;

        /**
         * 5月预算
         */
        @ApiModelProperty(value = "5月预算")
        private BigDecimal mayMoney;

        /**
         * 6月预算
         */
        @ApiModelProperty(value = "6月预算")
        private BigDecimal juneMoney;

        /**
         * 7月预算
         */
        @ApiModelProperty(value = "7月预算")
        private BigDecimal julyMoney;

        /**
         * 8月预算
         */
        @ApiModelProperty(value = "8月预算")
        private BigDecimal augustMoney;

        /**
         * 9月预算
         */
        @ApiModelProperty(value = "9月预算")
        private BigDecimal septemberMoney;

        /**
         * 10月预算
         */
        @ApiModelProperty(value = "10月预算")
        private BigDecimal octoberMoney;

        /**
         * 11月预算
         */
        @ApiModelProperty(value = "11月预算")
        private BigDecimal novemberMoney;

        /**
         * 12月预算
         */
        @ApiModelProperty(value = "12月预算")
        private BigDecimal decemberMoney;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

    }
