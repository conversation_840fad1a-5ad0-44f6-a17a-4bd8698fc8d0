<template>
  <BasicModal
    v-bind="$attrs"
    title="选择里程碑"
    :width="800"
    :min-height="400"
    wrap-class-name="addPlanTreeModal"
    @ok="confirm"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div
      v-if="showTable"
      class="modal-table-height"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import {
  BasicModal, useModalInner, OrionTable, BasicDrawer,
} from 'lyra-component-vue3';
import {
  defineEmits, defineProps, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
const props = defineProps<{
    selectType: any,
}>();

const emit = defineEmits(['ok']);
const showTable = ref(false);
const approvalId = ref('');
const [registerModal, { closeModal }] = useModalInner((modalData) => {
  approvalId.value = modalData.approvalId;
  showTable.value = true;
});
const tableRef: Ref = ref();

const tableOptions = {
  isTableHeader: false,
  rowSelection: {
    type: props.selectType,
  },
  showIndexColumn: false,
  // dataSource: [{}],
  api: (params) => {
    params.query = {
      approvalId: unref(approvalId),
    };
    return new Api('/pms').fetch(params, 'projectApprovalMilestone/getPages', 'POST');
  },
  columns: [
    {
      title: '节点名称',
      dataIndex: 'name',
    },
    {
      title: '计划开始时间',
      dataIndex: 'beginTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划结束时间',
      dataIndex: 'endTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
    },
    {
      title: '责任人',
      dataIndex: 'resPersonName',
    },
  ],
};
function confirm() {
  let selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择数据');
  }
  emit('ok', selectRows);
  closeModal();
}
function visibleChange(val) {
  if (!val) {
    showTable.value = false;
  }
}
</script>
<style lang="less" scoped>
.modal-table-height{
  height: 400px;
  overflow: hidden;
}
</style>
