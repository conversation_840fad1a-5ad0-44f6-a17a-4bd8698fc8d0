<template>
  <div
    class="push-role"
  >
    <div class="role-label">
      请选择问题需推送的角色:
    </div>
    <div>
      <BasicInputSelectRole
        v-model:value="ruleValue"
        selectType="checkbox"
        @change="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { BasicInputSelectRole } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const ruleValue:Ref<any[]> = ref([]);

defineExpose({
  async onSubmit() {
    // let formData = await validateFields();
    if (ruleValue.value.length === 0) {
      message.warning('请选择角色');
      return Promise.reject('请选择角色');
    }
    return Promise.resolve(ruleValue.value);
  },
});
</script>
<style scoped lang="less">
.push-role{
  padding: 10px;
  .role-label{
    padding-bottom: 5px;
    font-weight: 650;
    font-style: normal;
    font-size: 14px;
    color: #676767;
  }
}

</style>