package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.entity.AuthorPersonJobPostEqu;
import com.chinasie.orion.domain.dto.AuthorPersonJobPostEquDTO;
import com.chinasie.orion.domain.vo.AuthorPersonJobPostEquVO;
import com.chinasie.orion.service.AuthorPersonJobPostEquService;
import com.chinasie.orion.repository.AuthorPersonJobPostEquMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * AuthorPersonJobPostEqu 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:34
 */
@Service
@Slf4j
public class AuthorPersonJobPostEquServiceImpl extends  OrionBaseServiceImpl<AuthorPersonJobPostEquMapper, AuthorPersonJobPostEqu>   implements AuthorPersonJobPostEquService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  AuthorPersonJobPostEquVO detail(String id,String pageCode) throws Exception {
        AuthorPersonJobPostEqu authorPersonJobPostEqu =this.getById(id);
        AuthorPersonJobPostEquVO result = BeanCopyUtils.convertTo(authorPersonJobPostEqu,AuthorPersonJobPostEquVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param authorPersonJobPostEquDTO
     */
    @Override
    public  String create(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO) throws Exception {
        AuthorPersonJobPostEqu authorPersonJobPostEqu =BeanCopyUtils.convertTo(authorPersonJobPostEquDTO,AuthorPersonJobPostEqu::new);
        this.save(authorPersonJobPostEqu);

        String rsp=authorPersonJobPostEqu.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param authorPersonJobPostEquDTO
     */
    @Override
    public Boolean edit(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO) throws Exception {
        AuthorPersonJobPostEqu authorPersonJobPostEqu =BeanCopyUtils.convertTo(authorPersonJobPostEquDTO,AuthorPersonJobPostEqu::new);

        this.updateById(authorPersonJobPostEqu);

        String rsp=authorPersonJobPostEqu.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AuthorPersonJobPostEquVO> pages( Page<AuthorPersonJobPostEquDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AuthorPersonJobPostEqu> condition = new LambdaQueryWrapperX<>( AuthorPersonJobPostEqu. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AuthorPersonJobPostEqu::getCreateTime);


        Page<AuthorPersonJobPostEqu> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AuthorPersonJobPostEqu::new));

        PageResult<AuthorPersonJobPostEqu> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AuthorPersonJobPostEquVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AuthorPersonJobPostEquVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AuthorPersonJobPostEquVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业人员岗位授权等效导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AuthorPersonJobPostEquDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            AuthorPersonJobPostEquExcelListener excelReadListener = new AuthorPersonJobPostEquExcelListener();
        EasyExcel.read(inputStream,AuthorPersonJobPostEquDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AuthorPersonJobPostEquDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业人员岗位授权等效导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AuthorPersonJobPostEqu> authorPersonJobPostEques =BeanCopyUtils.convertListTo(dtoS,AuthorPersonJobPostEqu::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AuthorPersonJobPostEqu-import::id", importId, authorPersonJobPostEques, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AuthorPersonJobPostEqu> authorPersonJobPostEques = (List<AuthorPersonJobPostEqu>) orionJ2CacheService.get("pmsx::AuthorPersonJobPostEqu-import::id", importId);
        log.info("作业人员岗位授权等效导入的入库数据={}", JSONUtil.toJsonStr(authorPersonJobPostEques));

        this.saveBatch(authorPersonJobPostEques);
        orionJ2CacheService.delete("pmsx::AuthorPersonJobPostEqu-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AuthorPersonJobPostEqu-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AuthorPersonJobPostEqu> condition = new LambdaQueryWrapperX<>( AuthorPersonJobPostEqu. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AuthorPersonJobPostEqu::getCreateTime);
        List<AuthorPersonJobPostEqu> authorPersonJobPostEques =   this.list(condition);

        List<AuthorPersonJobPostEquDTO> dtos = BeanCopyUtils.convertListTo(authorPersonJobPostEques, AuthorPersonJobPostEquDTO::new);

        String fileName = "作业人员岗位授权等效数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AuthorPersonJobPostEquDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AuthorPersonJobPostEquVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<AuthorPersonJobPostEqu> listByAuthorManageId(String manageId) {
        LambdaQueryWrapperX<AuthorPersonJobPostEqu> wrapperX = new LambdaQueryWrapperX<>(AuthorPersonJobPostEqu.class);
        wrapperX.eq(AuthorPersonJobPostEqu::getAuthorManageId,manageId);
//        wrapperX.select(AuthorPersonJobPostEqu::getAuthorId,AuthorPersonJobPostEqu::getPersonId,AuthorPersonJobPostEqu::getHistoryAuthorId
//                ,AuthorPersonJobPostEqu::getAuthorManageId,AuthorPersonJobPostEqu::getJobPostCode,AuthorPersonJobPostEqu::getUserCode,AuthorPersonJobPostEqu::getId);
        List<AuthorPersonJobPostEqu> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        return list;
    }

    @Override
    public Boolean saveOrUpdate(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO) {

        // 获取 需要等效的 岗位授权管理ID
        String authorManageId = authorPersonJobPostEquDTO.getAuthorManageId();
        // 删除其他岗位授权 （一个作业岗位只能使用一个岗位等效）
        LambdaQueryWrapperX<AuthorPersonJobPostEqu> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(AuthorPersonJobPostEqu.class);
        lambdaQueryWrapperX.eq(AuthorPersonJobPostEqu::getAuthorManageId,authorManageId);
        this.remove(lambdaQueryWrapperX);
        AuthorPersonJobPostEqu authorPersonJobPostEqu = BeanCopyUtils.convertTo(authorPersonJobPostEquDTO, AuthorPersonJobPostEqu::new);
        return this.save(authorPersonJobPostEqu);
    }

    @Override
    public List<AuthorPersonJobPostEqu> listByUserCodeList(List<String> jobPostAuthorizeIdList,  List<String> userCodeList) {
        LambdaQueryWrapperX<AuthorPersonJobPostEqu> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(AuthorPersonJobPostEqu.class);
        lambdaQueryWrapperX.in(AuthorPersonJobPostEqu::getAuthorManageId,jobPostAuthorizeIdList);
        lambdaQueryWrapperX.in(AuthorPersonJobPostEqu::getJobPostCode,userCodeList);
        lambdaQueryWrapperX.select(AuthorPersonJobPostEqu::getAuthorId,AuthorPersonJobPostEqu::getPersonId
                ,AuthorPersonJobPostEqu::getId,AuthorPersonJobPostEqu::getAuthorManageId,AuthorPersonJobPostEqu::getUserCode
                ,AuthorPersonJobPostEqu::getHistoryAuthorId);
        return this.list(lambdaQueryWrapperX);
    }

    @Override
    public List<AuthorPersonJobPostEqu> listByJobAuthorizeIdList(List<String> idList) {

        LambdaQueryWrapperX<AuthorPersonJobPostEqu> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(AuthorPersonJobPostEqu.class);
        lambdaQueryWrapperX.in(AuthorPersonJobPostEqu::getHistoryAuthorId,idList);
        lambdaQueryWrapperX.select(AuthorPersonJobPostEqu::getAuthorId,AuthorPersonJobPostEqu::getPersonId
                ,AuthorPersonJobPostEqu::getId,AuthorPersonJobPostEqu::getAuthorManageId,AuthorPersonJobPostEqu::getUserCode
                ,AuthorPersonJobPostEqu::getHistoryAuthorId);
        return this.list(lambdaQueryWrapperX);
    }


    public static class AuthorPersonJobPostEquExcelListener extends AnalysisEventListener<AuthorPersonJobPostEquDTO> {

        private final List<AuthorPersonJobPostEquDTO> data = new ArrayList<>();

        @Override
        public void invoke(AuthorPersonJobPostEquDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AuthorPersonJobPostEquDTO> getData() {
            return data;
        }
    }


}
