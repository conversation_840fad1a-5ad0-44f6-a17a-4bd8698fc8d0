# 客户管理导入功能 PRD（产品需求文档）

## 1. 功能概述

客户管理导入功能允许用户通过Excel文件批量导入客户信息，支持模板下载、数据校验、预览确认和数据入库等完整流程。

## 2. 功能特性

### 2.1 核心功能
- **模板下载**：提供标准化的Excel导入模板
- **数据导入**：支持Excel文件上传和解析
- **数据校验**：实时验证数据格式和业务规则
- **预览确认**：导入前数据预览和确认
- **批量入库**：支持新增和更新操作
- **曾用名管理**：支持客户曾用名的导入和管理

### 2.2 业务规则
- 客户名称具有全局唯一性
- 客户编码作为主键，支持基于编码的更新操作
- 曾用名支持多个，逗号分隔
- 支持字典数据的中文描述导入（自动转换为编码值）
- 单次导入数据量限制为1000条

## 3. 流程设计

### 3.1 导入功能时序图

```plantuml
!include 客户导入功能时序图.puml
```

### 3.2 数据库设计

```plantuml
!include 客户管理数据库模型图.puml
```

主要涉及以下数据表：
- **pms_customer_info**：客户信息主表
- **pms_mk_alias**：客户曾用名表

## 4. 字段映射关系

### 4.1 Excel表格字段与数据库字段映射表

| 序号 | Excel列名(中文) | 是否必填 | 数据类型 | 英文字段名 | 数据库字段名 | 数据库表名 | 备注说明 |
|------|----------------|----------|----------|------------|-------------|------------|----------|
| 0 | 客户编码 | 是 | String | id/cusNumber | cus_number | pms_customer_info | 主键标识，支持更新操作 |
| 1 | 客户名称 | 是 | String | cusName | cus_name | pms_customer_info | 全局唯一性约束 |
| 2 | 业务收入类型 | 否 | String | ywsrlx | ywsrlx | pms_customer_info | 字典值：income_type |
| 3 | 所属行业 | 否 | String | industry | industry | pms_customer_info | 字典值：dict1804441009536851968 |
| 4 | 客户关系(集团内外) | 否 | String | groupInOut | group_in_out | pms_customer_info | 字典值：customer_relationship |
| 5 | 客户状态 | 否 | String | cusStatus | cus_status | pms_customer_info | 字典值：customer_status |
| 6 | 客户范围 | 否 | String | busScope | bus_scope | pms_customer_info | 字典值：customer_scope |
| 7 | 所属集团 | 否 | String | groupInfo | group_info | pms_customer_info | 自由文本 |
| 8 | 国标行业门类 | 否 | String | category | category | pms_customer_info | 自由文本 |
| 9 | 国标行业大类 | 否 | String | largeCategory | large_category | pms_customer_info | 自由文本 |
| 10 | 国标行业中类 | 否 | String | middleCategory | middle_category | pms_customer_info | 自由文本 |
| 11 | 客户级别 | 否 | String | cusLevel | cus_level | pms_customer_info | 字典值：customer_level |
| 12 | 国家 | 否 | String | country | country | pms_customer_info | 自由文本 |
| 13 | 所属省份 | 否 | String | province | province | pms_customer_info | 自由文本 |
| 14 | 所属城市 | 否 | String | city | city | pms_customer_info | 自由文本 |
| 15 | 所属区县 | 否 | String | county | county | pms_customer_info | 自由文本 |
| 16 | 客户联系地址 | 否 | String | cusAddress | cus_address | pms_customer_info | 自由文本 |
| 17 | 联系电话 | 否 | String | tel | tel | pms_customer_info | 自由文本 |
| 18 | 其他电话 | 否 | String | otherTel | other_tel | pms_customer_info | 自由文本 |
| 19 | 邮箱 | 否 | String | email | email | pms_customer_info | 自由文本 |
| 20 | 企业全称 | 否 | String | cusFullName | cus_full_name | pms_customer_info | 自由文本 |
| 21 | 英文名 | 否 | String | englishName | english_name | pms_customer_info | 自由文本 |
| 22 | 法定代表人 | 否 | String | legalRepr | legal_repr | pms_customer_info | 自由文本 |
| 23 | 工商注册号 | 否 | String | busRegisterCode | bus_register_code | pms_customer_info | 自由文本 |
| 24 | 纳税人识别号 | 否 | String | taxIdCode | tax_id_code | pms_customer_info | 自由文本 |
| 25 | 组织机构代码 | 否 | String | organizatioinCode | organizatioin_code | pms_customer_info | 自由文本 |
| 26 | 税务登记证号 | 否 | String | comtaxnumber | comtaxnumber | pms_customer_info | 自由文本 |
| 27 | 核准日期 | 否 | Date | approvedDate | approved_date | pms_customer_info | 日期格式 |
| 28 | 资质信息 | 否 | String | zzxx | zzxx | pms_customer_info | 自由文本 |
| 29 | 公司类型 | 否 | String | comtpye | comtpye | pms_customer_info | 自由文本 |
| 30 | 登记状态 | 否 | String | registStatus | regist_status | pms_customer_info | 自由文本 |
| 31 | 注册资本 | 否 | String | registeredCapital | registered_capital | pms_customer_info | 自由文本 |
| 32 | 实缴资本 | 否 | String | paidInCapital | paid_in_capital | pms_customer_info | 自由文本 |
| 33 | 营业期限 | 否 | String | bizPeriod | biz_period | pms_customer_info | 自由文本 |
| 34 | 注册地址 | 否 | String | registeredAddress | registered_address | pms_customer_info | 自由文本 |
| 35 | 统一社会信用代码 | 否 | String | uniformCreditCode | uniform_credit_code | pms_customer_info | 自由文本 |
| 36 | 企业类型 | 否 | String | cusCategory | cus_category | pms_customer_info | 自由文本 |
| 37 | 成立日期 | 否 | Date | registrationTime | registration_time | pms_customer_info | 日期格式 |
| 38 | 经营范围 | 否 | String | businessScope | business_scope | pms_customer_info | 自由文本 |
| 39 | 企业规模 | 否 | String | cusNumCount | cus_num_count | pms_customer_info | 自由文本 |
| 40 | 公众号信息 | 否 | String | publicAccountInfo | public_account_info | pms_customer_info | 自由文本 |
| 41 | 是否被使用 | 否 | String | isUsed | is_used | pms_customer_info | 0-未使用,1-已使用 |
| 42 | 所属基地 | 否 | String | homeBase | home_base | pms_customer_info | 基地编码 |
| 43 | 销售业务分类 | 否 | String | salesClass | sales_class | pms_customer_info | 自由文本 |

### 4.2 曾用名字段映射

| 字段名(中文) | 英文字段名 | 数据库字段名 | 数据库表名 | 备注说明 |
|-------------|------------|-------------|------------|----------|
| 曾用名称 | companyName | company_name | pms_mk_alias | 客户曾用名称 |
| 客户ID | customId | custom_id | pms_mk_alias | 关联客户主表ID |

## 5. 字典数据映射

### 5.1 支持中文描述导入的字典字段

| 字段名 | 字典类型编码 | 描述 |
|--------|-------------|------|
| 所属行业 | dict1804441009536851968 | 行业分类字典 |
| 客户关系 | dict1804392716872900608 | 集团内外关系 |
| 业务收入类型 | dict1804393678006050816 | 收入类型分类 |
| 客户范围 | dict1804392913271185408 | 客户范围分类 |
| 客户状态 | dict1804393066195509248 | 客户状态分类 |

## 6. 导入流程说明

### 6.1 API接口列表

| 接口名称 | HTTP方法 | 接口路径 | 功能描述 |
|---------|----------|----------|----------|
| 下载模板 | GET | /customer-info/download-template | 下载Excel导入模板 |
| 导入校验 | POST | /customer-info/import-check | 上传文件并进行数据校验 |
| 确认导入 | POST | /customer-info/import-confirm | 确认导入数据到数据库 |
| 取消导入 | POST | /customer-info/import-cancel | 取消本次导入操作 |

### 6.2 数据校验规则

#### 6.2.1 格式校验
- 文件格式：仅支持.xlsx格式
- 数据量限制：单次最多1000条记录
- 必填字段：客户编码、客户名称

#### 6.2.2 业务校验
- **客户名称唯一性**：新增客户名称不能与现有客户重复
- **客户编码唯一性**：同一导入批次内客户编码不能重复
- **曾用名唯一性**：曾用名在全局范围内不能重复
- **字典值校验**：字典字段支持中文描述，系统自动转换为对应编码

#### 6.2.3 错误提示
- 模板格式错误："数据不存在或导入模板表头解析错误，请检查"
- 数据量超限："数据量超限"
- 客户名称重复："客户名称重复，请检查"
- 曾用名重复："[具体名称]名称已存在!"

## 7. 技术实现要点

### 7.1 缓存机制
- 使用Redis缓存导入数据，缓存时间24小时
- 缓存Key格式：`pms::customerInfo-import::id`
- 支持导入确认前的数据预览

### 7.2 事务处理
- 客户信息和曾用名信息在同一事务中处理
- 支持批量操作，提高导入效率
- 异常回滚确保数据一致性

### 7.3 性能优化
- 使用EasyExcel进行文件解析，提高处理效率
- 批量数据库操作，减少数据库交互次数
- 异步处理大批量数据导入

## 8. 用户体验设计

### 8.1 操作流程
1. 用户点击"下载模板"获取标准Excel模板
2. 填写客户信息并上传文件
3. 系统进行数据校验并返回校验结果
4. 用户确认导入或修正错误后重新上传
5. 系统执行数据入库操作并返回结果

### 8.2 错误处理
- 详细的错误提示信息
- 支持部分成功的处理方式
- 提供错误数据的定位信息

## 9. 扩展性考虑

### 9.1 字段扩展
- 支持动态添加新的客户信息字段
- 模板和映射关系配置化管理

### 9.2 数据源扩展
- 支持多种文件格式（CSV、TXT等）
- 支持API接口批量导入

## 10. 安全性考虑

### 10.1 数据安全
- 上传文件格式验证
- 文件大小限制
- 恶意数据过滤

### 10.2 权限控制
- 导入操作需要相应权限
- 操作日志记录和审计

## 11. 测试要点

### 11.1 功能测试
- 模板下载功能正常
- 各种数据格式的导入测试
- 错误数据的校验测试
- 大数据量导入性能测试

### 11.2 异常测试
- 网络中断时的数据一致性
- 并发导入的数据安全性
- 异常文件格式的处理

## 12. 监控指标

### 12.1 业务指标
- 导入成功率
- 数据校验错误率
- 平均导入时间

### 12.2 技术指标
- 接口响应时间
- 系统资源使用率
- 错误日志统计 