package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectBudget Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 14:36:55
 */
@TableName(value = "pmsx_bud_project_budget")
@ApiModel(value = "ProjectBudget对象", description = "项目预算表")
@Data
public class ProjectBudget extends ObjectEntity implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    @TableField(value = "name" )
    private String name;

    /**
     * 成本中心ID
     */
    @ApiModelProperty(value = "成本中心ID")
    @TableField(value = "cost_center_id" )
    private String costCenterId;

    /**
     * 成本中心名字
     */
    @ApiModelProperty(value = "成本中心名字")
    @TableField(value = "cost_center_name" )
    private String costCenterName;

    /**
     * 费用科目ID
     */
    @ApiModelProperty(value = "费用科目ID")
    @TableField(value = "expense_account_id" )
    private String expenseAccountId;

    /**
     * 费用科目名
     */
    @ApiModelProperty(value = "费用科目名")
    @TableField(value = "expense_account_name" )
    private String expenseAccountName;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @TableField(value = "year" )
    private String year;

    /**
     * 年度预算总费用
     */
    @ApiModelProperty(value = "年度预算总费用")
    @TableField(value = "year_expense" )
    private BigDecimal yearExpense;

    /**
     * 费用预算月度表ID
     */
    @ApiModelProperty(value = "费用预算月度表ID")
    @TableField(value = "month_budget_id" )
    private String monthBudgetId;

    /**
     * 实际总成本
     */
    @ApiModelProperty(value = "实际总成本")
    @TableField(value = "total_cost" )
    private BigDecimal totalCost;

    /**
     * 差价
     */
    @ApiModelProperty(value = "差价")
    @TableField(value = "price_difference" )
    private BigDecimal priceDifference;

    /**
     * 是否超出预算 0为未超预算，1为超出预算
     */
    @ApiModelProperty(value = "是否超出预算 0为未超预算，1为超出预算")
    @TableField(value = "is_out" )
    private Integer isOut;

    /**
     * 执行进度
     */
    @ApiModelProperty(value = "执行进度")
    @TableField(value = "execution_schedule" )
    private Integer executionSchedule;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

}
