package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractAssessmentStandard DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:29:14
 */
@ApiModel(value = "ContractAssessmentStandardDTO对象", description = "审核标准表")
@Data
@ExcelIgnoreUnannotated
public class ContractAssessmentStandardDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    private String contractName;

    /**
     * 审核类别
     */
    @ApiModelProperty(value = "审核类别")
    @ExcelProperty(value = "审核类别 ", index = 2)
    private String assessmentType;

    /**
     * 考核内容
     */
    @ApiModelProperty(value = "考核内容")
    @ExcelProperty(value = "考核内容 ", index = 3)
    private String assessmentContent;

    /**
     * 考核标准
     */
    @ApiModelProperty(value = "考核标准")
    @ExcelProperty(value = "考核标准 ", index = 4)
    private String standard;




}
