<template>
  <BpmnMain
    :menu-instance-list-api="menuInstanceListApi"
    :template-list-api="templateListApi"
    :allTaskPageApi="allTaskPageApi"
    :addEditSavaApi="addEditSavaApi"
    :userId="userId"
    :journalApi="journalApi"
    :taskBtnApi="taskBtnApi"
    :nodeTableDataApi="nodeTableDataApi"
    :approvalListApi="approvalListApi"
    :approvalTableColumns="approvalTableColumns"
    :type="pageType === 'page' ? 1 : 2"
    @success="successChange"
    @openClick="openClick"
  />
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, inject, watch,
} from 'vue';
import { BpmnMain } from 'lyra-component-vue3';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import { stampDate } from '/@/utils/dateUtil';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'Information',
  components: {
    BpmnMain,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['changePage'],
  setup(props, { emit, attrs }) {
    const route = useRoute();
    const formData: any = inject('formData', {});
    const getFormData = inject('getFormData');
    const userStore = useUserStore();
    const state: any = reactive({
      deliveryId: '',
      dataType: '',
      bizId: '',
      procInstName: '',
      groupId: '',
      type: props.pageType === 'page' ? 1 : 2,
      userId: userStore.getUserInfo.id,
      templateList: [],
      approvalTableColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: 'status' },
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          slots: { customRender: 'modifyTime' },
        },
      ],
    });
    onMounted(() => {
      // if (formData?.value?.files) {
      //   tableRef.value.setTableData(formData?.value?.files);
      // }
    });

    function menuInstanceListApi(data) {
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: [
            {
              deliveryId: formData?.value?.id,
            },
          ],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST').then((res) => res);
    }
    async function templateListApi() {
      let params = {
        pageNum: 1,
        pageSize: 10000,
        query: {
          dataTypes: [formData.value.className],
          status: 1,
          userId: state.userId,
        },
      };
      return new Api('/workflow')
        .fetch(
          params,
          'process-template/major/page',
          'POST',
        )
        .then((res) => {
          state.templateList = res.content;
          return res.content.map((item) => ({
            label: item.name,
            value: item.procDefId,
            key: item.procDefId,
            id: item.procDefId,
          }));
        });
    }

    function allTaskPageApi(data) {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      return new Api('/workflow').fetch('', url, 'POST').then((res) => res);
    }

    // 保存
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      data.deliveries.forEach((item) => {
        item.deliveryId = formData?.value?.id;
      });
      let params: any = {
        userId: userStore.getUserInfo.id,
        // href: route.fullPath,
        // bizCatalogId: 'string',
        // bizCatalogName: 'string',
        bizId: data.bizId,
        // bizTypeName: 'string',
        // businessKey: 'string',
        deliveries: data.deliveries,
        flowInfoId: templateItem.id,
        // flowKey: 'string',
        href: `/pms/comprehensive-plan-detail?id=${formData.value.id}`,
        ownerId: userStore.getUserInfo.id,
        prearranges: data.prearranges,
        procDefId: templateItem.procDefId,
        procDefName: data.procDefName,
        procInstName: `${formData?.value?.name}实例${stampDate(Date.parse(new Date()), 'yyyy-MM-dd HH:mm:ss')}`,
      };
      if (typeof data.id === 'undefined') {
        params.id = data.id;
      }
      return new Api('/workflow').fetch(params, 'process-instance', `${typeof data.id === 'undefined' ? 'POST' : 'PUT'}`).then((res) => res);
    }

    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;
      let currentTasksItem = data.currentTasks.find((item) => item.assignee === userStore.getUserInfo.id);
      if (typeof currentTasksItem === 'undefined') return;
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET').then((res) => res);
    }

    const successChange = (type) => {
      getFormData.value(formData?.value?.id);
    };

    // 审批物列表
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([formData?.value]);
      });
    }

    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    const openClick = (record) => {
      window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
    };
    return {
      ...toRefs(state),
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      successChange,
      nodeTableDataApi,
      approvalListApi,
      openClick,
    };
  },
});
</script>
<style lang="less" scoped>
//.process {
//  height: 100%;
//}
</style>
