<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
   <modelVersion>4.0.0</modelVersion>
   <groupId>org.jboss.weld</groupId>
   <artifactId>weld-api-bom</artifactId>
   <packaging>pom</packaging>
   <version>1.0</version>

   <parent>
      <groupId>org.jboss.weld</groupId>
      <artifactId>weld-parent</artifactId>
      <version>6</version>
   </parent>

   <name>Weld and CDI APIs BOM</name>

   <!-- Minimal project metadata, for more see parent/pom.xml -->
   <url>http://www.seamframework.org/Weld</url>

   <description>Weld and CDI APIs "bill of materials" which can be imported by any project using the Weld implementation of CDI. It provides dependency management for the developer APIs and SPIs, as well as container integrator SPIs</description>

   <licenses>
      <license>
         <name>Apache License, Version 2.0</name>
         <distribution>repo</distribution>
         <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
      </license>
   </licenses>

   <developers>
      <developer>
         <name>Weld committers</name>
      </developer>
   </developers>

   <!-- Dependency management, including extra repositories if needed -->
   <repositories>
      <repository>
         <id>repository.jboss.org</id>
         <name>JBoss Release Repository</name>
         <url>http://repository.jboss.org/maven2</url>
         <releases>
            <enabled>true</enabled>
         </releases>
         <snapshots>
            <enabled>false</enabled>
         </snapshots>
      </repository>
      <repository>
         <id>snapshots.jboss.org</id>
         <name>JBoss Snapshots Repository</name>
         <url>http://snapshots.jboss.org/maven2</url>
         <releases>
            <enabled>false</enabled>
         </releases>
         <snapshots>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
         </snapshots>
      </repository>
      <repository>
         <id>oss.sonatype.org/jboss-snapshots</id>
         <name>JBoss (Nexus) Snapshots Repository</name>
         <url>http://oss.sonatype.org/content/repositories/jboss-snapshots</url>
         <releases>
            <enabled>false</enabled>
         </releases>
         <snapshots>
            <enabled>true</enabled>
            <updatePolicy>never</updatePolicy>
         </snapshots>
      </repository>
   </repositories>

   <properties>
      <atinject.api.version>1</atinject.api.version>
      <ejb.api.version>3.1.0</ejb.api.version>
      <jsr250.api.version>1.0</jsr250.api.version>
      <jpa.api.version>1.0</jpa.api.version>
      <validation.api.version>1.0.0.GA</validation.api.version>
      <servlet.api.version>2.5</servlet.api.version>
      <jsp.api.version>2.1</jsp.api.version>
      <jstl.api.version>1.2</jstl.api.version>
      <jta.api.version>1.0.1B</jta.api.version>
      <jms.api.version>1.1</jms.api.version>
      <uel.api.version>2.1.2-b04</uel.api.version>
      <jsf.api.version>1.2_13</jsf.api.version>
      <jaxws.api.version>2.1</jaxws.api.version>
      <interceptor.api.version>1.1</interceptor.api.version>
   </properties>

   <dependencyManagement>
      <dependencies>

         <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>jsr250-api</artifactId>
            <version>${jsr250.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
            <version>${jpa.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>${validation.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>${atinject.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>${servlet.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>${jsp.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
            <version>${jstl.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>jta</artifactId>
            <version>${jta.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.jms</groupId>
            <artifactId>jms</artifactId>
            <version>${jms.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.el</groupId>
            <artifactId>el-api</artifactId>
            <version>${uel.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.faces</groupId>
            <artifactId>jsf-api</artifactId>
            <version>${jsf.api.version}</version>
         </dependency>

         <!--
            TODO replace with javax.ejb:ejb-api:3.1 once the JCP/Sun
            publish this
         -->
         <dependency>
            <groupId>org.jboss.ejb3</groupId>
            <artifactId>jboss-ejb3-api</artifactId>
            <version>${ejb.api.version}</version>
            <exclusions>
               <exclusion>
                  <artifactId>jboss-jaxrpc</artifactId>
                  <groupId>jbossws</groupId>
               </exclusion>
               <exclusion>
                  <artifactId>jboss-transaction-api</artifactId>
                  <groupId>org.jboss.javaee</groupId>
               </exclusion>
               <exclusion>
                  <artifactId>jboss-jaxrpc</artifactId>
                  <groupId>jboss.jbossws</groupId>
               </exclusion>
            </exclusions>
         </dependency>

         <!--
            TODO replace with javax.interceptor:interceptor-api:3.1 once
            the JCP/Sun publish this
         -->
         <dependency>
            <groupId>org.jboss.interceptor</groupId>
            <artifactId>jboss-interceptor-api</artifactId>
            <version>${interceptor.api.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.enterprise</groupId>
            <artifactId>cdi-api</artifactId>
            <version>${project.version}</version>
         </dependency>

         <dependency>
            <groupId>org.jboss.weld</groupId>
            <artifactId>weld-api</artifactId>
            <version>${project.version}</version>
         </dependency>

         <dependency>
            <groupId>org.jboss.weld</groupId>
            <artifactId>weld-spi</artifactId>
            <version>${project.version}</version>
         </dependency>

         <dependency>
            <groupId>javax.xml.ws</groupId>
            <artifactId>jaxws-api</artifactId>
            <version>${jaxws.api.version}</version>
         </dependency>

         <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
         </dependency>

      </dependencies>
   </dependencyManagement>

  <scm>
    <connection>scm:svn:http://anonsvn.jboss.org/repos/weld/api/tags/1.0</connection>
    <developerConnection>scm:svn:https://svn.jboss.org/repos/weld/api/tags/1.0</developerConnection>
    <url>http://fisheye.jboss.org/browse/Weld/api/tags/1.0/build/tags/weld-parent-6/weld-api-bom</url>
  </scm>
</project>
