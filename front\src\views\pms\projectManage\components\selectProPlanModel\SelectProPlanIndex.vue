<template>
  <BasicModal
    title="选择项目计划"
    width="1000px"
    :height="400"
    @register="registerModel"
    @cancel="cancelHandle"
    @ok="okHandle"
  >
    <SelectProPlanMain v-if="state.visible" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import { reactive } from 'vue';
import SelectProPlanMain from './SelectProPlanMain.vue';

const state = reactive({
  visible: false,
});

const [registerModel, { closeModal }] = useModalInner((openProps) => {
  console.log(openProps);
  state.visible = true;
});

function cancelHandle() {

}

function okHandle() {

}
</script>

<style scoped>

</style>
