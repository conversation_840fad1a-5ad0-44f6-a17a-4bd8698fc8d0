package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.lang.String;

import java.util.List;

/**
 * BudgetExpendForm VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetExpendFormVO对象", description = "预算支出表单")
@Data
public class BudgetExpendFormVO extends ObjectVO implements Serializable {


    /**
     * 成本支出编码
     */
    @ApiModelProperty(value = "成本支出编码")
    private String number;

    @ApiModelProperty(value = "流程审批对象")
    private String name;


    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String expenseAccountName;


    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    private String expenseAccountNumber;


    /**
     * 科目Id
     */
    @ApiModelProperty(value = "科目Id")
    private String expenseAccountId;


    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本中心id")
    private String costCenterId;


    /**
     * 成本中心id
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    private Date occurrenceTime;


    /**
     * 发生人
     */
    @ApiModelProperty(value = "发生人")
    private String occurrencePerson;

    /**
     * 发生人
     */
    @ApiModelProperty(value = "发生人")
    private String occurrenceName;


    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    private BigDecimal expendMoney;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectName;



}
