package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/6/3
 * @description:
 */
@Data
public class ProjectSimpleVO extends ObjectVO {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    /**
     * 项目结束日期
     */
    @ApiModelProperty(value = "项目结束日期")
    private Date projectEndTime;

    /**
     * 项目开始日期
     */
    @ApiModelProperty(value = "项目开始日期")
    private Date projectStartTime;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String pm;

    /**
     * 项目经理id
     */
    @ApiModelProperty(value = "项目经理id")
    private String pmId;
}
