<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
<!--
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>23</version>
    <relativePath />
    <!-- no parent resolution -->
  </parent>
  <groupId>org.apache.zookeeper</groupId>
  <artifactId>parent</artifactId>
  <packaging>pom</packaging>
  <!-- to change version: mvn -\-batch-mode release:update-versions -DdevelopmentVersion=3.6.0-SNAPSHOT -->
  <version>3.6.2</version>
  <name>Apache ZooKeeper</name>
  <description>
    ZooKeeper is a centralized service for maintaining configuration information, naming,
    providing distributed synchronization, and providing group services. All of these kinds
    of services are used in some form or another by distributed applications. Each time they
    are implemented there is a lot of work that goes into fixing the bugs and race conditions
    that are inevitable. Because of the difficulty of implementing these kinds of services,
    applications initially usually skimp on them ,which make them brittle in the presence of
    change and difficult to manage. Even when done correctly, different implementations of
    these services lead to management complexity when the applications are deployed.
  </description>
  <url>http://zookeeper.apache.org</url>
  <inceptionYear>2008</inceptionYear>
  <!-- Set here so we can consistently use the correct name, even on branches with
       an ASF parent pom older than v15. Also uses the url from v18.
    -->
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>zookeeper-docs</module>
    <module>zookeeper-jute</module>
    <module>zookeeper-server</module>
    <module>zookeeper-metrics-providers</module>
    <module>zookeeper-client</module>
    <module>zookeeper-recipes</module>
    <module>zookeeper-assembly</module>
    <module>zookeeper-compatibility-tests</module>
  </modules>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/zookeeper.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/zookeeper.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf/zookeeper.git</url>
    <tag>release-3.6.2-1</tag>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://issues.apache.org/jira/browse/ZOOKEEPER</url>
  </issueManagement>
  <ciManagement>
    <system>jenkins</system>
    <url>https://ci-hadoop.apache.org/view/ZooKeeper/</url>
  </ciManagement>
  <mailingLists>
    <mailingList>
      <name>User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/zookeeper-user/</archive>
    </mailingList>
    <mailingList>
      <name>Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/zookeeper-dev/</archive>
    </mailingList>
    <mailingList>
      <name>Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/zookeeper-commits/</archive>
    </mailingList>
    <mailingList>
      <name>Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
    <mailingList>
      <name>Notifications List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>tdunning</id>
      <name>Ted Dunning	</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>camille</id>
      <name>Camille Fournier</name>
      <email><EMAIL></email>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>phunt</id>
      <name>Patrick Hunt</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>fpj</id>
      <name>Flavio Junqueira</name>
      <email><EMAIL></email>
      <timezone>+0</timezone>
    </developer>
    <developer>
      <id>ivank</id>
      <name>Ivan Kelly</name>
      <email><EMAIL></email>
      <timezone>+2</timezone>
    </developer>
    <developer>
      <id>mahadev</id>
      <name>Mahadev Konar</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>michim</id>
      <name>Michi Mutsuzaki</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>cnauroth</id>
      <name>Chris Nauroth</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>breed</id>
      <name>Benjamin Reed</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>henry</id>
      <name>Henry Robinson</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>rgs</id>
      <name>Raul Gutierrez Segales</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>rakeshr</id>
      <name>Rakesh Radhakrishnan</name>
      <email><EMAIL></email>
      <timezone>+5:30</timezone>
    </developer>
    <developer>
      <id>hanm</id>
      <name>Michael Han</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>gkesavan</id>
      <name>Giridharan Kesavan</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>akornev</id>
      <name>Andrew Kornev</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>shralex</id>
      <name>Alex Shraer</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>thawan</id>
      <name>Thawan Kooburat</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>hdeng</id>
      <name>Hongchao Deng</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>arshad</id>
      <name>Mohammad Arshad</name>
      <email><EMAIL></email>
      <timezone>+5:30</timezone>
    </developer>
    <developer>
      <id>afine</id>
      <name>Abraham Fine</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>andor</id>
      <name>Andor Molnar</name>
      <email><EMAIL></email>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>lvfangmin</id>
      <name>Allan Lyu</name>
      <email><EMAIL></email>
      <timezone>-8</timezone>
    </developer>
    <developer>
        <id>eolivelli</id>
        <name>Enrico Olivelli</name>
        <email><EMAIL></email>
        <timezone>+1</timezone>
    </developer>
  </developers>

  <profiles>
    <profile>
      <id>full-build</id>
      <modules>
        <module>zookeeper-it</module>
        <module>zookeeper-contrib</module>
      </modules>
    </profile>
    <profile>
      <id>fatjar</id>
      <modules>
        <module>zookeeper-it</module>
        <module>zookeeper-contrib</module>
      </modules>
    </profile>
    <profile>
      <id>java-build</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
        <id>apache-release</id>
        <build>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.apache.resources</groupId>
                            <artifactId>apache-source-release-assembly-descriptor</artifactId>
                            <version>1.0.6</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>source-release-assembly-tar-gz</id>
                            <phase>initialize</phase>
                            <goals>
                                <goal>single</goal>
                            </goals>
                            <configuration>
                                <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                                <descriptorRefs>
                                    <!-- defined in Apache Parent Pom -->
                                    <descriptorRef>${sourceReleaseAssemblyDescriptor}</descriptorRef>
                                </descriptorRefs>
                                <finalName>apache-zookeeper-${project.version}</finalName>
                                <appendAssemblyId>false</appendAssemblyId>
                                <formats>
                                    <format>tar.gz</format>
                                </formats>
                                <tarLongFileMode>posix</tarLongFileMode>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </build>
    </profile>
    <profile>
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <properties>
        <maven.compiler.release>8</maven.compiler.release>
      </properties>
    </profile>
    <profile>
      <id>jdk-release-flag</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <maven.compiler.release>8</maven.compiler.release>
      </properties>
    </profile>
  </profiles>

  <properties>
    <!-- maven properties -->
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <dependency.locations.enabled>false</dependency.locations.enabled>

    <surefire-forkcount>8</surefire-forkcount>

    <!-- dependency versions -->
    <slf4j.version>1.7.25</slf4j.version>
    <audience-annotations.version>0.5.0</audience-annotations.version>
    <jmockit.version>1.48</jmockit.version>
    <junit.version>4.12</junit.version>
    <log4j.version>1.2.17</log4j.version>
    <mockito.version>2.27.0</mockito.version>
    <hamcrest.version>1.3</hamcrest.version>
    <commons-cli.version>1.2</commons-cli.version>
    <netty.version>4.1.50.Final</netty.version>
    <jetty.version>9.4.24.v20191120</jetty.version>
    <jackson.version>2.10.3</jackson.version>
    <json.version>1.1.1</json.version>
    <jline.version>2.14.6</jline.version>
    <snappy.version>1.1.7</snappy.version>
    <kerby.version>2.0.0</kerby.version>
    <bouncycastle.version>1.60</bouncycastle.version>
    <commons-collections.version>3.2.2</commons-collections.version>
    <commons-lang.version>2.6</commons-lang.version>
    <dropwizard.version>3.2.5</dropwizard.version>
    <spotbugsannotations.version>4.0.2</spotbugsannotations.version>
    <checkstyle.version>8.17</checkstyle.version>

    <!-- parameter to pass to C client build -->
    <c-client-openssl>yes</c-client-openssl>

  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-all</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>${commons-collections.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-lang</groupId>
        <artifactId>commons-lang</artifactId>
        <version>${commons-lang.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.yetus</groupId>
        <artifactId>audience-annotations</artifactId>
        <version>${audience-annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-cli</groupId>
        <artifactId>commons-cli</artifactId>
        <version>${commons-cli.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kerby</groupId>
        <artifactId>kerb-core</artifactId>
        <version>${kerby.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.kerby</groupId>
        <artifactId>kerb-simplekdc</artifactId>
        <version>${kerby.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.kerby</groupId>
        <artifactId>kerby-config</artifactId>
        <version>${kerby.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcpkix-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${slf4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>*</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jmockit</groupId>
        <artifactId>jmockit</artifactId>
        <version>${jmockit.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler</artifactId>
        <version>${netty.version}</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>${netty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>${dropwizard.version}</version>
        <exclusions>
           <exclusion>
              <groupId>org.slf4j</groupId>
              <artifactId>slf4j-api</artifactId>
           </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.googlecode.json-simple</groupId>
        <artifactId>json-simple</artifactId>
        <version>${json.version}</version>
        <exclusions>
          <exclusion>
              <groupId>junit</groupId>
              <artifactId>junit</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>jline</groupId>
        <artifactId>jline</artifactId>
        <version>${jline.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-annotations</artifactId>
        <version>${spotbugsannotations.version}</version>
        <scope>provided</scope>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>org.xerial.snappy</groupId>
        <artifactId>snappy-java</artifactId>
        <version>${snappy.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
             <showWarnings>true</showWarnings>
             <compilerArgs>
               <compilerArg>-Werror</compilerArg>
               <compilerArg>-Xlint:deprecation</compilerArg>
               <compilerArg>-Xlint:unchecked</compilerArg>
               <compilerArg>-Xlint:-options</compilerArg>
               <compilerArg>-Xdoclint:-missing</compilerArg>
               <!-- https://issues.apache.org/jira/browse/MCOMPILER-205 -->
               <compilerArg>-Xpkginfo:always</compilerArg>
              </compilerArgs>
            </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.1.0</version>
          <configuration>
            <archive>
              <manifestEntries>
                <Implementation-Build>${mvngit.commit.id}</Implementation-Build>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.0.1</version>
          <configuration>
            <doclint>none</doclint>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-plugin</artifactId>
          <version>1.11.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.8</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>1.6.0</version>
        </plugin>
        <plugin>
          <groupId>com.github.koraktor</groupId>
          <artifactId>mavanagaiata</artifactId>
          <version>0.9.4</version>
          <configuration>
            <skipNoGit>true</skipNoGit>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>net.nicoulaj.maven.plugins</groupId>
          <artifactId>checksum-maven-plugin</artifactId>
          <version>1.8</version>
        </plugin>
        <plugin>
          <groupId>org.openclover</groupId>
          <artifactId>clover-maven-plugin</artifactId>
          <version>4.3.1</version>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>4.0.0</version>
          <configuration>
            <excludeFilterFile>excludeFindBugsFilter.xml</excludeFilterFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.owasp</groupId>
          <artifactId>dependency-check-maven</artifactId>
          <version>5.3.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.1.0</version>
          <dependencies>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>${checkstyle.version}</version>
            </dependency>
          </dependencies>
          <configuration>
            <configLocation>checkstyle-strict.xml</configLocation>
            <suppressionsLocation>checkstyleSuppressions.xml</suppressionsLocation>
            <encoding>UTF-8</encoding>
            <consoleOutput>true</consoleOutput>
            <failOnViolation>true</failOnViolation>
            <includeResources>false</includeResources>
            <includeTestResources>false</includeTestResources>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
          </configuration>
          <executions>
            <execution>
              <id>checkstyle</id>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <!-- we don't need this plugin-->
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <executions>
            <execution>
               <id>process-resource-bundles</id>
               <phase>none</phase>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>com.github.koraktor</groupId>
        <artifactId>mavanagaiata</artifactId>
        <executions>
          <execution>
            <id>find-current-git-revision</id>
            <goals>
              <goal>commit</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.openclover</groupId>
        <artifactId>clover-maven-plugin</artifactId>
        <configuration>
          <generateHtml>true</generateHtml>
          <generateXml>true</generateXml>
          <includes>
            <include>org/apache/zookeeper/**/*</include>
          </includes>
          <excludes>
            <exclude>org/apache/zookeeper/version/**/*</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <phase>pre-site</phase>
            <goals>
              <goal>instrument</goal>
              <goal>aggregate</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <exportAntProperties>true</exportAntProperties>
              <target>
                <property environment="env" />
                <exec executable="hostname" outputproperty="host.name" />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>Jar Tests Package</id>
            <phase>package</phase>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <includes>
                <include>org/**</include>
                <include>META_INF/**</include>
              </includes>
              <skipIfEmpty>true</skipIfEmpty>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
          <execution>
            <id>aggregate</id>
            <phase>site</phase>
            <goals>
              <goal>aggregate</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <overview>zookeeper-server/src/main/resources/overview.html</overview>
          <excludePackageNames>*.recipes.*</excludePackageNames>
        </configuration>
      </plugin>
      <plugin>
        <!-- Maven's deploy plugin only creates checksums during the deployment of the jar artifacts to repo. -->
        <!-- We also want to sign tarballs. Nicoulaj's plugin is the recommended solution by the community.   -->
        <groupId>net.nicoulaj.maven.plugins</groupId>
        <artifactId>checksum-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>artifacts</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <algorithms>
            <algorithm>SHA-512</algorithm>
          </algorithms>
          <appendFilename>true</appendFilename>
        </configuration>
      </plugin>

      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <configuration>
          <format>ALL</format>
          <failBuildOnCVSS>0</failBuildOnCVSS>
          <suppressionFiles>
            <suppressionsFile>owaspSuppressions.xml</suppressionsFile>
          </suppressionFiles>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>**/log4j.properties</exclude>
            <exclude>**/README.md</exclude>
            <exclude>**/findbugsExcludeFile.xml</exclude>
            <exclude>**/checkstyle-noframes-sorted.xsl</exclude>
            <exclude>**/configure.ac</exclude>
            <exclude>**/Makefile.am</exclude>
            <exclude>conf/zoo_sample.cfg</exclude>
            <exclude>conf/configuration.xsl</exclude>
            <exclude>.travis.yml</exclude>
            <exclude>excludeFindBugsFilter.xml</exclude>
            <exclude>README_packaging.md</exclude>
            <exclude>src/main/resources/markdown/skin/*</exclude>
            <exclude>src/main/resources/markdown/html/*</exclude>
            <exclude>src/main/resources/markdown/images/*</exclude>
            <!-- contrib -->
            <exclude>**/JMX-RESOURCES</exclude>
            <exclude>**/src/main/resources/mainClasses</exclude>
            <exclude>**/Changes</exclude>
            <exclude>**/MANIFEST</exclude>
            <exclude>**/src/test/zoo.cfg</exclude>
            <exclude>**/src/main/resources/webapp/org/apache/zookeeper/graph/resources/*</exclude>
            <exclude>**/src/main/java/com/nitido/utils/toaster/Toaster.java</exclude>
            <exclude>**/TODO</exclude>
            <!-- c client -->
            <exclude>**/acinclude.m4</exclude>
            <exclude>**/aminclude.am</exclude>
            <exclude>**/src/hashtable/*</exclude>
            <exclude>**/include/winconfig.h</exclude>
            <exclude>**/tests/wrappers.opt</exclude>
            <exclude>**/tests/zoo.cfg</exclude>
            <exclude>**/tests/wrappers-mt.opt</exclude>
            <exclude>**/c-doc.Doxyfile</exclude>
          </excludes>
          <consoleOutput>true</consoleOutput>
        </configuration>
      </plugin>
      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <inherited>false</inherited>
          <configuration>
            <!-- update the version inside the C sources -->
            <preparationGoals>clean install -DskipTests antrun:run@replace-cclient-files-during-release scm:add@add-cclient-files-during-release scm:checkin@commit-cclient-files-during-release</preparationGoals>
            <completionGoals>clean install -DskipTests antrun:run@replace-cclient-files-during-release scm:add@add-cclient-files-during-release scm:checkin@commit-cclient-files-during-release</completionGoals>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-plugin</artifactId>
          <inherited>false</inherited>
          <executions>
             <execution>
               <id>add-cclient-files-during-release</id>
               <phase>none</phase>
               <goals>
                 <goal>add</goal>
               </goals>
               <configuration>
                 <pushChanges>false</pushChanges>
                 <includes>zookeeper-client/zookeeper-client-c/CMakeLists.txt,zookeeper-client/zookeeper-client-c/configure.ac,zookeeper-client/zookeeper-client-c/include/zookeeper_version.h</includes>
               </configuration>
            </execution>
            <execution>
               <id>commit-cclient-files-during-release</id>
               <phase>none</phase>
               <goals>
                   <!-- git commit -->
                <goal>checkin</goal>
              </goals>
              <configuration>
                 <pushChanges>false</pushChanges>
                 <message>Prepared ${project.version}</message>
              </configuration>
            </execution>
        </executions>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>${project.basedir}src/main/java/resources</directory>
        <excludes>
          <exclude>**/*.*</exclude>
        </excludes>
      </resource>
    </resources>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.openclover</groupId>
        <artifactId>clover-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

</project>
