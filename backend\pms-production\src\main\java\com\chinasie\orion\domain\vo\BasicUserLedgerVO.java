package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * BasicUserLedger VO对象
 *
 * <AUTHOR>
 * @since 2024-09-11 09:54:35
 */
@ApiModel(value = "BasicUserLedgerVO对象", description = "技术支持人员台账记录")
@Data
public class BasicUserLedgerVO extends  ObjectVO   implements Serializable{

    /**
     * 技术支持人员id
     */
    @ApiModelProperty(value = "技术支持人员id")
    private String basicUserId;


    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 用人部门
     */
    @ApiModelProperty(value = "用人部门")
    private String departmentName;


    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    private String instituteName;


    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    private Date addUnittime;


    /**
     * 到岗时间
     */
    @ApiModelProperty(value = "到岗时间")
    private Date addWorkTime;




}
