<script setup lang="ts">
import {
  Layout,
  OrionTable,
  BasicTableAction,
  IOrionTableActionItem,
  BasicButton,
  BasicButtonGroup,
  Layout2,
  downloadByData,
  isPower,
} from 'lyra-component-vue3';
import {
  computed,
  h, reactive, ref, Ref, watchEffect,
} from 'vue';
import { Modal, RangePicker, Space } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { cloneDeep, get as loadGet, isEmpty } from 'lodash-es';
import MoneyRow from '../../components/MoneyRow.vue';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const tabsIndex = ref(0);
const rowTotalMoney = reactive([
  {
    key: 'number',
    title: '支付条数',
    value: '',
    suffix: '个',
  },
  {
    key: 'money',
    title: '订单金额',
    value: '',
    suffix: '元',
  },
]);
const rowMxMoney = reactive([
  {
    key: 'number',
    title: '支付条数',
    value: '',
    suffix: '个',
  },
  {
    key: 'money',
    title: '订单金额',
    value: '',
    suffix: '元',
  },
]);

const router = useRouter();
const tableRef: Ref = ref();
const powerData = ref([]);

const pageSearchConditions1 = ref(null);
const pageSearchConditions2 = ref(null);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['contractName', 'orderNumber'],
  filterConfig: {
    fields: [
      {
        field: 'orderNumber',
        fieldName: '订单编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractName',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'enterpriseName',
        fieldName: '企业名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'orderTime',
        fieldName: '下单时间',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'orderPlacer',
        fieldName: '下单人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'orderPayDay',
        fieldName: '订单待支付',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      width: 280,
    },
    {
      title: 'PO订单号',
      dataIndex: 'poOrderNumber',
      width: 200,
    },
    {
      title: '订单待支付',
      dataIndex: 'orderPayDay',
      width: 130,
    },
    {
      title: '电商渠道订单号',
      dataIndex: 'commerceChannelOrderNumber',
      width: 150,
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      width: 220,
    },
    {
      title: '供应商编码',
      dataIndex: 'contractId',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'contractName',
      width: 220,
    },
    {
      title: '下单人',
      dataIndex: 'orderPlacer',
      width: 150,
    },
    {
      title: '下单人电话',
      dataIndex: 'orderPhoneNumber',
      width: 150,
    },
    {
      title: '下单时间',
      dataIndex: 'orderTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '对账人',
      dataIndex: 'reconciler',
      width: 150,
    },
    {
      title: '收货负责人',
      dataIndex: 'consignee',
      width: 150,
    },
    {
      title: '收货审核人',
      dataIndex: 'receiptReviewer',
      width: 150,
    },
    {
      title: '支付负责人',
      dataIndex: 'paymentManager',
      width: 150,
    },
    {
      title: '验收方式',
      dataIndex: 'acceptanceMethod',
      width: 100,
    },
    {
      title: '结算方式',
      dataIndex: 'settlementMethod',
      width: 100,
    },
    {
      title: '要求到货日期',
      dataIndex: 'requestDeliveryDate',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单总金额',
      dataIndex: 'totalOrderAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '订单确认时间',
      dataIndex: 'orderConfirmationTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单审批时间',
      dataIndex: 'orderApprovalTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '支付时间',
      dataIndex: 'paidTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '开票时间',
      dataIndex: 'invoicingTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请开票时间',
      dataIndex: 'applicationForInvoicingTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '对账确认时间',
      dataIndex: 'reconciliationConfirmationTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '对账申请时间',
      dataIndex: 'reconciliationApplicationTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '发货耗时',
      dataIndex: 'usedTime',
      width: 100,
    },
    {
      title: '订单最后一次交货时间',
      dataIndex: 'timeOfDelivery',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单最后一次确认收货时间',
      dataIndex: 'timeOfLastReceipt',
      width: 200,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单状态',
      dataIndex: 'orderState',
      width: 100,
    },
    {
      title: '退货金额',
      dataIndex: 'returnAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  // 总表接口位置
  api: (params:Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['orderTime'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'orderTime') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    pageSearchConditions1.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/pms/ncfFormPurchOrderCollect/page').fetch({
      ...params,
      ...newSearchConditions,
    }, '', 'POST');
  },
};
const tableOptions2 = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['contractName', 'orderNumber'],
  filterConfig: {
    fields: [
      {
        field: 'orderNumber',
        fieldName: '订单编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractName',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'enterpriseName',
        fieldName: '企业名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'orderTime',
        fieldName: '下单时间',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
        optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
          return h(RangePicker, {
            style: {
              width: '100%',
            },
            onChange(date: any) {
              filterMethods.setFieldValue(filterItem.field, date, groupRelation);
            },
            valueFormat: 'YYYY-MM-DD',
          });
        },
      },
      {
        field: 'orderPlacer',
        fieldName: '下单人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'orderPayDay',
        fieldName: '订单待支付',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
    },
    {
      title: '订单待支付',
      dataIndex: 'orderPayDay',
    },
    {
      title: '供应商名称',
      dataIndex: 'contractName',
    },
    {
      title: '退货金额',
      dataIndex: 'returnAmount',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
    },
    {
      title: '下单人',
      dataIndex: 'orderPlacer',
    },
    {
      title: '下单时间',
      dataIndex: 'orderTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单最后一次交货时间',
      dataIndex: 'timeOfDelivery',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单最后一次确认收货时间',
      dataIndex: 'timeOfLastReceipt',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '发货耗时',
      dataIndex: 'usedTime',
    },
    {
      title: '对账申请时间',
      dataIndex: 'reconciliationApplicationTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '对账确认时间',
      dataIndex: 'reconciliationConfirmationTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请开票时间',
      dataIndex: 'applicationForInvoicingTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '开票时间',
      dataIndex: 'invoicingTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '支付时间',
      dataIndex: 'paidTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: 'PO订单号',
      dataIndex: 'poOrderNumber',
    },
    {
      title: '供应商编码',
      dataIndex: 'contractId',
    },
    {
      title: '支付负责人',
      dataIndex: 'paymentManager',
    },
    {
      title: '验收方式',
      dataIndex: 'acceptanceMethod',
    },
    {
      title: '订单总金额（含税含费）',
      dataIndex: 'orderTotalAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '采购申请号',
      dataIndex: 'purchReqDocCode',
    },
    {
      title: 'PR行项目',
      dataIndex: 'prProjectId',
    },
    {
      title: '订单状态',
      dataIndex: 'orderState',
    },
    {
      title: '商品后台类目',
      dataIndex: 'commodityBackgroundCategory',
    },
    {
      title: '单品编码',
      dataIndex: 'itemCoding',
    },
    {
      title: '单品名称',
      dataIndex: 'itemName',
    },
    {
      title: '电商订单编号',
      dataIndex: 'eCommerceOrderNumber',
    },
    {
      title: '子订单号',
      dataIndex: 'suborderNumber',
    },
    {
      title: '订单金额',
      dataIndex: 'orderAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '应付金额',
      dataIndex: 'amountPayable',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '结算状态',
      dataIndex: 'settlementStatus',
    },
    {
      title: '订单确认时间',
      dataIndex: 'orderConfirmationTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '订单审批时间',
      dataIndex: 'orderApprovalTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '电商渠道订单号',
      dataIndex: 'commerceChannelOrderNumber',
    },
    {
      title: '对账人',
      dataIndex: 'reconciler',
    },
    {
      title: '收货负责人',
      dataIndex: 'consignee',
    },
    {
      title: '收货审核人',
      dataIndex: 'receiptReviewer',
    },
    {
      title: '结算方式',
      dataIndex: 'settlementMethod',
    },
    {
      title: '要求到货日期',
      dataIndex: 'requestDeliveryDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '下单人电话',
      dataIndex: 'orderPhoneNumber',
    },
  ],
  // 明细表接口位置
  api: (params:Record<string, any>) => {
    const originSearchConditions = loadGet(cloneDeep(params), 'searchConditions', []);
    const query = {};
    const searchConditions = originSearchConditions.reduce((prev, cur) => {
      for (let i = 0; i < cur.length; i++) {
        const single = cur[i];
        const filedProp = loadGet(single, 'field', '');
        if (['orderTime'].includes(filedProp)) {
          const [first, second] = loadGet(single, 'values', []);
          if (filedProp === 'orderTime') {
            Object.assign(query, {
              startDate: first,
              endDate: second,
            });
          }
          cur.splice(i, 1, undefined);
        }
      }
      const lastCur = cur.filter(Boolean);
      if (lastCur.length) {
        return [...prev, lastCur];
      }
      return prev;
    }, []);
    const newSearchConditions = {
      searchConditions: searchConditions.length ? searchConditions : [],
      query: isEmpty(query) ? undefined : query,
    };
    pageSearchConditions2.value = params.searchConditions ? newSearchConditions : null;
    return new Api('/pms/ncfFormPurchOrderDetail/page').fetch({
      ...params,
      ...newSearchConditions,
    }, '', 'POST');
  },
};

const pageMenu = computed(() => [
  {
    name: '总订单',
    id: 'total',
    code: 'PMS_JCDDGL_container_01_02',
  },
  {
    name: '明细订单',
    id: 'detail',
    code: 'PMS_JCDDGL_container_01_03',
  },
].filter((item) => isPower(item.code, powerData)));
const showZExportButton = computed(() => isPower('PMS_JCDDGL_container_01_02_button_01', powerData.value));
const showMXExportButton = computed(() => isPower('PMS_JCDDGL_container_01_03_button_02', powerData.value));

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);
const selectKeys2: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  keys,
}) {
  selectKeys.value = keys; // 导出所选用
}// 表格多选回调
function selectionChange2({
  keys,
}) {
  selectKeys2.value = keys; // 导出所选用
}

// 批量导出按钮事件（接口需加）
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      const idsParams = {
        searchConditions: [
          [
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ],
      };
      downloadByData('/pms/ncfFormPurchOrderCollect/export/excel', {
        ...(ids.length ? idsParams : pageSearchConditions1.value),
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}
function handleDetailExport(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      const idsParams = {
        searchConditions: [
          [
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ],
      };
      downloadByData('/pms/ncfFormPurchOrderDetail/export/excel', {
        ...(ids.length ? idsParams : pageSearchConditions2.value),
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

const actions: IOrionTableActionItem[] = [
  // {
  //   text: '编辑',
  //   event: 'edit',
  // },
  {
    text: '查看',
    event: 'view',
  },
  // {
  //   text: '删除',
  //   event: 'delete',
  // },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      // openFormDrawer(NcfFormPurchOrderEdit, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'CollectionOrderItem',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/ncfFormPurchOrder').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const handleTabsChang = (index) => {
  tabsIndex.value = index;
};
const getMoney = async () => {
  try {
    const result = await new Api('/pms/ncfFormPurchOrderCollect/getNumMoney').fetch({
      ...(pageSearchConditions1.value || {}),
    }, '', 'POST');

    rowTotalMoney.forEach((item) => {
      item.value = result[item.key];
    });
  } catch (e) {
  }
};
const getContract = async () => {
  try {
    const result = await new Api('/pms/ncfFormPurchOrderDetail/getNumMoney').fetch({
      ...(pageSearchConditions2.value || {}),
    }, '', 'POST');

    rowMxMoney.forEach((item) => {
      item.value = result[item.key];
    });
  } catch (e) {
  }
};
const getPowerDataHandle = (data) => {
  powerData.value = data;
};

watchEffect(() => {
  getMoney();
});
watchEffect(() => {
  if (tabsIndex.value === 1) {
    getContract();
  }
});
</script>

<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    v-get-power="{pageCode: 'collectionOrderManage',getPowerDataHandle}"
    :tabs="pageMenu"
    :options="{ body: { scroll: true } }"
    @tabsChange="handleTabsChang"
  >
    <template v-if="tabsIndex===0">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
        :onSelectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <Space :size="12">
            <BasicButton
              v-if="showZExportButton"
              type="primary"
              icon="sie-icon-daochu"
              @click="exportTableData(selectKeys)"
            >
              导出全部
            </BasicButton>
            <MoneyRow :data="rowTotalMoney" />
          </Space>
        </template>
        <template #actions="{record}">
          <BasicTableAction
            :actions="actions"
            :record="record"
            @actionClick="actionClick($event,record)"
          />
        </template>
      </OrionTable>
    </template>
    <template v-if="tabsIndex===1">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions2"
        :onSelectionChange="selectionChange2"
      >
        <template #toolbarLeft>
          <Space :size="12">
            <BasicButton
              v-if="showMXExportButton"
              type="primary"
              icon="sie-icon-daochu"
              @click="handleDetailExport(selectKeys2)"
            >
              导出全部
            </BasicButton>
            <MoneyRow :data="rowMxMoney" />
          </Space>
        </template>
        <template #actions="{record}">
          <BasicTableAction
            :actions="actions"
            :record="record"
            @actionClick="actionClick($event,record)"
          />
        </template>
      </OrionTable>
    </template>
  </Layout2>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
