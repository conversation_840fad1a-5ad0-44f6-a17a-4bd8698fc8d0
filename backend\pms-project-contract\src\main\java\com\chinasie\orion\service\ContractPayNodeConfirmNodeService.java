package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ContractPayNodeConfirmNodeDTO;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirmNode;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmNodeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ContractPayNodeConfirmNode 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 21:48:50
 */
public interface ContractPayNodeConfirmNodeService extends OrionBaseService<ContractPayNodeConfirmNode> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractPayNodeConfirmNodeVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmNodeDTO
     */
    ContractPayNodeConfirmNodeVO create(ContractPayNodeConfirmNodeDTO contractPayNodeConfirmNodeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmNodeDTO
     */
    Boolean edit(ContractPayNodeConfirmNodeDTO contractPayNodeConfirmNodeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ContractPayNodeConfirmNodeVO> pages(Page<ContractPayNodeConfirmNodeDTO> pageRequest) throws Exception;

}
