<script setup lang="ts">
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const tableRef:Ref = ref();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  smallSearchField: ['number', 'name'],
  rowSelection: {
    type: 'radio',
  },
  api: (params) => new Api('/pms/scientificResearchDemandDeclare/getLeadPage').fetch({
    ...params,
  }, '', 'POST'),
  columns: [
    {
      title: '线索编号',
      dataIndex: 'number',
    },
    {
      title: '线索名称',
      dataIndex: 'name',
    },

    {
      title: '优先级',
      dataIndex: 'priorityName',
    },
    {
      title: '线索类型',
      dataIndex: 'leadModelName',
    },
    {
      title: '主/子线索',
      dataIndex: 'leadType',
    },
    {
      title: '提出人',
      dataIndex: 'proposeName',
    },

    {
      title: '提出日期',
      dataIndex: 'proposeDate',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },

    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },

  ],
};

defineExpose({
  getSelectRows: computed(() => tableRef.value.getSelectRows),
});
</script>

<template>
  <div style="height: 500px;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
