<template>
  <div class="action-table-icon-container">
    <div class="scan-btn scrollable-content">
      <Button
        @click="setZoom(0.1)"
      >
        <ZoomInOutlined />
        放大
      </Button>
      <Button
        @click="setZoom(-0.1)"
      >
        <ZoomOutOutlined />
        缩小
      </Button>
    </div>
    <div class="action-table-icon">
      <div>
        <CheckCircleFilled
          class="circle2"
        />
        已完成
      </div>
      <div>
        <div
          class="circle3"
        />
        进行中
      </div>
      <div>
        <div
          class="circle1"
        />
        未开始
      </div>
    </div>
    <div class="flow-wrapper">
      <BasicScrollbar>
        <Flow
          v-if="wrapperRef"
          class="action-table-antv"
          :projectId="projectId"
          :wrapper="wrapperRef"
          :level="level"
        />
      </BasicScrollbar>

      <div ref="wrapperRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircleFilled, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons-vue';
import { inject, ref, provide } from 'vue';
import { BasicScrollbar } from 'lyra-component-vue3';
import { Button } from 'ant-design-vue';
import Flow from './flow/index.vue';

// const projectId = inject('projectId');
const projectId = inject('projectIdNew');
const wrapperRef = ref<HTMLElement>();
const level = ref<number>(1);
const random = ref(Math.random());
provide('mathRandom', random);
provide('levelZoom', level);
// 缩放
function setZoom(number:number) {
  // event实例
  // number是放大或缩小
  random.value = Math.random();
  level.value = number;
}

</script>

<style lang="less" scoped>
.action-table-icon-container{
  width: 100%;
  position: relative;
  height: 670px;
  .scan-btn.scrollable-content{
    padding: 0 10px;
  }
  .action-table-icon{
    width: 300px;
    display: flex;
    justify-content: space-around;
    position: absolute;
    right: 0;
    top: -8px;
    >div{
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      .circle1{
        width: 15px;
        height: 15px;
        background: #fff;
        border:1.5px solid #999;
        border-radius: 50%;
        margin-right: 5px;
      }
      .circle2{
        font-size: 16px;
        color:#1890ff;
        border-radius: 50%;
        margin-right: 5px;
      }
      .circle3{
        width: 15px;
        height: 15px;
        background: #fff;
        border:1.5px solid #f09509;
        border-radius: 50%;
        margin-right: 5px;
      }
    }
  }
}

.flow-wrapper {
  width: 100%;
  height: 670px;
  position: relative;
  z-index: 10;
}

.action-table-antv {
  min-width: 1460px;
  height: 670px
}
</style>
