package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.job.SaveJobManageDTO;
import com.chinasie.orion.domain.dto.jobDown.JobImplDownDTO;
import com.chinasie.orion.domain.dto.jobDown.JobPrepDownDTO;
import com.chinasie.orion.domain.dto.tree.*;
import com.chinasie.orion.domain.entity.RelationOrgToJob;
import com.chinasie.orion.domain.dto.RelationOrgToJobDTO;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.RelationOrgToJobVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.*;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * RelationOrgToJob 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
public interface RelationOrgToJobService  extends  OrionBaseService<RelationOrgToJob>  {


        /**
         *  详情
         *
         * * @param id
         */
    RelationOrgToJobVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param relationOrgToJobDTO
         */
        String create(RelationOrgToJobDTO relationOrgToJobDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param relationOrgToJobDTO
         */
        Boolean edit(RelationOrgToJobDTO relationOrgToJobDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
    Page<RelationOrgToJobVO> pages( Page<RelationOrgToJobDTO> pageRequest)throws Exception;


        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
    void  setEveryName(List<RelationOrgToJobVO> vos)throws Exception;


    long existCountByRepairOrgIds(List<String> repairOrgIds);

    void delByRepairOrgIdAndIdList(String orgId, List<String> delNumberList);

    void relationSaveOrUpdate(String orgId, List<String> jobNumberList);

    /**
     *  获取工单结构树
     * @param searchVO
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<JobDetailVO>>>prepareTree(SearchVO searchVO) throws Exception;

    /**
     *  获取工单结构树-实施
     * @param searchVO
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>> implTree(SearchVO searchVO) throws Exception;

    /**
     *  作业编辑-准备
     * @param jobPrepareEditDTO
     * @return
     */
    JobPrepareEditVO prepareJobEdit(JobPrepareEditDTO jobPrepareEditDTO);

    /**
     * 作业编辑-实施
     * @param jobPrepareEditDTO
     * @return
     */
    JobImplEditVO implJobEdit(JobImlEditDTO jobPrepareEditDTO);

    /**
     *  修改开工的数据
     * @param beforeAndAfterFourEditDTO
     * @return
     */
    JobWorkImplEditVO implWokEdit(BeforeAndAfterFourEditDTO beforeAndAfterFourEditDTO);

    /**
     * 作业工单移除
     * @param idList
     * @return
     */
    Boolean jobRemoveBatch(List<String> idList);

    Boolean addBatch(SaveJobManageDTO saveJobManageDTO);

    Boolean assistAddBatch(AssistDTO assistDTO);

    //  下转数据显示
    List<JobDownVO> prepareDownList(JobPrepDownDTO jobPrepDownDTO) throws Exception;

    List<JobDownVO> impleDownList(JobImplDownDTO jobImplDownDTO) throws Exception;

    JobAllEditVO allJobEdit(JobAllEditDTO jobAllEditDTO);
}
