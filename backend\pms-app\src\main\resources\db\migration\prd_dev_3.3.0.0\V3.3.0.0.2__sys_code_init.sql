
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11790551233673695232', 'PR', NULL, '项目评审编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:14:16', '314j1787363372854079488', '2024-05-15 09:17:40', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '52e2cb0e0db848c592d78ead4e33d297', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'1');

INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1790660636213121024', 'number', '9hi11790551233673695232', 'Review', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-15 16:28:59', '314j1000000000000000000', '2024-05-15 16:28:59', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');

INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790551567666122752', '评审', '0', '9hi11790551233673695232', '', 'fixedValue', '1', 'PR', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:15:35', '314j1787363372854079488', '2024-05-15 09:15:35', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790551683550547968', '年', '0', '9hi11790551233673695232', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:16:03', '314j1787363372854079488', '2024-05-15 09:16:03', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790551753536704512', '月', '0', '9hi11790551233673695232', '', 'DATE_YY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:16:20', '314j1787363372854079488', '2024-05-15 09:16:20', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790551857853239296', '间隔', '0', '9hi11790551233673695232', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:16:44', '314j1787363372854079488', '2024-05-15 09:16:44', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790552011205382144', '流水号', '0', '9hi11790551233673695232', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '1', '', '4', 5, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-15 09:17:21', '314j1787363372854079488', '2024-05-15 09:17:21', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);


INSERT INTO `sys_code_rules`(`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11791372282564861952', 'WTK', NULL, '问题库编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:36:49', '314j1787363372854079488', '2024-05-17 15:45:45', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '0bf6057ebff8447189aa52d816515942', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');

INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791372809386221568', 'QC', '0', '9hi11791372282564861952', '', 'fixedValue', '1', 'QC', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:38:55', '314j1787363372854079488', '2024-05-17 15:38:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791373019797676032', '固定符一', '0', '9hi11791372282564861952', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:39:45', '314j1787363372854079488', '2024-05-17 15:39:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791373208730099712', '年份', '0', '9hi11791372282564861952', '', 'DATE_YYYY', '1', '', '0', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:40:30', '314j1787363372854079488', '2024-05-17 15:40:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791373332021665792', '固定符二', '0', '9hi11791372282564861952', '', 'DATE_YYYY', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:40:59', '314j1787363372854079488', '2024-05-17 15:42:48', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, -1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791373566390984704', '流水号', '0', '9hi11791372282564861952', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '1', '', '5', 5, '', '0', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:41:55', '314j1787363372854079488', '2024-05-17 15:41:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment`(`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1791373949259636736', '固定符二', '0', '9hi11791372282564861952', '', 'fixedValue', '1', '-', '0', 4, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-17 15:43:26', '314j1787363372854079488', '2024-05-17 15:43:26', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);

INSERT INTO `sys_code_mapping_relation`(`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1791377865179291648', 'number', '9hi11791372282564861952', 'QuestionLibrary', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-17 15:59:00', '314j1000000000000000000', '2024-05-17 15:59:00', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
