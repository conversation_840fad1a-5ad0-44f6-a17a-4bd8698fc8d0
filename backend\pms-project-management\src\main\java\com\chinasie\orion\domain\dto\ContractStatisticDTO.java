package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@ApiModel(value = "ContractStatisticDTO对象", description = "合同统计")
@Data
@ExcelIgnoreUnannotated
public class    ContractStatisticDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "核能统计map")
    private Map<String,Map<Integer, BigDecimal>> nuclearStatisticMap;

    @ApiModelProperty(value = "非核能统计map")
    private Map<String,Map<Integer, BigDecimal>> notNuclearStatisticMap;

    @ApiModelProperty(value = "历史合同总金额")
    private BigDecimal hisSumAmounts;

    @ApiModelProperty(value = "新签合同总金额")
    private BigDecimal newSumAmounts;


}
