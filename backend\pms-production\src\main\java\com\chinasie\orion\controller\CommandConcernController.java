package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CommandConcernDTO;
import com.chinasie.orion.domain.vo.CommandConcernVO;
import com.chinasie.orion.service.CommandConcernService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * CommandConcern 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@RestController
@RequestMapping("/commandConcern")
@Api(tags = "指挥中心关注")
public class  CommandConcernController  {

    @Autowired
    private CommandConcernService commandConcernService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【指挥中心关注】详情", type = "CommandConcern", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<CommandConcernVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        CommandConcernVO rsp = commandConcernService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param commandConcernDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【指挥中心关注】数据", type = "CommandConcern", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> create(@RequestBody List<CommandConcernDTO> commandConcernDTO) throws Exception {
        Boolean res =  commandConcernService.create(commandConcernDTO);
        return new ResponseDTO<>(res);
    }

    /**
     * 编辑
     *
     * @param commandConcernDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【指挥中心关注】数据", type = "CommandConcern", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  List<CommandConcernDTO> commandConcernDTOList) throws Exception {
        Boolean rsp = commandConcernService.edit(commandConcernDTOList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【指挥中心关注】数据", type = "CommandConcern", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = commandConcernService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【指挥中心关注】数据", type = "CommandConcern", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = commandConcernService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【指挥中心关注】分页数据", type = "CommandConcern", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<CommandConcernVO>> pages(@RequestBody Page<CommandConcernDTO> pageRequest) throws Exception {
        Page<CommandConcernVO> rsp =  commandConcernService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【指挥中心关注】列表数据", type = "CommandConcern", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<CommandConcernVO>> list() throws Exception {
        List<CommandConcernVO> rsp =  commandConcernService.getAll();
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation("指挥中心关注导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【指挥中心关注】导入模板", type = "CommandConcern", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        commandConcernService.downloadExcelTpl(response);
    }

    @ApiOperation("指挥中心关注导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【指挥中心关注】导入", type = "CommandConcern", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = commandConcernService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("指挥中心关注导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【指挥中心关注】导入", type = "CommandConcern", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  commandConcernService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消指挥中心关注导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【指挥中心关注】导入", type = "CommandConcern", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  commandConcernService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("指挥中心关注导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【指挥中心关注】数据", type = "CommandConcern", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        commandConcernService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("判断当前登录用户是否为大修角色")
    @PostMapping(value = "/isManager")
    @LogRecord(success = "【{USER{#logUserId}}】【指挥中心关注】判断当前登录用户是否为大修角色", type = "CommandConcern", subType = "判断当前登录用户是否为大修角色", bizNo = "")
    public ResponseDTO<Boolean> isManager(){
        return new ResponseDTO<>(commandConcernService.isManager());
    }

    @ApiOperation("获取当前大修经理的所有大修的大修轮次")
    @PostMapping(value = "/getRepairRound")
    @LogRecord(success = "【{USER{#logUserId}}】【指挥中心关注】获取当前大修经理的所有大修的大修轮次", type = "CommandConcern", subType = "获取当前大修经理的所有大修的大修轮次", bizNo = "")
    public ResponseDTO<List<String>> getRepairRound(){
        return new ResponseDTO<>(commandConcernService.getRepairRound());
    }
}
