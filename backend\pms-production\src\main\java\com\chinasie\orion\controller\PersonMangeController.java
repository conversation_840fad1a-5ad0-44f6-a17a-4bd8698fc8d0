package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.dto.job.EditContactUserDTO;
import com.chinasie.orion.domain.dto.job.EditNewcomerDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.LeaveDTO;
import com.chinasie.orion.domain.entity.PersonMange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.dto.PersonMangeDTO;
import com.chinasie.orion.domain.vo.PersonMangeVO;

import com.chinasie.orion.service.PersonMangeService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.Map;
import java.util.stream.Collectors;

import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonMange 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@RestController
@RequestMapping("/person-mange")
@Api(tags = "人员管理")
public class PersonMangeController {

    @Autowired
    private PersonMangeService personMangeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【人员管理】详情数据【{{#number}}}】", type = "PersonMange", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<PersonMangeVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PersonMangeVO rsp = personMangeService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personMangeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【人员管理】数据【{{#personMangeDTO.number}}】", type = "PersonMange", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonMangeDTO personMangeDTO) throws Exception {
        String rsp =  personMangeService.create(personMangeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param addParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加人员")
    @RequestMapping(value = "/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量【人员管理】新增用户数据【{{#codeList}}】", type = "BasicUser", subType = "新增", bizNo = "{{#codeList}}")
    public ResponseDTO<Map<String, PersonMange> > addBatchByCodeList(@RequestBody AddParamDTO addParamDTO) throws Exception {
        Map<String,PersonMange>  rsp =  personMangeService.addBatchByCodeList(addParamDTO);
        LogRecordContext.putVariable("codeList", addParamDTO.getCodeList().stream().collect(Collectors.joining(",")));
        return new ResponseDTO<>(rsp);
    }



    /**
     * 编辑
     *
     * @param personMangeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】对【人员管理】数据【{{#personMangeDTO.id}}】操作物资【{{#personMangeDTO.number}}】进场【{{#baseCode}}】", type = "PersonMange", subType = "入场", bizNo = "{{#personMangeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonMangeDTO personMangeDTO) throws Exception {
        Boolean rsp = personMangeService.edit(personMangeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【人员管理】【{{#id}}】数据", type = "PersonMange", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personMangeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【人员管理】【{{#ids.toString()}}】数据", type = "PersonMange", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personMangeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员管理】数据", type = "PersonMange", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonMangeVO>> pages(@RequestBody Page<PersonMangeDTO> pageRequest) throws Exception {
        Page<PersonMangeVO> rsp =  personMangeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 编辑
     *
     * @param leaveDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "离厂")
    @RequestMapping(value = "/leave", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】将【人员管理】人员【{{#name}}-{{#number}}】从基地【{{#baseCode}}】离厂了数据【{{#leaveDTO.id}}】", type = "PersonMange", subType = "离厂", bizNo = "{{#leaveDTO.id}}")
    public ResponseDTO<Boolean> leave(@RequestBody LeaveDTO leaveDTO) throws Exception {
        Boolean rsp = personMangeService.leave(leaveDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param personManageId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过人员管理ID获取最新的台账信息")
    @RequestMapping(value = "/last/info/{personManageId}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【人员管理】数据【{{#personManageId}}】", type = "PersonMange", subType = "获取", bizNo = "{{#personManageId}}")
    public ResponseDTO<PersonMangeVO> leave(@PathVariable("personManageId") String personManageId) throws Exception {
        PersonMangeVO rsp = personMangeService.lastInfoByPersonManagetId(personManageId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param inAndOutDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑进场离场时间")
    @RequestMapping(value = "/edit/date", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员管理】【{{#id}}】的进场离场时间", type = "PersonMange", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> editDate(@Validated @RequestBody InAndOutDTO inAndOutDTO) throws Exception {
        LogRecordContext.putVariable("id",inAndOutDTO.getId());
        Boolean rsp = personMangeService.editDate(inAndOutDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     * @param editNewcomerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑是否新人")
    @RequestMapping(value = "/edit/newcomer", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员管理】【{{#id}}】的是否新人", type = "PersonMange", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> editNewcomer(@Validated @RequestBody EditNewcomerDTO editNewcomerDTO) throws Exception {
        LogRecordContext.putVariable("id",editNewcomerDTO.getId());
        Boolean rsp = personMangeService.editNewcomer(editNewcomerDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     * @param editNewcomerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑接口人")
    @RequestMapping(value = "/edit/contact", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员管理】【{{#id}}】的接口人", type = "PersonMange", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> editContact(@Validated @RequestBody EditContactUserDTO editNewcomerDTO) throws Exception {
        Boolean rsp = personMangeService.editContact(editNewcomerDTO);
        LogRecordContext.putVariable("id",editNewcomerDTO.getId());
        return new ResponseDTO<>(rsp);
    }
}
