<template>
  <!-- <div class="productLibraryDetails layoutPage"></div> -->
  <div class="productLibraryDetails5920 layoutPage5920">
    <div
      class="productLibraryDetails_content5920 layoutPage_content5920"
      :style="{ height: contentHeight + 130 + 'px' }"
    >
      <div class="productLibraryDetails_left">
        <basicTitle :title="'预览'">
          <pdmImage
            :img-url="pictureBase + formState.projectImage"
            :show-delete="formType === 'edit'"
            @deleteImgUrl="deleteImgUrl"
          />
        </basicTitle>
      </div>

      <div class="productLibraryDetails_right">
        <basicTitle :title="'基本信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :rules="formType === 'details' ? {} : rules"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item
                label="编号"
                name="code"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.number }}</span>
              </a-form-item>
              <a-form-item
                label="标题"
                name="name"
                :style="{ height: '15px' }"
              >
                <a-input
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.name"
                  style="width: 330px"
                  placeholder="请输入标题"
                />
                <span v-else>{{ formState.name }}</span>
              </a-form-item>
              <a-form-item
                label="所属项目"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.projectName }}</span>
              </a-form-item>
              <a-form-item
                label="所属需求"
                name="source"
                :style="{ height: '15px' }"
              >
                <a-tree-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.parentId"
                  style="width: 330px"
                  :tree-data="parentId"
                  placeholder="请选择所属需求"
                />
                <span v-else>{{ formState.parentName ? formState.parentName : '无' }}</span>
              </a-form-item>

              <a-form-item label="内容">
                <aTextarea
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.remark"
                  style="width: 330px"
                  placeholder="请输入内容"
                  show-count
                  :maxlength="255"
                  :rows="4"
                />
                <span
                  v-else
                  class="descriptionStyle"
                >{{ formState.remark }}</span>
              </a-form-item>

              <a-form-item
                label="需求来源"
                name="source"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.source"
                  placeholder="请选择需求来源"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in source"
                    :key="index"
                    :value="item.id"
                  >
                    {{
                      item.name
                    }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.sourceName }}</span>
              </a-form-item>

              <a-form-item
                label="需求类型"
                name="type"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.type"
                  placeholder="请选择需求类型"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in type"
                    :key="index"
                    :value="item.id"
                  >
                    {{
                      item.name
                    }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.typeName }}</span>
              </a-form-item>

              <a-form-item
                label="提出人"
                name="exhibitor"
                :style="{ height: '15px' }"
              >
                <a-input
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.exhibitor"
                  style="width: 330px"
                  placeholder="请输入标题"
                />
                <span v-else>{{ formState.exhibitor }}</span>
              </a-form-item>

              <a-form-item
                label="提出时间"
                name="proposedTime"
                :style="{ height: '15px' }"
              >
                <a-date-picker
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.proposedTime"
                  style="width: 330px"
                  placeholder="年 / 月 / 日"
                />
                <span v-else>{{
                  formState.proposedTime ? dayjs(formState.proposedTime).format('YYYY-MM-DD') : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="期望完成时间"
                name="predictEndTime"
                :style="{ height: '15px' }"
              >
                <a-date-picker
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.predictEndTime"
                  style="width: 330px"
                  placeholder="年 / 月 / 日"
                />

                <span v-else>{{
                  formState.predictEndTime
                    ? dayjs(formState.predictEndTime).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="接收人"
                name="recipientId"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.recipientId"
                  :filter-option="filterHandle3"
                  placeholder="请输入负责人"
                  :options="recipientId"
                  style="width: 330px"
                  show-search
                  @search="handleChange3"
                />
                <span v-else>{{ formState.recipientName }}</span>
              </a-form-item>
              <a-form-item
                label="负责人"
                name="principalId"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.principalId"
                  :filter-option="filterHandle1"
                  placeholder="请输入负责人"
                  :options="roleOption"
                  style="width: 330px"
                  show-search
                  @search="handleChange"
                />
                <span v-else>{{ formState.principalName }}</span>
              </a-form-item>

              <a-form-item
                label="状态"
                name="status"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.status"
                  placeholder="请选择状态"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in status"
                    :key="index"
                    :value="item.value"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.statusName }}</span>
              </a-form-item>
              <a-form-item
                label="优先级"
                name="priorityLevel"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.priorityLevel"
                  placeholder="请选择优先级"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in priorityLevel"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.priorityLevelName }}</span>
              </a-form-item>

              <a-form-item
                label="进度"
                name="schedule"
                :style="{ height: '15px' }"
              >
                <div
                  v-if="formType === 'edit'"
                  style="width: 330px"
                >
                  <!-- <a-input
                  v-if="formType === 'edit'"
                  placeholder="请输入进度"
                  v-model:value="newformdata.schedule"
                  suffix="%"
                  style="width: 330px"
                /> -->
                  <a-input-number
                    v-if="formType === 'edit'"
                    v-model:value="newformdata.schedule"
                    placeholder="请输入进度"
                    style="width: 330px"
                    :min="0"
                    :max="100"
                    :formatter="
                      (value) => {
                        return value && `${value}%`;
                      }
                    "
                    :parser="(value) => value.replace('%', '')"
                  />
                </div>

                <span v-else>{{ formState.scheduleName }}</span>
              </a-form-item>
              <a-form-item
                label="修改人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.modifyName }}</span>
              </a-form-item>
              <a-form-item
                label="修改时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.modifyTime
                    ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="创建人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.creatorName }}</span>
              </a-form-item>
              <a-form-item
                label="创建时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.createTime
                    ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>
      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, onMounted, nextTick, inject, computed,
} from 'vue';
import {
  Form,
  Select,
  Input,
  message,
  Table,
  DatePicker,
  TreeSelect,
  InputNumber,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
/* 图片api */
import { pictureBase } from '/@/views/pms/projectLaborer/api/picture';
/* 角色api */
import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import {
  itemDetailsApi,
  editDemandApi,
  demandTypeApi,
  demandSourceApi,
  demandSimplePageApi,
  priorityLevelApi,
  getDemandStatusApi,
} from '/@/views/pms/projectLaborer/api/demandManagement';
import dayjs from 'dayjs';
import upload from '/@/views/pms/projectLaborer/componentsList/upload/index.vue';
import pdmImage from '/@/views/pms/projectLaborer/componentsList/image/index.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  isPower,
} from 'lyra-component-vue3';
export default defineComponent({
  name: 'DetailsTab',
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aSelect: Select,
    aTable: Table,
    aInput: Input,
    aTextarea: Input.TextArea,
    pdmImage,
    basicTitle,
    upload,
    newButtonModal,
    RangePicker: DatePicker.RangePicker,
    ADatePicker: DatePicker,
    ASelectOption: Select.Option,
    ATreeSelect: TreeSelect,
    AInputNumber: InputNumber,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  emits: ['editSuccess'],
  setup(props, { emit }) {
    const formRef = ref();
    const state: any = reactive({
      formType: 'details',
      formState: <any>{},
      newformdata: <any>{
        name: '',
        remark: '',
        schedule: '',
        priorityLevel: '',
        status: '',
        principalId: '',
        recipientId: '',
        predictEndTime: undefined,
        proposedTime: undefined,
        //   predictStartTime: '',
        exhibitor: '',
        source: '',
        parentId: '',
        type: '',
        /* 识别人和负责人 回显处理字段 */
        exhibitorName: '',
        principalName: '',
        recipientName: '',
        parentName: '',
        id: '',
        projectId: '',
      },
      treeData: [],
      oldFormState: <any>{},
      message: '',
      showVisible: false,
      btnType: '',
      dataRow: {},
      contentHeight: 500,

      // 负责人
      roleOption: <any>[],
      // 接收人
      recipientId: <any>[],
      // 类型
      type: <any>[],
      // 问题来源
      source: [],
      parentId: [],
      // 预估发生时间
      // 应对策略
      priorityLevel: [],
      status: [],
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        edit: { show: computed(() => isPower('XQ_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      },
    });
    let projectId: any = inject('projectId');
    let demandItemId: any = inject('demandItemId');
    const rules = {
      // name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
      // ownerName: [{ required: true, message: '请输入项目经理', trigger: 'blur' }],
      // number: [{ required: true, message: '请输入项目编号', trigger: 'blur' }]
      // startTime: [{ required: true, message: '请选择立项时间', trigger: 'blur' }],
      // endTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
      // projectEndTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
    };
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editFormData();
          break;
        case 'determine':
          okRow();
          break;
        case 'cancel':
          cancel();
          break;
      }
    };
    const convertTree = (tree) => {
      const result: any = [];
      tree.forEach((item) => {
        let children = item.child || [];
        let { name: title, id: key, id: value } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          key,
          value,
        });
      });
      return result;
    };
    const editFormData = async () => {
      try {
        state.type = await demandTypeApi();
        state.source = await demandSourceApi();

        //   state.predictStartTime = await startEffectRiskListApi();
        state.priorityLevel = await priorityLevelApi();
        state.status = await getDemandStatusApi(demandItemId.value);
        const planTree = {
          projectId: projectId.value,
        };
        const res6 = await demandSimplePageApi(planTree);
        /* 1 */
        const arr = [res6];
        state.parentId = convertTree(arr);
      } catch (err) {
      }
      for (let namex in state.newformdata) {
        if (namex === 'proposedTime' || namex === 'predictEndTime') {
          state.newformdata[namex] = state.formState[namex] ? dayjs(state.formState[namex]) : undefined;
        } else {
          state.newformdata[namex] = state.formState[namex];
        }
      }
      state.newformdata.principalId = state.newformdata.principalName;
      state.newformdata.recipientId = state.newformdata.recipientName;
      state.newformdata.parentId = state.newformdata.parentName;
      /* ---------- */
      state.formType = 'edit';
      state6.btnObjectData = {
        edit: { show: false },
        determine: { show: true },
        cancel: { show: true },
      };
    };
    const okRow = () => {
      formRef.value
        .validate()
        .then(() => {
          const newformdata888 = JSON.parse(JSON.stringify(state.newformdata));
          if (!newformdata888.predictEndTime) {
            newformdata888.predictEndTime = null;
          } else {
            newformdata888.predictEndTime = newformdata888.predictEndTime
              ? newformdata888.predictEndTime
              : state.formState.predictEndTime;
          }
          if (!newformdata888.proposedTime) {
            newformdata888.proposedTime = null;
          } else {
            newformdata888.proposedTime = newformdata888.proposedTime
              ? newformdata888.proposedTime
              : state.formState.proposedTime;
          }
          /* 如果值不变,则赋值回旧值 */
          if (newformdata888.principalId == state.formState.principalName) {
            newformdata888.principalId = state.formState.principalId;
          }
          if (newformdata888.recipientId == state.formState.recipientName) {
            newformdata888.recipientId = state.formState.recipientId;
          }

          if (newformdata888.parentId == state.formState.parentName) {
            newformdata888.parentId = state.formState.parentId;
          }
          delete newformdata888.exhibitorName;
          delete newformdata888.principalName;
          delete newformdata888.recipientName;
          delete newformdata888.parentName;
          // const love = {
          //   id: newformdata888?.id,
          //   name: newformdata888?.name,
          //   className: 'DemandManagement',
          //   moduleName: '项目管理-需求管理-概述', // 模块名称
          //   type: 'UPDATE', // 操作类型
          //   remark: `编辑了【${newformdata888?.id}】`,
          // };
          editDemandApi(newformdata888)
            .then(async () => {
              message.success('保存成功');
              emit('editSuccess');
              state6.btnObjectData = {
                edit: { show: computed(() => isPower('XQ_container_button_02', state.powerData)) },
                determine: { show: false },
                cancel: { show: false },
              };
              state.formType = 'details';
              state.newformdata = {
                name: '',
                remark: '',
                schedule: '',
                priorityLevel: '',
                status: '',
                principalId: '',
                recipientId: '',
                predictEndTime: undefined,
                proposedTime: undefined,
                //   predictStartTime: '',
                exhibitor: '',

                source: '',
                type: '',
                /* 识别人和负责人 */
                exhibitorName: '',
                principalName: '',
                recipientName: '',
                parentName: '',

                id: '',
                projectId: '',
              };
              await getDetail();
            })
            .catch(() => {});
        })
        .catch((error) => {
          console.log('error', error);
        });
    };

    const cancel = () => {
      state.formType = 'details';
      state.formState.projectImage = '';
      state6.btnObjectData = {
        edit: { show: computed(() => isPower('XQ_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      };
      getDetail();
    };

    onMounted(async () => {
      state.contentHeight = document.body.clientHeight - 365;
      await getDetail();
    });
    /* 获取详情 */
    const getDetail = () => {
      itemDetailsApi(props.id)
        .then((res) => {
          if (res) {
            state.formState = { ...res };
            //   state.oldFormState = JSON.parse(JSON.stringify(res));
          }
        })
        .catch(() => {});
    };
      /* 图片上传回调 */
      //   const successChange = (data) => {
      //     state.formState.projectImage = data.imageId;
      //     state.newformdata.projectImage = data.imageId;
      //   };
    const deleteImgUrl = () => {
      state.formState.projectImage = '';
      state.newformdata.projectImage = '';
    };
    const handleChange = (value) => {
      state.roleOption = [];
      try {
        getRole(value, projectId.value, 'role');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const handleChange2 = (value) => {
      try {
        getRole(value, projectId.value, 'role2');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const handleChange3 = (value) => {
      state.recipientId = [];

      try {
        getRole(value, projectId.value, 'role3');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const getRole = async (value, idkey, typeString) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        nextTick(() => {
          const qq = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          if (typeString === 'role') {
            state.roleOption = qq;
          }
          if (typeString === 'role2') {
            state.exhibitorId = qq;
          }
          if (typeString === 'role3') {
            state.recipientId = qq;
          }
        });
      });
    };
    const filterHandle1 = (_, option) => option;
    const filterHandle2 = (_, option) => option;
    const filterHandle3 = (_, option) => option;
    return {
      ...toRefs(state),
      ...toRefs(state6),
      formRef,
      rules,
      cancel,
      okRow,
      editFormData,
      // successChange,
      deleteImgUrl,
      clickType,
      dayjs,
      handleChange,
      handleChange2,
      handleChange3,
      filterHandle1,
      filterHandle2,
      filterHandle3,
      pictureBase,

    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/DetailStyle.less');
</style>
