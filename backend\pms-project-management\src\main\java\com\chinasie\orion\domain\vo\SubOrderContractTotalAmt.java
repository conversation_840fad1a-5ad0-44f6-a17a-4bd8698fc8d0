package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "SubOrderContractTotalAmt对象", description = "子订单总金额")
@Data
public class SubOrderContractTotalAmt {
    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmt;

    /**
     * 框架合同总金额
     */
    @ApiModelProperty(value = "框架合同总金额")
    private BigDecimal frameContractTotalAmt;
}
