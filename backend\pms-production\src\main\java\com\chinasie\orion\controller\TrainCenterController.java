package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.TrainCenterDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterParamDTO;
import com.chinasie.orion.domain.vo.TrainCenterVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.TrainCenterService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * TrainCenter 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
@RestController
@RequestMapping("/train-center")
@Api(tags = "培训中心管理")
public class  TrainCenterController  {

    @Autowired
    private TrainCenterService trainCenterService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【培训中心管理】【{{#number}}】的信息", type = "TrainCenter", subType = "详情", bizNo = "")
    public ResponseDTO<TrainCenterVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        TrainCenterVO rsp = trainCenterService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getAttendCenterName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainCenterDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【培训中心管理】数据【{{#id}}】", type = "TrainCenter", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TrainCenterDTO trainCenterDTO) throws Exception {
        String rsp =  trainCenterService.create(trainCenterDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param trainCenterDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【培训中心管理】数据【{{#number}}】", type = "TrainCenter", subType = "编辑", bizNo = "{{#trainCenterDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  TrainCenterDTO trainCenterDTO) throws Exception {
        LogRecordContext.putVariable("number", trainCenterDTO.getAttendCenter());
        Boolean rsp = trainCenterService.edit(trainCenterDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【培训中心管理】数据", type = "TrainCenter", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = trainCenterService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【培训中心管理】数据", type = "TrainCenter", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = trainCenterService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训中心管理】数据", type = "TrainCenter", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TrainCenterVO>> pages(@RequestBody Page<TrainCenterDTO> pageRequest) throws Exception {
        Page<TrainCenterVO> rsp =  trainCenterService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation(value = "获取培训下的中心列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训中心管理】据", type = "TrainCenter", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<TrainCenterVO>> listByEntity(@RequestBody TrainCenterDTO trainCenterDTO) throws Exception {
        List<TrainCenterVO> rsp =  trainCenterService.listByEntity(trainCenterDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 获取  培训下 对应的培训中心对应的 培训证明
     * @param centerParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取培训下对应的培训中心对应的培训证明")
    @RequestMapping(value = "/train/certificate/list", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【培训中心管理】培训下对应的培训中心对应的培训证明【{{#centerParamDTO.trainNumber}}】", type = "TrainCenter", subType = "编辑", bizNo = "{{#trainCenterDTO.id}}")
    public ResponseDTO<List<TrainCenterVO>> trainCertificateList(   @RequestBody TrainCenterParamDTO centerParamDTO) throws Exception {
        List<TrainCenterVO> rsp = trainCenterService.trainCertificateList(centerParamDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 导出培训证明附件
     * @param centerParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导出培训证明附件")
    @RequestMapping(value = "/train/certificate/export", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【培训中心管理】培训证明附件【{{#centerParamDTO.trainNumber}}】", type = "TrainCenter", subType = "编辑", bizNo = "{{#centerParamDTO.trainId}}")
    public void exportAttachment(@RequestBody TrainCenterParamDTO centerParamDTO, HttpServletResponse response) throws Exception {
        trainCenterService.exportAttachment(centerParamDTO,response);
    }



    /**
     * 新增
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "附件上传-相关证书")
    @RequestMapping(value = "/{id}/upload", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】上传【培训中心管理】附件", type = "TrainCenter", subType = "上传附件", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> uploadFile(@PathVariable("id") String  id, @RequestBody List<FileDTO> fileDTOS) throws Exception {
        Boolean rsp =  trainCenterService.uploadFile(id,fileDTOS);
        LogRecordContext.putVariable("id", id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "办结")
    @RequestMapping(value = "/end/finish/{centerId}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】办结【培训中心管理】数据【{{#centerId}}】", type = "TrainCenter", subType = "编辑", bizNo = "{{#trainCenterDTO.id}}")
    public ResponseDTO<Boolean> endFinish(@PathVariable("centerId") String centerId ) throws Exception {
        Boolean rsp = trainCenterService.endFinish(centerId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取培训中心列表")
    @RequestMapping(value = "/trainCenter/list", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训中心管理】培训中心列表", type = "TrainCenter", subType = "查询培训中心列表", bizNo = "")
    public ResponseDTO<List<TrainCenterVO>> getTrainCenterList(){
        List<TrainCenterVO> trainCenterList = trainCenterService.getTrainCenterList();
        return new ResponseDTO<>(trainCenterList);
    }


}
