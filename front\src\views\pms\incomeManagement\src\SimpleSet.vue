<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import { reactive, defineExpose, onMounted } from 'vue';
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';

const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  rows: {
    type: Array,
    default: () => [],
  },
});
const [registerForm, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 24,
  },
  schemas: [
    {
      field: 'saleOver',
      component: 'Select',
      label: '是否销售结束',
      rules: [
        {
          required: true,
          type: 'boolean',
        },
      ],
      componentProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
        numberToString: false,
      },
      ifShow() {
        return props.type === 'saleOver';
      },
    },
    {
      field: 'expectedOutcomes',
      component: 'InputNumber',
      label: '现预期总产出',
      rules: [{ required: true }],
      componentProps: {},
      ifShow() {
        return props.type === 'expectedOutcomes';
      },
    },
  ],
});
onMounted(() => {
  if (props.rows?.length === 1) {
    setFieldsValue(props.rows.map((item: any) => ({
      expectedOutcomes: item.expectedOutcomes,
      saleOver: item.saleOver,
    }))[0]);
  }
});

async function getData() {
  return await validate();
}

defineExpose({
  getData,
});
</script>
