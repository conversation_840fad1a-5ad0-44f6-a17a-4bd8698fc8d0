package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/**
 * ProjectOrder Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
@TableName(value = "pmsx_project_order")
@ApiModel(value = "ProjectOrderEntity对象", description = "商城订单")
@Data

public class ProjectOrder extends ObjectEntity implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @TableField(value = "order_person")
    private String orderPerson;

    /**
     * 下单企业
     */
    @ApiModelProperty(value = "下单企业")
    @TableField(value = "order_business")
    private String orderBusiness;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @TableField(value = "order_tel")
    private String orderTel;

    /**
     * 框架合同编号
     */
    @ApiModelProperty(value = "框架合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 框架合同名称
     */
    @ApiModelProperty(value = "框架合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费")
    @TableField(value = "order_surcharge")
    private BigDecimal orderSurcharge;

    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    @TableField(value = "contract_numbers")
    private String contractNumbers;

    /**
     * 商务接口人id
     */
    @ApiModelProperty(value = "商务接口人id")
    @TableField(value = "business_person_id")
    private String businessPersonId;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    @TableField(value = "business_person_name")
    private String businessPersonName;

    /**
     * 技术接口人id
     */
    @ApiModelProperty(value = "技术接口人id")
    @TableField(value = "technical_person_id")
    private String technicalPersonId;

    /**
     * 技术接口人名称
     */
    @ApiModelProperty(value = "技术接口人名称")
    @TableField(value = "technical_person_name")
    private String technicalPersonName;

    /**
     * 承接部门id
     */
    @ApiModelProperty(value = "承接部门id")
    @TableField(value = "bear_org_id")
    private String bearOrgId;

    /**
     * 承接部门名称
     */
    @ApiModelProperty(value = "承接部门名称")
    @TableField(value = "bear_org_name")
    private String bearOrgName;


    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    @TableField(value = "order_name")
    private String orderName;

    /**
     * po订单号
     */
    @ApiModelProperty(value = "po订单号")
    @TableField(value = "po_number")
    private String poNumber;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @TableField(value = "e_channel_number")
    private String eChannelNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @TableField(value = "order_time")
    private Date orderTime;

    /**
     * PR公司
     */
    @ApiModelProperty(value = "PR公司")
    @TableField(value = "pr_company")
    private String prCompany;

    /**
     * 要求到货时间
     */
    @ApiModelProperty(value = "要求到货时间")
    @TableField(value = "delivery_time")
    private String deliveryTime;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    @TableField(value = "customer")
    private String customer;
}
