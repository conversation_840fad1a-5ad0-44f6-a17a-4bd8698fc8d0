<script setup lang="ts">
import { BasicCard, UploadList, BasicTitle1 } from 'lyra-component-vue3';
import {
  computed, inject, onMounted, ref,
} from 'vue';
import { edit } from '/@/views/pms/api/documentModelLibrary';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const powerData: any = inject('powerData', []);
const powerCode = {
  delete: 'WDMBKXQ_container_01_button_03',
  download: 'WDMBKXQ_container_01_button_01',
  upload: 'WDMBKXQ_container_01_button_02',
};
const loading = ref(false);
const basicGridProps = computed(() => ({
  list: [
    {
      label: '文档名称',
      field: 'name',
    },
    {
      label: '文档版本',
      field: 'revId',
    },
    {
      label: '创建人',
      field: 'creatorName',
    },
    {
      label: '修改人',
      field: 'modifyName',
    },
    {
      label: '修改时间',
      field: 'modifyTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '文档应用范围',
      field: 'useScopeList',
      gridColumn: '2/5',
      valueRender: ({ text }) => (text ? text.map((item) => item.name).join('，') : '--'),
    },
    {
      label: '文档描述',
      field: 'remark',
      gridColumn: '1/5',
    },
  ],
  dataSource: props.data,
}));
const listData = ref([]);

const onChange = async (list) => {
  try {
    loading.value = true;
    // 保存附件列表
    await edit({
      ...props.data,
      fileDtoList: list,
    });
    listData.value = list;
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  listData.value = props.data.fileDtoList || [];
});

</script>

<template>
  <div>
    <BasicCard
      title="基本信息"
      :gridContentProps="basicGridProps"
    />
    <BasicTitle1
      title="文档模板"
      class="ml30 mb10"
    />
    <UploadList
      v-loading="loading"
      :listData="listData"
      :onChange="onChange"
      :powerCode="powerCode"
      :powerData="powerData"
      :height="400"
    />
  </div>
</template>

<style scoped lang="less">

</style>
