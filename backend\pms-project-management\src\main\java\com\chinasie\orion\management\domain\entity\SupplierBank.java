package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierBank Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_bank")
@ApiModel(value = "SupplierBankEntity对象", description = "银行信息")
@Data

public class SupplierBank extends ObjectEntity implements Serializable {

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    @TableField(value = "bank_code")
    private String bankCode;

    /**
     * 银行网点
     */
    @ApiModelProperty(value = "银行网点")
    @TableField(value = "bank_branch")
    private String bankBranch;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 默认银行账号
     */
    @ApiModelProperty(value = "默认银行账号")
    @TableField(value = "default_account")
    private String defaultAccount;

    /**
     * 账号名称（受益人）
     */
    @ApiModelProperty(value = "账号名称（受益人）")
    @TableField(value = "account_holder")
    private String accountHolder;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @TableField(value = "bank_account")
    private String bankAccount;

    /**
     * 国际银行代码（SWIFT)
     */
    @ApiModelProperty(value = "国际银行代码（SWIFT)")
    @TableField(value = "swift_code")
    private String swiftCode;

    /**
     * 国际银行账户号码（IBAN)
     */
    @ApiModelProperty(value = "国际银行账户号码（IBAN)	")
    @TableField(value = "iban")
    private String iban;

    /**
     * 分理处/营业点
     */
    @ApiModelProperty(value = "分理处/营业点")
    @TableField(value = "teller_office")
    private String tellerOffice;

    /**
     * 支行
     */
    @ApiModelProperty(value = "支行")
    @TableField(value = "sub_branch")
    private String subBranch;

    /**
     * 分行
     */
    @ApiModelProperty(value = "分行")
    @TableField(value = "branch")
    private String branch;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 开户行地区
     */
    @ApiModelProperty(value = "开户行地区")
    @TableField(value = "bank_district")
    private String bankDistrict;

    /**
     * 开户行城市
     */
    @ApiModelProperty(value = "开户行城市")
    @TableField(value = "bank_city")
    private String bankCity;

    /**
     * 开户行省份
     */
    @ApiModelProperty(value = "开户行省份")
    @TableField(value = "bank_province")
    private String bankProvince;

    /**
     * 开户行国家和地区
     */
    @ApiModelProperty(value = "开户行国家和地区")
    @TableField(value = "bank_country_area")
    private String bankCountryArea;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
