<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>pms</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pms-common</artifactId>
    <description>通用模块</description>
    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-base-api</artifactId>
        </dependency>
    </dependencies>
    <packaging>jar</packaging>
</project>