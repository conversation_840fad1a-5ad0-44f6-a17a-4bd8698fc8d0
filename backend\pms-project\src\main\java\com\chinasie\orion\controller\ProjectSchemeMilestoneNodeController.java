package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodeExcelDTO;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneNodeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeMilestoneNodeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * ProjectSchemeMilestoneNode 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@RestController
@RequestMapping("/projectSchemeMilestoneNode")
@Api(tags = "项目计划计划节点")
public class ProjectSchemeMilestoneNodeController {

    @Autowired
    private ProjectSchemeMilestoneNodeService projectSchemeMilestoneNodeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目计划计划节点", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeMilestoneNodeVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectSchemeMilestoneNodeVO rsp = projectSchemeMilestoneNodeService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectSchemeMilestoneNodeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目计划计划节点", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectSchemeMilestoneNodeVO> create(@RequestBody  ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception {
        ProjectSchemeMilestoneNodeVO rsp = projectSchemeMilestoneNodeService.create(projectSchemeMilestoneNodeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增(批量)
     *
     * @param projectSchemeMilestoneNodeDTOS
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增(批量)")
    @RequestMapping(value = "/createBatch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目计划计划节点", subType = "新增", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody  List<ProjectSchemeMilestoneNodeDTO> projectSchemeMilestoneNodeDTOS) throws Exception {
        List<String> rsp = projectSchemeMilestoneNodeService.createBatch(projectSchemeMilestoneNodeDTOS);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectSchemeMilestoneNodeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目计划计划节点", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception {
        Boolean rsp = projectSchemeMilestoneNodeService.edit(projectSchemeMilestoneNodeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目计划计划节点", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectSchemeMilestoneNodeService.remove(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 树列表
     *
     * @param tplId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "树列表")
    @RequestMapping(value = "/tree/{tplId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】树列表", type = "项目计划计划节点", subType = "树列表", bizNo = "")
    public ResponseDTO<List<ProjectSchemeMilestoneNodeVO>> tree(@PathVariable("tplId") String tplId) throws Exception {
        List<ProjectSchemeMilestoneNodeVO> rsp = projectSchemeMilestoneNodeService.tree(tplId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "项目计划计划节点", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeMilestoneNodeVO>> pages(@RequestBody Page<ProjectSchemeMilestoneNodeDTO> pageRequest) throws Exception {
        Page<ProjectSchemeMilestoneNodeVO> rsp = projectSchemeMilestoneNodeService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("计划模版下载（Excel）")
    @PostMapping(value = "/import/excel/template", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】计划模版下载", type = "项目计划计划节点", subType = "计划模版下载", bizNo = "")
    public void importByExcelTemplate(HttpServletResponse response) throws Exception {
        ExcelUtils.writeTemplate(response, ProjectSchemeMilestoneNodeExcelDTO.class, "计划导入模版.xlsx", "计划导入模版", "sheet1");
    }

    @ApiOperation("计划节点导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @LogRecord(success = "【{USER{#logUserId}}】计划节点导入校验", type = "项目计划计划节点", subType = "计划节点导入校验", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importByExcelCheck(@RequestPart MultipartFile file, @RequestParam(value = "tplId", required = false) String tplId) throws Exception {
        ImportExcelCheckResultVO rsp = projectSchemeMilestoneNodeService.importByExcelCheck(file, tplId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("计划确认导入")
    @PostMapping(value = "/import/excel/do")
    @LogRecord(success = "【{USER{#logUserId}}】计划确认导入", type = "项目计划计划节点", subType = "计划确认导入", bizNo = "")
    public ResponseDTO<String> importByExcelDo(@RequestParam("importKey") String importKey) throws Exception {
        projectSchemeMilestoneNodeService.importByExcelDo(importKey);
        return new ResponseDTO<>("确认导入成功");
    }

    @ApiOperation("计划取消导入")
    @PostMapping(value = "/import/excel/cancel")
    @LogRecord(success = "【{USER{#logUserId}}】计划取消导入", type = "项目计划计划节点", subType = "计划取消导入", bizNo = "")
    public ResponseDTO<String> importByExcelCancel(@RequestParam("importKey") String importKey) throws Exception {
        projectSchemeMilestoneNodeService.importByExcelCancel(importKey);
        return new ResponseDTO<>("取消导入成功");
    }


    @ApiOperation("待发布/已发布项目计划列表")
    @PostMapping(value = "/list/{templateId}")
    @LogRecord(success = "【{USER{#logUserId}}】待发布/已发布项目计划列表", type = "项目计划计划节点", subType = "待发布/已发布项目计划列表", bizNo = "")
    public ResponseDTO<List<ProjectSchemeMilestoneNodeVO>> getSchemeList(@PathVariable("templateId") String templateId) throws Exception {
        return ResponseDTO.success(projectSchemeMilestoneNodeService.getSchemeList(templateId));
    }

    /**
     * 查询里程碑计划模板列表
     *
     * @param templateId 计划模版id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询里程碑计划模板列表")
    @PostMapping(value = "/milestone/list/{templateId}")
    @LogRecord(success = "【{USER{#logUserId}}】查询里程碑计划模板列表", type = "项目计划计划节点", subType = "查询里程碑计划模板列表", bizNo = "")
    public ResponseDTO<List<ProjectSchemeMilestoneNodeVO>> getProjectSchemeMilestoneList(@PathVariable("templateId") String templateId) throws Exception {
        List<ProjectSchemeMilestoneNodeVO> rsp = projectSchemeMilestoneNodeService.getProjectSchemeMilestoneList(templateId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "添加计划")
    @RequestMapping(value = "/createSchemeMilestone", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】添加计划", type = "项目计划计划节点", subType = "添加计划", bizNo = "")
    public ResponseDTO<ProjectSchemeMilestoneNodeVO> createSchemeMilestone(@RequestBody  ProjectSchemeMilestoneNodeDTO projectSchemeMilestoneNodeDTO) throws Exception {
        ProjectSchemeMilestoneNodeVO rsp = projectSchemeMilestoneNodeService.createSchemeMilestone(projectSchemeMilestoneNodeDTO);
        return new ResponseDTO<>(rsp);
    }


}
