package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * customerContact Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@TableName(value = "pms_customer_contact")
@ApiModel(value = "customerContactEntity对象", description = "客户管理详情")
@Data
public class CustomerContact extends ObjectEntity implements Serializable {

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "p_remark")
    private String pRemark;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(value = "mail")
    private String mail;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @TableField(value = "is_main")
    private String isMain;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField(value = "sex")
    private String sex;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色")
    @TableField(value = "role_name")
    private String roleName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "name")
    private String name;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @TableField(value = "department")
    private String department;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
