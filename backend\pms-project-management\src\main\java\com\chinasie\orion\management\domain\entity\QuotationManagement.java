package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * QuotationManagement Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "pmsx_quotation_management")
@ApiModel(value = "QuotationManagementEntity对象", description = "报价管理")
@Data
public class QuotationManagement extends ObjectEntity implements Serializable {

    /**
     * 项目编号-需求编号
     */
    @ApiModelProperty(value = "项目编号-需求编号")
    @TableField(value = "requirement_number")
    private String requirementNumber;

    @ApiModelProperty(value = "报价单编码")
    @TableField(value = "quotation_id")
    private String quotationId;

    /**
     * 重新报价，原报价单ID,pmsx_quotation_management 的id
     */
    @ApiModelProperty(value = "重新报价，原报价单ID")
    @TableField(value = "re_quotation_id")
    private String reQuotationId;

    /**
     * 业务目标
     */
    @ApiModelProperty(value = "业务目标")
    @TableField(value = "busi_goal")
    private String busiGoal;

    /**
     * 业务目标内容
     */
    @ApiModelProperty(value = "业务目标内容")
    @TableField(value = "busi_goal_cont")
    private String busiGoalCont;

    /**
     * 业务信息
     */
    @ApiModelProperty(value = "业务信息")
    @TableField(value = "busi_info")
    private String busiInfo;

    /**
     * 业务信息内容
     */
    @ApiModelProperty(value = "业务信息内容")
    @TableField(value = "busi_info_cont")
    private String busiInfoCont;

    /**
     * 成本估算（资源）
     */
    @ApiModelProperty(value = "成本估算（资源）")
    @TableField(value = "cost_est_res")
    private String costEstRes;

    /**
     * 成本估算（资源）内容
     */
    @ApiModelProperty(value = "成本估算（资源）内容")
    @TableField(value = "cost_est_res_cont")
    private String costEstResCont;

    /**
     * 成本估算（人力、资源占用）
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）")
    @TableField(value = "cost_est_rr_res")
    private String costEstRrRes;

    /**
     * 成本估算（人力、资源占用）内容
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）内容")
    @TableField(value = "cost_est_hr_res_cont")
    private String costEstHrResCont;

    /**
     * 收益分析
     */
    @ApiModelProperty(value = "收益分析")
    @TableField(value = "rev_anal")
    private String revAnal;

    /**
     * 收益分析内容
     */
    @ApiModelProperty(value = "收益分析内容")
    @TableField(value = "rev_anal_cont")
    private String revAnalCont;

    /**
     * 其他信息
     */
    @ApiModelProperty(value = "其他信息")
    @TableField(value = "other_info")
    private String otherInfo;

    /**
     * 其他信息内容
     */
    @ApiModelProperty(value = "其他信息内容")
    @TableField(value = "other_info_cont")
    private String otherInfoCont;

    /**
     * 报价内容
     */
    @ApiModelProperty(value = "报价内容")
    @TableField(value = "quote_content")
    private String quoteContent;

    /**
     * 报价方案详情
     */
    @ApiModelProperty(value = "报价方案详情")
    @TableField(value = "quote_plan_detail")
    private String quotePlanDetail;

    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    @TableField(value = "quote_amt")
    private BigDecimal quoteAmt;

    /**
     * 报价币种
     */
    @ApiModelProperty(value = "报价币种")
    @FieldBind(dataBind = DictDataBind.class, type = "currency_type", target = "currencyName")
    @TableField(value = "currency")
    private String currency;

    /**
     * 报价币种名称
     */
    @ApiModelProperty(value = "报价币种名称")
    @TableField(exist = false)
    private String currencyName;

    /**
     * 底线价格
     */
    @ApiModelProperty(value = "底线价格")
    @TableField(value = "floor_price")
    private BigDecimal floorPrice;

    /**
     * 报价发出时间
     */
    @ApiModelProperty(value = "报价发出时间")
    @TableField(value = "issue_time")
    private Date issueTime;

    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户")
    @TableField(value = "issuer")
    @FieldBind(dataBind = UserDataBind.class, target = "issuerName")
    private String issuer;


    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户名称")
    @TableField(exist = false)
    private String issuerName;

    /**
     * 报价结果
     */
    @ApiModelProperty(value = "报价结果")
    @TableField(value = "result")
    private String result;

    /**
     * 报价结果备注
     */
    @ApiModelProperty(value = "报价结果备注")
    @TableField(value = "result_note")
    private String resultNote;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    @TableField(value = "quotation_status")
    private String quotationStatus;

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    @TableField(value = "requirement_id")
    private String requirementId;

    /**
     * 是否涉及现场工作
     */
    @ApiModelProperty(value = "是否涉及现场工作")
    @TableField(value = "fieldwork")
    private String fieldwork;

    /**
     * 报价接收人
     */
    @ApiModelProperty(value = "报价接收人")
    @TableField(value = "quote_accept_pen")
    private String quoteAcceptPen;

    /**
     * 报价接收方
     */
    @ApiModelProperty(value = "报价接收方")
    @TableField(value = "quote_accept_com")
    private String quoteAcceptCom;

    /**
     * 报价发出途径
     */
    @ApiModelProperty(value = "报价发出途径")
    @TableField(value = "issue_way")
    private String issueWay;

    /**
     * 报价备注
     */
    @ApiModelProperty(value = "报价备注")
    @TableField(value = "quote_remark")
    private String quoteRemark;
    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    @TableField(value = "quotation_name")
    private String quotationName;

    /**
     * 重新报价原因
     */
    @ApiModelProperty(value = "重新报价原因")
    @TableField(value = "re_quote_reason")
    private String reQuoteReason;

    /**
     * 作废原因
     */
    @ApiModelProperty(value = "作废原因")
    @TableField(value = "obsolete_reason")
    private String obsoleteReason;

    /**
     * 报价执行情况
     */
    @ApiModelProperty(value = "报价执行情况")
    @TableField(value = "quote_execu_condition")
    private String quoteExecuCondition;

    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    @TableField(value = "fin_trade_bus")
    private Boolean finTradeBus;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField(value = "business_type")
    private String businessType;

    @ApiModelProperty(value = "所级负责人")
    @TableField(value = "office_leader")
    private String officeLeader;

    @ApiModelProperty(value = "业务收入类型")
    @TableField(value = "ywsrlx")
    private String ywsrlx;

    @ApiModelProperty(value = "系统中触发发出报价的用户")
    @TableField(value = "send_out_user")
    private String sendOutUser;

    @ApiModelProperty(value = "系统中触发发出报价的时间")
    @TableField(value = "send_out_time")
    private Date sendOutTime;

    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @TableField(value = "priority")
    private String priority;

    /**
     * 中标金额
     */
    @ApiModelProperty(value = "中标金额")
    @TableField(value = "winning_bid_amount")
    private BigDecimal winningBidAmount;


    @ApiModelProperty(value = "需求归属中心")
    @TableField(exist = false)
    private String reqOwnership;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    @TableField(exist = false)
    private String custConPerson;

    @ApiModelProperty(value = "业务类型")
    @TableField(exist = false)
    private String requireBusinessType;

    @ApiModelProperty(value = "技术接口人(技术负责人)")
    @TableField(exist = false)
    private String techRes;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    @TableField(exist = false)
    private String custPerson;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @TableField(exist = false)
    private String custPersonName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @TableField(exist = false)
    private String resSource;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @TableField(exist = false)
    private String businessPerson;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @TableField(exist = false)
    private Date signDeadlnTime;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "报价接收方名称")
    @TableField(exist = false)
    private String quoteAcceptComName;
}


