package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ExponseDetailDTO;
import com.chinasie.orion.domain.vo.ExponseDetailVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ExponseDetailService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * ExponseDetail 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
@RestController
@RequestMapping("/project-cost")
@Api(tags = "预算执行")
public class ExponseDetailController {

    @Resource
    private ExponseDetailService exponseDetailService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【预算执行】 【【{{#number}}】详情", type = "CostCenter", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ExponseDetailVO> detail(@PathVariable(value = "id") String id,@RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        ExponseDetailVO rsp = exponseDetailService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param exponseDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【预算执行】 【【{{#number}}】详情", type = "CostCenter", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<ExponseDetailVO> create(@Valid @RequestBody ExponseDetailDTO exponseDetailDTO) throws Exception {
        ExponseDetailVO rsp =  exponseDetailService.create(exponseDetailDTO);
        LogRecordContext.putVariable("number", rsp.getNumber());
        LogRecordContext.putVariable("id", rsp.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param exponseDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【预算执行】 【【{{#number}}】详情", type = "CostCenter", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> edit(@Valid @RequestBody  ExponseDetailDTO exponseDetailDTO) throws Exception {
        Boolean rsp = exponseDetailService.edit(exponseDetailDTO);
        LogRecordContext.putVariable("number", exponseDetailDTO.getNumber());
        LogRecordContext.putVariable("id", exponseDetailDTO.getId());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【预算执行】 【【{{#id.toString()}}】详情", type = "CostCenter", subType = "编辑", bizNo = "{{#id.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = exponseDetailService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("预算编制点击详细分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/detail/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算执行】 预算编制点击详细分页", type = "CostCenter", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ExponseDetailVO>> getExponseDetailVOPage( @RequestBody Page<ExponseDetailDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(exponseDetailService.getExponseDetailVOPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("成本执行列表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算执行】 成本执行列表分页", type = "CostCenter", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ExponseDetailVO>> getExponseDetailVOPage1(@Valid @RequestBody Page<ExponseDetailDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(exponseDetailService.getExponseDetailVOPage1(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    @ApiOperation("编辑时导入文件")
    @RequestMapping(value = "importfiles/{id}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】【预算执行】 编辑时导入文件", type = "CostCenter", subType = "导入", bizNo = "")
    public ResponseDTO<List<String>> importFiles(@PathVariable("id") String id, @RequestBody List<FileDTO> files) throws Exception {
        List<String> rsp = exponseDetailService.importFiles(id, files);
        return new ResponseDTO<>(rsp);
    }

}