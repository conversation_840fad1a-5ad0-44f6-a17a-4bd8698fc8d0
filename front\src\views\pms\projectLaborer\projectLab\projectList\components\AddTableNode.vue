<template>
  <BasicDrawer
    v-model:isContinue="isContinue"
    :width="1000"
    wrap-class-name="addTableNode"
    :showFooter="true"
    :showContinue="true"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
    @cancel="cancel"
  >
    <div
      v-loading="loading"
      class="formContent"
    >
      <div class="formContent_content">
        <Wrap
          title="项目来源"
        >
          <OrionTable
            ref="tableRef"
            :max-height="200"
            :options="tableOptions"
            @selection-change="onSelectionChange"
          >
            <template #toolbarLeft>
              <BasicButton
                type="primary"
                icon="sie-icon-tianjiaxinzeng"
                ghost
                @click="openSource()"
              >
                添加来源
              </BasicButton>
              <BasicButton
                icon="sie-icon-del"
                :disabled="disabledBtn"
                @click="removeData"
              >
                移除
              </BasicButton>
            </template>
          </OrionTable>
        </Wrap>
        <Wrap title="基本信息">
          <BasicForm @register="registerForm" />
        </Wrap>
      </div>
    </div>
    <PlanTreeModal @register="modalTreeRegister" />
    <!--    <SelectUserModal-->
    <!--      selectType="radio"-->
    <!--      @ok="selectUserCallback"-->
    <!--      @register="registerSelectUser"-->
    <!--    />-->

    <!--  选择综合计划-->
    <SelectProPlanIndex
      :onPushRows="pushRows"
      @register="registerModelPlan"
    />
    <SourceModal
      :onPushRows="pushRows"
      @register="registerSourceModal"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  defineExpose,
  h,
  onMounted,
  provide,
  reactive,
  readonly,
  ref,
  Ref,
  toRefs,
  watch,
} from 'vue';
import {
  BasicButton,
  BasicDrawer,
  BasicForm,
  DataStatusTag,
  getDict,
  InputSelectUser,
  OrionTable,
  useDrawerInner,
  useForm,
  useModal,
  openTreeSelectModal,
  getDictByNumber,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import SelectProPlanIndex from './selectProPlanModel/SelectProPlanIndex.vue';
import { getProductListApi } from '/@/views/pms/projectLaborer/api/projectLab';
import SourceModal from '/@/views/pms/projectLaborer/projectLab/projectList/components/SourceModal/index.vue';

import PlanTreeModal from './PlanTreeModal.vue';
import { useUserStore } from '/@/store/modules/user';
import { getUserProfile } from '/@/views/pms/api';
import Wrap from './Wrap.vue';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    BasicForm,
    PlanTreeModal,
    BasicButton,
    Wrap,
    OrionTable,
    SelectProPlanIndex,
    SourceModal,
    // InputMoney,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const selectRows = ref([]);
    const disabledBtn = computed(() => selectRows.value.length === 0);
    const state = reactive({
      loadingBtn: false,
      checked: false,
      loading: false,
      formType: 'add',
      formId: '',
      productOptions: [],
      projectTypeOptions: [],
      statusOptions: [],
      schemeIdList: [],
      projectSourceDict: [],
      projectType: '',
      userOrgList: [], // 用户概要信息

      // 已选合同责任人
      selectPrincipalUser: [],
      // 合同责任部门选项
      principalDeptOptions: [],
    });
    let useData = useUserStore().getUserInfo;
    const isContinue:Ref<boolean> = ref(false);
    provide('userOrgList', readonly(computed(() => state.userOrgList)));
    const tableRef = ref();
    const [modalTreeRegister, { openModal }] = useModal();
    const [registerModelPlan, { openModal: openModelCode }] = useModal();
    const [registerSourceModal, { openModal: openSourceModal }] = useModal();

    const dataSource = ref([]);

    // 获取用户概要信息
    async function reqUserProfile(uid) {
      if (!uid) return;
      try {
        const {
          orgName, orgId, orgCode, deptName, deptId, deptCode, className, classId, code,
        } = await getUserProfile(uid);
        state.userOrgList = [
          { // 部门
            label: orgName,
            value: orgId,
            code: orgCode,
          },
          { // 科室
            label: deptName,
            value: deptId,
            code: deptCode,
          },
          { // 班组
            label: className,
            value: classId,
            code,
          },
        ].filter((item) => item.label && item.value);
      } finally {
        // state.loading = false;
      }
    }
    function visibleChange() {
      isContinue.value = false;
    }
    // 移除表格数据
    function removeData() {
      Modal.confirm({
        title: '移除确认提示',
        content: '请确认是否移除该关联计划信息？',
        onOk(closeFn) {
          dataSource.value = dataSource.value.filter((item) => selectRows.value.every((row) => row.id !== item.id));
          tableRef.value.clearSelectedRowKeys();
          closeFn();
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    }
    watch(() => dataSource.value, (val) => {
      setFieldsValue({
        planList: val,
      });
    });
    // 添加已选择数据
    function pushRows(rows) {
      const map = new Map();
      dataSource.value = dataSource.value.concat(rows)
        .filter((row) => !map.has(row.id) && map.set(row.id, 1));
    }
    const columns = [
      {
        title: '编号',
        dataIndex: 'number',
        width: 100,
      },
      {
        title: '名称',
        dataIndex: 'name',
      },

      {
        title: '状态',
        dataIndex: 'dataStatus',
        width: 100,
        customRender({ text }) {
          return text ? h(DataStatusTag, {
            statusData: text,
          }) : '';
        },
      },
      {
        title: '责任人',
        width: 100,
        dataIndex: 'rspUserName',
      },
      {
        title: '来源类型',
        width: 100,
        dataIndex: 'type',
      },
    ];

    const tableOptions = {
      rowSelection: {},
      columns,
      dataSource,
      pagination: false,
      showTableSetting: false,
      showToolButton: false,
      showSmallSearch: false,
    };

    const [modalRegister, { closeDrawer, setDrawerProps, changeOkLoading }] = useDrawerInner((drawerData) => {
      isContinue.value = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.formId = drawerData.id;
      state.projectType = drawerData.projectType;
      state.schemeIdList = [];
      dataSource.value = [];

      // 已选合同责任人
      state.selectPrincipalUser = [];
      // 合同责任部门选项
      state.principalDeptOptions = [];
      reqUserProfile(useData.id);
      clearValidate();
      resetFields();

      getProjectSourceDict();

      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增项目' });
      } else {
        setDrawerProps({ title: '编辑项目' });
        getStatusOptions();
        getFormData(state.formId);
        getBasePlanPage(state.formId);
      }
    });

    async function getBasePlanPage(id) {
      const result = await new Api('/pms/new-project-to-base-plan/page').fetch({
        pageSize: 999,
        pageNum: 1,
        query: {
          projectId: id,
        },
      }, '', 'POST');
      let arr = result.content.map((item) => ({
        ...item,
        name: item.sourceName,
        rspUserName: item.resUserName,
        type: item.sourceTypeName,
      }));
      dataSource.value = arr;
      tableRef.value.setTableData(dataSource);
    }
    async function getProjectSourceDict() {
      state.projectSourceDict = await getDict('dict1714906542609989632').then((res) => res?.map((item) => ({
        ...item,
        label: item.description,
        value: item.value,
      })) ?? []);
    }

    function getFormData(id) {
      state.loading = true;
      new Api(`/pms/project/detail/${id}`)
        .fetch('', '', 'GET')
        .then((res) => {
          state.loading = false;
          res.projectApproveTime = res.projectApproveTime ? dayjs(res.projectApproveTime) : '';
          res.projectStartTime = res.projectStartTime ? dayjs(res.projectStartTime) : '';
          res.projectEndTime = res.projectEndTime ? dayjs(res.projectEndTime) : '';
          let schemeIdList = []; let schemeIdListName = [];
          if (res.schemeList && Array.isArray(res.schemeList)) {
            res.schemeList.forEach((item) => {
              schemeIdList.push(item.id);
              schemeIdListName.push(item.name);
            });
            state.schemeIdList = schemeIdList;
          }
          res.schemeIdList = schemeIdListName.join(',');

          state.principalDeptOptions = [
            {
              value: res.resDept,
              label: res.resDeptName,
              key: res.resDept,
            },
          ];

          state.selectPrincipalUser = [
            {
              id: res.resPerson,
              name: res.resPersonName,
            },
          ];

          // state.selectPrincipalUser = users;
          // const resPerson = users?.[0]?.id;
          // // model[field] = resPerson ?? '';
          // setPrincipalDept(resPerson);
          // validateFields(['resPerson']);

          setFieldsValue(res);
        }).catch(() => {
          state.loading = false;
        });
    }
    /**
     * 设置责任部门
     * @param principalId 合同负责人ID
     */
    async function setPrincipalDept(principalId?: string) {
      if (!principalId) {
        setFieldsValue({
          resDept: '',
        });
        return;
      }
      const userInfo = await new Api(`/pmi/user/user-profile/${principalId}`).fetch('', '', 'GET');

      state.principalDeptOptions = [userInfo].map((item) => ({
        value: item.orgId,
        label: item.orgName,
        key: item.orgId,
      }));
      setFieldsValue({
        resDept: state.principalDeptOptions?.[0]?.value ?? '',
      });
    }

    async function getStatusOptions() {
      new Api('/pms').fetch('', 'project-task-status/policy/status/list/project', 'GET').then((res) => {
        state.statusOptions = res;
      });
    }
    function onSelectionChange({ rows }) {
      selectRows.value = rows;
    }

    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,

      schemas: [
        {
          field: 'number',
          component: 'Input',
          label: '项目编号',
          helpMessage: '创建后将自动生成项目编号',
          colProps: {
            span: 12,
          },
          componentProps: {
            disabled: true,
            placeholder: '创建后将自动生成项目编号',
          },
        },
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '项目名称',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
            disabled: computed(() => state.code === 'pm'),
          },
        },
        {
          field: 'projectType',
          component: 'Select',
          label: '项目类型',
          colProps: {
            span: 12,
          },
          componentProps: {
            options: computed(() => state.projectTypeOptions),
            fieldNames: {
              label: 'description',
              value: 'value',
            },
            onChange: (val) => {
              state.projectType = val;
              setFieldsValue({
                projectSubType: undefined,
              });
              clearValidate();
            },
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        {
          field: 'projectSubType',
          component: 'ApiSelect',
          helpMessage: '销售、科研、活动类型项目没有子类型',
          label: '子类型',
          colProps: {
            span: 12,
          },
          ifShow: () => computed(() => ['project_type_research', 'invest_server'].includes(state.projectType)).value,
          componentProps: () => {
            // 研发:project_type_research_children
            // 投资:business_pms_investment
            const str = state.projectType === 'project_type_research' ? 'project_type_research_children' : 'business_pms_investment';
            return {
              allowClear: false,
              api: () => new Api('/pms/dict/code').fetch('', str, 'GET'),
              labelField: 'description',
            };
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        // {
        //   field: 'projectSource',
        //   component: 'Select',
        //   label: '项目来源',
        //   colProps: {
        //     span: 11,
        //   },
        //   componentProps: {
        //     options: computed(() => state.projectSourceDict),
        //   },
        // },

        {
          field: 'resPerson',
          component: 'InputSearch',
          label: '项目负责人',
          colProps: {
            span: 12,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
          render({ model, field }) {
            return h(InputSelectUser, {
              selectUserData: computed(() => state.selectPrincipalUser),
              onChange(users) {
                state.selectPrincipalUser = users;

                const userId = users?.[0]?.id;
                model[field] = userId ?? '';
                setPrincipalDept(userId);
                validateFields(['resPerson']);
              },
              selectUserModalProps: {
                selectType: 'radio',
                treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
                  {
                    orders: [
                      {
                        asc: false,
                        column: '',
                      },
                    ],
                    pageNum: 0,
                    pageSize: 0,
                    query: { status: 1 },
                  },
                  '',
                  'POST',
                ),
              },
            });
          },
        },
        {
          field: 'needWorkFlow',
          component: 'Select',
          label: '是否走流程',
          colProps: {
            span: 12,
          },
          defaultValue: false,
          ifShow: () => computed(() => state.projectType === 'project_type_research').value,
          componentProps: {
            allowClear: false,
            options: [
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ],
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'boolean',
            },
          ],
        },
        {
          field: 'research',
          label: '研发类型',
          colProps: { span: 12 },
          required: true,
          ifShow: () => computed(() => state.projectType === 'project_type_research').value,
          componentProps: {
            placeholder: '请选择',
            api: () => getDictByNumber('project_research_type'),
            labelField: 'description',
            valueField: 'number',
          },
          component: 'ApiSelect',
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        // {
        //   field: 'isDeclare',
        //   component: 'Select',
        //   label: '是否需要申报',
        //   colProps: {
        //     span: 12,
        //   },
        //   defaultValue: true,
        //   componentProps: {
        //     allowClear: false,
        //     options: [
        //       {
        //         label: '是',
        //         value: true,
        //       },
        //       {
        //         label: '否',
        //         value: false,
        //       },
        //     ],
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       trigger: 'change',
        //       type: 'boolean',
        //     },
        //   ],
        // },

        {
          field: 'projectStartTime',
          component: 'DatePicker',
          label: '项目开始日期',
          colProps: {
            span: 12,

          },
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
        },
        {
          field: 'projectEndTime',
          component: 'DatePicker',
          label: '项目结束日期',
          colProps: {
            span: 12,
            // offset: 2,
          },
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
            disabledDate: (current) => {
              let projectStartTime = getFieldsValue().projectStartTime || '';
              if (projectStartTime) {
                let startTime = dayjs(projectStartTime).format('YYYY-MM-DD');
                let endTime = dayjs(current).format('YYYY-MM-DD');
                return !(Date.parse(endTime) >= Date.parse(startTime));
              }
              return false;
            },
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
        },
        {
          field: 'status',
          component: 'Select',
          label: '状态',
          colProps: {
            span: 12,
          },
          componentProps: {
            options: computed(() => state.statusOptions),
            fieldNames: {
              label: 'name',
              value: 'value',
            },
          },
          ifShow() {
            return computed(() => state.formType === 'edit').value;
          },
        },
        {
          field: 'productType',
          label: '产品类型',
          colProps: { span: 12 },
          required: true,
          ifShow: () => computed(() => state.projectType === 'project_type_research').value,
          componentProps: {
            placeholder: '请选择',
            api: () => getDictByNumber('project_product_type'),
            labelField: 'description',
            valueField: 'number',
          },
          component: 'ApiSelect',
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        {
          field: 'direction',
          label: '业务方向',
          colProps: { span: 12 },
          required: true,
          ifShow: () => computed(() => state.projectType === 'project_type_research').value,
          componentProps: {
            placeholder: '请选择',
            api: () => getDictByNumber('project_direction_type'),
            labelField: 'description',
            valueField: 'number',
          },
          component: 'ApiSelect',
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        {
          field: 'level',
          label: '项目级别',
          colProps: { span: 12 },
          required: true,
          ifShow: () => computed(() => state.projectType === 'project_type_research').value,
          componentProps: {
            placeholder: '请选择',
            api: () => getDictByNumber('project_level_type'),
            labelField: 'description',
            valueField: 'number',
          },
          component: 'ApiSelect',
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        // {
        //   field: 'productId',
        //   component: 'Select',
        //   label: '关联产品',
        //   colProps: {
        //     span: 11,
        //     offset: 2,
        //   },
        //   componentProps: {
        //     options: computed(() => state.productOptions),
        //     fieldNames: { label: 'name', value: 'id' },
        //   },
        // },

        // {
        //   field: 'resDept',
        //   component: 'Select',
        //   colProps: {
        //     span: 11,
        //     offset: 2,
        //   },
        //   label: '责任部门',
        //   helpMessage: '根据项目负责人自动获取',
        //   rules: [
        //     { required: true, trigger: 'change', type: 'string' },
        //   ],
        //   componentProps: {
        //     disabled: true,
        //     options: computed(() => state.principalDeptOptions),
        //   },
        // },

        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],

    });
    const cancel = () => {
      closeDrawer();
    };

    const columnsNew = [
      {
        title: '编号',
        dataIndex: 'number',
        width: 100,
      },
      {
        title: '名称',
        dataIndex: 'name',
      },

      {
        title: '状态',
        dataIndex: 'dataStatus',
        width: 100,
        customRender({ text }) {
          return text ? h(DataStatusTag, {
            statusData: text,
          }) : '';
        },
      },
      {
        title: '计划负责人',
        width: 100,
        dataIndex: 'rspUserName',
      },
    ];

    const openTreeSelect = () => {
      // openTreeSelectModal({
      //   selectType: 'checkbox',
      //   columns: columnsNew,
      //   treeApi: getTreeApi,
      //   tableApi: (params) => new Api('/pms/projectDeclare').fetch(params, 'page', 'POST'),
      //   onOk({ tableData }) {
      //     selectedOk(tableData);
      //   },
      // });

      // openTreeSelectModal({
      //   selectType: 'echeckbox',
      //   columns,
      //   treeApi: ['综合计划', '科研申报', '销售合同', '线索响应'],
      //   // tableApi: (params) => new Api('/pms/projectDeclare').fetch(params, 'page', 'POST'),
      //   onOk({ tableData }) {
      //     selectedOk(tableData);
      //   },
      // });
    };
    const selectedOk = (data) => {

    };
    const openSource = () => {
      openSourceModal(true, {});
    };
    function getTreeApi() {
      return [
        '综合计划',
        '科研申报',
        '销售合同',
        '线索响应',
      ];
    }

    const confirm = async () => {
      let formData:any = await validateFields();

      // 使用 map 方法提取所需属性
      let planList = dataSource.value;
      formData.planList = planList;
      formData.schemeIdList = state.schemeIdList;
      formData.projectId = state.projectId;
      state.loadingBtn = true;
      changeOkLoading(true);
      if (state.formType === 'add') {
        new Api('/pms').fetch(formData, 'project/save', 'POST').then((res) => {
          state.loadingBtn = false;
          changeOkLoading(false);
          message.success('新增成功');
          emit('update');
          if (isContinue.value) {
            resetFields();
            state.selectPrincipalUser = [];
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          changeOkLoading(false);
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        new Api('/pms').fetch(formData, 'project/edit', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          changeOkLoading(false);
          emit('update');
          closeDrawer();
        }).catch((err) => {
          state.loadingBtn = false;
          changeOkLoading(false);
        });
      }
    };
    onMounted(async () => {
      state.productOptions = await getProductListApi();
      state.projectTypeOptions = await new Api('/pms').fetch('', 'dict/code/pms_project_type', 'GET');
    });
    defineExpose({
      pushRows,
      tableMethods: tableRef,
    });
    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      modalTreeRegister,
      tableRef,
      tableOptions,
      pushRows,
      openModelCode,
      registerModelPlan,
      removeData,
      disabledBtn,
      onSelectionChange,
      isContinue,
      visibleChange,
      openTreeSelect,
      registerSourceModal,
      openSource,
    };
  },
});

</script>
<style lang="less">
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
