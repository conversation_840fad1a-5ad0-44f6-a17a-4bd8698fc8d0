package com.chinasie.orion.domain.dto.pas;


import com.chinasie.orion.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

@ApiModel(value = "EcrDTO对象", description = "变更申请")
public class EcrDTO extends ObjectDTO {

    @ApiModelProperty(value = "变更对象")
    List<ObjectDTO> pasObjectDTOS;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 变更申请目录
     */
    @ApiModelProperty(value = "变更申请目录")
    private String ecrDir;

    /**
     * 变更申请类型
     */
    @ApiModelProperty(value = "变更申请类型")
    private String ecrType;


    /**
     * 变更方式
     */
    @ApiModelProperty(value = "变更方式")
    private Integer changeWay;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String responsibler;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applyTime;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String reason;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    private String content;


    /**
     * 变更分析
     */
    @ApiModelProperty(value = "变更分析")
    private String analysis;


    @ApiModelProperty("属性值")
    private List<EcrAttrValueDTO> attrValues;


    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String responsiblerId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String responsiblerName;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getEcrDir() {
        return ecrDir;
    }

    public void setEcrDir(String ecrDir) {
        this.ecrDir = ecrDir;
    }

    public String getEcrType() {
        return ecrType;
    }

    public void setEcrType(String ecrType) {
        this.ecrType = ecrType;
    }

    public Integer getChangeWay() {
        return changeWay;
    }

    public void setChangeWay(Integer changeWay) {
        this.changeWay = changeWay;
    }

    public String getResponsibler() {
        return responsibler;
    }

    public void setResponsibler(String responsibler) {
        this.responsibler = responsibler;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }


    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }


    public List<ObjectDTO> getPasObjectDTOS() {
        return pasObjectDTOS;
    }

    public void setPasObjectDTOS(List<ObjectDTO> pasObjectDTOS) {
        this.pasObjectDTOS = pasObjectDTOS;
    }

    public List<EcrAttrValueDTO> getAttrValues() {
        return attrValues;
    }

    public void setAttrValues(List<EcrAttrValueDTO> attrValues) {
        this.attrValues = attrValues;
    }

    public String getResponsiblerId() {
        return responsiblerId;
    }

    public void setResponsiblerId(String responsiblerId) {
        this.responsiblerId = responsiblerId;
    }

    public String getResponsiblerName() {
        return responsiblerName;
    }

    public void setResponsiblerName(String responsiblerName) {
        this.responsiblerName = responsiblerName;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
