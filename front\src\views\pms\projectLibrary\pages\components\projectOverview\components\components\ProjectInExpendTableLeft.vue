<script setup lang="ts">
import {
  BasicScrollbar, Empty, Icon, InputMoney,
} from 'lyra-component-vue3';
import SpinView from '/@/views/pms/components/SpinView.vue';
import Api from '/@/api';
import {
  inject, onMounted, ref, Ref, watch,
} from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  showType: {
    type: String,
    default: '',
  },
});
const projectId: string = inject('projectId');
const revenueInfo: Ref<Record<string, any>> = ref({});
const loading: Ref<boolean> = ref(false);

watch(() => props.showType, (val) => {
  getRevenueInfo(val);
});

onMounted(() => {
  getRevenueInfo('');
});

// 获取信息;
async function getRevenueInfo(val) {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectIncome/${projectId}`).fetch({ costBusType: val }, '', 'GET');
    revenueInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}

// async function getRevenueInfo() {
//   loading.value = true;
//   try {
//     const result = await new Api('/pms/contractMilestone/tree').fetch({ projectId }, '', 'POST');
//     revenueInfo.value = result || {};
//   } finally {
//     loading.value = false;
//   }
// }

</script>

<template>
  <div class="total">
    <div class="item-cell">
      <span>合同名称</span>
    </div>
    <div class="item-cell">
      <span>里程碑名称</span>
    </div>
    <div class="item-cell">
      <span>财务业务分类</span>
    </div>
    <div class="item-cell">
      <span>里程碑时间</span>
    </div>
    <div class="item-cell">
      <span>计划金额</span>
    </div>

    <div class="item-cell">
      <span>实际金额</span>
    </div>
    <div class="item-cell">
      <span>结余金额</span>
    </div>
    <div class="item-cell">
      <span>进度</span>
    </div>
  </div>
  <spin-view v-if="loading" />
  <div class="item-parent">
    <BasicScrollbar v-if="revenueInfo?.incomes?.length">
      <div
        v-for="item in revenueInfo.incomes"
        :key="item.id"
        class="item"
      >
        <div class="item-cell">
          <span
            class="name flex-te"
            :title="item.name"
          >
            <Icon
              icon="fa-calendar-check-o"
              class="icon-self-define"
            />
            <span class="ml5">{{ item.name }}</span>
          </span>
        </div>
        <div
          class="item-cell"
          :title="item.milestoneName"
        >
          <span>{{ item.milestoneName }}</span>
        </div>
        <div
          class="item-cell"
          :title="item.costBusTypeName"
        >
          <span>{{ item.costBusTypeName }}</span>
        </div>
        <div class="item-cell">
          <span class="name">{{ item.milestoneTime ? dayjs(item.milestoneTime).format('YYYY-MM-DD'): '-' }}</span>
        </div>
        <div class="item-cell">
          <span>
            <InputMoney
              :value="item.planAmount ?? 0"
              type="view"
            />
          </span>
        </div>

        <div class="item-cell">
          <span>
            <InputMoney
              :value="item.practiceAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div class="item-cell">
          <span>
            <InputMoney
              :value="item.surplusAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div class="item-cell item-cell-children">
          <span>{{ item.schedule ?? 0 }}%</span>
        </div>
      </div>
    </BasicScrollbar>

    <div
      v-else
      class="h-full flex flex-pac"
    >
      <Empty />
    </div>
  </div>
</template>

<style scoped lang="less">
.total {
  background-color: #eee;
  font-weight: bold;
  height: 50px;
  line-height: 50px;
  display: flex;
  .item-cell {
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 10px;
  }
}

.item-parent{
  height: 350px;
  .item {
    padding-top: 5px;
    display: flex;
    border-top:1px dotted ~`getPrefixVar('border-color-base')`;
    border-bottom:1px dotted ~`getPrefixVar('border-color-base')`;
    .item-cell {
      height: 50px;
      line-height: 50px;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      padding: 0 10px;
    }
    .item-cell:nth-child(1) {
      border-top:none;
    }
    .item-cell:last-child {
      border-bottom:none;
    }
  }
}

.icon-self-define {
  color: #81A3F3;
}

</style>
