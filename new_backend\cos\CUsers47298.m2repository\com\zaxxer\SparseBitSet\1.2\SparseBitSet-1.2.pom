<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
   <modelVersion>4.0.0</modelVersion>

   <groupId>com.zaxxer</groupId>
   <artifactId>SparseBitSet</artifactId>
   <version>1.2</version>
   <packaging>jar</packaging>

   <name>SparseBitSet</name>
   <description>An efficient sparse bitset implementation for Java</description>
   <url>https://github.com/brettwooldridge/SparseBitSet</url>

   <organization>
      <name>Zaxxer.com</name>
      <url>https://github.com/brettwooldridge/SparseBitSet</url>
   </organization>

   <scm>
      <connection>scm:git:**************:brettwooldridge/SparseBitSet.git</connection>
      <developerConnection>scm:git:**************:brettwooldridge/SparseBitSet.git</developerConnection>
      <url>**************:brettwooldridge/SparseBitSet.git</url>
   </scm>

   <licenses>
      <license>
         <name>The Apache Software License, Version 2.0</name>
         <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
         <distribution>repo</distribution>
      </license>
   </licenses>

   <developers>
      <developer>
         <name>Brett Wooldridge</name>
         <email><EMAIL></email>
      </developer>
   </developers>

   <properties>
      <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
   </properties>

   <parent>
      <groupId>org.sonatype.oss</groupId>
      <artifactId>oss-parent</artifactId>
      <version>7</version>
   </parent>

   <dependencies>
      <dependency>
         <groupId>junit</groupId>
         <artifactId>junit</artifactId>
         <version>4.12</version>
         <scope>test</scope>
      </dependency>
   </dependencies>

   <build>
      <sourceDirectory>src/main/java</sourceDirectory>
      <testSourceDirectory>src/test/java</testSourceDirectory>
      <plugins>
         <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.1</version>
            <configuration>
               <source>1.5</source>
               <target>1.5</target>
            </configuration>
         </plugin>
         <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>2.2.1</version>
            <configuration>
               <attach>true</attach>
            </configuration>
            <executions>
               <execution>
                  <id>attach-sources</id>
                  <goals>
                     <goal>jar</goal>
                  </goals>
               </execution>
            </executions>
         </plugin>
         <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.9.1</version>
            <configuration>
               <show>public</show>
               <attach>true</attach>
            </configuration>
            <executions>
               <execution>
                  <goals>
                     <goal>jar</goal>
                  </goals>
               </execution>
            </executions>
         </plugin>
      </plugins>
   </build>

   <profiles>
      <profile>
         <id>release-sign-artifacts</id>
         <activation>
            <property>
               <name>performRelease</name>
               <value>true</value>
            </property>
         </activation>
         <build>
            <plugins>
               <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-gpg-plugin</artifactId>
                  <version>1.4</version>
                  <executions>
                     <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                           <goal>sign</goal>
                        </goals>
                     </execution>
                  </executions>
               </plugin>
            </plugins>
         </build>
      </profile>
   </profiles>
</project>
