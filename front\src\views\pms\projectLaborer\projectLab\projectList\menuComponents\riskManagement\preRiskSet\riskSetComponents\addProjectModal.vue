<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          label="名称"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <a-form-item
          label="提醒天数"
          name="name"
          style="text-align: left"
        >
          <div>
            <!-- <a-input-search v-model:value="formState.dayNum" placeholder="请输入天数" size="large">
              <template #enterButton>
                <a-button style="background: #ccc">天</a-button>
              </template>
            </a-input-search> -->
            <a-input-number
              v-model:value="formState.dayNum"
              placeholder="请输入天数"
              :min="0"
              :formatter="
                (value) => {
                  return value && `${value}天`;
                }
              "
              :parser="(value) => value.replace('天', '')"
            />
          </div>
        </a-form-item>

        <!--        <a-form-item-->
        <!--          label="提醒时间"-->
        <!--          name="time"-->
        <!--          style="text-align: left"-->
        <!--        >-->
        <!--          <a-time-picker-->
        <!--            v-model:value="formState.time"-->
        <!--            format="HH:mm"-->
        <!--            size="large"-->
        <!--            style="width: 100%"-->
        <!--          />-->
        <!--        </a-form-item>-->
        <a-form-item
          label="提醒频率"
          name="frequency"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.frequency"
            size="large"
            placeholder="请选择提醒频率"
          >
            <a-select-option
              v-for="(item, index) in frequency"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="提醒方式"
          name="warningWay"
          style="text-align: left"
        >
          <a-checkbox-group
            v-model:value="formState.warningWayList"
            :options="warningWayList"
          />
        </a-form-item>
        <a-form-item
          label="提醒人"
          name="roleList"
        >
          <a-select
            v-model:value="formState.roleList"
            show-search
            mode="multiple"
            placeholder="请选择提醒人"
            :options="roleOption"
            size="large"
          />
        </a-form-item>

        <a-form-item label="提醒描述">
          <a-textarea
            v-model:value="formState.remark"
            show-count
            :maxlength="250"
            placeholder="请输入提醒描述"
            allow-clear
            :rows="4"
          />
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </BasicDrawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick, inject,
} from 'vue';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
  DatePicker,
  TimePicker,
  InputNumber,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import {
  editwarningItemDetailsApi,
  probRiskListApi,
  getWarningWayListApi,
  warningFrequencyListApi,
  warningFrequencyItemDetailsApi,
  preRiskRoleListApi,
} from '/@/views/pms/projectLaborer/api/riskManege';
import dayjs from 'dayjs';
import moment from 'moment';

export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aCheckboxGroup: Checkbox.Group,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
    ADatePicker: DatePicker,
    ATimePicker: TimePicker,
    AInputNumber: InputNumber,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: {
      type: Array,
      default: () => [],
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const mode1 = ref('time');

    const state = reactive({
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: <any>{
        name: undefined,
        dayNum: undefined,
        time: undefined,
        frequency: undefined,
        warningWayList: undefined,
        roleList: undefined,
        remark: undefined,
      },
      frequency: <any>[],
      riskProbability: <any>[],

      roleOption: <any>[],
      warningWayList: [],
      showVisible: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        //   { required: true, message: '请输入项目名称', trigger: 'blur' },
        //   { min: 1, max: 25, message: '项目名称长度应在1~25位', trigger: 'blur' }
      ],
    };
    let formData: any = inject('formData');
    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;
        try {
          // await getRole('', projectId.value);
          state.riskProbability = await probRiskListApi();
          const roleListArr = await preRiskRoleListApi(formData?.value.projectId);
          state.roleOption = roleListArr.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          state.frequency = await warningFrequencyListApi();
          const res4 = await getWarningWayListApi();
          state.warningWayList = res4.map((item) => ({
            label: item.name,
            value: item.id,
          }));
        } catch (err) {
          console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
        }
        if (state.formState.roleList && state.formState.roleList[0] === undefined) {
          state.formState.roleList = [];
        }
        if (value.formType == 'add') {
          state.title = '创建信息';
          state.formType = 'add';
        } else {
          state.title = '设置信息';
          state.formType = 'edit';

          const res = await warningFrequencyItemDetailsApi(props.listData[0]);
          for (let namex in state.formState) {
            if (res[namex]) {
              state.formState[namex] = res[namex];
            }
          }
          state.formState.id = res.id;
          state.formState.time && (state.formState.time = moment(state.formState.time, 'HH:mm'));
        }
      },
    );
    /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      formRef.value.resetFields();
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      // console.log('195');
      state.showVisible = false;
      state.visible = false;
      state.formState = {
        name: undefined,
        dayNum: undefined,
        time: undefined,
        frequency: undefined,
        warningWayList: undefined,
        roleList: undefined,
        remark: undefined,
      };
    };
      /* 提交按钮 */
    const onSubmit = () => {
      const httpValue = { ...state.formState };
      httpValue.time = httpValue.time
        ? dayjs(state.formState.time._d).format('HH:mm')
        : undefined;
      zhttp(httpValue);
    };
    const addSuccess = () => {
      message.success('保存成功');
      state.loading = false;
      if (state.nextCheck) {
        formRef.value.resetFields();
      } else {
        state.visible = false;
      }
      emit('success', false);
      formRef.value.resetFields();
      state.formState = {
        name: undefined,
        dayNum: undefined,
        time: undefined,
        frequency: undefined,
        warningWayList: undefined,
        roleList: undefined,
        remark: undefined,
      };
    };
    const zhttp = (httpValue) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
            const love = {
              id: httpValue?.id,
              className: 'RiskManagement',
              moduleName: '项目管理-风险管理-风险预警',
              type: 'UPDATE',
              remark: `编辑了【${httpValue?.id}】`,
            };
            editwarningItemDetailsApi(httpValue, love)
              .then(() => {
                addSuccess();
              })
              .catch(() => {
                message.warning('操作失败');
                state.loading = false;
              });
          } else {
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };

    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      mode1,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
  .nodeForm {
    padding: 10px 10px 80px 10px;
  }
  .ant-form-item{
    display: block;
  }
  .nextCheck {
    height: 40px;
    line-height: 40px;
  }
  .nodeItemBtn {
    position: fixed;
    bottom: 0px;
    padding: 20px 0;
    text-align: center;
    width: 280px;
    height: 80px;
    background: #ffffff;
    margin-bottom: 0px;
  }
  .cancelBtn {
    color: #5172dc;
    background: #5172dc19;
    width: 110px;
    border-radius: 4px;
  }
  .bgDC {
    width: 110px;
    margin-left: 15px;
    border-radius: 4px;
  }
</style>
