package com.chinasie.orion.controller.approval;


import com.chinasie.orion.domain.dto.approval.ProjectApprovalIncomeDTO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalIncomeVO;
import com.chinasie.orion.service.approval.ProjectApprovalIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectApprovalIncome 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
@RestController
@RequestMapping("/projectApprovalIncome")
@Api(tags = "收益策划")
public class ProjectApprovalIncomeController {

    @Autowired
    private ProjectApprovalIncomeService projectApprovalIncomeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【收益策划】【{{#productName}}】】-【{{#productNumber}}】详情", type = "ProjectApprovalIncome", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalIncomeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectApprovalIncomeVO rsp = projectApprovalIncomeService.detail(id,pageCode);
        LogRecordContext.putVariable("productName",rsp.getProductName());
        LogRecordContext.putVariable("productNumber",rsp.getProductNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalIncomeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【收益策划】数据【{{#projectApprovalIncomeDTO.productName}}】", type = "ProjectApprovalIncome", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectApprovalIncomeDTO projectApprovalIncomeDTO) throws Exception {
        String rsp =  projectApprovalIncomeService.create(projectApprovalIncomeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectApprovalIncomeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【收益策划】数据【{{#projectApprovalIncomeDTO.productName}}】", type = "ProjectApprovalIncome", subType = "编辑", bizNo = "{{#projectApprovalIncomeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectApprovalIncomeDTO projectApprovalIncomeDTO) throws Exception {
        Boolean rsp = projectApprovalIncomeService.edit(projectApprovalIncomeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【收益策划】数据", type = "ProjectApprovalIncome", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalIncomeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【收益策划】数据", type = "ProjectApprovalIncome", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalIncomeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【收益策划】分页数据", type = "ProjectApprovalIncome", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectApprovalIncomeVO>> pages(@RequestBody Page<ProjectApprovalIncomeDTO> pageRequest) throws Exception {
        Page<ProjectApprovalIncomeVO> rsp =  projectApprovalIncomeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
