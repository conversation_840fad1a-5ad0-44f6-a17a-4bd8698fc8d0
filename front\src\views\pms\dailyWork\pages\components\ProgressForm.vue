<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import { onMounted, ref, Ref } from 'vue';

const props = defineProps<{
  record: any
}>();

const schemas: FormSchema[] = [
  {
    field: 'workDate',
    component: 'DatePicker',
    label: '作业日期',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'progressSchedule',
    component: 'InputNumber',
    label: '总体进展',
    componentProps: {
      min: 0,
      max: 100,
      precision: 0,
    },
  },
  {
    field: 'progressDetail',
    component: 'InputTextArea',
    label: '工作进展',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 1000,
      showCount: true,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-progress').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      new Api('/pms/job-progress').fetch({
        ...formValues,
        jobId: props?.record?.jobId,
        id: props?.record?.id,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>