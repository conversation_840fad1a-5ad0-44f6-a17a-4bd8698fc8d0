package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.conts.InterfaceNodeTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/19:46
 * @description:
 */
@Component("interfaceManagementSendMsgAdapter")
public class InterfaceManagementSendMsgAdapter extends EditSchemeSendMsgAdapter{

    /**
     * 项目接口管理详情
     */
    private final static String IM_URL = "/pms/icmManagementDetailsIndex/%s";
    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        Map<String, String> extParam = schemeMsgDTO.getExtParam();
        String id = extParam.get("id");
        String name = extParam.get("name");
        String flowType = extParam.get("flowType");
        List<String> recipientIds = schemeMsgDTO.getRecipientIds();
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", flowType).build()))
                .businessId(id)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_INTERFACE)
                .urgencyLevel(0)
                .messageMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$name$", name)
                        .build())
                .businessNodeCode(InterfaceNodeTypeEnum.NODE_INTERFACE_MANAGEMENT.getCode())
                .businessTypeCode("ProjectScheme")
                .businessTypeName("项目计划")
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$name$", name)
                        .build())
                .messageUrl(String.format(IM_URL, id))
                .messageUrlName("传递单详情")
                .recipientIdList(recipientIds)
                .senderTime(new Date())
                .senderId(userHelper.getUserId())
                .platformId(CurrentUserHelper.getPId())
                .orgId(TenantContextHolder.getTenantId())
                .build();
        return ListUtil.toList(sendMsc);
    }
}
