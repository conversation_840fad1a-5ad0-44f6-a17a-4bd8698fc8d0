<script setup lang="ts">
import { BasicButton, OrionTable } from 'lyra-component-vue3';
import {
  computed, inject, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { openMaterialDrawer } from '/@/views/pms/overhaulManagement/utils';

const router = useRouter();
const planDetailsData: Ref<Record<string, any>> = inject('formData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  maxHeight: 300,
  showToolButton: false,
  showTableSetting: false,
  smallSearchField: [
    'materialNumber',
    'materialName',
    'productCode',
  ],
  api: (params: object) => new Api('/pms/schemeToMaterial').fetch({
    ...params,
    query: {
      planSchemeId: planDetailsData.value?.id,
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '资产编码',
      dataIndex: 'materialNumber',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      dataIndex: 'materialName',
    },
    {
      title: '规格',
      dataIndex: 'specificationModel',
    },
    {
      title: '数量',
      dataIndex: 'demandNum',
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '物资状态',
      dataIndex: 'status',
      customRender({ text }) {
        switch (text) {
          case '1':
            return '';
          case '2':
            return '';
          case '3':
            return '';
          default:
            return '';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      modalTitle: '移除提示！',
      modalContent: '是否确认移除该数据？',
      modal: (record) => deleteApi([record.id]),
    },
  ],
};

const toolButtons = computed(() => [
  {
    label: '添加物资',
    event: 'add',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },
  {
    label: '移除',
    event: 'remove',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
  },
]);

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openMaterialDrawer({
        operationType: 'plan-pick',
        planSchemeId: planDetailsData.value.id,
        repairRound: planDetailsData.value?.repairRound,
        baseCode: planDetailsData.value?.enforceBasePlace,
      }, updateTable);
      break;
    case 'remove':
      Modal.confirm({
        title: '移除提示！',
        content: '确认移除已选择的数据？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/schemeToMaterial/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-bind="item"
          @click="handleToolButton(item.event)"
        >
          {{ item.label }}
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
