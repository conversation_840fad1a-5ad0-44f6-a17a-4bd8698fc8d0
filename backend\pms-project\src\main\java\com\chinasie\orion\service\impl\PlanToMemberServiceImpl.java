package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.PlanToMember;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToMemberRepository;
import com.chinasie.orion.service.PlanToMemberService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/19/18:06
 * @description:
 */
@Service
public class PlanToMemberServiceImpl extends OrionBaseServiceImpl<PlanToMemberRepository, PlanToMember> implements PlanToMemberService {


    @Override
    public PlanToMember saveParam(String id, String principalId, int i) throws Exception {
        PlanToMember planToMember = new PlanToMember();
        planToMember.setFromId(id);
        planToMember.setFromClass("Plan");
        planToMember.setToId(principalId);
        planToMember.setToClass("User");
        planToMember.setType(i);
        this.save(planToMember);
        return planToMember;
    }

    @Override
    public List<PlanToMember> listByFormIdAndType(String id, int i) throws Exception {

        return this.list(new LambdaQueryWrapper<>(PlanToMember.class)
                .eq(PlanToMember::getFromId, id)
                .eq(PlanToMember::getType, i));
    }
}
