<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import type { Ref } from 'vue';
import { onMounted, ref } from 'vue';

const props = defineProps<{
  projectId:string,
  id:string
}>();
const loading:Ref<boolean> = ref(false);
const [register, formMethods] = useForm({
  layout: 'vertical',
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '名称',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请输入名称',
        maxlength: 50,
      },
      required: true,
    },
    {
      field: 'planSubmitTime',
      component: 'DatePicker',
      label: '计划提交时间',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        style: { width: '100%' },
      },
    },
    {
      field: 'writer',
      component: 'ApiSelect',
      label: '编写人',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        api: () => {
          const url = `/pms/project-role-user/${props.projectId}/user/list`;
          return new Api(url).fetch('', '', 'GET');
        },
        labelField: 'name',
        valueField: 'id',
      },
    },

    {
      field: 'resPerson',
      component: 'ApiSelect',
      label: '当前责任方',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        api: () => {
          const url = `/pms/project-role-user/${props.projectId}/user/list`;
          return new Api(url).fetch('', '', 'GET');
        },
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      field: 'resDept',
      component: 'ApiSelect',
      label: '责任部门',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        api: () => {
          const url = `/pms/project-role-user/dept/list?projectId=${props.projectId}`;
          return new Api(url).fetch('', '', 'POST');
        },
        labelField: 'name',
        valueField: 'id',
      },
    },
    {
      field: 'type',
      component: 'ApiSelect',
      label: '类型',
      colProps: {
        span: 12,
      },
      componentProps: {
        api: () => {
          const url = '/pmi/dict/dict1751861244002025472';
          return new Api(url).fetch('', '', 'GET');
        },
        labelField: 'description',
        valueField: 'id',
      },
    },
    {
      field: 'fileStatus',
      component: 'ApiSelect',
      label: '文件状态',
      colProps: {
        span: 12,
      },
      componentProps: {
        api: () => {
          const url = '/pmi/dict/dict1751861961198649344';
          return new Api(url).fetch('', '', 'GET');
        },
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'revId',
      component: 'ApiSelect',
      label: '版本',
      colProps: {
        span: 12,
      },
      componentProps: {
        api: () => {
          const url = '/pmi/dict/dict1751862279340802048';
          return new Api(url).fetch('', '', 'GET');
        },
        labelField: 'description',
        valueField: 'value',
      },
    },

  ],
});

onMounted(async () => {
  if (props.id) {
    try {
      loading.value = true;
      const url = `/pms/deliverGoals/${props.id}`;
      const res = await new Api(url).fetch('', '', 'GET');
      formMethods.setFieldsValue(res);
    } finally {
      loading.value = false;
    }
  }
});
defineExpose({
  formMethods,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
