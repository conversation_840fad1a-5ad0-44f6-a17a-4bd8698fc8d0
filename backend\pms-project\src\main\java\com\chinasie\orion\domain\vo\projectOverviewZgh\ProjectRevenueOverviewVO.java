package com.chinasie.orion.domain.vo.projectOverviewZgh;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目营收
 */
@Data
@ApiModel(value = "ProjectRevenueOverviewVO", description = "项目营收")
public class ProjectRevenueOverviewVO {
    @ApiModelProperty(value = "合同总量")
    private Integer contractCount = 0;

    @ApiModelProperty(value = "营收目标")
    private BigDecimal targetRevenue = BigDecimal.ZERO;

    @ApiModelProperty(value = "实际营收")
    private BigDecimal actualRevenue = BigDecimal.ZERO;

    @ApiModelProperty(value = "待收金额")
    private BigDecimal pendRevenue = BigDecimal.ZERO;

    @ApiModelProperty(value = "子项")
    private List<ProjectRevenueOverviewItemVO> projectRevenues=new ArrayList<>();

    @Data
    @ApiModel(value = "ProjectRevenueOverviewItemVO", description = "项目营收条目")
    public static class ProjectRevenueOverviewItemVO {

        @ApiModelProperty(value = "合同编号")
        private String contractNumber;

        @ApiModelProperty(value = "营收目标")
        private BigDecimal targetRevenue = BigDecimal.ZERO;

        @ApiModelProperty(value = "实际营收")
        private BigDecimal actualRevenue = BigDecimal.ZERO;

        @ApiModelProperty(value = "待收金额")
        private BigDecimal pendRevenue = BigDecimal.ZERO;

    }
}
