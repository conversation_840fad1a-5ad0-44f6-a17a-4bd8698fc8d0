package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierRestrictedRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierRestrictedRecordDTO对象", description = "受限事件记录")
@Data
@ExcelIgnoreUnannotated
public class SupplierRestrictedRecordDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 2)
    private String serialNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 3)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 4)
    private String contractName;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @ExcelProperty(value = "申请编号 ", index = 5)
    private String applicationNumber;

    /**
     * 受限类型
     */
    @ApiModelProperty(value = "受限类型")
    @ExcelProperty(value = "受限类型 ", index = 6)
    private String restrictedType;

    /**
     * 整改状态
     */
    @ApiModelProperty(value = "整改状态")
    @ExcelProperty(value = "整改状态 ", index = 7)
    private String rectificationStatus;

    /**
     * 黑名单类型
     */
    @ApiModelProperty(value = "黑名单类型")
    @ExcelProperty(value = "黑名单类型 ", index = 8)
    private String blacklistType;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 9)
    private String projectName;

    /**
     * 申报日期
     */
    @ApiModelProperty(value = "申报日期")
    @ExcelProperty(value = "申报日期 ", index = 10)
    private Date declarationDate;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 11)
    private String applicant;

    /**
     * 申报公司
     */
    @ApiModelProperty(value = "申报公司")
    @ExcelProperty(value = "申报公司 ", index = 12)
    private String declaringCompany;

    /**
     * 内容描述
     */
    @ApiModelProperty(value = "内容描述")
    @ExcelProperty(value = "内容描述 ", index = 13)
    private String contentDescription;

    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    @ExcelProperty(value = "审批完成时间 ", index = 14)
    private String approvalCompletionTime;

    /**
     * 是否解冻
     */
    @ApiModelProperty(value = "是否解冻")
    @ExcelProperty(value = "是否解冻 ", index = 15)
    private String whetherThawed;

    /**
     * 受限范围
     */
    @ApiModelProperty(value = "受限范围")
    @ExcelProperty(value = "受限范围 ", index = 16)
    private String restrictedScope;

    /**
     * 集团发送SAP
     */
    @ApiModelProperty(value = "集团发送SAP")
    @ExcelProperty(value = "集团发送SAP ", index = 17)
    private String groupSendsSap;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 18)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 19)
    private String mainTableId;


}
