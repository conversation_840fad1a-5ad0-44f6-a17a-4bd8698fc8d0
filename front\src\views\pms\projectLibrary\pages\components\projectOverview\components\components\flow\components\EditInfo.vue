<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import { Checkbox, message, Textarea } from 'ant-design-vue';
import { onMounted, reactive } from 'vue';
import Api from '/@/api';

const props = defineProps({
  baseInfo: {
    type: Object,
    default: () => null,
  },
  projectId: {
    type: String,
    default: '',
  },
});

const state = reactive({
  loadingStatus: false,
  content: undefined,
  attachments: [],
  data: null,
});

onMounted(() => {
  getDetails();
});

async function getDetails() {
  state.loadingStatus = true;
  new Api(`/pms/projectOverview/zgh/node/${props.projectId}/${props.baseInfo?.id}`).fetch({}, '', 'GET').then((data) => {
    Object.assign(state, {
      content: data?.content,
      attachments: data?.attachments ?? [],
      data,
    });
  }).finally(() => {
    state.loadingStatus = false;
  });
}

function uploadChange(listData) {
  state.attachments = listData;
}

defineExpose({
  async save() {
    if (!state.content) {
      message.error('说明不能为空');
      return Promise.reject();
    }

    const { content, attachments, data } = state;

    return new Api(`/pms/projectOverview/zgh/node/${props.projectId}`).fetch({
      content,
      attachments,
      id: data?.id,
      nodeId: props.baseInfo?.id,
      projectId: props.projectId,
    }, '', 'PUT').then(() => {
      message.success('保存成功');
    });
  },
});
</script>

<template>
  <div
    v-loading="state.loadingStatus"
    class="drawar-content p-b-lr p-b-tb"
  >
    <div class="require-title red-star">
      {{ baseInfo?.name ?? '' }}说明：
    </div>
    <Textarea
      v-model:value="state.content"
      class="content-textarea"
      :autosize="{ minRows: 6, maxRows: 10 }"
      :maxlength="1000"
    />
    <div class="m-b-t">
      <UploadList
        :listData="state.attachments"
        type="modal"
        :isSpacing="false"
        :height="400"
        :onChange="uploadChange"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.require-title {
  margin:0 0 ~`getPrefixVar('button-margin')` 0;
  font-weight: 700;
}
.red-star::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun,sans-serif;
  line-height: 1;
  content: "*";
}

.content-textarea {
  margin:0 0 ~`getPrefixVar('button-margin')` 0;
}
</style>
