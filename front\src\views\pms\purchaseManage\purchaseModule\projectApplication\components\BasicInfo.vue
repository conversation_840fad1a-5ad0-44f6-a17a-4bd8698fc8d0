<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import { parseBooleanToRender, setBasicInfo } from '../../utils';
import LineInformation from './LineInformation.vue';
import Api from '/@/api';
import ApplicationProcess from './ApplicationProcess.vue';

const basicInfo = inject('projectApplicationItem');
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '申请单名称',
      field: 'name',
      gridColumn: '1 / span 3',
      wrap: true,
    },
    {
      label: '采购申请单编码',
      field: 'code',
    },
    {
      label: '采购立项完成时间',
      field: 'projectEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购立项号',
      field: 'projectCode',
    },
    {
      label: '文件名称',
      field: 'fileName',
    },
    {
      label: '申请单状态',
      field: 'state',
    },
    {
      label: '申请单类型',
      field: 'type',
    },
    {
      label: '申请单来源',
      field: 'source',
    },
    {
      label: '采购申请金额（元）',
      field: 'money',
    },
    {
      label: '汇率',
      field: 'rate',
    },
    {
      label: '预计开工时间',
      field: 'estimatedBeginTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '质保等级',
      field: 'warrantyLevel',
    },
    {
      label: '申请部门',
      field: 'applicantDept',
    },
    {
      label: '申请人',
      field: 'applicantUser',
    },
    {
      label: '币种',
      field: 'currency',
    },
    {
      label: '与现场安全相关',
      field: 'withSafety',
      formatter: (val) => (isBoolean(val) ? val ? '是' : '否' : '--'),
    },
    {
      label: '采购计划号',
      field: 'purchasePlanCode',
    },
    {
      label: '归口部门',
      field: 'bkDept',
    },
    {
      label: '归口管理',
      field: 'bkManage',
    },
  ]),
  column: 3,
  dataSource: basicInfo,
});
const baseInfoProps2 = reactive({
  list: setBasicInfo([
    {
      label: '建议采购方式',
      field: 'suggestPurchaseWay',
    },
    {
      label: '立项理由详述',
      field: 'purchaseContent',
    },
    {
      label: '推荐供应商名单',
      field: 'recSupList',
    },
    {
      label: '推荐潜在供应商名单',
      field: 'recPtSupList',
    },
    {
      label: '是否有匹配的框架合同',
      field: 'isFrameContrac',
      formatter: parseBooleanToRender,
    },
    {
      label: '框架合同号',
      field: 'contractId',
    },
  ]),
  column: 3,
  dataSource: basicInfo,
});
const route = useRoute();
const itemId = ref(route.params.id);

</script>

<template>
  <ApplicationProcess />
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="采购行信息"
    :isBorder="false"
  >
    <LineInformation />
  </BasicCard>

  <BasicCard
    title="寻源信息"
    :grid-content-props="baseInfoProps2"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
