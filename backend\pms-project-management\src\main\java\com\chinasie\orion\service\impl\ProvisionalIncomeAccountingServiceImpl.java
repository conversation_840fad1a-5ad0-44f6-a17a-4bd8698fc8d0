package com.chinasie.orion.service.impl;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.dto.ProvisionalIncomeAccountingDTO;
import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.entity.ProvisionalIncomeAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.domain.vo.ProvisionalIncomeAccountingVO;

import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProvisionalIncomeAccountingMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProvisionalIncomeAccountingService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProvisionalIncomeAccounting 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:52:56
 */
@Service
@Slf4j
public class ProvisionalIncomeAccountingServiceImpl extends  OrionBaseServiceImpl<ProvisionalIncomeAccountingMapper, ProvisionalIncomeAccounting>   implements ProvisionalIncomeAccountingService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Resource
    private ProvisionalIncomeAccountingMapper provisionalIncomeAccountingMapper;

    @Resource
    private DictBo dictBo;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProvisionalIncomeAccountingVO detail(String id, String pageCode) throws Exception {
        ProvisionalIncomeAccounting provisionalIncomeAccounting =this.getById(id);
        ProvisionalIncomeAccountingVO result = BeanCopyUtils.convertTo(provisionalIncomeAccounting,ProvisionalIncomeAccountingVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param provisionalIncomeAccountingDTO
     */
    @Override
    public  String create(ProvisionalIncomeAccountingDTO provisionalIncomeAccountingDTO) throws Exception {
        ProvisionalIncomeAccounting provisionalIncomeAccounting =BeanCopyUtils.convertTo(provisionalIncomeAccountingDTO,ProvisionalIncomeAccounting::new);
        this.save(provisionalIncomeAccounting);

        String rsp=provisionalIncomeAccounting.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param provisionalIncomeAccountingDTO
     */
    @Override
    public Boolean edit(ProvisionalIncomeAccountingDTO provisionalIncomeAccountingDTO) throws Exception {
        ProvisionalIncomeAccounting provisionalIncomeAccounting =BeanCopyUtils.convertTo(provisionalIncomeAccountingDTO,ProvisionalIncomeAccounting::new);

        this.updateById(provisionalIncomeAccounting);

        String rsp=provisionalIncomeAccounting.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProvisionalIncomeAccountingVO> pages( Page<ProvisionalIncomeAccountingDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProvisionalIncomeAccounting> condition = new LambdaQueryWrapperX<>( ProvisionalIncomeAccounting. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        ProvisionalIncomeAccountingDTO query = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(query)){
            condition.eqIfPresent(ProvisionalIncomeAccounting::getContractId,query.getContractId());
        }
        condition.orderByDesc(ProvisionalIncomeAccounting::getCreateTime);


        Page<ProvisionalIncomeAccounting> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProvisionalIncomeAccounting::new));

        PageResult<ProvisionalIncomeAccounting> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProvisionalIncomeAccountingVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProvisionalIncomeAccountingVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProvisionalIncomeAccountingVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "暂估收入核算信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProvisionalIncomeAccountingDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProvisionalIncomeAccountingExcelListener excelReadListener = new ProvisionalIncomeAccountingExcelListener();
        EasyExcel.read(inputStream,ProvisionalIncomeAccountingDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProvisionalIncomeAccountingDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("暂估收入核算信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountinges =BeanCopyUtils.convertListTo(dtoS,ProvisionalIncomeAccounting::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProvisionalIncomeAccounting-import::id", importId, provisionalIncomeAccountinges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountinges = (List<ProvisionalIncomeAccounting>) orionJ2CacheService.get("pmsx::ProvisionalIncomeAccounting-import::id", importId);
        log.info("暂估收入核算信息导入的入库数据={}", JSONUtil.toJsonStr(provisionalIncomeAccountinges));

        this.saveBatch(provisionalIncomeAccountinges);
        orionJ2CacheService.delete("pmsx::ProvisionalIncomeAccounting-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProvisionalIncomeAccounting-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProvisionalIncomeAccounting> condition = new LambdaQueryWrapperX<>( ProvisionalIncomeAccounting. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProvisionalIncomeAccounting::getCreateTime);
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountinges =   this.list(condition);

        List<ProvisionalIncomeAccountingDTO> dtos = BeanCopyUtils.convertListTo(provisionalIncomeAccountinges, ProvisionalIncomeAccountingDTO::new);

        String fileName = "暂估收入核算信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProvisionalIncomeAccountingDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProvisionalIncomeAccountingVO> vos)throws Exception {
        Map<String, String> incomeConfirmTypeDict = dictBo.getDictValue("income_confirm_type");
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(vo.getIncomeVerifyType())){
                vo.setIncomeVerifyTypeName(incomeConfirmTypeDict.get(vo.getIncomeVerifyType()));
            }
        });



    }

    /**
     * 根据关联合同id获取详情
     * @param contractId
     * @return
     * @throws Exception
     */
    @Override
    public List<ProvisionalIncomeAccounting> detailByContractId(String contractId) throws Exception {
        LambdaQueryWrapperX<ProvisionalIncomeAccounting> provisionalIncomeAccountingLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        provisionalIncomeAccountingLambdaQueryWrapperX.eq(ProvisionalIncomeAccounting::getContractId,contractId);
        List<ProvisionalIncomeAccounting> provisionalIncomeAccountingList = this.list(provisionalIncomeAccountingLambdaQueryWrapperX);

        return provisionalIncomeAccountingList;
    }

    @Override
    public ProvisionalIncomeAccountingVO getTotal(String contractId) {
        if(StrUtil.isBlank(contractId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"合同id为空");
        }
        return provisionalIncomeAccountingMapper.getTotal(contractId);
    }


    public static class ProvisionalIncomeAccountingExcelListener extends AnalysisEventListener<ProvisionalIncomeAccountingDTO> {

        private final List<ProvisionalIncomeAccountingDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProvisionalIncomeAccountingDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProvisionalIncomeAccountingDTO> getData() {
            return data;
        }
    }


}
