package com.chinasie.orion.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.constant.DeliverStatusEnum;
import com.chinasie.orion.domain.dto.PlanCountDTO;
import com.chinasie.orion.domain.dto.PlanCountParamDto;
import com.chinasie.orion.domain.entity.PlanCount;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanCountRepository;
import com.chinasie.orion.service.PlanCountService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/16/19:54
 * @description:
 */
@Service
public class PlanCountServiceImpl extends OrionBaseServiceImpl<PlanCountRepository, PlanCount> implements PlanCountService {


    @Override
    public synchronized void plusCount(PlanCountDTO planCountDTO) throws Exception {
        String uk = planCountDTO.getUk();
        String typeId = planCountDTO.getTypeId();
        if( StrUtil.isBlank(typeId)){
            return;
        }
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.eq(PlanCount::getUk, uk);
        planCountLambdaQueryWrapper.eq(PlanCount::getTypeId,typeId);
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(planCountDTOS)){
            PlanCount planCount = BeanCopyUtils.convertTo(planCountDTO, PlanCount::new);
            this.save(planCount);
        }else{
            PlanCount planCountDTO1 = planCountDTOS.get(0);
            Integer finishCount = planCountDTO1.getFinishCount();
            Integer finishingCount = planCountDTO1.getFinishingCount();
            Integer unFinishCount = planCountDTO1.getUnFinishCount();
            planCountDTO1.setFinishCount(planCountDTO.getFinishCount()+finishCount);
            planCountDTO1.setUnFinishCount(planCountDTO.getUnFinishCount()+unFinishCount);
            planCountDTO1.setFinishingCount(planCountDTO.getFinishingCount()+finishingCount);
            PlanCount planCount = BeanCopyUtils.convertTo(planCountDTO, PlanCount::new);
            this.updateById(planCount);
        }
    }

    @Override
    public synchronized void minusCount(PlanCountDTO planCountDTO) throws Exception {
        Date nowDay = planCountDTO.getNowDay();
        String typeId = planCountDTO.getTypeId();
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.eq(PlanCount::getNowDay, nowDay);
        planCountLambdaQueryWrapper.eq(PlanCount::getTypeId,typeId);
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
        if(!CollectionUtils.isEmpty(planCountDTOS)){
            PlanCount planCountDTO1 = planCountDTOS.get(0);
            Integer finishCount = planCountDTO1.getFinishCount();
            Integer finishingCount = planCountDTO1.getFinishingCount();
            Integer unFinishCount = planCountDTO1.getUnFinishCount();
            if(finishCount > 0){
                planCountDTO1.setFinishCount(planCountDTO.getFinishCount()-finishCount);
                planCountDTO1.setUnFinishCount(planCountDTO.getUnFinishCount()-unFinishCount);
                planCountDTO1.setFinishingCount(planCountDTO.getFinishingCount()-finishingCount);
            }
            PlanCount planCount = BeanCopyUtils.convertTo(planCountDTO, PlanCount::new);
            this.updateById(planCount);
        }
    }

    @Override
    public void updateCount(Integer oldStatus, Integer newStatus, String uk,String type) throws Exception {
        if( StrUtil.isBlank(type)){
            return;
        }
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.eq(PlanCount::getUk, uk);
        planCountLambdaQueryWrapper.eq(PlanCount::getTypeId,type);
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
        if(!CollectionUtils.isEmpty(planCountDTOS)){
            PlanCount planCountDTO1 = planCountDTOS.get(0);
            Integer finishCount = planCountDTO1.getFinishCount();
            Integer finishingCount = planCountDTO1.getFinishingCount();
            Integer unFinishCount = planCountDTO1.getUnFinishCount();
            if(DeliverStatusEnum.UN_START.getStatus().equals(oldStatus)){
                unFinishCount =  unFinishCount-1<=0?0:unFinishCount-1;
            }else if(DeliverStatusEnum.DEALING.getStatus().equals(oldStatus)){
                finishingCount = finishingCount - 1 <= 0 ? 0 : finishingCount - 1;
            }else if(DeliverStatusEnum.DEAL.getStatus().equals(oldStatus)){
                finishCount = finishCount - 1 <= 0 ? 0 : finishCount - 1;
            }
            if(DeliverStatusEnum.UN_START.getStatus().equals(newStatus)){
                unFinishCount= unFinishCount+1;
            }else if(DeliverStatusEnum.DEALING.getStatus().equals(newStatus)){
                finishingCount= finishingCount+1;
            }else if(DeliverStatusEnum.DEAL.getStatus().equals(newStatus)){
                finishCount= finishingCount+1;
            }
            planCountDTO1.setUnFinishCount(unFinishCount);
            planCountDTO1.setFinishingCount(finishingCount);
            planCountDTO1.setFinishCount(finishCount);
            PlanCount planCount = BeanCopyUtils.convertTo(planCountDTO1, PlanCount::new);
            this.updateById(planCount);
        }



    }

    @Override
    public void minusCount(Integer status, String planType,String uk) throws Exception {
        if( StrUtil.isBlank(planType)){
            return;
        }
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.eq(PlanCount::getUk, uk);
        planCountLambdaQueryWrapper.eq(PlanCount::getTypeId,planType);
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
        if(!CollectionUtils.isEmpty(planCountDTOS)){
            PlanCount planCountDTO1 = planCountDTOS.get(0);
            Integer finishCount = planCountDTO1.getFinishCount();
            Integer finishingCount = planCountDTO1.getFinishingCount();
            Integer unFinishCount = planCountDTO1.getUnFinishCount();
            if(DeliverStatusEnum.UN_START.getStatus().equals(status)){
                planCountDTO1.setUnFinishCount(finishCount-1 <=0?0:finishCount-1);
            }else if(DeliverStatusEnum.DEALING.getStatus().equals(status)){
                planCountDTO1.setFinishingCount(finishingCount-1<=0?0:finishingCount-1);
            }else if(DeliverStatusEnum.DEAL.getStatus().equals(status)){
                planCountDTO1.setUnFinishCount(unFinishCount-1<=0?0:unFinishCount-1);
            }
            PlanCount planCount = BeanCopyUtils.convertTo(planCountDTO1, PlanCount::new);
            this.updateById(planCount);
        }

    }

    @Override
    public void minusCountList(List<PlanCountParamDto> planCountParamDtos) throws Exception {
        if(CollectionUtils.isEmpty(planCountParamDtos)){
            return;
        }
        Set<String> ukList = new HashSet<>();
        for (PlanCountParamDto planCountParamDto : planCountParamDtos) {
            ukList.add(planCountParamDto.getUk());
        }
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.in(PlanCount::getUk, ukList.toArray());
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
       if(CollectionUtils.isEmpty(planCountDTOS)){
           return;
       }
        Map<String, PlanCount> ukToEntityMap = planCountDTOS.stream().collect(Collectors.toMap(PlanCount::getUk, o -> o));
        for (PlanCountParamDto planCountParamDto : planCountParamDtos) {
            String uk = planCountParamDto.getUk();
            PlanCount planCountDTO = ukToEntityMap.get(uk);
            Integer status = planCountParamDto.getStatus();
            if(!ObjectUtils.isEmpty(planCountDTO)){
                Integer finishCount = planCountDTO.getFinishCount();
                Integer finishingCount = planCountDTO.getFinishingCount();
                Integer unFinishCount = planCountDTO.getUnFinishCount();
                if(DeliverStatusEnum.UN_START.getStatus().equals(status)){
                    planCountDTO.setUnFinishCount(finishCount-1 <=0?0:finishCount-1);
                }else if(DeliverStatusEnum.DEALING.getStatus().equals(status)){
                    planCountDTO.setFinishingCount(finishingCount-1<=0?0:finishingCount-1);
                }else if(DeliverStatusEnum.DEAL.getStatus().equals(status)){
                    planCountDTO.setUnFinishCount(unFinishCount-1<=0?0:unFinishCount-1);
                }
                ukToEntityMap.put(uk,planCountDTO);
            }
        }
        List<PlanCount> planCountDTOS1 = new ArrayList<>();
        for (Map.Entry<String, PlanCount> stringPlanCountDTOEntry : ukToEntityMap.entrySet()) {
            planCountDTOS1.add(stringPlanCountDTOEntry.getValue());
        }
        this.updateBatchById(planCountDTOS1);
    }


  //  @Scheduled(cron = " 0 0 2 * * ?")
    public void updateCount() throws Exception {
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE,   -1);
        Date afterDay = ca.getTime();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sf.format(afterDay);
        LambdaQueryWrapper<PlanCount> planCountLambdaQueryWrapper = new LambdaQueryWrapper<>(PlanCount.class);
        planCountLambdaQueryWrapper.eq(PlanCount::getDateStr, format);
        List<PlanCount> planCountDTOS = this.list(planCountLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(planCountDTOS)){
            return;
        }
        Date date =new Date();
        String nowDay = sf.format(date);
        List<PlanCount> planCountDTOList1 = new ArrayList<>();
        for (PlanCount planCountDTO : planCountDTOS) {
            PlanCount planCountDTO1 = new PlanCount();
            BeanCopyUtils.copyProperties(planCountDTO,planCountDTO1);
            planCountDTO1.setDateStr(nowDay);
            String uk = String.format("%s:%s:%s",nowDay,planCountDTO.getTypeId(),planCountDTO.getProjectId());
            planCountDTO1.setNowDay(new Date());
            planCountDTO1.setId("");
            planCountDTO1.setUk(uk);
            planCountDTOList1.add(planCountDTO1);
        }
        this.saveBatch(planCountDTOList1);
    }

}
