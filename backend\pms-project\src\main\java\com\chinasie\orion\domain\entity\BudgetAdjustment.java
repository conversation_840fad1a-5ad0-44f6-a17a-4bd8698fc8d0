package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

/**
 * BudgetAdjustment Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@TableName(value = "pmsx_budget_adjustment")
@ApiModel(value = "BudgetAdjustmentEntity对象", description = "预算调整表")
@Data
public class BudgetAdjustment extends ObjectEntity implements Serializable {

    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算申请总金额")
    @TableField(value = "budget_money")
    private BigDecimal budgetMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    @TableField(value = "february_money")
    private BigDecimal februaryMoney;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    @TableField(value = "january_money")
    private BigDecimal januaryMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    @TableField(value = "april_money")
    private BigDecimal aprilMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    @TableField(value = "june_money")
    private BigDecimal juneMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    @TableField(value = "march_money")
    private BigDecimal marchMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    @TableField(value = "july_money")
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    @TableField(value = "august_money")
    private BigDecimal augustMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    @TableField(value = "october_money")
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    @TableField(value = "november_money")
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    @TableField(value = "december_money")
    private BigDecimal decemberMoney;

    @ApiModelProperty(value = "9月预算")
    @TableField(value = "september_money")
    private BigDecimal septemberMoney;

    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    @TableField(value = "first_quarter_money")
    private BigDecimal firstQuarterMoney;

    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    @TableField(value = "second_quarter")
    private BigDecimal secondQuarter;

    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    @TableField(value = "third_quarter")
    private BigDecimal thirdQuarter;

    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    @TableField(value = "fourth_quarter")
    private BigDecimal fourthQuarter;


    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    @TableField(value = "may_money")
    private BigDecimal mayMoney;

    /**
     * 调整单Id
     */
    @ApiModelProperty(value = "调整单Id")
    @TableField(value = "form_id")
    private String formId;

    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    @TableField(value = "budget_id")
    private String budgetId;

    @ApiModelProperty(value = "是否是调整数据改变数据")
    @TableField(value = "is_change")
    private String isChange;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

}
