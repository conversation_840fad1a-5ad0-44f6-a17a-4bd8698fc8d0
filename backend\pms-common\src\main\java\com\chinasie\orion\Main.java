//package com.chinasie.orion;
//
//import com.alibaba.fastjson.JSONObject;
//import com.chinasie.orion.constant.Permission;
//import com.chinasie.orion.util.Homework;
//import com.chinasie.orion.util.StatisticCalculator;
//import com.chinasie.orion.util.TreeInfoProcessor;
//import com.chinasie.orion.domain.vo.NodeVO;
//import com.chinasie.orion.domain.vo.TreeNodeVO;
//
//import java.util.*;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: ${YEAR}/${MONTH}/${DAY}/${TIME}
// * @description:
// */
//public class Main {
//    public static void main(String[] args)
//    {
//        List<NodeVO<Homework>> nodeList = Arrays.asList(
//                new NodeVO<>("1", null, "Root", "wys", new Homework("1", "Root", 0, 0, 0,0.0)),
//                new NodeVO<>("2", "1", "Child1", "Alice",  new Homework("2", "Child1", 0, 0, 0,0.0)),
//                new NodeVO<>("3", "1", "Child2", "yk", new Homework("3", "Child2", 0, 0, 0,0.0))
//        );
//        Map<String,List<NodeVO<Homework>>> idToHomework = new HashMap<>();
//        NodeVO<Homework>  homework1 =new NodeVO<>("DATA001", "工单001", "Child1", "Alice1",  new Homework("DATA001", "工单001", 1, 2, 3,0.0));
//        NodeVO<Homework>  homework2 =new NodeVO<>("DATA002", "工单002", "Child2", "Alice2",  new Homework("DATA002", "工单002", 3, 1, 1,0.0));
////        Homework homework2 =  new Homework("DATA002", "工单002", 3, 2, 1);
//        idToHomework.put("3",Arrays.asList(homework1));
//        idToHomework.put("2",Arrays.asList(homework2));
//
//        // 当前用户ID
//        String currentUserId = "zz";
//
//        // 是否过滤无读取权限的平级节点
//        boolean filterNoReadNodes = true;
//
//        HashMap<String, Set<String>> currentPermissions = new HashMap<>();
//        currentPermissions.put("DATA002", Set.of(Permission.WRITE.name()));
//
//        // 创建树处理器
//        TreeInfoProcessor<NodeVO<Homework>> processor = new TreeInfoProcessor<>(
//                nodeList,
//                NodeVO::getId,
//                NodeVO::getParentId,
//                NodeVO::getRspUserId,
//                currentUserId,
//                currentPermissions,
//                filterNoReadNodes,
//                idToHomework
//        );
//        TreeNodeVO<NodeVO<Homework>> root = processor.getRoot();
//        StatisticCalculator.calculateStatisticsRecursively(root);
//        // 获取根节点
//        System.out.println("数据 " + JSONObject.toJSONString(root));
//    }
//}