package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.SupplierInfoDTO;
import com.chinasie.orion.management.domain.dto.SupplierProductsDTO;
import com.chinasie.orion.management.domain.vo.SupplierProductsVO;
import com.chinasie.orion.management.service.SupplierProductsService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SupplierProducts 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@RestController
@RequestMapping("/supplierProducts")
@Api(tags = "可提供产品")
public class SupplierProductsController {

    @Autowired
    private SupplierProductsService supplierProductsService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "可提供产品", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<SupplierProductsVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SupplierProductsVO rsp = supplierProductsService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据采购编号查询详情及可提供产品
     *
     * @param supplierProductsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据采购编号查询详情")
    @RequestMapping(value = "/getProductByCode", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据采购编号查询详情", type = "可提供产品", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<List<SupplierProductsVO>> getProductByCode(@RequestBody SupplierProductsDTO supplierProductsDTO) throws Exception {

        //查询可提供产品信息
        List<SupplierProductsVO> productsVO = supplierProductsService.getByCode(supplierProductsDTO.getSupplierCode());

        return new ResponseDTO<>(productsVO);
    }


    /**
     * 新增
     *
     * @param supplierProductsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#supplierProductsDTO.name}}】", type = "可提供产品", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SupplierProductsDTO supplierProductsDTO) throws Exception {
        String rsp = supplierProductsService.create(supplierProductsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param supplierProductsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#supplierProductsDTO.name}}】", type = "可提供产品", subType = "编辑", bizNo = "{{#supplierProductsDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SupplierProductsDTO supplierProductsDTO) throws Exception {
        Boolean rsp = supplierProductsService.edit(supplierProductsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "可提供产品", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = supplierProductsService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "可提供产品", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = supplierProductsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "可提供产品", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<SupplierProductsVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<SupplierProductsDTO> pageRequest) throws Exception {
        Page<SupplierProductsVO> rsp = supplierProductsService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("可提供产品导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "可提供产品", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        supplierProductsService.downloadExcelTpl(response);
    }

    @ApiOperation("可提供产品导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "可提供产品", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = supplierProductsService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("可提供产品导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "可提供产品", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierProductsService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消可提供产品导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "可提供产品", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = supplierProductsService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("可提供产品导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "可提供产品", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        supplierProductsService.exportByExcel(searchConditions, response);
    }
}
