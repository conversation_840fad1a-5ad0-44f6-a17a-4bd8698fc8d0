package com.chinasie.orion.domain.dto.file;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * RevisionClassDTO 版本类DTO对象
 *
 * <AUTHOR> sie
 * @since 2021-06-12
 */
@ApiModel(value = "RevisionClassDTO对象", description = "版本类")
public class RevisionClassDTO extends ObjectDTO {

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    private Integer revOrder;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    private String nextRevId;

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    private String initialRevId;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private String previousRevId;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String revId;

    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    private String revKey;

    public Integer getRevOrder(){
        return revOrder;
    }

    public void setRevOrder(Integer revOrder) {
        this.revOrder = revOrder;
    }

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNextRevId(){
        return nextRevId;
    }

    public void setNextRevId(String nextRevId) {
        this.nextRevId = nextRevId;
    }

    public String getInitialRevId(){
        return initialRevId;
    }

    public void setInitialRevId(String initialRevId) {
        this.initialRevId = initialRevId;
    }

    public String getPreviousRevId(){
        return previousRevId;
    }

    public void setPreviousRevId(String previousRevId) {
        this.previousRevId = previousRevId;
    }

    public String getRevId(){
        return revId;
    }

    public void setRevId(String revId) {
        this.revId = revId;
    }

    public String getRevKey(){
        return revKey;
    }

    public void setRevKey(String revKey) {
        this.revKey = revKey;
    }

}

