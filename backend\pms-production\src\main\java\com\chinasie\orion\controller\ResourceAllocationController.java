package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.person.ObjJobDTO;
import com.chinasie.orion.domain.dto.source.SearchDTO;
import com.chinasie.orion.domain.vo.major.JobDashBoardVO;
import com.chinasie.orion.domain.vo.resource.MaterialSourceAllocationVO;
import com.chinasie.orion.domain.vo.resource.PersonSourceAllocationVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ResourceAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:03
 * @description:
 */
@RestController
@RequestMapping("/resource-allocation")
@Api(tags = "资源调配")
public class ResourceAllocationController {

    @Autowired
    private ResourceAllocationService resourceAllocationService;


    /**
     * 人员作业信息
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员作业信息")
    @RequestMapping(value = "/person/job/info", method = RequestMethod.POST)
    public ResponseDTO<PersonSourceAllocationVO> personAllocationCount(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        PersonSourceAllocationVO rsp = resourceAllocationService.personAllocationCount(searchDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * @param personJobDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员作业工作时间保存")
    @RequestMapping(value = "/person/job/work/save", method = RequestMethod.POST)
    public ResponseDTO<Boolean> savePersonJobDate(@RequestBody List<ObjJobDTO> personJobDTOList) throws Exception {
        Boolean rsp = resourceAllocationService.savePersonJobDate(personJobDTOList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 物资作业工作时间保存
     * @param materialJobDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资作业工作时间保存")
    @RequestMapping(value = "/material/job/work/save", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> saveMaterialJobDate(@RequestBody List<ObjJobDTO> materialJobDTOList) throws Exception {
        Boolean rsp = resourceAllocationService.saveMaterialJobDate(materialJobDTOList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 物资作业信息
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资作业信息")
    @RequestMapping(value = "/material/job/info", method = RequestMethod.POST)
    public ResponseDTO<MaterialSourceAllocationVO> materialAllocationCount(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        MaterialSourceAllocationVO rsp = resourceAllocationService.materialAllocationCount(searchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 人员作业信息
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员重叠数据信息")
    @RequestMapping(value = "/person/overlap/job/info", method = RequestMethod.POST)
    public ResponseDTO<PersonSourceAllocationVO> personOverlapAllocationCount(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        PersonSourceAllocationVO rsp = resourceAllocationService.personOverlapAllocationCount(searchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 人员作业信息
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资重叠数据信息")
    @RequestMapping(value = "/material/overlap/job/info", method = RequestMethod.POST)
    public ResponseDTO<MaterialSourceAllocationVO> materialOverlapAllocationCount(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        MaterialSourceAllocationVO rsp = resourceAllocationService.materialOverlapAllocationCount(searchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取重叠人天-物资
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取重叠天-物资")
    @RequestMapping(value = "/material/overlap/days", method = RequestMethod.POST)
    public ResponseDTO<Integer> materialOverlapDays(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        Integer rsp = resourceAllocationService.materialOverlapDays(searchDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取重叠天-人员")
    @RequestMapping(value = "/person/overlap/days", method = RequestMethod.POST)
    public ResponseDTO<Integer> personOverlapDays(@Validated  @RequestBody SearchDTO searchDTO) throws Exception {
        Integer rsp = resourceAllocationService.personOverlapDays(searchDTO);
        return new ResponseDTO<>(rsp);
    }
}
