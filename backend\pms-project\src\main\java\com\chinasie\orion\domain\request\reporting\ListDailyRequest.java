package com.chinasie.orion.domain.request.reporting;

import com.chinasie.orion.constant.reporting.TimeSearchTypeEnums;
import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/14/20:19
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListDailyRequest   extends ProjectDailyStatementDTO implements Serializable {

    @ApiModelProperty(value = "时间类型", required = true, example = "ALL")
    private TimeSearchTypeEnums timeType;

    @ApiModelProperty(value = "开始时间", required = true, example = "2021-01-01")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", required = true, example = "2021-01-01")
    private Date endTime;

    @ApiModelProperty(value = "负责人", required = true, example = "jkdsafa1231231")
    private String resp;

    @ApiModelProperty(value = "关键字", required = true, example = "关键词")
    private String keyword;

    @ApiModelProperty(value = "项目id", required = true)
    private List<String> projectIds = new ArrayList<>();
    @ApiModelProperty(value = "页面类型 false- 普通页面 true-审核页面 默认为false", required = true, example = "")
    private Boolean pageType= Boolean.FALSE;
}
