<script setup lang="ts">
import {
  OrionTable, randomString, getDictByNumber, isPower, BasicButton,
} from 'lyra-component-vue3';
import {
  ComputedRef,
  h, inject, onMounted, ref,
} from 'vue';
import {
  Input, message, Select, DatePicker,
} from 'ant-design-vue';
import { getList, saveOrRemove } from '/@/views/pms/api/projectContribute';
import dayjs from 'dayjs';

const props = defineProps<{
  projectId:string
}>();
const projectData: ComputedRef<Record<string, any>> = inject('formData');
const detailAuthList = projectData.value?.detailAuthList || [];
const loading = ref(false);
const loadingSave = ref(false);
const dataSource:any = ref([]);

const options = ref([]);

const tableOptionsStand = {
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: `add${isPower('PMS_XMXQ_container_12_06_button_03', detailAuthList) ? '' : '|delete'}|enable|disable`,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  rowSelection: {},
  columns: [
    {
      title: '专利/奖项',
      dataIndex: 'typeName',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Select, {
            placeholder: '请选择',
            allowClear: true,
            value: record.type,
            onChange: (value) => {
              record.type = value;
            },
            options: options.value,
            style: { width: '100%' },
          });
        }
        return record.typeName;
      },
    },

    {
      title: '类型',
      dataIndex: 'awardType',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Input, {
            value: record.awardType,
            maxlength: 128,
            placeholder: '请输入',
            allowClear: true,
            onChange: (e) => {
              record.awardType = e.target.value;
            },
            style: { width: '100%' },
          });
        }
        return record.awardType;
      },
    },
    {
      title: '名称',
      dataIndex: 'name',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Input, {
            value: record.name,
            maxlength: 128,
            placeholder: '请输入',
            allowClear: true,
            onChange: (e) => {
              record.name = e.target.value;
            },
            style: { width: '100%' },
          });
        }
        return record.name;
      },
    },
    {
      title: '主要人员',
      dataIndex: 'majorUser',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Input, {
            value: record.majorUser,
            maxlength: 128,
            placeholder: '请输入',
            allowClear: true,
            onChange: (e) => {
              record.majorUser = e.target.value;
            },
            style: { width: '100%' },
          });
        }
        return record.majorUser;
      },
    },
    {
      title: '授权/获取时间',
      dataIndex: 'authorizationTime',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(DatePicker, {
            value: record.authorizationTime,
            valueFormat: 'YYYY-MM-DD',
            onChange: (value) => {
              record.authorizationTime = value;
            },
            style: { width: '100%' },
          });
        }
        return record.authorizationTime;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '修改',
      isShow: (record: Record<string, any>) => !record.isEdit && isPower('PMS_XMXQ_container_12_06_button_04', detailAuthList),
      onClick(record: Record<string, any>) {
        record.isEdit = !record.isEdit;
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMXQ_container_12_06_button_05', detailAuthList),
      modal(record: Record<string, any>) {
        return new Promise((resolve, reject) => {
          dataSource.value = dataSource.value.filter((item) => item.id !== record.id);
          resolve('操作成功');
        });
      },
    },
  ],
  batchDeleteApi: ({ ids }) =>
    // 返回自己定义的请求
    new Promise((resolve, reject) => {
      dataSource.value = dataSource.value.filter((item) => !ids.some((id) => item.id === id));
      resolve('操作成功');
    })
  ,
};

const handleAdd = () => {
  dataSource.value.unshift({
    id: `ZDY_${randomString()}`,
    type: null,
    awardType: '',
    name: '',
    majorUser: '',
    authorizationTime: '',
    isEdit: true,
  });
};
const handleSave = async () => {
  for (let i = 0; i < dataSource.value.length; i++) {
    const item = dataSource.value[i];
    if (!item.type) {
      return message.warn(`请选择第${i + 1}行【专利/奖项】`);
    }
    if (!item.awardType?.trim()) {
      return message.warn(`第${i + 1}行【类型】不能为空`);
    }
    if (!item.name?.trim()) {
      return message.warn(`第${i + 1}行【名称】不能为空`);
    }
    if (!item.majorUser?.trim()) {
      return message.warn(`第${i + 1}行【主要人员】不能为空`);
    }
    if (!item.authorizationTime) {
      return message.warn(`请选择第${i + 1}行【授权/获取时间】`);
    }
  }

  const data = dataSource.value.map((item) => ({
    id: item.id.startsWith('ZDY_') ? null : item.id,
    type: item.type,
    awardType: item.awardType,
    name: item.name,
    majorUser: item.majorUser,
    authorizationTime: item.authorizationTime,
  }));

  try {
    loadingSave.value = true;
    await saveOrRemove(props.projectId, data);
    await handleGetList();
  } finally {
    loadingSave.value = false;
  }
};

const handleGetList = async () => {
  try {
    loading.value = true;
    const result = await getList(props.projectId);
    dataSource.value = result.map((item) => ({
      id: item.id,
      type: item.type,
      typeName: item.typeName,
      awardType: item.awardType,
      name: item.name,
      majorUser: item.majorUser,
      authorizationTime: item.authorizationTime ? dayjs(item.authorizationTime).format('YYYY-MM-DD') : '',
      isEdit: false,
    }));
  } finally {
    loading.value = false;
  }
};
const initOptions = async () => {
  const data:any = await getDictByNumber('pms_project_contribute');
  options.value = data.map((item) => ({
    value: item.number,
    label: item.name,
  }));
};
onMounted(() => {
  handleGetList();
  initOptions();
});
</script>

<template>
  <div style="height: 330px;overflow: hidden">
    <OrionTable
      ref="tableRefStand"
      :options="tableOptionsStand"
      :loading="loading"
      :dataSource="dataSource"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_06_button_01', detailAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新增
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_06_button_02', detailAuthList)"
          icon="sie-icon-baocun"
          :loading="loadingSave"
          @click="handleSave"
        >
          保存
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
