package com.chinasie.orion.domain.dto.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * GoodsStatusStatistics DTO对象
 *
 * <AUTHOR>
 * @since 2023-12-21 14:07:54
 */
@ApiModel(value = "GoodsStatusStatisticsDTO对象", description = "物资状态趋势统计表")
@Data
public class GoodsStatusStatisticsDTO  implements Serializable{

    @ApiModelProperty(value = "统计ID")
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    private Date nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    private String uk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 未审核数量
     */
    @ApiModelProperty(value = "未审核数量")
    private Integer noAuditCount;

    /**
     * 审核中数量
     */
    @ApiModelProperty(value = "审核中数量")
    private Integer underReviewCount;

    /**
     * 已审核数量
     */
    @ApiModelProperty(value = "已审核数量")
    private Integer reviewedCount;

    /**
     * 已入库数量
     */
    @ApiModelProperty(value = "已入库数量")
    private Integer storeCount;

}
