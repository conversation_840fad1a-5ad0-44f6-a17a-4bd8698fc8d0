<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="会议纪要"
  >
    <UploadList
      type="page"
      :listApi="listApi"
      :saveApi="saveApi"
      :deleteApi="deleteApi"
      :batchDeleteApi="batchDeleteApi"
    />

    <!--    <OrionTable-->
    <!--      ref="tableRef"-->
    <!--      :options="tableOptions"-->
    <!--      :onSelectionChange="selectionChange"-->
    <!--    >-->
    <!--      <template #toolbarLeft>-->
    <!--        <BasicButton-->
    <!--          type="primary"-->
    <!--          icon="orion-icon-upload"-->
    <!--          @click="onUploadFile"-->
    <!--        >-->
    <!--          上传附件-->
    <!--        </BasicButton>-->
    <!--        <BasicButton-->
    <!--          icon="sie-icon-del"-->
    <!--          :disabled="selectRows.length===0"-->
    <!--          @click="handleBatchDel"-->
    <!--        >-->
    <!--          移除-->
    <!--        </BasicButton>-->
    <!--      </template>-->
    <!--    </OrionTable>-->
    <!--    <BasicUpload-->
    <!--      ref="uploadRef"-->
    <!--      :max-number="100"-->
    <!--      :isClassification="false"-->
    <!--      :isToolRequired="false"-->
    <!--      :isButton="false"-->
    <!--      :onSaveChange="saveChange"-->
    <!--    />-->
  </Layout>
</template>

<script setup lang="ts">
import {
  Layout, OrionTable, BasicButton, BasicUpload, downLoadById, openFile, UploadList,
} from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { declarationData, declarationDataId } from '../keys';
import { getListDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';

const tableRef = ref(null);
const dataId: Ref = inject(declarationDataId);
const data = inject(declarationData);
const uploadRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);

async function listApi(params) {
  return new Api('').fetch({
    query: {
      dataId: unref(dataId),
    },
  }, 'pms/scientificResearchDemandDeclareFileInfo/meeting/getPage', 'POST');
}

async function saveApi(successAll) {
  const params = successAll.map((item) => ({
    ...item,
    dataId: unref(dataId),
    projectId: data.value?.projectId,
  }));
  return new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting/saveBatch').fetch(params, '', 'POST');
}

async function deleteApi(deleteApi) {
  return new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting/removeBatch').fetch([deleteApi.id], '', 'DELETE');
}

async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting/removeBatch').fetch(rows.map((item) => item.id), '', 'DELETE');
}

// const tableOptions = {
//   deleteToolButton: 'add|enable|disable|delete',
//   showToolButton: false,
//   rowSelection: {},
//   api: (params) => new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting/getPage').fetch({
//     ...params,
//     query: {
//       dataId: unref(dataId),
//     },
//   }, '', 'POST'),
//   columns: [
//     {
//       title: '文件名称',
//       dataIndex: 'fullName',
//     },
//     {
//       title: '文件大小',
//       dataIndex: 'fileSize',
//       customRender({ text }) {
//         return text ? `${text}kb` : '';
//       },
//     },
//     {
//       title: '创建人',
//       dataIndex: 'creatorName',
//     },
//     {
//       title: '修改时间',
//       dataIndex: 'createTime',
//       customRender({ text }) {
//         return text ? dayjs(text).format('YYYY-MM-DD') : '';
//       },
//     },
//     {
//       title: '操作',
//       dataIndex: 'action',
//       slots: { customRender: 'action' },
//       width: 140,
//       fixed: 'right',
//     },
//   ],
//   actions: [
//     {
//       text: '查看',
//       onClick(record) {
//         openFile(record);
//       },
//     },
//     {
//       text: '下载',
//       onClick(record) {
//         downLoadById(record.id);
//       },
//     },
//     {
//       text: '删除',
//       modal: (record) => batchDelete([record.id]),
//     },
//   ],
// };

function updateTable() {
  tableRef.value?.reload();
}

// 打开文件上传弹窗
function onUploadFile() {
  uploadRef.value.openModal(true);
}

// 保存回调
async function saveChange(successAll, cb) {
  const params = successAll.map((item) => ({
    ...item.result,
    dataId: unref(dataId),
    projectId: data.value?.projectId,
  }));

  new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting').fetch(params, 'saveBatch', 'POST').then(() => {
    message.success('上传成功');
    tableRef.value?.reload();
  }).catch(() => {
    tableRef.value?.reload();
  });
}

// 表格多选
function selectionChange({ rows }) {
  selectRows.value = rows;
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/scientificResearchDemandDeclareFileInfo/meeting').fetch(ids, 'removeBatch', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
</script>

<style scoped lang="less">

</style>
