<!--
 * @Description:作业信息指标盘
 * @Autor: laotao117
 * @Date: 2024-08-21 18:36:08
 * @LastEditors: laotao117
 * @LastEditTime: 2024-10-29 18:57:21
-->
<template>
  <BasicCard
    :isBorder="true"
    title="作业信息指标盘"
    class="card-border active-box"
  >
    <template #titleRight>
      <div class="card-right-box">
        <DatePicker
          picker="year"
          format="YYYY"
        />
      </div>
    </template>
    <div class="card-row">
      <Row>
        <Col span="12">
          <div class="table-box border-right">
            <div class="table-box-tit">
              现场未完工作业（截至{{ state.currentDate }}）
            </div>
            <div class="table-box-desc">
              <div
                class="table-box-desc-1"
                @click="handleHClick('', 'job')"
              >
                <Icon
                  class="table-box-desc-1-icon"
                  icon="orion-icon-appstore"
                />
                <div class="table-box-desc-1-tips">
                  作业总数
                </div>
                <div class="table-box-desc-1-num">
                  {{ state.jobData?.riskLevelTotal }}
                </div>
              </div>

              <div class="table-box-desc-2">
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('一级', 'job')"
                >
                  <div class="table-box-desc-2-gk-num">
                    {{ state.jobData?.childHeightRisks?.['一级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.jobData?.childHeightRisks?.['一级']?.riskLevel }}管控
                  </div>
                </div>
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('二级', 'job')"
                >
                  <div class="table-box-desc-2-gk-num gk-2-color">
                    {{ state.jobData?.childHeightRisks?.['二级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.jobData?.childHeightRisks?.['二级']?.riskLevel }}管控
                  </div>
                </div>
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('三级', 'job')"
                >
                  <div class="table-box-desc-2-gk-num gk-3-color">
                    {{ state.jobData?.childHeightRisks?.['三级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.jobData?.childHeightRisks?.['三级']?.riskLevel }}管控
                  </div>
                </div>
              </div>
            </div>
            <div class="table-box-radio">
              <!-- RadioGroup -->
              <RadioGroup v-model:value="state.jobDataradioValue">
                <RadioButton value="1">
                  作业地点
                </RadioButton>
                <RadioButton value="2">
                  作业类型
                </RadioButton>
              </RadioGroup>
            </div>
            <div class="table-box-com">
              <Table
                :columns="state.jobDataradioValue === '1' ? columns : columns2"
                :dataSource="state.jobDataradioValue === '1' ? state.jobData?.addressSub : state.jobData?.riskTypeNameSub"
                size="small"
                :scroll="{ y: 378 }"
                :pagination="false"
              />
            </div>
          </div>
        </Col>
        <Col span="12">
          <div class="table-box">
            <div class="table-box-tit ">
              <div>计划开工作业查询</div>
              <div class="table-box-tit-s">
                <!-- RangePicker -->
                <RangePicker v-model:value="state.planTime" />
                <Button
                  type="primary"
                  @click="getPlanData()"
                >
                  查询
                </Button>
                <Button @click="reset()">
                  重置
                </Button>
              </div>
            </div>
            <div class="table-box-desc">
              <div
                class="table-box-desc-1"
                @click="handleHClick('', 'plan')"
              >
                <Icon
                  class="table-box-desc-1-icon"
                  icon="orion-icon-appstore"
                />
                <div class="table-box-desc-1-tips">
                  作业总数
                </div>
                <div class="table-box-desc-1-num">
                  {{ state.planData?.riskLevelTotal }}
                </div>
              </div>

              <div class="table-box-desc-2">
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('一级', 'plan')"
                >
                  <div class="table-box-desc-2-gk-num">
                    {{ state.planData?.childHeightRisks?.['一级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.planData?.childHeightRisks?.['一级']?.riskLevel }}管控
                  </div>
                </div>
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('二级', 'plan')"
                >
                  <div class="table-box-desc-2-gk-num gk-2-color">
                    {{ state.planData?.childHeightRisks?.['二级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.planData?.childHeightRisks?.['二级']?.riskLevel }}管控
                  </div>
                </div>
                <div
                  class="table-box-desc-2-gk"
                  @click="handleHClick('三级', 'plan')"
                >
                  <div class="table-box-desc-2-gk-num gk-3-color">
                    {{ state.planData?.childHeightRisks?.['三级']?.riskLevelTotal }}
                  </div>
                  <div class="table-box-desc-2-gk-tips">
                    {{ state.planData?.childHeightRisks?.['三级']?.riskLevel }}管控
                  </div>
                </div>
              </div>
            </div>
            <div class="table-box-radio">
              <!-- RadioGroup -->
              <RadioGroup v-model:value="state.planDataradioValue">
                <RadioButton value="1">
                  作业地点
                </RadioButton>
                <RadioButton value="2">
                  作业类型
                </RadioButton>
              </RadioGroup>
            </div>
            <div class="table-box-com">
              <Table
                :columns="state.planDataradioValue === '1' ? columns11 : columns12"
                :dataSource="state.planDataradioValue === '1' ? state.planData?.addressSub : state.planData?.riskTypeNameSub"
                size="small"
                :scroll="{ y: 378 }"
                :pagination="false"
              />
            </div>
          </div>
        </Col>
      </Row>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">
import {
  Button,
  Col,
  RadioButton,
  RadioGroup,
  RangePicker,
  Row,
  Table,
  DatePicker,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  BasicCard,
  Icon,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';
import {
  h,
  onActivated,
  onMounted,
  reactive,
  ref,
  Ref,
  nextTick,
} from 'vue';
import Api from '/@/api';

const state: any = reactive({
  yearValue: dayjs(),
  jobData: {},
  planData: {},
  // 当前日期
  currentDate: dayjs().format('YYYY-MM-DD'),
  riskLevel: '',
  jobDataradioValue: '1',
  planDataradioValue: '1',
  // planTime: [dayjs(), dayjs()],
  // 前一个月
  planTime: [dayjs(), dayjs()],
  type: 'job',
  jobAddressName: '',
  judgmentStandards: '',

});
const columns = [
  {
    title: '作业地点',
    dataIndex: 'jobAddressName',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '作业总数',
    dataIndex: 'riskLevelTotal',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '一级管控',
    dataIndex: 'riskLevelTotal1',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '一级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '二级管控',
    dataIndex: 'riskLevelTotal2',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '二级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '三级管控',
    dataIndex: 'riskLevelTotal3',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '三级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
];
const columns2 = [
  {
    title: '作业类型',
    dataIndex: 'judgmentStandards',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '作业总数',
    dataIndex: 'riskLevelTotal',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '一级管控',
    dataIndex: 'riskLevelTotal1',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '一级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '二级管控',
    dataIndex: 'riskLevelTotal2',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '二级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '三级管控',
    dataIndex: 'riskLevelTotal3',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '三级', 'job'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
];
const columns11 = [
  {
    title: '作业地点',
    dataIndex: 'jobAddressName',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '作业总数',
    dataIndex: 'riskLevelTotal',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '一级管控',
    dataIndex: 'riskLevelTotal1',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '一级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '二级管控',
    dataIndex: 'riskLevelTotal2',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '二级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '三级管控',
    dataIndex: 'riskLevelTotal3',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '三级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
];
const columns12 = [
  {
    title: '作业类型',
    dataIndex: 'judgmentStandards',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '作业总数',
    dataIndex: 'riskLevelTotal',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '一级管控',
    dataIndex: 'riskLevelTotal1',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '一级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '二级管控',
    dataIndex: 'riskLevelTotal2',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '二级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
  {
    title: '三级管控',
    dataIndex: 'riskLevelTotal3',
    customRender: ({ text, record }) => h('div', {
      onClick: () => handleCellClick(record, '三级', 'plan'),
      style: {
        cursor: 'pointer',
      },
    }, text),
  },
];
onMounted(() => {
  init();
});
onActivated(() => {
  init();
});
function handleCellClick(record: any, riskLevel, type: string) {
  // planDataradioValue;
  // console.log('🚀 ~ handleCellClick ~ type:', type);
  // console.log('🚀 ~ handleCellClick ~ record:', record);
  // 数据不准临时关闭return
  // return;
  state.jobAddressName = record.jobAddressName;
  state.judgmentStandards = record.judgmentStandards;
  nextTick(() => {
    openMTable(riskLevel, type);
  });
}
function handleHClick(riskLevel, type) {
  state.jobAddressName = '';
  state.judgmentStandards = '';
  openMTable(riskLevel, type);
}

function init() {
  new Api('/pms').fetch({}, 'ampere/ring/board/statistics/job/total/undone', 'POST').then((res) => {
    res.addressSub.forEach((item: any) => {
      item.riskLevelTotal1 = item.childHeightRisks?.['一级']?.riskLevelTotal || 0;
      item.riskLevelTotal2 = item.childHeightRisks?.['二级']?.riskLevelTotal || 0;
      item.riskLevelTotal3 = item.childHeightRisks?.['三级']?.riskLevelTotal || 0;
    });
    // riskTypeNameSub
    res.riskTypeNameSub.forEach((item: any) => {
      item.riskLevelTotal1 = item.childHeightRisks?.['一级']?.riskLevelTotal || 0;
      item.riskLevelTotal2 = item.childHeightRisks?.['二级']?.riskLevelTotal || 0;
      item.riskLevelTotal3 = item.childHeightRisks?.['三级']?.riskLevelTotal || 0;
    });
    state.jobData = res;
  });
  getPlanData();
}
function reset() {
  state.planTime = [];
  getPlanData();
}
// getPlanData
function getPlanData() {
  // planTime
  let planTime = state.planTime;
  let planStartTime = '';
  let planEndTime = '';
  if (planTime && planTime?.length !== 0) {
    planStartTime = dayjs(planTime[0]).format('YYYY-MM-DD');
    planEndTime = dayjs(planTime[1]).format('YYYY-MM-DD');
  }

  new Api('/pms').fetch({
    planStartTime,
    planEndTime,
  }, 'ampere/ring/board/statistics/query/plan/start/work/total', 'POST').then((res) => {
    res.addressSub.forEach((item: any) => {
      item.riskLevelTotal1 = item.childHeightRisks?.['一级']?.riskLevelTotal || 0;
      item.riskLevelTotal2 = item.childHeightRisks?.['二级']?.riskLevelTotal || 0;
      item.riskLevelTotal3 = item.childHeightRisks?.['三级']?.riskLevelTotal || 0;
    });
    // riskTypeNameSub
    res.riskTypeNameSub.forEach((item: any) => {
      item.riskLevelTotal1 = item.childHeightRisks?.['一级']?.riskLevelTotal || 0;
      item.riskLevelTotal2 = item.childHeightRisks?.['二级']?.riskLevelTotal || 0;
      item.riskLevelTotal3 = item.childHeightRisks?.['三级']?.riskLevelTotal || 0;
    });
    state.planData = res;
  });
}

const milestoneTable = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  isFilter2: false,
  showTableSetting: false,
  height: 550,
  api: (params) => new Api('/pms').fetch({
    ...params,
    query: {
      riskLevel: state.riskLevel,
      type: state.type,
      planStartTime: state.type === 'plan' && state.planTime?.length !== 0 ? dayjs(state.planTime[0]).format('YYYY-MM-DD') : '',
      planEndTime: state.type === 'plan' && state.planTime?.length !== 0 ? dayjs(state.planTime[1]).format('YYYY-MM-DD') : '',
      jobAddressName: state.jobAddressName,
      judgmentStandards: state.judgmentStandards,
    },
  }, state.type === 'job' ? 'ampere/ring/board/statistics/job/undone/details' : 'ampere/ring/board/statistics/query/plan/start/work/details', 'POST'),

  // 作业主题、工作地址、风险作业类型、作业部门、项目负责人、项目负责人电话、管理人员、管理人员号码、监督人员、监督人员号码。
  columns: [
    {
      title: '作业主题',
      dataIndex: 'workTopics',
    },
    {
      title: '工作地址',
      dataIndex: 'jobAddressName',
    },
    {
      title: '风险作业类型',
      dataIndex: 'riskTypeName',
      customRender: ({ record }) => `${record.riskLevel}/${record.judgmentStandards}`,

    },
    {
      title: '作业部门',
      dataIndex: 'operatingDeptName',
    },
    {
      title: '项目负责人',
      dataIndex: 'workOwnerName',
    },
    {
      title: '项目负责人电话',
      dataIndex: 'workOwnerPhone',
    },

    {
      title: '管理人员',
      dataIndex: 'managerName',
    },
    {
      title: '管理人员号码',
      dataIndex: 'managerPhone',
    },
    {
      title: '监督人员',
      dataIndex: 'checkName',
    },
    {
      title: '监督人员号码',
      dataIndex: 'checkPhone',
    },

  ],
};

function openMTable(riskLevel, type) {
  state.type = type;
  state.riskLevel = riskLevel;
  const selectRef: Ref = ref();
  openModal({
    title: state.type === 'job' ? `现场未完成作业清单（截至${state.currentDate}）` : '计划开工作业查询',
    width: 1300,
    height: 750,
    footer: {
      isCancel: false,
      isOk: false,
    },
    content() {
      return h(OrionTable, {
        ref: selectRef,
        options: milestoneTable,
      });
    },
  });
}
</script>
<style scoped lang="less">
.card-border {
  border: 1px solid var(--ant-border-color-base);
  padding: 10px 15px;
  margin: 0 !important;
  overflow: hidden;
}

.active-box {

  // 鼠标移入时的样式 显示阴影效果
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

.gk-2-color {
  color: rgba(250, 186, 74, 1) !important;
}

.gk-3-color {
  color: rgba(163, 183, 255, 1) !important;
}

.table-box {
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

  padding: 20px;
  padding-top: 10px;

  &-tit {
    height: 40px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #333333;
    line-height: 19px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-s {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 10px;

    }
  }

  &-desc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;

    &-1 {
      width: 174px;
      height: 48px;
      background: rgba(22, 93, 255, 0.05);
      border-radius: 2px 2px 2px 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
      // 鼠标手
      cursor: pointer;

      &-icon {
        color: rgba(22, 93, 255, 1);
        font-size: 16px;
      }

      &-tips {
        color: rgba(22, 93, 255, 1);
        font-size: 14px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #165DFF;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      &-num {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 20px;
        color: #165DFF;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }

    &-2 {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 15px 0;

      &-gk {
        width: 72px;
        text-align: center;
        // 鼠标手
        cursor: pointer;

        &-num {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 16px;
          color: #F67C66;
          line-height: 30px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

        &-tips {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 30px;
          text-align: center;
          font-style: normal;
          text-transform: none
        }

      }
    }

  }

  &-com {
    margin-top: 20px;
    height: 378px;
  }

}

.border-right {
  border-right: 1px solid #eee;
}

.card-row {
  margin: -20px;
}

.card-right-box {
  position: relative;
  right: -400px;
}
</style>