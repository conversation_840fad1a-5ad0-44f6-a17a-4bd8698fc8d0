package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.domain.entity.projectStatistics.RiskStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.RiskManagementService;
import com.chinasie.orion.service.projectStatistics.RiskStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ProjectRiskStatisticXxlJob {
    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private RiskStatusStatisticsService riskStatusStatisticsService;

    @XxlJob("projectRiskStatisticDailyCount")
    public void projectRiskStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<RiskStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RiskStatusStatistics :: getDateStr,nowDate);
        riskStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<RiskManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(RiskManagement :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(RiskManagement :: getStatus);
        schemeLambdaQueryWrapper.groupBy(RiskManagement :: getProjectId, RiskManagement :: getRiskType);
        schemeLambdaQueryWrapper.select(" project_id projectId, risk_type riskType," +
                "IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) unFinishedCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as finishedCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as processCount");;
        List<Map<String, Object>> maps = riskManagementService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<RiskStatusStatistics> riskStatusStatisticsList = new ArrayList<>();
        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            String type = String.valueOf(p.get("riskType"));
            RiskStatusStatistics riskStatusStatistics =new RiskStatusStatistics();
            riskStatusStatistics.setNowDay(new Date());
            riskStatusStatistics.setDateStr(nowDate);
            riskStatusStatistics.setProjectId(projectId);
            riskStatusStatistics.setUk(nowDate+":"+type+":"+projectId);
            riskStatusStatistics.setTypeId(type);
            riskStatusStatistics.setUnFinishedCount(Integer.parseInt(p.get("unFinishedCount").toString()));
            riskStatusStatistics.setFinishedCount(Integer.parseInt(p.get("finishedCount").toString()));
            riskStatusStatistics.setProcessCount(Integer.parseInt(p.get("processCount").toString()));
            riskStatusStatisticsList.add(riskStatusStatistics);
        });
        riskStatusStatisticsService.saveBatch(riskStatusStatisticsList);
    }
}
