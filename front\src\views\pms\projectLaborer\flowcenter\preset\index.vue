<template>
  <Layout :options="{ body: { scroll: true } }">
    <div class="style-line">
      <span>选择流程：</span>
      <div>
        <a-select
          v-model:value="initValue"
          style="width: 260px"
          @change="onChange"
        >
          <select-option
            v-for="item in flowList"
            :key="item.processDefinitionId"
            :value="item.processDefinitionId"
          >
            {{ item.deploymentName }}
          </select-option>
        </a-select>
      </div>
      <div>
        <a-button @click="onSubmitFlow">
          提交
        </a-button>
      </div>
    </div>
    <orion-table
      v-if="options.auto.params.procDefId"
      ref="btnsTableRef"
      :options="options"
      row-key="taskName"
    >
      <template #action="{ record, index }">
        <a @click="onOpenModal(record, index)">指定人员</a>
      </template>
    </orion-table>
    <TransferModal
      ref="transferModalRef"
      :data-source-api="dataApi"
      :default-expand-all="true"
      row-key="id"
      :target-keys="selectIds"
      :is-tree="true"
      show-search
      module-title="人员"
      @submit="onSubmitSelectReviewer"
    />
  </Layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref,
} from 'vue';
import {
  Input, Button, Select, message,
} from 'ant-design-vue';
import {
  TransferModal, Layout, OrionTable,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
// import OrionTable from '/@/components/OrionTable';
// import TransferModal from '/@/components/TransferModal';
import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api/index';
import { _joinStr, onHandleTransferUpdate } from '../util/util';
import { workflowApi } from '../util/apiConfig';

interface IdType {
  id: string;
}
export default defineComponent({
  name: 'Btns',
  components: {
    Layout,
    OrionTable,
    Input,
    AButton: Button,
    ASelect: Select,
    SelectOption: Select.Option,
    TransferModal,
  },
  setup() {
    const userStore: any = useUserStore();
    const btnsTableRef = ref<Nullable<any>>(null); // table的ref
    const transferModalRef: any = ref(null);
    const state: any = reactive({
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/all-tasks`,
          params: {
            query: {
              userId: userStore.getUserInfo.id,
              procDefId: '',
            },
          },
        },
        columns: [
          {
            title: '节点名称',
            dataIndex: 'taskName',
          },
          {
            title: '流程负责人',
            dataIndex: 'assignees',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
      flowList: [],
      initValue: '',
      currentItem: null,
      selectIds: [],
      user: '',
      organizations: [],
      roles: [],
      currentIndex: 0,
      isSingle: false,
    });

    new Api(workflowApi)
      .fetch(
        {
          startedBy: userStore.getUserInfo.id,
          userId: userStore.getUserInfo.id,
        },
        'act-inst/my/page',
        'POST',
      )
      .then((res) => {
        state.flowList = res.content;
        state.options.auto.params.procDefId = res.content[0].processDefinitionId;
        state.initValue = res.content[0].processDefinitionId;
        if (btnsTableRef.value) {
          btnsTableRef.value.reload();
        }
      });

    function _changeStrToArr(str) {
      let arr: IdType[] = [];
      if (str) {
        let ids = str.split(',');
        ids.forEach((item) => {
          arr.push({
            id: item,
          });
        });
      }
      return arr;
    }
    // 校验是否配置了审批人
    function _checkReviewer() {
      let check = true;
      const data = btnsTableRef.value.getDataSource();
      for (let i = 0; i < data.length; i++) {
        if (!data[i].selectIds) {
          check = false;
          break;
        }
      }
      return check;
    }

    return {
      btnsTableRef,
      transferModalRef,
      ...toRefs(state),
      onChange(value) {
        console.log(value);
        state.options.auto.params.procDefId = value;
        btnsTableRef.value.reload();
      },
      dataApi: () => new Api(workflowApi).fetch(
        {
          id: state.user,
          organizations: state.organizations,
          roles: state.roles,
        },
        'act-user-feign/orgRoleUserList',
        'POST',
      ),
      onSubmitSelectReviewer(add) {
        if (state.isSingle && add.targetItems.length > 1) {
          message.warn('此节点为单人审批，不能选择多个审批人');
          return;
        }
        let res = onHandleTransferUpdate(add, state.currentItem, 'assignees', 'selectIds');
        if (res) {
          const { users, ids } = res;
          btnsTableRef.value.getDataSource()[state.currentIndex].assignees = users.join(',');
          btnsTableRef.value.getDataSource()[state.currentIndex].selectIds = ids.join(',');
          btnsTableRef.value.setProps(btnsTableRef.value.getDataSource());
        }
        transferModalRef.value.openModal(false);
      },
      onOpenModal(row, index) {
        state.currentItem = row;
        state.currentIndex = index;
        state.user = row.user;
        state.selectIds = row?.selectIds ? row?.selectIds.split(',') : [];
        state.isSingle = !row.multiInst; // 看是否多人审批
        state.organizations = _changeStrToArr(row.organization);
        state.roles = _changeStrToArr(row.role);
        transferModalRef.value.openModal();
      },
      onSubmitFlow() {
        if (!_checkReviewer()) {
          message.warn('请选中流程节点负责人');
          return;
        }
        let tableData = btnsTableRef.value.getDataSource();
        const params: any = {
          prearrangeds: [],
          procDefId: state.initValue,
          userId: userStore.getUserInfo.id,
        };
        tableData.forEach((item) => {
          params.prearrangeds.push({
            assignees: item.selectIds.split(','),
            taskDefinitionKey: item.taskDefinitionKey,
          });
        });
        new Api(workflowApi).fetch(params, 'act-inst', 'POST').then(() => {
          message.success('操作成功');
        });
      },
    };
  },
});
</script>
<style scoped lang="less">
.style-line {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
