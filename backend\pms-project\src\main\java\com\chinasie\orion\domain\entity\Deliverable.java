package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/18:09
 * @description:
 */
@Data
@TableName(value = "pms_deliverable")
@ApiModel(value = "Deliverable对象", description = "交付物")
public class Deliverable extends RevisionClass {
    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    @TableField(value = "plan_id")
    private String planId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @TableField(value = "delivery_time")
    private Date deliveryTime;

    /**
     *  预计交付时间
     */
    @ApiModelProperty(value = "预计交付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(value = "predict_delivery_time")
    private Date predictDeliverTime;

    /**
     *  项目Id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_Id")
    private String documentId;
}
