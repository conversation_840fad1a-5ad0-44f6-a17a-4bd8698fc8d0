package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * ExcessiveCost VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:23:54
 */
@ApiModel(value = "ExcessiveCostVO对象", description = "超额")
@Data
public class ExcessiveCostVO extends  ObjectVO   implements Serializable{

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String flowStatus;


    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String flowNo;


    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;


    /**
     * 超标类型编号
     */
    @ApiModelProperty(value = "超标类型编号")
    private String excessiveTypeNo;


    /**
     * 超标类型名称
     */
    @ApiModelProperty(value = "超标类型名称")
    private String excessiveTypeName;


    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    private String taskNo;


    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date startTime;


    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endTime;


    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    private Integer days;


    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;


    /**
     * 住宿类型
     */
    @ApiModelProperty(value = "住宿类型")
    private String type;


    /**
     * 实际住宿总金额
     */
    @ApiModelProperty(value = "实际住宿总金额")
    private BigDecimal actualAmount;


    /**
     * 超出金额
     */
    @ApiModelProperty(value = "超出金额")
    private BigDecimal excessAmount;




}
