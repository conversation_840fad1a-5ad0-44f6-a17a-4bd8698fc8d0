<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    title="搜索"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <BasicTitle title="搜索属性">
      <a-form layout="vertical">
        <a-form-item label="关键字">
          <a-input
            v-model:value="father.form.keyWord"
            placeholder="请输入关键字"
            allow-clear
            size="large"
          />
        </a-form-item>
      </a-form>
    </BasicTitle>

    <div class="drawer-footer">
      <a-button
        class="footer-btn"
        size="large"
        @click="father.visible = false"
      >
        取消
      </a-button>
      <a-button
        class="footer-btn-primary"
        size="large"
        type="primary"
        @click="submit"
      >
        确认
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import { computed, reactive, toRefs } from 'vue';
import {
  Drawer, Form, Input, Button,
} from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

export default {
  name: 'SearchAll',
  components: {
    BasicTitle,
    AButton: Button,
    AInput: Input,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: computed({
        get() {
          return props.data;
        },
      }),
    });
    function submit() {
      state.father.visible = false;
      emit('submit', state.father.form.keyWord);
    }
    return {
      ...toRefs(state),
      submit,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    .footer-btn {
      margin-left: 10px;
      margin-right: 20px;
      width: 130px;
      border-radius: 4px;
    }
    .footer-btn-primary {
      margin-right: 20px;
      width: 130px;
      border-radius: 4px;
      background: #5172dc;
    }
  }
</style>
