package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.EditLog;
import com.chinasie.orion.domain.dto.EditLogDTO;
import com.chinasie.orion.domain.vo.EditLogVO;



import com.chinasie.orion.service.EditLogService;
import com.chinasie.orion.repository.EditLogMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * EditLog 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:25:09
 */
@Service
@Slf4j
public class EditLogServiceImpl extends  OrionBaseServiceImpl<EditLogMapper, EditLog>   implements EditLogService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  EditLogVO detail(String id,String pageCode) throws Exception {
        EditLog editLog =this.getById(id);
        EditLogVO result = BeanCopyUtils.convertTo(editLog,EditLogVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param editLogDTO
     */
    @Override
    public  String create(EditLogDTO editLogDTO) throws Exception {
        EditLog editLog =BeanCopyUtils.convertTo(editLogDTO,EditLog::new);
        this.save(editLog);

        String rsp=editLog.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param editLogDTO
     */
    @Override
    public Boolean edit(EditLogDTO editLogDTO) throws Exception {
        EditLog editLog =BeanCopyUtils.convertTo(editLogDTO,EditLog::new);

        this.updateById(editLog);

        String rsp=editLog.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<EditLogVO> pages( Page<EditLogDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<EditLog> condition = new LambdaQueryWrapperX<>( EditLog. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(EditLog::getCreateTime);


        Page<EditLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), EditLog::new));

        PageResult<EditLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<EditLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<EditLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), EditLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "调整记录表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", EditLogDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            EditLogExcelListener excelReadListener = new EditLogExcelListener();
        EasyExcel.read(inputStream,EditLogDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<EditLogDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("调整记录表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<EditLog> editLoges =BeanCopyUtils.convertListTo(dtoS,EditLog::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::EditLog-import::id", importId, editLoges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<EditLog> editLoges = (List<EditLog>) orionJ2CacheService.get("pmsx::EditLog-import::id", importId);
        log.info("调整记录表导入的入库数据={}", JSONUtil.toJsonStr(editLoges));

        this.saveBatch(editLoges);
        orionJ2CacheService.delete("pmsx::EditLog-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::EditLog-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<EditLog> condition = new LambdaQueryWrapperX<>( EditLog. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(EditLog::getCreateTime);
        List<EditLog> editLoges =   this.list(condition);

        List<EditLogDTO> dtos = BeanCopyUtils.convertListTo(editLoges, EditLogDTO::new);

        String fileName = "调整记录表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", EditLogDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<EditLogVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class EditLogExcelListener extends AnalysisEventListener<EditLogDTO> {

        private final List<EditLogDTO> data = new ArrayList<>();

        @Override
        public void invoke(EditLogDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<EditLogDTO> getData() {
            return data;
        }
    }


}
