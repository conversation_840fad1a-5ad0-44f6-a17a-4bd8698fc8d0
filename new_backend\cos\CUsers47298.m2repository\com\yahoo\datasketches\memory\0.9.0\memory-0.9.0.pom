<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2015, Yahoo! Inc.
     Licensed under the terms of the Apache License 2.0.
     See LICENSE file at the project root for terms. -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>memory</artifactId>
    <name>${project.parent.groupId}:${project.artifactId}</name>
    <description>Memory contains interfaces and classes to allocate and access off-heap memory</description>

    <parent>
        <groupId>com.yahoo.datasketches</groupId>
        <artifactId>sketches</artifactId>
        <version>0.9.0</version>
    </parent>
    
</project>
