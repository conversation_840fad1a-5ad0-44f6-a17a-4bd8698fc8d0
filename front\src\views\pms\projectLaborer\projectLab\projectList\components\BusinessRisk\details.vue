<script setup lang="ts">
import {
  BasicTableAction, IDataStatus, isPower, Layout3, useDrawer,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, onMounted, provide, Ref, ref, watch, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { WorkflowAction, WorkflowProps } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import Process from '/@/views/pms/components/Process.vue';
import BasicInfo from './modal/BasicInfo.vue';
import Analyze from './modal/Analyze.vue';
import AssociationPlan from '/@/views/pms/components/associationPlan/associationPlan.vue';
import AssociationQuestion from '/@/views/pms/components/associationQuestion/associationQuestion.vue';
import AssociationAlter from '/@/views/pms/components/associationAlter/associationAlter.vue';
import AssociationReview from '/@/views/pms/components/associationReview/associationReview.vue';
import AddTableNode from './components/AddTableNode.vue';
import { filterDataByPowerCode } from '/@/views/pms/utils/utils';

interface DetailsDataType {
  id?: string,
  name?: string,
  className?: string,
  projectCode?: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,
}

const [register, { openDrawer }] = useDrawer();
const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const powerData: Ref = ref();
provide('powerData', powerData);

const planPowerCode = {
  addCode: 'PMS_FXGLXQ_container_02_01_button_01',
  deleteCode: 'PMS_FXGLXQ_container_02_01_button_02',
  checkCode: 'PMS_FXGLXQ_container_02_01_button_04',
};
const questionPowerCode = {
  addCode: 'PMS_FXGLXQ_container_02_02_button_01',
  quoteCode: 'PMS_FXGLXQ_container_02_02_button_07',
  removeCode: 'PMS_FXGLXQ_container_02_02_button_02',
  editCode: 'PMS_FXGLXQ_container_02_02_button_03',
  deleteCode: 'PMS_FXGLXQ_container_02_02_button_08',
  checkCode: 'PMS_FXGLXQ_container_02_02_button_04',
  closeCode: 'PMS_FXGLXQ_container_02_02_button_05',
  activateCode: 'PMS_FXGLXQ_container_02_02_button_06',
};
const alterPowerCode = {
  addCode: 'PMS_FXGLXQ_container_02_03_button_01',
  editCode: 'PMS_FXGLXQ_container_02_03_button_02',
  deleteCode: 'PMS_FXGLXQ_container_02_03_button_03',
  checkCode: 'PMS_FXGLXQ_container_02_03_button_04',
  closeCode: 'PMS_FXGLXQ_container_02_03_button_05',
};
const reviewPowerCode = {
  addCode: 'PMS_FXGLXQ_container_02_04_button_01',
  removeCode: 'PMS_FXGLXQ_container_02_04_button_02',
};

const detailsData: Ref<DetailsDataType> = ref({});
provide('formData', computed(() => detailsData.value));
const workflowActionRef = ref();
const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: detailsData.value,
  afterEvent: async () => {
    await getDetails();
    //  processViewRef.value?.init();
  },
}));
const pageType: Ref<string> = ref('risk');
const menuData = computed(() => filterDataByPowerCode([
  {
    id: 'basicInfo',
    name: '基本信息',
    powerCode: 'PMS_FXGLXQ_container_01',
  },
  {
    name: '风险受影响分析',
    id: 'analyze',
    powerCode: 'PMS_FXGLXQ_container_05',
  },
  {
    name: '关联内容',
    id: 'content',
    powerCode: 'PMS_FXGLXQ_container_02',
    children: [
      {
        name: '关联计划',
        id: 'plan',
        powerCode: 'PMS_FXGLXQ_container_02_01',
      },
      {
        name: '关联问题',
        id: 'question',
        powerCode: 'PMS_FXGLXQ_container_02_02',
      },
      // {
      //   name: '关联变更',
      //   id: 'changeApply',
      //   isShow: isPower('PMS_FXGLXQ_container_02_03', powerData),
      // },
      // {
      //   name: '评审单',
      //   id: 'review',
      //   isShow: isPower('PMS_FXGLXQ_container_02_04', powerData),
      // },
    ],
  },
  {
    name: '流程',
    id: 'process',
    powerCode: 'PMS_FXGLXQ_container_03',
  },
], powerData.value));

watch(() => menuData.value, (value) => {
  if (!actionId.value) {
    actionId.value = value?.[0]?.id;
  }
}, {
  deep: true,
});

function menuChange({ id }) {
  actionId.value = id;
}

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms').fetch({
      pageCode: 'PMS7451',
    }, `risk-management/detail/${dataId.value}`, 'GET');
    powerData.value = result?.detailAuthList || [];
    result.projectCode = result.number;
    result.ownerName = result.creatorName;
    detailsData.value = result;
    workflowActionRef.value?.setProps({
      businessData: detailsData.value,
    });
    if (route.query.type) {
      pageType.value = 'check';
    }
    actionId.value = menuData.value[0].id;
  } finally {
    loading.value = false;
  }
}

const actions = computed(() => [
  {
    text: '添加流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: () => workflowActionRef.value?.isAdd && isPower('PMS_FXGLXQ_container_04_button_02', powerData.value),
    onClick() {
      workflowActionRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => isPower('PMS_FXGLXQ_container_04_button_01', powerData.value),
    onClick() {
      openDrawer(true, {
        type: 'edit',
        data: detailsData.value,
      });
    },
  },
]);

// 关联计划Api
async function getPlanTableDataApi(params) {
  return new Api('/pas').fetch(params, `risk-management/relation/plan/${detailsData.value.id}`, 'POST');
}

async function deletePlanBatchApi(params) {
  return new Api('/pas').fetch(params, 'risk-management/relation/plan/batch', 'DELETE');
}

async function addPlanTableApi(params) {
  return new Api('/pas').fetch(params, 'risk-management/relation/plan', 'POST');
}

// 关联问题Api
async function getQuestionTableDataApi(params) {
  return new Api('/pas').fetch({
    ...params,
    query: {
      riskId: detailsData.value.id,
    },
  }, 'riskRelationQuestion/relationQuestion/page', 'POST');
}

async function deleteQuestionBatchApi(params) {
  return new Api('/pas').fetch(params, `riskRelationQuestion/relationQuestion/${detailsData.value.id}`, 'DELETE');
}

async function addQuestionTableApi(params) {
  return new Api('/pas').fetch(params, `riskRelationQuestion/relationQuestion/createQuestion/${detailsData.value.id}`, 'POST');
}

// 关联变更Api
async function getAlterTableDataApi(params) {
  return new Api('/pas').fetch({
    ...params,
    query: {
      dataSourceId: detailsData.value.id,
    },
  }, 'ecr/riskRelationEcr/page', 'POST');
}

async function addAlterTableApi(params) {
  return new Api('/pas').fetch(params, 'ecr/riskRelationEcr/createEcr', 'POST');
}

// 关联评审
async function getReviewTableDataApi(params) {
  return new Api('/pms').fetch(params, `risk-management/relation/reviewFrom/lists/${detailsData.value.id}`, 'POST');
}

async function deleteReviewBatchApi(ids) {
  return new Api('/pms').fetch({
    fromIds: ids,
    toId: detailsData.value.id,
  }, 'risk-management/relation/reviewFrom/remove', 'DELETE');
}

async function addReviewTableApi(data) {
  let params = {
    toId: detailsData.value.id,
    fromIds: data.map((item) => item.id),
  };
  return new Api('/pms').fetch(params, 'risk-management/relation/reviewFrom', 'POST');
}

function updateData() {
  getDetails();
}
</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="detailsData"
    :type="2"
    @menuChange="menuChange"
  >
    <template
      v-if="detailsData?.id"
      #header-right
    >
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <div
      v-if="detailsData?.id"
      class="layout-content"
    >
      <BasicInfo
        v-if="'basicInfo'===actionId"
        :details="detailsData"
      />
      <Analyze
        v-if="actionId==='analyze'"
        :formId="detailsData.id"
        :page-type="pageType"
      />
      <AssociationPlan
        v-if="actionId==='plan'"
        :formId="detailsData.id"
        :page-type="pageType"
        :getPlanTableDataApi="getPlanTableDataApi"
        :deletePlanBatchApi="deletePlanBatchApi"
        :addPlanTableApi="addPlanTableApi"
        :power-code="planPowerCode"
      />
      <AssociationQuestion
        v-if="actionId==='question'"
        :formId="detailsData.id"
        :page-type="pageType"
        :getQuestionTableDataApi="getQuestionTableDataApi"
        :deleteQuestionBatchApi="deleteQuestionBatchApi"
        :addQuestionTableApi="addQuestionTableApi"
        :power-code="questionPowerCode"
      />
      <AssociationAlter
        v-if="actionId==='changeApply'"
        :formId="detailsData.id"
        :page-type="pageType"
        :getAlterTableDataApi="getAlterTableDataApi"
        :addAlterTableApi="addAlterTableApi"
        :power-code="alterPowerCode"
      />
      <AssociationReview
        v-if="actionId==='review'"
        :formId="detailsData.id"
        :page-type="pageType"
        selectType="check"
        :getReviewTableDataApi="getReviewTableDataApi"
        :deleteReviewBatchApi="deleteReviewBatchApi"
        :addReviewTableApi="addReviewTableApi"
        :power-code="reviewPowerCode"
      />
      <Process
        v-if="actionId==='process'"
      />
    </div>
    <template
      v-if="!route.query.type"
      #footer
    >
      <WorkflowAction
        v-if="detailsData.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>

    <AddTableNode
      :formId="detailsData.id"
      @register="register"
      @update="updateData"
    />
  </Layout3>
</template>

<style scoped lang="less">
.layout-content {
  height: 100%;
  padding-top: 1px;
}
</style>
