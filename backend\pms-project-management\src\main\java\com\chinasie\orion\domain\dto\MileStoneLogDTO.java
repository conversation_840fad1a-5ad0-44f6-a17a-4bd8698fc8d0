package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MileStoneLog DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
@ApiModel(value = "MileStoneLogDTO对象", description = "里程碑执行记录")
@Data
@ExcelIgnoreUnannotated
public class MileStoneLogDTO extends  ObjectDTO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 0)
    private String milestoneId;

    /**
     * 执行操作
     */
    @ApiModelProperty(value = "执行操作")
    @ExcelProperty(value = "执行操作 ", index = 1)
    private String editDesc;

    /**
     * 执行说明
     */
    @ApiModelProperty(value = "执行说明")
    @ExcelProperty(value = "执行说明 ", index = 2)
    private String editMessage;

    /**
     * 附件数
     */
    @ApiModelProperty(value = "附件数")
    @ExcelProperty(value = "附件数 ", index = 3)
    private Integer fileCount;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @ExcelProperty(value = "执行人 ", index = 4)
    private String editPerson;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @ExcelProperty(value = "执行时间 ", index = 5)
    private Date editTime;




}
