package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * IedBaseLineInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@ApiModel(value = "IedBaseLineInfoVO对象", description = "ied基线信息表")
@Data
public class IedBaseLineInfoVO extends ObjectVO implements Serializable{

    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    @ApiModelProperty("所含基线数量")
    private Long count;

}
