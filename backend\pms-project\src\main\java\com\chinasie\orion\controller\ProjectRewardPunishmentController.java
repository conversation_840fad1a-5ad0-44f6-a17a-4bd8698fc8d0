package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectRewardPunishmentDTO;
import com.chinasie.orion.domain.vo.ProjectRewardPunishmentVO;
import com.chinasie.orion.service.ProjectRewardPunishmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectRewardPunishment 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@RestController
@RequestMapping("/projectRewardPunishment")
@Api(tags = "项目奖惩情况")
public class  ProjectRewardPunishmentController  {

    @Autowired
    private ProjectRewardPunishmentService projectRewardPunishmentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目奖惩情况", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectRewardPunishmentVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectRewardPunishmentVO rsp = projectRewardPunishmentService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 保存项目奖惩情况
     * @param projectId
     * @param projectRewardPunishmentDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "保存项目奖惩情况")
    @RequestMapping(value = "/saveOrRemove", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】保存项目奖惩情况", type = "项目奖惩情况", subType = "保存项目奖惩情况", bizNo = "#{{projectId}}")
    public ResponseDTO<Boolean> saveOrRemove(@RequestParam(value = "projectId") String projectId, @RequestBody List<ProjectRewardPunishmentDTO> projectRewardPunishmentDTOList) throws Exception {
        Boolean rsp = projectRewardPunishmentService.saveOrRemove(projectRewardPunishmentDTOList, projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取项目奖惩情况列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取项目奖惩情况列表")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取项目奖惩情况列表", type = "项目奖惩情况", subType = "列表", bizNo = "#{{projectId}}")
    public ResponseDTO<List<ProjectRewardPunishmentVO>> getList(@RequestParam(value = "projectId") String projectId) throws Exception {
        List<ProjectRewardPunishmentVO> rsp = projectRewardPunishmentService.getList(projectId);
        return new ResponseDTO<>(rsp);
    }
}
