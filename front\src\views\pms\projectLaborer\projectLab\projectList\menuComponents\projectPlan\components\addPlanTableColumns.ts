import {
  computed, h, ref, Ref,
} from 'vue';
import { message, Select as ASelect, Spin } from 'ant-design-vue';
import { getDictByNumber, Select, SelectDictVal } from 'lyra-component-vue3';
import Api from '/@/api';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { updateEdit } from '../index';

export const flagOptions = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

export function getColumns(updateTable: Function = () => {
}) {
  const optionsMap: Ref<Map<string, any[]>> = ref(new Map());
  const loadingMap: Ref<Map<string, boolean>> = ref(new Map());
  return {
    add: [
      {
        title: '是否作业',
        required: true,
        dataIndex: 'isWorkJob',
        width: 140,
        customRender({ text, record }) {
          // @ts-ignore
          return h(ASelect, {
            class: 'w-full',
            value: text,
            options: flagOptions,
            onChange(value: boolean) {
              record.isWorkJob = value;
              if (value === false) {
                record.enforceType = null;
                record.enforceBasePlace = null;
                record.enforceBasePlaceName = null;
                record.enforceScope = null;
                record.workContent = null;
                record.repairRound = null;
              }
            },
          });
        },
      },
      {
        title: '实施类型',
        dataIndex: 'enforceType',
        width: 140,
        customRender({ text, record }) {
          return h(SelectDictVal, {
            class: 'w-full',
            value: text,
            disabled: record?.isWorkJob === false,
            dictNumber: 'pms_enforce_Type',
            onChange(value: string) {
              record.enforceType = value;
              if (value === 'pms_not_dc_job' || value === 'pms_overseas_job') {
                record.enforceScope = null;
                record.workContent = null;
                record.repairRound = null;
                if (value === 'pms_overseas_job') {
                  record.enforceBasePlace = null;
                }
              }
            },
          });
        },
      },
      {
        title: '实施地点',
        dataIndex: 'enforceBasePlace',
        width: 140,
        customRender({ text, record, index }) {
          return h(Select, {
            class: 'w-full',
            value: text,
            disabled: record?.enforceType === 'pms_overseas_job' || record?.isWorkJob === false,
            async onDropdownVisibleChange(visible: boolean) {
              if (visible && (!optionsMap.value.get('enforceBasePlace') || optionsMap.value.get('enforceBasePlace').length === 0)) {
                loadingMap.value.set('enforceBasePlace', true);
                try {
                  const result = await new Api('/pms/base-place/list').fetch('', '', 'POST');
                  optionsMap.value.set('enforceBasePlace', result || []);
                } finally {
                  loadingMap.value.set('enforceBasePlace', false);
                }
              }
            },
            options: computed(() => optionsMap.value.get('enforceBasePlace')),
            fieldNames: {
              label: record?.enforceType === 'pms_not_dc_job' ? 'city' : 'name',
              value: 'code',
            },
            onChange(value: string, option: { name: string }) {
              record.repairRound = '';
              record.enforceBasePlaceName = option?.name;
              record.enforceBasePlace = value;
            },
          }, loadingMap.value.get('enforceBasePlace') ? {
            notFoundContent: () => h(Spin, {
              size: 'small',
            }),
          } : '');
        },
      },
      {
        title: '实施区域',
        dataIndex: 'enforceScope',
        width: 140,
        customRender({ text, record }) {
          return h(SelectDictVal, {
            class: 'w-full',
            value: text,
            disabled: record?.enforceType === 'pms_overseas_job' || record?.enforceType === 'pms_not_dc_job' || record?.isWorkJob === false,
            dictNumber: 'pms_enforce_scope',
            onChange(value: string) {
              record.enforceScope = value;
            },
          });
        },
      },
      {
        title: '工作内容',
        dataIndex: 'workContent',
        width: 140,
        customRender({ text, record }) {
          return h(SelectDictVal, {
            class: 'w-full',
            value: text,
            disabled: record?.enforceType === 'pms_overseas_job' || record?.enforceType === 'pms_not_dc_job' || record?.isWorkJob === false,
            dictNumber: 'pms_work_content',
            onChange(value: string) {
              record.workContent = value;
              if (value !== 'pms_major_repair_con') {
                record.repairRound = null;
              }
            },
          });
        },
      },
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
        width: 140,
        customRender({ text, record, index }) {
          // @ts-ignore
          return h(ASelect, {
            class: 'w-full',
            value: text,
            async onDropdownVisibleChange(visible: boolean) {
              if (visible) {
                loadingMap.value.set(`repairRound${index}`, true);
                try {
                  const result = await new Api('/pms/major-repair-plan/list').fetch({
                    baseCode: record?.enforceBasePlace,
                    isFinish: false,
                  }, '', 'POST');
                  optionsMap.value.set(`repairRound${index}`, result || []);
                } finally {
                  loadingMap.value.set(`repairRound${index}`, false);
                }
              } else {
                optionsMap.value.set(`repairRound${index}`, []);
              }
            },
            options: computed(() => optionsMap.value.get(`repairRound${index}`)),
            disabled: record?.workContent !== 'pms_major_repair_con' || record?.enforceType === 'pms_overseas_job' || record?.enforceType === 'pms_not_dc_job' || record?.isWorkJob === false,
            fieldNames: {
              label: 'repairRound',
              value: 'repairRound',
            },
            onChange(value: string) {
              record.repairRound = value;
            },
          }, loadingMap.value.get(`repairRound${index}`) ? {
            notFoundContent: () => h(Spin, {
              size: 'small',
            }),
          } : '');
        },
      },
    ],
    view: [
      {
        title: '是否作业',
        dataIndex: 'isWorkJob',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            editFlag: false,
            record,
            text: text ? '是' : text === false ? '否' : '',
            componentValue: text,
            componentProps: {
              options: flagOptions,
            },
            onSubmit(option: { label: string, value: boolean }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                isWorkJob: option.value,
              }).then(() => {
                record.isWorkJob = option.value;
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
      {
        title: '实施类型',
        dataIndex: 'enforceTypeName',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text,
            componentValue: record?.enforceType,
            componentProps: {
              api: () => getDictByNumber('pms_enforce_Type'),
              fieldNames: {
                label: 'name',
                value: 'value',
              },
            },
            onSubmit(option: { name: string, value: string }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                enforceType: option.value,
                enforceTypeName: option.name,
              }).then(() => {
                record.enforceType = option.value;
                record.enforceTypeName = option.name;
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
      {
        title: '实施地点',
        dataIndex: 'enforceBasePlaceName',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text,
            componentValue: record?.enforceBasePlace,
            componentProps: {
              api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
              fieldNames: {
                label: record?.enforceType === 'pms_not_dc_job' ? 'city' : 'name',
                value: 'code',
              },
            },
            onSubmit(option: { name: string, code: string }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                enforceBasePlace: option.code,
                enforceBasePlaceName: option.name,
              }).then(() => {
                updateTable();
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
      {
        title: '实施区域',
        dataIndex: 'enforceScopeName',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text,
            componentValue: record?.enforceScope,
            componentProps: {
              api: () => getDictByNumber('pms_enforce_scope'),
              fieldNames: {
                label: 'name',
                value: 'value',
              },
            },
            onSubmit(option: { name: string, value: string }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                enforceScope: option.value,
              }).then(() => {
                updateTable();
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
      {
        title: '工作内容',
        dataIndex: 'workContentName',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text,
            componentValue: record?.workContent,
            componentProps: {
              api: () => getDictByNumber('pms_work_content'),
              fieldNames: {
                label: 'name',
                value: 'value',
              },
            },
            onSubmit(option: { name: string, value: string }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                workContent: option.value,
              }).then(() => {
                updateTable();
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
        width: 140,
        customRender({ text, record }) {
          return h(MouseCellEdit, {
            component: 'Select',
            record,
            text: text || null,
            componentValue: text,
            componentProps: {
              api: () => new Api('/pms/major-repair-plan/list').fetch({
                baseCode: record?.enforceBasePlace,
                isFinish: false,
              }, '', 'POST'),
              fieldNames: {
                label: 'repairRound',
                value: 'repairRound',
              },
            },
            onSubmit(option: { repairRound: string }, resolve: (value: string) => void) {
              updateEdit({
                ...record,
                repairRound: option.repairRound,
              }).then(() => {
                updateTable();
                resolve('');
                message.success('保存成功');
              });
            },
          });
        },
      },
    ],
  };
}
