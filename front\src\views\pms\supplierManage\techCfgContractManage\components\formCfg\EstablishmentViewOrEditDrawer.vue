<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { ref } from 'vue';
import { get } from 'lodash-es';
import ContractBasicInfo
  from '../common/ContractBasicInfo.vue';
import { useContractPlanDetail } from '../../hooks/useContractPlanDetail';
import ContractEmploymentPlan
  from '../common/ContractEmploymentPlan.vue';

const tableRef = ref();
const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
});
const { basicContractEmployerPlan } = useContractPlanDetail(get(props, 'record.contractNumber'));

defineExpose({
  getContractEmploymentPlanData() {
    return tableRef.value?.exportTableData?.();
  },
});
</script>

<template>
  <ContractBasicInfo />
  <BasicCard
    v-if="basicContractEmployerPlan?.contractNumber"
    title="合同用人计划"
    :isBorder="false"
  >
    <ContractEmploymentPlan
      ref="tableRef"
      show-summary-row
      :record="props.record"
    />
  </BasicCard>
</template>

<style scoped lang="less">
.contract-employment-plan{
  overflow: hidden;
  min-height: 120px;
  max-height: 500px;
  overflow-y: auto;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>