package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * RankingVO
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "OverhaulDetailVO", description = "多基地大修准备及实施状态")
@Data
public class OverhaulDetailsVO extends ObjectVO implements Serializable {

    /**
     * 所属基地名称
     */
    @ApiModelProperty(value = "所属基地名称")
    private String baseName;

    /**
     * 大修list
     */
    @ApiModelProperty(value = "所属基地名称")
    private List<OverhaulDetailVO> overhaulDetails;
}
