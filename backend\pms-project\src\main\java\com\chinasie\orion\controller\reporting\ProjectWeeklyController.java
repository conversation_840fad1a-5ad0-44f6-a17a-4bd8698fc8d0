package com.chinasie.orion.controller.reporting;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.reporting.ExportParamDTO;
import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyDTO;
import com.chinasie.orion.domain.dto.reporting.WeeklyBatchAuditParamDTO;
import com.chinasie.orion.domain.dto.reporting.WeeklySingleAuditParamDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectWeekly;
import com.chinasie.orion.domain.request.reporting.AddWeeklyRequest;
import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import com.chinasie.orion.domain.request.reporting.ListWeeklyRequest;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyCardVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementVO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyCardVO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.reporting.ProjectWeeklyService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;





import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.TreeMap;

/**
 * <p>
 * ProjectWeekly 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
@RestController
@RequestMapping("/projectWeekly")
@Api(tags = "项目周报")
public class ProjectWeeklyController {

    @Autowired
    private ProjectWeeklyService projectWeeklyService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了【项目周报】", type = "项目周报", bizNo = "{{#id}}")
    public ResponseDTO<ProjectWeeklyVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectWeeklyVO rsp = projectWeeklyService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增周报")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> create(@RequestBody  ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        boolean rsp =  projectWeeklyService.create(projectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectWeeklyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        Boolean rsp = projectWeeklyService.edit(projectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectWeeklyService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "周报列表")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页查询【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<PageResult<ProjectWeeklyVO>> pages(@RequestBody com.chinasie.orion.sdk.metadata.page.Page<ListWeeklyRequest> pageRequest) throws Exception {
        PageResult<ProjectWeeklyVO> rsp =  projectWeeklyService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 审核
     *
     * @param weeklySingleAuditParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "审核")
    @RequestMapping(value = "/audit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】审核【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> audit(@RequestBody WeeklySingleAuditParamDTO weeklySingleAuditParamDTO) throws Exception {
        Boolean rsp = projectWeeklyService.audit(weeklySingleAuditParamDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量审核
     *
     * @param weeklyBatchAuditParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量审核")
    @RequestMapping(value = "/batch/audit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】审核【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> batchAudit(@RequestBody WeeklyBatchAuditParamDTO weeklyBatchAuditParamDTO) throws Exception {
        Boolean rsp = projectWeeklyService.batchAudit(weeklyBatchAuditParamDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提醒
     *
     * @param id 周报id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提醒")
    @RequestMapping(value = "/warn", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】提醒【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> warn(@RequestParam("id") String id) throws Exception {
        Boolean rsp =  projectWeeklyService.warn(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 审核卡片
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "审核卡片")
    @RequestMapping(value = "/checkCard", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】审核卡片【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<TreeMap<Integer, ProjectWeeklyCardVO>> checkCard(@RequestBody ListWeeklyRequest request) throws Exception {
        TreeMap<Integer, ProjectWeeklyCardVO> rsp =  projectWeeklyService.checkCard(request);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "提交")
    @RequestMapping(value = "/submit/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提交【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectWeeklyService.submitById(id);
        return new ResponseDTO(rsp);
    }

    @ApiOperation(value = "导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】导出【项目周报】", type = "项目周报", bizNo = "")
    public void exportData(@RequestBody ListWeeklyRequest listWeeklyRequest, HttpServletResponse response) throws Exception {
        projectWeeklyService.exportData(listWeeklyRequest,response);
    }

    @ApiOperation(value = "查询存在周计划")
    @RequestMapping(value = "/getWeeklyContent", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询存在【项目周报】", type = "项目周报", bizNo = "")
    public ResponseDTO<ProjectWeeklyVO> todayInfo(@RequestBody ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        ProjectWeeklyVO rsp = projectWeeklyService.getWeeklyContent(projectWeeklyDTO);
        return new ResponseDTO<>(rsp);
    }


}
