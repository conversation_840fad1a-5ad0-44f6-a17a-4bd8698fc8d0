package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * PerformanceTemplateToIndicator VO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@ApiModel(value = "PerformanceTemplateToIndicatorVO对象", description = "绩效模版和指标关联")
@Data
public class PerformanceTemplateToIndicatorVO extends ObjectVO implements Serializable{

        /**
         * 指标ID
         */
        @ApiModelProperty(value = "指标ID")
        private String indicatorId;

        /**
         * 绩效模版ID
         */
        @ApiModelProperty(value = "绩效模版ID")
        private String templateId;

        /**
         * 指标名称
         */
        @ApiModelProperty(value = "指标名称")
        private String indicatorName;

        /**
         * 指标权重
         */
        @ApiModelProperty(value = "指标权重")
        private BigDecimal weight;

        /**
         * 指标评分标准
         */
        @ApiModelProperty(value = "指标评分标准")
        private String scoreStandard;

    }
