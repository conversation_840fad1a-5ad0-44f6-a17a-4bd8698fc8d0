<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.github.yulichang</groupId>
    <artifactId>mybatis-plus-join-root</artifactId>
    <version>1.4.13</version>
  </parent>
  <groupId>com.github.yulichang</groupId>
  <artifactId>mybatis-plus-join-adapter</artifactId>
  <version>1.4.13</version>
  <packaging>pom</packaging>
  <name>mybatis-plus-join-adapter</name>
  <description>An enhanced toolkit of Mybatis-Plus to simplify development.</description>
  <url>https://github.com/yulichang/mybatis-plus-join</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mybatis-plus-join</id>
      <name>yulichang</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <modules>
    <module>mybatis-plus-join-adapter-base</module>
    <module>mybatis-plus-join-adapter-v33x</module>
    <module>mybatis-plus-join-adapter-v3431</module>
    <module>mybatis-plus-join-adapter-v352</module>
    <module>mybatis-plus-join-adapter-v355</module>
    <module>jsqlparser/mybatis-plus-join-adapter-jsqlparser</module>
    <module>jsqlparser/mybatis-plus-join-adapter-jsqlparser-v46</module>
  </modules>
  <scm>
    <connection>scm:git:https://github.com/yulichang/mybatis-plus-join.git</connection>
    <developerConnection>scm:git:https://github.com/yulichang/mybatis-plus-join.git</developerConnection>
    <url>https://github.com/yulichang/mybatis-plus-join</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
</project>
