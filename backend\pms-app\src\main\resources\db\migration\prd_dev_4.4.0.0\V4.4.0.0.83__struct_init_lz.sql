CREATE TABLE `pmsx_contract_main` (
                                      `id` varchar(64) NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                      `modify_time` datetime NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                      `contract_name` varchar(64) NOT NULL COMMENT '合同名称',
                                      `year` datetime NOT NULL COMMENT '年份',
                                      `contract_satus` int(11) DEFAULT NULL COMMENT '合同状态',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同计划主表';

CREATE TABLE `pmsx_contract_cost_type` (
                                           `id` varchar(64) NOT NULL COMMENT '主键',
                                           `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                           `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                           `modify_time` datetime NOT NULL COMMENT '修改时间',
                                           `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                           `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                           `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                           `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                           `status` int(11) NOT NULL COMMENT '状态',
                                           `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                           `contract_number` varchar(64) NOT NULL COMMENT '合同编号',
                                           `cost_type_number` varchar(64) DEFAULT NULL COMMENT '成本类型编号',
                                           `cost_type_name` varchar(64) DEFAULT NULL COMMENT '成本类型名称',
                                           `cost_name` varchar(64) DEFAULT NULL COMMENT '成本名称',
                                           `unit_price` decimal(10,0) DEFAULT NULL COMMENT '单价',
                                           `unit` varchar(11) DEFAULT NULL COMMENT '单位',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同计划成本类型';

CREATE TABLE `pmsx_contract_center_plan` (
                                             `id` varchar(64) NOT NULL COMMENT '主键',
                                             `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                             `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                             `modify_time` datetime NOT NULL COMMENT '修改时间',
                                             `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                             `create_time` datetime NOT NULL COMMENT '创建时间',
                                             `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                             `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                             `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                             `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                             `status` int(11) NOT NULL COMMENT '状态',
                                             `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                             `contract_number` varchar(64) NOT NULL COMMENT '合同编号',
                                             `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                             `center_code` varchar(64) NOT NULL COMMENT '用人单位代号',
                                             `center_name` varchar(64) DEFAULT NULL COMMENT '用人单位名称',
                                             `cost_type_number` varchar(64) DEFAULT NULL COMMENT '成本类型编号',
                                             `num` int(11) DEFAULT NULL COMMENT '数量',
                                             `year` datetime DEFAULT NULL COMMENT '年份',
                                             `cost_type_id` varchar(64) DEFAULT NULL COMMENT '成本类型id',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中心用人计划';

CREATE TABLE `pmsx_contract_center` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                        `center_code` varchar(64) DEFAULT NULL COMMENT '用人单位代号',
                                        `center_name` varchar(20) DEFAULT NULL COMMENT '用人中心名称',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用人中心';

CREATE TABLE `pmsx_contract_assessment_standard` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键',
                                                     `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                     `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                     `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                     `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                     `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                     `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                     `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                     `status` int(11) NOT NULL COMMENT '状态',
                                                     `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                     `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                                     `contract_name` varchar(64) DEFAULT NULL COMMENT '合同名称',
                                                     `assessment_type` varchar(20) DEFAULT NULL COMMENT '审核类别',
                                                     `assessment_content` varchar(1024) DEFAULT NULL COMMENT '考核内容',
                                                     `standard` decimal(10,0) DEFAULT NULL COMMENT '考核标准',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核标准表';

CREATE TABLE `pmsx_assessment_log` (
                                       `id` varchar(64) NOT NULL COMMENT '主键',
                                       `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                       `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                       `modify_time` datetime NOT NULL COMMENT '修改时间',
                                       `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                       `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                       `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                       `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                       `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                       `status` int(11) NOT NULL COMMENT '状态',
                                       `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                       `contract_number` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                       `center_name` varchar(64) DEFAULT NULL COMMENT '中心名称',
                                       `assessment_advice` varchar(1024) DEFAULT NULL COMMENT '审批意见',
                                       `person_id` varchar(64) DEFAULT NULL COMMENT '审批人id',
                                       `person_name` varchar(20) DEFAULT NULL COMMENT '审批人姓名',
                                       `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                       `assessment_time` datetime DEFAULT NULL COMMENT '审批时间',
                                       `type` varchar(64) DEFAULT NULL COMMENT '审批类型',
                                       `year` int(11) DEFAULT NULL COMMENT '年份',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核记录表';

CREATE TABLE `pmsx_edit_log` (
                                 `id` varchar(64) NOT NULL COMMENT '主键',
                                 `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                 `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                 `modify_time` datetime NOT NULL COMMENT '修改时间',
                                 `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                 `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                 `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                 `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                 `status` int(11) NOT NULL COMMENT '状态',
                                 `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                 `plan_id` varchar(64) DEFAULT NULL COMMENT '计划id',
                                 `center_name` varchar(20) DEFAULT NULL COMMENT '中心名称',
                                 `cost_type` varchar(64) DEFAULT NULL COMMENT '成本类型',
                                 `cost_name` varchar(64) DEFAULT NULL COMMENT '成本名称',
                                 `num` int(11) DEFAULT NULL COMMENT '调整数量',
                                 `edit_person_id` varchar(64) DEFAULT NULL COMMENT '调整人id',
                                 `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                 `assessment_time` datetime DEFAULT NULL COMMENT '审批时间',
                                 `assessment_person_id` varchar(64) DEFAULT NULL COMMENT '审批人id',
                                 `assessment_advice` varchar(1024) DEFAULT NULL COMMENT '审批意见',
                                 `assessment_person` varchar(20) DEFAULT NULL COMMENT '审批人姓名',
                                 `edit_person` varchar(20) DEFAULT NULL COMMENT '调整人姓名',
                                 `before_num` int(11) DEFAULT NULL COMMENT '调整前数量',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调整记录表';