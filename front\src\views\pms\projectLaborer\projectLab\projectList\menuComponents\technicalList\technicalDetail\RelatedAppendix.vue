<template>
  <UploadList
    :listApi="listApi"
    :saveApi="saveApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :powerCode="powerCode"
    :powerData="powerData"
  />
</template>

<script lang="ts">
import {
  inject, reactive, ref, toRefs,
} from 'vue';
import Api from '/@/api';
import {
  BasicTableAction, BasicUpload, isPower, OrionTable, UploadList,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';

export default {
  name: 'Index',
  components: {
    UploadList,
  },
  props: {
    projectId: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const state = reactive({
      tableRef: ref(),
      loading: false,
      dataSource: [],
      powerData: [],
      fileId: undefined,
    });
    state.powerData = inject('powerData');
    const powerCode = {
      download: 'PMS_JFW_container_02_button_03',
      upload: 'PMS_JFW_container_02_button_01',
      delete: 'PMS_JFW_container_02_button_04',
    };

    async function listApi() {
      return new Api('/pms/document/getList').fetch('', props.id, 'POST');
    }
    async function saveApi(files) {
      let fieldList = files.map((item) => {
        item.dataId = props?.id;
        item.projectId = props.projectId;
        return item;
      });
      return new Api('/pms/document/saveBatch').fetch(fieldList, '', 'POST');
    }

    async function deleteApi(deleteApi) {
      return new Api('/pms/document/removeBatch').fetch([deleteApi.id], '', 'DELETE');
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return new Api('/pms/document/removeBatch').fetch(rows.map((item) => item.id), '', 'DELETE');
    }

    return {
      ...toRefs(state),
      isPower,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCode,
    };
  },
};
</script>
