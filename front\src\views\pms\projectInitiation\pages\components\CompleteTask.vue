<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    />
  </div>
</template>
<script lang="ts" setup>
import { BasicForm, useForm } from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
let validateTime = async (rule, value) => {
  if (!value) {
    return Promise.reject('请选择时间');
  }
  return Promise.resolve();
};
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'actualEndTime',
      label: '实际结束时间：',
      colProps: { span: 24 },
      rules: [
        {
          required: true,
          trigger: 'change',
          validator: validateTime,
        },
      ],
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
      component: 'DatePicker',
    },
  ],
});

onMounted(async () => {
});

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    await new Api('/pms').fetch(params, 'collaborativeCompilationTask/finish', 'PUT');
    message.success('执行完成成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
//.upload-list{
//  height: 200px;
//  overflow: hidden;
//}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
</style>