package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

import java.util.List;

/**
 * BudgetAdjustment VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@ApiModel(value = "BudgetAdjustmentVO对象", description = "预算调整表")
@Data
public class BudgetAdjustmentVO extends ObjectVO implements Serializable {

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    private String name;


    /**
     * 预算申请编码
     */
    @ApiModelProperty(value = "预算编码")
    private String number;


    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    private String expenseSubjectNumber;


    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String expenseSubjectName;


    /**
     * 期间类型
     */
    @ApiModelProperty(value = "期间类型")
    private String timeType;

    @ApiModelProperty(value = "期间类型名称")
    private String timeTypeName;


    /**
     * 预算期间
     */
    @ApiModelProperty(value = "预算期间")
    private String budgetTime;


    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型")
    private String budgetObjectType;



    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型名称")
    private String budgetObjectTypeName;


    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    private String budgetObjectId;


    @ApiModelProperty(value = "预算对象名称")
    private String budgetObjectName;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;


    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算申请总金额")
    private BigDecimal budgetMoney;


    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    private BigDecimal februaryMoney;


    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    private BigDecimal januaryMoney;


    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    private BigDecimal aprilMoney;


    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    private BigDecimal juneMoney;


    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    private BigDecimal marchMoney;


    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    private BigDecimal julyMoney;


    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    private BigDecimal augustMoney;


    @ApiModelProperty(value = "9月预算")
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    private BigDecimal octoberMoney;


    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    private BigDecimal novemberMoney;


    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    private BigDecimal decemberMoney;


    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    private BigDecimal firstQuarterMoney;


    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    private BigDecimal secondQuarter;


    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    private BigDecimal thirdQuarter;


    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    private BigDecimal fourthQuarter;



    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    private BigDecimal mayMoney;


    /**
     * 调整单Id
     */
    @ApiModelProperty(value = "调整单Id")
    private String formId;


    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    private String budgetId;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "是否是调整数据改变数据")
    private String isChange;


}
