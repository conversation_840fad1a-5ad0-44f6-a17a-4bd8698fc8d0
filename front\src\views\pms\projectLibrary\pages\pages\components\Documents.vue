<script setup lang="ts">
import {
  BasicButton, BasicUpload, OrionTable, downLoadById, openFile, isPower, UploadList,
} from 'lyra-component-vue3';
import {
  h, inject, onMounted, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';

const props = defineProps<{
  powerCode: {
    upload: string,
    batchDelete: string,
    delete: string,
    download: string
  }
}>();

const powerData:Ref = inject('powerData', ref({}));

const powerCodes = {
  delete: props.powerCode?.delete,
  download: props.powerCode?.download,
  upload: props.powerCode?.upload,
};

const dataId: string = inject('dataId');
const uploadRef: Ref = ref();
const tableRef: Ref = ref();
const selectKeys: Ref<string[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  showSmallSearch: false,
  api: async () => {
    const result = await new Api('/res/manage/file/new').fetch('', dataId, 'GET');
    return result.map((item) => {
      delete item.children;
      return {
        ...item,
        fileName: `${item.name}.${item.filePostfix}`,
      };
    });
  },
  columns: [
    {
      title: '名称',
      dataIndex: 'fileName',
      customRender({ record, text }) {
        return h('div', {
          class: 'action-btn',
          onClick: () => openFile(record),
        }, text);
      },
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
    },
    {
      title: '上传时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '删除',
      isShow: () => isPower(props.powerCode.delete, powerData.value),
      modal: (record) => requestBatchDel([record.id]),
    },
    {
      text: '下载',
      isShow: () => isPower(props.powerCode.download, powerData.value),
      onClick: (record) => handleBatchDownload([record.id]),
    },
  ],
};

function saveChange(successAll, cb) {
  const files = successAll.map((item) => ({
    ...item.result,
    dataId,
  }));
  cb(new Api('/pms/document/saveBatch').fetch(files, '', 'POST').then(() => {
    updateTable();
  }));
}

// 表格更新
function updateTable() {
  tableRef.value.reload();
}

// 表格多选
function selectionChange({ keys }) {
  selectKeys.value = keys;
}

// 删除确认
function handleBatchDel(ids: string[]) {
  Modal.confirm({
    title: '删除提示',
    content: `确认要删除已选择的${ids.length}条记录？`,
    onOk: () => requestBatchDel(ids),
  });
}

// 批量删除请求
function requestBatchDel(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/res/manage/file').fetch(ids, '', 'DELETE')
      .then(() => {
        resolve('');
        updateTable();
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// 批量下载
function handleBatchDownload(ids) {
  ids.map((item) => {
    downLoadById(item);
  });
}

async function listApi(params) {
  return new Api('/res/manage/file/new').fetch('', dataId, 'GET');
}
async function saveApi(files) {
  let fieldList = files.map((item) => {
    item.dataId = dataId;
    return item;
  });
  return new Api('/pms/document/saveBatch').fetch(fieldList, '', 'POST');
}

async function deleteApi(deleteApi) {
  return new Api('/res/manage/file').fetch([deleteApi.id], '', 'DELETE');
}

async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return new Api('/res/manage/file').fetch(rows.map((item) => item.id), '', 'DELETE');
}

</script>

<template>
  <UploadList
    ref="tableRef"
    :listApi="listApi"
    :saveApi="saveApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :powerCode="powerCodes"
    :powerData="powerData"
  />

  <!--  <OrionTable-->
<!--    ref="tableRef"-->
<!--    :options="tableOptions"-->
<!--    :onSelectionChange="selectionChange"-->
<!--  >-->
<!--    <template #toolbarLeft>-->
<!--      <BasicButton-->
<!--        v-if="isPower(powerCode.upload,powerData)"-->
<!--        icon="orion-icon-upload"-->
<!--        type="primary"-->
<!--        @click="uploadRef.openModal(true)"-->
<!--      >-->
<!--        上传附件-->
<!--      </BasicButton>-->
<!--      <BasicButton-->
<!--        v-if="isPower(powerCode.batchDelete,powerData)"-->
<!--        icon="sie-icon-del"-->
<!--        :disabled="selectKeys.length===0"-->
<!--        @click="handleBatchDel(selectKeys)"-->
<!--      >-->
<!--        删除-->
<!--      </BasicButton>-->
<!--    </template>-->
<!--  </OrionTable>-->
<!--  <BasicUpload-->
<!--    ref="uploadRef"-->
<!--    :max-number="100"-->
<!--    :isClassification="false"-->
<!--    :isToolRequired="false"-->
<!--    :onSaveChange="saveChange"-->
<!--    :isButton="false"-->
<!--  />-->
</template>

<style scoped lang="less">

</style>
