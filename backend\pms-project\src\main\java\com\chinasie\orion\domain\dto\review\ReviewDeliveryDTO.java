package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Review DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewDTO对象", description = "项目评审")
@Data
@ExcelIgnoreUnannotated
public class ReviewDeliveryDTO implements Serializable {
    @ApiModelProperty("id")
    @NotBlank(message = "项目审核id不能为空！")
    private String id;

    /**
     * 会议纪要号
     */
    @ApiModelProperty(value = "会议纪要号")
    private String meetingNo;

    /**
     * 问题整改报告文件号
     */
    @ApiModelProperty(value = "问题整改报告文件号")
    private String questionFileNo;

    /**
     * 评审报告文件号
     */
    @ApiModelProperty(value = "评审报告文件号")
    private String reviewFileNo;

    /**
     * 评审文件PLM编号
     */
    @ApiModelProperty(value = "评审文件PLM编号")
    private String plmNo;


    //=======================交付物数据啊========================
    /**
     * 关联交付物
     */
    @ApiModelProperty(value = "关联交付物")
    private String deliverId;

    /**
     * 交付物名称
     */
    @ApiModelProperty(value = "交付物名称")
    @NotBlank(message = "交付物名称不能为空！")
    private String deliverName;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryTime;


    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;

}
