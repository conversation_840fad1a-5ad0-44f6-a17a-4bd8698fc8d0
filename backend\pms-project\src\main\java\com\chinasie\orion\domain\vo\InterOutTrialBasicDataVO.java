package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * InterOutTrialBasicData VO对象
 *
 * <AUTHOR>
 * @since 2024-04-29 15:50:29
 */
@ApiModel(value = "InterOutTrialBasicDataVO对象", description = "内外部试验项目基础数据")
@Data
public class InterOutTrialBasicDataVO extends ObjectVO implements Serializable{

            /**
         * 台数
         */
        @ApiModelProperty(value = "台数")
        private Integer num;


        /**
         * 批次
         */
        @ApiModelProperty(value = "批次")
        private Integer batch;


        /**
         * 单价
         */
        @ApiModelProperty(value = "单价")
        private BigDecimal price;


        /**
         * 单位
         */
        @ApiModelProperty(value = "单位")
        private String unit;

        @ApiModelProperty(value = "单位名称")
        private String unitName;

        /**
         * 设备参数
         */
        @ApiModelProperty(value = "设备参数")
        private String deviceParam;


        /**
         * 试验项目
         */
        @ApiModelProperty(value = "试验项目")
        private String name;


    

}
