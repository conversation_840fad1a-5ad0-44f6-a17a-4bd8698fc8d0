import type { AppRouteModule, AppRouteRecordRaw } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { cloneDeep } from 'lodash-es';
import devModules from '../../../modules/devModules';
import prodModules from '../../../modules/prodModules';
import { isDev } from '/@/utils/env';

let modules = {};

if (isDev()) {
  modules = devModules;
} else {
  modules = prodModules;
}

export type LayoutMapKey = 'LAYOUT';

const LayoutMap = new Map<LayoutMapKey, () => Promise<typeof import('*.vue')>>();

let dynamicViewsModules: Record<string, () => Promise<Recordable>>;

function asyncImportRoute(routes: AppRouteRecordRaw[] | undefined) {
  const requireComponent = {};

  dynamicViewsModules = dynamicViewsModules || requireComponent;

  if (!routes) return;
  routes.forEach((item) => {
    const { component, name } = item;
    const { children } = item;
    if (component && name) {
      const module = Object.keys(modules).find((key) => {
        const match = key.match(/\/views(.*?)\.vue/);
        return match?.[1] === component;
      });
      if (module) {
        item.component = modules[module];
      } else {
        item.component = null;
      }
    } else if (name) {
      item.component = getParentLayout();
    }
    children && asyncImportRoute(children);
  });
}

// Turn background objects into routing objects
export function transformObjToRoute<T = AppRouteModule>(routeList: AppRouteModule[]): T[] {
  LayoutMap.set('LAYOUT', LAYOUT);
  routeList.forEach((route) => {
    if (route.component) {
      if ((route.component as string).toUpperCase() === 'LAYOUT') {
        // route.component = LayoutMap.get(route.component as LayoutMapKey);
        route.component = LayoutMap.get((route.component as string).toUpperCase() as LayoutMapKey);
      } else {
        route.children = [cloneDeep(route)];
        route.component = LAYOUT;
        route.name = `${route.name}Parent`;
        route.path = '';
        const meta = route.meta as any || {};
        meta.single = true;
        meta.affix = false;
        route.meta = meta;
      }
    }
    route.children && asyncImportRoute(route.children);
  });
  return routeList as unknown as T[];
}

/**
 * 重新处理树形菜单信息，详情页可挂在列表页上
 * 把带组件信息的数据，抽取平铺进行返回
 * @param menuData
 */
export function transformTreeMenuToFlatRouteData(menuData) {
  const menu = JSON.parse(JSON.stringify(menuData));
  const menuArr:any = [];

  function recursiveMenuToFlat(menu:any[]) {
    for (const menuItem of menu) {
      if (menuItem.children && menuItem.children.length) {
        recursiveMenuToFlat(menuItem.children);
      }
      if (menuItem.component) {
        menuArr.push(menuItem);
        if (menuItem.children) {
          delete menuItem.children;
        }
      }
    }
  }
  recursiveMenuToFlat(menu);
  return menuArr;
}
