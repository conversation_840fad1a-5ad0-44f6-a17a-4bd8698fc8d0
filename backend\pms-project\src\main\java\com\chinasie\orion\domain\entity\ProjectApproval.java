package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectApproval Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
@TableName(value = "pmsx_project_approval")
@ApiModel(value = "ProjectApproval对象", description = "项目立项表")
@Data
public class ProjectApproval extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number" )
    private String projectNumber;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    @TableField(value = "name" )
    private String name;

    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    @TableField(value = "type" )
    private String type;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    @TableField(value = "source" )
    private String source;

    /**
     * 预估项目开始时间
     */
    @ApiModelProperty(value = "预估项目开始时间")
    @TableField(value = "estimate_start_time" )
    private Date estimateStartTime;

    /**
     * 预估项目结束时间
     */
    @ApiModelProperty(value = "预估项目结束时间")
    @TableField(value = "estimate_end_time" )
    private Date estimateEndTime;

    /**
     * 需求评审单
     */
    @ApiModelProperty(value = "需求评审单")
    @TableField(value = "require_review_id" )
    private String requireReviewId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "rsp_user" )
    private String rspUser;


    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @TableField(value = "start_time" )
    private Date startTime;
//
//    /**
//     * 项目来源
//     */
//    @ApiModelProperty(value = "项目来源")
//    @TableField(value = "project_source" )
//    private String projectSource;
//
//    /**
//     * 项目开始时间
//     */
//    @ApiModelProperty(value = "项目开始时间")
//    @TableField(value = "project_start_time" )
//    private Date projectStartTime;
//
//    /**
//     * 项目负责人
//     */
//    @ApiModelProperty(value = "项目负责人")
//    @TableField(value = "resres_person" )
//    private String resresPerson;
//
//    /**
//     * 责任部门
//     */
//    @ApiModelProperty(value = "责任部门")
//    @TableField(value = "res_dept" )
//    private String resDept;

    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    @TableField(value = "approval_reason" )
    private String approvalReason;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort" )
    private Long sort;

    /**
     * 立项编码
     */
    @ApiModelProperty(value = "立项编码")
    @TableField(value = "number" )
    private String number;

}
