package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.List;


/**
 * DocumentModelLibrary VO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@ApiModel(value = "DocumentModelLibraryVO对象", description = "文档模板库")
@Data
public class DocumentModelLibraryVO extends ObjectVO implements Serializable {

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    private String initialRevId;


    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;


    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private String previousRevId;


    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;


    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    private Integer revOrder;


    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    private String nextRevId;


    /**
     * 应用范围
     */
    @ApiModelProperty(value = "应用范围")
    private String useScope;
    @ApiModelProperty(value = "应用范围名称，多个逗号隔开")
    private String useScopeName;


    /**
     * 是否应用所有对象
     */
    @ApiModelProperty(value = "是否应用所有对象")
    private Boolean isUseAllObject;


    /**
     * 文档模板名称
     */
    @ApiModelProperty(value = "文档模板名称")
    private String name;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 文档应用范围
     */
    @ApiModelProperty(value = "文档应用范围")
    private List<SimpleVo> useScopeList;


    /**
     * 文件列表
     */
    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileDtoList;

}
