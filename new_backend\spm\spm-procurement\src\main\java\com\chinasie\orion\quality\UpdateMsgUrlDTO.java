package com.chinasie.orion.quality;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @className UpdateMsgUrlDTO
 * @description 修改消息URL地址
 * @since 2024/6/22
 */
@Data
@ApiModel(value = "UpdateMsgUrlDTO", description = "修改消息URL地址")
public class UpdateMsgUrlDTO {
    @ApiModelProperty(value = "消息ID",required = true)
    private String messageId;

    @ApiModelProperty(value = "消息URL地址",required = true)
    private String url;
}
