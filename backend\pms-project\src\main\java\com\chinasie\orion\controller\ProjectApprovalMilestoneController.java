package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectApprovalMilestoneDTO;
import com.chinasie.orion.domain.vo.ProjectApprovalMilestoneVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.service.ProjectApprovalMilestoneService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectApprovalMilestone 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@RestController
@RequestMapping("/projectApprovalMilestone")
@Api(tags = "项目立项里程碑")
public class ProjectApprovalMilestoneController {

    @Autowired
    private ProjectApprovalMilestoneService projectApprovalMilestoneService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目立项里程碑", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalMilestoneVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectApprovalMilestoneVO rsp = projectApprovalMilestoneService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalMilestoneDTOs
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectApprovalMilestoneDTOs}}】", type = "项目立项里程碑", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> create(@RequestBody List<ProjectApprovalMilestoneDTO> projectApprovalMilestoneDTOs) throws Exception {
        Boolean rsp =  projectApprovalMilestoneService.create(projectApprovalMilestoneDTOs);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectApprovalMilestoneDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectApprovalMilestoneDTO}}】", type = "项目立项里程碑", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectApprovalMilestoneDTO projectApprovalMilestoneDTO) throws Exception {
        Boolean rsp = projectApprovalMilestoneService.edit(projectApprovalMilestoneDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据【{{#ids}}】", type = "项目立项里程碑", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalMilestoneService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项里程碑", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalMilestoneVO>> pages(@RequestBody Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception {
        Page<ProjectApprovalMilestoneVO> rsp =  projectApprovalMilestoneService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页获取里程碑")
    @RequestMapping(value = "/getPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页获取里程碑", type = "项目立项里程碑", subType = "分页获取里程碑", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalMilestoneVO>> getPages(@RequestBody Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception {
        Page<ProjectApprovalMilestoneVO> rsp =  projectApprovalMilestoneService.getPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation("项目立项里程碑导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "项目立项里程碑", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectApprovalMilestoneService.downloadExcelTpl(response);
    }

    @ApiOperation("项目立项里程碑导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "项目立项里程碑", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file,
                                                                    @RequestParam String approvalId) throws Exception {
        ImportExcelCheckResultVO rsp = projectApprovalMilestoneService.importCheckByExcel(file,approvalId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目立项里程碑导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}/{approvalId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "项目立项里程碑", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId,
                                              @PathVariable("approvalId") String approvalId) throws Exception {
        Boolean rsp =  projectApprovalMilestoneService.importByExcel(importId,approvalId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消项目立项里程碑导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "项目立项里程碑", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  projectApprovalMilestoneService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("项目立项里程碑导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "项目立项里程碑", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions,
                              HttpServletResponse response,
                              @RequestParam String approvalId) throws Exception {
        projectApprovalMilestoneService.exportByExcel(searchConditions, response, approvalId);
    }
}
