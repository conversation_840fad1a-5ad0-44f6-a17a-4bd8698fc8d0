function customCell({ column, record }) {
    // console.log(column)
    // console.log(record)
    if (column.type === 'day') {
        let { positions, overlapIds } = checkDatePosition(column.dataIndex, record);//这里可以决定渲染或者填充的表格positions = ['data-cell', 'middle']
        let cellClassName = [];
        if (positions && positions.length) {
            //这里可以根据不同的类型来设置不同的颜色
            cellClassName.push(`${record.dataType === '1' ? 'in-out' : record.dataType === '2' ? 'job' : 'task'}`);
            //   cellClassName.push(`${'in-out'}`);
            switch (record.dataType) {
                // case '1':
                //   if (overlapIds.length > 1) {
                //     dataCellMap.value.set(`${record.uniqueId}-${column.dataIndex}`, {
                //       unLeaderData: overlapIds.filter((id) => !inAndOutDateVOMap.value.get(id)?.leaderData),
                //       overlapIds,
                //       timestamp: column.dataIndex,
                //     });
                //     cellClassName.push('overlap');
                //   } else {
                //     dataCellMap.value.delete(`${record.uniqueId}-${column.dataIndex}`);
                //   }
                //   break;
                // case '3':
                //   //重叠部分
                //   const taskOverlapIds = countMatchingDateRanges(column.dataIndex, record);
                //   if (taskOverlapIds.length > 0) {
                //     cellClassName.push('overlap');
                //   }
                //   dataCellMap.value.set(`${record.uniqueId}-${column.dataIndex}`, {
                //     showMenu: true,
                //     overlapIds,
                //     timestamp: column.dataIndex,
                //   });
                //   break;
                // default:
            }
            //   console.log(cellClassName)
        } else if (record.dataType === '3') {
            dataCellMap.value.delete(`${record.uniqueId}-${column.dataIndex}`);
        }
        // console.log(props);
        // cellClassName = props.legend.filter((item) => console.log(cellClassName,item.class)).map((item) => item.class);
        //下面这段确定了所要渲染的表格及对应的颜色
        cellClassName = props.legend.filter((item) => cellClassName.includes(item.class) && !item.disabled).map((item) => item.class);
        // console.log(cellClassName);
        return {
            class: positions.concat(cellClassName).join(' '),
            onClick() {
                if (!props.isEdit) return;
                handleCell(record, column);
            },
        };
    }

    if (column.dataIndex === 'jobName') {
        return {
            class: `job-name-row ${record.rowSpan ? 'job-name-row-3' : 'job-name-row-1'}`,
            title: record.jobName,
            onClick() {
                if (record.jobId && props.repairRound) {
                    const jobId = String(record.jobId).trim();
                    const repairRound = String(props.repairRound).trim();
                    if (jobId && repairRound) {
                        emits('close');
                        try {
                            router.push({
                                name: 'OverhaulOperationDetails',
                                params: {
                                    id: jobId,
                                },
                                query: {
                                    id: repairRound,
                                },
                            });
                        } catch (error) {
                            console.error('Router push failed:', error);
                        }
                    } else {
                        console.error('Invalid jobId or repairRound');
                    }
                }
            },
        };
    }
    return {};
}