<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.checkerframework</groupId>
  <artifactId>checker-qual</artifactId>
  <version>3.19.0</version>
  <name>Checker Qual</name>
  <description>checker-qual contains annotations (type qualifiers) that a programmer
writes to specify Java code for type-checking by the Checker Framework.
</description>
  <url>https://checkerframework.org</url>
  <licenses>
    <license>
      <name>The MIT License</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mernst</id>
      <name>Michael Ernst</name>
      <email><EMAIL></email>
      <url>https://homes.cs.washington.edu/~mernst/</url>
      <organization>University of Washington</organization>
      <organizationUrl>https://www.cs.washington.edu/</organizationUrl>
    </developer>
    <developer>
      <id>smillst</id>
      <name>Suzanne Millstein</name>
      <email><EMAIL></email>
      <organization>University of Washington</organization>
      <organizationUrl>https://www.cs.washington.edu/</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/typetools/checker-framework.git</connection>
    <developerConnection>scm:git:ssh://**************/typetools/checker-framework.git</developerConnection>
    <url>https://github.com/typetools/checker-framework.git</url>
  </scm>
</project>
