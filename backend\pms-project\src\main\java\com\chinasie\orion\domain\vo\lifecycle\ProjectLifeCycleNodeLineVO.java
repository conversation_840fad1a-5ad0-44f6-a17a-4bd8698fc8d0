package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeLineVO
 * @description 生命周期节点关系线VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeLineVO", description = "生命周期节点关系线VO")
public class ProjectLifeCycleNodeLineVO implements Serializable {
    public ProjectLifeCycleNodeLineVO() {
    }

    public ProjectLifeCycleNodeLineVO(String source, String target) {
        this.source = source;
        this.target = target;
    }

    @ApiModelProperty(value = "起始")
    private String source;

    @ApiModelProperty(value = "终点")
    private String target;

    @ApiModelProperty(value = "是否高亮")
    private Boolean isHighlight;
}
