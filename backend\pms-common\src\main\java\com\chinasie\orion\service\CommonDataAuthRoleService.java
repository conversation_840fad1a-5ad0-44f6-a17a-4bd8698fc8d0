package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.chinasie.orion.domain.dto.CommonDataAuthRoleBatchDTO;
import com.chinasie.orion.domain.dto.CommonDataAuthRoleDTO;
import com.chinasie.orion.domain.entity.CommonDataAuthRole;
import com.chinasie.orion.domain.vo.CommonDataAuthRoleVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CommonDataAuthRole 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
public interface CommonDataAuthRoleService  extends  OrionBaseService<CommonDataAuthRole>  {
        /**
         *  新增
         *
         * * @param commonDataAuthRoleDTO
         */
        String create(CommonDataAuthRoleDTO commonDataAuthRoleDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;

        /**
         *  权限设置
         * @param dataAuthRoleBatchDTO
         * @return
         */
        Boolean createBath(CommonDataAuthRoleBatchDTO dataAuthRoleBatchDTO);

        /**
         *  当前人拥有的权限对于数据列表
         * @param idList
         * @return
         */
        Map<String, Set<String>> currentUserRoles(List<String> idList);

        Map<String,List<CommonDataAuthRoleVO>> dataAuthRoleByDataId(String dataId);

        /**
         *  根据数据类型获取当前用户相应权限的id集合
         * @param dataType 数据类型
         * @param type read-只读;write-只写;null-可读写
         */
        List<String> getIdListByTypeForDataType(String dataType,String type);

        /**
         *  根据业务类型获取当前用户相应权限的id集合
         * @param businessType 业务类型
         * @param type read-只读;write-只写;null-可读写
         */
        List<String> getIdListByTypeForBusinessType(String businessType,String type);

        /**
         *  根据业务类型获取当前用户相应权限数据
         * @param businessType 业务类型
         * @param type read-只读;write-只写;null-可读写
         */
        List<CommonDataAuthRole> getDataAuthByTypeForBusinessType(String businessType,String type);
}
