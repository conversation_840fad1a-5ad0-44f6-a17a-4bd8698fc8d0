<template>
  <DetailsLayout
    title="验收信息"
    :list="AcceptanceInformation"
    :data-source="dataSource"
    :column="3"
  />
  <DetailTitle
    v-if="dataSource?.type&&dataSource?.type!='PROJECT'"
    title="验收相关项"
  />
  <div
    v-if="dataSource?.type&&dataSource?.type!='PROJECT'"
    class="tableBox"
  >
    <OrionTable

      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { onMounted, computed, h } from 'vue';

import { getProcurementPlanApproval } from '/@/views/pms/api';
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import DetailTitle from './detailTitle.vue';
const props = defineProps<{
  projectId: string
  id: string
  dataSource: any
}>();
const AcceptanceInformation = [
  {
    label: '验收单编号',
    field: 'number',
  },
  {
    label: '验收人',
    field: 'creatorName',
  },
  {
    label: '验收创建日期',
    field: 'createTime',
  },
  {
    label: '项目编号',
    field: 'projectNumber',
  },
  {
    label: '项目名称',
    field: 'projectName',
  },
  {
    label: '项目开始时间',
    field: 'projectBeginTime',
  },
  {
    label: '责任处室',
    field: 'projectRspSubDeptName',
  },
  {
    label: '项目负责人',
    field: 'projectRspUserName',
  },
  {
    label: '项目结束时间',
    field: 'projectEndTime',
  },
];

onMounted(() => {
});
const columns = [
  {
    title: '计划编号',
    fixed: 'left',
    dataIndex: 'number',
  },
  {
    title: '计划行号',
    fixed: 'left',
    dataIndex: 'lineNumber',

  },

  {
    title: '阶段',
    fixed: 'left',
    dataIndex: 'phase',

  },

  {
    title: '状态',
    fixed: 'left',
    dataIndex: 'data4',
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },
  {
    title: '物料/服务编码',
    fixed: 'left',
    dataIndex: 'suppliesNumber',
  },
  {
    title: '物料/服务描述',
    fixed: 'left',
    dataIndex: 'creatorCode',
  },
  {
    title: '规格型号',
    fixed: 'left',
    dataIndex: 'specificationsModels',
  },
  {
    title: '需求数量',
    fixed: 'left',
    dataIndex: 'demandNum',
  },

  {
    title: '计量单位',
    fixed: 'left',
    dataIndex: 'unitOfMeasureDemand',
  },
  {
    title: '需求人',
    fixed: 'left',
    dataIndex: 'demandPersonName',
  },

  {
    title: '需求类型',
    fixed: 'left',
    dataIndex: 'demandType',
  },

  {
    title: 'QA等级',
    fixed: 'left',
    dataIndex: 'qaLevel',
  },
  {
    title: '核安全等级',
    fixed: 'left',
    dataIndex: 'nucleusSafetyLevel',
  },
];
const tableOptions = computed(() => ({
  rowSelection: null,
  columns,
  showToolButton: false,
  // dataSource,
  showSmallSearch: false,
  api: (params) => getProcurementPlanApproval({
    ...params,
  }, props.id),
}));
</script>

<style scoped>
.tableBox {
  height:500px;
  overflow: hidden;
}
</style>
