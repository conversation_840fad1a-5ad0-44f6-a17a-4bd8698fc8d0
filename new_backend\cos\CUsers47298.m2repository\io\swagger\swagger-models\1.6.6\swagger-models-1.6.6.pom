<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-project</artifactId>
        <version>1.6.6</version>
        <relativePath>../..</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>swagger-models</artifactId>
    <packaging>bundle</packaging>
    <name>swagger-models</name>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <defaultGoal>install</defaultGoal>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>logback.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>${felix-version}</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>
                            io.swagger.models.auth,
                            io.swagger.models.parameters,
                            io.swagger.models.properties,
                            io.swagger.models.refs,
                            io.swagger.models.utils,
                            io.swagger.models
                        </Export-Package>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.openpojo</groupId>
            <artifactId>openpojo</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>asm</groupId>
            <artifactId>asm</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <!-- TODO increase coverage -->
        <coverage.complexity.minimum>0.07</coverage.complexity.minimum>
        <coverage.line.minimum>0.0</coverage.line.minimum>
        <!-- Setting this to a really high number since this module has almost no coverage -->
        <coverage.missed.classes>41</coverage.missed.classes>
        <powermock.version>1.6.4</powermock.version>
        <mockito-version>1.10.19</mockito-version>
    </properties>
</project>
