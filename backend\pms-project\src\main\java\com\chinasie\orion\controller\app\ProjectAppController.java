package com.chinasie.orion.controller.app;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.vo.NewProjectHomePageVO;
import com.chinasie.orion.domain.vo.NewProjectVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.app.ProjectAppService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/app/project")
@Api(tags = "项目信息小程序API")
public class ProjectAppController {

    @Resource
    private ProjectAppService projectAppService;

    @ApiOperation("项目分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】查看【项目信息小程序】分页", type = "NewProjectHomePage", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<NewProjectHomePageVO>> getProjectPage(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<NewProjectHomePageVO> rsp = projectAppService.getProjectPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "项目详情")
    @RequestMapping(value = "/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【项目信息小程序】项目【{{#projectNumber}}】详情", type = "NewProject", subType = "详情", bizNo = "")
    public ResponseDTO<NewProjectVO> getSingleDetail(@PathVariable(value = "projectId") String id) throws Exception {
        NewProjectVO rsp = projectAppService.detail(id);
        LogRecordContext.putVariable("projectNumber", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("获取项目角色列表")
    @GetMapping(value = "/getList/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】查看【项目信息小程序】项目【{{#projectNumber}}】角色的列表", type = "NewProject", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectRoleDTO>> getProjectRoleList(@PathVariable("projectId") String projectId) throws Exception {
        List<ProjectRoleDTO> rsp = projectAppService.listProjectRole(projectId);
        return new ResponseDTO<>(rsp);
    }
}
