package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ProjectFullSizeReportDTO;
import com.chinasie.orion.domain.entity.ProjectFullSizeReport;
import com.chinasie.orion.domain.vo.ProjectFullSizeReportVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectFullSizeReport 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
public interface ProjectFullSizeReportService extends OrionBaseService<ProjectFullSizeReport> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectFullSizeReportVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectFullSizeReportDTO
     */
    String create(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectFullSizeReportDTO
     */
    Boolean edit(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectFullSizeReportVO> pages(Page<ProjectFullSizeReportDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel( ProjectFullSizeReportDTO projectFullSizeReportDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectFullSizeReportVO> vos) throws Exception;

    List<ProjectFullSizeReportVO> getList(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception;

    Boolean syncData(Integer year) throws JsonProcessingException;
}
