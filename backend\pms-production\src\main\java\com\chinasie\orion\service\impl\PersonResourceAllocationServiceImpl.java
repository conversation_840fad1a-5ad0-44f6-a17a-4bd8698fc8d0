package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import com.chinasie.orion.domain.dto.resourceAllocation.ResourceAllocationDTO;
import com.chinasie.orion.domain.dto.resourceAllocation.UpdateTimeDTO;
import com.chinasie.orion.domain.vo.resourceAllocation.*;
import com.chinasie.orion.repository.PersonResourceAllocationMapper;
import com.chinasie.orion.repository.RepairPlanMapper;
import com.chinasie.orion.service.PersonResourceAllocationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * 人员资源调配服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@Service
@Slf4j
public class PersonResourceAllocationServiceImpl implements PersonResourceAllocationService {

    @Autowired
    private PersonResourceAllocationMapper personResourceAllocationMapper;

    @Autowired
    private RepairPlanMapper repairPlanMapper;

    /**
     * 获取个人资源分配信息
     * 根据提供的资源分配查询条件，返回相应的维修计划视图对象
     *
     * @param resourceAllocationDTO 资源分配查询条件封装对象，包含查询所需的参数
     * @return 返回RepairPlanVO对象，包含个人资源分配信息
     */
    @Override
    public RepairPlanVO getPersonInfo(ResourceAllocationDTO resourceAllocationDTO) {
        // 根据维修轮次查询维修计划
        RepairPlanVO repairPlanVO = repairPlanMapper.queryRepairPlanByRepairRound(resourceAllocationDTO.getRepairRound());

        // 如果维修轮次为空，则创建一个新的维修计划视图对象并返回
        if (repairPlanVO == null) {
            RepairPlanVO repairPlanVO1 = new RepairPlanVO();
            repairPlanVO1.setOverNumbers(0);
            repairPlanVO1.setRepairName(resourceAllocationDTO.getRepairRound());
            return repairPlanVO1;
        }

        // 获取查询类型
        String queryType = resourceAllocationDTO.getQueryType();
        List<ResourceAllocationInfoVO> test = new ArrayList<>();

        // 根据查询类型，调用不同的方法获取资源分配信息
        if ("0".equals(queryType)) {//人员
            test = personResourceAllocationMapper.getPersonInfo(resourceAllocationDTO.getKeyword(), null, null, resourceAllocationDTO.getRepairRound(), null, null);
        } else if ("1".equals(queryType)) {//物资
            test = personResourceAllocationMapper.getAssetsInfo(resourceAllocationDTO.getKeyword(), null, null, resourceAllocationDTO.getRepairRound(), null, null);
        }

        RepairPlanVO repairPlanVO1 = new RepairPlanVO();
        try {

            // 处理并获取资源分配信息列表
            List<ResourceAllocationInfos> resourceAllocationInfosList = getInfos(test);

            repairPlanVO1 = buildOrgTree(repairPlanVO, resourceAllocationDTO, resourceAllocationInfosList);
        } catch (Exception e){
            log.error("获取资源分配信息失败", e);
            repairPlanVO.setOverNumbers(0);
            repairPlanVO.setRepairName(resourceAllocationDTO.getRepairRound());
            return repairPlanVO;
        }


        // 构建并返回组织树结构的维修计划视图对象
        return repairPlanVO1;
    }

    @Override
    public RepairPlanVO queryRepairPlanByRepairRound(String repairRound) {
        return repairPlanMapper.queryRepairPlanByRepairRound(repairRound);
    }

    @Override
    public List<RepairPlanVO> queryRepairPlan(String repairRound) {
        return repairPlanMapper.queryRepairPlan(repairRound);
    }

    /**
     * 构建大修组织树
     */
    private RepairPlanVO buildOrgTree(RepairPlanVO repairPlanVO, ResourceAllocationDTO resourceAllocationDTO, List<ResourceAllocationInfos> resourceAllocationInfosList) {
        List<ResourceAllocationSpecialtyVO> nodes = personResourceAllocationMapper.queruSpecialtyandTeam(resourceAllocationDTO.getRepairRound());
        Map<String, ResourceAllocationSpecialtyVO> nodeMap = new HashMap<>();
        List<ResourceAllocationSpecialtyVO> roots = new ArrayList<>();

        String repairRound = resourceAllocationDTO.getRepairRound();
        String baseCode = repairPlanVO.getBaseCode();
        String status = resourceAllocationDTO.getStatus();
        Date beginTime = repairPlanVO.getBeginTime();
        Date endTime = repairPlanVO.getEndTime();

        AtomicReference<Integer> overNumbers = new AtomicReference<>(0);
        AtomicReference<Integer> overLapNumbers = new AtomicReference<Integer>(0);

        // 按 number 对信息 进行分组
        Map<String, List<ResourceAllocationInfos>> groupByNumber = resourceAllocationInfosList.stream()
                .collect(Collectors.groupingBy(ResourceAllocationInfos::getNumber));
        // 获取所有键的数组
        List<String> keysArray = new ArrayList<>(groupByNumber.keySet());
        // 查询所有其他大修的人员/资产信息
        List<ResourceAllocationInfoVO> groups = new ArrayList<>();
        if ("0".equals(resourceAllocationDTO.getQueryType())) {
            groups = personResourceAllocationMapper.getPersonInfo(resourceAllocationDTO.getKeyword(), beginTime, endTime, null, baseCode, keysArray);
        } else if ("1".equals(resourceAllocationDTO.getQueryType())) {
            groups = personResourceAllocationMapper.getAssetsInfo(resourceAllocationDTO.getKeyword(), beginTime, endTime, null, baseCode, keysArray);
        }
        List<ResourceAllocationInfos> infos;
        if (!groups.isEmpty()) {
            // 按 repairRound 分组
            Map<String, List<ResourceAllocationInfoVO>> groupByNumber1 = groups.stream()
                    .collect(Collectors.groupingBy(ResourceAllocationInfoVO::getNumber));
            // 对每个分组按 inDate 排序并取最早的一条
            List<ResourceAllocationInfoVO> earliestRecords = groupByNumber1.values().stream()
                    .map(records -> records.stream()
                            .sorted(Comparator.comparing(ResourceAllocationInfoVO::getInDate))
                            .findFirst()
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            infos = getInfos(earliestRecords);
        } else if ("0".equals(status) && groups.isEmpty()) {
            infos = null;
            resourceAllocationInfosList.clear();
        } else {
            infos = null;
        }

        // 获取并修改开始时间（减去1个月）
        if (beginTime != null) {
            LocalDate localDate = beginTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate newDate = localDate.minusMonths(1);
            Date newBeginTime = Date.from(newDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            repairPlanVO.setPlanBeginTime(beginTime);
            repairPlanVO.setBeginTime(newBeginTime);
        }
        // 获取并修改结束时间（增加2个月）
        if (endTime != null){
            LocalDate endDate = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate newDate = endDate.plusMonths(2);
            Date newEndTime = Date.from(newDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            repairPlanVO.setPlanEndTime(endTime);
            repairPlanVO.setEndTime(newEndTime);
        }

        if(!nodes.isEmpty()) {
            // 初始化节点映射
            for (ResourceAllocationSpecialtyVO node : nodes) {
                nodeMap.put(node.getId(), node);
            }
            // 构建专业下的班组
            for (ResourceAllocationSpecialtyVO node : nodes) {
                if (("3".equals(node.getLevel()) || "4".equals(node.getLevel())) && node.getParentId() != null) {
                    ResourceAllocationSpecialtyVO parent = nodeMap.get(node.getParentId());
                    if (parent != null) {
                        if (parent.getChildren() == null) parent.setChildren(new ArrayList<>());
                        ResourceAllocationTeamVO team = new ResourceAllocationTeamVO();
                        team.setId(node.getId());
                        team.setName(node.getName());
                        team.setCode(node.getCode());
                        team.setParentId(node.getParentId());
                        team.setLevel(node.getLevel());
                        team.setLevelType(node.getLevelType());
                        team.setRepairRound(node.getRepairRound());
                        team.setOverNumbers(0);

                        parent.getChildren().add(team);

                        if (team.getChildren() == null) team.setChildren(new ArrayList<>());
                        if (!resourceAllocationInfosList.isEmpty()) {
                            List<ResourceAllocationInfos> collect = resourceAllocationInfosList.stream()
                                    .filter(item -> team.getId().equals(item.getTeamId()))
                                    .collect(Collectors.toList());

                            // 按 number 对匹配的 items 进行分组
                            Map<String, List<ResourceAllocationInfos>> groupedByNumber = collect.stream()
                                    .collect(Collectors.groupingBy(ResourceAllocationInfos::getNumber));
                            // 将分组后的结果转换为所需的格式
                            List<ResourceAllocationInfos> flattenedList = new ArrayList<>();
                            for (List<ResourceAllocationInfos> group : groupedByNumber.values()) {
                                flattenedList.addAll(group);
                            }
                            AtomicReference<Integer> days = new AtomicReference<>(0);
                            // 遍历分组结果，查询人员信息
                            List<ResourceAllocationInfos> infosByNumber = new ArrayList<>();
                            groupedByNumber.forEach((key, value) -> {
                                if (infos != null) {
                                    List<ResourceAllocationInfos> collect1 = infos.stream()
                                            .filter(info -> info.getNumber().equals(key))
                                            .collect(Collectors.toList());
                                    if (collect1.isEmpty() && "0".equals(status)) {
                                        flattenedList.removeAll(value);
                                    }
                                    Map<String, List<ResourceAllocationInfos>> groupedByNumber1 = collect1.stream()
                                            .collect(Collectors.groupingBy(ResourceAllocationInfos::getNumber));
                                    groupedByNumber1.forEach((key1, value1) -> {
                                        if (key1.equals(key)) {
                                            Map<String, Object> stringObjectMap = calculateOverlapDays(value, value1, status, repairPlanVO);
                                            List<ResourceAllocationInfos> group1 = (List<ResourceAllocationInfos>) stringObjectMap.get("group");
                                            days.set((Integer) stringObjectMap.get("zDays"));
                                            if (days.get() == 0 && "0".equals(status)) {
                                                flattenedList.removeAll(value);
                                            } else {
                                                infosByNumber.addAll(group1);
                                            }
                                        } else if ("0".equals(status)) {
                                            flattenedList.removeAll(value);
                                        }
                                    });
                                } else if ("0".equals(status)) {
                                    flattenedList.removeAll(value);
                                }
                            });
                            flattenedList.addAll(infosByNumber);

                            flattenedList.sort(Comparator.comparing(ResourceAllocationInfos::getNumber));
                            //计算整个班组的重叠天数
                            if (!flattenedList.isEmpty()){
                                flattenedList.forEach(item -> overNumbers.set(overNumbers.get() + item.getOverNumbers()));
                            }
                            // 设置到 team 的 children 属性中
                            team.setOverNumbers(overNumbers.get());
                            overNumbers.set(0);
                            team.setChildren(flattenedList);
                        }
                    }
                }
            }

            // 找到根节点
            for (ResourceAllocationSpecialtyVO node : nodes) {
                if ("2".equals(node.getLevel())) roots.add(node);
            }
            try {
                if (!roots.isEmpty()) {
                    roots.forEach(root -> {
                        Integer overNumber = 0;
                        for (ResourceAllocationTeamVO child : root.getChildren()) {
                            overNumber += child.getOverNumbers();
                        }
                        overLapNumbers.set(overLapNumbers.get() + overNumber);
                        root.setOverNumbers(overNumber);
                    });
                    repairPlanVO.setOverNumbers(overLapNumbers.get());
                    repairPlanVO.setResourceAllocationSpecialtyList(roots);
                } else {
                    repairPlanVO.setOverNumbers(0);
                    repairPlanVO.setResourceAllocationSpecialtyList(new ArrayList<>());
                }
            }catch (Exception e){
                log.error("构建大修组织树失败！" + e.getMessage());
                repairPlanVO.setOverNumbers(0);
                repairPlanVO.setResourceAllocationSpecialtyList(new ArrayList<>());
            }

        }
        return repairPlanVO;
    }

    /**
     * 将人员时间信息放到人员sectionTime中
     */
    private List<ResourceAllocationInfos> getInfos(List<ResourceAllocationInfoVO> resourceAllocationInfoVOs) {
        //去重人员信息、时间信息
        Set<ResourceAllocationInfos> resourceAllocationInfosSet = new HashSet<>();
        Set<InfoTime> infoTimeSet = new HashSet<>();
        //遍历人员信息
        for (ResourceAllocationInfoVO node : resourceAllocationInfoVOs) {
            //人员信息
            ResourceAllocationInfos resourceAllocationInfos = new ResourceAllocationInfos();
            resourceAllocationInfos.setId(node.getId());
            resourceAllocationInfos.setRepairRound(node.getRepairRound());
            resourceAllocationInfos.setNumber(node.getNumber());
            resourceAllocationInfos.setName(node.getName());
            resourceAllocationInfos.setTeamName(node.getTeamName());
            resourceAllocationInfos.setTeamId(node.getTeamId());
            resourceAllocationInfos.setIsBasePermanent(node.getIsBasePermanent());
            resourceAllocationInfos.setOverNumbers(0);

            resourceAllocationInfosSet.add(resourceAllocationInfos);

            //人员时间信息
            InfoTime infoTime = new InfoTime();
            infoTime.setNumber(node.getNumber());
            infoTime.setRepairName(node.getRepairRound());
            infoTime.setRealStartDate(node.getInDate());
            infoTime.setRealEndDate(node.getOutDate());
            infoTime.setId(node.getId());
            infoTime.setTeamId(node.getTeamId());
            infoTime.setRelationId(node.getRelationId());
            infoTime.setIsBasePermanent(node.getIsBasePermanent());

            infoTimeSet.add(infoTime);
        }
        List<InfoTime> infoTimeList = new ArrayList<>(infoTimeSet);
        List<ResourceAllocationInfos> resourceAllocationInfosList = new ArrayList<>(resourceAllocationInfosSet);
        if (!resourceAllocationInfosList.isEmpty()) {
            //将人员时间信息放到sectionTime中
            resourceAllocationInfosList.forEach(infosList -> {
                //给每个节点随机生成key
                infosList.setKey(new Random().nextInt(100000000));
                //初始化sectionTime
                if (infosList.getSectionTime() == null) infosList.setSectionTime(new ArrayList<>());
                if (!infoTimeList.isEmpty()){
                    // 找到该节点的入出场时间
                    List<InfoTime> collect = infoTimeList.stream()
                            .filter(infoTime -> infoTime.getNumber().equals(infosList.getNumber())
                                    && infoTime.getRepairName().equals(infosList.getRepairRound())
                                    && infoTime.getTeamId().equals(infosList.getTeamId()))
                            .collect(Collectors.toList());
                    //将时间信息放到sectionTime中
                    infosList.getSectionTime().addAll(collect);
                }
            });
        }
        return resourceAllocationInfosList;
    }

    /**
     * 计算两个日期的重叠天数
     */
    private Map<String, Object> calculateOverlapDays(List<ResourceAllocationInfos> resourceAllocationInfosList, List<ResourceAllocationInfos> group, String status, RepairPlanVO repairPlanVO) {
        Date beginTime = repairPlanVO.getBeginTime();
        LocalDate localBeginTime = beginTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Map<String, Object> map = new HashMap<>();
        for (ResourceAllocationInfos infos : resourceAllocationInfosList) {
            Date inDate1 = infos.getSectionTime().get(0).getRealStartDate();
            Date outDate1 = infos.getSectionTime().get(0).getRealEndDate();
            int days = 0;
            int zDays = 0;

            Iterator<ResourceAllocationInfos> groupIterator = group.iterator();
            while (groupIterator.hasNext()) {
                ResourceAllocationInfos info = groupIterator.next();
                //判断是否是同一个人员
                if (infos.getNumber().equals(info.getNumber())) {
                    Date inDate2 = info.getSectionTime().get(0).getRealStartDate();
                    Date outDate2 = info.getSectionTime().get(0).getRealEndDate();
                    //判断时间是否为空
                    if (inDate1 == null || outDate1 == null || inDate2 == null || outDate2 == null) {
                        //为空则直接设置重叠数为0
                        days = 0;
                        infos.setOverNumbers(days);
                    } else {
                        //Date转LocalDate
                        LocalDate localInDate1 = inDate1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate localOutDate1 = outDate1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate localInDate2 = inDate2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate localOutDate2 = outDate2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        //判断是否重叠
                        LocalDate startMax = localInDate1.isAfter(localInDate2) ? localInDate1 : localInDate2;
                        LocalDate endMin = localOutDate1.isBefore(localOutDate2) ? localOutDate1 : localOutDate2;
                        //判断是否重叠
                        if (startMax.isAfter(endMin)) {
                            //无重叠设置重叠数为0
                            days = 0;
                            infos.setOverNumbers(days);
                        } else {
                            //添加重叠时间
                            InfoTime infoTime = new InfoTime();
                            infoTime.setRealStartDate(Date.from(startMax.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                            infoTime.setRealEndDate(Date.from(endMin.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                            infoTime.setNumber("-1");

                            //判断开始时间是否在大修开始时间之前
                            if (localInDate2.isBefore(localBeginTime)) {
//                                infoTime.setRealStartDate(Date.from(localBeginTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                                info.getSectionTime().get(0).setRealStartDate(Date.from(localBeginTime.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                            }

                            info.getSectionTime().add(infoTime);
                            days = (int) ChronoUnit.DAYS.between(startMax, endMin) + 1;
                            info.setOverNumbers(days);
                        }
                    }
                    //判断重叠数为0
                    if (days == 0 && "0".equals(status)) {
                        //为0则删除该节点
                        groupIterator.remove();
                    }
                }
                zDays += days;
            }
            map.put("zDays", zDays);
            map.put("group", group);
        }
        //计算并返回重叠天数
        return map;
    }

    @Override
    @Transactional
    public Integer setPeresonTime(List<UpdateTimeDTO> updateTimeDTOList) {

//        Map<Boolean, List<UpdateTimeDTO>> collect = updateTimeDTOList.stream()
//                .collect(Collectors.groupingBy(UpdateTimeDTO::getIsBasePermanent));
//
//        List<UpdateTimeDTO> permanentList = collect.get(true);
//        List<UpdateTimeDTO> nonPermanentList = collect.get(false);
//
//        Integer integer = 0;
//        Integer integer1 = 0;
//        if (permanentList != null) {
//            integer = personResourceAllocationMapper.setPersonTimeWithTrue(permanentList);
//        } else if (nonPermanentList != null) {
//            integer1 = personResourceAllocationMapper.setPersonTimeWithFalse(nonPermanentList);
//        }
        if (updateTimeDTOList.isEmpty()){
            return 0;
        }
        Integer integer = personResourceAllocationMapper.setPersonTimeWithTrue(updateTimeDTOList);
        // 调用 MyBatis 的更新方法
        return integer;

    }

    @Override
    public Integer setAssetTime(List<UpdateTimeDTO> updateTimeDTOList) {
        if (updateTimeDTOList.isEmpty()){
            return 0;
        }
        Integer integer = personResourceAllocationMapper.setAssetTime(updateTimeDTOList);
        return integer;
    }
}
