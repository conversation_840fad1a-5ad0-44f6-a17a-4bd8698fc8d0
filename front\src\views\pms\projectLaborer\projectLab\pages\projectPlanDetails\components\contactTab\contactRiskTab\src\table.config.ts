export const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    align: 'left',
    width: '220px',
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '风险类型',
    dataIndex: 'riskTypeName',
    key: 'riskType',
    width: '80px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'riskTypeName' },
    // sorter: true
    ellipsis: true,
  },
  {
    title: '发生概率',
    dataIndex: 'riskProbabilityName',
    key: 'riskProbabilityName',
    width: '100px',
    align: 'left',
    slots: { customRender: 'riskProbabilityName' },

    // sorter: true,
    ellipsis: true,
  },
  {
    title: '影响程度',
    dataIndex: 'riskInfluenceName',
    key: 'riskInfluenceName',

    width: '120px',
    align: 'left',
    slots: { customRender: 'riskInfluenceName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '预估发生时间',
    dataIndex: 'predictStartTimeName',
    key: 'predictStartTimeName',
    width: '120px',
    align: 'left',
    slots: { customRender: 'predictStartTimeName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '应对策略',
    dataIndex: 'copingStrategyName',
    key: 'copingStrategyName',

    width: '120px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'copingStrategyName' },
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'statusName',

    width: '120px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalName',

    width: '120px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',

    width: '170px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'createTime' },
  },
];
