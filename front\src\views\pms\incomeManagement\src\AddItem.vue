<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import {
  h, nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import {
  BasicForm, useForm,
} from 'lyra-component-vue3';

const emits = defineEmits([]);
const props = defineProps({
  detail: {
    type: Object,
    default: () => {
    },
  },
  type: {
    type: String,
    default: '',
  },
});
const [registerForm, formFunction] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 24,
  },
  schemas: [
    {
      field: 'expectedProductPrice',
      component: 'InputNumber',
      label: '原预期产品单价',
      rules: [{ required: true }],
      componentProps: {
        addonAfter: '元',
        style: { width: '100%' },
      },
    },
    {
      field: 'origExpectedOutcome',
      component: 'InputNumber',
      label: '原预期总产出',
      rules: [{ required: true }],
      componentProps: {
        addonAfter: '元',
        style: { width: '100%' },
      },
    },
    {
      field: 'expectedOutcomes',
      component: 'InputNumber',
      label: '现预期总产出',
      rules: [{ required: true }],
      componentProps: {
        addonAfter: '元',
        style: { width: '100%' },
      },
    },
  ],
});

onMounted(() => {
  if (props.type === 'edit') {
    initForm();
  }
});

async function initForm() {
  await formFunction.setFieldsValue({
    ...props.detail,
  });
  await nextTick();
  await formFunction.clearValidate();
}

defineExpose({
  async getData() {
    const formData = await formFunction.validate();
    return {
      ...formData,
    };
  },
});
</script>
