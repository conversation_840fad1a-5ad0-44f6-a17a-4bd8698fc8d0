<!--
 * @Description:安质环绩效排名
 * @Autor: laotao117
 * @Date: 2024-08-21 18:36:08
 * @LastEditors: laotao117
 * @LastEditTime: 2024-09-19 14:01:07
-->
<template>
  <BasicCard
    :isBorder="true"
    title="安质环绩效排名"
    class="card-border active-box"
  >
    <template #titleRight>
      <div class="flex-right-box">
        <DatePicker
          v-model:value="state.yearValue"
          picker="year"
          format="YYYY"
          :disabled-date="disabledDate"
          :allowClear="false"
          @change="changeOptions"
        />
      </div>
    </template>
    <div class="charts-box ">
      <Row :gutter="10">
        <Col span="12">
          <div class="charts-box-1">
            <div class="charts-box-1-pw">
              <Chart
                ref="chartOptions1Ref"
                :option="state.chartOptions1"
              />
            </div>
            <div class="charts-box-1-tips">
              <!-- 基准分:100分；优秀:≥105；正常:95-104.9；预警:85-94.9；危险:0-84.9 -->
              <div class="charts-box-1-tips-100">
                基准分:100分
              </div>
              <div class="charts-box-1-tips-105">
                <div class="charts-box-1-tips-105-box" />
                优秀:≥105
              </div>
              <div class="charts-box-1-tips-95">
                <div class="charts-box-1-tips-95-box" />
                正常:95-104.9
              </div>
              <div class="charts-box-1-tips-85">
                <div class="charts-box-1-tips-85-box" />
                预警:85-94.9
              </div>
              <div class="charts-box-1-tips-0">
                <div class="charts-box-1-tips-0-box" />
                危险:0-84.9
              </div>
            </div>
          </div>
        </Col>

        <Col span="12">
          <div class="charts-box-1">
            <div class="charts-box-1-pw">
              <Chart
                ref="chartOptions2Ref"
                :option="state.chartOptions2"
              />
            </div>
            <div class="charts-box-1-tips">
              <!-- 基准分:100分；优秀:≥105；正常:95-104.9；预警:85-94.9；危险:0-84.9 -->
              <div class="charts-box-1-tips-100">
                基准分:100分
              </div>
              <div class="charts-box-1-tips-105">
                <div class="charts-box-1-tips-105-box" />
                优秀:≥105
              </div>
              <div class="charts-box-1-tips-95">
                <div class="charts-box-1-tips-95-box" />
                正常:95-104.9
              </div>
              <div class="charts-box-1-tips-85">
                <div class="charts-box-1-tips-85-box" />
                预警:85-94.9
              </div>
              <div class="charts-box-1-tips-0">
                <div class="charts-box-1-tips-0-box" />
                危险:0-84.9
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">
import {
  Col,
  DatePicker,
  Row,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  BasicCard,
  Chart,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';

import {
  h,
  onMounted,
  reactive,
  ref,
  Ref,
  onActivated,
  nextTick,
  onBeforeUnmount,
} from 'vue';
import Api from '/@/api';
const state: any = reactive({
  kpiData: [],
  yearData: '1',
  yearValue: dayjs(),
  chartOptions1: {},
  chartOptions2: {},

});
const disabledDate = (time) => dayjs(time).year() < 2024;
const chartOptions1Ref = ref(null);
const chartOptions2Ref = ref(null);
onMounted(() => {
  init();
});
onActivated(() => {
  init();
});
onBeforeUnmount(() => {
  // 移除点击事件监听器
  if (chartOptions2Ref.value) {
    chartOptions2Ref.value.getChartAllMethods().off('click', 'series', handleChartClick);
  }
  if (chartOptions1Ref.value) {
    chartOptions1Ref.value.getChartAllMethods().off('click', 'series', handleChartClick);
  }
});
let deptCode = '';
function init() {
  new Api('/pms').fetch({
    year: dayjs(state.yearValue).format('YYYY'),
    deptScoreType: 'dept',
  }, '/ampere/ring/board/statistics/score', 'POST').then((res) => {
    nextTick(() => {
      // kpiScore deptName kpiScore需要显示指定颜色

      state.chartOptions1 = {
        // 标题 部门
        title: {
          text: '部门',
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: res.map((item: any) => item.deptName),
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              rotate: 45, // 将标签旋转45度
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            boundaryGap: ['0%', '6%'],
          },

        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '20px',
            data: res.map((item: any) => ({
              value: item.kpiScore,
              deptCode: item.deptCode,
              itemStyle: {
                color: item.kpiScore >= 105 ? 'rgba(111, 209, 149, 0.80)' : item.kpiScore >= 95 ? 'rgba(106, 151, 251, 0.80)' : item.kpiScore >= 85 ? 'rgba(250, 186, 74, 0.80)' : 'rgba(255, 146, 138, 0.80)',
              },
              label: {
                show: true,
                position: 'top',
              },
            })),
          },
        ],

      };

      setTimeout (() => {
        chartOptions1Ref.value?.getChartAllMethods().on('click', 'series', handleChartClick);
      }, 300);
    });
  });

  new Api('/pms').fetch({
    year: dayjs(state.yearValue).format('YYYY'),
    deptScoreType: 'project',
  }, '/ampere/ring/board/statistics/score', 'POST').then((res) => {
    // if (res?.length === 0) {
    //   return;
    // }
    nextTick(() => {
      state.chartOptions2 = {
        // 标题 部门
        title: {
          text: '项目部',
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: res.map((item: any) => item.deptName),
            axisTick: {
              alignWithLabel: true,
            },
            axisLabel: {
              rotate: 45, // 将标签旋转45度
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            boundaryGap: ['0%', '6%'],
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '20px',
            data: res.map((item: any) => ({
              value: item.kpiScore,
              deptCode: item.deptCode,
              itemStyle: {
                color: item.kpiScore >= 105 ? 'rgba(111, 209, 149, 0.80)' : item.kpiScore >= 95 ? 'rgba(106, 151, 251, 0.80)' : item.kpiScore >= 85 ? 'rgba(250, 186, 74, 0.80)' : 'rgba(255, 146, 138, 0.80)',
              },
              label: {
                show: true,
                position: 'top',
              },
            })),
          },
        ],

      };
      setTimeout (() => {
        chartOptions2Ref.value?.getChartAllMethods().on('click', 'series', handleChartClick);
      }, 300);
    });
  });
}
const handleChartClick = (params) => {
  deptCode = params.data.deptCode;
  openMTable();
};

const milestoneTable = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  isFilter2: false,
  showTableSetting: false,
  height: 550,
  api: (params) => new Api('/pms').fetch({
    ...params,
    query: {
      deptCode,
      year: dayjs(state.yearValue).format('YYYY'),
    },

  }, '/ampere/ring/board/statistics/score/details', 'POST'),
  columns: [
    {
      title: '事件主题',
      dataIndex: 'checkSubject',
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevel',
    },
    {
      title: '事件地点',
      dataIndex: 'eventAddress',
    },

    {
      title: '事件描述',
      dataIndex: 'eventDesc',
    },

    {
      title: '事发时间',
      dataIndex: 'eventDate',
      type: 'dateTime',
    },
    // 考核
    {
      title: '考核',
      dataIndex: 'score',
    },
    {
      title: '归口责任任部门',
      dataIndex: 'gkdept',
    },

  ],

};

function openMTable() {
  const selectRef: Ref = ref();
  openModal({
    title: '安全责任制考核',
    width: 1300,
    height: 750,
    footer: {
      isCancel: false,
      isOk: false,
    },
    content() {
      return h(OrionTable, {
        ref: selectRef,
        options: milestoneTable,
      });
    },
  });
}
const changeOptions = (value: string) => {
  // console.log(value);
  init();
};

</script>
<style scoped lang="less">
.card-border {
  border: 1px solid var(--ant-border-color-base);
  padding: 10px 15px;
  margin: 0 !important;
}

.flex-right-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.active-box {

  // 鼠标移入时的样式 显示阴影效果
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

.charts-box {
  width: 100%;
  height: 440px;

  &-1 {
    width: 100%;
    height: 440px;

    &-pw {
      height: 405px;
      width: 100%;
      // border: 1px solid #000;
    }

    &-tips {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 0 20px;
      margin-top: 10px;
      gap: 15px;
      // 文字不换行
      white-space: nowrap;
      padding-left: 30px;

      &-100 {
        color: #000;
        font-size: 14px;
      }

      &-105 {
        color: #000;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-box {
          width: 12px;
          height: 12px;
          background-color: rgba(111, 209, 149, 0.80);
          margin-right: 5px;
        }
      }

      &-95 {
        color: #000;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-box {
          width: 12px;
          height: 12px;
          background-color: rgba(106, 151, 251, 0.80);
          margin-right: 5px;
        }
      }

      &-85 {
        color: #000;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-box {
          width: 12px;
          height: 12px;
          background-color: rgba(250, 186, 74, 0.80);
          margin-right: 5px;
        }
      }

      &-0 {
        color: #000;
        font-size: 14px;
        display: flex;
        flex-direction: row;
        align-items: center;

        &-box {
          width: 12px;
          height: 12px;
          background-color: rgba(255, 146, 138, 0.80);
          margin-right: 5px;
        }
      }
    }

  }

  &-2 {
    width: 100%;
    height: 440px;
    border: 1px solid #000;
  }
}
</style>