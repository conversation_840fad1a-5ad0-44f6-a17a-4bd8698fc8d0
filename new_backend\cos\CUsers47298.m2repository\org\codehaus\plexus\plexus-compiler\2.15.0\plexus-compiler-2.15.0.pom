<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>17</version>
  </parent>

  <artifactId>plexus-compiler</artifactId>
  <version>2.15.0</version>
  <packaging>pom</packaging>

  <name>Plexus Compiler</name>
  <description>Plexus Compiler is a Plexus component to use different compilers through a uniform API.</description>

  <modules>
    <module>plexus-compiler-api</module>
    <module>plexus-compiler-manager</module>
    <module>plexus-compilers</module>
    <module>plexus-compiler-test</module>
    <module>plexus-compiler-its</module>
  </modules>

  <scm>
    <connection>${scm.url}</connection>
    <developerConnection>${scm.url}</developerConnection>
    <tag>plexus-compiler-2.15.0</tag>
    <url>http://github.com/codehaus-plexus/plexus-compiler/tree/${project.scm.tag}/</url>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/codehaus-plexus/plexus-compiler/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${scm.url}</url>
    </site>
  </distributionManagement>

  <properties>
    <scm.url>scm:git:**************:codehaus-plexus/plexus-compiler.git</scm.url>
    <javaVersion>8</javaVersion>
    <redirectTestOutputToFile>true</redirectTestOutputToFile>
    <project.build.outputTimestamp>2024-03-10T16:44:32Z</project.build.outputTimestamp>
    <aspectj.version>1.9.21</aspectj.version>
    <mavenVersion>3.6.3</mavenVersion>
    <minimalMavenBuildVersion>${mavenVersion}</minimalMavenBuildVersion>
    <errorprone.version>2.25.0</errorprone.version>
    <trimStackTrace>false</trimStackTrace>
    <preparationGoals>clean install</preparationGoals>
    <maven.compiler.version>3.12.1</maven.compiler.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-test</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-aspectj</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-eclipse</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-javac-errorprone</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-javac</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-compiler-manager</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>2.2.0</version>
      </dependency>
      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5Version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-testing</artifactId>
        <version>1.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>33.0.0-jre</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>org.eclipse.sisu.plexus</artifactId>
        <version>0.9.0.M2</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>org.eclipse.sisu.inject</artifactId>
        <version>0.9.0.M2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>4.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-xml</artifactId>
        <version>3.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.36</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <redirectTestOutputToFile>${redirectTestOutputToFile}</redirectTestOutputToFile>
            <systemPropertyVariables>
              <maven.repo.local>${settings.localRepository}</maven.repo.local>
            </systemPropertyVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven.compiler.version}</version>
          <configuration>
            <proc>none</proc>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <releaseProfiles>plexus-release,tools.jar</releaseProfiles>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <topSiteURL>${scm.url}</topSiteURL>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>[17,)</version>
                  <message>[ERROR] OLD JDK [${java.version}] in use. This projects requires JDK 17 or newer</message>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.eclipse.sisu</groupId>
        <artifactId>sisu-maven-plugin</artifactId>
      </plugin>

    </plugins>
  </build>

</project>
