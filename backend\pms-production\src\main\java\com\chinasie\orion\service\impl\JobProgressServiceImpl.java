package com.chinasie.orion.service.impl;

import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.dto.excel.JobProgressParamDTO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobProgress;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.vo.JobProgressVO;
import com.chinasie.orion.domain.vo.excel.JobProgressExportVO;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.service.JobNodeStatusService;
import com.chinasie.orion.service.JobProgressService;
import com.chinasie.orion.repository.JobProgressMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobProgress 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
@Service
@Slf4j
public class JobProgressServiceImpl extends  OrionBaseServiceImpl<JobProgressMapper, JobProgress>   implements JobProgressService {



    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private JobNodeStatusService jobNodeStatusService;

    @Autowired
    private JobManageMapper jobManageMapper;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobProgressVO detail(String id,String pageCode) throws Exception {
        JobProgress jobProgress =this.getById(id);
        JobProgressVO result = BeanCopyUtils.convertTo(jobProgress,JobProgressVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobProgressDTO
     */
    @Override
    public  String create(JobProgressDTO jobProgressDTO) throws Exception {
        JobProgress jobProgress =BeanCopyUtils.convertTo(jobProgressDTO,JobProgress::new);
        this.save(jobProgress);

        String rsp=jobProgress.getId();
        String jobId =jobProgress.getJobId();

        // 埋点
        jobNodeStatusService.setNodeStatus(jobId, Arrays.asList("devlopInfoMaintenance"));
        JobManage jobManage = jobManageMapper.selectById(jobProgressDTO.getJobId());
        String userId = jobManageMapper.selectImportantProjectRspUserByJobId(jobId);
        mscBuildHandlerManager.send(jobManage,MessageNodeDict.NODE_JOB_PROGRESS,userId);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobProgressDTO
     */
    @Override
    public Boolean edit(JobProgressDTO jobProgressDTO) throws Exception {
        JobProgress jobProgress =BeanCopyUtils.convertTo(jobProgressDTO,JobProgress::new);

        this.updateById(jobProgress);

        String rsp=jobProgress.getId();


        // TODO 需要发送消息
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobProgressVO> pages( Page<JobProgressDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobProgress> condition = new LambdaQueryWrapperX<>( JobProgress. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobProgress::getCreateTime);

        JobProgressDTO jobProgressDTO =pageRequest.getQuery();
        if(Objects.nonNull(jobProgressDTO)){
            if(StringUtils.hasText(jobProgressDTO.getJobId())){
                condition.eq(JobProgress::getJobId,jobProgressDTO.getJobId());
            }
        }
        Page<JobProgress> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
//        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobProgress::new));

        PageResult<JobProgress> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobProgressVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobProgressVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobProgressVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业工作进展导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobProgressDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobProgressExcelListener excelReadListener = new JobProgressExcelListener();
        EasyExcel.read(inputStream,JobProgressDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobProgressDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业工作进展导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobProgress> jobProgresses =BeanCopyUtils.convertListTo(dtoS,JobProgress::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobProgress-import::id", importId, jobProgresses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobProgress> jobProgresses = (List<JobProgress>) orionJ2CacheService.get("pmsx::JobProgress-import::id", importId);
        log.info("作业工作进展导入的入库数据={}", JSONUtil.toJsonStr(jobProgresses));

        this.saveBatch(jobProgresses);
        orionJ2CacheService.delete("pmsx::JobProgress-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobProgress-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(JobProgressParamDTO progressParamDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobProgress> condition = new LambdaQueryWrapperX<>( JobProgress. class);
        if (StringUtils.hasText(progressParamDTO.getJobId())) {
            condition.eq(JobProgress::getJobId, progressParamDTO.getJobId());
        }
        condition.orderByDesc(JobProgress::getWorkDate);
        List<JobProgress> jobProgresses =   this.list(condition);
        List<JobProgressExportVO> jobProgressExportVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(jobProgresses)){
            AtomicInteger i = new AtomicInteger(1);
            for (JobProgress jobProgress : jobProgresses) {
                JobProgressExportVO jobProgressExportVO= BeanCopyUtils.convertTo(jobProgress, JobProgressExportVO::new);
                jobProgressExportVO.setProgressSchedule(Objects.isNull(jobProgress.getProgressSchedule())? BigDecimal.ZERO.toString():
                        jobProgress.getProgressSchedule().doubleValue() + "%");
                jobProgressExportVO.setSort(i.get());
                i.incrementAndGet();
                jobProgressExportVOList.add(jobProgressExportVO);
            }
        }
        String fileName = "作业工作进展数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobProgressExportVO.class,jobProgressExportVOList );
    }

    @Override
    public void  setEveryName(List<JobProgressVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public Map<String, JobProgress> listByJobIdsAndDate(List<String> jobIdList, Date workDate) {
        LambdaQueryWrapperX<JobProgress> condition = new LambdaQueryWrapperX<>( JobProgress. class);
        condition.in(JobProgress::getJobId, jobIdList);
        if(Objects.nonNull(workDate)){
            condition.eq(JobProgress::getWorkDate, workDate);
        }
        condition.select(JobProgress::getJobId,JobProgress::getWorkDate,JobProgress::getProgressSchedule
                ,JobProgress::getProgressDetail,JobProgress::getCreateTime,JobProgress::getRemark);
        condition.orderByDesc(JobProgress::getCreateTime);
        List<JobProgress> jobProgresses =   this.list(condition);
        if(CollectionUtils.isEmpty(jobProgresses)){
            return  new HashMap<>();
        }

        Map<String, JobProgress> maxTimeDataPerGroup = new HashMap<>();
        for (JobProgress jobProgress : jobProgresses) {
            String jobId= jobProgress.getJobId();
            JobProgress jo=  maxTimeDataPerGroup.get(jobId);
            if(null == jo){
                maxTimeDataPerGroup.put(jobId, jobProgress);
            }else{
                if(jo.getWorkDate().before(jobProgress.getWorkDate())){
                    maxTimeDataPerGroup.put(jobId, jobProgress);
                }
            }
        }
        return maxTimeDataPerGroup;
    }


    public static class JobProgressExcelListener extends AnalysisEventListener<JobProgressDTO> {

        private final List<JobProgressDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobProgressDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobProgressDTO> getData() {
            return data;
        }
    }


}
