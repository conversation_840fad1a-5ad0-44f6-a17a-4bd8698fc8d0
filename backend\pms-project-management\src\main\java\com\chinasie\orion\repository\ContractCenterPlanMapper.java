package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.entity.ContractCenterPlan;
import com.chinasie.orion.domain.entity.ContractCenterPlanStatistics;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;
import com.chinasie.orion.domain.vo.CostVO;
import com.chinasie.orion.domain.vo.PlanVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * ContractCenterPlan Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@Mapper
public interface ContractCenterPlanMapper extends  OrionBaseMapper  <ContractCenterPlan> {


    @Select("SELECT \n" +
            "\tpcm.contract_number as 'contractNumber',\n" +
            "\tpcm.contract_name as 'contractName',\n" +
            "\tnfci.`status` as 'contractStatus',\n" +
            "\tpcm.status as 'status',\n" +
            "\tpccp.center_code as 'centerCode',\n" +
            "\tpccp.center_name as 'centerName', \n" +
            "\tnfci.status as 'contractStatus' \n" +
            "FROM\n" +
            "\tpmsx_contract_main pcm\n" +
            "\tINNER JOIN ncf_form_contract_info nfci on pcm.contract_number = nfci.contract_number\n" +
            "\tINNER JOIN pmsx_contract_center_plan pccp on pccp.contract_number = pcm.contract_number\n" +
            "\twhere pcm.logic_status = 1 and YEAR(pcm.year) = #{year}")
    /**
     * 获取计划
     * @param year 年份
     * @return 结果
     */
    public List<PlanVO> getPlanGroupByCenter(@Param("year") Integer year);


    @Select("SELECT \n" +
            "\tpcm.contract_number as 'contractNumber',\n" +
            "\tpcm.contract_name as 'contractName',\n" +
            "\tnfci.`status` as 'contractStatus',\n" +
            "\tpcm.status as 'status',\n" +
            "\tpccp.center_code as 'centerCode',\n" +
            "\tpccp.center_name as 'centerName', \n" +
            "\tnfci.status as 'contractStatus' \n" +
            "FROM\n" +
            "\tpmsx_contract_main pcm\n" +
            "\tINNER JOIN ncf_form_contract_info nfci on pcm.contract_number = nfci.contract_number\n" +
            "\tLEFT JOIN pmsx_contract_center_plan pccp on pccp.contract_number = pcm.contract_number\n" +
            "\twhere pcm.logic_status = 1 and YEAR(pcm.year) = #{year}")
    /**
    * 获取计划
    * @param year 年份
    * @return 结果
    */
    public List<PlanVO> getPlanGroupByCenterContract(@Param("year") Integer year);

    @Select("SELECT\n" +
            "\tpccp.center_code AS 'centerCode',\n" +
            "\tpccp.contract_number AS 'contractNumber',\n" +
            "\tpccp.num AS 'planPersonNum',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tpccp.cost_type_number as 'costType',\n" +
            "\tpcct.cost_name as 'costTypeName',\n" +
            "\tpccp.status as 'status'\n" +
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp \n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct on pccp.cost_type_id = pcct.id\n" +
            "\twhere pccp.logic_status = 1 and YEAR(year) = #{year}")
    /**
     * 获取所有的成本vo
     * @Param year 年份
     * @return 结果
     */
    public List<CostVO> getAllCost(@Param("year") Integer year);



    @Select("SELECT \n" +
            "\tpcm.contract_number as 'contractNumber',\n" +
            "\tpcm.contract_name as 'contractName',\n" +
            "\tnfci.`status` as 'contractStatus',\n" +
            "\tpcm.status as 'status',\n" +
            "\tpccp.center_code as 'centerCode',\n" +
            "\tpccp.center_name as 'centerName'\n" +
            "FROM\n" +
            "\tpmsx_contract_main pcm\n" +
            "\tINNER JOIN ncf_form_contract_info nfci on pcm.contract_number = nfci.contract_number\n" +
            "\tINNER JOIN pmsx_contract_center_plan pccp on pccp.contract_number = pcm.contract_number\n" +
            "\twhere pcm.logic_status = 1 and YEAR(pcm.year) = #{year} and pccp.center_code = #{deptCode}")
    List<PlanVO> getPlanGroupByCenterByDeptCode(@Param("deptCode") List<String> deptCode,@Param("year") Integer year);

    /**
     * 查询成本
     * @param contractCenterPlanListDTO 参数
     * @return 结果
     */
    @Select("SELECT\n" +
            "\tDISTINCT pccp.id,\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.cost_type_number AS 'costTypeNumber',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tpccp.num,\n" +
            "\tpel.before_num as 'beforeNum',\n" +
            "\tpcct.unit, \n" +
            "\tpccp.status, \n"+
            "\tpccp.year, \n"+
            "\tpccp.contract_number as 'contractNumber', \n"+
            "\tpccp.center_name as 'centerName' \n"+
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pccp.cost_type_id = pcct.id \n" +
            "\tAND pccp.contract_number = pcct.contract_number\n"+
            "\tLEFT JOIN pmsx_edit_log pel ON pccp.id = pel.plan_id \n" +
            "WHERE\n" +
            "\tpccp.contract_number = #{contractNumber}\n" +
            "\tAND pccp.center_code = #{centerCode}\n" +
            "\tAND YEAR ( pccp.YEAR ) = #{year}\n" +
            "\tAND pcct.logic_status = 1\n" +
            "\tAND pccp.logic_status = 1")
    List<ContractCenterPlanVO> planList(@Param("contractNumber")String contractNumber,@Param("centerCode")String centerCode,@Param("year")Integer year);


    /**
     * 查询成本
     * @param contractCenterPlanListDTO 参数
     * @return 结果
     */
    @Select("SELECT\n" +
            "\tpccp.id,\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tpccp.num,\n" +
            "\tpel.before_num as 'beforeNum',\n" +
            "\tpcct.unit \n" +
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pccp.cost_type_number = pcct.cost_type_number\n" +
            "\tLEFT JOIN pmsx_edit_log pel ON pccp.id = pel.plan_id \n" +
            "WHERE\n" +
            "\tpccp.contract_number = #{contractNumber}\n" +
            "\tAND YEAR ( pccp.YEAR ) = #{year}\n")
    List<ContractCenterPlanVO> planListNoSumAndCenter(@Param("contractNumber")String contractNumber,@Param("year")Integer year);


    /**
     * 查询成本
     * @param  参数
     * @return 结果
     */
    @Select("SELECT\n" +
            "\tpcct.id,\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.cost_type_number AS 'costTypeNumber',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\tSUM( pccp.num ) AS num,\n" +
            "\tpcct.unit,\n" +
            "\tpccp.STATUS,\n" +
            "\tpccp.YEAR \n" +
            "FROM\n" +
            "\tpmsx_contract_center_plan pccp\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pccp.cost_type_number = pcct.cost_type_number \n" +
            "\tAND pccp.contract_number = pcct.contract_number \n"+
            "\tWHERE\n" +
            "\tpccp.contract_number = #{contractNumber}\n" +
            "\tAND YEAR ( pccp.YEAR ) = #{year}\n" +
            "GROUP BY\n" +
            "\tpcct.cost_name,\n" +
            "\tpcct.cost_type_name,\n" +
            "\tpcct.unit_price,\n" +
            "\tpcct.unit,\n" +
            "\tpcct.id,\n" +
            "\tpccp.STATUS,\n" +
            "\tpccp.YEAR")
    List<ContractCenterPlanVO> planListNoCenter(@Param("contractNumber")String contractNumber,@Param("year")Integer year);




    /**
     * 查询成本
     * @param  参数
     * @return 结果
     */
    @Select("SELECT\n" +
            "\tpcct.id,\n" +
            "\tpcct.cost_type_name AS 'costTypeName',\n" +
            "\tpcct.cost_name AS 'costName',\n" +
            "\tpcct.cost_type_number AS 'costTypeNumber',\n" +
            "\tpcct.unit_price AS 'unitPrice',\n" +
            "\t0 AS 'num',\n" +
            "\tpcct.unit \n" +
            "FROM\n" +
            "\tpmsx_contract_main pcm\n" +
            "\tINNER JOIN pmsx_contract_cost_type pcct ON pcm.contract_number = pcct.contract_number \n" +
            "WHERE\n" +
            "\tpcm.contract_number = #{contractNumber} \n" +
            "\tAND YEAR(pcm.YEAR) = #{year} \n" +
            "\tAND pcct.logic_status = 1 \n" +
            "GROUP BY\n" +
            "\tpcct.cost_name,\n" +
            "\tpcct.cost_type_name,\n" +
            "\tpcct.unit_price,\n" +
            "\tpcct.unit," +
            "\tpcct.id")
    List<ContractCenterPlanVO> planListNoCenterAndPlan(@Param("contractNumber")String contractNumber,@Param("year")Integer year);


    List<ContractCenterPlanStatistics>  contractCenterPlanStatistics(@Param("year")Integer year, @Param("contractNo")String contractNo, @Param("centerCode")String centerCode, @Param("costTypeNumber")String costTypeNumber);
}

