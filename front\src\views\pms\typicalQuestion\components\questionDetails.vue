<template>
  <div
    v-loading="loading"
    class="details"
    :class="{'details-none':!formId}"
  >
    <BasicScrollbar v-if="formId">
      <BasicCard
        v-if="detailsData.questionType==='questionType_1'"
        title="问题发现及提出"
        :grid-content-props="baseInfoProps"
        :isBorder="false"
      />
      <BasicCard
        v-if="detailsData.questionType==='questionType_1'"
        title="问题处理及解决"
        :grid-content-props="baseDevelopmentProps"
        :isBorder="false"
      />
      <BasicCard
        v-if="detailsData.questionType==='questionType_1'"
        title="问题处理确认及转化"
        :grid-content-props="baseDevelopmentPropsOrder"
        :isBorder="false"
      />
      <BasicCard
        v-if="detailsData.questionType==='questionType_2'"
        title="评审问题信息"
        :grid-content-props="baseReviewProps"
        :isBorder="false"
      />
    </BasicScrollbar>
    <Empty v-else />
  </div>
</template>
<script lang="ts" setup>
import { BasicScrollbar, BasicCard, Empty } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, Ref, watch,
} from 'vue';
import { setBasicInfo } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/utils';
import dayjs from 'dayjs';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string
}>(), {
  formId: '',
});
const detailsData:Ref<Record<any, any>> = ref({});
const loading:Ref<boolean> = ref(false);
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '问题描述',
      field: 'content',
      gridColumn: '1/3',
    },
    {
      label: '过程环节',
      field: 'processLinkName',
    },
    {
      label: '过程分类',
      field: 'processClassifiName',
    },
    {
      label: '阶段',
      field: 'stageName',
    },

    {
      label: '问题等级分类',
      field: 'problemLevelName',
    },

    {
      label: '问题现象分类',
      field: 'problemPhenomenonOneName',
      formatter: (val, record) => {
        let valueList = [];
        if (record.problemPhenomenonOneName) {
          valueList.push(record.problemPhenomenonOneName);
        }
        if (record.problemPhenomenonTwoName) {
          valueList.push(record.problemPhenomenonTwoName);
        }
        if (record.problemPhenomenonThName) {
          valueList.push(record.problemPhenomenonThName);
        }
        return valueList.join('-');
      },
      gridColumn: '1/3',
    },
  ]),
  column: 2,
  dataSource: detailsData,
});
const baseDevelopmentProps = reactive({
  list: setBasicInfo([

    {
      label: '问题原因分类',
      field: 'reasionOneName',
      gridColumn: '1/3',
      formatter: (val, record) => {
        let valueList = [];
        if (record.reasionOneName) {
          valueList.push(record.reasionOneName);
        }
        if (record.reasionTwoName) {
          valueList.push(record.reasionTwoName);
        }
        if (record.reasionThreeName) {
          valueList.push(record.reasionThreeName);
        }
        return valueList.join('-');
      },
    },
    {
      label: '原因分析描述',
      field: 'reasionRemark',
      gridColumn: '1/3',
    },

    {
      label: '纠正分类',
      field: 'correctClassifiName',
    },
    {
      label: '关联ECN编号',
      field: 'ecnNumber',
    },

    {
      label: '纠正描述',
      field: 'correctRemark',
      gridColumn: '1/3',
    },
  ]),
  column: 2,
  dataSource: detailsData,
});
const baseDevelopmentPropsOrder = reactive({
  list: setBasicInfo([

    {
      label: '问题升级类型',
      field: 'questionUpType',
      gridColumn: '1/3',
    },
    {
      label: '举一反三',
      field: 'oneCaseToAnother',
      gridColumn: '1/3',
    },
    {
      label: '纠正措施描述',
      field: 'correAcDescription',
      gridColumn: '1/3',
    },
  ]),
  column: 2,
  dataSource: detailsData,
});
const baseReviewProps = reactive({
  list: setBasicInfo([
    {
      label: '意见类别',
      field: 'opinionCategoriesName',
    },
    {
      label: '意见分类',
      field: 'opClassificationName',
    },
    {
      label: '评审要点',
      field: 'reviewPoints',
    },
    {
      label: '采纳情况',
      field: 'adoptionSituationName',
    },
    {
      label: '是否典型问题',
      field: 'isTypicalProblems',
      formatter: (val) => (val ? '是' : '否'),
    },
    {
      label: '是否技术问题',
      field: 'isTechnicalIssues',
      formatter: (val) => (val ? '是' : '否'),
    },
    {
      label: '问题描述',
      field: 'content',
      gridColumn: '1/3',
    },
    {
      label: '整改情况描述',
      field: 'overallDescription',
      gridColumn: '1/3',
    },
  ]),
  column: 2,
  dataSource: detailsData,
});
watch(() => props.formId, async () => {
  await getDetailsData();
});
onMounted(async () => {
  if (props.formId) {
    await getDetailsData();
  }
});
async function getDetailsData() {
  loading.value = true;
  detailsData.value = await new Api('/pms').fetch('', `question-management/detail/${props.formId}`, 'GET');
  loading.value = false;
}

</script>
<style lang="less" scoped>
.details{
  height:100%
}
.details-none{
  display: flex;
  align-items: center;
  :deep(.ant-empty){
    width:100%
  }
}
</style>