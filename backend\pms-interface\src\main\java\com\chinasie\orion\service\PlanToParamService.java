package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.CopyEntityDTO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolInsDTO;
import com.chinasie.orion.domain.entity.PlanToParam;
import com.chinasie.orion.domain.dto.PlanToParamDTO;
import com.chinasie.orion.domain.vo.PlanToParamVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.vo.RelationParamInsVO;
import com.chinasie.orion.domain.vo.RelationParamVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PlanToParam 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
public interface PlanToParamService extends OrionBaseService<PlanToParam> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    PlanToParamVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param planToParamDTO
     */
    PlanToParamVO create(PlanToParamDTO planToParamDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param planToParamDTO
     */
    Boolean edit(PlanToParamDTO planToParamDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PlanToParamVO> pages(Page<PlanToParamDTO> pageRequest) throws Exception;

    /**
     * 关联参数
     *
     * @param planId
     * @param imIdList
     * @return
     */
    Boolean relationToParam(String planId, List<String> imIdList);

    /**
     * 异常参数关联
     *
     * @param planId
     * @param paramIdList
     * @return
     */
    Boolean removeRelation(String planId, List<String> paramIdList);

    /**
     *  获取计划关联的参数列表
     * @param planId
     * @return
     */
    List<RelationParamVO> relationList(String planId) throws Exception;

    /**
     *  设置模板
     * @param planId
     * @param paramId
     * @param modelId
     * @return
     */
    Boolean settingModel(String planId, String paramId, String modelId);

    /**
     *  填值
     * @param parameterPoolInsDTO
     * @return
     */
    Boolean addValue(ParameterPoolInsDTO parameterPoolInsDTO) throws Exception;

    /**
     *  将来源数据复制到目标下
     * @param sourceId
     * @param targetId
     * @return
     */
    Boolean copySourceIdToTargetId(String sourceId, String targetId, CopyEntityDTO copyEntityDTO) throws Exception;

    /**
     *  通过
     * @param fromIdList
     * @return
     */
    List<RelationParamInsVO> relationListByToIdList(List<String> fromIdList) throws Exception;
}
