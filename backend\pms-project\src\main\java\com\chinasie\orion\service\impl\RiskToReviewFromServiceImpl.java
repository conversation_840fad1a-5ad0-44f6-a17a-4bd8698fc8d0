package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.RiskToReviewFrom;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.RiskToReviewFromMapper;
import com.chinasie.orion.service.RiskToReviewFromService;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * RiskToReviewFrom 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 11:15:22
 */
@Service
@Slf4j
public class RiskToReviewFromServiceImpl extends OrionBaseServiceImpl<RiskToReviewFromMapper, RiskToReviewFrom> implements RiskToReviewFromService {

}

