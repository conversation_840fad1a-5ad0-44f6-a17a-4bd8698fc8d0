package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.BasicUserCertificate;
import com.chinasie.orion.domain.dto.BasicUserCertificateDTO;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * BasicUserCertificate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 22:07:29
 */
public interface BasicUserCertificateService extends OrionBaseService<BasicUserCertificate> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    BasicUserCertificateVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param basicUserCertificateDTO
     */
    String create(BasicUserCertificateDTO basicUserCertificateDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param basicUserCertificateDTO
     */
    Boolean edit(BasicUserCertificateDTO basicUserCertificateDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BasicUserCertificateVO> pages(Page<BasicUserCertificateDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BasicUserCertificateVO> vos) throws Exception;

    List<BasicUserCertificate> listByUserCodeList(List<String> userCodeList);

    /**
     *  通过用户编号获取证书列表
     * @param userCode
     * @return
     */
    List<BasicUserCertificateVO> userCertificateList(String userCode) throws Exception;
}
