<template>
  <BasicModal
    :width="'1400px'"
    title="计划编制"
    :bodyStyle="{ height: '425px', overflowY: 'hidden' }"
    @register="modalRegister"
    @ok="handleOk"
    @cancel="() => handleClosed"
  >
    <div
      class="add-body"
      style="height: 425px;overflow: hidden"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      >
        <template #toolbarLeft>
          <div class="source-table-slots">
            <BasicButton
              type="primary"
              icon="add"
              @click="() => addNewRow('plan')"
            >
              添加一行
            </BasicButton>

            <BasicButton
              icon="delete"
              :disabled="!selectedRowKeys.length"
              @click="deleteBatchNode"
            >
              删除
            </BasicButton>
          </div>
        </template>
        <template #name="{ record }">
          <div
            class="flex flex-ac"
            :style="parentData?.[0]?.id!==record?.id?{width:'calc(100% - 10px)',marginLeft:'10px'}:{}"
          >
            <!--计划图标-->
            <Icon
              v-if="record['nodeType']==='plan'"
              icon="orion-icon-carryout"
              class="primary-color"
              size="16"
            />
            <!--计划图标-->
            <Icon
              v-if="record['nodeType']==='milestone'"
              color="#FFB118"
              size="16"
              icon="orion-icon-flag"
            />
            <!--前后置计划图标-->
            <Icon
              v-if="record['isPrePost']"
              color="#D50072"
              icon="fa-sort-amount-asc"
            />
            <a-input
              v-model:value="record.name"
              :disabled="parentData?.[0]?.id===record?.id"
              placeholder="请输入计划名称"
              class="table-input ml10"
              @change="(value) => onChangeValue(value, record)"
            />
          </div>
        </template>
        <template #nodeType="{ record }">
          <a-select
            ref="select"
            v-model:value="record.nodeType"
            :disabled="parentData?.[0]?.id===record?.id"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label:'计划',
                value:'plan'
              },
              {
                label:'里程碑',
                value:'milestone'
              }
            ]"
          />
        </template>
        <!--责任部门-->
        <template #rspDept="{ record }">
          <a-select
            v-model:value="record.rspDeptId"
            style="width: 100%"
            :field-names="{ label: 'name', value: 'id' }"
            :options="rspDeptOptions"
            :filter-option="filterRspDeptOptions"
            show-search
          />
        </template>
        <template #durationDays="{ record }">
          <a-input-number
            v-model:value="record.durationDays"
            min="0"
            placeholder="请输入工期"
            class="table-input ml10"
          />
        </template>
        <template #delayDays="{ record }">
          <a-input-number
            v-model:value="record.delayDays"
            min="0"
            placeholder="请输入天数"
            class="table-input ml10"
          />
        </template>
        <template #processFlag="{ record }">
          <a-select
            ref="select"
            v-model:value="record.processFlag"
            :disabled="parentData?.[0]?.id===record?.id"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label:'是',
                value:true
              },
              {
                label:'否',
                value:false
              }
            ]"
          />
        </template>
        <!--计划描述-->
        <template #remark="{ record }">
          <a-input
            v-model:value="record.remark"
            :disabled="parentData?.[0]?.id===record?.id"
            :placeholder="parentData?.[0]?.id===record?.id?'':'请输入计划描述'"
            class="table-input"
            @change="(value) => onChangeValue(value, record)"
          />
        </template>
      </OrionTable>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, Ref, ref, watchEffect,
} from 'vue';
import {
  DatePicker, Input, message, Select, Tag, InputNumber,
} from 'ant-design-vue';
import {
  BasicButton, BasicModal, Icon, OrionTable, useModal, useModalInner,
} from 'lyra-component-vue3';
import { cloneDeep, throttle } from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';
import { getProjectUserList, getUserInfoByCode } from '/@/views/pms/projectLaborer/projectLab/api';
import {
  postProjectSchemeMilestoneNode,
} from '../api';
export default defineComponent({
  name: 'AddModal',
  components: {
    OrionTable,
    BasicButton,
    AInput: Input,
    ASelect: Select,
    AInputNumber: InputNumber,
    BasicModal,
    Icon,
  },
  props: {
    parentIds: {
      type: Array,
      default: () => [],
    },
    parentData: {
      type: Object,
      default: () => {
      },
    },
    projectData: {
      type: Object,
      default: () => {
      },
    },
    from: {
      type: String,
      default: () => '',
    },
  },
  emits: ['handleColse'],
  setup(props, { emit }) {
    const projectData = ref<any>({});
    const parentIds = ref([]);
    const parentData = ref<any>({});
    const from = ref<string>('');
    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const projectId = inject('projectId') as string;

    const [registerTemplate, { openModal: setSelectTemplate }] = useModal();

    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        getCheckboxProps: (record) => ({
          disabled: parentData.value?.[0]?.id === record?.id,
        }),
        selectedRowKeys,
        onChange: (keys = []) => {
          selectedRowKeys.value = keys;
        },
      },
      showSmallSearch: false,
      showIndexColumn: false,
      pagination: false,
      columns: [
        {
          title: '序号',
          dataIndex: 'columnIndex',
          width: 60,
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '计划类型',
          dataIndex: 'nodeType',
          align: 'left',
          width: 110,
          slots: { customRender: 'nodeType' },
        },
        {
          title: '责任部门',
          align: 'left',
          dataIndex: 'rspDept',
          slots: { customRender: 'rspDept' },
          width: 140,
        },
        {
          title: '工期（天）',
          align: 'left',
          dataIndex: 'durationDays',
          slots: { customRender: 'durationDays' },
          width: 140,
        },

        {
          title: '项目启动后n天开始',
          align: 'left',
          dataIndex: 'delayDays',
          slots: { customRender: 'delayDays' },
          width: 140,
        },
        {
          title: '是否关联流程',
          align: 'left',
          dataIndex: 'processFlag',
          slots: { customRender: 'processFlag' },
          width: 140,
        },
        {
          title: '计划描述',
          align: 'left',
          width: 140,
          dataIndex: 'remark',
          slots: { customRender: 'remark' },
        },
      ],
    });
    const rspDeptOptions = ref([]);
    const isTimeOut = ref(false);
    const showTips = ref(false);
    const deptList = ref([]);
    const userList = reactive({});
    const templateId = ref('');

    const parentId = ref('');
    const filterRspDeptOptions = (input: string, option: any) => option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    const [
      modalRegister,
      {
        closeModal,
        changeOkLoading,
      },
    ] = useModalInner(
      (rowData) => {
        tableSource.value = [];

        projectData.value = rowData.projectData;
        parentIds.value = rowData.parentIds;
        parentData.value = rowData.parentData;
        from.value = rowData.from ? rowData.from : '';
        showTips.value = false;
        isTimeOut.value = false;
        templateId.value = rowData.templateId || '';
        parentId.value = rowData.parentId || '';
        tableSource.value = cloneDeep(rowData.parentData.map((item) => ({
          ...item,
          columnIndex: '1',
        })));
        selectedRowKeys.value = [];
        rspDeptOptions.value = [];
        reqProjectUserList();
        getOrganization();
        changeOkLoading(false);
      },
    );

    function getOrganization() {
      new Api('/pmi')
        .fetch({}, 'organization/org-type', 'GET')
        .then((res) => {
          rspDeptOptions.value = res;
        });
    }
    getOrganization();
    // 获取项目成员列表
    const projectUserList: Ref = ref([]);
    async function reqProjectUserList() {
      const result = await getProjectUserList(projectId);
      projectUserList.value = result.map((item) => ({
        ...item,
        label: item.name,
        value: item.id,
      }));
    }

    // 添加一行
    const addNewRow = (nodeType) => {
      const list = cloneDeep(tableSource.value);
      list.push({
        id: new Date().getTime()
          .toString(),
        name: '',
        nodeType,
        rspDeptId: '',
        rspSectionId: '',
        delayDays: '',
        // rspUser: '',
        rspUserCode: '',
        processFlag: '',
        oldCode: '',
        // beginTime: '',
        // endTime: '',
        remark: '',
        projectId,
      });
      tableSource.value = list;
    };

    // 更新表格数据序号
    function updateColumn() {
      tableSource.value = tableSource.value.map((item, index) => {
        if (parentData.value?.length > 0) {
          if (parentData.value?.[0].id === item?.id) {
            return {
              ...item,
              columnIndex: '1',
            };
          }
          return {
            ...item,
            columnIndex: `1.${index}`,
          };
        }
        return {
          ...item,
          columnIndex: index + 1,
        };
      });
    }

    const deleteBatchNode = () => {
      const list = cloneDeep(tableSource.value);
      selectedRowKeys.value.forEach((item) => {
        const index = list.findIndex((val) => val.id === item);
        if (index !== -1) {
          list.splice(index, 1);
        }
      });
      tableSource.value = list;
      selectedRowKeys.value = [];
    };
    const getDayTime = (time = ''): number => {
      const date = new Date(time);
      return dayjs(dayjs(date)
        .format('YYYY-MM-DD'))
        .valueOf();
    };

    // 格式化部门
    const formatDept = (value: any[]): any[] =>
      value.map((item) => ({
        ...item,
        value: item.id,
        label: item.name,
        children: formatDept(item.children),
      }));

    const onChangeValue = throttle((e, record, keyName) => {
      let value = e?.target?.value
        ? e?.target?.value
        : typeof e === 'string'
          ? e
          : '';
      if (keyName === 'name' && value?.length > 100) {
        value = value.slice(0, 100);
      }

      tableSource.value.forEach((item) => {
        if (item.id === record.id) {
          if (keyName === 'rspDeptId') {
            item.rspUser = '';
          }
          item[keyName] = value;
        }
      });
      const list = [];

      showTips.value = list.includes(true);
    }, 500);

    const checkHasValue = (item) =>
      item.name
        && item.nodeType;

    async function handleOk() {
      if (tableSource.value.filter((item) => item?.id !== parentData.value?.[0]?.id).length === 0) return message.info('请添加内容');
      let isContinue = true;
      tableSource.value.forEach((item) => {
        if (!checkHasValue(item)) {
          isContinue = false;
        }
      });
      if (!isContinue) {
        message.error('带*号的为必填项，请完善');
      } else {
        const pid = parentIds.value?.length ? parentIds.value[0] : 0;
        const data = tableSource.value.filter((item) => item?.id !== parentData.value?.[0]?.id)
          .map((item) => {
            delete item.columnIndex;
            return {
              ...item,
              parentId: parentId.value,
              templateId: templateId.value,
            };
          });
        changeOkLoading(true);
        try {
          await new Api(
            '/pms/projectSchemeMilestoneNode/createBatch',
          ).fetch(data, '', 'POST');
          message.success('创建成功');
          handleClosed();
        } finally {
          changeOkLoading(false);
        }
      }
    }

    const handleClosed = () => {
      tableSource.value = [];

      closeModal();
      emit('handleColse');
    };

    const confirm = (data) => {
      if (data) {
        const list = cloneDeep(tableSource.value);
        for (let i in data) {
          list.push({
            name: data[i].nodeName,
            nodeType: 'plan',
            rspDeptId: '',
            rspSectionId: '',
            rspUser: '',
            rspUserCode: '',
            oldCode: '',
            remark: data[i].remark,
            projectId,
          });
        }
        tableSource.value = list;
      }
    };

    // 更新列表序号
    watchEffect(() => {
      updateColumn();
    });

    function openTemplateModal(value: boolean) {
      if (value) {
        setSelectTemplate(true, {});
      }
    }

    // 获取禁用时间范围
    const getDisableDate = (current, value) =>
      new Date(value).getTime() >= new Date(current).getTime();

    // 责任人下拉选择器筛选
    function filterUserOption(input: string, option: any) {
      return option.label.toLowerCase()
        .indexOf(input.toLowerCase()) !== -1;
    }

    // 员工编号输入框回车回调
    async function pressEnter({
      rspUserCode,
      oldCode,
    }, index) {
      if (oldCode === rspUserCode || !rspUserCode) return;
      const result = await getUserInfoByCode(projectId, rspUserCode);
      updateRecord(result || { code: rspUserCode }, index);
    }

    // 更新当前行数据
    function updateRecord(option, index) {
      tableSource.value.splice(index, 1, {
        ...tableSource.value[index],
        rspUser: option.id || '',
        rspDeptId: option.deptId || '',
        rspDeptName: option.deptName || '',
        rspSectionId: option.sectionId || '',
        rspSectionName: option.sectionName || '',
        rspUserCode: option.code || '',
        oldCode: option.code || '',
      });
    }

    return {
      tableRef,
      tableOptions,
      tableSource,
      addNewRow,
      deleteBatchNode,
      onChangeValue,
      selectedRowKeys,
      rspDeptOptions,
      isTimeOut,
      handleOk,
      showTips,
      deptList,
      userList,
      modalRegister,
      handleClosed,
      getDisableDate,
      registerTemplate,
      setSelectTemplate,
      openTemplateModal,
      confirm,
      // eslint-disable-next-line vue/no-dupe-keys
      parentData,
      pressEnter,
      filterUserOption,
      projectUserList,
      updateRecord,
      filterRspDeptOptions,
    };
  },
});
</script>
<style lang="less" scoped>
.add-body {
  :deep(.surely-table-center-container) {
    .surely-table-header-cell:nth-of-type(3),
    .surely-table-header-cell:nth-of-type(4)

   {
      .surely-table-header-cell-title-inner .header-column-wrap .flex-f1 {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
  }
}

.primary-color {
  color: ~`getPrefixVar('primary-color')`;
}
</style>
