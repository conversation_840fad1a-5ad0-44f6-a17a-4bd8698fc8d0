package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/11:21
 * @description:
 */
@Data
@TableName(value = "pms_risk_plan" )
@ApiModel(value = "RiskPlan对象", description = "风险预案")
public class RiskPlan extends ObjectEntity {
    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    @TableField(value = "risk_probability")
    private String riskProbability;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    @TableField(value = "risk_influence")
    private String riskInfluence;


    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    @TableField(value = "risk_type")
    private String riskType;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    @TableField(value = "solutions")
    private String solutions;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;


}
