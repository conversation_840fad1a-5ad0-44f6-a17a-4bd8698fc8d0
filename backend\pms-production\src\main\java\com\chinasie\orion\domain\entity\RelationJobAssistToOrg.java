package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * RelationJobAssistToOrg Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@TableName(value = "pmsx_relation_job_assist_to_org")
@ApiModel(value = "RelationJobAssistToOrgEntity对象", description = "作业专业协助关系表")
@Data

public class RelationJobAssistToOrg extends  ObjectEntity  implements Serializable{

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "job_number")
    private String jobNumber;

    /**
     * 大修组织ID：基本是执行班组
     */
    @ApiModelProperty(value = "大修组织ID：基本是执行班组")
    @TableField(value = "major_repair_org_id")
    private String majorRepairOrgId;

}
