package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.QuestionToRisk;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/10:06
 * @description:
 */
public interface QuestionToRiskService extends OrionBaseService<QuestionToRisk> {
    QuestionToRisk saveParam(String id, String questionId) throws Exception;

    List<String> getListByQuestionId(String toId) throws  Exception;


    boolean removeRelationToQuestion(String fromId, List<String> toIdList) throws Exception;

    List<String> getListByRiskId(String riskId) throws Exception;
}
