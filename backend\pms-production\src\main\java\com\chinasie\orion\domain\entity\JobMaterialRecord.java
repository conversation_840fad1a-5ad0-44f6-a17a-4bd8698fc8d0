package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobMaterialRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:37
 */
@TableName(value = "pmsx_job_material_record")
@ApiModel(value = "JobMaterialRecordEntity对象", description = "作业物资记录表")
@Data

public class JobMaterialRecord extends  ObjectEntity  implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 物资code
     */
    @ApiModelProperty(value = "物资code")
    @TableField(value = "materia_code")
    private String materiaCode;

    /**
     * 物资管理ID
     */
    @ApiModelProperty(value = "物资管理ID")
    @TableField(value = "materia_manage_id")
    private String materiaManageId;

}
