package com.chinasie.orion.management.constant;

/**
 * 客户级别字典
 */

public enum CustomerLevelEnum {

    NORMAL("normal","普通用户"),
    IMPORTANCE("importance","重要用户");

    private String name;
    private String desc;

    CustomerLevelEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (CustomerLevelEnum lt : CustomerLevelEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}