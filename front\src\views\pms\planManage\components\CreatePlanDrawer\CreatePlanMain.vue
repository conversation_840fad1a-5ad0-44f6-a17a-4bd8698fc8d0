<template>
  <div class="form-wrap">
    <BasicForm
      @register="formRegister"
    />
  </div>
</template>

<script setup lang="ts">
import { BasicForm, useForm, FormActionType } from 'lyra-component-vue3';
import { computed, onMounted, reactive } from 'vue';
import {
  getBusinessOrganizationList, getDeptDataFromOrgId, getPlanDict, getUsersFromDeptId,
} from '/@/views/pms/api';

const props = defineProps<{
  type: 'edit' | 'add',
  record: any,
  onFormInit?: (formMethods: FormActionType)=>void
}>();

const state = reactive({
  // 责任单位
  businessOrganizationTreeData: [],
  // 部门信息
  deptData: [],
  // 责任人列表
  resPersonList: [],
});

const [formRegister, formMethods] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      colProps: {
        span: 11,
      },
      label: '名称:',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
      },
    },
    {
      field: 'planType',
      component: 'ApiSelect',
      label: '计划类型:',
      colProps: {
        span: 11,
        offset: 1,
      },
      rules: [
        {
          required: true,
          trigger: 'blur',
        },
      ],
      componentProps: {
        api: () => getPlanDict('scheme_type'),
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'orgId',
      component: 'TreeSelect',
      label: '责任单位:',
      colProps: {
        span: 11,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        treeData: computed(() => state.businessOrganizationTreeData),
        fieldNames: {
          children: 'children',
          key: 'id',
          value: 'id',
          label: 'name',
        },
        onChange(e:string) {
          formMethods.setFieldsValue({
            deptId: undefined,
            resPerson: undefined,
          });
          state.deptData = [];
          state.resPersonList = [];
          if (e) {
            setDeptDataFormOrgId([e]);
          }
        },
      },
    },
    {
      field: 'deptId',
      component: 'Select',
      label: '责任部门:',
      colProps: {
        span: 11,
        offset: 1,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        showSearch: true,
        optionFilterProp: 'name',
        options: computed(() => state.deptData),
        fieldNames: {
          key: 'id',
          value: 'id',
          label: 'name',
        },
        onChange(id:string) {
          formMethods.setFieldsValue({
            resPerson: undefined,
          });
          state.resPersonList = [];
          if (id) {
            setResPersonList([id]);
          }
        },
      },
    },
    {
      field: 'resPerson',
      component: 'Select',
      label: '责任人:',
      colProps: {
        span: 11,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        options: computed(() => state.resPersonList),
        fieldNames: {
          key: 'id',
          value: 'id',
          label: 'name',
        },
      },
    },
    {
      field: 'planLevel',
      component: 'ApiSelect',
      label: '计划级别:',
      colProps: {
        span: 11,
        offset: 1,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
        },
      ],
      componentProps: {
        api: () => getPlanDict('scheme_level'),
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'startTime',
      component: 'DatePicker',
      label: '开始时间:',
      colProps: {
        span: 11,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        style: { width: '100%' },
      },
    },
    {
      field: 'endTime',
      component: 'DatePicker',
      label: '结束时间:',
      colProps: {
        span: 11,
        offset: 1,
      },
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        style: { width: '100%' },
      },
    },
  ],
});

onMounted(() => {
  init();
  if (props?.record?.orgId) {
    setDeptDataFormOrgId([props.record.orgId]);
  }
  if (props?.record?.deptId) {
    setResPersonList([props.record.deptId]);
  }
});

async function init() {
  if (props.type === 'edit' && props.record) {
    setEditValues(props.record);
  }
  props.onFormInit && props.onFormInit(formMethods);
  setBusinessOrganizationTreeData();
}

function setEditValues(record) {
  formMethods.setFieldsValue(record);
}

// 设置责任单位数据
async function setBusinessOrganizationTreeData() {
  state.businessOrganizationTreeData = await getBusinessOrganizationList();
}

// 设置部门可选数据，根据组织ID
async function setDeptDataFormOrgId(orgIds: string[]) {
  state.deptData = await getDeptDataFromOrgId(orgIds);
}

// 设置责任人可选数据,根据部门ID
async function setResPersonList(deptIds: string[]) {
  state.resPersonList = await getUsersFromDeptId(deptIds);
}

</script>

<style scoped lang="less">
.form-wrap {
  :deep(.ant-form-item) {
    display: block;
  }
}
</style>
