package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * AmpereringDictInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-30 00:02:31
 */
@ApiModel(value = "AmpereringDictInfoDTO对象", description = "业务字典表（明细表）")
@Data
@ExcelIgnoreUnannotated
public class AmpereringDictInfoDTO extends  ObjectDTO   implements Serializable{

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @ExcelProperty(value = "类型id ", index = 0)
    private String typeId;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @ExcelProperty(value = "类型id ", index = 1)
    private String parentId;

    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
    @ExcelProperty(value = "字典编码 ", index = 2)
    private String code;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    @ExcelProperty(value = "字典名称 ", index = 3)
    private String name;

    /**
     * 是否启用禁用【启用1，禁用0表示】
     */
    @ApiModelProperty(value = "是否启用禁用【启用1，禁用0表示】")
    @ExcelProperty(value = "是否启用禁用【启用1，禁用0表示】 ", index = 4)
    private Boolean isEnabled;

    /**
     * 排序,默认0，升序排序
     */
    @ApiModelProperty(value = "排序,默认0，升序排序")
    @ExcelProperty(value = "排序,默认0，升序排序 ", index = 5)
    private Integer sort;




}
