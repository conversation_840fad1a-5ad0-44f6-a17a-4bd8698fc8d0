package com.chinasie.orion.domain.vo;

import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PersonManageExecuteTreeVO {
    @ApiModelProperty(value = "必填未填数")
    @StatisticField(value = "requiredCount", type = StatisticType.SUM)
    private Integer requiredCount;
    @StatisticField("noActIn")
    @ApiModelProperty(value = "入场时间未报备")
    Integer noActIn;

    @StatisticField("actInCount")
    @ApiModelProperty(value = "实际入场人数")
    Integer actInCount;

    @StatisticField("ApiModelProperty")
    @ApiModelProperty(value = "实际离场人数")
    Integer actOutCount;

    @StatisticField("actOutNotReportCount")
    @ApiModelProperty(value = "实际离场未报备人数")
    Integer actOutNotReportCount;

    @ApiModelProperty(value = "实际进场日期")
    Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    Date actOutDate;

    @ApiModelProperty(value = "离厂原因")
    String leaveReason;

    @ApiModelProperty(value = "离厂原因")
    String leaveReasonName;

    @ApiModelProperty(value = "完成离场工作交接")
    Boolean isFinishOutHandover;

    @ApiModelProperty(value = "再次入场")
    Boolean isAgainIn;

    @ApiModelProperty("性别")
    String sex;

    @ApiModelProperty(value = "是否常驻")
    Boolean isBasePermanent;

    @ApiModelProperty(value = "驻地名称")
    String baseName;

    @ApiModelProperty(value = "驻地code：基地编号")
    String baseCode;

    @ApiModelProperty(value = "人员名称")
    String userName;

    @ApiModelProperty(value = "人员编号")
    String code;

    @ApiModelProperty(value = "状态")
    Integer status;

    @ApiModelProperty(value = "基础用户ID")
    String basicUserId;

    @ApiModelProperty(value = "人员ID")
    String personId;

    public PersonManageExecuteTreeVO() {
        this.noActIn = 0;
        this.actInCount = 0;
        this.actOutCount = 0;
        this.actOutNotReportCount = 0;
    }
}
