package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateLaborFee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * ProjectApprovalEstimate VO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateVO对象", description = "概算")
@Data
public class ProjectApprovalEstimateVO implements Serializable{

    @ApiModelProperty(value = "材料费")
    private List<ProjectApprovalEstimateMaterialVO> projectApprovalEstimateMaterialVOList;

    @ApiModelProperty(value = "材料费用概算")
    private BigDecimal materialFee;

    @ApiModelProperty(value = "工资及劳务费编制")
    private List<ProjectApprovalEstimateLaborFeeVO> projectApprovalEstimateLaborFeeVOList;

    /**
     * 人天数
     */
    @ApiModelProperty(value = "人天数")
    private BigDecimal peopleNum;

    /**
     * 工资及劳务费
     */
    @ApiModelProperty(value = "工资及劳务费")
    private BigDecimal laborFee;

    List<ProjectApprovalEstimateInterOutTrialFeeVO> projectApprovalEstimateInterOutTrialFeeVOList;

    private BigDecimal trialFee;

}
