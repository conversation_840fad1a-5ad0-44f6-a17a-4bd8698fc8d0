package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@ApiModel(value = "GoodsServiceUnitCodeVO对象", description = "物资服务计量单位")
@Data
public class GoodsServiceUnitCodeVO  implements Serializable {

    /**
     * 物资服务计量单位编码序号
     */
    @ApiModelProperty(value = "物资服务计量单位编码序号")
    private Integer unitCodeSort;

    /**
     * 物资服务计量编码
     */
    @ApiModelProperty(value = "物资服务计量单位编码")
    private String unitCode;

    /**
     * 物资服务计量编码名字
     */
    @ApiModelProperty(value = "物资服务计量单位编码名字")
    private String unitCodeName;

}
