package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "ContractDistributionVO对象", description = "经营看板-里程碑达成情况-合同分布")
@Data
public class ContractDistributionVO implements Serializable {


    /**
     * 合同总数
     */
    @ApiModelProperty(value = "合同总数")
    private Integer contractTotal;

    /**
     * 在履合同数
     */
    @ApiModelProperty(value = "在履合同数")
    private Integer performanceContract;

    /**
     * 核能合同数
     */
    @ApiModelProperty(value = "核能合同数")
    private Integer nuclearEnergyContractNum;

    /**
     * 非核能合同数
     */
    @ApiModelProperty(value = "非核能合同数")
    private Integer otherContractNum;


    /**
     * 在履合同中总价合同总数
     */
    @ApiModelProperty(value = "在履合同中总价合同总数")
    private Integer totalPriceContractNum;

    /**
     * 在履合同中框架合同总数
     */
    @ApiModelProperty(value = "在履合同中框架合同总数")
    private Integer frameContractNum;

    /**
     * 在履合同中复合合同总数
     */
    @ApiModelProperty(value = "在履合同中复合合同总数")
    private Integer compositeContractNum;
}

