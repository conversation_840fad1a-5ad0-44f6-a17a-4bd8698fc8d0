import { ref, Ref } from 'vue';

export function useDetailsCode(routeName: string) {
  const powerCodePrefix: Ref<string> = ref('');
  switch (routeName) {
    case 'PMSMajorRepairsWorkDetails':
      powerCodePrefix.value = 'PMS_DXZYXQ';
      break;
    case 'PMSDailyWorkDetails':
      powerCodePrefix.value = 'PMS_ZYXQ';
      break;
  }
  return {
    powerCodePrefix,
  };
}

export function useAuthDetailsCode(routeName: string) {
  const powerCodePrefix: Ref<string> = ref('');
  switch (routeName) {
    case 'PMSMajorRepairsAuthDetails':
      powerCodePrefix.value = 'PMS_DXZYSQGLXQ';
      break;
    case 'PMSDailyWorkAuthDetails':
      powerCodePrefix.value = 'PMS_ZYSQGLXQ';
      break;
  }
  return {
    powerCodePrefix,
  };
}

export function useMaterialDetailsCode(routeName: string) {
  const powerCodePrefix: Ref<string> = ref('');
  switch (routeName) {
    case 'PMSMajorRepairsMaterialDetails':
      powerCodePrefix.value = 'PMS_DXZYWZGLXQ';
      break;
    case 'PMSDailyWorkMaterialDetails':
      powerCodePrefix.value = 'PMS_ZYWZGLXQ';
      break;
  }
  return {
    powerCodePrefix,
  };
}

export function useRiskDetailsCode(routeName: string) {
  const powerCodePrefix: Ref<string> = ref('');
  switch (routeName) {
    case 'PMSMajorRepairsRiskDetails':
      powerCodePrefix.value = 'PMS_DXZYFXCSXQ';
      break;
    case 'PMSDailyWorkRiskDetails':
      powerCodePrefix.value = 'PMS_ZYFXCSXQ';
      break;
  }
  return {
    powerCodePrefix,
  };
}
