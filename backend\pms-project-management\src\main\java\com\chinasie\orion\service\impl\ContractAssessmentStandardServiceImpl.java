package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.ContractAssessmentStandard;
import com.chinasie.orion.domain.dto.ContractAssessmentStandardDTO;
import com.chinasie.orion.domain.vo.ContractAssessmentStandardVO;



import com.chinasie.orion.service.ContractAssessmentStandardService;
import com.chinasie.orion.repository.ContractAssessmentStandardMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ContractAssessmentStandard 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:29:14
 */
@Service
@Slf4j
public class ContractAssessmentStandardServiceImpl extends  OrionBaseServiceImpl<ContractAssessmentStandardMapper, ContractAssessmentStandard>   implements ContractAssessmentStandardService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractAssessmentStandardVO detail(String id,String pageCode) throws Exception {
        ContractAssessmentStandard contractAssessmentStandard =this.getById(id);
        ContractAssessmentStandardVO result = BeanCopyUtils.convertTo(contractAssessmentStandard,ContractAssessmentStandardVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractAssessmentStandardDTO
     */
    @Override
    public  String create(ContractAssessmentStandardDTO contractAssessmentStandardDTO) throws Exception {
        ContractAssessmentStandard contractAssessmentStandard =BeanCopyUtils.convertTo(contractAssessmentStandardDTO,ContractAssessmentStandard::new);
        this.save(contractAssessmentStandard);

        String rsp=contractAssessmentStandard.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractAssessmentStandardDTO
     */
    @Override
    public Boolean edit(ContractAssessmentStandardDTO contractAssessmentStandardDTO) throws Exception {
        ContractAssessmentStandard contractAssessmentStandard =BeanCopyUtils.convertTo(contractAssessmentStandardDTO,ContractAssessmentStandard::new);

        this.updateById(contractAssessmentStandard);

        String rsp=contractAssessmentStandard.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractAssessmentStandardVO> pages( Page<ContractAssessmentStandardDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractAssessmentStandard> condition = new LambdaQueryWrapperX<>( ContractAssessmentStandard. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractAssessmentStandard::getCreateTime);


        Page<ContractAssessmentStandard> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractAssessmentStandard::new));

        PageResult<ContractAssessmentStandard> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractAssessmentStandardVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractAssessmentStandardVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractAssessmentStandardVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "审核标准表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractAssessmentStandardDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ContractAssessmentStandardExcelListener excelReadListener = new ContractAssessmentStandardExcelListener();
        EasyExcel.read(inputStream,ContractAssessmentStandardDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractAssessmentStandardDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("审核标准表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractAssessmentStandard> contractAssessmentStandardes =BeanCopyUtils.convertListTo(dtoS,ContractAssessmentStandard::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractAssessmentStandard-import::id", importId, contractAssessmentStandardes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractAssessmentStandard> contractAssessmentStandardes = (List<ContractAssessmentStandard>) orionJ2CacheService.get("pmsx::ContractAssessmentStandard-import::id", importId);
        log.info("审核标准表导入的入库数据={}", JSONUtil.toJsonStr(contractAssessmentStandardes));

        this.saveBatch(contractAssessmentStandardes);
        orionJ2CacheService.delete("pmsx::ContractAssessmentStandard-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractAssessmentStandard-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractAssessmentStandard> condition = new LambdaQueryWrapperX<>( ContractAssessmentStandard. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractAssessmentStandard::getCreateTime);
        List<ContractAssessmentStandard> contractAssessmentStandardes =   this.list(condition);

        List<ContractAssessmentStandardDTO> dtos = BeanCopyUtils.convertListTo(contractAssessmentStandardes, ContractAssessmentStandardDTO::new);

        String fileName = "审核标准表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractAssessmentStandardDTO.class,dtos );

    }

    @Override
    public List<ContractAssessmentStandardVO> listByNumber(String contractNumber) {
        LambdaQueryWrapperX<ContractAssessmentStandard> wrapperX = new LambdaQueryWrapperX<>(ContractAssessmentStandard.class);
        wrapperX.eq(ContractAssessmentStandard::getContractNumber,contractNumber);
        List<ContractAssessmentStandard> list = this.list(wrapperX);
        return BeanCopyUtils.convertListTo(list, ContractAssessmentStandardVO::new);
    }

    @Override
    public void  setEveryName(List<ContractAssessmentStandardVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractAssessmentStandardExcelListener extends AnalysisEventListener<ContractAssessmentStandardDTO> {

        private final List<ContractAssessmentStandardDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractAssessmentStandardDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractAssessmentStandardDTO> getData() {
            return data;
        }
    }


}
