<script setup lang="ts">
import { BasicCard, OrionTable, randomString } from 'lyra-component-vue3';
import {
  h, inject, onMounted, reactive, ref, Ref,
} from 'vue';
import Api from '/@/api';

const detailsData: Record<string, any> = inject('detailsData');
const columns = [
  {
    title: '事件类型',
    dataIndex: 'eventType',
    ellipsis: true,
  },
  {
    title: '数量',
    colSpan: 2,
    dataIndex: 'chart',
    width: 210,
    customRender({ record }) {
      return h('div', {
        class: 'chart-item',
        style: {
          width: `${record.percent}%`,
        },
      });
    },
  },
  {
    title: '',
    colSpan: 0,
    width: 50,
    align: 'center',
    dataIndex: 'count',
  },
];

const data: {
  // 质量top
  quality: {
    label: string
    list: any[]
  }
  // 安全top
  safety: {
    label: string
    list: any[]
  }
} = reactive({
  safety: {
    label: '安全事件TOP5',
    list: [],
  },
  quality: {
    label: '质量事件TOP5',
    list: [],
  },
});
const loading: Ref<boolean> = ref(false);

async function getData() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/safety-quality-env/deviation/top?majorRepairTurn=${detailsData?.repairRound}&topSize=5`).fetch('', '', 'POST');
    const { qualityList, safetyList } = result;
    data.quality.list = qualityList?.map((item, index) => {
      item.id = `${randomString()}_${index}`;
      const max = (qualityList?.[0]?.count || 1);
      item.percent = Math.round(item.count / (max / 100));
      return item;
    }) || [];
    data.safety.list = safetyList?.map((item, index) => {
      item.id = `${randomString()}_${index}`;
      const max = (safetyList?.[0]?.count || 1);
      item.percent = Math.round(item.count / (max / 100));
      return item;
    }) || [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getData();
});

const tableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  columns,
  pagination: false,
};
</script>

<template>
  <div class="grid-container">
    <BasicCard
      v-for="(item,index) in data"
      :key="index"
      :title="item.label"
    >
      <OrionTable
        :options="tableOptions"
        :data-source="item.list"
        :scroll="{ y: 300 }"
      />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, minmax(400px, 1fr));
  gap: 20px;
}

:deep(.chart-item) {
  height: 20px;
  border-radius: 4px;
  background-color: #99CCCC;
}
</style>
