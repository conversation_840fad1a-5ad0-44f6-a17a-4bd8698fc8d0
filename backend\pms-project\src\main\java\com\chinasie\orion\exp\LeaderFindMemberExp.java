package com.chinasie.orion.exp;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Keafmd
 *
 * @ClassName: LeaderFindMemberExp
 * @Description: 找到领导的所属机构下面所有的成员
 *               主管领导 与  领导组成员
 * @author: zhangqianyang
 * @date: 2024/8/9 10:17
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class LeaderFindMemberExp implements IExp {

    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DeptUserDOMapper deptUserDOMapper;



    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "查找领导所属机构下面的成员";
    }

    @Override
    public List<String> exp(String s) {
        log.info("开始查找领导下面的成员");
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        //看当前用户是否是领导
        LambdaQueryWrapperX<DeptLeaderDO> queryLeaderWrapper=new LambdaQueryWrapperX<>(DeptLeaderDO.class);
        queryLeaderWrapper.eq(DeptLeaderDO::getUserId,currentUserId);
        queryLeaderWrapper.select(DeptLeaderDO::getDeptId);
        boolean exists = deptLeaderDORepository.exists(queryLeaderWrapper);
        if(exists){
            //查找当前领导所属的机构及其下属机构的所有的成员的
            List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(queryLeaderWrapper);
            List<String> leaderDeptIds = deptLeaderDOS.stream().map(DeptLeaderDO::getDeptId).distinct().collect(Collectors.toList());
            //查找下属的部门

            Map<String, DeptVO> deptVOMap = deptRedisHelper.mapAllDept();

            List<DeptVO> deptVOS= Lists.newArrayList();

            deptVOMap.forEach((k,vo)->deptVOS.add(vo));

            Map<String, List<DeptVO>> parentDeptVoMap = deptVOS.stream().collect(Collectors.groupingBy(DeptVO::getParentId));

            List<String> deptIds=Lists.newArrayList();

            leaderDeptIds.forEach(parntId->{
                findSubDept(parntId,deptIds,parentDeptVoMap);
            });

            //获取相关机构的成员
            if(!CollectionUtils.isEmpty(deptIds)){
                LambdaQueryWrapperX<DeptUserDO> queryDeptUserWrapper=new LambdaQueryWrapperX();
                queryDeptUserWrapper.in(DeptLeaderDO::getDeptId,deptIds);
                queryDeptUserWrapper.select(DeptLeaderDO::getUserId);
                List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(queryDeptUserWrapper);
                return deptUserDOS.stream().map(DeptUserDO::getDeptId).collect(Collectors.toList());
            }
        }else {
            log.info("当前用户不是领导");
        }
        return Arrays.asList(s.split(","));
    }

    /**
     * 查找下级
     * @param parntId
     * @param deptIds
     * @param parentDeptVoMap
     */
    private void findSubDept(String parntId, List<String> deptIds, Map<String, List<DeptVO>> parentDeptVoMap) {
        deptIds.add(parntId);
        if(parentDeptVoMap.containsKey(parntId)){
            List<DeptVO> deptVOS = parentDeptVoMap.get(parntId);
            List<String> tempDeptIds = deptVOS.stream().map(DeptVO::getId).collect(Collectors.toList());
            tempDeptIds.forEach(tempDeptId->{
                findSubDept(tempDeptId,deptIds,parentDeptVoMap);
            });
        }
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
