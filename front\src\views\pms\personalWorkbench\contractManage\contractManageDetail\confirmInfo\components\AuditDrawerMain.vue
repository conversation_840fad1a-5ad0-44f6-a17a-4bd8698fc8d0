<template>
  <div class="process flex flex-ac p-b-tb p-b-lr">
    <span class="label">审批编号：</span>
    <span class="value">{{ $props.detail?.contractPayNodeConfirmVO?.['number'] ||'--' }}</span>
  </div>
  <div class="process-placeholder" />

  <DetailsLayout
    title="节点确认信息"
    :column="2"
    :data-source="$props.detail?.contractPayNodeConfirmVO"
    :list="nodeInfo"
  >
    <template #table>
      <div
        class="m-b-t"
        style="height: 180px;overflow: hidden"
      >
        <OrionTable
          ref="nodeTableRef"
          :options="nodeTableOptions"
        />
      </div>
    </template>
  </DetailsLayout>

  <DetailsLayout
    title="节点确认材料"
  >
    <template #table>
      <BasicButton
        :disabled="state.fileSelect.length===0"
        @click="handleBatchDownload"
      >
        批量下载
      </BasicButton>
      <div
        class="m-b-t"
        style="height: 180px;overflow: hidden"
      >
        <OrionTable
          ref="fileTableRef"
          :options="fileTableOptions"
          @selection-change="selectionChange"
        />
      </div>
    </template>
  </DetailsLayout>

  <DetailsLayout
    title="节点审核支持性材料上传"
  >
    <BasicUpload
      :max-number="100"
      :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"
      buttonType="default"
      button-text="上传附件"
      :isClassification="false"
      :isToolRequired="false"
      @save-change="saveChange"
    />
    <div class="file-list">
      <div
        v-for="(item,index) in state.fileInfoDTOList"
        :key="index"
        class="file-item"
      >
        <span class="file-name">{{ item.name }}.{{ item['filePostfix'] }}</span>
        <Icon
          class="remove"
          icon="sie-icon-close"
          size="16"
          @click="removeFile(index)"
        />
      </div>
    </div>
  </DetailsLayout>

  <DetailsLayout title="节点审核意见">
    <Textarea
      v-model:value="state.comment"
      placeholder="请输入节点审核意见"
      :rows="4"
      maxlength="1000"
    />
  </DetailsLayout>
</template>

<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  computed,
  h,
  onMounted, reactive, ref, Ref,
} from 'vue';
import {
  BasicButton, OrionTable, downLoadById, BasicUpload, Icon, DataStatusTag, getDict,
} from 'lyra-component-vue3';
import { message, Textarea } from 'ant-design-vue';

const props = defineProps<{
    detail:any
}>();

const state = reactive({
  fileSelect: [],
  fileInfoDTOList: [],
  comment: '',
  payNodeTypeOptions: [],
});
const nodeInfo = [
  {
    label: '审核状态',
    field: ['dataStatus', 'name'],
  },
  {
    label: '提交时间',
    field: 'submitDate',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '提交人',
    field: 'submitUserIdName',
  },
  {
    label: '提交人工号',
    field: 'submitUserIdCode',
  },
  {
    label: '节点确认说明',
    field: 'confirmDesc',
  },
];
const nodeTableRef:Ref = ref();
const nodeTableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: computed(() => props.detail?.contractPayNodeVOList?.map((item) => ({
    ...item,
  })) ?? []),
  columns: [
    {
      title: '支付节点',
      dataIndex: 'payType',
      customRender({ text, record }) {
        return h('span', `${state.payNodeTypeOptions.find((result) => result.value === record.payType)?.description}`);
      },
    },
    {
      title: '支付说明',
      dataIndex: 'payDesc',
    },
  ],
};

const fileTableRef:Ref = ref();
const fileTableOptions = {
  rowSelection: {},
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: computed(() => props.detail?.documentVOList ?? []),
  columns: [
    {
      title: '名称',
      dataIndex: 'name',
      customRender({
        text, record,
      }) {
        return h('span', {
          title: text + record.filePostfix,
        }, [
          h('div', {
            class: 'action-btn flex-te',
            onClick(e:Event) {
              e.stopPropagation();
              window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
            },
          }, text),
        ]);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载',
      onClick({ id }) {
        downLoadById(id);
      },
    },
  ],
};

onMounted(() => {
  getPayNodeOptions();
});

async function getPayNodeOptions() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
}

// 批量下载
function handleBatchDownload() {
  state.fileSelect?.forEach((item) => {
    downLoadById(item?.id);
  });
}

// 表格多选
function selectionChange({ rows }) {
  state.fileSelect = rows;
}

// 保存按钮回调
function saveChange(fileList) {
  state.fileInfoDTOList = state.fileInfoDTOList.concat(fileList.map((item) => item.result));
}

// 移除文件
function removeFile(index) {
  state.fileInfoDTOList.splice(index, 1);
}

defineExpose({
  state,
});
</script>

<style scoped lang="less">
.process{
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  right: 0;
  background-color: ~`getPrefixVar('content-background')`;
}

.process-placeholder{
  width: 100%;
  height: 54px;
}

.file-list{
  display: flex;
  flex-direction: column;

  .file-item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    line-height: 30px;
    margin-top: 10px;

    .remove{
      display: none;
      cursor: pointer;
    }

    &:hover .remove{
      display: inline-block;
    }

    .file-name{
      width: 0;
      flex-grow: 1;
      margin-right: ~`getPrefixVar('content-margin')` ;
    }
  }
}
</style>
