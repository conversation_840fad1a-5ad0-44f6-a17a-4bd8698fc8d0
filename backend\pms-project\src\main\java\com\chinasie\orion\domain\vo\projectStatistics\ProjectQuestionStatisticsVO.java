package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectQuestionStatisticsVO对象", description = "问题统计表")
public class ProjectQuestionStatisticsVO {
    @ApiModelProperty(value = "项目id")
    private String id;
    @ApiModelProperty(value = "负责人名称")
    private String rspuserName;
    @ApiModelProperty(value = "负责人")
    private String rspuser;
    @ApiModelProperty(value = "展示时间")
    private String showTime;
    @ApiModelProperty(value = "时间")
    private Date timeValue;
    @ApiModelProperty(value = "未完成数量")
    private Integer unFinishedCount=0;
    @ApiModelProperty(value = "已完成数量")
    private Integer finishedCount=0;
    @ApiModelProperty(value = "已关闭数量")
    private Integer closeCount=0;
}
