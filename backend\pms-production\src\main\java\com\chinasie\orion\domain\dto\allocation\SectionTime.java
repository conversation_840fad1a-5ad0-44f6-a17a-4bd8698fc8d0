package com.chinasie.orion.domain.dto.allocation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SectionTime implements Serializable {

    private static final long serialVersionUID = 1L;

    private String rowId;

    private String relationId;

    private String orgId;

    private String staffNo;

    private String repairName;

    private String realStartDate;

    private String realEndDate;

    private String basePlaceCode;

    private String basePlaceName;

    private String teamCode;

    private String teamName;

    private String specialtyCode;

    private String specialtyName;

    private String repairRoundName;

    private String repairRoundCode;

    @Override
    public String toString() {
        return "SectionTime{" +
                "rowId='" + rowId + '\'' +
                ", relationId='" + relationId + '\'' +
                ", orgId='" + orgId + '\'' +
                ", staffNo='" + staffNo + '\'' +
                ", repairName='" + repairName + '\'' +
                ", realStartDate='" + realStartDate + '\'' +
                ", realEndDate='" + realEndDate + '\'' +
                ", basePlaceCode='" + basePlaceCode + '\'' +
                ", basePlaceName='" + basePlaceName + '\'' +
                ", teamCode='" + teamCode + '\'' +
                ", teamName='" + teamName + '\'' +
                ", specialtyCode='" + specialtyCode + '\'' +
                ", specialtyName='" + specialtyName + '\'' +
                ", repairRoundName='" + repairRoundName + '\'' +
                ", repairRoundCode='" + repairRoundCode + '\'' +
                '}';
    }
}
