package com.chinasie.orion.constant;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ContractMilestoneNode implements Serializable {

    /**
     * 里程碑验收
     */
    public static String NODE_CONTRACT_MILESTONE_END = "NODE_CONTRACT_MILESTONE_END";

    /**
     * 里程碑异常
     */
    public static String NODE_MILESTONE_EXCEPTION = "NODE_MILESTONE_EXCEPTION";

    /**
     * 里程碑日期修改
     */
    public static String NODE_MILESTONE_DATE_EDIT = "NODE_MILESTONE_DATE_EDIT";

    /**
     * 里程验收日期临期提醒
     */
    public static String NODE_MILESTONE_NOTIFY = "NODE_MILESTONE_NOTIFY";

    /**
     * 合同里程碑收入异常
     */
    public static final String CONTRACT_MILESTONE_INCOME = "CONTRACT_MILESTONE_INCOME";



}
