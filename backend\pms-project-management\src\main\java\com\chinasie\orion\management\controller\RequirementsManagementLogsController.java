package com.chinasie.orion.management.controller;

import com.chinasie.orion.domain.vo.RequirementsManagementLogsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.RequirementsManagementLogsDTO;
import com.chinasie.orion.management.service.RequirementsManagementLogsService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * RequirementsManagementLogs 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 11:10:30
 */
@RestController
@RequestMapping("/requirementsManagementLogs")
@Api(tags = "需求表单操作日志记录")
public class  RequirementsManagementLogsController  {

    @Autowired
    private RequirementsManagementLogsService requirementsManagementLogsService;


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "需求表单操作日志记录", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = requirementsManagementLogsService.remove(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求表单操作日志记录", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<RequirementsManagementLogsVO>> pages(@RequestBody Page<RequirementsManagementLogsDTO> pageRequest) throws Exception {
        Page<RequirementsManagementLogsVO> rsp =  requirementsManagementLogsService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "新增")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求表单操作日志记录", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResponseDTO<Boolean> add(@RequestBody RequirementsManagementLogsDTO requirementsManagementLogsDTO) throws Exception {
        boolean rsp = requirementsManagementLogsService.add(requirementsManagementLogsDTO);
        return new ResponseDTO<>(rsp);
    }




}
