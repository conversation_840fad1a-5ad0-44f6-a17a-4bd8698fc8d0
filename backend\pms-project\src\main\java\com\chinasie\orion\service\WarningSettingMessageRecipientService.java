package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.WarningSettingMessageRecipientDTO;
import com.chinasie.orion.domain.entity.ProjectDeclareFileInfo;
import com.chinasie.orion.domain.entity.WarningSettingMessageRecipient;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecipientVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.lang.String;
import java.util.List;
/**
 * <p>
 * WarningSettingMessageRecipient 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17 17:01:13
 */
public interface WarningSettingMessageRecipientService  extends OrionBaseService<WarningSettingMessageRecipient> {
    /**
     *  详情
     *
     * * @param id
     */
    WarningSettingMessageRecipientVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param warningSettingMessageRecipientDTO
     */
    WarningSettingMessageRecipientVO create(WarningSettingMessageRecipientDTO warningSettingMessageRecipientDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param warningSettingMessageRecipientDTO
     */
    Boolean edit(WarningSettingMessageRecipientDTO warningSettingMessageRecipientDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;



}
