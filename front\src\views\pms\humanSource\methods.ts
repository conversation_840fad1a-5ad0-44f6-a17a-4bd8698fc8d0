import dayjs from 'dayjs';

/* 获取本周数据* */
function getStartAndEndOfWeek() {
  // 获取今天的日期
  const today = dayjs();
  // 获取本周的开始日期和结束日期
  const startDate = today.startOf('week');
  const endDate = today.endOf('week');

  // 格式化日期为年月日字符串
  const formatDate = (date) => date.format('YYYY-MM-DD');

  // 返回开始日期和结束日期的数组
  return [formatDate(startDate), formatDate(endDate)];
}

// const [startOfWeek, endOfWeek] = getStartAndEndOfWeek();
// console.log('本周开始日期:', startOfWeek);
// console.log('本周结束日期:', endOfWeek);

/* 获取近两周 */
function getStartAndEndOfLastTwoWeeks() {
  // 获取今天的日期
  const today = dayjs();
  // 获取两周前的日期
  const twoWeeksAgo = today.subtract(1, 'week');
  // 获取本周的开始日期和结束日期
  const startDate1 = twoWeeksAgo.startOf('week');
  // 获取上一周的开始日期和结束日期
  const endDate2 = today.endOf('week');

  // 格式化日期为年月日字符串
  const formatDate = (date) => date.format('YYYY-MM-DD');

  // 返回两周的开始日期和结束日期的数组
  return [formatDate(startDate1), formatDate(endDate2)];
}

// 调用函数获取近两周的开始日期和结束日期
// const [lastTwoWeeks1, lastTwoWeeks2] = getStartAndEndOfLastTwoWeeks();
// console.log('近两周第一周的开始日期:', lastTwoWeeks1);
// console.log('近两周第二周的结束日期:', lastTwoWeeks2);
/*
 * 获取本月开始日期结束日期
 * */
function getStartAndEndOfMonth() {
  // 获取今天的日期
  const today = dayjs();
  // 获取本月的开始日期
  const startOfMonth = today.startOf('month');
  // 获取本月的结束日期
  const endOfMonth = today.endOf('month');

  // 格式化日期为年月日字符串
  const formatDate = (date) => date.format('YYYY-MM-DD');

  // 返回本月开始日期和结束日期
  return [formatDate(startOfMonth), formatDate(endOfMonth)];
}

// 调用函数获取本月开始日期和结束日期
// const [startOfMonth, endOfMonth] = getStartAndEndOfMonth();
// console.log('本月开始日期:', startOfMonth);
// console.log('本月结束日期:', endOfMonth);

/* 获取上月数据 */
function getStartAndEndOfLastMonth() {
  // 获取今天的日期
  const today = new Date();
  // 获取上个月的年份和月份
  let lastMonthYear = today.getFullYear();
  let lastMonth = today.getMonth() - 1;
  if (lastMonth === -1) {
    lastMonthYear--;
    lastMonth = 11; // 11表示12月
  }
  // 获取上个月的开始日期
  const startOfLastMonth = new Date(lastMonthYear, lastMonth, 1);
  // 获取上个月的结束日期
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);

  // 格式化日期为年月日字符串
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 返回上个月开始日期和结束日期
  return [formatDate(startOfLastMonth), formatDate(endOfLastMonth)];
}

// 调用函数获取上个月开始日期和结束日期
// const [startOfLastMonth, endOfLastMonth] = getStartAndEndOfLastMonth();
// console.log('上个月开始日期:', startOfLastMonth);
// console.log('上个月结束日期:', endOfLastMonth);

/* 获取下月开始日期和结束日期 */
function getStartAndEndOfNextMonth() {
  // 获取今天的日期
  const today = dayjs();
  // 获取下个月的开始日期
  const startOfNextMonth = today.add(1, 'month').startOf('month');
  // 获取下个月的结束日期
  const endOfNextMonth = today.add(1, 'month').endOf('month');

  // 格式化日期为年月日字符串
  const formatDate = (date) => date.format('YYYY-MM-DD');

  // 返回下个月开始日期和结束日期
  return [formatDate(startOfNextMonth), formatDate(endOfNextMonth)];
}

// 调用函数获取下个月开始日期和结束日期
// const [startOfNextMonth, endOfNextMonth] = getStartAndEndOfNextMonth();
// console.log('下个月开始日期:', startOfNextMonth);
// console.log('下个月结束日期:', endOfNextMonth);

// 获得上一个月时间范围
function getLastMonthRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取上个月的时间范围
  const lastMonthStartDate = startDate.subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
  const lastMonthEndDate = endDate.subtract(1, 'month').endOf('month').format('YYYY-MM-DD');

  return [lastMonthStartDate, lastMonthEndDate];
}

// 示例调用
// const dateRange = ['2024-04-01', '2024-04-30'];
// const lastMonthRange = getLastMonthRange(dateRange);
// console.log(lastMonthRange); // 输出：[ '2024-03-01', '2024-03-31' ]

// 获得下一个月时间范围
function getNextMonthRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取下个月的时间范围
  const nextMonthStartDate = startDate.add(1, 'month').startOf('month').format('YYYY-MM-DD');
  const nextMonthEndDate = endDate.add(1, 'month').endOf('month').format('YYYY-MM-DD');

  return [nextMonthStartDate, nextMonthEndDate];
}

// 示例调用
// const dateRange = ['2024-04-01', '2024-04-30'];
// const nextMonthRange = getNextMonthRange(dateRange);
// console.log(nextMonthRange); // 输出：[ '2024-05-01', '2024-05-31' ]

// 获得上一周时间范围
function getLastWeekRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取上一周的时间范围
  const lastWeekStartDate = startDate.subtract(1, 'week').startOf('week').format('YYYY-MM-DD');
  const lastWeekEndDate = endDate.subtract(1, 'week').endOf('week').format('YYYY-MM-DD');

  return [lastWeekStartDate, lastWeekEndDate];
}

// 获得下一周时间范围
function getNextWeekRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取下一周的时间范围
  const nextWeekStartDate = startDate.add(1, 'week').startOf('week').format('YYYY-MM-DD');
  const nextWeekEndDate = endDate.add(1, 'week').endOf('week').format('YYYY-MM-DD');

  return [nextWeekStartDate, nextWeekEndDate];
}

// 示例调用
// const dateRange = ['2024-04-15', '2024-04-21'];
// const nextWeekRange = getNextWeekRange(dateRange);
// console.log(nextWeekRange); // 输出：[ '2024-04-22', '2024-04-28' ]

// 上一个两周时间
function getLastTwoWeeksRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取上一个两周的时间范围
  const lastTwoWeeksStartDate = startDate.subtract(2, 'week').startOf('week').format('YYYY-MM-DD');
  const lastTwoWeeksEndDate = endDate.subtract(1, 'week').endOf('week').format('YYYY-MM-DD');

  return [lastTwoWeeksStartDate, lastTwoWeeksEndDate];
}

// 示例调用
// const dateRange = ['2024-04-08', '2024-04-21'];
// const lastTwoWeeksRange = getLastTwoWeeksRange(dateRange);
// console.log(lastTwoWeeksRange); // 输出：[ '2024-03-25', '2024-04-07' ]

// 下一个两周时间
function getNextTwoWeeksRange(dateRange) {
  // 解析开始日期和结束日期
  const startDate = dayjs(dateRange[0]);
  const endDate = dayjs(dateRange[1]);

  // 获取下一个两周的时间范围
  const nextTwoWeeksStartDate = startDate.add(2, 'week').startOf('week').format('YYYY-MM-DD');
  const nextTwoWeeksEndDate = endDate.add(3, 'week').endOf('week').format('YYYY-MM-DD');

  return [nextTwoWeeksStartDate, nextTwoWeeksEndDate];
}

// 示例调用
// const dateRange = ['2024-04-08', '2024-04-21'];
// const nextTwoWeeksRange = getNextTwoWeeksRange(dateRange);
// console.log(nextTwoWeeksRange); // 输出：[ '2024-04-22', '2024-05-05' ]
export {
  getStartAndEndOfWeek,
  getStartAndEndOfLastTwoWeeks,
  getStartAndEndOfMonth,
  getStartAndEndOfLastMonth,
  getStartAndEndOfNextMonth,
  getLastMonthRange,
  getNextMonthRange,
  getLastWeekRange,
  getNextWeekRange,
  getLastTwoWeeksRange,
  getNextTwoWeeksRange,
};
