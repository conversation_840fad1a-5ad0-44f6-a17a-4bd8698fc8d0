<template>
  <Layout2
    v-model:tabsIndex="state.tabsIndex"
    :tabs="state.tabs"
    @tabsChange="tabsChange"
  >
    <PlanDetailTableIndex
      v-if="state.tabActiveId === '1'"
      :planId="planId"
    />
    <PlanDetailGanttIndex
      v-else-if="state.tabActiveId === '2'"
      :planId="planId"
    />
  </Layout2>
</template>

<script setup lang="ts">
import { Layout2 } from 'lyra-component-vue3';
import { reactive } from 'vue';
import { useRoute } from 'vue-router';
import { PlanDetailTableIndex, PlanDetailGanttIndex } from './pages';

const route = useRoute();

const planId = route.query?.id as string;

const state = reactive({
  tabsIndex: 0,
  tabActiveId: '1',
  tabs: [
    {
      name: '计划',
      id: '1',
    },
    {
      name: '甘特图',
      id: '2',
    },
  ],
});

function tabsChange(index, tabItem) {
  state.tabActiveId = tabItem.id;
}
</script>

<style scoped>

</style>
