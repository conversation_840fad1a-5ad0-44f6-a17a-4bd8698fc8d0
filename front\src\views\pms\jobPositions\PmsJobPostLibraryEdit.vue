<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';
import { FormSchema } from '/@/components/Form';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '岗位名称',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'number',
    label: '岗位编码',
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'authorizationTime',
    label: '授权时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      min: 0,
      max: 99999,
      precision: 0,
      addonAfter: '月',
      class: 'w-full',
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'baseCode',
    label: '所属基地',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
      api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'code',
    },
    rules: [{ required: true }],
    component: 'ApiSelect',
  },
  {
    field: 'authorizationGuide',
    label: '授权指引',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      rows: 4,
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-post-library').fetch('', props?.record?.id, 'GET');
    setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/job-post-library').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
