package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderCollectDTO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDTO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDetailDTO;
import com.chinasie.orion.management.domain.entity.NcfFormPurchOrderCollect;
import com.chinasie.orion.management.domain.vo.NcfFormPurchOrderCollectVO;
import com.chinasie.orion.management.domain.vo.NumMoneyVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * NcfFormPurchOrderCollect 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
public interface NcfFormPurchOrderCollectService extends OrionBaseService<NcfFormPurchOrderCollect> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormPurchOrderCollectVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormPurchOrderCollectDTO
     */
    String create(NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormPurchOrderCollectDTO
     */
    Boolean edit(NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormPurchOrderCollectVO> pages(Page<NcfFormPurchOrderCollectDTO> pageRequest) throws Exception;

    /**
     * 支付条数及订单金额
     * <p>
     * * @param pageRequest
     */
    NumMoneyVO getNumMoney(Page<NcfFormPurchOrderCollectDTO> pageRequest);

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfFormPurchOrderCollectDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormPurchOrderCollectVO> vos) throws Exception;

    /**
     * 定时给订单待支付赋值
     */
    void updateOrderPayDay();

    /**
     * 集采订单发送邮件提醒
     */
    void sendEmailAndRemind();
}
