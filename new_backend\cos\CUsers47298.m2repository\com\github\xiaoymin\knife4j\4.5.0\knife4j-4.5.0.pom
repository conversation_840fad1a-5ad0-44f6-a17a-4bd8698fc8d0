<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.github.xiaoymin</groupId>
  <artifactId>knife4j</artifactId>
  <version>4.5.0</version>
  <packaging>pom</packaging>
  <name>knife4j</name>
  <description>knife4j是为Java
        MVC框架集成Swagger生成Api文档的增强解决方案,前身是swagger-bootstrap-ui,取名kni4j是希望她能像一把匕首一样小巧,轻量,并且功能强悍!!.</description>
  <url>https://gitee.com/xiaoym/knife4j</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>xiaoym</id>
      <name>肖玉民</name>
      <email><EMAIL></email>
      <roles>
        <role>Java Development Engineer</role>
      </roles>
      <timezone>2019-8-26 16:58:07</timezone>
    </developer>
  </developers>
  <modules>
    <module>knife4j-core</module>
    <module>knife4j-dependencies</module>
    <module>knife4j-openapi2-ui</module>
    <module>knife4j-openapi3-ui</module>
    <module>knife4j-openapi2-spring-boot-starter</module>
    <module>knife4j-openapi3-spring-boot-starter</module>
    <module>knife4j-aggregation-spring-boot-starter</module>
    <module>knife4j-openapi3-jakarta-spring-boot-starter</module>
    <module>knife4j-gateway-spring-boot-starter</module>
    <module>knife4j-openapi3-webflux-spring-boot-starter</module>
    <module>knife4j-openapi3-webflux-jakarta-spring-boot-starter</module>
  </modules>
  <scm>
    <connection>scm:*************:xiaoym/knife4j.git</connection>
    <developerConnection>scm:*************:xiaoym/knife4j.git</developerConnection>
    <url>*************:xiaoym/knife4j.git</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>oss</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>oss</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <knife4j-spring.version>5.3.20</knife4j-spring.version>
    <knife4j-swagger-models-v3.version>2.2.19</knife4j-swagger-models-v3.version>
    <knife4j-lombok.version>1.18.24</knife4j-lombok.version>
    <knife4j-slf4j.version>1.7.28</knife4j-slf4j.version>
    <knife4j-spring-boot.version>2.6.8</knife4j-spring-boot.version>
    <knife4j-spring-plugin.version>2.0.0.RELEASE</knife4j-spring-plugin.version>
    <knife4j-hutool.version>5.8.15</knife4j-hutool.version>
    <knife4j-javassist.version>3.25.0-GA</knife4j-javassist.version>
    <knife4j-servlet-jakarta.version>6.0.0</knife4j-servlet-jakarta.version>
    <knife4j-servlet.version>3.1.0</knife4j-servlet.version>
    <knife4j-junit-jupiter-api.version>5.7.2</knife4j-junit-jupiter-api.version>
    <knife4j-skipTests>true</knife4j-skipTests>
    <knife4j-springfox.version>2.10.5</knife4j-springfox.version>
    <knife4j-httpclient.version>4.5.14</knife4j-httpclient.version>
    <knife4j-springdoc-openapi.version>1.7.0</knife4j-springdoc-openapi.version>
    <knife4j-swagger-models.version>1.6.6</knife4j-swagger-models.version>
    <knife4j-project.build.sourceEncoding>UTF-8</knife4j-project.build.sourceEncoding>
    <knife4j-spotless-maven-plugin.version>2.22.1</knife4j-spotless-maven-plugin.version>
    <knife4j-java.version>1.8</knife4j-java.version>
    <knife4j-springdoc-openapi-jakarta.version>2.3.0</knife4j-springdoc-openapi-jakarta.version>
    <knife4j-gson.version>2.10.1</knife4j-gson.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-core</artifactId>
        <version>4.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi2-ui</artifactId>
        <version>4.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-ui</artifactId>
        <version>4.5.0</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${knife4j-hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${knife4j-lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${knife4j-httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${knife4j-gson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.javassist</groupId>
        <artifactId>javassist</artifactId>
        <version>${knife4j-javassist.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger.core.v3</groupId>
        <artifactId>swagger-annotations</artifactId>
        <version>${knife4j-swagger-models-v3.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger.core.v3</groupId>
        <artifactId>swagger-models</artifactId>
        <version>${knife4j-swagger-models-v3.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-models</artifactId>
        <version>${knife4j-swagger-models.version}</version>
      </dependency>
      <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-core</artifactId>
        <version>${knife4j-swagger-models.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>${knife4j-servlet.version}</version>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-swagger2</artifactId>
        <version>${knife4j-springfox.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-spring-webmvc</artifactId>
        <version>${knife4j-springfox.version}</version>
      </dependency>
      <dependency>
        <groupId>io.springfox</groupId>
        <artifactId>springfox-bean-validators</artifactId>
        <version>${knife4j-springfox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.plugin</groupId>
        <artifactId>spring-plugin-core</artifactId>
        <version>${knife4j-spring-plugin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.plugin</groupId>
        <artifactId>spring-plugin-metadata</artifactId>
        <version>${knife4j-spring-plugin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-common</artifactId>
        <version>${knife4j-springdoc-openapi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-webflux-core</artifactId>
        <version>${knife4j-springdoc-openapi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-webmvc-core</artifactId>
        <version>${knife4j-springdoc-openapi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-ui</artifactId>
        <version>${knife4j-springdoc-openapi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${knife4j-slf4j.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
            <source>${knife4j-java.version}</source>
            <target>${knife4j-java.version}</target>
            <encoding>UTF-8</encoding>
            <compilerArgument>-Xlint:unchecked</compilerArgument>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.10.4</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <phase>package</phase>
              <goals>
                <goal>jar</goal>
              </goals>
              <configuration>
                <additionalparam>-Xdoclint:none</additionalparam>
              </configuration>
            </execution>
          </executions>
          <configuration>
            <encoding>UTF-8</encoding>
            <charset>UTF-8</charset>
            <aggregate>true</aggregate>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.0.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.18.1</version>
          <configuration>
            <skipTests>${knife4j-skipTests}</skipTests>
            <argLine>-Dfile.encoding=UTF-8</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>1.2.7</version>
          <executions>
            <execution>
              <id>flatten</id>
              <phase>process-resources</phase>
              <goals>
                <goal>flatten</goal>
              </goals>
            </execution>
            <execution>
              <id>flatten.clean</id>
              <phase>clean</phase>
              <goals>
                <goal>clean</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <updatePomFile>true</updatePomFile>
            <flattenMode>resolveCiFriendliesOnly</flattenMode>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${knife4j-spotless-maven-plugin.version}</version>
          <executions>
            <execution>
              <phase>compile</phase>
              <goals>
                <goal>apply</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <java>
              <eclipse>
                <file>src/main/resources/spotless_knife4j_formatter.xml</file>
              </eclipse>
              <licenseHeader>
                <file>src/main/resources/license-header</file>
              </licenseHeader>
            </java>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.10.4</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <additionalparam>-Xdoclint:none</additionalparam>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <repository>
          <id>oss</id>
          <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
          <id>oss</id>
          <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
  </profiles>
</project>
