<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower, Layout3Content,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Spin } from 'ant-design-vue';
import { openFormDrawer } from '../../utils';
import Api from '/@/api';
import BasicInfo from '../components/BasicInfo.vue';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const infoId = ref(route.params.id);
const actionId: Ref<string | null> = ref('7fkSM8mB');
const dataId = computed(() => route.params?.id);
const detailsPowerData: Ref = ref(null);
const detailsDataInfo: Ref = ref({});
provide('detailsPowerData', detailsPowerData);
provide('detailsDataInfo', detailsDataInfo);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  id: detailsData.id,
  name: detailsData.name,
  className: detailsData.className,
  projectCode: detailsData.number,
}));

const menuData = ref([]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/spm/nonContractProc').fetch({
      pageCode: 'detail-container-042a7f-nonContractProc',
    }, dataId.value, 'GET');
    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

const actions = computed(() => [
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => isPower('edit-button-042a7f-nonContractProc-7fkSM8mB', detailsPowerData.value),
    onClick() {
      openFormDrawer(BasicInfo, { id: dataId.value }, getDetails);
    },
  },
]);
const getDetailInfo = async () => {
  try {
    const result = await new Api('/spm/nonContractProc').fetch({
      pageCode: 'noPurchaseContract',
    }, unref(infoId), 'GET');
    detailsDataInfo.value = result;
    const authCodeList = result?.detailAuthList?.map?.((item) => item.containerCode) ?? [];
    const basicArr = [
      {
        id: '7fkSM8mB',
        name: '无合同采购支付信息',
        code: 'PMS_WHTCGXQ_container_02_HB_01',
      },
    ].filter((item) => authCodeList.includes(item.code));
    menuData.value = basicArr;
  } catch (e) {

  }
};

onMounted(() => {
  getDetailInfo();
});
</script>

<template>
  <Layout3
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #code>
      <h2
        class="page-title"
        title="sdsds"
      >
        sdsds
      </h2>
      <div class="page-subtitle">
        <span>合同编号：{{ detailsDataInfo?.contractNumber }}</span>
        <span>采购立项号：{{ detailsDataInfo?.projectCode }}</span>
        <span>采购申请号：{{ detailsDataInfo?.purchaseApplicant }}</span>
      </div>
    </template>
    <template #header-info>
      <div class="type-map">
        <h2 class="type-h2-enum">
          ssd
        </h2>
        <h2 class="type-p-enum">
          无采购合同
        </h2>
      </div>
    </template>
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicInfo />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  height: 100px !important;

  .project-title {
    width: 400px !important;
  }

  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.page-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 18px;
  color: #000000D9;
  margin-bottom: 5px;
}

.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;

  span {
    margin-right: 20px;
  }
}

.type-map {
  display: flex;
  flex-direction: column;

  .type-h2-enum {
    font-size: 14px;
    color: #000000D9;
    margin-bottom: 3px;
  }

  .type-p-enum {
    font-size: 12px;
    color: #000000D9;
    margin-bottom: 0;
  }
}
</style>
