<template>
  <Layout
    v-get-power="{pageCode:'PMS_RYCYDBB_PersonJoinReport'}"
    :options="{ body: { scroll: true } }"
    contentTitle="人员参与度报表"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_RYCYDBB_container_button_01']"
          icon="fa-external-link"
          :disabled="tableInfo.disabled"
          @click="goDown(tableInfo.keys)"
        >
          导出所选
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_RYCYDBB_container_button_02']"
          icon="orion-icon-upload"
          @click="goDown()"
        >
          导出全部
        </BasicButton>
      </template>
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'taskOnTimeCompleteRate'">
          <span>
            {{ column.title }}
            <Tooltip
              title="计划准时完成数/计划总数"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </span>
        </template>
        <template v-if="column.dataIndex === 'manHourRate'">
          <span>
            {{ column.title }}
            <Tooltip
              title="当前人员准时报工的天数/总报工天数"
            >
              <Icon icon="sie-icon-attr" />
            </Tooltip>
          </span>
        </template>
      </template>

      <template #filter>
        <div class="padding-bottom-10">
          <div class="form-wrap">
            <BasicForm @register="register">
              <template #Button>
                <BasicButton
                  icon="sie-icon-sousuo"
                  @click="search"
                >
                  查询
                </BasicButton>
                <BasicButton
                  icon="sie-icon-chongzhi"
                  @click="reset"
                >
                  重置
                </BasicButton>
              </template>
            </BasicForm>
          </div>
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<script setup lang="ts">
import {
  h, reactive, ref,
} from 'vue';
import {
  OrionTable,
  useForm,
  BasicForm,
  BasicButton,
  DataStatusTag,
  Icon,
  useITable,
  downloadByData,
  Layout,
  openSelectModal,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Tooltip, Modal, Select } from 'ant-design-vue';
import { isArray } from 'lodash-es';
import dayjs from 'dayjs';
import { getColumns } from './src/tableConfig';
import { useUserStore } from '/@/store/modules/user';
import ToolBarLeft from './src/ToolBarLeft.vue';

const userStore: any = useUserStore();
const query = ref({});
const searchConditions: any = ref([]);
const paramsObj: any = ref({});
const [tableRef, tableInfo]: any = useITable({});
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  rowSelection: {},
  showTableSetting: false,
  resizeHeightOffset: 138,
  showIndexColumn: true,
  api: (tableParams) => {
    let params = {
      ...tableParams,
      query: query.value,
      searchConditions: searchConditions.value,
    };
    paramsObj.value = params;
    return new Api('/pms/userPerformanceReport/getPage').fetch(params, '', 'POST');
  },
  columns: [
    {
      title: '人员姓名',
      dataIndex: 'name',
      fixed: 'left',
    },
    {
      title: '工号',
      dataIndex: 'code',
    },
    {
      title: '所属部门',
      dataIndex: 'orgName',
    },
    {
      title: '所属处室',
      dataIndex: 'deptName',
    },
    {
      title: '参与项目数',
      dataIndex: 'projectNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('参与项目', record);
          },
        }, text || '--');
      },
    },
    {
      title: '负责计划数',
      dataIndex: 'resTaskNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('负责计划', record);
          },
        }, text || '--');
      },
    },
    {
      title: '已完成计划数',
      dataIndex: 'completeTaskNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('已完成计划', record);
          },
        }, text || '--');
      },
    },
    {
      title: '逾期计划数',
      dataIndex: 'overdueTaskNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('逾期计划', record);
          },
        }, text || '--');
      },
    },
    {
      title: '变更计划数',
      dataIndex: 'changeTaskNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('变更计划', record);
          },
        }, text || '--');
      },
    },
    {
      title: '交付物数量',
      dataIndex: 'deliverNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('交付物', record);
          },
        }, text || '--');
      },
    },
    {
      title: '计划准时完成率',
      dataIndex: 'taskOnTimeCompleteRate',
      slots: { customTitle: 'taskOnTimeCompleteRate' },
    },
    {
      title: '计划反馈次数',
      dataIndex: 'taskFeedbackNum',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('计划反馈', record);
          },
        }, text || '--');
      },
    },
    {
      title: '计划工期',
      dataIndex: 'durationDays',
    },
    {
      title: '实际工期',
      dataIndex: 'actualDurationDays',
    },
    {
      title: '已报工工时(天)',
      dataIndex: 'manHour',
    },
    {
      title: '报工及时率',
      dataIndex: 'manHourRate',
      slots: { customTitle: 'manHourRate' },
    },
  ],
});
const [register, method] = useForm({
  schemas: [
    {
      component: 'Input',
      field: 'name',
      label: '人员名称',
    },
    {
      component: 'Input',
      field: 'code',
      label: '人员工号',
    },
    {
      component: 'Input',
      field: 'A4',
      label: '',
      slot: 'Button',
    },
  ],
  baseColProps: {
    span: 4,
  },
  showActionButtonGroup: false,
  layout: 'horizontal',
});

// 搜索
function search() {
  const res = method.getFieldsValue();
  let Conditions: any = [];
  let obj: any = {};
  if (res) {
    let obj = JSON.parse(JSON.stringify(res));
    for (const key in obj) {
      if (!obj[key]) {
        delete obj[key];
      } else if (['name', 'code'].includes(key)) {
        Conditions.push({
          field: key,
          fieldType: 'String',
          values: [obj[key]],
          queryType: 'like',
        });
      } else {
        Conditions.push({
          field: key,
          fieldType: 'String',
          values: isArray(obj[key]) ? obj[key] : [obj[key]],
          queryType: 'eq',
        });
      }
    }
  }
  query.value = obj?.statisticalPeriod ? obj : {};
  searchConditions.value = Conditions?.length ? [Conditions] : [];
  tableRef.value.reload();
}

// 重置搜索
function reset() {
  method.resetFields();
  query.value = {};
  searchConditions.value = [];
  tableRef.value.reload();
}

// 页面上的导出
function goDown(keys) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出？',
    onOk() {
      // 如果勾选了数据,需要传入id
      let obj: any = JSON.parse(JSON.stringify(paramsObj.value));
      if (keys?.length) {
        if (obj.searchConditions?.length) {
          obj.searchConditions[0].push({
            field: 'id',
            fieldType: 'String',
            values: keys,
            queryType: 'in',
          });
        } else {
          obj.searchConditions = [
            [
              {
                field: 'id',
                fieldType: 'String',
                values: keys,
                queryType: 'in',
              },
            ],
          ];
        }
      }
      downloadByData('/pms/userPerformanceReport/export/excel', obj, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

function openR(title, record) {
  openSelectModal({
    tableConfig: {
      showRightList: false,
      tableOptions: {
        rowSelection: false,
        columns: getColumns(title, record),
      },
      tableApi: (params, keyWord, filterRes) => {
        let path = '';
        if (title === '参与项目') {
          path = `/pms/userPerformanceReport/projectByUser/getPage?userId=${record.id}`;
        } else if ([
          '负责计划',
          '已完成计划',
          '逾期计划',
          '变更计划',
          '计划反馈',
        ].includes(title)) {
          path = '/pms/userPerformanceReport/projectSchemeByUser/getPage';
        } else if (title === '交付物') {
          path = `/pms/userPerformanceReport/deliverable/getPage?userId=${record.id}`;
        }
        if ([
          '负责计划',
          '已完成计划',
          '逾期计划',
          '变更计划',
          '计划反馈',
        ].includes(title)) {
          params.query = {
            userId: record.id,
            completeTask: title === '已完成计划',
            overdueTask: title === '逾期计划',
            changeTask: title === '变更计划',
            taskFeedback: title === '计划反馈',
            projectId: filterRes ?? '',
          };
        }
        if (['交付物'].includes(title)) {
          params.query = {
            projectId: filterRes ?? '',
          };
        }
        return new Api(path).fetch({
          ...params,
        }, '', 'POST');
      },
      toolbarContent: (h) => h(ToolBarLeft, {
        showBtn: title !== '计划反馈', // 是否显示下载按钮
        showSelect: title !== '参与项目', // 是否显示筛选按钮
        onButtonChange: (projectId) => {
          goDownLoad(title, projectId, record.id);
        },
      }),
    },
    modalConfig: {
      title,
      footer: {
        cancelText: '关闭',
        isOk: false,
      },
    },
  });
}

// 弹窗中的导出
function goDownLoad(title, projectId, userId) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出？',
    onOk() {
      let path = '';
      let obj: any = {};
      // {
      //     "changeTask": true, //变更计划
      //     "completeTask": true, //已完成计划
      //     "overdueTask": true, //逾期计划
      //     "projectId": "",
      //     "taskFeedback": true, //反馈计划
      //     "userId": ""
      // }
      if (title === '参与项目') {
        path = `/pms/userPerformanceReport/export/projectByUser/excel?userId=${userId}`;
      } else if ([
        '负责计划',
        '已完成计划',
        '逾期计划',
        '变更计划',
        '计划反馈',
      ].includes(title)) {
        if (title === '负责计划') {
          obj = {
            userId,
            projectId,
          };
        } else if (title === '已完成计划') {
          obj = {
            userId,
            projectId,
            completeTask: true,
          };
        } else if (title === '逾期计划') {
          obj = {
            userId,
            projectId,
            overdueTask: true,
          };
        } else if (title === '计划反馈') {
          obj = {
            userId,
            projectId,
            taskFeedback: true,
          };
        } else if (title === '变更计划') {
          obj = {
            userId,
            projectId,
            changeTask: true,
          };
        }
        // obj = {
        //   userId,
        //   projectId,
        //   completeTask: title === '已完成计划',
        //   overdueTask: title === '逾期计划',
        //   taskFeedback: title === '计划反馈',
        // };
        path = '/pms/userPerformanceReport/export/projectSchemeByUser/excel';
      } else if (title === '交付物') {
        obj = {
          projectId,
        };
        path = `/pms/userPerformanceReport/export/deliverable/excel?userId=${userId}`;
      }
      downloadByData(path, obj, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}
</script>

<style scoped lang="less">
.date-footer {
  text-align: center;
  cursor: pointer;
}

.form-wrap {
  position: relative;
  //padding: ~`getPrefixVar('content-margin')`;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.24);
  border-radius: ~`getPrefixVar('radius-base')`;
}

.padding-bottom-10 {
  padding-bottom: ~`getPrefixVar('content-margin')`;
}
</style>
