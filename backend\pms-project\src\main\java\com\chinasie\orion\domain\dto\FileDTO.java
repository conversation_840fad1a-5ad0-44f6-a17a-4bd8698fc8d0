//package com.chinasie.orion.domain.dto;
//
//import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.util.Date;
//
//@Data
//@ApiModel(value = "FileDTO", description = "文件DTO")
//public class FileDTO {
//
//    /**
//     * ID
//     */
//    @ApiModelProperty(value = "ID")
//    private String id;
//
//    /**
//     * 下一个版本
//     */
//    @ApiModelProperty(value = "下一个版本")
//    private String nextRevId;
//
//    /**
//     * 版本顺序
//     */
//    @ApiModelProperty(value = "版本顺序")
//    private Integer revOrder;
//
//    /**
//     * 版本KEY
//     */
//    @ApiModelProperty(value = "版本KEY")
//    private String revKey;
//
//    /**
//     * 上一个版本
//     */
//    @ApiModelProperty(value = "上一个版本")
//    private String previousRevId;
//
//    /**
//     * 版本值
//     */
//    @ApiModelProperty(value = "版本值")
//    private String revId;
//
//    /**
//     * 初始版本
//     */
//    @ApiModelProperty(value = "初始版本")
//    private String initialRevId;
//
//    /**
//     * 预览图
//     */
//    @ApiModelProperty(value = "预览图")
//    private String imageId;
//
//    /**
//     * 类名称
//     */
//    @ApiModelProperty(value = "类名称")
//    private String className;
//
//    /**
//     * 名称
//     */
//    @ApiModelProperty(value = "名称")
//    private String name;
//
//    /**
//     * 编码
//     */
//    @ApiModelProperty(value = "编码")
//    private String number;
//
//    /**
//     * 代号
//     */
//    @ApiModelProperty(value = "代号")
//    private String code;
//
//    /**
//     * 状态 -1删除 0禁用 1启用
//     */
//    @ApiModelProperty(value = "状态 0禁用 1启用")
//    private Integer status;
//
//    @ApiModelProperty(value = "状态对象")
//    private DataStatusVO dataStatus;
//
//    /**
//     * 显示顺序
//     */
//    @ApiModelProperty(value = "显示顺序")
//    private Integer sort;
//
//    /**
//     * 描述
//     */
//    @ApiModelProperty(value = "描述")
//    private String description;
//
//    /**
//     * 创建者ID
//     */
//    @ApiModelProperty(value = "创建者ID")
//    private String creatorId;
//
//    /**
//     * 创建者工号.
//     */
//    @ApiModelProperty(value = "创建者工号")
//    private String creatorCode;
//
//
//    @ApiModelProperty(value = "创建者")
//    private String creatorName;
//
//    /**
//     * 创建时间
//     */
//    @ApiModelProperty(value = "创建时间")
//    private Date createTime;
//
//    /**
//     * 密级
//     */
//    @ApiModelProperty(value = "密级")
//    private String secretLevel;
//
//    /**
//     * 密级期限
//     */
//    @ApiModelProperty(value = "密级期限")
//    private String securityLimit;
//
//
//    @ApiModelProperty(value = "密级（中文）")
//    private String secretLevelName;
//
//    /**
//     * 修改人
//     */
//    @ApiModelProperty(value = "修改人ID")
//    private String modifyId;
//
//    @ApiModelProperty(value = "修改人")
//    private String modifyName;
//
//    /**
//     * 修改时间
//     */
//    @ApiModelProperty(value = "修改时间")
//    private Date modifyTime;
//
//    /**
//     * 拥有者
//     */
//    @ApiModelProperty(value = "拥有者Id")
//    private String ownerId;
//
//    @ApiModelProperty(value = "拥有者")
//    private String ownerName;
//
//    @ApiModelProperty(value = "logicStatus 逻辑删除字段")
//    private Integer logicStatus;
//
//    @ApiModelProperty(value = "签出")
//    private String checkIn;
//
//    /**
//     * 平台Id
//     */
//    @ApiModelProperty(value = "平台Id")
//    private String platformId;
//
//
//    /**
//     * 行政组织 Id
//     */
//    @ApiModelProperty(value = "行政组织 Id")
//    private String orgId;
//
//    /**
//     * 部门 Id
//     */
//    @ApiModelProperty(value = "部门 Id")
//    private String deptId;
//
//    /**
//     * 数据Id
//     */
//    @ApiModelProperty(value = "数据Id")
//    private String dataId;
//
//    /**
//     * 数据类型
//     */
//    @ApiModelProperty(value = "数据类型")
//    private String dataType;
//
//    /**
//     * 文件大小
//     */
//    @ApiModelProperty(value = "文件大小")
//    private Long fileSize;
//
//    /**
//     * 文件路径
//     */
//    @ApiModelProperty(value = "文件路径")
//    private String filePath;
//    /**
//     * pdfPath
//     */
//    @ApiModelProperty(value = "文件的pdf路径")
//    private String pfdPath;
//    /**
//     * 文件后缀
//     */
//    @ApiModelProperty(value = "文件后缀")
//    private String filePostfix;
//
//    /**
//     * 文件工具
//     */
//    @ApiModelProperty(value = "文件工具")
//    private String fileTool;
//
//    /**
//     * 文件类型
//     */
//    @ApiModelProperty(value = "文件类型")
//    private Integer type;
//
//    /**
//     * 文件父级
//     */
//    @ApiModelProperty(value = "文件父级")
//    private String parentId;
//
//    @ApiModelProperty(value = "保密期限")
//    private String securityLimitName;
//
//    /**
//     * 版本Id
//     */
//    @ApiModelProperty(value = "版本Id")
//    private String versionId;
//
//    /**
//     * 版本名称
//     */
//    @ApiModelProperty(value = "版本名称")
//    private String versionName;
//
//
//    /**
//     * 项目名称
//     */
//    @ApiModelProperty(value = "项目名称")
//    private String projectName;
//
//
//    /**
//     * 文档类型名称
//     */
//    @ApiModelProperty(value = "文档类型名称")
//    private String typeName;
//
//    /**
//     * 状态 1未发布 2流程中 3已发布
//     */
//    @ApiModelProperty(value = "文档状态名称")
//    private String statusName;
//
//    /**
//     * 创建人
//     */
//    @ApiModelProperty(value = "创建人")
//    private String createUserName;
//
//    /**
//     * 所有者
//     */
//    @ApiModelProperty(value = "所有者")
//    private String ownerUserName;
//
//    /**
//     * 修改人
//     */
//    @ApiModelProperty(value = "修改人")
//    private String modifyUserName;
//
//
//    @ApiModelProperty(value = "主文件")
//    private Integer masterFile;
//
//}
