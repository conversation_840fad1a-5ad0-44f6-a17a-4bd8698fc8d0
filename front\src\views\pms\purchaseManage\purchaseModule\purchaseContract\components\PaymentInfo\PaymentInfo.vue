<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  isSpacing: true,
  scroll: {
    y: 100,
  },
  smallSearchField: undefined,
  columns: [
    {
      title: '支付编号',
      dataIndex: 'procurementOrgName',
    },
    {
      title: '支付申请人',
      dataIndex: 'procurementOrgId',
    },
    {
      title: '支付申请发起时间',
      dataIndex: 'procurementGroupName',
    },
    {
      title: '支付类型',
      dataIndex: 'procurementGroupId',
    },
    {
      title: '本次支付汇总金额',
      dataIndex: 'businessRspUser',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '支付比例',
      dataIndex: 'businessRspUserId',
    },
  ],
  dataSource: [],
};
</script>

<template>
  <BasicCard
    title="合同支付信息"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
:deep(.ant-basic-table){
  padding: 0 !important;
}
</style>