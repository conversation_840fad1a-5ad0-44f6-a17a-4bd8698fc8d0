package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenanceLog Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-09 19:49:38
 */
@TableName(value = "pmsx_person_role_maintenance_log")
@ApiModel(value = "PersonRoleMaintenanceLogEntity对象", description = "人员角色维护日志")
@Data

public class PersonRoleMaintenanceLog extends  ObjectEntity  implements Serializable{

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    @TableField(value = "change_person")
    private String changePerson;

    /**
     * 变更人姓名
     */
    @ApiModelProperty(value = "变更人姓名")
    @TableField(value = "change_person_name")
    private String changePersonName;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    @TableField(value = "change_time")
    private Date changeTime;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    @TableField(value = "change_content")
    private String changeContent;

    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    @TableField(value = "before_change")
    private String beforeChange;

    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    @TableField(value = "after_changfe")
    private String afterChangfe;

    /**
     * 变更日志关联人员角色
     */
    @ApiModelProperty(value = "变更日志关联人员角色")
    @TableField(value = "change_id")
    private String changeId;

    /**
     * 人员角色维护表id
     */
    @ApiModelProperty(value = "人员角色维护表id")
    @TableField(value = "role_id")
    private String roleId;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @TableField(value = "change_reason")
    private String changeReason;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @TableField(value = "expertise_center_title")
    private String expertiseCenterTitle;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @TableField(value = "expertise_station_title")
    private String expertiseStationTitle;
}
