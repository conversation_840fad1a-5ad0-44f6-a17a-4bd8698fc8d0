package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.dto.ImToParameterDTO;
import com.chinasie.orion.domain.vo.ImToParameterVO;

import com.chinasie.orion.service.ImToParameterService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ImToParameter 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@RestController
@RequestMapping("/imToParameter")
@Api(tags = "接口和参数的关系")
public class ImToParameterController {

    @Autowired
    private ImToParameterService imToParameterService;




    /**
     * 批量新增
     *
     * @param imToParamBatchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】批量新增/编辑【接口和参数的关系】",
            type = "ImOrIfToParam",
            subType = "批量新增/编辑",
            bizNo = ""
    )
    public ResponseDTO<Boolean> batchCreateOrUpdate(@RequestBody ImOrIfToParamBatchDTO imToParamBatchDTO) throws Exception {
        return new ResponseDTO<>(imToParameterService.batchCreateOrUpdate(imToParamBatchDTO));
    }

    /**
     * 选择模板
     * @param imToParameterDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "选择模板")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】选择模板，业务编号：{#imToParameterDTO.id}",
            type = "ImToParameter",
            subType = "编辑",
            bizNo = "{#imToParameterDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@Validated @RequestBody  ImToParameterDTO imToParameterDTO) throws Exception {
        Boolean rsp = imToParameterService.edit(imToParameterDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除【接口和参数的关系】，业务编号：{ID_LIST{#ids}}",
            type = "ImToParameter",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = imToParameterService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 参数列表
     * @param im
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/detail/list", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行【接口和参数的关系】分页查询",
            type = "ImToParameter",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<List<ImToParameterVO>> detailList(@RequestBody ImToParameterVO im) throws Exception {
        List<ImToParameterVO> rsp =  imToParameterService.detailList(im);
        return new ResponseDTO<>(rsp);
    }
}
