package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectPerformanceDTO;
import com.chinasie.orion.domain.vo.ProjectPerformanceVO;
import com.chinasie.orion.service.ProjectPerformanceService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectPerformance 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@RestController
@RequestMapping("/projectPerformance")
@Api(tags = "项目绩效")
public class ProjectPerformanceController {

    @Autowired
    private ProjectPerformanceService projectPerformanceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目绩效", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectPerformanceVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectPerformanceVO rsp = projectPerformanceService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectPerformanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectPerformanceDTO}}】", type = "项目绩效", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectPerformanceVO> create(@RequestBody ProjectPerformanceDTO projectPerformanceDTO) throws Exception {
        ProjectPerformanceVO rsp = projectPerformanceService.create(projectPerformanceDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectPerformanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectPerformanceDTO}}】", type = "项目绩效", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectPerformanceDTO projectPerformanceDTO) throws Exception {
        Boolean rsp = projectPerformanceService.edit(projectPerformanceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目绩效", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectPerformanceService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages/{projectId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页了数据", type = "项目绩效", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectPerformanceVO>> pages(@PathVariable(value = "projectId", required = false) String projectId, @RequestBody Page<ProjectPerformanceDTO> pageRequest) throws Exception {
        Page<ProjectPerformanceVO> rsp = projectPerformanceService.pages(pageRequest, projectId);
        return new ResponseDTO<>(rsp);
    }

}
