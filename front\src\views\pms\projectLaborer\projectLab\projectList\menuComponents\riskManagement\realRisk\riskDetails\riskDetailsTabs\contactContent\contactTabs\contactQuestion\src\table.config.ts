export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',

    width: '240px',
    align: 'left',
    slots: { customRender: 'name' },
    // sorter: true,
    ellipsis: true,
  },

  {
    title: '提出人',
    dataIndex: 'exhibitor',
    key: 'exhibitor',
    width: '70px',
    align: 'left',
    slots: { customRender: 'exhibitor' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '提出时间',
    dataIndex: 'proposedTime',
    key: 'proposedTime',

    width: '100px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'proposedTime' },
  },
  {
    title: '期望完成时间',
    dataIndex: 'predictEndTime',
    key: 'predictEndTime',
    width: '100px',
    align: 'left',
    slots: { customRender: 'predictEndTime' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '严重程度',
    dataIndex: 'seriousLevelName',
    key: 'seriousLevel',

    width: '100px',
    align: 'left',
    slots: { customRender: 'seriousLevelName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevelName',
    key: 'priorityLevel',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'priorityLevelName' },
  },
  {
    title: '进度',
    dataIndex: 'scheduleName',
    key: 'schedule',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'scheduleName' },
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalId',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    key: 'modifyTime',

    width: '150px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'modifyTime' },
  },
];
