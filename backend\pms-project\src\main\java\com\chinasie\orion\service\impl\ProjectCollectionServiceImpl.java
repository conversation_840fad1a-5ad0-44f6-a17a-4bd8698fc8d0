package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.domain.dto.ProjectCollectionDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectCollection;
import com.chinasie.orion.domain.entity.ProjectCollectionToProject;
import com.chinasie.orion.domain.vo.ProjectCollectionStatisticsVO;
import com.chinasie.orion.domain.vo.ProjectCollectionTreeVO;
import com.chinasie.orion.domain.vo.ProjectCollectionVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectCollectionMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectCollectionService;
import com.chinasie.orion.service.ProjectCollectionToProjectService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectCollection 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@Service
public class ProjectCollectionServiceImpl extends OrionBaseServiceImpl<ProjectCollectionMapper, ProjectCollection> implements ProjectCollectionService {

    public static final String DICT_PROJECT_COLLECTION_TYPE = "project_collection_type";
    public static final String DICT_PROJECT_CPLLECTION_LEVEL = "project_collection_level";

    @Resource
    private DictBo dictBo;

    @Autowired
    private ProjectCollectionMapper projectCollectionMapper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectCollectionToProjectService toProjectService;


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DataStatusNBO dataStatusNBO;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectCollectionVO detail(String id) throws Exception {
        ProjectCollection projectCollection = projectCollectionMapper.selectById(id);
        ProjectCollectionVO result = BeanCopyUtils.convertTo(projectCollection, ProjectCollectionVO::new);
        Map<String, String> projectCollectionTypeMap = dictBo.getDictValue(DICT_PROJECT_COLLECTION_TYPE);
        Map<String, String> projectCollectionLevelMap = dictBo.getDictValue(DICT_PROJECT_CPLLECTION_LEVEL);

        if (StrUtil.isNotBlank(result.getProjectCollectionType())) {
            result.setProjectCollectionTypeName(projectCollectionTypeMap.get(result.getProjectCollectionType()));
        }
        if (StrUtil.isNotBlank(result.getProjectCollectionLevel())) {
            result.setProjectCollectionLevelName(projectCollectionLevelMap.get(result.getProjectCollectionLevel()));
        }

        List<ProjectCollectionToProject> projectCollectionToProjects = toProjectService.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getFromId, id).eq(ProjectCollectionToProject::getToClass, "Project"));
        if (CollectionUtil.isNotEmpty(projectCollectionToProjects)) {
            List<String> ids = projectCollectionToProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
            List<ProjectVO> projectVOList = projectService.getProjectList(ids);
            result.setProjectVOList(projectVOList);
        }
        if (StrUtil.isNotBlank(result.getRelatedPerson())) {
            String[] names = result.getRelatedPerson().split(",");
            List<UserVO> users = userRedisHelper.getUserByIds(Arrays.asList(names));

            if (CollectionUtil.isNotEmpty(users)) {
                List<Map> relatedPersonList = users.stream().map(item -> {
                            Map map = new HashMap();
                            map.put("id", item.getId());
                            map.put("name", item.getName());
                            return map;
                        }
                ).collect(Collectors.toList());
                result.setRelatedPersonList(relatedPersonList);
            }

        }
        if (StrUtil.isNotBlank(result.getResDept()) && ObjectUtil.isNotEmpty(deptRedisHelper.getDeptById(result.getResDept()))) {
            result.setResDeptName(deptRedisHelper.getDeptById(result.getResDept()).getName());
        }
        if (StrUtil.isNotBlank(result.getResPerson()) && ObjectUtil.isNotEmpty(userRedisHelper.getUserById(result.getResPerson()))) {
            result.setResPersonName(userRedisHelper.getUserById(result.getResPerson()).getName());
        }
        //状态
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(ProjectCollection.class.getSimpleName());
        result.setDataStatus(dataStatusMap.getOrDefault(result.getStatus(),null));

        return result;
    }


    @Override
    public ProjectCollectionVO detailPerson(String id) throws Exception {
        ProjectCollection projectCollection = projectCollectionMapper.selectById(id);
        ProjectCollectionVO result = BeanCopyUtils.convertTo(projectCollection, ProjectCollectionVO::new);
        if (StrUtil.isNotBlank(result.getResDept()) && ObjectUtil.isNotEmpty(deptRedisHelper.getDeptById(result.getResDept()))) {
            result.setResDeptName(deptRedisHelper.getDeptById(result.getResDept()).getName());
        }
        if (StrUtil.isNotBlank(result.getResPerson()) && ObjectUtil.isNotEmpty(userRedisHelper.getUserById(result.getResPerson()))) {
            result.setResPersonName(userRedisHelper.getUserById(result.getResPerson()).getName());
        }
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectCollectionDTO
     */
    @Override
    @Transactional
    public ProjectCollectionVO create(ProjectCollectionDTO projectCollectionDTO) {
        ProjectCollection projectCollection = BeanCopyUtils.convertTo(projectCollectionDTO, ProjectCollection::new);
        int insert = projectCollectionMapper.insert(projectCollection);
        List<String> projectIds = projectCollectionDTO.getProjectIds();
        if (CollectionUtil.isNotEmpty(projectIds)) {
            List<ProjectCollectionToProject> toProjects = new ArrayList<>();
            for (String projectId : projectIds) {
                ProjectCollectionToProject toProject = new ProjectCollectionToProject();
                toProject.setFromId(projectCollection.getId());
                toProject.setToId(projectId);
                toProject.setFromClass("ProjectCollection");
                toProject.setToClass("Project");
                toProjects.add(toProject);
            }
            toProjectService.saveBatch(toProjects);
        }
        ProjectCollectionVO rsp = BeanCopyUtils.convertTo(projectCollection, ProjectCollectionVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectCollectionDTO
     */
    @Override
    @Transactional
    public Boolean edit(ProjectCollectionDTO projectCollectionDTO) {
        ProjectCollection projectCollection = BeanCopyUtils.convertTo(projectCollectionDTO, ProjectCollection::new);
        int update = projectCollectionMapper.updateById(projectCollection);
        List<String> projectIds = projectCollectionDTO.getProjectIds();
        toProjectService.remove(new LambdaQueryWrapper<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getFromId, projectCollectionDTO.getId())
                .eq(ProjectCollectionToProject::getToClass, "Project"));
        if (CollectionUtil.isNotEmpty(projectIds)) {
            List<ProjectCollectionToProject> toProjects = new ArrayList<>();
            for (String projectId : projectIds) {
                ProjectCollectionToProject toProject = new ProjectCollectionToProject();
                toProject.setFromId(projectCollection.getId());
                toProject.setToId(projectId);
                toProject.setFromClass("ProjectCollection");
                toProject.setToClass("Project");
                toProjects.add(toProject);
            }
            toProjectService.saveBatch(toProjects);
        }
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional
    public Boolean remove(List<String> ids) {
        int delete = projectCollectionMapper.deleteBatchIds(ids);
        toProjectService.remove(new LambdaQueryWrapper<>(ProjectCollectionToProject.class)
                .in(ProjectCollectionToProject::getFromId, ids));
        return SqlHelper.retBool(delete);
    }

    @Override
    public Page<ProjectCollectionVO> getPages(Page<ProjectCollectionDTO> pageRequest) throws Exception {
        Page<ProjectCollection> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectCollection::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());

        LambdaQueryWrapperX<ProjectCollection> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectCollection.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectCollectionDTO projectCollectionDTO = pageRequest.getQuery();
            List<ProjectCollectionToProject> toProjects = toProjectService.getAllParents(projectCollectionDTO.getId());
            List<String> childs = toProjects.stream().map(ProjectCollectionToProject::getFromId).collect(Collectors.toList());
            childs.add(projectCollectionDTO.getId());
            if (CollectionUtil.isNotEmpty(childs)) {
                lambdaQueryWrapperX.notIn(ProjectCollection::getId, childs);
            }
        }
        lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapperX);
        PageResult<ProjectCollection> page = projectCollectionMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectCollectionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (CollectionUtil.isEmpty(page.getContent())) {
            return pageResult;
        }
        List<ProjectCollectionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectCollectionVO::new);
        pageResult.setContent(vos);
        return pageResult;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectCollectionStatisticsVO> pages(Page<ProjectCollectionDTO> pageRequest) throws Exception {
        Page<ProjectCollection> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectCollection::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());

        LambdaQueryWrapperX<ProjectCollection> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectCollection.class);
        lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapperX);

        lambdaQueryWrapperX.and(wrapper -> wrapper
                .like(ProjectCollection::getResPerson, CurrentUserHelper.getCurrentUserId())
                .or()
                .like(ProjectCollection::getRelatedPerson, CurrentUserHelper.getCurrentUserId())
                .or()
                .like(ProjectCollection::getCreatorId, CurrentUserHelper.getCurrentUserId())
        );
        lambdaQueryWrapperX.orderByDesc(ProjectCollection::getCreateTime);
        PageResult<ProjectCollection> page = projectCollectionMapper.selectPage(realPageRequest, lambdaQueryWrapperX);

        Page<ProjectCollectionStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (CollectionUtil.isEmpty(page.getContent())) {
            return pageResult;
        }
        List<ProjectCollection> vos = page.getContent();
        if (CollectionUtil.isEmpty(vos)) {
            return pageResult;
        }
        List<ProjectCollectionStatisticsVO> result = BeanCopyUtils.convertListTo(vos, ProjectCollectionStatisticsVO::new);

        List<String> ids = result.stream().map(ProjectCollectionStatisticsVO::getId).collect(Collectors.toList());
        List<ProjectCollectionToProject> toProjects = toProjectService.getAllChildNew(ids, null, new ArrayList<>());
        Map<String, List<String>> map = toProjects.stream().collect(Collectors.groupingBy(ProjectCollectionToProject::getFromId,
                Collectors.mapping(ProjectCollectionToProject::getToId, Collectors.toList())));
        Map<String, List<String>> firstChildMap = new HashMap<>();
        toProjectService.getTree(ids, map, firstChildMap);
        List<String> child = toProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
        child.addAll(ids);
        List<ProjectCollectionToProject> list = toProjectService.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getToClass, "Project")
                .in(ProjectCollectionToProject::getFromId, child));

        if (CollectionUtil.isNotEmpty(list)) {
            List<String> projectAllIds = list.stream().distinct().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
            List<ProjectCollectionStatisticsVO> planStatisticsVO = projectCollectionMapper.getPlan("", projectAllIds);
            List<ProjectCollectionStatisticsVO> budgetStatisticsVO = projectCollectionMapper.getbudgetSumMoney(projectAllIds);
            List<Project> projectList = projectService.listByIds(projectAllIds);

            Map<String, String> projectCollectionTypeMap = dictBo.getDictValue(DICT_PROJECT_COLLECTION_TYPE);
            Map<String, String> projectCollectionLevelMap = dictBo.getDictValue(DICT_PROJECT_CPLLECTION_LEVEL);

            Map<String, ProjectCollectionStatisticsVO> planStatisticsVOMap = planStatisticsVO.stream().collect(Collectors.toMap(ProjectCollectionStatisticsVO::getId, p -> p));
            Map<String, ProjectCollectionStatisticsVO> budgetStatisticsVOMap = budgetStatisticsVO.stream().collect(Collectors.toMap(ProjectCollectionStatisticsVO::getId, p -> p));
            Map<String, Project> projectMap = projectList.stream().collect(Collectors.toMap(Project::getId, p -> p));
            Map<String, List<String>> collectionToProjectMap = list.stream()
                    .collect(Collectors.groupingBy(ProjectCollectionToProject::getFromId, Collectors.mapping(ProjectCollectionToProject::getToId, Collectors.toList())));
            //状态
            Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(ProjectCollection.class.getSimpleName());

            //责任人
            List<String> userResPerson = result.stream().map(ProjectCollectionStatisticsVO::getResPerson).collect(Collectors.toList());
            Map<String, String> userResPersonMap =new HashMap<>();
            if (ObjectUtil.isNotEmpty(userResPerson)){
                List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(userResPerson);
                userResPersonMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
            }


            for (ProjectCollectionStatisticsVO projectCollectionStatisticsVO : result) {
                if (ObjectUtil.isNotEmpty(projectCollectionStatisticsVO.getStatus())){
                    projectCollectionStatisticsVO.setDataStatus(dataStatusMap.getOrDefault(projectCollectionStatisticsVO.getStatus(),null));
                }
                if (ObjectUtil.isNotEmpty(projectCollectionStatisticsVO.getResPerson())){
                    projectCollectionStatisticsVO.setResPersonName(userResPersonMap.getOrDefault(projectCollectionStatisticsVO.getResPerson(),""));
                }


                if (StrUtil.isNotBlank(projectCollectionStatisticsVO.getProjectCollectionType())) {
                    projectCollectionStatisticsVO.setProjectCollectionTypeName(projectCollectionTypeMap.get(projectCollectionStatisticsVO.getProjectCollectionType()));
                }
                if (StrUtil.isNotBlank(projectCollectionStatisticsVO.getProjectCollectionLevel())) {
                    projectCollectionStatisticsVO.setProjectCollectionLevelName(projectCollectionLevelMap.get(projectCollectionStatisticsVO.getProjectCollectionLevel()));
                }
                List<String> projectIds = firstChildMap.getOrDefault(projectCollectionStatisticsVO.getId(), new ArrayList<>())
                        .stream().map(collectionToProjectMap::get).filter(ObjectUtil::isNotNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(projectIds)) {
                    projectIds.forEach(id -> {
                        if (ObjectUtil.isNotEmpty(planStatisticsVOMap.get(id))) {
                            projectCollectionStatisticsVO.setTotalCount(projectCollectionStatisticsVO.getTotalCount() + planStatisticsVOMap.get(id).getTotalCount());
                            projectCollectionStatisticsVO.setFinishedCount(projectCollectionStatisticsVO.getFinishedCount() + planStatisticsVOMap.get(id).getFinishedCount());
                        }
                        if (ObjectUtil.isNotEmpty(budgetStatisticsVOMap.get(id))) {
                            projectCollectionStatisticsVO.setBudgetSumMoney(projectCollectionStatisticsVO.getBudgetSumMoney().add(budgetStatisticsVOMap.get(id).getBudgetSumMoney()));
                            projectCollectionStatisticsVO.setExecuteSumMoney(projectCollectionStatisticsVO.getExecuteSumMoney().add(budgetStatisticsVOMap.get(id).getExecuteSumMoney()));
                        }
                        if (ObjectUtil.isNotEmpty(projectMap.get(id))) {
                            Project project = projectMap.get(id);
                            projectCollectionStatisticsVO.setProjectTotalCount(projectCollectionStatisticsVO.getProjectTotalCount() + 1);
                            if (ProjectStatusEnum.ACCEPTED.getStatus().equals(project.getStatus())) {
                                projectCollectionStatisticsVO.setProjectFinishedCount(projectCollectionStatisticsVO.getProjectFinishedCount() + 1);
                            }
                        }
                    });
                    if (projectCollectionStatisticsVO.getBudgetSumMoney().compareTo(BigDecimal.ZERO) > 0) {
                        projectCollectionStatisticsVO.setImplementation(projectCollectionStatisticsVO.getExecuteSumMoney().
                                divide(projectCollectionStatisticsVO.getBudgetSumMoney(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                    }
                }
                //计算项目集的进度
                if (projectCollectionStatisticsVO.getFinishedCount() != 0
                        && projectCollectionStatisticsVO.getTotalCount() != 0) {
                    Double schedule = (double) (projectCollectionStatisticsVO.getFinishedCount()) / projectCollectionStatisticsVO.getTotalCount() * 100;
                    String formattedSchedule;
                    if (schedule % 1 == 0) {
                        formattedSchedule = String.format("%.0f", schedule);
                    } else {
                        DecimalFormat decimalFormat = new DecimalFormat("0.0");
                        formattedSchedule = decimalFormat.format(schedule);
                    }
                    projectCollectionStatisticsVO.setSchedule(formattedSchedule);
                } else {
                    projectCollectionStatisticsVO.setSchedule("0");
                }
            }
        }
        pageResult.setContent(result);
        return pageResult;
    }

    @Override
    public List<ProjectCollectionTreeVO> tree(String id, ProjectCollectionDTO projectCollectionDTO) throws Exception {
        List<ProjectCollectionToProject> toProjects = toProjectService.getAllChildNew(Arrays.asList(id), null, new ArrayList<>());
        List<String> childs = toProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
        List<ProjectCollectionTreeVO> projectCollectionTreeVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(childs)) {
            List<ProjectCollection> collections = this.listByIds(childs);
            List<ProjectCollectionVO> ProjectCollectionVOs = BeanCopyUtils.convertListTo(collections, ProjectCollectionVO::new);
            Map<String, ProjectCollectionVO> collectionVOMap = ProjectCollectionVOs.stream().collect(Collectors.toMap(ProjectCollectionVO::getId, p -> p));
            for (ProjectCollectionToProject collectionToProject : toProjects) {
                if (ObjectUtil.isNotEmpty(collectionVOMap.get(collectionToProject.getToId()))) {
                    ProjectCollectionVO projectCollectionVO = collectionVOMap.get(collectionToProject.getToId());
                    ProjectCollectionTreeVO projectCollectionTreeVO = BeanCopyUtils.convertTo(projectCollectionVO, ProjectCollectionTreeVO::new);
                    projectCollectionTreeVO.setParentId(collectionToProject.getFromId());
                    projectCollectionTreeVOS.add(projectCollectionTreeVO);
                }
            }
        }
        childs.add(id);
        List<ProjectCollectionToProject> childProject = toProjectService.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .in(ProjectCollectionToProject::getFromId, childs).eq(ProjectCollectionToProject::getToClass, "Project"));
        if (CollectionUtil.isNotEmpty(childProject)) {
            List<String> projectIds = childProject.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
            List<Project> projects = projectService.listByIds(projectIds);
            List<ProjectVO> projectVOS = BeanCopyUtils.convertListTo(projects, ProjectVO::new);
            Map<String, ProjectVO> projectVOMap = projectVOS.stream().collect(Collectors.toMap(ProjectVO::getId, p -> p));
            for (ProjectCollectionToProject collectionToProject : childProject) {
                if (ObjectUtil.isNotEmpty(projectVOMap.get(collectionToProject.getToId()))) {
                    ProjectVO projectVO = projectVOMap.get(collectionToProject.getToId());
                    ProjectCollectionTreeVO projectCollectionTreeVO = BeanCopyUtils.convertTo(projectVO, ProjectCollectionTreeVO::new);
                    projectCollectionTreeVO.setParentId(collectionToProject.getFromId());
                    projectCollectionTreeVOS.add(projectCollectionTreeVO);
                }
            }
        }
        Map<String, List<ProjectCollectionTreeVO>> map = projectCollectionTreeVOS.stream().collect(Collectors.groupingBy(ProjectCollectionTreeVO::getParentId));
        List<ProjectCollectionTreeVO> firstProjectCollection = map.get(id);
        if (CollectionUtil.isEmpty(firstProjectCollection)) {
            return new ArrayList<>();
        }

//        firstProjectCollection.forEach(item->{
//            item.setTreeId(IdUtil.randomUUID());
//        });
        getTrees(firstProjectCollection, map, 1000);
        List<ProjectCollectionTreeVO> result = search(firstProjectCollection, vo -> {
            boolean searched = StrUtil.isBlank(projectCollectionDTO.getName()) || ((StrUtil.isNotBlank(vo.getName()) && vo.getName().contains(projectCollectionDTO.getName())));
            return searched;
        });

        //状态
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(ProjectCollection.class.getSimpleName());
        for (ProjectCollectionTreeVO projectCollectionStatisticsVO : result) {
            if (ObjectUtil.isNotEmpty(projectCollectionStatisticsVO.getStatus())) {
                projectCollectionStatisticsVO.setDataStatus(dataStatusMap.getOrDefault(projectCollectionStatisticsVO.getStatus(), null));
            }
        }
        return result;
    }


    @Override
    public List<ProjectCollectionTreeVO> treeStatistics(String id, ProjectCollectionDTO projectCollectionDTO) throws Exception {
        List<ProjectCollectionToProject> toProjects = toProjectService.getAllChildNew(Arrays.asList(id), null, new ArrayList<>());
        toProjects = toProjects.stream()
                .collect(Collectors.toMap(ProjectCollectionToProject::getId, u -> u, (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());
        List<String> childs = toProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
        List<ProjectCollectionTreeVO> projectCollectionTreeVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(childs)) {
            List<ProjectCollection> collections = this.listByIds(childs);
            List<ProjectCollectionVO> ProjectCollectionVOs = BeanCopyUtils.convertListTo(collections, ProjectCollectionVO::new);
            Map<String, ProjectCollectionVO> collectionVOMap = ProjectCollectionVOs.stream().collect(Collectors.toMap(ProjectCollectionVO::getId, p -> p));
            for (ProjectCollectionToProject collectionToProject : toProjects) {
                if (ObjectUtil.isNotEmpty(collectionVOMap.get(collectionToProject.getToId()))) {
                    ProjectCollectionVO projectCollectionVO = collectionVOMap.get(collectionToProject.getToId());
                    ProjectCollectionTreeVO projectCollectionTreeVO = BeanCopyUtils.convertTo(projectCollectionVO, ProjectCollectionTreeVO::new);
                    projectCollectionTreeVO.setParentId(collectionToProject.getFromId());
                    projectCollectionTreeVO.setIsCollection("1");
                    projectCollectionTreeVOS.add(projectCollectionTreeVO);
                }
            }
        }
        childs.add(id);
        //项目信息赋值
        List<ProjectCollectionToProject> childProject = toProjectService.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .in(ProjectCollectionToProject::getFromId, childs).eq(ProjectCollectionToProject::getToClass, "Project"));
        if (CollectionUtil.isNotEmpty(childProject)) {
            List<String> projectIds = childProject.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
            //项目集赋值
            List<Project> projects = projectService.listByIds(projectIds);
            List<ProjectVO> projectVOS = BeanCopyUtils.convertListTo(projects, ProjectVO::new);
            Map<String, ProjectVO> projectVOMap = projectVOS.stream().collect(Collectors.toMap(ProjectVO::getId, p -> p));
            //统计项目问题数据
            List<ProjectCollectionStatisticsVO> questionStatisticsVOS = projectCollectionMapper.getQuestion(projectIds);
            Map<String, ProjectCollectionStatisticsVO> questionMap = questionStatisticsVOS.stream().collect(Collectors.toMap(ProjectCollectionStatisticsVO::getId, p -> p));

            //统计项目里程碑数据
            List<ProjectCollectionStatisticsVO> milestoneStatisticsVOS = projectCollectionMapper.getPlan("milestone", projectIds);
            Map<String, ProjectCollectionStatisticsVO> milestoneMap = milestoneStatisticsVOS.stream().collect(Collectors.toMap(ProjectCollectionStatisticsVO::getId, p -> p));
            for (ProjectCollectionToProject collectionToProject : childProject) {
                if (ObjectUtil.isNotEmpty(projectVOMap.get(collectionToProject.getToId()))) {
                    ProjectVO projectVO = projectVOMap.get(collectionToProject.getToId());
                    ProjectCollectionTreeVO projectCollectionTreeVO = BeanCopyUtils.convertTo(projectVO, ProjectCollectionTreeVO::new);
                    projectCollectionTreeVO.setParentId(collectionToProject.getFromId());
                    projectCollectionTreeVO.setIsCollection("0");
                    //项目的
                    if (ObjectUtil.isNotEmpty(questionMap.get(collectionToProject.getToId()))) {
                        ProjectCollectionStatisticsVO projectCollectionStatisticsVO = questionMap.get(collectionToProject.getToId());
                        projectCollectionTreeVO.setQuestionTotalCount(projectCollectionStatisticsVO.getQuestionTotalCount());
                        projectCollectionTreeVO.setQuestionFinishedCount(projectCollectionStatisticsVO.getQuestionFinishedCount());
                        projectCollectionTreeVO.setQuestionNOFinishedCount(projectCollectionStatisticsVO.getQuestionNOFinishedCount());
                    }
                    if (ObjectUtil.isNotEmpty(milestoneMap.get(collectionToProject.getToId()))) {
                        ProjectCollectionStatisticsVO projectCollectionStatisticsVO = milestoneMap.get(collectionToProject.getToId());
                        projectCollectionTreeVO.setTotalCount(projectCollectionStatisticsVO.getTotalCount());
                        projectCollectionTreeVO.setFinishedCount(projectCollectionStatisticsVO.getFinishedCount());
                        projectCollectionTreeVO.setNoFinishedCount(projectCollectionStatisticsVO.getNoFinishedCount());
                    }
                    projectCollectionTreeVOS.add(projectCollectionTreeVO);
                }
            }
        }
        Map<String, List<ProjectCollectionTreeVO>> map = projectCollectionTreeVOS.stream().collect(Collectors.groupingBy(ProjectCollectionTreeVO::getParentId));
        List<ProjectCollectionTreeVO> firstProjectCollection = map.get(id);
        if (CollectionUtil.isEmpty(firstProjectCollection)) {
            return new ArrayList<>();
        }
//        firstProjectCollection.forEach(item->{
//            item.setTreeId(IdUtil.randomUUID());
//        });
        //树形排列数据
        getTree(firstProjectCollection, map, 1000);

        List<ProjectCollectionTreeVO> result = search(firstProjectCollection, vo -> {
            boolean searched = StrUtil.isBlank(projectCollectionDTO.getName()) || ((StrUtil.isNotBlank(vo.getName()) && vo.getName().contains(projectCollectionDTO.getName())));
            return searched;
        });
        for (ProjectCollectionTreeVO vo : result) {
            if ("Project".equals(vo.getClassName())) {
                buildSchedule(vo);
            } else {
                //计算项目集的进度
                calculateSum(vo);
            }
        }

        //责任人
        List<String> userResPerson = result.stream().map(ProjectCollectionTreeVO::getResPerson).collect(Collectors.toList());
        Map<String, String> userResPersonMap =new HashMap<>();
        if (ObjectUtil.isNotEmpty(userResPerson)){
            List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(userResPerson);
            userResPersonMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        }
        //状态
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusNBO.getDataStatusMapByClassName(ProjectCollection.class.getSimpleName());
        for (ProjectCollectionTreeVO projectCollectionStatisticsVO : result) {
            if (ObjectUtil.isNotEmpty(projectCollectionStatisticsVO.getStatus())) {
                projectCollectionStatisticsVO.setDataStatus(dataStatusMap.getOrDefault(projectCollectionStatisticsVO.getStatus(), null));
            }
            if (ObjectUtil.isNotEmpty(projectCollectionStatisticsVO.getResPerson())){
                projectCollectionStatisticsVO.setResPersonName(userResPersonMap.getOrDefault(projectCollectionStatisticsVO.getResPerson(),""));
            }
        }
        return result;
    }

    private void buildSchedule(ProjectCollectionTreeVO vo) {
        if (vo.getFinishedCount() != 0
                && vo.getTotalCount() != 0) {
            Double schedule = (double) (vo.getFinishedCount()) / vo.getTotalCount() * 100;
            String formattedSchedule;
            if (schedule % 1 == 0) {
                formattedSchedule = String.format("%.0f", schedule);
            } else {
                DecimalFormat decimalFormat = new DecimalFormat("0.0");
                formattedSchedule = decimalFormat.format(schedule);
            }
            vo.setSchedule(formattedSchedule);
        } else {
            vo.setSchedule("0");
        }
    }


    public void calculateSum(ProjectCollectionTreeVO node) {
        List<ProjectCollectionTreeVO> children = node.getChildren();

        if (CollectionUtil.isEmpty(children)) {
            // 当前节点的子节点为空数组
            buildSchedule(node);
            return;
        }
        //Integer schedule=0;
        Integer questionTotalCount = 0;
        Integer questionFinishedCount = 0;
        Integer questionNOFinishedCount = 0;
        Integer totalCount = 0;
        Integer finishedCount = 0;
        Integer noFinishedCount = 0;
        for (ProjectCollectionTreeVO child : children) {
            calculateSum(child);
            // 将子节点的 number 加到相应的变量中
            questionTotalCount += child.getQuestionTotalCount();
            questionFinishedCount += child.getQuestionFinishedCount();
            questionNOFinishedCount += child.getQuestionNOFinishedCount();
            totalCount += child.getTotalCount();
            finishedCount += child.getFinishedCount();
            noFinishedCount += child.getNoFinishedCount();
        }
        buildSchedule(node);
        node.setQuestionTotalCount(questionTotalCount);
        node.setQuestionFinishedCount(questionFinishedCount);
        node.setQuestionNOFinishedCount(questionNOFinishedCount);
        node.setTotalCount(totalCount);
        node.setFinishedCount(finishedCount);
        node.setNoFinishedCount(noFinishedCount);
    }


    public List<ProjectCollectionTreeVO> getTree(List<ProjectCollectionTreeVO> projectCollectionTreeVOS, Map<String, List<ProjectCollectionTreeVO>> map, int maxCircul) {
        //判断死循环
        if (maxCircul == 0) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据存在问题");
        }
        List<ProjectCollectionTreeVO> m = new ArrayList<>();
        for (ProjectCollectionTreeVO projectCollectionTreeVO : projectCollectionTreeVOS) {
            if (CollectionUtil.isNotEmpty(map.get(projectCollectionTreeVO.getId()))) {
                List<ProjectCollectionTreeVO> list = new ArrayList<>(map.get(projectCollectionTreeVO.getId()));
//                list.forEach(item -> {
//                    item.setTreeId(IdUtil.randomUUID());
//                });
                projectCollectionTreeVO.setChildren(list);
                List<ProjectCollectionTreeVO> child = getTree(list, map, maxCircul - 1);
                m.addAll(child);
                getTotal(projectCollectionTreeVO, child);
            }
            m.add(projectCollectionTreeVO);
        }
        return m;
    }

    public void getTotal(ProjectCollectionTreeVO projectCollectionTreeVO, List<ProjectCollectionTreeVO> list) {

        //去掉重复数据
        List<ProjectCollectionTreeVO> projectCollectionTreeVOS = list.stream()
                .collect(Collectors.toMap(ProjectCollectionTreeVO::getId, u -> u, (existing, replacement) -> existing))
                .values()
                .stream()
                .filter(item -> item.getIsCollection().equals("0"))
                .collect(Collectors.toList());

        for (ProjectCollectionTreeVO projectCollectionTreeVO1 : projectCollectionTreeVOS) {
            projectCollectionTreeVO.setTotalCount(projectCollectionTreeVO.getTotalCount() + projectCollectionTreeVO1.getTotalCount());
            projectCollectionTreeVO.setNoFinishedCount(projectCollectionTreeVO.getNoFinishedCount() + projectCollectionTreeVO1.getNoFinishedCount());
            projectCollectionTreeVO.setQuestionNOFinishedCount(projectCollectionTreeVO.getQuestionNOFinishedCount() + projectCollectionTreeVO1.getQuestionNOFinishedCount());
            projectCollectionTreeVO.setQuestionTotalCount(projectCollectionTreeVO.getQuestionTotalCount() + projectCollectionTreeVO1.getQuestionTotalCount());
        }
    }


    public void getTrees(List<ProjectCollectionTreeVO> projectCollectionTreeVOS, Map<String, List<ProjectCollectionTreeVO>> map, int maxCircul) {
        if (maxCircul == 0) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据存在问题");
        }
        for (ProjectCollectionTreeVO projectCollectionTreeVO : projectCollectionTreeVOS) {
            if (CollectionUtil.isNotEmpty(map.get(projectCollectionTreeVO.getId()))) {
                List<ProjectCollectionTreeVO> list = new ArrayList<>(map.get(projectCollectionTreeVO.getId()));
//                list.forEach(item -> {
//                    item.setTreeId(IdUtil.randomUUID());
//                });
                projectCollectionTreeVO.setChildren(list);
                getTrees(list, map, maxCircul - 1);
            }
        }
    }

    //搜索
    public List<ProjectCollectionTreeVO> search(List<ProjectCollectionTreeVO> source, Predicate<ProjectCollectionTreeVO> predicate) {
        List<ProjectCollectionTreeVO> target = new ArrayList<>();
        source.forEach(o -> {
            List<ProjectCollectionTreeVO> child = o.getChildren();
            if (Objects.nonNull(child) && !child.isEmpty()) {
                List<ProjectCollectionTreeVO> searched = search(child, predicate);
                if (!searched.isEmpty()) {
                    o.setChildren(searched);
                    target.add(o);
                } else {
                    if (predicate.test(o)) {
                        target.add(o);
                    }
                }
            } else {
                if (predicate.test(o)) {
                    target.add(o);
                }
            }
        });
        return target;
    }


    @Override
    @Transactional
    public Boolean createProjectCollection(ProjectCollectionDTO projectCollectionDTO) {
        List<String> ids = projectCollectionDTO.getProjectCollectionIds();
        List<ProjectCollectionToProject> projectCollectionToProjects = toProjectService.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getFromId, projectCollectionDTO.getId())
                .eq(ProjectCollectionToProject::getToClass, "ProjectCollection"));
        List<ProjectCollectionToProject> toProjectParents = toProjectService.getAllParents(projectCollectionDTO.getId());
        List<String> childs = toProjectParents.stream().map(ProjectCollectionToProject::getFromId).collect(Collectors.toList());

        List<String> projectCollectionIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(projectCollectionToProjects)) {
            projectCollectionIds = projectCollectionToProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
        }
        List<ProjectCollectionToProject> toProjects = new ArrayList<>();
        for (String id : ids) {
            if (childs.contains(id)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "数据存在嵌套问题");
            }
            if (!projectCollectionIds.contains(id)) {
                ProjectCollectionToProject toProject = new ProjectCollectionToProject();
                toProject.setFromId(projectCollectionDTO.getId());
                toProject.setToId(id);
                toProject.setFromClass("ProjectCollection");
                toProject.setToClass("ProjectCollection");
                toProjects.add(toProject);
            }
        }
        toProjectService.saveBatch(toProjects);
        return true;
    }

    @Override
    @Transactional
    public Boolean removeProjectCollection(String parentId, String id) throws Exception {
        Boolean rsp = toProjectService.remove(new LambdaQueryWrapper<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getFromId, parentId)
                .eq(ProjectCollectionToProject::getToId, id));
        return rsp;
    }


}
