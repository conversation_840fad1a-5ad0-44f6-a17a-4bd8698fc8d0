<script setup lang="ts">
import {
  computed, onBeforeMount, onMounted, provide, ref, Ref, unref,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  BasicButton, isPower, Layout3, Layout3Content,
} from 'lyra-component-vue3';
import { Spin } from 'ant-design-vue';
import BasicInfo from '../components/BasicInfo.vue';
import Attachments from '../components/Attachments.vue';
import Api from '/@/api';
import DetailWeekly from '../components/DetailWeekly.vue';
interface MenuItem {
  id: string,
  name: string,
  children?: MenuItem[]
}

const route = useRoute();
const basicInfo = ref({});
const loading: Ref<boolean> = ref(false);
const layoutData = computed(() => ({
  name: basicInfo.value?.name ?? '--',
  ownerName: basicInfo.value?.applicantUser ?? '--',
  projectCode: `申请单编码：${basicInfo.value?.projectCode ?? '--'}`,
}));
provide('projectApplicationItem', basicInfo);
const defaultActionId: Ref<string> = ref('jBXX');
const powerData = ref();
const menuData: Ref<any[]> = computed(() => [
  {
    id: 'jBXX',
    name: '基本信息',
    code: 'PMS_CGLXXQXQ_container_02_01',
  },
  {
    id: 'Fj',
    name: '附件',
    code: 'PMS_CGLXXQXQ_container_02_02',
  },
  {
    id: 'weekly',
    name: '周报',
    code: 'PMS_CGLXXQXQ_container_02_03',
    hidden: computed(() => basicInfo.value.isEcpRecord),
  },
].filter((item) => (isPower(item.code, powerData.value)) && !item.hidden?.value));
const projectId: Ref<string> = ref(route.params.id as string);

provide('powerData', powerData);

const getPowerDataHandle = async (data?: any) => {
  powerData.value = data;
};
function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}
onBeforeMount(async () => {
  await getBasicInfo();
});
const getBasicInfo = async () => {
  try {
    const result = await new Api('/spm/ncfFormpurchaseRequest').fetch('', unref(projectId), 'GET');
    basicInfo.value = result;
  } catch (e) {

  }
};
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'projectApplicationItem',getPowerDataHandle}"
    class="purchase-manage-layout"
    :projectData="layoutData"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
    :onMenuChange="menuChange"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <BasicInfo
        v-if="defaultActionId==='jBXX'"
      />
      <Attachments
        v-if="defaultActionId==='Fj'"
      />
      <DetailWeekly
        v-if="defaultActionId==='weekly'"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  min-height: 60px;
  height: auto;
  .header-main{
    min-height: 33px;
    height: auto;
  }
  .project-title {
    width: auto !important;
    max-width: 800px !important;
    min-width: 300px;
    .flex-te{
      word-wrap: break-word;
      overflow-wrap: break-word;
      white-space: normal;
      line-height: 26px;
      padding-top: 10px;
    }
  }
}
.purchase-manage-layout{
  :deep(.ant-menu-overflow){
    min-width: 200px;
  }
}
</style>