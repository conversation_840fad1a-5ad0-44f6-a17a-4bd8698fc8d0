<script setup lang="ts">
import { BasicButton } from 'lyra-component-vue3';
import {
  computed, CSSProperties, reactive, ref, Ref, watch,
} from 'vue';
import { RadioButton, RadioGroup } from 'ant-design-vue';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';
import YearPicker from './YearPicker.vue';
import CalendarTable from './CalendarTable.vue';
import Overview from './Overview.vue';
import { openMajorRepairsForm } from '/@/views/pms/majorRepairs/utils';
import Api from '/@/api';

interface LegendItem {
  label: string
  color: string
  field: string
  value?: number
}

function getColor(backgroundColor: string): CSSProperties {
  return {
    width: '20px',
    height: '20px',
    backgroundColor,
  };
}

const radioType: Ref<string> = ref('calendar');
const radios = [
  {
    label: '日历',
    value: 'calendar',
  },
  {
    label: '概况',
    value: 'overview',
  },
];

const years: Ref<number[]> = ref([dayjs().year(), dayjs().add(1, 'year').year()]);

// 日历概况数据相关
const data: Record<string, any> = reactive({});
const loading: Ref<boolean> = ref(false);

async function getStatistic() {
  loading.value = true;
  try {
    const result = await new Api('/pms/majorRepairStatistic/getMajorRepair').fetch({
      statisticBeginTime: dayjs(years.value[0].toString()).startOf('year').format('YYYY-MM-DD'),
      statisticEndTime: dayjs(years.value[1].toString()).endOf('year').format('YYYY-MM-DD'),
    }, '', 'POST');
    const list = result?.majorRepairPlanBoardDTOList?.map((item) => {
      switch (item.majorStatus) {
        case 160:
          item.sort = 3;
          break;
        case 110:
          item.sort = 1;
          break;
        case 121:
          item.sort = 2;
          break;
      }
      return item;
    })?.sort((a, b) => a.sort - b.sort);

    Object.assign(data, {
      ...result,
      list,
    });
  } finally {
    loading.value = false;
  }
}

const debounced = debounce(getStatistic, 300);
watch(() => years.value, () => {
  debounced.cancel();
  debounced();
}, {
  deep: true,
  immediate: true,
});

const legend: Ref<LegendItem[]> = computed(() => [
  {
    label: '已完成的大修',
    color: '#85BD19',
    field: '160',
  },
  {
    label: '正在进行的大修',
    color: '#00A1E5',
    field: '110',
  },
  {
    label: '正在准备的大修',
    color: '#FFD600',
    field: '121',
  },
].map((item) => ({
  ...item,
  value: data?.statisticMap?.[item.field] || 0,
})));

function updateTable() {
  getStatistic();
}
</script>

<template>
  <div class="header-container">
    <BasicButton
      v-is-power="['PMS_DXGLNEW_container_01_button_01']"
      type="primary"
      icon="sie-icon-tianjiaxinzeng"
      @click="openMajorRepairsForm(null, updateTable);"
    >
      新增大修
    </BasicButton>
    <div class="legend">
      <div
        v-for="(item,index) in legend"
        :key="index"
      >
        <span :style="getColor(item.color)" />
        <span>{{ item.label }}({{ item.value }})</span>
      </div>
    </div>

    <RadioGroup
      v-model:value="radioType"
      style="margin-left: auto"
      button-style="solid"
    >
      <RadioButton
        v-for="(item,index) in radios"
        :key="index"
        :value="item.value"
      >
        {{ item.label }}
      </RadioButton>
    </RadioGroup>

    <YearPicker
      v-model:years="years"
      class="ml20"
    />
  </div>
  <CalendarTable
    v-if="radioType==='calendar'"
    :years="years"
    :data="data"
    :loading="loading"
  />
  <Overview
    v-if="radioType==='overview'"
    :years="years"
    :data="data"
    :loading="loading"
  />
</template>

<style scoped lang="less">
.header-container {
  display: flex;
  align-items: center;
  padding: ~`getPrefixVar('content-padding-top')` 0;

  .legend {
    display: flex;
    align-items: center;

    > div {
      display: flex;
      align-items: center;

      & + div {
        margin-left: ~`getPrefixVar('content-margin')`;
      }

      span + span {
        margin-left: ~`getPrefixVar('icon-margin')`;
      }
    }
  }
}

</style>
