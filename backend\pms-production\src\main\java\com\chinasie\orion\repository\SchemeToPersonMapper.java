package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.excel.PersonPlanVO;
import com.chinasie.orion.domain.entity.SchemeToPerson;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * SchemeToPerson Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@Mapper
public interface SchemeToPersonMapper extends  OrionBaseMapper  <SchemeToPerson> {


    @Select({"<script>"," select user_code from pmsx_scheme_to_person where repair_round=#{repairRound} and logic_status=1 ","</script>"})
    List<String> getUserCodeListByRepairRound(@Param("repairRound") String repairRound);


    @Select({"<script>"," select user_code from pmsx_scheme_to_person where repair_round=#{repairRound} and logic_status=1 and is_have_project=1 ","</script>"})
    List<String> getUserCodeListByRepairRoundAndHaveProject(@Param("repairRound") String repairRound);


    List<SchemeToPerson> getUserListByRepairRound(@Param("repairRound") String repairRound,@Param("keyword") String keyword);


    List<PersonPlanVO> listByRepairRound(@Param("repairRound") String repairRound);




    /**
     * 查找入场人员数据
     *
     * @param repairRound 大修轮次
     * @param status      人员状态
     * @return
     */
    @Select({"<script>"," SELECT\n" +
            "\taa.id,\n" +
            "\taa.user_code ,\n" +
            "\taa.user_name,\n" +
            "\tbb.in_date,\n" +
            "\tbb.out_date\n" +
            "FROM\n" +
            "\tpmsx_scheme_to_person aa\n" +
            "\tLEFT JOIN pmsx_person_mange bb ON aa.person_id = bb.id \n" +
            "WHERE\n" +
            "\taa.repair_round = #{repairRound} \n" +
            "\tAND aa.logic_status = 1 \n" +
            "\tAND bb.logic_status = 1 \n" +
            "\tAND bb.STATUS = #{status}","</script>"})
    List<SchemeToPersonVO> getAdmissionList(@Param("repairRound") String repairRound, @Param("status") int status);

    Integer countPage(@Param("repairRound") String repairRound,@Param("keyword") String keyword);

    List<SchemeToPerson> pageList(@Param("repairRound")String repairRound,@Param("keyword") String keyword,@Param("pageNum") long pageNum,@Param("pageSize") long pageSize);

    List<SchemeToPersonVO> getListByPersonIdList(@Param("repairRound")String repairRound,@Param("personIdList") List<String> personIdList);
}

