// ************************************************ //
// ******************计划管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';
import { downloadByData as basicDownloadByData } from 'lyra-component-vue3';

/**
 * 获取计划管理分页列表
 * @param params
 */
export async function getPlanPersonPage(params: any) {
  return new Api('/plan/scheme/scheme-shell').fetch(params, '', 'POST');
}

/**
 * 获取计划详情的计划树
 * @param id
 */
export async function getPlanTree(id:string) {
  return new Api('/plan/scheme/tree').fetch({}, id, 'POST');
}

let businessOrganizationList;
/**
 * 获取业务组织列表
 */
export async function getBusinessOrganizationList() {
  if (businessOrganizationList) {
    return businessOrganizationList;
  }
  return new Api('/pmi/business-organization/list').fetch('', '', 'GET').then((data) => {
    businessOrganizationList = data;
    return data;
  });
}

const planDictValue = {};
/**
 * 获取计划对应的字典
 * @param code
 */
export async function getPlanDict(code: string): Promise<{id:string, description: string}[]> {
  if (!code) return [];
  if (planDictValue[code]) {
    return planDictValue[code];
  }
  return new Api('/plan/dict/code').fetch('', code, 'GET').then((data:any) => {
    planDictValue[code] = data;
    return data;
  });
}

/**
 * 根据单位ID获取部门列表
 * @param orgIds 单位ID 数组
 */
export function getDeptDataFromOrgId(orgIds: string[]) {
  return new Api('/pmi/organization/business/org/list').fetch(orgIds, '', 'POST');
}

/**
 * 根据部门ID获取用户列表
 * @param deptIds
 */
export function getUsersFromDeptId(deptIds: string[]) {
  return new Api('/pmi/user/org/ids').fetch(deptIds, '', 'POST');
}

/**
 * 计划新增和编辑
 * @param type
 * @param params
 */
export function submitAddOrEditPlan(type: 'add' | 'edit', params: any) {
  return new Api('/plan/scheme').fetch(params, '', type === 'add' ? 'POST' : 'PUT');
}

/**
 * 删除一个计划
 * @param ids
 */
export function deletePlan(ids: string[]) {
  return new Api('/plan/scheme').fetch(ids, '', 'DELETE');
}

/**
 * 获取计划基础信息
 * @param planId
 */
export function getPlanDetail(planId: string) {
  return new Api('/plan/scheme/base').fetch('', planId, 'POST');
}

/**
 * 根据计划壳ID获取甘特图数据
 * @param planId 计划壳
 */
export function getPlanGanttData(planId: string) {
  return new Api('/plan/scheme-gtt/plan-shell').fetch('', planId, 'POST');
}

/**
 * 根据计划ID获取已关联的前置计划
 * @param planId
 */
export function getPlanLinksPre(planId: string) {
  return new Api('/plan/scheme/pre').fetch('', planId, 'GET');
}

/**
 * 获取前置任务分页
 * @param params
 */
export function getPlanPrePage(params) {
  return new Api('/plan/scheme/scheme-pre/page').fetch(params, '', 'POST');
}

/**
 * 计划关联前置关系
 * @param superPlanId 壳ID
 * @param postPlanId 当前关联计划ID
 * @param prePlanIdList 关联的计划ID
 */
export function setPlanPreLink(superPlanId: string, postPlanId: string, prePlanIdList: string[]) {
  return new Api('/plan/scheme-pre-post/').fetch({
    superPlanId,
    postPlanId,
    prePlanIdList,
  }, '', 'POST');
}

/**
 * 发布计划
 * @param planId
 */
export function publishPlan(planId: string) {
  return new Api('/plan/scheme/publish').fetch('', planId, 'GET');
}

/**
 * 关闭计划
 * @param planId
 */
export function closePlan(planId:string) {
  return new Api('/plan/scheme/close').fetch('', planId, 'GET');
}

/**
 * 下发计划
 * @param planId
 */
export function issuePlan(planId:string) {
  return new Api('/plan/scheme/issue').fetch('', planId, 'GET');
}

/**
 * 计划导出
 * @param planId
 */
export function exportPlan(planId:string) {
  return basicDownloadByData(`/api/plan/scheme-ie/export/excel/${planId}`, {}, '', 'POST', false, false);
}
