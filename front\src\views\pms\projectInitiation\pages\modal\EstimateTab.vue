<template>
  <div class="estimate-tab">
    <div class="message-button">
      <div class="message-button-left">
        <template v-if="projectData.status===101&&pageType==='1'">
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_01']"
            type="primary"
            icon="add"
            @click="quoteTemplate"
          >
            引入模版
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_02']"
            type="primary"
            icon="add"
            @click="addSubjectNode"
          >
            添加科目
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_03']"
            @click="calculateBatch"
          >
            批量计算
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_04']"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
          <BasicButton
            v-if="pageType==='1'"
            v-is-power="['XMLXXQ_container_01_button_05']"
            type="primary"
            @click="saveTemplate"
          >
            保存
          </BasicButton>
        </template>
      </div>
      <div class="message-button-right">
        <RadioGroup
          v-model:value="pageType"
          button-style="solid"
        >
          <RadioButton
            v-is-power="['XMLXXQ_container_01_button_21']"
            value="1"
          >
            概算编制
          </RadioButton>
          <RadioButton
            v-is-power="['XMLXXQ_container_01_button_21']"
            value="2"
          >
            试算工具
          </RadioButton>
        </RadioGroup>
      </div>
    </div>
    <div class="message-table">
      <BasicScrollbar>
        <AccountSummary
          v-if="pageType==='1'"
          ref="accountSummaryRef"
          :formId="projectData.id"
          :status="projectData.status"
          @selectChange="selectChange"
        />
        <EstimateTableList
          v-else
          :formId="projectData.id"
          :status="projectData.status"
        />
      </BasicScrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { BasicButton, BasicScrollbar, openModal } from 'lyra-component-vue3';
import {
  message, Modal, RadioButton, RadioGroup,
} from 'ant-design-vue';
import { inject, ref, Ref } from 'vue';
import dayjs from 'dayjs';
import AccountSummary from './AccountSummary.vue';
import EstimateTableList from './EstimateTableList.vue';
import { SelectListTable } from '/@/views/pms/components';
import { declarationData } from '../keys';
import Api from '/@/api';
import { SubjectTreeModal } from '/@/views/pms/estimateTemplate';

const pageType:Ref<string> = ref('1');
const projectData = inject(declarationData);
const accountSummaryRef = ref('');
const selectRowKeys:Ref<string[]> = ref([]);
function addSubjectNode() {
  const selectListTableRef = ref();
  openModal({
    title: '添加科目',
    width: 1100,
    height: 700,
    content(h) {
      return h(SubjectTreeModal, {
        ref: selectListTableRef,
      });
    },
    async onOk() {
      let selectData = await selectListTableRef.value.getSelectData();
      if (selectData.length === 0) {
        message.warning('请选择科目');
        return Promise.reject('');
      }
      let params = selectData.map((item) => ({
        id: item.id,
        number: item.number,
        parentId: item.parentId,
        name: item.name,
      }));
      new Api('/pms').fetch(params, `projectApprovalEstimate/addExpenseSubject?approvalId=${projectData.value.id}`, 'POST').then((res) => {
        message.success('添加科目成功');
        accountSummaryRef.value.update();
      });
    },
  });
}
function selectChange(val) {
  selectRowKeys.value = val;
}
function quoteTemplate() {
  const quoteTemplateRef = ref();
  openModal({
    title: '引用模版',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: quoteTemplateRef,
        getTableData: getTableTemplateData,
        getTreeApi,
        columns: [
          {
            title: '模板名称',
            dataIndex: 'name',
            type: 'input',
          },
          {
            title: '应用范围',
            dataIndex: 'scopeName',
            type: 'textarea',
          },
          {
            title: '所属类别',
            dataIndex: 'type',
            type: 'selectdict',
            valueField: 'typeName',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },

          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            customRender({ text }) {
              return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
            },
          },
        ],
        showLeftTree: true,
        selectType: 'radio',
      });
    },
    async onOk() {
      let formData = await quoteTemplateRef.value.getFormData();
      new Api('/pms').fetch('', `projectApprovalEstimate/addTemplate?approvalId=${projectData.value.id}&estimateTemplateId=${formData.selectedRowKeys[0]}`, 'POST').then((res) => {
        message.success('引用模版成功');
        accountSummaryRef.value.update();
      });
    },
  });
}
function calculateBatch() {
  let indexData = accountSummaryRef.value.getIndexData();
  new Api('/pms').fetch(indexData, `projectApprovalEstimate/subject/calculate/batch?approvalId=${projectData.value.id}`, 'POST').then((res) => {
    accountSummaryRef.value.setTableData(res);
  });
}
function getTreeApi() {
  return new Api('/pms').fetch('', 'projectApprovalEstimateTemplateClassify/list', 'GET');
}
function getTableTemplateData(id, params) {
  params.query = {
    templateClassifyId: id,
  };
  return new Api('/pms').fetch(params, 'projectApprovalEstimateTemplate/page', 'POST');
}
function saveTemplate() {
  let tableData = accountSummaryRef.value.getTableData();
  tableData = initTableData(tableData);
  new Api('/pms').fetch(tableData, 'projectApprovalEstimate/subject/editAmount', 'PUT').then((res) => {
    message.success('保存概算成功');
    accountSummaryRef.value.update();
  });
}
function initTableData(tableData) {
  return tableData.map((item) => {
    let newItem:any = {
      id: item.id,
      name: item.name,
      amount: item.amount,
      calculateAmount: item.calculateAmount,
    };
    if (Array.isArray(item.children)) {
      newItem.children = initTableData(item.children);
    }
    return newItem;
  });
}
function deleteBatch() {
  Modal.confirm({
    title: '删除提示',
    content: '是否删除选中的条目',
    onOk() {
      new Api('/pms').fetch(selectRowKeys.value, 'projectApprovalEstimate/subject/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        accountSummaryRef.value.update();
      });
    },
  });
}
</script>

<style lang="less" scoped>
:deep(.ant-btn){
  &:last-child{
    margin-right: 0;
  }
}
.estimate-tab{
  height: 100%;
  padding: 20px 20px 0;
  .estimate-tab-card{
    height: calc(~'100% - 60px');
    border-bottom: 0;
    :deep(.card-content){
      margin-bottom: 0;
      height: calc(~'100% - 30px');
    }
  }
  .message-button{
    display: flex;
    justify-content: space-between;
    .message-button-right{
      :deep(.ant-radio-button-wrapper){
        &:first-child{
          border-inline-start: 1px solid #d9d9d9;
          border-start-start-radius: 6px;
          border-end-start-radius: 6px;
        }
        &:last-child{
          border-start-end-radius: 6px;
          border-end-end-radius: 6px;
        }
      }
    }
  }
  .message-table{
    height: calc(~'100% - 32px');
  }
}
</style>