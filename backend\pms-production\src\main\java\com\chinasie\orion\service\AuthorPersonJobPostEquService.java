package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.AuthorPersonJobPostEqu;
import com.chinasie.orion.domain.dto.AuthorPersonJobPostEquDTO;
import com.chinasie.orion.domain.vo.AuthorPersonJobPostEquVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * AuthorPersonJobPostEqu 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:34
 */
public interface AuthorPersonJobPostEquService extends OrionBaseService<AuthorPersonJobPostEqu> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    AuthorPersonJobPostEquVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param authorPersonJobPostEquDTO
     */
    String create(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param authorPersonJobPostEquDTO
     */
    Boolean edit(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<AuthorPersonJobPostEquVO> pages(Page<AuthorPersonJobPostEquDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<AuthorPersonJobPostEquVO> vos) throws Exception;

    /**
     *  通过岗位授权 获取 等效的岗位授权信息
     * @param id
     * @return
     */
    List<AuthorPersonJobPostEqu> listByAuthorManageId(String id);

    /**
     *  新增岗位等效
     * @param authorPersonJobPostEquDTO
     * @return
     */
    Boolean saveOrUpdate(AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO);

    /**
     * 获取在当前  作业下 用户拥有的岗位等效信息
     *
     * @param jobPostAuthorizeIdList
     * @param userCodeList
     * @return
     */
    List<AuthorPersonJobPostEqu> listByUserCodeList(List<String> jobPostAuthorizeIdList, List<String> userCodeList);

    /**
     *  获取岗位等效信息
     * @param idList
     * @return
     */
    List<AuthorPersonJobPostEqu> listByJobAuthorizeIdList(List<String> idList);
}
