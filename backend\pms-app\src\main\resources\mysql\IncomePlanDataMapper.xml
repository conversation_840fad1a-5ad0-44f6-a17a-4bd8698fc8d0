<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.IncomePlanDataMapper">
    <select id="getTotal" resultType="com.chinasie.orion.domain.vo.IncomePlanDataTotalVO">
SELECT
  count(*) incomePlanDataTotal,
  ROUND(sum(d.income_plan_amt) / 10000, 2) incomePlanDataTotalAmt
FROM
 pmsx_income_plan_data d
WHERE
d.data_version = #{param.dataVersion} and  d.income_plan_id = #{param.incomePlanId}
and d.logic_status = 1
    </select>
</mapper>