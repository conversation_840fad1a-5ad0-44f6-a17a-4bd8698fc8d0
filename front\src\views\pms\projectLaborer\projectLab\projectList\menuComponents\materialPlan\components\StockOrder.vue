<script setup lang="ts">
import {
  Description,
  OrionTable,
  DescItem,
} from 'lyra-component-vue3';

import {
  page,
} from '/@/views/pms/api/documentModelLibrary';
import { InlineBlockFlexTe } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
const props = defineProps({
  number: {
    type: String,
    default: '',
  },
  remark: {
    type: String,
    default: '',
  },
  baseUnit: {
    type: String,
    default: '',
  },
  preparationNum: {
    type: String,
    default: '',
  },
});
const mockData: any = {
  number: props.number,
  remark: props.remark,
  baseUnit: props.baseUnit,
  preparationNum: props.preparationNum,
};
const schema: DescItem[] = [
  {
    field: 'number',
    label: '物料编码',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'remark',
    label: '物料描述',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'baseUnit',
    label: '基本单位',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'preparationNum',
    label: '备料量',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
];
const columns = [
  {
    title: '备料单号',
    // TODO:未对接字段
    dataIndex: 'name',
  },
  {
    title: '备料数量',
    // TODO:未对接字段
    dataIndex: 'number',
  },
  {
    title: '需求时间',
    // TODO:未对接字段
    dataIndex: 'remark',
  },
  {
    title: '申请时间',
    // TODO:未对接字段
    dataIndex: 'unit',
  },
  {
    title: '备料人',
    // TODO:未对接字段
    dataIndex: 'unit',
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  columns,
  // TODO:未对接接口
  api: (params) => page('sg4k1797909882754760704', params),
};

</script>

<template>
  <div style="height:100%;overflow:hidden;">
    <OrionTable :options="baseTableOption">
      <template #toolbarLeft>
        <Description
          :column="4"
          :bordered="false"
          :data="mockData"
          :schema="schema"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
