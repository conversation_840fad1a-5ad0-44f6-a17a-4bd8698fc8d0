package com.chinasie.orion.domain.dto;

import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SchemeMsgDTO {

    private List<ProjectScheme> projectSchemeList;

    private List<ProjectSchemeApplyApproval> projectSchemeApprovalList;

    private Map<String, String> extParam;

    private List<String> recipientIds;
}
