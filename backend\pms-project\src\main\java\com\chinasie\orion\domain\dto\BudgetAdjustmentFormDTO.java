package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetAdjustmentFrom DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetAdjustmentFromDTO对象", description = "预算调整表")
@Data
@ExcelIgnoreUnannotated
public class BudgetAdjustmentFormDTO extends ObjectDTO implements Serializable {

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @ExcelProperty(value = "项目Id ", index = 1)
    private String projectId;

    /**
     * 预算调整名称
     */
    @ApiModelProperty(value = "预算调整名称")
    @ExcelProperty(value = "预算调整名称 ", index = 2)
    private String name;

    /**
     * 预算调整编码
     */
    @ApiModelProperty(value = "预算调整编码")
    @ExcelProperty(value = "预算调整编码 ", index = 3)
    private String number;

    /**
     * 调整金额
     */
    @ApiModelProperty(value = "调整金额")
    @ExcelProperty(value = "调整金额 ", index = 4)
    private BigDecimal adjustmentMoney;

    /**
     * 调整预算条目
     */
    @ApiModelProperty(value = "调整预算条目")
    @ExcelProperty(value = "调整预算条目 ", index = 5)
    private Integer adjustmentNum;


    @ApiModelProperty(value = "调整预算数据")
    private List<BudgetAdjustmentDTO> budgetAdjustmentDTOS;


}
