package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectBudgetAnnualDTO;
import com.chinasie.orion.domain.dto.ProjectBudgetDTO;
import com.chinasie.orion.domain.entity.ProjectBudget;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.domain.vo.TotalCostVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ProjectBudget 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 14:48:31
 */
public interface ProjectBudgetService {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectBudgetVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectBudgetDTO
     */
    ProjectBudgetVO create(ProjectBudgetDTO projectBudgetDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectBudgetDTO
     */
    Boolean edit(ProjectBudgetDTO projectBudgetDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    Page<ProjectBudgetVO> getProjectBudgetVOPage(Page<ProjectBudgetDTO> pageRequest) throws Exception;


    List<ProjectBudgetVO> listProjectBudgetVO() throws Exception;

    Page<ProjectBudgetVO> getProjectBudgetVOPage1(Page<ProjectBudgetDTO> pageRequest) throws Exception;

    TotalCostVO getTotal(String projectId) throws Exception;

    /**
     * 通过IDs查询预算信息
     *
     * @param projectBudgetIds 项目预算IDS
     * @return map
     * @throws Exception e
     */
    Map<String, ProjectBudget> getProjectBudgetMap(List<String> projectBudgetIds) throws Exception;

    /**
     * 获取 项目对应的预算信息 通过项目id列表和年度
     *
     * @param projectBudgetAnnualDTO
     * @return
     */
    List<ProjectBudgetVO> getBudgetListByProjectIdListAndAnnual(ProjectBudgetAnnualDTO projectBudgetAnnualDTO) throws Exception;
}