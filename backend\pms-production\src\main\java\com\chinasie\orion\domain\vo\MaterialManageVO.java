package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MaterialManage VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@ApiModel(value = "MaterialManageVO对象", description = "物资库")
@Data
public class MaterialManageVO extends ObjectVO implements Serializable {

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;


    /**
     * 资产编码
     */
    @ApiModelProperty(value = "资产编码")
    private String number;


    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心编码")
    private String costCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String specificationModel;


    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;


    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;


    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;


    /**
     * 物质所在基地
     */
    @ApiModelProperty(value = "物质所在基地")
    private String baseId;
    /**
     * 物质所在基地编号
     */
    @ApiModelProperty(value = "物质所在基地编号")
    private String baseCode;

    /**
     * 物质所在基地
     */
    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;
    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    private String rspUserNo;


    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;


    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    private String useUserNo;


    /**
     * 使用人名称
     */
    @ApiModelProperty(value = "使用人名称")
    private String useUserName;


    /**
     * 资产入库日期
     */
    @ApiModelProperty(value = "资产入库日期")
    private Date enterDate;


    /**
     * 是否计量器具
     */
    @ApiModelProperty(value = "是否计量器具")
    private Boolean isMetering;


    /**
     * 是否向电厂报备
     */
    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;


    /**
     * 检定是否超期
     */
    @ApiModelProperty(value = "检定是否超期")
    private Boolean isOverdue;


    /**
     * 物质应用作业(工单号)
     */
    @ApiModelProperty(value = "物质应用作业(工单号)")
    private String jobNo;


    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    @ApiModelProperty(value = "入库数量")
    private Integer inputStockNum;

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String rspUserId;
    /**
     * 使用人id
     */
    @ApiModelProperty(value = "使用人id")
    private String useUserId;

    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "离开场倒计时（天）")
    private long outDays;

    @ApiModelProperty(value = "入场离场时间")
    private List<Date> inAndOutDateList;

    @ApiModelProperty(value = "是否合格")
    private Boolean isPass;

    @ApiModelProperty(value = "是否可用")
    private Boolean isAvailable;

    @ApiModelProperty(value = "产品编码")
    private String productCode;
    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;

    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;

    /**
     * 固定资产附件
     */
    @ApiModelProperty(value = "固定资产附件")
    private List<FileVO> fixedAssetsFileList;

    @ApiModelProperty(value = "资产所在地")
    private String storagePlaceName;

}
