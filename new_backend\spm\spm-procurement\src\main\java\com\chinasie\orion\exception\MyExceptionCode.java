package com.chinasie.orion.exception;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/31/14:03
 * @description:
 */
public enum MyExceptionCode implements   ErrorCode{

    ERROR_EXIST_DEPT_DATA(70005, "该部门数据已经存在"),
    ERROR_EXIST_DEPT(70001, "该部门不存在"),
    ERROR_NOT_PERMISSION(70019, "无权操作"),
    ERROR_HAVE_MEMBER(70011, "您选择的角色中，有成员未删除，请先移除!"),
    ERROR_NOT_FOUNT_JOB(70012, "作业数据不存在"),
    ERROR_PARAM(70012, "参数错误"),
    ERROR_DATA_PARAM(70012, "数据重复，请重新选择"),
    SYSTEM_ERROR_UPDATE_ENTITY_NULL(70013, "修改数据不存在!"),
    SYSTEM_ERROR_UPDATE_NAME_ALTER(70014, "修改时,名称不能修改!"),
    SYSTEM_ERROR_UPDATE_NAME_REPEAT(70015, "修改时,名称不能重复!"),
    SYSTEM_ERROR_CREATE_ENTITY_NULL(70016, "新增数据不存在!"),
    SYSTEM_ERROR_CREATE_NAME_NULL(70017, "创建时,名称不能为空!"),
    ERROR_USER_NOT_FOUNT(70018, "当前用户不存在"),
    ERROR_EXIST_DATA(70019, "该数据已经存在"),
    ERROR_DELETE_DATA(70020, "该数据不能删除"),
    ERROR_DATE(70021, "日期格式不对"), MAJOR_REPAIR_PLAN_NOT_EXIST(70022, "大修计划不存在");


    private Integer errorCode;
    private String errorDesc;
    private MyExceptionCode(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }
    @Override
    public Integer getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorDesc() {
        return errorDesc;
    }
}
