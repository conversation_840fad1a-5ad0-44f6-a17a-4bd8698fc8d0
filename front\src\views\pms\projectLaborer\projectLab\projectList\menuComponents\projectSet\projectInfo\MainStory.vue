<script setup lang="ts">
import {
  OrionTable, randomString, isPower, BasicButton,
} from 'lyra-component-vue3';
import {
  ComputedRef,
  h, inject, onMounted, ref,
} from 'vue';
import { RangePicker, Input, message } from 'ant-design-vue';
import { getList, saveOrRemove } from '/@/views/pms/api/projectMajorDeed';

const props = defineProps<{
  projectId:string
}>();
const projectData: ComputedRef<Record<string, any>> = inject('formData');
const detailAuthList = projectData.value?.detailAuthList || [];
const loading = ref(false);
const loadingSave = ref(false);
const dataSource:any = ref([]);
const tableOptionsStand = {
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: `add${isPower('PMS_XMXQ_container_12_05_button_03', detailAuthList) ? '' : '|delete'}|enable|disable`,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  rowSelection: {},
  columns: [
    {
      title: '起止时间',
      dataIndex: 'beginEndTime',
      width: 250,
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(RangePicker, {
            value: record.beginEndTime || [],
            valueFormat: 'YYYY-MM-DD',
            onChange: (_, dateStrings) => {
              record.beginEndTime = dateStrings;
            },
            style: { width: '100%' },
          });
        }
        return record.beginEndTime?.join('~');
      },
    },
    {
      title: '主要事迹',
      dataIndex: 'majorDeed',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Input, {
            value: record.majorDeed,
            maxlength: 1024,
            placeholder: '请输入',
            allowClear: true,
            onChange: (e) => {
              record.majorDeed = e.target.value;
            },
            style: { width: '100%' },
          });
        }
        return record.majorDeed;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '修改',
      isShow: (record: Record<string, any>) => !record.isEdit && isPower('PMS_XMXQ_container_12_05_button_04', detailAuthList),
      onClick(record: Record<string, any>) {
        record.isEdit = !record.isEdit;
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMXQ_container_12_05_button_05', detailAuthList),
      modal(record: Record<string, any>) {
        return new Promise((resolve, reject) => {
          dataSource.value = dataSource.value.filter((item) => item.id !== record.id);
          resolve('操作成功');
        });
      },
    },
  ],
  batchDeleteApi: ({ ids }) =>
    // 返回自己定义的请求
    new Promise((resolve, reject) => {
      dataSource.value = dataSource.value.filter((item) => !ids.some((id) => item.id === id));
      resolve('操作成功');
    })
  ,
};

const handleAdd = () => {
  dataSource.value.unshift({
    id: `ZDY_${randomString()}`,
    beginEndTime: [],
    majorDeed: '',
    isEdit: true,
  });
};
const handleSave = async () => {
  for (let i = 0; i < dataSource.value.length; i++) {
    const item = dataSource.value[i];
    if (!item.beginEndTime[0]) {
      return message.warn(`请选择第${i + 1}行【起止时间】`);
    }
    if (!item.majorDeed?.trim()) {
      return message.warn(`第${i + 1}行【主要事迹】不能为空`);
    }
  }

  const data = dataSource.value.map((item) => ({
    id: item.id.startsWith('ZDY_') ? null : item.id,
    beginEndTime: item.beginEndTime,
    majorDeed: item.majorDeed,
  }));

  try {
    loadingSave.value = true;
    await saveOrRemove(props.projectId, data);
    await handleGetList();
  } finally {
    loadingSave.value = false;
  }
};

const handleGetList = async () => {
  try {
    loading.value = true;
    const result = await getList(props.projectId);
    dataSource.value = result.map((item) => ({
      id: item.id,
      beginEndTime: item.beginEndTime,
      majorDeed: item.majorDeed,
      isEdit: false,
    }));
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  handleGetList();
});
</script>

<template>
  <div style="height: 330px;overflow: hidden">
    <OrionTable
      ref="tableRefStand"
      :options="tableOptionsStand"
      :loading="loading"
      :dataSource="dataSource"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_05_button_01', detailAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新增
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_05_button_02', detailAuthList)"
          icon="sie-icon-baocun"
          :loading="loadingSave"
          @click="handleSave"
        >
          保存
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
