package com.chinasie.orion.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/01/13:45
 * @description:
 */
@Data
public class DeliverableVo extends RevisionClassVO {

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "所属计划名称")
    private String planName;


    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date deliveryTime;
    /**
     *  预计交付时间
     */
    @ApiModelProperty(value = "预计交付时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date predictDeliverTime;

    /**
     *  版本号
     */
    @ApiModelProperty(value = "版本号")
    private String revId;

    @ApiModelProperty(value = "状态名称")
    private String statusName;


    @ApiModelProperty("创建人名字")
    private String creatorName;
    @ApiModelProperty("拥有者名字")
    private String ownerName;
    @ApiModelProperty("修改人名字")
    private String modifyName;

    /**
     * 实际交付物时间（计划实际结束时间）
     */
    @ApiModelProperty(value = "实际交付物时间（计划实际结束时间）")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;
}
