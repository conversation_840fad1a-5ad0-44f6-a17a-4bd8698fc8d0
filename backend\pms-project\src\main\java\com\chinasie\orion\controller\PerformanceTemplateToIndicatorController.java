package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.PerformanceTemplateDTO;
import com.chinasie.orion.domain.entity.PerformanceTemplateToIndicator;
import com.chinasie.orion.domain.vo.PerformanceTemplateVO;
import com.chinasie.orion.service.PerformanceTemplateToIndicatorService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * PerformanceTemplateToIndicator 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@RestController
@RequestMapping("/performanceTemplateToIndicator")
@Api(tags = "项目绩效模版和指标关联接口")
public class PerformanceTemplateToIndicatorController {

    @Autowired
    private PerformanceTemplateToIndicatorService performanceTemplateToIndicatorService;


    /**
     * 新增
     *
     * @param performanceTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增模版库下的指标")
    @RequestMapping(value = "/saveTemplateId/{templateId}", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增模版库下的指标", subType = "新增模版库下的指标", type = "项目绩效模版和指标关联接口", bizNo = "{{#templateId}}")
    public ResponseDTO<Boolean> saveTemplateId(@PathVariable(value = "templateId") String templateId, @RequestBody List<PerformanceTemplateToIndicator> performanceTemplateDTO) throws Exception {
        Boolean rsp = performanceTemplateToIndicatorService.create(templateId, performanceTemplateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除（批量）", subType = "删除（批量）", type = "项目绩效模版和指标关联接口", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = performanceTemplateToIndicatorService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 启用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "/enable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用", subType = "启用", type = "项目绩效模版和指标关联接口", bizNo = "")
    public ResponseDTO<Boolean> enable(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = performanceTemplateToIndicatorService.enable(ids);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 禁用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/disable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】禁用", subType = "禁用", type = "项目绩效模版和指标关联接口", bizNo = "")
    public ResponseDTO<Boolean> disEnable(@RequestBody List<String> ids ) throws Exception {
        Boolean rsp = performanceTemplateToIndicatorService.disEnable(ids);
        return new ResponseDTO<>(rsp);
    }







}
