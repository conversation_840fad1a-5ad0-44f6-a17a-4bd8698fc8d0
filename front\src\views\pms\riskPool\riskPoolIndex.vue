<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <div class="table-content">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        @selectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="isPower('DXFXK_container_01_button_01',powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="addTableNode"
          >
            批量入库
          </BasicButton>
          <BasicButton
            v-if="isPower('DXFXK_container_01_button_02',powerData)"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
          <BasicButton
            v-if="isPower('DXFXK_container_01_button_03',powerData)"
            icon="sie-icon-tijiao"
            @click="submit"
          >
            提交
          </BasicButton>
          <BasicButton
            v-if="isPower('DXFXK_container_01_button_04',powerData)"
            icon="sie-icon-daochu"
            @click="exportTable"
          >
            导出
          </BasicButton>
        </template>
        >
      </OrionTable>
    </div>
  </Layout>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable,
  Empty, openModal, downloadByData, isPower,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { stampDate } from '/@/utils/dateUtil';
import { SelectListTable } from '/@/views/pms/components';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
const selectRowKeys:Ref<string[]> = ref([]);
const router = useRouter();
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const powerData:Ref<any[]> = ref([]);
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],
  api: async (params) => {
    params.power = {
      pageCode: 'PMS7028',
      headContainerCode: 'DXFXK_container_01',
      containerCode: 'DXFXK_container_02',
    };
    let res = await new Api('/pas').fetch(params, 'riskLibrary/page', 'POST');
    powerData.value = res.headAuthList;
    return res;
  },
  columns: [
    {
      title: '风险编号',
      dataIndex: 'number',
      width: 100,
    },
    {
      title: '风险名称',
      dataIndex: 'name',
      minWidth: 200,
      ellipsis: true,
      customRender({ record, text }) {
        return h('span', {
          title: text,
          onClick: () => {
            router.push({
              name: 'PMSRiskManagementDetails',
              params: {
                id: record.riskId,
              },
              query: {
                type: 'riskPoll',
              },
            });
          },
          class: 'action-btn',
        }, text);
      },
    },
    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      width: 100,
      align: 'left',
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
      width: 100,
      align: 'left',
    },
    {
      title: '影响程度',
      dataIndex: 'riskInfluenceName',
      width: 100,
      align: 'left',
    },
    {
      title: '预估发生时间',
      dataIndex: 'predictStartTimeName',
      width: 150,
      align: 'left',
    },
    {
      title: '应对策略',
      dataIndex: 'copingStrategyName',
      width: 100,
      align: 'left',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      width: 150,
      slots: { customRender: 'status' },
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: 100,
    },
    {
      title: '修改日期',
      ellipsis: true,
      dataIndex: 'modifyTime',
      customRender: ({
        text, record, index, column,
      }) => (record.modifyTime && record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd') : ''),
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '提交',
      isShow: (record: Record<string, any>) => isPower('DXFXK_container_02_button_01', record?.rdAuthList),
      onClick(record) {
        submitBatch([record.id], 'one');
      },
    },
    {
      text: '移除',
      isShow: (record: Record<string, any>) => isPower('DXFXK_container_02_button_02', record?.rdAuthList),
      onClick(record) {
        deleteBatchData([record.id], 'one');
      },
    },
  ],
  //  beforeFetch,
});
function addTableNode() {
  const selectListTableRef = ref();
  openModal({
    title: '批量入库',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        showLeftTree: true,
        getTreeApi,
        getTableData,
        columns: [
          {
            title: '风险名称',
            dataIndex: 'name',
            width: 200,
          },
          {
            title: '风险类型',
            dataIndex: 'riskTypeName',
            width: 100,
            align: 'left',
          },
          {
            title: '发生概率',
            dataIndex: 'riskProbabilityName',
            width: 100,
            align: 'left',
          },
          {
            title: '影响程度',
            dataIndex: 'riskInfluenceName',
            width: 100,
            align: 'left',
          },
          {
            title: '所属项目',
            dataIndex: 'projectName',
            width: 100,
            align: 'left',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },

          },
        ],
        selectType: 'check',
        isTableTree: false,
        showTreeSearch: true,
      });
    },
    async onOk() {
      let formData = await selectListTableRef.value.getFormData();
      if (formData.selectTableData.length === 0) {
        message.warning('请选择风险入库');
        return Promise.reject('');
      }
      new Api('/pas').fetch(formData.selectedRowKeys, 'riskLibrary/add/batch', 'POST').then((res) => {
        message.success('批量入库成功');
        update();
      });
      // console.log(formData);
    },
  });
}
function deleteBatch() {
  deleteBatchData(selectRowKeys.value, 'all');
}
function deleteBatchData(params, type = 'all') {
  Modal.confirm({
    title: '移除提示',
    content: type === 'all' ? '是否移除选中的数据？' : '是否移除当前的数据？',
    onOk() {
      new Api('/pas').fetch(params, 'riskLibrary/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        update();
      });
    },
  });
}
function submit() {
  submitBatch(selectRowKeys.value, 'all');
}
function submitBatch(params, type = 'all') {
  Modal.confirm({
    title: '提交提示',
    content: type === 'all' ? '是否提交选中的数据？' : '是否提交当前的数据',
    onOk() {
      new Api('/pas').fetch(params, 'riskLibrary/submit', 'PUT').then((res) => {
        message.success('提交成功。');
        update();
      });
    },
  });
}
function getTreeApi(val = '') {
  let params:any = {};
  if (val) {
    params.keyword = val;
  }
  return new Api('/pms').fetch(params, 'project/getList', 'GET');
}
function getTableData(id, params) {
  params.query = {
    projectId: id,
    status: 130,
  };
  return new Api('/pms').fetch(params, 'risk-management/getPage', 'POST');
}
function exportTable() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出？',
    onOk() {
      downloadByData('/pas/riskLibrary/export/excel', [], '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

function update() {
  tableRef.value.reload({ page: 1 });
}

</script>

<style lang="less" scoped>
</style>
