package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomeProvisionInformationDTO;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomeProvisionInformationVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.InTransactionPreReconciliationService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/inTransactionPreReconciliation")
@Api(tags = "内部交易预对账明细表")
public class InTransactionPreReconciliationController {
    @Autowired
    private InTransactionPreReconciliationService inTransactionPreReconciliationService;

    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "内部交易预对账明细表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<InTransactionPreReconciliationVO>> pages(@RequestBody Page<InTransactionPreReconciliationDTO> pageRequest) throws Exception {
        Page<InTransactionPreReconciliationVO> rsp =  inTransactionPreReconciliationService.getPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "导出")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "内部交易预对账明细表", subType = "导出数据", bizNo = "")
    @RequestMapping(value = "/export/excel", method = RequestMethod.POST)
    public void pages(@RequestBody InTransactionPreReconciliationDTO in, HttpServletResponse response) throws Exception {
        inTransactionPreReconciliationService.exportByExcel(in, response);
    }
}
