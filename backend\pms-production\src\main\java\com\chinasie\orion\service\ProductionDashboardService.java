package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.EnvCountDTO;
import com.chinasie.orion.domain.dto.OverhaulDetailDTO;
import com.chinasie.orion.domain.dto.ProductionIndexDTO;
import com.chinasie.orion.domain.entity.ProductionIndex;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProductionIndex 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
public interface ProductionDashboardService extends OrionBaseService<KeySafetyVO> {
    /**
     * 安质环关键指标
     *
     * @return
     * @throws Exception
     */
    List<KeySafetyVO> getKeySafety(EnvCountDTO locationCode);

    PersonNumVO getPersonNum(String locationCode);

    List<LeadNumVO> getLeadNum(String locationCode);

    /**
     * 查询基地及编码
     *
     * @return
     * @throws Exception
     */
    List<BasePlaceVO> getLocation();


    /**
     * 营收额绩效排名
     *
     * @return
     * @throws Exception
     */
    List<RankingVO> getRevenueRankingList();

    /**
     * 大修工期节约绩效排名
     *
     * @return
     * @throws Exception
     */
    List<RankingVO> getDurationRankingList();

    /**
     * 集体剂量降低绩效排名
     *
     * @return
     * @throws Exception
     */
    List<RankingVO> getDoseRankingList();

    /**
     * 多基地大修准备及实施状态
     *
     * @return
     * @throws Exception
     */
    List<OverhaulVO> getOverhaul(String locationCode);

    /**
     * 安质环绩效排名
     *
     * @return
     * @throws Exception
     */
    List<RankingVO> getSafetyRankingList();

    /**
     * 多基地大修准备及实施状态
     *
     * @return
     * @throws Exception
     */
    List<OverhaulDetailsVO> getOverhaulDetail(OverhaulDetailDTO overhaulDetailDTO);
}
