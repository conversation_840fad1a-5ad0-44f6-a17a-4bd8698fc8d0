package com.chinasie.orion.repository;

import com.chinasie.orion.domain.vo.MyInitiationVO;
import com.chinasie.orion.management.domain.vo.PredictLeadVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PersonalCenterMapper extends OrionBaseMapper<MyInitiationVO> {
    @Select("SELECT * FROM ( " +
            "    (SELECT " +
            "        ps.id AS sourceId, " +
            "        ps.create_time AS acceptTime, " +
            "        ps.project_id AS projectId, " +
            "        ps.name AS title, " +
            "        4 AS since, " +
            "        ps.creator_id AS creatorId, " +
            "        ps.parent_chain AS parentChain, " +
            "        NULL AS riskType, " +
            "        '/pms/ProPlanDetails/' AS detailUrl, " +
            "        'projectPlan' AS taskType, " +
            "        '项目计划' AS taskTypeName " +
            "    FROM " +
            "        pms_project_scheme ps " +
            "    WHERE " +
            "        ps.creator_id = #{userId} " +
            "    AND      ps.logic_status = 1 " +
            "    ORDER BY " +
            "        ps.create_time DESC " +
            "    LIMIT 6) " +
            "    UNION ALL " +
            "    (SELECT " +
            "        pl.id AS sourceId, " +
            "        pl.create_time AS acceptTime, " +
            "        NULL AS projectId, " +
            "        pl.name AS title, " +
            "        4 AS since, " +
            "        pl.creator_id AS creatorId, " +
            "        NULL AS parentChain, " +
            "        NULL AS riskType, " +
            "        '/plan/daily-plan-details/' AS detailUrl, " +
            "        'dailyTask' AS taskType, " +
            "        '日常任务' AS taskTypeName " +
            "    FROM " +
            "        plan_scheme pl " +
            "    WHERE " +
            "        pl.creator_id = #{userId} " +
            "    AND       pl.logic_status = 1 " +
            "    ORDER BY " +
            "        pl.create_time DESC " +
            "    LIMIT 6) " +
            "    UNION ALL " +
            "    (SELECT " +
            "        qm.id AS sourceId, " +
            "        qm.create_time AS acceptTime, " +
            "        NULL AS projectId, " +
            "        qm.name AS title, " +
            "        4 AS since, " +
            "        qm.creator_id AS creatorId, " +
            "        NULL AS parentChain, " +
            "        NULL AS riskType, " +
            "        '/pms/question-management-details/' AS detailUrl, " +
            "        'projectProblem' AS taskType, " +
            "        '项目问题' AS taskTypeName " +
            "    FROM " +
            "        pms_question_management qm " +
            "    WHERE " +
            "        qm.creator_id = #{userId} " +
            "    AND       qm.logic_status = 1 " +
            "    ORDER BY " +
            "        qm.create_time DESC " +
            "    LIMIT 6) " +
            "    UNION ALL " +
            "    (SELECT " +
            "        rm.id AS sourceId, " +
            "        rm.create_time AS acceptTime, " +
            "        NULL AS projectId, " +
            "        rm.name AS title, " +
            "        rm.since AS since, " +
            "        rm.creator_id AS creatorId, " +
            "        NULL AS parentChain, " +
            "        rm. risk_type AS riskType, " +
            "        '/pms/risk-management-details/' AS detailUrl, " +
            "        'projectRisk' AS taskType, " +
            "        '项目风险' AS taskTypeName " +
            "    FROM " +
            "        pms_risk_management rm " +
            "    WHERE " +
            "        rm.creator_id = #{userId} " +
            "    AND       rm.logic_status = 1 " +
            "    ORDER BY " +
            "        rm.create_time DESC " +
            "    LIMIT 6) " +
            ") AS combinedResult " +
            "ORDER BY acceptTime DESC " +
            "LIMIT 6")
    List<MyInitiationVO> getMyInitiationVOs(@Param("userId") String userId);

    @Select("SELECT COUNT(DISTINCT sourceId) FROM ( " +
            "    (SELECT ps.id AS sourceId " +
            "    FROM pms_project_scheme ps " +
            "    WHERE ps.creator_id = #{userId} AND ps.logic_status = 1) " +
            "    UNION ALL " +
            "    (SELECT pl.id AS sourceId " +
            "    FROM plan_scheme pl " +
            "    WHERE pl.creator_id = #{userId} AND pl.logic_status = 1) " +
            "    UNION ALL " +
            "    (SELECT qm.id AS sourceId " +
            "    FROM pms_question_management qm " +
            "    WHERE qm.creator_id = #{userId} AND qm.logic_status = 1) " +
            "    UNION ALL " +
            "    (SELECT rm.id AS sourceId " +
            "    FROM pms_risk_management rm " +
            "    WHERE rm.creator_id = #{userId} AND rm.logic_status = 1) " +
            ") AS combined")
    int countMyInitiation(@Param("userId") String userId);


    @Select("SELECT estimated_transaction_amount, estimated_closing_time " +
            "FROM pas_lead_management AS ps " +
            "WHERE predicted_status = 170 " +
            "AND logic_status = 1")
    @Results({
            @Result(property = "account", column = "estimated_transaction_amount"),
            @Result(property = "time", column = "estimated_closing_time")
    })
    List<PredictLeadVO> selectEstimated();
}



