package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractClaim Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_claim")
@ApiModel(value = "ContractClaimEntity对象", description = "合同索赔信息表")
@Data

public class ContractClaim extends ObjectEntity implements Serializable {

    /**
     * 索赔编号
     */
    @ApiModelProperty(value = "索赔编号")
    @TableField(value = "claim_id")
    private String claimId;

    /**
     * 索赔标题
     */
    @ApiModelProperty(value = "索赔标题")
    @TableField(value = "claim_title")
    private String claimTitle;

    /**
     * 索赔状态
     */
    @ApiModelProperty(value = "索赔状态")
    @TableField(value = "claim_status")
    private String claimStatus;

    /**
     * 索赔方向
     */
    @ApiModelProperty(value = "索赔方向")
    @TableField(value = "claim_direction")
    private String claimDirection;

    /**
     * 索赔处理时间
     */
    @ApiModelProperty(value = "索赔处理时间")
    @TableField(value = "claim_process_time")
    private Date claimProcessTime;

    /**
     * 索赔申请时间
     */
    @ApiModelProperty(value = "索赔申请时间")
    @TableField(value = "claim_request_time")
    private Date claimRequestTime;

    /**
     * 累计索赔金额（含本次）
     */
    @ApiModelProperty(value = "累计索赔金额（含本次）")
    @TableField(value = "cumulative_claim_amount")
    private BigDecimal cumulativeClaimAmount;

    /**
     * 总累计索赔占原合同价%
     */
    @ApiModelProperty(value = "总累计索赔占原合同价%")
    @TableField(value = "total_claim_pct_of_orig_price")
    private String totalClaimPctOfOrigPrice;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @TableField(value = "procurement_group_id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @TableField(value = "procurement_group_name")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @TableField(value = "business_rsp_user_id")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @TableField(value = "business_rsp_user_name")
    private String businessRspUserName;
}
