package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PersonPrepareDTO implements Serializable {

    @ApiModelProperty("人员id")
    @Deprecated
    String personId;

    @ApiModelProperty("关系Id")
    @NotEmpty(message = "关系id不能为空")
    String relationId;

    @ApiModelProperty(value = "是否常驻")
    Boolean isBasePermanent;

    @ApiModelProperty(value = "是否新人")
    Boolean newcomer;

    @ApiModelProperty(value = "新人对口人")
    String newcomerMatchPerson;

    @ApiModelProperty(value = "新人对口人编号")
    String newcomerMatchPersonCode;

    @ApiModelProperty(value = "计划进场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    Date planInDate;

    @ApiModelProperty(value = "计划离场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    Date planOutDate;

    @ApiModelProperty(value = "实际进场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    Date actInDate;

    Long inDays;

    @ApiModelProperty(value = "状态")
    Integer status;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    private String repairOrgName;
    @ApiModelProperty(value = "基地code")
    private String baseCode;
    @ApiModelProperty(value = "驻地名称")
    private String baseName;


    private String permanentBasicCode;
    private String permanentBasicName;

}
