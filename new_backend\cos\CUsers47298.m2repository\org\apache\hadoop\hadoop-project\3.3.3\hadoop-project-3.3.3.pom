<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-main</artifactId>
    <version>3.3.3</version>
  </parent>
  <artifactId>hadoop-project</artifactId>
  <version>3.3.3</version>
  <description>Apache Hadoop Project POM</description>
  <name>Apache Hadoop Project POM</name>
  <packaging>pom</packaging>
  <inceptionYear>2008</inceptionYear>

  <properties>
    <!-- Set the Release year during release -->
    <release-year>2022</release-year>

    <failIfNoTests>false</failIfNoTests>
    <!--Whether to proceed to next module if any test failures exist-->
    <maven.test.failure.ignore>true</maven.test.failure.ignore>
    <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
    <jetty.version>9.4.43.v20210629</jetty.version>
    <test.exclude>_</test.exclude>
    <test.exclude.pattern>_</test.exclude.pattern>

    <!-- number of threads/forks to use when running tests in parallel, see parallel-tests profile -->
    <testsThreadCount>2</testsThreadCount>

    <!-- These 2 versions are defined here because they are used -->
    <!-- JDIFF generation from embedded ant in the antrun plugin -->
    <jdiff.version>1.0.9</jdiff.version>
    <!-- Version number for xerces used by JDiff -->
    <xerces.jdiff.version>2.12.1</xerces.jdiff.version>

    <kafka.version>2.8.1</kafka.version>

    <commons-daemon.version>1.0.13</commons-daemon.version>

    <test.build.dir>${project.build.directory}/test-dir</test.build.dir>
    <test.build.data>${test.build.dir}</test.build.data>

    <!-- Used for building path to native library loaded by tests.  Projects -->
    <!-- at different nesting levels in the source tree may need to override. -->
    <hadoop.common.build.dir>${basedir}/../../hadoop-common-project/hadoop-common/target</hadoop.common.build.dir>
    <java.security.egd>file:///dev/urandom</java.security.egd>

    <!-- avro version -->
    <avro.version>1.7.7</avro.version>

    <!-- jersey version -->
    <jersey.version>1.19</jersey.version>

    <!-- jackson versions -->
    <jackson.version>1.9.13</jackson.version>
    <jackson2.version>2.13.2</jackson2.version>
    <jackson2.databind.version>2.13.2.2</jackson2.databind.version>

    <!-- javax ws rs api version -->
    <javax.ws.rs-api.version>2.1.1</javax.ws.rs-api.version>

    <!-- httpcomponents versions -->
    <httpclient.version>4.5.13</httpclient.version>
    <httpcore.version>4.4.13</httpcore.version>

    <!-- SLF4J/LOG4J version -->
    <slf4j.version>1.7.36</slf4j.version>
    <reload4j.version>1.2.18.3</reload4j.version>

    <!-- com.google.re2j version -->
    <re2j.version>1.1</re2j.version>

    <!--Protobuf version for backward compatibility-->
    <protobuf.version>2.5.0</protobuf.version>
    <!-- ProtocolBuffer version, actually used in Hadoop -->
    <hadoop.protobuf.version>3.7.1</hadoop.protobuf.version>
    <protoc.path>${env.HADOOP_PROTOC_PATH}</protoc.path>

    <hadoop-thirdparty.version>1.1.1</hadoop-thirdparty.version>
    <hadoop-thirdparty-protobuf.version>${hadoop-thirdparty.version}</hadoop-thirdparty-protobuf.version>
    <hadoop-thirdparty-guava.version>${hadoop-thirdparty.version}</hadoop-thirdparty-guava.version>
    <hadoop-thirdparty-shaded-prefix>org.apache.hadoop.thirdparty</hadoop-thirdparty-shaded-prefix>
    <hadoop-thirdparty-shaded-protobuf-prefix>${hadoop-thirdparty-shaded-prefix}.protobuf</hadoop-thirdparty-shaded-protobuf-prefix>
    <hadoop-thirdparty-shaded-guava-prefix>${hadoop-thirdparty-shaded-prefix}.com.google.common</hadoop-thirdparty-shaded-guava-prefix>

    <zookeeper.version>3.5.6</zookeeper.version>
    <curator.version>4.2.0</curator.version>
    <findbugs.version>3.0.5</findbugs.version>
    <dnsjava.version>2.1.7</dnsjava.version>

    <guava.version>27.0-jre</guava.version>
    <guice.version>4.0</guice.version>
    <joda-time.version>2.9.9</joda-time.version>

    <bouncycastle.version>1.60</bouncycastle.version>

    <!-- Required for testing LDAP integration -->
    <apacheds.version>2.0.0-M21</apacheds.version>
    <ldap-api.version>1.0.0-M33</ldap-api.version>

    <!-- Apache Commons dependencies -->
    <commons-beanutils.version>1.9.4</commons-beanutils.version>
    <commons-cli.version>1.2</commons-cli.version>
    <commons-codec.version>1.15</commons-codec.version>
    <commons-collections.version>3.2.2</commons-collections.version>
    <commons-compress.version>1.21</commons-compress.version>
    <commons-csv.version>1.0</commons-csv.version>
    <commons-io.version>2.8.0</commons-io.version>
    <commons-lang3.version>3.12.0</commons-lang3.version>
    <commons-logging.version>1.1.3</commons-logging.version>
    <commons-logging-api.version>1.1</commons-logging-api.version>
    <commons-math3.version>3.1.1</commons-math3.version>
    <commons-net.version>3.6</commons-net.version>
    <commons-text.version>1.4</commons-text.version>

    <kerby.version>1.0.1</kerby.version>
    <jcache.version>1.0-alpha-1</jcache.version>
    <ehcache.version>3.3.1</ehcache.version>
    <hikari.version>2.4.12</hikari.version>
    <mssql.version>6.2.1.jre7</mssql.version>
    <okhttp.version>2.7.5</okhttp.version>
    <jdom.version>1.1</jdom.version>
    <jna.version>5.2.0</jna.version>
    <grizzly.version>2.2.21</grizzly.version>
    <gson.version>2.8.9</gson.version>
    <metrics.version>3.2.4</metrics.version>
    <netty3.version>3.10.6.Final</netty3.version>
    <netty4.version>4.1.68.Final</netty4.version>
    <snappy-java.version>1.1.8.2</snappy-java.version>
    <lz4-java.version>1.7.1</lz4-java.version>

    <!-- Maven protoc compiler -->
    <protobuf-maven-plugin.version>0.5.1</protobuf-maven-plugin.version>
    <maven-replacer-plugin.version>1.5.3</maven-replacer-plugin.version>

    <protobuf-compile.version>3.5.1</protobuf-compile.version>
    <grpc.version>1.10.0</grpc.version>
    <os-maven-plugin.version>1.5.0.Final</os-maven-plugin.version>

    <!-- define the Java language version used by the compiler -->
    <javac.version>1.8</javac.version>

    <!-- The java version enforced by the maven enforcer -->
    <!-- more complex patterns can be used here, such as
       [${javac.version})
    for an open-ended enforcement
    -->
    <enforced.java.version>[${javac.version},)</enforced.java.version>
    <enforced.maven.version>[3.3.0,)</enforced.maven.version>

    <!-- Plugin versions and config -->
    <maven-surefire-plugin.argLine>-Xmx2048m -XX:+HeapDumpOnOutOfMemoryError</maven-surefire-plugin.argLine>
    <maven-surefire-plugin.version>3.0.0-M1</maven-surefire-plugin.version>
    <maven-surefire-report-plugin.version>${maven-surefire-plugin.version}</maven-surefire-report-plugin.version>
    <maven-failsafe-plugin.version>${maven-surefire-plugin.version}</maven-failsafe-plugin.version>

    <maven-clean-plugin.version>3.1.0</maven-clean-plugin.version>
    <maven-compiler-plugin.version>3.1</maven-compiler-plugin.version>
    <maven-install-plugin.version>2.5.1</maven-install-plugin.version>
    <maven-resources-plugin.version>2.6</maven-resources-plugin.version>
    <maven-shade-plugin.version>3.2.1</maven-shade-plugin.version>
    <maven-jar-plugin.version>2.5</maven-jar-plugin.version>
    <maven-war-plugin.version>3.1.0</maven-war-plugin.version>
    <maven-source-plugin.version>2.3</maven-source-plugin.version>
    <maven-pdf-plugin.version>1.2</maven-pdf-plugin.version>
    <maven-remote-resources-plugin.version>1.5</maven-remote-resources-plugin.version>
    <build-helper-maven-plugin.version>1.9</build-helper-maven-plugin.version>
    <exec-maven-plugin.version>1.3.1</exec-maven-plugin.version>
    <make-maven-plugin.version>1.0-beta-1</make-maven-plugin.version>
    <surefire.fork.timeout>900</surefire.fork.timeout>
    <aws-java-sdk.version>1.11.1026</aws-java-sdk.version>
    <hsqldb.version>2.3.4</hsqldb.version>
    <frontend-maven-plugin.version>1.11.2</frontend-maven-plugin.version>
    <jasmine-maven-plugin.version>2.1</jasmine-maven-plugin.version>
    <phantomjs-maven-plugin.version>0.7</phantomjs-maven-plugin.version>
    <yuicompressor-maven-plugin.version>1.5.1</yuicompressor-maven-plugin.version>
    <maven-project-info-reports-plugin.version>2.9</maven-project-info-reports-plugin.version>

    <!-- the version of Hadoop declared in the version resources; can be overridden
    so that Hadoop 3.x can declare itself a 2.x artifact. -->
    <declared.hadoop.version>${hadoop.version}</declared.hadoop.version>

    <swagger-annotations-version>1.5.4</swagger-annotations-version>
    <snakeyaml.version>1.26</snakeyaml.version>
    <hbase.one.version>1.4.8</hbase.one.version>
    <hbase.two.version>2.0.2</hbase.two.version>
    <junit.version>4.13.2</junit.version>
    <junit.jupiter.version>5.5.1</junit.jupiter.version>
    <junit.vintage.version>5.5.1</junit.vintage.version>
    <junit.platform.version>1.5.1</junit.platform.version>
    <assertj.version>3.12.2</assertj.version>
    <jline.version>3.9.0</jline.version>
    <powermock.version>1.5.6</powermock.version>
    <solr.version>8.8.2</solr.version>
    <openssl-wildfly.version>1.0.7.Final</openssl-wildfly.version>
    <woodstox.version>5.3.0</woodstox.version>
    <json-smart.version>2.4.7</json-smart.version>
    <nimbus-jose-jwt.version>9.8.1</nimbus-jose-jwt.version>
    <nodejs.version>v12.22.1</nodejs.version>
    <yarnpkg.version>v1.22.5</yarnpkg.version>
    <apache-ant.version>1.10.11</apache-ant.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.squareup.okhttp</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>mockwebserver</artifactId>
        <version>3.7.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>jdiff</groupId>
        <artifactId>jdiff</artifactId>
        <version>${jdiff.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop.thirdparty</groupId>
        <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
        <version>${hadoop-thirdparty-protobuf.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop.thirdparty</groupId>
        <artifactId>hadoop-shaded-guava</artifactId>
        <version>${hadoop-thirdparty-guava.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-assemblies</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-annotations</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-modules</artifactId>
        <version>${hadoop.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-api</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-check-invariants</artifactId>
        <version>${hadoop.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-check-test-invariants</artifactId>
        <version>${hadoop.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-integration-tests</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-runtime</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client-minicluster</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-common</artifactId>
        <version>${hadoop.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-common</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-auth</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-auth</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-nfs</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-hdfs</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-hdfs-client</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-hdfs-rbf</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-hdfs</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-app</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-app</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-common</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-api</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-client</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-core</artifactId>
        <version>${hadoop.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-jobclient</artifactId>
        <version>${hadoop.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-shuffle</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn</artifactId>
        <version>${hadoop.version}</version>
        <type>pom</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-web-proxy</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-common</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-common</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
         <artifactId>hadoop-yarn-server-tests</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-common</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-common</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-registry</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-nodemanager</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-resourcemanager</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-resourcemanager</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-applicationhistoryservice</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-timelineservice</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
          <groupId>org.apache.hadoop</groupId>
          <artifactId>hadoop-yarn-server-timelineservice</artifactId>
          <version>${hadoop.version}</version>
          <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-timelineservice-hbase-client</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-timelineservice-hbase-common</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-applications-distributedshell</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-timeline-pluginstorage</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-timeline-pluginstorage</artifactId>
        <type>test-jar</type>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-server-router</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-services-core</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-services-core</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-services-api</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-yarn-applications-catalog-webapp</artifactId>
        <version>${hadoop.version}</version>
        <type>war</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
         <artifactId>hadoop-mapreduce-client-jobclient</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-client-hs</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-mapreduce-examples</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-gridmix</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-streaming</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-archives</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-archive-logs</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-distcp</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-distcp</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-datajoin</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-rumen</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-extras</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-minicluster</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-minikdc</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-openstack</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-azure</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-azure-datalake</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-aws</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-aliyun</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-cos</artifactId>
        <version>${hadoop.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-kms</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-kms</artifactId>
        <version>${hadoop.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.errorprone</groupId>
            <artifactId>error_prone_annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-cli</groupId>
        <artifactId>commons-cli</artifactId>
        <version>${commons-cli.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-math3</artifactId>
        <version>${commons-math3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${commons-compress.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-csv</artifactId>
        <version>${commons-csv.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.ws.rs</groupId>
        <artifactId>jsr311-api</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>javax.ws.rs</groupId>
        <artifactId>javax.ws.rs-api</artifactId>
        <version>${javax.ws.rs-api.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>${jetty.version}</version>
      </dependency>
        <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-server-impl</artifactId>
        <version>${jetty.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-client</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet.jsp</groupId>
        <artifactId>jsp-api</artifactId>
        <version>2.1</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish</groupId>
        <artifactId>javax.servlet</artifactId>
        <version>3.1</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>1.5.5</version>
      </dependency>

      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>5.0.4</version>
      </dependency>
      <dependency>
          <groupId>org.ojalgo</groupId>
          <artifactId>ojalgo</artifactId>
          <version>43.0</version>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-core</artifactId>
        <version>${jersey.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-servlet</artifactId>
        <version>${jersey.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-json</artifactId>
        <version>${jersey.version}</version>
        <exclusions>
          <exclusion>
            <groupId>stax</groupId>
            <artifactId>stax-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-server</artifactId>
        <version>${jersey.version}</version>
      </dependency>

      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>${guice.version}</version>
      </dependency>

      <dependency>
        <groupId>cglib</groupId>
        <artifactId>cglib</artifactId>
        <version>3.2.0</version>
      </dependency>

      <dependency>
        <groupId>com.google.inject.extensions</groupId>
        <artifactId>guice-servlet</artifactId>
        <version>${guice.version}</version>
      </dependency>

      <dependency>
        <groupId>com.sun.jersey.contribs</groupId>
        <artifactId>jersey-guice</artifactId>
        <version>${jersey.version}</version>
      </dependency>

      <dependency>
        <groupId>com.sun.jersey.jersey-test-framework</groupId>
        <artifactId>jersey-test-framework-core</artifactId>
        <version>${jersey.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey.jersey-test-framework</groupId>
        <artifactId>jersey-test-framework-grizzly2</artifactId>
        <version>${jersey.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>


      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty</artifactId>
        <version>${netty3.version}</version>
      </dependency>

      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-all</artifactId>
        <version>${netty4.version}</version>
      </dependency>

      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet-tester</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commons-logging.version}</version>
        <exclusions>
          <exclusion>
            <groupId>avalon-framework</groupId>
            <artifactId>avalon-framework</artifactId>
          </exclusion>
          <exclusion>
            <groupId>logkit</groupId>
            <artifactId>logkit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging-api</artifactId>
        <version>${commons-logging-api.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.reload4j</groupId>
        <artifactId>reload4j</artifactId>
        <version>${reload4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.sun.jdmk</groupId>
            <artifactId>jmxtools</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.sun.jmx</groupId>
            <artifactId>jmxri</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.jms</groupId>
            <artifactId>jmx</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.jms</groupId>
            <artifactId>jms</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.amazonaws</groupId>
        <artifactId>aws-java-sdk-bundle</artifactId>
        <version>${aws-java-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.mina</groupId>
        <artifactId>mina-core</artifactId>
        <version>2.0.16</version>
      </dependency>
      <dependency>
        <groupId>org.apache.sshd</groupId>
        <artifactId>sshd-core</artifactId>
        <version>1.6.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ftpserver</groupId>
        <artifactId>ftplet-api</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ftpserver</groupId>
        <artifactId>ftpserver-core</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ftpserver</groupId>
        <artifactId>ftpserver-deprecated</artifactId>
        <version>1.0.0-M2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>${junit.jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>${junit.jupiter.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>${junit.vintage.version}</version>
        <scope>test</scope>
      </dependency>
      <!-- Only required to run tests in an IDE that bundles an older version -->
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>${junit.platform.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.github.stefanbirkner</groupId>
        <artifactId>system-rules</artifactId>
        <version>1.18.0</version>
        <exclusions>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit-dep</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>${commons-collections.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>${commons-beanutils.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-configuration2</artifactId>
        <version>2.1.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-text</artifactId>
        <version>${commons-text.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-reload4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jdt</groupId>
        <artifactId>core</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.woodstox</groupId>
        <artifactId>stax2-api</artifactId>
        <version>4.2.1</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.woodstox</groupId>
        <artifactId>woodstox-core</artifactId>
        <version>${woodstox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-mapper-asl</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-core-asl</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-jaxrs</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-xc</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson2.databind.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jaxb-annotations</artifactId>
        <version>${jackson2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-cbor</artifactId>
        <version>${jackson2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>2.28.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-all</artifactId>
        <version>1.10.19</version>
      </dependency>
      <dependency>
        <groupId>org.objenesis</groupId>
        <artifactId>objenesis</artifactId>
        <version>2.6</version>
      </dependency>
      <dependency>
        <groupId>org.mock-server</groupId>
        <artifactId>mockserver-netty</artifactId>
        <version>5.10</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro</artifactId>
        <version>${avro.version}</version>
      </dependency>
      <dependency>
        <groupId>net.sf.kosmosfs</groupId>
        <artifactId>kfs</artifactId>
        <version>0.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant</artifactId>
        <version>${apache-ant.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.re2j</groupId>
        <artifactId>re2j</artifactId>
        <version>${re2j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>${protobuf.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-daemon</groupId>
        <artifactId>commons-daemon</artifactId>
        <version>${commons-daemon.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>0.1.55</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom</artifactId>
        <version>${jdom.version}</version>
      </dependency>
      <dependency>
        <groupId>com.googlecode.json-simple</groupId>
        <artifactId>json-simple</artifactId>
        <version>1.1.1</version>
      </dependency>

      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
        <exclusions>
          <exclusion>
            <!-- otherwise seems to drag in junit 3.8.1 via jline -->
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.sun.jdmk</groupId>
            <artifactId>jmxtools</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.sun.jmx</groupId>
            <artifactId>jmxri</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.jboss.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
          <exclusion>
            <groupId>jline</groupId>
            <artifactId>jline</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-native-epoll</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.kerby</groupId>
            <artifactId>kerb-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.kerby</groupId>
            <artifactId>kerb-simplekdc</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.kerby</groupId>
            <artifactId>kerby-config</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
        <type>test-jar</type>
        <exclusions>
          <exclusion>
            <groupId>org.jboss.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
          <exclusion>
            <groupId>jline</groupId>
            <artifactId>jline</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport-native-epoll</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.jline</groupId>
        <artifactId>jline</artifactId>
        <version>${jline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>${hsqldb.version}</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>${metrics.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-sls</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-cloud-storage</artifactId>
        <version>${hadoop.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
        <version>2.2.11</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.jettison</groupId>
        <artifactId>jettison</artifactId>
        <version>1.1</version>
        <exclusions>
          <exclusion>
            <groupId>stax</groupId>
            <artifactId>stax-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.sun.jersey</groupId>
        <artifactId>jersey-client</artifactId>
        <version>${jersey.version}</version>
      </dependency>

      <dependency>
        <groupId>org.glassfish.grizzly</groupId>
        <artifactId>grizzly-http-servlet</artifactId>
        <version>${grizzly.version}</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.grizzly</groupId>
        <artifactId>grizzly-http</artifactId>
        <version>${grizzly.version}</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.grizzly</groupId>
        <artifactId>grizzly-http-server</artifactId>
        <version>${grizzly.version}</version>
      </dependency>

      <dependency>
        <groupId>${leveldbjni.group}</groupId>
        <artifactId>leveldbjni-all</artifactId>
        <version>1.8</version>
      </dependency>

      <dependency>
        <groupId>com.microsoft.azure</groupId>
        <artifactId>azure-storage</artifactId>
        <version>7.0.1</version>
     </dependency>

      <!--Wildfly openssl dependency is introduced by HADOOP-15669-->
      <dependency>
        <groupId>org.wildfly.openssl</groupId>
        <artifactId>wildfly-openssl</artifactId>
        <version>${openssl-wildfly.version}</version>
      </dependency>
      <dependency>
        <groupId>org.wildfly.openssl</groupId>
        <artifactId>wildfly-openssl-java</artifactId>
        <version>${openssl-wildfly.version}</version>
      </dependency>

      <dependency>
        <groupId>org.threadly</groupId>
        <artifactId>threadly</artifactId>
        <version>4.9.0</version>
      </dependency>

      <dependency>
        <groupId>com.aliyun.oss</groupId>
        <artifactId>aliyun-sdk-oss</artifactId>
        <version>3.13.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
          </exclusion>
        </exclusions>
     </dependency>

      <dependency>
        <groupId>com.qcloud</groupId>
        <artifactId>cos_api-bundle</artifactId>
        <version>5.6.19</version>
      </dependency>

     <dependency>
       <groupId>org.apache.curator</groupId>
       <artifactId>curator-recipes</artifactId>
       <version>${curator.version}</version>
       <exclusions>
         <exclusion>
           <groupId>org.apache.zookeeper</groupId>
           <artifactId>zookeeper</artifactId>
         </exclusion>
         <exclusion>
           <groupId>com.google.guava</groupId>
           <artifactId>guava</artifactId>
         </exclusion>
         <exclusion>
           <groupId>org.slf4j</groupId>
           <artifactId>slf4j-api</artifactId>
         </exclusion>
         <exclusion>
           <groupId>log4j</groupId>
           <artifactId>log4j</artifactId>
         </exclusion>
       </exclusions>
     </dependency>
     <dependency>
       <groupId>org.apache.curator</groupId>
       <artifactId>curator-client</artifactId>
       <version>${curator.version}</version>
       <exclusions>
         <exclusion>
           <groupId>org.apache.zookeeper</groupId>
           <artifactId>zookeeper</artifactId>
         </exclusion>
         <exclusion>
           <groupId>com.google.guava</groupId>
           <artifactId>guava</artifactId>
         </exclusion>
         <exclusion>
           <groupId>org.slf4j</groupId>
           <artifactId>slf4j-api</artifactId>
         </exclusion>
       </exclusions>
     </dependency>
     <dependency>
       <groupId>org.apache.curator</groupId>
       <artifactId>curator-framework</artifactId>
       <version>${curator.version}</version>
       <exclusions>
         <exclusion>
           <groupId>org.apache.zookeeper</groupId>
           <artifactId>zookeeper</artifactId>
         </exclusion>
         <exclusion>
           <groupId>com.google.guava</groupId>
           <artifactId>guava</artifactId>
         </exclusion>
         <exclusion>
           <groupId>org.slf4j</groupId>
           <artifactId>slf4j-api</artifactId>
         </exclusion>
       </exclusions>
     </dependency>
     <dependency>
       <groupId>org.apache.curator</groupId>
       <artifactId>curator-test</artifactId>
       <version>${curator.version}</version>
       <exclusions>
         <exclusion>
           <groupId>org.apache.zookeeper</groupId>
           <artifactId>zookeeper</artifactId>
         </exclusion>
         <exclusion>
           <groupId>com.google.guava</groupId>
           <artifactId>guava</artifactId>
         </exclusion>
       </exclusions>
     </dependency>
     <dependency>
       <groupId>org.bouncycastle</groupId>
       <artifactId>bcprov-jdk15on</artifactId>
       <version>${bouncycastle.version}</version>
     </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcpkix-jdk15on</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>

     <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>${joda-time.version}</version>
     </dependency>

      <dependency>
          <groupId>com.nimbusds</groupId>
          <artifactId>nimbus-jose-jwt</artifactId>
          <version>${nimbus-jose-jwt.version}</version>
          <scope>compile</scope>
          <exclusions>
          <exclusion>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>dnsjava</groupId>
        <artifactId>dnsjava</artifactId>
        <version>${dnsjava.version}</version>
      </dependency>

      <dependency>
        <!-- HACK.  Transitive dependency for nimbus-jose-jwt.  Needed for
             packaging.  Please re-check this version when updating
             nimbus-jose-jwt.  Please read HADOOP-14903 for more details.
          -->
        <groupId>net.minidev</groupId>
        <artifactId>json-smart</artifactId>
        <version>${json-smart.version}</version>
      </dependency>
      <dependency>
        <groupId>org.skyscreamer</groupId>
        <artifactId>jsonassert</artifactId>
        <version>1.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-common</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-common</artifactId>
        <version>${hbase.version}</version>
        <scope>test</scope>
        <classifier>tests</classifier>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-client</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <!-- exclude jdk.tools (1.7) as we're not managing it -->
          <exclusion>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-server</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-server</artifactId>
        <version>${hbase.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-server</artifactId>
        <version>${hbase.version}</version>
        <scope>test</scope>
        <classifier>tests</classifier>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-testing-util</artifactId>
        <version>${hbase.version}</version>
        <scope>test</scope>
        <optional>true</optional>
        <exclusions>
          <exclusion>
            <groupId>org.jruby</groupId>
            <artifactId>jruby-complete</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-minicluster</artifactId>
          </exclusion>
          <exclusion>
            <artifactId>jdk.tools</artifactId>
            <groupId>jdk.tools</groupId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
        </dependency>
        <dependency>
          <groupId>org.apache.kerby</groupId>
          <artifactId>kerb-simplekdc</artifactId>
          <version>${kerby.version}</version>
        </dependency>
        <dependency>
          <groupId>org.apache.kerby</groupId>
          <artifactId>kerb-core</artifactId>
          <version>${kerby.version}</version>
        </dependency>
        <dependency>
          <groupId>org.apache.geronimo.specs</groupId>
          <artifactId>geronimo-jcache_1.0_spec</artifactId>
          <version>${jcache.version}</version>
        </dependency>
        <dependency>
          <groupId>org.ehcache</groupId>
          <artifactId>ehcache</artifactId>
          <version>${ehcache.version}</version>
        </dependency>
        <dependency>
          <groupId>com.zaxxer</groupId>
          <artifactId>HikariCP-java7</artifactId>
          <version>${hikari.version}</version>
        </dependency>
        <dependency>
          <groupId>com.microsoft.sqlserver</groupId>
          <artifactId>mssql-jdbc</artifactId>
          <version>${mssql.version}</version>
        </dependency>
        <dependency>
          <groupId>io.swagger</groupId>
          <artifactId>swagger-annotations</artifactId>
          <version>${swagger-annotations-version}</version>
        </dependency>
        <dependency>
          <groupId>com.fasterxml.jackson.jaxrs</groupId>
          <artifactId>jackson-jaxrs-json-provider</artifactId>
          <version>${jackson2.version}</version>
        </dependency>
        <dependency>
          <groupId>org.yaml</groupId>
          <artifactId>snakeyaml</artifactId>
          <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
          <groupId>org.hamcrest</groupId>
          <artifactId>hamcrest-library</artifactId>
          <version>1.3</version>
        </dependency>
        <dependency>
          <groupId>org.assertj</groupId>
          <artifactId>assertj-core</artifactId>
          <version>${assertj.version}</version>
          <scope>test</scope>
        </dependency>
      <dependency>
        <groupId>org.jruby.jcodings</groupId>
        <artifactId>jcodings</artifactId>
        <version>1.0.13</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>1.2.1</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna</artifactId>
        <version>${jna.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xerial.snappy</groupId>
        <artifactId>snappy-java</artifactId>
        <version>${snappy-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lz4</groupId>
        <artifactId>lz4-java</artifactId>
        <version>${lz4-java.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper-maven-plugin.version}</version>
        </plugin>
        <!-- We include the configuration for license-maven-plugin to correct
             maven-shade-plugin generated poms because it's always the same. We
             can't simply configure the plugin because we must ensure execution
             happens in the package phase after the shade plugin runs.
          -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>1.10</version>
          <configuration>
            <canUpdateCopyright>false</canUpdateCopyright>
            <roots><root>${project.basedir}</root></roots>
          </configuration>
          <executions>
            <execution>
              <id>update-pom-license</id>
              <goals>
                <goal>update-file-header</goal>
              </goals>
              <phase>package</phase>
              <configuration>
                <licenseName>apache_v2</licenseName>
                <includes>
                  <include>dependency-reduced-pom.xml</include>
                </includes>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven-clean-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <source>${javac.version}</source>
            <target>${javac.version}</target>
            <useIncrementalCompilation>false</useIncrementalCompilation>
            <!-- add flags to generate native headers -->
            <compilerArgs combine.children="append">
              <arg>-h</arg>
              <arg>${project.build.directory}/native/javah/</arg>
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven-shade-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
          <configuration>
            <doclint>all</doclint>
            <additionalOptions>
                <additionalOption>-Xmaxwarns 10000</additionalOption>
            </additionalOptions>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>make-maven-plugin</artifactId>
          <version>${make-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.avro</groupId>
          <artifactId>avro-maven-plugin</artifactId>
          <version>${avro.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>${exec-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pdf-plugin</artifactId>
          <version>${maven-pdf-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.hadoop</groupId>
          <artifactId>hadoop-maven-plugins</artifactId>
          <version>${hadoop.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>yuicompressor-maven-plugin</artifactId>
          <version>${yuicompressor-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.github.klieber</groupId>
          <artifactId>phantomjs-maven-plugin</artifactId>
          <version>${phantomjs-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.github.searls</groupId>
          <artifactId>jasmine-maven-plugin</artifactId>
          <version>${jasmine-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.github.eirslett</groupId>
          <artifactId>frontend-maven-plugin</artifactId>
          <version>${frontend-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.xolstice.maven.plugins</groupId>
          <artifactId>protobuf-maven-plugin</artifactId>
          <version>${protobuf-maven-plugin.version}</version>
          <extensions>true</extensions>
          <configuration>
            <protocArtifact>
              com.google.protobuf:protoc:${hadoop.protobuf.version}:exe:${os.detected.classifier}
            </protocArtifact>
            <attachProtoSources>false</attachProtoSources>
          </configuration>
          <executions>
            <execution>
              <id>src-compile-protoc</id>
              <phase>generate-sources</phase>
              <goals>
                <goal>compile</goal>
              </goals>
              <configuration>
                <includeDependenciesInDescriptorSet>false</includeDependenciesInDescriptorSet>
                <protoSourceRoot>${basedir}/src/main/proto</protoSourceRoot>
                <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
                <clearOutputDirectory>false</clearOutputDirectory>
                <skip>true</skip>
              </configuration>
            </execution>
            <execution>
              <id>src-test-compile-protoc</id>
              <phase>generate-test-sources</phase>
              <goals>
                <goal>test-compile</goal>
              </goals>
              <configuration>
                <protoTestSourceRoot>${basedir}/src/test/proto</protoTestSourceRoot>
                <outputDirectory>${project.build.directory}/generated-test-sources/java</outputDirectory>
                <clearOutputDirectory>false</clearOutputDirectory>
                <skip>true</skip>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <!-- Modify the generated source to use our shaded protobuf -->
        <plugin>
          <groupId>com.google.code.maven-replacer-plugin</groupId>
          <artifactId>replacer</artifactId>
          <version>${maven-replacer-plugin.version}</version>
          <executions>
            <execution>
              <id>replace-generated-sources</id>
              <phase>process-sources</phase>
              <goals>
                <goal>replace</goal>
              </goals>
              <configuration>
                <skip>true</skip>
                <basedir>${project.build.directory}/generated-sources</basedir>
                <includes>
                  <include>**/*.java</include>
                </includes>
                <replacements>
                  <replacement>
                    <token>([^\.])com.google.protobuf</token>
                    <value>$1${hadoop-thirdparty-shaded-protobuf-prefix}</value>
                  </replacement>
                </replacements>
              </configuration>
            </execution>
            <execution>
              <id>replace-generated-test-sources</id>
              <phase>process-test-resources</phase>
              <goals>
                <goal>replace</goal>
              </goals>
              <configuration>
                <skip>true</skip>
                <basedir>${project.build.directory}/generated-test-sources</basedir>
                <includes>
                  <include>**/*.java</include>
                </includes>
                <replacements>
                  <replacement>
                    <token>([^\.])com.google.protobuf</token>
                    <value>$1${hadoop-thirdparty-shaded-protobuf-prefix}</value>
                  </replacement>
                </replacements>
              </configuration>
            </execution>
            <execution>
              <id>replace-sources</id>
              <phase>process-sources</phase>
              <goals>
                <goal>replace</goal>
              </goals>
              <configuration>
                <skip>true</skip>
                <basedir>${basedir}/src/main/java</basedir>
                <includes>
                  <include>**/*.java</include>
                </includes>
                <replacements>
                  <replacement>
                    <token>([^\.])com.google.protobuf</token>
                    <value>$1${hadoop-thirdparty-shaded-protobuf-prefix}</value>
                  </replacement>
                </replacements>
              </configuration>
            </execution>
            <execution>
              <id>replace-test-sources</id>
              <phase>process-test-sources</phase>
              <goals>
                <goal>replace</goal>
              </goals>
              <configuration>
                <skip>true</skip>
                <basedir>${basedir}/src/test/java</basedir>
                <includes>
                  <include>**/*.java</include>
                </includes>
                <replacements>
                  <replacement>
                    <token>([^\.])com.google.protobuf</token>
                    <value>$1${hadoop-thirdparty-shaded-protobuf-prefix}</value>
                  </replacement>
                </replacements>
              </configuration>
            </execution>
            <execution>
              <id>replace-guava</id>
              <phase>process-sources</phase>
              <goals>
                <goal>replace</goal>
              </goals>
              <configuration>
                <skip>false</skip><!--This will run for all modules-->
                <basedir>${basedir}</basedir>
                <includes>
                  <include>src/main/java/**/*.java</include>
                  <include>src/test/java/**/*.java</include>
                </includes>
                <replacements>
                  <replacement>
                    <token>([^\.])com.google.common</token>
                    <value>$1${hadoop-thirdparty-shaded-guava-prefix}</value>
                  </replacement>
                </replacements>
              </configuration>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>${os-maven-plugin.version}</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <configuration>
          <filesets>
            <fileset>
              <directory>${project.basedir}</directory>
              <includes>
                <include>dependency-reduced-pom.xml</include>
              </includes>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>create-testdirs</id>
            <phase>validate</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <mkdir dir="${test.build.dir}"/>
                <mkdir dir="${test.build.data}"/>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <reuseForks>false</reuseForks>
          <forkedProcessTimeoutInSeconds>${surefire.fork.timeout}</forkedProcessTimeoutInSeconds>
          <argLine>${maven-surefire-plugin.argLine}</argLine>
          <environmentVariables>
            <HADOOP_COMMON_HOME>${hadoop.common.build.dir}</HADOOP_COMMON_HOME>
            <!-- HADOOP_HOME required for tests on Windows to find winutils -->
            <HADOOP_HOME>${hadoop.common.build.dir}</HADOOP_HOME>
            <LD_LIBRARY_PATH>${env.LD_LIBRARY_PATH}:${project.build.directory}/native/target/usr/local/lib:${hadoop.common.build.dir}/native/target/usr/local/lib</LD_LIBRARY_PATH>
            <DYLD_LIBRARY_PATH>${env.DYLD_LIBRARY_PATH}:${project.build.directory}/native/target/usr/local/lib:${hadoop.common.build.dir}/native/target/usr/local/lib</DYLD_LIBRARY_PATH>
            <MALLOC_ARENA_MAX>4</MALLOC_ARENA_MAX>
          </environmentVariables>
          <trimStackTrace>false</trimStackTrace>
          <systemPropertyVariables>

            <hadoop.log.dir>${project.build.directory}/log</hadoop.log.dir>
            <hadoop.tmp.dir>${project.build.directory}/tmp</hadoop.tmp.dir>

            <!-- TODO: all references in testcases should be updated to this default -->
            <test.build.dir>${test.build.dir}</test.build.dir>
            <test.build.data>${test.build.data}</test.build.data>
            <test.build.webapps>${test.build.webapps}</test.build.webapps>
            <test.cache.data>${test.cache.data}</test.cache.data>
            <test.build.classes>${project.build.directory}/test-classes</test.build.classes>

            <java.net.preferIPv4Stack>true</java.net.preferIPv4Stack>
            <java.security.krb5.conf>${project.build.directory}/test-classes/krb5.conf</java.security.krb5.conf>
            <java.security.egd>${java.security.egd}</java.security.egd>
            <require.test.libhadoop>${require.test.libhadoop}</require.test.libhadoop>
          </systemPropertyVariables>
          <includes>
            <include>**/Test*.java</include>
          </includes>
          <excludes>
            <exclude>**/${test.exclude}.java</exclude>
            <exclude>${test.exclude.pattern}</exclude>
            <exclude>**/Test*$*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pdf-plugin</artifactId>
        <configuration>
          <outputDirectory>${project.reporting.outputDirectory}</outputDirectory>
          <includeReports>false</includeReports>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>depcheck</id>
            <configuration>
              <rules>
                <DependencyConvergence>
                  <uniqueVersions>false</uniqueVersions>
                </DependencyConvergence>
                <bannedDependencies>
                  <excludes>
                    <exclude>cglib:cglib:</exclude>
                    <exclude>com.sun.jersey:*</exclude>
                    <exclude>com.sun.jersey.contribs:*</exclude>
                    <exclude>com.sun.jersey.jersey-test-framework:*</exclude>
                    <exclude>com.google.inject:guice</exclude>
                    <exclude>org.ow2.asm:asm</exclude>

                    <exclude>org.slf4j:slf4j-log4j12</exclude>
                    <exclude>log4j:log4j</exclude>
                  </excludes>
                  <includes>
                    <!-- for JDK 8 support -->
                    <include>cglib:cglib:3.2.0</include>
                    <include>com.google.inject:guice:4.0</include>
                    <include>com.sun.jersey:jersey-core:1.19</include>
                    <include>com.sun.jersey:jersey-servlet:1.19</include>
                    <include>com.sun.jersey:jersey-json:1.19</include>
                    <include>com.sun.jersey:jersey-server:1.19</include>
                    <include>com.sun.jersey:jersey-client:1.19</include>
                    <include>com.sun.jersey:jersey-grizzly2:1.19</include>
                    <include>com.sun.jersey:jersey-grizzly2-servlet:1.19</include>
                    <include>com.sun.jersey.jersey-test-framework:jersey-test-framework-core:1.19</include>
                    <include>com.sun.jersey.jersey-test-framework:jersey-test-framework-grizzly2:1.19</include>
                    <include>com.sun.jersey.contribs:jersey-guice:1.19</include>
                    <include>org.ow2.asm:asm:5.0.0</include>
                  </includes>
                </bannedDependencies>
              </rules>
            </configuration>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>verify</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>os.linux</id>
      <activation>
        <os>
          <family>!Mac</family>
        </os>
      </activation>
      <properties>
        <build.platform>${os.name}-${os.arch}-${sun.arch.data.model}</build.platform>
      </properties>
    </profile>
    <profile>
      <id>os.mac</id>
      <activation>
        <os>
          <family>Mac</family>
        </os>
      </activation>
      <properties>
        <build.platform>Mac_OS_X-${sun.arch.data.model}</build.platform>
        <!-- To make protoc work on Apple Silicon via fallback -->
        <os.detected.classifier>osx-x86_64</os.detected.classifier>
      </properties>
    </profile>
    <profile>
      <id>native-win</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <!-- We must use this exact string for egd on Windows, because the -->
        <!-- JVM will check for an exact string match on this.  If found, it -->
        <!-- will use a native entropy provider.  This will not really -->
        <!-- attempt to open a file at this path. -->
        <java.security.egd>file:/dev/urandom</java.security.egd>
        <bundle.zstd.in.bin>true</bundle.zstd.in.bin>
        <bundle.openssl.in.bin>true</bundle.openssl.in.bin>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <environmentVariables>
                <!-- Specify where to look for the native DLL on Windows -->
                <PATH>${env.PATH};${hadoop.common.build.dir}/bin;${zstd.lib}</PATH>
                <PATH>${env.PATH};${hadoop.common.build.dir}/bin;${openssl.lib}</PATH>
                <PATH>${env.PATH};${hadoop.common.build.dir}/bin;${isal.lib}</PATH>
              </environmentVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>test-patch</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <fork>true</fork>
              <source>${javac.version}</source>
              <target>${javac.version}</target>
              <compilerArgs>
                <arg>-Xlint</arg>
                <arg>-Xlint:unchecked</arg>
                <arg>-Xmaxwarns</arg>
                <arg>9999</arg>
              </compilerArgs>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>dist</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <!-- build javadoc jars per jar for publishing to maven -->
                <id>module-javadocs</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <!-- builds source jars and attaches them to the project for publishing -->
                <id>hadoop-java-sources</id>
                <phase>package</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                  <goal>test-jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>dist-enforce</id>
                <phase>package</phase>
                <goals>
                 <goal>enforce</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>resource-bundle</id>
      <!-- activate profile only when we build the whole project from project root directory -->
      <activation>
        <file>
          <exists>${env.PWD}/LICENSE.txt</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-remote-resources-plugin</artifactId>
            <version>${maven-remote-resources-plugin.version}</version>
            <executions>
              <execution>
                <goals>
                  <goal>process</goal>
                </goals>
                <configuration>
                  <resourceBundles>
                    <resourceBundle>org.apache.hadoop:hadoop-build-tools:${hadoop.version}</resourceBundle>
                  </resourceBundles>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- The profile for building against HBase 1.2.x
     This is the default.
     -->
    <profile>
      <id>hbase1</id>
      <activation>
        <property>
          <name>!hbase.profile</name>
        </property>
      </activation>
      <properties>
        <hbase.version>${hbase.one.version}</hbase.version>
        <hbase-compatible-hadoop.version>2.5.1</hbase-compatible-hadoop.version>
        <hbase-compatible-guava.version>12.0.1</hbase-compatible-guava.version>
        <hbase-server-artifactid>hadoop-yarn-server-timelineservice-hbase-server-1</hbase-server-artifactid>
      </properties>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>${hbase-server-artifactid}</artifactId>
            <version>${hadoop.version}</version>
          </dependency>
        </dependencies>
      </dependencyManagement>
    </profile>
    <!-- The profile for building against HBase 2.0.0.
     Activate using: mvn -Dhbase.profile=2.0
    -->
    <profile>
      <id>hbase2</id>
      <activation>
        <property>
          <name>hbase.profile</name>
          <value>2.0</value>
        </property>
      </activation>
      <properties>
        <hbase.version>${hbase.two.version}</hbase.version>
        <hbase-compatible-hadoop.version>3.0.0</hbase-compatible-hadoop.version>
        <hbase-compatible-guava.version>11.0.2</hbase-compatible-guava.version>
        <hbase-server-artifactid>hadoop-yarn-server-timelineservice-hbase-server-2</hbase-server-artifactid>
      </properties>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>${hbase-server-artifactid}</artifactId>
            <version>${hadoop.version}</version>
          </dependency>
        </dependencies>
      </dependencyManagement>
    </profile>
    <profile>
      <id>jdk11</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <additionalOptions>
                <!-- TODO: remove -html4 option to generate html5 docs when we stop supporting JDK8 -->
                <additionalOption>-html4</additionalOption>
              </additionalOptions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <repositories>
  </repositories>
</project>
