package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@ApiModel(value = "EvaluationTypeVO对象", description = "项目评价类型")
@Data
public class EvaluationTypeVO extends ObjectVO implements Serializable {

    /**
     * 评价类型编码
     */
    @ApiModelProperty(value = "评价类型编码")
    private String evaluationType;

    /**
     * 评价类型名字
     */
    @ApiModelProperty(value = "评价类型名字")
    private String evaluationTypeName;

}
