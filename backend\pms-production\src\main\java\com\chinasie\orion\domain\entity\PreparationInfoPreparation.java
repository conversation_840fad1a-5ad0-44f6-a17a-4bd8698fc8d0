package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PreparationInfoPreparation Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
@TableName(value = "pmsx_preparation_info_preparation")
@ApiModel(value = "PreparationInfoPreparationEntity对象", description = "准备信息维护")
@Data

public class PreparationInfoPreparation extends  ObjectEntity  implements Serializable{

    /**
     * 组织机构
     */
    @ApiModelProperty(value = "组织机构")
    @TableField(value = "org_structure")
    private String orgStructure;

    /**
     * 工单准备
     */
    @ApiModelProperty(value = "工单准备")
    @TableField(value = "job_prepare")
    private String jobPrepare;

    /**
     * 重大项目评审
     */
    @ApiModelProperty(value = "重大项目评审")
    @TableField(value = "important_project")
    private String importantProject;

    /**
     * 参修人员入场
     */
    @ApiModelProperty(value = "参修人员入场")
    @TableField(value = "part_user_join")
    private String partUserJoin;

    /**
     * 关注人员面谈
     */
    @ApiModelProperty(value = "关注人员面谈")
    @TableField(value = "like_person")
    private String likePerson;

    /**
     * 工具入场
     */
    @ApiModelProperty(value = "工具入场")
    @TableField(value = "tool_join")
    private String toolJoin;

    /**
     * 安全质量管理
     */
    @ApiModelProperty(value = "安全质量管理")
    @TableField(value = "safety_quality_env")
    private String safetyQualityEnv;

    /**
     * 工作包
     */
    @ApiModelProperty(value = "工作包")
    @TableField(value = "job_package")
    private String jobPackage;

    /**
     * 大修前培训
     */
    @ApiModelProperty(value = "大修前培训")
    @TableField(value = "major_train")
    private String majorTrain;

    /**
     * 交底演练
     */
    @ApiModelProperty(value = "交底演练")
    @TableField(value = "publish_drill")
    private String publishDrill;

    /**
     * 后期保障
     */
    @ApiModelProperty(value = "后期保障")
    @TableField(value = "rear_support")
    private String rearSupport;

    /**
     * 大修动员会
     */
    @ApiModelProperty(value = "大修动员会")
    @TableField(value = "major_rally")
    private String majorRally;

    /**
     * 大修准备率
     */
    @ApiModelProperty(value = "大修准备率")
    @TableField(value = "major_prepare_rate")
    private String majorPrepareRate;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;


}
