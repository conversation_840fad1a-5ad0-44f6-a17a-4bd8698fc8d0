import {
  DataStatusTag, getDictByNumber, isPower, openDrawer, openModal,
} from 'lyra-component-vue3';
import {
  createVNode, h, Ref, ref,
} from 'vue';
import { message, Modal, Tag } from 'ant-design-vue';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import dayjs from 'dayjs';
import Api from '/@/api';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import DistributeTask from './pages/components/DistributeTask.vue';
import Relationship from './pages/components/Relationship.vue';
import CompleteTask from './pages/components/CompleteTask.vue';
import VerifyTask from './pages/components/VerifyTask.vue';
import ReasonForTask from './pages/components/ReasonForTask.vue';
import ExpediteTask from './pages/components/ExpediteTask.vue';
import DelegateRow from './pages/components/DelegateRow.vue';
import {
  EditTaskRowDate,
  EditTaskRowDay,
  EditTaskRowName,
  EditTaskRowRemark,
  EditTaskRowSelect,
  EditTaskRowUser,
} from './pages/components/EstablishmentTaskEdit';

const selectValueOptions:Ref<Record<any, any>[]> = ref([]);
getDictByNumber('process_object_type').then((res) => {
  selectValueOptions.value = res;
});
/**
 *
 * @param amount 要格式化的金额（必需）
 * @param decimalCount 小数点后要显示的位数，默认为 2
 * @param decimal 小数点分隔符，默认为 "."
 * @param thousands 分隔符用于千位数，默认为 ","
 * @param multiple 格式化的倍数
 */
function formatMoney(amount, decimalCount = 2, decimal = '.', thousands = ',', multiple = 1) {
  if (amount == null || isNaN(amount)) return '0';
  decimalCount = Math.abs(decimalCount);
  decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

  const negativeSign = amount < 0 ? '-' : '';
  amount = Number(amount) / multiple;
  let iLength = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
  let j = (iLength.length > 3) ? iLength.length % 3 : 0;

  return negativeSign + (j ? iLength.substr(0, j) + thousands : '') + iLength.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) + (decimalCount ? decimal + Math.abs(amount - Number(iLength)).toFixed(decimalCount).slice(2) : '');
}
function getTableData(params) {
  return Promise.resolve([
    {
      name: '模拟数据23323',
      id: 11111,
    },
  ]);
}

/**
 *
 * @param tableData
 * @param index
 * @param indexData
 * @param level
 * @param topSortKey 记录置顶的id
 */

function initEstablishmentTableData(tableData, index, indexData, level, topSortKey:string[]) {
  let indexNumOrder = 1;
  let expandedRowKeys = [];
  tableData.forEach((item, indexNum) => {
    if (item.topSort !== 0 && !topSortKey.includes(item.id)) {
      item.key = `${item.id}_Top`;
      topSortKey.push(item.id);
    } else {
      item.key = item.id;
      indexData.value.push(item);
      if (index) {
        item.index = index + (indexNum + 1);
      } else {
        item.index = indexNumOrder;
        indexNumOrder++;
      }
    }
    if (item.children && item.children.length === 0) {
      delete item.children;
    } else if (item.children && item.children.length) {
      expandedRowKeys.push(item.id);
      let newData = initEstablishmentTableData(item.children, `${item.index}.`, indexData, level + 1, topSortKey);
      item.children = newData.tableData;
      expandedRowKeys = expandedRowKeys.concat(newData.expandedRowKeys);
    }
  });
  return {
    tableData,
    expandedRowKeys,
  };
}
function initDocumentTableData(tableData, index, indexData) {
  let expandedRowKeys = [];
  tableData.forEach((item, indexNum) => {
    item.key = item.id;
    indexData.value.push(item);
    if (index) {
      item.index = index + (indexNum + 1);
    } else {
      item.index = indexNum + 1;
    }
    if (item.children && item.children.length === 0) {
      delete item.children;
    } else if (item.children && item.children.length) {
      expandedRowKeys.push(item.id);
      let newData = initDocumentTableData(item.children, `${item.index}.`, indexData);
      item.children = newData.tableData;
      expandedRowKeys = expandedRowKeys.concat(newData.expandedRowKeys);
    }
  });
  return {
    tableData,
    expandedRowKeys,
  };
}
function initColumns(indexData, router, userInfo) {
  return [
    {
      title: '序号',
      dataIndex: 'index',
      width: 100,
      fixed: 'left',
      slots: { customRender: 'index' },
      customRender({ text, record }) {
        return indexData?.value.find((v) => v?.id === record?.id)?.index || '';
      },
    },
    {
      title: '',
      dataIndex: '',
      width: 50,
      fixed: 'left',
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      minWidth: 300,
      fixed: 'left',
      customRender({ text, record }) {
        const editRowNameRef = ref();
        return h(EditTaskRowName, {
          record,
          ref: editRowNameRef,
          onChange: async (value, changeFocus) => {
            record.name = value;
            await saveRowData(record);
            changeFocus(false);
            // await onTypeChange(record);
            // editRowNameRef.value.changeItemType();
          },
        });
      },
    },
    {
      title: '情况',
      dataIndex: 'circumstance',
      width: 120,
      customRender({ record }) {
        return h(Tag, { color: statusColor[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
      },
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '处理对象',
      dataIndex: 'processObject',
      width: 150,
      customRender({ text, record }) {
        const editRowRef = ref();
        return h(EditTaskRowSelect, {
          record,
          ref: editRowRef,
          options: selectValueOptions.value,
          onChange: async (itemData, changeItemType) => {
            record.processObject = itemData.number;
            record.processObjectName = itemData.description;
            await saveRowData(record);
            changeItemType();
            // editRowRef.value.changeItemType();
          },
        });
      },
    },
    {
      title: '处理实例',
      dataIndex: 'processInstancesName',
      width: 120,
    },
    {
      title: '任务开始日期',
      dataIndex: 'beginTime',
      width: 180,
      customRender({ text, record }) {
        const editRowTimeRef = ref();
        return isCreator(record, userInfo) ? h(EditTaskRowDate, {
          record,
          ref: editRowTimeRef,
          fieldName: 'beginTime',
          onChange: async (value, changeItemType) => {
            record.beginTime = value;
            if (record.durationDays) {
              record.endTime = dayjs(record.beginTime).add(record.durationDays, 'day').format('YYYY-MM-DD');
            }
            await saveRowData(record);
            changeItemType();
          },
        }) : dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '工期（天）',
      dataIndex: 'durationDays',
      width: 140,
      customRender({ text, record }) {
        const editRowRef = ref();
        return isCreator(record, userInfo) ? h(EditTaskRowDay, {
          record,
          ref: editRowRef,
          onChange: async (value, changeItemType) => {
            record.durationDays = value;
            if (record.beginTime) {
              record.endTime = dayjs(record.beginTime).add(record.durationDays, 'day').format('YYYY-MM-DD');
            }
            if (record.endTime) {
              record.beginTime = dayjs(record.endTime).subtract(record.durationDays, 'day').format('YYYY-MM-DD');
            }
            await saveRowData(record);
            changeItemType();
            // await onTypeChange(record);
            // editRowNameRef.value.changeItemType();
          },
        }) : text;
      },
    },
    {
      title: '任务结束日期',
      dataIndex: 'endTime',
      width: 180,
      customRender({ text, record }) {
        const editRowTimeRef = ref();
        return isCreator(record, userInfo) ? h(EditTaskRowDate, {
          record,
          ref: editRowTimeRef,
          fieldName: 'endTime',
          onChange: async (value, changeItemType) => {
            record.endTime = value;
            if (record.durationDays) {
              record.beginTime = dayjs(record.endTime).subtract(record.durationDays, 'day').format('YYYY-MM-DD');
            }
            await saveRowData(record);
            changeItemType();
            // await onTypeChange(record);
            // editRowNameRef.value.changeItemType();
          },
        }) : dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '实际任务开始日期',
      dataIndex: 'actualBeginTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '——';
      },
    },
    {
      title: '实际任务结束日期',
      dataIndex: 'actualEndTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '——';
      },
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
      width: 150,

      customRender({ text, record }) {
        return isCreator(record, userInfo) ? h(EditTaskRowUser, {
          record,
          onChange: async (userData) => {
            record.rspUserName = userData.name;
            record.rspUser = userData.id;
            record.rspDeptName = userData.simpleUser?.orgName;
            record.rspDept = userData.simpleUser?.orgId;
            await saveRowData(record);
          },
        }) : text;
      },
    },
    {
      title: '责任部门',
      dataIndex: 'rspDeptName',
      width: 120,
    },
    {
      title: '任务描述',
      dataIndex: 'remark',
      width: 200,
      customRender({ text, record }) {
        const editRowRef = ref();
        return isCreator(record, userInfo) ? h(EditTaskRowRemark, {
          record,
          ref: editRowRef,
          onChange: async (value, changeItemType) => {
            record.remark = value;
            await saveRowData(record);
            changeItemType();
            // await onTypeChange(record);
            // editRowNameRef.value.changeItemType();
          },
        }) : text;
      },
    },
    {
      title: '下达人',
      dataIndex: 'issuedUserName',
      width: 120,
    },
    {
      title: '下发时间',
      dataIndex: 'issueTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];
}
async function saveRowData(record) {
  let params = JSON.parse(JSON.stringify(record));
  // beginTime,endTime,actualBeginTime,actualEndTime
  if (params.beginTime) {
    params.beginTime = dayjs(params.beginTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.endTime) {
    params.endTime = dayjs(params.endTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.actualBeginTime) {
    params.actualBeginTime = dayjs(params.actualBeginTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.actualEndTime) {
    params.actualEndTime = dayjs(params.actualEndTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.modifyTime) {
    params.modifyTime = dayjs(params.modifyTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.createTime) {
    params.createTime = dayjs(params.createTime).format('YYYY-MM-DD HH:mm:ss');
  }
  if (params.issueTime) {
    params.issueTime = dayjs(params.issueTime).format('YYYY-MM-DD HH:mm:ss');
  }
  await new Api('/pms').fetch(record, 'collaborativeCompilationTask/edit', 'PUT');
  message.success('保存成功');
}
function quoteDocumentTemplate(component:any, update: () => void, detailsData) {
  const quoteTemplateRef = ref();
  openModal({
    title: '引用模版',
    width: 1100,
    height: 700,
    content(h) {
      return h(component, {
        ref: quoteTemplateRef,
        getTableData: getTableTemplateData,
        getTreeApi,
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
          },
          {
            title: '文档模板名称',
            dataIndex: 'name',
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '版本',
            dataIndex: 'revId',
          },
          {
            title: '创建人',
            dataIndex: 'creatorName',
          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            type: 'dateTime',
          },
        ],
        showLeftTree: true,
        selectType: 'radio',
      });
    },
    async onOk() {
      let modalData = await quoteTemplateRef.value.getFormData();
      let params = {
        approvalId: detailsData.value.id,
        modelId: modalData.selectedRowKeys[0],
      };
      new Api('/pms').fetch(params, `collaborativeCompilationTask/getDocument?approvalId=${detailsData.value.id}&modelId=${modalData.selectedRowKeys[0]}`, 'PUT').then((res) => {
        message.success('引用模版成功');
        update();
      });
    },
  });
}
function getTreeApi() {
  return new Api('/pms').fetch('', 'documentModelLibraryDir', 'GET');
}
function getTableTemplateData(id, params) {
  if (Array.isArray(params.searchConditions) && params.searchConditions.length > 0) {
    params.searchConditions[0].push({
      field: 'status',
      fieldType: 'Integer',
      values: ['130'],
      queryType: 'eq',
    });
  } else {
    params.searchConditions = [
      [
        {
          field: 'status',
          fieldType: 'Integer',
          values: ['130'],
          queryType: 'eq',
        },
      ],
    ];
  }
  return new Api('/pms').fetch(params, `documentModelLibrary/page/${id}`, 'POST');
}

function initActions(userInfo, upDateTable, indexData) {
  return [

    {
      text: '删除',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_02', record?.rdAuthList),
      onClick: (record) => {
        // actionClick('delete', record);
        deleteBatchData([record.id], 'one', upDateTable);
      },
    },
    {
      text: '置顶',
      isShow: (record) => isCreator(userInfo, record) && record.topSort === 0 && isPower('PMS_XMLX_container_03_table_button_03', record?.rdAuthList), // isPower('PMS_XMXQ_container_03_01_02_button_07', record?.rdAuthList) && record.topSort === 0 && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        new Api('/pms').fetch('', `collaborativeCompilationTask/top/${record.id}`, 'PUT').then((res) => {
          message.success('置顶成功');
          upDateTable();
        });
      },
    },
    {
      text: '取消置顶',
      isShow: (record) => isCreator(userInfo, record) && record.topSort !== 0 && isPower('PMS_XMLX_container_03_table_button_04', record?.rdAuthList), // isPower('PMS_XMXQ_container_03_01_02_button_08', record?.rdAuthList) && record.topSort !== 0 && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        new Api('/pms').fetch('', `collaborativeCompilationTask/unTop/${record.id}`, 'PUT').then((res) => {
          message.success('取消置顶成功');
          upDateTable();
        });
      },
    },
    {
      text: '上移',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_05', record?.rdAuthList),
      onClick: (record) => {
        new Api('/pms').fetch('', `collaborativeCompilationTask/up/${record.id}`, 'PUT').then((res) => {
          message.success('上移成功');
          upDateTable();
        });
      },
    },
    {
      text: '下移',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_06', record?.rdAuthList),
      onClick: (record) => {
        new Api('/pms').fetch('', `collaborativeCompilationTask/down/${record.id}`, 'PUT').then((res) => {
          message.success('下移成功');
          upDateTable();
        });
      },
    },
    {
      text: '催办',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_07', record?.rdAuthList),
      onClick: (record) => {
        expediteTaskRow(record, upDateTable);
      },
    },
    {
      text: '撤回',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_08', record?.rdAuthList),
      onClick: (record) => {
        // //actionClick('revocation', record);
        revocationChange(record, indexData, upDateTable);
      },
    },
    {
      text: '退回',
      isShow: (record) => isRespUser(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_09', record?.rdAuthList),
      onClick: (record) => {
        sendBackRow(record, upDateTable);
      },
    },
    {
      text: '转办',
      isShow: (record) => isRespUser(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_10', record?.rdAuthList),
      onClick: (record) => {
        delegateRowTask(record, upDateTable);
      },
    },
    {
      text: '开始执行',
      // 130 已下发 && 任务责任人
      isShow: (record) => isRespUser(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_11', record?.rdAuthList),
      onClick: (record) => {
        startExecution(record, upDateTable);
      },
    },
    {
      text: '执行完成',
      isShow: (record) => isRespUser(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_12', record?.rdAuthList),
      onClick: (record) => {
        completeTaskRow(record, upDateTable);
      },
    },
    {
      text: '完成确认',
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMLX_container_03_table_button_13', record?.rdAuthList),
      onClick: (record) => {
        verifyTaskRow(record, upDateTable);
      },
    },
  ];
}
/**
 * 任务下发
 * @param modalData
 * @param updateForm
 */
function distributeTaskRow(modalData, updateForm) {
  const modalRef: Ref = ref();
  openModal({
    title: '任务下发',
    width: 700,
    height: 400,
    content() {
      return h(DistributeTask, {
        ref: modalRef,
        modalData,
      });
    },
    async onOk(): Promise<void> {
      await modalRef.value.onSubmit();
      updateForm();
    },
  });
}
function isCreator(userInfo, record) {
  return [record.creator, record.issuedUser].includes(userInfo.id);
}
function isRespUser(userInfo, record) {
  return record.rspUser === userInfo.id;
}

/**
 * 设置前置关系
 * @param drawerData
 * @param updateForm
 */
function relationshipRow(drawerData, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '设置前后置关系',
    width: 700,
    content() {
      return h(Relationship, {
        ref: drawerRef,
        drawerData,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}
function completeTaskRow(record, updateForm) {
  const modelRef: Ref = ref();
  openModal({
    title: '执行完成',
    width: 400,
    height: 300,
    content() {
      return h(CompleteTask, {
        ref: modelRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await modelRef.value.onSubmit();
      updateForm();
    },
  });
}
function verifyTaskRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '任务执行完成确认',
    width: 800,
    content() {
      return h(VerifyTask, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}
function sendBackRow(record, updateForm) {
  const modelRef = ref();
  openModal({
    title: '任务退回',
    height: 350,
    content(h) {
      return h(ReasonForTask, {
        ref: modelRef,
        drawerData: {
          id: record.id,
        },

      });
    },
    async onOk() {
      await modelRef.value.onSubmit();
      updateForm();
    },
  });
}
function delegateRowTask(record, updateForm) {
  const modelRef = ref();
  openModal({
    title: '任务转办',
    height: 450,
    content(h) {
      return h(DelegateRow, {
        ref: modelRef,
        drawerData: {
          id: record.id,
        },
      });
    },
    async onOk() {
      await modelRef.value.onSubmit();
      updateForm();
    },
  });
}
function expediteTaskRow(record, updateForm) {
  const modalRef = ref();
  openModal({
    title: '任务催办',
    width: 650,
    height: 400,
    content(h) {
      return h(ExpediteTask, {
        ref: modalRef,
        record,
      });
    },
    async onOk() {
      await modalRef.value.onSubmit();
      updateForm();
    },
  });
}
function startExecution(record, upDateTable) {
  Modal.confirm({
    title: () => '执行提醒',
    icon: () => createVNode(InfoCircleOutlined),
    content: () => '是否开始执行此任务？',
    async onOk() {
      new Api('/pms').fetch({ id: record.id }, `collaborativeCompilationTask/projectScheme/actualBeginTime/update?id=${record.id}`, 'PUT').then((res) => {
        message.success('开始执行成功');
        upDateTable();
      });
    },
  });
}
function revocationChange(record, indexData, upDateTable) {
  let recordItem:any = {};
  if (indexData.length > 0) {
    recordItem = indexData.value.find((item) => item.id === record.id);
    if (Array.isArray(recordItem.children) && recordItem.children.length > 0) {
      let statusList = getChildrenStatus(recordItem.children);
      if (statusList.some((item) => item !== 130)) {
        message.warning('当前已有子级任务被执行，无法撤回');
        return;
      }
    }
  }
  Modal.confirm({
    title: '撤回提示',
    content: Array.isArray(recordItem.children) && recordItem.children.length > 0 ? '正在撤回父级任务，所有已下发子级任务会同步撤回，撤回后可继续下发。' : '正在撤回任务，撤回后可继续下发。',
    onOk() {
      new Api('/pms').fetch({ id: record.id }, 'collaborativeCompilationTask/revocation', 'GET').then((res) => {
        message.success(res);
        upDateTable();
      });
    },
  });
}
function getChildrenStatus(data) {
  let statusList = [];
  data.forEach((item) => {
    statusList.push(item.status);
    if (Array.isArray(item.children) && item.children.length > 0) {
      statusList = statusList.concat(getChildrenStatus(item.children));
    }
  });
  return statusList;
}
function deleteBatchData(params, type, upDateTable) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    async onOk() {
      await new Api('/pms').fetch(params, 'collaborativeCompilationTask/remove', 'DELETE');
      // await props.deleteQuestionBatchApi(params);
      message.success('删除成功');
      upDateTable();
    },
  });
}
export {
  formatMoney,
  getTableData,
  initEstablishmentTableData,
  initActions,
  initColumns,
  quoteDocumentTemplate,
  distributeTaskRow,
  relationshipRow,
  initDocumentTableData,
  deleteBatchData,
  expediteTaskRow,
  revocationChange,
  sendBackRow,
  delegateRowTask,
  startExecution,
  completeTaskRow,
  verifyTaskRow,
};