<template>
  <STableTree
    v-if="isShowTable && (isPower('XMX_container_02_01_01', powerData)||pageType==='modal')"
    v-model:selectedRowKeys="selectedRowKeys"
    :pageType="pageType"
    :data="dataSTableTree"
    :loading="loadingSTable"
    :formId="formId"
    :onActionClick="onActionClick"
    @checkClick="checkClick"
    @buttonHandle="submitNavButton"
  />

  <!-- 新增/编辑 -->
  <AddPlanNode
    v-if="pageType==='page'"
    @register="register"
    @update="updateData"
  />
  <SearchAll
    v-if="searchAll.visible &&pageType==='page'"
    :data="searchAll"
  />
  <SearchModal
    v-if="pageType==='page'"
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>

<script lang="ts">
import {
  computed, inject, nextTick, onBeforeMount, provide, reactive, ref, toRefs,
} from 'vue';
import { isPower, useDrawer } from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import STableTree from './components/STableTree.vue';
import SearchAll from './components/SearchAll.vue';
import Api from '/@/api';
import SearchModal from './components/SearchModal.vue';
import AddPlanNode from './components/AddPlanNode.vue';
import router from '/@/router';
import { downloadByData as basicDownloadByData } from '/@/views/pms/projectLaborer/utils/download';

export default {
  name: 'ProjectPlan',
  components: {
    SearchAll,
    STableTree,
    SearchModal,
    AddPlanNode,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['checkDetails'],
  setup(props, { emit }) {
    const formData = inject('formData', {});
    const uploadRef = ref(null);
    const [register, { openDrawer }] = useDrawer();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      selectedRowKeys: [],
      dataSTableTree: [],
      loadingSTable: false,
      isShowTable: false,
      searchAll: {
        form: { input: undefined },
      },
      powerData: [],
    });
    state.powerData = inject('powerData');
    function handleOpen(id) {
      let itemData = fingRowData(id, state.dataSTableTree);
      if (itemData.planType !== '0') {
        const url = `/pms/planManagement/planDetails?id=${id}&projectId=${props.formId}`;
        router.push(url);
      } else {
        const url = `/pms/planManagement/milestoneDetails?id=${id}&projectId=${props.formId}`;
        router.push(url);
      }
    }
    function isSelectCheck(type) {
      if (
        state.selectedRowKeys.length === 0
          && (type === 'create-task' || type === 'create-miles')
      ) {
        return undefined;
      }
      if (state.selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (state.selectedRowKeys.length === 1) {
        return state.selectedRowKeys[0];
      }
      if (state.selectedRowKeys.length > 1) {
        if (type === 'delete') {
          return true;
        }
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleAddNew(id, type) {
      openDrawer(true, {
        type: 'add',
        classType: type,
        data: { parentId: id },
      });
    }
    function handleEdit(id, type) {
      new Api('/pms').fetch('', `plan/${id}`, 'GET').then((res) => {
        openDrawer(true, {
          type: 'edit',
          data: res,
        });
      });
    }
    function getPage() {
      const data = { projectId: props.formId };
      state.loadingSTable = true;
      const love = {
        className: 'Plan', // 列表中获取也可根据实际情况手动输入
        moduleName: '项目管理-计划管理-项目计划', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了项目项目计划列表', // 根据实际情况描述
      };
      new Api('/pms', love)
        .fetch(data, 'plan/tree/list', 'POST')
        .then((res) => {
          state.loadingSTable = false;
          state.isShowTable = false;
          state.dataSTableTree = res;
          state.selectedRowKeys = [];
          nextTick(() => {
            state.isShowTable = true;
          });
        })
        .catch(() => {
          state.loadingSTable = false;
        });
    }
    provide('getFormData', computed(() => getPage));

    function updateData(isUpload) {
      getPage();
    }
    function submitNavButton(type) {
      if (type === 'create-task') {
        const id = isSelectCheck(type);
        if (id === undefined || id) {
          handleAddNew(id, type);
        }
      }
      if (type === 'import-field') {
        uploadRef.value.openModal(true);
      }
      if (type === 'close-plan') {
        if (state.selectedRowKeys.length === 0) {
          message.warning('请选择数据关闭');
          return;
        }
        Modal.confirm({
          title: '是否关闭计划',
          onOk() {
            new Api('/pms').fetch(state.selectedRowKeys, 'plan/closeplan', 'POST').then((res) => {
              message.success('关闭计划成功');
              getPage();
            });
          },
        });
      }
      if (type === 'export-plan') {
        if (state.selectedRowKeys.length > 1) {
          message.warning('只能选择一条数据进行导出');
        }
        let fileId = state.selectedRowKeys.length === 0 ? props.formId : state.selectedRowKeys[0];
        basicDownloadByData(`/api/pms/plan/export/excel/${fileId}`, {}, '', 'POST', false, false);
      }
      if (type === 'lssued-plan') {
        if (state.selectedRowKeys.length === 0) {
          message.warning('请选择数据进行下发');
          return;
        }
        Modal.confirm({
          title: '是否下发计划',
          onOk() {
            new Api('/pms').fetch(state.selectedRowKeys, 'plan/dispatch', 'POST').then((res) => {
              message.success('下发计划成功');
              getPage();
            });
          },
        });
      }
      if (type === 'resolve-plan') {
        if (state.selectedRowKeys.length === 0) {
          message.warning('请选择数据进行分解');
          return;
        }
        Modal.confirm({
          title: '是否分解计划',
          onOk() {
            new Api('/pms').fetch(state.selectedRowKeys, 'plan/resolve', 'POST').then((res) => {
              message.success('分解计划成功');
              getPage();
            });
          },
        });
      }
    }
    onBeforeMount(() => {
      getPage();
    });
    async function searchEmit(data) {
      const res = await new Api(`/pms/plan/list/query/${props.formId}`).fetch(data, '', 'POST');
      state.dataSTableTree = res;
      state.selectedRowKeys = [];
      nextTick(() => {
        state.isShowTable = true;
      });
    }
    function checkClick(data) {
      handleOpen(data.id);
    }
    function fingRowData(id, data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === id) {
          return data[i];
        }
        if (Array.isArray(data[i].children) && data[i].children.length > 0) {
          let itemData = fingRowData(id, data[i].children);
          if (itemData) {
            return itemData;
          }
        }
      }
    }

    function onActionClick(type: 'edit'|'delete', record: any) {
      switch (type) {
        case 'edit':
          handleEdit(record?.id, type);
          break;
        case 'delete':
          return new Api('/pms').fetch([record.id], 'plan/batch', 'DELETE').then(() => {
            message.success('删除成功');
            getPage();
          });
        default:
          break;
      }
    }

    return {
      ...toRefs(state),
      uploadRef,
      updateData,
      submitNavButton,
      isPower,
      searchRegister,
      searchEmit,
      checkClick,
      register,
      onActionClick,
    };
  },
};
</script>

<style scoped>

</style>
