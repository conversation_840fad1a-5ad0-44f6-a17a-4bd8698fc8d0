<script lang="ts" setup>
import {
  ref,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const dataSource = ref();

const AcceptanceInformation = [
  {
    label: '收入计划编号',
    field: 'code',
  },
  {
    label: '甲方单位名称',
    field: 'partyADeptIdName',
  },
  {
    label: '甲方纳税人识别号',
    field: 'partyADeptIdCAode',
  },
  {
    label: '合同编号',
    field: 'contractNumber',
  },
  {
    label: '合同名称',
    field: 'contractName',
  },
  {
    label: '合同类型',
    field: 'contractType',
  },
  {
    label: '合同里程碑名称',
    field: 'milestoneName',
  },
  {
    label: '开票/收入确认公司',
    field: 'billingCompanyName',
  },
];

async function setValues(data: any) {
  dataSource.value = data;
}

defineExpose({
  setValues,
});

</script>
<template>
  <DetailsLayout
    title="合同基本信息"
    :list="AcceptanceInformation"
    :data-source="dataSource"
    :column="3"
  />
</template>
<style lang="less" scoped>
 :deep(.details-container) {
   padding-bottom: 0!important;
 }
</style>