package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QuestionReplyManagement VO对象
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
@ApiModel(value = "QuestionReplyManagementVO对象", description = "问题答复")
@Data
public class QuestionReplyManagementVO extends ObjectVO implements Serializable {

    /**
     * 问题编号
     */
    @ApiModelProperty(value = "问题编号")
    private String questionNumber;


    /**
     * 答复详情
     */
    @ApiModelProperty(value = "答复详情")
    private String reply;


}
