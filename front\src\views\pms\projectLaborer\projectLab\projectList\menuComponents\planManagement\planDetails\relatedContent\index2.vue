<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <div class="relatedContents">
      <Demand
        v-if="contentTabs[contentTabsIndex]?.name === '关联需求'"
        :pageType="pageType"
        :formId="formId"
      />
      <Risk
        v-if="contentTabs[contentTabsIndex]?.name === '关联风险'"
        :pageType="pageType"
        :formId="formId"
        :projectId="projectId"
      />
      <Question
        v-if="contentTabs[contentTabsIndex]?.name === '关联问题'"
        :pageType="pageType"
        :formId="formId"
      />
      <!--    <Alteration  v-if="contentTabs[contentTabsIndex]?.name === '关联变更' &&  isPower('LCB_container_05_04', powerData)"/>-->
      <Document
        v-if="contentTabs[contentTabsIndex]?.name === '关联文档'"
        :pageType="pageType"
        :formId="formId"
        :projectId="projectId"
      />
    </div>
  </Layout2Content>
</template>

<script>
import {
  isPower,
  Layout2Content,
} from 'lyra-component-vue3';
// import { Layout2Content } from '/@/components/Layout2.0';
import {
  inject, onMounted, reactive, toRefs,
} from 'vue';
import Demand from './demand2/index.vue';
import Risk from './risk2/index.vue';
import Question from './question2/index.vue';
import Document from './document/index.vue';

export default {
  name: 'Index',
  components: {
    Demand,
    Risk,
    Question,
    Document,
    Layout2Content,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      contentTabsIndex: 0,
      powerData: [],
      // contentTabs: [
      //   { name: '关联需求' }, // 0 demand
      //   { name: '关联风险' }, // 1 risk
      //   { name: '关联问题' }, // 2 question
      //   { name: '关联变更' }, // 3 alteration
      //   { name: '关联文档' }, //  4 document
      // ],
    });
    const state6 = reactive({
      contentTabs: [],
    });
    state.powerData = inject('powerData');
    onMounted(() => {
      if (props.pageType === 'modal' || isPower('LCB_container_05_01', state.powerData)) {
        state6.contentTabs.push({ name: '关联需求' });
      }
      if (props.pageType === 'modal' || isPower('LCB_container_05_02', state.powerData)) {
        state6.contentTabs.push({ name: '关联风险' });
      }
      if (props.pageType === 'modal' || isPower('LCB_container_05_03', state.powerData)) {
        state6.contentTabs.push({ name: '关联问题' });
      }
      if (props.pageType === 'modal' || isPower('LCB_container_05_05', state.powerData)) {
        state6.contentTabs.push({ name: '关联文档' });
      }
    });
    function contentTabsChange(index) {
      state.tabsIndex = index;
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange,
      isPower,
    };
  },
};
</script>

<style scoped>
.relatedContents{
  display: flex;
  height: 100%;
}
</style>
