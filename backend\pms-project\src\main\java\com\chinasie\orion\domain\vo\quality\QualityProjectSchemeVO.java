package com.chinasie.orion.domain.vo.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeVO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:30
 * @description:
 * <p>
 * ProjectSchemeVO对象
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeVO对象", description = "项目计划")
public class QualityProjectSchemeVO extends ObjectVO {

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String schemeNumber;

    /**
     *
     */
    @ApiModelProperty(value = "计划名称")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "层级名称")
    private String levelName;
    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    private String parentChain;
    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    private Integer type;

    @ApiModelProperty(value = "计划类型名称(计划，里程碑)")
    private String typeName;
    /**
     *
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusName;
    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任科室姓名")
    private String rspSectionName;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    private String rspSubDept;

    @ApiModelProperty(value = "责任处室名称")
    private String rspSubDeptName;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    private String rspUserCode;

    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    private Integer circumstance;

    @ApiModelProperty(value = "计划情况名称")
    private String circumstanceName;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;
    @ApiModelProperty(value = "计划下发时间")
    private Date issueTime;

    @ApiModelProperty(value = "计划活动项")
    private String planActive;
    @ApiModelProperty(value = "计划活动项集合")
    private List<Map<String,String>> planActiveList;
    @ApiModelProperty(value = "计划活动项名称")
    private String planActiveName;
}
