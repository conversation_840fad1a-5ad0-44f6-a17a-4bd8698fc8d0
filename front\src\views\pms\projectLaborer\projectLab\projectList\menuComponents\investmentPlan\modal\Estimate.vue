<template>
  <div class="details-estimate">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <div
          class="every-table-slots"
        >
          <BasicButton
            v-if="isPower('TZJH_container_button_09', powerData)"
            icon="add"
            type="primary"
            @click="addTable('investment')"
          >
            添加概算
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_10', powerData)"
            icon="edit"
            type="primary"
            @click="editTable"
          >
            编辑
          </BasicButton>
          <!--          <BasicButton-->
          <!--            icon="delete"-->
          <!--            type="primary"-->
          <!--            @click="deleteTable"-->
          <!--          >-->
          <!--            删除-->
          <!--          </BasicButton>-->
        </div>
      </template>
    </OrionTable>
    <!--      创建概算-->
    <AddEstimateTableNode
      @register="register"
      @update="updateTable"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import AddEstimateTableNode from '../components/AddEstimateTableNode.vue';
import { estimateColumns, getEstimateList } from '../index';

export default defineComponent({
  name: 'Estimate',
  components: {
    OrionTable,
    BasicButton,
    AddEstimateTableNode,
  },
  setup() {
    const powerData = inject('powerData', null);
    const formData = inject('formData', { id: '' });
    const getFormData = inject('getFormData', null);
    const router = useRouter();
    const state = reactive({
    });
    const [register, { openDrawer }] = useDrawer();

    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      showSmallSearch: false,
      dataSource: [],
      pagination: false,
      api: (params) => getEstimateList(params, formData.value.id),
      columns: estimateColumns(),
    });
    function addTable(type = '') {
      openDrawer(true, {
        type: 'add',
        data: { investmentSchemeId: formData.value.id },
      });
    }
    function editTable() {
      let selectRows = tableRef.value.getSelectRows();
      if (selectRows.length === 0 || selectRows.length > 1) {
        message.warning(selectRows.length > 1 ? '只能编辑一条数据' : '请选择数据进行编辑');
        return;
      }
      openDrawer(true, {
        type: 'edit',
        data: selectRows[0],
      });
    }
    function changePage(record) {
      router.push({
        name: 'InvestmentPlanDetails',
        params: {
          id: record.id,
        },
      });
    }
    function deleteTable() {
      Modal.confirm({
        title: '删除提示',
        content: '是否删除所选的数据？',
        onOk() {
        },
      });
    }
    function updateTable() {
      tableRef.value.reload();
      getFormData();
    }
    function updateInit() {
      tableRef.value.reload();
    }

    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      addTable,
      editTable,
      register,
      changePage,
      deleteTable,
      updateTable,
      powerData,
      isPower,
      updateInit,
    };
  },
});
</script>
<style lang="less" scoped>
.details-estimate{
  height: 350px;
  overflow: hidden;
  position: relative;
}
</style>
