<script setup lang="ts">
import { Empty } from 'ant-design-vue';
import { Wrap, ProjectBasicInfo, Milestone } from './components';
import ProjectScheme from './components/ProjectScheme.vue';
import ProjectBudget from './components/ProjectBudget.vue';
import ProjectRevenue from './components/ProjectRevenue.vue';
import ProjectMaterials from './components/ProjectMaterials.vue';
import ProjectHours from './components/ProjectHours.vue';
import ProjectWarning from './components/ProjectWarning.vue';
import RiskChart from './charts/RiskChart.vue';
import QuestionChart from './charts/QuestionChart.vue';
import DemandChart from './charts/DemandChart.vue';
</script>

<template>
  <div class="project-overview">
    <Wrap title="项目基础信息">
      <ProjectBasicInfo />
    </Wrap>
    <Wrap title="项目里程碑">
      <Milestone />
    </Wrap>
    <Wrap title="项目计划">
      <ProjectScheme />
    </Wrap>
    <div class="flex">
      <Wrap
        class="flex-f1"
        title="项目预算概览"
      >
        <ProjectBudget />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="项目营收"
      >
        <ProjectRevenue />
      </Wrap>
    </div>
    <div class="flex">
      <Wrap
        class="flex-f1"
        title="项目物资"
      >
        <ProjectMaterials />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="项目工时"
      >
        <ProjectHours />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="项目预警"
      >
        <ProjectWarning />
      </Wrap>
    </div>
    <div class="flex">
      <Wrap
        class="flex-f1"
        title="风险统计"
      >
        <RiskChart />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="问题统计"
      >
        <QuestionChart />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="项目需求"
      >
        <DemandChart />
      </Wrap>
      <Wrap
        class="flex-f1"
        title="项目质量"
      >
        <Empty
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
          description="模块设计中"
        />
      </Wrap>
    </div>
  </div>
</template>

<style scoped lang="less">
@import "./public.less";
.project-overview {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;

  > div + div {
    margin-top: 10px;
  }

  > .flex {
    > div + div {
      margin-left: 10px;
    }
  }
}
</style>
