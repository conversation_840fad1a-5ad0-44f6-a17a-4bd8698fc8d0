package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectDeliverableStatisticsDTO;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.projectStatistics.DeliverableStatusStatistics;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDeliverableStatisticsVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DeliverableRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DeliverableService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.projectStatistics.DeliverableStatusStatisticsService;
import com.chinasie.orion.service.projectStatistics.ProjectDeliverableStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ProjectDeliverableStatisticsServiceImpl implements ProjectDeliverableStatisticsService {
    @Autowired
    private DeliverableService deliverableService;

    @Autowired
    private DeliverableStatusStatisticsService deliverableStatusStatisticsService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Resource
    private DeliverableRepository deliverableRepository;
    @Autowired
    private ProjectSchemeService projectSchemeService;
    @Resource
    private ProjectService projectService;
    @Resource
    private UserBo userBo;

    @Override
    public ProjectDeliverableStatisticsVO getProjectDeliverableStatusStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) {
        ProjectDeliverableStatisticsVO projectDeliverableStatisticsVO = new ProjectDeliverableStatisticsVO();
        LambdaQueryWrapperX<Deliverable> projectDeliverableLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectDeliverableLambdaQueryWrapperX.select("t1.status,count(t.id) as count");
        projectDeliverableLambdaQueryWrapperX.eq(Deliverable::getProjectId, projectDeliverableStatisticsDTO.getProjectId());
        projectDeliverableLambdaQueryWrapperX.leftJoin(ProjectScheme.class,ProjectScheme::getId,Deliverable::getPlanId);
        projectDeliverableLambdaQueryWrapperX.groupBy(ProjectScheme::getStatus);
        List<Map<String, Object>> list = deliverableService.listMaps(projectDeliverableLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("101".equals(map.get("status").toString())) {
                projectDeliverableStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("111".equals(map.get("status").toString())) {
                projectDeliverableStatisticsVO.setCompleteCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectDeliverableStatisticsVO.setReleaseCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectDeliverableStatisticsVO;
    }

    @Override
    public List<ProjectDeliverableStatisticsVO> getProjectDeliverableRspUserStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) {
        List<ProjectDeliverableStatisticsVO> projectDeliverableStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<Deliverable> projectDeliverableLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectDeliverableLambdaQueryWrapperX.select("t.principal_id as rspUser,IFNULL( sum( CASE  WHEN t1.`status`=101 THEN 1 ELSE 0 END ), 0 ) waitReleaseCount ," +
                "IFNULL( sum( CASE  WHEN t1.`status`=111 THEN 1 ELSE 0 END ), 0 ) as completeCount," +
                "IFNULL( sum( CASE  WHEN t1.`status`=130 THEN 1 ELSE 0 END ), 0 ) as releaseCount");
        projectDeliverableLambdaQueryWrapperX.eq(Deliverable::getProjectId, projectDeliverableStatisticsDTO.getProjectId());
        projectDeliverableLambdaQueryWrapperX.leftJoin(ProjectScheme.class,ProjectScheme::getId,Deliverable::getPlanId);
        projectDeliverableLambdaQueryWrapperX.groupBy(Deliverable::getPrincipalId);
        List<Map<String, Object>> list = deliverableService.listMaps(projectDeliverableLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectDeliverableStatisticsVO projectDeliverableStatisticsVO = new ProjectDeliverableStatisticsVO();
            projectDeliverableStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectDeliverableStatisticsVO.setRspuserName(userVO.getName());
            }
            projectDeliverableStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("waitReleaseCount").toString()));
            projectDeliverableStatisticsVO.setReleaseCount(Integer.parseInt(map.get("releaseCount").toString()));
            projectDeliverableStatisticsVO.setCompleteCount(Integer.parseInt(map.get("completeCount").toString()));
            projectDeliverableStatisticsVOs.add(projectDeliverableStatisticsVO);
        }
        return projectDeliverableStatisticsVOs;
    }



    @Override
    public List<ProjectDeliverableStatisticsVO> getProjectDeliverableChangeStatusStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) {
        List<ProjectDeliverableStatisticsVO> projectDeliverableStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<DeliverableStatusStatistics> projectDeliverableLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectDeliverableStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql = sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN un_finished_count ELSE 0 END ), 0 ) as unFinishedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN   DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN process_count ELSE 0 END ), 0 ) as  processCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN finished_count ELSE 0 END ), 0 ) as  finishedCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectDeliverableLambdaQueryWrapperX.select(sql);
        projectDeliverableLambdaQueryWrapperX.eq(DeliverableStatusStatistics::getProjectId, projectDeliverableStatisticsDTO.getProjectId());
        List<Map<String, Object>> list = deliverableStatusStatisticsService.listMaps(projectDeliverableLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectDeliverableStatisticsVO projectDeliverableStatisticsVO = new ProjectDeliverableStatisticsVO();
            projectDeliverableStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("unFinishedCountTime" + i).toString()));
            projectDeliverableStatisticsVO.setReleaseCount(Integer.parseInt(map.get("processCountTime" + i).toString()));
            projectDeliverableStatisticsVO.setCompleteCount(Integer.parseInt(map.get("finishedCountTime" + i).toString()));
            projectDeliverableStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectDeliverableStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectDeliverableStatisticsVOs.add(projectDeliverableStatisticsVO);
        }
        Collections.reverse(projectDeliverableStatisticsVOs);
        return projectDeliverableStatisticsVOs;
    }


    @Override
    public List<ProjectDeliverableStatisticsVO> getProjectDeliverableCreateStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) {
        List<ProjectDeliverableStatisticsVO> projectDeliverableStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<Deliverable> projectDeliverableLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectDeliverableStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectDeliverableStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectDeliverableLambdaQueryWrapperX.select(sql);
        projectDeliverableLambdaQueryWrapperX.eq(Deliverable::getProjectId, projectDeliverableStatisticsDTO.getProjectId());
        List<Map<String, Object>> list = deliverableService.listMaps(projectDeliverableLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectDeliverableStatisticsVO projectDeliverableStatisticsVO = new ProjectDeliverableStatisticsVO();
            projectDeliverableStatisticsVO.setWaitReleaseCount(Integer.parseInt(map.get("time" + i).toString()));
            projectDeliverableStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectDeliverableStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectDeliverableStatisticsVOs.add(projectDeliverableStatisticsVO);
        }
        Collections.reverse(projectDeliverableStatisticsVOs);
        return projectDeliverableStatisticsVOs;
    }

    @Override
    public Page<DeliverableVo> getProjectDeliverablePages(Page<ProjectDeliverableStatisticsDTO> pageRequest) throws Exception {
        Page<Deliverable> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<Deliverable> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(Deliverable.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO = pageRequest.getQuery();
            if(ObjectUtil.isNotEmpty(projectDeliverableStatisticsDTO.getStatus())){
                objectLambdaQueryWrapperX.select("t.id, t.plan_id as planId, t.principal_name as principalName, t.principal_id as principalId, t.delivery_time as deliveryTime, t.predict_delivery_time as predictDeliverTime, t.project_id as projectId, t.document_Id as documentId, t.next_rev_id as nextRevId, t.rev_order as revOrder, t.rev_key as revKey, t.previous_rev_id as previousRevId, t.rev_id as revId, t.initial_rev_id initialRevId, t.class_name as className, t.creator_id as creatorId, t.owner_id as ownerId, t.create_time as createTime, t.modify_id as modifyId, t.modify_time as modifyTime, t.remark, t.platform_id as platformId, t.org_id as orgId, t.status, t.logic_status as logicStatus, t.sort, t.number, t.name");
                objectLambdaQueryWrapperX.leftJoin(ProjectScheme.class,ProjectScheme::getId,Deliverable::getPlanId);
                objectLambdaQueryWrapperX.apply("t1.status={0}", projectDeliverableStatisticsDTO.getStatus());
            }
            objectLambdaQueryWrapperX.eq(Deliverable::getProjectId, projectDeliverableStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(Deliverable::getPrincipalId, projectDeliverableStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectDeliverableStatisticsDTO.getCreateTime())) {
                if (projectDeliverableStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(Deliverable::getCreateTime,DateUtil.beginOfDay(projectDeliverableStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectDeliverableStatisticsDTO.getCreateTime()));
                }
                if (projectDeliverableStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(Deliverable::getCreateTime,DateUtil.beginOfWeek(projectDeliverableStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectDeliverableStatisticsDTO.getCreateTime()));
                }
                if (projectDeliverableStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(Deliverable::getCreateTime,DateUtil.beginOfQuarter(projectDeliverableStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectDeliverableStatisticsDTO.getCreateTime()));
                }
                if (projectDeliverableStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(Deliverable::getCreateTime,DateUtil.beginOfMonth(projectDeliverableStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectDeliverableStatisticsDTO.getCreateTime()));
                }
                if (projectDeliverableStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(Deliverable::getCreateTime,DateUtil.beginOfYear(projectDeliverableStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectDeliverableStatisticsDTO.getCreateTime()));
                }
            }
        }
        PageResult<Deliverable> page = deliverableRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<DeliverableVo> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<Deliverable> content = page.getContent();
        if(CollectionUtil.isEmpty(content)){
            return pageResult;
        }
        Set<String> userIdSet = new HashSet<>();
        Set<String> planIdSet = new HashSet<>();
        Set<String> projectIdSet = new HashSet<>();
        List<String> idList = new ArrayList<>();
        for (Deliverable deliverable : content) {
            planIdSet.add(deliverable.getPlanId());
            projectIdSet.add(deliverable.getProjectId());
            String creatorId = deliverable.getCreatorId();
            String modifyId = deliverable.getModifyId();
            String ownerId = deliverable.getOwnerId();
            userIdSet.add(creatorId);
            userIdSet.add(modifyId);
            userIdSet.add(ownerId);
            idList.add(deliverable.getId());
        }
        List<ProjectScheme> projectDTOList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class).
                in(ProjectScheme::getId, planIdSet));
        Map<String, ProjectScheme> map = projectDTOList.stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity(), (v1, v2) -> v2));
        Map<String, String> idToNameMapByIdList = projectService.getIdToNameMapByIdList(new ArrayList<>(projectIdSet));
        List<DeliverableVo> deliverableVos = new ArrayList<>();
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
        for (Deliverable deliverable : content) {
            DeliverableVo deliverableVo = new DeliverableVo();
            BeanCopyUtils.copyProperties(deliverable, deliverableVo);
            String creatorId = deliverable.getCreatorId();
            String modifyId = deliverable.getModifyId();
            String ownerId = deliverable.getOwnerId();
            deliverableVo.setCreatorName(nameByUserIdMap.get(creatorId));
            deliverableVo.setModifyName(nameByUserIdMap.get(modifyId));
            deliverableVo.setOwnerName(nameByUserIdMap.get(ownerId));
            String planId1 = deliverable.getPlanId();
            String projectId = deliverable.getProjectId();
            deliverableVo.setProjectId(projectId);
            ProjectScheme projectScheme = map.get(planId1);
            if (Objects.nonNull(projectScheme)) {
                deliverableVo.setPlanName(projectScheme.getName());
                Integer status = projectScheme.getStatus();
                deliverableVo.setStatus(status);
                deliverableVo.setDataStatus(projectScheme.getDataStatus());
                if (Objects.nonNull(projectScheme.getDataStatus())) {
                    deliverableVo.setStatusName(projectScheme.getDataStatus().getName());
                }
                if (Objects.nonNull(projectScheme.getActualEndTime())){
                    deliverableVo.setActualEndTime(projectScheme.getActualEndTime());
                }
            }
            deliverableVo.setProjectName(idToNameMapByIdList.get(projectId));
            deliverableVos.add(deliverableVo);
        }
        pageResult.setContent(deliverableVos);
        return pageResult;
    }
}
