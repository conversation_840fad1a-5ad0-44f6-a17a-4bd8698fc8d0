package com.chinasie.orion.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.constant.MaterialToolTypeEnum;
import com.chinasie.orion.constant.MaterialTypeEnum;
import com.chinasie.orion.domain.dto.CountJobDTO;
import com.chinasie.orion.domain.dto.MaterialManageDTO;
import com.chinasie.orion.domain.dto.SchemeToMaterialDTO;
import com.chinasie.orion.domain.dto.material.MaterialManageBO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.domain.vo.excel.SchemeToMaterialExportExcel;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.JobMaterialMapper;
import com.chinasie.orion.repository.SchemeToMaterialMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobMaterialService;
import com.chinasie.orion.service.MaterialManageService;
import com.chinasie.orion.service.MaterialOutManageService;
import com.chinasie.orion.service.SchemeToMaterialService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.DictConts.PMS_OUT_REASON;
import static com.chinasie.orion.constant.DictConts.SUPPLIES_TYPE;


/**
 * <p>
 * SchemeToMaterial 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@Service
@Slf4j
public class SchemeToMaterialServiceImpl extends  OrionBaseServiceImpl<SchemeToMaterialMapper, SchemeToMaterial>   implements SchemeToMaterialService {





    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private JobMaterialService jobMaterialService;
    @Autowired
    private MaterialOutManageService materialOutManageService;

    private MaterialManageService materialManageService;

    private JobMaterialMapper jobMaterialMapper;

    @Autowired
    private SchemeToMaterialMapper schemeToMaterialMapper;


    @Autowired
    public void setJobMaterialService(JobMaterialMapper jobMaterialMapper) {
        this.jobMaterialMapper = jobMaterialMapper;
    }
    @Autowired
    public void setMaterialManageService(MaterialManageService materialManageService) {
        this.materialManageService = materialManageService;
    }
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  SchemeToMaterialVO detail(String id,String pageCode) throws Exception {
        SchemeToMaterial schemeToMaterial =this.getById(id);
        SchemeToMaterialVO result = BeanCopyUtils.convertTo(schemeToMaterial,SchemeToMaterialVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param schemeToMaterialDTO
     */
    @Override
    public  String create(SchemeToMaterialDTO schemeToMaterialDTO) throws Exception {
        if (!StringUtils.hasText(schemeToMaterialDTO.getBaseCode())){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL,"基地编码不能为空");
        }
        existsData(schemeToMaterialDTO.getPlanSchemeId(),schemeToMaterialDTO.getMaterialNumber());
        //保存物资关系
        SchemeToMaterial schemeToMaterial = BeanCopyUtils.convertTo(schemeToMaterialDTO, SchemeToMaterial::new);
        this.save(schemeToMaterial);
        String materialNumber = schemeToMaterialDTO.getMaterialNumber();
        String baseCode = schemeToMaterialDTO.getBaseCode();
        LambdaQueryWrapperX<MaterialManage> wrapperX = new LambdaQueryWrapperX<>(MaterialManage.class);
        wrapperX.eq(MaterialManage::getNumber,materialNumber);
        wrapperX.eq(MaterialManage::getBaseCode,baseCode);
        MaterialManage materialManage = materialManageService.getMaterialManageId(schemeToMaterialDTO.getAssetType(), schemeToMaterialDTO.getAssetCode()
                , schemeToMaterialDTO.getMaterialNumber(), schemeToMaterialDTO.getBaseCode());
        String materialManageId = "";
        if (Objects.isNull(materialManage)){
            MaterialManageDTO materialManageDTO = new MaterialManageDTO();
            materialManageDTO.setAssetType(schemeToMaterialDTO.getAssetType());
            materialManageDTO.setAssetCode(schemeToMaterialDTO.getAssetCode());
            materialManageDTO.setNumber(schemeToMaterialDTO.getMaterialNumber());
            materialManageDTO.setAssetName(schemeToMaterialDTO.getMaterialName());
            materialManageDTO.setCostCenterName(schemeToMaterialDTO.getCostCenterName());
            materialManageDTO.setCostCenter(schemeToMaterialDTO.getCostCenter());
            materialManageDTO.setSpecificationModel(schemeToMaterialDTO.getSpecificationModel());
            materialManageDTO.setStockNum(schemeToMaterialDTO.getStockNum());
            materialManageDTO.setInputStockNum(schemeToMaterialDTO.getDemandNum());
            materialManageDTO.setNextVerificationDate(schemeToMaterialDTO.getNextVerificationDate());
            materialManageDTO.setIsVerification(schemeToMaterialDTO.getIsVerification());
            materialManageDTO.setBaseCode(schemeToMaterialDTO.getBaseCode());
            materialManageDTO.setStatus(StatusEnum.DISABLE.getIndex());
            materialManageDTO.setProductCode(schemeToMaterialDTO.getProductCode());
            materialManageDTO.setToolStatus(schemeToMaterialDTO.getToolStatus());
            materialManageDTO.setMaintenanceCycle(schemeToMaterialDTO.getMaintenanceCycle());
            materialManageId = materialManageService.create(materialManageDTO);
        }else {
            materialManageId = materialManage.getId();
            schemeToMaterial.setAvaliable(materialManage.getIsAvailable());
        }
        schemeToMaterial.setMaterialId(materialManageId);
        if (!StringUtils.hasText(schemeToMaterial.getMaterialId())){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL,"物资关系保存失败");
        }
        this.updateById(schemeToMaterial);

        return schemeToMaterial.getId();
    }

    public  void existsData  (String schemeId,String number) throws Exception {
        LambdaQueryWrapperX<SchemeToMaterial> wrapperX = new LambdaQueryWrapperX<>(SchemeToMaterial.class);
        wrapperX.eq(SchemeToMaterial::getMaterialNumber,number);
        if(StringUtils.hasText(number)){
            wrapperX.eq(SchemeToMaterial::getMaterialNumber,number);
        }
        if(StringUtils.hasText(schemeId)){
            wrapperX.eq(SchemeToMaterial::getPlanSchemeId,schemeId);
        }
        wrapperX.select(SchemeToMaterial::getPlanSchemeId);
        if(this.count(wrapperX)>0) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "物资当前物资数据已重复，请重新添加");
        }
    }
    /**
     *  编辑
     *
     * * @param schemeToMaterialDTO
     */
    @Override
    public Boolean edit(SchemeToMaterialDTO schemeToMaterialDTO) throws Exception {
        SchemeToMaterial schemeToMaterial =BeanCopyUtils.convertTo(schemeToMaterialDTO,SchemeToMaterial::new);

        this.updateById(schemeToMaterial);

        String rsp=schemeToMaterial.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<SchemeToMaterial> schemeToMaterials = this.listByIds(ids);
        if (schemeToMaterials.isEmpty()){
            return true;
        }
        String planSchemeId = schemeToMaterials.get(0).getPlanSchemeId();
        List<String> materialIds = schemeToMaterials.stream().map(SchemeToMaterial::getMaterialId).collect(Collectors.toList());
        List<MaterialManageVO> materialManageVOS = materialManageService.listByIdList(materialIds);
        materialManageVOS.forEach(one->{
            if (Objects.isNull(one)){
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL,"物资未在该基地");
            }
        });
        //判断是否基地物资在关系中是最后一条
        List<MaterialManageBO> idToCount = this.getBaseMapper().countByMaterial(materialIds);
        Map<String, Integer> collect = idToCount.stream().collect(Collectors.toMap(MaterialManageBO::getMaterialId, MaterialManageBO::getCount));
        List<String> removeIds = new ArrayList<>();
        materialManageVOS.forEach(item->{
            Integer count = collect.getOrDefault(item.getId(), -1);
            if (count==1){
                removeIds.add(item.getId());
            }
        });
        //删除物资计划关系
        this.removeBatchByIds(ids);
        //删除是最后一条关系的外层物资
        List<MaterialManageVO> readyToRemove = materialManageService.listByIdList(removeIds);

        for (MaterialManageVO materialManageVO : readyToRemove) {
            if (materialManageVO.getStatus() == 0 || materialManageVO.getStatus()== 2){
                continue;
            }
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_POWER_LOGIC_DELETE.getErrorCode(),"物资已入场，无法删除");
        }
        //第三层删除
        if(!removeIds.isEmpty()){
            LambdaUpdateWrapper<JobMaterial> wrapper = new LambdaUpdateWrapper<>(JobMaterial.class);
            wrapper.set(JobMaterial::getLogicStatus,-1);
            wrapper.in(JobMaterial::getMaterialId,removeIds);
            jobMaterialService.update(wrapper);
            //物资管理删除
            materialManageService.removeBatchByIds(removeIds);
        }

        return true;
    }



    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SchemeToMaterialVO> pages( Page<SchemeToMaterialDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SchemeToMaterial> condition = new LambdaQueryWrapperX<>( SchemeToMaterial. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SchemeToMaterial::getCreateTime);


        Page<SchemeToMaterial> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SchemeToMaterial::new));
        SchemeToMaterialDTO query = pageRequest.getQuery();
        if (!Objects.isNull(query)){
            if (!Objects.isNull(query.getPlanSchemeId())){
                condition.eq(SchemeToMaterial::getPlanSchemeId, query.getPlanSchemeId());
            }
        }
        PageResult<SchemeToMaterial> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SchemeToMaterialVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SchemeToMaterialVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SchemeToMaterialVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<SchemeToMaterialVO> vos)throws Exception {

        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> materialIdList =vos.stream().map(SchemeToMaterialVO::getMaterialId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 获取物资信息
        List<MaterialManageVO> materialManageVOList= materialManageService.listByIdList(materialIdList);
        Map<String, MaterialManageVO> manageVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(materialManageVOList)) {
            for (MaterialManageVO personMange : materialManageVOList) {
                manageVOMap.put(personMange.getId(), personMange);
            }
        }
        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(DictConts.SUPPLIES_TYPE);
        Map<String,String> numberToDesc = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber,DictValueVO::getDescription));
        vos.forEach(item->{
            MaterialManageVO materialManageVO= manageVOMap.get(item.getMaterialId());
            if(Objects.nonNull(materialManageVO)){
                item.setMaterialManageVO(materialManageVO);
                item.setAssetType(materialManageVO.getAssetType());
                item.setAssetTypeName(materialManageVO.getAssetTypeName());
                item.setCostCenter(materialManageVO.getCostCenter());
                item.setCostCenterName(materialManageVO.getCostCenterName());
                item.setBaseCode(materialManageVO.getBaseCode());
                item.setAssetCode(materialManageVO.getAssetCode());
                item.setAssetName(materialManageVO.getAssetName());
                item.setSpecificationModel(materialManageVO.getSpecificationModel());
                item.setStockNum(materialManageVO.getStockNum());
                item.setToolStatusName(MaterialToolTypeEnum.getDescByKey(item.getToolStatus()));
            }
            item.setAssetTypeName(numberToDesc.getOrDefault(item.getAssetType(),""));
        });



    }

    @Override
    public Page<SchemeToMaterialVO> materialPages(Page<SchemeToMaterialDTO> pageRequest) throws Exception {
        SchemeToMaterialDTO schemeToPersonDTO= pageRequest.getQuery();
        long total= schemeToMaterialMapper.pageCount(schemeToPersonDTO.getRepairRound(),schemeToPersonDTO.getKeyword());
        Page<SchemeToMaterialVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        pageResult.setTotalSize(total);
        if(total <= 0){
            return  pageResult;
        }
        long current=(pageRequest.getPageNum()-1)*pageRequest.getPageSize();
        List<SchemeToMaterial> list= schemeToMaterialMapper.pageToList(schemeToPersonDTO.getRepairRound(),schemeToPersonDTO.getKeyword(),current,pageResult.getPageSize());

        List<String> materialIdList= list.stream().map(SchemeToMaterial::getMaterialId).filter(Objects::nonNull).collect(Collectors.toList());
        List<SchemeToMaterialVO> materialManageVOList= schemeToMaterialMapper.listByMaterialIdList(schemeToPersonDTO.getRepairRound(),materialIdList);

        Map<String,List<SchemeToMaterialVO>> materialMap= materialManageVOList.stream().collect(Collectors.groupingBy(SchemeToMaterialVO::getMaterialId));

        List<SchemeToMaterialVO> vos = BeanCopyUtils.convertListTo(list, SchemeToMaterialVO::new);
        // 获取物资所在 作业数量
        List<CountJobDTO> countJobDTOList = jobMaterialMapper.getMaterialNumMap(materialIdList);
            Map<String,Integer> keyToCount = new HashMap<>();
        if(!CollectionUtils.isEmpty(countJobDTOList)) {
            for (CountJobDTO countJobDTO : countJobDTOList) {
                keyToCount.put(countJobDTO.getKey(), countJobDTO.getValue());
            }
        }
        this.setEveryName(vos);
        vos.forEach(item->{
            item.setJobNum(keyToCount.getOrDefault(item.getMaterialId(), 0));
            List<SchemeToMaterialVO> scheme = materialMap.get(item.getMaterialId());
            if(!CollectionUtils.isEmpty(scheme)){
                item.setProjectName(scheme.stream().map(SchemeToMaterialVO::getProjectName).filter(Objects::nonNull).collect(Collectors.joining(",")));
                item.setPlanSchemeName(scheme.stream().map(SchemeToMaterialVO::getPlanSchemeName).filter(Objects::nonNull).collect(Collectors.joining(",")));
            }
        });
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void saveEntity(String planSchemeId, String repairRound, String jobBase, String number
            , Integer stockNum, String assetType, String materialManageId,String name
            ,String productCode,String toolStatus,Integer maintenanceCycle) {
        LambdaQueryWrapperX<SchemeToMaterial> wrapperX =new LambdaQueryWrapperX<>(SchemeToMaterial.class);
        wrapperX.eq(SchemeToMaterial::getPlanSchemeId,planSchemeId);
        wrapperX.eq(SchemeToMaterial::getRepairRound,repairRound);
        wrapperX.eq(SchemeToMaterial::getMaterialId,materialManageId);
        if(this.count(wrapperX)>0){
            return;
        }
        SchemeToMaterial schemeToMaterial = new SchemeToMaterial();
        schemeToMaterial.setPlanSchemeId(planSchemeId);
        schemeToMaterial.setMaterialId(materialManageId);
        schemeToMaterial.setRepairRound(repairRound);
        schemeToMaterial.setBaseCode(jobBase);
        schemeToMaterial.setMaterialNumber(number);
        schemeToMaterial.setDemandNum(stockNum);
        schemeToMaterial.setAssetType(assetType);
        schemeToMaterial.setMaterialName(name);
        schemeToMaterial.setProductCode(productCode);
        schemeToMaterial.setToolStatus(toolStatus);
        schemeToMaterial.setMaintenanceCycle(maintenanceCycle);
        this.save(schemeToMaterial);
    }

    @Override
    public List<String> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<SchemeToMaterial> wrapperX =new LambdaQueryWrapperX<>(SchemeToMaterial.class);
        wrapperX.eq(SchemeToMaterial::getRepairRound,repairRound);
        wrapperX.select(SchemeToMaterial::getMaterialNumber,SchemeToMaterial::getMaterialId);
        List<SchemeToMaterial> schemeToMaterialList = this.list(wrapperX);
        if(CollectionUtils.isEmpty(schemeToMaterialList)){
            return new ArrayList<>();
        }
        return  schemeToMaterialList.stream().filter(item-> StringUtils.hasText(item.getMaterialId()))
                .map(SchemeToMaterial::getMaterialId).distinct().collect(Collectors.toList());
    }

    /**
     * 导出物资入场离场信息
     *
     * @param query    查询
     * @param response 导出到浏览器
     */
    @Override
    public void exportSchemeMaterialExcel(SchemeToMaterialDTO query, HttpServletResponse response) {
        final LambdaQueryWrapperX<SchemeToMaterial> condition = new LambdaQueryWrapperX<>(SchemeToMaterial.class);
        condition.select(SchemeToMaterial::getMaterialId, SchemeToMaterial::getAssetType,
                SchemeToMaterial::getMaterialNumber, SchemeToMaterial::getMaterialName, SchemeToMaterial::getBaseCode,
                SchemeToMaterial::getDemandNum, SchemeToMaterial::getRepairRound);

        condition.leftJoin(MaterialManage.class, "mm", on -> on.eq(SchemeToMaterial::getMaterialId, MaterialManage::getId))
                .select(MaterialManage::getAssetName, MaterialManage::getSpecificationModel, MaterialManage::getCostCenterName,
                        MaterialManage::getStockNum, MaterialManage::getInDate, MaterialManage::getOutDate,
                        MaterialManage::getActInDate, MaterialManage::getActOutDate, MaterialManage::getStatus,
                        MaterialManage::getIsVerification, MaterialManage::getNextVerificationDate,
                        MaterialManage::getRspUserNo, MaterialManage::getRspUserName, MaterialManage::getIsOverdue,
                        MaterialManage::getIsMetering)
                .selectAs(MaterialManage::getIsOverdue, SchemeToMaterialExportExcel::getIsPass)
                .selectAs(MaterialManage::getDemandNum, SchemeToMaterialExportExcel::getRequiredNum);

        condition.leftJoin(BasePlace.class, "bp", on -> on.eq(SchemeToMaterial::getBaseCode, BasePlace::getCode))
                .selectAs(BasePlace::getName, SchemeToMaterialExportExcel::getBaseName);

        condition.select("(select count(1) from pmsx_job_material jm where jm.material_id = t.material_id) job_num");

        condition.eq(SchemeToMaterial::getRepairRound, query.getRepairRound());

        List<SchemeToMaterialExportExcel> excelList = this.selectJoinList(SchemeToMaterialExportExcel.class, condition);

        List<SchemeToMaterialExportExcel> list = new ArrayList<>(excelList.stream()
                .collect(Collectors.toMap(
                        u -> new CompositeKey(u.getMaterialName(), u.getMaterialId(), u.getMaterialNumber(),
                                u.getBaseCode(), u.getAssetType(), u.getRepairRound()),
                        u -> u,
                        (u1, u2) -> u1
                )).values());

        List<DictValueVO> assetTypeDicts = dictRedisHelper.getDictListByCode(SUPPLIES_TYPE);
        Map<String, String> assetTypeMap = assetTypeDicts.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));

        final List<DictValueVO> reasonDicts = dictRedisHelper.getDictListByCode(PMS_OUT_REASON);
        Map<String, String> reasonMap = reasonDicts.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        // 查找出库，关联最新的一条出库数据
        final LambdaQueryWrapperX<MaterialOutManage> outQuery = new LambdaQueryWrapperX<>(MaterialOutManage.class);
        outQuery.select(MaterialOutManage::getSourceId, MaterialOutManage::getOutReason, MaterialOutManage::getOutNum,
                MaterialOutManage::getOutDate, MaterialOutManage::getMaterialDestination, MaterialOutManage::getIsAgainIn,
                MaterialOutManage::getStockNum, MaterialOutManage::getIsReport, MaterialOutManage::getIsMetering,
                MaterialOutManage::getType);
        outQuery.in(MaterialOutManage::getSourceId, list.stream().map(SchemeToMaterialExportExcel::getMaterialId).collect(Collectors.toList()));
        outQuery.orderByDesc(MaterialOutManage::getCreateTime);
        final List<MaterialOutManage> outList = materialOutManageService.list(outQuery);
        Map<String, List<MaterialOutManage>> outMap = outList.stream().collect(Collectors.groupingBy(MaterialOutManage::getSourceId));

        list.forEach(e -> {
            String key = e.getMaterialId();
            e.setAssetTypeName(assetTypeMap.getOrDefault(e.getAssetType(), ""));
            Calendar startCalendar;
            Calendar endCalendar;
            if (Objects.equals(e.getStatus(), StatusEnum.DISABLE.getIndex()) && !Objects.isNull(e.getInDate())) {
                startCalendar = Calendar.getInstance();
                endCalendar = Calendar.getInstance();
                startCalendar.setTime(DateUtil.beginOfDay(e.getInDate()));
                endCalendar.setTime(DateUtil.beginOfDay(new Date()));
                e.setInDays(((startCalendar.getTimeInMillis() - endCalendar.getTimeInMillis()) / (24 * 60 * 60 * 1000)));
            }
            if (outMap.containsKey(key)) {
                // 根据创建时间取最新的一条数据，取它的入库、出库数量
                Optional<MaterialOutManage> inData = outMap.get(key).stream().filter(k ->
                        ObjectUtil.equal(MaterialTypeEnum.INPUT.getKey(), k.getType())).findFirst();
                if (inData.isPresent()) {
                    e.setActDemandNum(inData.get().getStockNum());
                    e.setIsReport(inData.get().getIsReport());
                }
                Optional<MaterialOutManage> outData = outMap.get(key).stream().filter(k ->
                        ObjectUtil.equal(MaterialTypeEnum.OUT.getKey(), k.getType())).findFirst();
                if (outData.isPresent()) {
                    e.setActOutNum(outData.get().getOutNum());
                    e.setOutReason(outData.get().getOutReason());
                    e.setMaterialDestination(outData.get().getMaterialDestination());
                    e.setIsAgainIn(outData.get().getIsAgainIn());
                }

            }
            e.setOutReasonName(reasonMap.getOrDefault(e.getOutReason(), ""));
        });

        String fileName = "导出物资入场离场信息.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), SchemeToMaterialExportExcel.class).sheet("sheet1").doWrite(list);
        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }

    }

    @AllArgsConstructor
    @Data
    protected static class CompositeKey {

        final String materialName;

        final String materialId;

        final String materialNumber;

        private String baseCode;

        private String assetType;

        private String repairRound;
    }
}
