package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * MarketContractSign Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@TableName(value = "pms_market_contract_sign")
@ApiModel(value = "MarketContractSignEntity对象", description = "市场合同签署信息")
@Data

public class MarketContractSign extends  ObjectEntity  implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 签署结果
     */
    @ApiModelProperty(value = "签署结果")
    @TableField(value = "sign_result")
    private Boolean signResult;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    @TableField(value = "sign_date")
    private Date signDate;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    @TableField(value = "effect_date")
    private Date effectDate;

    /**
     * 合同完结日期
     */
    @ApiModelProperty(value = "合同完结日期")
    @TableField(value = "complete_date")
    private Date completeDate;

    /**
     * 合同完结类型
     */
    @ApiModelProperty(value = "合同完结类型")
    @TableField(value = "complete_type")
    @FieldBind(dataBind = DictDataBind.class, type = "contract_completion_type", target = "completeTypeName")
    private String completeType;


    /**
     * 合同完结类型名称
     */
    @ApiModelProperty(value = "合同完结类型名称")
    @TableField(exist = false)
    private String completeTypeName;


    /**
     * 终止签署原因
     */
    @ApiModelProperty(value = "终止签署原因")
    @TableField(value = "end_sign_reason")
    private String endSignReason;

    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @TableField(value = "cust_contract_no")
    private String custContractNo;

}
