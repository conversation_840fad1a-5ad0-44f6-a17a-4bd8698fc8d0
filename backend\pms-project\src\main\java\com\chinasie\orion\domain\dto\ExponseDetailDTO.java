package com.chinasie.orion.domain.dto;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ExponseDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
@ApiModel(value = "ExponseDetailDTO对象", description = "支出详情表")
@Data
public class ExponseDetailDTO extends ObjectDTO implements Serializable{


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 成本中心ID
     */
    @ApiModelProperty(value = "成本中心ID")
    @NotEmpty(message = "成本中心ID不能为空")
    private String costCenterId;

    /**
     * 成本中心名字
     */
    @ApiModelProperty(value = "成本中心名字")
    private String costCenterName;

    /**
     * 费用科目ID
     */
    @ApiModelProperty(value = "费用科目ID")
    @NotEmpty(message = "费用科目不能为空")
    private String expenseAccountId;

    /**
     * 费用科目名字
     */
    @ApiModelProperty(value = "费用科目名字")
    private String expenseAccountName;

    /**
     * 支出人ID
     */
    @ApiModelProperty(value = "支出人ID")
    @NotEmpty(message = "支出人ID不能为空")
    private String outPersonId;

    /**
     * 支出人姓名
     */
    @ApiModelProperty(value = "支出人姓名")
    private String outPersonName;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @DecimalMax(value="************.00", message = "支出金额必须小于100,000,000,000.00")
    private BigDecimal outMoney;

    /**
     * 支出时间
     */
    @ApiModelProperty(value = "支出时间")
    private Date outTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 附件URL
     */
    @ApiModelProperty(value = "附件URL")
    private String tag;

    /**
     * 预算ID
     */
    @ApiModelProperty(value = "预算ID")
    @NotEmpty(message = "预算ID不能为空")
    private String budgetProjectId;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    private String budgetProjectName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;


    @ApiModelProperty(value = "附件文档")
    private List<FileVO> attachments;

}
