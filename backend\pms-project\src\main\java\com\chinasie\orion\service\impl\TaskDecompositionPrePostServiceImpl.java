package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.TaskDecompositionPrePostVO;
import com.chinasie.orion.domain.vo.TaskDecompositionVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TaskDecompositionPrePostMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.TaskDecompositionPrePostService;
import com.chinasie.orion.service.TaskDecompositionService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;


import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * TaskDecompositionPrePost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
@Service
@Slf4j
public class TaskDecompositionPrePostServiceImpl extends  OrionBaseServiceImpl<TaskDecompositionPrePostMapper, TaskDecompositionPrePost>   implements TaskDecompositionPrePostService {


    @Autowired
    private ClassRedisHelper classRedisHelper;


    @Autowired
    private TaskDecompositionService taskDecompositionService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TaskDecompositionVO detail(String id, String pageCode) throws Exception {
        TaskDecomposition taskDecomposition = taskDecompositionService.getById(id);
//        TaskDecompositionPrePost taskDecompositionPrePost = this.getById(id);
        TaskDecompositionVO result = BeanCopyUtils.convertTo(taskDecomposition, TaskDecompositionVO::new);

        List<TaskDecompositionPrePost> taskDecompositionPrePosts = this
                .list(new LambdaQueryWrapper<>(TaskDecompositionPrePost.class).eq(TaskDecompositionPrePost::getTaskDecompositionId, id));

        List<TaskDecompositionPrePostVO> taskDecompositionPrePostVO = BeanCopyUtils.convertListTo(taskDecompositionPrePosts, TaskDecompositionPrePostVO::new);
//        taskDecompositionPrePostVO.forEach(o -> {
//            o.setProjectSchemeName(projectSchemeMilestoneNode.getName());
//        });

        Map<Integer, List<TaskDecompositionPrePostVO>> prePostMapVOMap = taskDecompositionPrePostVO.stream().collect(Collectors.groupingBy(TaskDecompositionPrePostVO::getType));

        result.setIsPrePost(prePostMapVOMap.containsKey(Status.SCHEME_PRE.getCode()));
        result.setSchemePrePostVOList(prePostMapVOMap.getOrDefault(Status.SCHEME_PRE.getCode(), new ArrayList<>()));
        result.setSchemePostVOList(prePostMapVOMap.getOrDefault(Status.SCHEME_POST.getCode(), new ArrayList<>()));
        result.setProjectSchemePrePostVOS(prePostMapVOMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
        return result;
    }

    /**
     *  新增
     *
     * * @param taskDecompositionPrePostDTO
     */
    @Override
    public  String create(TaskDecompositionPrePostDTO taskDecompositionPrePostDTO) throws Exception {
        TaskDecompositionPrePost taskDecompositionPrePost =BeanCopyUtils.convertTo(taskDecompositionPrePostDTO,TaskDecompositionPrePost::new);
        this.save(taskDecompositionPrePost);
        return taskDecompositionPrePost.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createBatch(TaskPreTaskDTO taskPreTaskDTO) throws Exception {
        List<String> taskIds = taskPreTaskDTO.getTaskIds();
        if (CollectionUtil.isEmpty(taskIds)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_SCHEME_NULL);
        }

        if (taskPreTaskDTO.getTaskDecompositionPrePostDTO().stream()
                .anyMatch(a -> StrUtil.isBlank(a.getPostTaskId()) && StrUtil.isBlank(a.getPreTaskId()))) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "存在前后置关系未选择任务");
        }
        if (CollectionUtil.isEmpty(taskPreTaskDTO.getTaskDecompositionPrePostDTO())) {
            this.remove(new LambdaQueryWrapper<>(TaskDecompositionPrePost.class)
                    .in(TaskDecompositionPrePost::getTaskDecompositionId, taskIds).or()
                    .in(TaskDecompositionPrePost::getPreTaskId, taskIds).or()
                    .in(TaskDecompositionPrePost::getPostTaskId, taskIds));
            return new ArrayList<>();
        }

        this.remove(new LambdaQueryWrapper<>(TaskDecompositionPrePost.class)
                .in(TaskDecompositionPrePost::getTaskDecompositionId, taskIds).or()
                .in(TaskDecompositionPrePost::getPreTaskId, taskIds).or()
                .in(TaskDecompositionPrePost::getPostTaskId, taskIds));
        List<TaskDecompositionPrePost> list = new ArrayList<>();

        for (String taskId : taskIds) {
            for (TaskDecompositionPrePostDTO nodePrePostDTO : taskPreTaskDTO.getTaskDecompositionPrePostDTO()) {
                TaskDecompositionPrePost prePost = new TaskDecompositionPrePost();
                TaskDecompositionPrePost relationPrePost = new TaskDecompositionPrePost();
                if (StrUtil.isNotBlank(nodePrePostDTO.getPreTaskId())) {
                    prePost.setType(Status.SCHEME_PRE.getCode());
                    prePost.setPreTaskId(nodePrePostDTO.getPreTaskId());
                    relationPrePost.setType(Status.SCHEME_POST.getCode());
                    relationPrePost.setTaskDecompositionId(nodePrePostDTO.getPreTaskId());
                    relationPrePost.setPostTaskId(taskId);
                } else {
                    prePost.setType(Status.SCHEME_POST.getCode());
                    prePost.setPostTaskId(nodePrePostDTO.getPostTaskId());
                    relationPrePost.setType(Status.SCHEME_PRE.getCode());
                    relationPrePost.setTaskDecompositionId(nodePrePostDTO.getPostTaskId());
                    relationPrePost.setPostTaskId(taskId);
                }
                prePost.setId(classRedisHelper.getUUID(TaskDecompositionPrePost.class.getSimpleName()));
                prePost.setTemplateId(nodePrePostDTO.getTemplateId());
                prePost.setTaskDecompositionId(taskId);
                relationPrePost.setId(classRedisHelper.getUUID(TaskDecompositionPrePost.class.getSimpleName()));
                relationPrePost.setTemplateId(nodePrePostDTO.getTemplateId());
                list.add(prePost);
                list.add(relationPrePost);
            }
        }
        this.saveBatch(list);
        return list.stream().map(TaskDecompositionPrePost::getId).collect(Collectors.toList());
    }

    /**
     *  编辑
     *
     * * @param taskDecompositionPrePostDTO
     */
    @Override
    public Boolean edit(TaskDecompositionPrePostDTO taskDecompositionPrePostDTO) throws Exception {
        TaskDecompositionPrePost taskDecompositionPrePost =BeanCopyUtils.convertTo(taskDecompositionPrePostDTO,TaskDecompositionPrePost::new);

        this.updateById(taskDecompositionPrePost);
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TaskDecompositionPrePostVO> pages( Page<TaskDecompositionPrePostDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TaskDecompositionPrePost> condition = new LambdaQueryWrapperX<>( TaskDecompositionPrePost. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TaskDecompositionPrePost::getCreateTime);


        Page<TaskDecompositionPrePost> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TaskDecompositionPrePost::new));

        PageResult<TaskDecompositionPrePost> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TaskDecompositionPrePostVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TaskDecompositionPrePostVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TaskDecompositionPrePostVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<TaskDecompositionPrePostVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }


}
