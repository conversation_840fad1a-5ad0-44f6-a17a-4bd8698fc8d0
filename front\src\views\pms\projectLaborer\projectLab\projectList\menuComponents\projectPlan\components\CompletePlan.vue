<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicCard title="完成信息">
      <a-row
        :gutter="[20, 10]"
        class="information-row"
      >
        <template
          v-for="item in filedList"
          :key="item.label"
        >
          <ACol
            :span="item.span"
            class="task-item"
          >
            <div class="item-title">
              {{ item.label }}：
            </div>

            <!--                :title="formData[item.field]"-->
            <div
              class="item-value flex-te3"
              :title="item.formatter?item.formatter(formData[item.field]):formData[item.field]"
            >
              {{ item.formatter?item.formatter(formData[item.field]):formData[item.field] }}
            </div>
          </ACol>
        </template>
      </a-row>
      <div class="upload-list">
        <UploadList
          :edit="false"
          :listData="listData"
          height="300px"
        />
      </div>
    </BasicCard>
    <BasicCard
      title="确认审核"
      class="no-bottom"
    />
    <BasicForm
      @register="register"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList,
} from 'lyra-component-vue3';
import { Ref, ref, onMounted } from 'vue';
import { Row as ARow, Col as ACol, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const formData:Ref<Record<any, any>> = ref({});
const filedList:Ref<Record<any, any>[]> = ref([
  {
    label: '计划名称',
    field: 'name',
    span: 12,
  },
  {
    label: '计划父级',
    field: 'parentName',
    span: 12,
  },
  {
    label: '计划类型',
    field: 'nodeType',
    span: 12,
    formatter: (val) => (val === 'milestone' ? '里程碑节点' : '计划'),
  },
  {
    label: '计划属性',
    field: 'parentName',
    span: 12,
  },
  {
    label: '责任人',
    field: 'rspUserName',
    span: 12,
  },
  {
    label: '责任部门',
    field: 'rspSubDeptName',
    span: 12,
  },
  {
    label: '参与人',
    field: 'participantUserNames',
    span: 12,
  },
  {
    label: '工期（天）',
    field: 'durationDays',
    span: 12,
  },
  {
    label: '计划开始日期',
    field: 'beginTime',
    span: 12,
    formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') : '-'),
  },
  {
    label: '计划结束日期',
    field: 'endTime',
    span: 12,
    formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') : '-'),
  },
  {
    label: '实际开始日期',
    field: 'actualBeginTime',
    span: 12,
    formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') : '-'),
  },
  {
    label: '实际结束日期',
    field: 'actualEndTime',
    span: 12,
    formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') : '-'),
  },
  {
    label: '计划执行情况说明',
    field: 'executeDesc',
    span: 24,
  },
]);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'confirmResult',
      label: '结果',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        options: [
          {
            label: '同意',
            value: 'agree',
          },
          {
            label: '驳回',
            value: 'reject',
          },
        ],
      },
      component: 'Select',
    },
    {
      field: 'reasonConfirmation',
      label: '理由',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
  ],
});
const listData:Ref<Record<any, any>[]> = ref([]);
onMounted(() => {
  getFormData(props.record.id);
});
function getFormData(id) {
  loading.value = true;
  new Api('/pms').fetch('', `projectScheme/${id}`, 'GET').then((res) => {
    formData.value = res;
    listData.value = res.completeFiles || [];
    loading.value = false;
  });
}
function getListData(id) {
  new Api('/res').fetch([id], 'manage/file/listByIds', 'POST').then((res) => {
    listData.value = res;
  });
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    await new Api('/pms').fetch(params, 'projectScheme/completeConfirmation', 'PUT');
    message.success('完成确认成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
.upload-list{
  :deep(.ant-basic-table){
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
.no-bottom{
  margin-bottom: 0 !important;
}
</style>