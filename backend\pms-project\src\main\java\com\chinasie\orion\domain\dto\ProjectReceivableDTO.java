package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectReceivable Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@ApiModel(value = "ProjectReceivableDTO对象", description = "项目应收表")
@Data
public class ProjectReceivableDTO extends ObjectDTO implements Serializable{

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String name;

    /**
     * 应收编码
     */
    @ApiModelProperty(value = "应收编码")
    private String number;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点")
    private String collectionPoint;

    /**
     * 应收日期
     */
    @ApiModelProperty(value = "应收日期")
    private Date receivableDate;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal amountReceivable;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal fundsReceived;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期")
    private Date saleSate;


    /**
     * 未收金额
     */
    @ApiModelProperty(value = "未收金额")
    private BigDecimal noAmountReceived;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "附件文档")
    private List<FileDTO> attachments;

    /**
     * 客户名称id
     */
    @ApiModelProperty(value = "客户名称id")
    private String stakeholderId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String stakeholderName;


}
