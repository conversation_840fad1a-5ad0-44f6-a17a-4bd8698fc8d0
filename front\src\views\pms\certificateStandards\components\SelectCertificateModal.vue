<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { cloneDeep } from 'lodash-es';

const props = withDefaults(defineProps<{
  selectedList?: any[],
  type?: 'checkbox' | 'radio',
  certificateType?: string
}>(), {
  type: 'radio',
  selectedList: () => [],
  certificateType: '',
});

const tableRef: Ref = ref();
const keyword: Ref<string> = ref('');
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    type: props.type,
    onChange(_keys: string[], rows: any[]) {
      selectedRows.value = rows || [];
    },
  },
  showToolButton: false,
  api: (params: Record<string, any>) => {
    if (props?.certificateType) {
      const certificateType = [
        [
          {
            field: 'certificateType',
            fieldType: 'String',
            values: [props.certificateType],
            queryType: 'like',
          },
        ],
      ];
      if (params.searchConditions) {
        params.searchConditions[0].push(certificateType[0][0]);
      } else {
        params.searchConditions = certificateType;
      }
    }
    return new Api('/pms/certificate-info').fetch({
      ...params,
    }, 'page', 'POST');
  },
  afterFetch() {
    const keys: string[] = props.selectedList?.map((item) => item.id);
    if (keys?.length) {
      setTimeout(() => {
        tableRef.value?.setSelectedRowKeys(keys);
      });
    }
  },
  columns: [
    {
      title: '类型',
      dataIndex: 'certificateTypeName',
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '等级',
      dataIndex: 'levelName',
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '是否需要复审',
      dataIndex: 'isNeedRenewal',
    },
  ],
};

defineExpose({
  confirm() {
    return new Promise((resolve, reject) => {
      if (selectedRows.value.length === 0) {
        message.error('请选择数据');
        return reject('');
      }
      resolve(selectedRows.value);
    });
  },
});
</script>

<template>
  <div style="height: 100%;overflow: hidden">
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
