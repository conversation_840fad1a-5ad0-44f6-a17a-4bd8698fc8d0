package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "TotalCostVO对象", description = "总预算差异")
@Data
public class TotalCostVO  extends ObjectVO {

    /**
     * 总预算
     */
    @ApiModelProperty(value = "总预算")
    private BigDecimal totalBudget;

    /**
     * 总花费
     */
    @ApiModelProperty(value = "总花费")
    private BigDecimal totalCost;

    /**
     * 总差异
     */
    @ApiModelProperty(value = "总差异")
    private BigDecimal totalDifference;

    /**
     * 是否超出预算 0为未超预算，1为超出预算
     */
    @ApiModelProperty(value = "是否超出预算 0为未超预算，1为超出预算")
    private Integer isOut;
}
