package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.relationOrgToMaterial.*;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.material.MaterialInVO;
import com.chinasie.orion.domain.vo.material.MaterialOutVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialDownVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManageImplementTreeVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManagePlanTreeVO;
import com.chinasie.orion.domain.vo.tree.SearchVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.RelationOrgToMaterialService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * relationOrgToMaterial 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@RestController
@RequestMapping("/relationOrgToMaterial")
@Api(tags = "大修组织和大修组织物资关系")
public class RelationOrgToMaterialController {

    @Autowired
    private RelationOrgToMaterialService relationOrgToMaterialService;

    /**
     * @param
     * @return新增组织物资关系
     */
    @ApiOperation(value = "新增组织物资关系")
    @RequestMapping(value = "/addRelation", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在【{{#place}}】新增的数据为(资产编码-资产名称)：【{{#data}}】", type = "Material", subType = "新增组织物资关系", bizNo = "")
    public ResponseDTO<Object> addRelation(@RequestBody OrgMaterialRelationDTO dto) {
        relationOrgToMaterialService.addRelation(dto);
        return new ResponseDTO<>(ResponseDTO.success());
    }

    /**
     * 编辑组织物资
     *
     * @param
     * @return
     */
    @ApiOperation(value = "编辑物资信息--准备阶段")
    @RequestMapping(value = "/edit/in/material/info", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在【{{#place}}】编辑了(资产编码-资产名称)：【{{#data}}】", type = "Material", subType = "编辑物资信息--准备阶段", bizNo = "{#{materialInDTO.id}}")
    public ResponseDTO<MaterialInVO> editInMaterialInfo(@RequestBody MaterialInDTO materialInDTO) {
        MaterialInVO rsp = relationOrgToMaterialService.editMaterialInfo(materialInDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑物资信息--实施阶段
     *
     * @param material 物资信息
     * @return 编辑物资信息
     */
    @ApiOperation(value = "编辑物资信息--实施")
    @RequestMapping(value = "/edit/out/material/info", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑实施物资(资产编码-资产名称)：【{{#data}}】", type = "", subType = "编辑物资信息--实施阶段", bizNo = "")
    public ResponseDTO<MaterialOutVO> editOutMaterialInfo(@RequestBody MaterialOutDTO material) {
        MaterialOutVO rsp = relationOrgToMaterialService.editOutMaterialInfo(material);
        return new ResponseDTO<>(rsp);
    }

    /**
     * @param
     * @return 删除组织物资关系
     */
    @ApiOperation(value = "批量删除组织物资关系")
    @RequestMapping(value = "/deleteRelation", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在【{{#place}}】删除了(资产编码-资产名称)：【{{#data}}】的相关关联数据", type = "", subType = "删除组织物资关系", bizNo = "")
    public ResponseDTO<Boolean> deleteRelation(@RequestBody List<OrgMaterialRelationDeleteDTO> dto) {
        Boolean rsp = relationOrgToMaterialService.deleteRelation(dto);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "大修准备树")
    @RequestMapping(value = "/planTree", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了【{{#searchVO.repairRound}}】的准备物资树数据", type = "", subType = "物资大修准备树", bizNo = "")
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManagePlanTreeVO>>>> planTree(@RequestBody SearchVO searchVO) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManagePlanTreeVO>>> treeNodeVO = relationOrgToMaterialService.planTree(searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "大修实施树")
    @RequestMapping(value = "/implementTree", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了【{{#searchVO.repairRound}}】的实施物资树数据", type = "", subType = "物资大修实施树", bizNo = "")
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManageImplementTreeVO>>>> implementTree(@RequestBody SearchVO searchVO) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManageImplementTreeVO>>> treeNodeVO = relationOrgToMaterialService.implementTree(searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation("获取准备阶段的统计下钻物资列表")
    @RequestMapping(value = "/prepare/down/list", method = RequestMethod.POST)
    public ResponseDTO<List<MaterialDownVO>> prepareDownList(@RequestBody MaterialDownDTO dto) throws Exception {
        return new ResponseDTO<>(relationOrgToMaterialService.prepareDownList(dto));
    }
}
