package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NonContractProcDTO;
import com.chinasie.orion.domain.entity.NonContractProc;
import com.chinasie.orion.domain.vo.NonContractProcVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * NonContractProc 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12 11:04:24
 */
public interface NonContractProcService extends OrionBaseService<NonContractProc> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NonContractProcVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param nonContractProcDTO
     */
    String create(NonContractProcDTO nonContractProcDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param nonContractProcDTO
     */
    Boolean edit(NonContractProcDTO nonContractProcDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NonContractProcVO> pages(Page<NonContractProcDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NonContractProcDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NonContractProcVO> vos) throws Exception;

    /**
     * 获取总数及金额
     * <p>
     * * @param pageRequest
     */
    Map<String, Object> getNumMoney(Page<NonContractProcDTO> pageRequest) throws Exception;
}
