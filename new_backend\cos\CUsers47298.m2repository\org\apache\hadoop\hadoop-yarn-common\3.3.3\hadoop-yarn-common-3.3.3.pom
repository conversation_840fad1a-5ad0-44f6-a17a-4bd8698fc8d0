<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>hadoop-yarn</artifactId>
    <groupId>org.apache.hadoop</groupId>
    <version>3.3.3</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hadoop-yarn-common</artifactId>
  <version>3.3.3</version>
  <name>Apache Hadoop YARN Common</name>

  <properties>
    <!-- Needed for generating FindBugs warnings using parent pom -->
    <yarn.basedir>${project.parent.basedir}</yarn.basedir>
    <should.run.jdiff>true</should.run.jdiff>
    <dev-support.relative.dir>../dev-support</dev-support.relative.dir>
  </properties>

  <dependencies>
    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-hdfs-client</artifactId>
    </dependency>

    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-yarn-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-auth</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-guava</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
    </dependency>


    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.inject.extensions</groupId>
      <artifactId>guice-servlet</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey.jersey-test-framework</groupId>
      <artifactId>jersey-test-framework-grizzly2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.inject</groupId>
      <artifactId>guice</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-server</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-json</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey.contribs</groupId>
      <artifactId>jersey-guice</artifactId>
    </dependency>
    <dependency>
     <groupId>ch.qos.reload4j</groupId>
     <artifactId>reload4j</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-json-provider</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>javax.ws.rs-api</artifactId>
    </dependency>
  </dependencies>

  <build>
    <!--
    Include all files in src/main/resources.  By default, do not apply property
    substitution (filtering=false), but do apply property substitution to
    yarn-version-info.properties (filtering=true).  This will substitute the
    version information correctly, but prevent Maven from altering other files
    like yarn-default.xml.
    -->
    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <excludes>
          <exclude>yarn-version-info.properties</exclude>
        </excludes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>yarn-version-info.properties</include>
        </includes>
        <filtering>true</filtering>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>${project.basedir}/src/test/resources</directory>
      </testResource>
      <testResource>
        <directory>${project.basedir}/src/test/resources/resource-types</directory>
      </testResource>
    </testResources>
    <plugins>
     <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/main/resources/webapps/mapreduce/.keep</exclude>
            <exclude>src/main/resources/webapps/jobhistory/.keep</exclude>
            <exclude>src/main/resources/webapps/yarn/.keep</exclude>
            <exclude>src/main/resources/webapps/applicationhistory/.keep</exclude>
            <exclude>src/main/resources/webapps/timeline/.keep</exclude>
            <exclude>src/main/resources/webapps/sharedcache/.keep</exclude>
            <exclude>src/main/resources/webapps/cluster/.keep</exclude>
            <exclude>src/main/resources/webapps/test/.keep</exclude>
            <exclude>src/main/resources/webapps/proxy/.keep</exclude>
            <exclude>src/main/resources/webapps/node/.keep</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/css/jquery.dataTables.css</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/css/custom_datatable.css</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/css/jui-dt.css</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/css/demo_table.css</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/images/Sorting icons.psd</exclude>
            <exclude>src/main/resources/webapps/static/dt-1.10.18/js/jquery.dataTables.min.js</exclude>
            <exclude>src/main/resources/webapps/static/jt/jquery.jstree.js</exclude>
            <exclude>src/main/resources/webapps/static/jquery/jquery-ui-1.12.1.custom.min.js</exclude>
            <exclude>src/main/resources/webapps/static/jquery/jquery-3.5.1.min.js</exclude>
            <exclude>src/main/resources/webapps/static/jquery/themes-1.9.1/base/jquery-ui.css</exclude>
            <exclude>src/test/resources/application_1440536969523_0001.har/_index</exclude>
            <exclude>src/test/resources/application_1440536969523_0001.har/part-0</exclude>
            <exclude>src/test/resources/application_1440536969523_0001.har/_masterindex</exclude>
            <exclude>src/test/resources/application_1440536969523_0001.har/_SUCCESS</exclude>
            <exclude>src/test/resources/application_123456_0001.har/_index</exclude>
            <exclude>src/test/resources/application_123456_0001.har/part-0</exclude>
            <exclude>src/test/resources/application_123456_0001.har/_masterindex</exclude>
            <exclude>src/test/resources/application_123456_0001.har/_SUCCESS</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>src-compile-protoc</id>
            <configuration>
              <skip>false</skip>
              <additionalProtoPathElements>
                <additionalProtoPathElement>
                  ${basedir}/../../../hadoop-common-project/hadoop-common/src/main/proto
                </additionalProtoPathElement>
                <additionalProtoPathElement>
                  ${basedir}/../hadoop-yarn-api/src/main/proto
                </additionalProtoPathElement>
              </additionalProtoPathElements>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>replace-generated-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-test-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-maven-plugins</artifactId>
        <executions>
          <execution>
            <id>version-info</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>version-info</goal>
            </goals>
            <configuration>
              <source>
                <directory>${basedir}/src/main</directory>
                <includes>
                  <include>java/**/*.java</include>
                  <include>proto/**/*.proto</include>
                </includes>
              </source>
            </configuration>
          </execution>
          <execution>
            <id>resource-gz</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>resource-gz</goal>
            </goals>
            <configuration>
              <inputDirectory>${basedir}/src/main/resources/webapps/static</inputDirectory>
              <outputDirectory>${basedir}/target/classes/webapps/static</outputDirectory>
              <extensions>js,css</extensions>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <phase>test-compile</phase>
          </execution>
        </executions>
      </plugin>
     
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <phase>pre-site</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <tasks>
                <copy file="src/main/resources/yarn-default.xml" todir="src/site/resources"/>
                <copy file="src/main/xsl/configuration.xsl" todir="src/site/resources"/>
              </tasks>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <configuration>
          <filesets>
            <fileset>
              <directory>src/site/resources</directory>
              <includes>
                <include>configuration.xsl</include>
                <include>yarn-default.xml</include>
              </includes>
              <followSymlinks>false</followSymlinks>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <!-- TODO: Remove this profile when hamlet was removed. -->
  <profiles>
    <profile>
      <id>java9</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>org/apache/hadoop/yarn/webapp/hamlet/**</exclude>
              </excludes>
              <testExcludes>
                <exclude>org/apache/hadoop/yarn/webapp/hamlet/**</exclude>
              </testExcludes>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <excludePackageNames>org.apache.hadoop.yarn.webapp.hamlet</excludePackageNames>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
