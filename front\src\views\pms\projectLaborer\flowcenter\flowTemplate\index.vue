<template>
  <Layout :options="{ body: { scroll: true } }">
    <!-- tree start -->
    <template #left>
      <search-tree
        v-loading="loading"
        :disable="!selectedKeys.length"
        @change="keywordSearch"
        @add="addNode"
        @delete="deleteNode"
        @edit="editNode"
        @up="nodeMove('up')"
        @down="nodeMove('down')"
      >
        <a-tree
          v-if="treeData.length"
          ref="treeRef"
          v-model:selectedKeys="selectedKeys"
          :tree-data="serializationTree"
          :replace-fields="replaceFields"
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          @expand="onExpand"
          @select="handleSelectNode"
        >
          <template #title="{ name }">
            <div
              v-highlight="{ key: keyword, word: name }"
              class="node-text-wrapper"
            />
          </template>
        </a-tree>
      </search-tree>
    </template>
    <orion-table
      ref="flowTableRef"
      :options="options"
      :filter-schemas="filterSchemas"
      @filterChange="filterChange"
      @filterOpenChange="filterOpenChange"
      @initData="initData"
      @actionChange="actionChange"
    >
      <template #status="{ record }">
        <span v-if="record.status === 1">启用</span>
        <span
          v-if="record.status === 0"
          style="color: #9ea3b5"
        >草稿</span>
        <span
          v-if="record.status === -1"
          style="color: red"
        >禁用</span>
      </template>
      <template #action="{ record }">
        <div v-if="record.status === 1">
          <a @click="onCopy(record)">复制</a>
          <span class="split">|</span>
          <a @click="onStopFlow(record)">禁用</a>
        </div>
        <div v-if="record.status === 0">
          <a @click="goEditFlow(record)">编辑</a>
          <span class="split">|</span>
          <a @click="onStartFlow(record, '发布')">发布</a>
          <span class="split">|</span>
          <a @click="onDeleteFlow(record)">删除</a>
        </div>
        <div v-if="record.status === -1">
          <a @click="onCopy(record)">复制</a>
          <span class="split">|</span>
          <a @click="onStartFlow(record, '启用')">启用</a>
        </div>
      </template>
      <template #name="{ record }">
        <a @click="goEdit(record)"> {{ record.name }}</a>
      </template>
    </orion-table>
    <form-modal
      :width="600"
      :tree-data="treeData"
      :action-type="dialogType"
      @update-list="updateList"
      @update-filed="updateFiled"
      @register="registerForm"
    />
    <set-flow-modal
      :width="600"
      :tree-data="treeData"
      @update-table="updateTable"
      @register="registerFlowForm"
    />
    <a-modal
      v-model:visible="deletedVisible"
      class="deleted-node-modal"
      title="删除确认"
      :confirm-loading="confirmLoading"
      @cancel="deletedVisible = false"
      @ok="removeNodeConfirm"
    >
      <p>
        你是否要删除 <span class="error">{{ selectedNode.name }}</span> 该节点树!
      </p>
    </a-modal>
    <a-modal
      v-model:visible="copyVisible"
      :centered="true"
      title="复制流程"
      @ok="handleCopy"
    >
      <div class="copy-modal">
        <span>模版名称:</span>
        <a-input
          v-model:value="copyValue"
          placeholder="请输入模版名称"
        />
      </div>
    </a-modal>
  </Layout>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, computed, onMounted, toRefs, createVNode,
} from 'vue';
import { Tree, message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, SearchTree,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
// import OrionTable from '/@/components/OrionTable';
// import SearchTree from '/@/components/SearchTree';
import { TreeActionType } from '/@/components/Tree/index';
// import { useModal } from '/@/components/Modal';
import FormModal from './modal/FormModal.vue';
import SetFlowModal from './modal/SetFlowModal.vue';
import Api from '/@/api/index';
// import { useGo } from '/@/hooks/web/usePage';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import { workflowApi } from '../util/apiConfig';
import {
  findParents,
  findNodeById,
  moveTreeNode,
  deletedNodeById,
  addTreeNode,
} from '/@/views/pms/projectLaborer/utils/tree/index';
// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';

export default defineComponent({
  name: 'FlowTemplate',
  components: {
    Layout,
    SearchTree,
    ATree: Tree,
    OrionTable,
    FormModal,
    SetFlowModal,
    ExclamationCircleOutlined,
  },
  setup() {
    const userStore: any = useUserStore();
    const treeRef = ref<Nullable<TreeActionType>>(null); // 树的ref
    const flowTableRef = ref<Nullable<any>>(null); // table的ref
    const apiOrg = new Api(`${workflowApi}/act-classify`);
    const router = useRouter();

    // 注册一个表单弹窗,用于增加和更新部门
    const [registerForm, { openModal: openModalForm, setModalProps: setModalFormProps }] = useModal();

    const [registerFlowForm, { openModal: openModalFlowForm, setModalProps: setModalFlowFormProps }] = useModal();

    const state: any = reactive({
      copyVisible: false,
      copyValue: '',
      currentId: '',
      keyword: '',
      treeData: [],
      selectedNode: {
        name: '组织管理',
      },
      dialogType: 'add',
      deletedVisible: false,
      loading: true,
      // 确定children下是否有数据,如果没有就是叶子节点,没有展开图标
      serializationTree: computed(() => {
        function serializationData(data: any) {
          data.forEach((ele: any) => {
            const { children } = ele;
            if (children?.length) {
              serializationData(children);
            } else {
              ele.isLeaf = true;
              delete ele.children;
            }
          });
        }
        serializationData(state.treeData);
        return state.treeData;
      }),
      dataList: computed(() => {
        const dataList: any[] = [];
        const generateList = (data: any) => {
          for (let i = 0; i < data.length; i++) {
            const node = data[i];
            dataList.push(node);
            if (node.children) {
              generateList(node.children);
            }
          }
        };
        generateList(state.treeData);
        return dataList;
      }),
      selectedKeys: [],
      expandedKeys: [],
      autoExpandParent: false,
      options: {
        rowSelection: {},
        deleteToolButton: 'add|delete|disable',
        tool: [
          {
            type: 'button',
            position: 'before',
            buttonGroup: [
              [
                {
                  icon: 'fa fa-plus',
                  name: '新增',
                  enable: true,
                  cb: () => {
                    // go('/flowcenter/bpmn');
                    router.push({
                      name: 'TaskFlow',
                    });
                  },
                },
              ],
            ],
          },
          {
            type: 'button',
            buttonGroup: [
              [
                {
                  icon: 'fa fa-ban',
                  name: '禁用',
                  cb: () => {
                    const tableList = flowTableRef.value.getSelectRows();
                    let params: any[] = [];
                    tableList.forEach((item) => {
                      params.push({
                        id: item.id,
                        status: -1,
                      });
                    });
                    new Api(workflowApi).fetch(params, 'act-flow/batch', 'PUT').then(() => {
                      message.success('操作成功');
                      flowTableRef.value.reload();
                    });
                  },
                },
                {
                  icon: 'fa fa-cog',
                  name: '设置分类',
                  cb: () => {
                    openModalFlowForm(true, flowTableRef.value.getSelectRows());
                  },
                },
              ],
            ],
          },
        ],
        auto: {
          url: `${workflowApi}/act-flow`,
          params: {
            query: {
              search: '',
              classifyId: '',
              endDate: '',
              startDate: '',
              userIds: [],
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [],
          },
        },
        columns: [
          {
            title: '流程名称',
            dataIndex: 'name',
            slots: { customRender: 'name' },
          },
          {
            title: '版本号',
            dataIndex: 'version',
          },
          {
            title: '所属分类',
            dataIndex: 'classifyName',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },
          },
          {
            title: '修改人',
            dataIndex: 'modifyName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            type: 'dateTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
    });
      // 获取树的列表
    const fetchTreeList = async (id?: string) => {
      state.loading = true;
      const res = await new Api(workflowApi).fetch({}, 'act-classify/tree', 'GET');
      state.treeData = res;

      if (id) {
        state.selectedKeys = [id]; // 选中当前ID
        state.selectedNode = findNodeById(res, id); // 获取节点信息
        state.expandedKeys = findParents(res, id, false); // 展开当前节点
      } else if (res?.length) {
        state.selectedKeys = [res[0].id];
        state.selectedNode = res[0];
      }
      state.loading = false;
    };

    // 一开始就去获取树的列表
    onMounted(() => {
      fetchTreeList();
    });
    // 获取当前节点的父节点
    function getParentKey(id: string, tree: any) {
      let parentKey: any;
      for (let i = 0; i < tree.length; i++) {
        const node: any = tree[i];
        if (node.children) {
          if (node.children.some((item: any) => item.id === id)) {
            parentKey = node.id;
          } else if (getParentKey(id, node.children)) {
            parentKey = getParentKey(id, node.children);
          }
        }
      }
      return parentKey;
    }

    // 启用流程（单个）
    function onStartFlow(row, statusStr) {
      new Api(workflowApi)
        .fetch(
          {
            id: row.id,
            userId: userStore.getUserInfo.id,
          },
          'act-flow/publish',
          'GET',
        )
        .then(() => {
          message.success('操作成功');
          flowTableRef.value.reload();

          const { id, name } = row;
        });
    }
    // 禁用流程
    function onStopFlow(row) {
      new Api(workflowApi)
        .fetch(
          {
            id: row.id,
            userId: userStore.getUserInfo.id,
          },
          'act-flow/disable',
          'GET',
        )
        .then(() => {
          message.success('操作成功');
          flowTableRef.value.reload();
        });
    }

    // copy流程
    function onCopy(row) {
      state.copyVisible = true;
      state.copyValue = `${row.name}_副本`;
      state.currentId = row.id;
    }
    function handleCopy() {
      new Api(workflowApi)
        .fetch(
          {
            id: state.currentId,
            name: state.copyValue,
            continueVersion: false,
            userId: userStore.getUserInfo.id,
          },
          'act-flow/copy',
          'GET',
        )
        .then((result) => {
          state.copyVisible = false;
          message.success('复制成功');
          flowTableRef.value.reload();
        });
    }

    return {
      onStartFlow,
      onStopFlow,
      onCopy,
      handleCopy,
      goEdit(row) {
        router.push({
          name: 'FlowDetail',
          query: {
            id: row.id,
            flowKey: row.flowKey,
            procdefId: row.procDefId,
            status: row.status,
            classifyId: row.classifyId,
            name: row.name,
            createTime: row.createTime,
            modifyTime: row.modifyTime,
            classifyName: row.classifyName,
            description: row.description,
            modifyName: row.modifyName,
            creatorName: row.creatorName,
            showBtn: '1',
          },
        });
      },
      goEditFlow(row) {
        router.push({
          name: 'TaskFlow',
          query: {
            id: row.id,
            classifyId: row.classifyId,
            name: row.name,
            status: row.status,
            createTime: row.createTime,
            modifyTime: row.modifyTime,
            classifyName: row.classifyName,
            description: row.description,
            modifyName: row.modifyName,
            creatorName: row.creatorName,
            routerName: 'FlowTemplate',
          },
        });
      },
      // 节点的移动功能,上移和下移
      async nodeMove(type: string) {
        const { id } = state.selectedNode;
        state.loading = true;
        new Api(workflowApi)
          .fetch(
            {
              classifyId: id,
              moveType: type,
            },
            'act-classify/move',
            'PUT',
          )
          .then(() => {
            moveTreeNode(state.treeData, id, type);
            message.success('移动成功！');
            state.loading = false;

            const { name } = state.selectedNode;
          })
          .catch(() => {
            state.loading = false;
          });
      },
      // 节点树的选择功能,去获取角色和角色下的用户
      handleSelectNode(selectedKeys: any, e: any) {
        if (selectedKeys?.length) {
          state.selectedNode = e?.selectedNodes[0].props;
          let selectNode = findNodeById(state.treeData, selectedKeys[0]);
          state.options.auto.params.query.classifyId = selectNode.parentId ? selectedKeys[0] : ''; // 如果是顶部分类则参数为空
          flowTableRef.value.reload();
        } else {
          // 不能取消当前选中
          state.selectedKeys = [state.selectedNode.id];
        }
      },
      setModalFormProps,
      setModalFlowFormProps,
      registerForm,
      registerFlowForm,
      replaceFields: {
        title: 'name',
        key: 'id',
      },
      // 关键字键功能
      keywordSearch: (e: any) => {
        const value = e.target.value;
        state.expandedKeys = state.dataList
          .map((item: any) => {
            if (item.name.indexOf(value) > -1) {
              return getParentKey(item.id, state.treeData);
            }
            return null;
          })
          .filter((item: any, i: number, self: any) => item && self.indexOf(item) === i);
        state.autoExpandParent = true;
        state.keyword = value;
      },
      // 打开增加节点弹窗
      addNode() {
        state.dialogType = 'add';
        openModalForm(true, state.selectedNode);
      },
      // 打开删除弹窗
      deleteNode() {
        if (state?.selectedNode?.id) {
          state.deletedVisible = true;
        } else {
          message.error('请选择一个要删除的节点');
        }
      },
      // 删除节点确认功能
      removeNodeConfirm() {
        const { selectedNode } = state;
        if (selectedNode?.children?.length) {
          message.error('请先删除子节点');
        } else {
          state.confirmLoading = true;
          apiOrg
            .del(selectedNode.id)
            .then(() => {
              message.success('删除成功！');
              state.treeData = deletedNodeById(state.treeData, selectedNode.id); // 本地删除,不刷新列表
              state.deletedVisible = false;
              state.confirmLoading = false;
            })
            .catch(() => {
              state.deletedVisible = false;
              state.confirmLoading = false;
            });
        }
      },
      editNode() {
        state.dialogType = 'edit';
        openModalForm(true, state.selectedNode);
      },
      // 编辑确认之后,本地更新字段.
      updateFiled: (formModel: any) => {
        fetchTreeList(formModel.id);
      },
      // 重新获取列表,并选中当前创建的新节点
      updateList(data: any) {
        const { id, parentId } = data;
        state.treeData = addTreeNode(state.treeData, parentId, data);
        state.selectedKeys = [id]; // 选中当前ID
        state.selectedNode = findNodeById(state.treeData, id); // 获取节点信息
        state.expandedKeys = findParents(state.treeData, id, false); // 展开当前节点
      },
      updateTable(classifyId: string) {
        state.selectedKeys = [classifyId];
        state.expandedKeys = findParents(state.treeData, classifyId, false); // 展开当前节点
        state.options.auto.params.query.classifyId = classifyId;
        flowTableRef.value.reload();
      },
      onExpand(expandedKeys: []) {
        state.expandedKeys = expandedKeys;
        state.autoExpandParent = false;
      },
      flowTableRef,
      treeRef,
      ...toRefs(state),
      filterSchemas: [
        {
          field: 'publish_id',
          component: 'ApiSelect',
          label: '发布人',
          colProps: {
            span: 8,
          },
          componentProps: {
            mode: 'multiple',
            api: () => new Api(workflowApi).fetch({}, 'act-system/publisher/page', 'POST'),
            labelField: 'name',
            valueField: 'id',
          },
        },
        {
          field: 'publish_time',
          component: 'RangePicker',
          label: '发布时间',
          colProps: {
            span: 12,
          },
          componentProps: {},
        },
      ],
      filterChange({ queryCondition }) {
        console.log('外面接收到的', queryCondition);
      },
      // 打开/关闭筛选按钮事件
      filterOpenChange() {
        console.log('打开关闭');
      },
      onDeleteFlow(row) {
        Modal.confirm({
          title: '删除提示',
          icon: createVNode(ExclamationCircleOutlined),
          content: '确认删除这条数据吗',
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            new Api(workflowApi)
              .fetch(
                {},
                `act-flow/delete?id=${row.id}&userId=${userStore.getUserInfo.id}`,
                'DELETE',
              )
              .then(() => {
                message.success('删除成功');
                flowTableRef.value.reload();
              });
          },
        });
      },
      confirmDel() {},
      initData() {
      },
      actionChange({ type }) {
      },
    };
  },
});
</script>
<style lang="less" scoped>
  .split {
    color: #dcdcdc;
    margin: 0 10px;
  }
  .copy-modal {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
    span {
      white-space: nowrap;
      margin-right: 10px;
    }
  }
</style>
