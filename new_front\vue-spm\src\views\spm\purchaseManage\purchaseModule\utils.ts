import { isPower, openDrawer } from 'lyra-component-vue3';
import {
  h, reactive, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { isBoolean, isString, isNumber } from 'lodash-es';
import { Num } from 'windicss/types/lang/tokens';

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  const formData = reactive({ ...(record || {}) });
  openDrawer({
    title: record?.id ? '编辑' : '新增',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
        formData,
        record,
      });
    },
    async onOk(): Promise<void> {
      const form = drawerRef.value;
      await form?.onSubmit?.();
      cb?.();
    },
  });
}

// 表格字段特殊处理
export function formatTableColumns(columns: any[]): any[] {
  return columns.map((item) => {
    switch (item.type) {
      case 'selectuser':
      case 'selectdict':
        const getValue = (record) => record[item.valueField]?.map((v: any) => v.name).join('、');
        item.customRender = ({ record }) => h('span', {
          class: 'flex-te',
          title: getValue(record),
        }, getValue(record));
        break;
      case 'selectdept':
      case 'selectproject':
        item.customRender = ({ record }: {
                    record: Record<string, any>
                }) => h('span', {
          class: 'flex-te',
          title: record[item.valueField],
        }, record[item.valueField]);
        break;
    }
    switch (item.dataIndex) {
      case 'creatorId':
      case 'modifyId':
      case 'ownerId':
        item.customRender = ({ record }: {
                    record: Record<string, any>
                }) => h('span', {
          class: 'flex-te',
          title: record[item.dataIndex.replace('Id', 'Name')],
        }, record[item.dataIndex.replace('Id', 'Name')]);
        break;
      case 'createTime':
      case 'modifyTime':
        item.customRender = ({ text }: {
                    text: string
                }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
        break;
    }
    return item;
  });
}

// 编辑页面表单回显
export function setFormFieldsValues({
  forms, selectedData, result,
  setFieldsValue,
}) {
  forms.forEach((field: Record<string, any>) => {
    switch (field.type) {
      case 'selectdept':
      case 'selectproject':
        selectedData[field.field] = result[field.field] ? [
          {
            id: result[field.field],
            name: result[field.valueField],
          },
        ] : [];
        break;
      case 'selectdict':
        selectedData[field.field] = result[field.field] ? result[field.valueField]?.map((item: Record<string, any>) => ({
          value: item.value,
          label: item.description,
        })) : [];
        break;
      case 'selectuser':
        selectedData[field.field] = result[field.field] ? result[field.valueField]?.map((item: Record<string, any>) => ({
          id: item.id,
          name: item.name,
        })) : [];
        break;
    }
    // 解决字段未挂载完成提前校验的问题
    setTimeout(() => {
      setFieldsValue({
        [field.field]: result[field.field],
      });
    });
  });
}

// 详情基本信息回显
export function setBasicInfo(list: any[]): any[] {
  list = list.map((item) => {
    switch (item.type) {
      case 'selectproject':
      case 'selectdept':
        item.valueRender = ({ record }) => record[`${item.field}Name`];
        break;
      case 'selectuser':
      case 'selectdict':
        item.valueRender = ({ record }) => record[`${item.field}Name`]?.map((v) => v.name)?.join('、') || '-';
        break;
    }
    if (item.formatter) {
      item.valueRender = ({ record }) => item.formatter(record[item.field], record);
    }
    item.wrap = true;
    return item;
  });
  return list;
}

// 格式化表格行权限配置
export function formatActionsPower(actions: any[]): any[] {
  return actions.map((item) => ({
    ...item,
    isShow: (record: Record<string, any>) => isPower(item.code, record.rdAuthList),
  }));
}

export function usePriceUnit(price:number) {
  return price
    ? `￥${parseFloat(price).toFixed(2)}`
    : '-';
}
export function parsePriceByNumber(num) {
  if (!num || isNaN(Number(num))) {
    return '0.00';
  }
  let [integer, decimal = ''] = String(num).split('.');
  decimal = decimal
    .padEnd(2, '0');
  integer = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return [integer, decimal].join('.');
}
export function parseBooleanToRender(val) {
  if (isBoolean(val)) {
    return val ? '是' : '否';
  }
  if (['0', '1'].includes(`${val}`)) {
    return `${val}` === '0' ? '否' : '是';
  }
  if (isString(val)) {
    return val.trim() ? val : '否';
  }
  return val ? '是' : '否';
}

function getCurrentWeek(date:dayjs.Dayjs, type:dayjs.UnitType) {
  // 获取指定年月的第一天和最后一天
  const firstDay = date.startOf(type);
  const lastDay = date.endOf(type);
  // 获取指定日期的星期一
  let monday = getMondayOfCurrentWeek(firstDay);
  let currentWeek = getCurrentWeekOfYear(firstDay);
  let weeks = getCurrentWeekData(monday, firstDay, lastDay, currentWeek);
  // 获取当前日期所在周的周数
  const currentWeekNumber = getCurrentWeekOfYear(dayjs());
  return {
    weeks,
    totalWeeks: weeks.length,
    currentWeekNumber,
  };
}
function getCurrentWeekData(monday:dayjs.Dayjs, firstDay:dayjs.Dayjs, lastDay:dayjs.Dayjs, currentWeek:number) {
  let weeks = [];
  if (monday.isBefore(lastDay) || monday.isSame(lastDay)) {
    let weekBegin = monday.format('YYYY-MM-DD');
    let weekEnd = monday.add(6, 'day').format('YYYY-MM-DD');
    weeks.push({
      weekBegin,
      weekEnd,
      value: currentWeek,
      name: `第${currentWeek}周（${weekBegin}~${weekEnd}）`,
    });
    let nextWeek = getCurrentWeekData(monday.add(7, 'day'), firstDay, lastDay, currentWeek + 1);
    weeks = weeks.concat(nextWeek);
  }
  return weeks;
}
// 获取当前日期的星期一
function getMondayOfCurrentWeek(date:dayjs.Dayjs) {
  const dayOfWeek = date.day(); // 获取今天是本周的第几天，0表示周日，1表示周一，以此类推
  const diff = date.date() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // 计算本周一的日期

  return date.date(diff);
}
// 获取当前日期是当年的第几周
function getCurrentWeekOfYear(date:dayjs.Dayjs) {
  const startOfYear = date.startOf('year'); // 当年第一天
  const diffDays = date.diff(startOfYear, 'day'); // 当年第几天
  return Math.ceil((diffDays + startOfYear.day()) / 7); // 当年第几周;
}

export {
  getCurrentWeek,
  getCurrentWeekOfYear,
  getMondayOfCurrentWeek,
};

