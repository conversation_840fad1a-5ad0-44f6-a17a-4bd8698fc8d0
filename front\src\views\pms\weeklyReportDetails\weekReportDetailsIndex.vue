<template>
  <Layout3
    v-loading="state.layoutLoading"
    :projectData="state.mainDataInfo"
    :defaultActionId="state.actionId"
    :type="2"
    :menuData="state.tabsOption"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        v-if="state.mainDataInfo"
        :actions="state.actionsLis"
        :record="state.mainDataInfo"
        type="button"
        @actionClick="actionClick"
      />
    </template>
    <MainDataIndex
      v-if="state.actionId==='weeklyReportDetails'"
      :dataInfo="state.cacheData"
    />
    <addOrEditDrawer
      ref="addOrEditRef"
      @update="updateTable"
    />
    <CheckDrawer
      ref="CheckRef"
      @update="updateTable"
    />
  </Layout3>
</template>

<script setup lang="ts">
import {
  reactive, PropType, onMounted, ref,
} from 'vue';
import { Button, Modal, message } from 'ant-design-vue';
import { Layout3, BasicTableAction } from 'lyra-component-vue3';
import Api from '/@/api';
// import { PlusOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { getTabsOptions, getHeaderRightActionsList } from './config';
import MainDataIndex from './component/mainDataInfo/MainDataIndex.vue';
import CheckDrawer
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/weekReportCheck/component/addOrEdit/CheckDrawer.vue';
import AddOrEditDrawer
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/weeklyReport/component/addOrEdit/addOrEditDrawer.vue';
const route = useRoute();

const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  trigger: {
    type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
    default: () => ['contextmenu'],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const addOrEditRef = ref(null);
const CheckRef = ref(null);
const state = reactive({
  actionId: '',
  mainDataInfo: {},
  cacheData: {},
  tabsOption: getTabsOptions(),
  layoutLoading: false,
  actionsLis: getHeaderRightActionsList(),
});

// 头部右侧按钮回调
function actionClick(record) {
  switch (record.event) {
    case 'edit':
      addOrEditRef.value.openDrawer({
        action: 'edit',
        info: { id: `${route.query?.curId}` },
      });
      break;
    case 'approve':
      CheckRef.value.openModal({
        action: 'add',
        info: {
          record: { id: route.query?.curId },
          type: '1',
        },
      });
      break;
    case 'commit':
      Modal.confirm({
        title: '提交确认提示',
        content: '请确认是否提交？',
        onOk() {
          return new Api('/pms').fetch('', `projectWeekly/submit/${state.cacheData?.id}`, 'PUT').then(() => {
            message.info('操作成功');
            getDayilyData();
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
      break;
  }
}

function getDayilyData() {
  state.layoutLoading = true;
  return new Api(`/pms/projectWeekly/${route.query?.curId}`).fetch('', '', 'GET').then((res) => {
    res.name = `${dayjs(res.weekBegin).format('YYYY')}年第${res.week}周周报`;
    state.cacheData = JSON.parse(JSON.stringify(res));
    state.mainDataInfo = res;
    // fileDTOList
  }).finally(() => {
    state.layoutLoading = false;
  });
}

onMounted(() => {
  state.tabsOption?.length && (state.actionId = state.tabsOption[0]?.id);

  getDayilyData();
});

function updateTable() {
  getDayilyData();
}

function menuChange(val) {
  state.actionId = val.id;
}
</script>

<style scoped lang="less"></style>
