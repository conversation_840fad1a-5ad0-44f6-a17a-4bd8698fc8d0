<template>
  <Layout2
    left-title="计划规则设置"
    class="resource-left"
  >
    <template #left>
      <div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          style="width: 99.5%"
          mode="inline"
          :open-keys="openKeys"
          @select="select"
        >
          <a-sub-menu key="sub1">
            <template #icon>
              <ApartmentOutlined />
            </template>
            <template
              #title
            >
              规则类型
            </template>
          </a-sub-menu>
          <a-menu-item
            v-for="item in menuOptions"
            :key="item.id"
          >
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
    <div class="project-rule-content">
      <template v-if="selectedKey==='prePlanTimeRule'">
        <div class="project-rule-time">
          <div
            v-if="isPower('PMS_XMXQ_container_12_04_button_01',powerData)"
            class="rule-time-title"
          >
            <span class="title-span">是否启用前置计划时间规则：</span>
            <ARadioGroup
              v-model:value="ruleValue"
              :options="timeOptions"
              @change="changeRadio"
            />
          </div>
          <div class="rule-time-content">
            <div class="rule-time-content-title">
              已启用计划前置关系时间控制功能，则本项目的所有计划需受以下条件约束：
            </div>
            <div class="rule-time-content-list">
              <div class="rule-list-item">
                1、前置任务的完成是后置任务的开始条件，前置任务必须在后置任务开始之前完成
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, watch, inject, unref,
} from 'vue';
import { isPower, Layout2 } from 'lyra-component-vue3';
import { ApartmentOutlined } from '@ant-design/icons-vue';
import { Menu, message, Radio } from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'Resource',
  components: {
    Layout2,
    AMenu: Menu,
    ASubMenu: Menu.SubMenu,
    AMenuItem: Menu.Item,
    ApartmentOutlined,
    ARadioGroup: Radio.Group,
  },

  emits: ['business'],
  setup(props, { emit }) {
    const formData = inject('formData');
    const powerData = inject('powerData');
    const state = reactive({
      menuOptions: [
        {
          id: 'prePlanTimeRule',
          name: '前置计划时间规则',
        },
      ],
      selectedKeys: ['prePlanTimeRule'],
      openKeys: ['sub1'],
      selectedKey: 'prePlanTimeRule',
      timeOptions: [
        {
          label: '是',
          value: 'true',
        },
        {
          label: '否',
          value: 'false',
        },
      ],
      ruleValue: '',
      ruleData: {},
    });

    onMounted(async () => {
      getFormData();
    });
    function getFormData() {
      new Api('/pms').fetch('', `projectSetUp/${formData.value.id}/${state.selectedKey}`, 'GET').then((res) => {
        state.ruleValue = res.value;
        state.ruleData = res;
      });
    }
    function saveForm(val) {
      let params = {
        id: state.ruleData.id,
        key: state.ruleData.key,
        projectId: state.ruleData.projectId,
        value: val,
      };
      new Api('/pms').fetch(params, 'projectSetUp', 'PUT').then((res) => {
        message.success('保存规则成功');
      });
    }
    function changeRadio(data) {
      saveForm(data.target.value);
    }

    const select = ({ key }) => {
      state.selectedKey = key;
      // state.roleId = key;
      // const rows = state.menuOptions.filter((s) => s.id === key);
      // businessIdEmit(rows[0].businessId);
    };
    return {
      ...toRefs(state),
      select,
      changeRadio,
      powerData,
    };
  },
  methods: { isPower },
});
</script>

<style lang="less" scoped>
:deep(.ant-menu) {
  box-sizing: border-box;
  margin: 0;
  border: 0;
  .ant-menu-item,
  .ant-menu-item-active {
    &::before,
    &::after {
      content: '';
      width: 0;
      height: 0;
    }
  }
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      background: #f5f5f5;
      padding: 0 0 0 10px !important;

      .ant-menu-submenu-arrow {
        &::before,
        &::after {
          content: '';
          width: 0;
          height: 0;
        }
      }
    }
  }
}
.project-rule-content{
  padding: 60px 20px;
  .project-rule-time{
      .rule-time-title{
        padding-bottom: 30px;
        .title-span{
          font-weight: 600;
          padding-right: 5px;
        }
      }
      .rule-time-content{
        .rule-time-content-title{

        }
        .rule-time-content-list{
          .rule-list-item{
            padding-top: 5px;
          }
        }
      }
  }
}

</style>
