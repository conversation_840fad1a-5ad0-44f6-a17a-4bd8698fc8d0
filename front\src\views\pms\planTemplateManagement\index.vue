<template>
  <div
    v-get-power="{pageCode:'PlanTemplateManagement', getPowerDataHandle}"
    class="milestoneImport"
  >
    <div class="topBtn">
      <BasicButton
        v-if="isPower('PMS_JHMBGL_container_04_button_01',powerData)"
        type="primary"
        icon="add"
        @click="handleImport"
      >
        导入模板
      </BasicButton>
    </div>

    <div class="flex content">
      <div class="left-menu">
        <div class="menu-top flex flex-ac flex-pj">
          <span class="templateTitie">计划模板列表</span>
          <div class="menu-top-right">
            <Icon
              v-if="isPower('PMS_JHMBGL_container_01_button_01',powerData)"
              :size="18"
              class="addTemplate"
              icon="sie-icon-tianjiaxinzeng"
              @click="setTemplate('add')"
            />
            <Icon
              v-if="isPower('PMS_JHMBGL_container_01_button_02',powerData)"
              :size="18"
              class="editTemplate"
              icon="sie-icon-bianji"
              @click="setTemplate('edit')"
            />
            <Icon
              v-if="isPower('PMS_JHMBGL_container_01_button_03',powerData)"
              :size="18"
              class="editTemplate ml15"
              icon="sie-icon-del"
              @click="setTemplate('del')"
            />
          </div>
        </div>
        <BasicScrollbar class="menu-content">
          <div>
            <SpinMain v-if="loading" />
            <div
              v-for="(item,index) in menus"
              :key="index"
              class="menuItem"
              :class="item.id===templateId?'active':''"
              @click="getTemplateDetails(item)"
            >
              {{ item.templateName }}
            </div>
          </div>
        </BasicScrollbar>
      </div>
      <div class="right-table">
        <!--          :expandIconColumnIndex="3"-->
        <OrionTable
          ref="tableRef"
          :options="tableOption"
        >
          <template #num="slotProps">
            {{ convertToFormat(slotProps.recordIndexs) }}
          </template>
          <template #name="{ record }">
            <div>
              <Tooltip :getPopupContainer="getPopupContainer">
                <template #title>
                  <div class="pre-post-tooltip">
                    <template v-if="record?.['schemePrePostVOList']?.length">
                      <span>前置任务：</span>
                      <span
                        v-for="(item,index) in record?.['schemePrePostVOList']"
                        :key="item.id"
                      >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                    </template>

                    <template v-if="record?.['schemePostVOList']?.length">
                      <span>后置任务：</span>
                      <span
                        v-for="(item,index) in record?.['schemePostVOList']"
                        :key="item.id"
                      >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                    </template>
                  </div>
                </template>
                <!--前后置计划图标-->
                <Icon
                  v-if="record?.['schemePostVOList']?.length || record?.['schemePrePostVOList']?.length"
                  color="#D50072"
                  icon="fa-sort-amount-asc"
                />
              </Tooltip>
              <span
                class="flex-te"
                :title="record.name"
              >
                {{ record.name }}
              </span>
            </div>
          </template>

          <template #toolbarLeft>
            <span>
              <BasicButton
                v-if="isPower('PMS_JHMBGL_container_03_button_02',powerData)"
                type="primary"
                icon="edit"
                @click="setContentModal('edit')"
              >
                编辑
              </BasicButton>

              <BasicButton
                v-if="isPower('PMS_JHMBGL_container_03_button_01',powerData)"
                type="primary"
                icon="add"
                @click="setContentModal('add')"
              >
                添加
              </BasicButton>
              <BasicButton
                v-if="isPower('PMS_JHMBGL_container_03_button_03',powerData)"
                type="primary"
                icon="delete"
                @click="deleteNodes()"
              >
                删除
              </BasicButton>
              <BasicButton
                v-if="isPower('PMS_JHMBGL_container_03_button_04',powerData)"
                icon="sie-icon-jihuafenjie"
                @click="setBeForeRelation"
              >
                设置前后置关系
              </BasicButton>
            </span>
          </template>
        </OrionTable>
      </div>
    </div>

    <NodeDrawer
      @editSuccess="editSuccess"
      @register="registerNodeDrawer"
      @close="() => setEditPlanModal(false)"
    />

    <TemplateDrawer
      @templateSuccess="templateSuccess"
      @register="registerTemplate"
      @close="() => setEditPlanModal(false)"
    />

    <!-- 计划编制 -->
    <AddPlan
      @register="registerAdd"
      @handleColse="() => addModalVisibleChange(false)"
    />
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
    <!-- 前置关系 -->
    <BeforeRelation

      @register="registerBefore"
      @close="updateTable"
    />
  </div>
</template>

<script setup lang="ts">
import {
  BasicButton, BasicImport, BasicScrollbar, Icon, isPower, OrionTable, useDrawer, useModal, useProjectPower,
} from 'lyra-component-vue3';
import {
  getCurrentInstance,
  inject, nextTick, onMounted, Ref, ref, watchEffect,
} from 'vue';
import { message, Modal, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  deleteNewProject,
  deleteProjectSchemeMilestoneNode,
  postProjectSchemeMilestoneNodeTree,
  postProjectSchemeMilestoneTemplatelist,
} from './api';
import { projectIdKey } from './type';
import NodeDrawer from './component/nodeDrawer.vue';
import AddPlan from './component/AddPlan.vue';
import TemplateDrawer from './component/templateDrawer.vue';
import SpinMain from '/@/views/pms/projectLaborer/projectLab/components/SpinMain.vue';
import Api from '/@/api';
import BeforeRelation from './component/BeforeRelation.vue';
const projectId: string = inject(projectIdKey);
const menus: Ref<any[]> = ref([]);
const tableRef: Ref = ref();
// const powerData = inject('powerData');
const powerData = ref();
const loading = ref<boolean>(false);
const templateId = ref<string>('');
const tableData:Ref<any[]> = ref([]);

const downloadFileObj = {
  url: '/pms/projectSchemeMilestoneNode/import/excel/template',
  method: 'POST',
};
const [registerAdd, { openModal: setAddPlan }] = useModal();
const [register, { openModal }] = useModal();
const [registerNodeDrawer, { openDrawer: openEditNodeDrawer }] = useDrawer();
const [registerTemplate, { openDrawer: openEditTemplate }] = useDrawer();
const fileTypeList = ref([]);
const tableOption = {
  rowSelection: {},
  showToolButton: false,
  showTableSetting: false,
  immediate: false,
  showSmallSearch: false,
  showIndexColumn: false,
  deleteToolButton: 'add|delete|enable|disable',
  api: async (params) => {
    const result = await postProjectSchemeMilestoneNodeTree(
      templateId.value,
    );
    tableData.value = result?.content || [];
    return result;
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'num',
      width: 60,
      slots: { customRender: 'num' },
    },
    {
      title: '计划名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '层级',
      dataIndex: 'nodeChainName',
    },
    {
      title: '计划类型',
      dataIndex: 'nodeType',
      customRender({ text }) {
        return text === 'milestone' ? '里程碑节点' : '计划';
      },
    },
    {
      title: '责任部门',
      dataIndex: 'rspDeptName',
    },
    {
      title: '工期',
      dataIndex: 'durationDays',
    },
    {
      title: '项目从N天开始',
      dataIndex: 'delayDays',
    },
    {
      title: '是否关联流程',
      dataIndex: 'processFlag',
      customRender({ record }) {
        return record.processFlag ? '是' : '否';
      },
    },
    {
      title: '描述说明',
      dataIndex: 'remark',
    },
  ],
};

onMounted(async () => {
  getMenus();
});
const internalInstance = getCurrentInstance();
// 获取权限
function getPowerDataHandle(pData) {
  powerData.value = pData;
}
// 默认选中第一条数据
watchEffect(() => {
  if (menus.value?.length && menus.value?.every((item) => item?.id !== templateId.value)) {
    templateId.value = menus.value[0]?.id;
    updateTable();
  }
});
function addModalVisibleChange(value: boolean) {
  // if (tableRows.value.length > 1) {
  //   message.error('计划编制只能选择一条项目计划');
  //   return;
  // }
  //
  // if (value) {
  //   setAddPlan(true, {
  //     parentIds: tableRows.value.map((item) => item?.id),
  //     parentData: tableRows.value,
  //     projectData: props?.projectData,
  //     from: props?.from || '',
  //   });
  // }
  // if (!value) {
  //   updateForm();
  // }
  updateTable();
}
// 获取左侧菜单
async function getMenus() {
  loading.value = true;
  try {
    menus.value = await postProjectSchemeMilestoneTemplatelist();
    menus.value = menus.value.map((item) => ({
      ...item,
      name: item.roleName,
    }));
  } finally {
    loading.value = false;
  }
}

// 刷新表格数据
function updateTable() {
  nextTick(() => {
    tableRef?.value?.reload();
  });
}

function getTemplateDetails(item) {
  templateId.value = item.id;
  updateTable();
}

function setEditPlanModal(value) {
}
function editSuccess(data) {
  if (data) {
    updateTable();
  }
}
function templateSuccess(data) {
  getMenus();
}

const setTemplate = (type = 'edit') => {
  if (type === 'add') {
    openEditTemplate(true, {
      type,
      templateId: templateId.value,
    });
  } else if (type === 'edit') {
    if (templateId.value) {
      openEditTemplate(true, {
        type,
        id: templateId.value,
        sort: menus.value?.filter((item) => item?.id === templateId?.value)?.[0]?.sort,
        templateName: menus.value?.filter((item) => item?.id === templateId?.value)?.[0]?.templateName,
      });
    } else {
      message.warn('请选择一条计划模板进行编辑！');
    }
  } else if (type === 'del') {
    if (templateId.value) {
      if (tableData.value.length) {
        return message.error('当前计划模板存在里程碑节点，无法删除！');
      }
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除所选计划模板，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            new Api('/pms/projectSchemeMilestoneTemplate').fetch([templateId.value], '', 'delete')
              .then(() => {
                getMenus();
                message.success('删除成功');
                resolve(true);
              })
              .finally(() => {
                resolve('');
              });
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    } else {
      message.warn('请选择一条计划模板进行删除！');
    }
  }
};

const setContentModal = (type = 'edit') => {
  let selectRow = tableRef.value.getSelectRows();
  if (type === 'add') {
    // openEditNodeDrawer(true, {
    //   type,
    //   templateId: templateId.value,
    // });
    if (selectRow.length <= 1) {
      setAddPlan(true, {
        type,
        templateId: templateId.value,
        parentId: selectRow.length === 0 ? '' : selectRow[0].id,
      });
    } else {
      message.warn('请选择一条计划节点新增！');
    }
  } else if (selectRow && selectRow.length === 1) {
    openEditNodeDrawer(true, {
      type,
      templateId: templateId.value,
      ...selectRow[0],
    });
  } else {
    message.warn('请选择一条计划节点编辑！');
  }
};
function convertToFormat(recordIndexes) {
  if (recordIndexes.length === 0) {
    return '--';
  }

  const incrementedIndexes = recordIndexes.map((item, index) => {
    if (index === 0) {
      return parseInt(item) + 1;
    }
    return parseInt(item) + 1;
  });
  const formattedIndexes = incrementedIndexes.join('-');
  return formattedIndexes;
}
async function deleteNodes() {
  let selectRow = tableRef.value.getSelectRows();
  let ids = [];
  if (selectRow) {
    for (let i in selectRow) {
      ids.push(selectRow[i].id);
    }
  }
  if (ids && ids.length > 0) {
    Modal.confirm({
      title: '删除确认提示？',
      content: '请确认是否删除所选计划节点，删除后不可恢复？',
      onOk() {
        return new Promise((resolve) => {
          deleteProjectSchemeMilestoneNode(ids).then(() => {
            updateTable();
            resolve(true);
          })
            .finally(() => {
              resolve('');
            });
        });
      },
      onCancel() {
        Modal.destroyAll();
      },
    });
  } else {
    message.warn('请选择一条计划节点删除！');
  }
}

const handleImport = () => {
  openModal(true, {});
};

function changeImportModalFlag() {
  // updateForm();
}

function requestBasicImport(data) {
  // let importId = '';
  // let selectRowKeys = tableRef.value.getSelectRowKeys();
  // if (selectRowKeys.length === 1) {
  //   importId = selectRowKeys[0];
  // }
  let formData = new FormData();
  formData.append('file', data[0]);
  formData.append('tplId', templateId.value);

  return new Api('/pms').fetch(formData, 'projectSchemeMilestoneNode/import/excel/check', 'POST');
}
function getPopupContainer(): Element {
  return document.querySelector('.milestoneImport');
}

const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/projectSchemeMilestoneNode/import/excel/do/?importKey=${importId}`).fetch('', '', 'post')
      .then(() => {
        resolve({
          result: true,
        });
      }).then(() => {
        getMenus();
        updateTable();
      });
  });

const [registerBefore, { openDrawer: openBeforeRelation }] = useDrawer();
function setBeForeRelation() {
  let selectRow = tableRef.value.getSelectRows();
  if (selectRow.length !== 1) {
    message.warning('请选择一条数据设置前置关系');
    return;
  }
  openBeforeRelation(true, {
    templateId: templateId.value,
    parentIds: selectRow.map((item) => item.id),
  });
}

</script>

<style scoped lang="less">
.milestoneImport {
  height:100%;
  padding:0 16px ;
  background: #fff;
  .topBtn {
    background: #fff;
    padding:16px 0  ;
  }
  .content {
    border: 1px solid rgba(233, 233, 233, 1);
    height: 100%;

  }

  .left-menu {
    height: 100%;
    width:300px;
    background: #fff;
    border-right: 1px solid rgba(233, 233, 233, 1);
    .menu-top {
      height:60px;
      line-height: 60px;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(233, 233, 233, 1);      .templateTitie {
      font-size:18px;
      color:#333;
    }
      .addTemplate {
        cursor: pointer;
        margin-right:10px;
      }
      .editTemplate {
        cursor: pointer;
      }
    }

    .menu-content {
      height:555px;
      overflow: hidden;

      .menuItem {
        height: 42px;
        line-height: 42px;
        cursor: pointer;
        font-size:14px;
        padding: 0 20px;
        color:#333;
        &:hover {
          color:~`getPrefixVar('primary-color')` !important;
        }

      }

      .active {
        background:~`getPrefixVar('primary-1')`  !important;
        color:~`getPrefixVar('primary-color')` !important;
      }
    }

  }

  .right-table {
    flex:1;

    overflow: hidden;
    .lichengbei-icon {
      color:#FFB119;
    }
  }
}
.pre-post-tooltip {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  padding: 0 5px;

  span {
    line-height: 1.5;
  }
}
</style>
