package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BasePlaceDTO;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.xxljob.PersonJobPostAuthorizeJobHandler;
import com.chinasie.orion.xxljob.PersonVerificationXxlJob;
import com.chinasie.orion.xxljob.PositionAuthorizeXxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasePlace 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
@RestController
@RequestMapping("/base-place")
@Api(tags = "基地库")
public class BasePlaceController {

    @Autowired
    private BasePlaceService BasePlaceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看基地库【{{#id}}】-【{{#name}}】详情", type = "BasePlace", subType = "查询", bizNo = "{{#id}}")
    public ResponseDTO<BasePlaceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        BasePlaceVO rsp = BasePlaceService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param basePlaceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#basePlaceDTO.name}}】基地", type = "BasePlace", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody BasePlaceDTO basePlaceDTO) throws Exception {
        String rsp =  BasePlaceService.create(basePlaceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     * @param basePlaceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#basePlaceDTO.name}}】的信息", type = "BasePlace", subType = "编辑", bizNo = "{{#basePlaceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody BasePlaceDTO basePlaceDTO) throws Exception {
        Boolean rsp = BasePlaceService.edit(basePlaceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除基地【{{#baseCodes}}】-【{{#baseNames}}】数据", type = "BasePlace", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = BasePlaceService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除基地【{{#baseCodes}}】-【{{#baseNames}}】数据", type = "BasePlace", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = BasePlaceService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询基地列表数据", type = "BasePlace", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasePlaceVO>> pages(@RequestBody Page<BasePlaceDTO> pageRequest) throws Exception {
        Page<BasePlaceVO> rsp =  BasePlaceService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表接口")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询基地数据列表", type = "BasePlace", subType = "查询列表", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<BasePlaceVO>> getBasePlace() throws Exception {
        List<BasePlaceVO> rsp =  BasePlaceService.allList();
        return new ResponseDTO<>(rsp);
    }

    @Autowired
    PersonVerificationXxlJob personVerificationXxlJob;

    @PostMapping("/job1")
    public ResponseDTO<Void> testJob() throws Exception {
        personVerificationXxlJob.personVerificationValidJob();
        return new ResponseDTO<>();
    }

    @Autowired
    PositionAuthorizeXxlJob positionAuthorizeXxlJob;

    @PostMapping("/job2")
    public ResponseDTO<Void> testJo2(){
        positionAuthorizeXxlJob.positionAuthorize();
        return new ResponseDTO<>();
    }

}
