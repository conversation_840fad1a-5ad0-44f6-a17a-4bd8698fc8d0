package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SupplierInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierInfoDTO对象", description = "潜在供应商报表")
@Data
@ExcelIgnoreUnannotated
public class SupplierInfoExcelDTO  implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String name;


    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    @ExcelProperty(value = "供应商类别 ", index = 2)
    private String secondaryCompanyName;



    /**
     * 可提供产品/服务
     */
    @ApiModelProperty(value = "可提供产品/服务")
    @ExcelProperty(value = "可提供产品/服务 ", index = 3)
    private String productsServices;

    /**
     * 可提供产品/服务文字描述
     */
    @ApiModelProperty(value = "可提供产品/服务文字描述")
    @ExcelProperty(value = "可提供产品/服务文字描述 ", index = 4)
    private String productsServicesDesc;



    /**
     * 营业执照注册号/统一社会信用代码
     */
    @ApiModelProperty(value = "营业执照注册号/统一社会信用代码")
    @ExcelProperty(value = "营业执照注册号/统一社会信用代码 ", index = 5)
    private String businessLicenseNum;


    /**
     * 营业执照注册日期
     */
    @ApiModelProperty(value = "营业执照注册日期")
    @ExcelProperty(value = "营业执照注册日期", index = 6)
    private Date licenseRegDate;

    /**
     * 营业执照有效期起
     */
    @ApiModelProperty(value = "营业执照有效期起")
    @ExcelProperty(value = "营业执照有效期起 ", index = 7)
    private Date businessLicenseStart;

    /**
     * 营业执照有效期至
     */
    @ApiModelProperty(value = "营业执照有效期至")
    @ExcelProperty(value = "营业执照有效期至 ", index = 8)
    private Date businessLicenseEnd;


    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    @ExcelProperty(value = "联系人手机 ", index = 9)
    private String landlinePhone;


    /**
     * 找回密码邮箱
     */
    @ApiModelProperty(value = "找回密码邮箱")
    @ExcelProperty(value = "联系人邮箱 ", index = 10)
    private String findPassEamil;


    /**
     * 供应商缴费有效截止日期
     */
    @ApiModelProperty(value = "供应商缴费有效截止日期")
    @ExcelProperty(value = "供应商缴费有效截止日期", index = 11)
    private Date paymentEffectiveDeadline;


}
