package com.chinasie.orion.management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.management.domain.dto.RequirementManageCustContactDTO;
import com.chinasie.orion.management.domain.entity.RequirementManageCustContact;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.RequirementManageCustContactVO;
import com.chinasie.orion.management.repository.RequirementManageCustContactMapper;
import com.chinasie.orion.management.service.RequirementManageCustContactService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * RequirementManageCustContact 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 11:00:57
 */
@Service
@Slf4j
public class RequirementManageCustContactServiceImpl extends OrionBaseServiceImpl<RequirementManageCustContactMapper, RequirementManageCustContact> implements RequirementManageCustContactService {

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public RequirementManageCustContactVO detail(String id, String pageCode) {
        RequirementManageCustContact requirementManageCustContact = this.getById(id);
        RequirementManageCustContactVO result = BeanCopyUtils.convertTo(requirementManageCustContact,
                RequirementManageCustContactVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }

    /**
     * 需求单保存客户相关联系人
     *
     * @param contacts    联系人
     * @param requirement 需求单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRequirementContracts(List<RequirementManageCustContact> contacts, RequirementMangement requirement) {
        final LambdaQueryWrapper<RequirementManageCustContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RequirementManageCustContact::getRequirementId, requirement.getId());

        final List<RequirementManageCustContact> existsList = this.list(queryWrapper);

        // 默认所有existsList都做删除，如果根据id对比在contacts中存在，则做更新操作
        final List<String> removes = existsList.stream().map(RequirementManageCustContact::getId)
                .filter(id -> !contacts.stream().map(RequirementManageCustContact::getId).collect(Collectors.toList())
                        .contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removes)) {
            this.removeBatchByIds(removes);
        }

        final List<RequirementManageCustContact> updateList = Lists.newArrayList();
        final List<RequirementManageCustContact> insertList = Lists.newArrayList();
        contacts.forEach(e -> {
            e.setRequirementId(requirement.getId());
            if (null != e.getId() && StringUtils.isNotBlank(e.getId())) {
                updateList.add(e);
            } else {
                insertList.add(e);
            }
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            this.saveBatch(insertList);
        }
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequirementManageCustContactVO> pages(Page<RequirementManageCustContactDTO> pageRequest) {
        LambdaQueryWrapperX<RequirementManageCustContact> condition = new LambdaQueryWrapperX<>(RequirementManageCustContact.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (null != pageRequest.getOrders()) {
            pageRequest.getOrders().forEach(order -> condition.orderBy(true, order.getAsc(), order.getColumn()));
        }

        PageResult<RequirementManageCustContact> page = this.getBaseMapper().selectPage(pageRequest, condition);

        Page<RequirementManageCustContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RequirementManageCustContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RequirementManageCustContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void setEveryName(List<RequirementManageCustContactVO> vos) {

        vos.forEach(vo -> {
        });

    }

}
