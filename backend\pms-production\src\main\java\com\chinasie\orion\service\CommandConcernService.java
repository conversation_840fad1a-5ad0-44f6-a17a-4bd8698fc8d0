package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.CommandConcernDTO;
import com.chinasie.orion.domain.entity.CommandConcern;
import com.chinasie.orion.domain.vo.CommandConcernVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CommandConcern 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
public interface CommandConcernService extends OrionBaseService<CommandConcern> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    CommandConcernVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param commandConcernDTO
     */
    Boolean create(List<CommandConcernDTO> commandConcernDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param commandConcernDTO
     */
    Boolean edit(List<CommandConcernDTO> commandConcernDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<CommandConcernVO> pages(Page<CommandConcernDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CommandConcernVO> vos) throws Exception;

    /**
     * @return 获取所有数据
     */
    List<CommandConcernVO> getAll() throws Exception;

    /**
     * 判断当前登录用户是否为大修角色
     * @return 判断当前登录用户是否为大修角色
     */
    Boolean isManager();

    /**
     * 获取当前用户的大修轮次
     * @return 获取当前用户的大修轮次
     */
    List<String> getRepairRound();


}
