<project xmlns="http://maven.apache.org/POM/4.0.0" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<name>Bean Searcher</name>
	<artifactId>bean-searcher-boot-starter</artifactId>
	<packaging>jar</packaging>

	<parent>
		<groupId>cn.zhxu</groupId>
		<artifactId>bean-searcher-parent</artifactId>
		<version>4.2.4</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>cn.zhxu</groupId>
			<artifactId>bean-searcher</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
			<version>${spring.boot.version}</version>
		</dependency>
		<dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-autoconfigure</artifactId>
		  <version>${spring.boot.version}</version>
		  <scope>provided</scope>
		</dependency>
		<dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-configuration-processor</artifactId>
		  <version>${spring.boot.version}</version>
		  <optional>true</optional>
		</dependency>
		<dependency>
			<groupId>cn.zhxu</groupId>
			<artifactId>xjsonkit-api</artifactId>
			<version>${xjsonkit.version}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<finalName>bean-searcher-boot-starter</finalName>
	</build>
	
</project>