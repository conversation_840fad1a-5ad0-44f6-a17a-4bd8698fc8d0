import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 计划列表 */
  planSearchList = 'projectScheme/search/list',
  /* add需求关联计划 */
  addDemandContactitem = 'demand-management/relation/plan',
  /* add风险关联计划 */
  addRiskContactitem = 'risk-management/relation/plan',
  /* add问题关联计划 */
  addquestionContactitem = 'question-management/relation/plan',
  /* 简单计划树 */
  simplePlanTreeList = 'plan/tree/simple/list',
  /* 需求转计划所属类型 */
  isType = 'task-subject/getList',
  /* 转单计划 */
  addOddPlan = 'demand-management/demandChangePlans',
  /* 零组件列表 */
  concactTable = 'plan/relation/component',
  // 通过字段搜索零组件
  searchItem = 'plan/search/component',
  // 添加零组件
  addsearchItem = 'plan/relation/component',
  // 删除零组件
  deleteItem = 'plan/relation/component/batch'
}

// 通过字段搜索零组件
export function deleteItemApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.deleteItem}/`, 'DELETE');
}
// 通过字段搜索零组件
export function searchItemApi(params) {
  return new Api(base).fetch(params, `${zApi.searchItem}/`, 'POST');
}
// 添加零组件
export function addsearchItemApi(params) {
  return new Api(base).fetch(params, `${zApi.addsearchItem}/`, 'POST');
}
// 分页
export function planSearchListApi(params) {
  return new Api(base).fetch(params, `${zApi.planSearchList}/`, 'POST');
}
/* add需求关联计划 */
export function addDemandContactitemApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.addDemandContactitem}/`, 'POST');
}
/* add风险关联计划 */
export function addRiskContactitemApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.addRiskContactitem}/`, 'POST');
}
/* add问题关联计划 */
export function addquestionContactitemApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.addquestionContactitem}/`, 'POST');
}
/* add问题关联计划 */
export function simplePlanTreeListApi(projectxx) {
  return new Api(base).fetch(projectxx, `${zApi.simplePlanTreeList}/`, 'POST');
}
/* 所属计划 */
export function isTypeApi(projectxx) {
  return new Api(base).fetch('', `${zApi.isType}/${projectxx}`, 'GET');
}
/* add问题关联计划 */
export function addOddPlanApi(itemIdd, obj) {
  const love = {
    id: itemIdd,
    className: 'DemandManagement',
    moduleName: '项目管理-计划管理-需求管理', // 模块名称
    type: 'SAVE', // 操作类型
    remark: `分解了任务【${itemIdd}】`,
  };
  return new Api(base, love).fetch(obj, `${zApi.addOddPlan}/${itemIdd}`, 'POST');
}
/* 零组件列表 */
export function concactTableApi(itemIdd, type) {
  if (type === 'plan') {
    const love = {
      id: itemIdd,
      className: 'Plan',
      moduleName: '项目管理-计划管理-项目计划-关联内容-关联零组件', // 模块名称
      type: 'GET', // 操作类型
      remark: `获取/搜索了【${itemIdd}】关联零组件列表`,
    };
    return new Api(base, love).fetch('', `${zApi.concactTable}/${itemIdd}`, 'GET');
  }
  return new Api(base).fetch('', `${zApi.concactTable}/${itemIdd}`, 'GET');
}
