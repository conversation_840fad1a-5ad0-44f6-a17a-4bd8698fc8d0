package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:46
 * @description:
 */
@Data
@TableName(value = "pms_project_role_user")
@ApiModel(value = "ProjectRoleUser对象", description = "项目角色用户")
public class ProjectRoleUser extends ObjectEntity {
    /**
     * 项目角色Id
     */
    @ApiModelProperty(value = "项目角色Id")
    @TableField(value = "project_role_id")
    private String projectRoleId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    @TableField(value = "user_id")
    private String userId;

    @ApiModelProperty(value = "用户Id")
    @TableField(value = "code")
    private String code;

    @ApiModelProperty(value = "用户Id")
    @TableField(value = "email")
    private String email;

    @ApiModelProperty(value = "电话")
    @TableField(value = "mobile")
    private String mobile;


    @ApiModelProperty(value = "是否默认添加，1 是 2 否")
    @TableField(value = "default_flag")
    private Integer defaultFlag;
    @ApiModelProperty(value = "部门id")
    @TableField(exist = false)
    private String deptId;
    @ApiModelProperty(value = "部门名称")
    @TableField(exist = false)
    private String deptName;
    @ApiModelProperty(value = "部门编号")
    @TableField(exist = false)
    private String deptCode;

    @ApiModelProperty(value = "部门ID")
    @TableField(exist = false)
    private String parentId;

    @ApiModelProperty(value = "部门类型")
    @TableField(exist = false)
    private String type;

//    @ApiModelProperty(value = "部门编号")
//    @TableField(exist = false)
//    private String name;
}
