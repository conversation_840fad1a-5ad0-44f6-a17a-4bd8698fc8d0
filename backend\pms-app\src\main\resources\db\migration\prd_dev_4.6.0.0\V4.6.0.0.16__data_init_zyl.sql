-- 类绑定状态策略
insert into pmi_data_policy (`id`,`class_name`,`name`,`label`,`remark`,`description`,`creator_id`,`modify_id`,`owner_id`,`create_time`,`modify_time`,`status`,`platform_id`,`unique_key`,`logic_status`)
values ('txf71863782613078380543','DataPolicy','资产转固状态策略',null,null,null,'user00000000000000000100000000000000','user00000000000000000100000000000000','user00000000000000000100000000000000','2024-01-15 18:13:55','2024-01-15 18:13:55',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1);

-- 状态策略值
insert into pmi_data_status (`id`,`class_name`,`parent_id`,`label`,`color`,`name`,`status_value`,`description`,`remark`,`creator_id`,`modify_id`,`modify_time`,`owner_id`,`create_time`,`status`,`sort`,`platform_id`,`unique_key`,`logic_status`,`is_initial_value`)
values ('qtks339bad02d5474d479db68ad034899678','DataStatus','txf71863782613078380543',null,'3','已生效',130,null,null,'user00000000000000000100000000000000','314j1000000000000000000','2024-12-03 14:05:50','user00000000000000000100000000000000','2024-01-09 17:23:40',1,2972,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,0);
insert into pmi_data_status (`id`,`class_name`,`parent_id`,`label`,`color`,`name`,`status_value`,`description`,`remark`,`creator_id`,`modify_id`,`modify_time`,`owner_id`,`create_time`,`status`,`sort`,`platform_id`,`unique_key`,`logic_status`,`is_initial_value`)
values ('qtks75ea995262704a75b7287adc31b5f456','DataStatus','txf71863782613078380543',null,'5','审批中',110,null,null,'user00000000000000000100000000000000','314j1000000000000000000','2024-12-03 14:05:51','user00000000000000000100000000000000','2024-01-09 17:23:18',1,2970,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,0);
insert into pmi_data_status (`id`,`class_name`,`parent_id`,`label`,`color`,`name`,`status_value`,`description`,`remark`,`creator_id`,`modify_id`,`modify_time`,`owner_id`,`create_time`,`status`,`sort`,`platform_id`,`unique_key`,`logic_status`,`is_initial_value`)
values ('qtksbae4c2688cc04bfe9087f83828cbb132','DataStatus','txf71863782613078380543',null,'1','编制中',101,null,null,'user00000000000000000100000000000000','314j1000000000000000000','2024-12-03 14:05:53','user00000000000000000100000000000000','2024-01-09 17:22:46',1,2968,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,1);


-- 编码映射
insert into sys_code_mapping_relation (`id`,`data_field`,`code_rules`,`data_type`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`,`share`,`build_in`)
values ('e96m1863825215806902272','number','9hi11863824534677098496','ProjectAssetApply','SysCodeMappingRelation','','314j1000000000000000000','314j1000000000000000000','2024-12-03 13:58:54','314j1000000000000000000','2024-12-03 13:58:54',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1,1,1);


-- 编码规则
insert into sys_code_rules (`id`,`code_number`,`department`,`code_name`,`classification_id`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`,`rev_key`,`next_rev_id`,`previous_rev_id`,`code`,`rev_id`,`initial_rev_id`,`rev_order`,`share`,`build_in`)
values ('9hi11863824534677098496','ProjectAssetApply',null,'资产转固申请编码','zmz81787723211795013632','SysCodeRules',null,'314j1000000000000000000','314j1000000000000000000','2024-12-03 13:56:12','314j1000000000000000000','2024-12-03 14:06:12',130,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1,'b3f3890ad99c432a846f3574d6a93564',null,null,null,'A',null,1,1,1);

-- 编码规则码段
insert into sys_code_segment (`id`,`code_segment_name`,`running_water`,`code_rules_id`,`reference_type`,`code_segment_type`,`must`,`default_value`,`code_segment_length`,`code_segment_order`,`smbs_code_segment`,`vacancy_compensation`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`)
values ('s3rd1796120845122519321','固定值','0','9hi11863824534677098496','','fixedValue','','XMZG','0',1,'','','SysCodeSegment','','314j1000000000000000000','314j1000000000000000000','2024-05-30 18:45:39','314j1000000000000000000','2024-05-30 18:45:39',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1);
insert into sys_code_segment (`id`,`code_segment_name`,`running_water`,`code_rules_id`,`reference_type`,`code_segment_type`,`must`,`default_value`,`code_segment_length`,`code_segment_order`,`smbs_code_segment`,`vacancy_compensation`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`)
values ('s3rd1796120918740942451','年','0','9hi11863824534677098496','','DATE_YYYY','','','0',2,'','','SysCodeSegment','','314j1000000000000000000','314j1000000000000000000','2024-05-30 18:45:56','314j1000000000000000000','2024-05-30 18:45:56',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1);
insert into sys_code_segment (`id`,`code_segment_name`,`running_water`,`code_rules_id`,`reference_type`,`code_segment_type`,`must`,`default_value`,`code_segment_length`,`code_segment_order`,`smbs_code_segment`,`vacancy_compensation`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`)
values ('s3rd1796120944338780678','月','0','9hi11863824534677098496','','DATE_M','','','0',3,'','','SysCodeSegment','','314j1000000000000000000','314j1000000000000000000','2024-05-30 18:46:02','314j1000000000000000000','2024-05-30 18:46:02',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1);
insert into sys_code_segment (`id`,`code_segment_name`,`running_water`,`code_rules_id`,`reference_type`,`code_segment_type`,`must`,`default_value`,`code_segment_length`,`code_segment_order`,`smbs_code_segment`,`vacancy_compensation`,`class_name`,`remark`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`status`,`platform_id`,`org_id`,`unique_key`,`logic_status`)
values ('s3rd1796121097623814788','流水号','0','9hi11863824534677098496','f9g6089cb3c19a6d4b1da0d67bbd398efdd1','piPer','','','5',4,'','','SysCodeSegment','','314j1000000000000000000','314j1000000000000000000','2024-05-30 18:46:39','314j1000000000000000000','2024-12-03 14:04:41',1,'ykovb40e9fb1061b46fb96c4d0d3333dcc13','rxlm5e5bd8798c08434c99e927ea98e5d296',null,1);


-- 页面权限配置
-- 分页的配置
insert into pmi_container (`id`,`name`,`page_id`,`parent_id`,`code`,`path`,`label`,`sort`,`role_permissions`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_id`)
values ('exxr1866756251100553216','资产转固','xaxe72e43902a1b64923a12a24658318ac81','exxr95fb025fe30b4a59a94950345be64b38','PMS_XMXQ_container_08_03',null,'zi chan zhuan gu',null,null,'Container',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:05:48','314j1000000000000000000','2024-12-11 16:05:48',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);


insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866756547105169408','创建申请单','exxr1866756251100553216','PMS_XMXQ_container_08_03_button_01','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:06:58','314j1000000000000000000','2024-12-11 16:06:58',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866756744375869440','删除','exxr1866756251100553216','PMS_XMXQ_container_08_03_button_02','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:07:45','314j1000000000000000000','2024-12-11 16:07:45',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866757090254954496','编辑','exxr1866756251100553216','PMS_XMXQ_container_08_03_button_03','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:09:08','314j1000000000000000000','2024-12-11 16:09:08',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866757226095878144','表格行删除','exxr1866756251100553216','PMS_XMXQ_container_08_03_button_04','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:09:40','314j1000000000000000000','2024-12-11 16:09:40',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866757309201817600','提交申请','exxr1866756251100553216','PMS_XMXQ_container_08_03_button_05','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:10:00','314j1000000000000000000','2024-12-11 16:10:00',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);

-- 详情的配置
insert into pmi_page (`id`,`name`,`component`,`component_name`,`path`,`code`,`page_type`,`config_id`,`micro_web_name`,`sort`,`is_system`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`menu_id`,`default_page`)
values ('f2tr1863876548371025920','资产转固详情','\/pms\/projectLaborer\/projectLab\/projectList\/menuComponents\/AssetConsolidation\/AssetsFixedDetail','AssetsFixedDetail','\/assetsFixedDetail\/:id','PMS_ZCZGXQ_page_AssetsFixedDetail001','0',null,'pms',null,null,'Page',1,'314j1000000000000000000','314j1000000000000000000','2024-12-03 17:22:53','314j1000000000000000000','2024-12-03 17:23:06',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,'menu1784523943286304768',0);

insert into pmi_container (`id`,`name`,`page_id`,`parent_id`,`code`,`path`,`label`,`sort`,`role_permissions`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_id`)
values ('exxr1866762271130558464','资产转固详情','f2tr1863876548371025920',null,'PMS_ZCZGXQ_container_01',null,'zi chan zhuan gu xiang qing',null,null,'Container',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:29:43','314j1000000000000000000','2024-12-11 16:29:43',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_container (`id`,`name`,`page_id`,`parent_id`,`code`,`path`,`label`,`sort`,`role_permissions`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_id`)
values ('exxr1866762830633934848','关联 WBS 数据','f2tr1863876548371025920','exxr1866762271130558464','PMS_ZCZGXQ_container_01_01',null,'guan lian   w b s   shu ju',null,null,'Container',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:31:56','314j1000000000000000000','2024-12-11 16:32:28',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_container (`id`,`name`,`page_id`,`parent_id`,`code`,`path`,`label`,`sort`,`role_permissions`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`class_id`)
values ('exxr1866763572555980800','关联资产','f2tr1863876548371025920','exxr1866762271130558464','PMS_ZCZGXQ_container_01_02',null,'guan lian zi chan',null,null,'Container',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:34:53','314j1000000000000000000','2024-12-11 16:34:53',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);

insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866762384716505088','编辑','exxr1866762271130558464','PMS_ZCZGXQ_container_01_button_01','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:30:10','314j1000000000000000000','2024-12-11 16:30:10',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866762490098393088','启动流程','exxr1866762271130558464','PMS_ZCZGXQ_container_01_button_02','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:30:35','314j1000000000000000000','2024-12-11 16:30:35',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763154425815040','添加','exxr1866762830633934848','PMS_ZCZGXQ_container_01_01_buton_01','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:33:13','314j1000000000000000000','2024-12-11 16:33:13',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763233371004928','移除','exxr1866762830633934848','PMS_ZCZGXQ_container_01_01_buton_02','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:33:32','314j1000000000000000000','2024-12-11 16:33:32',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763436362735616','表格行移除','exxr1866762271130558464','PMS_ZCZGXQ_container_01_01_buton_03','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:34:21','314j1000000000000000000','2024-12-11 16:34:21',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763724742107136','添加','exxr1866763572555980800','PMS_ZCZGXQ_container_01_02_button_01','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:35:29','314j1000000000000000000','2024-12-11 16:35:29',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763808556883968','新增资产','exxr1866763572555980800','PMS_ZCZGXQ_container_01_02_button_02','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:35:49','314j1000000000000000000','2024-12-11 16:35:49',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866763902924529664','移除','exxr1866763572555980800','PMS_ZCZGXQ_container_01_02_button_03','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:36:12','314j1000000000000000000','2024-12-11 16:36:12',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866764250615554048','表格行移除','exxr1866763572555980800','PMS_ZCZGXQ_container_01_02_button_04','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:37:35','314j1000000000000000000','2024-12-11 16:37:35',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
insert into pmi_button (`id`,`name`,`container_id`,`code`,`label`,`icon`,`sort`,`path`,`parent_id`,`class_name`,`status`,`owner_id`,`creator_id`,`create_time`,`modify_id`,`modify_time`,`remark`,`platform_id`,`unique_key`,`logic_status`,`row_button`)
values ('bqeo1866764306211053568','编辑','exxr1866763572555980800','PMS_ZCZGXQ_container_01_02_button_05','view','fa-th-large',null,null,null,'Button',1,'314j1000000000000000000','314j1000000000000000000','2024-12-11 16:37:48','314j1000000000000000000','2024-12-11 16:37:48',null,'ykovb40e9fb1061b46fb96c4d0d3333dcc13',null,1,null);
