package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractSupplierRecord Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_supplier_record")
@ApiModel(value = "ContractSupplierRecordEntity对象", description = "合同供应商记录表")
@Data

public class ContractSupplierRecord extends ObjectEntity implements Serializable {

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @TableField(value = "supplier_id")
    private String supplierId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 供应商来源
     */
    @ApiModelProperty(value = "供应商来源")
    @TableField(value = "supplier_from")
    private String supplierFrom;

    /**
     * 是否询价供应商
     */
    @ApiModelProperty(value = "是否询价供应商")
    @TableField(value = "is_inquiry_supplier")
    private Boolean isInquirySupplier;

    /**
     * 是否中标供应商
     */
    @ApiModelProperty(value = "是否中标供应商")
    @TableField(value = "is_winner_supplier")
    private Boolean isWinnerSupplier;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

}
