package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * DeliverGoals DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@ApiModel(value = "DeliverGoalsDTO对象", description = "交付目标（ied）")
@Data
public class DeliverGoalsDTO extends ObjectDTO implements Serializable{

    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 计划提交时间
     */
    @ApiModelProperty(value = "计划提交时间")
    private Date planSubmitTime;

    /**
     * 编写人
     */
    @ApiModelProperty(value = "编写人")
    private String writer;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 文件状态
     */
    @ApiModelProperty(value = "文件状态")
    private String fileStatus;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String revId;

}
