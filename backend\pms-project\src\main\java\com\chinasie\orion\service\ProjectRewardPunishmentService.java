package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.ProjectRewardPunishmentDTO;
import com.chinasie.orion.domain.entity.ProjectRewardPunishment;
import com.chinasie.orion.domain.vo.ProjectRewardPunishmentVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectRewardPunishment 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
public interface ProjectRewardPunishmentService  extends  OrionBaseService<ProjectRewardPunishment>  {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectRewardPunishmentVO detail(String id, String pageCode)throws Exception;

    /**
     * 新增编辑删除主要事迹
     * @param projectRewardPunishmentDTOList
     * @param projectId
     * @return
     * @throws Exception
     */
    Boolean saveOrRemove(List<ProjectRewardPunishmentDTO> projectRewardPunishmentDTOList, String projectId) throws Exception;


    /**
     * 获取主要事迹列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectRewardPunishmentVO> getList(String projectId) throws Exception;
}
