<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem, Layout, OrionTable, isPower, BasicButton, downloadByData, Layout3,
} from 'lyra-component-vue3';
import {
  computed, ref, Ref, onMounted, ComputedRef, h, watchEffect,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();
const tableRef: Ref = ref();
const pageSearchConditions = ref(null);
const powerData = ref();

// 供应商数据页面展示
const suppliers = ref([]);

// 供应商在库数量
const supplierCount = ref<any>({});

// 年度引入数量
const yearNum = ref(0);

// 表格数据
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: true,
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierNumber',
      width: 110,
    },
    {
      title: '供应商名称',
      dataIndex: 'name',
      width: 220,
    },
    {
      title: '二级公司名称',
      dataIndex: 'secondaryCompanyName',
      width: 200,
    },
    {
      title: '供应商类别',
      dataIndex: 'supplierCategory',
      width: 100,
    },
    {
      title: '供应商级别',
      dataIndex: 'supplierLevel',
      width: 100,
    },
    {
      title: '板块名称',
      dataIndex: 'sectorName',
      width: 100,
    },
    {
      title: '资质有效期',
      dataIndex: 'qualValidity',
      width: 150,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '供应商缴费有效截止日期',
      dataIndex: 'paymentEffectiveDeadline',
      width: 180,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '采购品类',
      dataIndex: 'procurementCat',
      width: 100,
    },
    {
      title: '供应商简称',
      dataIndex: 'simName',
      width: 150,
    },
    {
      title: '联系人姓名',
      dataIndex: 'contractName',
      width: 150,
    },
    {
      title: '联系人手机',
      dataIndex: 'contractTel',
      width: 140,
    },
    {
      title: '联系人邮箱',
      dataIndex: 'contractEmail',
      width: 160,
    },
  ],
  smallSearchField: [
    'name',
    'supplierNumber',
    'supplierLevel',
    'sectorName',
    'procurementCat',
    'simName',
    'contractName',
  ],
  filterConfig: {
    fields: [
      {
        field: 'name',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierNumber',
        fieldName: '供应商编码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierLevel',
        fieldName: '供应商级别',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'sectorName',
        fieldName: '板块名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'procurementCat',
        fieldName: '采购品类',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'simName',
        fieldName: '供应商简称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractName',
        fieldName: '联系人姓名',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  api: (params:Record<string, any>) => {
    const defConditions = [
      // [
      //   {
      //     field: 'secondaryCompanyCode',
      //     fieldType: 'String',
      //     values: ['A01-005'],
      //     queryType: 'eq',
      //   },
      //   {
      //     field: 'supplierCategory',
      //     fieldType: 'String',
      //     values: ['合格供应商'],
      //     queryType: 'eq',
      //   },
      // ],
    ];
    const searchConditions = (params.searchConditions || []);
    pageSearchConditions.value = params.searchConditions ? [...searchConditions] : null;
    return new Api('/spm/supplierInfo').fetch({
      power: {
        pageCode: 'PMS00002',
        containerCode: 'PMS_SZHG_container_02',
      },
      searchConditions: pageSearchConditions.value,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    }, 'park/page', 'POST');
  },
};

// 表格右上角按钮定义
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '导出全部',
    icon: 'sie-icon-daochu',
    code: 'PMS_SZHG_container_01_button_01',
  },
].filter((item) => isPower('PMS_SZHG_container_01_button_01', powerData.value)));

// 操作区域按钮定义
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    isShow: (record) => isPower('PMS_SZHG_container_02_button_01', record.rdAuthList),
    event: 'view',
  },
];

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 批量导出按钮事件（接口需加）
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'supplierLevel',
              fieldType: 'String',
              values: ['公司级'],
              queryType: 'eq',
            },
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      } else {
        exportConditions = pageSearchConditions.value;
      }
      downloadByData('/spm/supplierInfo/park/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 表格操作区域按钮点击事件
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      router.push({
        name: 'QualifiedSupplierDetails',
        params: {
          id: record.id,
        },
      });
      break;
  }
}

const goToStatistics = async () => {
  try {
    const result = await new Api('/spm/supplierInfo').fetch({
      searchConditions: pageSearchConditions.value,
    }, 'getNum', 'POST');
    supplierCount.value = result;
  } catch (e) {}
};
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};

// 页面加载时获取供应商数量
watchEffect(async () => {
  await goToStatistics();
});

</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS00002',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-daochu"
            @click="exportTableData(selectKeys)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
        <div class="supplier-count-box">
          <div class="supplier-count-box-top">
            供应商在库数量：{{ supplierCount.allNum }}个
          </div>
          <div class="supplier-count-box-top">
            年度引入数量：{{ supplierCount.yearNum }}个
          </div>
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.supplier-count-box {
  display: flex !important;
  align-items: center;
  background-color: #fff;

  .supplier-count-box-top{
    margin-left: 15px;
    margin-right: 15px;
  }
}

.toolbar-container{
  display: flex;
  align-items: center !important;
}

:deep(.flex-f1 .flex){
  align-items: center;
}

.supplier-count-box-special{
  margin-left: 50px;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
