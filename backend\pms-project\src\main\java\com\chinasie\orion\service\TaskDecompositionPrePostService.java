package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.TaskDecompositionPrePostDTO;
import com.chinasie.orion.domain.dto.TaskPreTaskDTO;
import com.chinasie.orion.domain.entity.TaskDecompositionPrePost;
import com.chinasie.orion.domain.vo.TaskDecompositionPrePostVO;
import com.chinasie.orion.domain.vo.TaskDecompositionVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * TaskDecompositionPrePost 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
public interface TaskDecompositionPrePostService  extends  OrionBaseService<TaskDecompositionPrePost>  {


        /**
         *  详情
         *
         * * @param id
         */
        TaskDecompositionVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param taskDecompositionPrePostDTO
         */
        String create(TaskDecompositionPrePostDTO taskDecompositionPrePostDTO)throws Exception;


        /**
         * 添加前后置关系(批量)
         * @param taskPreTaskDTO
         * @return
         */
        List<String> createBatch(TaskPreTaskDTO taskPreTaskDTO) throws Exception;

        /**
         *  编辑
         *
         * * @param taskDecompositionPrePostDTO
         */
        Boolean edit(TaskDecompositionPrePostDTO taskDecompositionPrePostDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;

        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<TaskDecompositionPrePostVO> pages( Page<TaskDecompositionPrePostDTO> pageRequest)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<TaskDecompositionPrePostVO> vos)throws Exception;
}
