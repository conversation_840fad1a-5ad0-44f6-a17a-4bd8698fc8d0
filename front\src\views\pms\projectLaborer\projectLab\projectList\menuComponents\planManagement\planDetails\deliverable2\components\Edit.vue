<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
    @close="handleClose"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :rules="rules"
      :model="father.form"
    >
      <a-form-item
        label="名称"
        name="name"
      >
        <a-input
          v-model:value="father.form.name"
          placeholder="请输入名称"
          allow-clear
          :maxlength="64"
          size="large"
        />
      </a-form-item>
      <a-form-item
        label="负责人"
        name="principalId"
      >
        <a-select
          v-model:value="father.form.principalId"
          placeholder="请选择负责人"
          allow-clear
          show-search
          :filter-option="filterOption"
          :options="namesList"
          size="large"
          @change="handlePrincipal(namesList, father.form.principalId)"
        />
      </a-form-item>
      <a-form-item label="计划交付时间">
        <a-date-picker
          v-model:value="father.form.predictDeliverTime"
          show-time
          type="date"
          placeholder="请选择开始日期"
          allow-clear
          value-format="YYYY-MM-DD HH:mm:ss"
          size="large"
          class="w-full"
        />
      </a-form-item>
      <a-form-item label="描述">
        <a-textarea
          v-model:value="father.form.remark"
          placeholder="请输入描述"
          allow-clear
          :maxlength="255"
          size="large"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </a-form-item>
    </a-form>
    <a-checkbox
      v-if="father.type === 'addNew'"
      v-model:checked="isGo"
      size="large"
    >
      继续创建下一个
    </a-checkbox>
    <div class="drawer-footer">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-button
            size="large"
            block
            @click="handleClose"
          >
            取消
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            size="large"
            type="primary"
            block
            :loading="loading"
            @click="handleSave(father.type, isGo)"
          >
            确认
          </a-button>
        </a-col>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import {
  onBeforeMount, reactive, toRefs, ref,
} from 'vue';
import {
  Row,
  Col,
  Drawer,
  Form,
  Checkbox,
  Select,
  DatePicker,
  Input,
  Button,
  message,
} from 'ant-design-vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    AInput: Input,
    AButton: Button,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
    ACheckbox: Checkbox,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: props.data,
      isGo: false,
      loading: false,
      formRef: ref(),
      namesList: [], // 负责人
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
    });

    function getProjectRole() {
      const url = `project-role-user/getListByName/${parseURL().projectId}?name=`;
      new Api('/pms').fetch('', url, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }

    function filterOption(inputValue, treeNode) {
      return treeNode.props.label.includes(inputValue);
    }

    function handleSave(type, isGo) {
      state.formRef
        .validate()
        .then(() => {
          state.loading = true;
          const love = {
            id: type === 'addNew' ? '' : state.father.form?.id,
            name: state.father.form?.name,
            className: 'Plan', // 列表中获取也可根据实际情况手动输入
            moduleName: '项目管理-计划管理-项目计划-交付物', // 模块名称
            type: type === 'addNew' ? 'SAVE' : 'UPDATE', // 操作类型
            remark: `${type === 'addNew' ? '新增' : '编辑'}了【${state.father.form?.name}】`,
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'deliverable', type === 'addNew' ? 'POST' : 'PUT')
            .then(() => {
              state.loading = false;
              message.success('操作成功');
              if (type === 'addNew' && isGo) {
                state.father.form = {
                  planId: parseURL().id,
                  projectId: parseURL().projectId,
                  name: undefined,
                  principalId: undefined,
                  principalName: undefined,
                  predictDeliverTime: undefined,
                  remark: undefined,
                };
              } else {
                emit('submit', true);
              }
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    function handleClose() {
      emit('submit', false);
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    onBeforeMount(() => {
      getProjectRole(); // 负责人
    });
    return {
      ...toRefs(state),
      handleSave,
      handleClose,
      handlePrincipal,
      filterOption,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
</style>
