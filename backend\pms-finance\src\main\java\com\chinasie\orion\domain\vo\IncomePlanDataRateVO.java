package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.entity.AdvancePaymentInformation;
import com.chinasie.orion.domain.entity.BillingAccountInformation;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.entity.IncomeProvisionInformation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * IncomePlanDataRate VO对象
 *
 * <AUTHOR>
 * @since 2024-10-18 16:03:39
 */
@ApiModel(value = "IncomePlanDataRateVO对象", description = "多项目税率维护保存")
@Data
public class IncomePlanDataRateVO {

    /**
     * 收入计划填报数据
     */
    @ApiModelProperty(value = "收入计划填报数据")
    private IncomePlanData incomePlanDataRate;

    /**
     * 收入计提信息
     */
    @ApiModelProperty(value = "收入计提信息")
    private List<IncomeProvisionInformation> incomeProvisionInformation;

    /**
     * 收入计提信息
     */
    @ApiModelProperty(value = "收入计提信息")
    private List<BillingAccountInformationVO> billingAccountInformation;

    /**
     * 预收款信息
     */
    @ApiModelProperty(value = "预收款信息")
    private List<AdvancePaymentInformation> advancePaymentInformation;
}
