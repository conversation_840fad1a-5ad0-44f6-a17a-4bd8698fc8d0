package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/24/15:46
 * @description:
 */
public enum TrainEquivalentStatus {
    no_push(101,"编制中")
    ,executiong(110,"流程中")
    ,FINISH(130,"已完成")
    ;


    private Integer status;

    private String desc;

    TrainEquivalentStatus(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
