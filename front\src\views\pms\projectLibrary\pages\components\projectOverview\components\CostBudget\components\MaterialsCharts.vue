<template>
  <div class="materials-charts">
    <div class="materials-charts-left">
      <div class="left-label">
        税费
      </div>
      <div class="left-value">
        <span
          style=" font-size: 14px;"
          class="errorcolor"
        >金额</span>
        <div class="left-money">
          <!--        {{ formatMoney(summaryData.materialFee) }}-->
          5448
        </div>
      </div>
    </div>
    <div
      ref="myChart"
      class="materials-charts-right"
    />
  </div>
</template>
<script setup lang="ts">

import {
  markRaw, onMounted, onUnmounted, ref, Ref, unref, watch,
} from 'vue';
import * as echarts from 'echarts';
import { formatMoney } from '/@/views/pms/projectInitiation/index';
const props = withDefaults(defineProps<{
    summaryData:{
        materialFee:number|string,
        materialFeeRate:number|string
    }
}>(), {
  summaryData: () => ({
    materialFee: 0,
    materialFeeRate: 50,
  }),
});

const myChart: Ref = ref();
const chartValue:Ref<Record<any, any>[]> = ref([
  {
    value: 0,
    name: '材料费',
    color: '#0099FF',
  },
  {
    value: 0,
    name: '总额',
    color: '#cccccc',
  },
]);
const chartInstance = ref();
onMounted(() => {
  chartValue.value[0].value = props.summaryData.materialFeeRate || 0;
  chartValue.value[1].value = 100 - chartValue.value[0].value;
  chartInstance.value = echarts.init(myChart.value);
  let chartOption = initOptions(chartValue.value);
  chartInstance.value.setOption(chartOption);
  window.addEventListener('resize', resizeChart);
});
onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
});
watch(() => props.summaryData, (val) => {
  chartValue.value[0].value = val.materialFeeRate || 0;
  chartValue.value[1].value = 100 - chartValue.value[0].value;
  let chartOption = initOptions(chartValue.value);
  chartInstance.value.setOption(chartOption);
});
function resizeChart() {
  chartInstance.value.resize();
}
function initOptions(seriesData) {
  return {
    grid: {
      left: 'center',
    },
    color: seriesData.map((item) => item.color),
    series: [
      {
        name: '物资种类',
        type: 'pie',
        radius: ['45%', '70%'],
        label: {
          show: false,
        },
        data: seriesData,
      },
    ],
  };
}
</script>
<style lang="less" scoped>
.materials-charts{
  width: 100%;
  display: flex;
  height: 100%;
  justify-content: space-between;
  font-family: '微软雅黑';
}

.materials-charts-left{
  .left-label{
  }
  .left-value{
    display: flex;
    align-items: center;
    margin-top: 40px;
    .left-money{
      font-size: 30px !important;
      margin-left: 5px;
    }
  }
}
.materials-charts-right{
  height: 100px;
  width: 120px;
}

.errorcolor{
  color: ~`getPrefixVar('error-color')`;
}
</style>