<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.eclipse.jetty</groupId>
  <artifactId>jetty-bom</artifactId>
  <version>9.4.30.v20200611</version>
  <packaging>pom</packaging>
  <name>Jetty :: Bom</name>
  <description>Jetty BOM artifact</description>
  <url>http://www.eclipse.org/jetty</url>
  <inceptionYear>1995</inceptionYear>
  <organization>
    <name>Webtide</name>
    <url>https://webtide.com</url>
  </organization>
  <licenses>
    <license>
      <name>Apache Software License - Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
    <license>
      <name>Eclipse Public License - Version 1.0</name>
      <url>http://www.eclipse.org/org/documents/epl-v10.php</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>gregw</id>
      <name>Greg Wilkins</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>janb</id>
      <name>Jan Bartel</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>jesse</id>
      <name>Jesse McConnell</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-7</timezone>
    </developer>
    <developer>
      <id>sbordet</id>
      <name>Simone Bordet</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>1</timezone>
    </developer>
    <developer>
      <id>djencks</id>
      <name>David Jencks</name>
      <email><EMAIL></email>
      <organization>IBM</organization>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>Australia/Brisbane</timezone>
    </developer>
  </developers>
  <mailingLists>
    <mailingList>
      <name>Jetty Developer Mailing List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-dev</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-dev</unsubscribe>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-dev/maillist.html</archive>
    </mailingList>
    <mailingList>
      <name>Jetty Commit Mailing List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-commit</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-commit</unsubscribe>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-commit/maillist.html</archive>
    </mailingList>
    <mailingList>
      <name>Jetty Users Mailing List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-users</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-users</unsubscribe>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-users/maillist.html</archive>
    </mailingList>
    <mailingList>
      <name>Jetty Announce Mailing List</name>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-announce</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-announce</unsubscribe>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-announce/maillist.html</archive>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://github.com/eclipse/jetty.project.git/jetty-bom</connection>
    <developerConnection>scm:git:**************:eclipse/jetty.project.git/jetty-bom</developerConnection>
    <url>https://github.com/eclipse/jetty.project/jetty-bom</url>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>https://github.com/eclipse/jetty.project/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>oss.sonatype.org</id>
      <name>Jetty Staging Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>oss.sonatype.org</id>
      <name>Jetty Snapshot Repository</name>
      <url>https://oss.sonatype.org/content/repositories/jetty-snapshots/</url>
    </snapshotRepository>
    <site>
      <id>jetty.eclipse.website</id>
      <url>scp://build.eclipse.org:/home/<USER>/httpd/download.eclipse.org/jetty/9.4.30.v20200611/jetty-bom/</url>
    </site>
  </distributionManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jstl</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-annotations</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ant</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-continuation</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.30.v20200611</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.30.v20200611</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.gcloud</groupId>
        <artifactId>jetty-gcloud-session-manager</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.30.v20200611</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.30.v20200611</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-common</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-hpack</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-http-client-transport</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-spi</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-common</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-remote-query</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-embedded-query</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-hazelcast</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaas</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaspi</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jndi</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.memcached</groupId>
        <artifactId>jetty-memcached-sessions</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-nosql</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-jsp</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-warurl</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-httpservice</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-plus</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-proxy</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-quickstart</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-openid</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlets</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-spring</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-unixsocket</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-client-impl</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-server-impl</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-api</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-client</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-common</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-server</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-servlet</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>9.4.30.v20200611</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
