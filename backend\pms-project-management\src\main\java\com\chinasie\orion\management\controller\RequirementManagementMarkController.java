package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.RequirementManagementMarkDTO;
import com.chinasie.orion.management.domain.vo.RequirementManagementMarkVO;
import com.chinasie.orion.management.service.RequirementManagementMarkService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * RequirementManagementMark 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05 19:52:08
 */
@RestController
@RequestMapping("/requirement/management/mark")
@Api(tags = "需求管理标注")
@RequiredArgsConstructor
public class RequirementManagementMarkController {

    private final RequirementManagementMarkService requirementManagementMarkService;

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求管理标注", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<?> detail(
            @PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) {
        RequirementManagementMarkVO rsp = requirementManagementMarkService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#requirementManagementMarkDTO.name}}】",
            type = "需求管理标注", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<?> create(@RequestBody RequirementManagementMarkDTO requirementManagementMarkDTO) {
        String rsp = requirementManagementMarkService.save(requirementManagementMarkDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#requirementManagementMarkDTO.name}}】",
            type = "需求管理标注", subType = "编辑", bizNo = "{{#requirementManagementMarkDTO.id}}")
    public ResponseDTO<?> edit(@RequestBody RequirementManagementMarkDTO requirementManagementMarkDTO) {
        requirementManagementMarkService.save(requirementManagementMarkDTO);
        return new ResponseDTO<>("");
    }


    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "需求管理标注",
            subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<?> remove(@PathVariable("id") String id) {
        Boolean rsp = requirementManagementMarkService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "需求管理标注",
            subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<?> remove(@RequestBody List<String> ids) {
        Boolean rsp = requirementManagementMarkService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "需求管理标注",
            subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<?> pages(@RequestBody Page<RequirementManagementMarkDTO> pageRequest) {
        Page<RequirementManagementMarkVO> rsp = requirementManagementMarkService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
