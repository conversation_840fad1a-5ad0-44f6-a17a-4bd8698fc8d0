package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalMilestoneTemplate DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:59
 */
@ApiModel(value = "ProjectApprovalMilestoneTemplateDTO对象", description = "项目立项里程碑模板")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalMilestoneTemplateDTO extends  ObjectDTO   implements Serializable{

/**
 * 模板名称
 */
@ApiModelProperty(value = "模板名称")
@ExcelProperty(value = "模板名称 ", index = 0)
private String name;




}
