<script setup lang="ts">
import {
  computed, inject, nextTick, ref, Ref,
} from 'vue';
import {
  Icon, isPower, BasicScrollbar, Empty,
} from 'lyra-component-vue3';
import {
  Divider, Popover, RadioButton, RadioGroup, RangePicker, Space,
} from 'ant-design-vue';
import { useSafetyOptions } from '/@/views/pms/majorRepairs/hooks';
import dayjs, { Dayjs } from 'dayjs';

const props = defineProps<{
  type: string
}>();

const emits = defineEmits<{
  (e: 'update:type', value: string): void
}>();

type RangeValue = [Dayjs, Dayjs]

const powerData: Ref = inject('powerData');

const year = ref<RangeValue>([dayjs(), dayjs().add(1, 'year')]);
const dates = ref<RangeValue>();
const hackValue = ref<RangeValue>();

const tooLate = computed(() => (current) => dates.value[0] && current.diff(dates.value[0], 'year') > 0);

const tooEarly = computed(() => (current) => dates.value[1] && dates.value[1].diff(current, 'year') > 0);
const disabledDate = (current: Dayjs) => {
  if (!dates.value || (dates.value as any).length === 0) {
    return false;
  }

  return tooLate.value(current) || tooEarly.value(current);
};
const onOpenChange = async (open: boolean) => {
  if (open) {
    await nextTick();
    dates.value = [] as any;
    hackValue.value = [] as any;
  } else {
    hackValue.value = undefined;
  }
};

const onChange = (val: RangeValue) => {
  year.value = val;
  updateTable();
};
const onCalendarChange = (val: RangeValue) => {
  dates.value = val;
};

const {
  loading, safetyOptions, fetching, majorRepairsData, updateTable,
} = useSafetyOptions({ year });

function changeType(e) {
  emits('update:type', e.target.value);
}

function getBaseColor(baseName: string) {
  if (baseName.includes('大亚湾') || baseName.includes('岭奥')) {
    return '#99CCCC';
  }
  if (baseName.includes('红沿河')) {
    return '#9999FF';
  }
  if (baseName.includes('宁德')) {
    return '#CCCCCC';
  }
  if (baseName.includes('阳江')) {
    return '#99CC99';
  }
  if (baseName.includes('防城港')) {
    return '#99FF99';
  }
  if (baseName.includes('台山')) {
    return '#CC9999';
  }
  if (baseName.includes('陆丰')) {
    return '#9999CC';
  }
  if (baseName.includes('惠州')) {
    return '#CCCC99';
  }
  return '#CCCCCC';
}

const legends = [
  {
    label: '已完成的大修',
    key: 'finish',
    color: '#85BD19',
  },
  {
    label: '正在进行的大修',
    key: 'progress',
    color: '#00A1E5',
  },
  {
    label: '正在准备的大修',
    key: 'wait',
    color: '#FFD600',
  },
];

// 获取当前图例颜色
function getLegendColor(label: string) {
  return legends.find((item) => item.label === label)?.color;
}

// 获取当前大修图例左定位
function getProgressLeft(beginTime: string) {
  return getWidthByDate(beginTime);
}

const scrollRef = ref();

// 根据日期获取宽度
function getWidthByDate(date: Dayjs | string): number {
  const scrollWidth: number = scrollRef.value?.wrapRef?.offsetWidth || 0;
  const month = dayjs(date).month();
  const step = (scrollWidth - 150) / 24;
  const yearStep = (dayjs(date).year() - dayjs(year.value[0]).year()) * 12 * step;
  const monthDay = dayjs(date).daysInMonth();
  const diffDay = dayjs(date).diff(dayjs(date).startOf('month'), 'day') + 1;
  const ratio = diffDay / monthDay;
  const width = step * month + 150 + ratio * step + yearStep;
  return Math.ceil(width);
}

// 获取当前大修图例宽度
function getProgressWidth(beginTime: string, endTime: string) {
  return getWidthByDate(endTime) - getWidthByDate(beginTime);
}
</script>

<template>
  <Space
    v-loading="loading"
    class="container"
    style="width: 100%"
    direction="vertical"
    :size="20"
  >
    <Space>
      <div
        v-for="(location,index) in safetyOptions"
        :key="index"
        :style="{backgroundColor:getBaseColor(location.label)}"
        class="location"
      >
        {{ location.label }}
      </div>
    </Space>
    <Divider />
    <div
      v-loading="fetching"
      class="flex flex-ac"
    >
      <h2 class="mb0">
        大修日历
      </h2>

      <div class="flex ma">
        <div
          v-for="(legend,index) in legends"
          :key="index"
          class="flex flex-pac mr20"
        >
          <span
            class="color-block"
            :style="{backgroundColor:legend.color}"
          />
          <span class="txt">{{ legend.label }}</span>
        </div>
      </div>

      <RadioGroup
        v-if="isPower('PMS_DXGL_container_01',powerData) && isPower('PMS_DXGL_container_02',powerData)"
        :value="type"
        class="radio-group"
        button-style="solid"
        @change="changeType"
      >
        <RadioButton value="list">
          <Icon icon="orion-icon-align-left" />
        </RadioButton>
        <RadioButton value="chart">
          <Icon icon="orion-icon-appstore" />
        </RadioButton>
      </RadioGroup>
      <RangePicker
        :allow-clear="false"
        :value="hackValue||year"
        picker="year"
        :disabled-date="disabledDate"
        @change="onChange"
        @openChange="onOpenChange"
        @calendarChange="onCalendarChange"
      />
    </div>

    <div v-if="majorRepairsData.length">
      <div class="table-grid">
        <div />
        <div
          v-for="(item,index) in 24"
          :key="index"
        >
          {{ item > 12 ? item - 12 : item }}月
        </div>
      </div>

      <BasicScrollbar
        ref="scrollRef"
        height="calc(100vh - 377px)"
      >
        <div
          v-for="(location,idx) in majorRepairsData"
          :key="idx"
          class="table-grid"
          :style="{gridTemplateRows:`repeat(${location?.overhaulDetails?.length}, 40px)`}"
        >
          <template
            v-for="(overhaul,index) in location?.overhaulDetails||[]"
            :key="index"
          >
            <div
              v-if="index===0"
              :style="{backgroundColor:getBaseColor(location.baseName),gridRowStart:`span ${location?.overhaulDetails?.length||1}`}"
            >
              {{ overhaul.baseName }}
            </div>
            <div
              v-for="item in 24"
              :key="item"
              :style="{backgroundColor:getBaseColor(location.baseName)}"
            />
            <Popover
              :title="overhaul?.haulStatus"
              placement="rightBottom"
            >
              <template #content>
                <div class="flex flex-ver">
                  <span>计划开始时间：{{
                    overhaul?.beginTime ? dayjs(overhaul?.beginTime).format('YYYY-MM-DD') : ''
                  }}</span>
                  <span>计划完成时间：{{
                    overhaul?.endTime ? dayjs(overhaul?.endTime).format('YYYY-MM-DD') : ''
                  }}</span>
                  <span>实际开始时间：{{
                    overhaul?.actualBeginTime ? dayjs(overhaul?.actualBeginTime).format('YYYY-MM-DD') : ''
                  }}</span>
                  <span>实际完成时间：{{
                    overhaul?.actualEndTime ? dayjs(overhaul?.actualEndTime).format('YYYY-MM-DD') : ''
                  }}</span>
                  <span>大修工期：{{ overhaul?.workDuration || '' }}</span>
                  <span>大修经理：{{ overhaul?.repairManagerName || '' }}</span>
                  <span>大修类别：{{ overhaul?.type || '' }}</span>
                  <span>大修里程碑：</span>
                  <span>当前时间：{{ dayjs().format('YYYY-MM-DD') }}</span>
                </div>
              </template>
              <div
                :title="overhaul?.repairRound||''"
                class="progress-block flex-te"
                :style="{
                  top:(index*41)+4+'px',
                  backgroundColor:getLegendColor(overhaul?.haulStatus),
                  left: getProgressLeft(overhaul?.sTime)+'px',
                  width:getProgressWidth(overhaul?.sTime, overhaul?.eTime)+'px'
                }"
              >
                {{ overhaul?.repairRound || '' }}
              </div>
            </Popover>
          </template>
        </div>
      </BasicScrollbar>
    </div>
    <div
      v-else
      class="flex flex-pac"
      style="height: calc(100vh - 337px)"
    >
      <Empty />
    </div>
  </Space>
</template>

<style scoped lang="less">
.container {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}

.location {
  width: 100px;
  text-align: center;
  line-height: 100px;
  font-weight: bold;
}

.radio-group {
  display: block;
  text-align: right;
  margin: 0 20px;
}

:deep(.ant-divider-horizontal) {
  margin: 0;
}

.color-block {
  display: block;
  width: 35px;
  height: 25px;
  border-radius: 6px;
  margin-right: 10px;
}

.table-grid {
  display: grid;
  grid-template-columns: 150px repeat(24, minmax(0, 1fr));
  grid-template-rows: repeat(1, 40px);
  gap: 1px;
  font-size: 16px;
  font-weight: bold;
  position: relative;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;

    &.progress-block {
      position: absolute;
      height: 32px;
      top: 0;
      border-radius: 6px;
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 2px;
      border: none;
    }
  }
}
</style>
