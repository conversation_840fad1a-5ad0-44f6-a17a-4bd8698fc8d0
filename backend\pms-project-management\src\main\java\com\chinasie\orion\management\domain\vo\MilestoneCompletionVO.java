package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@ApiModel(value = "MilestoneCompletionVO对象", description = "经营看板-里程碑达成情况")
@Data
public class MilestoneCompletionVO implements Serializable {

    /**
     * 已验收收入（万元）
     */
    @ApiModelProperty(value = "已验收收入")
    private BigDecimal acceptedRevenue;

    /**
     * 计划验收收入（万元）
     */
    @ApiModelProperty(value = "计划验收收入")
    private BigDecimal plannedRevenue;

    /**
     * 核能已验收收入（万元）
     */
    @ApiModelProperty(value = "核能已验收收入")
    private BigDecimal nuclearAcceptedRevenue;

    /**
     * 计划验收收入（万元）
     */
    @ApiModelProperty(value = "核能计划验收收入")
    private BigDecimal NuclearPlannedRevenue;

    /**
     * 非核能已验收收入（万元）
     */
    @ApiModelProperty(value = "非核能已验收收入")
    private BigDecimal otherAcceptedRevenue;

    /**
     * 非核能计划验收收入（万元）
     */
    @ApiModelProperty(value = "非核能计划验收收入")
    private BigDecimal otherPlannedRevenue;


    /**
     * 30内待验收里程碑数量
     */
    @ApiModelProperty(value = "30内待验收里程碑数量")
    private Integer waitingMilestone;

    /**
     * 验收异常报告
     */
    @ApiModelProperty(value = "验收异常报告")
    private Integer exceptionReportNum;
}

