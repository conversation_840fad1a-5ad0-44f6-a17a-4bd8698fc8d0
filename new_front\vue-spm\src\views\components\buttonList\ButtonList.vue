<template>
  <div class="button-list-box">
    <template
      v-for="(action, index) of allShowButtonList.buttonLeftGroup"
      :key="`${index}-${action?.text}`"
    >
      <Dropdown :getPopupContainer="getMircoAppContainer">
        <template
          v-if="action.children?.length"
          #overlay
        >
          <AMenu>
            <AMenuItem
              v-for="(current, indexs) of action.children"
              :key="`${indexs}-${current?.text}`"
            >
              <BasicButton
                class="button-style"
                type="link"
                v-bind="getBindButtonProps(current)"
                @click="handleMenuClick(current)"
              >
                {{ current?.text }}
              </BasicButton>
            </AMenuItem>
          </AMenu>
        </template>
        <BasicButton
          v-bind="getBindButtonProps(action)"
          @click="handleMenuClick(action)"
        >
          {{ action?.text }}
          <DownOutlined v-if="action.children?.length" />
        </BasicButton>
      </Dropdown>
    </template>
    <Dropdown v-if="allShowButtonList.dropDownButtonGroup?.length">
      <template #overlay>
        <AMenu>
          <AMenuItem
            v-for="(action, index) of allShowButtonList.dropDownButtonGroup"
            :key="`${index}-${action?.text}`"
          >
            <BasicButton
              class="button-style"
              type="link"
              v-bind="getBindButtonProps(action)"
              @click="handleMenuClick(action)"
            >
              {{ action?.text }}
            </BasicButton>
          </AMenuItem>
        </AMenu>
      </template>
      <BasicButton>
        更多
        <DownOutlined />
      </BasicButton>
    </Dropdown>
  </div>
</template>
<script setup lang="ts">
import { BasicButton, getMircoAppContainer } from 'lyra-component-vue3';
import { Dropdown, Menu, Modal } from 'ant-design-vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { computed, unref } from 'vue';
import { omit, cloneDeep, isFunction } from 'lodash-es';
import { openContentDrawer, openContentModal } from '/@/views/spm/utils/utils';

const AMenu = Menu;
const AMenuItem = Menu.Item;
const props = defineProps({
  actions: {
    type: Array,
    default: () => [],
  },
  showButtonMaxNumber: {
    type: Number,
    default: 1000,
  },
});

function splitActions(actions, maxNumber) {
  const buttonLeftGroup = actions.slice(0, maxNumber);
  const dropDownButtonGroup = actions.slice(maxNumber);
  return [buttonLeftGroup, dropDownButtonGroup];
}

const allShowButtonList = computed(() => {
  const list = cloneDeep(props.actions);
  const arr: any = [];

  function isShow(action: any) {
    if (isFunction(unref(action?.isShow))) {
      return unref(action?.isShow)();
    }
    if (!Object.prototype.hasOwnProperty.call(action, 'isShow')) {
      return true;
    }
    return unref(action.isShow);
  }

  function isDisabled(action) {
    if (isFunction(unref(action?.disabled))) {
      return unref(action?.disabled)();
    }
    if (!Object.prototype.hasOwnProperty.call(action, 'disabled')) {
      return false;
    }
    return unref(action.disabled);
  }

  list?.forEach((c: any) => {
    if (Object.prototype.hasOwnProperty.call(c, 'children') && c?.children?.length) {
      const showActions = c?.children.filter((action: any) => {
        action.disabled = isDisabled(action);
        return isShow(action);
      });
      c.children = showActions;
      arr.push(c);
    } else if (isShow(c)) {
      c.disabled = isDisabled(c);
      arr.push(c);
    }
  });
  const [buttonLeftGroup, dropDownButtonGroup] = splitActions(arr, props.showButtonMaxNumber);
  return {
    buttonLeftGroup,
    dropDownButtonGroup,
  };
});

function getBindButtonProps(props = {}) {
  return omit(props, 'openModal', 'openDrawer', 'modal', 'onClick');
}

function handleMenuClick(action) {
  if (isFunction(action.onClick)) {
    action.onClick();
    return;
  }
  if (action?.openDrawer) {
    openContentDrawer(action.openDrawer ?? {});
    return;
  }
  if (action?.openModal) {
    openContentModal(action.openModal ?? {});
  }
  if (action?.modal) {
    Modal.confirm({
      title: action?.modalTitle ? `${action?.modalTitle}` : '删除提示',
      closable: true,
      content: action?.modalContent ? `${action?.modalContent}` : '确认要删除吗?',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        return (isFunction(action?.modal) && action?.modal());
      },
    });
  }
}

</script>
<style lang="less">
.button-style {
  border: none !important;
  color: black !important;
  padding: 0 !important;
  margin: 0 !important;
  height: 25px !important;
  line-height: 25px !important;
}

.button-list-box {
  display: inline-block;
}
</style>
