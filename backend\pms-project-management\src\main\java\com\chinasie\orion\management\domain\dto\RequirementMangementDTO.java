package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.management.domain.entity.RequirementManageCustContact;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * RequirementMangement DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RequirementMangementDTO对象", description = "主表")
@Data
@ExcelIgnoreUnannotated
public class RequirementMangementDTO extends ObjectDTO implements Serializable {

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @ExcelProperty(value = "需求编号 ", index = 0)
    private String requirementNumber;

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    @ExcelProperty(value = "需求标题 ", index = 1)
    private String requirementName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @ExcelProperty(value = "需求来源 ", index = 2)
    private String resSource;

    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开标时间 ", index = 3)
    private Date bidOpeningTm;

    /**
     * 报名开始日期
     */
    @ApiModelProperty(value = "报名开始日期")
    @ExcelProperty(value = "报名开始日期 ", index = 4)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signStartTime;

    /**
     * 报名结束日期
     */
    @ApiModelProperty(value = "报名结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "报名结束日期 ", index = 5)
    private Date signEndTime;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "报价截止时间 ", index = 6)
    private Date signDeadlnTime;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    @ExcelProperty(value = "客户 ", index = 7)
    private String custPerson;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @ExcelProperty(value = "客户范围 ", index = 8)
    private String custScope;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    @ExcelProperty(value = "客户主要联系人 ", index = 9)
    private String custConPerson;

    /**
     * 客户主要联系人电话
     */
    @ApiModelProperty(value = "客户主要联系人电话")
    @ExcelProperty(value = "客户主要联系人电话 ", index = 10)
    private String custContactPh;

    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    @ExcelProperty(value = "客户商务接口人 ", index = 11)
    private List<String> custBsPerson;

    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    @ExcelProperty(value = "客户技术接口人 ", index = 12)
    private List<String> custTecPerson;
    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertakeDept;
    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @ExcelProperty(value = "商务接口人 ", index = 13)
    private String businessPerson;
    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人名称")
    @ExcelProperty(value = "商务接口人名称 ", index = 21)
    private String businessPersonName;

    /**
     * 技术接口人(技术负责人)
     */
    @ApiModelProperty(value = "技术接口人(技术负责人)")
    @ExcelProperty(value = "技术接口人(技术负责人) ", index = 14)
    private String techRes;

    /**
     * 技术接口人名称(技术负责人名称)
     */
    @ApiModelProperty(value = "技术接口人名称(技术负责人名称)")
    @ExcelProperty(value = "技术接口人名称(技术负责人名称) ", index = 14)
    private String techResName;

    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    @ExcelProperty(value = "需求归属中心 ", index = 15)
    private String reqOwnership;

    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    @ExcelProperty(value = "配合部门接口人 ", index = 16)
    private List<String> cooperatePerson;

    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    @ExcelProperty(value = "配合部门 ", index = 17)
    private List<String> cooperateDpt;

    /**
     * 需求状态
     */
    @ApiModelProperty(value = "需求状态")
    @ExcelProperty(value = "需求状态 ", index = 18)
    private String projectStatus;

    /**
     * 富文本框，需求详情
     */
    @ApiModelProperty(value = "需求详情")
    @ExcelProperty(value = "需求详情 ", index = 19)
    private String projectDetail;
    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    @ExcelProperty(value = "响应状态 ", index = 19)
    private String responseStatus;

    /**
     * 附件材料上传
     */
    @ApiModelProperty(value = "附件材料上传")
    @Valid
    private List<FileDTO> fileList;

    /**
     * 需求确认备注
     */
    @ApiModelProperty(value = "需求确认备注")
    @ExcelProperty(value = "需求确认备注 ", index = 20)
    private String confirmRemark;

    /**
     * 标段名称
     */
    @ApiModelProperty(value = "标段名称")
    @ExcelProperty(value = "标段名称 ", index = 23)
    private String sectionName;

    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    @ExcelProperty(value = "客户部门 ", index = 24)
    private String custDptName;


    /**
     * ECP状态
     */
    @ApiModelProperty(value = "ECP状态")
    @ExcelProperty(value = "ECP状态 ", index = 26)
    private String ecpStatus;

    /**
     * ECP上次更新时间
     */
    @ApiModelProperty(value = "ECP上次更新时间")
    @ExcelProperty(value = "ECP上次更新时间 ", index = 27)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ecpUpdateTime;

    /**
     * 查询类型
     */
    @ApiModelProperty(value = "查询类型")
    @ExcelIgnore
    private String requirementType;

    /**
     * 是否发布公示
     */
    @ApiModelProperty(value = "是否发布公示")
    @ExcelProperty(value = "是否发布公示 ", index = 28)
    private String isPublished;

    /**
     * 来源ecp组织
     */
    @ApiModelProperty(value = "来源ecp组织")
    @ExcelProperty(value = "是否发布公示 ", index = 29)
    private String ecpGroup;

    /**
     * 来源ecp组织名称
     */
    @ApiModelProperty(value = "来源ecp组织名称")
    @ExcelProperty(value = "是否发布公示 ", index = 30)
    private String ecpGroupName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称 ", index = 31)
    private String custPersonName;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型 ", index = 32)
    private String businessType;


    /**
     * 编辑类型
     */
    @ApiModelProperty(value = "编辑类型")
    @ExcelIgnore
    private String editType;

    @ApiModelProperty(value = "已报价 1.是 0.否")
    @ExcelIgnore
    private Integer hadQuotation;

    /**
     * ECP系统删除标识
     */
    @ApiModelProperty(value = "ECP系统删除标识")
    @ExcelProperty(value = "ECP系统删除标识 ", index = 23)
    private String deleteFlag;


    /**
     * 按年范围筛选
     */
    @ApiModelProperty(value = "按年范围筛选")
    @ExcelIgnore
    private Integer filterYear;

    private LocalDate startTime;

    private LocalDate endTime;

    @ApiModelProperty(value = "客户-联系人")
    private List<RequirementManageCustContact> custContacts;

    @ApiModelProperty(value = "分发时间")
    private Date distributeTime;

    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @ExcelProperty(value = "优先级1低2中3高 ", index = 33)
    private String priority;

    @ApiModelProperty(value = "优先级排序0升1降序")
    private String prioritySort;
    /**
     * 报名申请人
     */
    @ApiModelProperty(value = "报名申请人")
    private String applicantUser;

    /**
     * 报名部门
     */
    @ApiModelProperty(value = "报名部门")

    private String applicantDept;
    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    /**
     * 所级负责人
     */
    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    /**
     * 关联交易表单Id
     */
    @ApiModelProperty(value = "关联交易表单Id")
    @ExcelProperty(value = "关联交易表单Id ", index = 26)
    private String transApprId;

    /**
     * 是否关联交易表单
     */
    @ApiModelProperty(value = "是否关联交易表单")
    @ExcelProperty(value = "是否关联交易表单 ", index = 27)
    private Boolean relTransAppr;

    /**
     * 不关联原因
     */
    @ApiModelProperty(value = "不关联原因")
    @ExcelProperty(value = "不关联原因 ", index = 28)
    private String unrelatedReason;

    /**
     * 关联交易表单编号
     */
    @ApiModelProperty(value = "关联交易表单编号")
    @ExcelProperty(value = "关联交易表单编号 ", index = 29)
    private String transApprNumber;

}
