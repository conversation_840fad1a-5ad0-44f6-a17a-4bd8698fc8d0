package org.jeecg.modules.demo.dkm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 技术支持人员信息
 * @Author: jeecg-boot
 * @Date:   2024-08-22
 * @Version: V1.0
 */
@Data
@TableName("dtc_tec_personnels")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="dtc_tec_personnels对象", description="技术支持人员信息")
public class DtcTecPersonnels implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private String id;
	/**工号*/
	@Excel(name = "工号", width = 15)
    @ApiModelProperty(value = "工号")
    private String staffNo;
	/**人员状态id(0:离职，1：在职)*/
	@Excel(name = "人员状态id(0:离职，1：在职)", width = 15)
    @ApiModelProperty(value = "人员状态id(0:离职，1：在职)")
    private String staffStatusId;
	/**人员状态*/
	@Excel(name = "人员状态", width = 15)
    @ApiModelProperty(value = "人员状态")
    private String staffStatus;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private String staffName;
	/**性别ID*/
	@Excel(name = "性别ID", width = 15)
    @ApiModelProperty(value = "性别ID")
    private String staffSexId;
	/**性别*/
	@Excel(name = "性别", width = 15)
    @ApiModelProperty(value = "性别")
    private String staffSex;
	/**有效证件类别*/
	@Excel(name = "有效证件类别", width = 15)
    @ApiModelProperty(value = "有效证件类别")
    private String staffIdType;
	/**有效证件号码*/
	@Excel(name = "有效证件号码", width = 15)
    @ApiModelProperty(value = "有效证件号码")
    private String staffIdCard;
	/**出生日期*/
	@Excel(name = "出生日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "出生日期")
    private Date staffBirthday;
	/**婚姻状况*/
	@Excel(name = "婚姻状况", width = 15)
    @ApiModelProperty(value = "婚姻状况")
    private String staffMaritalstatus;
	/**民族*/
	@Excel(name = "民族", width = 15)
    @ApiModelProperty(value = "民族")
    private String staffEthnic;
	/**联系方式*/
	@Excel(name = "联系方式", width = 15)
    @ApiModelProperty(value = "联系方式")
    private String mobilePhone1;
	/**最高学历*/
	@Excel(name = "最高学历", width = 15)
    @ApiModelProperty(value = "最高学历")
    private String education;
	/**所学专业*/
	@Excel(name = "所学专业", width = 15)
    @ApiModelProperty(value = "所学专业")
    private String major;
	/**职称*/
	@Excel(name = "职称", width = 15)
    @ApiModelProperty(value = "职称")
    private String staffTitle;
	/**专业技术证书*/
	@Excel(name = "专业技术证书", width = 15)
    @ApiModelProperty(value = "专业技术证书")
    private String certificate;
	/**所在公司*/
	@Excel(name = "所在公司", width = 15)
    @ApiModelProperty(value = "所在公司")
    private String company;
	/**所在部门*/
	@Excel(name = "所在部门", width = 15)
    @ApiModelProperty(value = "所在部门")
    private String workDept;
	/**所在研究所/专业室部门*/
	@Excel(name = "所在研究所/专业室部门", width = 15)
    @ApiModelProperty(value = "所在研究所/专业室部门")
    private String specializedDept;
	/**分管项目经理*/
	@Excel(name = "分管项目经理", width = 15)
    @ApiModelProperty(value = "分管项目经理")
    private String projectManager;
	/**供应商编码*/
	@Excel(name = "供应商编码", width = 15)
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
	/**所属供应商*/
	@Excel(name = "所属供应商", width = 15)
    @ApiModelProperty(value = "所属供应商")
    private String supplier;
	/**是否项目制人员*/
	@Excel(name = "是否项目制人员", width = 15)
    @ApiModelProperty(value = "是否项目制人员")
    private String izProjectStaff;
	/**合同编号*/
	@Excel(name = "合同编号", width = 15)
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;
	/**合同级别*/
	@Excel(name = "合同级别", width = 15)
    @ApiModelProperty(value = "合同级别")
    private String contractLevel;
	/**合同级别编号*/
	@Excel(name = "合同级别编号", width = 15)
    @ApiModelProperty(value = "合同级别编号")
    private String contractLevelId;
	/**合同名称*/
	@Excel(name = "合同名称", width = 15)
    @ApiModelProperty(value = "合同名称")
    private String contractName;
	/**工作内容*/
	@Excel(name = "工作内容", width = 15)
    @ApiModelProperty(value = "工作内容")
    private String workCon;
	/**常驻服务地点*/
	@Excel(name = "常驻服务地点", width = 15)
    @ApiModelProperty(value = "常驻服务地点")
    private String residentServiceLocation;
	/**常驻服务地点编号*/
	@Excel(name = "常驻服务地点编号", width = 15)
    @ApiModelProperty(value = "常驻服务地点编号")
    private String residentServiceLocationId;
	/**是否从事放射性工作*/
	@Excel(name = "是否从事放射性工作", width = 15)
    @ApiModelProperty(value = "是否从事放射性工作")
    private String existRadiologicalWork;
	/**是否已完成体检*/
	@Excel(name = "是否已完成体检", width = 15)
    @ApiModelProperty(value = "是否已完成体检")
    private String izExamCompleted;
	/**入场时间*/
	@Excel(name = "入场时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;
	/**办卡或授权选择*/
	@Excel(name = "办卡或授权选择", width = 15)
    @ApiModelProperty(value = "办卡或授权选择")
    private String applyOrAuth;
	/**离场时间*/
	@Excel(name = "离场时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "离场时间")
    private Date departureTime;
	/**是否已取消授权*/
	@Excel(name = "是否已取消授权", width = 15)
    @ApiModelProperty(value = "是否已取消授权")
    private String izRevokedAuth;
	/**是否有亲属在集团内*/
	@Excel(name = "是否有亲属在集团内", width = 15)
    @ApiModelProperty(value = "是否有亲属在集团内")
    private String haveRelativesInGroup;
	/**亲属姓名*/
	@Excel(name = "亲属姓名", width = 15)
    @ApiModelProperty(value = "亲属姓名")
    private String relativesName;
	/**亲属职务*/
	@Excel(name = "亲属职务", width = 15)
    @ApiModelProperty(value = "亲属职务")
    private String relativesPosition;
	/**亲属公司*/
	@Excel(name = "亲属公司", width = 15)
    @ApiModelProperty(value = "亲属公司")
    private String relativesCompany;
	/**是否技术配置*/
	@Excel(name = "是否技术配置", width = 15)
    @ApiModelProperty(value = "是否技术配置")
    private String izTecnologyConfiged;
	/**预计离岗时间*/
	@Excel(name = "预计离岗时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预计离岗时间")
    private Date estimatedTimeOfAbsence;
	/**是否违反相关安全规范*/
	@Excel(name = "是否违反相关安全规范", width = 15)
    @ApiModelProperty(value = "是否违反相关安全规范")
    private String izViolationOfRelevantSafetyRegulations;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**操作人*/
	@Excel(name = "操作人", width = 15)
    @ApiModelProperty(value = "操作人")
    private String operator;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**更新时间*/
	@Excel(name = "更新时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date modifyTime;
	/**组织单元编号*/
	@Excel(name = "组织单元编号", width = 15)
    @ApiModelProperty(value = "组织单元编号")
    private String deptNo;
	/**组织单元编号*/
	@Excel(name = "组织单元", width = 15)
    @ApiModelProperty(value = "组织单元")
    private String deptName;
}
