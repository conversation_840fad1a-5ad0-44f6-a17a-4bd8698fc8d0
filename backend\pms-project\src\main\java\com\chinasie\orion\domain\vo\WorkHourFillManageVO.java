package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * WorkHourFillManageVO Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillManageVO对象", description = "工时填报(项目经理)")
@Data
public class WorkHourFillManageVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    private String number;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    private String workHourType;

    /**
     * 填报角色
     */
    @ApiModelProperty(value = "填报角色")
    private String fillRole;

    /**
     * 成员角色
     */
    @ApiModelProperty(value = "成员角色")
    private String memberRoleName;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;


    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;
    
    /**
     * 记录
     */
    @ApiModelProperty(value = "记录")
    List<WorkHourFillDayInfoVO> dayList;
}
