package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormCreateDTO;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormQueryDTO;
import com.chinasie.orion.domain.vo.AcceptanceFormListVO;
import com.chinasie.orion.domain.vo.AcceptanceFormVO;
//import com.chinasie.orion.domain.vo.ProcurementPlanApprovalListVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AcceptanceFormService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 验收单Controller.
 *
 * <AUTHOR>
 */
@Api(tags = "验收单管理")
@RestController
@RequestMapping("/acceptance-form")
public class AcceptanceFormController {

    @Resource
    private AcceptanceFormService acceptanceFormService;

    /**
     * 分页获取验收单列表.
     *
     * @param pageRequest
     * @return
     */
    @ApiOperation("验收单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")})
    @PostMapping("/page")
    @LogRecord(
            success = "【{USER{#logUserId}}】在【项目验收列表】获取验收单数据",
            fail = "【{USER{#logUserId}}】在【项目验收列表】获取验收单数据，失败原因：「{{#_errorMsg}}」",
            type = "验收单管理", subType = "项目验收列表", bizNo = "", extra = OperateTypeDict.GET)
    public ResponseDTO page(@RequestBody Page<AcceptanceFormQueryDTO> pageRequest) throws Exception {
        Page<AcceptanceFormListVO> page = acceptanceFormService.pageInfo(pageRequest);
        return new ResponseDTO(page);
    }

    /**
     * 创建验收单, 支持采购计划立项验收单、项目验收单.
     *
     * @param dto
     * @return
     */
    @ApiOperation("创建验收单")
    @PostMapping("")
    @LogRecord(success = "【{USER{#logUserId}}】创建验收单【{{#id}}】", type = "验收单管理", subType = "创建验收单", bizNo = "")
    public ResponseDTO create(@RequestBody AcceptanceFormCreateDTO dto) throws Exception {
        AcceptanceFormVO ret = acceptanceFormService.createAcceptanceForm(dto);
        return new ResponseDTO(ret);
    }

    /**
     * 获取验收单详情.
     *
     * @param acceptanceFormId
     * @return
     */
    @ApiOperation("验收单详情")
    @GetMapping("/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】查看验收单详情【{{#id}}】", type = "验收单管理", subType = "创建验收单", bizNo = "{{#id}}")
    public ResponseDTO findById(@PathVariable("id") String acceptanceFormId,@RequestParam("pageCode") String pageCode) throws Exception {
        AcceptanceFormVO vo = acceptanceFormService.findById(acceptanceFormId,pageCode);
        return new ResponseDTO(vo);
    }

    /**
     * 通过项目id获取验收单详情.
     *
     * @param projectId
     * @return
     */
    @ApiOperation("通过项目id获取验收单详情")
    @GetMapping("/byProjectId/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】通过项目id获取验收单详情", type = "验收单管理", subType = "通过项目id获取验收单详情", bizNo = "")
    public ResponseDTO findByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        AcceptanceFormVO vo = acceptanceFormService.findByProjectId(projectId,"");
        return new ResponseDTO(vo);
    }


    /**
     * 更改验收单状态.
     * status = COMPLETE, 验收完安城.
     * status = IN_PROGRESS, 表示验收中, 目前暂不支持.
     *
     * @param acceptanceFormId
     * @param status
     * @return
     */
    @ApiOperation("验收单状态变更")
    @PutMapping("/{id}/status")
    @LogRecord(success = "【{USER{#logUserId}}】验收单状态变更", type = "验收单管理", subType = "验收单状态变更", bizNo = "")
    public ResponseDTO changeStatus(@PathVariable("id") String acceptanceFormId, String status) throws Exception {
        Boolean ret = acceptanceFormService.changeAcceptanceFormStatus(acceptanceFormId, status);
        if (ret) {
            return new ResponseDTO(200,"操作成功");
        } else {
            return new ResponseDTO(PMSErrorCode.PMS_ERR.getErrorCode(), "操作失败");
        }
    }

//    /**
//     * 获取验收单关联的采购计划立项列表.
//     *
//     * @param acceptanceFormId
//     * @return
//     */
//    @ApiOperation("验收单关联采购计划立项列表")
//    @PostMapping("/{id}/procurement-plan-approval")
//    public ResponseDTO findProcurementPlanApprovalItems(@PathVariable("id") String acceptanceFormId, @RequestBody Page<ProcurementPlanApprovalQueryDTO> pageRequest) throws Exception {
//        if (pageRequest.getQuery() == null) {
//            pageRequest.setQuery(new ProcurementPlanApprovalQueryDTO());
//        }
//        pageRequest.getQuery().setAcceptanceFormId(acceptanceFormId);
//        Page<ProcurementPlanApprovalListVO> page = acceptanceFormService.pageQueryProcurementPlanApproval(pageRequest);
//        return new ResponseDTO(page);
//    }

//    private Page<ProcurementPlanApprovalListVO> fakeProcurementPlanApprovalItems() {
//        Page<ProcurementPlanApprovalListVO> page = new Page<ProcurementPlanApprovalListVO>();
//        page.setPageNum(1);
//        page.setPageSize(15);
//        page.setTotalSize(8);
//        page.setTotalPages(1);
//        page.setContent(new ArrayList<>());
//
//        ProcurementPlanApprovalListVO vo1 = createProcurementPlanApprovalListVO("ppal001", "分包", 101, "已立项", "number000A01", "linNumer01",
//                "物料001", "规格002", 2, "件", "工业部", "日常", "Q2", "F");
//        page.getContent().add(vo1);
//
//        ProcurementPlanApprovalListVO vo2 = createProcurementPlanApprovalListVO("ppal002", "合同", 101, "已立项", "number000A01", "linNumer01",
//                "物料003", "规格00A", 12, "件", "工业部", "日常", "Q2", "F");
//        page.getContent().add(vo2);
//
//        return page;
//    }

//    private ProcurementPlanApprovalListVO createProcurementPlanApprovalListVO(String id, String phase, Integer status, String dataStatusName,
//                                                                              String number, String lineNumber, String suppliesNumber, String specificationsModels,
//                                                                              Integer demandNum, String unitOfMeasureDemand, String demandPersonName,
//                                                                              String demandType, String qaLevel, String nucleusSafetyLevel) {
//        ProcurementPlanApprovalListVO vo = new ProcurementPlanApprovalListVO();
//        vo.setId(id);
//        vo.setPhase(phase);
//        vo.setStatus(1);
//
//        DataStatusVO statusVO = new DataStatusVO();
//        statusVO.setId("dataStatus0001");
//        statusVO.setName(dataStatusName);
//        vo.setDataStatus(statusVO);
//
//        vo.setNumber(number);
//        vo.setLineNumber(lineNumber);
//        vo.setSuppliesNumber(suppliesNumber);
//        vo.setSpecificationsModels(specificationsModels);
//        vo.setDemandNum(demandNum);
//        vo.setUnitOfMeasureDemand(unitOfMeasureDemand);
//        vo.setDemandPersonName(demandPersonName);
//        vo.setDemandType(demandType);
//        vo.setQaLevel(qaLevel);
//        vo.setNucleusSafetyLevel(nucleusSafetyLevel);
//
//        return vo;
//    }

    /**
     * 验收文件列表.
     *
     * @param acceptanceFormId
     * @return
     */
    @ApiOperation("验收文件列表")
    @PostMapping("/{id}/files/page")
    @LogRecord(success = "【{USER{#logUserId}}】获取验收文件列表", type = "验收单管理", subType = "验收文件列表", bizNo = "")
    public ResponseDTO listFiles(@PathVariable("id") String acceptanceFormId, @RequestBody Page pageRequest) throws Exception {
        return new ResponseDTO(acceptanceFormService.listRelatedFiles(acceptanceFormId, pageRequest));
    }

    /**
     * 更改验收文件状态.
     *
     * @param acceptanceFormId 验收单Id
     * @param fileIds          验收文件Id
     * @return
     */
    @ApiOperation("删除验收文件")
    @DeleteMapping("/{id}/files")
    @LogRecord(success = "【{USER{#logUserId}}】删除验收文件", type = "验收单管理", subType = "删除验收文件", bizNo = "")
    public ResponseDTO deleteFiles(@PathVariable("id") String acceptanceFormId, @RequestBody List<String> fileIds) throws Exception {
        Boolean ret = acceptanceFormService.deleteRelatedFile(acceptanceFormId, fileIds);
        return new ResponseDTO();
    }

    /**
     * 保存验收文件.
     *
     * @param acceptanceFormId
     * @param files
     * @return
     */
    @ApiOperation("保存验收文件")
    @PostMapping("/{id}/files")
    @LogRecord(success = "【{USER{#logUserId}}】保存验收文件", type = "验收单管理", subType = "保存验收文件", bizNo = "")
    public ResponseDTO saveFiles(@PathVariable("id") String acceptanceFormId, @RequestBody List<FileDTO> files) throws Exception {
        Boolean ret = acceptanceFormService.saveRelatedFiles(acceptanceFormId, files);
        if (ret) {
            return new ResponseDTO(200);
        } else {
            return new ResponseDTO(PMSErrorCode.PMS_ERR.getErrorCode(), "操作失败");
        }
    }
}
