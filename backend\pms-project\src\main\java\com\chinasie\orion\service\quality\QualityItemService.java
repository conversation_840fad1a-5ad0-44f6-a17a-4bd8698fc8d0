package com.chinasie.orion.service.quality;


import com.chinasie.orion.domain.dto.quality.QualityItemDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemToProductDTO;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * QualityItem 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
public interface QualityItemService extends OrionBaseService<QualityItem> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    QualityItemVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param qualityItemDTO
     */
    Boolean create(List<String> qualityIds, String projectId) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param qualityItemDTO
     */
    Boolean edit(QualityItemDTO qualityItemDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<QualityItemVO> pages(Page<QualityItemDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String projectId) throws Exception;

    void setEveryName(List<QualityItemVO> vos) throws Exception;


    Boolean promoteExecute(String schemeId);

    /**
     * 根据消息id获取分页数据
     * @param pageRequest
     * @return
     */
    Page<QualityItemVO> pagesByMessageId(Page<QualityItemDTO> pageRequest) throws Exception;

//    /**
//     * @param pageRequest
//     * @return
//     * @throws Exception
//     */
//    List<String> qualityItemHeaderAuth(Page<QualityItemDTO> pageRequest) throws Exception;

    /**
     * 生成文档
     *
     * @param projectId
     */
    Object getDocument(String templateId, String projectId);

    /**
     * 质量管控项（产品列表）
     * @param projectId
     * @return
     */
    List<ProductEstimateMaterialVO> getProductList(String projectId);

    /**
     * 质量控制项关联项目信息
     * @param qualityItemToProductDTO
     * @return
     */
    Boolean relatedProduct(QualityItemToProductDTO qualityItemToProductDTO);
}
