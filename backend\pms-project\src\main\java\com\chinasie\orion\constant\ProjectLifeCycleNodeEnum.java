package com.chinasie.orion.constant;

import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleNodeLineVO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeEnum
 * @description 项目生命周期节点枚举值 枚举值
 * @since 2023/10/27
 */
public enum ProjectLifeCycleNodeEnum {
    PROJECT_DECLARE("PROJECT_DECLARE", "项目申报", "NORMAL_NODE"),
    CONTRACT_SIGNING("CONTRACT_SIGNING", "合同签订", "NORMAL_NODE"),
    PROJECT_APPROVAL("PROJECT_APPROVAL", "项目立项", "NORMAL_NODE"),
    MEMBER_MANAGEMENT("MEMBER_MANAGEMENT", "成员管理", "NORMAL_NODE"),
    PLAN_MANAGEMENT("PLAN_MANAGEMENT", "计划管理", "NORMAL_NODE"),
    MAN_HOUR_MANAGEMENT("MAN_HOUR_MANAGEMENT", "工时管理", "NORMAL_NODE"),
    BUDGET_MANAGEMENT("BUDGET_MANAGEMENT", "预算管理", "NORMAL_NODE"),
    COST_MANAGEMENT("COST_MANAGEMENT", "成本管理", "NORMAL_NODE"),
    REVENUE_MANAGEMENT("REVENUE_MANAGEMENT", "营收管理", "NORMAL_NODE"),
    MATERIAL_MANAGEMENT("MATERIAL_MANAGEMENT", "物资管理", "NORMAL_NODE"),
    PURCHASING_MANAGEMENT("PURCHASING_MANAGEMENT", "采购管理", "NORMAL_NODE"),
    CONTRACT_MANAGEMENT("CONTRACT_MANAGEMENT", "合同管理", "NORMAL_NODE"),
    RISK_MANAGEMENT("RISK_MANAGEMENT", "风险管理", "NORMAL_NODE"),
    PROBLEM_MANAGEMENT("PROBLEM_MANAGEMENT", "问题管理", "NORMAL_NODE"),
    CHANGE_MANAGEMENT("CHANGE_MANAGEMENT", "变更管理", "NORMAL_NODE"),
    DELIVER_MANAGEMENT("DELIVER_MANAGEMENT", "交付物管理", "NORMAL_NODE"),
    PROJECT_DOCUMENT("PROJECT_DOCUMENT", "项目文档", "NORMAL_NODE"),
    PROJECT_ACCEPTANCE("PROJECT_ACCEPTANCE", "项目验收", "NORMAL_NODE"),
    PROJECT_EVALUATION("PROJECT_EVALUATION", "项目评价", "NORMAL_NODE"),
    START("START", "开始", "START_END_NODE"),
    END("END", "结束", "START_END_NODE"),
    CREATE("CREATE", "已创建", "STATUS_NODE"),
    PENDING("PENDING", "待立项", "STATUS_NODE"),
    APPROVED("APPROVED", "已立项", "STATUS_NODE"),
    ACCEPTED("ACCEPTED", "已验收", "STATUS_NODE"),
    CLOSE("CLOSE", "已关闭", "STATUS_NODE"),
    ;
    // 节点名称
    private final String nodeName;
    // 节点KEY
    private final String nodeKey;
    // 节点类型
    private final String nodeType;

    ProjectLifeCycleNodeEnum(String nodeKey, String nodeName, String nodeType) {
        this.nodeKey = nodeKey;
        this.nodeName = nodeName;
        this.nodeType = nodeType;
    }

    public String getNodeName() {
        return nodeName;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public String getNodeType() {
        return nodeType;
    }

    /**
     * 节点高亮集合
     *
     * @return Map
     */
    public static Map<Integer, List<String>> getNodeHighLight() {
        return statusNodeKeyHighLightMap;
    }

    // 销售类项目、投资/服务类项目高亮
    private static final Map<Integer, List<String>> statusNodeKeyHighLightMap = new HashMap<>();

    // 投资/服务类项目::已创建
    static final String[] createArr = new String[]{
            START.getNodeKey(), CREATE.getNodeKey(),
            PROJECT_DECLARE.getNodeKey(), CONTRACT_SIGNING.getNodeKey()
    };

    // 投资/服务类项目::待立项
    static final String[] highLightPendingArr = new String[]{
            START.getNodeKey(), CREATE.getNodeKey(), PROJECT_DECLARE.getNodeKey(),
            CONTRACT_SIGNING.getNodeKey(), PROJECT_APPROVAL.getNodeKey(), PENDING.getNodeKey()
    };
    // 投资/服务类项目::已立项
    static final String[] approvedArr = new String[]{
            START.getNodeKey(), CREATE.getNodeKey(),
            PROJECT_DECLARE.getNodeKey(), CONTRACT_SIGNING.getNodeKey(), PROJECT_APPROVAL.getNodeKey(),
            PENDING.getNodeKey(), APPROVED.getNodeKey(),
            MEMBER_MANAGEMENT.getNodeKey(), PLAN_MANAGEMENT.getNodeKey(), MAN_HOUR_MANAGEMENT.getNodeKey(),
            BUDGET_MANAGEMENT.getNodeKey(), COST_MANAGEMENT.getNodeKey(), REVENUE_MANAGEMENT.getNodeKey(),
            MATERIAL_MANAGEMENT.getNodeKey(), PURCHASING_MANAGEMENT.getNodeKey(), CONTRACT_MANAGEMENT.getNodeKey(),
            RISK_MANAGEMENT.getNodeKey(), PROBLEM_MANAGEMENT.getNodeKey(), CHANGE_MANAGEMENT.getNodeKey(),
            DELIVER_MANAGEMENT.getNodeKey(), PROJECT_DOCUMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()
    };
    // 投资/服务类项目::已验收
    static final String[] acceptedArr = new String[]{
            START.getNodeKey(), CREATE.getNodeKey(),
            PROJECT_DECLARE.getNodeKey(), CONTRACT_SIGNING.getNodeKey(), PROJECT_APPROVAL.getNodeKey(),
            PENDING.getNodeKey(), APPROVED.getNodeKey(),
            MEMBER_MANAGEMENT.getNodeKey(), PLAN_MANAGEMENT.getNodeKey(), MAN_HOUR_MANAGEMENT.getNodeKey(),
            BUDGET_MANAGEMENT.getNodeKey(), COST_MANAGEMENT.getNodeKey(), REVENUE_MANAGEMENT.getNodeKey(),
            MATERIAL_MANAGEMENT.getNodeKey(), PURCHASING_MANAGEMENT.getNodeKey(), CONTRACT_MANAGEMENT.getNodeKey(),
            RISK_MANAGEMENT.getNodeKey(), PROBLEM_MANAGEMENT.getNodeKey(), CHANGE_MANAGEMENT.getNodeKey(),
            DELIVER_MANAGEMENT.getNodeKey(), PROJECT_DOCUMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey(),
            ACCEPTED.getNodeKey(), PROJECT_EVALUATION.getNodeKey()
    };
    // 投资/服务类项目::已关闭
    static final String[] closeArr = new String[]{
            START.getNodeKey(), CREATE.getNodeKey(),
            PROJECT_DECLARE.getNodeKey(), CONTRACT_SIGNING.getNodeKey(), PROJECT_APPROVAL.getNodeKey(),
            PENDING.getNodeKey(), APPROVED.getNodeKey(),
            MEMBER_MANAGEMENT.getNodeKey(), PLAN_MANAGEMENT.getNodeKey(), MAN_HOUR_MANAGEMENT.getNodeKey(),
            BUDGET_MANAGEMENT.getNodeKey(), COST_MANAGEMENT.getNodeKey(), REVENUE_MANAGEMENT.getNodeKey(),
            MATERIAL_MANAGEMENT.getNodeKey(), PURCHASING_MANAGEMENT.getNodeKey(), CONTRACT_MANAGEMENT.getNodeKey(),
            RISK_MANAGEMENT.getNodeKey(), PROBLEM_MANAGEMENT.getNodeKey(), CHANGE_MANAGEMENT.getNodeKey(),
            DELIVER_MANAGEMENT.getNodeKey(), PROJECT_DOCUMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey(),
            ACCEPTED.getNodeKey(), PROJECT_EVALUATION.getNodeKey(), CLOSE.getNodeKey(), END.getNodeKey()
    };

    static {
        statusNodeKeyHighLightMap.put(ProjectStatusEnum.CREATE.getStatus(),
                Arrays.stream(createArr).collect(Collectors.toList()));

        statusNodeKeyHighLightMap.put(ProjectStatusEnum.PENDING.getStatus(),
                Arrays.stream(highLightPendingArr).collect(Collectors.toList()));

        statusNodeKeyHighLightMap.put(ProjectStatusEnum.APPROVED.getStatus(),
                Arrays.stream(approvedArr).collect(Collectors.toList()));

        statusNodeKeyHighLightMap.put(ProjectStatusEnum.ACCEPTED.getStatus(),
                Arrays.stream(acceptedArr).collect(Collectors.toList()));

        statusNodeKeyHighLightMap.put(ProjectStatusEnum.CLOSE.getStatus(),
                Arrays.stream(closeArr).collect(Collectors.toList()));
    }

    /*
     * 节点连接线
     * */
    public static Map<String, List<ProjectLifeCycleNodeLineVO>> getNodeLine() {
        return nodeLineMap;
    }

    private static final Map<String, List<ProjectLifeCycleNodeLineVO>> nodeLineMap = new HashMap<>();

    static {
        // 销售类项目生命周期连接线

        nodeLineMap.put(ProjectTypeEnum.PROJECT_TYPE_SELL.getValue(),
                getProjectLifeCycleNodeLines(ProjectTypeEnum.PROJECT_TYPE_SELL.getValue()));
        // 投资/服务类项目生命周期连接线

        nodeLineMap.put(ProjectTypeEnum.PROJECT_TYPE_INVEST_SERVER.getValue(),
                getProjectLifeCycleNodeLines(ProjectTypeEnum.PROJECT_TYPE_INVEST_SERVER.getValue()));

        nodeLineMap.put(ProjectTypeEnum.PROJECT_TYPE_SCIENTIFIC_RESEARCH.getValue(),
                getProjectLifeCycleNodeLines(ProjectTypeEnum.PROJECT_TYPE_SCIENTIFIC_RESEARCH.getValue()));
    }

    /**
     * 根据项目类型生成节点间关联线
     *
     * @param projectType 项目类型
     * @return List
     */
    private static List<ProjectLifeCycleNodeLineVO> getProjectLifeCycleNodeLines(String projectType) {
        List<ProjectLifeCycleNodeLineVO> list = new ArrayList<>();
        list.add(new ProjectLifeCycleNodeLineVO(START.getNodeKey(), CREATE.getNodeKey()));
        if (ProjectTypeEnum.PROJECT_TYPE_SELL.getValue().equals(projectType)) {
            list.add(new ProjectLifeCycleNodeLineVO(CREATE.getNodeKey(), CONTRACT_SIGNING.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(CONTRACT_SIGNING.getNodeKey(), PENDING.getNodeKey()));
        } else{
            list.add(new ProjectLifeCycleNodeLineVO(CREATE.getNodeKey(), PROJECT_DECLARE.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(PROJECT_DECLARE.getNodeKey(), PENDING.getNodeKey()));
        }
        if(ProjectTypeEnum.PROJECT_TYPE_SCIENTIFIC_RESEARCH.getValue().equals(projectType)){
            list.add(new ProjectLifeCycleNodeLineVO(PENDING.getNodeKey(), CONTRACT_SIGNING.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(CONTRACT_SIGNING.getNodeKey(), PROJECT_APPROVAL.getNodeKey()));
        }
        else{
            list.add(new ProjectLifeCycleNodeLineVO(PENDING.getNodeKey(), PROJECT_APPROVAL.getNodeKey()));
        }
        list.add(new ProjectLifeCycleNodeLineVO(PROJECT_APPROVAL.getNodeKey(), APPROVED.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), MEMBER_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(MEMBER_MANAGEMENT.getNodeKey(), PLAN_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(PLAN_MANAGEMENT.getNodeKey(), MAN_HOUR_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(MAN_HOUR_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));

        if (ProjectTypeEnum.PROJECT_TYPE_SELL.getValue().equals(projectType)) {
            list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), REVENUE_MANAGEMENT.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(REVENUE_MANAGEMENT.getNodeKey(), COST_MANAGEMENT.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(COST_MANAGEMENT.getNodeKey(), BUDGET_MANAGEMENT.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(BUDGET_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));
        }
        else{
            list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), BUDGET_MANAGEMENT.getNodeKey()));
            list.add(new ProjectLifeCycleNodeLineVO(BUDGET_MANAGEMENT.getNodeKey(), COST_MANAGEMENT.getNodeKey()));
            if(ProjectTypeEnum.PROJECT_TYPE_SCIENTIFIC_RESEARCH.getValue().equals(projectType)){
                list.add(new ProjectLifeCycleNodeLineVO(COST_MANAGEMENT.getNodeKey(), REVENUE_MANAGEMENT.getNodeKey()));
                list.add(new ProjectLifeCycleNodeLineVO(REVENUE_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));
            }
            else{
                list.add(new ProjectLifeCycleNodeLineVO(COST_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));
            }

        }

        list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), MATERIAL_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(MATERIAL_MANAGEMENT.getNodeKey(), PURCHASING_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(PURCHASING_MANAGEMENT.getNodeKey(), CONTRACT_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(CONTRACT_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));

        list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), RISK_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(RISK_MANAGEMENT.getNodeKey(), PROBLEM_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(PROBLEM_MANAGEMENT.getNodeKey(), CHANGE_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(CHANGE_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));

        list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), DELIVER_MANAGEMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(DELIVER_MANAGEMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));

        list.add(new ProjectLifeCycleNodeLineVO(APPROVED.getNodeKey(), PROJECT_DOCUMENT.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(PROJECT_DOCUMENT.getNodeKey(), PROJECT_ACCEPTANCE.getNodeKey()));

        list.add(new ProjectLifeCycleNodeLineVO(PROJECT_ACCEPTANCE.getNodeKey(), ACCEPTED.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(ACCEPTED.getNodeKey(), PROJECT_EVALUATION.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(PROJECT_EVALUATION.getNodeKey(), CLOSE.getNodeKey()));
        list.add(new ProjectLifeCycleNodeLineVO(CLOSE.getNodeKey(), END.getNodeKey()));

        return list;
    }
}
