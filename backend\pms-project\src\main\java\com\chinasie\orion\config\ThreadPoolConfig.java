package com.chinasie.orion.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;


/**
 * <AUTHOR>
 * @date 2024 年 08 月 15 日
 * 线程池
 **/
@Configuration
@EnableAsync
public class ThreadPoolConfig {
    /**
     * 项目对学生使用的线程池
     * @return
     */
    @Bean("projectPool")
    public ThreadPoolTaskExecutor projectPool(){
        ThreadPoolTaskExecutor projectPool = new ThreadPoolTaskExecutor();
        projectPool.setCorePoolSize(5);
        projectPool.setMaxPoolSize(10);
        projectPool.setQueueCapacity(100);
        projectPool.setThreadNamePrefix("project_pool_");
        projectPool.setAllowCoreThreadTimeOut(true);
        return projectPool;
    }
}
