package com.chinasie.orion.controller.quality;

import com.chinasie.orion.domain.dto.quality.QualityItemMessageDTO;
import com.chinasie.orion.domain.vo.quality.QualityItemMessageVO;
import com.chinasie.orion.service.quality.QualityItemMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * QualityItemMessage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@RestController
@RequestMapping("/qualityItemMessage")
@Api(tags = "质量管控项和消息关联关系")
public class QualityItemMessageController {

    @Autowired
    private QualityItemMessageService qualityItemMessageService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【质量管控项和消息关联关系】详情", type = "QualityItemMessage", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<QualityItemMessageVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        QualityItemMessageVO rsp = qualityItemMessageService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param qualityItemMessageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody QualityItemMessageDTO qualityItemMessageDTO) throws Exception {
        String rsp = qualityItemMessageService.create(qualityItemMessageDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param qualityItemMessageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "编辑", bizNo = "{{#qualityItemMessageDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody QualityItemMessageDTO qualityItemMessageDTO) throws Exception {
        Boolean rsp = qualityItemMessageService.edit(qualityItemMessageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = qualityItemMessageService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = qualityItemMessageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QualityItemMessageVO>> pages(@RequestBody Page<QualityItemMessageDTO> pageRequest) throws Exception {
        Page<QualityItemMessageVO> rsp = qualityItemMessageService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("质量管控项和消息关联关系导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【质量管控项和消息关联关系】导入模板", type = "QualityItemMessage", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        qualityItemMessageService.downloadExcelTpl(response);
    }

    @ApiOperation("质量管控项和消息关联关系导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【质量管控项和消息关联关系】导入", type = "QualityItemMessage", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = qualityItemMessageService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("质量管控项和消息关联关系导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【质量管控项和消息关联关系】导入", type = "QualityItemMessage", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = qualityItemMessageService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消质量管控项和消息关联关系导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【质量管控项和消息关联关系】导入", type = "QualityItemMessage", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = qualityItemMessageService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("质量管控项和消息关联关系导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【质量管控项和消息关联关系】数据", type = "QualityItemMessage", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        qualityItemMessageService.exportByExcel(searchConditions, response);
    }
}
