/**
 *
 * @param amount 要格式化的金额（必需）
 * @param decimalCount 小数点后要显示的位数，默认为 2
 * @param decimal 小数点分隔符，默认为 "."
 * @param thousands 分隔符用于千位数，默认为 ","
 * @param multiple 格式化的倍数
 */

function formatMoney(amount, decimalCount = 2, decimal = '.', thousands = ',', multiple = 1) {
  if (amount == null || isNaN(amount)) return '0';
  decimalCount = Math.abs(decimalCount);
  decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

  const negativeSign = amount < 0 ? '-' : '';
  amount = Number(amount) / multiple;
  let iLength = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
  let j = (iLength.length > 3) ? iLength.length % 3 : 0;

  return negativeSign + (j ? iLength.substr(0, j) + thousands : '') + iLength.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) + (decimalCount ? decimal + Math.abs(amount - Number(iLength)).toFixed(decimalCount).slice(2) : '');
}
function getTableData(params) {
  return Promise.resolve([
    {
      name: '模拟数据23323',
      id: 11111,
    },
  ]);
}

export {
  formatMoney,
  getTableData,
};
