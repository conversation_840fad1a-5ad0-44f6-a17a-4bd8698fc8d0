package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;


/**
 * 项目计划催办，消息推送处理
 */
@Slf4j
@Component("projectSchemeUrgeSendMsgAdapter")
public class ProjectSchemeUrgeSendMsgAdapter extends SendMessageCommonAdapter {

    @Resource
    private UserRedisHelper userRedisHelper;


    @Resource
    private MessageCenterApi messageCenterApi;


    /**
     * 项目计划详情页
     */
    private final static String JUMP_URL = "/pms/ProPlanDetails/%s";

    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        List<ProjectScheme> projectSchemeList = schemeMsgDTO.getProjectSchemeList();


        Map<String, String> extParam = schemeMsgDTO.getExtParam();
        String remark = extParam.get("remark");
        String notifyUserIdStr = extParam.get("notifyUserId");
        List<String> notifyUserIds = JSONUtil.toBean(notifyUserIdStr, new TypeReference<>() {
        }, false);

        String name = userRedisHelper.getUserBaseCacheById(CurrentUserHelper.getCurrentUserId()).getName();


        for (ProjectScheme item : projectSchemeList) {
            Map<String, Object> messageMap = new HashMap<>(2);
            messageMap.put("$urgeUser$", name);
            messageMap.put("$schemeName$", item.getName());

            UserVO rspUser = userRedisHelper.getUserById(item.getRspUser());
            messageMap.put("$rspUser$", rspUser.getName());

            if (StrUtil.isNotBlank(remark)) {
                messageMap.put("$remark$", remark);
            } else {
                messageMap.put("$remark$", "");
            }
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            List<String> recipientIds = new ArrayList<>();
            String participantUsers = item.getParticipantUsers();
            if(StringUtils.hasText(participantUsers)){
                List<String> split = ListUtil.of(participantUsers.split(","));
                recipientIds.addAll(split);
            }
            recipientIds.add(item.getRspUser());

            if (!CollectionUtils.isEmpty(notifyUserIds)) {
                SendMessageDTO sendMsc = SendMessageDTO.builder()
                        .businessData(JSON.toJSONString(MapUtil.builder()
                                .put("parentId",parentId)
                                .put("id",item.getId())
                                .put("type", "plan")
                                .put("projectId", item.getProjectId())
                                .build()))
                        .businessId(item.getId())
                        .todoStatus(0)
                        .todoType(0)
                        .urgencyLevel(0)
                        .messageMap(messageMap)
                        .businessNodeCode("pms_project_scheme_urge_leader")
                        .businessTypeCode("ProjectScheme")
                        .businessTypeName("项目计划")
                        .titleMap(messageMap)
                        .messageUrl(String.format(JUMP_URL, item.getId()))
                        .messageUrlName("详情")
                        .recipientIdList(notifyUserIds)
                        .senderId(CurrentUserHelper.getCurrentUserId())
                        .senderTime(new Date())
                        .build();
                messageDTOList.add(sendMsc);
            }

            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessId(item.getId())
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode("pms_project_scheme_urge_rspuser")
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .titleMap(messageMap)
                    .messageUrl(String.format(JUMP_URL, item.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(recipientIds)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            messageDTOList.add(sendMsc);

        }
        return messageDTOList;
    }

    @Override
    protected void clearToDo(MsgBusinessTypeEnum type, String businessId, String userId) {
        log.debug("【{}】消除待办参数：{}", type.getDesc(), packageBusinessId(type, businessId));
        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(userId)
                .businessId(businessId)
                .build());
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));

    }
}
