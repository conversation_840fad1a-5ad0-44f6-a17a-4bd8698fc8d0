package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * SchemeToPerson Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@TableName(value = "pmsx_scheme_to_person")
@ApiModel(value = "SchemeToPersonEntity对象", description = "计划相关的人员")
@Data

public class SchemeToPerson extends  ObjectEntity  implements Serializable{

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    @TableField(value = "plan_scheme_id")
    private String planSchemeId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 人员ID： 人员管理ID
     */
    @ApiModelProperty(value = "人员ID： 人员管理ID")
    @TableField(value = "person_id")
    private String personId;

    /**
     * 用户工号
     */
    @ApiModelProperty(value = "用户工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    @ApiModelProperty(value = "有无项目:默认有")
    @TableField(value = "is_have_project")
    private Boolean isHaveProject;
}
