package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.ProjectMaterialPreparationStatus;
import com.chinasie.orion.domain.dto.ProjectMaterialPlanPreparationDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPreparationDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPreparationInfoDTO;
import com.chinasie.orion.domain.entity.ProjectMaterialPreparation;
import com.chinasie.orion.domain.entity.ProjectMaterialPreparationInfo;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanPreparationVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.manager.WfInstanceProjectMaterialPreparationManage;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectMaterialPreparationMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.RevisionOrderCalculationHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectMaterialPreparationInfoService;
import com.chinasie.orion.service.ProjectMaterialPreparationService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.alibaba.excel.context.AnalysisContext;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectMaterialPreparation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
@Service
@Slf4j
public class ProjectMaterialPreparationServiceImpl extends  OrionBaseServiceImpl<ProjectMaterialPreparationMapper, ProjectMaterialPreparation>   implements ProjectMaterialPreparationService {


    @Autowired
    private ProjectMaterialPreparationInfoService projectMaterialPreparationInfoService;

    @Autowired
    private WfInstanceProjectMaterialPreparationManage wfInstanceProjectMaterialPreparationManage;

    @Autowired
    private NumberApiService numberApiService;

    @Autowired
    private RevisionOrderCalculationHelper revisionOrderCalculationHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectMaterialPreparationVO detail(String id, String pageCode) throws Exception {
        ProjectMaterialPreparation projectMaterialPreparation =this.getById(id);
        ProjectMaterialPreparationVO result = BeanCopyUtils.convertTo(projectMaterialPreparation,ProjectMaterialPreparationVO::new);
        List<ProjectMaterialPreparationInfoVO> list = projectMaterialPreparationInfoService.getList(id);
        result.setPreparationInfoList(list);
        return result;
    }

    /**
     *  新增
     *
     * * @param projectMaterialPreparationDTO
     */
    @Override
    public  String create(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, Boolean submit) throws Exception {
        ProjectMaterialPreparation projectMaterialPreparation =BeanCopyUtils.convertTo(projectMaterialPreparationDTO,ProjectMaterialPreparation::new);
        List<ProjectMaterialPreparation> list = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparation.class)
                .select(ProjectMaterialPreparation::getId)
                .eq(ProjectMaterialPreparation::getProjectId, projectMaterialPreparation.getProjectId())
                .eq(ProjectMaterialPreparation::getIsMainRev, true)
                .eq(ProjectMaterialPreparation::getMaterialNumber, projectMaterialPreparation.getMaterialNumber()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该产品/电路板已经存在备料与加工申请，请去相应单据进行变更。");
        }

        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(ProjectMaterialPreparationInfo.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String generate = numberApiService.generate(generateNumberRequest);
        if (StrUtil.isBlank(generate)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "编码生成失败!");
        }
        projectMaterialPreparation.setRevKey(IdUtil.randomUUID());
        projectMaterialPreparation.setRevId("A");
        projectMaterialPreparation.setNumber(generate);
        projectMaterialPreparation.setIsMainRev(true);
        this.save(projectMaterialPreparation);

        String rsp=projectMaterialPreparation.getId();
        List<ProjectMaterialPreparationInfoDTO> preparationInfoDTOS = projectMaterialPreparationDTO.getPreparationInfoList();
        if (CollectionUtil.isNotEmpty(preparationInfoDTOS)) {
            projectMaterialPreparationInfoService.create(preparationInfoDTOS, projectMaterialPreparation.getRequireCompleteTime(), rsp);
        }
        if (submit) {
            wfInstanceProjectMaterialPreparationManage.process(rsp, CurrentUserHelper.getOrgId());
        }


        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectMaterialPreparationDTO
     */
    @Override
    public Boolean edit(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, Boolean submit) throws Exception {
        ProjectMaterialPreparation projectMaterialPreparation =BeanCopyUtils.convertTo(projectMaterialPreparationDTO,ProjectMaterialPreparation::new);
        List<ProjectMaterialPreparation> list = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparation.class)
                .select(ProjectMaterialPreparation::getId)
                .eq(ProjectMaterialPreparation::getProjectId, projectMaterialPreparation.getProjectId())
                .eq(ProjectMaterialPreparation::getMaterialNumber, projectMaterialPreparation.getMaterialNumber())
                .eq(ProjectMaterialPreparation::getIsMainRev, true)
                .ne(ProjectMaterialPreparation::getId, projectMaterialPreparationDTO.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该产品/电路板已经存在备料与加工申请，请去相应单据进行变更。");
        }
        if (submit){
            ProjectMaterialPreparation byId = this.getById(projectMaterialPreparation.getId());
            if (!ProjectMaterialPreparationStatus.CREATE.getCode().equals(byId.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "只有已创建状态才能启动流程。");
            }
        }
        this.updateById(projectMaterialPreparation);

        String rsp=projectMaterialPreparation.getId();
        List<ProjectMaterialPreparationInfoDTO> preparationInfoDTOS = projectMaterialPreparationDTO.getPreparationInfoList();
        projectMaterialPreparationInfoService.edit(preparationInfoDTOS, projectMaterialPreparation.getRequireCompleteTime(), rsp);
        if (submit) {
            wfInstanceProjectMaterialPreparationManage.process(rsp, CurrentUserHelper.getOrgId());
        }

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        projectMaterialPreparationInfoService.remove(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                .in(ProjectMaterialPreparationInfo::getPreparationId, ids));
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectMaterialPreparationVO> pages(Page<ProjectMaterialPreparationDTO> pageRequest) throws Exception {
        ProjectMaterialPreparationDTO query = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id为空");
        }
        LambdaQueryWrapperX<ProjectMaterialPreparation> condition = new LambdaQueryWrapperX<>( ProjectMaterialPreparation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectMaterialPreparation::getCreateTime);
        condition.eq(ProjectMaterialPreparation::getProjectId, query.getProjectId());
        condition.eq(ProjectMaterialPreparation::getIsMainRev, true);

        Page<ProjectMaterialPreparation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        realPageRequest.setQuery(BeanCopyUtils.convertTo(query, ProjectMaterialPreparation::new));

        PageResult<ProjectMaterialPreparation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectMaterialPreparationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectMaterialPreparationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectMaterialPreparationVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }





    public static class ProjectMaterialPreparationExcelListener extends AnalysisEventListener<ProjectMaterialPreparationDTO> {

        private final List<ProjectMaterialPreparationDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectMaterialPreparationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectMaterialPreparationDTO> getData() {
            return data;
        }
    }

    @Override
    public String upgrade(ProjectMaterialPreparationDTO projectMaterialPreparationDTO, Boolean submit) throws Exception {
        String id = projectMaterialPreparationDTO.getId();
        ProjectMaterialPreparation byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        byId.setId(null);
        byId.setCreatorId(null);
        byId.setCreateTime(null);
        byId.setStatus(null);
        byId.setIsMainRev(true);
        byId.setRevId(revisionOrderCalculationHelper.AToN(byId.getRevId()));
        this.save(byId);
        String rsp = byId.getId();
        this.update(new LambdaUpdateWrapper<>(ProjectMaterialPreparation.class)
                .set(ProjectMaterialPreparation::getIsMainRev, false)
                .eq(ProjectMaterialPreparation::getId, projectMaterialPreparationDTO.getId()));

        List<ProjectMaterialPreparationInfoDTO> preparationInfoList = projectMaterialPreparationDTO.getPreparationInfoList();
        if (CollectionUtil.isNotEmpty(preparationInfoList)) {
            projectMaterialPreparationInfoService.upgrade(preparationInfoList, projectMaterialPreparationDTO.getRequireCompleteTime(),
                    projectMaterialPreparationDTO.getId(), rsp);
        }
        if (submit) {
            wfInstanceProjectMaterialPreparationManage.process(rsp, CurrentUserHelper.getOrgId());
        }
        return rsp;
    }

    @Override
    public List<ProjectMaterialPreparationVO> getAllRevList(String revKey) throws Exception {
        List<ProjectMaterialPreparation> list = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparation.class)
                .eq(ProjectMaterialPreparation::getRevKey, revKey)
                .orderByAsc(ProjectMaterialPreparation::getRevId));
        return BeanCopyUtils.convertListTo(list, ProjectMaterialPreparationVO::new);
    }

    @Override
    public Map<String, Integer> getPreparationNum(String projectId, List<String> materialNumberList) {
        Map<String, Integer> preparationNumMap = projectMaterialPreparationInfoService.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                .innerJoin(ProjectMaterialPreparation.class, ProjectMaterialPreparation::getId, ProjectMaterialPreparationInfo::getPreparationId)
                .select(ProjectMaterialPreparationInfo::getMaterialNumber, ProjectMaterialPreparationInfo::getNum)
                .in(ProjectMaterialPreparationInfo::getMaterialNumber, materialNumberList)
                .eq(ProjectMaterialPreparation::getProjectId, projectId)
                .eq(ProjectMaterialPreparation::getIsMainRev, true))
                .stream().collect(Collectors.groupingBy(ProjectMaterialPreparationInfo::getMaterialNumber,
                        Collectors.mapping(ProjectMaterialPreparationInfo::getNum, Collectors.summingInt(s -> s))));
        return preparationNumMap;
    }

    @Override
    public Page<ProjectMaterialPlanPreparationVO> pageByMaterialPlan(Page<ProjectMaterialPlanPreparationDTO> pageRequest) throws Exception {
        ProjectMaterialPlanPreparationDTO query = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getMaterialNumber()) || StrUtil.isBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id或物料编码未传");
        }
        LambdaQueryWrapperX<ProjectMaterialPreparation> condition = new LambdaQueryWrapperX<>(ProjectMaterialPreparation.class);
        condition.innerJoin(ProjectMaterialPreparationInfo.class, ProjectMaterialPreparationInfo::getPreparationId, ProjectMaterialPreparation::getId)
                .select(ProjectMaterialPreparation::getId, ProjectMaterialPreparation::getNumber, ProjectMaterialPreparation::getRequireCompleteTime,
                        ProjectMaterialPreparation::getCreateTime, ProjectMaterialPreparation::getCreatorId)
                .selectSum(ProjectMaterialPreparationInfo::getNum, ProjectMaterialPreparation::getPreparationNum)
                .eq(ProjectMaterialPreparationInfo::getMaterialNumber, query.getMaterialNumber())
                .eq(ProjectMaterialPreparation::getProjectId, query.getProjectId())
                .eq(ProjectMaterialPreparation::getIsMainRev, true)
                .orderByDesc(ProjectMaterialPreparation::getCreateTime)
                .groupBy(ProjectMaterialPreparation::getId);
        PageResult<ProjectMaterialPreparation> page = this.getBaseMapper().selectPage(pageRequest, condition);
        List<ProjectMaterialPreparation> content = page.getContent();
        Page<ProjectMaterialPlanPreparationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        pageResult.setContent(BeanCopyUtils.convertListTo(content, ProjectMaterialPlanPreparationVO::new));
        return pageResult;

    }
}
