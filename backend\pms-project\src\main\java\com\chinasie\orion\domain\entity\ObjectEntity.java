package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.BusinessOrgDataBind;
import com.chinasie.orion.sdk.core.data.bind.StatusDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:34
 * @description:
 */
@Data
@TableName(value = "pms_object")
@ApiModel(value = "ObjectEntity对象", description = "Pmsx 基类")
public class ObjectEntity implements Serializable {
    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "creatorName")
    private String creatorId;

    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String creatorName;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "ownerName")
    private String ownerId;

    @ApiModelProperty("拥有者名字")
    @TableField(exist = false)
    private String ownerName;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @FieldBind(dataBind = UserDataBind.class, target = "modifyName")
    private String modifyId;

    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String modifyName;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = BusinessOrgDataBind.class, target = "businessOrgName")
    private String orgId;

    @ApiModelProperty("业务组织名称")
    @TableField(exist = false)
    private String businessOrgName;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    @FieldBind(dataBind = StatusDataBind.class, target = "dataStatus")
    private Integer status;

    @ApiModelProperty("状态对象")
    @TableField(exist = false)
    private DataStatusVO dataStatus;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;


    @ApiModelProperty(value = "排序字段")
    @TableField(value = "sort")
    private Long sort;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;
}
