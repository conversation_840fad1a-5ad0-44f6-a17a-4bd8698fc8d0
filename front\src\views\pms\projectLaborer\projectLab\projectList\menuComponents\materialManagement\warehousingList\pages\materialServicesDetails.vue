<template>
  <Layout3
    :defaultActionId="tabsIndex"
    :projectData="projectData.headerData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="contentTabsChange"
  >
    <template #header-right>
      <BasicTableAction
        v-if="Number(projectInfo?.dataStatus?.statusValue) === 120"
        :actions="rightBtnActions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectInfo?.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <MaterialServices
      v-if="tabsIndex==='WZFWXX_01'"
      :detailsData="projectData.detailsData"
    />
    <WorkflowView
      v-if="tabsIndex==='process'"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
    <WarehousingListDrawer
      @upTableDate="upTableDate"
      @register="modalWarehousingListRegister"
    />
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, ref, onMounted, Ref, ComputedRef, watchEffect,
} from 'vue';
import {
  Layout3, ITableActionItem, useDrawer, BasicTableAction,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { getWarehousingDetails } from '/@/views/pms/api';
import MaterialServices from './components/materialServices.vue';
import WarehousingListDrawer from '../components/warehousingListDrawer.vue';
import login from '/@/views/sys/login/Login.vue';
import { setTitleByRootTabsKey } from '/@/utils';
const [modalWarehousingListRegister, { openDrawer: openWarehousingListDrawer }] = useDrawer();
const route = useRoute();
const tabsIndex = ref('WZFWXX_01'); // 选中的项index
const menuData = ref([]);// 右边菜单选项
const projectData = ref({
  headerData: {},
  detailsData: {},
  id: '',
});
const projectInfo = ref({} as any);
const processRef: Ref = ref();
const processViewRef:Ref = ref();
function getDetailsData() {
  getWarehousingDetails(route.query?.id).then((res) => {
    projectInfo.value = res;
    processRef.value?.setProps({
      businessData: res,
    });
    setTitleByRootTabsKey(route?.query?.rootTabsKey, res.description);
    projectData.value.headerData = {
      name: res.description,
      // projectCode: `计划编号:${res.planNumber}`,
      projectCode: res.planNumber ? `计划编号:${res.planNumber}` : '',
      ownerName: res.ownerName,
      dataStatus: res.dataStatus,
    };
    projectData.value.detailsData = res;
  });
}
onMounted(() => {
  menuData.value.push({
    name: '物资/服务信息',
    id: 'WZFWXX_01',
  }, {
    name: '流程',
    id: 'process',
  });
  getWarehousingDetails(route.query?.id).then((res) => {
    projectInfo.value = res;
    processRef.value?.setProps({
      businessData: res,
    });
    projectData.value.headerData = {
      name: res.description,
      projectCode: res.planNumber ? `计划编号:${res.planNumber}` : '',
      ownerName: res.ownerName,
      dataStatus: res.dataStatus,
    };
    projectData.value.detailsData = res;
  });
});
const workflowProps:ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: {
    ...projectData.value.detailsData,
    name: projectData.value.detailsData.description,
  },
  afterEvent: (type, props) => {
    processViewRef.value?.init();
  },
}));

const rightBtnActions:Ref<ITableActionItem[]> = computed(() => [
  {
    text: '编辑',
    // isShow: computed(() => Number(projectInfo.value?.dataStatus?.statusValue) === 120),
    onClick() {
      openWarehousingListDrawer(true, {
        type: 'edit',
        itemData: projectData.value.detailsData,
        projectId: route.query?.projectId,
      });
    },
  },
  (processRef.value?.isAdd ? {
    text: '添加流程',
    icon: 'sie-icon-tianjiaxinzeng',
    // isShow: computed(() => Number(projectInfo.value?.dataStatus?.statusValue) === 120),
    onClick() {
      processRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  } : undefined),

].filter((item) => item));
function upTableDate() {
  getDetailsData();
}
const contentTabsChange = (index) => {
  tabsIndex.value = index.id;
};
</script>
<style scoped lang="less">
</style>
