<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="modalDetails checkDrawer"
    title="查看基本信息"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="modalDetails_main"
    >
      <div class="modalDetails_content">
        <div class="viewTitle">
          <div class="rowItem titleLabel">
            {{ formData.name }}
          </div>
          <div class="rowItem">
            <div class="rowCell">
              <div class="rowCell_icon icon_user">
                <i class="orion-icon-user" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ formData.creatorName }}
                </div>
                <div class="val_bot">
                  创建人
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-hourglass" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  <DataStatusTag :status-data="formData.dataStatus" />
                </div>
                <div class="val_bot">
                  状态
                </div>
              </div>
            </div>

            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div
                  class="val_top"
                  :class="{ red: formData.priorityLevelName === '最高' }"
                >
                  {{ formData.priorityLevelName }}
                </div>
                <div class="val_bot">
                  优先级
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ stampDate(formData.modifyTime) }}
                </div>
                <div class="val_bot">
                  修改时间
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <BasicTabs
        v-model:tabsIndex="tabsIndex"
        :tabs="tabs"
        @tabsChange="tabsChange"
      />
      <div
        class="modalDetailsPage"
      >
        <Summarize
          v-if="actionId===3333331"
          :id="formId"
          pageType="modal"
        />
        <Document
          v-if="actionId===3333332"
          :id="formId"
          pageType="modal"
        />
        <Flow
          v-if="actionId===3333333"
          :id="formId"
          pageType="modal"
        />
        <Colophon
          v-if="actionId===3333334"
          pageType="modal"
          :revKey="formData.revKey"
        />
        <!--    变更管理-->
        <ChangeApply
          v-if="actionId===3333335"
          :formId="formId"
          ecrDirName="项目"
          pageType="modal"
          style="width: 100%"
        />
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, nextTick, provide, ref, readonly,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicTabs, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';

import Summarize from '../menuComponents/planManagement/deliverDetails/summarize/index.vue';
import Document from '../menuComponents/planManagement/deliverDetails/document/index.vue';
import Colophon from '../menuComponents/planManagement/deliverDetails/colophon/index.vue';
import Flow from '../menuComponents/planManagement/deliverDetails/flow/index.vue';
import { ChangeApply } from '../menuComponents/ChangeApply';
export default defineComponent({
  name: 'DeliverableModal',
  components: {
    BasicDrawer,
    BasicTabs,
    DataStatusTag,
    Summarize,
    Document,
    Colophon,
    Flow,
    ChangeApply,
  },
  setup(props, { emit }) {
    const state:any = reactive({
      formData: {} as any,
      formId: '',
      showTabs: true,
      loading: false,
      tabsIndex: 0,
      actionId: '',
      provideProjectId: '',
      tabs: [
        {
          name: '概述',
          id: 3333331,
        },
        {
          name: '交付物文件',
          id: 3333332,
        },
        {
          name: '变更管理',
          id: 3333335,
        },
        {
          name: '流程',
          id: 3333333,
        },
        {
          name: '版本记录',
          id: 3333334,
        },
      ],
    });
    const [modalRegister, { closeDrawer, setDrawerProps, changeLoading }] = useDrawerInner((drawerData) => {
      state.formId = drawerData.id;
      getFormData(drawerData.id);
    });

    // provide('provideProjectId', readonly(provideProjectId));
    function getFormData(id) {
      new Api(`/pms/deliverable/${id}`).fetch('', '', 'GET').then((data) => {
        state.formData = data;
        nextTick(() => {
          state.tabsIndex = 0;
          state.actionId = state.tabs[0].id;
        });
      });
    }
    provide(
      'projectInfo',
      computed(() => state.formData),
    );
    const tabsChange = (index, item) => {
      state.actionId = item.id;
    };
    onMounted(() => {
    });

    const visibleChange = (val) => {
      if (!val) {
        state.actionId = '';
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };

    return {
      ...toRefs(state),
      modalRegister,
      stampDate,
      tabsChange,
      visibleChange,
    };
  },
});

</script>
<style lang="less">
.modalDetails{
  .ant-drawer-body{
    padding:0px;
    .scrollbar__view{
      height: 100%;
    }
  }
  .modalDetails_main{
    display: flex;
    height: 100%;
    flex-direction: column;
  }
  .modalDetails_content{
    padding: 10px;
    .fa {
      font-family: 'FontAwesome';
    }
    .viewTitle {
      * {
        font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
      }
      padding-bottom: 20px;
      border-bottom: 1px dashed #e4e4e7;
      .titleLabel {
        font-weight: 700;
        font-style: normal;
        font-size: 20px;
        color: #000000;
        height: 60px;
        line-height: 60px;
      }
      .rowItem {
        display: flex;
        .rowCell {
          display: flex;
          width: 250px;
          .rowCell_icon {
            height: 40px;
            width: 40px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 5px;
          }
          .icon_user {
            background: #6e72fb;
            color: #ffffff;
          }
          .icon_calendar {
            background: #f1f4fd;
            color: #5678dd;
          }
          .rowCell_val {
            .val_top {
              font-weight: 500;
              font-style: normal;
              font-size: 16px;
              height: 25px;
            }
            .val_bot {
              font-weight: 400;
              font-style: normal;
              font-size: 12px;
              color: #686f8b;
            }
          }
        }
      }
    }
  }
  .tabs-main{
    padding: 0px 10px;
  }
  .modalDetailsPage{
    flex: 1;
    display: flex;
  }
}

</style>
