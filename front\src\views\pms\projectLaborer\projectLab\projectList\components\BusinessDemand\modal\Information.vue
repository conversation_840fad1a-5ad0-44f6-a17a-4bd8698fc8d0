<template>
  <div class="materialinformation">
    <div class="information">
      <div class="information_content">
        <div class="information_msg">
          <span>需求信息</span>
        </div>
        <ARow>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              编号：
            </div>
            <div class="labelVal flex-te">
              {{ formData.number }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              标题：
            </div>
            <div class="labelVal flex-te">
              {{ formData.name }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              需求类型：
            </div>
            <div class="labelVal flex-te">
              {{ formData.typeName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              父级需求：
            </div>
            <div class="labelVal flex-te">
              {{ formData.parentName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              状态：
            </div>
            <div class="labelVal flex-te">
              <DataStatusTag :status-data="formData.dataStatus" />
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              需求来源：
            </div>
            <div class="labelVal flex-te">
              {{ formData.sourceName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              提出人：
            </div>
            <div class="labelVal flex-te">
              {{ formData.exhibitorName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              提出日期：
            </div>
            <div class="labelVal flex-te">
              {{ formData.proposedTime?stampDate(formData.proposedTime, 'yyyy-MM-dd'):'' }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              优先级：
            </div>
            <div class="labelVal flex-te">
              {{ formData.priorityLevelName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              负责人：
            </div>
            <div class="labelVal flex-te">
              {{ formData.principalName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              期望完成时间：
            </div>
            <div class="labelVal flex-te">
              {{ formData.predictEndTime?stampDate(formData.predictEndTime, 'yyyy-MM-dd'):'' }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              进度：
            </div>
            <div class="labelVal flex-te">
              <!--              {{ formData?.scheduleName!=null? formData?.scheduleName+'%':'' }}-->
              {{ formData?.scheduleName ?? '0%' }}
            </div>
          </ACol>
          <ACol :span="24">
            <div class="labelMsg flex-te">
              描述：
            </div>
            <div class="labelVal flex-te3">
              {{ formData.remark }}
            </div>
          </ACol>
        </ARow>
      </div>
      <div
        v-if="listData.length>0"
        class="information_content"
      >
        <div class="information_msg">
          <span>类型属性</span>
        </div>
        <ARow>
          <template
            v-for="item in listData"
            :key="item.id"
          >
            <ACol :span="pageType==='page'?6:8">
              <div class="labelMsg flex-te">
                {{ item.name }}：
              </div>
              <div class="labelVal flex-te">
                {{ formData[item.number] }}
              </div>
            </ACol>
          </template>
        </ARow>
      </div>
      <div
        class="information_content"
      >
        <div class="information_msg">
          <span>基础信息</span>
        </div>
        <ARow>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              创建人：
            </div>
            <div class="labelVal flex-te">
              {{ formData.creatorName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              创建时间：
            </div>
            <div class="labelVal flex-te">
              {{ formData.createTime?stampDate(formData.createTime, 'yyyy-MM-dd HH:mm:ss'):'' }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              修改人：
            </div>
            <div class="labelVal flex-te">
              {{ formData.modifyName }}
            </div>
          </ACol>
          <ACol :span="pageType==='page'?6:8">
            <div class="labelMsg flex-te">
              修改时间：
            </div>
            <div class="labelVal flex-te">
              {{ formData.modifyTime?stampDate(formData.modifyTime, 'yyyy-MM-dd HH:mm:ss'):'' }}
            </div>
          </ACol>
        </ARow>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, inject, watch,
} from 'vue';
import {
  Row, Col, Image,
} from 'ant-design-vue';
import { stampDate } from '/@/utils/dateUtil';
import { DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';
export default defineComponent({
  name: 'Information',
  components: {
    ARow: Row,
    ACol: Col,
    DataStatusTag,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['editNode'],
  setup(_, { emit }) {
    const powerData: any = inject('powerData', {});
    const formData: any = inject('formData', {});
    const state:any = reactive({
      formData: formData?.value || {},
      listData: [],
      btnList: [
        {
          type: 'edit',
          powerCode: 'CPGL_container_button_12',
        },
      ],
    });
    function getTypeList() {
      new Api('/pas').fetch({ status: 1 }, `demand-type-to-demand-type-attribute/list/${state.formData.type}`, 'GET').then((res) => {
        state.listData = res;
      });
    }
    onMounted(() => {
      if (JSON.stringify(state.formData) != '{}') {
        getTypeList();
      }
    });
    watch(
      () => formData?.value,
      (val) => {
        state.formData = val;
        getTypeList();
      },
    );
    const clickType = (type) => {
      emit('editNode');
    };
    return {
      ...toRefs(state),
      stampDate,
      clickType,
      powerData,
    };
  },
});
</script>
<style lang="less" scoped>
.materialinformation{
  display: flex;
  height: 100%;
  .information{
    * {
      font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
    }
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
    padding: 0px 20px;
    .information_content{
      .information_msg{
        font-size: 0px;
        padding: 15px 0px;
        &:before{
          content: ' ';
          width: 4px;
          display: inline-block;
          background: #6e72fb;
          height: 22px;
          vertical-align: middle;
        }
        span{
          font-weight: 700;
          vertical-align: middle;
          font-style: normal;
          font-size:16px;
          padding-left: 10px;
        }
      }
      .ant-col{
        margin-bottom:10px;
        display: flex;
        .labelMsg{
          color: #686F8B;
          width: 100px;
          display: inline-block;
          vertical-align: middle;
        }
        .labelVal{
          flex: 1;
        }
      }
      .imgList{
        margin-bottom: 10px;
        display: flex;
        :deep(.ant-image){
          margin-right: 10px;
        }
        :deep(.ant-image-img){
          height: 180px;
        }
      }
    }
  }
}
</style>
