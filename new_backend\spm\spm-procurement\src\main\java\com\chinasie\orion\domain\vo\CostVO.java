package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CostVO implements Serializable {


    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    public String contractNumber;

    /**
     * 用人单位代号
     */
    @ApiModelProperty("用人单位代号")
    public String centerCode;

    /**
     * 计划成本
     */
    @ApiModelProperty("计划成本")
    public BigDecimal totalBudget;

    /**
     * 实际成本
     */
    @ApiModelProperty("实际成本")
    public BigDecimal actualMoney;

    /**
     * 实际人数
     */
    @ApiModelProperty("实际人数")
    public Integer personNum;

    /**
     * 计划人数
     */
    @ApiModelProperty("计划人数")
    public Integer planPersonNum;

    /**
     * 成本类型
     */
    @ApiModelProperty("成本类型")
    public String costType;

    /**
     * 成本类型名称
     */
    @ApiModelProperty("成本类型名称")
    public String costTypeName;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    public BigDecimal unitPrice;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    public Integer status;

    public CostVO() {
        this.totalBudget = BigDecimal.valueOf(0);
        this.actualMoney = BigDecimal.valueOf(0);
    }
}
