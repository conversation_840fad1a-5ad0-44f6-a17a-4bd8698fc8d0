package com.chinasie.orion.controller.review;

import com.chinasie.orion.domain.dto.review.ReviewLibraryDTO;
import com.chinasie.orion.domain.vo.review.ReviewLibraryVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewLibraryService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ReviewLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@RestController
@RequestMapping("/reviewLibrary")
@Api(tags = "评审要点库")
public class ReviewLibraryController {

    @Autowired
    private ReviewLibraryService reviewLibraryService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审要点库】数据详情【{{#number}}】", type = "Review", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ReviewLibraryVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ReviewLibraryVO rsp = reviewLibraryService.detail(id,pageCode);
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param reviewLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【评审要点库】数据【{{#reviewLibraryDTO.name}}】", type = "ReviewLibrary", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Valid ReviewLibraryDTO reviewLibraryDTO) throws Exception {
        String rsp =  reviewLibraryService.create(reviewLibraryDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param reviewLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【评审要点库】数据【{{#reviewLibraryDTO.name}}】", type = "ReviewLibrary", subType = "编辑", bizNo = "{{#reviewLibraryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ReviewLibraryDTO reviewLibraryDTO) throws Exception {
        Boolean rsp = reviewLibraryService.edit(reviewLibraryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【评审要点库】数据", type = "ReviewLibrary", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = reviewLibraryService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【评审要点库】数据", type = "ReviewLibrary", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewLibraryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审要点库】分页数据", type = "ReviewLibrary", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ReviewLibraryVO>> pages(@RequestBody Page<ReviewLibraryDTO> pageRequest) throws Exception {
        Page<ReviewLibraryVO> rsp =  reviewLibraryService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取列表")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【评审要点库】数据列表", type = "ReviewLibrary", subType = "列表", bizNo = "")
    public ResponseDTO<List<ReviewLibraryVO>> getList() throws Exception {
        List<ReviewLibraryVO> rsp = reviewLibraryService.getList();
        return new ResponseDTO(rsp);
    }


}
