package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.InTransactionPreReconciliationExportDTO;
import com.chinasie.orion.domain.dto.IncomePlanDataExportDTO;
import com.chinasie.orion.domain.dto.IncomePlanExecutionTrackDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.domain.vo.IncomeProvisionInformationVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.InTransactionPreReconciliationMapper;
import com.chinasie.orion.repository.IncomePlanDataMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InTransactionPreReconciliationServiceImpl implements InTransactionPreReconciliationService {
    @Resource
    private InTransactionPreReconciliationMapper inTransactionPreReconciliationMapper;

    @Autowired
    private UserBaseApiService userBaseApiService;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DictBo dictBo;

    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;

    @Autowired
    private MarketContractCustContactService marketContractCustContactService;

    @Resource
    private DeptDOMapper deptDOMapper;

    @Resource
    private DeptLeaderDORepository deptLeaderDORepository;

    @Resource
    private RoleDOMapper roleDOMapper;


    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;

    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;

    @Autowired
    private RoleUserHelper roleUserHelper;


    public Page<InTransactionPreReconciliationVO> getPages(Page<InTransactionPreReconciliationDTO> page){


        com.baomidou.mybatisplus.extension.plugins.pagination.Page realPageRequest = new
                com.baomidou.mybatisplus.extension.plugins.pagination.Page(page.getPageNum(), page.getPageSize());

        InTransactionPreReconciliationDTO inTransactionPreReconciliationDTO = page.getQuery();
        if(ObjectUtil.isEmpty(inTransactionPreReconciliationDTO)){
            inTransactionPreReconciliationDTO = new InTransactionPreReconciliationDTO();
        }
        List<String> centers = new ArrayList<>();
        List<String> stations = new ArrayList<>();
        Boolean isPermission = getPermission(centers,stations);
        inTransactionPreReconciliationDTO.setIsPermission(isPermission);
        inTransactionPreReconciliationDTO.setCenters(centers);
        inTransactionPreReconciliationDTO.setStations(stations);
        inTransactionPreReconciliationDTO.setUserId(CurrentUserHelper.getCurrentUserId());
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<InTransactionPreReconciliationVO> incomePlanDataVOS
                = inTransactionPreReconciliationMapper.getDatas(inTransactionPreReconciliationDTO,realPageRequest);
        Page<InTransactionPreReconciliationVO> pageResult = new Page<>(incomePlanDataVOS.getCurrent(), incomePlanDataVOS.getPages(), incomePlanDataVOS.getTotal());
        if(CollUtil.isEmpty(incomePlanDataVOS.getRecords())){
            return pageResult;
        }
        List<InTransactionPreReconciliationVO> inTransactionPreReconciliationVOS = incomePlanDataVOS.getRecords();
        setEveryName(inTransactionPreReconciliationVOS);
        pageResult.setContent(inTransactionPreReconciliationVOS);
        return pageResult;
    }

    @Override
    public void exportByExcel(InTransactionPreReconciliationDTO inTransactionPreReconciliationDTO, HttpServletResponse response) throws Exception {
        List<String> centers = new ArrayList<>();
        List<String> stations = new ArrayList<>();
        Boolean isPermission = getPermission(centers,stations);
        inTransactionPreReconciliationDTO.setIsPermission(isPermission);
        inTransactionPreReconciliationDTO.setCenters(centers);
        inTransactionPreReconciliationDTO.setStations(stations);
        List<InTransactionPreReconciliationVO> inTransactionPreReconciliationVOS = inTransactionPreReconciliationMapper.getExportDatas(inTransactionPreReconciliationDTO);
        setEveryName(inTransactionPreReconciliationVOS);

        List<InTransactionPreReconciliationExportDTO> dtos = BeanCopyUtils.convertListTo(inTransactionPreReconciliationVOS,InTransactionPreReconciliationExportDTO::new);

        String fileName = "内部交易预对账明细表.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", InTransactionPreReconciliationExportDTO.class, dtos);
    }

    public Boolean getPermission(List<String> centers,List<String> stations){
        String userId = CurrentUserHelper.getCurrentUserId();
        //获取公司领导
        LambdaQueryWrapperX<DeptDO> deptQueryWrapperx = new LambdaQueryWrapperX(DeptDO.class);
        deptQueryWrapperx.in(DeptDO::getDeptCode, "00014500", "00014505");
        List<DeptDO> deptDO = deptDOMapper.selectList(deptQueryWrapperx);
        List<String> ids = deptDO.stream().map(DeptDO::getId).collect(Collectors.toList());
        List<DeptLeaderDO> deptLeaderDO = new ArrayList<>();
        if(CollUtil.isNotEmpty(ids)) {
            LambdaQueryWrapperX<DeptLeaderDO> queryWrapperX = new LambdaQueryWrapperX(DeptLeaderDO.class);
            queryWrapperX.eq(DeptLeaderDO::getType, "group");
            queryWrapperX.in(DeptLeaderDO::getDeptId, ids);
            queryWrapperX.eq(DeptLeaderDO::getUserId, userId);
            deptLeaderDO = deptLeaderDORepository.selectList(queryWrapperX);
        }
        //获取财务管理员角色
        LambdaQueryWrapperX<RoleDO> roleWrapperX = new LambdaQueryWrapperX(RoleDO.class);
        roleWrapperX.eq(RoleDO::getCode, "FIN001");
        RoleDO roleDO = roleDOMapper.selectOne(roleWrapperX);
        List<String> userIds = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(roleDO)) {
            userIds = roleUserHelper.getUserIdsOfRoleId(CurrentUserHelper.getCurrentUserId(), roleDO.getId());
        }
        if (CollUtil.isEmpty(deptLeaderDO) && !userIds.contains(userId)) {

            List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                    .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
            );
            if (CollUtil.isEmpty(detailList)) {
                List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                        .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
                );
                stations.addAll(station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList()));

                List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                        .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
                );
                centers.addAll(center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList()));
                return false;
            }
            return true;
        }else{
            return true;
        }
    }

    public void setEveryName(List<InTransactionPreReconciliationVO> inTransactionPreReconciliationVOS){
        if(CollUtil.isEmpty(inTransactionPreReconciliationVOS)){
            return;
        }
        List<String> userIds = new ArrayList<>();
        Map<String,String> userNameMap = new HashMap<>();
        Map<String,String> deptNameMap = new HashMap<>();
        //Map<String,CustomerInfo> cusMap = new HashMap<>();

        Map<String, Map<String, String>> marketContractCustContactMap = new HashMap<>();


        Map<String,String> billingRecognitionCompanyDictMap = dictBo.getDictValue(IncomePlanDict.BILLING_RECOGNITION_COMPANY);

        Map<String,String> marketContractTypeDictMap = dictBo.getDictValue(IncomePlanDict.MARKET_CONTRACT_TYPE);
        Map<String, String> industryDict = dictBo.getDictValue(IncomePlanDict.CUSTOMER_INDUSTRY);

        List<DataStatusVO>  statusVOS =statusRedisHelper.getStatusInfoListByClassName(MarketContract.class.getSimpleName());
        Map<Integer,String> statusMap =  statusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue,DataStatusVO::getName));

        List<String> deptIds = new ArrayList<>();

        //List<String> cusIds = new ArrayList<>();

        List<String> contractIds = new ArrayList<>();


        for(InTransactionPreReconciliationVO item:inTransactionPreReconciliationVOS){
            if(StrUtil.isNotBlank(item.getExpertiseCenter())){
                deptIds.add(item.getExpertiseCenter());
            }
            if(StrUtil.isNotBlank(item.getExpertiseStation())){
                deptIds.add(item.getExpertiseStation());
            }
            if(StrUtil.isNotBlank(item.getTechRspUser())){
                userIds.add(item.getTechRspUser());
            }
            if(StrUtil.isNotBlank(item.getBusRspUser())){
                userIds.add(item.getBusRspUser());
            }
//            if(StrUtil.isNotBlank(item.getPartyADeptId())){
//                cusIds.add(item.getPartyADeptId());
//            }
            if(StrUtil.isNotBlank(item.getContractId())){
                contractIds.add(item.getContractId());
            }

        }

        if(CollUtil.isNotEmpty(contractIds)){
//            List<ContractOurSignedSubject> contractOurSignedSubjects  =  contractOurSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractOurSignedSubject.class)
//                    .in(ContractOurSignedSubject::getContractId,contractIds));

            List<MarketContractCustContact> marketContractCustContacts  =  marketContractCustContactService.list(new LambdaQueryWrapperX<>(MarketContractCustContact.class)
                    .in(MarketContractCustContact::getContractId,contractIds));

//            if(CollUtil.isNotEmpty(contractOurSignedSubjects)){
//                contractOurSignedSubjectMap = contractOurSignedSubjects.stream().filter(item->StrUtil.isNotBlank(item.getCusContractNumber()))
//                        .collect(Collectors.groupingBy(
//                                ContractOurSignedSubject::getContractId,
//                                Collectors.mapping(ContractOurSignedSubject::getCusContractNumber, Collectors.joining(", "))
//                        ));
//            }

            if(CollUtil.isNotEmpty(marketContractCustContacts)){
                userIds.addAll(marketContractCustContacts.stream().filter(item->StrUtil.equals(item.getContactType(),"business")||StrUtil.equals(item.getContactType(),"technology")).map(MarketContractCustContact::getCustContactId).collect(Collectors.toList()));
                marketContractCustContactMap = marketContractCustContacts.stream().filter(item->StrUtil.equals(item.getContactType(),"business")||StrUtil.equals(item.getContactType(),"technology"))
                        .collect(Collectors.groupingBy(
                                myObject -> myObject.getContractId(), // 第一级分组依据
                                Collectors.toMap(
                                        MarketContractCustContact::getContactType, // 第二级分组依据
                                        myObject -> myObject.getCustContactId(), // 映射到对象
                                        (existing, replacement) -> existing // 如果有重复的键，保留现有的对象（即第一个）
                                )
                        ));
            }
        }
        if(CollUtil.isNotEmpty(userIds)){
            userIds = userIds.stream().distinct().collect(Collectors.toList());
            List<SimpleUserVO> simpleUsers = userBaseApiService.getUserByIds(userIds);
            userNameMap = simpleUsers.stream().collect(Collectors.toMap(SimpleUserVO::getId,SimpleUserVO::getName));
        }
        if(CollUtil.isNotEmpty(deptIds)){
            deptIds = deptIds.stream().distinct().collect(Collectors.toList());
            List<DeptVO> deptVOS = deptRedisHelper.getDeptByIds(deptIds);
            deptNameMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId,DeptVO::getName));
        }

//        if(CollUtil.isNotEmpty(cusIds)){
//            cusIds = cusIds.stream().distinct().collect(Collectors.toList());
//            List<CustomerInfo> customerInfos = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).in(ContractMilestone::getId, cusIds));
//            cusMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
//        }

        for(InTransactionPreReconciliationVO item:inTransactionPreReconciliationVOS){
            if(StrUtil.isNotBlank(item.getExpertiseCenter())){
                item.setExpertiseCenterName(deptNameMap.get(item.getExpertiseCenter()));
            }
            if(StrUtil.isNotBlank(item.getExpertiseStation())){
                item.setExpertiseStationName(deptNameMap.get(item.getExpertiseStation()));
            }
            if(StrUtil.isNotBlank(item.getTechRspUser())){
                item.setTechRspUserName(userNameMap.get(item.getTechRspUser()));
            }
            if(StrUtil.isNotBlank(item.getBusRspUser())){
                item.setBusRspUserName(userNameMap.get(item.getBusRspUserName()));
            }
//            if(StrUtil.isNotBlank(item.getPartyADeptId())&& ObjectUtil.isNotEmpty(cusMap.get(item.getPartyADeptId()))){
//                item.setPartyADeptIdName(cusMap.get(item.getPartyADeptId()).getCusName());
//                item.setPartASecondDept(cusMap.get(item.getPartyADeptId()).getGroupInfo());
//            }
            if(StrUtil.isNotBlank(item.getContractStatus())){
                item.setContractStatusName(statusMap.get(Integer.parseInt(item.getContractStatus())));
            }

            if(StrUtil.isNotBlank(item.getContractType())){
                item.setContractTypeName(marketContractTypeDictMap.get(item.getContractType()));
            }

            if(StrUtil.isNotBlank(item.getBillingCompany())){
                item.setBillingCompanyName(billingRecognitionCompanyDictMap.get(item.getBillingCompany()));
            }
            if (StrUtil.isNotBlank(item.getIndustry())) {
                item.setIndustry(industryDict.get(item.getIndustry()));
            }

            if(StrUtil.isNotBlank(item.getTaxRate())){
                String[] taxRate = item.getTaxRate().split("、");
                String taxName = Arrays.stream(taxRate)
                        .map(s -> s + "%") // 每个元素后面加%
                        .collect(Collectors.joining(","));
                item.setTaxRate(taxName);
            }
            if(StrUtil.isNotBlank(item.getContractId())){
               // item.setOrderNumber(contractOurSignedSubjectMap.get(item.getContractId()));
                Map<String,String> map = marketContractCustContactMap.get(item.getContractId());
                if(ObjectUtil.isNotEmpty(map)){
                    item.setCusBusRspUser(map.get("business"));
                    item.setCusTechRspUser(map.get("technology"));
                    if(StrUtil.isNotBlank(item.getCusBusRspUser())){
                        item.setCusBusRspUserName(userNameMap.get(item.getCusBusRspUser()));
                    }
                    if(StrUtil.isNotBlank(item.getTechRspUser())){
                        item.setCusTechRspUserName(item.getCusTechRspUser());
                    }
                }

            }

        }
    }
}
