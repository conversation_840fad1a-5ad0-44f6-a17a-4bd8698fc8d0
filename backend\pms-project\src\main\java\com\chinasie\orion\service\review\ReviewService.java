package com.chinasie.orion.service.review;


import com.chinasie.orion.domain.dto.review.ReviewBaseDTO;
import com.chinasie.orion.domain.dto.review.ReviewDTO;
import com.chinasie.orion.domain.dto.review.ReviewDeliveryDTO;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.vo.review.IdAndNameVO;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * Review 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
public interface ReviewService extends OrionBaseService<Review> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ReviewVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param reviewDTO
     */
    String create(ReviewDTO reviewDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param reviewDTO
     */
    Boolean edit(ReviewDTO reviewDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ReviewVO> pages(Page<ReviewDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ReviewVO> vos) throws Exception;

    IdAndNameVO manageUser(String projectId) throws Exception;

    /**
     * 基础信息修改
     * @param reviewBaseDTO
     * @return
     */
    Boolean baseEdit(ReviewBaseDTO reviewBaseDTO);

    /**
     * 添加交付物数据
     * @param dto
     * @return
     */
    Boolean deliverableEdit(ReviewDeliveryDTO dto);

    /**
     * 交付物数据查询
     * @param id
     * @return
     */
    ReviewVO deliverableQuery(String id);
}
