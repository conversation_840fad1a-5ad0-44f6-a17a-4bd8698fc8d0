package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * ProjectContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@ApiModel(value = "ProjectContractChangeApplyList对象", description = "项目合同变更列表信息")
@Data
public class ProjectContractChangeApplyList extends ProjectContractChangeApply {

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "name")
    private String contractName;
}
