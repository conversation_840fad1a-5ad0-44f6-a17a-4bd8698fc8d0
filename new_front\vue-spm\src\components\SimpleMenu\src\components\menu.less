@menu-prefix-cls: ~'vben-menu';
@menu-popup-prefix-cls: ~'vben-menu-popup';
@submenu-popup-prefix-cls: ~'vben-menu-submenu-popup';

@transition-time: 0.2s;
@menu-dark-subsidiary-color: rgba(255, 255, 255, 0.7);


.light-border {
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    width: 2px;
    content: '';
    background-color: ~`getPrefixVar('primary-color')`;
  }
}

.vben-menu {
  padding: 0;
  margin: 0;

  .vben-simple-menu-sub-title {
    margin-left: 10px;
  }

  .vben-menu-submenu {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: ~`getPrefixVar('font-size-base')`;
    color: ~`getPrefixVar('text-color-base')`;
    list-style: none;
    outline: none;
  }

  &-opened > * > &-submenu-title-icon {
    transform: translateY(-50%) rotate(90deg) !important;
  }

  &-item,
  &-submenu-title {
    position: relative;
    z-index: 1;
    padding: 12px 20px;
    color: #393F4D !important;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &-icon {
      position: absolute;
      top: 50%;
      right: 18px;
      transition: transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform: translateY(-50%) rotate(-90deg);
    }
  }

  &-dark {
    .vben-menu-item,
    .vben-menu-submenu-title {
      color: @menu-dark-subsidiary-color;
      // background: @menu-dark-active-bg;

      &:hover {
        color: #fff;
      }

      &-selected {
        color: #fff;
        background-color: ~`getPrefixVar('primary-color')` !important;
      }
    }
  }

  &-light {
    .vben-menu-item,
    .vben-menu-submenu-title {
      color: ~`getPrefixVar('text-color-base')`;

      &:hover {
        color: ~`getPrefixVar('primary-color')`;
      }

      &-selected {
        z-index: 2;
        color: ~`getPrefixVar('primary-color')`;
        background-color: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        .light-border();
      }
    }
  }
}
//.content();
.content() {
  .vben-menu {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    font-size: ~`getPrefixVar('font-size-base')`;
    color: ~`getPrefixVar('text-color-base')`;
    list-style: none;
    outline: none;

    // .collapse-transition {
    //   transition: @transition-time height ease-in-out, @transition-time padding-top ease-in-out,
    //     @transition-time padding-bottom ease-in-out;
    // }

    &-light {
      background-color: #fff;

      .vben-menu-submenu-active {
        color: ~`getPrefixVar('primary-color')` !important;

        &-border {
          .light-border();
        }
      }
    }

    &-dark {
      .vben-menu-submenu-active {
        color: #fff !important;
      }
    }

    &-item {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      font-size: ~`getPrefixVar('font-size-base')`;
      color: inherit;
      list-style: none;
      cursor: pointer;
      outline: none;

      &:hover,
      &:active {
        color: inherit;
      }
    }

    &-item > i {
      margin-right: 6px;
    }

    &-submenu-title > i,
    &-submenu-title span > i {
      margin-right: 8px;
    }

    // vertical
    &-vertical &-item,
    &-vertical &-submenu-title {
      position: relative;
      z-index: 1;
      padding: 14px 24px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      margin-left: 10px;

      &:hover {
        color: ~`getPrefixVar('primary-color')`;
      }

      .vben-menu-tooltip {
        width: calc(100% - 0px);
        padding: 12px 0;
        text-align: center;
      }
      .vben-menu-submenu-popup {
        padding: 12px 0;
      }
    }

    &-vertical &-submenu-collapse {
      .vben-menu-submenu-popup {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .vben-menu-submenu-collapsed-show-tit {
        flex-direction: column;
      }
    }

    &-vertical&-collapse &-item,
    &-vertical&-collapse &-submenu-title {
      padding: 0;
    }

    &-vertical &-submenu-title-icon {
      position: absolute;
      top: 50%;
      right: 18px;
      transform: translateY(-50%);
    }

    &-submenu-title-icon {
      transition: transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    &-vertical &-opened > * > &-submenu-title-icon {
      transform: translateY(-50%) rotate(180deg);
    }

    &-vertical &-submenu {
      &-nested {
        padding-left: 20px;
      }
      .vben-menu-item {
        padding-left: 43px;
      }
    }

    &-light&-vertical &-item {
      &-active:not(.vben-menu-submenu) {
        z-index: 2;
        color: ~`getPrefixVar('primary-color')`;
        background-color: fade(~`getPrefixVar('primary-color')`, 10);

        .light-border();
      }
      &-active.vben-menu-submenu {
        color: ~`getPrefixVar('primary-color')`;
      }
    }

    &-light&-vertical&-collapse {
      > li.vben-menu-item-active,
      .vben-menu-submenu-active {
        position: relative;
        background-color: fade(~`getPrefixVar('primary-color')`, 5);

        &::after {
          display: none;
        }

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          content: '';
          background-color: ~`getPrefixVar('primary-color')`;
        }
      }
    }

    &-dark&-vertical &-item,
    &-dark&-vertical &-submenu-title {
      color: ~`getPrefixVar('text-color')`;
      &-active:not(.vben-menu-submenu) {
        color: #fff !important;
        background-color: ~`getPrefixVar('primary-color')` !important;
      }

      &:hover {
        color: #fff;
      }
    }

    &-dark&-vertical&-collapse {
      > li.vben-menu-item-active,
      .vben-menu-submenu-active {
        position: relative;
        color: #fff !important;
        background-color: @sider-dark-darken-bg-color !important;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 3px;
          height: 100%;
          content: '';
          background-color: ~`getPrefixVar('primary-color')`;
        }

        .vben-menu-submenu-collapse {
          background-color: transparent;
        }
      }
    }

    &-dark&-vertical &-submenu &-item {
      &-active,
      &-active:hover {
        color: #fff;
        border-right: none;
      }
    }

    &-dark&-vertical &-child-item-active > &-submenu-title {
      color: #fff;
    }

    &-dark&-vertical &-opened {
      .vben-menu-submenu-has-parent-submenu {
        .vben-menu-submenu-title {
          background-color: transparent;
        }
      }
    }
  }
}
