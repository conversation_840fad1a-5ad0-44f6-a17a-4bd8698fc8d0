package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.AdvancePaymentInvoicedDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInvoiced;
import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.service.AdvancePaymentInvoicedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * AdvancePaymentInvoiced 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 15:21:06
 */
@RestController
@RequestMapping("/advancePaymentInvoiced")
@Api(tags = "预收款开票挂账信息")
public class  AdvancePaymentInvoicedController  {

    @Autowired
    private AdvancePaymentInvoicedService advancePaymentInvoicedService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询详情id为{{#id}}", type = "预收款开票挂账信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<AdvancePaymentInvoicedVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        AdvancePaymentInvoicedVO rsp = advancePaymentInvoicedService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据关联合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据关联合同id获取详情")
    @RequestMapping(value = "/detailByContractId/{contractId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】根据关联合同id{{#contractId}}获取详情", type = "预收款开票挂账信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<List<AdvancePaymentInvoiced>> detailByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<AdvancePaymentInvoiced> rsp = advancePaymentInvoicedService.detailByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param advancePaymentInvoicedDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#advancePaymentInvoicedDTO.name}}】", type = "预收款开票挂账信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody AdvancePaymentInvoicedDTO advancePaymentInvoicedDTO) throws Exception {
        String rsp =  advancePaymentInvoicedService.create(advancePaymentInvoicedDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param advancePaymentInvoicedDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#advancePaymentInvoicedDTO.name}}】", type = "预收款开票挂账信息", subType = "编辑", bizNo = "{{#advancePaymentInvoicedDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  AdvancePaymentInvoicedDTO advancePaymentInvoicedDTO) throws Exception {
        Boolean rsp = advancePaymentInvoicedService.edit(advancePaymentInvoicedDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "预收款开票挂账信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = advancePaymentInvoicedService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "预收款开票挂账信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = advancePaymentInvoicedService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "预收款开票挂账信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<AdvancePaymentInvoicedVO>> pages(@RequestBody Page<AdvancePaymentInvoicedDTO> pageRequest) throws Exception {
        Page<AdvancePaymentInvoicedVO> rsp =  advancePaymentInvoicedService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预收款开票挂账信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "预收款开票挂账信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        advancePaymentInvoicedService.downloadExcelTpl(response);
    }

    @ApiOperation("预收款开票挂账信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "预收款开票挂账信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = advancePaymentInvoicedService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预收款开票挂账信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "预收款开票挂账信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  advancePaymentInvoicedService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消预收款开票挂账信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "预收款开票挂账信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  advancePaymentInvoicedService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("预收款开票挂账信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "预收款开票挂账信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        advancePaymentInvoicedService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("预收款开票挂账信息表统计")
    @GetMapping(value = "/getTotal")
    @LogRecord(success = "【{USER{#logUserId}}】统计数据", type = "AdvancePaymentInvoiced", subType = "统计数据", bizNo = "")
    public ResponseDTO<AdvancePaymentInvoicedVO> getTotal(@RequestParam String contractId) throws Exception {
        AdvancePaymentInvoicedVO rsp  =advancePaymentInvoicedService.getTotal(contractId);
        return  new ResponseDTO<>(rsp);
    }
}
