package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectGraph Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
@TableName(value = "pmsx_project_graph")
@ApiModel(value = "ProjectGraphEntity对象", description = "技术人员统计表")
@Data

public class ProjectGraph extends ObjectEntity implements Serializable {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "index_order")
    private Integer indexOrder;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @TableField(value = "index_name")
    private String indexName;

    /**
     * 一月
     */
    @ApiModelProperty(value = "一月")
    @TableField(value = "jan")
    private String jan;

    /**
     * 二月
     */
    @ApiModelProperty(value = "二月")
    @TableField(value = "feb")
    private String feb;

    /**
     * 三月
     */
    @ApiModelProperty(value = "三月")
    @TableField(value = "mar")
    private String mar;

    /**
     * 第一季度
     */
    @ApiModelProperty(value = "第一季度")
    @TableField(value = "first_quarter")
    private String firstQuarter;

    /**
     * 四月
     */
    @ApiModelProperty(value = "四月")
    @TableField(value = "apr")
    private String apr;

    /**
     * 五月
     */
    @ApiModelProperty(value = "五月")
    @TableField(value = "may")
    private String may;

    /**
     * 六月
     */
    @ApiModelProperty(value = "六月")
    @TableField(value = "jun")
    private String jun;

    /**
     * 七月
     */
    @ApiModelProperty(value = "七月")
    @TableField(value = "jul")
    private String jul;

    /**
     * 八月
     */
    @ApiModelProperty(value = "八月")
    @TableField(value = "aug")
    private String aug;

    /**
     * 第二季度
     */
    @ApiModelProperty(value = "第二季度")
    @TableField(value = "second_quarter")
    private String secondQuarter;

    /**
     * 九月
     */
    @ApiModelProperty(value = "九月")
    @TableField(value = "sept")
    private String sept;

    /**
     * 第三季度
     */
    @ApiModelProperty(value = "第三季度")
    @TableField(value = "third_quarter")
    private String thirdQuarter;

    /**
     * 十月
     */
    @ApiModelProperty(value = "十月")
    @TableField(value = "oct")
    private String oct;

    /**
     * 十一月
     */
    @ApiModelProperty(value = "十一月")
    @TableField(value = "nov")
    private String nov;

    /**
     * 十二月
     */
    @ApiModelProperty(value = "十二月")
    @TableField(value = "dece")
    private String dece;

    /**
     * 第四季度
     */
    @ApiModelProperty(value = "第四季度")
    @TableField(value = "fourth_quarter")
    private String fourthQuarter;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @TableField(value = "index_year")
    private String indexYear;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "project_graph_type")
    private String projectGraphType;

}
