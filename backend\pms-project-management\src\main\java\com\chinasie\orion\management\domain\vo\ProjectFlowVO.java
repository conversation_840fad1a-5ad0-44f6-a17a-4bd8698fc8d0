package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.Date;
import java.util.List;

/**
 * ProjectFlow VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:17:01
 */
@ApiModel(value = "ProjectFlowVO对象", description = "流程信息")
@Data
public class ProjectFlowVO extends ObjectVO implements Serializable {

    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    private String flowPayPerson;


    /**
     * 收货申请人
     */
    @ApiModelProperty(value = "收货申请人")
    private String flowReceivePerson;


    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNumber;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String businessPerson;

    /**
     * 技术接口人
     */
    @ApiModelProperty(value = "技术接口人")
    private String technicalPerson;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String bearOrg;


    /**
     * 商城系统订单状态
     */
    @ApiModelProperty(value = "商城系统订单状态")
    private String orderStatus;

    /**
     * 预订单签章状态
     */
    @ApiModelProperty(value = "预订单签章状态")
    private String orderSignatureStatus;

    /**
     * 最终合同签章状态
     */
    @ApiModelProperty(value = "最终合同签章状态")
    private String contractSignatureStatus;

    /**
     * 售后状态
     */
    @ApiModelProperty(value = "售后状态")
    private String salesStatus;

    /**
     * 预订单签章时间
     */
    @ApiModelProperty(value = "预订单签章时间")
    private Date orderSignatureDate;

    /**
     * 最终合同签章时间
     */
    @ApiModelProperty(value = "最终合同签章时间")
    private Date contractSignatureDate;
}
