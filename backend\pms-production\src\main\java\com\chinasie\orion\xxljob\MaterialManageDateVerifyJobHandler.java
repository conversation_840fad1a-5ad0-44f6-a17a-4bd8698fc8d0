package com.chinasie.orion.xxljob;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.MaterialManageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/28/0:17
 * @description:
 */

@Component
public class MaterialManageDateVerifyJobHandler {

    @Autowired
    private MaterialManageService materialManageService;

    @XxlJob("materialManageDateVerify")
    public void materialManageDateVerifyHandler() {
        try {
            XxlJobHelper.log("物资管理时间校验，开始时间：{}", DateUtil.date());
            materialManageService.materialManageDateVerifyHandler();
        } catch (Exception e) {
            XxlJobHelper.log("物资管理时间校验，结束时间：{}，执行异常，原因：{}", DateUtil.date(), e.getMessage(), e);
        }
    }
}
