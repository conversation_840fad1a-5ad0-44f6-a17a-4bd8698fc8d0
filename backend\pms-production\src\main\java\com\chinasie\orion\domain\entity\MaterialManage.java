package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * MaterialManage Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@TableName(value = "pmsx_material_manage")
@ApiModel(value = "MaterialManageEntity对象", description = "物资库")
@Data
public class MaterialManage extends  ObjectEntity  implements Serializable{

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    @TableField(value = "asset_type")
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @TableField(value = "asset_code")
    private String assetCode;

    /**
     * 资产编码
     */
    @ApiModelProperty(value = "资产编码")
    @TableField(value = "number")
    @JsonAlias({"number", "materialNumber"})
    private String number;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @TableField(value = "asset_name")
    private String assetName;

    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心名称")
    @TableField(value = "cost_center_name")
    private String costCenterName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "specification_model")
    private String specificationModel;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    @TableField(value = "stock_num")
    private Integer stockNum;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @TableField(value = "next_verification_date")
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @TableField(value = "is_verification")
    private Boolean isVerification;

    /**
     * 物质所在基地
     */
    @ApiModelProperty(value = "物质所在基地")
    @TableField(value = "base_id")
    private String baseId;

    @ApiModelProperty(value = "物质所在基地")
    @TableField(value = "base_code")
    private String baseCode;
    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    @TableField(value = "rsp_user_no")
    @JsonAlias({"rspUserNo", "rspUserCode"})
    private String rspUserNo;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(value = "rsp_user_name")
    private String rspUserName;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    @TableField(value = "use_user_no")
    @JsonAlias({"useUserNo", "userUserCode"})
    private String useUserNo;

    /**
     * 使用人名称
     */
    @ApiModelProperty(value = "使用人名称")
    @TableField(value = "use_user_name")
    @JsonAlias({"useUserName", "userUserName"})
    private String useUserName;

//    /**
//     * 资产入库日期
//     */
//    @ApiModelProperty(value = "资产入库日期")
//    @TableField(value = "enter_date")
//    private Date enterDate;

    /**
     * 是否计量器具
     */
    @ApiModelProperty(value = "是否计量器具")
    @TableField(value = "is_metering")
    private Boolean isMetering;

    /**
     * 是否向电厂报备
     */
    @ApiModelProperty(value = "是否向电厂报备")
    @TableField(value = "is_report")
    private Boolean isReport;

    /**
     * 检定是否超期
     */
    @ApiModelProperty(value = "检定是否超期")
    @TableField(value = "is_overdue")
    private Boolean isOverdue;

    /**
     * 物质应用作业(工单号)
     */
    @ApiModelProperty(value = "物质应用作业(工单号)")
    @TableField(value = "job_no")
    private String jobNo;

    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业名称")
    @TableField(value = "job_name")
    private String jobName;

    @ApiModelProperty(value = "入库数量")
    @TableField(value = "input_stock_num")
    private Integer inputStockNum;
    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    @TableField(value = "rsp_user_id")
    private String rspUserId;
    /**
     * 使用人id
     */
    @ApiModelProperty(value = "使用人id")
    @TableField(value = "use_user_id")
    private String useUserId;

    @ApiModelProperty(value = "实际入场日期")
    @TableField(value = "act_in_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    @TableField(value = "act_out_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    @TableField(value = "in_date",updateStrategy = FieldStrategy.IGNORED)
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    @TableField(value = "out_date",updateStrategy = FieldStrategy.IGNORED)
    private Date outDate;

    @ApiModelProperty(value = "是否再次入场")
    @TableField(value = "is_again_in")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "是否可用")
    @TableField(value = "is_available")
    private Boolean isAvailable;

    @ApiModelProperty(value = "进场倒计时（天）")
    @TableField(value = "in_days")
    private long inDays;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "demand_num")
    private Integer demandNum;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    @TableField(value = "tool_status")
    private String toolStatus;

    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    @TableField(value = "maintenance_cycle")
    private Integer maintenanceCycle;

    @ApiModelProperty(value = "资产所在地")
    @TableField(value = "storage_place_name")
    private String storagePlaceName;
}
