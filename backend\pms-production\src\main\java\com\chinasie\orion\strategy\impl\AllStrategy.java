package com.chinasie.orion.strategy.impl;

import com.chinasie.orion.domain.vo.PersonTmpVO;
import com.chinasie.orion.repository.PersonMangeMapper;
import com.chinasie.orion.strategy.StrategyService;
import com.chinasie.orion.strategy.constant.PersonDeepEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface AllStrategy extends StrategyService<List<String>, List<PersonTmpVO>> {

    Map<String, String> FILE_TO_SQL = PersonDeepEnum.getFieldMap();

}

@Component
class MapperClass {
    @Resource
    PersonMangeMapper personMangeMapper;
}

@Component
class PersonCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.personCount.getStatisticFieldName();
    }
}



@Component
class NewPersonCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.newPersonCount.getStatisticFieldName();
    }
}



@Component
class NoPlanInImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.noPlanIn.getStatisticFieldName();
    }
}


@Component
class NoActInImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.noActIn.getStatisticFieldName();
    }
}



@Component
class ActInCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.actInCount.getStatisticFieldName();
    }
}



@Component
class ActOutCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.actOutCount.getStatisticFieldName();
    }
}



@Component
class ActOutNotReportCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.actOutNotReportCount.getStatisticFieldName();
    }
}


/**
 *
 *  必填未填 准备实施
 */
@Component
class requiredCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.requiredPrepImplCount.getStatisticFieldName();
    }
}

/**
 *  大修完结
 */
@Component
class requiredFinishCountImpl extends MapperClass implements AllStrategy {

    @Override
    public List<PersonTmpVO> strategy(List<String> orgIds,String keyword) {
        return personMangeMapper.getPersonManageTreeDataStrategy(orgIds, FILE_TO_SQL.getOrDefault(getMark(),""),keyword);
    }

    @Override
    public String getMark() {
        return PersonDeepEnum.requiredRepairFinishCount.getStatisticFieldName();
    }
}