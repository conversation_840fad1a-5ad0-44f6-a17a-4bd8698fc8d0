<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';
import { openCertificateTableSelect } from '/@/views/pms/certificateStandards/utils';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '要求名称',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'type',
    label: '要求类型',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
      dictNumber: 'pms_basic_requirement',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'certificateName',
    label: '应取得证书',
    colProps: { span: 24 },
    ifShow({ model }) {
      return model?.type === 'pms_req_certificate';
    },
    componentProps() {
      return {
        allowClear: false,
        onFocus() {
          openCertificateTableSelect({
            cb(list: any[]) {
              setFieldsValue({
                certificateName: list[0]?.name,
                certificateId: list[0]?.id,
              });
            },
          });
        },
      };
    },
    component: 'InputSearch',
  },
  {
    field: 'certificateId',
    label: '应取得证书Id',
    show: false,
    ifShow({ model }) {
      return model?.type === 'pms_req_certificate';
    },
    component: 'Input',
  },
  {
    field: 'trainNumber',
    label: '应通过培训',
    colProps: { span: 24 },
    componentProps: {
      dictNumber: 'pms_train_dict',
    },
    ifShow({ model }) {
      return model?.type === 'pms_req_train';
    },
    component: 'SelectDictVal',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-post-requirement').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
      jobPostId: props?.record?.jobPostId,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/job-post-requirement').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
