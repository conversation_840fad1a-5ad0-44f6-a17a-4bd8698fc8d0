<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { get } from 'lodash-es';
import { message } from 'ant-design-vue';
import { ref } from 'vue';

const props = withDefaults(defineProps<{
  record:Record<string, any>
}>(), {
  record: () => ({}),
});

const tableRef = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {
  },
  columns: [
    {
      title: '资产编码/资产名称',
      dataIndex: 'asset',
      width: 300,
    },
    {
      title: '资产类型',
      dataIndex: 'generalLedgerSubject',
      width: 100,
    },
    {
      title: '项目编号/名称',
      dataIndex: 'projectIdName',
      width: 300,
    },
    {
      title: '成本中心',
      dataIndex: 'costCenter',
      width: 200,
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/projectAssetApplyDetailAssets/page').fetch({
    ...params,
    query: {
      projectId: get(props, 'record.projectId'),
    },
  }, '', 'POST'),
};

defineExpose({
  onSubmit() {
    return new Promise((resolve) => {
      const targetKeys = tableRef.value?.getSelectRowKeys();
      new Api('/pms/projectScheme/relation/ncFormpurchase').fetch({
        toId: get(props, 'record.id'),
        fromIds: targetKeys,
      }, '', 'POST')
        .then((res) => {
          message.success('添加成功');
          resolve(res);
        });
    });
  },
});
</script>

<template>
  <div class="assets-center-modal">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.assets-center-modal{
  height: 590px;
  overflow: hidden;
  :deep(.ant-basic-table){
    padding-bottom: 0 !important;
  }
}
</style>