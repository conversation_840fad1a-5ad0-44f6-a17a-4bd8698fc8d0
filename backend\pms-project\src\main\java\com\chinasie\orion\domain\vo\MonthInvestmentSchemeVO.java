package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * MonthInvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:17:18
 */
@ApiModel(value = "MonthInvestmentSchemeVO对象", description = "月投资计划")
@Data
public class MonthInvestmentSchemeVO extends ObjectVO implements Serializable {

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * 实际
     */
    @ApiModelProperty(value = "实际")
    private BigDecimal actual = new BigDecimal("0");

    /**
     * 预计
     */
    @ApiModelProperty(value = "预计")
    private BigDecimal predicate=new BigDecimal("0");

    /**
     * 年度投资计划Id
     */
    @ApiModelProperty(value = "年度投资计划Id")
    private String yearId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String name;


    /**
     * 是否已执行
     */
    @ApiModelProperty(value = "是否已执行")
    private Integer hasDo;


    /**
     * 关联计划反馈
     */
    @ApiModelProperty(value = "关联计划反馈")
    private String feedbackId;

}
