package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/30/15:37
 * @description:
 */
public enum ContactManageTypeEnum {
    TRAIN_ENGINEER("train_engineer","培训工程师"),
    TRAIN_CONTACT("train_contact","培训联络人");

    public  static Map<String,String> map = new HashMap<>();

    private String type;

    private String desc;

    ContactManageTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    static {
        ContactManageTypeEnum[]  values=  ContactManageTypeEnum.values();
        for (ContactManageTypeEnum value : values) {
            map.put(value.getType(),value.desc);
        }
    }
}
