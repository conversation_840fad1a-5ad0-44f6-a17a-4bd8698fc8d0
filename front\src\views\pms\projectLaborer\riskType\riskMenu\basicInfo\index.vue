<template>
  <Layout2>
    <div class="toolbar-btn-wrap">
      <BasicButton
        v-if="isPower('LXSZ_container_button_35',powerData)"
        type="primary"
        icon="sie-icon-bianji"
        @click="btnClick('edit')"
      >
        编辑
      </BasicButton>
      <BasicButton
        v-if="isPower('LXSZ_container_button_36',powerData)"
        icon="sie-icon-qiyong"
        @click="btnClick('useState')"
      >
        启用
      </BasicButton>
      <BasicButton
        v-if="isPower('LXSZ_container_button_37',powerData)"
        icon="sie-icon-jinyong"
        @click="btnClick('banState')"
      >
        禁用
      </BasicButton>
    </div>
    <div
      v-if="details.id"
      class="p-b-l"
    >
      <div
        class="fw-b mt25 mb15"
        style="font-size: 14px"
      >
        类型信息
      </div>
      <a-descriptions :column="4">
        <a-descriptions-item
          label="编号"
          span="1"
        >
          {{ details?.number }}
        </a-descriptions-item>
        <a-descriptions-item
          label="名称"
          span="1"
        >
          {{ details?.name }}
          <!--      {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}-->
        </a-descriptions-item>
        <a-descriptions-item
          label="所属类型"
          span="1"
        >
          {{ details.parentName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="状态"
          span="1"
        >
          <DataStatusTag
            v-if="details?.dataStatus?.name"
            :status-data="details?.dataStatus"
          />
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item
          label="描述"
          span="4"
        >
          {{ details?.remark }}
        </a-descriptions-item>
        <a-descriptions-item label="类型图标">
          <span
            v-if="details?.icon"
            class="fa iconStyle6"
            :class="details?.icon"
          />
          <span
            v-else
            class="iconStyle6"
          >{{ }}</span>
        </a-descriptions-item>
      </a-descriptions>
      <div
        class="fw-b mt25 mb15"
        style="font-size: 14px"
      >
        基础信息
      </div>
      <a-descriptions :column="4">
        <a-descriptions-item
          label="创建人"
          span="1"
        >
          {{ details?.creatorName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="创建时间"
          span="1"
        >
          {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="修改人"
          span="1"
        >
          {{ details?.modifyName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="修改时间"
          span="1"
        >
          {{ details?.modifyTime?dayjs(details.modifyTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <a-empty
      v-else
      description="暂无数据"
      class="mt60"
    />
    <AEDrawer
      @register="register"
      @add-success="addSuccess"
    />
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs, watch,
} from 'vue';
import {
  BasicButton, DataStatusTag, isPower, Layout2, useDrawer,
} from 'lyra-component-vue3';
import {
  Descriptions, Empty, message, Modal,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import AEDrawer from './AEDrawer.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'Index',
  components: {
    BasicButton,
    Layout2,
    ADescriptions: Descriptions,
    ADescriptionsItem: Descriptions.Item,
    DataStatusTag,
    AEDrawer,
    AEmpty: Empty,
  },
  props: {
    selectChangeData: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: [],
  setup(props) {
    const [register, DrawerM] = useDrawer();
    const powerData: any = inject('powerData', {});
    const state = reactive({
      details: {},
    });
    const state6 = reactive({
      btnList: [
        { type: 'edit' },
        { type: 'useState' },
        { type: 'banState' },
      ],
    });
    onMounted(async () => {
      props?.selectChangeData?.id && await getDetails(props.selectChangeData?.id);
    });
    async function getDetails(id) {
      state.details = await new Api(`/pms/projectPlan-type/${id}`).fetch('', '', 'GET');
    }
    watch(() => props.selectChangeData, () => {
      getDetails(props.selectChangeData?.id);
    });
    function btnClick(type) {
      switch (type) {
        case 'edit':
          props.selectChangeData?.id && editFn();
          break;
        case 'useState':
          props.selectChangeData?.id && useFn();
          break;
        case 'banState':
          props.selectChangeData?.id && banFn();
          break;
      }
    }
    function editFn() {
      DrawerM.setDrawerProps({
        title: '编辑风险类型',
      });
      DrawerM.openDrawer(true, ['edit', { data: JSON.parse(JSON.stringify(state.details)) }]);
    }
    const reloadAll:any = inject('reloadAll');
    function useFn() {
      Modal.confirm({
        title: '启用提示',
        content: '您确认要将状态修改为启用吗？',
        async onOk() {
          return await new Api(`/pms/projectPlan-type/use/${state.details?.id}`).fetch('', '', 'PUT').then(() => {
            message.success('启用成功');
            reloadAll();
            getDetails(props.selectChangeData?.id);
          });
        },
      });
    }
    function banFn() {
      Modal.confirm({
        title: '禁用提示',
        content: '您确认要将状态修改为禁用吗？',
        async onOk() {
          return await new Api(`/pms/projectPlan-type/ban/${state.details?.id}`).fetch('', '', 'PUT').then(() => {
            message.success('禁用成功');
            reloadAll();
            getDetails(props.selectChangeData?.id);
          });
        },
      });
    }
    function addSuccess() {
      getDetails(props.selectChangeData?.id);
      reloadAll();
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      btnClick,
      register,
      dayjs,
      addSuccess,
      powerData,
      isPower,
    };
  },
});
</script>

<style scoped lang="less">
.iconStyle6{
  min-width: 38px;
  height: 38px;
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: .2s;
  padding: 0 5px;
  font-size: 18px;
  position: relative;
  border-radius: 2px;
}

.toolbar-btn-wrap {
  padding-top: ~`getPrefixVar('content-margin-top')`;
  padding-left: ~`getPrefixVar('content-margin-left')`;
}
</style>
