import Api from '/@/api';

/**
 * 分页查询
 * @param params 参数
 */
export const pages = (params) => new Api('/pms/projectApproval/pages').fetch(params, '', 'POST');
/**
 * 分页查询
 * @param params 参数
 */
export const pageNotRelevance = (params) => new Api('/pms/projectApproval/page/notRelevance').fetch(params, '', 'POST');

/**
 * 关联
 * @param projectId 参数
 * @param approvalId 参数
 */
export const relevanceProject = (projectId, approvalId) => new Api(`/pms/projectApproval/relevance/project/${approvalId}/${projectId}`).fetch('', '', 'GET');

/**
 * 关联--保存
 * @param
 */
export const saveRelevanceProject = (params) => new Api('/pms/projectInitiation/project/save').fetch(params, '', 'POST');

/**
 * 项目立项关联项目
 */
export const associationProject = () => new Api('/pms/projectInitiation/project/page').fetch('', '', 'POST');
