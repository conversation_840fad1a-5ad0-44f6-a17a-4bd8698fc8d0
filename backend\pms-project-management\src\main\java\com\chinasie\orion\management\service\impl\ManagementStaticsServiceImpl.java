package com.chinasie.orion.management.service.impl;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.constant.MarketContractStatusEnum;
import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.management.constant.IncomeTypeEnum;
import com.chinasie.orion.management.domain.dto.ManagementStaticsReqDTO;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.*;

import com.chinasie.orion.management.repository.ManagementStaticsMapper;
import com.chinasie.orion.management.service.ManagementStaticsService;
import com.chinasie.orion.management.service.QuotationManagementService;
import com.chinasie.orion.management.service.RequireInfoService;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ManagementStaticsServiceImpl implements ManagementStaticsService {

    @Autowired
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;
    @Autowired
    private RequirementMangementService requireInfoService;

    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private QuotationManagementService quotationManagementService;
    @Autowired
    private MarketContractMapper marketContractMapper;

    @Autowired
    private ManagementStaticsMapper managementStaticsMapper;
    private static final String NUCLEAR_ENERGY_TYPE = IncomeTypeEnum.NUCLEAR_NERGY.getName();
    private static final String NOT_NUCLEAR_ENERGY_TYPE = IncomeTypeEnum.NON_NUCLEAR_NERGY.getName();
    @Override
    public MilestoneCompletionVO milestoneCompletion(ManagementStaticsReqDTO managementStaticsReqDTO) {
        Integer filterYear = managementStaticsReqDTO.getFilterYear();
        Integer filterMonth = managementStaticsReqDTO .getFilterMonth();
        LocalDate startTime = null;
        LocalDate endTime = null;
        // 获取传参全年的开始和结束日期，月份为全年
        if (filterYear != null && filterMonth == null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);
            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }
        // 获取传参某年某月的开始和结束日期
        if (filterYear != null && filterMonth != null) {
            startTime = YearMonth.of(filterYear, filterMonth).atDay(1);
            endTime = YearMonth.of(filterYear, filterMonth).atEndOfMonth();
        }
        //全局情况
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),ContractMilestone::getUndertDept,managementStaticsReqDTO.getDeptId());
        contractMilestoneLambdaQueryWrapperX.between(ContractMilestone::getActualAcceptDate, LocalDateTime.of(startTime, LocalTime.MIN), LocalDateTime.of(endTime, LocalTime.MAX));
//        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(contractMilestoneLambdaQueryWrapperX);
        //已验收收入（万元）
        //BigDecimal acceptedRevenue = managementStaticsMapper.selectAcceptedRevenue(managementStaticsReqDTO.getDeptId(),startTime,endTime,"");
        BigDecimal acceptedRevenue = managementStaticsMapper.selectMilestoneCompletion(managementStaticsReqDTO.getDeptId(),startTime,endTime,"");
        //计划验收金额
        BigDecimal plannedRevenue = managementStaticsMapper.selectMilestonePlan(managementStaticsReqDTO.getDeptId(),startTime,endTime,"");

        //todo 合同还没确认核能和非核能
        //核能已验收收入（万元）
        BigDecimal nuclearAcceptedRevenue = managementStaticsMapper.selectMilestoneCompletion(managementStaticsReqDTO.getDeptId(),startTime,endTime,NUCLEAR_ENERGY_TYPE);
        //核能计划验收金额
        BigDecimal NuclearPlannedRevenue = managementStaticsMapper.selectMilestonePlan(managementStaticsReqDTO.getDeptId(),startTime,endTime,NUCLEAR_ENERGY_TYPE);
        //非核能已验收收入（万元）
        BigDecimal otherAcceptedRevenue = managementStaticsMapper.selectMilestoneCompletion(managementStaticsReqDTO.getDeptId(),startTime,endTime,NOT_NUCLEAR_ENERGY_TYPE);
        //非核能计划验收金额
        BigDecimal otherPlannedRevenue = managementStaticsMapper.selectMilestonePlan(managementStaticsReqDTO.getDeptId(),startTime,endTime,NOT_NUCLEAR_ENERGY_TYPE);
        MilestoneCompletionVO milestoneCompletionVO = new MilestoneCompletionVO();
        milestoneCompletionVO.setAcceptedRevenue(acceptedRevenue!=null?acceptedRevenue:BigDecimal.ZERO);
        milestoneCompletionVO.setPlannedRevenue(plannedRevenue!=null?plannedRevenue:BigDecimal.ZERO);
        milestoneCompletionVO.setNuclearAcceptedRevenue(nuclearAcceptedRevenue!=null?nuclearAcceptedRevenue:BigDecimal.ZERO);
        milestoneCompletionVO.setNuclearPlannedRevenue(NuclearPlannedRevenue!=null?NuclearPlannedRevenue:BigDecimal.ZERO);
        milestoneCompletionVO.setOtherAcceptedRevenue(otherAcceptedRevenue!=null?otherAcceptedRevenue:BigDecimal.ZERO);
        milestoneCompletionVO.setOtherPlannedRevenue(otherPlannedRevenue!=null?otherPlannedRevenue:BigDecimal.ZERO);
        Page<ContractMilestoneDTO> pageRequest = new Page<>();
        try {
            milestoneCompletionVO.setWaitingMilestone((int) this.pages(pageRequest).getTotalSize());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Integer exceptionReportNum = managementStaticsMapper.getExceptionReportNum();
        milestoneCompletionVO.setExceptionReportNum(exceptionReportNum);
        return milestoneCompletionVO;
    }

    @Override
    public ContractDistributionVO contractDistribution(ManagementStaticsReqDTO managementStaticsReqDTO) {
        ContractDistributionVO contractDistributionVO = new ContractDistributionVO();

        Integer filterYear = managementStaticsReqDTO.getFilterYear();
        Integer filterMonth = managementStaticsReqDTO.getFilterMonth();
        LocalDate startTime = null;
        LocalDate endTime = null;
        // 获取传参全年的开始和结束日期，月份为全年
        if (filterYear != null && filterMonth == null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);
            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }
        // 获取传参某年某月的开始和结束日期
        if (filterYear != null && filterMonth != null) {
            startTime = YearMonth.of(filterYear, filterMonth).atDay(1);
            endTime = YearMonth.of(filterYear, filterMonth).atEndOfMonth();
        }
        //合同总数
        Integer contractTotal = managementStaticsMapper.selectContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        //履行合同数
        Integer performanceContract = managementStaticsMapper.fulfuilContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        //核能合同总数
        Integer nuclearEnergyContractNum = managementStaticsMapper.selectContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NUCLEAR_ENERGY_TYPE);
        //非核能合同总数
        Integer otherContractNum = managementStaticsMapper.selectContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NOT_NUCLEAR_ENERGY_TYPE);
        //履行合同中总价合同
        Integer totalPriceContractNum = managementStaticsMapper.fulfuilTpContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        //履行合同中框架合同
        Integer frameContractNum = managementStaticsMapper.fulfuilframeContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        //履行合同中复合合同
        Integer compositeContractNum = managementStaticsMapper.fulfuilCompositeContractNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        contractDistributionVO.setContractTotal(contractTotal);
        contractDistributionVO.setPerformanceContract(performanceContract);
        contractDistributionVO.setNuclearEnergyContractNum(nuclearEnergyContractNum);
        contractDistributionVO.setOtherContractNum(otherContractNum);
        contractDistributionVO.setTotalPriceContractNum(totalPriceContractNum);
        contractDistributionVO.setFrameContractNum(frameContractNum);
        contractDistributionVO.setCompositeContractNum(compositeContractNum);
        return contractDistributionVO;
    }

    @Override
    public QuoteOutbidVO quoteOutbid(ManagementStaticsReqDTO managementStaticsReqDTO) {
        QuoteOutbidVO quoteOutbidVO = new QuoteOutbidVO();
        Integer filterYear = managementStaticsReqDTO.getFilterYear();
        Integer filterMonth = managementStaticsReqDTO.getFilterMonth();
        LocalDate startTime = null;
        LocalDate endTime = null;
        // 获取传参全年的开始和结束日期，月份为全年
        if (filterYear != null && filterMonth == null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);
            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }
        // 获取传参某年某月的开始和结束日期
        if (filterYear != null && filterMonth != null) {
            startTime = YearMonth.of(filterYear, filterMonth).atDay(1);
            endTime = YearMonth.of(filterYear, filterMonth).atEndOfMonth();
        }
        // 统计发出报价的报价单数量
        Integer sendQuoteNum = managementStaticsMapper.sendQuoteNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        Integer biddingQuotNum = managementStaticsMapper.biddingQuotNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, "");
        //中标率
        Double quoteOutbidNum = 0.0;
        if (sendQuoteNum != 0) {
            quoteOutbidNum = biddingQuotNum.doubleValue() / sendQuoteNum;
        }
        //核能
        Integer nuclearSendQuoteNum = managementStaticsMapper.sendQuoteNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NUCLEAR_ENERGY_TYPE);
        Integer nuclearBiddingQuotNum = managementStaticsMapper.biddingQuotNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NUCLEAR_ENERGY_TYPE);
        //核能中标率
        Double nuclearQuoteOutbidNum = 0.0;
        if (nuclearSendQuoteNum != 0) {
            nuclearQuoteOutbidNum = nuclearBiddingQuotNum.doubleValue() / nuclearSendQuoteNum;
            BigDecimal bd = BigDecimal.valueOf(nuclearQuoteOutbidNum);
            BigDecimal bigDecimal = bd.setScale(2, BigDecimal.ROUND_HALF_UP);
            nuclearQuoteOutbidNum = bigDecimal.doubleValue();
        }
        //非核能
        Integer notSendQuoteNum = managementStaticsMapper.sendQuoteNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NOT_NUCLEAR_ENERGY_TYPE);
        Integer notBiddingQuotNum = managementStaticsMapper.biddingQuotNum(managementStaticsReqDTO.getDeptId(), startTime, endTime, NOT_NUCLEAR_ENERGY_TYPE);
        //非核能中标率
        Double notQuoteOutbidNum = 0.0;
        if (notSendQuoteNum != 0) {
            notQuoteOutbidNum = notBiddingQuotNum.doubleValue() / notSendQuoteNum;
            BigDecimal bd = BigDecimal.valueOf(notQuoteOutbidNum);
            BigDecimal bigDecimal = bd.setScale(2, BigDecimal.ROUND_HALF_UP);
            notQuoteOutbidNum = bigDecimal.doubleValue();
        }

        quoteOutbidVO.setQuoteOutbidNum(quoteOutbidNum * 100);
        quoteOutbidVO.setOtherQuoteOutbidNum(notQuoteOutbidNum * 100);
        quoteOutbidVO.setNuclearEnergyQuoteOutbidNum(nuclearQuoteOutbidNum * 100);
        quoteOutbidVO.setSendQuoteNum(sendQuoteNum);
        quoteOutbidVO.setNuclearEnergySendQuoteNum(nuclearSendQuoteNum);
        quoteOutbidVO.setOtherSendQuoteNum(notSendQuoteNum);
        return quoteOutbidVO;
    }

    @Override
    public MarketManagementTotalVO eachStateStatistics(ManagementStaticsReqDTO managementStaticsReqDTO) {
        MarketManagementTotalVO marketManagementTotalVO = new MarketManagementTotalVO();
        Integer filterYear = managementStaticsReqDTO.getFilterYear();
        Integer filterMonth = managementStaticsReqDTO.getFilterMonth();
        LocalDate startTime = null;
        LocalDate endTime = null;
        // 获取传参全年的开始和结束日期，月份为全年
        if (filterYear != null && filterMonth == null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);
            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }
        // 获取传参某年某月的开始和结束日期
        if (filterYear != null && filterMonth != null) {
            startTime = YearMonth.of(filterYear, filterMonth).atDay(1);
            endTime = YearMonth.of(filterYear, filterMonth).atEndOfMonth();
        }
        //需求总数
        LambdaQueryWrapperX<RequirementMangement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.between(RequirementMangement::getCreateTime, startTime, endTime);
        lambdaQueryWrapperX.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),RequirementMangement::getCustPerson,managementStaticsReqDTO.getDeptId());
        lambdaQueryWrapperX.eq(RequirementMangement::getLogicStatus,1);
        lambdaQueryWrapperX.and(wq->wq.isNull(RequirementMangement::getEcpStatus).or().ne(RequirementMangement::getEcpStatus, "可报名项目"));
        long requirementTotal = requireInfoService.count(lambdaQueryWrapperX);

        LambdaQueryWrapperX<RequirementMangement> distributed = new LambdaQueryWrapperX<>();
        distributed.between(RequirementMangement::getCreateTime, startTime, endTime);
        distributed.in(RequirementMangement::getStatus,120);
        distributed.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),RequirementMangement::getCustPerson,managementStaticsReqDTO.getDeptId());
        distributed.eq(RequirementMangement::getLogicStatus,1);
        long distributedRequirement = requireInfoService.count(distributed);

        LambdaQueryWrapperX<RequirementMangement> confirmed = new LambdaQueryWrapperX<>();
        confirmed.between(RequirementMangement::getCreateTime, startTime, endTime);
        confirmed.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),RequirementMangement::getCustPerson,managementStaticsReqDTO.getDeptId());
        confirmed.eq(RequirementMangement::getLogicStatus,1);
        confirmed.in(RequirementMangement::getStatus,130);
        long confirmedRequirement = requireInfoService.count(confirmed);
        //已签署合同
        LambdaQueryWrapperX<MarketContract> signedContract = new LambdaQueryWrapperX<>();
        signedContract.between(RequirementMangement::getCreateTime, startTime, endTime);
        signedContract.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),MarketContract::getTechRspDept,managementStaticsReqDTO.getDeptId());
        signedContract.in(MarketContract::getStatus,MarketContractStatusEnum.COMPLATED.getStatus(),MarketContractStatusEnum.FULFIL.getStatus());
        signedContract.eq(QuotationManagement::getLogicStatus,1);
        long signedContractNum = marketContractService.count(signedContract);

        //进行中合同
        LambdaQueryWrapperX<MarketContract> goingContract = new LambdaQueryWrapperX<>();
        goingContract.between(RequirementMangement::getCreateTime, startTime, endTime);
        goingContract.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),MarketContract::getTechRspDept,managementStaticsReqDTO.getDeptId());
        goingContract.eq(MarketContract::getStatus,MarketContractStatusEnum.FULFIL.getStatus());
        goingContract.eq(QuotationManagement::getLogicStatus,1);
        long goingContractNum = marketContractService.count(goingContract);

        //已关闭合同
        LambdaQueryWrapperX<MarketContract> closedContract = new LambdaQueryWrapperX<>();
        closedContract.between(RequirementMangement::getCreateTime, startTime, endTime);
        closedContract.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),MarketContract::getTechRspDept,managementStaticsReqDTO.getDeptId());
        closedContract.eq(MarketContract::getStatus,MarketContractStatusEnum.COMPLATED.getStatus());
        closedContract.eq(QuotationManagement::getLogicStatus,1);
        long closedContractNum = marketContractService.count(closedContract);

        //已发出报价
        LambdaQueryWrapperX<QuotationManagement> sendQuote = new LambdaQueryWrapperX<>();
        sendQuote.between(QuotationManagement::getCreateTime, startTime, endTime);
        //lambdaQueryWrapperX.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),MarketContract::getTechRspDept,managementStaticsReqDTO.getDeptId());
        sendQuote.in(QuotationManagement::getStatus,121,1,140);
        sendQuote.eq(QuotationManagement::getLogicStatus,1);
        long sendQuoteNum = quotationManagementService.count(sendQuote);
        //已中标报价
        LambdaQueryWrapperX<QuotationManagement> outbidQuote = new LambdaQueryWrapperX<>();
        outbidQuote.between(QuotationManagement::getCreateTime, startTime, endTime);
        //lambdaQueryWrapperX.eq(StringUtils.hasText(managementStaticsReqDTO.getDeptId()),MarketContract::getTechRspDept,managementStaticsReqDTO.getDeptId());
        outbidQuote.eq(MarketContract::getStatus,1);
        outbidQuote.eq(QuotationManagement::getLogicStatus,1);
        long outbidQuoteNum = quotationManagementService.count(outbidQuote);

        marketManagementTotalVO.setRequirementTotal(requirementTotal);
        marketManagementTotalVO.setDistributedRequirement(distributedRequirement);
        marketManagementTotalVO.setConfirmedRequirement(confirmedRequirement);
        marketManagementTotalVO.setSignedContract(signedContractNum);
        marketManagementTotalVO.setGoingContract(goingContractNum);
        marketManagementTotalVO.setClosedContract(closedContractNum);
        marketManagementTotalVO.setOutbidQuoteNum(outbidQuoteNum);
        marketManagementTotalVO.setSendQuoteNum(sendQuoteNum);
        return marketManagementTotalVO;
    }

    @Override
    public MilestoneLineChartVO milestoneLineChart(ManagementStaticsReqDTO managementStaticsReqDTO) {
        MilestoneLineChartVO milestoneLineChartVO  = new MilestoneLineChartVO();
        List allMonth = new ArrayList();
        List acceptAmount = new ArrayList();
        List planAmount = new ArrayList();
        Integer filterYear =  managementStaticsReqDTO.getFilterYear();
        List<MilestoneLineChartQueryVO> actualAccept = managementStaticsMapper.actualAccept(filterYear, managementStaticsReqDTO.getDeptId());
        List<MilestoneLineChartQueryVO> planAccept = managementStaticsMapper.planAccept(filterYear, managementStaticsReqDTO.getDeptId());
        Map<Integer, BigDecimal> actualAcceptMap = new HashMap<>();
        actualAccept.forEach(item ->{
            actualAcceptMap.put(item.getSaleMonth(), item.getAcceptAmount());
        });
//        Map<Integer, BigDecimal> actualAcceptMap = actualAccept.stream().collect(Collectors.toMap(
//                MilestoneLineChartQueryVO::getSaleMonth,
//                MilestoneLineChartQueryVO::getAcceptAmount
//        ));
        Map<Integer, BigDecimal> planAcceptMap = new HashMap<>();
        planAccept.forEach(item ->{
            planAcceptMap.put(item.getSaleMonth(), item.getPlanAmount());
        });
//        Map<Integer, BigDecimal> planAcceptMap = planAccept.stream().collect(Collectors.toMap(
//                MilestoneLineChartQueryVO::getSaleMonth,
//                MilestoneLineChartQueryVO::getPlanAmount
//        ));
        int currentYear = LocalDate.now().getYear();
        int month = LocalDate.now().getMonthValue();
        if (filterYear.equals(currentYear)) {
            for (int i = 1; i < month+1; i++) {
                allMonth.add(i);
                acceptAmount.add(actualAcceptMap.get(i)!=null?actualAcceptMap.get(i):BigDecimal.ZERO);
                planAmount.add(planAcceptMap.get(i)!=null?planAcceptMap.get(i):BigDecimal.ZERO);
            }
        }else {
            for (int i = 1; i < 13; i++) {
                allMonth.add(i);
                acceptAmount.add(actualAcceptMap.get(i)!=null?actualAcceptMap.get(i):BigDecimal.ZERO);
                planAmount.add(planAcceptMap.get(i)!=null?planAcceptMap.get(i):BigDecimal.ZERO);
            }
        }
        milestoneLineChartVO.setAllMonth(allMonth);
        milestoneLineChartVO.setAcceptAmount(acceptAmount);
        milestoneLineChartVO.setPlanAmount(planAmount);
        return milestoneLineChartVO;
    }

    @Override
    public Page<ContractMilestoneVO> pages(Page<ContractMilestoneDTO> pageRequest) throws Exception {
        LocalDate localDate = LocalDate.now().plusDays(30);
        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>( ContractMilestone. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractMilestone::getCreateTime);
        ContractMilestoneDTO contractMilestoneDTO = pageRequest.getQuery();
        if(contractMilestoneDTO != null){
            String contractId = contractMilestoneDTO.getContractId();
            if(org.apache.commons.lang3.StringUtils.isNotBlank(contractId)){
                condition.eq(ContractMilestone :: getContractId,contractId);
            }
        }
        condition.eq(ContractMilestone ::getStatus, MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
        condition.le(ContractMilestone::getExpectAcceptDate,localDate);
        Page<ContractMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractMilestone::new));

        PageResult<ContractMilestone> page = contractMilestoneMapper.selectPage(realPageRequest, condition);

        Page<ContractMilestoneVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractMilestoneVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractMilestoneVO::new);
        if(!CollectionUtils.isEmpty(vos)){
            List<String> contractIds = vos.stream().map(ContractMilestoneVO :: getContractId).collect(Collectors.toList());
            List<MarketContract> contracts = marketContractMapper.selectBatchIds(contractIds);
            Map<String,String> contractMap = contracts.stream().collect(Collectors.toMap(MarketContract :: getId,MarketContract :: getNumber));
            vos.forEach(item ->{
                item.setContractNumber( contractMap.get(item.getContractId()));
                long surplusDays = 0L;
                Date expectAcceptDate = item.getExpectAcceptDate();
                if(expectAcceptDate != null){
                    if(new Date().compareTo(expectAcceptDate) >=0){
                        surplusDays = 0; // 逾期
                    }
                    else{
                        surplusDays = DateUtil.betweenDay(new Date(),expectAcceptDate,true);
                        if(surplusDays < 0){
                            surplusDays = 0;
                        }
                    }
                }
                item.setSurplusDays(surplusDays);
            });
        }
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }

    public void  setEveryName(List<ContractMilestoneVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }
}

