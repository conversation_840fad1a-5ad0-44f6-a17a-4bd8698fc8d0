package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IfToParameterToIns DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@ApiModel(value = "IfToParameterToInsDTO对象", description = "意见单和参数和参数实列的关系")
@Data
public class IfToParameterToInsDTO extends ObjectDTO implements Serializable {

    /**
     * 意见单id
     */
    @ApiModelProperty(value = "意见单id")
    private String ifId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    private String parameterId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    private String insId;

    /**
     * 拷贝类型
     */
    @ApiModelProperty(value = "拷贝类型")
    private String copyType;

    @ApiModelProperty(value = "模板Id")
    private String modelId;

}
