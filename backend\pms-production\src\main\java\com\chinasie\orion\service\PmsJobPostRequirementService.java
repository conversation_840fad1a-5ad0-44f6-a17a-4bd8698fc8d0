package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.PmsJobPostRequirement;
import com.chinasie.orion.domain.dto.PmsJobPostRequirementDTO;
import com.chinasie.orion.domain.vo.PmsJobPostRequirementVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PmsJobPostRequirement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
public interface PmsJobPostRequirementService extends OrionBaseService<PmsJobPostRequirement> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    PmsJobPostRequirementVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param pmsJobPostRequirementDTO
     */
    String create(PmsJobPostRequirementDTO pmsJobPostRequirementDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param pmsJobPostRequirementDTO
     */
    Boolean edit(PmsJobPostRequirementDTO pmsJobPostRequirementDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<PmsJobPostRequirementVO> pages(String mainTableId, Page<PmsJobPostRequirementDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<PmsJobPostRequirementVO> vos) throws Exception;

    /**
     *  获取 岗位需要满足的条件
     * @param jobIdList
     * @return
     */
    List<PmsJobPostRequirement> getRequirementByJobId(List<String> jobIdList);


    List<PmsJobPostRequirement> getRequirementByJobCodeList(List<String> jobCodeList);
}
