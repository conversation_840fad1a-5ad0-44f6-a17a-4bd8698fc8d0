export const contractLifecycleCfg = [
  {
    busStatus: 121,
    children: [
      {
        code: 'compilingBasicInformation',
        isLightUp: false,
        name: '编制基础信息',
        sort: 1,
      },
      {
        code: 'entryEmploymentPlan',
        isLightUp: false,
        name: '录入用人计划',
        sort: 2,
      },
      {
        code: 'reviewEmploymentPlan',
        isLightUp: false,
        name: '审核用人计划',
        sort: 3,
      },
    ],
    code: 'declarationStage',
    isLightUp: false,
    name: '申报阶段',
    sort: 1,
  },
  {
    busStatus: 101,
    children: [
      {
        code: 'initiatePeopleManageProcess',
        isLightUp: false,
        name: '发起人员管理流程',
        sort: 1,
      },
      {
        code: 'importPersonnelData',
        isLightUp: false,
        name: '导入人员数据',
        sort: 2,
      },
      {
        code: 'generateStatisticalReports',
        isLightUp: false,
        name: '生成统计报表',
        sort: 3,
      },
    ],
    code: 'executionPhase',
    isLightUp: false,
    name: '执行阶段',
    sort: 2,
  },
  {
    busStatus: 110,
    children: [
      {
        code: 'supplierConfirmsActualCost',
        isLightUp: false,
        name: '供应商确认实际费用',
        sort: 1,
      },
      {
        code: 'employerConfirmsActualExpenses',
        isLightUp: false,
        name: '用人单位确认实际费用',
        sort: 2,
      },
      {
        code: 'initiateAcceptanceProcess',
        isLightUp: false,
        name: '发起验收流程',
        sort: 2,
      },
      {
        code: 'initiateStaffSatisfactionEvaluation',
        isLightUp: false,
        name: '发起人员满意度评价',
        sort: 2,
      },
    ],
    code: 'acceptanceEvaluationPhase',
    isLightUp: false,
    name: '验收评价阶段',
    sort: 2,
  },
  {
    busStatus: 111,
    children: [
      {
        code: 'initiateStatementProcess',
        isLightUp: false,
        name: '发起结算单流程',
        sort: 1,
      },
    ],
    code: 'settlementPhase',
    isLightUp: false,
    name: '结算阶段',
    sort: 2,
  },
];
export const contractLifecycleCfg1 = [
  {
    busStatus: 121,
    children: [
      {
        code: 'compilingBasicInformation',
        isLightUp: true,
        name: '编制基础信息',
        sort: 1,
        nextCode: 'entryEmploymentPlan',
      },
      {
        code: 'entryEmploymentPlan',
        isLightUp: false,
        name: '录入用人计划',
        sort: 2,
        nextCode: 'reviewEmploymentPlan',
      },
      {
        code: 'reviewEmploymentPlan',
        isLightUp: false,
        name: '审核用人计划',
        sort: 3,
        nextCode: null,
      },
    ],
    code: 'declarationStage',
    isLightUp: true,
    name: '申报阶段',
    sort: 1,
    nextCode: 'executionPhase',
  },
  {
    busStatus: 101,
    children: [
      {
        code: 'initiatePeopleManageProcess',
        isLightUp: false,
        name: '发起人员管理流程',
        sort: 1,
        nextCode: 'importPersonnelData',
      },
      {
        code: 'importPersonnelData',
        isLightUp: false,
        name: '导入人员数据',
        sort: 2,
        nextCode: 'generateStatisticalReports',
      },
      {
        code: 'generateStatisticalReports',
        isLightUp: false,
        name: '生成统计报表',
        sort: 3,
        nextCode: null,
      },
    ],
    code: 'executionPhase',
    nextCode: 'acceptanceEvaluationPhase',
    isLightUp: false,
    name: '执行阶段',
    sort: 2,
  },
  {
    busStatus: 110,
    children: [
      {
        code: 'supplierConfirmsActualCost',
        isLightUp: false,
        name: '供应商确认实际费用',
        sort: 1,
        nextCode: 'employerConfirmsActualExpenses',
      },
      {
        code: 'employerConfirmsActualExpenses',
        isLightUp: false,
        name: '用人单位确认实际费用',
        sort: 2,
        nextCode: 'initiateAcceptanceProcess',
      },
      {
        code: 'initiateAcceptanceProcess',
        isLightUp: false,
        name: '发起验收流程',
        sort: 2,
        nextCode: 'initiateStaffSatisfactionEvaluation',
      },
      {
        code: 'initiateStaffSatisfactionEvaluation',
        isLightUp: false,
        name: '发起人员满意度评价',
        sort: 2,
        nextCode: null,
      },
    ],
    code: 'acceptanceEvaluationPhase',
    isLightUp: false,
    name: '验收评价阶段',
    sort: 2,
    nextCode: 'settlementPhase',
  },
  {
    busStatus: 111,
    children: [
      {
        code: 'initiateStatementProcess',
        isLightUp: false,
        name: '发起结算单流程',
        sort: 1,
        nextCode: null,
      },
    ],
    code: 'settlementPhase',
    isLightUp: false,
    name: '结算阶段',
    sort: 2,
  },
];