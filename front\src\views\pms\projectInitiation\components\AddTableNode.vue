<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    >
      <template #slotInfo>
        <BasicCard
          title="项目立项支持性材料"
          class="basic-card"
        />
        <UploadList
          ref="tableRef"
          :listData="listData||[]"
          height="300px"
          :onChange="onChange"
          type="modal"
        />
      </template>
    </BasicForm>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList, openSelectUserModal, getDictByNumber, openModal,
} from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed, h,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import Api from '/@/api';
import { SelectListTable } from '/@/views/pms/components';

const props = withDefaults(defineProps<{
    drawerData:object,
    formType:string
}>(), {
  drawerData: () => ({}),
  formType: 'add',
});
const listData:Ref<Record<any, any>[]> = ref([]);
const loading:Ref<boolean> = ref(false);
const rspUser:Ref<string> = ref(''); // 负责人Id
const requireReviewId:Ref<string> = ref('');// 需求评审Id
const [register, { setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'name',
      label: '名称：',
      colProps: { span: 12 },
      rules: [
        {
          required: true,
          type: 'string',
          message: '请输入名称',
        },
      ],
      component: 'Input',
    },
    {
      field: 'type',
      label: '类型：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      rules: [
        {
          required: true,
          type: 'string',
          message: '请选择类型',
        },
      ],
      componentProps: {
        allowClear: false,
        placeholder: '请选择',
        api: () => getDictByNumber('approvalType'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'source',
      label: '来源：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      rules: [
        {
          required: true,
          type: 'string',
          message: '请选择来源',
        },
      ],
      componentProps: {
        placeholder: '请选择',
        api: () => getDictByNumber('approvalSource'),
        labelField: 'description',
        valueField: 'number',
      },
    },
    {
      field: 'rspUserName',
      label: '负责人：',
      colProps: { span: 12 },
      component: 'Input',
      rules: [
        {
          required: true,
          type: 'string',
          message: '请选择负责人',
        },
      ],
      componentProps: {
        allowClear: true,
        onClick() {
          openSelectUser();
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              openSelectUser();
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          rspUser.value = '';
          await setFieldsValue({ rspUserName: '' });
        },
      },
    },
    {
      field: 'estimateStartTime',
      component: 'DatePicker',
      colProps: { span: 12 },
      label: '预估项目开始时间',
      // required: true,
      componentProps: ({ formModel }) => ({
        disabledDate: (date:Dayjs) => {
          if (formModel.estimateEndTime) {
            return date.isAfter(dayjs(formModel.estimateEndTime));
          }
          return false;
        },
        valueFormat: 'YYYY-MM-DD',
      }),
    },
    {
      field: 'estimateEndTime',
      component: 'DatePicker',
      colProps: { span: 12 },
      label: '预估项目结束时间',
      // required: true,
      componentProps: ({ formModel }) => ({
        disabledDate: (date:Dayjs) => {
          if (formModel.estimateStartTime) {
            return date.isBefore(dayjs(formModel.estimateStartTime));
          }
          return false;
        },
        valueFormat: 'YYYY-MM-DD',
      }),
    },
    {
      field: 'requireReviewName',
      label: '需求评审单：',
      colProps: { span: 24 },
      component: 'Input',
      componentProps: {
        allowClear: true,
        onClick() {
          openSelectTable();
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              openSelectTable();
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          await setFieldsValue({ principalId: '' });
        },
      },
    },
    {
      field: 'approvalReason',
      label: '立项理由：',
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
    {
      component: 'Input',
      field: 'attachments',
      colProps: {
        span: 24,
      },
      slot: 'slotInfo',
    },
  ],
});
function openSelectUser() {
  openSelectUserModal([], {
    selectType: 'radio',
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      rspUser.value = data[0].id;
      setFieldsValue({ rspUserName: data[0].name });
    },
  });
}
function openSelectTable() {
  const selectListTableRef = ref();
  openModal({
    title: '添加需求评审单1111',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        getTableData,
        smallSearchField: ['requireReviewLogo'],
        columns: [
          {
            title: '需求评审标识',
            dataIndex: 'requireReviewLogo',
          },
          {
            title: '应用场景',
            dataIndex: 'applicationScenarios',
            width: 100,
          },
          {
            title: '军兵种',
            dataIndex: 'armyArms',
            width: 100,
          },
          {
            title: '创建人',
            dataIndex: 'creatorName',
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            customRender({ text }) {
              return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
            },
          },
        ],
        showLeftTree: false,
        selectType: 'radio',
        isTableTree: false,
      });
    },
    async onOk() {
      let selectTable = await selectListTableRef.value.getFormData();
      if (selectTable.selectedRowKeys.length === 0) {
        message.warning('请选择需求评审单');
        return Promise.reject('');
      }
      requireReviewId.value = selectTable.selectTableData[0].id;
      await setFieldsValue({
        requireReviewName: selectTable.selectTableData[0].requireReviewLogo,
      });
    },
  });
}
function getTableData(params) {
  return new Api('/pms').fetch(params, 'requireReviewForm/page', 'POST');
}
onMounted(() => {
  if (props.formType === 'edit') {
    getDetails();
  }
});
function getDetails() {
  loading.value = true;
  new Api('/pms').fetch('', `projectApproval/${props.drawerData.id}`, 'GET').then(async (res) => {
    rspUser.value = res.rspUser;
    requireReviewId.value = res.requireReviewId;
    loading.value = false;
    listData.value = res.attachments || [];
    await setFieldsValue(res);
  });
}
function onChange(data) {
  listData.value = data;
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.requireReviewId = requireReviewId.value;
    params.rspUser = rspUser.value;
    params.attachments = listData.value;
    // params.attachments = listData.value;
    if (props.formType === 'edit') {
      params.id = props.drawerData.id;
    }
    // params.attachments = listData.value;
    await new Api('/pms').fetch(params, 'projectApproval', props.formType === 'add' ? 'POST' : 'PUT');
    message.success(props.formType === 'add' ? '新增立项成功' : '编辑立项成功');
  },
});
</script>
<style lang="less" scoped>
.basic-card{
  margin: 0 !important;
  :deep(.card-content){
    margin-top: 0 !important;
  }
}

:deep(.default-spacing){
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>