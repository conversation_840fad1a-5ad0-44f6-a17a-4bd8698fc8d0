<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';
import dayjs from 'dayjs';
import { Timeline as ATimeline, TimelineItem as ATimelineItem } from 'ant-design-vue';
import { setBasicInfo } from '../utils';
const detailsData: Record<string, any> = inject('formData', reactive({}));

const props = withDefaults(defineProps<{
  riskName:string,
}>(), {
  riskName: '',
});
const baseInfoProps = reactive({
  list: setBasicInfo([
    // {
    //   label: '问题编号',
    //   field: 'number',
    // },
    {
      label: '名称',
      field: 'name',
    },
    {
      label: '提出日期',
      field: 'proposedTime',
      type: 'date',
      formatter: (val) => dayjs(val).format('YYYY-MM-DD') || '',
    },
    {
      label: '问题提出人',
      field: 'exhibitorName',
    },
    {
      label: '问题类型',
      field: 'questionTypeName',
    },
    {
      label: '问题负责人',
      field: 'principalName',
    },
    {
      label: '期望完成日期',
      field: 'predictEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') : ''),
    },
    {
      label: '优先级',
      field: 'priorityLevelName',
    },
    {
      label: '关联风险',
      field: 'riskName',
      hidden: !props.riskName,
    },
    // {
    //   label: '创建时间',
    //   field: 'createTime',
    //   formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    // },
    // {
    //   label: '修改人',
    //   field: 'modifyName',
    // },
    // {
    //   label: '修改时间',
    //   field: 'modifyTime',
    //   formatter: (val) => dayjs(val).format('YYYY-MM-DD HH:mm:ss') || '',
    // },
    // {
    //   label: '进度',
    //   field: 'scheduleName',
    //   gridColumn: '1/5',
    // },
    // {
    //   label: '问题描述',
    //   field: 'content',
    //   type: 'textarea',
    //   gridColumn: '1/5',
    // },
  ]),
  column: 4,
  dataSource: detailsData,
});
const baseDevelopmentProps = reactive({
  list: setBasicInfo([
    {
      label: '关联产品/物料编码',
      field: 'productNumber',
      formatter: (val, record) => {
        let valueList = [];
        if (record.productNumber) {
          valueList.push(record.productNumber);
        }
        if (record.materialNumber) {
          valueList.push(record.materialNumber);
        }
        return valueList.join('/');
      },
    },
    {
      label: '关联产品编号',
      field: 'productNumberSn',
    },
    {
      label: '关联生产订单',
      field: 'productionOrders',
    },
    {
      label: '阶段',
      field: 'stageName',
    },
    {
      label: '过程环节',
      field: 'processLinkName',
    },
    {
      label: '过程分类',
      field: 'processClassifiName',
    },
    {
      label: '问题现象分类',
      field: 'problemPhenomenonOneName',
      formatter: (val, record) => {
        let valueList = [];
        if (record.problemPhenomenonOneName) {
          valueList.push(record.problemPhenomenonOneName);
        }
        if (record.problemPhenomenonTwoName) {
          valueList.push(record.problemPhenomenonTwoName);
        }
        if (record.problemPhenomenonThName) {
          valueList.push(record.problemPhenomenonThName);
        }
        return valueList.join('-');
      },
    },
    {
      label: '问题等级分类',
      field: 'problemLevelName',
    },
    {
      label: '问题原因分类',
      field: 'reasionOneName',
      gridColumn: '1/3',
      formatter: (val, record) => {
        let valueList = [];
        if (record.reasionOneName) {
          valueList.push(record.reasionOneName);
        }
        if (record.reasionTwoName) {
          valueList.push(record.reasionTwoName);
        }
        if (record.reasionThreeName) {
          valueList.push(record.reasionThreeName);
        }
        return valueList.join('-');
      },
    },
    {
      label: '纠正分类',
      field: 'correctClassifiName',
      gridColumn: '3/5',
    },
    {
      label: '原因分析描述',
      field: 'reasionRemark',
      gridColumn: '1/3',
    },
    {
      label: '纠正描述',
      field: 'correctRemark',
      gridColumn: '3/5',
    },
    {
      label: '关联ECN编号',
      field: 'ecnNumber',
      gridColumn: '1/3',
    },
    {
      label: '问题升级类型',
      field: 'questionUpType',
      gridColumn: '3/5',
    },
    {
      label: '纠正措施描述',
      field: 'correAcDescription',
      gridColumn: '1/3',
    },
    {
      label: '举一反三',
      field: 'oneCaseToAnother',
      gridColumn: '3/5',
    },
  ]),
  column: 4,
  dataSource: detailsData,
});
const baseReviewProps = reactive({
  list: setBasicInfo([
    {
      label: '意见类别',
      field: 'opinionCategoriesName',
    },
    {
      label: '意见分类',
      field: 'opClassificationName',
    },
    {
      label: '是否技术问题',
      field: 'isTechnicalIssues',
      formatter: (val) => (val ? '是' : '否'),
    },
    {
      label: '采纳情况',
      field: 'adoptionSituationName',
    },
    {
      label: '评审要点',
      field: 'reviewPoints',
    },
    {
      label: '是否典型问题',
      field: 'isTypicalProblems',
      formatter: (val) => (val ? '是' : '否'),
    },
    {
      label: '整改情况描述',
      field: 'overallDescription',
      gridColumn: '1/5',
    },
  ]),
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <div class="basic-info-content">
    <BasicCard
      title="问题基本信息"
      :grid-content-props="baseInfoProps"
      :isBorder="false"
    />
    <BasicCard title="问题描述">
      <div v-html="detailsData.content" />
    </BasicCard>
    <BasicCard title="执行记录">
      <a-timeline class="timeline-box">
        <a-timeline-item
          v-for=" item in detailsData.planDetail"
          :key="item.id"
        >
          <div>{{ dayjs(item.modifyTime ).format('YYYY-MM-DD HH:mm:ss') }}</div>
          <div>{{ item.modifyName +":"+ item.reply }}</div>
        </a-timeline-item>
      </a-timeline>
    </BasicCard>
    <!-- <BasicCard
      v-if="detailsData.questionType==='questionType_1'"
      title="产品研发问题信息"
      :grid-content-props="baseDevelopmentProps"
      :isBorder="false"
    />
    <BasicCard
      v-if="detailsData.questionType==='questionType_2'"
      title="评审问题信息"
      :grid-content-props="baseReviewProps"
      :isBorder="false"
    /> -->
  </div>
</template>

<style scoped lang="less">
.basic-info-content{
  height: 100%;
}
.timeline-box{
  padding-top: 10px;
}
</style>
