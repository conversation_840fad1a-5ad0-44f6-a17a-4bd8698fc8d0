<template>
  <BasicDrawer
    v-bind="$attrs"
    title="合同变更申请"
    :width="1000"
    :showFooter="true"
    :maskClosable="false"
    @register="modalRegister"
    @ok="onOk"
    @visibleChange="visibleChange"
  >
    <!--每次打开，根据visibleStatus来重置业务组件-->
    <BusinessComponent
      v-if="state.visibleStatus"
      ref="businessComponentRef"
      :editData="state.editData"
      :allMoney="state.allMoney"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import BusinessComponent from './ChangeContractForm.vue';
import Api from '/@/api';

const state = reactive({
  type: 'add' as 'add' | 'edit',
  visibleStatus: false,
  // 合同ID
  contractId: '',
  editData: null,
  successChange: null,
});
const businessComponentRef = ref();

const [modalRegister, { closeDrawer, setDrawerProps, changeOkLoading }] = useDrawerInner((openProps:{type: 'add' | 'edit', contractId: string, editData?:any, allMoney: string, successChange: any}) => {
  const {
    type, editData, successChange, allMoney, contractId,
  } = openProps;

  Object.assign(state, {
    type,
    editData,
    successChange,
    allMoney,
    contractId,
  });

  if (state.type === 'add') {
    setDrawerProps({
      title: '合同变更申请',
    });
  } else {
    setDrawerProps({
      title: '编辑变更申请',
    });
  }

  if (editData) {
    setEditInfo(editData);
  }

  state.visibleStatus = true;
});

// 设置编辑信息
async function setEditInfo(editData) {
  const details = await new Api(`/pas/projectContract/all/${editData.id}`).fetch('', '', 'get');

  businessComponentRef.value?.setValues(details);
}

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

async function onOk() {
  const params = await businessComponentRef.value?.getFormData();
  params.projectContractChangeApplyDTO.contractId = state.contractId;

  changeOkLoading(true);
  try {
    if (state.type === 'edit') {
      params.projectContractChangeApplyDTO.id = state.editData.id;
      await new Api('/pas/projectContractChangeApply').fetch(params, '', 'PUT');
    } else {
      await new Api('/pas/projectContractChangeApply').fetch(params, '', 'POST');
    }
  } finally {
    changeOkLoading(false);
  }

  message.success('操作成功');
  state.successChange?.();
  closeDrawer();
}
</script>
