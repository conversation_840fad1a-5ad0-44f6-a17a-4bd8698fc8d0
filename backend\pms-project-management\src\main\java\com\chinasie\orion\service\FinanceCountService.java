package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.vo.FinanceCountVO;
import com.chinasie.orion.domain.vo.PageResultVO;
import com.chinasie.orion.sdk.metadata.page.Page;

public interface FinanceCountService  {
    FinanceCountVO getCount(String contractId);

    PageResultVO getPageResult(Page<InvoicingRevenueAccountingDTO> pageRequest) throws Exception;
}
