package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneTemplateDTO;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneTemplateVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeMilestoneTemplateService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * <p>
 * ProjectSchemeMilestoneTemplate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
@RestController
@RequestMapping("/projectSchemeMilestoneTemplate")
@Api(tags = "项目计划里程碑模版")
public class ProjectSchemeMilestoneTemplateController {

    @Autowired
    private ProjectSchemeMilestoneTemplateService projectSchemeMilestoneTemplateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目计划里程碑模版", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeMilestoneTemplateVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectSchemeMilestoneTemplateVO rsp = projectSchemeMilestoneTemplateService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectSchemeMilestoneTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目计划里程碑模版", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectSchemeMilestoneTemplateVO> create(@RequestBody  ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        ProjectSchemeMilestoneTemplateVO rsp =  projectSchemeMilestoneTemplateService.create(projectSchemeMilestoneTemplateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectSchemeMilestoneTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目计划里程碑模版", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        Boolean rsp = projectSchemeMilestoneTemplateService.edit(projectSchemeMilestoneTemplateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目计划里程碑模版", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectSchemeMilestoneTemplateService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "项目计划里程碑模版", subType = "分页", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeMilestoneTemplateVO>> pages(@RequestBody Page<ProjectSchemeMilestoneTemplateDTO> pageRequest) throws Exception {
        Page<ProjectSchemeMilestoneTemplateVO> rsp =  projectSchemeMilestoneTemplateService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @param projectSchemeMilestoneTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】列表", type = "项目计划里程碑模版", subType = "列表", bizNo = "")
    public ResponseDTO<List<ProjectSchemeMilestoneTemplateVO>> lists(@RequestBody(required = false) ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        List<ProjectSchemeMilestoneTemplateVO> list =  projectSchemeMilestoneTemplateService.lists(projectSchemeMilestoneTemplateDTO);
        return new ResponseDTO<>(list);
    }

    @ApiOperation(value = "启用")
    @RequestMapping(value = "/use", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】启用", type = "项目计划里程碑模版", subType = "启用", bizNo = "")
    public ResponseDTO<Boolean> useTemplate(@RequestParam(value = "id") String id) throws Exception {
        Boolean rsp = projectSchemeMilestoneTemplateService.enable(id);
        return new ResponseDTO<>(rsp);
    }
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/stop", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】禁用", type = "项目计划里程碑模版", subType = "禁用", bizNo = "")
    public ResponseDTO<Boolean> stopTemplate(@RequestParam(value = "id") String id) throws Exception {
        Boolean rsp = projectSchemeMilestoneTemplateService.disEnable(id);
        return new ResponseDTO<>(rsp);
    }
}
