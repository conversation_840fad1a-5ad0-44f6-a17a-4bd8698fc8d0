package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.vo.BasicUserInfoVO;
import com.chinasie.orion.domain.vo.BasicUserVO;
import java.lang.String;
import java.util.List;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * BasicUser 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 09:17:18
 */
public interface BasicUserService extends OrionBaseService<BasicUser> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    BasicUserVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param BasicUserDTO
     */
    String create(BasicUserDTO basicUserDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param BasicUserDTO
     */
    Boolean edit(BasicUserDTO basicUserDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BasicUserVO> pages(Page<BasicUserDTO> pageRequest) throws Exception;


    /**
     * 分页查询人员基本信息
     * <p>
     * * @param pageRequest
     */
    Page<BasicUserInfoVO> getUserInfo(Page<BasicUserDTO> pageRequest) throws Exception;


    /**
     * 分页查询人员基本信息
     * <p>
     * * @param pageRequest
     */
    void exportByExcel(Page<BasicUserDTO> pageRequest, HttpServletResponse response) throws Exception;


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BasicUserVO> vos) throws Exception;


    /**
     *  通过编号获取用户信息
     * @param number
     * @return
     */
    BasicUser getSingleEntity(String number);


    /**
     *  通过编号获取人眼信息Map
     * @param numberList
     * @return
     */
    Map<String,BasicUser> getMapByNumberList(List<String> numberList);

    /**
     *  获取用户信息
     * @param userCode
     * @return
     */
    BasicUserVO detailUserCode(String userCode);

    /**
     *  获取列表通过 编号
     * @param collect
     * @return
     */
    List<BasicUser> listByNumberList(List<String> collect);

    /**
     * 离岗/在职
     * @param basicUserDTOs
     * @return
     */
    Boolean duty(List<BasicUserDTO> basicUserDTOs) throws Exception;

    /**
     * 根据身份证号查询
     * @param basicUserDTO
     * @return
     */
    BasicUserVO detailValidate(BasicUserDTO basicUserDTO);

    /**
     * 查下用户信息
     * @param userCodes
     * @return
     */
    List<BasicUserVO> listByUserCodes(List<String> userCodes);

    Map<String, BasicUser> getSimMapByNumberList(List<String> codeList);
}
