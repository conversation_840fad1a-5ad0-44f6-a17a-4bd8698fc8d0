package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/15
 * @description:
 */
@Data
public class ProjectIncomeUpdateBatchDTO {

    @NotEmpty(message = "未选择收益")
    @ApiModelProperty(value = "收益id")
    private List<String> ids;

    @ApiModelProperty(value = "销售是否结束")
    private Boolean saleOver;

    /**
     * 现预期总产出
     */
    @ApiModelProperty(value = "现预期总产出")
    private BigDecimal expectedOutcomes;
}
