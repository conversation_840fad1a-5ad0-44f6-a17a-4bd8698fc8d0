package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ContractCenter VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:31:28
 */
@ApiModel(value = "ContractCenterVO对象", description = "用人中心")
@Data
public class ContractCenterVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 用人单位代号
         */
        @ApiModelProperty(value = "用人单位代号")
        private String centerCode;


        /**
         * 用人中心名称
         */
        @ApiModelProperty(value = "用人单位名称")
        private String centerName;

        /**
         * 用人中心类型
         */
        @ApiModelProperty(value = "用人单位类型")
        private String centerType;
    

}
