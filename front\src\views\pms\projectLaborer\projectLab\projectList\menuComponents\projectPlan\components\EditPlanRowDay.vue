<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch, onUnmounted,
} from 'vue';
import { InputNumber as AInputNumber } from 'ant-design-vue';
const props = withDefaults(defineProps<{
    record:object,
}>(), {
  record: () => ({}),
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const durationDays:Ref<string> = ref('');
// 下拉框展开操作
function handleMouseleave() {
  isEdit.value = false;
}

const handleClick = (event) => {
  if (props.record.status === 101) {
    isEdit.value = true;
    durationDays.value = props.record.durationDays;
  } else {
    isEdit.value = false;
  }
};
onMounted(() => {
  durationDays.value = props.record.durationDays;
});
function pressEnter(value: []) {
  emit('change', durationDays.value, changeItemType);
}
function changeItemType() {
  isEdit.value = false;
}
defineExpose({
  changeItemType: () => {
    isEdit.value = false;
  },
});
</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
    @mouseenter="handleClick"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="record.durationDays"
      @click="handleClick"
    >
      {{ record.durationDays }}
    </div>
    <div
      v-else
      class="row-name-value"
    >
      <AInputNumber
        v-model:value="durationDays"
        :min="0"
        @pressEnter="pressEnter"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
