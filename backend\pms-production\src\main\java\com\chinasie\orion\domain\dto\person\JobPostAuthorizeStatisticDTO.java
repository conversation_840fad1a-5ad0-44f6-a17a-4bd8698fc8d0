package com.chinasie.orion.domain.dto.person;


import com.chinasie.orion.domain.vo.JobPostAuthorizeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * @description:
 */
@Data
public class JobPostAuthorizeStatisticDTO extends JobPostAuthorizeVO {

    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "加入作业数量")
    private int jobNumber;


}
