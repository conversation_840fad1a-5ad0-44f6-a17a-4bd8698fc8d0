package com.chinasie.orion.management.constant;

/**
 * 客户状态字典
 */


public enum CustomerStatusEnum {

    ENABLE("enable","启用"),
    DISABLE("disable","停用");

    private String name;
    private String desc;

    CustomerStatusEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (CustomerStatusEnum lt : CustomerStatusEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}