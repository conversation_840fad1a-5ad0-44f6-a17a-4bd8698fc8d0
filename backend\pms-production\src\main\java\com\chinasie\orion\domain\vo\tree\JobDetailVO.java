package com.chinasie.orion.domain.vo.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/18/16:02
 * @description:
 */
@Data
public class JobDetailVO implements Serializable {

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "关系ID")
    private String  relationId;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;
    @ApiModelProperty(value = "是否重大项目")
    private Boolean  isMajorProject;
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date beginTime;
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;

    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;

    @ApiModelProperty(value = "工期")
    private Integer workDuration;

    private String  firstExecute;
    @ApiModelProperty(value = "首次执行")
    private String firstExecuteName;
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;
    @ApiModelProperty(value = "高风险等级")
    private String heightRiskLevelName;

    private String antiForfeignLevel;
    @ApiModelProperty(value = "防异物等级名称")
    private String antiForfeignLevelName;

    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;

    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;

    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;

    @ApiModelProperty(value = "管理人员Id")
    private String managePersonId;

    @ApiModelProperty(value = "管理人员工号")
    private String managePersonCode;

    @ApiModelProperty(value = "管理人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作包审查状态")
    private Integer workPackageStatus;

    @ApiModelProperty(value = "高风险描述拼接")
    private String highRiskDescribe;

    @ApiModelProperty(value = "协助")
    private Boolean isCollaboration;
    @ApiModelProperty(value = "协同专业名称拼接")
    private String collaborationNames;
    @ApiModelProperty(value = "协同专业ID拼接")
    private String collaborationIds;

    @ApiModelProperty(value = "作业数")
    @StatisticField(value = "jobCount", type = StatisticType.SUM)
    private Integer jobCount=0;
    /**
     *  统计作业中字段为必填项，但未填信息的字段总数量
     */
    @ApiModelProperty(value = "必填未填数")
    @StatisticField(value = "requiredCount", type = StatisticType.SUM)
    private Integer requiredCount=0;
    /**
     * 统计作业中重大项目打钩的总数量
     */
    @ApiModelProperty(value = "重大项目数")
    @StatisticField(value = "majorProjectCount", type = StatisticType.SUM)
    private Integer majorProjectCount=0;
    @ApiModelProperty(value = "重大项目工作包已审查数")
    @StatisticField(value = "majorAuditProjectCount", type = StatisticType.SUM)
    private Integer majorAuditProjectCount=0;
    @ApiModelProperty(value = "重大项目工单审查进展（%）")
    @StatisticField(value = "majorProjectAuditP", type = StatisticType.PERCENTAGE, fields = {"majorAuditProjectCount","majorProjectCount"})
    private double majorProjectAuditP=0;
    @ApiModelProperty(value = "高风险作业数：统计高风险为“是”的作业总数")
    @StatisticField(value = "highRiskCount", type = StatisticType.SUM)
    private Integer highRiskCount=0;

    @ApiModelProperty(value = "高风险未报备数：作业中高风险为是，但高风险等级信息为空（安质环系统未推送信息）的作业总数量")
    @StatisticField(value = "highRiskNotTellCount", type = StatisticType.SUM)
    private Integer highRiskNotTellCount=0;

    /**
     * 高风险等级数：统计高风险等级总数。如：一级：5，二级：12，三级：20;
     * 防异物等级数：统计防异物等级总数。如：一级：5，二级：12，三级：20,NA：5;
     */
    @StatisticField(value = "highRiskOneCount", type = StatisticType.SUM)
    private Integer highRiskOneCount=0;
    @StatisticField(value = "highRiskTwoCount", type = StatisticType.SUM)
    private Integer highRiskTwoCount=0;
    @StatisticField(value = "highRiskThreeCount", type = StatisticType.SUM)
    private Integer highRiskThreeCount=0;
    @StatisticField(value = "antiForfeignLevelOneCount", type = StatisticType.SUM)
    private Integer antiForfeignLevelOneCount=0;
    @StatisticField(value = "antiForfeignLevelTwoCount", type = StatisticType.SUM)
    private Integer antiForfeignLevelTwoCount=0;
    @StatisticField(value = "antiForfeignLevelThreeCount", type = StatisticType.SUM)
    private Integer antiForfeignLevelThreeCount=0;
    @StatisticField(value = "antiForfeignLevelNaCount", type = StatisticType.SUM)
    private Integer antiForfeignLevelNaCount=0;
}
