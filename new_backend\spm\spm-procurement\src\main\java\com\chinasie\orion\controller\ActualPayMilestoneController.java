package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.ActualPayMilestoneDTO;
import com.chinasie.orion.domain.vo.ActualPayMilestoneVO;
import com.chinasie.orion.service.ActualPayMilestoneService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ActualPayMilestone 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/actualPayMilestone")
@Api(tags = "合同支付里程碑（实际）")
public class ActualPayMilestoneController {

    @Autowired
    private ActualPayMilestoneService actualPayMilestoneService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同支付里程碑（实际）", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ActualPayMilestoneVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ActualPayMilestoneVO rsp = actualPayMilestoneService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param actualPayMilestoneDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#actualPayMilestoneDTO.name}}】", type = "合同支付里程碑（实际）", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ActualPayMilestoneDTO actualPayMilestoneDTO) throws Exception {
        String rsp = actualPayMilestoneService.create(actualPayMilestoneDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param actualPayMilestoneDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#actualPayMilestoneDTO.name}}】", type = "合同支付里程碑（实际）", subType = "编辑", bizNo = "{{#actualPayMilestoneDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ActualPayMilestoneDTO actualPayMilestoneDTO) throws Exception {
        Boolean rsp = actualPayMilestoneService.edit(actualPayMilestoneDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "合同支付里程碑（实际）", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = actualPayMilestoneService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "合同支付里程碑（实际）", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = actualPayMilestoneService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同支付里程碑（实际）", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ActualPayMilestoneVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<ActualPayMilestoneDTO> pageRequest) throws Exception {
        Page<ActualPayMilestoneVO> rsp = actualPayMilestoneService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询支付里程碑（计划）
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询支付里程碑（计划）")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ContractLineInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ActualPayMilestoneVO>> getByCode(@RequestBody Page<ActualPayMilestoneDTO> pageRequest) throws Exception {
        Page<ActualPayMilestoneVO> rsp = actualPayMilestoneService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同支付里程碑（实际）导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "合同支付里程碑（实际）", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        actualPayMilestoneService.downloadExcelTpl(response);
    }

    @ApiOperation("合同支付里程碑（实际）导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "合同支付里程碑（实际）", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = actualPayMilestoneService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同支付里程碑（实际）导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "合同支付里程碑（实际）", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = actualPayMilestoneService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同支付里程碑（实际）导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "合同支付里程碑（实际）", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = actualPayMilestoneService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同支付里程碑（实际）导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "合同支付里程碑（实际）", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        actualPayMilestoneService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("合同支付里程碑（实际）发送邮件提醒")
    @PostMapping(value = "/sendEmailAndRemind")
    @LogRecord(success = "【{USER{#logUserId}}】发送邮件提醒", type = "合同支付里程碑（实际）", subType = "发送邮件提醒", bizNo = "")
    public void sendEmailAndRemind() throws Exception {
        actualPayMilestoneService.sendEmailAndRemind();
    }

}
