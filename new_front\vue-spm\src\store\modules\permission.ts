import type { AppRouteRecordRaw, Menu } from '/@/router/types';

import { defineStore } from 'pinia';
import { store } from '/@/store';
import { useI18n } from '/@/hooks/web/useI18n';
import { useUserStore } from './user';
import { useAppStoreWidthOut } from './app';
import { transformObjToRoute, transformTreeMenuToFlatRouteData } from '/@/router/helper/routeHelper';
import { transformRouteToMenu } from '/@/router/helper/menuHelper';

import projectSetting from '/@/settings/projectSetting';

import { PermissionModeEnum } from '/@/enums/appEnum';
import { ERROR_LOG_ROUTE, PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

import { useMessage } from '/@/hooks/web/useMessage';

import Api from '/@/api';

import { testMenu } from '/@/store/modules/testMenu';
import { isArray } from '/@/utils/is';
import { isQianKun, MICRO_NAME, useQiankun } from '/@/utils/qiankun/useQiankun';

interface PermissionState {
  // Permission code list
  permCodeList: string[];
  // Whether the route has been dynamically added
  isDynamicAddedRoute: boolean;
  // To trigger a menu update
  lastBuildMenuTime: number;
  // Backstage menu list
  backMenuList: Menu[];
}
export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): PermissionState => ({
    permCodeList: [],
    // Whether the route has been dynamically added
    isDynamicAddedRoute: false,
    // To trigger a menu update
    lastBuildMenuTime: 0,
    // Backstage menu list
    backMenuList: [],
  }),
  getters: {
    getPermCodeList() {
      return this.permCodeList;
    },
    getBackMenuList() {
      return this.backMenuList;
    },
    getLastBuildMenuTime() {
      return this.lastBuildMenuTime;
    },
    getIsDynamicAddedRoute() {
      return this.isDynamicAddedRoute;
    },
  },
  actions: {
    setPermCodeList(codeList: string[]) {
      this.permCodeList = codeList;
    },

    setBackMenuList(list: Menu[]) {
      this.backMenuList = list;
    },

    setLastBuildMenuTime() {
      this.lastBuildMenuTime = new Date().getTime();
    },

    setDynamicAddedRoute(added: boolean) {
      this.isDynamicAddedRoute = added;
    },
    resetState(): void {
      this.isDynamicAddedRoute = false;
      this.permCodeList = [];
      this.backMenuList = [];
      this.lastBuildMenuTime = 0;
    },
    async getMenuList(): Promise<AppRouteRecordRaw[] | any> {
      // 加载微前端菜单
      const { menuData } = useQiankun();
      if (isQianKun() && menuData[MICRO_NAME] && menuData[MICRO_NAME].length) {
        return menuData[MICRO_NAME];
      }
      // 加载本地配置菜单数据
      if (testMenu && isArray(testMenu) && testMenu.length) {
        return testMenu;
      }
      const baseApi = new Api('/pmi/power/function');
      return await baseApi.fetch({}, 'current-user/menu', 'GET');
    },

    /**
     * 排序方法
     * @param meaus
     */
    sortFuc(meaus: any[]) {
      meaus.forEach((item) => {
        meaus.sort((a, b) => (a.sort || 0) - (b.sort || 0));
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          this.sortFuc(item.children);
        }
      });
    },

    async buildRoutesAction(id?: number | string): Promise<AppRouteRecordRaw[]> {
      const { t } = useI18n();
      const userStore = useUserStore();
      const appStore = useAppStoreWidthOut();

      let routes: AppRouteRecordRaw[] = [];
      const { permissionMode = projectSetting.permissionMode } = appStore.getProjectConfig;

      if (permissionMode === PermissionModeEnum.ROLE) {

        //  If you are sure that you do not need to do background dynamic permissions, please comment the entire judgment below
      } else if (permissionMode === PermissionModeEnum.BACK) {
        const { createMessage } = useMessage();
        if (!isQianKun()) {
          createMessage.loading({
            content: t('sys.app.menuLoading'),
            duration: 1,
          });
        }

        const menuData = await this.getMenuList();

        // 保存原始菜单数据
        this.setBackMenuList(transformRouteToMenu(menuData));

        // 重新处理树形菜单信息，详情页可挂在列表页上
        const treeMenuToFlatRouteData = transformTreeMenuToFlatRouteData(menuData);

        // 把抽取，带组件的数据，挂载组件文件和嵌套到LAYOUT框架中
        const routeList = transformObjToRoute(treeMenuToFlatRouteData);

        routes = [PAGE_NOT_FOUND_ROUTE, ...routeList];
      }
      routes.push(ERROR_LOG_ROUTE);
      return routes;
    },
  },
});

// Need to be used outside the setup
export function usePermissionStoreWidthOut() {
  return usePermissionStore(store);
}
