package com.chinasie.orion.service.projectStatistics;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.QuestionStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.QuestionStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.QuestionStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * QuestionStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:37:28
 */
public interface QuestionStatusStatisticsService  extends OrionBaseService<QuestionStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    QuestionStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param questionStatusStatisticsDTO
     */
    QuestionStatusStatisticsVO create(QuestionStatusStatisticsDTO questionStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param questionStatusStatisticsDTO
     */
    Boolean edit(QuestionStatusStatisticsDTO questionStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<QuestionStatusStatisticsVO> pages(Page<QuestionStatusStatisticsDTO> pageRequest) throws Exception;

}
