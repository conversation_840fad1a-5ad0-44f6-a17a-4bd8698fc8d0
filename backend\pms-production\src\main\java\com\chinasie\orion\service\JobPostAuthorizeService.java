package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.entity.JobPostAuthorize;
import com.chinasie.orion.domain.dto.JobPostAuthorizeDTO;
import com.chinasie.orion.domain.vo.JobPostAuthorizeVO;

import java.lang.String;
import java.util.Date;
import java.util.List;


import com.chinasie.orion.domain.vo.job.AuthorizeInfoVO;
import com.chinasie.orion.domain.vo.job.JobEquivalentInfoVO;
import com.chinasie.orion.domain.vo.job.JobPostAuthorizeDetailVO;
import com.chinasie.orion.domain.vo.job.JobPostAuthorizeInfoVO;
import com.chinasie.orion.domain.vo.validation.ValidationResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobPostAuthorize 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
public interface JobPostAuthorizeService extends OrionBaseService<JobPostAuthorize> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobPostAuthorizeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobPostAuthorizeDTO
     */
    String create(JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobPostAuthorizeDTO
     */
    Boolean edit(JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobPostAuthorizeVO> pages(Page<JobPostAuthorizeDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobPostAuthorizeVO> vos,String jobId) throws Exception;

    /**
     *
     *  岗位授权保存
     * @param jobPostAuthorizeDTO
     * @return
     */
    Boolean manageSave(JobPostAuthorizeParamDTO jobPostAuthorizeDTO) throws Exception;

    /**
     *  获取岗位 授权信息
     * @param id
     * @return
     */
    JobPostAuthorizeDetailVO manageInfo(String id) throws Exception;

    /**
     *  获取岗位授权对应的等效岗位列表
     * @param id
     * @return
     */
    List<JobEquivalentInfoVO> manageEquivalentInfoList(String id);

    /**
     *  岗位等效新增 （只能和已经落地的 数据等效）
     * @param jobPostEquDTO
     * @return
     */
    Boolean manageEquivalentInfoSave(JobPostEquDTO jobPostEquDTO);

    /**
     *  通过用户获取可供等效的 授权信息
     * @param userCode
     * @return
     */
    List<AuthorizeInfoVO> manageEquivalentInfoListByUser(String userCode);

    /**
     * 授权核验数据
     * @param idList
     * @return
     */
    String authorizeValidation(String jobId,List<String> idList);

    /**
     *  获取人员 岗位授权信息
     * @param userCode
     * @return
     */
    List<JobPostAuthorizeInfoVO> personJobPostAuthorizeList(String userCode);

    List<JobPostAuthorize> listByRepairRound(String repairRound);

    /**
     *  批量新增 人员列表
     * @param jobPostAuthorizeDTO
     * @return
     */
    Boolean addPersonList(JobAuthorizeUserParamDTO jobPostAuthorizeDTO) throws Exception;

    /**
     *  编辑作业岗位
     * @param personJobPostDTO
     * @return
     */
    Boolean editJobPost(PersonJobPostDTO personJobPostDTO);

    /**
     *  岗位等效分页
     * @param pageRequest
     * @return
     */
    Page<JobPostAuthorizeInfoVO> userEquivalentPage(Page<JobPostAuthorizeDTO> pageRequest) throws Exception;

    /**
     *  通过关系id 删除岗位等效
     * @param idList
     * @return
     */
    Boolean delJobEquList(List<String> idList) throws Exception;

    /**
     *  进入基地时间
     * @param baseCode
     * @param date
     */
    void updateJoinBaseTime(String baseCode, Date date);

    JobPostAuthorizeInfoVO getSimpleBy(String authorizeId);

    /**
     *  授权核验新
     * @param jobId
     * @param idList
     * @return
     */
    ValidationResult authorizeValidationNew(String jobId, List<String> idList);

    /**
     *  批量移除数据
     * @param jobPersonParamDTO
     * @return
     */
    Boolean removeBatchNew(JobPersonParamDTO jobPersonParamDTO);

    void updatePersonLedgerId(String jobId, String personLedgerId, String personManageId,String userCode,String oldManageId);

    /**
     *  通过人员ID获取 作业id列表
     * @param id
     * @return
     */
    List<String> listByPersonId(String id);

    void copyByJoIdToTargetId(String sourceId, List<String> targetIdList);

    Boolean editDate(StartEndDTO startEndDTO);

    /**
     *  拥有新人的作业
     * @param idList
     * @return
     */
    List<String> listByJobIdList(List<String> idList);

    void updateJobNewcomer(String id, Boolean isNewcomer);
}
