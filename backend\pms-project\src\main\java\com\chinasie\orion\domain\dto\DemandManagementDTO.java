package com.chinasie.orion.domain.dto;

import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:57
 * @description:
 */
@Data
@ApiModel(value = "DemandManagementDTO对象", description = "需求管理")
public class DemandManagementDTO extends ObjectDTO {

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @NotEmpty(message = "负责人不能为空")
    private String principalId;
    private String principalName;

    /**
     * 接收人Id
     */
    @ApiModelProperty(value = "接收人Id")
    private String recipientId;
    private String recipientName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String source;

    /**
     * 父级ID
     */
    @NotEmpty(message = "所属需求不能为空")
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 期望完成日期
     */
    @ApiModelProperty(value = "期望完成日期")
    private Date predictEndTime;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityLevel;

    /**
     * 提出时间
     */
    @ApiModelProperty(value = "提出时间")
    private Date proposedTime;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String type;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String exhibitor;
    private String exhibitorName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    /**
     * 进度
     */
    @DecimalMin(value = "0.00", message = "进度值应大于等于0")
    @DecimalMax(value = "100.00", message = "进度值应小于或等于100")
    @ApiModelProperty(value = "进度")
    private BigDecimal schedule;



    @ApiModelProperty("属性值")
    private List<TypeAttrValueDTO> typeAttrValueDTOList;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;

}
