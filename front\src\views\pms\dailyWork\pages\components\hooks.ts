import { Ref, ref } from 'vue';

// 根据路由名称获取公共组件对应配置

interface Config {
    detailRouteName?: string

    [propName: string]: any
}

// 授权组件
export function useAuthPageConfig(routeName: string) {
  const detailRouteName: Ref<string> = ref();
  switch (routeName) {
    case 'PMSDailyWorkDetails':
      detailRouteName.value = 'PMSDailyWorkAuthDetails';
      break;
    case 'PMSMajorRepairsWorkDetails':
      detailRouteName.value = 'PMSMajorRepairsAuthDetails';
      break;
  }
  return {
    detailRouteName,
  };
}

// 物资组件
export function useMaterialPageConfig(routeName: string) {
  let config: Config = {};
  switch (routeName) {
    case 'PMSDailyWorkDetails':
      config = {
        detailRouteName: 'PMSDailyWorkMaterialDetails',
      };
      break;
    case 'PMSMajorRepairsWorkDetails':
      config = {
        detailRouteName: 'PMSMajorRepairsMaterialDetails',
      };
      break;
  }
  return config;
}

// 风险组件
export function useRiskPageConfig(routeName: string) {
  let config: Config = {};
  switch (routeName) {
    case 'PMSDailyWorkDetails':
      config = {
        detailRouteName: 'PMSDailyWorkRiskDetails',
      };
      break;
    case 'PMSMajorRepairsWorkDetails':
      config = {
        detailRouteName: 'PMSMajorRepairsRiskDetails',
      };
      break;
  }
  return config;
}
