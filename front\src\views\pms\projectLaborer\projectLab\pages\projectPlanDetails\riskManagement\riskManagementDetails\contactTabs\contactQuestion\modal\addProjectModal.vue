<template>
  <div class="addNodeModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          ref="name"
          label="名称"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <a-form-item
          label="风险类型"
          name="riskType"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.riskType"
            size="large"
            placeholder="请选择风险类型"
          >
            <a-select-option
              v-for="(item, index) in riskType"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="发生概率"
          name="riskProbability"
        >
          <a-select
            v-model:value="formState.riskProbability"
            size="large"
            placeholder="请选择发生概率"
          >
            <a-select-option
              v-for="(item, index) in riskProbability"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="风险影响"
          name="riskInfluence"
        >
          <a-select
            v-model:value="formState.riskInfluence"
            size="large"
            placeholder="请选择风险影响"
          >
            <a-select-option
              v-for="(item, index) in riskInfluence"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="识别人"
          name="discernPerson"
        >
          <a-select
            v-model:value="formState.discernPerson"
            show-search
            placeholder="请输入识别人"
            :filter-option="filterHandle"
            :options="personOption"
            size="large"
            @search="personHandleChange"
          />
        </a-form-item>
        <a-form-item
          label="预估发生时间"
          name="predictStartTime"
        >
          <a-select
            v-model:value="formState.predictStartTime"
            size="large"
            placeholder="预估发生时间"
          >
            <a-select-option
              v-for="(item, index) in predictStartTime"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="风险描述">
          <a-textarea
            v-model:value="formState.remark"
            show-count
            :maxlength="200"
            placeholder="请输入风险描述"
            allow-clear
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="预计完成时间">
          <a-date-picker
            v-model:value="formState.predictEndTime"
            size="large"
            placeholder="请选择预计完成时间"
            allow-clear
          />
        </a-form-item>
        <a-form-item
          label="负责人"
          name="principalId"
        >
          <a-select
            v-model:value="formState.principalId"
            show-search
            :filter-option="filterHandle"
            placeholder="请输入负责人"
            :options="roleOption"
            size="large"
            @search="handleChange"
          />
        </a-form-item>

        <a-form-item
          label="应对策略"
          name="copingStrategy"
        >
          <a-select
            v-model:value="formState.copingStrategy"
            size="large"
            placeholder="请选择应对策略"
          >
            <a-select-option
              v-for="(item, index) in copingStrategy"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="应对措施"
          name="solutions"
        >
          <a-textarea
            v-model:value="formState.solutions"
            show-count
            :maxlength="200"
            placeholder="请输入应对措施"
            :rows="4"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <DrawerFooterButtons
          v-model:checked="nextCheck"
          :isContinue="formType == 'add'"
          :loading="loading"
          @cancelClick="cancel"
          @okClick="onSubmit"
        />
      </template>
    </a-drawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
  DatePicker,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import {
  addRiskApi, editRiskApi,
  startEffectRiskListApi,
  probRiskListApi,
  RistTypeApi,
  EffectRiskListApi,
  soluteRiskListApi,
  roleListApi,
  planTreeListApi,
} from '/@/views/pms/projectLaborer/api/riskManege';

import dayjs from 'dayjs';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';

export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,

    aDrawer: Drawer,

    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,

    ADatePicker: DatePicker,
    DrawerFooterButtons,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: {
      type: Array,
      default: () => [],
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: <any>{
        name: '',
        // 类型
        riskType: '',
        //   //关联1
        //   planId: '',
        // 概率2
        riskProbability: '',
        // 影响3
        riskInfluence: '',
        // 负责人4
        principalName: '',
        // 负责人4
        principalId: '',

        // 识别人
        discernPerson: '',
        // 识别人
        discernPersonName: '',

        // 预期发生时间
        predictStartTime: '',
        // 风险描述255
        remark: '',
        // 预计完成时间
        predictEndTime: '',
        // 应对策略5
        copingStrategy: '',
        // 应对措施 255字节
        solutions: '',
      },
      oldformState: {},
      // getselectList:{
      //     riskType:{},
      // },
      // 预期发生时间
      predictStartTime: [], // 类型
      riskType: <any>[],
      // 关联1
      // planId: <any>[],
      // 概率2
      riskProbability: <any>[],
      // 影响3
      riskInfluence: <any>[],
      // 负责人4
      principalId: <any>[],
      roleOption: <any>[],
      personOption: <any>[],
      // 应对策略5
      copingStrategy: <any>[],
      showVisible: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
        {
          min: 1,
          max: 25,
          message: '名称长度应在1~25位',
          trigger: 'blur',
        },
      ],
    };
    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;
        try {
          // const planTree = {
          //   projectId: props.Projectid
          // };
          // const res6 = await planTreeListApi(planTree);
          // console.log('测试🚀 ~ file: addProjectModal.vue ~ line 304 ~ res6', res6);
          // state.planId = convertTree(res6);
          const res = await startEffectRiskListApi();
          state.predictStartTime = res;
          const res2 = await probRiskListApi();
          state.riskProbability = res2;
          const res3 = await RistTypeApi();
          state.riskType = res3;
          const res4 = await EffectRiskListApi();
          state.riskInfluence = res4;
          const res5 = await soluteRiskListApi();
          state.copingStrategy = res5;
        } catch (err) {
          // console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
        }
        if (value.formType === 'add') {
          state.title = '创建信息';
          state.formType = 'add';
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          // console.log(props.listData);
          state.oldformState = { ...props.listData[0] };
          // for (let namex in props.listData[0]) {
          //   state.formState[namex] = props.listData[0][namex];
          // }

          for (let namex in state.formState) {
            state.formState[namex] = props.listData[0][namex];
          }
          state.formState.id = props.listData[0].id;
          state.formState.principalId = state.formState.principalName;
          state.formState.discernPerson = state.formState.discernPersonName;
        }
      },
    );
    //   const convertTree = (tree) => {
    //     const result = [];
    //     tree.forEach((item) => {
    //       let children = item.children || [];
    //       // let expand = true;
    //       let { name: title, id: key, id: value } = item;
    //       if (children && children.length) {
    //         children = convertTree(children);
    //       }
    //       result.push({
    //         title,
    //         children,
    //         // expand,
    //         key,
    //         value
    //       });
    //     });
    //     return result;
    //   };
    /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      formRef.value.resetFields();
      state.formState = {
        name: '',
        // 类型
        riskType: '',
        //   //关联1
        //   planId: '',
        // 概率2
        riskProbability: '',
        // 影响3
        riskInfluence: '',
        // 负责人4
        principalName: '',
        // 负责人4
        principalId: '',

        // 识别人
        discernPerson: '',
        // 识别人
        discernPersonName: '',

        // 预期发生时间
        predictStartTime: '',
        // 风险描述255
        remark: '',
        // 预计完成时间
        predictEndTime: '',
        // 应对策略5
        copingStrategy: '',
        // 应对措施 255字节
        solutions: '',
      };
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      // console.log('195');
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      const httpValue = { ...state.formState };
      state.formState.principalId === state.formState.principalName
        ? (httpValue.principalId = state.oldformState.principalId)
        : '';
      state.formState.discernPerson === state.formState.discernPersonName
        ? (httpValue.discernPerson = state.oldformState.discernPerson)
        : '';
      httpValue.predictEndTime = httpValue.predictEndTime
        ? dayjs(httpValue.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : '';
      httpValue.projectId = props.Projectid;
      delete httpValue.createTime;
      httpValue;
      state.formType === 'edit' ? zhttp(editRiskApi(httpValue)) : zhttp(addRiskApi(httpValue));
    };
    const zhttp = (fn) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          // dealValue();
          fn.then(() => {
            message.success('保存成功');
            state.loading = false;
            if (state.nextCheck) {
              formRef.value.resetFields();
            } else {
              state.visible = false;
            }
            emit('success', false);
            state.formState = {
              name: '',
              // 类型
              riskType: '',
              //   //关联1
              //   planId: '',
              // 概率2
              riskProbability: '',
              // 影响3
              riskInfluence: '',
              // 负责人4
              principalName: '',
              // 负责人4
              principalId: '',

              // 识别人
              discernPerson: '',
              // 识别人
              discernPersonName: '',

              // 预期发生时间
              predictStartTime: '',
              // 风险描述255
              remark: '',
              // 预计完成时间
              predictEndTime: '',
              // 应对策略5
              copingStrategy: '',
              // 应对措施 255字节
              solutions: '',
            };
          }).catch((err) => {
            state.loading = false;
          });
        })
        .catch((error) => {
          // console.log('error', error);
        });
    };
    const handleChange = (value) => {
      state.roleOption = [];

      try {
        getRole(value, props.Projectid, 'role');
      } catch (err) {
        // console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const personHandleChange = (value) => {
      state.roleOption = [];
      try {
        getRole(value, props.Projectid, 'person');
      } catch (err) {
        // console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const getRole = async (value, idkey, typeString) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        nextTick(() => {
          const qq = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          if (typeString === 'role') {
            state.roleOption = qq;
          } else {
            state.personOption = qq;
          }
        });
      });
    };
    const filterHandle = (inputValue, option) => option;
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      handleChange,
      personHandleChange,
      filterHandle,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: ~`getPrefixVar('primary-color')`;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
</style>
