package com.chinasie.orion.domain.vo.workflow;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * FlowTemplateBusiness Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-26 17:01:52
 */
@ApiModel(value = "FlowTemplateBusinessVO对象", description = "流程模版与业务关联表")
@Data
public class FlowTemplateBusinessVO extends ObjectVO implements Serializable {

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;

    /**
     * 流程模版id
     */
    @ApiModelProperty(value = "流程模版id")
    private String templateId;

    /**
     * 流程模版名称
     */
    @ApiModelProperty(value = "流程模版名称")
    private String templateName;

    /**
     * 流程模版名称
     */
    @ApiModelProperty(value = "流程模版名称")
    private Integer templateVersion;

    /**
     * 业务Id
     */
    @ApiModelProperty(value = "业务Id")
    private String businessId;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String processStatus;

    /**
     * 发起人Id
     */
    @ApiModelProperty(value = "发起人Id")
    private String applyUserId;

    /**
     * 发起人名称
     */
    @ApiModelProperty(value = "发起人名称")
    private String applyUserName;

    /**
     * 数据类型编号
     */
    @ApiModelProperty(value = "数据类型编号")
    private String dataTypeCode;


    /**
     * 数据类型名称
     */
    @ApiModelProperty(value = "数据类型名称")
    private String dataTypeName;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String businessName;

    /**
     * 消息url
     */
    @ApiModelProperty(value = "消息url")
    private String messageUrl;

    @ApiModelProperty(value = "发起时间")
    private Date applyTime;


    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
