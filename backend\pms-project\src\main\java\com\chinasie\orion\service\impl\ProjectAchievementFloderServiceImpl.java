package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ProjectAchievementFloderDTO;
import com.chinasie.orion.domain.entity.ProjectAchievementFloder;
import com.chinasie.orion.domain.vo.ProjectAchievementFloderVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectAchievementFloderMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectAchievementFloderService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * ProjectAchievementFloder 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 13:43:27
 */
@Service
public class ProjectAchievementFloderServiceImpl extends OrionBaseServiceImpl<ProjectAchievementFloderMapper, ProjectAchievementFloder> implements ProjectAchievementFloderService {

    @Autowired
    private ProjectAchievementFloderMapper projectAchievementFloderMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectAchievementFloderVO detail(String id) throws Exception {
        ProjectAchievementFloder projectAchievementFloder = projectAchievementFloderMapper.selectById(id);
        ProjectAchievementFloderVO result = BeanCopyUtils.convertTo(projectAchievementFloder, ProjectAchievementFloderVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectAchievementFloderDTO
     */
    @Override
    public ProjectAchievementFloderVO create(ProjectAchievementFloderDTO projectAchievementFloderDTO) throws Exception {
        ProjectAchievementFloder projectAchievementFloder = BeanCopyUtils.convertTo(projectAchievementFloderDTO, ProjectAchievementFloder::new);
        int insert = projectAchievementFloderMapper.insert(projectAchievementFloder);
        ProjectAchievementFloderVO rsp = BeanCopyUtils.convertTo(projectAchievementFloder, ProjectAchievementFloderVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectAchievementFloderDTO
     */
    @Override
    public Boolean edit(ProjectAchievementFloderDTO projectAchievementFloderDTO) throws Exception {
        ProjectAchievementFloder projectAchievementFloder = BeanCopyUtils.convertTo(projectAchievementFloderDTO, ProjectAchievementFloder::new);
        int update = projectAchievementFloderMapper.updateById(projectAchievementFloder);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = projectAchievementFloderMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectAchievementFloderVO> pages(Page<ProjectAchievementFloderDTO> pageRequest) throws Exception {
        Page<ProjectAchievementFloder> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectAchievementFloder::new));

        PageResult<ProjectAchievementFloder> page = projectAchievementFloderMapper.selectPage(realPageRequest, null);

        Page<ProjectAchievementFloderVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectAchievementFloderVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectAchievementFloderVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProjectAchievementFloderVO> lists(String approvalId) {
        LambdaQueryWrapperX<ProjectAchievementFloder> condition = new LambdaQueryWrapperX<>();
        condition.eq(ProjectAchievementFloder::getApprovalId, approvalId);
        List<ProjectAchievementFloder> projectAchievementFloders = this.list(condition);
        List<ProjectAchievementFloderVO> vos = BeanCopyUtils.convertListTo(projectAchievementFloders, ProjectAchievementFloderVO::new);
        return vos;
    }
}
