package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.constant.ActionItemBusStatusEnum;
import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.service.ProdActionItemService;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 * 生产大修行动项状态变更
 */
@Component
@Slf4j
public class ProdActionItemChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "ProdActionItem";

    @Resource
    private ProdActionItemService prodActionItemService;

    @Resource
    private ClassRedisHelper classRedisHelper;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("生产大修行动项状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("生产大修行动项状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {
        LambdaUpdateWrapper<ProdActionItem> wrapper=new LambdaUpdateWrapper<>(ProdActionItem.class);
        wrapper.eq(ProdActionItem::getId,message.getBusinessId());
        ProdActionItem actionItem =  prodActionItemService.getOne(wrapper);
        if(Objects.isNull(actionItem)){
            log.error("生产大修行动项不存在，id:{}",message.getBusinessId());
            return;
        }
        // 如果是验证通过  那么需要判断 同一个父级下 的所有子集所有都已经完成 那么会 触发父级的状态变为 关闭
        actionItem.setStatus(message.getStatus());
        Boolean bool=prodActionItemService.updateById(actionItem);
        if(Objects.equals(message.getStatus(), ActionItemBusStatusEnum.VERIFY_PASS.getStatus())){
            wrapper.clear();
            wrapper.eq(ProdActionItem::getParentId,actionItem.getParentId());
            wrapper.ne(ProdActionItem::getId,actionItem.getId());
            wrapper.ne(ProdActionItem::getStatus,ActionItemBusStatusEnum.VERIFY_PASS.getStatus());
            if(prodActionItemService.count(wrapper)==0){
                wrapper.clear();
                wrapper.eq(ProdActionItem::getId,actionItem.getParentId());
                wrapper.set(ProdActionItem::getStatus,ActionItemBusStatusEnum.CLOSE.getStatus());
                prodActionItemService.update(wrapper);
            }
        }
        log.info("生产大修行动项状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), bool);
    }

}
