import Api from '/@/api';

// 表格数据
async function getTableApi(type: string, assetName: string, teamCode?: string, paramsObj?: any): Promise<any[]> {
  try {
    const params = type === 'org'
      ? {
        deptCode: teamCode,
        keyword: assetName,
        pageNum: paramsObj?.pageNum,
        pageSize: paramsObj?.pageSize,
      }
      : {
        keyword: assetName,
        specialtyTeamCode: teamCode,
        pageNum: paramsObj?.pageNum,
        pageSize: paramsObj?.pageSize,
      };

    const endpoint = type === 'org' ? '/icm/deptCode/person/page' : '/icm/specialtyTeamCode/person/page';
    const result = await fetchApi(endpoint, params);
    return {
      ...result,
      content: result?.content?.map((item) => ({
        ...item,
        id: item.userCode,
        name: item?.userName,
      })),
    };
  } catch (error) {
    return [];
  }
}

// 左侧物资树
async function getTreeApi(likeName: string, teamCode: string): Promise<any[]> {
  try {
    const result = await fetchApi('/icm/teamOrg/page', {
      likeName,
      teamCode,
    });
    const treeList = Object.keys(result).map((key) => ({
      key,
      value: result[key],
    }));

    return treeList.map((item) => ({
      id: item?.value?.code,
      name: item?.value?.name,
      children: item?.value?.children,
      data: {
        nodeType: item?.value?.data?.nodeType,
      },
    }));
  } catch (error) {
    return [];
  }
}

// 提取重复的API调用逻辑
async function fetchApi(endpoint: string, params: any): Promise<any> {
  try {
    const result = await new Api(endpoint).fetch(params, '', 'POST');
    return result;
  } catch (error) {
    return {};
  }
}

export function getUserProps(teamCode: string) {
  return {
    title: '添加人员',
    selectType: 'checkbox',
    selectedTreeDefault: true,
    width: 1200,
    tableColumns: [
      {
        title: '姓名',
        dataIndex: 'userName',
        width: 120,
      },
      {
        title: '所属部门',
        dataIndex: 'deptName',
        width: 120,
      },
      {
        title: '工号',
        dataIndex: 'userCode',
        width: 120,
      },
      {
        title: '职务',
        dataIndex: 'nowPosition',
        width: 120,
      },
      // {
      //   title: '职级',
      //   dataIndex: 'jobLevel',
      //   width: 120,
      // },
      {
        title: '岗位',
        dataIndex: 'jobTitle',
        width: 120,
      },
      {
        title: '电话',
        dataIndex: 'phone',
        width: 120,
      },
    ],
    async tableApi(params: any) {
      const nodeType = params?.treeItem?.data?.nodeType;
      const assetName = params?.searchConditions?.[0]?.[0]?.values?.[0];
      const teamCode = params?.treeItem?.id;
      return nodeType === 'org'
        ? getTableApi('org', assetName, teamCode, params)
        : getTableApi('specialtyTeamCode', assetName, teamCode, params);
    },
    async treeApi() {
      return getTreeApi('', teamCode);
    },
  };
}
