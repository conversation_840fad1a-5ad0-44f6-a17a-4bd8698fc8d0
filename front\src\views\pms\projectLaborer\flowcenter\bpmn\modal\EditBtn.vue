<template>
  <BasicModal
    v-bind="$attrs"
    title="修改按钮"
    @register="registerModal"
  >
    <BasicForm
      :show-action-button-group="false"
      @register="register"
    />
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs } from 'ant-design-vue';
// import Api from '/@/api/index';
// import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    BasicForm,
  },
  props: {
    treeData: Object,
  },
  setup(_, { emit }) {
    // 使用 `toRefs` 创建对prop的 `data` property 的响应式引用
    // 定义表单的字段规则
    const currentIndex = ref(0);
    const schemas: FormSchema[] = [
      {
        field: 'name',
        component: 'Input',
        label: '按钮名称',
        colProps: {
          span: 18,
        },
        required: true,
      },
    ];

    // 注册一个表单
    const [register, { setFieldsValue, validateFields }] = useForm({
      labelWidth: 120,
      schemas,
      actionColOptions: {
        span: 24,
      },
    });

    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      const { row, index } = data;
      setFieldsValue(row);
      currentIndex.value = index;
    });
      /*
       * 验证增加和编辑时候的表单
       * */
    async function validateForm() {
      const res = await validateFields();
      emit('update', {
        btn: res,
        index: currentIndex.value,
      });
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      register,
      validateForm,
    };
  },
});
</script>
