import BaseRenderer from 'diagram-js/lib/draw/BaseRenderer'; // 引入默认的renderer
import {
  append as svgAppend,
  attr as svgAttr,
  classes as svgClasses,
  create as svgCreate,
  remove as svgRemove,
  select,
  selectAll,
} from 'tiny-svg';
import { customElements, customConfig } from '../utils/util';

const HIGH_PRIORITY = 1500; // 最高优先级
export default class CustomRenderer extends BaseRenderer {
  // 继承BaseRenderer
  constructor(eventBus, bpmnRenderer) {
    super(eventBus, HIGH_PRIORITY);
    this.bpmnRenderer = bpmnRenderer;
  }

  canRender(element) {
    // ignore labels
    return !element.labelTarget;
  }

  drawShape(parentNode, element) {
    // 核心函数就是绘制shape
    const type = element.type; // 获取到类型
    const userTaskAndDoFlag = customElements.includes(type) && element.do;
    const renderDoFlag = element.businessObject?.assignee === '${startBy}';
    if (userTaskAndDoFlag || renderDoFlag) {
      // 判断是否do节点
      let customIcon = svgCreate('rect');
      svgAttr(customIcon, {
        x: 0,
        y: 0,
        width: 100,
        height: 80,
        rx: 10,
        ry: 10,
        fill: '#fff',
        stroke: '#000',
        strokeWidth: 1.5,
      });
      svgAppend(parentNode, customIcon);
      // 渲染文字
      if (element.businessObject.name) {
        const wrapperText = svgCreate('text', {
          lineHeight: 1.2,
          x: 30,
          style:
            'font-family: Arial, sans-serif; font-size: 12px; font-weight: normal; fill: black;',
        });
        svgAppend(parentNode, wrapperText);
        wrapperText.innerHTML = element.businessObject.name;
        const nodeWidth = wrapperText.getBoundingClientRect().width;
        svgAttr(wrapperText, {
          x: (100 - nodeWidth) / 2,
          y: 43,
        });
      }
      // 渲染图标
      const leftIcon = svgCreate('image', {
        href: '/images/do-left.jpg',
        width: 20,
        height: 20,
        x: 4,
        y: 4,
      });
      svgAppend(parentNode, leftIcon);
      return customIcon;
    }
    const shape = this.bpmnRenderer.drawShape(parentNode, element);
    return shape;
  }

  getShapePath(shape) {
    return this.bpmnRenderer.getShapePath(shape);
  }
}

CustomRenderer.$inject = ['eventBus', 'bpmnRenderer'];
