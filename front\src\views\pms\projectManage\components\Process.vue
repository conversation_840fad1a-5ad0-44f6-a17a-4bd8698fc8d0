<template>
  <BpmnMain
    v-if="formId"
    ref="bpmnMain"
    :menu-instance-list-api="menuInstanceListApi"
    :template-list-api="templateListApi"
    :allTaskPageApi="allTaskPageApi"
    :addEditSavaApi="addEditSavaApi"
    :userId="userId"
    :journalApi="journalApi"
    :is-flow-top="true"
    :taskBtnApi="taskBtnApi"
    :nodeTableDataApi="nodeTableDataApi"
    :approvalListApi="approvalListApi"
    :approvalTableColumns="approvalTableColumns"
    :show-btn="false"
    :flowStartBeforeMethod="flowStartBeforeMethod"
    :userDataApi="userDataApi"
    :start2Api="start2Api"
    :getlsStartButton="getlsStartButton"
    :planListTitle="planListTitle"
    @success="successChange"
    @openClick="openClick"
  >
    <template
      v-if="$slots.baseInfo"
      #baseInfo="{bpmnModuleData}"
    >
      <slot
        name="baseInfo"
        :bpmnModuleData="bpmnModuleData"
      />
    </template>
    <template
      v-if="$slots.planList"
      #planList="{bpmnModuleData}"
    >
      <slot
        name="planList"
        :bpmnModuleData="bpmnModuleData"
      />
    </template>
  </BpmnMain>
</template>

<script lang="ts">
import {
  defineComponent, inject, nextTick, onMounted, reactive, ref, toRefs, watch,
} from 'vue';
import { BpmnMain } from 'lyra-component-vue3';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import { useRoute } from 'vue-router';
import { getQiankunProps } from '/@/utils/qiankun/useQiankun';

export default defineComponent({
  name: 'Information',
  components: {
    BpmnMain,
  },
  props: {
    planListTitle: {
      type: String,
      default: '审批物列表',
    },
    formId: {
      type: String,
      default: '',
    },
    href: {
      type: String,
      default: '',
    },
    processName: {
      type: String,
      default: '',
    },
    bizCatalogName: {
      type: String,
      default: '',
    },
    flowStartBeforeMethod: {
      type: Function,
      default: null,
    },
    deliveries: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['changePage'],
  setup(props, { emit, attrs }) {
    const useWebSocket = getQiankunProps()?.mainStore?.useWebSocketStore();
    const route = useRoute();
    const bpmnMain = ref();
    const formData: any = inject('formData', {});
    const getFormData:(()=>void) = inject('getFormData');
    const userStore = useUserStore();
    const state = reactive({
      deliveryId: '',
      dataType: '',
      bizId: '',
      procInstName: '',
      groupId: '',
      userId: userStore.getUserInfo.id,
      templateList: [],
      approvalTableColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: 'status' },
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          type: 'dateTime',
        },
      ],
    });

    onMounted(() => {

    });

    watch(
      () => formData?.value,
      (val) => {
        if (bpmnMain?.value?.refresh) {
          nextTick(() => {
            bpmnMain?.value?.refresh();
          });
        }
      },
    );
    function getlsStartButton() {
      return formData?.value.creatorId === state.userId;
    }
    function menuInstanceListApi(data) {
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: props.deliveries.length === 0 ? [
            {
              deliveryId: props.formId,
            },
          ] : props.deliveries,
          dataTypeBinds: [
            {
              classNameCode: formData.value?.className,
              dataStatusCode: formData.value?.status,
            },
          ],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST').then((res) => res);
    }

    async function templateListApi() {
      let params = {
        pageNum: 1,
        pageSize: 10000,
        query: {
          dataTypeBinds: [
            {
              classNameCode: formData.value.className,
              dataStatusCode: formData.value.status,
            },
          ],
          status: 1,
          userId: state.userId,
        },
      };
      return new Api('/workflow')
        .fetch(
          params,
          'process-template/major/page',
          'POST',
        )
        .then((res) => {
          state.templateList = res.content;
          return res.content.map((item) => ({
            label: item.name,
            value: item.procDefId,
            key: item.procDefId,
            id: item.procDefId,
          }));
        });
    }

    function allTaskPageApi(data) {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      if (data.type === 'edit') {
        url += `&processInstanceId=${data.menuActionItem?.id}`;
      }
      return new Api('/workflow').fetch('', url, 'POST').then((res) => res);
    }

    // 保存
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      data.deliveries.forEach((item) => {
        item.deliveryId = props.formId;
      });
      let params: any = {
        userId: state.userId,
        // bizCatalogId: 'string',
        // bizCatalogName: 'string',
        bizId: data.bizId,
        // bizTypeName: 'string',
        // businessKey: 'string',
        deliveries: data.deliveries,
        flowInfoId: templateItem.id,
        // flowKey: 'string',',
        href: `${props.href}/${props.formId}?tabsType=process`,
        ownerId: userStore.getUserInfo.id,
        prearranges: data.prearranges,
        procDefId: templateItem.procDefId,
        procDefName: data.procDefName,
        procInstName: props.processName,
        bizCatalogName: props.bizCatalogName,
      };
      if (typeof data.id === 'undefined') {
        params.id = data.id;
      }
      return new Api('/workflow').fetch(params, 'process-instance', `${typeof data.id === 'undefined' ? 'POST' : 'PUT'}`).then((res) => res);
    }

    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;

      let currentTasksItem = data.currentTasks.find((item) => {
        let predicate = false;
        if (item.applicant) {
          predicate = (data.startId === userStore.getUserInfo.id);
        } else {
          predicate = item.assigneesUser.some((sItem) => sItem.id === userStore.getUserInfo.id);
        }
        return predicate;
      });

      if (typeof currentTasksItem === 'undefined') return;
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET').then((res) => res);
    }

    const successChange = (type) => {
      getFormData();
      useWebSocket?.getMessageTotal();
    };

    // 审批物列表
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([formData?.value]);
      });
    }

    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET').then((res) => res);
    }

    const openClick = (record) => {
      window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
    };
    function userDataApi({ currentTask }) {
      const {
        post, organization, role, user,
      } = currentTask;

      return new Api('/pmi/user/role-dept-user-job').fetch({
        // 岗位
        jobs: post ? post.split(',') : undefined,
        // 组织id
        orgIds: organization ? organization.split(',') : undefined,
        // 角色
        roleIds: role ? role.split(',') : undefined,
        // 用户
        userIds: user ? user.split(',') : undefined,
      }, '', 'POST');
    }
    async function start2Api(data) {
      const { comment } = data;
      if (props.flowStartBeforeMethod) {
        await props.flowStartBeforeMethod();
      }
      return new Api('/workflow/facade/initiate').fetch({
        userId: userStore.getUserInfo.id,
        bizId: '',
        deliveries: [
          {
            deliveryId: props.formId,
          },
        ],
        comment,
        dataTypeBinds: [
          {
            classNameCode: formData.value.className,
            dataStatusCode: formData.value.status,
          },
        ],
        href: `${props.href}/${props.formId}?tabsType=process`,
        ownerId: userStore.getUserInfo.id,
        procInstName: props.processName,
        bizCatalogName: props.bizCatalogName,
      }, '', 'post').then((res) => {
        getFormData();
        useWebSocket?.getMessageTotal();
      });
    }
    return {
      ...toRefs(state),
      bpmnMain,
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      successChange,
      nodeTableDataApi,
      approvalListApi,
      openClick,
      userDataApi,
      start2Api,
      getlsStartButton,
    };
  },
});
</script>
<style lang="less" scoped>
//.process {
//  height: 100%;
//}
</style>
