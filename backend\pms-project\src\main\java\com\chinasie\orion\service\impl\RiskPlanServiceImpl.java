package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.DocumentBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.DocumentClassNameConstant;
import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.RiskPlan;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalRiskPlan;
import com.chinasie.orion.domain.vo.RiskPlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.RiskPlanRepository;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.service.FileInfoService;
import com.chinasie.orion.service.RiskPlanService;
import com.chinasie.orion.service.approval.ProjectApprovalRiskPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/11:23
 * @description:
 */
@Service
public class RiskPlanServiceImpl extends OrionBaseServiceImpl<RiskPlanRepository, RiskPlan> implements RiskPlanService {

    @Resource
    private UserBo userBo;
    @Resource
    private DictBo dictBo;
    @Resource
    private DocumentBo documentBo;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileInfoService fileInfoService;
    @Autowired
    private CodeBo codeBo;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private ProjectApprovalRiskPlanService projectApprovalRiskPlanService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveRiskPlan(RiskPlanDTO riskPlanDTO) throws Exception {
        List<RiskPlan> riskPlanDTOList = this.list(new LambdaQueryWrapper<>(RiskPlan.class).
                eq(RiskPlan::getName, riskPlanDTO.getName()).
                eq(RiskPlan::getProjectId, riskPlanDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskPlanDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.RISK_PLAN, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            riskPlanDTO.setNumber(code);
        }
        RiskPlan riskPlan = BeanCopyUtils.convertTo(riskPlanDTO, RiskPlan::new);

        this.save(riskPlan);
        String id = riskPlan.getId();
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setName(riskPlanDTO.getName());
        documentDTO.setNumber(riskPlanDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Risk_Plan_Document);
        String s = documentBo.insertDocument(documentDTO);

        riskPlanDTO.setId(id);
        riskPlanDTO.setDocumentId(s);
        RiskPlan riskPlan2 = BeanCopyUtils.convertTo(riskPlanDTO, RiskPlan::new);

        this.updateById(riskPlan2);
        return id;
    }

    @Override
    public Boolean saveBatchRiskPlan(String projectId, List<RiskPlanDTO> riskPlanList) throws Exception {
        if (StrUtil.isEmpty(projectId)){
            return Boolean.FALSE;
        }
        if (CollectionUtil.isEmpty(riskPlanList)){
            return Boolean.FALSE;
        }
        List<RiskPlan> riskPlanDTOList = this.list(new LambdaQueryWrapper<>(RiskPlan.class));

        List<RiskPlan> saveRiskPlanList = riskPlanList.stream().map(m -> {
            RiskPlan riskPlan = new RiskPlan();
            riskPlan.setProjectId(projectId);
            riskPlan.setName(m.getName());
            riskPlan.setNumber(m.getNumber());
            riskPlan.setRiskType(m.getRiskType());
            riskPlan.setRiskProbability(m.getRiskProbability());
            riskPlan.setRiskInfluence(m.getRiskInfluence());
            riskPlan.setRemark(m.getRemark());
            riskPlan.setSolutions(m.getSolutions());
            return riskPlan;
        }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(riskPlanDTOList)){
            this.saveBatch(saveRiskPlanList);
        }else {
            Map<String, List<RiskPlan>> riskPlanMap = riskPlanDTOList.stream().collect(Collectors.groupingBy(item -> item.getProjectId() + item.getName()));
            this.saveBatch(saveRiskPlanList.stream().map(m -> {
                if (riskPlanMap.containsKey(m.getProjectId() + m.getName())) {
                    return null;
                }
                return m;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean synApprovalRiskPlan(String projectId) throws Exception {

        List<ProjectApprovalRiskPlan> list = projectApprovalRiskPlanService.list(new LambdaQueryWrapperX<>(ProjectApprovalRiskPlan.class)
                .eq(ProjectApprovalRiskPlan::getProjectId, projectId).distinct());
        if (CollectionUtil.isEmpty(list)){
            return Boolean.FALSE;
        }
        return this.saveBatchRiskPlan(projectId, BeanCopyUtils.convertListTo(list, RiskPlanDTO::new));
    }

    @Override
    public Page<RiskPlanVO> getRiskPlanPage(Page<RiskPlanDTO> pageRequest) throws Exception {
        Page<RiskPlanVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());

        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        LambdaQueryWrapperX<RiskPlan> riskPlanLambdaQueryWrapper = new LambdaQueryWrapperX<>(RiskPlan.class);
        riskPlanLambdaQueryWrapper.eq(RiskPlan::getProjectId, pageRequest.getQuery().getProjectId());
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), riskPlanLambdaQueryWrapper);
        }

        PageResult<RiskPlan> pageResult = this.getBaseMapper().selectPage(pageRequest, riskPlanLambdaQueryWrapper);

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getContent())) {
            return resultPage;
        }
        List<RiskPlan> riskPlanList = pageResult.getContent();
        List<RiskPlanVO> riskPlanVOList = BeanCopyUtils.convertListTo(riskPlanList, RiskPlanVO::new);
        List<String> userIdList = riskPlanVOList.stream().map(RiskPlanVO::getModifyId).collect(Collectors.toList());
        userIdList.addAll(riskPlanVOList.stream().map(RiskPlanVO::getCreatorId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
       // Map<String, String> riskTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_TYPES);
        Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
        Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);

        riskPlanVOList.forEach(o -> {
            o.setCreatorName(userIdAndNameMap.get(o.getCreatorId()));
            o.setModifyName(userIdAndNameMap.get(o.getModifyId()));
            o.setRiskProbabilityName(riskProbabilityValueToDesMap.get(o.getRiskProbability()));
            o.setRiskInfluenceName(riskInfluenceValueToDesMap.get(o.getRiskInfluence()));

        });
        this.setContent(riskPlanVOList);
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(riskPlanVOList);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), riskPlanVOList, RiskPlanVO::getId, RiskPlanVO::getDataStatus, RiskPlanVO::setRdAuthList,
                RiskPlanVO::getCreatorId,
                RiskPlanVO::getModifyId,
                RiskPlanVO::getOwnerId,
                dataRoleMap);

        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(riskPlanVOList);


        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<RiskPlanVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (RiskPlanVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }


    @Override
    public RiskPlanVO getRiskPlanDetail(String id) throws Exception {
        RiskPlan riskPlanDTO = this.getById(id);
        if (Objects.isNull(riskPlanDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        RiskPlanVO riskPlanVO = new RiskPlanVO();
        BeanCopyUtils.copyProperties(riskPlanDTO, riskPlanVO);

        List<String> userIdList = new ArrayList<>();
        userIdList.add(riskPlanVO.getCreatorId());
        userIdList.add(riskPlanVO.getModifyId());
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> riskTypeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_TYPES);
        Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
        Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);

        riskPlanVO.setCreatorName(userIdAndNameMap.get(riskPlanVO.getCreatorId()));
        riskPlanVO.setModifyName(userIdAndNameMap.get(riskPlanVO.getModifyId()));
        riskPlanVO.setRiskTypeName(riskTypeValueToDesMap.get(riskPlanVO.getRiskType()));
        riskPlanVO.setRiskProbabilityName(riskProbabilityValueToDesMap.get(riskPlanVO.getRiskProbability()));
        riskPlanVO.setRiskInfluenceName(riskInfluenceValueToDesMap.get(riskPlanVO.getRiskInfluence()));
        this.setContent(Collections.singletonList(riskPlanVO));
        return riskPlanVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editRiskPlan(RiskPlanDTO riskPlanDTO) throws Exception {
        String id = riskPlanDTO.getId();
        List<RiskPlan> riskPlanDTOList = this.list(new LambdaQueryWrapper<>(RiskPlan.class).
                ne(RiskPlan::getId, id).
                eq(RiskPlan::getName, riskPlanDTO.getName()).
                eq(RiskPlan::getProjectId, riskPlanDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskPlanDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        RiskPlan riskPlan = BeanCopyUtils.convertTo(riskPlanDTO, RiskPlan::new);
        Boolean result = this.updateById(riskPlan);

        RiskPlan riskPlanDTO1 = this.getById(id);
        String documentId = riskPlanDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(riskPlanDTO.getName());
            documentDTO.setNumber(riskPlanDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Risk_Plan_Document);
            documentBo.updateDocument(documentDTO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatchRiskPlan(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<RiskPlan> riskPlanDTOList = this.list(new LambdaQueryWrapper<>(RiskPlan.class).
                in(RiskPlan::getId, ids.toArray()));
        if (CollectionUtils.isEmpty(riskPlanDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, ids.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            documentService.deleteBatchFileInfo(fileInfoDTOList);
        }
        List<String> documentIdList = new ArrayList<>();
        for (RiskPlan riskPlanDTO : riskPlanDTOList) {
            String documentId = riskPlanDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }
        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }
//        documentBo.delByIdList(ids);
        return this.removeBatchByIds(ids);
    }


    public void setContent(List<RiskPlanVO> riskPlanVOList) throws Exception {
        List<String> typeIdList = riskPlanVOList.stream().map(RiskPlanVO::getRiskType).distinct().collect(Collectors.toList());
        Map<String, String> riskTypeIdToNameMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(typeIdList)) {
            ResponseDTO<List<SimpleVO>> typeByIds = pasFeignService.getTypeByIds(typeIdList);
            List<SimpleVO> result = typeByIds.getResult();
            if (ObjectUtil.isNotNull(result)){
                riskTypeIdToNameMap.putAll(result.stream().collect(Collectors.toMap(SimpleVO::getId, SimpleVO::getName)));
            }

        }
        riskPlanVOList.forEach(o -> {
            o.setRiskTypeName(riskTypeIdToNameMap.get(o.getRiskType()));
        });
    }
}
