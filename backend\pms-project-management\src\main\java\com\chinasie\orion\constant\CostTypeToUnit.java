package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum CostTypeToUnit {


    positionCost("岗级成本", "人/月"),
    accommodationFee("住宿费", "人/天"),
    transferFee("换乘费", "人/次"),
    physicalExaminationFee("RP体检费", "人/年"),
    laborFee("劳保费","人/年"),
    logisticsFee("后勤费","人/月"),
    restaurantManagementFee("餐厅管理费","人/月"),
    otherFee("其他","项");

    String costType;

    String unit;

    public String getCostType() {
        return costType;
    }

    public String getUnit() {
        return unit;
    }

    CostTypeToUnit(String costType, String unit) {
        this.costType = costType;
        this.unit = unit;
    }

    public static Map<String,String> getMap(){
        Map<String, String> res = new HashMap<>();
        res.put(positionCost.getCostType(), positionCost.getUnit());
        res.put(accommodationFee.getCostType(), accommodationFee.getUnit());
        res.put(transferFee.getCostType(), transferFee.getUnit());
        res.put(physicalExaminationFee.getCostType(), physicalExaminationFee.getUnit());
        res.put(laborFee.getCostType(), laborFee.getUnit());
        res.put(logisticsFee.getCostType(), logisticsFee.getUnit());
        res.put(restaurantManagementFee.getCostType(), restaurantManagementFee.getUnit());
        res.put(otherFee.getCostType(), otherFee.getUnit());
        return res;
    }
}
