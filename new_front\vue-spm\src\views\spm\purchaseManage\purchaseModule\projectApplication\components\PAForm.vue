<script setup lang="ts">
import { BasicForm, InputSelectUser, useForm } from 'lyra-component-vue3';
import {
  onMounted, Ref, ref, unref, watchEffect, h,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps<{
  record: Record<string, any> | null,
  formData: any
}>();
const selectUserData = ref([]);
const applicantDept = ref([]);
const schemas = [
  {
    field: 'applicantDept',
    label: '申请部门',
    colProps: { span: 12 },
    componentProps: { placeholder: '请选择部门' },
    rules: [{ required: true }],
    component: 'TreeSelectOrg',
  },
  {
    field: 'applicantUser',
    label: '申请人',
    colProps: { span: 12 },
    rules: [{ required: true }],
    component: 'Select',
    render() {
      return h(InputSelectUser, {
        selectUserData,
        onChange: async (users: any) => {
          selectUserData.value = users;
          await setFieldsValue({
            applicantUser: users[0].id,
          });
          await validateFields(['applicantUser']);
        },
        placeholder: '请选择申请人',
        selectUserModalProps: { selectType: 'radio' },
      });
    },
  },
  {
    field: 'money',
    label: '申请金额（元）：',
    colProps: { span: 12 },
    componentProps: { min: 0 },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'currency',
    label: '币种',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'bkManage',
    label: '归口管理',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'withSafety',
    label: '与现场安全相关',
    colProps: { span: 12 },
    componentProps: {},
    component: 'Switch',
  },
  {
    field: 'purchasePlanCode',
    label: '采购计划号',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'estimatedBeginTime',
    label: '预计开工时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'bkDept',
    label: '归口部门',
    colProps: { span: 12 },
    componentProps: { placeholder: '请选择部门' },
    rules: [{ required: true }],
    component: 'TreeSelectOrg',
  },
  {
    field: 'warrantyLevel',
    label: '质保等级',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'rate',
    label: '汇率',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'suggestPurchaseWay',
    label: '建议采购方式',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'isFrameContrac',
    label: '是否有匹配的框架合同',
    colProps: { span: 12 },
    componentProps: {},
    component: 'Switch',
  },
  {
    field: 'recSupList',
    label: '推荐供应商名单',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'recPtSupList',
    label: '推荐潜在供应商名单',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'purchaseContent',
    label: '采购内容',
    colProps: { span: 24 },
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
];

const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
const getBasicInfo = async () => {
  // const result = await new Api('/spm/ncfFormpurchaseRequest').fetch('', unref(props.formData.id), 'GET');
  setTimeout(() => {
    selectUserData.value = [
      {
        id: '',
        name: '',
      },
    ];
    setFieldsValue({
      ...props.formData,
      estimatedBeginTime: props.formData.estimatedBeginTime
        ? dayjs(props.formData.estimatedBeginTime).format('YYYY-MM-DD')
        : '',
    });
  }, 1000);
};
onMounted(async () => {
  if (Object.keys(props.formData).length) {
    await getBasicInfo();
  }
});

const loading: Ref<boolean> = ref(false);
const onSubmit = async () => {
  const formValues = await validate();
  const params = {
    id: props?.record?.id,
    ...formValues,
    mainTableId: props?.record?.mainTableId,
  };

  return new Promise((resolve, reject) => {
    new Api('/spm/contractInfo').fetch(params, 'edit', 'PUT').then(() => {
      resolve('');
    }).catch((err) => {
      reject(err);
    });
  });
};
defineExpose({
  onSubmit,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
