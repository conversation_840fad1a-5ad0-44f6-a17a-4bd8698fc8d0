package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.bo.UserDeptBo;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.management.constant.CustomerIndustryEnum;
import com.chinasie.orion.management.constant.CustomerRelationshipEnum;
import com.chinasie.orion.management.constant.CustomerScopeEnum;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.management.repository.CustomerInfoMapper;
import com.chinasie.orion.management.repository.ProjectInitiationWBSMapper;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.management.service.ProjectInitiationWBSService;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMapper;
import com.chinasie.orion.repository.MileStoneLogMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptLeaderRelationVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.DeptUserRelationVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.ContractOurSignedSubjectService;
import com.chinasie.orion.service.MileStoneLogService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ServletUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.io.IOException;
import java.lang.String;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * MileStoneLog 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
@Service
@Slf4j
public class MileStoneLogServiceImpl extends OrionBaseServiceImpl<MileStoneLogMapper, MileStoneLog> implements MileStoneLogService {


    private final MileStoneExcelListener sheetOneListener = new MileStoneExcelListener();


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptUserHelper deptUserHelper;

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    private MarketContractMapper marketContractMapper;

    @Autowired
    private WorkflowFeignService workflowFeignService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private RoleUserHelper roleUserHelper;
    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;
    @Autowired
    private DeptLeaderHelper deptLeaderHelper;
    @Autowired
    private UserDeptBo userDeptBo;

    @Autowired
    private DeptRedisHelper deptRedisHelper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MileStoneLogVO detail(String id, String pageCode) throws Exception {
        MileStoneLog mileStoneLog = this.getById(id);
        MileStoneLogVO result = BeanCopyUtils.convertTo(mileStoneLog, MileStoneLogVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param mileStoneLogDTO
     */
    @Override
    public String create(MileStoneLogDTO mileStoneLogDTO) throws Exception {
        MileStoneLog mileStoneLog = BeanCopyUtils.convertTo(mileStoneLogDTO, MileStoneLog::new);
        this.save(mileStoneLog);

        String rsp = mileStoneLog.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param mileStoneLogDTO
     */
    @Override
    public Boolean edit(MileStoneLogDTO mileStoneLogDTO) throws Exception {
        MileStoneLog mileStoneLog = BeanCopyUtils.convertTo(mileStoneLogDTO, MileStoneLog::new);

        this.updateById(mileStoneLog);

        String rsp = mileStoneLog.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MileStoneLogVO> pages(Page<MileStoneLogDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MileStoneLog> condition = new LambdaQueryWrapperX<>(MileStoneLog.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(MileStoneLog::getMilestoneId, pageRequest.getQuery().getMilestoneId());
        condition.orderByDesc(MileStoneLog::getCreateTime);


        Page<MileStoneLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MileStoneLog::new));

        PageResult<MileStoneLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MileStoneLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MileStoneLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MileStoneLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "里程碑执行记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MileStoneLogDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MileStoneLogExcelListener excelReadListener = new MileStoneLogExcelListener();
        EasyExcel.read(inputStream, MileStoneLogDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MileStoneLogDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("里程碑执行记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MileStoneLog> mileStoneLoges = BeanCopyUtils.convertListTo(dtoS, MileStoneLog::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MileStoneLog-import::id", importId, mileStoneLoges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MileStoneLog> mileStoneLoges = (List<MileStoneLog>) orionJ2CacheService.get("pmsx::MileStoneLog-import::id", importId);
        log.info("里程碑执行记录导入的入库数据={}", JSONUtil.toJsonStr(mileStoneLoges));

        this.saveBatch(mileStoneLoges);
        orionJ2CacheService.delete("pmsx::MileStoneLog-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MileStoneLog-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MileStoneLog> condition = new LambdaQueryWrapperX<>(MileStoneLog.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MileStoneLog::getCreateTime);
        List<MileStoneLog> mileStoneLoges = this.list(condition);

        List<MileStoneLogDTO> dtos = BeanCopyUtils.convertListTo(mileStoneLoges, MileStoneLogDTO::new);

        String fileName = "里程碑执行记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MileStoneLogDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<MileStoneLogVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class MileStoneLogExcelListener extends AnalysisEventListener<MileStoneLogDTO> {

        private final List<MileStoneLogDTO> data = new ArrayList<>();

        @Override
        public void invoke(MileStoneLogDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MileStoneLogDTO> getData() {
            return data;
        }
    }

    @Override
    public List<MileStoneLogVO> listMileStoneLog(MileStoneLogDTO mileStoneLogDTO) {
        String milestoneId = mileStoneLogDTO.getMilestoneId();
        LambdaQueryWrapperX<MileStoneLog> condition = new LambdaQueryWrapperX<>(MileStoneLog.class);
        condition.eq(MileStoneLog::getMilestoneId, milestoneId);
        condition.orderByDesc(MileStoneLog::getCreateTime);
        List<MileStoneLog> list = this.list(condition);
        List<MileStoneLogVO> resp = BeanCopyUtils.convertListTo(list, MileStoneLogVO::new);
        return resp;
    }


    @Override
    public void exportExcelDatatree(MileStoneTreeVo dto, HttpServletResponse response) throws IOException {

//        boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
        String userId = CurrentUserHelper.getCurrentUserId();
        String orgId = CurrentUserHelper.getOrgId();
        UserBaseCacheVO userBaseCacheById = userRedisHelper.getUserBaseCacheById(userId);
        //当前登陆人的角色
        List<String> roleIdsOfUserId = roleUserHelper.getRoleIdsOfUserId(orgId, userId);
        //当前登录人编码
        String code = userBaseCacheById.getCode();
        //全部数据
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (!CollectionUtils.isEmpty(dto.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(dto.getSearchConditions(), contractMilestoneLambdaQueryWrapperX);
        }
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(contractMilestoneLambdaQueryWrapperX);

        //计划经营部中心主任 全部  Business_06
        //中心主任 自己中心       Business_05
        //公司商务 全部          Business_020、Business_023、Business_021、Business_022、
        //中心商务 自己中心       Business_01
        //商务负责人 自己负责单据
        //技术负责人 自己负责单据、
        //商务接口人 自己负责单据
        //按照承担部门分组
        //公司商务 全部
        List<String> total = Arrays.asList("Business_01", "Business_06", "Business_05", "Business_020", "Business_023", "Business_021", "Business_022");
        List<String> temp = Arrays.asList("Business_020", "Business_023", "Business_021", "Business_022");

        List<ContractMilestone> result = new ArrayList<>();
        Map<String, List<ContractMilestone>> deptMap = new HashMap<>();

        for (ContractMilestone contractMilestone : contractMilestones) {
            //承担部门
            String undertDept = contractMilestone.getUndertDept();
            if (StrUtil.isBlank(undertDept)) {
                undertDept = "temp";
            }
            if (!deptMap.containsKey(undertDept)) {
                List<ContractMilestone> list = new ArrayList<>();
                list.add(contractMilestone);
                deptMap.put(undertDept, list);
            } else {
                deptMap.get(undertDept).add(contractMilestone);
            }
        }
        //计划经营部中心主任 全部
        if (roleIdsOfUserId.contains("Business_06")) {
            result.addAll(contractMilestones);
        }
        //中心主任 自己中心
        if (roleIdsOfUserId.contains("Business_05") || roleIdsOfUserId.contains("Business_01")) {
            LambdaQueryWrapperX<DeptDO> deptWrapper = new LambdaQueryWrapperX<>(DeptDO.class);
            deptWrapper.eq(DeptDO::getOrgId, orgId);
            List<DeptDO> deptDOS = deptDOMapper.selectList(deptWrapper);
            for (DeptDO deptDO : deptDOS) {
                List<ContractMilestone> list = deptMap.get(deptDO.getId());
                result.addAll(list);
            }
        }
        //公司商务 全部
        if (Collections.disjoint(temp, roleIdsOfUserId)) {
            result.addAll(contractMilestones);
        }
        //其他
        if (!Collections.disjoint(total, roleIdsOfUserId)) {
            for (ContractMilestone contractMilestone : contractMilestones) {
                //技术负责人
                String techRspUser = contractMilestone.getTechRspUser();
                //商务负责人
                String busRspUser = contractMilestone.getBusRspUser();
                //技术接口人
                String departmental = contractMilestone.getDepartmental();
                if (userId.equals(techRspUser)) {
                    result.add(contractMilestone);
                }
                if (userId.equals(busRspUser)) {
                    result.add(contractMilestone);
                }
                if (userId.equals(departmental)) {
                    result.add(contractMilestone);
                }
            }
        }
        //导出
        List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOS = BeanCopyUtils.convertListTo(result, MileStoneExportDataVo::new);
        //合同id
        List<String> contractIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getContractId).distinct().collect(Collectors.toList());
        List<String> userTempIds = new ArrayList<>();
        //所级负责人
        List<String> officeLeadIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getOfficeLeader).distinct().collect(Collectors.toList());
        //客户
        List<String> cusPersonIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getCusPersonId).distinct().collect(Collectors.toList());
        Map<String, MarketContract> contractMap = new HashMap<>();
        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程人id
        List<String> collectFolwUserIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contractIds)) {
            List<MarketContract> marketContracts = marketContractMapper.selectBatchIds(contractIds);
            for (MarketContract marketContract : marketContracts) {
                contractMap.put(marketContract.getId(), marketContract);
            }

            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(contractIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> rep = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : rep) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = rep.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }

        }
        userTempIds.addAll(officeLeadIds);
        userTempIds.addAll(collectFolwUserIds);
//        userTempIds.addAll(cusPersonIds);
        LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        customerInfoLambdaQueryWrapperX.in(CustomerInfo::getId, cusPersonIds);
        List<CustomerInfo> customerInfos = customerInfoMapper.selectList(customerInfoLambdaQueryWrapperX);
        Map<String, String> customerInfoMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(customerInfos)) {
            for (CustomerInfo customerInfo : customerInfos) {
                customerInfoMap.put(customerInfo.getId(), customerInfo.getCusName());
            }
        }
        List<String> totalUserIds = userTempIds.stream().distinct().collect(Collectors.toList());
        //承担部门
        Map<String, String> deptMapNow = new HashMap<>();
        List<String> deptIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getUndertDept).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(deptIds);
            for (DeptDO deptDO : deptDOS) {
                deptMapNow.put(deptDO.getId(), deptDO.getName());
            }
        }

        //币种
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        //用户
        Map<String, UserVO> uesrMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(totalUserIds)) {
            List<UserVO> userByIds = userRedisHelper.getUserByIds(totalUserIds);
            for (UserVO userById : userByIds) {
                uesrMap.put(userById.getId(), userById);
            }
        }

        for (MileStoneExportDataVo excelExportDTO : contractMilestoneTreeExcelExportDTOS) {
            MarketContract marketContract = contractMap.get(excelExportDTO.getContractId());
            //合同信息
            if (ObjectUtil.isNotEmpty(marketContract)) {
                excelExportDTO.setContractNumber(marketContract.getNumber());
                excelExportDTO.setContractName(marketContract.getName());
                excelExportDTO.setContractStatus(marketContract.getStatus());
                excelExportDTO.setContractAmt(marketContract.getContractAmt());
                //销售业务分类
                excelExportDTO.setCustSaleBusType(marketContract.getCustSaleBusType());
                if (ObjectUtil.isNotEmpty(excelExportDTO.getCustSaleBusType())) {
                    DictValueVO byNumber = dictRedisHelper.getByNumber(excelExportDTO.getCustSaleBusType(), CurrentUserHelper.getOrgId());
                    excelExportDTO.setCustSaleBusTypeName(byNumber == null ? "" : byNumber.getDescription());
                }
                if (101 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("编制中");
                }
                if (130 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("履行中");
                }
                if (160 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("已完成");
                }
                if (140 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("待分发");
                }
                if (110 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("审核中");
                }
                if (121 == excelExportDTO.getContractStatus()) {
                    excelExportDTO.setContractStatusName("待签署");
                }

            }
            //币种
            excelExportDTO.setCurrencyName(currencyeMap.getOrDefault(excelExportDTO.getCurrency(), ""));
            //承担部门
            excelExportDTO.setUndertDeptName(deptMapNow.getOrDefault(excelExportDTO.getUndertDept(), ""));
            //工作主题
            excelExportDTO.setWorkTopic(excelExportDTO.getUndertDeptName() + "-" + excelExportDTO.getContractName() + "- 合同管理流程");
            //流程信息
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(excelExportDTO.getContractId());
            if (ObjectUtil.isNotEmpty(flowTemplateBusinessVO)) {
                excelExportDTO.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                excelExportDTO.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                excelExportDTO.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                excelExportDTO.setFlowCreatePersonNumber(uesrMap.get(flowTemplateBusinessVO.getApplyUserId()).getCode());
            }

            //负责人
            if (ObjectUtil.isNotEmpty(excelExportDTO.getOfficeLeader())) {
                UserVO userVO = uesrMap.get(excelExportDTO.getOfficeLeader());
                excelExportDTO.setOfficeLeaderName(userVO == null ? "" : userVO.getName());
            }
            //客户
            if (ObjectUtil.isNotEmpty(excelExportDTO.getCusPersonId())) {
//                UserVO userVO = uesrMap.get(excelExportDTO.getCusPersonId());
//                excelExportDTO.setCusPersonName(userVO==null?"":userVO.getName());
                excelExportDTO.setCusPersonName(customerInfoMap.getOrDefault(excelExportDTO.getCusPersonId(), ""));
            }
        }

        String fileName = "合同里程碑报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), MileStoneExportDataVo.class).sheet("sheet1").doWrite(contractMilestoneTreeExcelExportDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }


    }

    @Override
    public void downloadExcelMileStone(MileStoneExcelDto dto, HttpServletResponse response) {
        String fileName = "里程碑导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            this.createTemplateCustomer(dto, response, fileName, dictRedisHelper);

        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    public void createTemplateCustomer(MileStoneExcelDto dto, HttpServletResponse response, String fileName, DictRedisHelper dictRedisHelper) throws Exception {
        XSSFWorkbook sheets = new XSSFWorkbook();
        XSSFSheet sheet = sheets.createSheet("sheet");
        XSSFSheet sheet1 = sheets.createSheet("业务类型");


        //准备表头数据
        List<String> head = Arrays.asList("里程碑标题*", "里程碑编号", "上级里程碑编号", "税率（%）", "金额*", "金额类型*", "验收日期*", "日期类型*", "收入类型*",
                "商务接口人", "商务接口人工号*", "技术接口人", "技术接口人工号*", "业务类型*", "客户", "客户编码*", "业务收入类型*");

        //准备下拉框数据
        Map<Integer, List<String>> totalMap = new HashMap<>();
        List<String> sheetOne = new ArrayList<>();
        sheetOne.add(0, "业务类型*");
        MarketContract marketContract = marketContractMapper.selectById(dto.getContractId());
        //金额类型 （框架合同对应预估）/ 其他所有约定
        List<String> moneyMapList = new ArrayList<>();
//        Map<String,String> moneyMap=new HashMap<>();
        if (ObjectUtil.isNotEmpty(marketContract)) {
            String contractType = marketContract.getContractType();
            if (StrUtil.isNotBlank(contractType)) {
                //合同类型框架合同
                if ("frameContract".equals(contractType)) {
//                    moneyMap.put("except_acceptance_amt","初始预估验收金额");
                    moneyMapList.add("初始预估验收金额");
                } else {
//                    moneyMap.put("milestone_amt","合同约定验收金额");
                    moneyMapList.add("合同约定验收金额");
                }
            }
        }
        totalMap.put(5, moneyMapList);
        //日期类型
        List<String> dateMapList = new ArrayList<>();
//        Map<String,String> dateMap=new HashMap<>();
//        dateMap.put("plan_accept_date","合同约定验收日期");
//        dateMap.put("expect_accept_date","初始预估验收日期");
        dateMapList.add("合同约定验收日期");
        dateMapList.add("初始预估验收日期");
        totalMap.put(7, dateMapList);
        //收入类型
        List<String> inComeList = new ArrayList<>();
//        Map<String,String> inComeMap=new HashMap<>();
//        inComeMap.put("process_money","进度款");
//        inComeMap.put("advance_money","预收款");
//        inComeMap.put("warranty_money","质保款");
        inComeList.add("进度款");
        inComeList.add("预收款");
        inComeList.add("质保款");
        totalMap.put(8, inComeList);

        //税率
        List<String> taxRateList = new ArrayList<>();
        taxRateList.add("13");
        taxRateList.add("9");
        taxRateList.add("6");
        taxRateList.add("5");
        taxRateList.add("3");
        taxRateList.add("2");
        taxRateList.add("1");
        taxRateList.add("0.5");
        taxRateList.add("0");
        totalMap.put(3, taxRateList);

        //业务类型
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber("cos_business_type", CurrentUserHelper.getOrgId());
        List<String> businessList = byDictNumber.stream().map(DictValueVO::getDescription).distinct().collect(Collectors.toList());
        sheetOne.addAll(businessList);
        //业务收入类型
        List<String> bussComeList = new ArrayList<>();
//        Map<String,String> bussComeMap=new HashMap<>();
//        bussComeMap.put("nuclear_nergy","核能");
//        bussComeMap.put("non_nuclear_nergy","非核能");
        bussComeList.add("核能");
        bussComeList.add("非核");
        totalMap.put(16, bussComeList);

        List<String> mes = new ArrayList<>();
        mes.add(0, "说明：");
        mes.add(1, "1.标*为必填字段，请按实际业务填写。");
        mes.add(2, "2.需要对里程碑进行梯度上传时，可提供里程碑编号进行挂靠上传。里程碑最多支持两层，发生多层挂靠时下层里程碑统一会设置为二级里程碑。");
        mes.add(3, "3.对综合税率的一级里程碑，税率可留空。");
        mes.add(4, "4.为避免重名，人员主要通过工号进行识别，请注意工号保持准确。里程碑的承担部门和所会根据技术接口人带出，不用填写。");
        mes.add(5, "5.提供暂估收入金额，存在时可填写，不填写时默认为里程碑无暂估验收金额。");
        mes.add(6, "6.导入模版是直接在合同详情进行上传，因此不需要填写合同编号等信息。");
        mes.add(7, "");
        mes.add(8, "");
        mes.add(9, "");

        //第一行加说明
        for (int i = 0; i < 10; i++) {
            XSSFRow row = sheet.createRow(i);
            Cell cell = row.createCell(0);
            cell.setCellValue(mes.get(i));
        }


        //模板
        insertValue(sheets, sheet, sheet.createRow(10), head, totalMap);
        //sheet1写数据
        insertTempValue(sheets, sheet1, sheetOne);

        ServletUtils.writeAttachment(fileName, response);
        sheets.write(response.getOutputStream());
        sheets.close();
    }


    public void insertValue(XSSFWorkbook workbook, XSSFSheet sheet, Row row, List<String> headList, Map<Integer, List<String>> totalMap) {
        //字体
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        //合并说明单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(6, 6, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(7, 7, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(8, 8, 0, headList.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(9, 9, 0, headList.size() - 1));

        for (int i = 0; i < headList.size(); i++) {
            sheet.setColumnWidth(i, 30 * 256);
            Cell cell = row.createCell(i);
            cell.setCellValue(headList.get(i));
            cell.setCellStyle(cellStyle);
        }
        //设置下拉框
        if (ObjectUtil.isNotEmpty(totalMap)) {
            for (Map.Entry<Integer, List<String>> indexMap : totalMap.entrySet()) {
                Integer key = indexMap.getKey();
                List<String> value = indexMap.getValue();
                XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
                XSSFDataValidationConstraint dvConstraint = (XSSFDataValidationConstraint) dvHelper.createExplicitListConstraint(value.toArray(new String[0]));
                CellRangeAddressList addressList = new CellRangeAddressList(11, 5000, key, key);
                DataValidation validation = dvHelper.createValidation(dvConstraint, addressList);
                //设置单元格中只能是列表中的内容，否则报错
                validation.setSuppressDropDownArrow(true);
                validation.setShowErrorBox(true);
                sheet.addValidationData(validation);
            }
        }

    }

    private void insertTempValue(XSSFWorkbook workbook, XSSFSheet sheet, List<String> sheetOne) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);

        for (int i = 0; i < sheetOne.size(); i++) {
            sheet.setColumnWidth(i, 30 * 256);
            XSSFRow row = sheet.createRow(i);
            Cell cell = row.createCell(0);
            cell.setCellValue(sheetOne.get(i));
        }
    }


    @Override
    public ImportExcelCheckResultVO mileStoneCheck(MultipartFile excel) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MileStoneExcelListener excelReadListener = new MileStoneExcelListener();

        ExcelReader read = EasyExcel.read(inputStream).build();
        ReadSheet sheet = EasyExcel.readSheet("sheet").head(MileStoneImportCheckDTO.class).headRowNumber(11).registerReadListener(sheetOneListener).build();
        read.read(sheet);
        read.finish();
        //通过监听读取数据
        ThreadLocal<List<MileStoneImportCheckDTO>> dataList = sheetOneListener.getDataList();
        List<MileStoneImportCheckDTO> mileStoneImportCheckDTOS = dataList.get();

        //数据检查条数
        checkData(result, mileStoneImportCheckDTOS);
        if (result.getCode() == 400) {
            return result;
        }
        if (!CollectionUtils.isEmpty(result.getErr())) {
            return result;
        }
        //检查数据是否存在
        checkDataExist(mileStoneImportCheckDTOS, result);
        if (!CollectionUtils.isEmpty(result.getErr())) {
            return result;
        }
        HashMap<String, List<?>> map = new HashMap<>();
        map.put("contractMileStones", mileStoneImportCheckDTOS);
        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractMileStone-import::id", importId, map, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);


        return result;
    }

    private ImportExcelCheckResultVO checkDataExist(List<MileStoneImportCheckDTO> mileStoneImportCheckDTOS, ImportExcelCheckResultVO result) {
        //业务类型
        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber("cos_business_type", CurrentUserHelper.getOrgId());
        List<String> businessList = byDictNumber.stream().map(DictValueVO::getDescription).distinct().collect(Collectors.toList());
        //用户
        List<String> userCodeTotal = new ArrayList<>();
        List<String> collectUserOne = mileStoneImportCheckDTOS.stream().map(MileStoneImportCheckDTO::getBusRspUserNo).distinct().collect(Collectors.toList());
        List<String> collectUserTwo = mileStoneImportCheckDTOS.stream().map(MileStoneImportCheckDTO::getTechRspUserNo).distinct().collect(Collectors.toList());
        List<String> userIdsOne = new ArrayList<>();
        List<String> userIdsTwo = new ArrayList<>();
        for (String s : collectUserOne) {
            if (StrUtil.isNotBlank(s)) {
                userIdsOne.add(s);
            }
        }
        for (String s : collectUserTwo) {
            if (StrUtil.isNotBlank(s)) {
                userIdsTwo.add(s);
            }
        }
        userCodeTotal.addAll(userIdsOne);
        userCodeTotal.addAll(userIdsTwo);
        userCodeTotal.stream().distinct();
        Map<String, SimpleUser> userMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(userCodeTotal)) {
            List<SimpleUser> simpleUserByCode = userRedisHelper.getSimpleUserByCode(userCodeTotal);
            for (SimpleUser simpleUser : simpleUserByCode) {
                userMap.put(simpleUser.getCode(), simpleUser);
            }
        }
        //客户
        List<String> personCode = new ArrayList<>();
        List<String> cusPersonNumber = mileStoneImportCheckDTOS.stream().map(MileStoneImportCheckDTO::getCusPersonNumber).distinct().collect(Collectors.toList());
        for (String s : cusPersonNumber) {
            if (StrUtil.isNotBlank(s)) {
                personCode.add(s);
            }
        }
        personCode.stream().distinct();
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(personCode)) {
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            customerInfoLambdaQueryWrapperX.in(CustomerInfo::getCusNumber, personCode);
            List<CustomerInfo> customerInfos = customerInfoMapper.selectList(customerInfoLambdaQueryWrapperX);
            for (CustomerInfo customerInfo : customerInfos) {
                customerInfoMap.put(customerInfo.getCusNumber(), customerInfo);

            }
        }

        List<ImportExcelErrorNoteVO> errList = new ArrayList<>();
        for (int i = 0; i < mileStoneImportCheckDTOS.size(); i++) {
            MileStoneImportCheckDTO mileStoneImportCheckDTO = mileStoneImportCheckDTOS.get(i);
            //校验里程碑名称
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getMilestoneName())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("里程碑标题不能为空"));
                errList.add(importExcelErrorNoteVO);
            }


            //税率
            if (ObjectUtil.isEmpty(mileStoneImportCheckDTO.getTaxRate())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("税率不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //金额
            if (ObjectUtil.isEmpty(mileStoneImportCheckDTO.getAmtTax())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("金额不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //金额类型
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getAmmountType())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("金额类型不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //验收日期
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getExpectAcceptDate())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("验收日期不能为空"));
                errList.add(importExcelErrorNoteVO);
            }

            //日期类型
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getDateType())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("日期类型不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //收入类型
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getIncomeType())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("收入类型不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //商务接口人工号
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getBusRspUserNo())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("商务接口人工号不能为空"));
                errList.add(importExcelErrorNoteVO);
                //不为空用工号匹配人
            } else {
                SimpleUser simpleUser = userMap.get(mileStoneImportCheckDTO.getBusRspUserNo());
                if (ObjectUtil.isEmpty(simpleUser)) {
                    ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                    importExcelErrorNoteVO.setOrder((i + 11) + "");
                    importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("商务接口人工号与系统用户不匹配请正确填写"));
                    errList.add(importExcelErrorNoteVO);
                } else {
                    //改名称为用户id
                    mileStoneImportCheckDTO.setBusRspUser(simpleUser.getId());
                }
            }
            //技术负责人
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getTechRspUserNo())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("技术负责人工号不能为空"));
                errList.add(importExcelErrorNoteVO);
                //不为空用工号匹配人
            } else {
                SimpleUser simpleUser = userMap.get(mileStoneImportCheckDTO.getTechRspUserNo());
                if (ObjectUtil.isEmpty(simpleUser)) {
                    ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                    importExcelErrorNoteVO.setOrder((i + 11) + "");
                    importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("技术负责人工号与系统用户不匹配请正确填写"));
                    errList.add(importExcelErrorNoteVO);
                } else {
                    //改名称为用户id
                    mileStoneImportCheckDTO.setTechRspUser(simpleUser.getId());
                }
            }
            //业务类型
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getCostBusType())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("业务类型不能为空"));
                errList.add(importExcelErrorNoteVO);
            } else {
                if (!businessList.contains(mileStoneImportCheckDTO.getCostBusType())) {
                    ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                    importExcelErrorNoteVO.setOrder((i + 11) + "");
                    importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("业务类型填与系统用户不匹配请正确填写"));
                    errList.add(importExcelErrorNoteVO);
                }
            }
            //业务收入类型
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getBussIncomeType())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("业务收入类型不能为空"));
                errList.add(importExcelErrorNoteVO);
            }
            //客户编码
            if (StrUtil.isBlank(mileStoneImportCheckDTO.getCusPersonNumber())) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setOrder((i + 11) + "");
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("客户编码不能为空"));
                errList.add(importExcelErrorNoteVO);
            } else {
                CustomerInfo customerInfo = customerInfoMap.get(mileStoneImportCheckDTO.getCusPersonNumber());
                if (ObjectUtil.isEmpty(customerInfo)) {
                    ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                    importExcelErrorNoteVO.setOrder((i + 11) + "");
                    importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("客户编码与系统客户不匹配请正确填写"));
                    errList.add(importExcelErrorNoteVO);
                } else {
                    mileStoneImportCheckDTO.setCusPersonNumber(customerInfo.getId());
                }

            }

        }
        result.setErr(errList);
        result.setCode(200);
        return result;
    }

    private ImportExcelCheckResultVO checkData(ImportExcelCheckResultVO result, List<?> data) {
        if (CollectionUtils.isEmpty(data)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (data.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同里程碑导入的原始数据={}", JSONUtil.toJsonStr(data));
        result.setCode(200);
        return result;
    }

    private static class TemplateExcelListener<T> extends AnalysisEventListener<T> {

        List<T> saveList = new ArrayList<>();

        ThreadLocal<List<T>> dataList = new ThreadLocal<>();

        //每读取一行内容，都会调用一次该对象的invoke，在invoke可以操作使用读取到的数据
        @Override
        public void invoke(T data, AnalysisContext context) {
            saveList.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            List<T> newList = new ArrayList<>(saveList);
            saveList.clear();
            dataList.set(newList);
        }

        public ThreadLocal<List<T>> getDataList() {
            return dataList;
        }
    }

    public static class MileStoneExcelListener extends TemplateExcelListener<MileStoneImportCheckDTO> {
    }


    @Override
    public Boolean mileStoneImport(MileStoneExcelDto dto) throws Exception {
        Map<String, List<?>> dataMap = (Map<String, List<?>>) orionJ2CacheService.get("pmsx::ContractMileStone-import::id", dto.getImportId());
        if (Objects.isNull(dataMap)) {
            throw new Exception("数据不存在或已过期");
        }
        log.info("合同下里程碑导入入库数据={}", JSONUtil.toJsonStr(dataMap.toString()));
        List<MileStoneImportCheckDTO> contractMileStones = getTypedList(dataMap, "contractMileStones");
        MarketContract marketContract = marketContractMapper.selectById(dto.getContractId());
        if (ObjectUtil.isEmpty(marketContract)) {
            throw new Exception("合同不存在");
        }
        List<DictValueVO> dictValueVOS = new ArrayList<>();
        //字典值新建
        Map<String, String> dictMap = new HashMap<>();
        //金额类型
        List<DictValueVO> one = this.dictRedisHelper.getByDictNumber("finance_type", CurrentUserHelper.getOrgId());
        //日期类型date_type
        List<DictValueVO> two = this.dictRedisHelper.getByDictNumber("date_type", CurrentUserHelper.getOrgId());
        //收入类型
        List<DictValueVO> three = this.dictRedisHelper.getByDictNumber("mileStoneIncomeType", CurrentUserHelper.getOrgId());
        //业务收入类型(里程碑收入类型)
        List<DictValueVO> four = this.dictRedisHelper.getByDictNumber("income_type", CurrentUserHelper.getOrgId());
        //成本业务分类
        List<DictValueVO> five = this.dictRedisHelper.getByDictNumber("cos_business_type", CurrentUserHelper.getOrgId());
        dictValueVOS.addAll(one);
        dictValueVOS.addAll(two);
        dictValueVOS.addAll(three);
        dictValueVOS.addAll(four);
        dictValueVOS.addAll(five);
        //
        for (DictValueVO dictValueVO : dictValueVOS) {
            dictMap.put(dictValueVO.getDescription(), dictValueVO.getNumber());
        }
        List<ContractMilestone> contractMilestoneList = new ArrayList<>();
        Map<String, String> parentMap = new HashMap<>();
        //数据设置
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        for (MileStoneImportCheckDTO importCheckDTO : contractMileStones) {
            ContractMilestone contractMilestone = new ContractMilestone();
            String uuid = classRedisHelper.getUUID(ContractMilestone.class.getSimpleName());
            contractMilestone.setId(uuid);
            contractMilestone.setContractId(marketContract.getId());
            contractMilestone.setMilestoneName(importCheckDTO.getMilestoneName());
            contractMilestone.setTaxRate(new BigDecimal(importCheckDTO.getTaxRate()));
            contractMilestone.setAmtTax(importCheckDTO.getAmtTax());
            contractMilestone.setAmmountType(dictMap.getOrDefault(importCheckDTO.getAmmountType(), ""));
            String ammountType = importCheckDTO.getAmmountType();
            if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("合同约定验收金额")) {
                contractMilestone.setMilestoneAmt(importCheckDTO.getAmtTax());
            } else if (ObjectUtil.isNotEmpty(ammountType) && ammountType.equals("初始预估验收金额")) {
                contractMilestone.setExceptAcceptanceAmt(importCheckDTO.getAmtTax());
            }
            LocalDate localDate = LocalDate.parse(importCheckDTO.getExpectAcceptDate(), formatter);
            // 转换为java.util.Date
            Date javaDate = Date.from(localDate.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());

            String dateType = importCheckDTO.getDateType();
            if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("合同约定验收日期")) {
                contractMilestone.setPlanAcceptDate(javaDate);
            } else if (ObjectUtil.isNotEmpty(dateType) && dateType.equals("初始预估验收日期")) {
                contractMilestone.setExpectAcceptDate(javaDate);
            }
            contractMilestone.setDateType(dictMap.getOrDefault(importCheckDTO.getDateType(), ""));
            contractMilestone.setMileIncomeType(dictMap.getOrDefault(importCheckDTO.getIncomeType(), ""));
            contractMilestone.setBusRspUser(importCheckDTO.getBusRspUser());
            contractMilestone.setTechRspUser(importCheckDTO.getTechRspUser());
            contractMilestone.setCostBusType(dictMap.getOrDefault(importCheckDTO.getCostBusType(), ""));
            contractMilestone.setCusPersonId(importCheckDTO.getCusPersonNumber());
            contractMilestone.setBusinessIncomeType(dictMap.getOrDefault(importCheckDTO.getBussIncomeType(), ""));
            contractMilestone.setConfirmIncomeProvisionalEstimate(importCheckDTO.getConfirmIncomeProvisionalEstimate() == null ? BigDecimal.ZERO : importCheckDTO.getConfirmIncomeProvisionalEstimate());
            contractMilestone.setIsProvisionalEstimate(0);
            String techRspUser = importCheckDTO.getTechRspUser();
            SimpleUser simplerUsers = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), techRspUser);
            if (ObjectUtil.isNotEmpty(simplerUsers)) {
                String orgId = simplerUsers.getOrgId();//承担部门
                String deptId = simplerUsers.getDeptId();//所级
                contractMilestone.setUndertDept(orgId);
                contractMilestone.setOfficeDept(deptId);
            }
            //设置里程碑类型
            if (StrUtil.isEmpty(importCheckDTO.getParentMilestoneNumber())) {
                contractMilestone.setMilestoneType("1");
                contractMilestone.setParentId("");
            } else {
                contractMilestone.setMilestoneType("0");
                //临时id 方便找建立父子关系
                contractMilestone.setParentId(importCheckDTO.getParentMilestoneNumber());
            }
            if (!StrUtil.isEmpty(importCheckDTO.getMilestoneNumber())) {
                parentMap.put(importCheckDTO.getMilestoneNumber(), uuid);
            }
            contractMilestoneList.add(contractMilestone);

        }
        //父子关系建立
        for (ContractMilestone contractMilestone : contractMilestoneList) {
            if (ObjectUtil.isNotEmpty(contractMilestone.getParentId())) {
                String parentId = parentMap.getOrDefault(contractMilestone.getParentId(), "");
                contractMilestone.setParentId(parentId);
            }
        }

        if (CollectionUtil.isNotEmpty(contractMilestoneList)) {
            contractMilestoneMapper.insertBatch(contractMilestoneList);
        }
        orionJ2CacheService.delete("pmsx::ContractMileStone-import::id", dto.getImportId());
        return true;
    }

    private <T> List<T> getTypedList(Map<String, List<?>> dataMap, String key) {
        List<T> list = (List<T>) dataMap.get(key);
        if (list == null) {
            list = new ArrayList<>();
        }
        return list;
    }

    @Override
    public void exportExcelDatatreeNew(MileStoneTreeVo dto, HttpServletResponse response) throws Exception {

        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        condition.leftJoin(ProjectInitiation.class, "pi", ProjectInitiation::getProjectNumber,
                "t", ContractMilestone::getProjectCode);
        condition.selectAs(ProjectInitiation::getProjectNumber, ContractMilestone::getProjectCode);
        condition.selectAs(ProjectInitiation::getProjectPerson, ContractMilestone::getProjectPerson);
        condition.leftJoin(MarketContractSign.class, MarketContractSign::getContractId, ContractMilestone::getContractId);
        condition.selectAs(MarketContractSign::getSignDate, ContractMilestone::getSignTime);
        if (!CollectionUtils.isEmpty(dto.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(dto.getSearchConditions(), condition);
        }
        condition.selectAll(ContractMilestone.class);

        condition.orderByDesc(ContractMilestone::getCreateTime);


        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(condition);
        List<String> creatorId = contractMilestones.stream()
                .map(ContractMilestone::getCreatorId)
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());

        //导出
        List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOS = BeanCopyUtils.convertListTo(contractMilestones, MileStoneExportDataVo::new);
        List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOSFilter = contractMilestoneTreeExcelExportDTOS.stream()
                .filter(e -> StrUtil.isNotBlank(e.getProjectCode()) && StrUtil.isNotBlank(e.getProjectPerson()))
                .collect(Collectors.toList());
        Map<String, String> initiationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(contractMilestoneTreeExcelExportDTOSFilter)) {
            initiationMap = contractMilestoneTreeExcelExportDTOSFilter.stream()
                    .collect(Collectors.toMap(
                            MileStoneExportDataVo::getProjectCode,
                            MileStoneExportDataVo::getProjectPerson,
                            (existing, replacement) -> existing // 处理键冲突的情况，这里选择保留已存在的值
                    ));
        }

        //合同id
        List<String> contractIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getContractId).distinct().collect(Collectors.toList());
        List<String> userTempIds = new ArrayList<>();
        //所级负责人
        List<String> officeLeadIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getOfficeLeader).distinct().collect(Collectors.toList());
        //客户
        List<String> cusPersonIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getCusPersonId).distinct().collect(Collectors.toList());
        Map<String, MarketContract> contractMap = new HashMap<>();
        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        List<String> frameContractIds = contractMilestones.stream()
                .map(ContractMilestone::getContractId)
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());

        //甲方签约主体表
        LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (ObjectUtil.isNotEmpty(frameContractIds)) {
            contractOurSignedSubjectLambdaQueryWrapperX.in(ContractOurSignedSubject::getContractId, frameContractIds);
        }
        contractOurSignedSubjectLambdaQueryWrapperX.select(ContractOurSignedSubject::getId, ContractOurSignedSubject::getContractId, ContractOurSignedSubject::getContractNumber);
        List<ContractOurSignedSubject> contractOurSignedSubject = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
        Map<String, ContractOurSignedSubject> ourSignedSubjectMap = contractOurSignedSubject.stream()
                .collect(Collectors.toMap(ContractOurSignedSubject::getContractId, Function.identity(), (v1, v2) -> v1));
        //流程人id
        List<String> collectFolwUserIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contractIds)) {
            List<MarketContract> marketContracts = marketContractMapper.selectBatchIds(contractIds);
            for (MarketContract marketContract : marketContracts) {
                contractMap.put(marketContract.getId(), marketContract);
            }

            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(contractIds);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> rep = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : rep) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = rep.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }

        }
        userTempIds.addAll(officeLeadIds);
        userTempIds.addAll(collectFolwUserIds);
        LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (ObjectUtil.isNotEmpty(cusPersonIds)) {
            customerInfoLambdaQueryWrapperX.in(CustomerInfo::getId, cusPersonIds);
        }
        customerInfoLambdaQueryWrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getSalesClass);
        List<CustomerInfo> customerInfos = customerInfoMapper.selectList(customerInfoLambdaQueryWrapperX);
        Map<String, String> customerInfoMap = new HashMap<>();
        HashMap<String, String> salesClassMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(customerInfos)) {
            for (CustomerInfo customerInfo : customerInfos) {
                customerInfoMap.put(customerInfo.getId(), customerInfo.getCusName());
                salesClassMap.put(customerInfo.getId(), customerInfo.getSalesClass());
            }
        }
        List<String> totalUserIds = userTempIds.stream().distinct().collect(Collectors.toList());
        //承担部门
        Map<String, String> deptMapNow = new HashMap<>();
        List<String> deptIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getUndertDept).distinct().collect(Collectors.toList());
        List<String> uniqueOfficeDept = contractMilestones.stream()
                .map(ContractMilestone::getOfficeDept) // 提取每个对象的 officeLeader
                .filter(Objects::nonNull) // 过滤掉 null 值（可选）
                .distinct() // 去重
                .collect(Collectors.toList()); // 收集为 List
        deptIds.addAll(uniqueOfficeDept);
        deptIds = deptIds.stream()
                .distinct()
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(deptIds);
            for (DeptDO deptDO : deptDOS) {
                deptMapNow.put(deptDO.getId(), deptDO.getName());
            }
        }
        //里程碑调整维护记录
        LambdaQueryWrapperX<MileStoneLog> mileStoneLogLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        mileStoneLogLambdaQueryWrapperX.select(MileStoneLog::getId, MileStoneLog::getMilestoneId, MileStoneLog::getEditMessage);
        List<MileStoneLog> mileStoneLogs = this.list(mileStoneLogLambdaQueryWrapperX);
        Map<String, String> mileLogMap = mileStoneLogs.stream().collect(Collectors.toMap(MileStoneLog::getMilestoneId, MileStoneLog::getEditMessage, (v1, v2) -> v1));

        //币种
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        //用户
        Map<String, UserBaseCacheVO> uesrMap = new HashMap<>();
        List<String> uniqueOfficeLeaders = contractMilestoneTreeExcelExportDTOS.stream()
                .map(MileStoneExportDataVo::getOfficeLeader) // 提取每个对象的 officeLeader
                .filter(Objects::nonNull) // 过滤掉 null 值（可选）
                .distinct() // 去重
                .collect(Collectors.toList()); // 收集为 List
        totalUserIds.addAll(creatorId);
        totalUserIds.addAll(uniqueOfficeLeaders);
        totalUserIds = totalUserIds.stream()
                .distinct()
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(totalUserIds)) {
            List<UserBaseCacheVO> userByIds = userRedisHelper.getUserBaseCacheByIds(totalUserIds);
            for (UserBaseCacheVO userById : userByIds) {
                uesrMap.put(userById.getId(), userById);
            }
        }


        Map<Integer, String> statusMap = Map.of(
                MarketContractMilestoneStatusEnum.CREATED.getStatus(), MarketContractMilestoneStatusEnum.CREATED.getDesc(),
                MarketContractMilestoneStatusEnum.APPROVAL.getStatus(), MarketContractMilestoneStatusEnum.APPROVAL.getDesc(),
                MarketContractMilestoneStatusEnum.PROGRESS.getStatus(), MarketContractMilestoneStatusEnum.PROGRESS.getDesc(),
                MarketContractMilestoneStatusEnum.COMPLATED.getStatus(), MarketContractMilestoneStatusEnum.COMPLATED.getDesc()
        );
        //业务分类
        List<DictValueVO> costBusTypeList = dictRedisHelper.getDictList("dict1803442064070205440");
        Map<String, String> costBusTypeMap = costBusTypeList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        //销售业务分类
        List<DictValueVO> custSaleBusTypeList = dictRedisHelper.getDictList("dict1838196886603444224");
        Map<String, String> custSaleBusTypeMap = custSaleBusTypeList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        extracted(contractMilestoneTreeExcelExportDTOS, mileLogMap, statusMap, contractMap, initiationMap, ourSignedSubjectMap, custSaleBusTypeMap, currencyeMap, deptMapNow, flowMap, uesrMap, customerInfoMap, salesClassMap, costBusTypeMap);

        String fileName = "合同里程碑报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        EasyExcel.write(response.getOutputStream(), MileStoneExportDataVo.class).sheet("合同里程碑报表").doWrite(contractMilestoneTreeExcelExportDTOS);


    }

    private static void extracted(List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOS, Map<String, String> mileLogMap, Map<Integer, String> statusMap, Map<String, MarketContract> contractMap, Map<String, String> initiationMap, Map<String, ContractOurSignedSubject> ourSignedSubjectMap, Map<String, String> custSaleBusTypeMap, Map<String, String> currencyeMap, Map<String, String> deptMapNow, Map<String, FlowTemplateBusinessVO> flowMap, Map<String, UserBaseCacheVO> uesrMap, Map<String, String> customerInfoMap, Map<String, String> saleClassMap, Map<String, String> costBusTypeMap) {
        for (MileStoneExportDataVo excelExportDTO : contractMilestoneTreeExcelExportDTOS) {
            //所级 （取合同的技术负责人的所级）
            String officeDept = excelExportDTO.getOfficeDept();
            if (ObjectUtil.isNotEmpty(officeDept)) {
                excelExportDTO.setDepartmentalName(deptMapNow.getOrDefault(officeDept, ""));
            }
            String officeLeader = excelExportDTO.getOfficeLeader();
            if (ObjectUtil.isNotEmpty(officeLeader)) {
                UserBaseCacheVO userBaseCacheVO = uesrMap.get(officeLeader);
                if (ObjectUtil.isNotEmpty(userBaseCacheVO)) {
                    excelExportDTO.setOfficeLeaderName(userBaseCacheVO.getName());
                }
            }
            //业务分类
            String costBusType = excelExportDTO.getCostBusType();
            if (ObjectUtil.isNotEmpty(costBusType)) {
                excelExportDTO.setCostBusTypeName(costBusTypeMap.getOrDefault(costBusType, ""));
            }
            String creatorId1 = excelExportDTO.getCreatorId();
            if (ObjectUtil.isNotEmpty(creatorId1) && ObjectUtil.isNotEmpty(uesrMap) && ObjectUtil.isNotEmpty(uesrMap.get(creatorId1))) {
                excelExportDTO.setCreatorName("[" + uesrMap.get(creatorId1).getCode() + "]" +uesrMap.get(creatorId1).getName());
            }
            String milestoneId = excelExportDTO.getId();
            String mileLog = mileLogMap.get(milestoneId);
            if (ObjectUtil.isNotEmpty(mileLog)) {
                excelExportDTO.setIsHaveLog("是");
            } else {
                excelExportDTO.setIsHaveLog("否");
            }
            Integer status = excelExportDTO.getStatus();
            excelExportDTO.setStatusName(statusMap.get(status));
            MarketContract marketContract = contractMap.get(excelExportDTO.getContractId());
            String projectCode = excelExportDTO.getProjectCode();
            if (ObjectUtil.isNotEmpty(projectCode) && ObjectUtil.isNotEmpty(initiationMap)) {
                String projectPerson = initiationMap.get(projectCode);
                if (ObjectUtil.isNotEmpty(projectPerson)) {
                    excelExportDTO.setProjectPerson(projectPerson);
                }
            }
            // 合同信息
            if (marketContract != null) {
                String id = marketContract.getId();
                ContractOurSignedSubject contractOurSignedSubject1 = ourSignedSubjectMap.get(id);
                if (ObjectUtil.isNotEmpty(contractOurSignedSubject1)) {
                    excelExportDTO.setCustomerContractNumber(contractOurSignedSubject1.getCusContractNumber());
                }
                excelExportDTO.setContractNumber(marketContract.getNumber());
                excelExportDTO.setContractName(marketContract.getName());
                excelExportDTO.setContractStatus(marketContract.getStatus());
                BigDecimal contractAmt = marketContract.getContractAmt();
                BigDecimal frameContractAmt = marketContract.getFrameContractAmt();
                if (ObjectUtil.isEmpty(contractAmt)) {
                    contractAmt = BigDecimal.ZERO;
                }
                if (ObjectUtil.isEmpty(frameContractAmt)) {
                    frameContractAmt = BigDecimal.ZERO;
                }
                excelExportDTO.setContractAmt(frameContractAmt.add(contractAmt));
                excelExportDTO.setCustSaleBusType(marketContract.getCustSaleBusType());
                excelExportDTO.setSignTime(excelExportDTO.getSignTime());


                // 销售业务分类
                if (excelExportDTO.getCusPersonId() != null) {
                    String saleClassMapOrDefault = saleClassMap.getOrDefault(excelExportDTO.getCusPersonId(), "");
                    if (ObjectUtil.isNotEmpty(saleClassMapOrDefault)) {
                        String saleBusTypeMapOrDefault = custSaleBusTypeMap.getOrDefault(saleClassMapOrDefault, "");
                        excelExportDTO.setCustSaleBusTypeName(saleBusTypeMapOrDefault);
                    }
                }

                // 合同状态名称映射
                Map<Integer, String> contractStatusMap = Map.of(
                        101, "编制中",
                        130, "履行中",
                        160, "已完成",
                        140, "待分发",
                        110, "审核中",
                        121, "待签署"
                );
                excelExportDTO.setContractStatusName(contractStatusMap.getOrDefault(excelExportDTO.getContractStatus(), ""));
                Integer contractStatus = excelExportDTO.getContractStatus();
                if (contractStatus == 101) {
                    excelExportDTO.setFlowStatus("未发起");
                } else if (contractStatus == 110) {
                    excelExportDTO.setFlowStatus("审批中");
                } else {
                    excelExportDTO.setFlowStatus("已完成");
                }
            }

            // 币种
            excelExportDTO.setCurrencyName(currencyeMap.getOrDefault(excelExportDTO.getCurrency(), ""));
            //剩余未确认收入
            if (ObjectUtil.isNotEmpty(excelExportDTO.getAmmountType()) && ObjectUtil.isNotEmpty(excelExportDTO.getConfirmIncomeSum())) {
                if (excelExportDTO.getAmmountType().equals(MarketContractMilestoneEnum.MILESTONEAMT.getCode())) {
                    if (ObjectUtil.isNotEmpty(excelExportDTO.getMilestoneAmt())) {
                        excelExportDTO.setNoConfirmIncomeSum(excelExportDTO.getMilestoneAmt().subtract(excelExportDTO.getConfirmIncomeSum()));
                    }
                } else {
                    if (ObjectUtil.isNotEmpty(excelExportDTO.getExceptAcceptanceAmt())) {
                        excelExportDTO.setNoConfirmIncomeSum(excelExportDTO.getExceptAcceptanceAmt().subtract(excelExportDTO.getConfirmIncomeSum()));
                    }
                }
            }


            // 承担部门
            excelExportDTO.setUndertDeptName(deptMapNow.getOrDefault(excelExportDTO.getUndertDept(), ""));

            // 工作主题
            StringBuilder workTopicBuilder = new StringBuilder();
            workTopicBuilder.append(excelExportDTO.getUndertDeptName())
                    .append("-")
                    .append(excelExportDTO.getContractName())
                    .append("- 合同管理流程");
            excelExportDTO.setWorkTopic(workTopicBuilder.toString());

            // 流程信息
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(excelExportDTO.getContractId());
            if (flowTemplateBusinessVO != null) {
                excelExportDTO.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                excelExportDTO.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                excelExportDTO.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                UserBaseCacheVO flowCreatePerson = uesrMap.get(flowTemplateBusinessVO.getApplyUserId());
                excelExportDTO.setFlowCreatePersonNumber(flowCreatePerson == null ? "" : flowCreatePerson.getCode());
            }

            // 负责人
            if (excelExportDTO.getOfficeLeader() != null) {
                UserBaseCacheVO userVO = uesrMap.get(excelExportDTO.getOfficeLeader());
                excelExportDTO.setOfficeLeaderName(userVO == null ? "" :"[" + userVO.getCode() + "]" + userVO.getName());
            }

            //税率
            if (ObjectUtil.isNotEmpty(excelExportDTO.getTaxRate())) {
                excelExportDTO.setTaxRateName(excelExportDTO.getTaxRate().stripTrailingZeros().toPlainString() + "%");
            }

            // 客户
            if (excelExportDTO.getCusPersonId() != null) {
                excelExportDTO.setCusPersonName(customerInfoMap.getOrDefault(excelExportDTO.getCusPersonId(), ""));
            }
        }
    }

    @Override
    public Page<ContractMilestoneVO> pagesMenu(Page<ContractMilestoneDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractMilestone> condition = new LambdaQueryWrapperX<>(ContractMilestone.class);
        condition.leftJoin(ProjectInitiation.class, "pi", ProjectInitiation::getProjectNumber,
                "t", ContractMilestone::getProjectCode);
        condition.selectAs(ProjectInitiation::getProjectPerson, ContractMilestone::getProjectPerson);
        condition.selectAs(ProjectInitiation::getProjectNumber, ContractMilestone::getProjectCode);
        condition.leftJoin(MarketContractSign.class, MarketContractSign::getContractId, ContractMilestone::getContractId);
        condition.selectAs(MarketContractSign::getSignDate, ContractMilestone::getSignTime);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(ContractMilestone.class);

        condition.orderByDesc(ContractMilestone::getCreateTime);
        Page<ContractMilestone> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractMilestone::new));
        IPage<ContractMilestone> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<ContractMilestone> result = contractMilestoneMapper.selectPage(mPage, condition);
        Page<ContractMilestoneVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        List<ContractMilestoneVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(result.getRecords(), ContractMilestoneVO::new);

        List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOS = BeanCopyUtils.convertListTo(contractMilestoneVOS, MileStoneExportDataVo::new);

        List<String> creatorId = contractMilestoneVOS.stream()
                .map(ContractMilestoneVO::getCreatorId)
                .filter(Objects::nonNull) // 过滤掉 null 值
                .distinct() // 去重
                .collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(contractMilestoneTreeExcelExportDTOS)) {
            List<MileStoneExportDataVo> contractMilestoneTreeExcelExportDTOSFilter = contractMilestoneTreeExcelExportDTOS.stream()
                    .filter(e -> StrUtil.isNotBlank(e.getProjectCode()) && StrUtil.isNotBlank(e.getProjectPerson()))
                    .collect(Collectors.toList());
            Map<String, String> initiationMap = contractMilestoneTreeExcelExportDTOSFilter.stream()
                    .collect(Collectors.toMap(
                            MileStoneExportDataVo::getProjectCode,
                            MileStoneExportDataVo::getProjectPerson,
                            (existing, replacement) -> existing // 处理键冲突的情况，这里选择保留已存在的值
                    ));
            if (!CollectionUtils.isEmpty(contractMilestoneTreeExcelExportDTOS)) {
                //合同id
                List<String> contractIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getContractId).distinct().collect(Collectors.toList());
                List<String> userTempIds = new ArrayList<>();
                //所级负责人
                List<String> officeLeadIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getOfficeLeader).distinct().collect(Collectors.toList());
                //客户
                List<String> cusPersonIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getCusPersonId).distinct().collect(Collectors.toList());
                Map<String, MarketContract> contractMap = new HashMap<>();
                Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
                //甲方签约主体表
                LambdaQueryWrapperX<ContractOurSignedSubject> contractOurSignedSubjectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                contractOurSignedSubjectLambdaQueryWrapperX.select(ContractOurSignedSubject::getId, ContractOurSignedSubject::getContractId, ContractOurSignedSubject::getContractNumber);
                List<ContractOurSignedSubject> contractOurSignedSubject = contractOurSignedSubjectService.list(contractOurSignedSubjectLambdaQueryWrapperX);
                Map<String, ContractOurSignedSubject> ourSignedSubjectMap = contractOurSignedSubject.stream()
                        .collect(Collectors.toMap(ContractOurSignedSubject::getContractId, Function.identity(), (v1, v2) -> v1));
                //流程人id
                List<String> collectFolwUserIds = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(contractIds)) {
                    List<MarketContract> marketContracts = marketContractMapper.selectBatchIds(contractIds);
                    for (MarketContract marketContract : marketContracts) {
                        contractMap.put(marketContract.getId(), marketContract);
                    }

                    ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
                    try {
                        listByBusinessIds = workflowFeignService.findListByBusinessIds(contractIds);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                        List<FlowTemplateBusinessVO> rep = listByBusinessIds.getResult();
                        for (FlowTemplateBusinessVO flowTemplateBusinessVO : rep) {
                            flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                        }
                        //收集流程人员id
                        collectFolwUserIds = rep.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
                    }

                }
                userTempIds.addAll(officeLeadIds);
                userTempIds.addAll(collectFolwUserIds);
                List<CustomerInfo> customerInfos = new ArrayList<>();
                HashMap<String, String> salesClassMap = new HashMap<>();
                if (ObjectUtil.isNotEmpty(cusPersonIds)) {
                    LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                    customerInfoLambdaQueryWrapperX.in(CustomerInfo::getId, cusPersonIds);
                    customerInfoLambdaQueryWrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getSalesClass);
                    customerInfos = customerInfoMapper.selectList(customerInfoLambdaQueryWrapperX);
                    Map<String, String> customerInfoMap = new HashMap<>();
                    if (ObjectUtil.isNotEmpty(customerInfos)) {
                        for (CustomerInfo customerInfo : customerInfos) {
                            customerInfoMap.put(customerInfo.getId(), customerInfo.getCusName());
                            salesClassMap.put(customerInfo.getId(), customerInfo.getSalesClass());
                        }
                    }
                }

                Map<String, String> customerInfoMap = new HashMap<>();
                if (ObjectUtil.isNotEmpty(customerInfos)) {
                    for (CustomerInfo customerInfo : customerInfos) {
                        customerInfoMap.put(customerInfo.getId(), customerInfo.getCusName());
                    }
                }
                List<String> totalUserIds = userTempIds.stream().distinct().collect(Collectors.toList());
                //承担部门
                Map<String, String> deptMapNow = new HashMap<>();
                List<String> deptIds = contractMilestoneTreeExcelExportDTOS.stream().map(MileStoneExportDataVo::getUndertDept).distinct().collect(Collectors.toList());
                List<ContractMilestone> records = result.getRecords();
                List<String> uniqueOfficeDept = records.stream()
                        .map(ContractMilestone::getOfficeDept) // 提取每个对象的 officeLeader
                        .filter(Objects::nonNull) // 过滤掉 null 值（可选）
                        .distinct() // 去重
                        .collect(Collectors.toList()); // 收集为 List
                deptIds.addAll(uniqueOfficeDept);
                deptIds = deptIds.stream()
                        .distinct()
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(deptIds)) {
                    List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(deptIds);
                    for (DeptDO deptDO : deptDOS) {
                        deptMapNow.put(deptDO.getId(), deptDO.getName());
                    }
                }
                //里程碑调整维护记录
                LambdaQueryWrapperX<MileStoneLog> mileStoneLogLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                mileStoneLogLambdaQueryWrapperX.select(MileStoneLog::getId, MileStoneLog::getMilestoneId, MileStoneLog::getEditMessage);
                List<MileStoneLog> mileStoneLogs = this.list(mileStoneLogLambdaQueryWrapperX);
                Map<String, String> mileLogMap = mileStoneLogs.stream().collect(Collectors.toMap(MileStoneLog::getMilestoneId, MileStoneLog::getEditMessage, (v1, v2) -> v1));

                //币种
                List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
                Map<String, String> currencyeMap = currencyList.stream().collect(
                        Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

                //用户
                Map<String, UserBaseCacheVO> uesrMap = new HashMap<>();
                List<String> uniqueOfficeLeaders = contractMilestoneTreeExcelExportDTOS.stream()
                        .map(MileStoneExportDataVo::getOfficeLeader) // 提取每个对象的 officeLeader
                        .filter(Objects::nonNull) // 过滤掉 null 值（可选）
                        .distinct() // 去重
                        .collect(Collectors.toList()); // 收集为 List
                totalUserIds.addAll(creatorId);
                totalUserIds.addAll(uniqueOfficeLeaders);
                totalUserIds = totalUserIds.stream()
                        .distinct()
                        .collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(totalUserIds)) {
                    List<UserBaseCacheVO> userByIds = userRedisHelper.getUserBaseCacheByIds(totalUserIds);
                    for (UserBaseCacheVO userById : userByIds) {
                        uesrMap.put(userById.getId(), userById);
                    }
                }
                Map<Integer, String> statusMap = Map.of(
                        MarketContractMilestoneStatusEnum.CREATED.getStatus(), MarketContractMilestoneStatusEnum.CREATED.getDesc(),
                        MarketContractMilestoneStatusEnum.APPROVAL.getStatus(), MarketContractMilestoneStatusEnum.APPROVAL.getDesc(),
                        MarketContractMilestoneStatusEnum.PROGRESS.getStatus(), MarketContractMilestoneStatusEnum.PROGRESS.getDesc(),
                        MarketContractMilestoneStatusEnum.COMPLATED.getStatus(), MarketContractMilestoneStatusEnum.COMPLATED.getDesc()
                );
                //业务分类
                List<DictValueVO> costBusTypeList = dictRedisHelper.getDictList("dict1803442064070205440");
                Map<String, String> costBusTypeMap = costBusTypeList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
                //销售业务分类
                List<DictValueVO> custSaleBusTypeList = dictRedisHelper.getDictList("dict1838196886603444224");
                Map<String, String> custSaleBusTypeMap = custSaleBusTypeList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
                extracted(contractMilestoneTreeExcelExportDTOS, mileLogMap, statusMap, contractMap, initiationMap, ourSignedSubjectMap, custSaleBusTypeMap, currencyeMap, deptMapNow, flowMap, uesrMap, customerInfoMap, salesClassMap, costBusTypeMap);

            }
        }

        List<ContractMilestoneVO> milestoneVOS = BeanCopyUtils.convertListTo(contractMilestoneTreeExcelExportDTOS, ContractMilestoneVO::new);
        pageResult.setContent(milestoneVOS);
        return pageResult;
    }

    @Override
    public Page<MileStoneLogVO> reschedulePages(Page<MileStoneLogDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MileStoneLog> condition = new LambdaQueryWrapperX<>(MileStoneLog.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(MileStoneLog::getEditDesc, "跟踪确认");
        condition.eq(MileStoneLog::getMilestoneId, pageRequest.getQuery().getMilestoneId());
        condition.orderByDesc(MileStoneLog::getCreateTime);


        Page<MileStoneLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MileStoneLog::new));

        PageResult<MileStoneLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MileStoneLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MileStoneLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MileStoneLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<MileStoneLogVO> acceptancePages(Page<MileStoneLogDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MileStoneLog> condition = new LambdaQueryWrapperX<>(MileStoneLog.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(MileStoneLog::getEditDesc, "验收");
        condition.eq(MileStoneLog::getMilestoneId, pageRequest.getQuery().getMilestoneId());
        condition.orderByDesc(MileStoneLog::getCreateTime);


        Page<MileStoneLog> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MileStoneLog::new));

        PageResult<MileStoneLog> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MileStoneLogVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MileStoneLogVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MileStoneLogVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

}


