package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.dto.MajorPersonStatisticDTO;
import com.chinasie.orion.domain.dto.importantProject.ImportParamDTO;
import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.domain.dto.ImportantProjectDTO;
import com.chinasie.orion.domain.vo.ImportantProjectVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.JobProgressVO;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.domain.vo.count.DateComputeVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ImportantProject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
public interface ImportantProjectService  extends  OrionBaseService<ImportantProject>  {


        /**
         *  详情
         *
         * * @param id
         */
    ImportantProjectVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param importantProjectDTO
         */
        String create(ImportantProjectDTO importantProjectDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param importantProjectDTO
         */
        Boolean edit(ImportantProjectDTO importantProjectDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ImportantProjectVO> pages( Page<ImportantProjectDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(Page<ImportantProject> pageRequest,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ImportantProjectVO> vos)throws Exception;

    /**
     * 计算时间
     * @param ids job列表
     * @return 结果
     */
    DateComputeVO computeDate(List<String> ids);

    /**
     * 通过重大项目id获取进展列表
     * @param pageRequest 参数
     * @return 结果
     */
    Page<JobProgressVO> getJobProgressByProjectId(Page<JobProgressDTO> pageRequest);

    /**
     *  过滤
     * @param materialParamDTOPage
     * @return
     */
    Page<SchemeToMaterialVO> materialPages(Page<ImportParamDTO> materialParamDTOPage) throws Exception;

    /**
     *  获取数据进行统计
     * @param materialParamDTO
     * @return
     */
    MajorPersonStatisticDTO getMaterialStatistic(ImportParamDTO materialParamDTO);

    /**
     *
     * @param pageRequest
     * @return
     */
    Page<SchemeToPersonVO> personPages(Page<ImportParamDTO> pageRequest);

    MajorPersonStatisticDTO getPersonStatistic(ImportParamDTO importParamDTO);

    /**
     * 新增重大项目进度
     * @param jobProgressDTO 参数
     */
    void addProgress(JobProgressDTO jobProgressDTO);

    /**
     * 编辑重大项目进度
     * @param jobProgressDTO 参数
     */
    void editProgress(JobProgressDTO jobProgressDTO);

    /**
     * 删除进展
     * @param ids id列表
     * @return 结果
     */
    Boolean deleteProgress(List<String> ids);

    public void getJobProgressByProjectIdExport(JobProgressDTO jobProgressDTO, HttpServletResponse response) throws Exception;
}
