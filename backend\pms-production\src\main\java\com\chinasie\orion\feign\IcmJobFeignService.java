package com.chinasie.orion.feign;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.dto.RepairJobManagerDTO;
import com.chinasie.orion.feign.vo.NewJobMangeVO;
import com.chinasie.orion.feign.vo.PageVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "icm",configuration = {FeignConfig.class})
@Lazy
public interface IcmJobFeignService {


    @PostMapping("/job/manage/page")
    ResponseDTO<PageVO<NewJobMangeVO>> jobManageList(@RequestBody RepairJobManagerDTO repairJobManagerDTO);


}
