package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * JobProgress VO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
@ApiModel(value = "JobProgressVO对象", description = "作业工作进展")
@Data
public class JobProgressVO extends  ObjectVO   implements Serializable{
        @ApiModelProperty(value = "重大项目名称")
        private String importProjectName;
        @ApiModelProperty(value = "作业名称")
        private String jobName;
        @ApiModelProperty(value = "作业编码")
        private String jobNumber;
        @ApiModelProperty(value = "责任人名称")
        private String rspUserName;

            /**
         * 作业id
         */
        @ApiModelProperty(value = "作业id")
        private String jobId;


        /**
         * 工作日期
         */
        @ApiModelProperty(value = "工作日期")
        private Date workDate;


        /**
         * 总体进展
         */
        @ApiModelProperty(value = "总体进展")
        private BigDecimal progressSchedule;


        /**
         * 工作进展
         */
        @ApiModelProperty(value = "工作进展")
        private String progressDetail;


    

}
