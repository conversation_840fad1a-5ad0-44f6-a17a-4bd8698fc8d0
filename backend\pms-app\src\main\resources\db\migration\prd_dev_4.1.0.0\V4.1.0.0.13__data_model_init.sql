INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11820634282397937664', 'MarketContractNo1', NULL, '合同单号流水号1', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', '合同单号流水号1', '314j1000000000000000000', '314j1000000000000000000', '2024-08-06 09:33:33', 'user00000000000000000100000000000000', '2024-08-06 09:59:57', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '4da9185f09794da8bd6911e36db50543', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11820634400077524992', 'MarketContractNo2', NULL, '合同单号流水号2', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-08-06 09:34:01', 'user00000000000000000100000000000000', '2024-08-06 09:59:59', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'd268cd0c72e0450e80107a4e3f65333a', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11820634449113133056', 'MarketContractNo3', NULL, '合同单号流水号3', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-08-06 09:34:13', 'user00000000000000000100000000000000', '2024-08-06 10:00:00', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, '1dbad3e6ca184498b69b51db68c9ba87', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');

INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1820640565314334720', '流水号', '0', '9hi11820634282397937664', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-06 09:58:31', 'user00000000000000000100000000000000', '2024-08-06 09:58:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1820640710307229696', '流水号', '0', '9hi11820634400077524992', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-06 09:59:06', 'user00000000000000000100000000000000', '2024-08-06 09:59:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1820640878721118208', '流水号', '0', '9hi11820634449113133056', 'f9g6089cb3c19a6d4b1da0d67bbd398efdd1', 'piPer', '', '', '0', 1, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-06 09:59:46', 'user00000000000000000100000000000000', '2024-08-06 09:59:46', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);

INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1821358472549773312', 'code1', '9hi11820634282397937664', 'MarketContract', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-08 09:31:14', 'user00000000000000000100000000000000', '2024-08-08 09:31:14', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1821358538379374592', 'code2', '9hi11820634400077524992', 'MarketContract', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-08 09:31:29', 'user00000000000000000100000000000000', '2024-08-08 09:31:29', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1821358593953902592', 'code3', '9hi11820634449113133056', 'MarketContract', 'SysCodeMappingRelation', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-08-08 09:31:43', 'user00000000000000000100000000000000', '2024-08-08 09:31:43', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');
