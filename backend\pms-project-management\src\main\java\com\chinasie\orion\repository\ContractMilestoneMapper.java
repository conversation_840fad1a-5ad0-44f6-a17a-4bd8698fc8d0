package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.vo.MarketContractApiVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * ContractMilestone Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@Mapper
public interface ContractMilestoneMapper extends  OrionBaseMapper  <ContractMilestone> {

    /**
     * 根据项目编号查询市场合同
     *
     * @param
     * @return
     */
    List<ContractMilestone> selectListContract(@Param("contractId") String contractId,@Param("milestoneName") String milestoneName);


    void setPlanAcceptDateToNull(@Param("id") String id);

    void setExpectAcceptDateToNull(@Param("id") String id);
}

