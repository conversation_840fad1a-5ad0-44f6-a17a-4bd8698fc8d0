<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower, downloadByData,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, nextTick, onMounted, reactive, ref, Ref, watchEffect,
} from 'vue';
import {
  message, Modal, RangePicker, Space,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import { openFormDrawer, parseBooleanToRender, parsePriceByNumber } from '../utils';
import Api from '/@/api';
import NoContractForm from './components/NoContractForm.vue';
import MoneyRow from '../components/MoneyRow.vue';

const rowMoney = reactive([
  {
    key: 'total',
    title: '支付条数',
    value: '',
    suffix: '条',
  },
  {
    key: 'allMoney',
    title: '申请金额',
    value: '',
    suffix: '元',
  },
]);
const router = useRouter();

const selectKeys: Ref<string[]> = ref([]);

const tableRef: Ref = ref();
const pageSearchConditions = ref(null);
const loadStatus = ref(false);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  isSpacing: true,
  pagination: {},
  smallSearchField: [
    'workTopic',
    'processName',
    'projectCode',
  ],
  filterConfig: {
    fields: [
      {
        field: 'workTopic',
        fieldName: '工作主题',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'processName',
        fieldName: '流程名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierInfo',
        fieldName: '供应商信息',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'projectCode',
        fieldName: '立项号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'claimant',
        fieldName: '报销人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '工作主题',
      dataIndex: 'workTopic',
      width: 480,
      minWidth: 480,
    },
    {
      title: '流程名称',
      dataIndex: 'processName',
      width: 180,
    },
    {
      title: '发起时间',
      dataIndex: 'initiationTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '发起人',
      dataIndex: 'initiator',
      width: 150,
    },
    {
      title: '报销人',
      dataIndex: 'claimant',
      width: 150,
    },
    {
      title: '申请公司编码',
      dataIndex: 'applyCompanyCode',
      width: 130,
    },
    {
      title: '申请公司名称',
      dataIndex: 'applyCompanyName',
      width: 220,
    },
    {
      title: '申请部门',
      dataIndex: 'applyDept',
      width: 150,
    },
    {
      title: '费用归属公司编码',
      dataIndex: 'expenseCompanyCode',
      width: 160,
    },
    {
      title: '费用归属公司名称',
      dataIndex: 'xpenseCompanyName',
      width: 220,
    },
    {
      title: '报销金额',
      dataIndex: 'reimbursedAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '要求付款时间',
      dataIndex: 'reqPaymentTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
    },
    {
      title: '折合人民币',
      dataIndex: 'inRmb',
      width: 100,
    },
    {
      title: '申请原因',
      dataIndex: 'applyReason',
      width: 220,
    },
    {
      title: '费用信息',
      dataIndex: 'expenseInfo',
      width: 150,
    },
    {
      title: '供应商信息',
      dataIndex: 'supplierInfo',
      width: 150,
    },
    {
      title: '是否内部交易',
      dataIndex: 'isInternalTx',
      width: 130,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '内部交易号',
      dataIndex: 'internalTxNumber',
      width: 150,
    },
    {
      title: '流程状态',
      dataIndex: 'processStatus',
      width: 100,
      customRender({ text }) {
        return `${text}` === '3' ? '流程审批完成' : text;
      },
    },
    {
      title: '支付方式',
      dataIndex: 'paymentWay',
      width: 100,
    },
    {
      title: '立项类别',
      dataIndex: 'projectCategory',
      width: 100,
    },
    {
      title: '立项号',
      dataIndex: 'projectCode',
      width: 100,
    },
    {
      title: '立项名称',
      dataIndex: 'projectName',
      width: 150,
    },
    {
      title: '归口部门',
      dataIndex: 'bkDept',
      width: 150,
    },
    {
      title: '支付金额',
      dataIndex: 'paymentAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '交易金额',
      dataIndex: 'transactionAmount',
      width: 100,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '实际支付金额',
      dataIndex: 'actualPaymentAmount',
      width: 110,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params:Record<string, any>) => {
    pageSearchConditions.value = params.searchConditions ? [...params.searchConditions] : null;
    return new Api('/spm/nonContractProc').fetch({
      ...params,
      power: {
        pageCode: 'noPurchaseContract',
        containerCode: 'PMS_WCGHT_container_01',
      },
    }, 'page', 'POST');
  },
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_WCGHT_container_01_button_03', record.rdAuthList),
  },
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_WCGHT_container_01_button_02', record.rdAuthList),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record) => isPower('PMS_WCGHT_container_01_button_01', record.rdAuthList),
  },
];
const powerData = ref();

const showExportButton = computed(() => isPower('PMS_WCGHT_container_01_button_04', powerData.value));

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(NoContractForm, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'NoPurchaseContractInfo',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}
function updateTable() {
  tableRef.value?.reload();
}
function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/spm/nonContractProc').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
const getMoney = async () => {
  try {
    const res = await new Api('/spm/nonContractProc/getNumMoney').fetch({
      searchConditions: pageSearchConditions.value ? pageSearchConditions.value : null,
    }, '', 'POST');
    rowMoney.forEach((item) => {
      item.value = res[item.key];
    });
  } catch (e) {
  }
};
const exportTable = () => {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      let res = await downloadByData('/spm/nonContractProc/export/excel', {
        searchConditions: selectKeys.value.length
          ? [// 数组外层为or，内层为and
            [
              {
                field: 'id',
                fieldType: 'String',
                values: selectKeys.value,
                queryType: 'in',
              },
            ],
          ]
          : pageSearchConditions.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
};
function selectionChange({
  keys,
}) {
  selectKeys.value = keys; // 导出所选用
}

const getPowerDataHandle = (data) => {
  powerData.value = data;
};

watchEffect(async () => {
  await getMoney();
});
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'noPurchaseContract',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <Space>
          <BasicButton
            v-if="showExportButton"
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable()"
          >
            导出全部
          </BasicButton>
          <MoneyRow :data="rowMoney" />
        </Space>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
