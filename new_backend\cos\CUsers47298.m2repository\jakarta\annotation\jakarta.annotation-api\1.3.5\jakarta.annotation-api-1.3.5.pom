<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2012, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>jakarta.annotation</groupId>
        <artifactId>ca-parent</artifactId>
        <version>1.3.5</version>
    </parent>

    <artifactId>jakarta.annotation-api</artifactId>

    <properties>
        <non.final>false</non.final>
        <spec.version>1.3</spec.version>
        <extension.name>jakarta.annotation</extension.name>
        <vendor.name>Eclipse Foundation</vendor.name>
        <implementation.vendor.id>org.glassfish</implementation.vendor.id>
        <findbugs.version>3.0.5</findbugs.version>
        <findbugs.exclude>exclude.xml</findbugs.exclude>
        <findbugs.threshold>Low</findbugs.threshold>
    </properties>

    <name>Jakarta Annotations API</name>
    <description>Jakarta Annotations API</description>

    <url>https://projects.eclipse.org/projects/ee4j.ca</url>

    <developers>
        <developer>
            <name>Linda De Michiel</name>
            <organization>Oracle Corp.</organization>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:https://github.com/eclipse-ee4j/common-annotations-api.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/common-annotations-api.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/common-annotations-api</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/common-annotations-api/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Jakarta Annotations mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/ca-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/ca-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/ca-dev</archive>
        </mailingList>
    </mailingLists>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>spec-version-maven-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>4.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>${findbugs.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.7.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${basedir}/..</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgument>-Xlint:unchecked</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>spec-version-maven-plugin</artifactId>
                <configuration>
                    <specMode>jakarta</specMode>
                    <spec>
                        <nonFinal>${non.final}</nonFinal>
                        <jarType>api</jarType>
                        <specBuild>${spec.build}</specBuild>
                        <specVersion>${spec.version}</specVersion>
                        <newSpecVersion>${new.spec.version}</newSpecVersion>
                        <specImplVersion>${project.version}</specImplVersion>
                        <apiPackage>${extension.name}</apiPackage>
                    </spec>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>set-spec-properties</goal>
                            <!-- TODO:
                            glassfish-spec-version-maven-plugin needs to be updated
                            in order to check 'jakarta.' prefixed values in manifest entries
                            -->
                            <!--<goal>check-module</goal>-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <supportedProjectTypes>
                        <supportedProjectType>jar</supportedProjectType>
                    </supportedProjectTypes>
                    <instructions>
                        <Bundle-Version>${spec.bundle.version}</Bundle-Version>
                        <Bundle-SymbolicName>${spec.bundle.symbolic-name}</Bundle-SymbolicName>
                        <Extension-Name>${spec.extension.name}</Extension-Name>
                        <Implementation-Version>${spec.implementation.version}</Implementation-Version>
                        <Specification-Version>${spec.specification.version}</Specification-Version>
                        <Bundle-Description>${project.name}</Bundle-Description>
                        <Specification-Vendor>${vendor.name}</Specification-Vendor>
                        <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                        <Implementation-Vendor-Id>${implementation.vendor.id}</Implementation-Vendor-Id>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <!-- for JDK 9 -->
                            <Automatic-Module-Name>
                                java.annotation
                            </Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <includePom>true</includePom>
                </configuration>
                <executions>
                    <execution>
                       <id>attach-sources</id>
                       <goals>
                           <goal>jar-no-fork</goal>
                       </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <doctitle>Jakarta Annotations ${project.version} API Specification</doctitle>
                    <sourceFileExcludes>
                        <sourceFileExclude>**/module-info.java</sourceFileExclude>
                        <sourceFileExclude>target/**/*.java</sourceFileExclude>
                    </sourceFileExcludes>
                    <docfilessubdirs>true</docfilessubdirs>
                    <links>
                        <link>http://docs.oracle.com/javase/8/docs/api/</link>
                    </links>
                    <detectJavaApiLink>false</detectJavaApiLink>
                    <detectOfflineLinks>false</detectOfflineLinks>
                    <header><![CDATA[<br>Jakarta Annotations API v${project.version}]]>
                    </header>
                    <bottom><![CDATA[
                    Copyright &#169; 2019 Eclipse Foundation. All rights reserved.<br>
                    Use is subject to <a href="{@docRoot}/doc-files/EFSL.html" target="_top">license terms</a>.]]>
                    </bottom>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <configuration>
                    <threshold>${findbugs.threshold}</threshold>
                    <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <mavenExecutorId>forked-path</mavenExecutorId>
                    <useReleaseProfile>false</useReleaseProfile>
                    <arguments>${release.arguments}</arguments>
                    <tagNameFormat>@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <configuration>
                    <reporting>
                        <plugins>
                            <plugin>
                                <groupId>org.codehaus.mojo</groupId>
                                <artifactId>findbugs-maven-plugin</artifactId>
                                <version>${findbugs.version}</version>
                                <configuration>
                                    <threshold>${findbugs.threshold}</threshold>
                                    <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                                </configuration>
                            </plugin>
                        </plugins>
                    </reporting>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
