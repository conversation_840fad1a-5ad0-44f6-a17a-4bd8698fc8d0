<script setup lang="ts">
import dayjs from 'dayjs';
import { h, ref } from 'vue';
import Api from '/@/api';
import { get } from 'lodash-es';
import { message } from 'ant-design-vue';
import { OrionTable } from 'lyra-component-vue3';

const props = withDefaults(defineProps<{
  record:Record<string, any>
}>(), {
  record: () => ({}),
});

const tableRef = ref();
const tableOptions = {
  rowSelection: {},
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '资产代码',
      width: 100,
      dataIndex: 'code',
    },
    {
      title: '资产编码/条码',
      width: 190,
      dataIndex: 'number',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      width: 370,
      dataIndex: 'name',
    },
    {
      title: '数量',
      width: 100,
      dataIndex: 'numCount',
    },
    {
      title: '成本中心名称',
      width: 115,
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      width: 222,
      dataIndex: 'spModel',
    },
    {
      title: '是否需要检定',
      width: 115,
      dataIndex: 'isNeedVerification',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '下次检定日期',
      width: 115,
      dataIndex: 'nextVerificationTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('span', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '责任人工号',
      width: 115,
      dataIndex: 'rspUserNumber',
    },
    {
      title: '责任人姓名',
      width: 110,
      dataIndex: 'rspUserName',
    },
    {
      title: '使用人工号',
      width: 110,
      dataIndex: 'useUserNumber',
    },
    {
      title: '使用人姓名',
      width: 100,
      dataIndex: 'useUserName',
    },
    {
      title: '资产存放地',
      width: 100,
      dataIndex: 'storageLocationName',
    },
  ],
  smallSearchField: [
    'number',
    'name',
    'productCode',
  ],
  api: (params: Record<string, any>) => new Api('/pms/fixed-assets').fetch({
    ...params,
  }, 'page', 'POST'),
};

defineExpose({
  onSubmit() {
    return new Promise((resolve) => {
      const targetKeys = tableRef.value?.getSelectRowKeys();
      new Api('/pms/projectScheme/relation/fixedAsset').fetch({
        toId: get(props, 'record.id'),
        fromIds: targetKeys,
      }, '', 'POST')
        .then((res) => {
          message.success('添加成功');
          resolve(res);
        });
    });
  },
});
</script>

<template>
  <div class="materials-equipment-modal">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.materials-equipment-modal{
  height: 590px;
  overflow: hidden;
}
</style>