<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :isTable="!isTable"
      class="card-list-table"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_04_01_01_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addBudget"
        >
          新增预算
        </BasicButton>
        <BasicButtonGroup>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_04_01_01',powerData)"
            :type="isTable?'':'primary'"
            :ghost="!isTable"
            @click="emits('budgetType', true)"
          >
            预算编制
          </BasicButton>
          <BasicButton
            v-if="isPower('PMS_XMXQ_container_04_01_02',powerData)"
            :type="isTable?'primary':''"
            :ghost="isTable"
            @click="emits('budgetType', false)"
          >
            预算执行
          </BasicButton>
        </BasicButtonGroup>
      </template>
    </OrionTable>
    <!--编辑抽屉-->
    <BudgetingDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  nextTick, ref, reactive, computed, inject,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton,
  BasicButtonGroup, isPower,
  Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import {
  Modal, message, Empty,
} from 'ant-design-vue';
import Api from '/@/api';
import { getBudgeting } from '/@/views/pms/api/costManage';
import BudgetingDrawer from './components/budgetingDrawer.vue';
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const emits = defineEmits(['budgetType']);
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const isTable = ref(false);
const powerData = inject('powerData');
const tableRef = ref(null);
const monthlyBudget = reactive([
  {
    title: '1月',
    dataIndex: ['budgetMonthVO', 'januaryMoney'],
    minWidth: 140,
  },
  {
    title: '2月',
    dataIndex: ['budgetMonthVO', 'februaryMoney'],
    minWidth: 140,
  },
  {
    title: '3月',
    dataIndex: ['budgetMonthVO', 'marchMoney'],
    minWidth: 140,
  },
  {
    title: '4月',
    dataIndex: ['budgetMonthVO', 'aprilMoney'],
    minWidth: 140,
  },
  {
    title: '5月',
    dataIndex: ['budgetMonthVO', 'mayMoney'],
    minWidth: 140,
  },
  {
    title: '6月',
    dataIndex: ['budgetMonthVO', 'juneMoney'],
    minWidth: 140,
  },
  {
    title: '7月',
    dataIndex: ['budgetMonthVO', 'julyMoney'],
    minWidth: 140,
  },
  {
    title: '8月',
    dataIndex: ['budgetMonthVO', 'augustMoney'],
    minWidth: 140,
  },
  {
    title: '9月',
    dataIndex: ['budgetMonthVO', 'septemberMoney'],
    minWidth: 140,
  },
  {
    title: '10月',
    dataIndex: ['budgetMonthVO', 'octoberMoney'],
    minWidth: 140,
  },
  {
    title: '11月',
    dataIndex: ['budgetMonthVO', 'novemberMoney'],
    minWidth: 140,
  },
  {
    title: '12月',
    dataIndex: ['budgetMonthVO', 'decemberMoney'],
    minWidth: 140,
  },
]);
const columns = [
  {
    title: '预算编码',
    dataIndex: 'number',
    width: 120,
  },
  {
    title: '预算名称',
    dataIndex: 'name',
    minWidth: 150,
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterName',
    width: 100,
  },
  {
    title: '科目',
    dataIndex: 'expenseAccountName',
    width: 100,
  },
  {
    title: '年度',
    dataIndex: 'year',
    width: 100,
  },
  {
    title: '年度总预算(元)',
    dataIndex: 'yearExpense',
    minWidth: 200,
  },
  {
    title: '月份预算(元)',
    align: 'center',
    children: monthlyBudget,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    minWidth: 200,
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_04_01_01_button_02', record.rdAuthList),
    onClick(record) {
      openEditDrawer(true, {
        type: 'edit',
        formData: record,
        projectId: props.id,
      });
    },
  },
  {
    text: '删除',
    isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_04_01_01_button_03', record.rdAuthList),
    modal(record) {
      return new Api('/pms/project-budget').fetch([record.id], '', 'DELETE').then(() => {
        message.success('删除成功');
        tableRef.value.reload();
      });
    },
  },
];
const baseTableOption = {
  rowSelection: {},
  columns,
  api: (params) => getBudgeting({
    ...params,
    query: { projectId: props.id },
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_01_01',
    },
  }),
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_PROJECTCOSTMANAGE_BUDGET_COMPONENTS_BUDGETING',
  actions,
};
function addBudget() {
  openEditDrawer(true, {
    type: 'add',
    projectId: props.id,
  });
}
function updatePage() {
  nextTick();
  tableRef.value.reload();
}
</script>
<style scoped lang="less">
:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}

</style>
