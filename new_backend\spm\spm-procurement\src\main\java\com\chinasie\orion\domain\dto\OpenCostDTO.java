package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * OpenCost DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:17:25
 */
@ApiModel(value = "OpenCostDTO对象", description = "开口项费用")
@Data
@ExcelIgnoreUnannotated
public class OpenCostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @ExcelProperty(value = "中心编号 ", index = 0)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 1)
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @ExcelProperty(value = "部门编号 ", index = 2)
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称 ", index = 3)
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 4)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 5)
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 6)
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 7)
    private String userName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 8)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 9)
    private String supplierName;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @ExcelProperty(value = "日期 ", index = 10)
    private Date payDate;

    /**
     * 费用类别编号
     */
    @ApiModelProperty(value = "费用类别编号")
    @ExcelProperty(value = "费用类别编号 ", index = 11)
    private String payTypeNo;

    /**
     * 费用类别名称
     */
    @ApiModelProperty(value = "费用类别名称")
    @ExcelProperty(value = "费用类别名称 ", index = 12)
    private String payTypeName;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @ExcelProperty(value = "费用金额 ", index = 13)
    private BigDecimal payAmt;




}
