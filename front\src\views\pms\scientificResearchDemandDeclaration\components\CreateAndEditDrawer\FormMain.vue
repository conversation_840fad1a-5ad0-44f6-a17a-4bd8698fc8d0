<script setup lang="ts">
import {
  BasicForm, FormSchema, getDict, InputSelectUser, SelectUserModal, useForm, useModal,
} from 'lyra-component-vue3';
import TableRender from './TableRender.vue';
import dayjs from 'dayjs';
import {
  computed,
  h, nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import CheckProjectModal from '../CheckProjectModal/Index.vue';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const props = defineProps<{
  detail: Record<string, any>
}>();

interface UserData {
  id: string,
  name: string
}

const [registerCheckProject, { openModal: openCheckProjectModal }] = useModal();
const [registerSelectUser, { openModal: openSelectUserModal }] = useModal();

const tableRef: Ref = ref();
const deptOptions: Ref<any[]> = ref([]);
const clueId = ref('');
const state = reactive({
  resPerson: '',
  selectPrincipalUser: [],
  principalDeptOptions: [],
});
// 已选择项目id
const selectProjectId: Ref<string> = ref();
// 已选择项目负责人
const selectUser: Ref<UserData> = ref();
const schemas: FormSchema[] = [
  {
    field: 'title',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '基本信息',
        isFormItem: true,
      });
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '申报编码',
    required: true,
    componentProps: {
      disabled: props.detail?.operationType === 'fixed',
      allowClear: false,
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '申报名称',
    required: true,
    componentProps: {
      disabled: props.detail?.operationType === 'fixed',
      allowClear: false,
      onChange() {
        selectProjectId.value = undefined;
      },
      onSearch(value:string, event:Record<string, any>) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },

  {
    field: 'clueId',
    component: 'InputSearch',
    label: '申报线索ID',
    required: true,
    ifShow: false,
    componentProps: {
      style: 'width:100%',
      allowClear: false,
      readOnly: true,
      onSearch(value:string, event:Record<string, any>) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },

  {
    field: 'clueName',
    component: 'InputSearch',
    label: '申报线索',
    required: true,
    componentProps: {
      style: 'width:100%',
      allowClear: false,
      readOnly: true,
      onSearch(value:string, event:Record<string, any>) {
        if (event.type === 'click') {
          openCheckProjectModal(true, {});
        }
      },
    },
  },
  {
    field: 'priority',
    component: 'ApiSelect',
    label: '优先级',
    componentProps: {
      api: async () => await getDict('dict1727595887917682688'),
      labelField: 'description',
    },
  },
  {
    field: 'beginTime',
    component: 'DatePicker',
    label: '申报开始时间',
    required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date:Date) => (formModel.endTime ? dayjs(date).valueOf() >= dayjs(formModel.endTime).valueOf() : false),
    }),
  },
  {
    field: 'endTime',
    component: 'DatePicker',
    label: '期望结束时间',
    required: true,
    componentProps: ({ formModel }) => ({
      disabledDate: (date:Date) => (formModel.beginTime ? dayjs(date).valueOf() <= dayjs(formModel.beginTime).valueOf() : false),
    }),
  },
  {
    field: 'resPerson',
    component: 'InputSearch',
    label: '申报负责人',
    required: true,

    render({ model, field }) {
      return h(InputSelectUser, {
        selectUserData: computed(() => state.selectPrincipalUser),
        onChange(users) {
          state.selectPrincipalUser = users;
          const userId = users?.[0]?.id;
          model[field] = userId ?? '';
          setPrincipalDept(userId);
          // validateFields(['resPerson']);
        },
        selectUserModalProps: {
          selectType: 'radio',
          treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
            {
              orders: [
                {
                  asc: false,
                  column: '',
                },
              ],
              pageNum: 0,
              pageSize: 0,
              query: { status: 1 },
            },
            '',
            'POST',
          ),
        },
      });
    },

    // componentProps: {
    //   placeholder: '请选择',
    //   allowClear: false,
    //   onFocus(e:Record<string, any>) {
    //     e.target.blur();
    //     openSelectUserModal(true);
    //   },
    // },
  },
  {
    field: 'resDept',
    component: 'Select',
    label: '责任部门',
    required: true,
    componentProps: {
      disabled: true,
      options: computed(() => state.principalDeptOptions),
    },
  },
  {
    field: 'declareReason',
    component: 'Input',
    label: '需求申请理由',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'fileInfoDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    render() {
      return h(TableRender, { ref: tableRef });
    },
  },
  {
    field: 'declareBackground',
    component: 'Input',
    label: '申报背景摘要',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'declareTarget',
    component: 'Input',
    label: '申报目标摘要',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'declareTechnology',
    component: 'Input',
    label: '技术方案摘要',
    colProps: {
      span: 24,
    },
  },
];

const [register, { setFieldsValue, validate }] = useForm({
  layout: 'vertical',
  schemas,
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
});

onMounted(() => {
  (props.detail?.id || props.detail?.operationType === 'fixed') && initForm();
});

// 初始化表单
async function initForm() {
  await setFieldsValue(props.detail);
  selectProjectId.value = props.detail?.projectId;
  deptOptions.value = [
    {
      label: props.detail?.rspDeptName,
      value: props.detail?.rspDeptId,
    },
  ];
  selectUser.value = {
    id: props.detail?.resUserId,
    name: props.detail?.resUserName,
  };
  state.principalDeptOptions = [
    {
      value: props.detail.resDept,
      label: props.detail.resDeptName,
      key: props.detail.resDept,
    },
  ];
  state.selectPrincipalUser = [
    {
      id: props.detail.resPerson,
      name: props.detail.resPersonName,
    },
  ];
  await nextTick();
  tableRef.value.setData(props.detail?.materialList);
}

// 选择项目回调
function checkProjectCallback(project:Record<string, string>, dept:Array<Record<string, any>>, user:UserData) {
  clueId.value = project.id;
  setFieldsValue({
    clueName: project.name ?? '',
  });
}
async function setPrincipalDept(principalId?: string) {
  if (!principalId) {
    setFieldsValue({
      resDept: '',
    });
    return;
  }
  const userInfo = await new Api(`/pmi/user/user-profile/${principalId}`).fetch('', '', 'GET');

  state.principalDeptOptions = [userInfo].map((item) => ({
    value: item.orgId,
    label: item.orgName,
    key: item.orgId,
  }));
  setFieldsValue({
    resDept: state.principalDeptOptions?.[0]?.value ?? '',
  });
}
// 选择人员回调
async function selectUserCallback(user:UserData) {
  const result: Record<string, any> = await new Api('/pmi/user/user-profile').fetch('', user[0].id, 'GET');
  deptOptions.value = [
    {
      label: result.orgName,
      value: result.orgId,
    },
  ];
  selectUser.value = {
    id: result.userId,
    name: result.name,
  };

  state.resPerson = result.id;
  await setFieldsValue({
    resPersonName: result.name,
    resDept: result.orgId,
  });
}

defineExpose({
  validate,
  selectUser,
  selectProjectId,
  getTableData: () => tableRef.value.getData(),
  getClueId: () => clueId.value,
});
</script>

<template>
  <BasicForm @register="register" />
  <CheckProjectModal
    :onCheckProjectCallback="checkProjectCallback"
    @register="registerCheckProject"
  />
  <SelectUserModal
    selectType="radio"
    @ok="selectUserCallback"
    @register="registerSelectUser"
  />
</template>

<style scoped lang="less">

</style>
