<template>
  <div class="information">
    <BasicCard title="基本信息">
      <a-row
        :gutter="[20, 10]"
        class="information-row"
      >
        <template
          v-for="item in state.informationField"
          :key="item.label"
        >
          <ACol
            :span="item.span"
            class="task-item"
          >
            <div class="item-title">
              {{ item.label }}：
            </div>

            <!--                :title="formData[item.field]"-->
            <div
              v-if="item.field!=='status'"
              class="item-value flex-te3"
              :title="item.formatter?item.formatter(formData[item.field]):formData[item.field]"
            >
              {{ item.formatter?item.formatter(formData[item.field]):formData[item.field] }}
            </div>
            <div
              v-else
              class="item-value "
            >
              <DataStatusTag
                v-if="formData?.dataStatus"
                :status-data="formData?.dataStatus"
              />
            </div>
          </ACol>
        </template>
      </a-row>
    </BasicCard>
    <BasicCard title="科目信息">
      <Subject
        :formId="formData.id"
        :subjectTree="formData?.subjectTree||[]"
      />
    </BasicCard>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, DataStatusTag,
} from 'lyra-component-vue3';
import {
  Row as ARow,
  Col as ACol,
} from 'ant-design-vue';
import { reactive, inject, watch } from 'vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { Subject } from '../index';

const props = withDefaults(defineProps<{
    periodTypeOptions:any[],
}>(), {
  periodTypeOptions: () => [],
});
interface informationField {
    label:string,
    field:string,
    span:number,
    formatter?:(val:any)=>any
}
const formData = inject('formData', {});
const router = useRouter();
const state = reactive({
  informationField: [
    {
      label: '概算模板名称',
      field: 'name',
      span: 8,
    },
    {
      label: '概算模板说明',
      field: 'remark',
      span: 24,
    },
  ] as informationField[],
});
watch(
  () => formData.value,
  (val) => {
  },
);

function formatterTime(val, type = 'YYYY-MM-DD') {
  return val ? dayjs(val).format(type) : '';
}
// 切换步
</script>
<style lang="less" scoped>
.information{
  height: 100%;
  padding-top: 1px;
  .task-item {
    display: flex;
    line-height: 30px;
    min-height: 30px;
    .item-title {
      padding-right: 5px;
      color: #000000a5;
      width: 103px;
    }
    .item-value {
      flex: 1;
      width: calc(~'100% - 108px');
    }
  }
}
</style>