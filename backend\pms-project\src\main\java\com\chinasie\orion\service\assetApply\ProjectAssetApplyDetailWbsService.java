package com.chinasie.orion.service.assetApply;

import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailWbsDTO;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailWbs;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailWbsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;


/**
 * <p>
 * ProjectAssetApplyDetailWbs 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:30:30
 */
public interface ProjectAssetApplyDetailWbsService  extends OrionBaseService<ProjectAssetApplyDetailWbs> {


//    /**
//     *  详情
//     *
//     * * @param id
//     */
//    ProjectAssetApplyDetailWbsVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectAssetApplyDetailWbsDTO
     */
    String create(ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO)throws Exception;
//
//    /**
//     *  编辑
//     *
//     * * @param projectAssetApplyDetailWbsDTO
//     */
//    Boolean edit(ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO)throws Exception;
//

    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectAssetApplyDetailWbsVO> pages(Page<ProjectAssetApplyDetailWbsDTO> pageRequest)throws Exception;

    /**
     *  分页 - 查询用
     *
     * * @param pageRequest
     *
     */
    Page<ProjectAssetApplyDetailWbsVO> getPages(Page<ProjectAssetApplyDetailWbsDTO> pageRequest)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


//    /**
//     *  下载模板
//     *
//     * * @param response
//     */
//    void downloadExcelTpl(HttpServletResponse response)throws Exception;
//
//    /**
//     *  导入校验
//     *
//     * * @param file
//     */
//    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;
//
//
//    /**
//     *  确认导入
//     *
//     * * @param importId
//     */
//    Boolean importByExcel(String importId)throws Exception;
//
//    /**
//     *  取消导入
//     *
//     * * @param importId
//     */
//    Boolean importCancelByExcel(String importId)throws Exception;
//
//    /**
//     *  导出
//     *
//     * * @param searchConditions
//     * * @param response
//     */
//    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectAssetApplyDetailWbsVO> vos)throws Exception;
}
