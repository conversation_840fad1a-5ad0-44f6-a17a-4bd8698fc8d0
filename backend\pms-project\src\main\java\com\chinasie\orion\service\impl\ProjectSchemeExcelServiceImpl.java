package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.domain.vo.UserDeptVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.bo.SchemeRowWriteHandler;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.conts.ProjectSchemeConts;
import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeExcelDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeReadExcelDTO;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.request.SchemeExportRequest;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.vo.ImportExcelErrorNoteVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.manager.SchemeCommonManager;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ProjectSchemeExcelServiceImpl
 *
 * @author: yangFy
 * @date: 2023/4/24 11:47
 * @description: <p>
 * 项目计划文档操作业务
 * </p>
 */
@Service
@Slf4j
public class ProjectSchemeExcelServiceImpl implements ProjectSchemeExcelService {

    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Resource
    private UserRedisHelper userRedisHelper;

//    @Resource
//    private ClassRedisHelper classRedisHelper;
    @Resource
    private  SchemeCommonManager commonManager;
    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private SchemeCommonManager schemeCommonManager;

//    @Resource
//    private ProjectSchemePrePostService prePostService;

    //    @Resource
//    private ProjectSchemeApplyApprovalService applyApprovalService;
    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Resource
    private ProjectService projectService;

    @Resource
    private OrionJ2CacheService cacheService;

    @Resource
    private ProjectRoleUserService projectRoleUserService;

    @Resource
    private DeptBaseApiService deptBaseApiService;

    @Resource
    private ProjectSchemeContentService projectSchemeContentService;

    private final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 项目计划导入
     *
     * @param projectId
     * @param pid
     * @param excel
     * @return
     * @throws Exception
     */
    @Override
    public ImportExcelCheckResultVO importExcel(String projectId, String pid, MultipartFile excel) throws Exception {
        long startTime = System.currentTimeMillis();
        Assert.notBlank(pid, () -> new BaseException(PMSErrorCode.PMS_ERROR_NON_PROJECT.getErrorCode(), "pid不允许为空,非子级默认为0"));
        Assert.notBlank(projectId, () -> new BaseException(PMSErrorCode.PMS_ERROR_NON_PROJECT.getErrorCode(), "项目计划不允许为空"));
        ImportExcelCheckResultVO resultVO = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SchemeExcelReadListener taskExcelReadListener = new SchemeExcelReadListener();
        EasyExcel.read(inputStream, ProjectSchemeReadExcelDTO.class, taskExcelReadListener).sheet().headRowNumber(2).doRead();
        List<ProjectSchemeReadExcelDTO> schemeExcelDTOS = taskExcelReadListener.getSchemeExcelDTOS();
        if (CollectionUtils.isEmpty(schemeExcelDTOS)) {
            resultVO.setOom("非正确模板，无法解析数据");
            resultVO.setCode(4000);
            return resultVO;
        }
        if (schemeExcelDTOS.size() > 1000) {
            resultVO.setOom("最多导入1000条");
            resultVO.setCode(400);
            return resultVO;
        }

        /*
         * 验证序号是否存在重复
         * */
        String duplicateOrder = schemeExcelDTOS.stream()
                .filter(item -> StrUtil.isNotBlank(item.getOrder()))
                .collect(Collectors.groupingBy(ProjectSchemeReadExcelDTO::getOrder))
                .entrySet().stream()
                .filter(entry -> {
                    List<ProjectSchemeReadExcelDTO> list = entry.getValue();
                    return list != null && list.size() > 1;
                }).map(Map.Entry::getKey)
                .collect(Collectors.joining("、"));
        if (StrUtil.isNotBlank(duplicateOrder)) {
            resultVO.setOom("存在重复的排序号：" + duplicateOrder);
            resultVO.setCode(4000);
            return resultVO;
        }

        String projectNumber = "";
        String projectName = "";
        if (StrUtil.isNotBlank(projectId)) {
            Project project = projectService.getById(projectId);
            if (project != null) {
                projectNumber = project.getNumber();
                projectName = project.getName();
            }
        }

        ProjectScheme pScheme = "0".equals(pid) ? null : projectSchemeService.getById(pid);
        long endTime1 = System.currentTimeMillis();
        log.debug("start1: 一阶段查询任务执行耗时: {} 毫秒", (endTime1 - startTime));
        /*
         * 渲染文档导入的错误结果VO
         */
        //获取项目下的所有用户
        List<SimpleUserVO> userVOs = projectRoleUserService.getSimpleUserByProjectId(projectId);
        Map<String, SimpleUserVO> userVOMap = userVOs.stream().collect(Collectors.toMap(SimpleUserVO::getCode, user -> user));
        List<ImportExcelErrorNoteVO> errorNotes = renderExcelResult(projectId, pScheme, schemeExcelDTOS,userVOMap);

        long endTime2 = System.currentTimeMillis();
        log.debug("start2: 二阶段查询任务执行耗时: {} 毫秒", (endTime2 - endTime1));
        if (CollectionUtils.isEmpty(errorNotes)) {
            List<ProjectSchemeDTO> schemeDTOS = convert(projectId, projectNumber, pid, pScheme, schemeExcelDTOS,userVOs);
            long endTime3 = System.currentTimeMillis();
            log.debug("start3: 三阶段查询任务执行耗时: {} 毫秒", (endTime3 - endTime2));
            String importId = IdUtil.fastSimpleUUID();
            resultVO.setSucc(importId);
            //TODO 整改 提取到公共常量里面去或者配置
            cacheService.set("project_scheme", importId, schemeDTOS, 24 * 60 * 60);
            cacheService.set("project_scheme_pid", importId, pid, 24 * 60 * 60);
        }
        resultVO.setCode(200);
        resultVO.setErr(errorNotes);
        return resultVO;
    }

    /**
     * 渲染文档导入的错误结果VO
     *
     * @param projectId       项目Id
     * @param pScheme         父计划 rxlm04256c6e0d9d429a89084be972fdfa7f
     * @param schemeExcelDTOS 导入数据列表
     */
    private List<ImportExcelErrorNoteVO> renderExcelResult(String projectId, ProjectScheme pScheme, List<ProjectSchemeReadExcelDTO> schemeExcelDTOS,Map<String, SimpleUserVO> userVOMap) throws Exception {
        long startTime = System.currentTimeMillis();
        List<ImportExcelErrorNoteVO> errorNoteVOList = new ArrayList<>();
        Project project = projectService.getById(projectId);
        long endTime4 = System.currentTimeMillis();
        log.debug("start4: 四阶段查询任务执行耗时: {} 毫秒", (endTime4 - startTime));
        Map<String, ProjectSchemeReadExcelDTO> orderToSchemeMap = schemeExcelDTOS.stream()
                .filter(item -> StrUtil.isNotBlank(item.getOrder()))
                .collect(Collectors.toMap(ProjectSchemeReadExcelDTO::getOrder, item -> item));
        long endTime5 = System.currentTimeMillis();
        log.debug("start5: 五阶段查询任务执行耗时: {} 毫秒", (endTime5 - endTime4));
        for (int i = 0; i < schemeExcelDTOS.size(); i++) {
            ProjectSchemeReadExcelDTO excelDTO = schemeExcelDTOS.get(i);
            boolean dateFormatFlat = false;
            List<String> errorNotes = new ArrayList<>();

            String order = excelDTO.getOrder();
            if (StrUtil.isBlank(order)) {
                errorNotes.add("序号不允许为空");
            } else {
                if (order.contains(".")) {
                    if (order.endsWith(".")) {
                        errorNotes.add("序号格式不正确");
                    } else {
                        String parentOrder = order.substring(0, order.lastIndexOf("."));
                        if (!orderToSchemeMap.containsKey(parentOrder)) {
                            errorNotes.add("序号对应的父级序号不存在");
                        }
                    }
                }
            }

            if (StrUtil.isBlank(excelDTO.getName()) || excelDTO.getName().length() > 100) {
                errorNotes.add("任务名称不允许为空或超过100个字符");
            }
            if (StrUtil.isBlank(excelDTO.getRspUserCode()) || !userVOMap.containsKey(excelDTO.getRspUserCode())) {
                errorNotes.add("责任人为空或非项目成员");
            }
            if (StrUtil.isBlank(excelDTO.getBeginTimeStr()) || StrUtil.isBlank(excelDTO.getEndTimeStr())) {
                errorNotes.add("计划起止时间不允许为空");
            }
            if (StrUtil.isNotBlank(excelDTO.getBeginTimeStr())) {
                if (!dateFormat(excelDTO.getBeginTimeStr())) {
                    errorNotes.add("计划开始时间日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setBeginTime(DateUtil.parse(excelDTO.getBeginTimeStr(), DATE_FORMAT));
                }
            }
            if (StrUtil.isNotBlank(excelDTO.getEndTimeStr())) {
                if (!dateFormat(excelDTO.getEndTimeStr())) {
                    errorNotes.add("计划结束时间日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setEndTime(DateUtil.parse(excelDTO.getEndTimeStr(), DATE_FORMAT));
                    dateFormatFlat = true;
                }
            }
            if (StrUtil.isNotBlank(excelDTO.getActualBeginTimeStr())) {
                if (!dateFormat(excelDTO.getActualBeginTimeStr())) {
                    errorNotes.add("计划实际开始时间日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setActualBeginTime(DateUtil.parse(excelDTO.getActualBeginTimeStr(), DATE_FORMAT));
                }
            }
            if (StrUtil.isNotBlank(excelDTO.getActualEndTimeStr())) {
                if (!dateFormat(excelDTO.getActualEndTimeStr())) {
                    errorNotes.add("计划实际结束时间日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setActualEndTime(DateUtil.parse(excelDTO.getActualEndTimeStr(), DATE_FORMAT));
                }
            }
            if (StrUtil.isNotBlank(excelDTO.getIssueTimeStr())) {
                if (!dateFormat(excelDTO.getIssueTimeStr())) {
                    errorNotes.add("计划下发时间日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setIssueTime(DateUtil.parse(excelDTO.getIssueTimeStr()));
                }
            }
            if (StrUtil.isBlank(excelDTO.getSchemeType())) {
                errorNotes.add("任务类型为空");
            }
            if (Objects.nonNull(pScheme) && dateFormatFlat) {
                Date pEndTime = DateUtil.offsetHour(pScheme.getEndTime(), 24);
                boolean isStartOut = DateUtil.isIn(excelDTO.getBeginTime(), pScheme.getBeginTime(), pEndTime);
                boolean isEndOut = DateUtil.isIn(excelDTO.getBeginTime(), pScheme.getBeginTime(), pEndTime);
                if (!isStartOut) {
                    errorNotes.add("开始时间超过父级计划起止时间");
                }
                if (!isEndOut) {
                    errorNotes.add("结束时间超过父级计划起止时间");
                }
                if (excelDTO.getBeginTime().after(DateUtil.offsetHour(excelDTO.getEndTime(), 24))) {
                    errorNotes.add("开始时间在结束时间之后");
                }
            }

            if (Objects.nonNull(project) && dateFormatFlat) {
                if (Objects.nonNull(project.getProjectEndTime())) {
                    if (excelDTO.getEndTime().after(project.getProjectEndTime())) {
                        errorNotes.add("计划结束时间超过项目结束时间");
                    }
                }
            }
            if (!CollectionUtils.isEmpty(errorNotes)) {
                ImportExcelErrorNoteVO errorNoteVO = new ImportExcelErrorNoteVO();
                errorNoteVO.setOrder(String.valueOf(i + 3));
                errorNoteVO.setErrorNotes(errorNotes);
                errorNoteVOList.add(errorNoteVO);
            }
        }
        long endTime6 = System.currentTimeMillis();
        log.debug("start6: 六阶段查询任务执行耗时: {} 毫秒", (endTime6 - endTime5));
        return errorNoteVOList;
    }

    private boolean matchRspSubDept(String rspSubDeptCode, String rspUserCode, Map<String, UserVO> userVOMap) {
        UserVO userVO = userVOMap.get(rspUserCode);
        if (Objects.isNull(userVO)) {
            return false;
        }
        List<DeptVO> deptVOList = userVO.getOrganizations();
        if (CollUtil.isEmpty(deptVOList)) {
            return false;
        }
        return deptVOList.stream().map(DeptVO::getDeptCode).anyMatch(item -> item.equals(rspSubDeptCode));
    }

    private boolean dateFormat(String str) {
        try {
            DateUtil.parseLocalDateTime(str, DATE_FORMAT);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    private List<ProjectSchemeDTO> convert(String projectId, String projectNumber, String pid, ProjectScheme pScheme, List<ProjectSchemeReadExcelDTO> schemeExcelDTOS, List<SimpleUserVO> userVOs) {
        List<ProjectSchemeDTO> projectSchemeDTOList = new ArrayList<>();
        /**
         * 业务逻辑
         * 1. 生成编码
         */
        /**
         * 获取人员、科室、部门、类 等信息,
         */
        long endTim11 = System.currentTimeMillis();
        Map<String, String> userMap = Optional.ofNullable(userVOs)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getCode, (v1, v2) -> v2));
//        Map<String, String> deptMap = Optional.ofNullable(deptRedisHelper.listAllDept(CurrentUserHelper.getOrgId()))
//                .orElse(new ArrayList<>())
//                .stream()
//                .collect(Collectors.toMap(DeptVO::getId, DeptVO::getDeptCode, (v1, v2) -> v2));
        long endTime12 = System.currentTimeMillis();
        log.debug("start12: 十二阶段查询任务执行耗时: {} 毫秒", (endTime12 - endTim11));
        /*
         * 根据排序字段映射ID
         * */
        Map<String, String> orderToIdMap = schemeExcelDTOS.stream()
                .collect(Collectors.toMap(ProjectSchemeReadExcelDTO::getOrder, entity -> classRedisHelper.getUUID(ProjectScheme.class.getSimpleName())));

        /*
         * 根据排序映射层级
         * 1、未选择父级计划（即：pid=0），层级根据序号处理即可
         * 2、选择了父级计划，层级需要根据父级的层级累加
         * */
        Map<String, Integer> orderToLevelMap = schemeExcelDTOS.stream()
                .collect(Collectors.toMap(ProjectSchemeReadExcelDTO::getOrder,
                        entity -> getSchemeLevel(entity.getOrder(), '.', Optional.ofNullable(pScheme).orElse(new ProjectScheme()).getLevel())));
        /*
         * 根据排序号映射父级链
         * */

        long endTime13 = System.currentTimeMillis();
        log.debug("start13: 十三阶段查询任务执行耗时: {} 毫秒", (endTime13 - endTime12));
        List<String> userIds = userVOs.stream().map(SimpleUserVO::getId).collect(Collectors.toList());
        Map<String, List<UserDeptVO>> userDeptMap  = deptBaseApiService.getDeptByUserId(userIds);
        for (ProjectSchemeReadExcelDTO excelDTO : schemeExcelDTOS) {
            ProjectSchemeDTO projectSchemeDTO = new ProjectSchemeDTO();

            String order = excelDTO.getOrder();

            projectSchemeDTO.setId(orderToIdMap.get(order));
            projectSchemeDTO.setName(excelDTO.getName());

            // 存在“.”，表明该行数据不是第一级
            String parentOrder = getParentOrder(order);
            if ("0".equals(parentOrder)) {
                projectSchemeDTO.setParentId(pid);
            } else {
                projectSchemeDTO.setParentId(orderToIdMap.get(parentOrder));
            }

            projectSchemeDTO.setProjectNumber(projectNumber);
            projectSchemeDTO.setStatus(Status.PENDING.getCode());
            projectSchemeDTO.setType(getProjectSchemeType(excelDTO.getSchemeType()).getCode());
            projectSchemeDTO.setNodeType(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_NAME_MAP.get(excelDTO.getSchemeType()));
            projectSchemeDTO.setProjectId(projectId);
            projectSchemeDTO.setBeginTime(excelDTO.getBeginTime());
            projectSchemeDTO.setEndTime(excelDTO.getEndTime());
            projectSchemeDTO.setActualBeginTime(excelDTO.getActualBeginTime());
            projectSchemeDTO.setActualEndTime(excelDTO.getActualEndTime());
            projectSchemeDTO.setSchemeDesc(excelDTO.getSchemeDesc());
            projectSchemeDTO.setIssueTime(excelDTO.getIssueTime());
            String rspUser = userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(excelDTO.getRspUserCode());
            projectSchemeDTO.setRspUser(rspUser);
            projectSchemeDTO.setCircumstance(0);
            projectSchemeDTO.setStatus(buildStatus(excelDTO));
            projectSchemeDTO.setLevel(orderToLevelMap.getOrDefault(order, Status.LEVEL_1.getCode()));
            projectSchemeDTO.setRspUserCode(excelDTO.getRspUserCode());
            if(CollUtil.isNotEmpty(userDeptMap.get(rspUser))) {
                setOrgInfo(projectSchemeDTO, userDeptMap.get(rspUser).get(0));
            }
            projectSchemeDTOList.add(projectSchemeDTO);
        }
        long endTime14 = System.currentTimeMillis();
        log.debug("start14: 十四阶段查询任务执行耗时: {} 毫秒", (endTime14 - endTime13));
        return projectSchemeDTOList;
    }


    private void setOrgInfo(ProjectSchemeDTO projectSchemeDTO,  UserDeptVO userDeptVO) {

        if (ObjectUtil.isEmpty(userDeptVO)) {
            return;
        }
        DeptVO deptVO = deptRedisHelper.getDeptById(userDeptVO.getDeptId());
        if ("20".equals(deptVO.getType())) {
            projectSchemeDTO.setRspSubDept(deptVO.getId());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = getParentOrgId(deptVO);
            projectSchemeDTO.setRspSubDept(deptOrg.getId());
            projectSchemeDTO.setRspSectionId(deptVO.getId());
        } else if ("40".equals(deptVO.getType())) {
            DeptVO sectionOrg = getParentOrgId(deptVO);
            DeptVO deptOrg = getParentOrgId(sectionOrg);
            projectSchemeDTO.setRspSubDept(deptOrg.getId());
            projectSchemeDTO.setRspSectionId(sectionOrg.getId());
        }

    }

    private DeptVO getParentOrgId(DeptVO org) {
        String parentId = org.getParentId();
        DeptVO organization = deptRedisHelper.getDeptById(parentId);
        return Objects.nonNull(organization) ? organization : new DeptVO();
    }

    private Integer buildStatus(ProjectSchemeReadExcelDTO excelDTO) {
        Date actualBeginTime = excelDTO.getActualBeginTime();
        Date actualEndTime = excelDTO.getActualEndTime();
        Date issueTime = excelDTO.getIssueTime();
        if (Objects.nonNull(actualBeginTime) && Objects.nonNull(actualEndTime) && Objects.nonNull(issueTime)) {
            return Status.FINISHED.getCode();
        }
        if (Objects.nonNull(issueTime)) {
            return Status.PUBLISHED.getCode();
        }
        return Status.PENDING.getCode();
    }

    private Status getProjectSchemeType(String schemeType) {
        if (Status.ICON_MILESTONE.getName().equals(schemeType)) {
            return Status.ICON_MILESTONE;
        }
        return Status.ICON_SCHEME;

    }

    @Override
    public boolean importByExcelVerify(String importId) throws Exception {
        List<ProjectSchemeDTO> schemeDTOS = (List<ProjectSchemeDTO>) cacheService.get("project_scheme", importId);
        //TODO 整改 提取到公共常量里面去或者配置
        String pid = (String) cacheService.get("project_scheme_pid", importId);
        projectSchemeService.importByExcelVerify(pid, schemeDTOS);
        return true;
    }

    @Override
    public boolean importByExcelCancel(String importId) {
        //TODO 整改 提取到公共常量里面去或者配置
        cacheService.delete("project_scheme", importId);
        cacheService.delete("project_scheme_id", importId);
        return true;
    }

    /**
     * 项目计划导出
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @Override
    public void exportExcel(SchemeExportRequest request, HttpServletResponse response) throws Exception {
        Project project = projectService.getById(request.getProjectId());
        if (null == project) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PROJECT_NULL);
        }
        String fileName = String.format("%s项目计划.xlsx", project.getName());
        List<ProjectScheme> schemes = exportScheme(request);
        if (CollectionUtils.isEmpty(schemes)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "未查询到项目计划");
        }

        /**
         * 判断是否存在前置关系
         */
        List<ProjectSchemeVO> projectSchemeVOS = BeanCopyUtils.convertListTo(schemes, ProjectSchemeVO::new);
        //赋值转化
        schemeCommonManager.codeMapping(projectSchemeVOS);
        Map<String, ProjectSchemeVO> schemeVOMap = projectSchemeVOS.stream()
                .collect(Collectors.toMap(ProjectSchemeVO::getId, Function.identity()));
        List<String> schemeIds= schemes.stream().map(ObjectEntity::getId).distinct().collect(Collectors.toList());
        Map<String,String> schemeIdToProgress = projectSchemeContentService.getMapBySchemeIds(schemeIds);
        List<ProjectSchemeExcelDTO> planExcelDTOS = schemes.stream().map(scheme -> {
            ProjectSchemeVO projectSchemeVO = schemeVOMap.get(scheme.getId());
            projectSchemeVO.setChildren(new ArrayList<>());
            ProjectSchemeExcelDTO excelDTO = BeanCopyUtils.convertTo(projectSchemeVO, ProjectSchemeExcelDTO::new);
            excelDTO.setActualBeginTime(scheme.getActualBeginTime());
            excelDTO.setLevelName(projectSchemeVO.getLevel() + "级");
            excelDTO.setSchemeType(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.getOrDefault(projectSchemeVO.getNodeType(), ""));
            if(Objects.nonNull(projectSchemeVO.getDataStatus())){ // 状态不为空时，获取在状态
                excelDTO.setStatusName(projectSchemeVO.getDataStatus().getName());
            }
            excelDTO.setParticipantNames(projectSchemeVO.getParticipantUserCodeNames());
            excelDTO.setOrder(scheme.getSort());
            if(StrUtil.isEmpty(projectSchemeVO.getParentId())){
                excelDTO.setParentId("0");
            }
            excelDTO.setIssueTime(projectSchemeVO.getIssueTime());
            excelDTO.setSchemeDesc(projectSchemeVO.getRemark());
            excelDTO.setSchemeProgressRecord(schemeIdToProgress.getOrDefault(scheme.getId(),""));
            return excelDTO;
        }).collect(Collectors.toList());
        // 先根据sort排序
        planExcelDTOS.sort(Comparator.comparingLong(ProjectSchemeExcelDTO::getOrder));
        // 再根据parentId排序，但是不能打乱sort排序
        planExcelDTOS = sortAndAssignOrder(planExcelDTOS, ProjectSchemeExcelDTO::getParentId, ProjectSchemeExcelDTO::getId, ProjectSchemeExcelDTO::setSort);
        planExcelDTOS.forEach(o -> o.setSchemeNumber(o.getProjectNumber() + "-" + o.getSort().replace(".", "-")));

        /**
         * 导出
         * */
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        EasyExcel.write(response.getOutputStream())
                .head(ProjectSchemeExcelDTO.class)
                .registerWriteHandler(new SchemeRowWriteHandler())
                .sheet("计划")
                .doWrite(planExcelDTOS);
    }

    private List<ProjectScheme> exportScheme(SchemeExportRequest request) throws Exception {
        // 这里直接获取当前项目下的所有项目计划  -- 单项目计划最多几千条 为了避免redis 转换问题（状态，人员等）
        List<ProjectScheme> projectSchemeList = commonManager.listByProjectId(request.getProjectId(),request.getType());
        if (CollUtil.isNotEmpty(request.getProjectSchemeIds())) {
            return   projectSchemeList.stream().filter(scheme -> request.getProjectSchemeIds().contains(scheme.getId())).collect(Collectors.toList());
        }else{
            List<List<SearchCondition>> searchConditions = request.getSearchConditions();
            List<ProjectScheme> projectSchemes = commonManager.filterateByUser(projectSchemeList,request.getType());
            Boolean isManager = projectRoleUserService.isPmRoleUser(request.getProjectId());
            List<OrderItem> orderItems = request.getOrders();
            List<ProjectScheme> schemeList11 = commonManager.listProjectSchemeByProjectId(request.getProjectId(), request.getSearchConditions(), isManager, orderItems);
            if (CollUtil.isEmpty(schemeList11)) {
                return new ArrayList<>();
            }
            Map<String,ProjectScheme> projectSchemeMap = projectSchemes.stream().collect(Collectors.toMap(ObjectEntity::getId, Function.identity()));
            List<ProjectScheme> schemeList =new ArrayList<>();
            schemeList11.forEach(item->{
                ProjectScheme projectScheme =  projectSchemeMap.get(item.getId());
                if(!Objects.isNull(projectScheme)){
                    schemeList.add(projectScheme);
                }
            });
            if(CollectionUtils.isEmpty(schemeList)){
                return new ArrayList<>();
            }
            List<ProjectScheme> viewList = new ArrayList<>();
            if (request.getTypeEnum() == SchemeListTypeEnum.PROJECT_SCHEME) {
                List<ProjectScheme> list = new ArrayList<>();
                if (!CollectionUtils.isEmpty(searchConditions)) {
                    viewList = commonManager.filterView(schemeList, request.getTypeEnum());
                    if(!CollectionUtils.isEmpty(viewList)){
                        list.addAll(viewList);
                    }
                }else{
                    list.addAll(schemeList);
                }
                if(!CollectionUtils.isEmpty(list)){
                    viewList = commonManager.getList(list, projectSchemes);
                }
            } else {
                viewList = commonManager.filterView(schemeList, request.getTypeEnum());
            }
            if (CollUtil.isEmpty(viewList)) {
                return new ArrayList<>();
            }
            return  viewList;
        }
    }


    public static class SchemeExcelReadListener implements ReadListener<ProjectSchemeReadExcelDTO> {
        private final List<ProjectSchemeReadExcelDTO> schemeExcelDTOS = new ArrayList<>();


        public List<ProjectSchemeReadExcelDTO> getSchemeExcelDTOS() {
            return schemeExcelDTOS;
        }

        @Override
        public void invoke(ProjectSchemeReadExcelDTO data, AnalysisContext context) {
            schemeExcelDTOS.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }

        @Override
        public void extra(CellExtra extra, AnalysisContext context) {
        }
    }


    /**
     * @param date1
     * @param date2
     * @param date3
     * @param date4
     * @return
     */
    public static boolean betweenOn(Date date1, Date date2, Date date3, Date date4) {
        if (date1 == null || date2 == null || date3 == null || date4 == null) {
            return false;
        }
        return (date1.getTime() - date3.getTime()) > 0 && ((date4.getTime() - date2.getTime()) > 0);
    }

    /**
     * 获取计划的层级
     *
     * @param order       字符串
     * @param target      指定字符
     * @param parentLevel 父级层级
     * @return 次数
     */
    public static Integer getSchemeLevel(String order, char target, Integer parentLevel) {

        // 序号不存在指定字符，表明为第一层级
        if (!order.contains(String.valueOf(target))) {
            return parentLevel == null ? Status.LEVEL_1.getCode() : parentLevel + 1;
        }

        int length = order.length();

        int count = 0;
        for (int i = 0; i < length; i++) {
            if (order.charAt(i) == target) {
                count++;
            }
        }
        // 父级层级为空，直接加统计指定字符出现次数即为当前数据的层级
        if (parentLevel == null) {
            return Status.LEVEL_1.getCode() + count;
        }

        // 父级层级不为空，需在统计指定字符出现次数的基础上加1表示为当前数据的层级数
        return Status.LEVEL_1.getCode() + count + 1;
    }

    /**
     * 根据序号获取父级序号
     *
     * @param order 序号
     * @return 父级序号
     */
    public static String getParentOrder(String order) {
        if (order.contains(".")) {
            return order.substring(0, order.lastIndexOf("."));
        }

        return "0";
    }

    private String buildSort(ProjectScheme projectScheme, List<ProjectScheme> projectSchemeList) {
        List<String> sortList = CollUtil.toList();
        addSortLink(projectScheme, projectSchemeList, sortList);
        StringBuilder sb = new StringBuilder("");
        if (CollUtil.isNotEmpty(sortList)) {
            for (int i = sortList.size() - 1; i > -1; i--) {
                sb.append(sortList.get(i)).append(".");
            }
            return sb.substring(0, sb.toString().lastIndexOf("."));
        }
        return sb.toString();
    }

    private void addSortLink(ProjectScheme projectScheme, List<ProjectScheme> projectSchemeList, List<String> sortList) {
        if (StrUtil.isNotBlank(projectScheme.getParentId()) && !ProjectSchemeConts.ROOT_ID.equals(projectScheme.getParentId())) {
            sortList.add(String.valueOf(projectScheme.getSort()));
            ProjectScheme pScheme = projectSchemeList.stream().filter(item -> item.getId().equals(projectScheme.getParentId())).findFirst().orElse(new ProjectScheme());
            addSortLink(pScheme, projectSchemeList, sortList);
        } else {
            sortList.add(String.valueOf(projectScheme.getSort()));
        }
    }

    private Map<String, String> initSortMap(String projectId) throws Exception {
        Map<String, String> sortMap = MapUtil.newHashMap();
        List<ProjectScheme> projectSchemeList = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, projectId));
        if (CollUtil.isEmpty(projectSchemeList)) {
            return sortMap;
        }
        projectSchemeList.forEach(item -> sortMap.put(item.getId(), buildSort(item, projectSchemeList)));
        return sortMap;
    }


    public static <T> List<T> sortAndAssignOrder(List<T> items, Function<T, String> getParentId, Function<T, String> getId, BiConsumer<T, String> setOrder) {
        Map<String, List<T>> parentChildMap = new HashMap<>();
        for (T item : items) {
            parentChildMap.computeIfAbsent(getParentId.apply(item), k -> new ArrayList<>()).add(item);
        }

        List<T> sortedItems = new ArrayList<>();
        assignOrderAndFlatten(parentChildMap, "0", "", sortedItems, getId, setOrder);
        return sortedItems;
    }

    private static <T> void assignOrderAndFlatten(Map<String, List<T>> parentChildMap, String parentId, String prefix, List<T> result, Function<T, String> getId, BiConsumer<T, String> setOrder) {
        List<T> children = parentChildMap.get(parentId);
        if (children == null) {
            return;
        }

        for (int i = 0; i < children.size(); i++) {
            T child = children.get(i);
            String order = prefix.isEmpty() ? Integer.toString(i + 1) : prefix + "." + (i + 1);
            setOrder.accept(child, order);
            result.add(child);
            assignOrderAndFlatten(parentChildMap, getId.apply(child), order, result, getId, setOrder);
        }
    }


}

