package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolInsDTO;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.dto.IfToParameterToInsDTO;
import com.chinasie.orion.domain.vo.IfToParameterToInsVO;

import com.chinasie.orion.service.IfToParameterToInsService;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * IfToParameterToIns 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@RestController
@RequestMapping("/ifToParameterToIns")
@Api(tags = "意见单和参数和参数实列的关系")
public class IfToParameterToInsController {

    @Autowired
    private IfToParameterToInsService ifToParameterToInsService;


    /**
     * 批量新增
     *
     * @param imToParamBatchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增参数")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】批量新增/编辑【意见单和参数和参数实列的关系】",
            type = "ImOrIfToParam",
            subType = "批量新增/编辑",
            bizNo = ""
    )
    public ResponseDTO<Boolean> batchCreateOrUpdate(@RequestBody ImOrIfToParamBatchDTO imToParamBatchDTO) throws Exception {
        return new ResponseDTO<>(ifToParameterToInsService.batchCreateOrUpdate(imToParamBatchDTO));
    }

    /**
     * 编辑
     *
     * @param parameterPoolInsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "填值")
    @RequestMapping(value = "/add/value", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑【意见单和参数和参数实列的关系填值】，业务编号：{#parameterPoolInsDTO.id}",
            type = "ParameterPoolIns",
            subType = "填值",
            bizNo = "{#parameterPoolInsDTO.id}"
    )
    public ResponseDTO<Boolean> addValue(@Validated @RequestBody ParameterPoolInsDTO parameterPoolInsDTO) throws Exception {
        Boolean rsp = ifToParameterToInsService.addValue(parameterPoolInsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除【意见单和参数和参数实列的关系】，业务编号：{ID_LIST{#ids}}",
            type = "ParameterPoolIns",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ifToParameterToInsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     * @param ifToParameterToInsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/data/list", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行【意见单和参数和参数实列的关系】列表查询",
            type = "ParameterPoolIns",
            subType = "列表查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<List<IfToParameterToInsVO>> detailList(@RequestBody IfToParameterToInsDTO ifToParameterToInsDTO) throws Exception {
        List<IfToParameterToInsVO> rsp =  ifToParameterToInsService.detailList(ifToParameterToInsDTO);
        return new ResponseDTO<>(rsp);
    }
}
