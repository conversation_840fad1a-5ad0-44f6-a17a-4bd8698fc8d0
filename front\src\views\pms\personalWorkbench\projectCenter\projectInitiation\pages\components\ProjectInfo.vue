<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import GridFileList from './GridFileList.vue';
import {
  computed, inject, ref, Ref,
} from 'vue';
import { declarationData } from '../keys';
import { OrionTable } from 'lyra-component-vue3';
const tableRef = ref();
// 立项详情数据
const data = inject(declarationData);
// 立项基本信息
const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '立项编号',
    field: 'number',
  },
  {
    label: '项目编号',
    field: 'projectNumber',
  },
  {
    label: '项目名称',
    field: 'projectName',
  },
  {
    label: '项目预估金额',
    field: 'estimateAmt',
    isMoney: true,
    unit: '元',
  },
  {
    label: '项目类型',
    field: 'projectTypeName',
  },
  {
    label: '发起日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '项目来源',
    field: 'projectSourceName',
  },
  {
    label: '项目开始日期',
    field: 'projectStartTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目结束日期',
    field: 'projectEndTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目负责人',
    field: 'resPersonName',
  },
  {
    label: '责任部门',
    field: 'resDeptName',
  },
  {
    label: '立项理由',
    field: 'approvalReason',
    // gridColumn: '1/5',
  },
]);

// 立项详细信息;
const projectDetailInfo: Ref<any[]> = ref([
  {
    label: '项目立项支持性材料',
    field: '',
    class: 'flex-ver',
    labelBold: true,
    slotName: 'fileTable',
  },
]);
const fileList: Ref<Array<{
  id: string,
  name: string,
  filePostfix: string,
  createUserName: string,
  createTime: string,
}>> = computed(() => data.value?.materialList && data.value.materialList.map((item) => ({
  ...item,
  fileName: `${item.name}.${item.filePostfix}`,
  filePostfix: `.${item.filePostfix}`,
})));
</script>

<template>
  <DetailsLayout
    border-bottom
    title="立项基本信息"
    :data-source="data"
    :list="projectBasicInfo"
  />
  <DetailsLayout
    border-bottom
    title="立项详细信息"
    :column="1"
    :data-source="data"
    :list="projectDetailInfo"
  >
    <template #fileTable>
      <GridFileList
        :column="1"
        :list="fileList"
      />
    </template>
  </DetailsLayout>
</template>

<style scoped lang="less">

</style>
