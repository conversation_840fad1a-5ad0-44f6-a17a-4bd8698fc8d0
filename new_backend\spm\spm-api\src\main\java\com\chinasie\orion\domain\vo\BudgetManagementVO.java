package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

import java.util.List;

/**
 * BudgetManagement VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@ApiModel(value = "BudgetManagementVO对象", description = "预算管理表")
@Data
public class BudgetManagementVO extends ObjectVO implements Serializable {

    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    private String expenseSubjectNumber;


    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String expenseSubjectName;


    /**
     * 期间类型
     */
    @ApiModelProperty(value = "期间类型")
    private String timeType;

    @ApiModelProperty(value = "期间类型名称")
    private String timeTypeName;


    /**
     * 预算期间
     */
    @ApiModelProperty(value = "预算期间")
    private String budgetTime;


    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型")
    private String budgetObjectType;

    @ApiModelProperty(value = "预算对象类型名称")
    private String budgetObjectTypeName;


    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    private String budgetObjectId;


    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    private String budgetObjectName;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;



    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算申请总金额")
    private BigDecimal budgetMoney;


    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    private BigDecimal januaryMoney;


    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    private BigDecimal februaryMoney;


    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    private BigDecimal marchMoney;


    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    private BigDecimal aprilMoney;


    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    private BigDecimal mayMoney;


    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    private BigDecimal juneMoney;


    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    private BigDecimal julyMoney;


    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    private BigDecimal augustMoney;


    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    private BigDecimal septemberMoney;


    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    private BigDecimal octoberMoney;


    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    private BigDecimal novemberMoney;


    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    private BigDecimal decemberMoney;


    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    private BigDecimal firstQuarterMoney;


    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    private BigDecimal secondQuarter;


    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    private BigDecimal thirdQuarter;


    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    private BigDecimal fourthQuarter;


    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    private String name;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 预算剩余金额
     */
    @ApiModelProperty(value = "预算剩余金额")
    private BigDecimal residueMoney;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * 年度预算金额
     */
    @ApiModelProperty(value = "年度预算金额")
    private BigDecimal yearMoney;

    /**
     * 总成本
     */
    @ApiModelProperty(value = "总成本")
    private BigDecimal expendTotal;

    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    private BigDecimal remainder;
}
