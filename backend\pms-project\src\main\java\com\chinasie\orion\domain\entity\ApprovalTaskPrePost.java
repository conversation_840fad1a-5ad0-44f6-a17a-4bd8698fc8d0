package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ApprovalTaskPrePost Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
@TableName(value = "pmsx_approval_task_pre_post")
@ApiModel(value = "ApprovalTaskPrePostEntity对象", description = "项目立项任务前后置关系表")
@Data

public class ApprovalTaskPrePost extends  ObjectEntity  implements Serializable{

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    @TableField(value = "approval_id")
    private String approvalId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @TableField(value = "type")
    private Integer type;

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @TableField(value = "pre_task_id")
    private String preTaskId;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @TableField(value = "post_task_id")
    private String postTaskId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

}
