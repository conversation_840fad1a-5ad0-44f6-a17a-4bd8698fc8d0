<template>
  <span class="status-wrap">
    <i
      v-if="itemData"
      :style="{ background: itemData.bgColor, color: itemData.color }"
    >{{
      itemData.name
    }}</i>
  </span>
</template>

<script>
import { defineComponent, computed } from 'vue';
import { PROJECT_STATUS } from '../enums/projectEnums';
export default defineComponent({
  name: 'ProjectStatusTag',
  props: {
    status: {
      type: [String, Number],
      default: -1,
    },
  },
  setup(props) {
    return {
      itemData: computed(() => {
        for (const item of PROJECT_STATUS) {
          if (item.key === props.status) {
            return item;
          }
        }

        return {
          bgColor: 'none',
          name: '未定义',
        };
      }),
    };
  },
});
</script>

<style scopeds lang="less">
  .status-wrap {
    > i {
      min-width: 62px;
      text-align: center;
      display: inline-block;
      height: 24px;
      line-height: 24px;
      border-radius: ~`getPrefixVar('border-radius-base')`;
      padding: 0 6px;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
  }
</style>
