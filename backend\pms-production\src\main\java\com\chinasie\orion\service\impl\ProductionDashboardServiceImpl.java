package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.EnvCountDTO;
import com.chinasie.orion.domain.dto.OverhaulDetailDTO;
import com.chinasie.orion.domain.dto.ProductionIndexDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.entity.ProductionIndex;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProductionDashboardMapper;
import com.chinasie.orion.repository.ProductionIndexMapper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.service.ProductionDashboardService;
import com.chinasie.orion.service.ProductionIndexService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * ProductionIndex 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@Service
@Slf4j
public class ProductionDashboardServiceImpl extends OrionBaseServiceImpl<ProductionDashboardMapper, KeySafetyVO> implements ProductionDashboardService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private PersonMangeService personMangeService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private MajorRepairPlanService majorRepairPlanService;


    /**
     * 安质环关键指标
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<KeySafetyVO> getKeySafety(EnvCountDTO envCountDTO) {
        StringBuilder stringBuilder = new StringBuilder();

        String locationCode =envCountDTO.getLocationCode();
        stringBuilder.append("SELECT\n" +
                "\t'A类违章数量' as label,\n" +
                "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                "\tSUM(CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                "\tSUM(CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                "FROM\n" +
                "\tpms_amperering_event_check_data_info \n" +
                "where \n" +
                "\tassessment_level = 'pms_class_a_violation'\n" );

        if(StringUtils.hasText(locationCode)){
            stringBuilder.append(  "\tand base_code like '%" + locationCode + "%'\n");
        }
        stringBuilder.append(
                "union all\n" +
                        "SELECT\n" +
                        "\t'B类违章数量' as label,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                        "FROM\n" +
                        "\tpms_amperering_event_check_data_info \n" +
                        "where \n" +
                        "\tassessment_level = 'pms_class_b_violation'\n"
        );

        if(StringUtils.hasText(locationCode)){
            stringBuilder.append(  "\tand base_code like '%" + locationCode + "%'\n" );
        }


        stringBuilder.append(
                "union all\n" +
                        "SELECT\n" +
                        "\t'C类违章数量' as label,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                        "FROM\n" +
                        "\tpms_amperering_event_check_data_info \n" +
                        "where \n" +
                        "\tassessment_level = 'pms_class_c_violation'\n"
        );

        if(StringUtils.hasText(locationCode)){
            stringBuilder.append( "\tand base_code like '%" + locationCode + "%'\n");
        }
        stringBuilder.append(
                "union all\n" +
                        "SELECT\n" +
                        "\t'F1事件数量' as label,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                        "FROM\n" +
                        "\tpms_amperering_event_check_data_info \n" +
                        "where \n" +
                        "\tassessment_level = 'pms_f1_defect'\n"
        );
        if(StringUtils.hasText(locationCode)){
            stringBuilder.append("\tand base_code like '%" + locationCode + "%'\n");
        }

        stringBuilder.append(
                "union all\n" +
                        "SELECT\n" +
                        "\t'F2事件数量' as label,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                        "\tSUM(CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                        "FROM\n" +
                        "\tpms_amperering_event_check_data_info \n" +
                        "where \n" +
                        "\tassessment_level = 'pms_f2_defect'\n"
        );

        if(StringUtils.hasText(locationCode)){
            stringBuilder.append("\tand base_code like '%" + locationCode + "%'\n");
        }


        stringBuilder.append(
                "union all\n" +
                        "SELECT\n" +
                        "\t'指标事件' as label,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_external_quality_env_supervision', 'pms_interface_dept_check' ) THEN 1 ELSE 0 END ) AS outNum,\n" +
                        "\tsum( CASE WHEN pyramid_category IN ( 'pms_project_dept_check', 'pms_spec_ctr_self_check' ) THEN 1 ELSE 0 END ) AS inNum,\n" +
                        "\tSUM( CASE WHEN YEAR(event_date) = (YEAR(CURDATE()) - 1) THEN 1 ELSE 0 END) as lastNum,\n" +
                        "\tSUM( CASE WHEN YEAR(event_date) = YEAR(CURDATE()) THEN 1 ELSE 0 END) as num\n" +
                        "FROM\n" +
                        "\tpms_amperering_event_check_data_info \n" +
                        "where \n" +
                        "\tassessment_level like 'pms_index_event%'\n"
        );
        if(StringUtils.hasText(locationCode)){
            stringBuilder.append( "\tand base_code like '%" + locationCode + "%'\n");
        }
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(stringBuilder.toString(), new HashMap<>());

        List<KeySafetyVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            KeySafetyVO vo = new KeySafetyVO();
            vo.setLabel(map.get("label").toString());
            vo.setOutNum(map.get("outNum") == null ? 0 : Integer.parseInt(map.get("outNum").toString()));
            vo.setInNum(map.get("inNum") == null ? 0 : Integer.parseInt(map.get("inNum").toString()));
            vo.setLastNum(map.get("lastNum") == null ? 0 : Integer.parseInt(map.get("lastNum").toString()));
            vo.setNum(map.get("num") == null ? 0 : Integer.parseInt(map.get("num").toString()));
            voList.add(vo);
        }
        //根据当年数据排序
        voList.sort(Comparator.comparing(KeySafetyVO::getNum));

        return voList;
    }

    @Override
    public PersonNumVO getPersonNum(String locationCode) {
        LambdaQueryWrapperX<PersonMange> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(StringUtils.hasText(locationCode), PersonMange::getBaseCode, locationCode);
        queryWrapperX.isNotNull(PersonMange::getIsBasePermanent);
        List<PersonMange> list = personMangeService.list(queryWrapperX);
        Map<Boolean, List<String>> collect = list.stream()
                .collect(Collectors.groupingBy(PersonMange::getIsBasePermanent,
                        Collectors.mapping(PersonMange::getId, Collectors.toList())));
        PersonNumVO personNumVO = new PersonNumVO();
        personNumVO.setNumber(list.size());
        if (!CollectionUtils.isEmpty(collect.get(Boolean.FALSE))) {
            personNumVO.setPermanentNumber(collect.get(Boolean.FALSE).size());
        } else {
            personNumVO.setPermanentNumber(0);
        }

        if (!CollectionUtils.isEmpty(collect.get(Boolean.TRUE))) {
            personNumVO.setBusinessTripNumber(collect.get(Boolean.TRUE).size());
        } else {
            personNumVO.setBusinessTripNumber(0);
        }
        return personNumVO;
    }

    @Override
    public List<LeadNumVO> getLeadNum(String locationCode) {
        String sql = "SELECT count( id ) AS count,lead_manage_type FROM pas_lead_management WHERE lead_manage_type IN('pms_equirement_track','pms_tech_leader') GROUP BY lead_manage_type";
        List<Map<String, Object>> maps = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<LeadNumVO> list = new ArrayList<>();
        maps.forEach(map -> {
            LeadNumVO leadNumVO = new LeadNumVO();
            leadNumVO.setLeadType(map.get("lead_manage_type"));
            if (map.get("lead_manage_type").equals("pms_equirement_track")) {
                leadNumVO.setLeadTypeName("需求跟踪");
            }else {
                leadNumVO.setLeadTypeName("技术引领");
            }
            leadNumVO.setLeadTypeNumber((Long) map.get("count"));
            list.add(leadNumVO);
        });

        Long sum = list.stream()
                .mapToLong(LeadNumVO::getLeadTypeNumber)
                .sum();

        list.forEach(leadNumVO -> {
            leadNumVO.setLeadTypePercentage(Float.valueOf(String.format("%.2f", leadNumVO.getLeadTypeNumber() / (float) sum * 100)));
        });
        return list;
    }

    @Override
    public List<BasePlaceVO> getLocation() {
        String sql = "select distinct code,name from pmsx_base_place where logic_status = 1";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<BasePlaceVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            BasePlaceVO vo = new BasePlaceVO();
            vo.setCode(map.get("code").toString());
            vo.setName(map.get("name").toString());
            voList.add(vo);
        }
        return voList;
    }


    @Override
    public List<RankingVO> getSafetyRankingList() {
        String sql = "SELECT\n" +
                "\trsp_dept_name AS locationName,\n" +
                "\tcount( 1 ) AS figure \n" +
                "FROM\n" +
                "\tpms_amperering_event_check_data_info \n" +
                "WHERE\n" +
                "\tlogic_status = 1  and ( rsp_dept_name is not null  or rsp_dept_name != '' )\n" +
                "GROUP BY\n" +
                "\trsp_dept_name";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<RankingVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            RankingVO vo = new RankingVO();
            vo.setLocationName(map.get("locationName").toString());
            vo.setFigure(map.get("figure") == null ? "0" : map.get("figure").toString());
            voList.add(vo);
        }
        //降序排序
        voList.sort(Comparator.comparing(RankingVO::getFigure, Comparator.comparingInt(Integer::parseInt)).reversed());
        return voList;
    }

    @Override
    public List<OverhaulDetailsVO> getOverhaulDetail(OverhaulDetailDTO overhaulDetailDTO) {
        //准备大修
        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition.eq(StringUtils.hasText(overhaulDetailDTO.getBaseName()), MajorRepairPlan::getBaseCode, overhaulDetailDTO.getBaseName());
        condition.eq(MajorRepairPlan::getStatus,121);
        condition.ge(StringUtils.hasText(overhaulDetailDTO.getStartDate()), MajorRepairPlan::getBeginTime, overhaulDetailDTO.getStartDate());
        condition.le(StringUtils.hasText(overhaulDetailDTO.getEndDate()), MajorRepairPlan::getEndTime, overhaulDetailDTO.getEndDate());
        List<MajorRepairPlan> list = majorRepairPlanService.list(condition);

        //实施大修
        LambdaQueryWrapperX<MajorRepairPlan> condition1 = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition1.eq(StringUtils.hasText(overhaulDetailDTO.getBaseName()), MajorRepairPlan::getBaseCode, overhaulDetailDTO.getBaseName());
        condition1.eq(MajorRepairPlan::getStatus,110);
        condition1.ge(StringUtils.hasText(overhaulDetailDTO.getStartDate()), MajorRepairPlan::getActualBeginTime, overhaulDetailDTO.getStartDate());
        condition1.le(StringUtils.hasText(overhaulDetailDTO.getEndDate()), MajorRepairPlan::getEndTime, overhaulDetailDTO.getEndDate());
        List<MajorRepairPlan> list1 = majorRepairPlanService.list(condition1);

        //完成大修
        LambdaQueryWrapperX<MajorRepairPlan> condition2 = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition2.eq(StringUtils.hasText(overhaulDetailDTO.getBaseName()), MajorRepairPlan::getBaseCode, overhaulDetailDTO.getBaseName());
        condition2.eq(MajorRepairPlan::getStatus,160);
        condition2.ge(StringUtils.hasText(overhaulDetailDTO.getStartDate()), MajorRepairPlan::getActualBeginTime, overhaulDetailDTO.getStartDate());
        condition2.le(StringUtils.hasText(overhaulDetailDTO.getEndDate()), MajorRepairPlan::getActualEndTime, overhaulDetailDTO.getEndDate());
        List<MajorRepairPlan> list2 = majorRepairPlanService.list(condition2);

        List<MajorRepairPlan> list3 = new ArrayList<>();
        list3.addAll(list);
        list3.addAll(list1);
        list3.addAll(list2);
        List<OverhaulDetailVO> voList = new ArrayList<>();
        if (!list3.isEmpty()) {
            list3.forEach(majorRepairPlan -> {
                OverhaulDetailVO vo = new OverhaulDetailVO();
                vo.setBaseName(majorRepairPlan.getBaseName());
                vo.setRepairRound(majorRepairPlan.getRepairRound());
                vo.setHaulStatus(majorRepairPlan.getStatus() != null && majorRepairPlan.getStatus() == 121 ? "正在准备的大修" : (majorRepairPlan.getStatus() != null && majorRepairPlan.getStatus() == 110 ? "正在进行的大修" : "已完成的大修"));
                vo.setBeginTime(majorRepairPlan.getBeginTime() == null ? "" : majorRepairPlan.getBeginTime().toString());
                vo.setEndTime(majorRepairPlan.getEndTime() == null ? "" : majorRepairPlan.getEndTime().toString());
                vo.setActualBeginTime(majorRepairPlan.getActualBeginTime() == null ? "" : majorRepairPlan.getActualBeginTime().toString());
                vo.setActualEndTime(majorRepairPlan.getActualEndTime() == null ? "" : majorRepairPlan.getActualEndTime().toString());
                vo.setWorkDuration(majorRepairPlan.getWorkDuration());
                vo.setRepairManager(majorRepairPlan.getRepairManager());
                vo.setType(majorRepairPlan.getType() != null && majorRepairPlan.getType().equals("pms_year_rp") ? "年度大修" : majorRepairPlan.getType() != null && majorRepairPlan.getType().equals("pms_ten_year_rp") ? "十年大修" : majorRepairPlan.getType() != null && majorRepairPlan.getType().equals("pms_twenty_year_rp") ? "二十年大修" : majorRepairPlan.getType() != null && majorRepairPlan.getType().equals("pms_thirty_year_rp") ? "三十年大修" : "短大修");
                voList.add(vo);
            });
        }

        Map<String, List<OverhaulDetailVO>> vos = voList.stream().collect(Collectors.groupingBy(OverhaulDetailVO::getBaseName));
        List<OverhaulDetailsVO> overhaulDetailsVOS = new ArrayList<>();
        vos.forEach((k, v) -> {
            OverhaulDetailsVO overhaulDetailsVO = new OverhaulDetailsVO();
            overhaulDetailsVO.setBaseName(k);
            overhaulDetailsVO.setOverhaulDetails(v);
            overhaulDetailsVOS.add(overhaulDetailsVO);
        });
        return overhaulDetailsVOS;
    }

    @Override
    public List<RankingVO> getRevenueRankingList() {
        String sql = "SELECT\n" +
                "\ttech_rsp_dept as locationName,\n" +
                "\tsum( contract_amt ) as figure \n" +
                "FROM\n" +
                "\tpms_market_contract \n" +
                "WHERE\n" +
                "\ttech_rsp_dept IS NOT NULL \n" +
                "\tand logic_status = 1\n" +
                "GROUP BY\n" +
                "\ttech_rsp_dept";

        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<RankingVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            RankingVO vo = new RankingVO();
            vo.setLocationName(deptRedisHelper.getDeptById(map.get("locationName").toString()) == null ? "" : deptRedisHelper.getDeptById(map.get("locationName").toString()).getName());
            vo.setFigure(map.get("figure") == null ? "0" : map.get("figure").toString());
            voList.add(vo);
        }
        //降序排序
        voList.sort(Comparator.comparing(RankingVO::getFigure, Comparator.comparingDouble(Double::parseDouble)).reversed());
        return voList;
    }

    @Override
    public List<RankingVO> getDurationRankingList() {
        String sql = "SELECT\n" +
                "\tb.rsp_dept as locationName,\n" +
                "\tsum( a.economize_duration ) as figure \n" +
                "FROM\n" +
                "\tpmsx_major_repair_plan_economize a,\n" +
                "\tpmsx_job_manage b  \n" +
                "where \n" +
                "\ta.job_manage_id = b.id\n" +
                "\tand a.logic_status = 1 \n" +
                "\tand b.logic_status = 1\n" +
                "\tand a.job_manage_id is not null\n" +
                "GROUP BY\n" +
                "\tb.rsp_dept";

        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<RankingVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            RankingVO vo = new RankingVO();
            vo.setLocationName(deptRedisHelper.getDeptById(map.get("locationName").toString()) == null ? "" : deptRedisHelper.getDeptById(map.get("locationName").toString()).getName());
            vo.setFigure(map.get("figure") == null ? "0" : map.get("figure").toString());
            voList.add(vo);
        }
        //降序排序
        voList.sort(Comparator.comparing(RankingVO::getFigure, Comparator.comparingDouble(Double::parseDouble)).reversed());
        return voList;
    }

    @Override
    public List<RankingVO> getDoseRankingList() {
        String sql = "SELECT\n" +
                "\tb.rsp_dept as locationName,\n" +
                "\tsum( a.conserve_meter ) as figure \n" +
                "FROM\n" +
                "\tpmsx_major_repair_plan_meter_reduce a,\n" +
                "\tpmsx_job_manage b  \n" +
                "where \n" +
                "\ta.job_manage_id = b.id\n" +
                "\tand a.logic_status = 1 \n" +
                "\tand b.logic_status = 1\n" +
                "\tand a.job_manage_id is not null\n" +
                "GROUP BY\n" +
                "\tb.rsp_dept";

        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<RankingVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            RankingVO vo = new RankingVO();

            vo.setLocationName(deptRedisHelper.getDeptById(map.get("locationName").toString()) == null ? "" : deptRedisHelper.getDeptById(map.get("locationName").toString()).getName());
            vo.setFigure(map.get("figure") == null ? "0" : map.get("figure").toString());
            voList.add(vo);
        }
        //降序排序
        voList.sort(Comparator.comparing(RankingVO::getFigure, Comparator.comparingDouble(Double::parseDouble)).reversed());
        return voList;
    }

    @Override
    public List<OverhaulVO> getOverhaul(String locationCode) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT \n" +
                "\tSTATUS as overhaulStatus,\n" +
                "\tcount( 1 ) as figure,\n" +
                "\tCONCAT(ROUND((COUNT(*) * 100.0) / (SELECT COUNT(*) FROM pmsx_job_manage where n_or_o = 'o' and logic_status = 1), 2), '%') AS ratio\n" +
                "FROM\n" +
                "\tpmsx_job_manage\n" +
                "where \n" +
                "\tn_or_o = 'o'\n" +
                "\tand logic_status = 1\n");
        if (StringUtils.hasText(locationCode)) {
            stringBuilder.append("and job_base_name like '%" + locationCode + "%'\n");
        }
        stringBuilder.append("group by STATUS\n");
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(stringBuilder.toString(), new HashMap<>());
        List<OverhaulVO> voList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            OverhaulVO vo = new OverhaulVO();
            vo.setOverhaulStatus(map.get("overhaulStatus").equals(121) ? "大修准备" : (map.get("overhaulStatus").equals(110) ? "大修实施" : "大修完成"));
            vo.setFigure(map.get("figure") == null ? "0" : map.get("figure").toString());
            vo.setRatio(map.get("ratio") == null ? "0%" : map.get("ratio").toString());
            voList.add(vo);
        }
        return voList;
    }

}
