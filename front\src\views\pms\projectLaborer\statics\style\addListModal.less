.searchModalDrawer {
  .ant-drawer-body {
    //padding: 60px 0px 80px 0px !important;
  }


  //.nodeItemBtn {
  //  position: fixed;
  //  bottom: 0px;
  //  padding: 20px 0px;
  //  text-align: center;
  //  width: 310px;
  //  height: 80px;
  //  background: #ffffff;
  //  margin-bottom: 0px;
  //  text-align: center;
  //}
  //
  //.cancelBtn {
  //  color: #5172dc;
  //  background: #5172dc19;
  //  width: 120px;
  //  border-radius: 4px;
  //}
  //.bgDC {
  //  width: 120px;
  //  margin-left: 15px;
  //  border-radius: 4px;
  //}
}
.search_title {
  padding: 10px 0px;
  border-bottom: 1px solid #d2d7e1;
  text-align: center;
  margin-bottom: 10px;
  .ant-input-search {
    width: 310px;
  }
}
.basicTitle {
  padding: 0px 15px;
}
.rowItem {
  margin-bottom: 100px;
  //   background: red;
  //   height: 300px;
  .rowItem_label {
    padding-left: 5px;
    color: #444b5e;
  }
  .ant-select {
    width: 100%;
  }
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
.ant-menu {
  /deep/li:hover {
    background: #eef1fc;
  }
}
.avatar {
  display: flex;
  align-items: center;
  .left {
    width: 60px;
    height: 60px;
  }
  .right {
    font-size: 16px;
    padding-left: 15px;
    .rightitem1 {
      font-size: 18px;
    }
    .rightitem {
      margin-bottom: 0;
    }
    .rightitem2 {
      height: 20px;
      line-height: 20px;
      font-size: 12px;
      color: #666666;
    }
  }
}
