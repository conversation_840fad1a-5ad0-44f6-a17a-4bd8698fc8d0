<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.SchemeToPersonMapper">



    <select id="getUserListByRepairRound" resultType="com.chinasie.orion.domain.entity.SchemeToPerson">
        select user_code,user_name,person_id,base_code from pmsx_scheme_to_person where repair_round=#{repairRound} and logic_status=1 and is_have_project=1
        <if test="null != keyword and keyword != ''">
            and (user_code like concat('%',#{keyword},'%') or user_name like concat('%',#{keyword},'%'))
        </if>
    </select>


    <select id="listByRepairRound" resultType="com.chinasie.orion.domain.dto.excel.PersonPlanVO">
        select DISTINCT ps.person_id,ps.user_code,user_name,pp.in_date,pp.out_date,case pp.newcomer when 1 then '是' else '否' end as isNewcomer,pp.contact_user_name
        from  pmsx_scheme_to_person ps
            INNER JOIN pmsx_person_mange pp on  ps.person_id =  pp.id and pp.logic_status=1
        where ps.repair_round =#{repairRound} and ps.logic_status=1 and pp.status in (0,2) and ps.person_id is not null
    </select>
    <select id="countPage" resultType="java.lang.Integer">
        select count(distinct pst .person_id) from pmsx_scheme_to_person pst
        left join  pms_project_scheme ps on ps.id = pst.plan_scheme_id and ps.logic_status=1
        left join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where pst.repair_round =#{repairRound}  and pst.logic_status=1
        <if test="null != keyword and keyword != ''">
            and (pst.user_code like concat('%',#{keyword},'%') or pst.user_name like concat('%',#{keyword},'%')
                or pp.name like concat('%',#{keyword},'%') or pp.number like concat('%',#{keyword},'%')
                or ps.name like concat('%',#{keyword},'%'))
        </if>

    </select>
    <!-- condition.select(SchemeToPerson::getUserId,SchemeToPerson::getUserCode,SchemeToPerson::getUserName,SchemeToPerson::getPersonId,SchemeToPerson::getIsHaveProject); -->

    <select id="pageList" resultType="com.chinasie.orion.domain.entity.SchemeToPerson">
        select distinct  b.user_id ,b.user_code,b.user_name,b.person_id,b.is_have_project from (

        select    pst.user_id ,pst.user_code,pst.user_name,pst.person_id,pst.is_have_project
        from pmsx_scheme_to_person pst
        left join pms_project_scheme ps on ps.id = pst.plan_scheme_id and ps.logic_status=1
        left join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where pst.repair_round =#{repairRound}  and pst.logic_status=1
        <if test="null != keyword and keyword != ''">
            and (pst.user_code like concat('%',#{keyword},'%') or pst.user_name like concat('%',#{keyword},'%')
            or pp.name like concat('%',#{keyword},'%') or pp.number like concat('%',#{keyword},'%')
            or ps.name like concat('%',#{keyword},'%'))
        </if>
        order by pp.name desc ,ps.name desc,pst.create_time desc
                              ) b
        limit #{pageNum},#{pageSize}
    </select>
    <select id="getListByPersonIdList" resultType="com.chinasie.orion.domain.vo.SchemeToPersonVO">
        select    pst.person_id as personId,ps.name as planSchemeName,pp.name as projectName,pst.plan_scheme_id as planSchemeId,pp.id as projectId
        from pmsx_scheme_to_person pst
                 inner join pms_project_scheme ps on ps.id = pst.plan_scheme_id
                 inner join  pms_project pp on ps.project_id = pp.id and pp.logic_status=1
        where ps.repair_round =#{repairRound} and ps.logic_status=1 and pst.logic_status=1
        and pst.person_id in
        <foreach collection="personIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
</mapper>

