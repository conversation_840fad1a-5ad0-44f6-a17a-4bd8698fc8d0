package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectDemandStatisticsDTO;

import com.chinasie.orion.domain.vo.DemandManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDemandStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectDemandStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectDemandStatistics")
@Api(tags = "项目内需求统计")
public class ProjectDemandStatisticsController {
    @Autowired
    private ProjectDemandStatisticsService projectDemandStatisticsService;


    @ApiOperation(value = "需求状态分布统计")
    @RequestMapping(value = "/getProjectDemandStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【需求状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectDemandStatisticsVO> getProjectDemandStatusStatistics(@RequestBody ProjectDemandStatisticsDTO projectDemandStatisticsDTO) throws Exception {
        ProjectDemandStatisticsVO rsp =  projectDemandStatisticsService.getProjectDemandStatusStatistics(projectDemandStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "需求状态负责人统计")
    @RequestMapping(value = "/getProjectDemandRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【需求状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectDemandStatisticsVO>> getProjectDemandRspUserStatistics(@RequestBody ProjectDemandStatisticsDTO projectDemandStatisticsDTO) throws Exception {
        List<ProjectDemandStatisticsVO> rsp =  projectDemandStatisticsService.getProjectDemandRspUserStatistics(projectDemandStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "需求状态趋势统计")
    @RequestMapping(value = "/getProjectDemandChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【需求状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectDemandStatisticsVO>> getProjectDemandChangeStatusStatistics( @RequestBody ProjectDemandStatisticsDTO projectDemandStatisticsDTO) throws Exception {
        List<ProjectDemandStatisticsVO> rsp =  projectDemandStatisticsService.getProjectDemandChangeStatusStatistics(projectDemandStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "需求新增趋势统计")
    @RequestMapping(value = "/getProjectDemandCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【需求新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectDemandStatisticsVO>> getProjectDemandCreateStatistics( @RequestBody ProjectDemandStatisticsDTO projectDemandStatisticsDTO) throws Exception {
        List<ProjectDemandStatisticsVO> rsp =  projectDemandStatisticsService.getProjectDemandCreateStatistics(projectDemandStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "需求分页查询")
    @RequestMapping(value = "/getProjectDemandPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【需求分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<DemandManagementVO>> getProjectDemandPages(@RequestBody Page<ProjectDemandStatisticsDTO> pageRequest) throws Exception {
        Page<DemandManagementVO> rsp =  projectDemandStatisticsService.getProjectDemandPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
