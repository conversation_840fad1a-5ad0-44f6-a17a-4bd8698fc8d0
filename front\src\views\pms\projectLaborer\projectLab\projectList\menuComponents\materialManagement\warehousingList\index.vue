<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_06_04_01_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="headAdd"
        >
          添加入库
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_06_04_01_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <WarehousingListDrawer
      @upTableDate="upTableDate"
      @register="modalWarehousingListRegister"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  ref, toRefs, nextTick, onMounted, h, computed, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
import { getWarehousingList } from '/@/views/pms/api';
import dayjs from 'dayjs';
import Tags from './components/Tags.vue';
import WarehousingListDrawer from './components/warehousingListDrawer.vue';
const [modalWarehousingListRegister, { openDrawer: openWarehousingListDrawer }] = useDrawer();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const tableRef = ref(null);
const selectRows = ref([]);
const disabledBtn = ref(false);
const powerData:Ref<any[]> = ref([]);
const baseTableOption = {
  api: async (params) => {
    const result:Record<string, any> = await getWarehousingList({
      ...params,
      query: { projectId: props.id },
      power: {
        pageCode: 'PMS0004',
        containerCode: 'PMS_XMXQ_container_06_04_02',
        headContainerCode: 'PMS_XMXQ_container_06_04_01',
      },
    });
    powerData.value = result.headAuthList;
    return result;
  },
  rowSelection: {},
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_MATERIALMANAGEMENT_WAREHOUSINGLIST_INDEX',
  smallSearchField: ['goods_service_number', 'description'],
  columns: [
    {
      title: '类型',
      dataIndex: 'type',
      width: 50,
    },
    {
      title: '物资/服务编码',
      dataIndex: 'goodsServiceNumber',
      width: 150,
    },
    {
      title: '物资/服务描述',
      dataIndex: 'description',
      width: 150,
    },
    {
      title: '规格型号',
      dataIndex: 'normsModel',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 80,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '入库数量',
      dataIndex: 'totalStoreAmount',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '入库日期',
      dataIndex: 'storeTime',
      width: 120,
      customRender({ text, record }) {
        return dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '添加时间',
      dataIndex: 'createTime',
      width: 200,
      customRender({ text }) {
        return text || '--';
      },
    },

    {
      title: '添加人',
      dataIndex: 'dateCreatorName',
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      minWidth: 200,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_04_02_button_01', record.rdAuthList),
      onClick(record) {
        router.push({
          name: 'MaterialWarehousingDetails',
          query: {
            id: record.id,
            projectId: props.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_04_02_button_02', record.rdAuthList),
      onClick(record) {
        openWarehousingListDrawer(true, {
          type: 'edit',
          itemData: record,
          projectId: props.id,
        });
      },
    },
    {
      text: '删除',
      isShow: (record:Record<string, any>) => isPower('PMS_XMXQ_container_06_04_02_button_03', record.rdAuthList),
      modal: (record) => batchDelete([record.id]),
    },
    {
      text: '添加入库',
      isShow: (record) => isPower('PMS_XMXQ_container_06_04_02_button_04', record.rdAuthList) && record.typeCode === 'goodsType',
      onClick(record) {
        openWarehousingListDrawer(true, {
          type: 'add',
          itemData: record,
          projectId: props.id,
        });
      },
    },
  ],
};

function selectionChange({ rows }) {
  selectRows.value = rows;
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的评价？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/goods-service-store').fetch(ids, '', 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function upTableDate() {
  tableRef.value?.reload();
}
function headAdd() {
  if (selectRows.value.length > 0) {
    if (selectRows.value.length === 1 && selectRows.value[0].typeCode === 'goodsType' && Number(selectRows.value[0]?.dataStatus?.statusValue) === 160) {
      openWarehousingListDrawer(true, {
        type: 'add',
        itemData: selectRows.value[0],
        projectId: props.id,
      });
    } else {
      message.info('请勾选需要删除的数据');
    }
  } else {
    openWarehousingListDrawer(true, {
      type: 'headAdd',
      itemData: {},
      projectId: props.id,
    });
  }
}

</script>
<style scoped lang="less">
</style>
