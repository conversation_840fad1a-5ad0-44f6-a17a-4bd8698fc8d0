<template>
  <Layout2
    class="contract-main"
  >
    <div class="table-content">
      <OrionTable
        ref="tableRef"
        :dataSource="taskDataSource"
        :options="state.options"
      />
    </div>
  </Layout2>
</template>

<script lang="ts" setup>
import {
  h, inject, reactive, ref,
} from 'vue';
import { Input as AInput } from 'ant-design-vue';
import { Layout2, OrionTable, randomString } from 'lyra-component-vue3';
import Api from '/@/api';

const projectId = inject('projectId');
const tableRef = ref(null);
const taskDataSource = ref([
  {
    id: `${randomString()}`,
    code: 'xd234',
    name: '设备/软件使用费',
    total: '11230',
    dcl: '58',
    hb: '2',
    tb: '3',
    isEdited: false,
  },
  {
    id: `${randomString()}`,
    code: 'xd234',
    name: '日常行政管理费用',
    total: '11230',
    dcl: '58',
    hb: '2',
    tb: '3',
    isEdited: false,
  },
  {
    id: `${randomString()}`,
    code: 'xd234',
    name: '前台项目部成本分摊',
    total: '11230',
    dcl: '58',
    hb: '2',
    tb: '3',
    isEdited: false,
  },
  {
    id: `${randomString()}`,
    code: 'xd234',
    name: '内部委托成本',
    total: '11230',
    dcl: '58',
    hb: '2',
    tb: '3',
    isEdited: false,
  },
  {
    id: `${randomString()}`,
    code: 'xd234',
    name: '税费',
    total: '11230',
    dcl: '58',
    hb: '2',
    tb: '3',
    isEdited: false,
  },
]);
const state = reactive({
  powerData: null,
  options: {
    deleteToolButton: 'add|delete|enable|disable',
    rowSelection: false,
    showSmallSearch: true,
    isFilter2: false,
    smallSearchField: ['indexName'],
    api: () => new Api('/pms/projectFinanceIndex/page').fetch({ query: { projectId } }, '', 'POST'),
    columns: [
      {
        title: '财务指标名称',
        dataIndex: 'indexName',
      },
      {
        title: '总值（万元/%）',
        dataIndex: 'indexCost',
        width: 200,
        customRender: ({ record }: { record: any }) => {
          if (record?.isEdited) {
            return h(AInput, {
              value: record.indexCost,
              onChange: (event) => {
                record.indexCost = event.target.value;
              },
              placeholder: '',
              style: {
                width: '100%',
              },
            });
          }
          return record?.indexCost;
        },
      },
      {
        title: '达成率 （%）',
        dataIndex: 'achievementRate',
        width: 200,
        customRender: ({ record }: { record: any }) => {
          if (record?.isEdited) {
            return h(AInput, {
              value: record.achievementRate,
              onChange: (event) => {
                record.achievementRate = event.target.value;
              },
              placeholder: '',
              style: {
                width: '100%',
              },
            });
          }
          return record?.achievementRate;
        },
      },
      {
        title: '计划金额',
        dataIndex: 'indexPlan',
        width: 200,
        customRender: ({ record }: { record: any }) => {
          if (record?.isEdited) {
            return h(AInput, {
              value: record.indexPlan,
              onChange: (event) => {
                record.indexPlan = event.target.value;
              },
              placeholder: '',
              style: {
                width: '100%',
              },
            });
          }
          return record?.indexPlan;
        },
      },
      {
        title: '结余金额',
        dataIndex: 'residue',
        width: 200,
        customRender: ({ record }: { record: any }) => {
          if (record?.isEdited) {
            return h(AInput, {
              value: record.residue,
              onChange: (event) => {
                record.residue = event.target.value;
              },
              placeholder: '',
              style: {
                width: '100%',
              },
            });
          }
          return record?.residue;
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
        width: 200,
        fixed: 'right',
      },
    ],
    actions: [
      {
        isShow: (record) => !record.isEdited,
        text: '编辑',
        onClick: (record) => {
          record.isEdited = true;
        },
      },
      {
        isShow: (record) => record.isEdited === true,
        text: '确认',
        onClick: (record) => {
          fnOk(record);
        },
      },
      {
        isShow: (record) => record.isEdited === true,
        text: '取消',
        onClick: (record) => {
          // 使用map方法遍历数据源，找到与record.id匹配的数据，并修改isEdited属性
          const updatedDataList = taskDataSource.value.map((item) => {
            if (item.id === record.id) {
              return {
                ...item,
                isEdited: false,
              };
            }
            return item;
          });
          // 更新数据源
          taskDataSource.value = updatedDataList;
          // 重新加载表格数据
          tableReload();
        },
      },
    ],
  },
});

function fnOk(record) {
  const params = {
    id: record.id,
    indexCost: record.indexCost,
    achievementRate: record.achievementRate,
    indexPlan: record.indexPlan,
    residue: record.residue,
    projectId,
  };
  new Api('/pms/projectFinanceIndex/edit').fetch(params, '', 'PUT').then(() => {
    // 在数据源中查找与record.id匹配的数据，并将isEdited属性设置为false
    taskDataSource.value.forEach((item) => {
      item.isEdited = false;
    });
    // 重新加载表格数据
    tableReload();
  });
}

function tableReload() {
  tableRef.value.reload();
}

</script>

<style lang="less" scoped>
.table-content {
  .ant-empty {
    top: 50%;
    position: absolute;
    width: 100%;
  }
}

.risk-table-title {
  flex: 1;
  display: flex;
  justify-content: space-between;

  .ant-input-search {
    width: 200px
  }
}

.contract-main {
  &.layout2-wrap {
    :deep(.left) {
      overflow: hidden !important;
      box-sizing: border-box !important;
      width: 280px !important;
      margin-right: 0 !important;
      border-right: 1px solid #e5e7eb;

      .left-wrap {
        overflow: hidden !important;
        padding: 0 !important;
      }
    }
  }
}
</style>
