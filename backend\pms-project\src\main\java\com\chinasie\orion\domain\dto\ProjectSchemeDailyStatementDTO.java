package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "ProjectCollectionTreeUtil对象", description = "项目计划关联日报")
@Data
public class ProjectSchemeDailyStatementDTO extends ObjectDTO implements Serializable {


    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 日报日期
     */
    @ApiModelProperty(value = "日报日期")
    private Date date;
}
