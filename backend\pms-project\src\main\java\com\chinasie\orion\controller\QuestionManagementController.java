package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionManagementService;
import com.chinasie.orion.service.RiskManagementService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:33
 * @description:
 */
@RestController
@RequestMapping("/question-management")
@Api(tags = "问题管理")
public class QuestionManagementController {
    @Resource
    private DictBo dictBo;
    @Resource
    private QuestionManagementService questionManagementService;
    @Resource
    private RiskManagementService riskManagementService;

    @Resource
    private ComponentFeignService componentFeignService;

    @ApiOperation("新增问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionManagementDTO", dataType = "QuestionManagementDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增问题", type = "问题管理", subType = "新增问题", bizNo = "")

    public ResponseDTO<String> saveQuestionManagement(@RequestBody QuestionManagementDTO questionManagementDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.saveQuestionManagement(questionManagementDTO));
    }

    @ApiOperation("批量新增问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionManagementDTOList", dataType = "QuestionManagementDTO")
    })
    @PostMapping(value = "/save/batch")
    @LogRecord(success = "【{USER{#logUserId}}】批量新增问题", type = "问题管理", subType = "批量新增问题", bizNo = "")
    public ResponseDTO saveBatch(@RequestBody List<QuestionManagementDTO> questionManagementDTOList) throws Exception {
        return new ResponseDTO<>(questionManagementService.createBatch(questionManagementDTOList));
    }

    @ApiOperation("获取问题分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取问题分页", type = "问题管理", subType = "获取问题分页", bizNo = "")
    public ResponseDTO<Page<QuestionManagementVO>> getQuestionManagementPage(@RequestBody Page<QuestionManagementDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(questionManagementService.getQuestionManagementPage(pageRequest));
    }

    @ApiOperation("获取问题详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取问题详情", type = "问题管理", subType = "获取问题详情", bizNo = "{{#id}}")
    public ResponseDTO<QuestionManagementVO> getQuestionManagementDetail(@PathVariable("id") String id, String pageCode) throws Exception {
        return new ResponseDTO<>(questionManagementService.getQuestionManagementDetail(id, pageCode));
    }

    @ApiOperation("编辑问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionManagementDTO", dataType = "QuestionManagementDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑问题", type = "问题管理", subType = "编辑问题", bizNo = "")
    public ResponseDTO<Boolean> editQuestionManagement(@RequestBody QuestionManagementDTO questionManagementDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.editQuestionManagement(questionManagementDTO));
    }

    @ApiOperation("批量删除问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除问题", type = "问题管理", subType = "批量删除问题", bizNo = "")
    public ResponseDTO<Boolean> removeQuestionManagement(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(questionManagementService.removeQuestionManagement(ids));
    }

    @ApiOperation("批量删除问题相关的风险和计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/deleteQuestionRelationRiskAndPlan")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除问题相关的风险和计划", type = "问题管理", subType = "批量删除问题相关的风险和计划", bizNo = "")
    public ResponseDTO<Boolean> deleteQuestionRelationRiskAndPlan(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(questionManagementService.deleteQuestionRelationRiskAndPlan(ids));
    }

    @ApiOperation("获取问题类型列表")
    @GetMapping(value = "/questionTypeList")
    @LogRecord(success = "【{USER{#logUserId}}】获取问题类型列表", type = "问题管理", subType = "获取问题类型列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getQuestionTypeList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.QUESTION_TYPE));
    }

    @ApiOperation("获取问题来源列表")
    @GetMapping(value = "/questionSourceList")
    @LogRecord(success = "【{USER{#logUserId}}】获取问题来源列表", type = "问题管理", subType = "获取问题来源列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getQuestionSourceList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.QUESTION_SOURCE));
    }

    @ApiOperation("获取严重程度列表")
    @GetMapping(value = "/seriousLevelList")
    @LogRecord(success = "【{USER{#logUserId}}】获取严重程度列表", type = "问题管理", subType = "获取严重程度列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getSeriousLevelList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.QUESTION_SERIOUS_LEVEL));
    }

    @ApiOperation("问题转计划")
    @PostMapping(value = "/questionChangePlan/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String", required = true),
    })
    @LogRecord(success = "【{USER{#logUserId}}】问题转计划", type = "问题管理", subType = "问题转计划", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> questionChangePlan(@PathVariable("id") String id, @RequestBody List<ProjectSchemeDTO> projectSchemes) throws Exception {
        return new ResponseDTO<>(questionManagementService.questionChangePlan(id,projectSchemes));
    }

    @ApiOperation("关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @PostMapping(value = "/relation/plan")
    @LogRecord(success = "【{USER{#logUserId}}】关联计划", type = "问题管理", subType = "关联计划", bizNo = "")
    public ResponseDTO<Boolean> relationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.relationToPlan(relationToPlanDTO));
    }

    @ApiOperation("批量删除关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @DeleteMapping(value = "/relation/plan/batch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除关联计划", type = "问题管理", subType = "批量删除关联计划", bizNo = "")
    public ResponseDTO<Boolean> removeRelationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.removeRelationToPlan(relationToPlanDTO));
    }

    @ApiOperation("获取关联计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String"),
            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
    })
    @PostMapping(value = "/relation/plan/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取关联计划列表", type = "问题管理", subType = "获取关联计划列表", bizNo = "{{#id}}")
    public ResponseDTO<List<PlanDetailVo>> getPlanManagementListByRisk(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.getPlanManagementListByQuestion(id, planQueryDTO));
    }

    @ApiOperation("获取关联风险列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", dataType = "String"),
            @ApiImplicitParam(name = "riskQueryDTO", dataType = "RiskQueryDTO")
    })
    @PostMapping(value = "/relation/risk/{questionId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取关联风险列表", type = "问题管理", subType = "获取关联风险列表", bizNo = "{{#questionId}}")
    public ResponseDTO<List<RiskManagementVO>> getRiskManagementListByQuestion(@PathVariable("questionId") String questionId, @RequestBody(required = false) RiskQueryDTO riskQueryDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.getRiskManagementListByQuestion(questionId, riskQueryDTO));
    }

    @ApiOperation("获取问题列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
    })
    @PostMapping(value = "/search/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取问题列表", type = "问题管理", subType = "获取问题列表", bizNo = "")
    public ResponseDTO<PlanSearchDataVo> searchList(@RequestBody KeywordDto keywordDto) throws Exception {
        return new ResponseDTO<>(questionManagementService.searchList(keywordDto));
    }


    @ApiOperation(value = "关闭")
    @RequestMapping(value = "/close", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】关闭了数据", type = "问题管理", subType = "关闭", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> close(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionManagementService.close(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "激活")
    @RequestMapping(value = "/open", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】激活了数据", type = "问题管理", subType = "激活", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> open(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionManagementService.open(ids);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("问题管理导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "问题管理", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        questionManagementService.downloadExcelTpl(response);
    }

    @ApiOperation("问题管理导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "问题管理", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file,@RequestParam String id) throws Exception {
        ImportExcelCheckResultVO rsp = questionManagementService.importCheckByExcel(file,id);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("问题管理导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "问题管理", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = questionManagementService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消问题管理导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "问题管理", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = questionManagementService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("问题管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "问题管理", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        questionManagementService.exportByExcel(searchConditions, response);
    }


    @ApiOperation("关联评审单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", dataType = "String"),
            @ApiImplicitParam(name = "reviewFormId", dataType = "String")
    })
    @PostMapping(value = "/relation/reviewForm")
    @LogRecord(success = "【{USER{#logUserId}}】关联评审单", type = "问题管理", subType = "关联评审单", bizNo = "{{#questionId}}")
    public ResponseDTO<Boolean> relationToReviewForm(@RequestParam("questionId") String questionId, @RequestParam("reviewFormId") String reviewFormId) throws Exception {
        return new ResponseDTO<>(questionManagementService.relationToReviewForm(questionId, reviewFormId));
    }

    @ApiOperation("删除关联评审单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionId", dataType = "String"),
            @ApiImplicitParam(name = "reviewFormId", dataType = "String")
    })
    @DeleteMapping(value = "/relation/reviewForm")
    @LogRecord(success = "【{USER{#logUserId}}】删除关联评审单", type = "问题管理", subType = "删除关联评审单", bizNo = "{{#questionId}}")
    public ResponseDTO<Boolean> removeRelationToReviewForm(@RequestParam("questionId") String questionId, @RequestParam("reviewFormId") String reviewFormId) throws Exception {
        return new ResponseDTO<>(questionManagementService.removeRelationToReviewForm(questionId, reviewFormId));
    }

    //    @ApiOperation("获取关联计划列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", dataType = "String"),
//            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
//    })
//    @PostMapping(value = "/relation/plan/{id}")
//    public ResponseDTO<List<PlanDetailVo>> getPlanManagementListByRisk(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(questionManagementService.getPlanManagementListByQuestion(id, planQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }


}
