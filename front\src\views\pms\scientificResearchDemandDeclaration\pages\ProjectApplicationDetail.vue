<script setup lang="ts">
import {
  BasicButton, Layout3, Layout3Content, useDrawer,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, onMounted, provide, readonly, ref, Ref, unref, watchEffect,
} from 'vue';
import { Spin, Empty } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { MenuItem } from './type';
import ProjectInfo from './components/ProjectInfo.vue';
import MeetingMinutes from './components/MeetingMinutes.vue';
import AssociatedItem from './components/AssociatedItem.vue';
import RelatedAccessories from './components/RelatedAccessories.vue';
import { declarationData, declarationDataId, updateDeclarationData } from './keys';
import Api from '/@/api';
import CreateAndEditDrawer from '../components/CreateAndEditDrawer/Index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import AddTableNode from '/@/views/pms/scientificResearchDemandDeclaration/pages/components/components/addNode.vue';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
const route = useRoute();
// 申报数据id
const dataId: Ref<string> = ref(route.query.id as string);
provide(declarationDataId, readonly(dataId));
const projectData: Ref = ref();
provide(declarationData, projectData);
const defaultActionId: Ref<string> = ref('xMXX');
const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const relatedAccessoriesRef : Ref = ref();
const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: {
    ...projectData.value,
    name: projectData.value.projectName,
  },
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));
const loading: Ref<boolean> = ref(false);
// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && projectData.value?.status === 101);
// 详情顶部数据
const layoutData = computed(() => ({
  name: projectData.value?.projectName,
  ownerName: projectData.value?.resUserName,
  dataStatus: projectData.value?.dataStatus,
  projectCode: projectData.value?.projectNumber,
}));
const menuData: Ref<MenuItem[]> = ref([
  {
    id: 'xMXX',
    name: '项目信息',
  },
  // {
  //   id: 'ySXX',
  //   name: '预算信息',
  // },
  {
    id: 'hYJY',
    name: '会议纪要',
  },
  {
    id: 'xGFJ',
    name: '相关附件',
  },

  {
    id: 'gLXM',
    name: '关联项目',
  },

  {
    id: 'sPLC',
    name: '审批流程',
  },
]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/scientificResearchDemandDeclare').fetch('', unref(dataId), 'GET');
    projectData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey, result.name);
  } finally {
    loading.value = false;
  }
}

provide(updateDeclarationData, getDetailData);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

const addNode = () => {
  openDrawerAdd(true, { type: 'add' });
};
// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}
function update() {
  relatedAccessoriesRef.value?.updateTable();
}
</script>

<template>
  <Layout3
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectData?.name }}
        </div>
        <div class="numberStyle">
          {{ projectData?.number }}
        </div>
      </div>
    </template>

    <template #header-right>
      <BasicButton
        v-if="projectData?.status===130"
        icon="sie-icon-bianji"
        type="primary"
        @click="addNode"
      >
        创建项目
      </BasicButton>
      <BasicButton
        v-if="projectData?.status===101"
        icon="sie-icon-bianji"
        type="primary"
        @click="openCreateAndEdit(true,{id:projectData?.id})"
      >
        编辑
      </BasicButton>
      <BasicButton
        v-if="showWorkflowAdd"
        type="primary"
        icon="sie-icon-qidongliucheng"
        @click="handleAddWorkflow"
      >
        发起流程
      </BasicButton>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <!--项目信息-->
      <ProjectInfo v-if="defaultActionId==='xMXX'" />
      <!--预算信息设计中-->
      <div
        v-if="defaultActionId==='ySXX'"
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty description="模块设计中..." />
      </div>
      <!--会议纪要-->
      <MeetingMinutes v-if="defaultActionId==='hYJY'" />

      <AssociatedItem v-if="defaultActionId==='gLXM'" />

      <!--相关附件-->
      <RelatedAccessories
        v-if="defaultActionId==='xGFJ'"
        ref="relatedAccessoriesRef"
      />
      <!--审批流程-->
      <WorkflowView
        v-if="defaultActionId==='sPLC'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </Layout3Content>

    <!--编辑申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="getDetailData"
      @register="registerCreateAndEdit"
    />
    <AddTableNode
      @register="registerAdd"
      @update="update"
    />
  </Layout3>
</template>

<style scoped lang="less">
.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;

  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
