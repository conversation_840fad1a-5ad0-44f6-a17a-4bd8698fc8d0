package com.chinasie.orion.domain.dto.relationOrgToMaterial;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * relationOrgToMaterial DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@ApiModel(value = "RelationOrgToMaterialDTO对象", description = "大修组织和大修组织物资关系")
@Data
@ExcelIgnoreUnannotated
public class RelationOrgToMaterialDTO extends  ObjectDTO   implements Serializable{

    /**
     * 大修组织id
     */
    @ApiModelProperty(value = "大修组织id")
    @ExcelProperty(value = "大修组织id ", index = 0)
    private String repairOrgId;

    /**
     * 物质管理id
     */
    @ApiModelProperty(value = "物质管理id")
    @ExcelProperty(value = "物质管理id ", index = 1)
    private String materialId;

    /**
     * 物质编码
     */
    @ApiModelProperty(value = "物质编码")
    @ExcelProperty(value = "物质编码 ", index = 2)
    private String materialNumber;






}
