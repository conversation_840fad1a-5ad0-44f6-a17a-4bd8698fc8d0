import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import MaterialEnterForm from './components/MaterialEnterForm.vue';
import MaterialLeaveForm from './components/MaterialLeaveForm.vue';

// 物资入库、编辑
export function openMaterialEnterForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑物资' : '物资入库',
    width: 1000,
    content() {
      return h(MaterialEnterForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 物资出库
export function openMaterialLeaveForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '物资出库',
    width: 1000,
    content() {
      return h(MaterialLeaveForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}