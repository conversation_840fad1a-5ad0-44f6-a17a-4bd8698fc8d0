package com.chinasie.orion.service.impl;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.UserConvertBO;
import com.chinasie.orion.domain.dto.BasePlaceDTO;
import com.chinasie.orion.domain.entity.BasePlace;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BasePlaceMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * BasePlace 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
@Service
@Slf4j
public class BasePlaceServiceImpl extends OrionBaseServiceImpl<BasePlaceMapper, BasePlace> implements BasePlaceService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  BasePlaceVO detail(String id,String pageCode) throws Exception {
        BasePlace BasePlace =this.getById(id);
        BasePlaceVO result = BeanCopyUtils.convertTo(BasePlace,BasePlaceVO::new);
        setEveryName(Collections.singletonList(result));

        LogRecordContext.putVariable("name", result.getName());
        return result;
    }

    /**
     *  新增
     *
     * * @param BasePlaceDTO
     */
    @Override
    public  String create(BasePlaceDTO BasePlaceDTO) throws Exception {
        BasePlace BasePlace =BeanCopyUtils.convertTo(BasePlaceDTO,BasePlace::new);
        this.save(BasePlace);

        String rsp=BasePlace.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param BasePlaceDTO
     */
    @Override
    public Boolean edit(BasePlaceDTO BasePlaceDTO) throws Exception {
        BasePlace BasePlace =BeanCopyUtils.convertTo(BasePlaceDTO,BasePlace::new);

        this.updateById(BasePlace);

        String rsp=BasePlace.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        condition.in(BasePlace::getId, ids);
        condition.select(BasePlace::getId,BasePlace::getCode,BasePlace::getName,BasePlace::getCity,BasePlace::getNumber);
        List<BasePlace> list = this.list(condition);

        LogRecordContext.putVariable("baseCodes", list.stream().map(BasePlace::getCode).collect(Collectors.joining(",")));
        LogRecordContext.putVariable("baseNames", list.stream().map(BasePlace::getName).collect(Collectors.joining(",")));
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasePlaceVO> pages( Page<BasePlaceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BasePlace::getCreateTime);


        Page<BasePlace> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasePlace::new));

        PageResult<BasePlace> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasePlaceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BasePlaceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BasePlaceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "基地库导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasePlaceDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            BasePlaceExcelListener excelReadListener = new BasePlaceExcelListener();
        EasyExcel.read(inputStream,BasePlaceDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BasePlaceDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("基地库导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BasePlace> BasePlacees =BeanCopyUtils.convertListTo(dtoS,BasePlace::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::BasePlace-import::id", importId, BasePlacees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BasePlace> BasePlacees = (List<BasePlace>) orionJ2CacheService.get("ncf::BasePlace-import::id", importId);
        log.info("基地库导入的入库数据={}", JSONUtil.toJsonStr(BasePlacees));

        this.saveBatch(BasePlacees);
        orionJ2CacheService.delete("ncf::BasePlace-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::BasePlace-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BasePlace::getCreateTime);
        List<BasePlace> BasePlacees =   this.list(condition);

        List<BasePlaceDTO> dtos = BeanCopyUtils.convertListTo(BasePlacees, BasePlaceDTO::new);

        String fileName = "基地库数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasePlaceDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BasePlaceVO> vos)throws Exception {
        Set<String> userIds = new HashSet<>();
        for (BasePlaceVO vo : vos) {
            if (StringUtils.hasText(vo.getCreatorId())){
                userIds.add(vo.getCreatorId());
            }
            if (StringUtils.hasText(vo.getModifyId())){
                userIds.add(vo.getModifyId());
            }
        }
        final Map<String, String> mapByIdsForRedis = UserConvertBO.getMapByIdsForRedis(new ArrayList<>(userIds), true);

        vos.forEach(vo->{
            if (StringUtils.hasText(vo.getCreatorId())){
                vo.setCreatorName(mapByIdsForRedis.getOrDefault(vo.getCreatorId(),""));
            }
            if (StringUtils.hasText(vo.getModifyId())){
                vo.setModifyName(mapByIdsForRedis.getOrDefault(vo.getModifyId(),""));
            }
        });


    }

    @Override
    public List<BasePlaceVO> allList() {
        List<BasePlace> list = this.list();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return  BeanCopyUtils.convertListTo(list,BasePlaceVO::new);

    }

    @Override
    public Map<String, String> allMapSimpleList() {
        LambdaQueryWrapperX<BasePlace> wrapperX = new LambdaQueryWrapperX<>(BasePlace.class);
        List<BasePlace> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(BasePlace::getCode,BasePlace::getName));
    }

    @Override
    public Map<String, BasePlace> mapEntityAll() {
        List<BasePlace> list = this.list();
        if(CollectionUtils.isEmpty(list)){
            return new HashMap<>();
        }
        return  list.stream().collect(Collectors.toMap(BasePlace::getCode,Function.identity()));
    }

    @Override
    public String getNameByCode(String baseCode) {
        if(!StringUtils.hasText(baseCode)){
            return  "";
        }
        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        condition.eq(BasePlace::getCode,baseCode);
        condition.select(BasePlace::getId,BasePlace::getCode,BasePlace::getName);
        List<BasePlace> basePlaceList=  this.list(condition);
        if(CollectionUtils.isEmpty(basePlaceList)){
            return  "";
        }

        return basePlaceList.get(0).getName();
    }

    @Override
    public Map<String, String> getNameByCode(List<String> baseCode) {
        if(ObjectUtil.isEmpty(baseCode)){
            return  null;
        }
        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        condition.in(BasePlace::getCode,baseCode);
        condition.select(BasePlace::getId,BasePlace::getCode,BasePlace::getName);
        List<BasePlace> basePlaceList=  this.list(condition);
        if(CollectionUtils.isEmpty(basePlaceList)){
            return  null;
        }
        Map<String, String> map = basePlaceList.stream().collect(Collectors.toMap(BasePlace::getCode, BasePlace::getName));
        return map;
    }


    public static class BasePlaceExcelListener extends AnalysisEventListener<BasePlaceDTO> {

        private final List<BasePlaceDTO> data = new ArrayList<>();

        @Override
        public void invoke(BasePlaceDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BasePlaceDTO> getData() {
            return data;
        }
    }


}
