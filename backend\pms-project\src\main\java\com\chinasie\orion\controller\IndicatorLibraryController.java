package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IndicatorLibraryDTO;
import com.chinasie.orion.domain.vo.IndicatorLibraryVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IndicatorLibraryService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * IndicatorLibrary 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 15:13:04
 */
@RestController
@RequestMapping("/indicatorLibrary")
@Api(tags = "绩效管理指标库")
public class IndicatorLibraryController {

    @Autowired
    private IndicatorLibraryService indicatorLibraryService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IndicatorLibrary详情】",subType = "详情", type = "绩效管理指标库", bizNo = "{{#id}}")
    public ResponseDTO<IndicatorLibraryVO> detail(@PathVariable(value = "id") String id) throws Exception {
        IndicatorLibraryVO rsp = indicatorLibraryService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param indicatorLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【IndicatorLibrary】", type = "绩效管理指标库",subType = "新增",bizNo = "{{#indicatorLibraryDTO.name}}")
    public ResponseDTO<IndicatorLibraryVO> create(@RequestBody IndicatorLibraryDTO indicatorLibraryDTO) throws Exception {
        IndicatorLibraryVO rsp =  indicatorLibraryService.create(indicatorLibraryDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param indicatorLibraryDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【IndicatorLibrary】", type = "绩效管理指标库", subType = "编辑",bizNo = "{{#indicatorLibraryDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  IndicatorLibraryDTO indicatorLibraryDTO) throws Exception {
        Boolean rsp = indicatorLibraryService.edit(indicatorLibraryDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除", type = "绩效管理指标库", subType = "删除批量", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = indicatorLibraryService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IndicatorLibrary分页】", subType = "分页",type = "绩效管理指标库", bizNo = "")
    public ResponseDTO<Page<IndicatorLibraryVO>> pages(@RequestBody Page<IndicatorLibraryDTO> pageRequest) throws Exception {
        Page<IndicatorLibraryVO> rsp =  indicatorLibraryService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 启用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "/enable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用【IndicatorLibrary】", type = "绩效管理指标库", subType = "启用",bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> enable(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = indicatorLibraryService.enable(ids);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 禁用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/disable", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】禁用【IndicatorLibrary】", type = "绩效管理指标库", subType = "禁用",bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> disEnable(@RequestBody List<String> ids ) throws Exception {
        Boolean rsp = indicatorLibraryService.disEnable(ids);
        return new ResponseDTO<>(rsp);
    }
}
