<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    title="重命名"
    :body-style="bodyStyle"
    :mask-closable="false"
    @close="handleClose"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :rules="rules"
      :model="father.form"
    >
      <a-form-item
        label="名称"
        name="name"
      >
        <a-input
          v-model:value="father.form.name"
          placeholder="请输入名称"
          allow-clear
          :maxlength="64"
          size="large"
        />
      </a-form-item>
      <div class="drawer-footer">
        <a-row :gutter="20">
          <a-col :span="12">
            <a-button
              size="large"
              block
              @click="handleClose"
            >
              取消
            </a-button>
          </a-col>
          <a-col :span="12">
            <a-button
              size="large"
              type="primary"
              block
              :loading="loading"
              @click="handleSave"
            >
              确认
            </a-button>
          </a-col>
        </a-row>
      </div>
    </a-form>
  </a-drawer>
</template>

<script>
import { reactive, toRefs, ref } from 'vue';
import {
  Row, Col, Drawer, Form, Input, Button, message,
} from 'ant-design-vue';
import Api from '/@/api';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    AInput: Input,
    AButton: Button,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: props.data,
      loading: false,
      formRef: ref(),
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
    });
    function handleClose() {
      state.father.visible = false;
    }

    function handleSave() {
      state.formRef
        .validate()
        .then(() => {
          state.loading = true;
          const love = {
            id: state.father.form?.id,
            name: state.father.form?.name,
            className: 'Plan',
            moduleName: '项目管理-计划管理-项目计划-关联内容-关联文档', // 模块名称
            type: 'GET', // 操作类型
            remark: `编辑了【${state.father.form?.name}】`,
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'document/edit', 'PUT')
            .then(() => {
              state.loading = false;
              state.father.visible = false;
              message.success('操作成功');
              emit('submit');
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }

    return {
      ...toRefs(state),
      handleClose,
      handleSave,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
</style>
