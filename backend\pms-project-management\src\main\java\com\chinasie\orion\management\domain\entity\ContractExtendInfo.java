package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractExtendInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_extend_info")
@ApiModel(value = "ContractExtendInfoEntity对象", description = "合同拓展信息")
@Data

public class ContractExtendInfo extends ObjectEntity implements Serializable {

    /**
     * 采购组织
     */
    @ApiModelProperty(value = "采购组织")
    @TableField(value = "procurement_org_name")
    private String procurementOrgName;

    /**
     * 采购组织ID
     */
    @ApiModelProperty(value = "采购组织ID")
    @TableField(value = "procurement_org_id")
    private String procurementOrgId;

    /**
     * 采购组
     */
    @ApiModelProperty(value = "采购组")
    @TableField(value = "procurement_group_name")
    private String procurementGroupName;

    /**
     * 采购组ID
     */
    @ApiModelProperty(value = "采购组ID")
    @TableField(value = "procurement_group_id")
    private String procurementGroupId;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "business_rsp_user")
    private String businessRspUser;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @TableField(value = "business_rsp_user_id")
    private String businessRspUserId;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @TableField(value = "technical_rsp_user")
    private String technicalRspUser;

    /**
     * 技术负责人ID
     */
    @ApiModelProperty(value = "技术负责人ID")
    @TableField(value = "technical_rsp_user_id")
    private String technicalRspUserId;

    /**
     * 推荐依据
     */
    @ApiModelProperty(value = "推荐依据")
    @TableField(value = "recommendation_basis")
    private String recommendationBasis;

    /**
     * 节省总金额（RMB）
     */
    @ApiModelProperty(value = "节省总金额（RMB）")
    @TableField(value = "negotiate_save_amount")
    private BigDecimal negotiateSaveAmount;

    /**
     * 渠道优化节省金额
     */
    @ApiModelProperty(value = "渠道优化节省金额")
    @TableField(value = "sum_save_amount")
    private BigDecimal sumSaveAmount;

    /**
     * 谈判节省金额
     */
    @ApiModelProperty(value = "谈判节省金额")
    @TableField(value = "compare_save_amount")
    private BigDecimal compareSaveAmount;

    /**
     * 与立项相比节省金额
     */
    @ApiModelProperty(value = "与立项相比节省金额")
    @TableField(value = "channel_save_amount")
    private BigDecimal channelSaveAmount;

    /**
     * 优化采购节省金额
     */
    @ApiModelProperty(value = "优化采购节省金额")
    @TableField(value = "optimize_save_amount")
    private BigDecimal optimizeSaveAmount;

    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    @TableField(value = "is_process_amount")
    private Boolean isProcessAmount;

    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    @TableField(value = "prcess_amount_pay_way")
    private String prcessAmountPayWay;

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @TableField(value = "prcess_amount")
    private String prcessAmount;

    /**
     * 账户名称
     */
    @ApiModelProperty(value = "账户名称")
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    @TableField(value = "bank_account")
    private String bankAccount;

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    @TableField(value = "bank_code")
    private String bankCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;
}
