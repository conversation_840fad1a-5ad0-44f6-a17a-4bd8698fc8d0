package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectSchemeDTO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:17
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeDTO对象", description = "项目计划")
public class ProjectSchemeDTO extends ObjectDTO implements Serializable {

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private String id;
    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String schemeNumber;

    /**
     *
     */
    @ApiModelProperty(value = "计划名称")
    @NotEmpty(message = "项目计划名称不能为空")
    @Length(max = 100, message = "计划名称过长，建议控制在100字符以内")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "icon")
    private String icon;
    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    /**
     *
     */
    @ApiModelProperty(value = "层级")
    private Integer level;
    /**
     *
     */
    @ApiModelProperty(value = "父id")
    private String parentId;
    /**
     *
     */
    @ApiModelProperty(value = "父级链")
    private String parentChain;
    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    private Integer type;
    /**
     *
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     *
     */
    @ApiModelProperty(value = "责任科室")
    private String rspSectionId;
    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
//    @NotEmpty(message = "责任部门不能为空")
    private String rspSubDept;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    @NotEmpty(message = "责任人")
    private String rspUser;

    @ApiModelProperty(value = "责任人编号")
    private String rspUserCode;

    /**
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @NotNull(message = "开始时间不能为空")
    private Date beginTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
    /**
     *
     */
    @ApiModelProperty(value = "计划情况")
    private Integer circumstance;
    /**
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
    /**
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "项目计划描述")
    @Length(max = 200, message = "项目计划描述过长，建议控制在200字符以内")
    private String schemeDesc;

    @ApiModelProperty(value = "置顶序号（0：取消置顶，其他根据序号排列置顶计划）")
    private Integer topSort;

    @ApiModelProperty(value = "执行情况说明")
    private String executeDesc;

    @ApiModelProperty(value = "附件文档")
    private List<FileDTO> attachments;

    @ApiModelProperty(value = "计划下发时间")
    private Date issueTime;

    @ApiModelProperty(value = "来源")
    private String from;

    @ApiModelProperty(value = "来源Id")
    private String fromId;



    @ApiModelProperty(value = "是否超时完成 1 是，0 否")
    private Boolean delayEndFlag;

    @ApiModelProperty(value = "超时原因")
    private String delayEndReason;


    @ApiModelProperty(value = "是否关联流程 1 是，0 否")
    private Boolean processFlag;

    @ApiModelProperty(value = "关联流程实例Id")
    private String processId;

    @ApiModelProperty(value = "关联流程实例Id")
    private String formCodeId;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;

 /**
     * 子级
     */
    @ApiModelProperty(value = "子级")
    private List<ProjectSchemeDTO> children;

    /**
     *
     */
    @ApiModelProperty(value = "计划类型")
    private String nodeType;


    @ApiModelProperty(value = "下达人")
    private String issuedUser;
    @ApiModelProperty(value = "参与人")
    private String participantUsers;
    @ApiModelProperty(value = "参与人集合")
    private List<String> participantUserList;
    @ApiModelProperty(value = "审批人")
    private String examineUser;
    @ApiModelProperty(value = "评分状态")
    private String examineType;

    @ApiModelProperty(value = "计划活动项")
    private String planActive;

    @ApiModelProperty(value = "选择基线")
    private String baseLine;

    @ApiModelProperty(value = "转办理由")
    private String  reasonTransfer;

    @ApiModelProperty(value = "产品编码")
    private String  productId;

    @ApiModelProperty(value = "设计任务编码")
    private String designTaskNumber;

    @ApiModelProperty(value = "标识")
    private String identification;
    @ApiModelProperty(value = "确认理由")
    private String reasonConfirmation;
    @ApiModelProperty(value = "确认结果")
    private String confirmResult;


    @ApiModelProperty(value = "暂停时间")
    private Date suspendTime;

    @ApiModelProperty(value = "终止时间")
    private Date terminateTime;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "暂停理由")
    private String suspendReason;

    @ApiModelProperty(value = "终止理由")
    private String terminateReason;

    @ApiModelProperty(value = "启动理由")
    private String startReason;


    @ApiModelProperty(value = "上个状态")
    private Integer lastStatus;

    @ApiModelProperty(value = "是否启动流程")
    private Boolean isProcess;


    @ApiModelProperty(value = "是否作业")
    private Boolean isWorkJob;

    @ApiModelProperty(value = "实施类型")
    private String enforceType;

    @ApiModelProperty(value = "实施类型名称")
    private String enforceTypeName;

    @ApiModelProperty(value = "实施地点")
    private String enforceBasePlace;
    @ApiModelProperty(value = "实施地点名称")
    private String enforceBasePlaceName;

    @ApiModelProperty(value = "实施区域/范围")
    private String enforceScope;
    @ApiModelProperty(value = "实施区域/范围名称")
    private String enforceScopeName;

    @ApiModelProperty(value = "工作内容")
    private String workContent;
    @ApiModelProperty(value = "工作内容名称")
    private String workContentName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "是否携带物资")
    private Boolean isCarryMaterials;

    @ApiModelProperty(value = "紧急程度")
    private String  urgency;

    @ApiModelProperty(value = "是否作业 -1：否 1：是")
    private Integer isWork;

    @ApiModelProperty(value = "逾期次数")
    private Integer overdueCount;

    @ApiModelProperty(value = "合同里程碑id")
    private String contractMilestoneId;

    @ApiModelProperty(value = "问题id")
    private String questionId;
}
