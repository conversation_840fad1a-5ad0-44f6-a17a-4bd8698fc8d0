package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/23/11:28
 * @description:
 */
@Data
@TableName(value = "pms_status_count" )
@ApiModel(value = "StatusCount对象", description = "每日状态统计")
public class StatusCount implements Serializable {

    /**
     * 类型（任务、需求、问题、风险......）
     */
    @ApiModelProperty(value = "类型（任务、需求、问题、风险......）")
    @TableField(value = "type")
    private Integer type;

    /**
     * 当前日期
     */
    @ApiModelProperty(value = "当前日期")
    @TableField(value = "now_date")
    private Date nowDate;

    /**
     *
     */
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id ")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    @TableField(value = "un_finish_count")
    private Integer unFinishCount;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    @TableField(value = "finish_count")
    private Integer finishCount;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    @TableField(value = "finishing_count")
    private Integer finishingCount;

}
