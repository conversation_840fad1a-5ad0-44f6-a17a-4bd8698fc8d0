package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/5/31
 * @description:
 */
@Data
public class ProjectSchemeTemplateEditVO {

    private String id;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;
}
