package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.repository.*;
import com.chinasie.orion.constant.RoleColeConstant;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Keafmd
 *
 * @ClassName: DepartmentCenterInterfaceRoleExp
 * @Description: 用户所属部门分支中的
 * 「部门/中心」一级与业务单据「承担部门」匹配
 * 且用户属于角色「中心商务接口人(Business_01)」或「中心技术接口人(Business_02)」
 * @author: zhangqianyang
 * @date: 2024/8/12 10:02
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class DepartmentCenterMemberInterfaceRoleExp implements IExp {

    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;


    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptUserDOMapper deptUserDOMapper;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private RoleUserDOMapper roleUserDOMapper;



    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "当前登录人所属的部门/中心 角色 是承担部门";
    }

    @Override
    public List<String> exp(String s) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        //获取当前用户的所在的部门
        LambdaQueryWrapperX<DeptUserDO> deptUserQuery = new LambdaQueryWrapperX<>();
        deptUserQuery.eq(DeptUserDO::getUserId, currentUserId);
        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(deptUserQuery);
        if (CollectionUtils.isEmpty(deptUserDOS)) {
            log.info("当前用户没有归属的部门");
            return Arrays.asList("");
        }
        LambdaQueryWrapperX<DeptDO> deptQuery = new LambdaQueryWrapperX();
        deptQuery.in(DeptDO::getId, deptUserDOS.stream().map(DeptUserDO::getDeptId).collect(Collectors.toList()));
        List<DeptDO> deptDOS = deptDOMapper.selectList(deptQuery);
        if (CollectionUtils.isEmpty(deptDOS)) {
            log.info("当前用户查询的部门信息不存在");
            return Arrays.asList("");
        }

        //获取当前用户的上级组织
        Set<String> parentDept = Sets.newHashSet();
        deptDOS.forEach(dept -> {
            if (StringUtils.hasText(dept.getChain())) {
                parentDept.addAll(Arrays.asList(dept.getChain().split(",")));
            }
        });

        //获取部门、中心一级的组织机构

        LambdaQueryWrapperX<DeptDO> deptCenterQuery = new LambdaQueryWrapperX<>();
        deptCenterQuery.eq(DeptDO::getType, "20");
        deptCenterQuery.eq(DeptDO::getLogicStatus, 1);
        List<DeptDO> departCenterDepts = deptDOMapper.selectList(deptCenterQuery);

        List<String> returnLists = Lists.newArrayList();
        departCenterDepts.stream().forEach(deptDO -> {
            if (parentDept.contains(deptDO.getId())) {
                returnLists.add(deptDO.getId());
            }
        });

        if (CollectionUtils.isEmpty(returnLists)) {
            log.info("当前用户的用户所在的部门的上级机构中不存在 部门/中心一级的机构");
            return Arrays.asList("");
        }

        //用户是承担部门的 主要领导 或者 用户是中心商务接口人 ， 中心技术接口人 角色

        //如果是 中心商务接口人 或者 中心技术接口人 角色
        if(isInterfaceRole(currentUserId)){
            log.info("当前用户是【{}】中心/部门  的中心商务接口人,或者中心技术接口人",returnLists);
            return returnLists;
        }

        //找出上面的部门中心的部门中，那些部门中心，当前用户是主管领导
        LambdaQueryWrapperX<DeptLeaderDO> deptLeaderQueryWrapperx=new LambdaQueryWrapperX<>();
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getType,"main");//主管lingd
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getClassName,"OrganizationLeader");//组织管理领导
        deptLeaderQueryWrapperx.in(DeptLeaderDO::getDeptId,returnLists);//承担部门（部门、中心）
        deptLeaderQueryWrapperx.eq(DeptLeaderDO::getUserId, CurrentUserHelper.getCurrentUserId());//当前登录用户
        List<DeptLeaderDO> deptLeaderDOS = deptLeaderDORepository.selectList(deptLeaderQueryWrapperx);
        List<String> userMainLeaderDeptIds = new ArrayList<>();
        for (DeptLeaderDO deptLeaderDO : deptLeaderDOS) {
            String deptId = deptLeaderDO.getDeptId();
            userMainLeaderDeptIds.add(deptId);
        }
        if(CollectionUtils.isEmpty(userMainLeaderDeptIds)){
            log.info("用户不存在部门/中心的主管领导的部门");
            return Arrays.asList("");
        }
        return userMainLeaderDeptIds;
    }

    /**
     * 当前用户是否是 中心商务接口人 或者 中心技术接口人 接口人
     *
     * @param currentUserId
     * @return
     */
    private boolean isInterfaceRole(String currentUserId) {
        List<RoleUserDO> roleUserDOS = roleUserDOMapper.selectList(RoleUserDO::getUserId, currentUserId);
        if (CollectionUtils.isEmpty(roleUserDOS)) {
            log.info("当前用户没有关联角色");
            return false;
        }
        List<String> roleUserRoleIDs = roleUserDOS.stream().map(RoleUserDO::getRoleId).collect(Collectors.toList());

        List<RoleDO> roleDOS = roleDOMapper.selectList(RoleDO::getCode, Arrays.asList(RoleColeConstant.CENTER_BUSINESS_INTERFACE_ROLE_CODE,RoleColeConstant.CENTER_ROLE_CODE, RoleColeConstant.CENTER_TECHNICAL_INTERFACE_ROLE_CODE));

        if(CollectionUtils.isEmpty(roleDOS)){
            log.info("当前系统没有中心商务接口人/中心技术接口人 的角色");
            return false;
        }

        List<String> roleIds = roleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());

        for(String id:roleIds){
            if(roleUserRoleIDs.contains(id)){
                return true;
            }
        }
        return false;
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
