package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/6/26
 * @description:
 */
@Data
public class ProjectMaterialPlanPreparationVO {

    private String id;

    @ApiModelProperty("创建人")
    private String creatorId;
    @ApiModelProperty("申请人")
    private String creatorName;
    @ApiModelProperty("申请人")
    private Date createTime;


    /**
     * 备料单号
     */
    @ApiModelProperty(value = "备料单号")
    private String number;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    private Date requireCompleteTime;


    /**
     * 备料数量
     */
    @ApiModelProperty(value = "备料数量")
    private Integer preparationNum;
}
