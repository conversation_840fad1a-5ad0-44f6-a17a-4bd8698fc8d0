package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.RequirementPayMangementDTO;
import com.chinasie.orion.management.domain.entity.RequirementPayMangement;
import com.chinasie.orion.management.domain.vo.RequirementPayMangementVO;
import com.chinasie.orion.management.repository.RequirementPayMangementMapper;
import com.chinasie.orion.management.service.RequirementPayMangementService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * RequirementPayMangement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15 14:00:35
 */
@Service
@Slf4j
public class RequirementPayMangementServiceImpl extends OrionBaseServiceImpl<RequirementPayMangementMapper, RequirementPayMangement> implements RequirementPayMangementService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RequirementPayMangementVO detail(String id, String pageCode) throws Exception {
        RequirementPayMangement requirementPayMangement = this.getById(id);
        RequirementPayMangementVO result = BeanCopyUtils.convertTo(requirementPayMangement, RequirementPayMangementVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param requirementPayMangementDTO
     */
    @Override
    public String create(RequirementPayMangementDTO requirementPayMangementDTO) throws Exception {
        RequirementPayMangement requirementPayMangement = BeanCopyUtils.convertTo(requirementPayMangementDTO, RequirementPayMangement::new);
        this.save(requirementPayMangement);

        String rsp = requirementPayMangement.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param requirementPayMangementDTO
     */
    @Override
    public Boolean edit(RequirementPayMangementDTO requirementPayMangementDTO) throws Exception {
        RequirementPayMangement requirementPayMangement = BeanCopyUtils.convertTo(requirementPayMangementDTO, RequirementPayMangement::new);

        this.updateById(requirementPayMangement);

        String rsp = requirementPayMangement.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequirementPayMangementVO> pages(Page<RequirementPayMangementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RequirementPayMangement> condition = new LambdaQueryWrapperX<>(RequirementPayMangement.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RequirementPayMangement::getCreateTime);


        Page<RequirementPayMangement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RequirementPayMangement::new));

        PageResult<RequirementPayMangement> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RequirementPayMangementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RequirementPayMangementVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RequirementPayMangementVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "需求支付信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequirementPayMangementDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        RequirementPayMangementExcelListener excelReadListener = new RequirementPayMangementExcelListener();
        EasyExcel.read(inputStream, RequirementPayMangementDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<RequirementPayMangementDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("需求支付信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<RequirementPayMangement> requirementPayMangementes = BeanCopyUtils.convertListTo(dtoS, RequirementPayMangement::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::RequirementPayMangement-import::id", importId, requirementPayMangementes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<RequirementPayMangement> requirementPayMangementes = (List<RequirementPayMangement>) orionJ2CacheService.get("pmsx::RequirementPayMangement-import::id", importId);
        log.info("需求支付信息导入的入库数据={}", JSONUtil.toJsonStr(requirementPayMangementes));

        this.saveBatch(requirementPayMangementes);
        orionJ2CacheService.delete("pmsx::RequirementPayMangement-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::RequirementPayMangement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RequirementPayMangement> condition = new LambdaQueryWrapperX<>(RequirementPayMangement.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(RequirementPayMangement::getCreateTime);
        List<RequirementPayMangement> requirementPayMangementes = this.list(condition);

        List<RequirementPayMangementDTO> dtos = BeanCopyUtils.convertListTo(requirementPayMangementes, RequirementPayMangementDTO::new);

        String fileName = "需求支付信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequirementPayMangementDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<RequirementPayMangementVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class RequirementPayMangementExcelListener extends AnalysisEventListener<RequirementPayMangementDTO> {

        private final List<RequirementPayMangementDTO> data = new ArrayList<>();

        @Override
        public void invoke(RequirementPayMangementDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<RequirementPayMangementDTO> getData() {
            return data;
        }
    }


}
