package com.chinasie.orion.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.MsgTaskName;
import com.chinasie.orion.constant.SchemeMessageNode;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.dto.TaskMsgDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class TaskSendMessageCommonAdapter {

    @Resource
    private MessageCenterApi messageCenterApi;

    @Resource
    protected CurrentUserHelper userHelper;
    @Resource
    protected PmsMQProducer mqProducer;

    /**
     * 构建消息发送体
     *
     * @param schemeMsgDTO
     * @param <T>
     * @return
     * @throws Exception
     */
    protected abstract <T> List<SendMessageDTO> buildMscMessageDTO(TaskMsgDTO taskMsgDTO) throws Exception;


    protected <T> void handle(TaskMsgDTO taskMsgDTO) throws Exception {
        List<SendMessageDTO> dtoList = buildMscMessageDTO(taskMsgDTO);
        if (CollUtil.isNotEmpty(dtoList)) {
            dtoList.forEach(item -> mqProducer.sendPmsMessage(item));
        }
    }

    protected void clearToDo(MsgBusinessTypeEnum type, String businessId, String userId) {
        log.debug("【{}】消除待办参数：{}", type.getDesc(), packageBusinessId(type, businessId));
        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(userId)
                .businessId(packageBusinessId(type, businessId))
                .build());
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));
    }

    protected String packageBusinessId(MsgBusinessTypeEnum type, String businessId) {
        return new StringBuilder(String.valueOf(type)).append(StrUtil.UNDERLINE).append(businessId).toString();
    }


    protected void clearToDo(MsgBusinessTypeEnum type, String businessId) {
        log.debug("【{}】消除待办参数：{}", type.getDesc(), packageBusinessId(type, businessId));
        ResponseDTO<?> responseDTO = messageCenterApi.todoMessageChangeStatusByBusinessId(packageBusinessId(type, businessId));
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));
    }
}
