package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.InvoiceInformation;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.domain.vo.InvoiceInformationVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * InvoiceInformation Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@Mapper
public interface InvoiceInformationMapper extends  OrionBaseMapper  <InvoiceInformation> {

    InvoiceInformationVO getTotal(@Param("contractId") String contractId);
}

