package com.chinasie.orion.filter;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.MDC;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

@Aspect
@Component
@Slf4j
public class LoggingAspect {

    public static final String TID = "tid";

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *) || " +
            "within(@org.springframework.stereotype.Controller *)")
    public void controllerMethods() {
    }

    @Before("controllerMethods()")
    public void logRequest(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String method = request.getMethod();
        String url = request.getRequestURL().toString();
        String queryString = request.getQueryString();

        Object[] args = joinPoint.getArgs();
        String params = JSONUtil.toJsonStr(args);
        // 打印所有请求头信息
        Enumeration<String> headerNames = request.getHeaderNames();
        StringBuilder headersInfo = new StringBuilder();
        String tid = request.getHeader(TID);
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            if (CharSequenceUtil.isNotBlank(headerValue)) {
                headersInfo.append(headerName).append(": ").append(headerValue).append(",");
                if (TID.equals(headerName)) {
                    tid = headerValue;
                }
            }
        }
        String uid = CurrentUserHelper.getCurrentUserId();
        if (CharSequenceUtil.isNotBlank(uid)) {
            tid = uid + " | " + tid;
        }
        if (CharSequenceUtil.isNotBlank(tid)) {
            MDC.put(TID, tid);
        }

        // 生成 curl 命令
        StringBuilder curlCommand = new StringBuilder("[ curl -X ").append(method).append(" '").append(url);
        if (CharSequenceUtil.isNotBlank(queryString)) {
            curlCommand.append("?").append(queryString);
        }
        curlCommand.append("'");

        // 添加请求头
        if (headersInfo.length() > 0) {
            String[] headers = headersInfo.toString().split(",");
            for (String header : headers) {
                curlCommand.append(" -H '").append(header.trim()).append("'");
            }
        }

        // 添加请求体
        if (method.equalsIgnoreCase("POST") || method.equalsIgnoreCase("PUT") || method.equalsIgnoreCase("PATCH")) {
            curlCommand.append(" -d '").append(params).append("'").append(" ]");
        }

        // 使用 SLF4J 占位符机制避免不必要的 JSON 序列化
        if (log.isInfoEnabled()) {
            log.info("> TID: {} | {}", tid, curlCommand);
        }
    }

    @AfterReturning(pointcut = "controllerMethods()", returning = "result")
    public void logResponse(JoinPoint joinPoint, Object result) {
        // 获取 TID，避免空指针异常
        String tid = MDC.get(TID) != null ? MDC.get(TID) : "N/A";

        // 获取响应状态码
        int statusCode = getStatusCode(joinPoint, result);

        // 计算返回结果大小
        String resultSize = result != null ? String.valueOf(JSONUtil.toJsonStr(result).length()) : "0";

        // 使用 SLF4J 占位符机制避免不必要的 JSON 序列化
        if (log.isInfoEnabled()) {
            log.info("< TID: {} | M: {} | Status Code: {} | Result Size: {}", tid, joinPoint.getSignature().toShortString(), statusCode, resultSize);
        }
    }

    // 新增方法，用于获取响应状态码
    private int getStatusCode(JoinPoint joinPoint, Object result) {
        // 假设返回结果是一个 ResponseEntity 对象
        if (result instanceof ResponseEntity) {
            ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
            return responseEntity.getStatusCodeValue();
        }
        // 如果不是 ResponseEntity，返回默认状态码 200
        return 200;
    }


    @Around("controllerMethods()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object proceed = joinPoint.proceed();
        long executionTime = System.currentTimeMillis() - start;
        if (executionTime > 1000) {
            log.info("< TID: {}  {} time {} ms", MDC.get(TID), joinPoint.getSignature().toShortString(), executionTime);
        }
        return proceed;
    }

    @AfterThrowing(pointcut = "controllerMethods()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Throwable ex) {
        log.error("< TID: {} Exception in {} with message: {} | {}", MDC.get(TID), joinPoint.getSignature().toShortString(), ex.getMessage(), ex.getStackTrace());
    }
}