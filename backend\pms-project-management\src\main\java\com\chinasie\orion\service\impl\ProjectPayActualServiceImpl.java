package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.dto.ProjectPayActualDTO;
import com.chinasie.orion.domain.entity.ProjectPayActual;
import com.chinasie.orion.domain.vo.ProjectPayActualVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPayActualMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPayActualService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectPayActual 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:18
 */
@Service
@Slf4j
public class ProjectPayActualServiceImpl extends  OrionBaseServiceImpl<ProjectPayActualMapper, ProjectPayActual>   implements ProjectPayActualService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectPayActualVO detail(String id, String pageCode) throws Exception {
        ProjectPayActual projectPayActual =this.getById(id);
        ProjectPayActualVO result = BeanCopyUtils.convertTo(projectPayActual,ProjectPayActualVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectPayActualDTO
     */
    @Override
    public  String create(ProjectPayActualDTO projectPayActualDTO) throws Exception {
        ProjectPayActual projectPayActual =BeanCopyUtils.convertTo(projectPayActualDTO,ProjectPayActual::new);
        this.save(projectPayActual);

        String rsp=projectPayActual.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPayActualDTO
     */
    @Override
    public Boolean edit(ProjectPayActualDTO projectPayActualDTO) throws Exception {
        ProjectPayActual projectPayActual =BeanCopyUtils.convertTo(projectPayActualDTO,ProjectPayActual::new);

        this.updateById(projectPayActual);

        String rsp=projectPayActual.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectPayActualVO> pages( Page<ProjectPayActualDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPayActual> condition = new LambdaQueryWrapperX<>( ProjectPayActual. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectPayActual::getCreateTime);

        ProjectPayActualDTO projectPayActualDTO =  pageRequest.getQuery();
        if(!Objects.isNull(projectPayActualDTO)){
            if(StringUtils.hasText(projectPayActualDTO.getPsphi())){
                condition.eq(ProjectPayActual::getPsphi,projectPayActualDTO.getPsphi());
            }
            if(StringUtils.hasText(projectPayActualDTO.getKstar())){
                condition.eq(ProjectPayActual::getKstar,projectPayActualDTO.getKstar());
            }
        }

        Page<ProjectPayActual> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPayActual::new));

        PageResult<ProjectPayActual> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPayActualVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPayActualVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPayActualVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "实际成本金额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayActualDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectPayActualExcelListener excelReadListener = new ProjectPayActualExcelListener();
        EasyExcel.read(inputStream,ProjectPayActualDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectPayActualDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("实际成本金额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectPayActual> projectPayActuales =BeanCopyUtils.convertListTo(dtoS,ProjectPayActual::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectPayActual-import::id", importId, projectPayActuales, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectPayActual> projectPayActuales = (List<ProjectPayActual>) orionJ2CacheService.get("pmsx::ProjectPayActual-import::id", importId);
        log.info("实际成本金额导入的入库数据={}", JSONUtil.toJsonStr(projectPayActuales));

        this.saveBatch(projectPayActuales);
        orionJ2CacheService.delete("pmsx::ProjectPayActual-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectPayActual-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectPayActual> condition = new LambdaQueryWrapperX<>( ProjectPayActual. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectPayActual::getCreateTime);
        List<ProjectPayActual> projectPayActuales =   this.list(condition);

        List<ProjectPayActualDTO> dtos = BeanCopyUtils.convertListTo(projectPayActuales, ProjectPayActualDTO::new);

        String fileName = "实际成本金额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayActualDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectPayActualVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectPayActualExcelListener extends AnalysisEventListener<ProjectPayActualDTO> {

        private final List<ProjectPayActualDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectPayActualDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectPayActualDTO> getData() {
            return data;
        }
    }


}
