<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch, onUnmounted,
} from 'vue';
import { Input as AInput, Tooltip, Tooltip as ATooltip } from 'ant-design-vue';
import { planActiveColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import { isPower, Icon } from 'lyra-component-vue3';
import { useRouter } from 'vue-router';

const props = withDefaults(defineProps<{
    record:object,
}>(), {
  record: () => ({}),
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const isFocus:Ref<boolean> = ref(false);// 是否获取焦点
const rowNameValue:Ref<string> = ref('');
const router = useRouter();
// 下拉框展开操作
function handleMouseleave() {
  if (isFocus.value) return;
  isEdit.value = false;
}
const singleClickTimeout = ref(null);

const handleClick = (event) => {
  router.push({
    name: 'ProPlanDetails',
    params: { id: props.record.id },
  });
};

const handleRightClick = (event) => {
  if (props.record.status === 101) {
    isEdit.value = true;
    rowNameValue.value = props.record.name;
  } else {
    isEdit.value = false;
  }
};
onMounted(() => {
  rowNameValue.value = props.record.name;
});
// 组件卸载时清除未执行的单击事件
onUnmounted(() => {
  if (singleClickTimeout.value) {
    clearTimeout(singleClickTimeout.value);
  }
});
function getPopupContainer(): Element {
  return document.querySelector('.plan-container');
}
function pressEnter(value: []) {
  emit('change', rowNameValue.value, changeFocus);
}
function changeFocus(val) {
  if (isFocus.value && val) {
    isFocus.value = false;
    return;
  }
  isFocus.value = val;
  // emit('change', rowNameValue.value);
}

</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="record.name"
      @mouseover="handleRightClick"
    >
      <!--计划图标-->
      <Icon
        v-if="record['nodeType']==='plan'"
        icon="orion-icon-carryout"
        class="primary-color"
        size="16"
      />
      <!--里程碑图标-->
      <Icon
        v-if="record['nodeType']==='milestone'"
        color="#FFB118"
        size="16"
        icon="orion-icon-flag"
      />
      <ATooltip :getPopupContainer="getPopupContainer">
        <template #title>
          <div class="pre-post-tooltip">
            <template v-if="record?.['schemePrePostVOList']?.length">
              <span>前置任务：</span>
              <span
                v-for="(item,index) in record?.['schemePrePostVOList']"
                :key="item.id"
              >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
            </template>

            <template v-if="record?.['schemePostVOList']?.length">
              <span>后置任务：</span>
              <span
                v-for="(item,index) in record?.['schemePostVOList']"
                :key="item.id"
              >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
            </template>
          </div>
        </template>
        <!--前后置计划图标-->
        <Icon
          v-if="record?.['schemePostVOList']?.length || record?.['schemePrePostVOList']?.length"
          color="#D50072"
          icon="fa-sort-amount-asc"
        />
      </ATooltip>
      <span
        class="ml10"
      >{{ record.name }}</span>
    </div>
    <div
      v-else
      class="row-name-value"
    >
      <AInput
        v-model:value="rowNameValue"
        @pressEnter="pressEnter"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
