package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectToProduct DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
@ApiModel(value = "ProjectToProductDTO对象", description = "项目产品关联关系表")
@Data
@ExcelIgnoreUnannotated
public class ProjectToProductDTO extends ObjectDTO implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 0)
    private String projectId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @ExcelProperty(value = "产品id ", index = 1)
    private String productId;


}
