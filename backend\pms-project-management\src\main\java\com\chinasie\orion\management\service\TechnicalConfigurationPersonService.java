package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.TechnicalConfigurationPersonDTO;
import com.chinasie.orion.management.domain.entity.TechnicalConfigurationPerson;
import com.chinasie.orion.management.domain.vo.TechnicalConfigurationPersonVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * TechnicalConfigurationPerson 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
public interface TechnicalConfigurationPersonService extends OrionBaseService<TechnicalConfigurationPerson> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TechnicalConfigurationPersonVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param technicalConfigurationPersonDTO
     */
    String create(TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param technicalConfigurationPersonDTO
     */
    Boolean edit(TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TechnicalConfigurationPersonVO> pages(Page<TechnicalConfigurationPersonDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<TechnicalConfigurationPersonDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TechnicalConfigurationPersonVO> vos) throws Exception;
}
