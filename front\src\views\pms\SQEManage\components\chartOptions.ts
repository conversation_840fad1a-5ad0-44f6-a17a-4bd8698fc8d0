import { EChartsOption } from 'echarts';

export function getOption(data: Array<{
    name: string,
    value: number
}>, color: string): EChartsOption | object {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: 50,
      right: 20,
      bottom: 50,
      top: 20,
    },
    xAxis: {
      type: 'category',
      data: data.map((item) => item.name),
      axisLabel: {
        width: 50,
        interval: 0,
        overflow: 'truncate',
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: data.map((item) => item.value),
        type: 'bar',
        barWidth: '20%',
        itemStyle: {
          color,
        },
      },
    ],
  };
}
