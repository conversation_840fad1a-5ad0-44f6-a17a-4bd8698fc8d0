package com.chinasie.orion.domain.dto.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectSchemeStatisticsDTO对象", description = "项目计划统计")
public class ProjectSchemeStatisticsDTO {
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "计划类型")
    private String nodeType;
    @ApiModelProperty(value = "负责人id")
    private String rspUser;
    @ApiModelProperty(value = "时间类型")
    private String timeType;
    @ApiModelProperty(value = "新增时间")
    private Date createTime;
    @ApiModelProperty(value = "完成时间")
    private Date completeTime;
    @ApiModelProperty(value = "状态")
    private Integer status;
}
