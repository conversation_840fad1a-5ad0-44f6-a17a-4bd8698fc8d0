<script setup lang="ts">
import { OrionTable, BasicButton } from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed, inject,
} from 'vue';
import { openFormModal } from './utils';
import { useRouter } from 'vue-router';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectId:string = inject('projectId') || '';
const tableOptions = {
  showToolButton: false,
  smallSearchField: ['name'],
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  api: (params: Record<string, any>) => new Api(`/pms/projectPerformance/pages/${projectId}`).fetch(params, '', 'POST'),
  columns: [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '评价类型',
      dataIndex: 'typeName',
    },
    {
      title: '评价人',
      dataIndex: 'ownerName',
    },
    {
      title: '评价总分',
      dataIndex: 'totalScore',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      slots: { customRender: 'modifyTime' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      onClick(record: Record<string, any>) {
        openFormModal(projectId, record, updateTable);
      },
    },
    {
      text: '删除',
      modal: (record: Record<string, any>) => deleteApi([record?.id]),
    },
  ],
};

onMounted(() => {

});

const toolButtons = computed(() => [
  {
    event: 'add',
    text: '新建',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },

  {
    event: 'delete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
  },
]);
function handleToolButton(operation: string) {
  switch (operation) {
    case 'add':
      openFormModal(projectId, {}, updateTable, null);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}
function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectPerformance').fetch(
      ids,
      '',
      'DELETE',
    ).then(() => {
      resolve('');
      updateTable();
    }).catch((err) => {
      reject(err);
    });
  });
}
async function updateTable() {
  await tableRef.value?.reload();
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-for="button in toolButtons"
        v-bind="button"
        :key="button.event"
        @click="handleToolButton(button.event)"
      >
        {{ button.text }}
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>