package com.chinasie.orion.msc;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.dto.ContractMainDTO;
import com.chinasie.orion.domain.entity.ContractMain;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ContractMainMessageHandler implements MscBuildHandler<ContractMain> {

    @Override
    public SendMessageDTO buildMsc(ContractMain contractMain, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$contractName$", contractMain.getContractName());
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(contractMain.getContractNumber()+"_"+contractMain.getDeptId())
                // .messageUrl("/pas/MarketDemandManagementDetails/" + projectOrderDTO.getOrderNumber())
                .messageUrl("/pms/techCfgContractManage")
                .messageUrlName("技术配置合同管理")
                .senderTime(new Date())
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList((List<String>) objects[0])
                .platformId(contractMain.getPlatformId())
                .orgId(CurrentUserHelper.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgHandlerConstant.CONTRACT_MAIN;
    }
}
