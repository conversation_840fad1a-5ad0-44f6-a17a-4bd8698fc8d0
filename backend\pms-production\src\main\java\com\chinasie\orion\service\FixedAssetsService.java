package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.FixedAssetsDTO;
import com.chinasie.orion.domain.dto.FixedAssetsFileDTO;
import com.chinasie.orion.domain.entity.FixedAssets;

import java.lang.String;
import java.util.Date;
import java.util.List;

import com.chinasie.orion.domain.vo.FixedAssetsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * NcfFormGVWdVZFnw 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
public interface FixedAssetsService extends OrionBaseService<FixedAssets> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    FixedAssetsVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormGVWdVZFnwDTO
     */
    String create(FixedAssetsDTO ncfFormGVWdVZFnwDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormGVWdVZFnwDTO
     */
    Boolean edit(FixedAssetsDTO ncfFormGVWdVZFnwDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<FixedAssetsVO> pages(Page<FixedAssetsDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<FixedAssetsVO> vos) throws Exception;

    /**
     *  通过编码获取详细信息
     * @param number
     * @return
     */
    FixedAssetsVO detailByNumber(String number);

    /**
     *  修改是否超期
     * @param number
     * @param assetCode
     * @param overDue
     * @param isverifivation
     * @param date
     */
    void updateByNumber(String number, String assetCode, Boolean overDue, Boolean isverifivation, Date date);

    /**
     *  修改是否超期处理
     * @param key
     * @param value
     */
    void updateByList(Boolean key, List<String> value);

    /**
     *  过滤掉数据为空的
     * @param pageRequest
     * @return
     */
    Page<FixedAssetsVO> materialPages(Page<FixedAssetsDTO> pageRequest) throws Exception;

    /**
     * 确认附件修改
     * @param fixedAssetsFileDTO 参数
     * @return 结果
     */
    Boolean uploadFixedAssets(FixedAssetsFileDTO fixedAssetsFileDTO) throws Exception;

    /**
     * 删除附件
     * @param fileIds 文件id列表
     * @return 结果
     */
    Boolean deleteFixedAssetsFile(List<String> fileIds);
}
