package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@ApiModel(value = "ProjectPayDTO对象", description = "项目支出DTO")
@Data
public class ProjectPayDTO {
    @ApiModelProperty(value = "科目名称")
    private String subjectName;


    @ApiModelProperty(value = "承诺金额")
    private BigDecimal promiseAmount;


    @ApiModelProperty(value = "计划金额")
    private BigDecimal planAmount;


    @ApiModelProperty(value = "实际金额")
    private BigDecimal actualAmount;


    @ApiModelProperty(value = "结余金额")
    private BigDecimal balanceAmount;


    @ApiModelProperty(value = "进度")
    private BigDecimal progress;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectNumber;
}
