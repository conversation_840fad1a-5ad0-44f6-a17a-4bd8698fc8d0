package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectDeclareFileInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 18:02:59
 */
@TableName(value = "pms_project_declare_file_info")
@ApiModel(value = "ProjectDeclareFileInfo对象", description = "项目申报文件信息")
@Data
public class ProjectDeclareFileInfo extends ObjectEntity implements Serializable{

    /**
     * 项目申报id
     */
    @ApiModelProperty(value = "项目申报id")
    @TableField(value = "project_declare_id" )
    private String projectDeclareId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type" )
    private String type;

    /**
     * 文件数据id
     */
    @ApiModelProperty(value = "文件数据id")
    @TableField(value = "file_data_id" )
    private String fileDataId;

}
