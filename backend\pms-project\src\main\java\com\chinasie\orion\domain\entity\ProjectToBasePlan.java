package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * NewProjectToBasePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
@TableName(value = "pms_project_to_base_plan")
@ApiModel(value = "NewProjectToBasePlan对象", description = "项目和综合计划的关系表（1;N）")
@Data
public class ProjectToBasePlan extends ObjectEntity implements Serializable {


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 综合计划ID
     */
    @ApiModelProperty(value = "综合计划ID")
    @TableField(value = "base_plan_id")
    private String basePlanId;

    /**
     * 综合计划编号
     */
    @ApiModelProperty(value = "综合计划编号")
    @TableField(value = "base_plan_number")
    private String basePlanNumber;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    @TableField(value = "source_type")
    private String sourceType;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 关联类型(0:主动,1:被动)
     */
    @ApiModelProperty(value = "关联类型(0:主动,1:被动)")
    @TableField(value = "relation_type")
    private String relationType;

    @ApiModelProperty(value = "是否例行")
    @TableField(value = "routine")
    private Boolean routine;
}
