<?xml version="1.0" encoding="utf-8"?>
<!--
   Copyright 2013 <PERSON>.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>com.github.stephenc.jcip</groupId>
  <artifactId>jcip-annotations</artifactId>
  <version>1.0-1</version>

  <name>JCIP Annotations under Apache License</name>
  <description>
    A clean room implementation of the JCIP Annotations based entirely on the specification provided by the javadocs.
  </description>
  <url>http://stephenc.github.com/jcip-annotations</url>
  <inceptionYear>2013</inceptionYear>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>stephenc</id>
      <name>Stephen Connolly</name>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>

  <prerequisites>
    <maven>2.2.1</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:git://github.com/stephenc/jcip-annotations.git</connection>
    <developerConnection>scm:git:**************:stephenc/jcip-annotations.git</developerConnection>
    <url>http://github.com/stephenc/jcip-annotations/tree/master/</url>
    <tag>jcip-annotations-1.0-1</tag>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>http://github.com/stephenc/jcip-annotations/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github.com</id>
      <url>gitsite:**************/stephenc/jcip-annotations.git</url>
    </site>
  </distributionManagement>

  <properties>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <project.build.outputEncoding>UTF-8</project.build.outputEncoding>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.2</version>
          <dependencies>
            <dependency>
              <groupId>com.github.stephenc.wagon</groupId>
              <artifactId>wagon-gitsite</artifactId>
              <version>0.4.1</version>
            </dependency>
            <dependency>
              <groupId>org.apache.maven.doxia</groupId>
              <artifactId>doxia-module-markdown</artifactId>
              <version>1.3</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.13</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.6</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.3</version>
      </plugin>
    </plugins>
  </reporting>

</project>
