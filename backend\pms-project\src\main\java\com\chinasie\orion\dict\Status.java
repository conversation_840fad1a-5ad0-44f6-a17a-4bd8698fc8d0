package com.chinasie.orion.dict;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Status
 *
 * @author: yangFy
 * @date: 2023/4/19 17:44
 * @description
 * <p>
 * 项目计划状态
 * </p>
 */
@Getter
public enum Status {
    ICON_SCHEME(100001, "计划"),
    ICON_MILESTONE(100002, "里程碑"),
    CIRCUMSTANCE_NORMAL(100011, "正常"),
    CIRCUMSTANCE_NEAR(100012, "已临期"),
    CIRCUMSTANCE_OVERD(100013, "已逾期"),
    CIRCUMSTANCE_FALLBACK(100014, "计划退回"),
    CIRCUMSTANCE_COMPLETE_NORMAL(100015, "正常完成"),
    CIRCUMSTANCE_COMPLETE_OVERD(100016, "逾期完成"),
    CIRCUMSTANCE_WAIT(100017, "待处理"),
    ADJUSTMENT_APPLICATION(100018, "调整申请中"),
    WAIT_COMPLETE_AFFIRM(100019, "待完成确认"),
    ISSUE_APPROVAL(100020, "下发审批中"),
    AFFIRM_APPROVAL(100021, "完成审批中"),
    SUSPEND_APPROVAL(100022, "暂停审批中"),
    TERMINATION_APPROVAL(100023, "终止审批中"),
    START_APPROVAL(100024, "启动审批中"),
    COMPLETE_APPROVAL(100025, "执行完成审批中"),
    LEVEL_1(100021, "1级"),
    LEVEL_2(100022, "2级"),
    LEVEL_3(100023, "3级"),
    LEVEL_4(100024, "4级"),
    LEVEL_5(100025, "5级"),
    LEVEL_6(100026, "6级"),
    LEVEL_7(100027, "7级"),
    LEVEL_8(100028, "8级"),
    LEVEL_9(100029, "9级"),
    LEVEL_10(100030, "10级"),
    PENDING(101, "待发布"),
    FALLBACK(120, "计划退回"),
    PUBLISHED(130, "已下发"),
    EXECUTING(140, "执行中"),
    FINISHED(111, "已完成"),
    SUSPEND(150, "计划暂停"),
    TERMINATION(160, "计划终止"),
    CONFIRMED(121, "待确认"),
    CHANGE(110, "变更中"),
    SCHEME_PRE(100061, "前置关系"),
    SCHEME_POST(100062, "后置关系"),
    CREATED(1, "已创建"),
    AUDIT(2, "待审核"),
    TO_BE_ISSUED(3, "带待发"),
    UNDER_WAY(4, "进行中"),
    COMPLETED(5, "已完成"),
    OVERDUE(6, "逾期");
    private Integer code;
    private String name;

    Status(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取名称
     *
     * @param code
     * @return
     */
    public static String codeMapping(Integer code) {
        if (0 == code) {
            return "";
        }
        Status status = Arrays.stream(Status.values()).filter(sta -> sta.getCode().equals(code)).findFirst().orElse(null);
        if (status == null) {
            return "";
        }
        return status.getName();
    }

    public static final Map<Integer, Status> schemeStatusMap = new HashMap<>();


    /**
     * 逾期-OVERDUE
     * 临期
     * 正常
     * 已创建（待下发）-CREATED
     * 已下发（进行中）-UNDER_WAY
     * 已完成- COMPLETED
     */
    static {
        schemeStatusMap.put(PENDING.getCode(), CREATED);
        schemeStatusMap.put(PUBLISHED.getCode(), UNDER_WAY);
        schemeStatusMap.put(FINISHED.getCode(), COMPLETED);
        schemeStatusMap.put(CIRCUMSTANCE_NEAR.getCode(), CIRCUMSTANCE_NEAR);
        schemeStatusMap.put(CIRCUMSTANCE_OVERD.getCode(), OVERDUE);
    }

    public static Status getStatus(Integer status) {
        return schemeStatusMap.get(status);
    }

    public static final Map<Integer, Integer> levelMap = new HashMap<>();


    public static final Map<Integer, String> levelDescMap = new HashMap<>();

    static {
        levelMap.put(0, LEVEL_1.getCode());
        levelMap.put(1, LEVEL_2.getCode());
        levelMap.put(2, LEVEL_3.getCode());
        levelMap.put(3, LEVEL_4.getCode());
        levelMap.put(4, LEVEL_5.getCode());
        levelMap.put(5, LEVEL_6.getCode());
        levelMap.put(6, LEVEL_7.getCode());
        levelMap.put(7, LEVEL_8.getCode());
        levelMap.put(8, LEVEL_9.getCode());
        levelMap.put(9, LEVEL_10.getCode());


        Status[] statuses = Status.values();
        for (Status status : statuses) {
            levelDescMap.put(status.getCode(),status.getName());
        }
    }
}
