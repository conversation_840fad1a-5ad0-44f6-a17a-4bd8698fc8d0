package org.jeecg.modules.demo.dkm.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jeecg.modules.demo.dkm.util.StringCheckUtil;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 字段缺失检查配置类
 * 用于配置不同字段的检查条件、表头和行数据生成逻辑
 *
 * <AUTHOR>
 * @date 2025-4-16
 * @param <T> 数据对象类型
 */
@Getter
@AllArgsConstructor
public class FieldCheckConfig<T> {
    /** 字段名称（检查项） */
    private final String fieldName;
    /** 数据缺失判断条件 */
    private final Predicate<T> missingCondition;
    /** 表头信息 */
    private final List<String> headers;
    /** 行数据生成器 */
    private final Function<T, List<String>> rowGenerator;
    
    /**
     * 创建字符串字段为空的检查条件
     *
     * @param fieldGetter 字段获取器
     * @return 判断条件
     * @param <E> 数据对象类型
     */
    public static <E> Predicate<E> createEmptyStringCondition(Function<E, String> fieldGetter) {
        return p -> StringCheckUtil.isEmpty(fieldGetter.apply(p));
    }
    
    /**
     * 创建行数据生成器
     *
     * @param fieldGetters 字段获取器数组
     * @return 行数据生成器
     * @param <E> 数据对象类型
     */
    @SafeVarargs
    public static <E> Function<E, List<String>> createRowGenerator(Function<E, String>... fieldGetters) {
        return p -> {
            String[] values = new String[fieldGetters.length];
            for (int i = 0; i < fieldGetters.length; i++) {
                values[i] = StringCheckUtil.defaultIfBlank(fieldGetters[i].apply(p), "");
            }
            return Arrays.asList(values);
        };
    }
    
    /**
     * 创建字段缺失检查配置
     *
     * @param fieldName 字段名称
     * @param fieldGetter 字段获取器
     * @param headers 表头信息
     * @param rowFieldGetters 行数据字段获取器数组
     * @return 字段缺失检查配置
     * @param <E> 数据对象类型
     */
    @SafeVarargs
    public static <E> FieldCheckConfig<E> create(
            String fieldName,
            Function<E, String> fieldGetter,
            List<String> headers,
            Function<E, String>... rowFieldGetters) {
        return new FieldCheckConfig<>(
                fieldName,
                createEmptyStringCondition(fieldGetter),
                headers,
                createRowGenerator(rowFieldGetters)
        );
    }
} 