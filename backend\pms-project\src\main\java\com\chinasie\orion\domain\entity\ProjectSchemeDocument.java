package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeDocument Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-26 13:33:08
 */
@TableName(value = "pms_project_scheme_document")
@ApiModel(value = "ProjectSchemeDocument对象", description = "项目计划关联文档")
@Data
public class ProjectSchemeDocument extends ObjectEntity implements Serializable{

    /**
     * res文件Id
     */
    @ApiModelProperty(value = "res文件Id")
    @TableField(value = "file_id" )
    private String fileId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    @TableField(value = "project_scheme_id" )
    private String projectSchemeId;
}
