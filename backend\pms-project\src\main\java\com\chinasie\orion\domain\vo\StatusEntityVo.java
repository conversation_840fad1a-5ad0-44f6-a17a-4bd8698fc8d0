package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/11/13:55
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "StatusEntityVo对象", description = "状态")
public class StatusEntityVo implements Serializable {
    @ApiModelProperty(value = "状态名称")
    public  String name;
    @ApiModelProperty(value = "转态值")
    public Integer value;
}
