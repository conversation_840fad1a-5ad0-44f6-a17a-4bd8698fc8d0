package com.chinasie.orion.domain.entity.reporting;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectDailyStatementContent Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@TableName(value = "pms_project_daily_statement_content")
@ApiModel(value = "ProjectDailyStatementContent对象", description = "日报内容表")
@Data
public class ProjectDailyStatementContent extends ObjectEntity implements Serializable {

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @TableField(value = "content")
    private String content;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "task_time")
    private BigDecimal taskTime;

    /**
     * 是否计划内
     */
    @ApiModelProperty(value = "是否计划内")
    @TableField(value = "the_plan")
    private Integer thePlan;

    /**
     * 关联关系
     */
    @ApiModelProperty(value = "关联关系")
    @TableField(value = "relationship")
    private String relationship;

    /**
     * 关联日报
     */
    @ApiModelProperty(value = "关联日报")
    @TableField(value = "daily_statement_id")
    private String dailyStatementId;


    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型")
    @TableField(value = "relation_type")
    private String relationType;

}
