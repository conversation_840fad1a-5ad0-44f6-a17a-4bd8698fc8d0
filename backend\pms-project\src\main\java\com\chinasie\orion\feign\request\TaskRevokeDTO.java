package com.chinasie.orion.feign.request;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * @author: yk
 * @date: 2023/9/19 11:28
 * @description:
 */
@Data
@ApiModel("撤销")
public class TaskRevokeDTO {
    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    @NotBlank(message = "流程实例id不能为空")
    private String processInstanceId;

    @ApiModelProperty("表单数据")
    private JSONObject formData;

    @ApiModelProperty("变量")
    private Map<String, Object> variables;

    @ApiModelProperty("审批意见")
    private String comment;

}
