<template>
  <BasicModal
    v-bind="$attrs"
    :width="500"
    :min-height="300"
    :use-wrapper="false"
    title="添加页面"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="ok"
  >
    <div class="select-page-wrap">
      <div class="filter-input-wrap">
        <AInputSearch
          v-model:value="filterInputValue"
          placeholder="请输入内容筛选"
        />
      </div>
      <div class="page-list">
        <template v-if="pageList.length">
          <div
            v-for="(item, index) in pageList.filter((filterItem) => {
              return !filterInputValue ? true : filterItem.name.includes(filterInputValue);
            })"
            :key="item.id + index"
            class="page-item"
            @click="checkPageItem(item)"
          >
            <ACheckbox v-model:checked="item.isCheck" /><span class="ml5">{{
              item.name
            }}</span>
          </div>
        </template>
        <div
          v-else
          class="h-full flex flex-pac"
        >
          <Empty description="没有可添加的页面" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts">
import {
  defineComponent, ref, nextTick, reactive, toRefs,
} from 'vue';
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import {
  message, Input, Checkbox, Empty,
} from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'AddPageModal',
  components: {
    BasicModal,
    AInputSearch: Input.Search,
    ACheckbox: Checkbox,
    Empty,
  },
  emits: ['reload'],
  setup(_, { emit }) {
    const buttonLoading = ref(false);
    const state = reactive({
      roleId: '', // 角色ID
      filterInputValue: '',
      pageList: [],
      addedPageList: [], // 已添加的页面
    });
      // useModal
    const [modalRegister, { closeModal, changeOkLoading, changeLoading }] = useModalInner(
      ({ addedPageList, roleId }) => {
        state.addedPageList = addedPageList;
        state.roleId = roleId;
        init();
      },
    );

    async function init() {
      state.pageList = (await loadPageList()).filter((item) => !state.addedPageList.find((addedItem) => addedItem.id === item.id));
    }

    function loadPageList() {
      changeLoading(true);
      return new Api('/pmi/page-manage/list')
        .fetch({}, '', 'GET')
        .then((data) => (Array.isArray(data)
          ? data.map((item) => ({
            ...item,
            isCheck: false,
          }))
          : []))
        .finally(() => {
          changeLoading(false);
        });
    }

    return {
      ...toRefs(state),
      modalRegister,
      buttonLoading,
      visibleChange(visible) {
        if (!visible) {
          state.pageList = [];
          state.filterInputValue = '';
        } else {
        }
      },
      cancel() {
        closeModal();
      },
      reset() {},
      ok() {
        const params = state.pageList
          .filter((item) => item.isCheck)
          .map((item) => ({
            pageId: item.id,
            subjectId: state.roleId,
            subjectType: '10',
          }));

        changeOkLoading(true);
        new Api('/pmi/business-privilege/subject-policy')
          .fetch(params, '', 'POST')
          .then(() => {
            message.success('添加成功');
            emit('reload');
            closeModal();
          })
          .finally(() => {
            changeOkLoading(false);
          });
      },
      // 选择页面
      checkPageItem(pageItem) {
        pageItem.isCheck = !pageItem.isCheck;
      },
    };
  },
});
</script>

<style scoped lang="less">
  .select-page-wrap {
    border: 1px solid ~`getPrefixVar('border-color-base')`;
  }

  .filter-input-wrap {
    padding: 10px;
  }

  .page-list {
    height: 240px;
    overflow-y: auto;

    > .page-item {
      height: 36px;
      line-height: 36px;
      padding: 0 10px;
      cursor: pointer;
      transition: 0.2s;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 1;
      }

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
</style>
