<script setup lang="ts">
import Api from '/@/api';
import {
  cloneDeep, get as loadGet, get, isEmpty,
} from 'lodash-es';
import { h, onMounted, ref } from 'vue';
import {
  Layout, BasicTitle1, DataStatusTag, OrionTable, randomString,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

const router = useRouter();
const actionItemManageStatistics = ref([
  '行动项合计0条',
  '在办0条',
  '已关闭0条',
]);
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '大修轮次',
      dataIndex: 'repairRound',
      width: 100,
    },
    {
      title: '维度',
      dataIndex: 'dimensionDictName',
      width: 100,
    },
    {
      title: '问题描述',
      dataIndex: 'problemDesc',
      customCell({ text, record }) {
        return {
          style: {
            color: '#5172dc',
            cursor: 'pointer',
          },
          onClick: () => {
            router.push({
              name: 'ActionItemManageDetail',
              params: {
                id: record.id,
              },
              query: {
                query: randomString(),
                repairId: record.repairId,
              },
            });
          },
        };
      },
    },
    {
      title: '责任单位',
      dataIndex: 'rspDeptNames',
      width: 200,
    },
    {
      title: '责任人',
      dataIndex: 'rspUserNames',
      width: 200,
    },
    {
      title: '完成时限',
      dataIndex: 'finishDeadline',
      width: 100,
    },
    {
      title: '行动验证人',
      dataIndex: 'verifierName',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      width: 100,
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/prodActionItem/list').fetch({}, '', 'POST'),
};

async function getActionStatistics() {
  try {
    const result = await new Api('/pms/prodActionItem/count').fetch({}, '', 'GET');
    actionItemManageStatistics.value = [
      `行动项合计${get(result, 'totalCount', 0) ?? 0}条`,
      `在办${get(result, 'unClosedCount', 0) ?? 0}条`,
      `已关闭${get(result, 'closedCount', 0) ?? 0}条`,
    ];
  } catch (e) {

  }
}

onMounted(() => {
  getActionStatistics();
});
</script>

<template>
  <BasicTitle1>
    <div class="custom-title">
      <h2>行动项反馈</h2>
      （
      <a-space :size="0">
        {{ actionItemManageStatistics.join("，") }}
      </a-space>
      ）
    </div>
  </BasicTitle1>
  <div class="action-item-feedback">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.custom-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: normal;

  h2 {
    padding-bottom: 0;
    margin-bottom: 0;
    font-weight: 700;
    line-height: 1;
    font-size: 16px;
    margin-right: 6px;
  }
}

.action-item-feedback {
  height: 500px;
  overflow: hidden;
}
</style>