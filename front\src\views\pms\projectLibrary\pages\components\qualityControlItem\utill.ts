export const getExecuteItem = (text) => {
  let color = '';
  let child = '--';
  if (text === 0) {
    color = '#ff9900';
    child = '待执行';
  } else if (text === 1) {
    color = '#73d13d ';
    child = '正在执行';
  } else if (text === 2) {
    color = '#ffcc00';
    child = '已完成';
  }
  return {
    color,
    child,
  };
};

// 格式化置顶
export const formatTreeTop = (list, ids = []) => list.map((item) => {
  let obj = item;
  if (ids.includes(obj.id)) {
    obj.key = `${obj.id}Top`;
  } else {
    obj.key = obj.id;
    ids.push(obj.id);
  }
  if (item.children && item.children.length === 0) {
    delete obj.children;
  } else if (item.children && item.children.length) {
    return {
      ...obj,
      children: formatTreeTop(item.children, ids),
    };
  }
  return obj;
});

// 格式化序号
export const formatTreeKey = (list, ids = [], str?) => list.map((item, index) => {
  let obj = item;
  if (ids.map((v) => v.id)
    .includes(obj.id)) {
    obj.index = ids.filter((v) => v.id === obj.id)[0].index;
  } else {
    obj.index = str + (index + 1);
    ids.push(obj);
  }

  if (item.children && item.children.length === 0) {
    delete obj.children;
  } else if (item.children && item.children.length) {
    return {
      ...obj,
      children: formatTreeKey(item.children, ids, `${obj.index}.`),
    };
  }
  return obj;
});

export const getDefaultExpandedRowKeys = (data) => {
  let rowKeys = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.children && item.children.length > 0) {
      rowKeys.push(item.id);
      let rowKeys1 = getDefaultExpandedRowKeys(item.children);
      if (rowKeys1) {
        rowKeys = rowKeys.concat(rowKeys1);
      }
    }
  }
  return rowKeys;
};
