package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * IndicatorLibrary VO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 15:13:04
 */
@ApiModel(value = "IndicatorLibraryVO对象", description = "绩效管理指标库")
@Data
public class IndicatorLibraryVO extends ObjectVO implements Serializable{

            /**
         * 绩效评分标准
         */
        @ApiModelProperty(value = "绩效评分标准")
        private String scoreStandard;

        /**
         * 该指标默认权重
         */
        @ApiModelProperty(value = "该指标默认权重")
        private BigDecimal weight;

        /**
         * 绩效指标名称
         */
        @ApiModelProperty(value = "绩效指标名称")
        private String name;

    }
