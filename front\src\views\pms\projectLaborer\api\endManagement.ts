import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 结项分页 */
  getPage = 'post-project/getPage',
  /* 需求简单分页 */
  endType = 'post-project/type',

  /* add */
  save = 'post-project/save',
  /* edit */
  edit = 'post-project/edit',
  /* 删除 */
  remove = 'post-project/removeBatch',
  /* edit */
  getDetails = 'post-project/detail',
  /* 详情-列表 */
  getListDetails = 'document/getList',
  /* 详情-删除列表 */
  removeBatchDetails = 'document/removeBatch',
  /* 获取项目信息 */
  getProject = 'post-project/getProject'
  //   /* 需求来源 */
  //   demandSource = 'demand-management/demandSource',
  //   /* 需求类型 */
  //   demandType = 'demand-management/demandType',
  //   /* 优先级 */
  //   priorityLevel = 'demand-management/priorityLevel',
  //   /* 添加 */
  //   addDemand = 'demand-management/save',
  //   /* 编辑 */
  //   editDemand = 'demand-management/edit',
  //   /* 编辑 */
  //   deletDemand = 'demand-management/removeBatch',
  //   /* 状态 */
  //   statusDemand = 'demand-management/status',
  //   /* 搜索 */
  //   queryDemand = 'demand-management/getPage'
}

/**
 * @description: 需求管理
 */
// 获取project根信息
export function getProjectApi(params) {
  return new Api(base).fetch('', `${zApi.getProject}/${params}`, 'GET');
}
// 分页
export function getPageApi(params) {
  return new Api(base).fetch(params, `${zApi.getPage}/`, 'POST');
}
// 结项
export function endTypeApi() {
  return new Api(base).fetch('', `${zApi.endType}/`, 'GET');
}
// 添加
export function saveApi(params) {
  return new Api(base).fetch(params, `${zApi.save}/`, 'POST');
}
// 编辑
export function editApi(params) {
  return new Api(base).fetch(params, `${zApi.edit}/`, 'PUT');
}
// 删除
export function removeApi(params) {
  return new Api(base).fetch(params, `${zApi.remove}/`, 'DELETE');
}
// 删除--详情结项列表
export function removeBatchDetailsApi(params) {
  return new Api(base).fetch(params, `${zApi.removeBatchDetails}`, 'DELETE');
}
// 详情
export function getDetailsApi(params) {
  return new Api(base).fetch('', `${zApi.getDetails}/${params}/`, 'GET');
}
// 列表
export function getListDetailsApi(params, obj = {}) {
  return new Api(base).fetch(obj, `${zApi.getListDetails}/${params}/`, 'POST');
}
// // 需求来源
// export function demandSourceApi() {
//   return new Api(base).fetch('', `${zApi.demandSource}/`, 'GET');
// }
// // 需求类型
// export function demandTypeApi() {
//   return new Api(base).fetch('', `${zApi.demandType}/`, 'GET');
// }
// // 优先级
// export function priorityLevelApi() {
//   return new Api(base).fetch('', `${zApi.priorityLevel}/`, 'GET');
// }

// // 状态
// export function statusDemandApi() {
//   return new Api(base).fetch('', `${zApi.statusDemand}/`, 'GET');
// }
// // 搜索
// export function queryDemandApi(params) {
//   return new Api(base).fetch(params, `${zApi.queryDemand}/`, 'POST');
// }
