<script setup lang="ts">
import {
  BasicButton, IOpenBasicSelectModalProps, openBasicSelectModal, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, h, inject, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { get as _get, map as _mao } from 'lodash-es';
import { message, Modal, Select as ASelect } from 'ant-design-vue';

const props = defineProps({
  record: {
    type: Object,
    default: () => {},
  },
});
const detailsData = computed(() => _get(props.record, 'parent', {}));

const columnLevelOption = [
  {
    label: '第一层',
    value: '1',
  },
  {
    label: '第二层',
    value: '2',
  },
  {
    label: '第三层',
    value: '3',
  },
];
const tableRef = ref();
const selectedKeys: Ref<string[]> = ref([]);
const keyword: Ref<string> = ref('');
const tableOptions = {
  rowSelection: {
    onChange(keys: string[]) {
      selectedKeys.value = keys;
    },
  },
  api: (params: any) => {
    delete params.searchConditions;
    return new Api('/pms/majorRepairPlanRole/page').fetch({
      ...params,
      query: {
        majorRepairTurn: detailsData.value.repairRound,
        roleName: unref(keyword),
      },
    }, '', 'POST');
  },
  showToolButton: false,
  pagination: {
    pageSize: 5,
  },
  scroll: {
    y: 220,
  },
  columns: [
    {
      title: '名称',
      dataIndex: 'roleName',
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
    },
    {
      title: '组织层级',
      dataIndex: 'roleLevel',
      customRender({ text, record }) {
        let focusable = false;
        // return !focusable && text
        //   ? h('span', {
        //     onClick() {
        //       focusable = true;
        //     },
        //   }, _get([{}, ...columnLevelOption], `${text}.label`))
        //   : h(ASelect, {
        //     style: {
        //       width: '90px',
        //     },
        //     options: columnLevelOption,
        //     onChange(val) {
        //       text = val;
        //       updateRoleLevel(val, record);
        //     },
        //     onBlur() {
        //       focusable = false;
        //     },
        //   });
        return h(ASelect, {
          style: {
            width: '90px',
          },
          value: text,
          options: columnLevelOption,
          onChange(val) {
            text = val;
            updateRoleLevel(val, record);
          },
          onBlur() {
            focusable = false;
          },
        });
      },
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

const toolButton = computed(() => [
  {
    text: '系统添加',
    icon: 'sie-icon-fenpei',
    key: 'add',
  },
  {
    text: '删除',
    icon: 'delete',
    key: 'delete',
    disabled: selectedKeys.value.length === 0,
  },
]);

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openBasicSelectModal(roleModalOptions);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除已选择的角色？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/majorRepairPlanRole/remove').fetch(ids, '', 'DELETE').then(() => {
      message.success('操作成功');
      updateTable();
      resolve(true);
    }).catch(() => {
      resolve(true);
    });
  });
}
async function updateRoleLevel(level, row) {
  try {
    await new Api('/pms/majorRepairPlanRole/edit')
      .fetch({
        majorRepairTurn: detailsData.value.repairRound,
        roleLevel: level,
        roleCode: row.roleCode,
        id: row.id,
      }, '', 'PUT')
      .then((res) => {
        message.success('操作成功');
        updateTable();
      });
  } catch (e) {}
}

const roleModalOptions: IOpenBasicSelectModalProps = {
  title: '添加角色',
  tableColumns: [
    {
      title: '角色编号',
      dataIndex: 'code',
    },
    {
      title: '角色名称',
      dataIndex: 'name',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 100,
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'description',
      width: 100,
    },
  ],
  tableApi: async (params: any) => {
    const obj: Record<string, any> = {
      sysModuleTypeId: 'LEAD',
      status: 1,
    };
    let keyword: string = '';
    params.searchConditions && (keyword = params.searchConditions[0][0].values.join(','));
    const result = await new Api('/pmi/role/list/module/LEAD').fetch(obj, '', 'GET');
    return result?.filter((item) => item.name.includes(keyword));
  },
  onOk(selectedRows: Record<string, any>[]) {
    if (selectedRows.length === 0) {
      message.info('请选择角色');
      return Promise.reject();
    }
    return new Promise((resolve) => {
      new Api('/pms/majorRepairPlanRole/batch').fetch({
        majorRepairTurn: detailsData.value.repairRound,
        roleCodeList: selectedRows.map((item) => item.code),
      }, '', 'POST').then(() => {
        message.success('操作成功');
        updateTable();
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
};
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-model:keyword="keyword"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-for="item in toolButton"
        :key="item.key"
        v-bind="item"
        @click="handleToolButton(item.key)"
      >
        {{ item.text }}
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
