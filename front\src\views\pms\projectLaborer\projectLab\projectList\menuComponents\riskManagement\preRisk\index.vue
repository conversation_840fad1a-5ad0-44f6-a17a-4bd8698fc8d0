<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="selectionChange"
  >
    <template #toolbarLeft>
      <div>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_07_03_01_button_01', powerData) "
          icon="add"
          type="primary"
          @click="addNode"
        >
          创建预案
        </BasicButton>
        <BasicButton
          icon="add"
          @click="synchronizationNode"
        >
          同步策划风险
        </BasicButton>

        <BasicButton
          icon="add"
          @click="quoteRiskPool"
        >
          引用风险库
        </BasicButton>
        <BasicButton
          icon="add"
          :disabled="selectRowKeys.length!==1"
          @click="addRiskNode"
        >
          转为风险
        </BasicButton>
      </div>
    </template>

    <template #modifyTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>
    <template #riskInfluenceName="{ text }">
      <span
        v-if="text"
        :class="{ 'red-text': text === '较大' || text === '大' }"
      >
        {{ text }}
      </span>
    </template>

    <template #riskProbabilityName="{ text }">
      <span
        v-if="text"
        :class="{ 'red-text':text === '极高' || text === '较高' || text === '高' }"
      >
        {{ text }}
      </span>
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>

  <AddTableNode
    @register="register"
    @update="updateData"
  />
  <AddTableRiskNode @register="registerRisk" />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, useDrawer, openModal,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import AddTableNode from './modal/AddTableNode.vue';
import RiskPoolModal from './modal/RiskPoolModal.vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { deletePreRiskPageApi, getPreRiskPageApi } from '/@/views/pms/projectLaborer/api/riskManege';
import AddTableRiskNode from '/@/views/pms/projectLaborer/projectLab/projectList/components/BusinessRisk/components/AddTableNode.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicTableAction,
    BasicButton,
    AddTableNode,
    OrionTable,
    AddTableRiskNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const [registerRisk, { openDrawer: openDrawerRisk }] = useDrawer();
    const formData = inject('formData', {});
    const tableRef = ref();
    const state = reactive({
      powerData: [],
      message: '',
      params: {},
      selectRowKeys: [],
    });
    state.powerData = inject('powerData');

    const router = useRouter();

    const getFormData = async () => {
      tableRef.value?.reload();
    };

    const openDetails = (data) => {
      router.push({
        name: 'PreRiskDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      openDrawer(true, {
        type: 'add',
        projectId: props.id,
      });
    };
    function synchronizationNode() {
      Modal.confirm({
        title: '同步提示',
        content: '是否同步策划风险',
        onOk() {
          new Api('/pms').fetch({}, `risk-plan/syn/approval/riskPlan/${props.id}`, 'POST').then((res) => {
            message.success('同步成功');
            updateData();
          });
        },
      });
    }

    /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
    };

    function updateData() {
      getFormData();
    }

    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|enable|disable',
      rowSelection: {},
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_RISKMANAGE_RISKPLAN',
      api(params) {
        return getPreRiskPageApi(getListParams({
          ...params,
          query: {
            projectId: props.id,
          },
          power: {
            pageCode: 'PMS0004',
            containerCode: 'PMS_XMXQ_container_07_03_02',
            headContainerCode: 'PMS_XMXQ_container_07_03_01',
          },
        }));
      },
      batchDeleteApi({ ids }) {
        return deletePreRiskPageApi(ids)
          .then((res) => {
            message.success('删除成功');
            getFormData();
          });
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: '120px',
        },
        {
          title: '名称',
          dataIndex: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('PMS_XMXQ_container_07_03_02_button_01', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('PMS_XMXQ_container_07_03_02_button_01', state.powerData)) {
                    openDetails(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
          minWidth: 240,
        },

        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          width: '100px',
          align: 'left',
          slots: { customRender: 'riskTypeName' },
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          width: '90px',
          slots: { customRender: 'riskProbabilityName' },
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          width: '80px',
          slots: { customRender: 'riskInfluenceName' },
        },
        {
          title: '修改人',
          dataIndex: 'modifyName',
          width: '130px',
          slots: { customRender: 'modifyName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          width: '150px',
          slots: { customRender: 'modifyTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn:ITableActionItem[] = [
      {
        text: '编辑',
        isShow: computed(() => isPower('PMS_XMXQ_container_07_03_02_button_02', state.powerData)),
        onClick(record:any) {
          openDrawer(true, {
            type: 'edit',
            projectId: props.id,
            id: record.id,
            data: record,
          });
        },
      },
      {
        text: '删除',
        isShow: computed(() => isPower('PMS_XMXQ_container_07_03_02_button_03', state.powerData)),
        modal(record:any) {
          return deletePreRiskPageApi([record.id])
            .then((res) => {
              message.success('删除成功');
              getFormData();
            });
        },
      },
    ];
    function selectionChange(data) {
      state.selectRowKeys = data.keys;
    }
    function addRiskNode() {
      let selectRows = tableRef.value.getSelectRows();
      let drawerData: any = {
        projectId: formData?.value?.id,
        fromObjName: formData?.value?.name,
        modelName: 'pms',
      };
      openDrawerRisk(true, {
        type: 'add',
        pageType: 'change',
        data: drawerData,
        modalData: selectRows[0],
      });
    }
    function quoteRiskPool() {
      const quoteRiskPoolRef = ref();
      openModal({
        title: '引用风险库',
        width: 1100,
        height: 700,
        content(h) {
          return h(RiskPoolModal, {
            ref: quoteRiskPoolRef,
            showLeftTree: true,
          });
        },
        async onOk() {
          let selectRowData = quoteRiskPoolRef.value.getSelectData();
          if (selectRowData.length === 0) {
            message.warning('请选择数据');
            return Promise.reject('');
          }
          let params = selectRowData.map((item) => ({
            name: item.name,
            number: item.number,
            riskType: item.riskType,
            riskProbability: item.riskProbability,
            remark: item.remark,
            solutions: item.solutions,
            riskInfluence: item.riskInfluence,
          }));
          new Api('/pms').fetch(params, `risk-plan/save/batch/${formData.value.id}`, 'POST').then((res) => {
            message.success('引用风险库成功');
            getFormData();
          });
        },
      });
    }

    return {
      ...toRefs(state),
      tableRef,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      successSave,
      isPower,
      register,
      updateData,
      tableOptions,
      actionsBtn,
      selectionChange,
      addRiskNode,
      registerRisk,
      quoteRiskPool,
      synchronizationNode,
    };
  },
});
</script>
<style lang="less" scoped>
  .red-text {
    color: red;
  }
  .blue {
    color: blue;
  }
  .green {
    color: green;
  }
</style>
