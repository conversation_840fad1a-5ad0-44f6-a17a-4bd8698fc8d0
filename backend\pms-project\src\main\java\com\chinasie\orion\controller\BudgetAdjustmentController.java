package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetAdjustmentDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationSaveDTO;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.service.BudgetAdjustmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetAdjustment 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@RestController
@RequestMapping("/budgetAdjustment")
@Api(tags = "预算调整表")
public class BudgetAdjustmentController {

    @Autowired
    private BudgetAdjustmentService budgetAdjustmentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#id}}】", type = "BudgetAdjustment", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<BudgetAdjustmentVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        BudgetAdjustmentVO rsp = budgetAdjustmentService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param budgetAdjustmentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#budgetAdjustmentDTO.name}}】", type = "BudgetAdjustment", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception {
        String rsp = budgetAdjustmentService.create(budgetAdjustmentDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param budgetAdjustmentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#budgetAdjustmentDTO.name}}】", type = "BudgetAdjustment", subType = "编辑", bizNo = "{{#budgetAdjustmentDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception {
        Boolean rsp = budgetAdjustmentService.edit(budgetAdjustmentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "BudgetAdjustment", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = budgetAdjustmentService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "BudgetAdjustment", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = budgetAdjustmentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "BudgetAdjustment", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BudgetAdjustmentVO>> pages(@RequestBody Page<BudgetAdjustmentDTO> pageRequest) throws Exception {
        Page<BudgetAdjustmentVO> rsp = budgetAdjustmentService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预算调整表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "BudgetAdjustment", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        budgetAdjustmentService.downloadExcelTpl(response);
    }

    @ApiOperation("预算调整表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "BudgetAdjustment", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = budgetAdjustmentService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("预算调整表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "BudgetAdjustment", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = budgetAdjustmentService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消预算调整表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "BudgetAdjustment", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = budgetAdjustmentService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("预算调整表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "BudgetAdjustment", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        budgetAdjustmentService.exportByExcel(searchConditions, response);
    }


    /**
     * 查询列表
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "BudgetApplication", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseDTO<List<BudgetAdjustmentVO>> getList(@RequestParam String formId) throws Exception {
        List<BudgetAdjustmentVO> rsp = budgetAdjustmentService.getList(formId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "保存预算调整")
    @RequestMapping(value = "/saveBatch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据", type = "BudgetApplication", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> saveBatchBudgetAdjustment(@RequestBody List<BudgetAdjustmentDTO> budgetAdjustmentDTOS) throws Exception {
        Boolean rsp = budgetAdjustmentService.saveBatchBudgetAdjustment(budgetAdjustmentDTOS);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
}
