<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    title="支付节点审核"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <template #footer>
      <div
        v-if="state.visibleStatus"
        class="flex flex-pe"
      >
        <BasicButton
          :loading="state.btnLoading"
          @click="okHandle('reject')"
        >
          驳回
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="state.btnLoading"
          @click="okHandle('ok')"
        >
          同意
        </BasicButton>
      </div>
    </template>
    <SpinMain :loading="state.loading" />
    <AuditDrawerMain
      v-if="state.visibleStatus"
      ref="mainRef"
      :detail="state.detail"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner, BasicButton } from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref,
} from 'vue';
import { message } from 'ant-design-vue';
import AuditDrawerMain from './AuditDrawerMain.vue';
// import { getOrderNodeDetail, putAuditOk, putReject } from '/@/views/pms/api';

import {
  contractDetailKey,
} from '../../types';
import SpinMain from '/@/views/pms/projectLaborer/components/SpanMain/SpinMain.vue';
import { postContractPayNodeConfirmReject, postContractPayNodeConfirmAgree, getContractPayNodeConfirm } from '/@/views/pms/projectLaborer/projectLab/api';

const mainRef:Ref = ref();
const updateNodePages:(()=>void) = inject('updateNodePages');
const state = reactive({
  visibleStatus: false,
  detail: {
    auditNumber: '',
    id: '',
  },
  contractDetail: inject(contractDetailKey),
  loading: false,
  btnLoading: false,
  currentId: '',
});

const [register, { closeDrawer }] = useDrawerInner(async (record) => {
  state.currentId = record?.id;
  await getDetail(record?.id);
  state.visibleStatus = true;
});

function visibleChange(visible:boolean) {
  !visible && (state.visibleStatus = visible);
}

// 获取订单确认详情
async function getDetail(id) {
  if (!id) return;
  state.loading = true;
  try {
    state.detail = await getContractPayNodeConfirm(id);
  } finally {
    state.loading = false;
  }
}
// 提交
async function okHandle(type) {
  state.btnLoading = true;
  const params = {
    auditNumber: state?.detail?.auditNumber,
    comment: mainRef.value?.state?.comment,
    contractNumber: state.contractDetail?.number,
    fileInfoDTOList: mainRef.value?.state?.fileInfoDTOList,
    id: state?.currentId,
  };
  try {
    await (type === 'ok' ? postContractPayNodeConfirmAgree : postContractPayNodeConfirmReject)(params);
    message.success('操作成功');
    updateNodePages();
  } finally {
    state.btnLoading = false;
    closeDrawer();
  }
}
</script>

<style scoped>

</style>
