package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Builder;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * PreparationInfoPreparation VO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
@ApiModel(value = "PreparationInfoPreparationVO对象", description = "准备信息维护")
@Data
public class PreparationInfoPreparationVO extends  ObjectVO   implements Serializable{

            /**
         * 组织机构
         */
        @ApiModelProperty(value = "组织机构")
        private String orgStructure;


        /**
         * 工单准备
         */
        @ApiModelProperty(value = "工单准备")
        private String jobPrepare;


        /**
         * 重大项目评审
         */
        @ApiModelProperty(value = "重大项目评审")
        private String importantProject;


        /**
         * 参修人员入场
         */
        @ApiModelProperty(value = "参修人员入场")
        private String partUserJoin;


        /**
         * 关注人员面谈
         */
        @ApiModelProperty(value = "关注人员面谈")
        private String likePerson;


        /**
         * 工具入场
         */
        @ApiModelProperty(value = "工具入场")
        private String toolJoin;


        /**
         * 安全质量管理
         */
        @ApiModelProperty(value = "安全质量管理")
        private String safetyQualityEnv;


        /**
         * 工作包
         */
        @ApiModelProperty(value = "工作包")
        private String jobPackage;


        /**
         * 大修前培训
         */
        @ApiModelProperty(value = "大修前培训")
        private String majorTrain;


        /**
         * 交底演练
         */
        @ApiModelProperty(value = "交底演练")
        private String publishDrill;


        /**
         * 后期保障
         */
        @ApiModelProperty(value = "后期保障")
        private String rearSupport;


        /**
         * 大修动员会
         */
        @ApiModelProperty(value = "大修动员会")
        private String majorRally;


        /**
         * 大修准备率
         */
        @ApiModelProperty(value = "大修准备率")
        private String majorPrepareRate;

        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;
    

}
