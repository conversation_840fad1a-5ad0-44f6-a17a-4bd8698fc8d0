package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.SimVO;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.entity.CertificateInfo;
import com.chinasie.orion.domain.dto.CertificateInfoDTO;
import com.chinasie.orion.domain.vo.CertificateInfoVO;
import com.chinasie.orion.service.CertificateInfoService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * CertificateInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@RestController
@RequestMapping("/certificate-info")
@Api(tags = "证书信息")
public class  CertificateInfoController  {

    @Autowired
    private CertificateInfoService certificateInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【证书信息】详情", type = "CertificateInfo", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<CertificateInfoVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        CertificateInfoVO rsp = certificateInfoService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param certificateInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【证书信息】数据【{{#certificateInfoDTO.name}}】", type = "CertificateInfo", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody CertificateInfoDTO certificateInfoDTO) throws Exception {
        String rsp =  certificateInfoService.create(certificateInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param certificateInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#certificateInfoDTO.name}}】", type = "CertificateInfo", subType = "编辑", bizNo = "{{#certificateInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  CertificateInfoDTO certificateInfoDTO) throws Exception {
        Boolean rsp = certificateInfoService.edit(certificateInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【证书信息】数据", type = "CertificateInfo", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = certificateInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【证书信息】数据", type = "CertificateInfo", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = certificateInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【证书信息】分页数据", type = "CertificateInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<CertificateInfoVO>> pages(@RequestBody Page<CertificateInfoDTO> pageRequest) throws Exception {
        Page<CertificateInfoVO> rsp =  certificateInfoService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("证书信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【证书信息】导入模板", type = "CertificateInfo", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        certificateInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("证书信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【证书信息】导入", type = "CertificateInfo", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = certificateInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("证书信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【证书信息】导入", type = "CertificateInfo", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  certificateInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消证书信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【证书信息】导入", type = "CertificateInfo", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  certificateInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("证书信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【证书信息】数据", type = "CertificateInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        certificateInfoService.exportByExcel(searchConditions, response);
    }


    @ApiOperation(value = "获取证书列表（简化）")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【证书信息】简化版分页数据", type = "CertificateInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/simple/list", method = RequestMethod.POST)
    public ResponseDTO<List<SimVO>> list() throws Exception {
        List<SimVO> rsp =  certificateInfoService.allSimpleList();
        return new ResponseDTO<>(rsp);
    }


}
