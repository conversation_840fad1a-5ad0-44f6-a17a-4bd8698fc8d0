package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * EditLog DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:25:09
 */
@ApiModel(value = "EditLogDTO对象", description = "调整记录表")
@Data
@ExcelIgnoreUnannotated
public class EditLogDTO extends  ObjectDTO   implements Serializable{

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @ExcelProperty(value = "计划id ", index = 0)
    private String planId;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 1)
    private String centerName;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @ExcelProperty(value = "成本类型 ", index = 2)
    private String costType;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    @ExcelProperty(value = "成本名称 ", index = 3)
    private String costName;

    /**
     * 调整数量
     */
    @ApiModelProperty(value = "调整数量")
    @ExcelProperty(value = "调整数量 ", index = 4)
    private Integer num;

    /**
     * 调整人id
     */
    @ApiModelProperty(value = "调整人id")
    @ExcelProperty(value = "调整人id ", index = 5)
    private String editPersonId;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @ExcelProperty(value = "提交时间 ", index = 6)
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @ExcelProperty(value = "审批时间 ", index = 7)
    private Date assessmentTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    @ExcelProperty(value = "审批人id ", index = 8)
    private String assessmentPersonId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @ExcelProperty(value = "审批意见 ", index = 9)
    private String assessmentAdvice;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    @ExcelProperty(value = "审批人姓名 ", index = 10)
    private String assessmentPerson;

    /**
     * 调整人姓名
     */
    @ApiModelProperty(value = "调整人姓名")
    @ExcelProperty(value = "调整人姓名 ", index = 11)
    private String editPerson;




}
