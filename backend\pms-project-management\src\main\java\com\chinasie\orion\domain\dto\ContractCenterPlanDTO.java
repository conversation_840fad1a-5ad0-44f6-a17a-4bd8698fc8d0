package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ContractCenterPlan DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@ApiModel(value = "ContractCenterPlanDTO对象", description = "中心用人计划")
@Data
@ExcelIgnoreUnannotated
public class ContractCenterPlanDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    private String contractName;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    @ExcelProperty(value = "用人单位代号 ", index = 2)
    private String centerCode;

    /**
     * 用人单位名称
     */
    @ApiModelProperty(value = "用人单位名称")
    @ExcelProperty(value = "用人单位名称 ", index = 3)
    private String centerName;

    /**
     * 成本类型编号
     */
    @ApiModelProperty(value = "成本类型编号")
    @ExcelProperty(value = "成本类型编号 ", index = 4)
    private String costTypeNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量 ", index = 5)
    private Integer num;

    /**
     * 调整前数量
     */
    @ApiModelProperty(value = "调整前数量")
    private Integer beforeNum;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份 ", index = 6)
    private Date year;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    private String costName;

    @ApiModelProperty(value = "成本类型id")
    private String costTypeId;

    @ApiModelProperty(value = "审核前状态")
    Integer preStatus;
}
