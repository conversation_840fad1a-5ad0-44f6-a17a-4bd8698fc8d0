<template>
  <div>
    <DetailsLayout
      border-bottom
      title="基本信息"
      :data-source="evaluateData.data"
      :list="projectBasicInfo"
    />
    <DetailsLayout
      border-bottom
      title="评价内容"
    >
      <template #title-right>
        <div class="title_right">
          <BasicButton
            size="small"
            icon="add"
            type="primary"
            @click="openAll"
          >
            全部展开
          </BasicButton>
          <BasicButton
            size="small"
            icon="orion-icon-minus"
            @click="closeAll"
          >
            全部收起
          </BasicButton>
        </div>
      </template>

      <div class="evaluate_panel">
        <Collapse
          v-model:activeKey="activeKey"
          :bordered="false"
          ghost
          @change="changeCollapse"
        >
          <CollapsePanel
            v-for="(item,index) in projectEvaluationList "
            :key="index"
            :style="customStyle"
          >
            <template #header>
              <div class="header_cont">
                <div class="header_cont_tit">
                  {{ item.outcomeEvaluation }}
                </div>
                <div class="header_cont_theme">
                  <span class="Brief">
                    {{ item.evaluateValues }}
                  </span>
                </div>
                <div
                  class="header_cont_score"
                  @click.stop=""
                >
                  <span class="header_cont_score_rf">评分:</span>
                  <Rate
                    v-model:value="item.score"
                    :disabled="!item.isEdit"
                    :tooltips="desc"
                  />
                  <span class="header_cont_score_lf header_cont_score_fraction">{{ item.score }}分</span>
                  <div style="width: 100px">
                    <span
                      v-if="item.score>0"
                      class="header_cont_score_lf"
                    >{{ desc[item.score - 1] }}</span>
                    <!--                    <span-->
                    <!--                      v-else-->
                    <!--                      class="header_cont_score_lf"-->
                    <!--                    >{{ '极差' }}</span>-->
                  </div>
                </div>
                <div
                  class="header_cont_edit"
                  @click.stop=""
                >
                  <span
                    v-if="!item.isEdit"
                    class="action-btn"
                    @click.stop="clickEdit(item,index)"
                  >编辑</span>
                </div>
              </div>
            </template>
            <Textarea
              v-model:value="item.evaluateValues"
              :disabled="!item.isEdit"
              allow-clear
              :maxlength="1500"
              size="large"
              placeholder="请输入评价信息"
            />
            <div class="btm_operate">
              <span class="statistics">{{ item.evaluateValues?.length?item.evaluateValues?.length:'0' }}/1500</span>
              <div class="btm_operate_rt">
                <BasicButton
                  size="small"
                  :disabled="!item.isEdit"
                  @click="cancellation(item,index)"
                >
                  取消
                </BasicButton>
                <BasicButton
                  :loading="loadingBut"
                  size="small"
                  type="primary"
                  :disabled="!item.isEdit"
                  @click="okEdit(item,index)"
                >
                  确认
                </BasicButton>
              </div>
            </div>
          </CollapsePanel>
        </Collapse>
      </div>
    </DetailsLayout>
  </div>
</template>

<script setup lang="ts">
import {
  ref, Ref, defineProps,
} from 'vue';
import { useRoute } from 'vue-router';
import { BasicButton } from 'lyra-component-vue3';
import {
  Collapse, CollapsePanel, Rate, Textarea,
} from 'ant-design-vue';
import { getProjectEvaluateDetails, getEvaluateItemData } from '/@/views/pms/api/projectEvaluation';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import Api from '/@/api';
const Props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const route = useRoute();

const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '编码',
    field: 'number',
  },
  {
    label: '项目评价名称',
    field: 'name',
    gridColumn: '1/5',
    wrap: true,
  },
  {
    label: '项目评价类型',
    field: 'evaluationTypeName',
  },
  {
    label: '评价对象',
    field: 'evaluationObject',
  },
  {
    label: '发起项目评价日期',
    field: 'evaluationTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目名称',
    field: 'name',
  },
  {
    label: '项目评价责任人',
    field: 'evaluationPersonName',
  },
  {
    label: '项目经理',
    field: 'projectManagerName',
  },
  {
    label: '项目开始日期',
    field: 'projectStartTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目结束日期',
    field: 'projectEndTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '项目评价描述',
    field: 'remark',
    gridColumn: '1/5',
    wrap: true,

  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '最后修改日期',
    field: 'modifyTime',
    formatTime: 'YYYY-MM-DD',
  },
]);
// 评价信息的内容value
const evaluateData = ref({
  data: {
  },
});

// 评价内容-------------------------
const customStyle = 'background: #f7f7f7;border-radius: 4px;margin-bottom: 24px;border: 0;overflow: hidden';
const activeKey = ref([]); // 默认展开的折叠面板
const desc = ([
  '非常差',
  '差',
  '一般',
  '好',
  '极好',
]); // 项目评分描述
const projectEvaluationList = ref([]);
const loadingBut = ref(false);
getEvaluateItemData(route.query?.id).then((res) => {
  evaluateData.value.data = res;
});

function getData() {
  getProjectEvaluateDetails(route.query?.id).then(async (res) => {
    projectEvaluationList.value = await res.map((item) => ({
      id: item.id,
      outcomeEvaluation: item.name, // 项目目标和成果评价
      score: item.score / 2, // 项目评分
      evaluateValues: item.evaluationContent, // 评价信息res
      isEdit: false,
    }));
  });
  return projectEvaluationList.value;
}
getData();

function changeCollapse(key) {
  for (let i = 0; i < projectEvaluationList.value.length; i++) {
    if (!key.includes(i)) {
      projectEvaluationList.value[i].isEdit = false;
    }
  }
}
function clickEdit(item, index) {
  activeKey.value.push(String(index));
  activeKey.value = [...new Set(activeKey.value)];
  projectEvaluationList.value[index].isEdit = true;
}
function okEdit(item, index) {
  loadingBut.value = true;
  const data = {
    id: item.id,
    score: item.score * 2,
    evaluationContent: item.evaluateValues,
  };
  new Api('/pms/evaluation-detail').fetch(data, '', 'PUT').then((res) => {
    projectEvaluationList.value = res.map((item) => ({
      id: item.id,
      outcomeEvaluation: item.name, // 项目目标和成果评价
      score: item.score / 2, // 项目评分
      evaluateValues: item.evaluationContent, // 评价信息res
      isEdit: false,
    }));
    projectEvaluationList.value[index] = getData()[index];
  }).finally(() => {
    loadingBut.value = false;
    const i = activeKey.value.indexOf(String(index));
    activeKey.value.splice(i, 1);
  });
}
function cancellation(item, index) {
  projectEvaluationList.value[index] = getData()[index];
  const i = activeKey.value.indexOf(String(index));
  activeKey.value.splice(i, 1);
}
function openAll() {
  for (let i = 0; i < projectEvaluationList.value.length; i++) {
    activeKey.value.push(String(i));
  }
  activeKey.value = [...new Set(activeKey.value)];
}
function closeAll() {
  activeKey.value = [];
}
function getScoreDescription(score) {
  if (Number(score) > 0 && Number(score) < 1) {
    return '非常差';
  } if (Number(score) > 1 && Number(score) < 2) {
    return '非常差';
  } if (Number(score) > 2 && Number(score) < 3) {
    return '非常差';
  } if (Number(score) > 3 && Number(score) < 4) {
    return '非常差';
  } if (Number(score) > 4 && Number(score) < 5) {
    return '非常差';
  }
}

</script>
<style scoped lang="less">
.evaluate_panel{
  padding-top:16px;
  .header_cont{
    width: 100%;
    display: flex;
    .header_cont_tit{
      flex: 1;
      display: flex;
      align-items: center;
    }
    .header_cont_theme{
      flex: 1;
      display: flex;
      align-items: center;
      .Brief{
        width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #ccc;
      }
    }

    .header_cont_score{
      flex: 1.5;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      line-height: 22px;
      padding: 12px 0;
      .header_cont_score_rf{
        margin-right: 10px;
      }
      .header_cont_score_lf{
        margin-left: 26px;

      }
      .header_cont_score_fraction{
        color: #FADB15;
      }
    }
    .header_cont_edit{
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

  }
  .evaluate_panel_cont{
    background-color: red;
  }
}
.btm_operate{
  display: flex;
  justify-content: space-between;
  padding: 10px 0 0;
  .btm_operate_rt{}
  .statistics{
    line-height: 22px;
    color: #ccc;
  }
}
:deep(.ant-collapse-content ){
  background-color:#FFFFFF !important;
}
//ant-collapse-item ant-collapse-item-active//ant-collapse-content-box
:deep( .ant-collapse-item ){
  margin-bottom: 10px !important;
}
:deep(.ant-collapse-header){
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-right: 0 !important;
  display: flex!important;
  align-items: center!important;
}
.title_right{
  padding: 0 30px;
}
</style>
