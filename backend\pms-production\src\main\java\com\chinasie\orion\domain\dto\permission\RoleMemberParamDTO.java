package com.chinasie.orion.domain.dto.permission;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/31/14:27
 * @description:
 */
@Data
public class RoleMemberParamDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    @Size(min = 1, message = "用户不能为空")
    private List<String> userIdList;
    @NotEmpty(message = "大修轮次不能为空")
    private String majorRepairTurn;

    @NotEmpty(message = "所属角色ID不能为空")
    private String businessId;

    @NotEmpty(message = "所属角色编码不能为空")
    private String roleCode;
}
