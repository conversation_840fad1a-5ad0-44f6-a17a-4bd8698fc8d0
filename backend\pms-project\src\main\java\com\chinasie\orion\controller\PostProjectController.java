package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.PostProjectDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.vo.PostProjectVo;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.PostProjectService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:58
 * @description:
 */
@RestController
@RequestMapping("/post-project")
@Api(tags = "结项")
public class PostProjectController {

    @Resource
    private PostProjectService postProjectService;

    @Resource
    private DictBo dictBo;

    @ApiOperation("获取项目信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "String")
    })
    @GetMapping("/getProject/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目信息", subType = "获取项目信息", type = "结项", bizNo = "{{#projectId}}")
    public ResponseDTO<PostProjectDTO> getProjectInfo(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(postProjectService.getProjectInfo(projectId));
    }

    @ApiOperation("新增结项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postProjectDTO", dataType = "PostProjectDTO")
    })
    @PostMapping("/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增结项", subType = "新增结项", type = "结项", bizNo = "")
    public ResponseDTO<String> savePostProject(@RequestBody PostProjectDTO postProjectDTO) throws Exception {
        return new ResponseDTO<>(postProjectService.savePostProject(postProjectDTO));
    }

    @ApiOperation("结项分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】结项分页", subType = "结项分页", type = "结项", bizNo = "")
    public ResponseDTO<PageResult<PostProjectVo>> getPostProjectPage(@RequestBody PageRequest<PostProjectDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(postProjectService.getPostProjectPage(pageRequest));
    }

    /**
     * 结项搜索
     *
     * @param searchDTO
     * @return
     */
    @ApiOperation("结项搜索")
    @RequestMapping(value = "/search", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】结项搜索", subType = "结项搜索", type = "结项", bizNo = "")
    public ResponseDTO<List<PostProjectVo>> search(@RequestBody SearchDTO searchDTO) throws Exception {
        List<PostProjectVo> rsp = postProjectService.search(searchDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("结项详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping("/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取结项详情", subType = "获取结项详情", type = "结项", bizNo = "{{#id}}")
    public ResponseDTO<PostProjectVo> getPostProjectDetail(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(postProjectService.getPostProjectDetail(id));
    }

    @ApiOperation("编辑结项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "postProjectDTO", dataType = "PostProjectDTO")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑结项", subType = "编辑结项", type = "结项", bizNo = "")
    public ResponseDTO<Boolean> editPostProject(@RequestBody PostProjectDTO postProjectDTO) throws Exception {
        return new ResponseDTO<>(postProjectService.editPostProject(postProjectDTO));
    }

    @ApiOperation("批量删除结项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", dataType = "List")
    })
    @DeleteMapping("/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除结项", subType = "批量删除结项", type = "结项", bizNo = "")
    public ResponseDTO<Boolean> removeBatchPostProject(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(postProjectService.removeBatchPostProject(idList));
    }

    @ApiOperation("获取结项类型")
    @GetMapping(value = "/type")
    @LogRecord(success = "【{USER{#logUserId}}】获取结项类型", subType = "获取结项类型", type = "结项", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getCopingStrategy() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.POST_PROJECT_TYPE));
    }

}
