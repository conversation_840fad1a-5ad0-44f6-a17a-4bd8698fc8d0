package com.chinasie.orion.domain.vo.reporting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *@ClassName ProjectDailyStatementCardVO
 *@Description
 *<AUTHOR> sirui
 *@Date2023/11/15 14:55
 **/
@Data
public class ProjectWeeklyCardVO {
    /**
     * 周
     */
    @ApiModelProperty(value = "周")
    private Integer weekly;

    @ApiModelProperty(value = "已提交")
    private List<ProjectWeeklyStatementCardVO> submitted = new ArrayList<>();
    @ApiModelProperty(value = "已提交数量")
    private Integer submittedNumber;
    @ApiModelProperty(value = "未提交")
    private List<ProjectWeeklyStatementCardVO> noSubmitted = new ArrayList<>();
    @ApiModelProperty(value = "未提交数量")
    private Integer noSubmittedNumber;



}
