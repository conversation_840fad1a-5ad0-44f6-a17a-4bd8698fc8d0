package com.chinasie.orion.service.impl;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.entity.PlanToNcFormpurchase;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToNcFormpurchaseMapper;
import com.chinasie.orion.service.PlanToNcFormpurchaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <p>
 * PlanToFixedAsset 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-23 11:06:02
 */
@Service
@Slf4j
public class PlanToNcFormpurchaseServiceImpl extends OrionBaseServiceImpl<PlanToNcFormpurchaseMapper, PlanToNcFormpurchase> implements PlanToNcFormpurchaseService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;



}
