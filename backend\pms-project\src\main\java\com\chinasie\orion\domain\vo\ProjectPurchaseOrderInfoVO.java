package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectPurchaseOrderInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@ApiModel(value = "ProjectPurchaseOrderInfoVO对象", description = "采购订单基本信息")
@Data
public class ProjectPurchaseOrderInfoVO extends ObjectVO implements Serializable{

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String number;

    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    private String name;

    /**
     * 采购员
     */
    @ApiModelProperty(value = "采购员")
    private String resUserId;

    /**
     * 采购负责部门
     */
    @ApiModelProperty(value = "采购负责部门")
    private String rspDeptId;

    /**
     * 含税总金额
     */
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal haveTaxTotalAmt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 采购总数量
     */
    @ApiModelProperty(value = "采购总数量")
    private BigDecimal purchaseTotalAmount;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    private String purchaseType;

    /**
     * 订单到货日期
     */
    @ApiModelProperty(value = "订单到货日期")
    private Date orderArrivalDate;

    /**
     * 订单说明
     */
    @ApiModelProperty(value = "订单说明")
    private String orderDesc;

    /**
     * 采购员名称
     */
    @ApiModelProperty(value = "采购员名称")
    private String resUserName;

    /**
     * 采购员工号
     */
    @ApiModelProperty(value = "采购员工号")
    private String resUserCode;

    /**
     * 创建人工号
     */
    @ApiModelProperty(value = "创建人工号")
    private String creatorCode;

    /**
     * 采购负责部门名称
     */
    @ApiModelProperty(value = "采购负责部门名称")
    private String rspDeptName;

    /**
     * 采购类型名称
     */
    @ApiModelProperty(value = "采购类型名称")
    private String purchaseTypeName;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单类型名称
     */
    @ApiModelProperty(value = "订单类型名称")
    private String orderTypeName;

    /**
     * 订单执行状态
     */
    @ApiModelProperty(value = "订单执行状态")
    private String orderExcuteStatus;

    /**
     * 订单执行状态名称
     */
    @ApiModelProperty(value = "订单执行状态名称")
    private String orderExcuteStatusName;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    /**
     * 订单来源名称
     */
    @ApiModelProperty(value = "订单来源名称")
    private String orderSourceName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

}
