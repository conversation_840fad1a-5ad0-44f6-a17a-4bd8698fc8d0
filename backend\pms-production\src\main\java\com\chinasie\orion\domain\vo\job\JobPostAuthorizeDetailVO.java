package com.chinasie.orion.domain.vo.job;

import com.chinasie.orion.domain.entity.PmsJobPostLibrary;
import com.chinasie.orion.domain.vo.PmsJobPostLibraryVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/21:17
 * @description:
 */
@Data
public class JobPostAuthorizeDetailVO implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 是否申请岗位等效
     */
    @ApiModelProperty(value = "是否申请岗位等效: 如果为是 岗位等效需要请求获取岗位等效接口，如果 否，显示当前返回信息")
    private Boolean isApplyJobEqu;

    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "岗位授权指引（冗余）")
    private String authorizationGuide;


    /**
     * 授权起始日期
     */
    @ApiModelProperty(value = "授权起始日期")
    private Date authorizeStartDate;


    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    private Date endDate;


    @ApiModelProperty(value = "岗位授权/等效材料")
    private List<FileVO> fileVOList;

    @ApiModelProperty(value = "岗位信息")
    private PmsJobPostLibraryVO jobPostLibrary;


    @ApiModelProperty(value = "基地编码")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    private String baseName;

}
