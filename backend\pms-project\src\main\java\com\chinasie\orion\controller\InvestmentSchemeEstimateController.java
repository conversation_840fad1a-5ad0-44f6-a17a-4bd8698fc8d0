package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.InvestmentSchemeEstimateDTO;
import com.chinasie.orion.domain.vo.InvestmentSchemeEstimateVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.InvestmentSchemeEstimateService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * InvestmentSchemeEstimate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:33:06
 */
@RestController
@RequestMapping("/investmentSchemeEstimate")
@Api(tags = "概算")
public class InvestmentSchemeEstimateController {

    @Autowired
    private InvestmentSchemeEstimateService investmentSchemeEstimateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【InvestmentSchemeEstimate详情】", subType = "详情", type = "概算", bizNo = "{{#id}}")
    public ResponseDTO<InvestmentSchemeEstimateVO> detail(@PathVariable(value = "id") String id) throws Exception {
        InvestmentSchemeEstimateVO rsp = investmentSchemeEstimateService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param investmentSchemeEstimateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【InvestmentSchemeEstimate】", type = "概算", subType = "新增", bizNo = "{{#investmentSchemeEstimateDTO.name}}")
    public ResponseDTO<InvestmentSchemeEstimateVO> create(@RequestBody InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO) throws Exception {
        InvestmentSchemeEstimateVO rsp = investmentSchemeEstimateService.create(investmentSchemeEstimateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param investmentSchemeEstimateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【InvestmentSchemeEstimate】", type = "概算", subType = "编辑", bizNo = "{{#investmentSchemeEstimateDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO) throws Exception {
        Boolean rsp = investmentSchemeEstimateService.edit(investmentSchemeEstimateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 列表
     *
     * @param investmentSchemeId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list/{investmentSchemeId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【InvestmentSchemeEstimate列表】", subType = "列表", type = "概算", bizNo = "{{#investmentSchemeId}}")
    public ResponseDTO<List<InvestmentSchemeEstimateVO>> list(@PathVariable("investmentSchemeId") String investmentSchemeId) throws Exception {
        List<InvestmentSchemeEstimateVO> rsp = investmentSchemeEstimateService.list(investmentSchemeId);
        return new ResponseDTO<>(rsp);
    }
}
