package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.CenterPlanApprovalDTO;
import com.chinasie.orion.domain.dto.ContractCenterPlanDTO;
import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.dto.TopCenterExportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.handler.MyExcelUtils;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;

import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractCenterPlanMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Year;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;




/**
 * <p>
 * ContractCenterPlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@Service
@Slf4j
public class ContractCenterPlanServiceImpl extends  OrionBaseServiceImpl<ContractCenterPlanMapper, ContractCenterPlan>   implements ContractCenterPlanService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ContractMainService contractMainService;

    @Autowired
    private EditLogService editLogService;

    @Autowired
    private AssessmentLogService assessmentLogService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private ContractCenterService contractCenterService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Resource
    private MessageCenterApi messageCenterApi;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractCenterPlanVO detail(String id,String pageCode) throws Exception {
        ContractCenterPlan contractCenterPlan =this.getById(id);
        ContractCenterPlanVO result = BeanCopyUtils.convertTo(contractCenterPlan,ContractCenterPlanVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractCenterPlanDTO
     */
    @Override
    public  String create(ContractCenterPlanDTO contractCenterPlanDTO) throws Exception {
        ContractCenterPlan contractCenterPlan =BeanCopyUtils.convertTo(contractCenterPlanDTO,ContractCenterPlan::new);
        this.save(contractCenterPlan);

        String rsp=contractCenterPlan.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractCenterPlanDTO
     */
    @Override
    public Boolean edit(List<ContractCenterPlanDTO> contractCenterPlanDTOList) throws Exception {
        List<ContractCenterPlanDTO> recordData = new ArrayList<>();
        contractCenterPlanDTOList.forEach(vo->{
            Integer status = vo.getStatus();
            if (vo.getStatus()!=null){
                if (status.equals(CenterPlanEnum.EDITABLE.getKey())){
                    recordData.add(vo);
                }
            }
            vo.setStatus(CenterPlanEnum.UNREVIEWED.getKey());
        });
        List<ContractCenterPlan> contractCenterPlans = BeanCopyUtils.convertListTo(contractCenterPlanDTOList, ContractCenterPlan::new);
        //审批记录
        insertAssessmentLog(contractCenterPlanDTOList.get(0));

        this.updateBatchById(contractCenterPlans);

        //修改合同计划状态
        ContractCenterPlanDTO contractCenterPlanDTO = contractCenterPlanDTOList.get(0);
        String contractNumber = contractCenterPlanDTO.getContractNumber();
        Date yearDate = contractCenterPlanDTO.getYear();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(yearDate);
        int year = calendar.get(Calendar.YEAR);

        LambdaUpdateWrapper<ContractMain> updateWrapper = new LambdaUpdateWrapper<>(ContractMain.class);
        updateWrapper.eq(ContractMain::getContractNumber, contractNumber);
        updateWrapper.apply("EXTRACT(YEAR FROM year) = {0}", year);
        updateWrapper.set(ContractMain::getStatus, ContractPlanStatusConstant.APPROVING.getKey());
        contractMainService.update(updateWrapper);
        ContractCenterPlan contractCenterPlan = this.getById(contractCenterPlanDTOList.get(0).getId());
        messageCenterApi.todoMessageChangeStatusByBusinessIds(Arrays.asList(contractCenterPlan.getContractNumber()+"_"+contractCenterPlan.getCenterCode()));
        return true;
    }

    /**
     * 审批记录
     * @param param 参数
     */
    public void insertAssessmentLog(ContractCenterPlanDTO param){
        AssessmentLog assessmentLog = new AssessmentLog();
        assessmentLog.setSubmitTime(new Date());
        assessmentLog.setCenterName(param.getCenterName());
        assessmentLog.setContractNumber(param.getContractNumber());
        assessmentLog.setStatus(CenterPlanEnum.UNREVIEWED.getKey());
        assessmentLog.setYear(getYearFromParam(param.getYear()));
        if (param.getBeforeNum()==null){
            assessmentLog.setType("录入用人计划");
        }else {
            assessmentLog.setType("调整用人计划");
        }
        assessmentLogService.save(assessmentLog);
    }

    public void updateAssessmentLog(ContractCenterPlanDTO param,CenterPlanApprovalDTO centerPlanApprovalDTO){
        AssessmentLog assessmentLog = new AssessmentLog();
        assessmentLog.setAssessmentTime(new Date());
        //审批人封装
        String userId = CurrentUserHelper.getCurrentUserId();
        UserVO userById = userRedisHelper.getUserById(userId);
        assessmentLog.setPersonId(userId);
        assessmentLog.setPersonName(userById.getName());
        assessmentLog.setStatus(centerPlanApprovalDTO.getStatus());
        assessmentLog.setAssessmentAdvice(centerPlanApprovalDTO.getAdvice());
        LambdaUpdateWrapper<AssessmentLog> wrapperX = new LambdaUpdateWrapper<>(AssessmentLog.class);
        //更新
        wrapperX.eq(AssessmentLog::getContractNumber,param.getContractNumber())
                .eq(AssessmentLog::getCenterName,param.getCenterName())
                .eq(AssessmentLog::getYear,getYearFromParam(param.getYear()));

        if (param.getPreStatus().equals(CenterPlanEnum.UNREVIEWED.getKey())){
            wrapperX.eq(AssessmentLog::getType,"录入用人计划");
        }else {
            wrapperX.eq(AssessmentLog::getType,"调整用人计划");
        }
        assessmentLogService.update(assessmentLog,wrapperX);
    }

    private static int getYearFromParam(Date param) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 调整记录
     * @param param 参数
     */
    public void insertEditLog(List<ContractCenterPlanDTO> param){
        List<EditLog> editLog = new ArrayList<>();
        for (ContractCenterPlanDTO contractCenterPlanDTO : param) {
            EditLog tmp = new EditLog();
            tmp.setCenterName(contractCenterPlanDTO.getCenterName());
            tmp.setPlanId(contractCenterPlanDTO.getId());
            tmp.setCostType(contractCenterPlanDTO.getCostTypeNumber());
            tmp.setCostName(contractCenterPlanDTO.getCostName());
            tmp.setNum(contractCenterPlanDTO.getNum());
            tmp.setBeforeNum(contractCenterPlanDTO.getBeforeNum());
            tmp.setAssessmentPersonId(CurrentUserHelper.getCurrentUserId());
            tmp.setAssessmentTime(new Date());
            editLog.add(tmp);
        }

        editLogService.saveBatch(editLog);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractCenterPlanVO> pages( Page<ContractCenterPlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractCenterPlan> condition = new LambdaQueryWrapperX<>( ContractCenterPlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractCenterPlan::getCreateTime);


        Page<ContractCenterPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractCenterPlan::new));

        PageResult<ContractCenterPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractCenterPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractCenterPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractCenterPlanVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "中心用人计划导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCenterPlanDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ContractCenterPlanExcelListener excelReadListener = new ContractCenterPlanExcelListener();
        EasyExcel.read(inputStream,ContractCenterPlanDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractCenterPlanDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("中心用人计划导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractCenterPlan> contractCenterPlanes =BeanCopyUtils.convertListTo(dtoS,ContractCenterPlan::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractCenterPlan-import::id", importId, contractCenterPlanes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractCenterPlan> contractCenterPlanes = (List<ContractCenterPlan>) orionJ2CacheService.get("pmsx::ContractCenterPlan-import::id", importId);
        log.info("中心用人计划导入的入库数据={}", JSONUtil.toJsonStr(contractCenterPlanes));

        this.saveBatch(contractCenterPlanes);
        orionJ2CacheService.delete("pmsx::ContractCenterPlan-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractCenterPlan-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractCenterPlan> condition = new LambdaQueryWrapperX<>( ContractCenterPlan. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractCenterPlan::getCreateTime);
        List<ContractCenterPlan> contractCenterPlanes =   this.list(condition);

        List<ContractCenterPlanDTO> dtos = BeanCopyUtils.convertListTo(contractCenterPlanes, ContractCenterPlanDTO::new);

        String fileName = "中心用人计划数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCenterPlanDTO.class,dtos );

    }

    @Override
    public void exportTopByExcel(List<TopCenterExportDTO> exportDTO, HttpServletResponse response) throws Exception {
        //获取表头
        List<Map<String, Object>> sheetHeads = contractMainService.getSheetHeads();
        String fileName = "中心用人计划汇总表.xlsx";
        //写入
        MyExcelUtils.writeSheet(sheetHeads,exportDTO,fileName,"sheet1",response);
    }

    @Override
    public void approval(CenterPlanApprovalDTO centerPlanApprovalDTO) {
        LambdaUpdateWrapper<ContractCenterPlan> wrapper = new LambdaUpdateWrapper<>(ContractCenterPlan. class);
        if (CollectionUtils.isEmpty(centerPlanApprovalDTO.getCenterPlans())){
            throw new BaseException(MyExceptionCode.ERROR_PARAM.getErrorCode(),"无用人计划，无法通过");
        }
        wrapper.apply("EXTRACT(YEAR FROM year) = {0}", centerPlanApprovalDTO.getYear());
        wrapper.eq(ContractCenterPlan::getContractNumber, centerPlanApprovalDTO.getContractNumber());
        wrapper.eq(ContractCenterPlan::getCenterName,centerPlanApprovalDTO.getCenterPlans().get(0).getCenterName());
        wrapper.set(ContractCenterPlan::getStatus, centerPlanApprovalDTO.getStatus());

        update(wrapper);

        //todo 判断相关合同当年数据是否全部审批通过，如果全部通过则合同状态改为已审批
        List<Integer> status = new ArrayList<>();
        status.add(CenterPlanEnum.EDIT.getKey());
        status.add(CenterPlanEnum.UNREVIEWED.getKey());
        status.add(CenterPlanEnum.REJECTED.getKey());
        status.add(CenterPlanEnum.UNSUBMIT.getKey());

        String contractNumber = centerPlanApprovalDTO.getContractNumber();
        LambdaQueryWrapperX<ContractCenterPlan> wrapperX = new LambdaQueryWrapperX<>(ContractCenterPlan.class);

        wrapperX.eq(ContractCenterPlan::getContractNumber, contractNumber)
               .like(ContractCenterPlan::getYear, String.valueOf(centerPlanApprovalDTO.getYear()))
               .in(ContractCenterPlan::getStatus, status);
        List<ContractCenterPlan> list = list(wrapperX);
        if (CollectionUtils.isEmpty(list)){
            LambdaUpdateWrapper<ContractMain> mainWrapper = new LambdaUpdateWrapper<>(ContractMain.class);
            mainWrapper.eq(ContractMain::getContractNumber, contractNumber);
            mainWrapper.apply("EXTRACT(YEAR FROM year) = {0}", centerPlanApprovalDTO.getYear());
            mainWrapper.set(ContractMain::getStatus, ContractPlanStatusConstant.EFFECTIVE.getKey());
            contractMainService.update(mainWrapper);
        }

        //调整记录
        insertEditLog(centerPlanApprovalDTO.getCenterPlans());
        //记录插入
        ContractCenterPlanDTO param = centerPlanApprovalDTO.getCenterPlans().get(0);
        param.setStatus(centerPlanApprovalDTO.getStatus());
        param.setPreStatus(centerPlanApprovalDTO.getPreStatus());
        updateAssessmentLog(param, centerPlanApprovalDTO);
        //待办消除
//        LambdaQueryWrapperX<ContractCenterPlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ContractCenterPlan. class);
//        lambdaQueryWrapperX.apply("EXTRACT(YEAR FROM year) = {0}", centerPlanApprovalDTO.getYear());
//        lambdaQueryWrapperX.eq(ContractCenterPlan::getContractNumber, centerPlanApprovalDTO.getContractNumber());
//        lambdaQueryWrapperX.eq(ContractCenterPlan::getCenterName,centerPlanApprovalDTO.getCenterPlans().get(0).getCenterName());
//        List<ContractCenterPlan> contractCenterPlan = this.list(lambdaQueryWrapperX);
//         messageCenterApi.todoMessageChangeStatusByBusinessIds(Arrays.asList(centerPlanApprovalDTO.getContractNumber()+"_"+contractCenterPlan.get(0).getCenterCode()));
    }

    @Override
    public List<CostTypeUnitVO> getCostType() {
        List<DictValueVO> costType = dictRedisHelper.getByDictNumber(CostDictNumberConstant.COST_DICT_NUMBER, CurrentUserHelper.getOrgId());
        List<CostTypeUnitVO> res = new ArrayList<>();

        Map<String, String> map = CostTypeToUnit.getMap();
        for (DictValueVO dictValueVO : costType) {
            CostTypeUnitVO costTypeUnitVO = new CostTypeUnitVO();
            costTypeUnitVO.setCostTypeName(dictValueVO.getDescription());
            costTypeUnitVO.setCostTypeNumber(dictValueVO.getValue());
            costTypeUnitVO.setUnit(map.getOrDefault(dictValueVO.getDescription(),""));
            res.add(costTypeUnitVO);
        }
        return res;
    }

    @Override
    public List<Map<String, String>> getCenterHeads(String contractNumber, Integer year) {
        LambdaQueryWrapper<ContractCenter> wrapper = new LambdaQueryWrapper<>(ContractCenter.class);
        wrapper.eq(ContractCenter::getContractNumber, contractNumber);
//        wrapper.apply("EXTRACT(YEAR FROM year) = {0}", year);
        wrapper.select(ContractCenter::getCenterName,ContractCenter::getCenterCode);
        if (!judgeRole()){
            List<DeptVO> deptByUserId = deptRedisHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
            wrapper.in(ContractCenter::getCenterCode,deptByUserId.stream().map(DeptVO::getDeptCode).collect(Collectors.toList()));
        }
        List<ContractCenter> list = contractCenterService.list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        List<Map<String,String>> res = new ArrayList<>();
        for (ContractCenter contractCenter : list) {
            Map<String, String> map = new HashMap<>();
            map.put(contractCenter.getCenterName(),contractCenter.getCenterCode());
            res.add(map);
        }

        return res;
    }

    /**
     * 判断当前登陆人是否是财务角色或者技术角色
     * @return 是则返回true 不是则返回false
     */
    public Boolean judgeRole(){
        String userId = CurrentUserHelper.getCurrentUserId();
        UserVO userById = userRedisHelper.getUserById(userId);
        if (Objects.isNull(userById)){
            throw new BaseException(MyExceptionCode.ERROR_USER_NOT_FOUNT);
        }
        List<RoleVO> roles = userById.getRoles();
        List<String> codeList = roles.stream().map(RoleVO::getCode).collect(Collectors.toList());
        return (codeList.contains(RoleCode.contractFinancial)||codeList.contains(RoleCode.technologyConfig));
    }


    @Override
    public void  setEveryName(List<ContractCenterPlanVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }
    @OperationPower(operationType = OperationPowerType.PAGE)
    @Override
    public List<CenterDisPlayPlanVO> planGroupByCenter(Integer year) {
        if (year == null){
            year = Year.now().getValue();
        }

        List<PlanVO> planVos = getByUserRolePlanVo(year);
        List<CostVO> allCost = this.getBaseMapper().getAllCost(year);
        //封装内层
        getPlanVOPublic(planVos, allCost);

        //封装外层
        List<CenterDisPlayPlanVO> res = outSideWrapper(planVos,0);

        return res;
    }

    @OperationPower(operationType = OperationPowerType.PAGE)
    @Override
    public List<CenterDisPlayPlanVO> planGroupByContract(Integer year) {
        ContractCenterPlanMapper baseMapper = this.getBaseMapper();
        List<PlanVO> allPlan = baseMapper.getPlanGroupByCenterContract(year);
        List<CostVO> allCost = baseMapper.getAllCost(year);


        List<PlanVO> planVoPublic = getPlanVOPublic(allPlan, allCost);


        return outSideWrapper(planVoPublic,1);
    }


    public List<CenterDisPlayPlanVO> outSideWrapper(List<PlanVO> planVos,Integer type){
        List<CenterDisPlayPlanVO> res = new ArrayList<>();
        Map<String, List<PlanVO>> nameToList = new HashMap<>();
        //通过不同类型 分别按照不同中心计划或者合同计划分组
        if (type.equals(0)){
            nameToList = planVos.stream().collect(Collectors.groupingBy(PlanVO::getCenterName));
        }else {
            nameToList = planVos.stream().collect(Collectors.groupingBy(PlanVO::getContractName));
        }

        Set<Map.Entry<String, List<PlanVO>>> entries = nameToList.entrySet();
        Map<Integer, String> statusToName = NfcContractStatus.getMap();
        Map<Integer, String> statusMap = ContractPlanStatusConstant.getMap();
        for (Map.Entry<String, List<PlanVO>> entry : entries) {
            CenterDisPlayPlanVO centerDisPlayPlanVO = new CenterDisPlayPlanVO();
            //0封装中心 1封装合同
            if (type.equals(0)){
                centerDisPlayPlanVO.setCenterName(entry.getKey());
                centerDisPlayPlanVO.setCenterCode(entry.getValue().get(0).getCenterCode());
            }else {
                PlanVO planVO = entry.getValue().get(0);
                centerDisPlayPlanVO.setContractName(planVO.getContractName());
                centerDisPlayPlanVO.setContractNumber(planVO.getContractNumber());
                centerDisPlayPlanVO.setStatus(planVO.getStatus());
                centerDisPlayPlanVO.setStatusName(statusMap.get(planVO.getStatus()));
                centerDisPlayPlanVO.setContractStatus(planVO.getContractStatus());
                centerDisPlayPlanVO.setContractStatusName(statusToName.getOrDefault(planVO.getContractStatus(),""));
            }

            //统计固定统计数据
            centerDisPlayPlanVO.setPersonTotalBudget(BigDecimal.valueOf(0));
            centerDisPlayPlanVO.setPositionTotalMoney(BigDecimal.valueOf(0));
            centerDisPlayPlanVO.setPersonTotalCount(0);


            Map<String, BigDecimal> totalMap = ContractMainServiceImpl.SheetHeadHandler.getSheetHeads(dictRedisHelper);
            //统计成本总额并且按照部门统计
            //将合同计划和单位计划分开处理
            if (type.equals(0)){
                //按照中心统计
                List<PlanVO> planList = entry.getValue();
                Map<String, List<PlanVO>> byContract = planList.stream().filter(p -> p.getContractNumber()!=null).collect(Collectors.groupingBy(PlanVO::getContractName));
                List<PlanVO> planVOList = wrapperCenterOrContract(byContract, totalMap,centerDisPlayPlanVO);
                centerDisPlayPlanVO.setPlanList(planVOList);
            }else {
                //按照部门统计
                if (centerDisPlayPlanVO.getStatus()!=null
                    &&!centerDisPlayPlanVO.getStatus().equals(ContractPlanStatusConstant.ING.getKey())){
                    List<PlanVO> planList = entry.getValue();
                    Map<String, List<PlanVO>> byCenter = planList.stream().filter(p -> p.getCenterName()!=null).collect(Collectors.groupingBy(PlanVO::getCenterName));
                    List<PlanVO> planVOList = wrapperCenterOrContract(byCenter, totalMap,centerDisPlayPlanVO);
                    centerDisPlayPlanVO.setPlanList(planVOList);
                }
            }
            centerDisPlayPlanVO.setSheetHeadsMap(totalMap);
            res.add(centerDisPlayPlanVO);
        }
        res.sort((a, b) -> {
            boolean aIsTarget = "编制中".equals(a.getStatusName());
            boolean bIsTarget = "编制中".equals(b.getStatusName());
            if (aIsTarget && !bIsTarget) {
                return -1; // a在目标状态，排在前面
            } else if (!aIsTarget && bIsTarget) {
                return 1;  // b在目标状态，排在前面
            } else {
                return 0;  // 保持其他元素原有顺序
            }
        });
        return res;
    }

    public List<PlanVO> wrapperCenterOrContract(Map<String, List<PlanVO>> param,Map<String, BigDecimal> totalMap,CenterDisPlayPlanVO centerDisPlayPlanVO){
        List<PlanVO> planVOList = new ArrayList<>();
        Map<Integer, String> statusToName = CenterPlanEnum.getMap();
        for (Map.Entry<String, List<PlanVO>> item : param.entrySet()) {
            PlanVO planVO = null;
            //创建表头
            Map<String, BigDecimal> planVOHeadMap = ContractMainServiceImpl.SheetHeadHandler.getSheetHeads(dictRedisHelper);
            for (PlanVO tmp : item.getValue()) {
                if (Objects.isNull(planVO)){
                    planVO = BeanCopyUtils.convertTo(tmp,PlanVO::new);
                    centerDisPlayPlanVO.setPersonTotalBudget(centerDisPlayPlanVO.getPersonTotalBudget().add(tmp.getPersonTotalBudget()));
                    centerDisPlayPlanVO.setPositionTotalMoney(centerDisPlayPlanVO.getPositionTotalMoney().add(tmp.getPositionTotalMoney()));
                    centerDisPlayPlanVO.setPersonTotalCount(centerDisPlayPlanVO.getPersonTotalCount()+planVO.getPersonTotalCount());
                    continue;
                }

                //统计第一层表头和第二层表头
                if (Objects.nonNull(tmp.getSheetHeadsMap())) {
                    for (Map.Entry<String, BigDecimal> vo : tmp.getSheetHeadsMap().entrySet()) {
                        totalMap.put(vo.getKey(), totalMap.getOrDefault(vo.getKey(), BigDecimal.valueOf(0)).add(vo.getValue()));
                        planVOHeadMap.put(vo.getKey(), totalMap.getOrDefault(vo.getKey(), BigDecimal.valueOf(0)).add(vo.getValue()));
                    }
                }
                planVO.setPersonTotalBudget(planVO.getPersonTotalBudget().add(tmp.getPersonTotalBudget()));
                planVO.setPositionTotalMoney(planVO.getPositionTotalMoney().add(tmp.getPositionTotalMoney()));
                planVO.setPersonTotalCount(planVO.getPersonTotalCount()+tmp.getPersonTotalCount());
                centerDisPlayPlanVO.setPersonTotalBudget(centerDisPlayPlanVO.getPersonTotalBudget().add(tmp.getPersonTotalBudget()));
                centerDisPlayPlanVO.setPositionTotalMoney(centerDisPlayPlanVO.getPositionTotalMoney().add(tmp.getPositionTotalMoney()));
                centerDisPlayPlanVO.setPersonTotalCount(centerDisPlayPlanVO.getPersonTotalCount()+planVO.getPersonTotalCount());
                //todo 实际金额统计
            }
            if (!Objects.isNull(planVO)){
//                planVO.setSheetHeadsMap(planVOHeadMap);
                planVO.setStatus(planVO.getCostList().get(0).getStatus());
                planVO.setStatusName(statusToName.getOrDefault(planVO.getStatus(),""));
            }
            planVOList.add(planVO);
        }

        return planVOList;
    }


    @Override
    public List<ContractCenterPlanVO> planList(ContractCenterPlanListDTO contractCenterPlanListDTO) {
        if (ObjectUtil.isNull(contractCenterPlanListDTO)){
            return null;
        }
        List<ContractCenterPlanVO> res;
        LambdaQueryWrapperX<ContractMain> wrapperX = new LambdaQueryWrapperX<>(ContractMain.class);
        wrapperX.like(ContractMain::getYear, contractCenterPlanListDTO.getYear());
        wrapperX.eq(ContractMain::getContractNumber, contractCenterPlanListDTO.getContractNumber());
        ContractMain byId = contractMainService.getOne(wrapperX);

        if (ObjectUtil.isNull(byId)){
            throw new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB.getErrorCode(),"数据不存在");
        }

        if (byId.getStatus()!=121){
            if (StringUtils.hasText(contractCenterPlanListDTO.getCenterCode())){
                res = this.getBaseMapper().planList(contractCenterPlanListDTO.getContractNumber(),
                        contractCenterPlanListDTO.getCenterCode(),
                        contractCenterPlanListDTO.getYear());
                if (!CollectionUtils.isEmpty(res)){
                    res.forEach(vo->{
                        vo.setTotalAmount(vo.getUnitPrice().multiply(BigDecimal.valueOf(vo.getNum())));
                    });
                }

            }else{
                res = this.getBaseMapper().planListNoCenter(contractCenterPlanListDTO.getContractNumber(), contractCenterPlanListDTO.getYear());
                res.forEach(vo->{
                    vo.setTotalAmount(vo.getUnitPrice().multiply(BigDecimal.valueOf(vo.getNum())));
                });
            }
        }else {
                res = this.getBaseMapper().planListNoCenterAndPlan(contractCenterPlanListDTO.getContractNumber(), contractCenterPlanListDTO.getYear());
            res.forEach(vo->{
                vo.setTotalAmount(BigDecimal.valueOf(0));
            });
        }


        return res;
    }

    /**
     * 封装
     * @param planVos 参数
     * @param allCost 参数
     * @return 结果
     */
    private List<PlanVO> getPlanVOPublic(List<PlanVO> planVos, List<CostVO> allCost){
        if (!CollectionUtils.isEmpty(allCost)){
            Map<String, List<CostVO>> numberToCostVo = allCost.stream().collect(Collectors.groupingBy(CostVO::getContractNumber));

            Map<Integer, String> planStatus = ContractPlanStatusConstant.getMap();
            Map<Integer, String> contractStatus = NfcContractStatus.getMap();

            for (PlanVO planVo : planVos) {
                //状态封装
                planVo.setContractStatusName(contractStatus.getOrDefault(planVo.getContractStatus(), ""));
                planVo.setStatusName(planStatus.getOrDefault(planVo.getStatus(), ""));

                //人力成本
                BigDecimal totalBudget = new BigDecimal(0);
                BigDecimal actualMoney = new BigDecimal(0);
                //岗级成本
                BigDecimal positionTotalBudget = new BigDecimal(0);
                BigDecimal positionActualMoney = new BigDecimal(0);
                //岗级人数
                Integer positionPersonTotalNum = 0;
                Integer positionPersonActualNum = 0;
                //成本list
                List<CostVO> costList = new ArrayList<>();
                List<CostVO> groupByNumber = numberToCostVo.getOrDefault(planVo.getContractNumber(), new ArrayList<>());
                Map<String, List<CostVO>> groupByType = groupByNumber.stream().collect(Collectors.groupingBy(CostVO::getCostType));
                Set<Map.Entry<String, List<CostVO>>> entries = groupByType.entrySet();

                //获取表头map
                Map<String, BigDecimal> sheetHeads = ContractMainServiceImpl.SheetHeadHandler.getSheetHeads(dictRedisHelper);

                for (Map.Entry<String, List<CostVO>> entry : entries) {
                    List<CostVO> values = entry.getValue();
                    //类型封装
                    CostVO typeCostVo = new CostVO();
                    for (CostVO costVO:values) {
                        if (planVo.getCenterCode().equals(costVO.getCenterCode())){
                            //计算总成本
                            if (costVO.getUnitPrice()!=null&&costVO.getPlanPersonNum()!=null){
                                costVO.setTotalBudget(costVO.getUnitPrice().multiply(BigDecimal.valueOf(costVO.getPlanPersonNum())));
                            }
                            totalBudget = totalBudget.add(costVO.getTotalBudget());
                            if ("岗级成本".equals(costVO.getCostType())){
                                positionTotalBudget = positionTotalBudget.add(costVO.getTotalBudget());
                                positionPersonTotalNum += costVO.getPersonNum();
                            }
                            if (ObjectUtil.isNull(typeCostVo)){
                                typeCostVo.setCostType(costVO.getCostType());
                                typeCostVo.setCostTypeName(costVO.getCostTypeName());
                                typeCostVo.setTotalBudget(costVO.getTotalBudget());
                                typeCostVo.setContractNumber(costVO.getContractNumber());
                                typeCostVo.setCenterCode(costVO.getCenterCode());
                                continue;
                            }
                            typeCostVo.setTotalBudget(typeCostVo.getTotalBudget().add(costVO.getTotalBudget()));
                            ContractMainServiceImpl.SheetHeadHandler.setAmount(costVO, sheetHeads);
                            typeCostVo.setStatus(costVO.getStatus());
                        }
                    }
                    if (!ObjectUtil.isNull(typeCostVo)){
                        costList.add(typeCostVo);
                    }
                }
                planVo.setPositionTotalMoney(positionTotalBudget);
                planVo.setPersonTotalBudget(totalBudget);
                planVo.setPersonTotalCount(positionPersonTotalNum);
                //list
                planVo.setCostList(costList);
                log.info("表头信息为:{}",sheetHeads);
                planVo.setSheetHeadsMap(sheetHeads);
                log.info("赋值之后表头信息为:{}",planVo.getSheetHeadsMap());
            }
        }
        return planVos;
    }

    /**
     * 根据角色获取相对应权限的数据
     * @param year 年度
     * @return 结果
     */
    public List<PlanVO> getByUserRolePlanVo(Integer year){
        Boolean res = roleJudge();
        if (res){
            return this.getBaseMapper().getPlanGroupByCenter(year);
        }
        SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<DeptVO> allOrganizationList = simplerUser.getAllOrganizationList();
        if (CollectionUtils.isEmpty(allOrganizationList)){
            throw new BaseException(MyExceptionCode.ERROR_PARAM.getErrorCode(),"用户没有所属部门");
        }
        List<String> codeList = allOrganizationList.stream().map(DeptVO::getDeptCode).collect(Collectors.toList());
        return this.getBaseMapper().getPlanGroupByCenterByDeptCode(codeList, year);
    }

    /**
     * 判断当前用户是否为技术配置员或合同财务
     * @return boolean
     */
    @Override
    public Boolean roleJudge(){
        UserVO userVo = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        List<RoleVO> roles = userVo.getRoles();
        Map<String, RoleVO> collect = roles.stream().collect(Collectors.toMap(RoleVO::getCode, Function.identity()));
        return(!Objects.isNull(collect.getOrDefault(RoleCode.technologyConfig, null))||
                !Objects.isNull(collect.getOrDefault(RoleCode.contractFinancial, null)));
    }


    public static class ContractCenterPlanExcelListener extends AnalysisEventListener<ContractCenterPlanDTO> {

        private final List<ContractCenterPlanDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractCenterPlanDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractCenterPlanDTO> getData() {
            return data;
        }
    }


}
