package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.allocation.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.service.ResourceAllocationOfService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/resource-allocation-of")
@Api(tags = "人员物资调配FOCK")
public class ResourceAllocationOfController {


    @Autowired
    private ResourceAllocationOfService resourceAllocationOfService;

    @Autowired
    private PersonMangeService personMangeService;

    @ApiOperation(value = "人员调配列表查询")
    @PostMapping("/resourceAllocation/person/list")
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员物资调配FOCK】-【人员调配】数据", type = "ResourceAllocationOf", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<DeptTreeVO>> persionLlist(@RequestBody QueryParams param) {

        if (param.getSearchType() == null || "".equals(param.getSearchType())) {
            param.setSearchType("p");
        }

        if (param.getRealStartDate() == null || "".equals(param.getRealStartDate()) ||
                param.getRealEndDate() == null || "".equals(param.getRealEndDate())) {

            //获取时间范围
            LocalDate now = LocalDate.now();
            LocalDate firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate lastDayNextMonth = now.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("YYYY-MM-dd");

            param.setRealStartDate(firstDayOfMonth.format(formatter));
            param.setRealEndDate(lastDayNextMonth.format(formatter));
        }

        //约定 p 表示人员 m 表示物资
        List<DeptTreeVO> dtoList = resourceAllocationOfService.getResourceAllocationOfPersonMaterial(param);
        if (dtoList.size() == 0 || dtoList == null) {
            return new ResponseDTO<>(200, new ArrayList<DeptTreeVO>(), "未获取到userId为" + param.getStaffNo() + "的数据");
        }
        return new ResponseDTO<>(dtoList);
    }

    @ApiOperation(value = "调配计划删除")
    @PostMapping("/resourceAllocation/persion/delete")
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员物资调配FOCK】-【调配计划删除】数据", type = "ResourceAllocationOf", subType = "删除", bizNo = "")
    public ResponseDTO updateAllocationByIdAndType(@RequestBody AllocationOfPersonMaterialDTO dto) {
        Integer integer = resourceAllocationOfService.deleteResourceAllocationOfPersonMaterial(dto);
        if (integer.equals(0)) {
            return ResponseDTO.failure(500, "删除失败");
        }
        return ResponseDTO.success("删除成功" + integer + "条数据");
    }

    @ApiOperation(value = "调配计划更新")
    @PostMapping("/resourceAllocation/persion/update")
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员物资调配FOCK】-【调配计划更新】数据", type = "ResourceAllocationOf", subType = "更新", bizNo = "")
    public ResponseDTO deleteAllocationByIdAndType(@RequestBody Map param) {

        if (param.containsKey("updateType") || StringUtils.isNotBlank(param.get("updateType").toString())) {
            return ResponseDTO.failure(3001, "参数不正确");
        }
        if (!param.containsKey("id") || !StringUtils.isNotBlank(param.get("id").toString())) {
            return ResponseDTO.failure(3001, "参数不正确");
        }

        if (!param.containsKey("realStartDate") || !StringUtils.isNotBlank(param.get("realStartDate").toString()) ||
                !param.containsKey("realEndDate") || !StringUtils.isNotBlank(param.get("realEndDate").toString()) ||
                !LocalDate.parse(param.get("realStartDate").toString()).isAfter(LocalDate.parse(param.get("realEndDate").toString()))) {
            return ResponseDTO.failure(3001, "参数不正确");
        }
        Integer updateFlag = resourceAllocationOfService.updateResourceAllocationOfPersonMaterial(param);
        if (updateFlag.equals(0)) {
            return ResponseDTO.failure(500, "更新失败");
        }
        return ResponseDTO.success();
    }

    @ApiOperation(value = "调配计划新增")
    @PostMapping("/resourceAllocation/persion/add")
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员物资调配FOCK】-【调配计划新增】数据", type = "ResourceAllocationOf", subType = "新增", bizNo = "")
    public ResponseDTO addAllocationByIdAndType(@RequestBody AllocationOfPersonMaterialDTO dto) {
        List<MarkBusinessRow> markBusinessRows = dto.getMarkBusinessRows();
        if (ObjectUtils.isEmpty(markBusinessRows)) {
            return ResponseDTO.failure(3001, "参数不正确");
        }

        Integer result = resourceAllocationOfService.addResourceAllocationOfPersonMaterial(dto);
        if (result > 0) {
            return ResponseDTO.success();
        } else {
            return ResponseDTO.failure(500, "新增计划失败");
        }

    }

    @ApiOperation(value = "查询大修轮次")
    @PostMapping("/resourceAllocation/queryRepairPlanAndSpecialtyTeam")
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员物资调配FOCK】-【查询大修轮次】数据", type = "ResourceAllocationOf", subType = "查询", bizNo = "")
    public ResponseDTO queryRepairPlan(@RequestBody PlanAndSpecialtyTeamVo planAndSpecialtyTeamVo) {
        Map<String, Object> resultMap = new HashMap<>();
        //Search Team
        List<SpecialtyAndTeam> specialtyAndTeams = resourceAllocationOfService.querySpecialtyAndTeam(planAndSpecialtyTeamVo.getStaffNo());
        //构建数据
        if (!specialtyAndTeams.isEmpty()) {
            List<Map<String, Object>> specialtyAndTeamMap = buildSpecialtyAndTeams(specialtyAndTeams);
            resultMap.put("specialtyAndTeams", specialtyAndTeamMap);
        }
        List<RepairRoundAnd> repairRoundAnds = resourceAllocationOfService.queryRepairPlan(planAndSpecialtyTeamVo.getRepairRound());
        List<Map<String, Object>> buildRepairRoundAnds = buildRepairRoundAnds(repairRoundAnds);
        resultMap.put("repairRoundAnds", buildRepairRoundAnds);
        return ResponseDTO.success(resultMap);
    }


    public List<Map<String, Object>> buildSpecialtyAndTeams(List<SpecialtyAndTeam> specialtyAndTeams) {
        Map<String, List<SpecialtyAndTeam>> groupedBySpecialtyNames = specialtyAndTeams.stream()
                .collect(Collectors.groupingBy(SpecialtyAndTeam::getSpecialtyName));

        List<Map<String, Object>> locations = groupedBySpecialtyNames.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> location = new HashMap<>();
                    location.put("label", entry.getKey()); // 专业名称作为label
                    List<Map<String, String>> options = entry.getValue().stream()
                            .map(emp -> {
                                Map<String, String> option = new HashMap<>();
                                option.put("value", emp.getSpecialtyCode() + "-" + emp.getTeamCode() + "-" + emp.getSpecialtyName() + "-" + emp.getTeamName());
                                option.put("label", emp.getTeamName()); // 提取工号后缀作为标签
                                return option;
                            })
                            .collect(Collectors.toList());
                    location.put("options", options); // 将选项列表添加到location
                    return location;
                })
                .collect(Collectors.toList());


        return locations;
    }

    public List<Map<String, Object>> buildRepairRoundAnds(List<RepairRoundAnd> repairRoundAnds) {
        Map<String, List<RepairRoundAnd>> groupedByBaseNames = repairRoundAnds.stream()
                .collect(Collectors.groupingBy(RepairRoundAnd::getBaseName));

        List<Map<String, Object>> locations = groupedByBaseNames.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> location = new HashMap<>();
                    location.put("label", entry.getKey()); // 基地名称作为label
                    List<Map<String, String>> options = entry.getValue().stream()
                            .map(emp -> {
                                Map<String, String> option = new HashMap<>();
                                option.put("value", emp.getBaseCode() + "-" + emp.getBaseName() + "-" + emp.getRepairRound() + "-" + emp.getRepairName());
                                option.put("label", emp.getRepairName()); // 提取大修轮次后缀作为标签
                                option.put("repairRoundCode", emp.getRepairRound()); // 提取大修轮次后缀作为标签
                                return option;
                            })
                            .collect(Collectors.toList());
                    location.put("options", options); // 将选项列表添加到location
                    return location;
                })
                .collect(Collectors.toList());
        return locations;
    }
}
