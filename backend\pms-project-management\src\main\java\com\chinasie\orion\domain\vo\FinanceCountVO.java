package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FinanceCountVO  implements Serializable {
    @ApiModelProperty(value = "发票信息")
   private  InvoiceInformationVO invoiceInformationVO;
    @ApiModelProperty(value = "开票收入核算信息表")
   private  InvoicingRevenueAccountingVO  invoicingRevenueAccountingVO;
    @ApiModelProperty(value = "暂估收入核算信息")
   private  ProvisionalIncomeAccountingVO provisionalIncomeAccountingVO;
    @ApiModelProperty(value = "预收款开票挂账信息")
   private  AdvancePaymentInvoicedVO advancePaymentInvoicedVO;

}
