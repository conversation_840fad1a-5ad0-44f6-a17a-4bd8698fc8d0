package com.chinasie.orion.management.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.vo.RequirementNoticeVO;
import com.chinasie.orion.management.domain.dto.QuotationManagementDTO;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * QuotationManagement Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@Mapper
public interface QuotationManagementMapper extends OrionBaseMapper<QuotationManagement> {

    /**
     * 查询主数据
     *
     * @param page  表格对象
     * @param query 查询条件
     * @return page
     */
    Page<QuotationManagementVO> queryByPage(Page<?> page, @Param("param") QuotationManagementDTO query);

    /**
     * 根据条件查询需要提醒的需求对接人
     * @return
     */
    List<RequirementNoticeVO> queryData();

}