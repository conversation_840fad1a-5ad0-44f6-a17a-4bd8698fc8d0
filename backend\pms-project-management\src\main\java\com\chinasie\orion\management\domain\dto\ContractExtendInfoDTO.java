package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractExtendInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractExtendInfoDTO对象", description = "合同拓展信息")
@Data
@ExcelIgnoreUnannotated
public class ContractExtendInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 采购组织
     */
    @ApiModelProperty(value = "采购组织")
    @ExcelProperty(value = "采购组织 ", index = 0)
    private String procurementOrgName;

    /**
     * 采购组织ID
     */
    @ApiModelProperty(value = "采购组织ID")
    @ExcelProperty(value = "采购组织ID ", index = 1)
    private String procurementOrgId;

    /**
     * 采购组
     */
    @ApiModelProperty(value = "采购组")
    @ExcelProperty(value = "采购组 ", index = 2)
    private String procurementGroupName;

    /**
     * 采购组ID
     */
    @ApiModelProperty(value = "采购组ID")
    @ExcelProperty(value = "采购组ID ", index = 3)
    private String procurementGroupId;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @ExcelProperty(value = "商务负责人 ", index = 4)
    private String businessRspUser;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @ExcelProperty(value = "商务负责人ID ", index = 5)
    private String businessRspUserId;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @ExcelProperty(value = "技术负责人 ", index = 6)
    private String technicalRspUser;

    /**
     * 技术负责人ID
     */
    @ApiModelProperty(value = "技术负责人ID")
    @ExcelProperty(value = "技术负责人ID ", index = 7)
    private String technicalRspUserId;

    /**
     * 推荐依据
     */
    @ApiModelProperty(value = "推荐依据")
    @ExcelProperty(value = "推荐依据 ", index = 8)
    private String recommendationBasis;

    /**
     * 节省总金额（RMB）
     */
    @ApiModelProperty(value = "节省总金额（RMB）")
    @ExcelProperty(value = "节省总金额（RMB） ", index = 9)
    private BigDecimal negotiateSaveAmount;

    /**
     * 渠道优化节省金额
     */
    @ApiModelProperty(value = "渠道优化节省金额")
    @ExcelProperty(value = "渠道优化节省金额 ", index = 10)
    private BigDecimal sumSaveAmount;

    /**
     * 谈判节省金额
     */
    @ApiModelProperty(value = "谈判节省金额")
    @ExcelProperty(value = "谈判节省金额 ", index = 11)
    private BigDecimal compareSaveAmount;

    /**
     * 与立项相比节省金额
     */
    @ApiModelProperty(value = "与立项相比节省金额")
    @ExcelProperty(value = "与立项相比节省金额 ", index = 12)
    private BigDecimal channelSaveAmount;

    /**
     * 优化采购节省金额
     */
    @ApiModelProperty(value = "优化采购节省金额")
    @ExcelProperty(value = "优化采购节省金额 ", index = 13)
    private BigDecimal optimizeSaveAmount;

    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    @ExcelProperty(value = "是否办理履约保证金 ", index = 14)
    private Boolean isProcessAmount;

    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    @ExcelProperty(value = "保证金支付方式 ", index = 15)
    private String prcessAmountPayWay;

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @ExcelProperty(value = "保证金 ", index = 16)
    private String prcessAmount;

    /**
     * 账户名称
     */
    @ApiModelProperty(value = "账户名称")
    @ExcelProperty(value = "账户名称 ", index = 17)
    private String accountName;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @ExcelProperty(value = "银行账号 ", index = 18)
    private String bankName;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    @ExcelProperty(value = "开户银行 ", index = 19)
    private String bankAccount;

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    @ExcelProperty(value = "银行代码 ", index = 20)
    private String bankCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 21)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 22)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 23)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 24)
    private String contractName;
}
