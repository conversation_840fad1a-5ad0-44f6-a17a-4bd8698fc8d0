import dayjs from 'dayjs';
import { h } from 'vue';
import { Tooltip } from 'ant-design-vue';
import { isPower } from 'lyra-component-vue3';

export function getColumns(type: string, positionType:string, navDetails: (id: string) => void) {
  const columns: any[] = [
    {
      title: '员工号',
      dataIndex: 'userCode',
      customRender({ text, record }) {
        if (isPower('PMS_JSZCRYK_container_01_button_09', record?.rdAuthList)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
      width: 80,
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      width: 80,
    },
    {
      title: '公司',
      dataIndex: 'companyName',
      minWidth: 280,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 60,
    },
    {
      title: '民族',
      dataIndex: 'nation',
      width: 80,
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
      width: 180,
    },
    {
      title: '用人部门',
      show: ['support'],
      dataIndex: 'deptName',
      width: 110,
    },
    {
      title: '政治面貌',
      show: ['configuration'],
      dataIndex: 'politicalAffiliation',
      width: 110,
    },
    {
      title: '岗位授权',
      dataIndex: 'postAuthorizes',
      width: 110,
    },
    {
      title: '证书信息',
      dataIndex: 'certificateNames',
      width: 110,
    },
    {
      title: '参加工作时间',
      dataIndex: 'joinWorkTime',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '到岗时间',
      dataIndex: 'addWorkTime',
      show: ['support'],
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '离岗时间',
      dataIndex: 'leaveWorkTime',
      show: ['leave'],
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '人员状态',
      show: ['support'],
      dataIndex: 'userStatus',
      width: 110,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 160,
      fixed: 'right',
    },
  ];
  return columns.filter((item) => item.show === undefined || item.show.includes(type) || item.show.includes(positionType));
}
