package com.chinasie.orion.domain.vo.investmentschemeReport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ColumnWidth(15)
public class ExportTotalDoByExcelVO {


    /**
     * 序号
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "序号"}, index = 0)
    private String order;


//    /**
//     * 项目所属公司
//     */
//    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目所属公司"}, index = 1)
//    private String companyName;


    /**
     * 年度
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "年度"}, index = 2)
    private String yearName;


    /**
     * 项目编码
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目编码"}, index = 3)
    private String projectNumber;

    /**
     * 项目名称
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目名称"}, index = 4)
    private String projectName;


    /**
     * 项目状态
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目状态"}, index = 5)
    private String projectStatusName;


    /**
     * 项目处室
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目处室"}, index = 6)
    private String rspDeptName;

    /**
     * 项目负责人
     */
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "项目负责人"}, index = 7)
    private String rspUserName;

    /**
     * 年度投资计划
     */
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "年度投资计划"}, index = 8)
    private String total;


    /**
     * 调整后年度投资计划
     */
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "调整后年度投资计划"}, index = 9)
    private String totalChange;


    /**
     * 年度投资计划执行
     */
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "年度投资计划执行"}, index = 10)
    private String totalDo;


    /**
     * 年度投资计划执行率
     */
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "年度投资计划执行率"}, index = 11)
    private String totalDoPercent;


    /**
     * 结转投资
     */
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "结转投资"}, index = 12)
    private String balance;

    /**
     * 累计Y年下达投资计划
     */
    @ApiModelProperty(value = "累计Y年下达投资计划")
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "至Y年累计下达投资计划"}, index = 13)
    private String cutOffGiveY;

    /**
     * 累计Y年投资计划完成
     */
    @ApiModelProperty(value = "累计Y年投资计划完成")
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "至Y年累计完成投资计划"}, index = 14)
    private String cutOffCompleteY;


    /**
     * 至Y年累计完成率
     */
    @ApiModelProperty(value = "至Y年累计完成率")
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "至Y年累计完成率"}, index = 15)
    private String finishRate;

    /**
     * 待执行概算
     */
    @ApiModelProperty(value = "待执行概算")
    @ExcelProperty(value = {"投资计划总体执行报表  单位：万元", "待执行概算"}, index = 16)
    private String notExecuteGive;
}
