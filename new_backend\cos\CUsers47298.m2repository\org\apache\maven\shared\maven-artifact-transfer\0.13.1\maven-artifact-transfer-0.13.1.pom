<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>maven-shared-components</artifactId>
    <groupId>org.apache.maven.shared</groupId>
    <version>34</version>
    <relativePath>../../pom/maven/maven-shared-components/pom.xml/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>maven-artifact-transfer</artifactId>
  <name>Apache Maven Artifact Transfer</name>
  <version>0.13.1</version>
  <description>An API to install, deploy and resolving artifacts with Maven 3</description>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MSHARED</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-builds.apache.org/job/Maven/job/maven-box/job/maven-artifact-transfer/</url>
  </ciManagement>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-artifact-transfer.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-artifact-transfer.git</developerConnection>
    <tag>maven-artifact-transfer-0.13.1</tag>
    <url>https://github.com/apache/maven-artifact-transfer/tree/${project.scm.tag}</url>
  </scm>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>org.apache.maven.shared.artifact.transfer</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <showDeprecation>true</showDeprecation>
          <compilerArgs>
            <arg>-Xlint:deprecation</arg>
            <arg>-Xlint:unchecked</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <artifactSet>
                <includes>
                  <include>org.eclipse.aether:aether-util</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>org.eclipse.aether:aether-util</artifact>
                  <includes>
                    <include>org/eclipse/aether/util/artifact/SubArtifact.class</include>
                    <include>org/eclipse/aether/util/filter/*</include>
                  </includes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>dependency-reduced-pom.xml</exclude>
            <exclude>src/it/**/.settings/**</exclude>
            <exclude>src/it/**/.project</exclude>
            <exclude>src/it/**/.classpath</exclude>
            <exclude>src/it/**/target/**</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.19</version>
        <executions>
          <execution>
            <id>sniff</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java17</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-invoker-plugin</artifactId>
            <executions>
              <execution>
                <id>pre-integration-tests</id>
                <goals>
                  <goal>install</goal>
                </goals>
              </execution>
              <execution>
                <id>integration-tests</id>
                <goals>
                  <goal>run</goal>
                </goals>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>3.0.7</version>
              </dependency>
              <dependency>
                <groupId>org.codehaus.gmaven.runtime</groupId>
                <artifactId>gmaven-runtime-2.0</artifactId>
                <version>1.5</version>
              </dependency>
            </dependencies>
            <configuration>
              <addTestClassPath>true</addTestClassPath>
              <debug>false</debug>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <projectsDirectory>src/it</projectsDirectory>
              <showVersion>false</showVersion>
              <pomIncludes>
                <pomInclude>*/pom.xml</pomInclude>
              </pomIncludes>
              <preBuildHookScript>setup</preBuildHookScript>
              <postBuildHookScript>verify</postBuildHookScript>
              <settingsFile>src/it/settings.xml</settingsFile>
              <parallelThreads>1</parallelThreads>
              <filterProperties>
                <repository.proxy.url>${repository.proxy.url}</repository.proxy.url>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              </filterProperties>
              <extraArtifacts>
                <extraArtifact>org.apache.maven:apache-maven:3.0.5:tar.gz:bin</extraArtifact>
                <extraArtifact>org.apache.maven:apache-maven:3.1.1:tar.gz:bin</extraArtifact>
                <extraArtifact>org.apache.maven:apache-maven:3.2.5:tar.gz:bin</extraArtifact>
                <extraArtifact>org.apache.maven:apache-maven:3.3.1:tar.gz:bin</extraArtifact>
                <extraArtifact>org.apache.maven:apache-maven:3.3.9:tar.gz:bin</extraArtifact>
                <extraArtifact>org.apache.maven:apache-maven:3.5.0:tar.gz:bin</extraArtifact>
              </extraArtifacts>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <version>2.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-common-artifact-filters</artifactId>
      <version>3.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-util</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-impl</artifactId>
      <version>1.7</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>aether-spi</artifactId>
          <groupId>org.sonatype.aether</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>0.9.0.M2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-impl</artifactId>
      <version>0.9.0.M2</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>aether-spi</artifactId>
          <groupId>org.eclipse.aether</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.28.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>
  <properties>
    <javaVersion>7</javaVersion>
    <project.build.outputTimestamp>2020-12-22T10:31:40Z</project.build.outputTimestamp>
  </properties>
</project>
