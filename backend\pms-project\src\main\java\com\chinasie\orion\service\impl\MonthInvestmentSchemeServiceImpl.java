package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.MonthInvestmentScheme;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.MonthInvestmentSchemeRepository;
import com.chinasie.orion.service.MonthInvestmentSchemeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * MonthInvestmentScheme 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:17:18
 */
@Service
public class MonthInvestmentSchemeServiceImpl extends OrionBaseServiceImpl<MonthInvestmentSchemeRepository,MonthInvestmentScheme> implements MonthInvestmentSchemeService {

}
