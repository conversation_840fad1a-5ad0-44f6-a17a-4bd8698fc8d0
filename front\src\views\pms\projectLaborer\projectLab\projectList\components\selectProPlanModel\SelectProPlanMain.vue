<template>
  <ModalSearch
    ref="searchRef"
    :schemas="schemas"
    :span="4"
  />
  <div class="table-wrap">
    <OrionTable
      ref="tableRef"
      :isTableHeader="false"
      :options="tableOption"
    />
  </div>
</template>

<script setup lang="ts">
import {
  h, inject, nextTick, onMounted, Ref, ref, unref,
} from 'vue';
import { DataStatusTag, FormSchema, OrionTable } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import ModalSearch from './ModalSearch.vue';
import {
  getPlanSchemeLevel, postSchemePages, postSourceSearch,
} from '/@/views/pms/api';

const emit = defineEmits(['initTable']);

const userOrgList:Ref = inject('userOrgList');
const keyword: Ref<string> = ref('');
const planSourceId: Ref<string> = ref('');
const level = ref<string>('');
const rspDept:Ref<string> = ref(unref(userOrgList)[0]?.value);
const rspSubDept = ref<string>('');
const rspTeam = ref<string>('');
const searchRef = ref();
const schemas: FormSchema[] = [
  {
    field: 'planSource',
    component: 'ApiSelect',
    label: '计划来源',
    defaultValue: '',
    componentProps: {
      allowClear: false,
      api: async () => {
        let data = [
          {
            name: '全部',
            id: '',
          },
        ];
        try {
          const result = await postSourceSearch();
          data = data.concat(result);
        } catch (e) {

        }
        return data;
      },
      labelField: 'name',
      valueField: 'id',
      onChange(val) {
        planSourceId.value = val;
        updateTable();
      },
    },
  },
  {
    field: 'planLevel',
    component: 'ApiSelect',
    label: '计划层级',
    defaultValue: '',
    componentProps: {
      allowClear: false,
      api: async () => {
        let data = [
          {
            description: '全部',
            value: '',
          },
        ];
        try {
          const result = await getPlanSchemeLevel();
          data = data.concat(result);
        } catch (e) {

        }
        return data;
      },
      labelField: 'description',
      valueField: 'value',
      onChange(val) {
        level.value = val;
        updateTable();
      },
    },
  },
  {
    field: 'rspDept',
    component: 'Select',
    label: '责任部门',
    defaultValue: unref(rspDept),
    componentProps: {
      allowClear: false,
      options: unref(userOrgList),
      onChange(val) {
        let index = unref(userOrgList).findIndex((item) => item.value === val);
        rspDept.value = '';
        rspSubDept.value = '';
        rspTeam.value = '';
        switch (index) {
          case 0:
            rspDept.value = val;
            break;
          case 1:
            rspSubDept.value = val;
            break;
          case 2:
            rspTeam.value = val;
            break;
        }
        updateTable();
      },
    },
  },
  {
    field: 'keyword',
    component: 'InputSearch',
    label: '',
    componentProps: {
      placeholder: '请输入关键字',
      onSearch(val) {
        keyword.value = val;
        updateTable();
      },
    },
  },
];

const tableRef = ref();

const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    width: 220,
  },
  {
    title: '计划名称',
    dataIndex: 'name',
    width: 240,
  },
  {
    title: '是否例行计划',
    dataIndex: 'routine',
    width: 140,
    customRender({ text }) {
      return h('div', text === 1 ? '是' : '否');
    },
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender({ text }) {
      return text ? h(DataStatusTag, {
        statusData: text,
      }) : '';
    },
  },
  {
    title: '计划来源',
    dataIndex: 'planSourceName',
  },
  {
    title: '计划层级',
    dataIndex: 'levelName',
  },
  {
    title: '责任部门',
    dataIndex: 'rspDeptName',
  },
  {
    title: '计划负责人',
    dataIndex: 'rspUserName',
  },
  {
    title: '计划开始时间',
    dataIndex: 'beginTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '计划完成时间',
    dataIndex: 'endTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
];

const dataSource = ref([]);

const tableOption = {
  rowSelection: {},
  api: (params) => postSchemePages({
    ...params,
    searchConditions: [
      [
        (unref(keyword) ? {
          field: 'name',
          fieldType: 'string',
          queryType: 'like',
          values: [unref(keyword)],
        } : undefined),
        (rspDept.value ? {
          field: 'rspDept',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspDept.value],
        } : undefined),
        (rspSubDept.value ? {
          field: 'rspSubDept',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspSubDept.value],
        } : undefined),
        (rspTeam.value ? {
          field: 'rspTeam',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspTeam.value],
        } : undefined),
        (unref(level) ? {
          field: 'level',
          fieldType: 'string',
          queryType: 'eq',
          values: [unref(level)],
        } : undefined),
        {
          field: 'status',
          fieldType: 'string',
          queryType: 'in',
          values: [
            100,
            104,
            101,
            130,
            105,
            106,
            107,
            109,
          ],
        },
        (unref(planSourceId) ? {
          field: 'planSourceId',
          fieldType: 'string',
          queryType: 'eq',
          values: [unref(planSourceId)],
        } : undefined),
      ].filter((item) => item),
      [
        (unref(keyword) ? {
          field: 'number',
          fieldType: 'string',
          queryType: 'like',
          values: [unref(keyword)],
        } : undefined),
        (rspDept.value ? {
          field: 'rspDept',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspDept.value],
        } : undefined),
        (rspSubDept.value ? {
          field: 'rspSubDept',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspSubDept.value],
        } : undefined),
        (rspTeam.value ? {
          field: 'rspTeam',
          fieldType: 'string',
          queryType: 'eq',
          values: [rspTeam.value],
        } : undefined),
        (unref(level) ? {
          field: 'level',
          fieldType: 'string',
          queryType: 'eq',
          values: [unref(level)],
        } : undefined),
        {
          field: 'status',
          fieldType: 'string',
          queryType: 'in',
          values: [
            100,
            104,
            101,
            130,
            105,
            106,
            107,
            109,
          ],
        },
        (unref(planSourceId) ? {
          field: 'planSourceId',
          fieldType: 'string',
          queryType: 'eq',
          values: [unref(planSourceId)],
        } : undefined),
      ].filter((item) => item),
    ].filter((item) => item && item.length),
  }),
  columns,
  dataSource,
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
};

onMounted(() => {
  emit('initTable', unref(tableRef));
});

// 更新表格
function updateTable() {
  nextTick(() => {
    tableRef.value?.reload({ page: 1 });
  });
}

</script>

<style scoped lang="less">
.table-wrap {
  height: 460px;
  overflow: hidden;
}
</style>
