package com.chinasie.orion.bo;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/03/10/10:45
 * @description:
 */
@Component
public class DataClassNameBO {


    @Autowired
    private ClassRedisHelper classRedisHelper;

    public String getClassNameByDataId(String dataId){
        if(StringUtils.hasText(dataId)){
            ClassVO data = classRedisHelper.getClassVOByDataId(dataId);
            return  null != data?data.getClassName():"ProductPBS";
        }
        return "";
    }


    public String getTableName(String dataId){
        if(StringUtils.hasText(dataId)){
            ClassVO data = classRedisHelper.getClassVOByDataId(dataId);
            return  null != data?data.getTableName():"";
        }
        return "";
    }
}
