<script setup lang="ts">
import {
  BasicButton, IDataStatus, Layout3, Select,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watch, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import {
  UseBusinessWorkflowReturn, WorkflowAction, WorkflowProps, WorkflowView,
} from 'lyra-workflow-component-vue3';
import { message } from 'ant-design-vue';
import MeteringOrder from './components/MeteringOrder.vue';
import { useUserStore } from '/@/store/modules/user';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const userInfo = useUserStore().getUserInfo;
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.jobManageName,
  projectCode: detailsData.number,
  ownerName: detailsData?.ownerName,
  dataStatus: detailsData?.dataStatus,
}));

const menuData = computed(() => [
  {
    id: 'order',
    name: '工单详情',
    powerCode: 'PMS_JTJLJDXQ_container_02',
  },
  {
    id: 'workflow',
    name: '审批流程',
    powerCode: 'PMS_JTJLJDXQ_container_03',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/major-repair-plan-meter-reduce').fetch({
      pageCode: 'PMSMajorRepairsMeteringDetails',
    }, dataId.value, 'GET');
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

const workflowActionRef: Ref<UseBusinessWorkflowReturn> = ref(null);
const workflowViewRef: Ref<UseBusinessWorkflowReturn> = ref(null);
const workflowProps = computed(() => ({
  Api,
  businessData: {
    ...detailsData,
    name: detailsData?.jobManageName,
  },
  async beforeEvent(type: string) {
    if (type === 'pass') {
      if (detailsData.isContinueUse === undefined || detailsData.isContinueUse === null) {
        message.info('请选择是否可沿用');
        return Promise.reject();
      }
    }
  },
  async afterEvent() {
    await getDetails();
    await workflowViewRef.value?.init();
    await getApproveNodeByBusinessId();
  },
} as WorkflowProps));

const loadingBtn: Ref<boolean> = ref(false);
const isContinueUseOptions = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

const editLoading: Ref<boolean> = ref(false);

async function changeIsContinueUse(isContinueUse: boolean) {
  editLoading.value = true;
  new Api('/pms/major-repair-plan-meter-reduce/edit').fetch({
    id: detailsData?.id,
    isContinueUse,
  }, '', 'PUT').then(() => {
    getDetails();
  }).finally(() => {
    editLoading.value = false;
  });
}

function handleToolButton(operationType: string) {
  switch (operationType) {
    case 'workflow':
      workflowActionRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      }, 'start', loadingBtn);
      break;
  }
}

// 获取当前节点审批人
watch(() => workflowActionRef.value?.isAdd, (value) => {
  if (!value && value !== undefined) {
    getApproveNodeByBusinessId();
  }
});

const approveIds: Ref<string[]> = ref([]);

async function getApproveNodeByBusinessId() {
  const result = await new Api(`/wf/processInstanceAssignee/current-flow-node/assignee/business-id/${dataId.value}`).fetch('', '', 'POST');
  approveIds.value = result || [];
}

const isApprove = computed(() => approveIds.value.includes(userInfo.id));
</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicButton
        v-if="!!workflowActionRef?.isAdd && detailsData?.status !== 130"
        v-is-power="['PMS_JTJLJDXQ_container_01_button_01']"
        type="primary"
        :loading="loadingBtn"
        icon="sie-icon-qidongliucheng"
        @click="handleToolButton('workflow')"
      >
        发起流程
      </BasicButton>
    </template>
    <template v-if="detailsData?.id">
      <MeteringOrder v-if="actionId==='order'" />
      <WorkflowView
        v-if="actionId==='workflow'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template
      v-if="detailsData?.id"
      #footer
    >
      <WorkflowAction
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      >
        <template
          v-if="isApprove"
          #right
        >
          <div class="flex flex-ac">
            <span class="mr10">是否可沿用</span>
            <Select
              :value="detailsData.isContinueUse"
              style="width: 200px"
              :options="isContinueUseOptions"
              @change="changeIsContinueUse"
            />
          </div>
        </template>
      </WorkflowAction>
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
