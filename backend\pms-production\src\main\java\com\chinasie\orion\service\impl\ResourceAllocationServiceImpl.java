package com.chinasie.orion.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.domain.dto.person.ObjJobDTO;
import com.chinasie.orion.domain.dto.source.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.resource.*;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.ResourceAllocationService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:13
 * @description:
 */
@Service
public class ResourceAllocationServiceImpl  implements ResourceAllocationService {

    @Autowired
    private JobPostAuthorizeMapper jobPostAuthorizeMapper;

    @Autowired
    private PersonMangeMapper personMangeMapper;

    @Autowired
    private MaterialManageMapper materialManageMapper;

    @Autowired
    private JobMaterialMapper jobMaterialMapper;

    @Autowired
    private SchemeToPersonMapper schemeToPersonMapper;

    @Autowired
    private SchemeToMaterialMapper schemeToMaterialMapper;


    @Autowired
    private BasicUserService basicUserService;

    @Override
    public PersonSourceAllocationVO personAllocationCount(@NotNull SearchDTO searchDTO) {
        String keyword = searchDTO.getKeyword();
        int  yearNum = searchDTO.getYearNum();
        String  repairRound= searchDTO.getRepairRound();
        int quarterNum= searchDTO.getQuarterNum();
        List<SchemeToPerson> schemeToPersonList =schemeToPersonMapper.getUserListByRepairRound(repairRound,keyword);
        PersonSourceAllocationVO personSourceAllocationVO=new PersonSourceAllocationVO();
        Map<Integer, Date[]>  quarterDates =  this.getQuarterStartAndEndDates(yearNum);
        if(CollectionUtils.isEmpty(schemeToPersonList)){
            personSourceAllocationVO.setPersonSourceVOList(new ArrayList<>());
            personSourceAllocationVO.setQuarterCountVOList(this.initPersonQuarterCount(yearNum,quarterDates));
            return  personSourceAllocationVO;
        }
        // 获取 用户作业数据 不过滤季度
        List<PersonSourceDTO> allDtoList =jobPostAuthorizeMapper.getUserJobInfoList(repairRound,keyword,yearNum);
        // 通过用户编号 分组 对所有数据
        Map<String,List<PersonSourceDTO>> personSourceDTOMap= allDtoList.stream().collect(Collectors.groupingBy(PersonSourceDTO::getUserCode));
        List<String> personIdList= schemeToPersonList.stream().map(SchemeToPerson::getPersonId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //获取当前大修下所有人员的进出时间
        List<PersonInfoDTO> personInfoDTOList=  CollectionUtils.isEmpty(personIdList)?new ArrayList<>(): personMangeMapper.getPersonManageInAndOutDateList(personIdList);
        // 通过人员ID 组装并封装 分组
        Map<String, List<PersonInfoDTO>>  idToList = personInfoDTOList.stream().collect(Collectors.groupingBy(PersonInfoDTO::getId));
        List<PersonSourceVO> personSourceVOceList =new ArrayList<>();

        Map<String,String> personIdToBaseCode= new HashMap<>();
        for (SchemeToPerson scheme : schemeToPersonList) {
            personIdToBaseCode.put(scheme.getPersonId(),scheme.getBaseCode());
        }

        Map<String,SchemeToPerson> userCodeToPersonIdMap = schemeToPersonList.stream().collect(Collectors.toMap(SchemeToPerson::getUserCode,Function.identity(), (v1, v2)->v1));
        List<PersonSourceVO> tailList = new ArrayList<>();
        List<PersonSourceVO> lList = new ArrayList<>();
        // 进行数据过滤 通过季度过滤
        Map<String,Set<String>> quarterToPersonCodeMap = new HashMap<>();
        Date[] quarterStartAndEndDates = quarterDates.get(quarterNum);



        for (Map.Entry<String, SchemeToPerson> stringStringEntry : userCodeToPersonIdMap.entrySet()) {
            String userCode= stringStringEntry.getKey();
            PersonSourceVO personSourceVO = new PersonSourceVO();
            /**
             *  人员作业相关的数据
             */
            List<PersonSourceDTO> personSourceDTOList= personSourceDTOMap.get(userCode);
            SchemeToPerson person=stringStringEntry.getValue();
            String personId = person.getPersonId();
            boolean isHaveWork= Boolean.FALSE;
            // 数据类型: 1-人，2-作业，3.任务
            personSourceVO.setDataType("1");
            personSourceVO.setOverlapCount(0);
            if(!CollectionUtils.isEmpty(personSourceDTOList)){
                // 说明有作业也没有  任务
                // 随机取一条作为 人员第一条总览
                PersonSourceDTO personSourceDTO= personSourceDTOList.get(0);
                // 人员信息
                String fullName =personSourceDTO.getFullName();

                // 人员管理ID
                personId= personSourceDTO.getPersonId();
                personSourceVO.setId(personSourceDTO.getRelationId());
                personSourceVO.setJobId(personSourceDTO.getJobId());
                personSourceVO.setUniqueId(IdUtil.fastUUID());

                personSourceVO.setFullName(fullName);
                personSourceVO.setRepairRound(repairRound);
                personSourceVO.setJobName(personSourceDTO.getJobName());
                personSourceVO.setUserCode(userCode);
                isHaveWork = Boolean.TRUE;
            }else{
                personSourceVO.setUniqueId(personId);
                personSourceVO.setId(personId);
                personSourceVO.setUserCode(userCode);
                personSourceVO.setFullName(person.getUserName());
                personSourceVO.setDataType("1");
            }
            // 设置第一层的 人员类型的 进出时间
            List<PersonInfoDTO> personInfoDTOList1= idToList.get(personId);
            boolean isOutAndInDate= Boolean.FALSE;

            String baseCode= personIdToBaseCode.get(personId);
            if(!CollectionUtils.isEmpty(personInfoDTOList1)){ // 如果有进出时间
                List<InAndOutDateVO> inAndOutDateVOList  = new ArrayList<>();
                List<String>  ukList = new ArrayList<>();
                for (PersonInfoDTO personInfoDTO : personInfoDTOList1) {
                    if(Objects.equals(personInfoDTO.getType(),"1") && Objects.equals(personInfoDTO.getInOrOut(),0)  ){// 人员管理数据 并且带入场 该数据如果存在计划进出时间那么作为
                        this.packageDate(personInfoDTO,repairRound,inAndOutDateVOList,Boolean.FALSE,baseCode,ukList,personInfoDTO.getUniqueKey());
                    }else{// 台账数据
                        if(!Objects.equals(personInfoDTO.getInOrOut(),2)  ){
                            this.packageDate(personInfoDTO,repairRound,inAndOutDateVOList,Boolean.TRUE,baseCode,ukList,personInfoDTO.getUniqueKey());
                        }
                    }
                }
                if(inAndOutDateVOList.size() > 0){
                    isOutAndInDate=Boolean.TRUE;
                }


                if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                    for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
                        Date[] quarterStartAndEndDates22 = integerEntry.getValue();
                        Integer qu= integerEntry.getKey();
                        Date startDate= quarterStartAndEndDates22[0];
                        Date endDate= quarterStartAndEndDates22[1];

                        inAndOutDateVOList.forEach(inAndOutDateVO->{
                            boolean  isOverlap= false;
                            if(inAndOutDateVO.getInDate().compareTo(startDate)>=0 && inAndOutDateVO.getOutDate().compareTo(endDate)<0){
                                if(quarterNum == qu){
                                    inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                                }
                                isOverlap= true;
                            }
                            // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                            if(inAndOutDateVO.getInDate().compareTo(startDate)<0 && inAndOutDateVO.getOutDate().compareTo(startDate)>0 && !isOverlap){
                                if(quarterNum == qu){
                                    inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                                }
                                isOverlap= true;
                            }
                        });
                        if(!inAndOutDateVOList.stream().anyMatch(item->item.isCurrentQuarter())){
                            Random random = new Random();
                            int randomIndex = random.nextInt(inAndOutDateVOList.size()); // 生成一个随机索引
                            InAndOutDateVO selectedItem = inAndOutDateVOList.get(randomIndex); // 获取随机选中的元素
                            selectedItem.setCurrentQuarter(true);
                        }
                    }
                }
                personSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
            }
            if(isHaveWork){
                // 判断数据过滤 条件：任务，作业，
                List<PersonSourceVO> valueList = this.packageJobList(personSourceDTOList,personSourceVO);
                List<PersonSourceVO> filterList = this.fileterPersonList(valueList,quarterToPersonCodeMap,quarterNum,yearNum,personSourceVO,lList);
                if(!CollectionUtils.isEmpty(filterList)){
                    personSourceVOceList.addAll(filterList);
                }
            }else{
                if(isOutAndInDate){
                    // 那么需要果过滤 进出时间
                    personSourceVO.setSort(1);
                    List<InAndOutDateVO> inAndOutDateVOList =  personSourceVO.getInAndOutDateVOList();
                    if(this.isInToQuarterScope(inAndOutDateVOList,quarterDates,quarterNum,quarterToPersonCodeMap,userCode)){
                        lList.add(personSourceVO);
                    }
                    personSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
                }else {
                    // 如果沒有 作业或者任务 那么就直接加入到尾部数据
                    personSourceVO.setSort(0);
                    tailList.add(personSourceVO);
                }
            }
        }
        List<PersonQuarterCountVO> quarterCountVOList = new ArrayList<>();
        for (Map.Entry<Integer, Date[]> integerIntegerEntry : quarterDates.entrySet()) {
            PersonQuarterCountVO quarterCountVO =new PersonQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerIntegerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerIntegerEntry.getKey());
            quarterCountVO.setPersonCount(quarterToPersonCodeMap.getOrDefault(integerIntegerEntry.getKey().toString(),new HashSet<>()).size());
            quarterCountVOList.add(quarterCountVO);
        }
        if(lList.size() >0){
            personSourceVOceList.addAll(lList);
        }
        if(tailList.size()> 0){
            personSourceVOceList.addAll(tailList);
        }
        this.packageMinDateData(personSourceVOceList,personSourceAllocationVO,quarterStartAndEndDates);
        personSourceAllocationVO.setQuarterCountVOList(quarterCountVOList);
        return personSourceAllocationVO;
    }
    public void packageMinDateData(List<PersonSourceVO> personSourceVOceList,PersonSourceAllocationVO personSourceAllocationVO,Date[] quarterStartAndEndDates){
        personSourceVOceList.forEach(item->{
            List<Date> startDate = new ArrayList<>();

            if(Objects.nonNull(item.getJobBeginDate())){
                startDate.add(item.getJobBeginDate());
            }
            if(Objects.nonNull(item.getTaskBeginDate())){
                startDate.add(item.getTaskBeginDate());
            }
            List<InAndOutDateVO> inAndOutDateVOList = item.getInAndOutDateVOList();
            if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                inAndOutDateVOList.forEach(item1->{
                    if(item1.isCurrentQuarter()){
                        startDate.add(item1.getInDate());
                    }
                });
            }
            if(!CollectionUtils.isEmpty(startDate)){
                Date minDate = Collections.min(startDate);
                item.setMinDate(minDate);
            }
        });
        personSourceAllocationVO.setPersonSourceVOList(personSourceVOceList);
        List<PersonSourceVO> personSourceVOList= personSourceVOceList.stream().filter(item-> Objects.nonNull(item.getMinDate())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(personSourceVOList)){
            personSourceVOList.sort(Comparator.comparing(PersonSourceVO::getMinDate));
            PersonSourceVO personSourceVO= personSourceVOList.get(0);
            personSourceAllocationVO.setShortTimePersonData(personSourceVO);
            Date dataMindate= personSourceVO.getMinDate();
            Date quarterMinDate= quarterStartAndEndDates[0];
            personSourceAllocationVO.setMinDate(
                    Objects.isNull(dataMindate)?quarterMinDate.getTime():dataMindate.compareTo(quarterMinDate)>0?dataMindate.getTime():quarterMinDate.getTime());

        }
    }


    public void packageMaterialMinDateData(List<MaterialSourceVO> materialSourceVOceList,MaterialSourceAllocationVO materialSourceAllocationVO,Date[] quarterStartAndEndDates){
        materialSourceVOceList.forEach(item->{
            List<Date> startDate = new ArrayList<>();

            if(Objects.nonNull(item.getJobBeginDate())){
                startDate.add(item.getJobBeginDate());
            }
            if(Objects.nonNull(item.getTaskBeginDate())){
                startDate.add(item.getTaskBeginDate());
            }
            List<InAndOutDateVO> inAndOutDateVOList = item.getInAndOutDateVOList();
            if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                inAndOutDateVOList.forEach(item1->{
                    if(item1.isCurrentQuarter()){
                        startDate.add(item1.getInDate());
                    }
                });
            }
            if(!CollectionUtils.isEmpty(startDate)){
                Date minDate = Collections.min(startDate);
                item.setMinDate(minDate);
            }
        });
        materialSourceAllocationVO.setMaterialSourceVOList(materialSourceVOceList);
        List<MaterialSourceVO> personSourceVOList= materialSourceVOceList.stream().filter(item-> Objects.nonNull(item.getMinDate())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(personSourceVOList)){
            personSourceVOList.sort(Comparator.comparing(MaterialSourceVO::getMinDate));
            MaterialSourceVO materialSourceVO= personSourceVOList.get(0);
            materialSourceAllocationVO.setShortTimeMaterialData(materialSourceVO);
            materialSourceAllocationVO.setMinDate(Objects.isNull(materialSourceVO.getMinDate())?0L:materialSourceVO.getMinDate().getTime());

            Date dataMindate= materialSourceVO.getMinDate();
            Date quarterMinDate= quarterStartAndEndDates[0];
            materialSourceAllocationVO.setMinDate(
                    Objects.isNull(dataMindate)?quarterMinDate.getTime():dataMindate.compareTo(quarterMinDate)>0?dataMindate.getTime():quarterMinDate.getTime());
        }
    }


    private List<PersonSourceVO> initPackagePerson(List<String> userCodeList) {
        List<BasicUser> basicUserList= basicUserService.listByNumberList(userCodeList);
        List<InAndOutDateVO> inAndOutDateVOList = new ArrayList<>();
        List<PersonSourceVO> personSourceVOList = new ArrayList<>();
        for (BasicUser basicUser : basicUserList) {
            PersonSourceVO personSourceVO = new PersonSourceVO();
            personSourceVO.setId(IdUtil.fastUUID());
            personSourceVO.setUniqueId(IdUtil.fastUUID());
            personSourceVO.setFullName(basicUser.getFullName());
            personSourceVO.setUserCode(basicUser.getUserCode());
            personSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
            personSourceVOList.add(personSourceVO);
        }
        return  personSourceVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePersonJobDate(List<ObjJobDTO> personJobDTOList) {
        Map<Integer,List<ObjJobDTO>> dataToList = personJobDTOList.stream().collect(Collectors.groupingBy(ObjJobDTO::getType));
        for (Map.Entry<Integer, List<ObjJobDTO>> integerListEntry : dataToList.entrySet()) {
            List<ObjJobDTO> objJobDTOList=  integerListEntry.getValue();
            Integer type =integerListEntry.getKey();
            switch (type){
                case 1:
                    LambdaUpdateWrapper<JobPostAuthorize> updateWrapper = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
                    for (ObjJobDTO personJobDTO : objJobDTOList) {
                        updateWrapper.clear();
                        updateWrapper.set(JobPostAuthorize::getPlanBeginDate,personJobDTO.getInDate());
                        updateWrapper.set(JobPostAuthorize::getPlanEndDate,personJobDTO.getOutDate());
                        updateWrapper.eq(JobPostAuthorize::getId,personJobDTO.getId());
                        jobPostAuthorizeMapper.update(updateWrapper);
                    }
                    break;
                case 0:
                    LambdaUpdateWrapper<PersonMange> updateWrapper1 = new LambdaUpdateWrapper<>(PersonMange.class);
                    for (ObjJobDTO personJobDTO : objJobDTOList) {
                        updateWrapper1.clear();
                        updateWrapper1.set(PersonMange::getInDate,personJobDTO.getInDate());
                        updateWrapper1.set(PersonMange::getOutDate,personJobDTO.getOutDate());
                        updateWrapper1.eq(PersonMange::getId,personJobDTO.getId());
                        personMangeMapper.update(updateWrapper1);
                    }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveMaterialJobDate(List<ObjJobDTO> materialJobDTOList) {

        Map<Integer,List<ObjJobDTO>> dataToList = materialJobDTOList.stream().collect(Collectors.groupingBy(ObjJobDTO::getType));

        for (Map.Entry<Integer, List<ObjJobDTO>> integerListEntry : dataToList.entrySet()) {

            List<ObjJobDTO> objJobDTOList=  integerListEntry.getValue();
            Integer type =integerListEntry.getKey();
            switch (type){
                case 1:
                    LambdaUpdateWrapper<JobMaterial> updateWrapper = new LambdaUpdateWrapper<>(JobMaterial.class);
                    for (ObjJobDTO personJobDTO : objJobDTOList) {
                        updateWrapper.clear();
                        updateWrapper.set(JobMaterial::getPlanBeginDate,personJobDTO.getInDate());
                        updateWrapper.set(JobMaterial::getPlanEndDate,personJobDTO.getOutDate());
                        updateWrapper.eq(JobMaterial::getId,personJobDTO.getId());
                        jobMaterialMapper.update(updateWrapper);
                    }
                    break;
                case 0:
                    LambdaUpdateWrapper<MaterialManage> updateWrapper1 = new LambdaUpdateWrapper<>(MaterialManage.class);
                    for (ObjJobDTO personJobDTO : objJobDTOList) {
                        updateWrapper1.clear();
                        updateWrapper1.set(MaterialManage::getInDate,personJobDTO.getInDate());
                        updateWrapper1.set(MaterialManage::getOutDate,personJobDTO.getOutDate());
                        updateWrapper1.eq(MaterialManage::getId,personJobDTO.getId());
                        materialManageMapper.update(updateWrapper1);
                    }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public MaterialSourceAllocationVO materialAllocationCount(SearchDTO searchDTO) {
        String keyword = searchDTO.getKeyword();
        int  yearNum = searchDTO.getYearNum();
        String  repairRound= searchDTO.getRepairRound();
        int quarterNum= searchDTO.getQuarterNum();
        MaterialSourceAllocationVO materialSourceAllocationVO = new MaterialSourceAllocationVO();
        List<SchemeToMaterial> schemeToMaterialList= schemeToMaterialMapper.getListByRepairRoundAndKeyword(repairRound,keyword);
        if(CollectionUtils.isEmpty(schemeToMaterialList)){
            materialSourceAllocationVO.setMaterialSourceVOList(new ArrayList<>());
            materialSourceAllocationVO.setQuarterCountVOList(this.initMaterialQuarterCount(yearNum));
            return  materialSourceAllocationVO;
        }
        // 获取 物资作业数据 不过滤季度
        List<MaterialSourceDTO> materialSourceDTOList= jobMaterialMapper.getMaterialJobInfoList(repairRound,keyword,yearNum);
//        List<MaterialQuarterCountVO> quarterCountVOList = new ArrayList<>();
        Map<String,Set<String>> queryNumToPersonCodeList = new HashMap();
        // 获取 同一个物资编码 对应的多少个 作业下使用同个 大修的情况
        Map<String,List<MaterialSourceDTO>> materialSourceDTOMap= materialSourceDTOList.stream().collect(Collectors.groupingBy(MaterialSourceDTO::getMaterialCode));
        // 获取生成了多少个 物资ID
        List<String> idList= schemeToMaterialList.stream().map(SchemeToMaterial::getMaterialId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //todo 查询物资 进出场（计划进出场）数据纪录
        List<MaterialInfoDTO> materialInfoDTOList= CollectionUtils.isEmpty(idList)?new ArrayList<>(): materialManageMapper.getMaterialManageInAndOutDateList(idList);
        Map<String, List<MaterialInfoDTO>>  idToList = materialInfoDTOList.stream().collect(Collectors.groupingBy(MaterialInfoDTO::getId));
        List<MaterialSourceVO> materialSourceVOceList =new ArrayList<>();

        Map<String,SchemeToMaterial> materialCodeToEntityMap = schemeToMaterialList.stream().collect(Collectors.toMap(SchemeToMaterial::getMaterialNumber
                ,Function.identity(), (v1, v2)->v1));
        List<MaterialSourceVO> tailList = new ArrayList();
        List<MaterialSourceVO> lList = new ArrayList();
        Map<Integer, Date[]> quarterDates = getQuarterStartAndEndDates(yearNum);
        Date[] quarterStartAndEndDates = quarterDates.get(quarterNum);

        Map<String,String> materialIdToBaseCode = new HashMap<>();
        for (SchemeToMaterial schemeToMaterial : schemeToMaterialList) {
            materialIdToBaseCode.put(schemeToMaterial.getMaterialId(),schemeToMaterial.getBaseCode());
        }

        for (Map.Entry<String, SchemeToMaterial> stringSchemeToMaterialEntry : materialCodeToEntityMap.entrySet()) {
            String materialCode= stringSchemeToMaterialEntry.getKey();
            SchemeToMaterial schemeToMaterial=  stringSchemeToMaterialEntry.getValue();

            MaterialSourceVO materialSourceVO = new MaterialSourceVO();
            /**
             *  人员作业相关的数据
             */
            List<MaterialSourceDTO> materialSourceDTOS= materialSourceDTOMap.get(materialCode);
            String materialId = schemeToMaterial.getMaterialId();
            boolean isHaveWork= Boolean.FALSE;
            // 数据类型: 1-物资，2-作业，3.任务
            materialSourceVO.setDataType("1");
            materialSourceVO.setOverlapCount(0);
            if(!CollectionUtils.isEmpty(materialSourceDTOS)){
                MaterialSourceDTO materialSourceDTO=  materialSourceDTOS.get(0);
                materialSourceVO.setId(materialSourceDTO.getRelationId());

                materialSourceVO.setJobId(materialSourceDTO.getJobId());            // 数据类型: 1-人，2-作业，3.任务
                materialSourceVO.setDataType("1");
                materialSourceVO.setOverlapCount(0);
                materialSourceVO.setJobName(materialSourceDTO.getJobName());
                materialSourceVO.setJobId(materialSourceDTO.getJobId());
                materialSourceVO.setMaterialName(schemeToMaterial.getMaterialName());
                materialSourceVO.setRepairRound(repairRound);
                materialSourceVO.setUniqueId(IdUtil.objectId());
                materialSourceVO.setMaterialCode(materialCode);
                isHaveWork = Boolean.TRUE;
            }else{
                materialSourceVO.setUniqueId(materialId);
                materialSourceVO.setId(materialId);
                materialSourceVO.setMaterialCode(materialCode);
                materialSourceVO.setMaterialName(schemeToMaterial.getMaterialName());
                materialSourceVO.setDataType("1");
            }

            // 设置第一层的 人员类型的 进出时间
            List<MaterialInfoDTO> materialDTOList1= idToList.get(materialId);
            boolean isOutAndInDate= Boolean.FALSE;

            String baseCode= materialIdToBaseCode.get(materialId);
            if(!CollectionUtils.isEmpty(materialDTOList1)){
                List<InAndOutDateVO> inAndOutDateVOList  = new ArrayList<>();
                List<String> ukList = new ArrayList<>();
                for (MaterialInfoDTO materialInfoDTO : materialDTOList1) {
                    if(Objects.equals(materialInfoDTO.getType(),"1") && Objects.equals(materialInfoDTO.getInOrOut(),0)  ){// 物资管理数据 并且带入场 该数据如果存在计划进出时间那么作为
                        this.packageMaterialDate(materialInfoDTO,repairRound,inAndOutDateVOList,Boolean.FALSE,baseCode,ukList,materialInfoDTO.getUniqueKey());
                    }else{// 台账数据
                        if(!Objects.equals(materialInfoDTO.getInOrOut(),2)  ){
                            this.packageMaterialDate(materialInfoDTO,repairRound,inAndOutDateVOList,Boolean.TRUE,baseCode,ukList,materialInfoDTO.getUniqueKey());
                        }
                    }
                }
                if(inAndOutDateVOList.size() > 0){
                    isOutAndInDate=Boolean.TRUE;
                }

                if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                    for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
                        Date[] quarterStartAndEndDates22 = integerEntry.getValue();
                        Integer qu= integerEntry.getKey();
                        Date startDate= quarterStartAndEndDates22[0];
                        Date endDate= quarterStartAndEndDates22[1];

                        inAndOutDateVOList.forEach(inAndOutDateVO->{
                            boolean  isOverlap= false;
                            if(inAndOutDateVO.getInDate().compareTo(startDate)>=0 && inAndOutDateVO.getOutDate().compareTo(endDate)<0){
                                if(quarterNum == qu){
                                    inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                                }
                                isOverlap= true;
                            }
                            // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                            if(inAndOutDateVO.getInDate().compareTo(startDate)<0 && inAndOutDateVO.getOutDate().compareTo(startDate)>0 && !isOverlap){
                                if(quarterNum == qu){
                                    inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                                }
                                isOverlap= true;
                            }
                        });
                        if(!inAndOutDateVOList.stream().anyMatch(item->item.isCurrentQuarter())){
                            Random random = new Random();
                            int randomIndex = random.nextInt(inAndOutDateVOList.size()); // 生成一个随机索引
                            InAndOutDateVO selectedItem = inAndOutDateVOList.get(randomIndex); // 获取随机选中的元素
                            selectedItem.setCurrentQuarter(true);
                        }
                    }
                }
                materialSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
            }
            List<InAndOutDateVO> inAndOutDateVOList= materialSourceVO.getInAndOutDateVOList();
            if(isHaveWork){
                // 判断数据过滤 条件：任务，作业， fileterPersonList
                List<MaterialSourceVO> valueList = this.packageMaterialJobList(materialSourceDTOS,materialSourceVO);
                List<MaterialSourceVO> filterList = this.fileterMaterialList(valueList,queryNumToPersonCodeList,quarterNum,yearNum,materialSourceVO,lList);
                if(!CollectionUtils.isEmpty(filterList)){
                    materialSourceVOceList.addAll(filterList);
                }
            }else{ // 如果沒有 作业或者任务 那么就直接加入到尾部数据
//                materialSourceVO.setSort(0);
                if(isOutAndInDate){
                    // 那么需要果过滤 进出时间
                    materialSourceVO.setSort(1);
                    if(this.isInToQuarterScope(inAndOutDateVOList,quarterDates,quarterNum,queryNumToPersonCodeList,materialSourceVO.getMaterialCode())){
//                        materialSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
                        lList.add(materialSourceVO);
                    }
                }else {
                    // 如果沒有 作业或者任务 那么就直接加入到尾部数据
                    materialSourceVO.setSort(0);
                    tailList.add(materialSourceVO);
                }
            }
        }

        List<MaterialQuarterCountVO> quarterCountVOList = new ArrayList<>();
        for (Map.Entry<Integer, Date[]> integerIntegerEntry : quarterDates.entrySet()) {
            MaterialQuarterCountVO quarterCountVO =new MaterialQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerIntegerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerIntegerEntry.getKey());
            quarterCountVO.setMaterialCount(queryNumToPersonCodeList.getOrDefault(integerIntegerEntry.getKey().toString(),new HashSet<>()).size());
            quarterCountVOList.add(quarterCountVO);
        }
        if(lList.size() >0){
            materialSourceVOceList.addAll(lList);
        }
        if(tailList.size()> 0){
            materialSourceVOceList.addAll(tailList);
        }
        materialSourceAllocationVO.setQuarterCountVOList(quarterCountVOList);
        this.packageMaterialMinDateData(materialSourceVOceList,materialSourceAllocationVO,quarterStartAndEndDates);
        return materialSourceAllocationVO;

    }

    private boolean isInToQuarterScope(List<InAndOutDateVO> inAndOutDateVOList,Map<Integer, Date[]> quarterDates,Integer quarterNum
            ,Map<String,Set<String>> queryNumToPersonCodeList,String code) {
        AtomicReference<Boolean> isInScope= new AtomicReference<>(Boolean.FALSE);
        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            Integer currentQuarterNum=  integerEntry.getKey();
            Date[] quarterStartAndEndDates= integerEntry.getValue();
            Date beginDate= quarterStartAndEndDates[0];
            Date endDate= quarterStartAndEndDates[1];
            AtomicReference<Boolean> currentScope= new AtomicReference<>(Boolean.FALSE);

            inAndOutDateVOList.forEach(inAndOutDateVO->{
                if(inAndOutDateVO.getInDate().compareTo(beginDate)>=0 && inAndOutDateVO.getInDate().compareTo(endDate)<0){
                    if(quarterNum.equals(currentQuarterNum)) {
                        inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                        isInScope.set(Boolean.TRUE);
                    }
                    currentScope.set(Boolean.TRUE);
                }
                if(inAndOutDateVO.getInDate().compareTo(beginDate)<0 && inAndOutDateVO.getOutDate().compareTo(beginDate)>0){
                    if(quarterNum.equals(currentQuarterNum)) {
                        inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                        isInScope.set(Boolean.TRUE);
                    }
                    currentScope.set(Boolean.TRUE);
                }

            });
            if(currentScope.get()){
                Set<String> codeSet= queryNumToPersonCodeList.getOrDefault(currentQuarterNum.toString(),new HashSet<>());
                codeSet.add(code);
                queryNumToPersonCodeList.put(currentQuarterNum.toString(),codeSet);
            }
        }
        //如果没有当前季度 的基础时间那么随机获取一个设置 为true 方便前端显示
        if(!isInScope.get() && !CollectionUtils.isEmpty(inAndOutDateVOList)){
            Random random = new Random();
            int randomIndex = random.nextInt(inAndOutDateVOList.size()); // 生成一个随机索引
            InAndOutDateVO selectedItem = inAndOutDateVOList.get(randomIndex); // 获取随机选中的元素
            selectedItem.setCurrentQuarter(true);
        }
        return isInScope.get();
    }

    private List<MaterialSourceVO> fileterMaterialList(List<MaterialSourceVO> materialSourceVOList,  Map<String,Set<String>> queryNumToPersonCodeList
            , Integer quarterNum, Integer yearNum, MaterialSourceVO materialSourceVODemo,List<MaterialSourceVO> lList) {

        List<MaterialSourceVO>  filterList= new ArrayList<>();
        Map<Integer, Date[]> quarterDates = getQuarterStartAndEndDates(yearNum);
        // 如果季度为空默认设置为0 最终获取的 季度数据也是为空
        if(Objects.isNull(quarterNum)|| quarterNum<= 0 ){
            quarterNum =0;
        }
        Date[] dates= quarterDates.get(quarterNum);
        Map<String,List<MaterialSourceVO>> materialCodeToQuarterToList = new HashMap<>();
        for (MaterialSourceVO materialSourceVO : materialSourceVOList) {
            // 作业时间
            Date jobBeginDate =  materialSourceVO.getJobBeginDate();
            Date jobEndDate =  materialSourceVO.getJobEndDate();
            String materialCode= materialSourceVO.getMaterialCode();
            // 任务时间
            Date taskBeanDate= materialSourceVO.getTaskBeginDate();
            Date taskEndDate= materialSourceVO.getTaskEndDate();
            // 判断 季度开始结束时间为空 那么无需进行季度过滤
            if(Objects.isNull(dates)){
                // todo 处理数据进行封装
                continue;
            }
            // 按季度 过滤统计数据
            for (Map.Entry<Integer, Date[]> quarterEntry : quarterDates.entrySet()) {
                Date[] dateArr =  quarterEntry.getValue();
                Date startDate= dateArr[0];
                Date endDate= dateArr[1];
                String key = String.format("%s_%s",materialCode,quarterEntry.getKey());
                boolean filterFlag=false;
                List<MaterialSourceVO> materialSourceVOList1 = materialCodeToQuarterToList.getOrDefault(key,new ArrayList<>());
                // 1. 1.人员、物资已绑定作业，且设置了计划任务时间
                //则人员、物资任务管理，在按季度时间筛选人员、物资信息时，通过计划任务开始时间、计划任务结束时间来统计数量；
                if(Objects.nonNull(taskBeanDate) && Objects.nonNull(taskEndDate)){
                    materialSourceVO.setSort(3);
                    materialSourceVODemo.setSort(3);
                    if(taskBeanDate.compareTo(startDate)>=0 && taskEndDate.compareTo(endDate)<0){
                        materialSourceVOList1.add(materialSourceVO);
                        filterFlag =true;
                    }
                    if(taskBeanDate.compareTo(startDate)<0 && taskEndDate.compareTo(startDate)>0 && !filterFlag){
                        materialSourceVOList1.add(materialSourceVO);
                        filterFlag =true;
                    }
                }else  if(Objects.nonNull(jobBeginDate) && Objects.nonNull(jobEndDate)){  //1.2人员、物资已绑定作业，未设置了计划任务时间
                    //则人员、物资任务管理，在按季度时间筛选人员、物资信息时，通过计划作业开始时间、计划作业结束时间来统计数量；
                    materialSourceVO.setSort(2);
                    materialSourceVODemo.setSort(2);
                    if(jobBeginDate.compareTo(startDate)>=0 && jobBeginDate.compareTo(endDate)<0){
                        materialSourceVOList1.add(materialSourceVO);
                        filterFlag =true;
                    }
                    if(jobBeginDate.compareTo(startDate)<0 && jobEndDate.compareTo(startDate)>0 && !filterFlag){
                        materialSourceVOList1.add(materialSourceVO);
                        filterFlag =true;
                    }
                }
                //1.5排序（最后来做，20号晚一点找用户确认下再敲定）
                //①有作业、有设置任务时间的人员、物资排在最前面；
                //②有作业、没设置任务时间的人员、物资排在第二顺位；
                //③无作业、有计划进出时间的人员、物资排在第三顺位；
                //④无作业、无计划进出时间的人员、物资排在最末尾顺位。
                // 如果未传入季度数据那么默认全部
                if(filterFlag){
                    materialCodeToQuarterToList.put(key,materialSourceVOList1);
                }
            }
        }
        List<MaterialSourceVO> currentQuerterList = new ArrayList<>();



        // 当前人员作业和任务 数据为空 后进行 人员进出过滤
        String userCode=materialSourceVODemo.getMaterialCode();
        if(MapUtil.isEmpty(materialCodeToQuarterToList)){
            materialSourceVODemo.setSort(1);
            List<InAndOutDateVO> inAndOutDateVOList= materialSourceVODemo.getInAndOutDateVOList();
            if( this.isInToQuarterScope(inAndOutDateVOList,quarterDates,quarterNum,queryNumToPersonCodeList,materialSourceVODemo.getMaterialCode())){
                materialSourceVODemo.setInAndOutDateVOList(inAndOutDateVOList);
                lList.add(materialSourceVODemo);
            }
        }
        // 组装 季度统计数量
        for (Map.Entry<String, List<MaterialSourceVO>> stringListEntry : materialCodeToQuarterToList.entrySet()) {
            String querterKey =  stringListEntry.getKey();
            String[] keyArr = querterKey.split("_");
//                String key = String.format("%s_%s",userCode,quarterNum);
            String quarterNumStr= keyArr[1];
            String currentUserCode= keyArr[0];

            // 获取当前季度的人员信息
            if(quarterNumStr.equals(quarterNum.toString())){
                currentQuerterList.addAll(stringListEntry.getValue());
            }
            // 获取 季度对应的用户code
            Set<String> codeList=  queryNumToPersonCodeList.getOrDefault(quarterNumStr,new HashSet<>());
            codeList.add(currentUserCode);
            queryNumToPersonCodeList.put(quarterNumStr,codeList);
        }
        // 组装 季度的数据
        if(CollectionUtils.isEmpty(currentQuerterList)){
            materialSourceVODemo.setSort(0);
            filterList.add(materialSourceVODemo);
            return  new ArrayList<>();
        }
        filterList.add(materialSourceVODemo);
        filterList.addAll(currentQuerterList);
        return  filterList;

    }

    @Override
    public PersonSourceAllocationVO personOverlapAllocationCount(SearchDTO searchDTO) {
        String keyword = searchDTO.getKeyword();
        int  yearNum = searchDTO.getYearNum();
        String  repairRound= searchDTO.getRepairRound();
        Integer quarterNum= searchDTO.getQuarterNum();

        // 获取当前大修下的 人员列表
        List<String> userCodeList= schemeToPersonMapper.getUserCodeListByRepairRoundAndHaveProject(repairRound);
        PersonSourceAllocationVO personSourceAllocationVO = new PersonSourceAllocationVO();
        // 如果人员为空   那么所有数据都为空
        Map<Integer, Date[]>  quarterDates =  this.getQuarterStartAndEndDates(yearNum);
        if(CollectionUtils.isEmpty(quarterDates)){
            personSourceAllocationVO.setPersonSourceVOList(new ArrayList<>());
            personSourceAllocationVO.setQuarterCountVOList(this.initPersonQuarterCount(yearNum,quarterDates));
            return  personSourceAllocationVO;
        }
        Date[] startAndEndDate = quarterDates.get(quarterNum);
        // 获取 人员作业信息列表所有大修
        List<PersonSourceDTO>  personSourceDTOList= jobPostAuthorizeMapper.peronOverlapSourceList(repairRound,keyword,yearNum,userCodeList);
        if(CollectionUtils.isEmpty(personSourceDTOList)){
            personSourceAllocationVO.setPersonSourceVOList(new ArrayList<>());
            personSourceAllocationVO.setQuarterCountVOList(this.initPersonQuarterCount(yearNum,quarterDates));
            return  personSourceAllocationVO;
        }
        //获取当前大修的 人员列表
        List<PersonSourceDTO> personSourceDTOS= personSourceDTOList.stream().filter(item-> Objects.equals(item.getRepairRound(),repairRound)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(personSourceDTOS)){
            personSourceAllocationVO.setPersonSourceVOList(new ArrayList<>());
            personSourceAllocationVO.setQuarterCountVOList(this.initPersonQuarterCount(yearNum,quarterDates));
            return  personSourceAllocationVO;
        }
        // 通过大修轮次分组   获取 数据中 无当前大修的 其他大修 人员数据 -- .filter(item-> !Objects.equals(item.getRepairRound(),repairRound))
        Map<String, List<PersonSourceDTO>> personSourceDTOMap = personSourceDTOList.stream().filter(item-> StringUtils.hasText(item.getRepairRound())).collect(Collectors.groupingBy(PersonSourceDTO::getRepairRound));
        // 将当前大修的数据作为 主数据去计算哪些人员 数据有重叠 并且计算重叠天数
      //  Map<String, List<PersonSourceVO>> userCodeToOverlapList =  new HashMap<>();
        Map<String, List<PersonSourceVO>> ukToOverlapList =  new HashMap<>();
        // 主数据列表
        Map<String,PersonSourceVO> mainDateMap = new HashMap<>();// List<PersonSourceVO> mainDateList = new ArrayList<>();

        Set<String> personIdSet = new HashSet<>();
        // 主人员编号对应的列表数据
        Map<String,List<PersonSourceVO>> mainCodeToList = new HashMap<>();


        Map<String,OverlapResultDTO> userCodeToQuarterNumToPersonSourceDTOMap = new HashMap();


        // 这里 定义 人员管理ID列表目的  是为了人员去重避免重复计算
        List<String> personIdList =new ArrayList<>();
        // 这里对 人员主数据需要去重如果存在重复的
        Map<String,PersonSourceDTO> personSourceVOMap = new HashMap<>();
        for (PersonSourceDTO personSourceDTO : personSourceDTOS) {
            personSourceVOMap.put(personSourceDTO.getUserCode(),personSourceDTO);
        }

        // 获取 当前大修下  所有不同作业下的 物资信息
        personSourceDTOMap.remove(repairRound);
        List<PersonSourceDTO> personSourceDTOList1 = new ArrayList<>();
        for (PersonSourceDTO materialSourceDTO : personSourceDTOS) {
            if(Objects.nonNull(materialSourceDTO.getTaskBeginDate()) && Objects.nonNull(materialSourceDTO.getTaskEndDate())){
                personSourceDTOList1.add(materialSourceDTO);
            }
        }

        for (PersonSourceDTO personSourceDTO : personSourceDTOList1) {
            String userCode= personSourceDTO.getUserCode();
            boolean isOverlap=false;
            // 用戶code
            if(Objects.isNull(personSourceDTO.getTaskEndDate())||Objects.isNull(personSourceDTO.getTaskBeginDate())){
                continue;
            }
            for (Map.Entry<String, List<PersonSourceDTO>> entry : personSourceDTOMap.entrySet()) {
                // 其他大修轮次
                String otherRepairRound= entry.getKey();
                // 其他大修轮次对应的 人员作业信息列表
                List<PersonSourceDTO> value =entry.getValue();
                for (PersonSourceDTO otherPersonSourceDTO : value) {
                    if(Objects.equals(otherPersonSourceDTO.getUserCode(),personSourceDTO.getUserCode())){
                        // 如果 另外的大修中人员 在作业中的 计划开始时间和结束时间为空也过滤掉
                        if(Objects.isNull(otherPersonSourceDTO.getTaskEndDate())||Objects.isNull(otherPersonSourceDTO.getTaskBeginDate())){
                            continue;
                        }
                        // 如果是同一个大修 那么需要过滤掉
                        // && Objects.equals(otherPersonSourceDTO.getJobId(),personSourceDTO.getJobId())
                        if(Objects.equals(otherRepairRound,repairRound) ){
                            continue;
                        }
                        // 计算当前任务和 当前其他任务的重叠天数 以及统计 当前人员在其中季度（所重叠的季度中）中所重叠的天数
                        Map<Integer,OverlapResultDTO> quarterNumToOverlapDays = calculateOverlapDays(personSourceDTO.getTaskBeginDate(),personSourceDTO.getTaskEndDate(),otherPersonSourceDTO.getTaskBeginDate()
                                ,otherPersonSourceDTO.getTaskEndDate(),quarterDates);
                        // 重叠天数大于0
                        OverlapResultDTO overlapDays= quarterNumToOverlapDays.getOrDefault(quarterNum,  new OverlapResultDTO(null, null, 0));
                        if(overlapDays.getOverlapDays()>0){
                            isOverlap = true;
                            String key = String.format("%s_%s",userCode,personSourceDTO.getJobId());
                            List<PersonSourceVO> workAndJobList= ukToOverlapList.getOrDefault(key,new ArrayList<>());
                            personIdSet.add(otherPersonSourceDTO.getPersonId());
                            this.packageOverlap(overlapDays,otherPersonSourceDTO,otherRepairRound,workAndJobList,otherPersonSourceDTO.getPersonId());
                            ukToOverlapList.put(key,workAndJobList);
                        }
                        // 累加 数据
                        if(MapUtil.isNotEmpty(quarterNumToOverlapDays)){
//                           isOverlap = true;
                            for (Map.Entry<Integer, OverlapResultDTO> integerOverlapResultDTOEntry : quarterNumToOverlapDays.entrySet()) {
                                OverlapResultDTO overlapResultDTO=   integerOverlapResultDTOEntry.getValue();
                                String key =String.format("%s_%s",userCode,integerOverlapResultDTOEntry.getKey());
                                OverlapResultDTO userCodeOverlapResult= userCodeToQuarterNumToPersonSourceDTOMap.getOrDefault(
                                        key ,new OverlapResultDTO(null, null, 0));
                                userCodeOverlapResult.setOverlapDays(overlapResultDTO.getOverlapDays()+userCodeOverlapResult.getOverlapDays());
                                userCodeOverlapResult.setOverlapEnd(overlapResultDTO.getOverlapEnd());
                                userCodeOverlapResult.setOverlapStart(overlapResultDTO.getOverlapStart());
                                userCodeToQuarterNumToPersonSourceDTOMap.put(key,userCodeOverlapResult);
                            }
                        }
                    }
                }
            }

            if(isOverlap){
                //  主体数据 填充
                PersonSourceVO personSourceVO = new PersonSourceVO();
                personSourceVO.setId(personSourceDTO.getRelationId());
                personSourceVO.setJobId("");
                personSourceVO.setUniqueId(IdUtil.fastUUID());
                personSourceVO.setPersonId(personSourceDTO.getPersonId());
                personIdSet.add(personSourceDTO.getPersonId());
                // 数据类型: 1-人
                personSourceVO.setDataType("1");
                personSourceVO.setOverlapCount(0);
                personSourceVO.setFullName(personSourceDTO.getFullName());
                personSourceVO.setRepairRound(repairRound);
                personSourceVO.setJobName("");
                personSourceVO.setUserCode(personSourceDTO.getUserCode());
                mainDateMap.put(userCode,personSourceVO);
                personIdList.add(personSourceDTO.getPersonId());

                List<PersonSourceVO> workAndJobList = mainCodeToList.getOrDefault(personSourceDTO.getUserCode(),new ArrayList<>());
                this.packageOverlap(new OverlapResultDTO(null,null,0),
                        personSourceDTO, repairRound, workAndJobList, personSourceDTO.getPersonId());
                mainCodeToList.put(userCode, workAndJobList);
            }
        }
        // 如果主数据为空 那么全为空
        if(MapUtil.isEmpty(mainDateMap)){
            personSourceAllocationVO.setPersonSourceVOList(new ArrayList<>());
            personSourceAllocationVO.setQuarterCountVOList(this.quarterCountVOListPackage(userCodeToQuarterNumToPersonSourceDTOMap,quarterDates));
            return  personSourceAllocationVO;
        }
        // 对主数据进行处理
        // 获取主数据 中人员 对应的进出场时间 在大修中
        List<PersonInfoDTO> personInfoDTOList= personMangeMapper.getPersonManageInAndOutDateList(new ArrayList<>(personIdSet));
        Map<String, List<PersonInfoDTO>>  codeToList = personInfoDTOList.stream().collect(Collectors.groupingBy(PersonInfoDTO::getNumber));

        // 获取人员id 对应的大修
        Map<String,String> personIdToRepairRoundMap= new HashMap<>();
        for (PersonSourceDTO personSourceDTO : personSourceDTOList) {
            personIdToRepairRoundMap.put(personSourceDTO.getPersonId(),personSourceDTO.getBaseCode());
        }
        // 组装数据
        List<PersonSourceVO> personSourceVOList = new ArrayList<>();
        Map<String,Integer> overlapDaysMap = new HashMap<>();
        List<PersonQuarterCountVO> quarterCountVOList = new ArrayList<>();
        for (Map.Entry<String, PersonSourceVO> stringPersonSourceVOEntry : mainDateMap.entrySet()) {
            PersonSourceVO personSourceVO=  stringPersonSourceVOEntry.getValue();

            List<PersonInfoDTO> personInfoDTOList1= codeToList.get(personSourceVO.getUserCode());
            if(CollectionUtils.isEmpty(personInfoDTOList1)){
                continue;
            }
            List<InAndOutDateVO> inAndOutDateVOList  = new ArrayList<>();
            Map<String, List<PersonInfoDTO> > idToList = personInfoDTOList1.stream().collect(Collectors.groupingBy(PersonInfoDTO::getId));
            for (Map.Entry<String, List<PersonInfoDTO>> stringListEntry : idToList.entrySet()) {

                List<PersonInfoDTO> value= stringListEntry.getValue();
                String baseCode= personIdToRepairRoundMap.get(stringListEntry.getKey());
                List<String> ukList = new ArrayList<>();
                for (PersonInfoDTO personInfoDTO : value) {
                    if(Objects.equals(personInfoDTO.getType(),"1") && Objects.equals(personInfoDTO.getInOrOut(),0)  ){// 人员管理数据 并且带入场 该数据如果存在计划进出时间那么作为
                        this.packageDate(personInfoDTO,null,inAndOutDateVOList,Boolean.FALSE,baseCode,ukList,personInfoDTO.getUniqueKey());
                    }else{// 台账数据
                        if(!Objects.equals(personInfoDTO.getInOrOut(),2)  ){
                            this.packageDate(personInfoDTO,null,inAndOutDateVOList,Boolean.TRUE,baseCode,ukList,personInfoDTO.getUniqueKey());
                        }
                    }
                }
            }

            if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
                    Date[] quarterStartAndEndDates = integerEntry.getValue();
                    Integer qu= integerEntry.getKey();
                    Date startDate= quarterStartAndEndDates[0];
                    Date endDate= quarterStartAndEndDates[1];

                    inAndOutDateVOList.forEach(inAndOutDateVO->{
                        boolean  isOverlap= false;
                        if(inAndOutDateVO.getInDate().compareTo(startDate)>=0 && inAndOutDateVO.getOutDate().compareTo(endDate)<0){
                            if(quarterNum.equals(qu)){
                                inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                            }
                            isOverlap= true;
                        }
                        // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                        if(inAndOutDateVO.getInDate().compareTo(startDate)<0 && inAndOutDateVO.getOutDate().compareTo(startDate)>0 && !isOverlap){
                            if(quarterNum.equals(qu)){
                                inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                            }
                            isOverlap= true;
                        }
                    });
                    if(!inAndOutDateVOList.stream().anyMatch(item-> item.isCurrentQuarter())){
                        Random random = new Random();
                        int randomIndex = random.nextInt(inAndOutDateVOList.size()); // 生成一个随机索引
                        InAndOutDateVO selectedItem = inAndOutDateVOList.get(randomIndex); // 获取随机选中的元素
                        selectedItem.setCurrentQuarter(true);
                    }
                }
            }
            personSourceVO.setInAndOutDateVOList(inAndOutDateVOList);

            List<PersonSourceVO>  otherList= mainCodeToList.get(personSourceVO.getUserCode());
            if(!CollectionUtils.isEmpty(otherList)){
                Map<String,List<PersonSourceVO>> map= otherList.stream().collect(Collectors.groupingBy(item->String.format("%s_%s",item.getUserCode(),item.getJobId())));
                int overlapCount= 0;
                boolean isAddMianData=Boolean.FALSE;
                for (Map.Entry<String, List<PersonSourceVO>> stringListEntry : map.entrySet()) {
                    String targetKey= stringListEntry.getKey();
                    List<PersonSourceVO> targetList= stringListEntry.getValue();
                    List<PersonSourceVO>  overlapList= ukToOverlapList.get(targetKey);
                    if(!CollectionUtils.isEmpty(overlapList)){
                        overlapCount = overlapCount+overlapList.stream().filter(item-> Objects.equals(item.getDataType(),"3")).mapToInt(PersonSourceVO::getOverlapCount).sum();
                        List<PersonSourceVO> filterList =  this.filterPersonListAndCount(overlapList,quarterCountVOList,quarterNum,quarterDates);
                        if(!CollectionUtils.isEmpty(filterList)){
                            // 获取 参照对象中 为任务类型的数据
                            PersonSourceVO taskTargetData=  targetList.stream().filter(item-> Objects.equals(item.getDataType(),"3")).findFirst().get();
                            filterList.forEach(item->{
                                if(Objects.equals(item.getDataType(),"3")){//如果是任务数据 那么需要设置对应的对比对象数据
                                    item.setTargetDateVOList(taskTargetData.getInAndOutDateVOList());
                                }
                            });
                            if(!isAddMianData){
                                isAddMianData=Boolean.TRUE;
                                personSourceVOList.add(personSourceVO);
                            }
                            personSourceVOList.addAll(targetList);
                            personSourceVOList.addAll(filterList);
                        }
                    }
                }
                overlapDaysMap.put(personSourceVO.getUserCode(),overlapCount);
            }
        }
        personSourceVOList.forEach(item->{
            if(Objects.equals("1",item.getDataType())){
                item.setOverlapCount(overlapDaysMap.getOrDefault(item.getUserCode(),0));
            }
        });

        this.packageMinDateData(personSourceVOList,personSourceAllocationVO,startAndEndDate);
        personSourceAllocationVO.setQuarterCountVOList(this.quarterCountVOListPackage(userCodeToQuarterNumToPersonSourceDTOMap,quarterDates));
        return  personSourceAllocationVO;
    }



    @Override
    public MaterialSourceAllocationVO materialOverlapAllocationCount(SearchDTO searchDTO) {
        String keyword = searchDTO.getKeyword();
        int  yearNum = searchDTO.getYearNum();
        String  repairRound= searchDTO.getRepairRound();
        Integer quarterNum= searchDTO.getQuarterNum();

        // 获取当前大修下的 物资列表
        List<String> materialNumberList= schemeToMaterialMapper.getMaterialNumberByRepairRound(repairRound);
        MaterialSourceAllocationVO materialSourceAllocationVO = new MaterialSourceAllocationVO();
        // 如果人员为空   那么所有数据都为空
        if(CollectionUtils.isEmpty(materialNumberList)){
            materialSourceAllocationVO.setMaterialSourceVOList(new ArrayList<>());
            materialSourceAllocationVO.setQuarterCountVOList(this.initMaterialQuarterCount(yearNum));
            return  materialSourceAllocationVO;
        }
        // 获取 物资作业信息列表所有大修
        List<MaterialSourceDTO>  materialSourceDTOList= jobMaterialMapper.materiallapSourceList(repairRound,keyword,yearNum,materialNumberList);
        if(CollectionUtils.isEmpty(materialSourceDTOList)){
            materialSourceAllocationVO.setMaterialSourceVOList(new ArrayList<>());
            materialSourceAllocationVO.setQuarterCountVOList(this.initMaterialQuarterCount(yearNum));
            return  materialSourceAllocationVO;
        }
        //获取当前大修的 物资列表
        List<MaterialSourceDTO> materialSourceDTOS= materialSourceDTOList.stream().filter(
                item-> Objects.equals(item.getRepairRound(),repairRound)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(materialSourceDTOS)){
            materialSourceAllocationVO.setMaterialSourceVOList(new ArrayList<>());
            materialSourceAllocationVO.setQuarterCountVOList(this.initMaterialQuarterCount(yearNum));
            return  materialSourceAllocationVO;
        }
        // 任务数据过滤 季度
        Map<String, List<MaterialSourceDTO>> materialSourceDTOMap = materialSourceDTOList.stream().filter(item-> StringUtils.hasText(item.getRepairRound())).collect(Collectors.groupingBy(MaterialSourceDTO::getRepairRound));
        // 将当前大修的数据作为 主数据去计算哪些物资 数据有重叠 并且计算重叠天数
        Map<String, List<MaterialSourceVO>> materialCodeToOverlapList =  new HashMap<>();

        Map<String, List<MaterialSourceVO>> ukToOverlapList =  new HashMap<>();

        Map<String,MaterialSourceVO> mainDateMap = new HashMap<>();

        Set<String> materialIdSet = new HashSet<>();

        Map<String,List<MaterialSourceVO>> mainCodeToList = new HashMap<>();

        // 这里 定义 物资管理ID列表目的  是为了人员去重避免重复计算
        List<String> materialIdList =new ArrayList<>();
        // 这里对 物资主数据需要去重如果存在重复的
//        Map<String,MaterialSourceDTO> materialSourceVOMap = new HashMap<>();

        //移除掉 当前大修的数据
        materialSourceDTOMap.remove(repairRound);
        // 获取 当前大修下  所有不同作业下的 物资信息
        List<MaterialSourceDTO> materialSourceDTOList1 = new ArrayList<>();
        for (MaterialSourceDTO materialSourceDTO : materialSourceDTOS) {
            if(Objects.nonNull(materialSourceDTO.getTaskBeginDate()) && Objects.nonNull(materialSourceDTO.getTaskEndDate())){
                materialSourceDTOList1.add(materialSourceDTO);
            }
        }
        Map<Integer, Date[]>  quarterDates =  this.getQuarterStartAndEndDates(yearNum);
        Date[] quarterStartAndEndDates=  quarterDates.get(quarterNum);

        Map<String, OverlapResultDTO>  materialCodeToQuarterNumToPersonSourceDTOMap = new HashMap<>();
        // 循环 以当前大修作为对比对象（参照对象进行对比重叠）
        for (MaterialSourceDTO materialSourceDTO : materialSourceDTOList1) {
            String materialCode=  materialSourceDTO.getMaterialCode();
            boolean isOverlap=false;
            if(Objects.isNull(materialSourceDTO.getTaskEndDate())||Objects.isNull(materialSourceDTO.getTaskBeginDate())){
                continue;
            }
            String jobId=materialSourceDTO.getJobId();

            for (Map.Entry<String, List<MaterialSourceDTO>> entry : materialSourceDTOMap.entrySet()) {
                // 大修轮次
                String otherRepairRound= entry.getKey();
                // 其他大修轮次对应的 物资作业信息列表
                List<MaterialSourceDTO> value =entry.getValue();
                for (MaterialSourceDTO otherMaterialSourceDTO : value) {
                    if(Objects.equals(otherMaterialSourceDTO.getMaterialCode(),materialCode)){
                        // 如果 另外的大修中人员 在作业中的 计划开始时间和结束时间为空也过滤掉
                        if(Objects.isNull(otherMaterialSourceDTO.getTaskEndDate())||Objects.isNull(otherMaterialSourceDTO.getTaskBeginDate())){
                            continue;
                        }
                        // 如果是同一个大修 那么需要过滤掉
                        // && Objects.equals(otherPersonSourceDTO.getJobId(),personSourceDTO.getJobId())
                        if(Objects.equals(otherRepairRound,repairRound) ){
                            continue;
                        }

                        // 计算重叠天数
                        Map<Integer,OverlapResultDTO> quarterNumToOverlapDays =calculateOverlapDays(materialSourceDTO.getTaskBeginDate()
                                ,materialSourceDTO.getTaskEndDate(),otherMaterialSourceDTO.getTaskBeginDate()
                                ,otherMaterialSourceDTO.getTaskEndDate(),quarterDates);
                        // 重叠天数大于0
                        OverlapResultDTO overlapDays= quarterNumToOverlapDays.getOrDefault(quarterNum,  new OverlapResultDTO(null, null, 0));
                        if(overlapDays.getOverlapDays()>0){
                            String targetKey =String.format("%s_%s",materialCode,jobId);
                            isOverlap = true;
//                            需要处理对象Map
                            List<MaterialSourceVO> workAndJobList= ukToOverlapList.getOrDefault(targetKey,new ArrayList<>());
                            materialIdSet.add(otherMaterialSourceDTO.getMaterialId());
                            this.packageMaterialOverlap(overlapDays,otherMaterialSourceDTO,otherRepairRound,workAndJobList,otherMaterialSourceDTO.getMaterialId());
                            materialCodeToOverlapList.put(materialCode,workAndJobList);
                            ukToOverlapList.put(targetKey,workAndJobList);

                        }

                        // 累加 数据--计算重叠数据
                        if(MapUtil.isNotEmpty(quarterNumToOverlapDays)){
//                           isOverlap = true;

                            for (Map.Entry<Integer, OverlapResultDTO> integerOverlapResultDTOEntry : quarterNumToOverlapDays.entrySet()) {
                                OverlapResultDTO overlapResultDTO=   integerOverlapResultDTOEntry.getValue();
                                String key =String.format("%s_%s",materialCode,integerOverlapResultDTOEntry.getKey());
                                OverlapResultDTO userCodeOverlapResult= materialCodeToQuarterNumToPersonSourceDTOMap.getOrDefault(
                                        key ,new OverlapResultDTO(null, null, 0));
                                userCodeOverlapResult.setOverlapDays(overlapResultDTO.getOverlapDays()+userCodeOverlapResult.getOverlapDays());
                                userCodeOverlapResult.setOverlapEnd(overlapResultDTO.getOverlapEnd());
                                userCodeOverlapResult.setOverlapStart(overlapResultDTO.getOverlapStart());
                                materialCodeToQuarterNumToPersonSourceDTOMap.put(key,userCodeOverlapResult);
                            }
                        }
                    }
                }
            }

            // 如果重叠 那么需要记录主体记录
            if(isOverlap){
                //  主体数据 填充
                MaterialSourceVO materialSourceVO = new MaterialSourceVO();
                materialSourceVO.setId(materialSourceDTO.getRelationId());
                materialSourceVO.setJobId("");
                materialSourceVO.setUniqueId(IdUtil.fastUUID());
                materialSourceVO.setMaterialId(materialSourceDTO.getMaterialId());
                materialIdSet.add(materialSourceDTO.getMaterialId());
                // 数据类型: 1-人
                materialSourceVO.setDataType("1");
                materialSourceVO.setOverlapCount(0);
                materialSourceVO.setMaterialName(materialSourceDTO.getMaterialName());
                materialSourceVO.setRepairRound(repairRound);
                materialSourceVO.setJobName("");
                materialSourceVO.setMaterialCode(materialSourceDTO.getMaterialCode());
                mainDateMap.put(materialSourceDTO.getMaterialCode(),materialSourceVO);
                materialIdList.add(materialSourceDTO.getMaterialId());

                List<MaterialSourceVO> workAndJobList = mainCodeToList.getOrDefault(materialSourceDTO.getMaterialCode(),new ArrayList<>());
                materialIdSet.add(materialSourceDTO.getMaterialId());
                this.packageMaterialOverlap(new OverlapResultDTO(null,null,0),
                        materialSourceDTO, repairRound, workAndJobList, materialSourceVO.getMaterialId());
                mainCodeToList.put(materialSourceDTO.getMaterialCode(), workAndJobList);
            }
        }
        // 如果主数据为空 那么全为空
        if(MapUtil.isEmpty(mainDateMap)){
            materialSourceAllocationVO.setMaterialSourceVOList(new ArrayList<>());
            materialSourceAllocationVO.setQuarterCountVOList(this.quarterCountMaterialVOListPackage(materialCodeToQuarterNumToPersonSourceDTOMap,quarterDates));
            return  materialSourceAllocationVO;
        }
        // 对主数据进行处理
        // 获取主数据 中物资 对应的进出场时间 在大修中
        List<MaterialInfoDTO> materialInfoDTOList= materialManageMapper.getMaterialManageInAndOutDateList(new ArrayList<>(materialIdSet));
        Map<String, List<MaterialInfoDTO>>  codeToList = materialInfoDTOList.stream().collect(Collectors.groupingBy(MaterialInfoDTO::getNumber));
        // 组装数据
        List<MaterialSourceVO> materialSourceVOList = new ArrayList<>();
        Map<String,Integer> overlapDaysMap = new HashMap<>();

        Map<String,String> materialIdToBaseCode = new HashMap<>();
        for (MaterialSourceDTO materialSourceDTO : materialSourceDTOList) {
            materialIdToBaseCode.put(materialSourceDTO.getMaterialId(),materialSourceDTO.getBaseCode());
        }

        List<MaterialQuarterCountVO> quarterCountVOList = new ArrayList<>();
        for (Map.Entry<String, MaterialSourceVO> entry : mainDateMap.entrySet()) {
            String materialCode= entry.getKey();
            MaterialSourceVO materialSourceVO= entry.getValue();
            List<MaterialInfoDTO> materialInfoDTOList1= codeToList.get(materialCode);
            if(CollectionUtils.isEmpty(materialInfoDTOList1)){
                continue;
            }
            List<InAndOutDateVO> inAndOutDateVOList  = new ArrayList<>();
            Map<String, List<MaterialInfoDTO> > idToList = materialInfoDTOList1.stream().collect(Collectors.groupingBy(MaterialInfoDTO::getId));
            for (Map.Entry<String, List<MaterialInfoDTO>> stringListEntry : idToList.entrySet()) {
                List<MaterialInfoDTO> value= stringListEntry.getValue();

                String baseCode= materialIdToBaseCode.get(stringListEntry.getKey());
                List<String> ukList = new ArrayList<>();
                for (MaterialInfoDTO materialInfoDTO : value) {
                    if(Objects.equals(materialInfoDTO.getType(),"1") && Objects.equals(materialInfoDTO.getInOrOut(),0)  ){// 人员管理数据 并且带入场 该数据如果存在计划进出时间那么作为
                        this.packageMaterialDate(materialInfoDTO,repairRound,inAndOutDateVOList,Boolean.FALSE,baseCode,ukList,materialInfoDTO.getUniqueKey());
                    }else{// 台账数据
                        if(!Objects.equals(materialInfoDTO.getInOrOut(),2)  ){
                            this.packageMaterialDate(materialInfoDTO,repairRound,inAndOutDateVOList,Boolean.TRUE,baseCode,ukList,materialInfoDTO.getUniqueKey());
                        }
                    }
                }
            }
            if(!CollectionUtils.isEmpty(inAndOutDateVOList)){
                for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
                    Date[] quarterStartAndEndDates1 = integerEntry.getValue();
                    Integer qu= integerEntry.getKey();
                    Date startDate= quarterStartAndEndDates1[0];
                    Date endDate= quarterStartAndEndDates1[1];
                    inAndOutDateVOList.forEach(inAndOutDateVO->{
                        boolean  isOverlap= false;
                        if(inAndOutDateVO.getInDate().compareTo(startDate)>=0 && inAndOutDateVO.getOutDate().compareTo(endDate)<0){
                            if(quarterNum.equals(qu)){
                                inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                            }
                            isOverlap= true;
                        }
                        // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                        if(inAndOutDateVO.getInDate().compareTo(startDate)<0 && inAndOutDateVO.getOutDate().compareTo(startDate)>0 && !isOverlap){
                            if(quarterNum.equals(qu)){
                                inAndOutDateVO.setCurrentQuarter(Boolean.TRUE);
                            }
                            isOverlap= true;
                        }
                    });
                     if(!inAndOutDateVOList.stream().anyMatch(item-> item.isCurrentQuarter())){
                         Random random = new Random();
                         int randomIndex = random.nextInt(inAndOutDateVOList.size()); // 生成一个随机索引
                         InAndOutDateVO selectedItem = inAndOutDateVOList.get(randomIndex); // 获取随机选中的元素
                         selectedItem.setCurrentQuarter(true);
                     }
                }
            }
            materialSourceVO.setInAndOutDateVOList(inAndOutDateVOList);
            // 获取主数据 参照对象列表
            List<MaterialSourceVO> workAndJobList=  mainCodeToList.get(materialSourceVO.getMaterialCode());
            int overlapCount= 0;

            if(!CollectionUtils.isEmpty(workAndJobList)){
                Map<String,List<MaterialSourceVO>> uk = workAndJobList.stream().collect(Collectors.groupingBy(item-> String.format("%s_%s",item.getMaterialCode(),item.getJobId())));
                boolean isAddMianData=Boolean.FALSE;
                for (Map.Entry<String, List<MaterialSourceVO>> stringListEntry : uk.entrySet()) {
                    String targetKey = stringListEntry.getKey();
                    List<MaterialSourceVO>  overlapList= ukToOverlapList.get(targetKey);
                    if(!CollectionUtils.isEmpty(overlapList)){
                        overlapCount= overlapCount+overlapList.stream().filter(item-> Objects.equals(item.getDataType(),"3")).mapToInt(MaterialSourceVO::getOverlapCount).sum();
                        List<MaterialSourceVO> filterList =  this.filterOverlapMaterialListAndCount(overlapList,quarterCountVOList,quarterNum,yearNum);
                        if(!CollectionUtils.isEmpty(filterList)){
                            List<MaterialSourceVO> targetList =  stringListEntry.getValue();
                            MaterialSourceVO taskTargetData=  targetList.stream().filter(item-> Objects.equals(item.getDataType(),"3")).findFirst().get();
                            filterList.forEach(item->{
                                if(Objects.equals(item.getDataType(),"3")){//如果是任务数据 那么需要设置对应的对比对象数据
                                    item.setTargetDateVOList(taskTargetData.getInAndOutDateVOList());
                                }
                            });
                            if(!isAddMianData){
                                isAddMianData=Boolean.TRUE;
                                materialSourceVOList.add(materialSourceVO);
                            }
                            materialSourceVOList.addAll(targetList);
                            materialSourceVOList.addAll(filterList);
                        }
                        overlapDaysMap.put(materialSourceVO.getMaterialCode(),overlapCount);
                    }
                }

            }
        }
        materialSourceVOList.forEach(item->{
            if(Objects.equals("1",item.getDataType())){
                item.setOverlapCount(overlapDaysMap.getOrDefault(item.getMaterialCode(),0));
            }
        });
        this.packageMaterialMinDateData(materialSourceVOList,materialSourceAllocationVO,quarterStartAndEndDates);
        materialSourceAllocationVO.setQuarterCountVOList(this.quarterCountMaterialVOListPackage(materialCodeToQuarterNumToPersonSourceDTOMap,quarterDates));
        return  materialSourceAllocationVO;
    }

    @Override
    public Integer materialOverlapDays(SearchDTO searchDTO) {
        MaterialSourceAllocationVO materialSourceAllocationVO=  materialOverlapAllocationCount(searchDTO);
        List<MaterialQuarterCountVO> materialQuarterCountVOList=  materialSourceAllocationVO.getQuarterCountVOList();
        return  materialQuarterCountVOList.stream().mapToInt(MaterialQuarterCountVO::getOverlapDayCount).sum();
    }

    @Override
    public Integer personOverlapDays(SearchDTO searchDTO) {
        PersonSourceAllocationVO personOverlapAllocationCount=  this.personOverlapAllocationCount(searchDTO);
        List<PersonQuarterCountVO> personQuarterCountVOList=  personOverlapAllocationCount.getQuarterCountVOList();
        return  personQuarterCountVOList.stream().mapToInt(PersonQuarterCountVO::getOverlapDayCount).sum();
    }

    private void packageMaterialOverlap(OverlapResultDTO overlapDaysDto, MaterialSourceDTO otherMaterialSourceDTO
            , String otherRepairRound, List<MaterialSourceVO> workAndJobList, String materialId) {

        // 会生成两条数据 1.工作数据 2.作业计划数据
        MaterialSourceVO jobMaterialSourceVO = new MaterialSourceVO();
        jobMaterialSourceVO.setId(otherMaterialSourceDTO.getRelationId());
        jobMaterialSourceVO.setJobId(otherMaterialSourceDTO.getJobId());
        jobMaterialSourceVO.setUniqueId(IdUtil.fastUUID());
        // 数据类型: 2-作业   3-任务
        jobMaterialSourceVO.setDataType("2");
        jobMaterialSourceVO.setOverlapCount(overlapDaysDto.getOverlapDays());
        jobMaterialSourceVO.setMaterialName(otherMaterialSourceDTO.getMaterialName());
        jobMaterialSourceVO.setRepairRound(otherRepairRound);
        jobMaterialSourceVO.setJobName(otherMaterialSourceDTO.getJobName());
        jobMaterialSourceVO.setBaseCode(otherMaterialSourceDTO.getBaseCode());
        jobMaterialSourceVO.setTaskBeginDate(otherMaterialSourceDTO.getTaskBeginDate());
        jobMaterialSourceVO.setTaskEndDate(otherMaterialSourceDTO.getTaskEndDate());
        jobMaterialSourceVO.setMaterialCode(otherMaterialSourceDTO.getMaterialCode());
        List<InAndOutDateVO> jobInAndOutDateList =new ArrayList<>();
        InAndOutDateVO jobInAndOutDateVO = new InAndOutDateVO();
        jobInAndOutDateVO.setId(otherMaterialSourceDTO.getRelationId());

        jobMaterialSourceVO.setJobBeginDate(otherMaterialSourceDTO.getJobBeginDate());
        jobMaterialSourceVO.setJobEndDate(otherMaterialSourceDTO.getJobEndDate());

        jobInAndOutDateVO.setRepairRound(otherMaterialSourceDTO.getRepairRound());
        jobInAndOutDateVO.setInDate(otherMaterialSourceDTO.getJobBeginDate());
        jobInAndOutDateVO.setOutDate(otherMaterialSourceDTO.getJobEndDate());
        jobInAndOutDateList.add(jobInAndOutDateVO);
        jobMaterialSourceVO.setInAndOutDateVOList(jobInAndOutDateList);
        // 设置 物资管理ID
        jobMaterialSourceVO.setMaterialId(materialId);
        workAndJobList.add(jobMaterialSourceVO);

        MaterialSourceVO workMaterialSourceVO = new MaterialSourceVO();
        workMaterialSourceVO.setId(otherMaterialSourceDTO.getRelationId());
        workMaterialSourceVO.setJobId(otherMaterialSourceDTO.getJobId());
        workMaterialSourceVO.setUniqueId(IdUtil.fastUUID());
        // 数据类型: 2-作业   3-任务
        workMaterialSourceVO.setDataType("3");
        workMaterialSourceVO.setOverlapCount(overlapDaysDto.getOverlapDays());
        workMaterialSourceVO.setMaterialName(otherMaterialSourceDTO.getMaterialName());
        workMaterialSourceVO.setRepairRound(otherRepairRound);
        workMaterialSourceVO.setBaseCode(otherMaterialSourceDTO.getBaseCode());
        workMaterialSourceVO.setJobName(otherMaterialSourceDTO.getJobName());
        workMaterialSourceVO.setMaterialCode(otherMaterialSourceDTO.getMaterialCode());
        List<InAndOutDateVO> wordInAndOutDateVOList =new ArrayList<>();
        InAndOutDateVO workInAndOutDateVO = new InAndOutDateVO();
        workInAndOutDateVO.setId(otherMaterialSourceDTO.getRelationId());
        workInAndOutDateVO.setRepairRound(otherMaterialSourceDTO.getRepairRound());
        workInAndOutDateVO.setInDate(otherMaterialSourceDTO.getTaskBeginDate());
        workInAndOutDateVO.setOutDate(otherMaterialSourceDTO.getTaskEndDate());
        workMaterialSourceVO.setJobBeginDate(otherMaterialSourceDTO.getJobBeginDate());
        workMaterialSourceVO.setJobEndDate(otherMaterialSourceDTO.getJobEndDate());
        workInAndOutDateVO.setOverlapBeginDate(overlapDaysDto.getOverlapStart());
        workMaterialSourceVO.setTaskBeginDate(otherMaterialSourceDTO.getTaskBeginDate());
        workMaterialSourceVO.setTaskEndDate(otherMaterialSourceDTO.getTaskEndDate());
        workInAndOutDateVO.setOverlapEndDate(overlapDaysDto.getOverlapEnd());
        wordInAndOutDateVOList.add(workInAndOutDateVO);
        workMaterialSourceVO.setMaterialId(materialId);
        workMaterialSourceVO.setInAndOutDateVOList(wordInAndOutDateVOList);

        workAndJobList.add(workMaterialSourceVO);
    }


    private List<PersonSourceVO> filterPersonListAndCount(List<PersonSourceVO> personSourceVOList, List<PersonQuarterCountVO> quarterCountVOList, Integer quarterNum,Map<Integer, Date[]> quarterDates) {
        List<PersonSourceVO>  filterList= new ArrayList<>();
//        Map<Integer,Set<String>> quarterSetNumberMap=new HashMap<>();
//        Map<Integer,Integer> quarterToDayNumMap=new HashMap<>();
        for (PersonSourceVO personSourceVO : personSourceVOList) {
            Date jobBeginDate =  personSourceVO.getJobBeginDate();
            Date jobEndDate =  personSourceVO.getJobEndDate();
            if(!Objects.isNull(quarterNum) && quarterNum>0){
                Date[] quarterStartAndEndDates =  quarterDates.get(quarterNum);
                Date startDate= quarterStartAndEndDates[0];
                Date endDate= quarterStartAndEndDates[1];
                boolean  isOverlap= false;
                if(jobBeginDate.compareTo(startDate)>=0 && jobBeginDate.compareTo(endDate)<0){
                    filterList.add(personSourceVO);
                    isOverlap=true;
                }
                // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                if(jobBeginDate.compareTo(startDate)<0 && jobEndDate.compareTo(startDate)>0 && !isOverlap){
                    filterList.add(personSourceVO);
                }
            }else{
                filterList.add(personSourceVO);
            }
        }
        if(CollectionUtils.isEmpty(filterList)){
            return  filterList;
        }
        return  filterList;

    }


    private List<PersonQuarterCountVO> quarterCountVOListPackage(Map<String, OverlapResultDTO>  userCodeToQuarterNumToPersonSourceDTOMap,Map<Integer, Date[]> quarterDates) {
        List<PersonQuarterCountVO> quarterCountVOList=new ArrayList<>();
        Map<Integer,Integer> quarterNumToPersonCount = new HashMap<>();
        Map<Integer,Integer> quarterNumToDayCount = new HashMap<>();
        if(MapUtil.isNotEmpty(userCodeToQuarterNumToPersonSourceDTOMap)){
            for (Map.Entry<String, OverlapResultDTO> stringOverlapResultDTOEntry : userCodeToQuarterNumToPersonSourceDTOMap.entrySet()) {
                String key= stringOverlapResultDTOEntry.getKey();
                String[] keys= key.split("_");
                Integer quarterNum= Integer.parseInt(keys[1]);
                // 获取重叠人员数
                Integer personCount= quarterNumToPersonCount.getOrDefault(quarterNum,0);
                personCount=personCount+1;
                quarterNumToPersonCount.put(quarterNum,personCount);

                // 获取重叠天数
                Integer overlapDayCount= stringOverlapResultDTOEntry.getValue().getOverlapDays();
                Integer dayCount=quarterNumToDayCount.getOrDefault(quarterNum,0);
                overlapDayCount = overlapDayCount+dayCount;
                quarterNumToDayCount.put(quarterNum,overlapDayCount);
            }
        }
        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            PersonQuarterCountVO quarterCountVO =new PersonQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerEntry.getKey());
            // 重叠天数
            quarterCountVO.setOverlapDayCount(quarterNumToDayCount.getOrDefault(integerEntry.getKey(),0));
            // 重叠人员数量
            quarterCountVO.setOverlapPersonCount(quarterNumToPersonCount.getOrDefault(integerEntry.getKey(),0));
            quarterCountVOList.add(quarterCountVO);
        }
        return  quarterCountVOList;
    }


    private List<MaterialQuarterCountVO> quarterCountMaterialVOListPackage(Map<String, OverlapResultDTO>  materialCodeToQuarterNumToPersonSourceDTOMap,Map<Integer, Date[]> quarterDates) {
        List<MaterialQuarterCountVO> quarterCountVOList=new ArrayList<>();
        Map<Integer,Integer> quarterNumToPersonCount = new HashMap<>();
        Map<Integer,Integer> quarterNumToDayCount = new HashMap<>();
        if(MapUtil.isNotEmpty(materialCodeToQuarterNumToPersonSourceDTOMap)){
            for (Map.Entry<String, OverlapResultDTO> stringOverlapResultDTOEntry : materialCodeToQuarterNumToPersonSourceDTOMap.entrySet()) {
                String key= stringOverlapResultDTOEntry.getKey();
                String[] keys= key.split("_");
                Integer quarterNum= Integer.parseInt(keys[1]);
                // 获取重叠人员数
                Integer personCount= quarterNumToPersonCount.getOrDefault(quarterNum,0);
                personCount=personCount+1;
                quarterNumToPersonCount.put(quarterNum,personCount);

                // 获取重叠天数
                Integer overlapDayCount= stringOverlapResultDTOEntry.getValue().getOverlapDays();
                Integer dayCount=quarterNumToDayCount.getOrDefault(quarterNum,0);
                overlapDayCount = overlapDayCount+dayCount;
                quarterNumToDayCount.put(quarterNum,overlapDayCount);
            }
        }
        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            MaterialQuarterCountVO quarterCountVO =new MaterialQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerEntry.getKey());
            // 重叠天数
            quarterCountVO.setOverlapDayCount(quarterNumToDayCount.getOrDefault(integerEntry.getKey(),0));
            // 重叠人员数量
            quarterCountVO.setOverlapMaterialCount(quarterNumToPersonCount.getOrDefault(integerEntry.getKey(),0));
            quarterCountVOList.add(quarterCountVO);
        }
        return  quarterCountVOList;
    }

    private List<MaterialSourceVO> filterOverlapMaterialListAndCount(List<MaterialSourceVO> materialSourceVOList, List<MaterialQuarterCountVO> quarterCountVOList, Integer quarterNum, int yearNum) {
        List<MaterialSourceVO>  filterList= new ArrayList<>();
        Map<Integer, Date[]> quarterDates = getQuarterStartAndEndDates(yearNum);
        for (MaterialSourceVO materialSourceVO : materialSourceVOList) {
            Date taskBeginDate =  materialSourceVO.getTaskBeginDate();
            Date taskEndDate =  materialSourceVO.getTaskEndDate();
            if(Objects.nonNull(quarterNum) && quarterNum > 0){
                Date[] quarterStartAndEndDates =  quarterDates.get(quarterNum);
                Date startDate= quarterStartAndEndDates[0];
                Date endDate= quarterStartAndEndDates[1];
                boolean isOverlap=false;
                if(taskBeginDate.compareTo(startDate)>=0 && taskBeginDate.compareTo(endDate)<0){
                    filterList.add(materialSourceVO);
                    isOverlap= true;
                }
                // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                if(taskBeginDate.compareTo(startDate)<0 && taskEndDate.compareTo(startDate)>0 && !isOverlap){
                    filterList.add(materialSourceVO);
                }
            }else{
                filterList.add(materialSourceVO);
            }
        }

        return  filterList;

    }


    private void packageOverlap(OverlapResultDTO overlapDaysDto,PersonSourceDTO personSourceDTO,String otherRepairRound,List<PersonSourceVO> workAndJobList,String personId){
        // 会生成两条数据 1.工作数据 2.作业计划数据
        PersonSourceVO jobPersonSourceVO = new PersonSourceVO();
        jobPersonSourceVO.setId(personSourceDTO.getRelationId());
        jobPersonSourceVO.setJobId(personSourceDTO.getJobId());
        jobPersonSourceVO.setUniqueId(IdUtil.fastUUID());
        // 数据类型: 2-作业   3-任务
        jobPersonSourceVO.setDataType("2");
        jobPersonSourceVO.setOverlapCount(overlapDaysDto.getOverlapDays());
        jobPersonSourceVO.setFullName(personSourceDTO.getFullName());
        jobPersonSourceVO.setRepairRound(otherRepairRound);
        jobPersonSourceVO.setJobName(personSourceDTO.getJobName());
        jobPersonSourceVO.setTaskBeginDate(personSourceDTO.getTaskBeginDate());
        jobPersonSourceVO.setTaskEndDate(personSourceDTO.getTaskEndDate());
        jobPersonSourceVO.setUserCode(personSourceDTO.getUserCode());
        jobPersonSourceVO.setBaseCode(personSourceDTO.getBaseCode());
        List<InAndOutDateVO> jobInAndOutDateList =new ArrayList<>();
        InAndOutDateVO jobInAndOutDateVO = new InAndOutDateVO();
        jobInAndOutDateVO.setId(personSourceDTO.getRelationId());

        jobPersonSourceVO.setJobBeginDate(personSourceDTO.getJobBeginDate());
        jobPersonSourceVO.setJobEndDate(personSourceDTO.getJobEndDate());

        jobInAndOutDateVO.setRepairRound(personSourceDTO.getRepairRound());
        jobInAndOutDateVO.setInDate(personSourceDTO.getJobBeginDate());
        jobInAndOutDateVO.setOutDate(personSourceDTO.getJobEndDate());
        jobInAndOutDateList.add(jobInAndOutDateVO);
        jobPersonSourceVO.setInAndOutDateVOList(jobInAndOutDateList);
        // 设置 人员管理ID
        jobPersonSourceVO.setPersonId(personId);

        workAndJobList.add(jobPersonSourceVO);

        PersonSourceVO workPersonSourceVO = new PersonSourceVO();
        workPersonSourceVO.setId(personSourceDTO.getRelationId());
        workPersonSourceVO.setJobId(personSourceDTO.getJobId());
        workPersonSourceVO.setUniqueId(IdUtil.fastUUID());
        // 数据类型: 2-作业   3-任务
        workPersonSourceVO.setDataType("3");
        workPersonSourceVO.setBaseCode(personSourceDTO.getBaseCode());
        workPersonSourceVO.setOverlapCount(overlapDaysDto.getOverlapDays());
        workPersonSourceVO.setFullName(personSourceDTO.getFullName());
        workPersonSourceVO.setRepairRound(otherRepairRound);
        workPersonSourceVO.setJobName(personSourceDTO.getJobName());
        workPersonSourceVO.setUserCode(personSourceDTO.getUserCode());
        List<InAndOutDateVO> wordInAndOutDateVOList =new ArrayList<>();
        InAndOutDateVO workInAndOutDateVO = new InAndOutDateVO();
        workInAndOutDateVO.setId(personSourceDTO.getRelationId());
        workInAndOutDateVO.setRepairRound(personSourceDTO.getRepairRound());
        workInAndOutDateVO.setInDate(personSourceDTO.getTaskBeginDate());
        workInAndOutDateVO.setOutDate(personSourceDTO.getTaskEndDate());
        workPersonSourceVO.setJobBeginDate(personSourceDTO.getJobBeginDate());
        workPersonSourceVO.setJobEndDate(personSourceDTO.getJobEndDate());
        workInAndOutDateVO.setOverlapBeginDate(overlapDaysDto.getOverlapStart());
        workInAndOutDateVO.setOverlapEndDate(overlapDaysDto.getOverlapEnd());
        wordInAndOutDateVOList.add(workInAndOutDateVO);
        workPersonSourceVO.setTaskBeginDate(personSourceDTO.getTaskBeginDate());
        workPersonSourceVO.setTaskEndDate(personSourceDTO.getTaskEndDate());
        workPersonSourceVO.setPersonId(personId);
        workPersonSourceVO.setInAndOutDateVOList(wordInAndOutDateVOList);

        workAndJobList.add(workPersonSourceVO);
    }

    // 数据过滤处理
    private List<PersonSourceVO> fileterPersonList(List<PersonSourceVO> personSourceVOList, Map<String,Set<String>> queryNumToPersonCodeList
            ,Integer  quarterNum,int yearNum,PersonSourceVO personSourceVODemo,List<PersonSourceVO> lList) {
        List<PersonSourceVO>  filterList= new ArrayList<>();
        Map<Integer, Date[]> quarterDates = getQuarterStartAndEndDates(yearNum);
        // 如果季度为空默认设置为0 最终获取的 季度数据也是为空
        if(Objects.isNull(quarterNum)|| quarterNum<= 0 ){
            quarterNum =0;
        }
        Date[] dates= quarterDates.get(quarterNum);
        Map<String,List<PersonSourceVO>> userCodeToQuarterToList = new HashMap<>();

        for (PersonSourceVO personSourceVO : personSourceVOList) {
            // 作业时间
            Date jobBeginDate =  personSourceVO.getJobBeginDate();
            Date jobEndDate =  personSourceVO.getJobEndDate();
            String userCode= personSourceVO.getUserCode();
            // 任务时间
            Date taskBeanDate= personSourceVO.getTaskBeginDate();
            Date taskEndDate= personSourceVO.getTaskEndDate();
            // 判断 季度开始结束时间为空 那么无需进行季度过滤
            if(Objects.isNull(dates)){
                // todo 处理数据进行封装
                continue;
            }
            // 按季度 过滤统计数据
            for (Map.Entry<Integer, Date[]> quarterEntry : quarterDates.entrySet()) {
                Date[] dateArr =  quarterEntry.getValue();
                Date startDate= dateArr[0];
                Date endDate= dateArr[1];
                String key = String.format("%s_%s",userCode,quarterEntry.getKey());
                boolean filterFlag=false;
                List<PersonSourceVO> personSourceVOList1 = userCodeToQuarterToList.getOrDefault(key,new ArrayList<>());
                // 1. 1.人员、物资已绑定作业，且设置了计划任务时间
                //则人员、物资任务管理，在按季度时间筛选人员、物资信息时，通过计划任务开始时间、计划任务结束时间来统计数量；
                if(Objects.nonNull(taskBeanDate) && Objects.nonNull(taskEndDate)){
                    personSourceVO.setSort(3);
                    personSourceVODemo.setSort(3);
                    if(jobBeginDate.compareTo(startDate)>=0 && jobBeginDate.compareTo(endDate)<0){
                        filterFlag =true;
                    }
                    if(jobBeginDate.compareTo(startDate)<0 && jobEndDate.compareTo(startDate)>0 && !filterFlag){
                        filterFlag =true;
                    }
                    if(filterFlag){
                        personSourceVOList1.add(personSourceVO);
                    }
                }else  if(Objects.nonNull(jobBeginDate) && Objects.nonNull(jobEndDate)){  //1.2人员、物资已绑定作业，未设置了计划任务时间
                    //则人员、物资任务管理，在按季度时间筛选人员、物资信息时，通过计划作业开始时间、计划作业结束时间来统计数量；
                    personSourceVO.setSort(2);
                    personSourceVODemo.setSort(2);
                    if(jobBeginDate.compareTo(startDate)>=0 && jobBeginDate.compareTo(endDate)<0){
                        filterFlag =true;
                    }
                    if(jobBeginDate.compareTo(startDate)<0 && jobEndDate.compareTo(startDate)>0 && !filterFlag){
                        filterFlag =true;
                    }
                    if(filterFlag){
                        personSourceVOList1.add(personSourceVO);
                    }
                }
                //1.5排序（最后来做，20号晚一点找用户确认下再敲定）
                //①有作业、有设置任务时间的人员、物资排在最前面；
                //②有作业、没设置任务时间的人员、物资排在第二顺位；
                //③无作业、有计划进出时间的人员、物资排在第三顺位；
                //④无作业、无计划进出时间的人员、物资排在最末尾顺位。
                // 如果未传入季度数据那么默认全部
                if(filterFlag){
                    userCodeToQuarterToList.put(key,personSourceVOList1);
                }
            }
        }

        // 当前人员作业和任务 数据为空 后进行 人员进出过滤
        String userCode=personSourceVODemo.getUserCode();
        String key = String.format("%s_%s",userCode,quarterNum);
        if(MapUtil.isEmpty(userCodeToQuarterToList)){
            personSourceVODemo.setSort(1);
            List<InAndOutDateVO> inAndOutDateVOList= personSourceVODemo.getInAndOutDateVOList();
            if( this.isInToQuarterScope(inAndOutDateVOList,quarterDates,quarterNum,queryNumToPersonCodeList,userCode)){
//                personSourceVODemo.setInAndOutDateVOList(inAndOutDateVOList);
                lList.add(personSourceVODemo);
            }
        }
        List<PersonSourceVO> currentQuerterList = new ArrayList<>();
//        Map<String,Set<String>> queryNumToPersonCodeList = new HashMap();

        for (Map.Entry<String, List<PersonSourceVO>> stringListEntry : userCodeToQuarterToList.entrySet()) {
            String querterKey =  stringListEntry.getKey();
            String[] keyArr = querterKey.split("_");
//                String key = String.format("%s_%s",userCode,quarterNum);
            String quarterNumStr= keyArr[1];
            String currentUserCode= keyArr[0];

            // 获取当前季度的人员信息
            if(quarterNumStr.equals(quarterNum.toString())){
                currentQuerterList.addAll(stringListEntry.getValue());
            }
            // 获取 季度对应的用户code

            Set<String> codeList=  queryNumToPersonCodeList.getOrDefault(quarterNumStr,new HashSet<>());
            codeList.add(currentUserCode);
            queryNumToPersonCodeList.put(quarterNumStr,codeList);
        }
        // 组装 季度的数据
        if(CollectionUtils.isEmpty(currentQuerterList)){
            personSourceVODemo.setSort(0);
            filterList.add(personSourceVODemo);
            return  new ArrayList<>();
        }
        filterList.add(personSourceVODemo);
        filterList.addAll(currentQuerterList);
        return  filterList;
    }


    public List<PersonQuarterCountVO> initPersonQuarterCount(int year,Map<Integer, Date[]>  quarterDates){
        List<PersonQuarterCountVO> quarterCountVOList=new ArrayList<>();

        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            PersonQuarterCountVO quarterCountVO =new PersonQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerEntry.getKey());
            quarterCountVO.setPersonCount(0);
            quarterCountVOList.add(quarterCountVO);
        }
        return quarterCountVOList;
    }




    private List<MaterialSourceDTO> filterMaterialListAndCount(List<MaterialSourceDTO> materialSourceDTOList, List<MaterialQuarterCountVO> quarterCountVOList,Integer  quarterNum,int yearNum) {
        List<MaterialSourceDTO>  filterList= new ArrayList<>();
        Map<Integer, Date[]> quarterDates = getQuarterStartAndEndDates(yearNum);
        Map<Integer,Set<String>> quarterSetNumberMap=new HashMap<>();
        for (MaterialSourceDTO materialSourceDTO : materialSourceDTOList) {
            Date jobBeginDate =  materialSourceDTO.getJobBeginDate();
            Date jobEndDate =  materialSourceDTO.getJobEndDate();
            if(!Objects.isNull(quarterNum) && quarterNum >0){
                Date[] quarterStartAndEndDates =  quarterDates.get(quarterNum);
                Date startDate= quarterStartAndEndDates[0];
                Date endDate= quarterStartAndEndDates[1];
                boolean isfilterFlag=false;
                if(jobBeginDate.compareTo(startDate)>=0 && jobBeginDate.compareTo(endDate)<0){
                    filterList.add(materialSourceDTO);
                    isfilterFlag = true;
                }
                // 计划开始时间 小于等于 第三季度的开始时间 然后 计划结束时间大于 第三季度的开始时间
                if(jobBeginDate.compareTo(startDate)<0 && jobEndDate.compareTo(startDate)>0 && !isfilterFlag){
                    filterList.add(materialSourceDTO);
                }
            }else{
                filterList.add(materialSourceDTO);
            }
            for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
                Date[] dates=  integerEntry.getValue();
                Date startDate1= dates[0];
                Date endDate2= dates[1];
                if(jobBeginDate.compareTo(startDate1)>=0 && jobBeginDate.compareTo(endDate2)<0){
                    Set<String> set= quarterSetNumberMap.getOrDefault(integerEntry.getKey(),new HashSet<>());
                    set.add(materialSourceDTO.getMaterialCode());
                    quarterSetNumberMap.put(integerEntry.getKey(),set);
                }
                if(jobBeginDate.compareTo(startDate1)<0 && jobEndDate.compareTo(startDate1)>=0){
                    Set<String> set= quarterSetNumberMap.getOrDefault(integerEntry.getKey(),new HashSet<>());
                    set.add(materialSourceDTO.getMaterialCode());
                    quarterSetNumberMap.put(integerEntry.getKey(),set);
                }
            }
        }

        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            Integer quarterNum1= integerEntry.getKey();
            if(!quarterSetNumberMap.containsKey(quarterNum1)){
                quarterSetNumberMap.put(quarterNum1,new HashSet<>());
            }
        }
        for (Map.Entry<Integer, Set<String>> integerIntegerEntry : quarterSetNumberMap.entrySet()) {
            MaterialQuarterCountVO quarterCountVO =new MaterialQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerIntegerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerIntegerEntry.getKey());
            quarterCountVO.setMaterialCount(integerIntegerEntry.getValue().size());
            quarterCountVOList.add(quarterCountVO);
        }
        if(CollectionUtils.isEmpty(filterList)){
            return  filterList;
        }
        return  filterList;
    }


    public List<MaterialQuarterCountVO> initMaterialQuarterCount(int year){
        List<MaterialQuarterCountVO> quarterCountVOList=new ArrayList<>();
        Map<Integer, Date[]>  quarterDates =  this.getQuarterStartAndEndDates(year);
        for (Map.Entry<Integer, Date[]> integerEntry : quarterDates.entrySet()) {
            MaterialQuarterCountVO quarterCountVO =new MaterialQuarterCountVO();
            quarterCountVO.setQuarterName(String.format("第%s季度",integerEntry.getKey()));
            quarterCountVO.setQuarterNum(integerEntry.getKey());
            quarterCountVO.setMaterialCount(0);
            quarterCountVOList.add(quarterCountVO);
        }
        return quarterCountVOList;
    }

    public static Map<Integer, Date[]> getQuarterStartAndEndDates(int year) {
        Map<Integer, Date[]> quarterDates = new HashMap<>();
        for (int quarter = 1; quarter <= 4; quarter++) {
            Calendar startCal = Calendar.getInstance();
            startCal.set(Calendar.YEAR, year);
            startCal.set(Calendar.MONTH, (quarter - 1) * 3);
            startCal.set(Calendar.SECOND,0);
            startCal.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
            startCal.set(Calendar.MINUTE, 0); // 设置分钟为0
            startCal.set(Calendar.MILLISECOND, 0);
            startCal.set(Calendar.DAY_OF_MONTH, 1);
            Date startDate = startCal.getTime();

            Calendar endCal = (Calendar) startCal.clone();
            endCal.add(Calendar.MONTH, 3);
            endCal.set(Calendar.SECOND,0);
            endCal.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
            endCal.set(Calendar.MINUTE, 0); // 设置分钟为0
            endCal.set(Calendar.MILLISECOND, 0);
            endCal.add(Calendar.DAY_OF_MONTH, -1);
            Date endDate = endCal.getTime();

            Date[] dates = {startDate, endDate};
            quarterDates.put(quarter, dates);
        }

        return quarterDates;
    }

    public List<PersonSourceVO>  packageJobList(List<PersonSourceDTO> personSourceDTOList,PersonSourceVO personSourceVO){
        List<PersonSourceVO> personSourceVOList = new ArrayList<>();
        for (PersonSourceDTO personSourceDTO : personSourceDTOList) {
            // 会生成两条数据 1.工作数据 2.作业计划数据
            PersonSourceVO jobPersonSourceVO = new PersonSourceVO();
//            BeanCopyUtils.copyProperties(personSourceVO,jobPersonSourceVO);
            // 作业信息设置 作业的计划开始时间和结束时间
            List<InAndOutDateVO> jobInAndOutDateList =new ArrayList<>();
            InAndOutDateVO jobInAndOutDateVO = new InAndOutDateVO();
//            inAndOutDateVO.setClassName();
            jobInAndOutDateVO.setInDate(personSourceDTO.getJobBeginDate());
            jobInAndOutDateVO.setOutDate(personSourceDTO.getJobEndDate());
            jobInAndOutDateVO.setId(personSourceDTO.getJobId());
            jobInAndOutDateVO.setRepairRound(personSourceDTO.getRepairRound());
            jobInAndOutDateList.add(jobInAndOutDateVO);
            jobPersonSourceVO.setId(personSourceDTO.getRelationId());
            jobPersonSourceVO.setJobName(personSourceDTO.getJobName());
            jobPersonSourceVO.setJobId(personSourceDTO.getJobId());
            jobPersonSourceVO.setDataType("2");
            jobPersonSourceVO.setUniqueId(IdUtil.simpleUUID());
            jobPersonSourceVO.setUserCode(personSourceDTO.getUserCode());

            jobPersonSourceVO.setJobBeginDate(personSourceDTO.getJobBeginDate());
            jobPersonSourceVO.setJobEndDate(personSourceDTO.getJobEndDate());
            jobPersonSourceVO.setInAndOutDateVOList(jobInAndOutDateList);
            personSourceVOList.add(jobPersonSourceVO);

            PersonSourceVO workPersonSourceVO = new PersonSourceVO();
            BeanCopyUtils.copyProperties(personSourceDTO,workPersonSourceVO);

            List<InAndOutDateVO> wordInAndOutDateVOList =new ArrayList<>();
            InAndOutDateVO workInAndOutDateVO = new InAndOutDateVO();
//            inAndOutDateVO.setClassName();
            workPersonSourceVO.setId(personSourceDTO.getRelationId());
            workPersonSourceVO.setJobName(personSourceDTO.getJobName());
            workPersonSourceVO.setJobId(personSourceDTO.getJobId());
            workInAndOutDateVO.setId(personSourceDTO.getRelationId());
            workInAndOutDateVO.setRepairRound(personSourceDTO.getRepairRound());
            workInAndOutDateVO.setInDate(personSourceDTO.getTaskBeginDate());
            workInAndOutDateVO.setOutDate(personSourceDTO.getTaskEndDate());
            wordInAndOutDateVOList.add(workInAndOutDateVO);
            workPersonSourceVO.setDataType("3");
            workPersonSourceVO.setUniqueId(IdUtil.simpleUUID());
            workPersonSourceVO.setJobBeginDate(personSourceDTO.getJobBeginDate());
            workPersonSourceVO.setJobEndDate(personSourceDTO.getJobEndDate());
            workPersonSourceVO.setInAndOutDateVOList(wordInAndOutDateVOList);
            workPersonSourceVO.setUserCode(personSourceDTO.getUserCode());
            personSourceVOList.add(workPersonSourceVO);
        }
        return  personSourceVOList;
    }


    public void packageDate(PersonInfoDTO personInfoDTO,String repairRound,List<InAndOutDateVO> inAndOutDateVOList
            ,Boolean isLeaderData,String baseCode,List<String> ukList,String uk){
        if(Objects.nonNull(personInfoDTO.getInDate()) && Objects.nonNull(personInfoDTO.getOutDate())){
            if(ukList.contains(uk)){
                return;
            }
            InAndOutDateVO inAndOutDateVO = new InAndOutDateVO();
            inAndOutDateVO.setInDate(personInfoDTO.getInDate());
            inAndOutDateVO.setRepairRound(repairRound);
            inAndOutDateVO.setBaseCode(baseCode);
            inAndOutDateVO.setLeaderData(isLeaderData);
            inAndOutDateVO.setOutDate(personInfoDTO.getOutDate());
            inAndOutDateVO.setId(personInfoDTO.getId());
            inAndOutDateVOList.add(inAndOutDateVO);
            ukList.add(uk);

        }
    }

    public List<MaterialSourceVO>  packageMaterialJobList(List<MaterialSourceDTO> materialSourceDTOList,MaterialSourceVO materialSourceVO){
        List<MaterialSourceVO> materialSourceVOList = new ArrayList<>();
        for (MaterialSourceDTO materialSourceDTO : materialSourceDTOList) {
            // 会生成两条数据 1.工作数据 2.作业计划数据
            MaterialSourceVO jobMaterialSourceVO = new MaterialSourceVO();
            BeanCopyUtils.copyProperties(materialSourceDTO,jobMaterialSourceVO);
            // 作业信息设置 作业的计划开始时间和结束时间
            List<InAndOutDateVO> jobInAndOutDateList =new ArrayList<>();
            InAndOutDateVO jobInAndOutDateVO = new InAndOutDateVO();
            jobInAndOutDateVO.setId(materialSourceDTO.getRelationId());
//            inAndOutDateVO.setClassName();
            jobInAndOutDateVO.setInDate(materialSourceDTO.getJobBeginDate());
            jobInAndOutDateVO.setOutDate(materialSourceDTO.getJobEndDate());
            jobInAndOutDateList.add(jobInAndOutDateVO);
            jobMaterialSourceVO.setDataType("2");
            jobMaterialSourceVO.setJobName(materialSourceDTO.getJobName());
            jobMaterialSourceVO.setJobId(materialSourceDTO.getJobId());
            jobMaterialSourceVO.setId(materialSourceDTO.getRelationId());
            jobMaterialSourceVO.setUniqueId(IdUtil.fastSimpleUUID());
            jobMaterialSourceVO.setInAndOutDateVOList(jobInAndOutDateList);
            jobMaterialSourceVO.setJobBeginDate(materialSourceDTO.getJobBeginDate());
            jobMaterialSourceVO.setJobEndDate(materialSourceDTO.getJobEndDate());
            materialSourceVOList.add(jobMaterialSourceVO);

            MaterialSourceVO workMaterialSourceVO = new MaterialSourceVO();
            BeanCopyUtils.copyProperties(materialSourceDTO,workMaterialSourceVO);

            List<InAndOutDateVO> wordInAndOutDateVOList =new ArrayList<>();
            InAndOutDateVO workInAndOutDateVO = new InAndOutDateVO();
            workInAndOutDateVO.setId(materialSourceDTO.getRelationId());
//            inAndOutDateVO.setClassName();
            workInAndOutDateVO.setInDate(materialSourceDTO.getTaskBeginDate());
            workInAndOutDateVO.setOutDate(materialSourceDTO.getTaskEndDate());
            wordInAndOutDateVOList.add(workInAndOutDateVO);
            workMaterialSourceVO.setId(materialSourceDTO.getRelationId());
            workMaterialSourceVO.setUniqueId(IdUtil.fastSimpleUUID());
            workMaterialSourceVO.setDataType("3");
            workMaterialSourceVO.setJobName(materialSourceDTO.getJobName());
            workMaterialSourceVO.setJobId(materialSourceDTO.getJobId());
            workMaterialSourceVO.setInAndOutDateVOList(wordInAndOutDateVOList);
            workMaterialSourceVO.setJobBeginDate(materialSourceDTO.getJobBeginDate());
            workMaterialSourceVO.setJobEndDate(materialSourceDTO.getJobEndDate());
            materialSourceVOList.add(workMaterialSourceVO);
        }
        return materialSourceVOList;
    }


    public void packageMaterialDate(MaterialInfoDTO personInfoDTO,String repairRound,List<InAndOutDateVO> inAndOutDateVOList
            ,Boolean isLeaderData,String baseCode,List<String> ukList,String uk){
        if(Objects.nonNull(personInfoDTO.getInDate()) && Objects.nonNull(personInfoDTO.getOutDate())){
            if(ukList.contains(uk)){
                return;
            }
            InAndOutDateVO inAndOutDateVO = new InAndOutDateVO();
            inAndOutDateVO.setInDate(personInfoDTO.getInDate());
            inAndOutDateVO.setRepairRound(repairRound);
            inAndOutDateVO.setBaseCode(baseCode);
            inAndOutDateVO.setLeaderData(isLeaderData);
            inAndOutDateVO.setOutDate(personInfoDTO.getOutDate());
            inAndOutDateVO.setId(personInfoDTO.getId());
            inAndOutDateVOList.add(inAndOutDateVO);
            ukList.add(uk);
        }
    }

    /**
     *
     * @param start1  任务开始
     * @param end1     任务结束
     * @param start2    其他大修任务开始
     * @param end2      其他大修任务结束
     * @param quarterDateMap  季度区间
     * @return
     */
    // 计算两个时间段的重叠天数
    public static Map<Integer,OverlapResultDTO> calculateOverlapDays(Date start1, Date end1, Date start2, Date end2
            , Map<Integer, Date[]>  quarterDateMap) {
        LocalDate localDateStart1 = start1.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate localDateEnd1 = end1.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate localDateStart2 = start2.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate localDateEnd2 = end2.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate maxStart = localDateStart1.isAfter(localDateStart2) ? localDateStart1 : localDateStart2;
        LocalDate minEnd = localDateEnd1.isBefore(localDateEnd2) ? localDateEnd1 : localDateEnd2;
        Map<Integer,OverlapResultDTO> quarterNumToOverlapDays = new HashMap<>();
        // Calculate the number of overlapping days and the overlap start and end dates  判断是否重叠 如果有重叠那么 需要 判断数据处于哪些季度重叠统计出 所处的季度统计
        if (minEnd.isAfter(maxStart) || minEnd.equals(maxStart)) {
            // There is an overlap
            int overlapDays =  ChronoUnit.DAYS.between(maxStart, minEnd) > 0 ? (int) ChronoUnit.DAYS.between(maxStart, minEnd)+1  : 1;
            if(overlapDays >0){
                for (Map.Entry<Integer, Date[]> integerEntry : quarterDateMap.entrySet()) {
                    Integer quarterNumKey = integerEntry.getKey();
                    Date[] quarterDates =   integerEntry.getValue();

                    Date beginDate= quarterDates[0];
                    Date endDate= quarterDates[1];

                    if(start1.compareTo(beginDate)<0){
                        localDateStart1 = beginDate.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }else{
                        localDateEnd1 = start1.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }
                    if(end1.compareTo(endDate)>0){
                        localDateEnd1 = endDate.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }else{
                        localDateEnd1 = end1.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }
                    maxStart = localDateStart1.isAfter(localDateStart2) ? localDateStart1: localDateStart2 ;
                    minEnd = localDateEnd1.isBefore(localDateEnd2) ? localDateEnd1 : localDateEnd2;
                    if(minEnd.isAfter(maxStart) || minEnd.equals(maxStart)){
                        overlapDays = ChronoUnit.DAYS.between(maxStart, minEnd) > 0 ? (int) ChronoUnit.DAYS.between(maxStart, minEnd)+1  : 1;
                        if(overlapDays > 0){
                            Date overlapStartDate = Date.from(maxStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
                            Date overlapEndDate = Date.from(minEnd.atStartOfDay(ZoneId.systemDefault()).plusDays(1).minusNanos(1).toInstant());

                            quarterNumToOverlapDays.put(quarterNumKey,new OverlapResultDTO(overlapStartDate, overlapEndDate, overlapDays)) ;
                        }
                        localDateStart1 = start1.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                        localDateEnd1 = end1.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    }
                }
            }
        }
        return quarterNumToOverlapDays;
    }




}
