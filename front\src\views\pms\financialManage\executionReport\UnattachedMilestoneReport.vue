<script setup lang="ts">
import {
  ref, h, Ref, nextTick,
} from 'vue';
import { message, Modal, Tooltip } from 'ant-design-vue';
import {
  BasicButton, downloadByData, Layout, useVueToPrint, OrionTable, randomString,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { getUnattachedMilestone, getUnattachedAccounts } from '/@/views/pms/financialManage/report';
import MilestoneToolbar from '../components/MilestoneToolbar.vue';
import {
  UnattachedPlanFilterConfig,
  UnattachedVoucherFilterConfig,
} from '/@/views/pms/financialManage/filterIndex';

const mode = ref('plan');
const selectRowKeys = ref([]);
const selectionRows = ref([]);
const powerData = ref<any[]>([]);
const loadStatus: Ref<boolean> = ref(false);
const isFlag = ref(true);
const downParams = ref();
const elRef = ref(null);
const randomKey = ref(randomString(6));
const tables = ref([
  {
    mode: 'tablePlan',
    ref: 'tablePlan',
    columns: getUnattachedMilestone(() => {
      onUpdateValue();
    }),
  },
  {
    mode: 'other',
    ref: 'tableRef',
    columns: getUnattachedAccounts(() => {
      onUpdateValue();
    }),
  },
]);

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const conditions = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition));

    // 将 conditions 转换为 { field1: value1, field2: value2, ... } 的形式
    const query = conditions.reduce((acc, condition) => {
      if (condition.values && condition.values.length > 0) {
        acc[condition.field] = condition.values.join(','); // 假设 values 是一个数组，这里用逗号连接
      }
      return acc;
    }, {});

    return {
      ...params,
      query: {
        ...query,
      },
      power: {
        pageCode: 'WGRLCBSRMX_001',
      },
    };
  }
  return {
    ...params,
    query: {
      number: params.number,
    },
    power: {
      pageCode: 'WGRLCBSRMX_001',
    },
  };
}

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectRowKeys.value = _keys;
      selectionRows.value = rows;
      isFlag.value = hasInvalidRows(rows);
    },
  },
  showIndexColumn: true,
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  showSmallSearch: true,
  smallSearchField: ['number'],
  showTableSetting: false,
  filterConfig: {
    fields: UnattachedPlanFilterConfig,
  },
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms/connectedMilestones/incomePlanPage').fetch(newParams, '', 'POST').then((res) => {
      downParams.value = params.searchConditions;
      return res;
    });
  },
  columns: tables.value[0].columns,
};

// 如果存在任何一个 row 对象中包含未填写的必填字段，则 rows.some 返回 true。如果所有 row 对象中的必填字段都不为空，则 rows.some 返回 false。
function hasInvalidRows(rows) {
  if (!Array.isArray(rows) || rows.length === 0) {
    return false;
  }

  return rows.some((row) =>
    !row.contractNumber
      || !row.contractName
      || !row.contractId
      || !row.milestoneName);
}

const adjustmentVoucherTableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectRowKeys.value = _keys;
      selectionRows.value = rows;
    },
  },
  showIndexColumn: true,
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  showSmallSearch: true,
  smallSearchField: ['contractName'],
  showTableSetting: false,
  filterConfig: {
    fields: UnattachedVoucherFilterConfig,
  },
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms/adjustmentVoucher/page').fetch(newParams, '', 'POST').then((res) => {
      downParams.value = params.searchConditions;
      return res;
    });
  },
  columns: tables.value[1].columns,
};

const toolButtons = [
  {
    event: 'mountingConfirmation',
    text: '挂接确认',
    icon: 'sie-icon-chakantuisong',
    powerCode: 'PMS_WGRLCBSRMX_container_01_button_01',
  },
  {
    event: 'bulkExport',
    text: '批量导出',
    icon: 'sie-icon-daochu',
    powerCode: 'PMS_WGRLCBSRMX_container_01_button_02',
  },
  {
    event: 'reportPrinting',
    text: '报表打印',
    icon: 'sie-icon-hetongqianding',
    powerCode: 'PMS_WGRLCBSRMX_container_01_button_03',
  },
];

function onUpdateValue() {
  updateTable();
}

function getExportParams(): Record<string, any> {
  const downParamsSearch = downParams.value;
  return {
    searchConditions: downParamsSearch,
    ids: selectRowKeys.value,
  };
}

// 导出
async function handleExport() {
  const exportParams = getExportParams();
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData(mode.value === 'plan' ? '/pms/connectedMilestones/export/excel' : '/pms/adjustmentVoucher/export/excel', exportParams, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 切换table模式
function handleModeChange(e) {
  selectRowKeys.value = [];
  selectionRows.value = [];
  mode.value = e.target.value;
  updateTable();
}

const confirmModalBatch = ref<any>(null);

const showConfirmModal = (title: string, content: string | (() => any), onOk?: () => void) => {
  confirmModalBatch.value = Modal.confirm({
    title,
    content,
    onOk,
  });
};

const toolClick = (button: any) => {
  switch (button.event) {
    case 'mountingConfirmation':
      showConfirmModal(
        '合同里程碑挂接后不可取消',
        () => h('div', {}, [h('p', '请确认是否继续执行？')]),
        () =>
        // eslint-disable-next-line no-async-promise-executor
          new Promise(async (resolve, reject) => {
            confirmModalBatch.value.update({ okButtonProps: { loading: true } }); // 设置 loading 状态
            try {
              // 执行挂接确认逻辑
              await submitMilestone(resolve, reject);
            } catch (error) {
              reject(error);
            } finally {
              confirmModalBatch.value.update({ okButtonProps: { loading: false } }); // 关闭 loading 状态
            }
          })
        ,
      );
      break;
    case 'bulkExport':
      handleExport();
      break;
    case 'reportPrinting':
      handlePrint();
      break;
  }
};

async function submitMilestone(resolve, reject) {
  if (!selectRowKeys.value.length) return;
  const result = await new Api(mode.value === 'plan' ? '/pms/connectedMilestones/hangingConnect' : '/pms/adjustmentVoucher/hangingConnect').fetch(selectRowKeys.value, '', 'POST');
  if (result) {
    resolve(null);
    message.success('挂接成功');
    updateTable();
  } else {
    confirmModalBatch.value.update({ okButtonProps: { loading: false } }); // 关闭 loading 状态
    reject('');
  }
}

// 打印
const { handlePrint } = useVueToPrint({
  content: elRef,
  removeAfterPrint: true,
  bodyClass: 'surely-table-body-container',
  documentTitle: '未挂接里程碑收入明细报表',
  pageStyle: `.flex.orion-table-header-wrap {
          display: none;
        }
      `,
});

function updateTable() {
  randomKey.value = randomString(10);
}

// 获取权限数据
function getPowerDataHandle(power: any) {
  powerData.value = power;
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'WGRLCBSRMX_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <div ref="elRef">
      <OrionTable
        :ref="mode === 'plan' ? tables[0].ref : tables[1].ref"
        :key="randomKey"
        :options="mode === 'plan' ? tableOptions : adjustmentVoucherTableOptions"
        :columns="mode === 'plan' ? tables[0].columns : tables[1].columns"
        xVirtual
        :class="elRef"
      >
        <template #toolbarLeft>
          <template
            v-for="button in toolButtons"
            :key="button.event"
          >
            <Tooltip placement="top">
              <template
                v-if="button.event === 'mountingConfirmation' && (selectionRows.length && isFlag)"
                #title
              >
                <span class="tooltip">勾选数据存在必填字段未填写，请检查数据！</span>
              </template>
              <BasicButton
                v-is-power="[button.powerCode]"
                v-bind="button"
                type="primary"
                :disabled="button.event === 'mountingConfirmation' && (!selectionRows.length || isFlag)"
                :ghost="button.event !== 'mountingConfirmation'"
                @click="toolClick(button)"
              >
                {{ button.text }}
              </BasicButton>
            </Tooltip>
          </template>
        </template>
        <template #toolbarCenter>
          <div class="flex-row-right">
            <MilestoneToolbar
              :mode="mode"
              @handleModeChange="handleModeChange"
            />
          </div>
        </template>
      </OrionTable>
    </div>
  </Layout>
</template>

<style lang="less">
.surely-table-cell.required-cell {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    left: calc(50% - 35px);
  }
}
.surely-table-cell{
  .action-hover-btn{
    display: inline-block;
    cursor: pointer;
    width: 100%;
    min-height: 22px;
  }
}
</style>

<style lang="less" scoped>
@import url('../details/css/report.less');
</style>
