package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.entity.EvaluationProject;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.domain.vo.EvaluationProjectVO;
import com.chinasie.orion.domain.vo.EvaluationTypeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * EvaluationProject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
public interface EvaluationProjectService extends OrionBaseService<EvaluationProject> {

    /**
     * 新增
     * <p>
     * * @param evaluationProjectDTO
     */
    EvaluationProjectVO create(EvaluationProjectDTO evaluationProjectDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param evaluationProjectDTO
     */
    Boolean edit(EvaluationProjectDTO evaluationProjectDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    PageResult<EvaluationProjectVO> getProjectEvaluationPage(Page<EvaluationProjectDTO> pageRequest) throws Exception;

    List<EvaluationTypeVO> getEvaluationType(String projectId);

    EvaluationProjectVO getByDataId(String id) throws Exception;
}
