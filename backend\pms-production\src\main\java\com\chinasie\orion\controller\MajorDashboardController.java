package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.JobStudyReviewVO;
import com.chinasie.orion.domain.vo.major.BaseMaterialCountVO;
import com.chinasie.orion.domain.vo.major.JobDashBoardVO;
import com.chinasie.orion.domain.vo.major.MaterialDashboardVO;
import com.chinasie.orion.domain.vo.major.PersonDashboardVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.MajorDashboardService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/18:33
 * @description:
 */
@RestController
@RequestMapping("/major-dashboard")
@Api(tags = "大修看板")
public class MajorDashboardController  implements Serializable {


    @Autowired
    private MajorDashboardService majorDashboardService;

    /**
     * 详情
     * @param repairRound
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业统计")
    @RequestMapping(value = "/job/dashboard/count", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修看板-作业】统计", type = "JobDashBoard", subType = "大修看板", bizNo = "{{#repairRound}}")
    public ResponseDTO<JobDashBoardVO> jobDashboardCount(@RequestParam("repairRound") String repairRound) throws Exception {
        JobDashBoardVO rsp = majorDashboardService.jobDashboardCount(repairRound);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 详情
     * @param repairRound
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "人员统计")
    @RequestMapping(value = "/person/dashboard/count", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修看板-人员】统计", type = "JobDashBoard", subType = "大修看板", bizNo = "{{#repairRound}}")
    public ResponseDTO<PersonDashboardVO> personDashboardCount(@RequestParam("repairRound") String repairRound) throws Exception {
        PersonDashboardVO rsp = majorDashboardService.personDashboardCount(repairRound);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     * @param repairRound
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资统计")
    @RequestMapping(value = "/material/dashboard/count", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修看板-物资】统计", type = "JobDashBoard", subType = "大修看板", bizNo = "{{#repairRound}}")
    public ResponseDTO<MaterialDashboardVO> materialDashboardCount(@RequestParam("repairRound") String repairRound) throws Exception {
        MaterialDashboardVO rsp = majorDashboardService.materialDashboardCount(repairRound);
        return new ResponseDTO<>(rsp);
    }

}
