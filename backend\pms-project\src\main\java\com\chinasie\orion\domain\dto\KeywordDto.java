package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/18:13
 * @description:
 */
@Data
public class KeywordDto implements Serializable {
    @ApiModelProperty(value = "关键词")
    private String keyword;
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "所属项目不能为空")
    private String projectId;
    @ApiModelProperty(value = "状态")
    private List<Integer> status;
}
