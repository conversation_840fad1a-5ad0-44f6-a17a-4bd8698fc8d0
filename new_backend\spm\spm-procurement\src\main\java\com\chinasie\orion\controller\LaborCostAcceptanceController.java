package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.LaborCostAcceptanceDTO;
import com.chinasie.orion.domain.vo.LaborCostAcceptanceVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.LaborCostAcceptanceService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * LaborCostAcceptance 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
@RestController
@RequestMapping("/laborCostAcceptance")
@Api(tags = "人力成本验收单")
public class  LaborCostAcceptanceController  {

    @Autowired
    private LaborCostAcceptanceService laborCostAcceptanceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人力成本验收单", subType = "查询", bizNo = "{{#id}}")
    public ResponseDTO<LaborCostAcceptanceVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        LaborCostAcceptanceVO rsp = laborCostAcceptanceService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param laborCostAcceptanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#laborCostAcceptanceDTO.name}}】", type = "人力成本验收单", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<LaborCostAcceptanceVO> create(@RequestBody @Validated  LaborCostAcceptanceDTO laborCostAcceptanceDTO) throws Exception {
        LaborCostAcceptanceVO rsp =  laborCostAcceptanceService.create(laborCostAcceptanceDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param laborCostAcceptanceDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#laborCostAcceptanceDTO.name}}】", type = "人力成本验收单", subType = "编辑", bizNo = "{{#laborCostAcceptanceDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated  LaborCostAcceptanceDTO laborCostAcceptanceDTO) throws Exception {
        Boolean rsp = laborCostAcceptanceService.edit(laborCostAcceptanceDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "人力成本验收单", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = laborCostAcceptanceService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "人力成本验收单", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = laborCostAcceptanceService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人力成本验收单", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<LaborCostAcceptanceVO>> pages(@RequestBody Page<LaborCostAcceptanceDTO> pageRequest) throws Exception {
        Page<LaborCostAcceptanceVO> rsp =  laborCostAcceptanceService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人力成本验收单导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "人力成本验收单", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        laborCostAcceptanceService.downloadExcelTpl(response);
    }

    @ApiOperation("人力成本验收单导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "人力成本验收单", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = laborCostAcceptanceService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人力成本验收单导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "人力成本验收单", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  laborCostAcceptanceService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消人力成本验收单导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "人力成本验收单", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  laborCostAcceptanceService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人力成本验收单导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "人力成本验收单", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        laborCostAcceptanceService.exportByExcel(searchConditions, response);
    }
}
