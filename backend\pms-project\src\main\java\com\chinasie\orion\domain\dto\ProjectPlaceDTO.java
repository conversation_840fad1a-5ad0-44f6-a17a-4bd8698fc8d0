package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectPlace DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
@ApiModel(value = "ProjectPlaceDTO对象", description = "项目地点")
@Data
@ExcelIgnoreUnannotated
public class ProjectPlaceDTO extends ObjectDTO implements Serializable {

    /**
     * 地区id
     */
    @ApiModelProperty(value = "地区id")
    private String areaId;

    /**
     * 项目数据主键
     */
    @ApiModelProperty(value = "项目数据主键")
    @ExcelProperty(value = "项目数据主键 ", index = 3)
    private String projectId;


}
