package com.chinasie.orion.domain.entity.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QualityItemToProduct Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-23 16:13:20
 */
@TableName(value = "pmsx_quality_item_to_product")
@ApiModel(value = "QualityItemToProductEntity对象", description = "质量管控项目产品关联表")
@Data

public class QualityItemToProduct extends  ObjectEntity  implements Serializable{

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    @TableField(value = "product_id")
    private String productId;

    /**
     * 质量管控项ID
     */
    @ApiModelProperty(value = "质量管控项ID")
    @TableField(value = "quality_item_id")
    private String qualityItemId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(value = "product_name")
    private String ProductName;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_number")
    private String productNumber;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

}
