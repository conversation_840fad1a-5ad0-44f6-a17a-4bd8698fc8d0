<script setup lang="ts">
import { InputSelectUser, Select } from 'lyra-component-vue3';
import {
  computed, ref, Ref, watch,
} from 'vue';
import {
  Input, InputNumber, Spin, DatePicker, Popover,
} from 'ant-design-vue';
import { filterProperty } from '/@/views/pms/utils/utils';

const props = withDefaults(defineProps<{
  popoverContainer: any,
  record: Record<string, any>,
  text: string,
  componentValue: any,
  component: string,
  componentProps?: any,
  editFlag?: boolean,
}>(), {
  componentProps: () => ({}),
});

const emits = defineEmits<{
  (e: 'submit', value: any, resolve: (value: any) => void): void
}>();

const editValue: Ref = ref();
const timer: Ref = ref();
const isFocus: Ref<boolean> = ref(false);
const options: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);
const isEdit = ref(false);
const visible = ref<boolean>(false);

function onFocus() {
  isFocus.value = true;
}

function onBlur() {
  isFocus.value = false;
}

watch(() => props.editFlag, async (value) => {
  if (!value) {
    editValue.value = null;
    options.value = [];
  } else {
    editValue.value = props?.text;
    await fetch();
    editValue.value = props?.componentValue;
  }
});

function onInputSelectUserChange(users: any[]) {
  submit(users?.[0]?.id);
}

const isSubmit: Ref<boolean> = ref(false);

async function submit(data: any) {
  await new Promise((resolve) => {
    emits('submit', data, resolve);
    visible.value = false;
  });
  isSubmit.value = true;
}

function onSelectChange(_value: string, option: Record<string, any>) {
  submit(option);
}

async function fetch() {
  if (typeof props?.componentProps?.api === 'function') {
    fetching.value = true;
    try {
      const result = await props.componentProps.api();
      options.value = result || [];
    } finally {
      fetching.value = false;
    }
  } else {
    options.value = [];
  }
}

const getPopupContainer = () => props.popoverContainer.value;
</script>

<template>
  <div
    :class="component==='Input'?'mouse-cell-input':'mouse-cell'"
  >
    <template v-if="props.editFlag">
      <Popover
        v-model:visible="visible"
        trigger="click"
        placement="bottom"
        :getPopupContainer="getPopupContainer"
      >
        <template #content>
          <template v-if="component==='Input'">
            <Input
              v-model:value="editValue"
              @pressEnter="submit(editValue)"
              @focus="onFocus"
              @blur="submit(editValue)"
            />
          </template>
          <template v-if="component==='InputSelectUser'">
            <InputSelectUser
              v-bind="componentProps"
              :selectUserData="editValue"
              @focus="onFocus"
              @blur="onBlur"
              @change="onInputSelectUserChange"
            />
          </template>
          <template v-if="component==='Select'">
            <Select
              class="sw130"
              :options="options"
              v-bind="filterProperty(componentProps,'api')"
              :value="editValue"
              @focus="onFocus"
              @blur="onBlur"
              @change="onSelectChange"
            >
              <template
                v-if="fetching"
                #notFoundContent
              >
                <Spin size="small" />
              </template>
            </Select>
          </template>
          <template v-if="component==='InputNumber'">
            <InputNumber
              v-model:value="editValue"
              v-bind="componentProps"
              @pressEnter="submit(editValue)"
              @focus="onFocus"
              @blur="onBlur"
            />
          </template>
          <template v-if="component==='DatePicker'">
            <DatePicker
              v-model:value="editValue"
              v-bind="componentProps"
              @focus="onFocus"
              @blur="onBlur"
              @change="(...arg)=>submit(arg)"
            />
          </template>
        </template>
        <div class="custom-power">
          {{ record.urgency || 'D' }}
        </div>
      </Popover>
    </template>
    <template v-else>
      <Popover
        placement="top"
        :getPopupContainer="getPopupContainer"
      >
        <template #content>
          {{ record.urgency === 'A' ? '紧急重要' : (record.urgency === 'B' ? '紧急不重要' : (record.urgency === 'C' ? '不紧急重要' : '不紧急不重要')) }}
        </template>
        {{ record.urgency || 'D' }}
      </Popover>
    </template>
  </div>
</template>

<style scoped lang="less">
.mouse-cell {
  min-height: 22px;
}
.mouse-cell-input {
  min-height: 22px;
  div{
    color: #0000ff;
  }
}
.sw130{
  width: 150px!important;
}
.custom-power {
  cursor: pointer;
}
</style>
