package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDetailDTO;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceDetail;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceDetailVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * PersonRoleMaintenanceDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
public interface PersonRoleMaintenanceDetailService  extends  OrionBaseService<PersonRoleMaintenanceDetail>  {


    /**
     *  详情
     *
     * * @param id
     */
    PersonRoleMaintenanceDetailVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param personRoleMaintenanceDetailDTO
     */
    String create(PersonRoleMaintenanceDetailDTO personRoleMaintenanceDetailDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param personRoleMaintenanceDetailDTO
     */
    Boolean edit(PersonRoleMaintenanceDetailDTO personRoleMaintenanceDetailDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<PersonRoleMaintenanceDetailVO> pages( Page<PersonRoleMaintenanceDetailDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<PersonRoleMaintenanceDetailVO> vos)throws Exception;
}
