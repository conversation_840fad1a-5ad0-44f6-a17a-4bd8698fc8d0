<template>
  <ul
    class="menu-children"
    :class="size"
  >
    <li
      v-for="(item, index) in menuData"
      :key="index"
      class="menu-item"
      :class="item.isOpen ? 'open' : ''"
    >
      <div
        :style="{ 'padding-left': item.paddingLeft + 'px' }"
        :class="{
          active: actionId === item.id,
          left: actionLineAlign === 'left',
          right: actionLineAlign === 'right'
        }"
        @click.stop="menuChange({ id: item.id, index, item }, menuData)"
      >
        <slot
          name="menuItem"
          :item="item"
          :index="index"
        >
          {{ item.name }}
        </slot>
        <div
          v-if="
            item.isOpen !== null &&
              item.children &&
              item.children.length &&
              $attrs.isOpenIcon !== false
          "
          class="open-icon"
        >
          <Icon
            icon="fa-angle-up"
            size="13"
            color="rgba(0,0,0,.6)"
          />
        </div>
      </div>
      <template v-if="item.children && item.children.length > 0">
        <BasicMenuItem
          v-bind="$attrs"
          :size="size"
          :menu-data="item.children"
          @menuChange="childItemChange"
          @click.stop="menuChange({ id: item.id, index, item }, menuData)"
        >
          <!--          @click.stop="menuChange({ id: item.id, index, item }, menuData)"-->
          <!--          @menuChange="menuChange"-->
          <template
            v-for="item in Object.keys($slots)"
            #[item]="data"
          >
            <slot
              v-bind="data"
              :name="item"
            />
          </template>
        </BasicMenuItem>
      </template>
    </li>
  </ul>
</template>

<script>
import { ref, watch, inject } from 'vue';
import { UpOutlined } from '@ant-design/icons-vue';
import {
  Icon,
} from 'lyra-component-vue3';
// import Icon from '/@/components/Icon';

export default {
  name: 'BasicMenuItem',
  components: {
    // UpOutlined,
    Icon,
  },
  props: {
    menuData: {
      type: Array,
      default: () => [],
    },
    actionLineAlign: {
      type: String,
      default: 'left',
    },
    size: {
      type: String,
      default: 'default',
    },
  },
  emits: ['menuChange'],
  setup(props, context) {
    const actionId = inject('actionId');
    const updateActionId = inject('updateActionId');

    watch(
      () => props.actionId,
      () => {
        actionIdNow.value = props.actionId;
      },
    );

    return {
      // actionIdNow,
      actionId,
      menuChange({ id, index, item }, menuData) {
        menuData
            && menuData.forEach((menuItem) => {
              if (menuItem.isOpen !== undefined && menuItem.id !== id) {
                menuItem.isOpen = false;
              }
            });
        if (item.isOpen !== undefined) {
          item.isOpen = !item.isOpen;
        }
        updateActionId(id);
        context.emit('menuChange', {
          id,
          index,
          item,
        });
      },
      childItemChange({ id, index, item }) {
        context.emit('menuChange', {
          id,
          index,
          item,
        });
      },
    };
  },
};
</script>

<style lang="less" scoped>
  .menu-children {
    > :deep(.menu-item) {
      //padding-left: 20px;
      position: relative;

      > .menu-children {
        max-height: 0;
        //transition: 1s;
        overflow: hidden;
      }

      &.open {
        > .menu-children {
          max-height: 1000px;
        }
        > div {
          > .open-icon {
            transform: rotate(180deg);
          }
        }
      }

      > div {
        transition: 0.3s;
        height: 50px;
        line-height: 50px;
        cursor: pointer;
        position: relative;

        &:active {
          color: ~`getPrefixVar('primary-color')`;
          background-color: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        }

        &.right {
          &::before {
            left: auto;
            right: 0;
          }
        }

        &::before {
          content: '';
          width: 3px;
          height: 0;
          background: ~`getPrefixVar('primary-color')`;
          position: absolute;
          left: 0;
          top: 50%;
          transition: 0.2s;
          opacity: 0;
        }

        &.active {
          color: ~`getPrefixVar('primary-color')`;
          background-color: ~`getPrefixVar('primary-color-deprecated-f-12')`;

          &::before {
            height: 100%;
            top: 0;
            opacity: 1;
          }
        }

        &:hover {
          color: ~`getPrefixVar('primary-color')`;
          background-color: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        }

        .open-icon {
          position: absolute;
          right: 10px;
          font-size: 12px;
          transition: 0.3s;
          transform: rotate(0deg);
          width: 12px;
          height: 12px;
          line-height: 12px;
          top: 50%;
          margin-top: -6px;
        }
      }
    }

    &.small {
      > :deep(.menu-item) {
        > div {
          height: 40px;
          line-height: 40px;
        }
      }
    }
  }
</style>
