package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ProjectSchemePrePostDTO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:14
 * @description:
 * <p>
 * 项目计划前后置关系DTO
 * </p>
 */
@Data
@ApiModel(value = "ProjectRoleUserDTO对象", description = "项目计划前后置关系DTO")
public class ProjectSchemePrePostDTO extends ObjectDTO {

    /**
     *前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    private String preSchemeId;

    /**
     *后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    private String postSchemeId;

    /**
     *项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;


    /**
     *项目计划id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    private Integer type;

}
