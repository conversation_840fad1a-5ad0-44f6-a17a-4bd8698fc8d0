package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "ProjectManhourVO", description = "项目工时统计")
public class ProjectWorkHourVO {
    @ApiModelProperty(value = "计划工时")
    private Integer estimateWorkHour  = 0;
    @ApiModelProperty(value = "填报工时")
    private Integer fillWorkHour  = 0;
    @ApiModelProperty(value = "剩余工时")
    private Integer surplusWorkHour=0;
    @ApiModelProperty(value = "预估偏差")
    private Integer estimateDeviation=0;
    @ApiModelProperty(value = "工时填报进度")
    private BigDecimal fillSchedule=new BigDecimal("0");
    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRatio=new BigDecimal("0");
}
