import dayjs from 'dayjs';

export const sTableTree = {
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      width: 200,
      resizable: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 300,
      resizable: true,
    },
    // {
    //   title: '管理节点',
    //   dataIndex: 'manageNode',
    //   minWidth: 100,
    //   resizable: true,
    // },
    // {
    //   title: '风险项',
    //   dataIndex: 'riskItem',
    //   minWidth: 100,
    //   resizable: true,
    // },
    {
      title: '计划类型',
      dataIndex: 'planTypeName',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      align: 'left',
      width: 150,
      ellipsis: true,
      slots: { customRender: 'statusName' },
    },
    {
      title: '进度状态',
      dataIndex: 'speedStatusName',
      align: 'left',
      width: 150,
      ellipsis: true,
      slots: { customRender: 'speedStatusName' },
    },
    {
      title: '责任单位',
      dataIndex: 'resOrgName',
      minWidth: 100,
      resizable: true,
    },
    // {
    //   title: '参与单位',
    //   dataIndex: 'joinOrgsName',
    //   minWidth: 100,
    //   resizable: true,
    // },
    {
      title: '责任科室',
      dataIndex: 'resDeptName',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '责任人',
      dataIndex: 'resUserName',
      width: 100,
      resizable: true,
    },
    // {
    //   title: '参与科室',
    //   dataIndex: 'joinDeptsName',
    //   width: 100,
    //   resizable: true,
    // },
    {
      title: '计划开始日期',
      dataIndex: 'planStartTime',
      width: 120,
      resizable: true,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'planEndTime',
      width: 120,
      resizable: true,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
};
export const index = {
  btnConfig: {
    check: { show: true },
    open: { show: true },
    edit: { show: true },
    delete: { show: true },
    // up: { show: true },
    // down: { show: true },
    search: { show: true },
  },
  addForm() {
    return {
      className: undefined,
      createTime: undefined,
      creatorId: undefined,
      id: undefined,
      logicStatus: undefined,
      modifyId: undefined,
      modifyTime: undefined,
      name: undefined,
      number: undefined,
      ownerId: undefined,
      parentId: undefined,
      planEndTime: undefined,
      planImage: undefined,
      planPredictEndTime: undefined,
      planPredictStartTime: undefined,
      planStartTime: undefined,
      planType: undefined,
      principalId: undefined,
      principalName: undefined,
      priorityLevel: undefined,
      projectId: undefined,
      remark: undefined,
      schedule: undefined,
      sort: undefined,
      status: undefined,
      taskStatusId: undefined,
      realityManHour: undefined,
    };
  },
};
