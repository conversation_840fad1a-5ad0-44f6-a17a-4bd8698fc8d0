package com.chinasie.orion.feign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/03/13/15:48
 * @description:
 */
@Data
public class ParamInsCopyDTO {
    @ApiModelProperty(value = "数据ID")
    private String dataId;
    @ApiModelProperty(value = "数据名称")
    private String dataName;
    @ApiModelProperty(value = "数据跳转地址")
    private String dataHref;
    @ApiModelProperty(value = "实列ID列表")
    private List<String> insIdList;

}
