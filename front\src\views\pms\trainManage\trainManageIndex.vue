<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  h, nextTick, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { openTrainManageForm } from '/@/views/pms/trainManage/utils';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  showToolButton: false,
  api: (params: any) => new Api('/pms/train-manage/page').fetch({
    ...params,
    query: {
      trainType: '1',
    },
    power: {
      pageCode: 'PMSTrainManage',
      containerCode: 'PMS_YWZXPX_container_02',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '是否考核',
      dataIndex: 'isCheck',
      width: 80,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
      width: 110,
    },
    {
      title: '培训名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        if (isPower('PMS_YWZXPX_container_02_button_01', record?.rdAuthList)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '培训内容',
      dataIndex: 'content',
    },
    {
      title: '拟完成时间',
      dataIndex: 'completeDate',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
      width: 80,
    },
    {
      title: '业务状态',
      dataIndex: 'dataStatus',
      width: 80,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'endDate',
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record) => isPower('PMS_YWZXPX_container_02_button_01', record?.rdAuthList),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
    {
      text: '编辑',
      isShow: (record) => isPower('PMS_YWZXPX_container_02_button_02', record?.rdAuthList) && record?.edit,
      onClick(record: { id: string }) {
        openTrainManageForm(record, updateTable);
      },
    },
    {
      text: '移除',
      isShow: (record) => isPower('PMS_YWZXPX_container_02_button_03', record?.rdAuthList) && record?.edit,
      modal: (record: { id: string }) => removeApi([record?.id]),
    },
  ],
};

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}

function navDetails(id: string) {
  router.push({
    name: 'PMSTrainManageDetails',
    params: {
      id,
    },
  });
}

function handleRemove() {
  if (selectedRows.value.every((item: any) => item?.edit)) {
    return Modal.confirm({
      title: '移除提示！',
      content: '确认移除已勾选数据？',
      onOk: () => removeApi(selectedKeys.value),
    });
  }
  return message.info('存在不可移除的数据，请重新选择！');
}

function removeApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/train-manage/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch(() => {
      reject('');
    });
  });
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSTrainManage', getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_YWZXPX_container_01_button_01']"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openTrainManageForm(null,updateTable)"
        >
          添加培训
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_YWZXPX_container_01_button_02']"
          icon="sie-icon-shanchu"
          :disabled="selectedKeys.length===0"
          @click="handleRemove"
        >
          移除
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
