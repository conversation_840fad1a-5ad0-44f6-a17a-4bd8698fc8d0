package com.chinasie.orion.service.impl;





import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.PersonJobPostEqu;
import com.chinasie.orion.domain.entity.PersonTrainEquRecord;
import com.chinasie.orion.domain.dto.PersonTrainEquRecordDTO;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.vo.PersonJobPostEquVO;
import com.chinasie.orion.domain.vo.PersonTrainEquRecordVO;


import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.service.PersonTrainEquRecordService;
import com.chinasie.orion.repository.PersonTrainEquRecordMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PersonTrainEquRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:55
 */
@Service
@Slf4j
public class PersonTrainEquRecordServiceImpl extends  OrionBaseServiceImpl<PersonTrainEquRecordMapper, PersonTrainEquRecord>   implements PersonTrainEquRecordService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PersonTrainEquRecordVO detail(String id,String pageCode) throws Exception {
        PersonTrainEquRecord personTrainEquRecord =this.getById(id);
        PersonTrainEquRecordVO result = BeanCopyUtils.convertTo(personTrainEquRecord,PersonTrainEquRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personTrainEquRecordDTO
     */
    @Override
    public  String create(PersonTrainEquRecordDTO personTrainEquRecordDTO) throws Exception {
        PersonTrainEquRecord personTrainEquRecord =BeanCopyUtils.convertTo(personTrainEquRecordDTO,PersonTrainEquRecord::new);
        this.save(personTrainEquRecord);

        String rsp=personTrainEquRecord.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personTrainEquRecordDTO
     */
    @Override
    public Boolean edit(PersonTrainEquRecordDTO personTrainEquRecordDTO) throws Exception {
        PersonTrainEquRecord personTrainEquRecord =BeanCopyUtils.convertTo(personTrainEquRecordDTO,PersonTrainEquRecord::new);

        this.updateById(personTrainEquRecord);

        String rsp=personTrainEquRecord.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonTrainEquRecordVO> pages( Page<PersonTrainEquRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonTrainEquRecord> condition = new LambdaQueryWrapperX<>( PersonTrainEquRecord. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonTrainEquRecord::getCreateTime);


        Page<PersonTrainEquRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonTrainEquRecord::new));

        PageResult<PersonTrainEquRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonTrainEquRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonTrainEquRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonTrainEquRecordVO::new);
        if(CollectionUtils.isEmpty(vos)){
            pageResult.setContent(vos);
            return pageResult;
        }
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员培训等效信息记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonTrainEquRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            PersonTrainEquRecordExcelListener excelReadListener = new PersonTrainEquRecordExcelListener();
        EasyExcel.read(inputStream,PersonTrainEquRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonTrainEquRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员培训等效信息记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonTrainEquRecord> personTrainEquRecordes =BeanCopyUtils.convertListTo(dtoS,PersonTrainEquRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonTrainEquRecord-import::id", importId, personTrainEquRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonTrainEquRecord> personTrainEquRecordes = (List<PersonTrainEquRecord>) orionJ2CacheService.get("pmsx::PersonTrainEquRecord-import::id", importId);
        log.info("人员培训等效信息记录导入的入库数据={}", JSONUtil.toJsonStr(personTrainEquRecordes));

        this.saveBatch(personTrainEquRecordes);
        orionJ2CacheService.delete("pmsx::PersonTrainEquRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonTrainEquRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonTrainEquRecord> condition = new LambdaQueryWrapperX<>( PersonTrainEquRecord. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonTrainEquRecord::getCreateTime);
        List<PersonTrainEquRecord> personTrainEquRecordes =   this.list(condition);

        List<PersonTrainEquRecordDTO> dtos = BeanCopyUtils.convertListTo(personTrainEquRecordes, PersonTrainEquRecordDTO::new);

        String fileName = "人员培训等效信息记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonTrainEquRecordDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonTrainEquRecordVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<PersonTrainEquRecordVO> listByUserCode(String userCode) throws Exception {
        if(StrUtil.isEmpty(userCode)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<PersonTrainEquRecord> condition = new LambdaQueryWrapperX<>( PersonTrainEquRecord. class);
        condition.eq(PersonTrainEquRecord::getUserCode,userCode);
        condition.select(PersonTrainEquRecord::getUserCode,PersonTrainEquRecord::getTrainNumber
                ,PersonTrainEquRecord::getSourceId,PersonTrainEquRecord::getEquivalentBaseCode,PersonTrainEquRecord::getEquivalentBaseName
                ,PersonTrainEquRecord::getEquivalentDate,PersonTrainEquRecord::getId,PersonTrainEquRecord::getFormTrainNumber);
        List<PersonTrainEquRecord> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<PersonTrainEquRecordVO> postEquVOS = BeanCopyUtils.convertListTo(list,PersonTrainEquRecordVO::new);
        List<String> dataIdList= postEquVOS.stream().map(PersonTrainEquRecordVO::getSourceId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String,List<FileVO>>  map = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataIdList)){
            List<FileVO> filesByDataIds = fileApiService.listMaxFileByDataIds(dataIdList);
            if(!CollectionUtils.isEmpty(filesByDataIds)){
                map = filesByDataIds.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }
        }
        Map<String, List<FileVO>> finalMap = map;
        postEquVOS.forEach(item->{
            item.setFileVOList(finalMap.getOrDefault(item.getSourceId(),new ArrayList<>()));
        });
        return  postEquVOS;
    }

    @Override
    public List<PersonTrainEquRecord> listByUserCodeList(List<String> userCodeList) {
        LambdaQueryWrapperX<PersonTrainEquRecord> condition = new LambdaQueryWrapperX<>( PersonTrainEquRecord. class);
        condition.in(PersonTrainEquRecord::getUserCode,userCodeList);
        return this.list(condition);
    }

    public static class PersonTrainEquRecordExcelListener extends AnalysisEventListener<PersonTrainEquRecordDTO> {

        private final List<PersonTrainEquRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonTrainEquRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonTrainEquRecordDTO> getData() {
            return data;
        }
    }


}
