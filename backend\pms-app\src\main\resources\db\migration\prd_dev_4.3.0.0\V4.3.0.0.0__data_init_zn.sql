INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`,
                              `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                              `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`,
                              `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`)
VALUES ('9hi11828245473364566016', 'quotationNo', NULL, '报价单编号', 'zmz823968efc482c4f658f9635a04036bd71',
        'SysCodeRules', '报价单编码生成规则', 'user00000000000000000100000000000000',
        'user00000000000000000100000000000000', '2024-08-27 09:37:43', 'user00000000000000000100000000000000',
        '2024-08-27 09:53:05', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f',
        NULL, 1, '1696832bc9e9426cb74e85006fb8e494', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'0');


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828247737718300672', '年', '1', '9hi11828245473364566016', '', 'DATE_YYYY', '', '', '0', 2, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-27 09:46:43', 'user00000000000000000100000000000000', '2024-08-27 09:48:31', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828247790927241216', '月', '1', '9hi11828245473364566016', '', 'DATE_M', '', '', '0', 3, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-27 09:46:55', 'user00000000000000000100000000000000', '2024-08-27 09:48:38', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828247843603505152', '日', '1', '9hi11828245473364566016', '', 'DATE_D', '', '', '0', 4, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-27 09:47:08', 'user00000000000000000100000000000000', '2024-08-27 09:48:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828248157488439296', 'BJ', '0', '9hi11828245473364566016', '', 'charCode', '', 'BJ', '0', 1, '', '',
        'SysCodeSegment', '固定值', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-27 09:48:23', 'user00000000000000000100000000000000', '2024-08-27 09:49:22', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828249075869048832', '占位符', '0', '9hi11828245473364566016', '', 'charCode', '', '$', '0', 5, '', '',
        'SysCodeSegment', '占位符，最后用承担部门编码替换', 'user00000000000000000100000000000000',
        'user00000000000000000100000000000000', '2024-08-27 09:52:02', 'user00000000000000000100000000000000',
        '2024-08-27 09:52:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL,
        1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1828249235869163520', '流水器', '0', '9hi11828245473364566016', 'f9g6017b6f50410849188667eacdf1a928d8',
        'piPer', '', '', '3', 6, '', '0', 'SysCodeSegment', '', 'user00000000000000000100000000000000',
        'user00000000000000000100000000000000', '2024-08-27 09:52:40', 'user00000000000000000100000000000000',
        '2024-08-27 09:52:40', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL,
        1);


INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`,
                                         `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                         `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`)
VALUES ('e96m1828258759137058816', 'number', '9hi11828245473364566016', 'QuotationManagement', 'SysCodeMappingRelation',
        '报检单编号生成', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-27 10:30:30', 'user00000000000000000100000000000000', '2024-08-27 10:30:30', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');



ALTER TABLE `pmsx_quotation_management`
    MODIFY COLUMN `re_quotation_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重新报价，原报价单ID,pmsx_quotation_management 的id' AFTER `quotation_id`;


ALTER TABLE `pmsx_quotation_management`
    MODIFY COLUMN `quotation_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报价单编码' AFTER `requirement_number`;