package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * SchemeToMaterial Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@TableName(value = "pmsx_scheme_to_material")
@ApiModel(value = "SchemeToMaterialEntity对象", description = "计划相关的物资")
@Data

public class SchemeToMaterial extends  ObjectEntity  implements Serializable{

    /**
     * 项目计划ID
     */
    @ApiModelProperty(value = "项目计划ID")
    @TableField(value = "plan_scheme_id")
    private String planSchemeId;

    /**
     * 大修伦次
     */
    @ApiModelProperty(value = "大修伦次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 物资编码（固定资产编码）
     */
    @ApiModelProperty(value = "物资编码（固定资产编码）")
    @TableField(value = "material_number")
    private String materialNumber;

    /**
     * 物资ID：物资管理的Id
     */
    @ApiModelProperty(value = "物资ID：物资管理的Id")
    @TableField(value = "material_id")
    private String materialId;

    /**
     * 物资名称
     */
    @ApiModelProperty(value = "物资名称")
    @TableField(value = "material_name")
    private String materialName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 物资类型
     */
    @ApiModelProperty(value = "资产类型")
    @TableField(value = "asset_type")
    private String assetType;

    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量")
    @TableField(value = "demand_num")
    private Integer demandNum;

    @ApiModelProperty(value = "是否可用")
    @TableField(value = "avaliable")
    private Boolean avaliable;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    @TableField(value = "tool_status")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    @TableField(value = "maintenance_cycle")
    private Integer maintenanceCycle;


}
