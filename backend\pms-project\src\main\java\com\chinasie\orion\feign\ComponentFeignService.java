package com.chinasie.orion.feign;

import com.chinasie.orion.domain.dto.ComponentDocumentParamDto;
import com.chinasie.orion.domain.dto.ComponentQueryDTO;
import com.chinasie.orion.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.domain.dto.pdm.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.dto.ParamInsCopyDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/03/10 14:59
 * @description:
 */
@FeignClient(name = "pdm", path = "",configuration = FeignConfig.class)
@Lazy
public interface ComponentFeignService {

    /**
     *  搜索获取零组件
     * @param componentQueryDTO
     * @return
     * @throws Exception
     */
    @PostMapping("/component/search")
    ResponseDTO<List<ComponentVO>> searchComponent(@RequestBody ComponentQueryDTO componentQueryDTO) throws Exception;

    /**
     *  获取零组件详情
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/component/detail/{id}")
    ResponseDTO<ComponentVO> componentDetail(@PathVariable("id") String id) throws Exception;

    /**
     * 获取产品列表
     * @return
     * @throws Exception
     */
    @GetMapping("/product/list")
    ResponseDTO<List<ProductVO>> productList() throws Exception;


    /**
     *  搜索获取产品列表
     * @param componentQueryDTO
     * @return
     * @throws Exception
     */
    @PostMapping("/product/search")
    ResponseDTO<List<ProductVO>> searchProduct(@RequestBody ComponentQueryDTO componentQueryDTO) throws Exception;


    /**
     *  获取零组件详情
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/product/detail/{id}")
    ResponseDTO<ProductVO> productVODetail(@PathVariable("id") String id) throws Exception;


    /**
     *  通过id列表获取 文档列表
     * @param componentDocumentParamDto
     * @return
     * @throws Exception
     */
    @PostMapping("/pdm-item-to-document/relations")
    ResponseDTO<List<PlanAndComponentDocumentVo>> documentRelations(@RequestBody ComponentDocumentParamDto componentDocumentParamDto) throws Exception;

    /**
     * 获取文档下的 列表 文件
     * @param documentId
     * @return
     * @throws Exception
     */
    @GetMapping("/document/getFilesByDataId/{documentId}")
    ResponseDTO<List<PlanAndComponentDocumentVo>> getFilesByDocumentId(@PathVariable("documentId") String documentId) throws Exception;



    @PostMapping("/parameterPool/lists")
    ResponseDTO<List<ParameterPoolVO>> parameterPoolLists(@RequestBody SearchDTO searchDTO) throws Exception;


    @ApiOperation(value = "获取参数模板列表")
    @RequestMapping(value = "/parameterPoolModule/search", method = RequestMethod.POST)
    ResponseDTO<List<ParameterPoolModuleVO>> parameterPoolModuleSearch(@RequestBody SearchDTO searchDTO) throws Exception;



    @ApiOperation(value = "获取参数实例列表")
    @RequestMapping(value = "/parameterPoolIns/search", method = RequestMethod.POST)
    public ResponseDTO<List<ParameterPoolInsVO>> parameterPoolInsSearch(@RequestBody SearchDTO searchDTO) throws Exception;


    @RequestMapping(value = "/parameterPoolIns", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    ResponseDTO<ParameterPoolInsVO> create(@RequestBody ParameterPoolInsDTO parameterPoolInsDTO) throws Exception;

    @RequestMapping(value = "/parameterPoolIns", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    ResponseDTO<Boolean> edit(@RequestBody ParameterPoolInsDTO parameterPoolInsDTO) throws Exception;


    @RequestMapping(value = "/product-pbs/simple/{id}", method = {RequestMethod.GET})
    ResponseDTO<ProductPBSSimpleVO> detailPBSSimple(@PathVariable("id") String id) throws Exception;

    @ApiOperation(value = "参数实列复制")
    @RequestMapping(value = "/parameterPoolIns/copy/ids", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    ResponseDTO<List<ParameterPoolInsVO>> copyByIdList(@RequestBody ParamInsCopyDTO paramInsCopyDTO) throws Exception;

    @ApiOperation("物料分页列表")
    @RequestMapping(value = "/productEstimateMaterial/getAllTreePage", method = {RequestMethod.POST})
    ResponseDTO<Page<ProductEstimateMaterialVO>> getAllTreePage(@RequestBody Page<ProductEstimateMaterialDTO> pageRequest) throws Exception ;

    @ApiOperation("分页列表")
    @RequestMapping(value = "/productEstimateMaterial/pages", method = {RequestMethod.POST})
    ResponseDTO<Page<ProductEstimateMaterialVO>> pages( @RequestBody Page<ProductEstimateMaterialDTO> pageRequest) throws Exception;

    @ApiOperation("根据id获取产品列表")
    @RequestMapping(value = "/productEstimateMaterial/getList", method = {RequestMethod.POST})
     ResponseDTO<List<ProductEstimateMaterialVO>> getList( @RequestBody List<String> ids) throws Exception;

    @ApiOperation("根据编码获取产品")
    @RequestMapping(value = "/productEstimateMaterial/getByNumber", method = {RequestMethod.GET})
    ResponseDTO<ProductEstimateMaterialVO> getByNumber(@RequestParam("number") String number) throws Exception;


}
