package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ContractCenter Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:31:28
 */
@TableName(value = "pmsx_contract_center")
@ApiModel(value = "ContractCenterEntity对象", description = "用人中心")
@Data

public class ContractCenter extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    @TableField(value = "center_code")
    private String centerCode;

    /**
     * 用人中心名称
     */
    @ApiModelProperty(value = "用人中心名称")
    @TableField(value = "center_name")
    private String centerName;

}
