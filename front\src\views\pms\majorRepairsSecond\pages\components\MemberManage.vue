<script setup lang="ts">
import { BasicButton, OrionTable, randomString } from 'lyra-component-vue3';
import { Dropdown, Menu, MenuItem } from 'ant-design-vue';
import {
  computed, CSSProperties, h, inject, onActivated, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { message } from 'ant-design-vue/lib/components';
import dayjs from 'dayjs';
import { openInAndOutSite, openPersonnelDetailsDrawer } from '/@/views/pms/overhaulManagement/utils';
import { get as _get, isArray, map as _map } from 'lodash-es';
import { parseBooleanToRender } from '/@/views/pms/utils/utils';
import { usePersonInOut } from '/@/views/pms/majorRepairsSecond/hooks/usePersonInOut';
import { usePersonExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';

const detailsData: Record<string, any> = inject('detailsData');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};

const statusMap = [
  '待入场',
  '已入场',
  '已离场',
];
const tableRef: Ref = ref();
const parseDate = (obj) => {
  if (isArray(obj)) {
    return _map(obj, (item) => (item
      ? dayjs(item).format('YYYY-MM-DD')
      : '')).join(' ~ ');
  }
  return text ?? '-';
};

const keyword: Ref<string> = ref('');
const tableOptions = {
  showToolButton: false,
  isSpacing: false,
  smallSearchField: [],
  api: (params: object) => new Api('/pms/schemeToPerson/person/page').fetch({
    ...params,
    query: {
      keyword: unref(keyword),
      repairRound: detailsData?.repairRound,
    },
    power: {
      pageCode: 'PMSMajorRepairsSecondDetail',
      containerCode: 'PMS_DXXQEC_container_03',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 240,
    },
    {
      title: '项目计划',
      dataIndex: 'planSchemeName',
      width: 240,
    },
    {
      title: '员工号',
      dataIndex: 'userCode',
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      slots: { customRender: 'userName' },
    },
    {
      title: '性别',
      dataIndex: 'sex',
    },
    {
      title: '人员性质',
      dataIndex: 'personnelNature',
    },
    {
      title: '是否常驻',
      dataIndex: 'isBasePermanent',
    },
    {
      title: '参修类型',
      dataIndex: 'isHaveProject',
      customRender({ text }) {
        return text ? '有项目' : text === false ? '无项目' : '';
      },
    },
    {
      title: '部门',
      dataIndex: 'deptName',
    },
    {
      title: '研究所',
      dataIndex: 'instituteName',
    },
    {
      title: '计划入场离场时间',
      dataIndex: ['personMangeVO', 'inAndOutDateList'],
      width: 260,
      customRender({ text, record }) {
        if (_get(record, 'personMangeVO.status') === 1) {
          return parseDate(text);
        }
        return h(MouseCellEdit, {
          component: 'RangePicker',
          isKeepEdit: true,
          editFlag: record.personMangeVO?.status !== 1,
          record,
          text: (text || []).map((item) => (item ? dayjs(item).format('YYYY-MM-DD') : '')).join(' 至 '),
          componentValue: text || [],
          componentProps: {
            allowClear: false,
            valueFormat: 'YYYY-MM-DD',
          },
          onSubmit(dates: [string, string], resolve: (value: any) => void) {
            new Api('/pms/person-mange/edit/date').fetch({
              id: record.personMangeVO?.id,
              inAndOutDateList: dates[0] || [],
            }, '', 'PUT').then(() => {
              message.success('操作成功');
              updateTable();
            }).finally(() => {
              resolve(true);
            });
          },
        });
      },
    },
    {
      title: '实际入场日期',
      dataIndex: ['personMangeVO', 'actInDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否新人',
      dataIndex: ['personMangeVO', 'newcomer'],
      customRender({ text, record }) {
        if (_get(record, 'isHaveProject') === false) {
          return '';
        }
        if (_get(record, 'personMangeVO.status') === 1) {
          return parseBooleanToRender(text);
        }
        return h(MouseCellEdit, {
          component: 'Select',
          isKeepEdit: true,
          record,
          text: text ? '是' : text === false ? '否' : '',
          componentValue: text,
          componentProps: {
            options: [
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ],
          },
          onSubmit(option: { label: string, value: boolean }, resolve: (value: any) => void) {
            new Api('/pms/person-mange/edit/newcomer').fetch({
              id: record?.personMangeVO?.id,
              isNewcomer: option.value,
            }, '', 'PUT').then(() => {
              resolve(true);
              message.success('操作成功');
              updateTable();
            });
          },
        });
      },
    },
    {
      title: '接口人',
      dataIndex: ['personMangeVO', 'contactUserName'],
      customRender({ text, record }) {
        if (_get(record, 'isHaveProject') === false) {
          return '';
        }
        if (_get(record, 'personMangeVO.status') === 1) {
          return text;
        }
        return h(MouseCellEdit, {
          component: 'Input',
          isKeepEdit: true,
          editFlag: computed(() => _get(record, 'personMangeVO.newcomer', false)).value,
          record,
          text,
          componentValue: text,
          onSubmit(value: string, resolve: (value: any) => void) {
            new Api('/pms/person-mange/edit/contact').fetch({
              id: record?.personMangeVO?.id,
              contactUserName: value,
            }, '', 'PUT').then(() => {
              resolve(true);
              message.success('操作成功');
              updateTable();
            });
          },
        });
      },
    },
    {
      title: '是否入场',
      dataIndex: ['personMangeVO', 'status'],
      customRender({ text, record }) {
        return _get(statusMap, text);
      },
    },
    {
      title: '进场时间倒计时（天）',
      dataIndex: ['personMangeVO', 'inDays'],
      width: 200,
    },
    {
      title: '参与作业数',
      dataIndex: 'jobNum',
      customRender({ record }) {
        if (_get(record, 'isHaveProject') === false) {
          return '';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '进场',
      onClick(record) {
        const dateList = _get(record, 'personMangeVO.inAndOutDateList');
        if (!isArray(dateList) || dateList.length !== 2 || !(dateList || []).every(Boolean)) {
          message.warn('请补充人员的计划入场离场时间后再试');
          return;
        }
        openInAndOutSite({
          operationType: 'in',
          id: record.personMangeVO?.id,
          userCode: record.userCode,
        }, () => {
          updateTable();
        });
      },
      isShow: (record) => [[0, 2].includes(_get(record, 'personMangeVO.status'))].every(Boolean),
    },
    {
      text: '离场',
      onClick(record) {
        openInAndOutSite({
          operationType: 'out',
          id: record.personMangeVO?.id,
          userCode: record.userCode,
        }, () => {
          updateTable();
        });
      },
      isShow: (record) => [[1].includes(_get(record, 'personMangeVO.status'))].every(Boolean),
    },
    {
      text: '移除',
      modalTitle: '移除提示！',
      modalContent: '是否移除该人员？',
      modal: (record) => removeApi({
        repairRound: detailsData?.repairRound,
        userCodeList: [record?.userCode],
      }, updateTable),
      isShow: (record) => _get(record, 'personMangeVO.status') !== 1 && !_get(record, 'isHaveProject'),
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

onActivated(() => {
  updateTable();
});

const { addApi, removeApi } = usePersonInOut();
const {
  importApi, exportApi, OrderImportRender, importTypes,
} = usePersonExcel();
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      rowKey="userId"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="addApi({
            baseCode: detailsData?.baseCode,
            repairRound: detailsData?.repairRound,
            isHaveProject:false
          },()=>{
            updateTable();
          })"
        >
          无项目人员添加
        </BasicButton>
        <Dropdown>
          <BasicButton
            icon="sie-icon-daoru"
          >
            导入
          </BasicButton>
          <template #overlay>
            <Menu @click="importApi">
              <MenuItem
                v-for="item in importTypes"
                :key="item.key"
              >
                {{ item.name }}
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>

        <BasicButton
          icon="sie-icon-daochu"
          @click="exportApi({
            repairRound: detailsData?.repairRound,
          })"
        >
          导出
        </BasicButton>
      </template>
      <template
        #fullName="{text,record}:{text:string,record:{
        personLedgerId:string,
        personMangeVO:Record<string,any>
        }}"
      >
        <div>
          <div
            v-if="record.personLedgerId"
            class="action-btn flex-te"
            :title="text"
            @click="openPersonnelDetailsDrawer(record)"
          >
            {{ text }}
          </div>
          <div
            v-else
            class="flex-te"
            :title="text"
          >
            {{ text }}
          </div>
          <div class="flex">
            <new-tag v-if="record.personMangeVO.newcomer" />
            <kong-tag v-if="record.personMangeVO.designCtrlZoneOp" />
            <ban-tag v-if="record.personMangeVO.chemicalToxinUseJob" />
          </div>
        </div>
      </template>
    </OrionTable>

    <OrderImportRender
      :repairRound="detailsData?.repairRound"
      :cb="updateTable"
    />
  </div>
</template>

<style scoped lang="less">
.tag-icon {
  position: relative;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 16px;
  color: ~`getPrefixVar('warning-color')`;
  border: 1px solid ~`getPrefixVar('warning-color')`;
  border-radius: 50%;
  font-size: 12px;

  & + & {
    margin-left: 5px;
  }

  &.ban::after {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    content: '';
    width: 100%;
    height: 1px;
    background-color: ~`getPrefixVar('warning-color')`;
    opacity: 0.5;
  }
}
</style>
