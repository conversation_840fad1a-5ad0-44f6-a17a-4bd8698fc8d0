package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.PurchaseStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.PurchaseStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.PurchaseStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.PurchaseStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.PurchaseStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * PurchaseStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 14:22:48
 */
@Service
public class PurchaseStatusStatisticsServiceImpl extends OrionBaseServiceImpl<PurchaseStatusStatisticsMapper, PurchaseStatusStatistics> implements PurchaseStatusStatisticsService {

    @Autowired
    private PurchaseStatusStatisticsMapper purchaseStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public PurchaseStatusStatisticsVO detail(String id) throws Exception {
        PurchaseStatusStatistics purchaseStatusStatistics =purchaseStatusStatisticsMapper.selectById(id);
        PurchaseStatusStatisticsVO result = BeanCopyUtils.convertTo(purchaseStatusStatistics,PurchaseStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param purchaseStatusStatisticsDTO
     */
    @Override
    public  PurchaseStatusStatisticsVO create(PurchaseStatusStatisticsDTO purchaseStatusStatisticsDTO) throws Exception {
        PurchaseStatusStatistics purchaseStatusStatistics =BeanCopyUtils.convertTo(purchaseStatusStatisticsDTO,PurchaseStatusStatistics::new);
        int insert = purchaseStatusStatisticsMapper.insert(purchaseStatusStatistics);
        PurchaseStatusStatisticsVO rsp = BeanCopyUtils.convertTo(purchaseStatusStatistics,PurchaseStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param purchaseStatusStatisticsDTO
     */
    @Override
    public Boolean edit(PurchaseStatusStatisticsDTO purchaseStatusStatisticsDTO) throws Exception {
        PurchaseStatusStatistics purchaseStatusStatistics =BeanCopyUtils.convertTo(purchaseStatusStatisticsDTO,PurchaseStatusStatistics::new);
        int update =  purchaseStatusStatisticsMapper.updateById(purchaseStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = purchaseStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<PurchaseStatusStatisticsVO> pages(Page<PurchaseStatusStatisticsDTO> pageRequest) throws Exception {
        Page<PurchaseStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchaseStatusStatistics::new));

        PageResult<PurchaseStatusStatistics> page = purchaseStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<PurchaseStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchaseStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchaseStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
