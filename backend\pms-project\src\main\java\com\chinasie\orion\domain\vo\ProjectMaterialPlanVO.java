package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectMaterialPlan VO对象
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
@ApiModel(value = "ProjectMaterialPlanVO对象", description = "项目物资计划")
@Data
public class ProjectMaterialPlanVO extends  ObjectVO   implements Serializable{

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String number;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private String materialId;


    /**
     * 计划使用时间
     */
    @ApiModelProperty(value = "计划使用时间")
    private Date planUseTime;


    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private Integer planNum;


    /**
     * 基本单位
     */
    @ApiModelProperty(value = "基本单位")
    private String baseUnit;

    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态")
    private String statusName;

    /**
     * 研发单台物料量
     */
    @ApiModelProperty(value = "研发单台物料量")
    private Integer materialNum = 0;

    @ApiModelProperty(value = "台数")
    private Integer units = 0;

    /**
     * 研发总物料量
     */
    @ApiModelProperty(value = "研发总物料量")
    private Integer  materialTotalNum = 0;

    /**
     * 备料数量
     */
    @ApiModelProperty(value = "备料量")
    private Integer preparationNum;

    /**
     * 短缺量
     */
    @ApiModelProperty(value = "短缺量")
    private Integer shortageNum = 0;

    /**
     * 未清采购申请量
     */
    @ApiModelProperty(value = "未清采购申请量")
    private String applyNum = "0";

    /**
     * 未清采购订单量
     */
    @ApiModelProperty(value = "未清采购订单量")
    private String orderNum = "0";

    /**
     * 质检中
     */
    @ApiModelProperty(value = "质检中")
    private String qualityNum = "0";

    /**
     * 库存量
     */
    @ApiModelProperty(value = "库存量")
    private String stockNum = "0";

    /**
     * 领用量
     */
    @ApiModelProperty(value = "领用量")
    private String useNum = "0";


    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    private BigDecimal procurementCycle;
}

