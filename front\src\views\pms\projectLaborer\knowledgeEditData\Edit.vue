<template>
  <a-modal
    v-model:visible="father.visible"
    width="100%"
    wrap-class-name="full-modal"
    :footer="null"
    :closable="false"
  >
    <template #title>
      <div class="fl pt10">
        <b>{{ father.title }}</b>
      </div>
      <div class="fr">
        <a-space :value="20">
          <a-button @click="father.visible = false">
            关闭
          </a-button>
          <a-button
            v-if="isPower('ZSCJ_container_button_10', powerData)"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            发布
          </a-button>
          <a-button
            v-if="isPower('ZSCJ_container_button_11', powerData)"
            type="primary"
            @click="handlePreview"
          >
            预览
          </a-button>
        </a-space>
      </div>
    </template>
    <Layout :options="{ body: { scroll: true }, right: { width: 400 } }">
      <template #right>
        <a-form
          ref="formRef"
          :model="father.form"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 17 }"
        >
          <div class="content-title-icon">
            <h3>基本信息</h3>
          </div>
          <div style="padding-left: 140px">
            <a-upload
              name="avatar"
              list-type="picture-card"
              class="avatar-uploader"
              :show-upload-list="false"
              :before-upload="beforeUpload"
              accept=".jpg, .jpeg, .png"
            >
              <img
                v-if="father.form.thumb"
                :src="father.form.thumb"
                style="width: 100%; height: 100%"
                alt="avatar"
              >
              <div v-else>
                <div class="ant-upload-text">
                  缩略图
                </div>
              </div>
            </a-upload>
          </div>
          <div style="text-align: center">
            <div style="font-size: 10px">
              提示：可上传5M以内的图片或视频
            </div>
            <div style="color: red; font-size: 12px">
              展示封面，优质的封面有利于推荐
            </div>
          </div>
          <a-form-item label="创建者">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="当前版本">
            {{ father.form.revision }}
          </a-form-item>
          <a-form-item
            label="知识标题"
            name="name"
          >
            <a-input
              v-model:value="father.form.name"
              placeholder="请输入"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="知识作者">
            <a-input
              v-model:value="father.form.author"
              placeholder="请输入"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="关联知识">
            <a-select
              v-model:value="father.form.knowledgeIdList"
              placeholder="请选择关联知识"
              mode="multiple"
              :options="knowledgeIdList"
              :filter-option="filterOption"
              allow-clear
            />
          </a-form-item>
          <a-form-item
            label="知识密级"
            name="secretLevel"
          >
            <a-select
              v-model:value="father.form.secretLevel"
              placeholder="请选择知识密级"
              :options="secretLevelList"
              allow-clear
              @change="father.form.securityLimit = null"
            />
          </a-form-item>
          <a-form-item
            v-if="isSecurityLimit"
            label="密级期限"
            required
          >
            <a-select
              v-model:value="father.form.securityLimit"
              placeholder="请选择密级期限"
              :options="deadlinelList"
              allow-clear
            />
          </a-form-item>
          <div class="content-title-icon">
            <h3>属性选择</h3>
          </div>
          <a-form-item label="选择属性">
            <a-button
              v-if="isPower('ZSCJ_container_button_7', powerData)"
              @click="clickSelectProperties"
            >
              选择属性
            </a-button>
          </a-form-item>
          <a-form-item
            v-for="(s, i) in father.form.attributeParamDtoList"
            :key="s.id"
            :label="stringIntercept(s.name, 4)"
            :required="!!s.required"
          >
            <a-select
              v-if="s.selectType === 0"
              v-model:value="s.value"
              class="attr-width"
              allow-clear
              placeholder="请选择"
            >
              <a-select-option
                v-for="(v, j) in s.attributeTypeList"
                :key="j"
                :value="v.name"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
            <a-select
              v-if="s.selectType === 1"
              v-model:value="s.valueList"
              mode="multiple"
              class="attr-width"
              allow-clear
              placeholder="请选择"
            >
              <a-select-option
                v-for="(v, j) in s.attributeTypeList"
                :key="j"
                :value="v.name"
              >
                {{ v.name }}
              </a-select-option>
            </a-select>
            <a-input
              v-if="s.type === 0 && s.selectType === 2"
              v-model:value="s.value"
              class="attr-width"
              placeholder="请输入"
              allow-clear
            />
            <a-input-number
              v-if="s.type === 1 && s.selectType === 2"
              v-model:value="s.value"
              class="attr-width"
              placeholder="请输入"
              allow-clear
            />
            <a-date-picker
              v-if="s.selectType === 3"
              v-model:value="s.value"
              class="attr-width"
              allow-clear
            />
            <a-range-picker
              v-if="s.selectType === 4"
              v-model:value="s.valueList"
              class="attr-width"
              allow-clear
            />
            <a-button @click="deleteAttrSelect(i)">
              删除
            </a-button>
          </a-form-item>
          <div class="content-title-icon">
            <h3>标签选择</h3>
          </div>
          <a-form-item label="知识标签">
            <SelectLabel
              v-model:values="father.form.labelDtoList"
              :options="selectLabelList"
            />
            <div class="mt10">
              <a-tag
                v-for="(s, i) in father.form.labelDtoList"
                :key="i"
                style="margin-bottom: 10px; padding: 5px 10px"
              >
                {{ s }}
                <CloseOutlined
                  class="pointer"
                  @click="father.form.labelDtoList.splice(i, 1)"
                />
              </a-tag>
            </div>
            <a-button
              v-if="isPower('ZSCJ_container_button_8', powerData)"
              @click="handleAdd"
            >
              +添加系统标签
            </a-button>
          </a-form-item>
        </a-form>
      </template>
      <div class="edit-content">
        <div class="breadcrumb-box">
          <a-space :size="20">
            <span>你选择的分类是：{{ father.form.classifyName }}</span>
            <a-button
              v-if="isPower('ZSCJ_container_button_2', powerData)"
              type="link"
              @click="selectClass('选择分类', 'class')"
            >
              修改分类
            </a-button>
            <span>{{ father.form.modelName }}</span>
            <a-button
              v-if="isPower('ZSCJ_container_button_3', powerData)"
              type="link"
              @click="replaceTemplate"
            >
              更换模板
            </a-button>
          </a-space>
          <div class="float-right">
            <a-select
              v-model:value="valueModel"
              style="width: 200px"
              placeholder="选择应用模板"
              :options="modelList"
              allow-clear
            />
            <a-button
              type="link"
              @click="handleEditModel(modelList, valueModel)"
            >
              切换
            </a-button>
          </div>
        </div>
        <a-upload
          accept=".doc,.docx"
          :show-upload-list="false"
          :multiple="false"
          :before-upload="zwbeforeUpload"
        >
          <a-button block>
            <Icon icon="fa-upload" /> 上传正文附件并解析
          </a-button>
        </a-upload>
        <Tinymce
          v-model="father.form.content"
          :show-image-upload="false"
          :height="500"
        />
        <BasicUpload
          v-if="isPower('ZSCJ_container_button_4', powerData)"
          class="allUpload"
          :max-number="100"
          :multiple="true"
          button-text="上传其他附件"
          button-type="default"
          :accept="'.jpg,.zip,.pdf,.xls,.xlsx,.doc,.docx,.pdf,.video,.text,.png,.dwg'"
          :is-classification="true"
          @saveChange="handleSaveFile"
        />
        <div class="file-box">
          <label class="accessory">上传附件：</label>
          <div class="content">
            <a-row
              v-for="v in fileDataSource"
              :key="v.id"
            >
              <a-col :span="20">
                <div class="m5">
                  <a-space :size="10">
                    <Icon
                      :icon="getFileObj(v.filePostfix).icon"
                      :style="{ color: getFileObj(v.filePostfix).color }"
                    />
                    <span
                      class="fileItem-text"
                      @click="previewView(v.filePath, v.filePostfix)"
                    >
                      {{ v.name + v.filePostfix }}
                    </span>
                    <span
                      class="fa fa-times pointer"
                      style="color: #9e9e9e"
                      @click.stop="deleteHandle(v.id)"
                    />
                  </a-space>
                </div>
              </a-col>
              <a-col
                :span="4"
                class="text-right"
              >
                <span class="mr-4">
                  {{ v.fileSize ? (v.fileSize / 1024 / 1024).toFixed(2) + ' MB' : '' }}
                </span>
                <DownloadOutlined
                  class="text-link mr20"
                  @click="downLoad(v.id)"
                />
              </a-col>
            </a-row>
          </div>
        </div>
        <div>
          <div
            class="fl"
            style="width: 100px"
          />
        </div>
        <a-tabs>
          <a-tab-pane
            v-if="isPower('ZSCJ_container_button_5', powerData)"
            key="1"
            tab="权限设置"
            force-render
          >
            <a-table
              row-key="type"
              :show-index-column="false"
              :max-height="300"
              :bordered="true"
              :pagination="false"
              :data-source="father.form.permissionJsonDtoList"
              :columns="columnsSetting"
            >
              <template #type="{ text }">
                <span v-if="text === 1">可阅读</span>
                <span v-if="text === 2">可编辑</span>
                <span v-if="text === 3">可下载</span>
              </template>
              <template #projectList="{ record }">
                <span
                  v-for="(s, i) in record.projectList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #organizationList="{ record }">
                <span
                  v-for="(s, i) in record.organizationList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #personList="{ record }">
                <span
                  v-for="(s, i) in record.personList"
                  :key="i"
                >{{ s.name }}；</span>
              </template>
              <template #isPublic="{ record }">
                <a-checkbox v-model:checked="record.isPublic">
                  公开
                </a-checkbox>
                <a-button @click="handleChooseObject(record)">
                  选择对象
                </a-button>
              </template>
            </a-table>
            <br>
          </a-tab-pane>
          <a-tab-pane
            v-if="isPower('ZSCJ_container_button_6', powerData)"
            key="2"
            tab="流程设置"
            force-render
          >
            <a-button
              class="mb10"
              @click="handleFlow"
            >
              选择流程
            </a-button>
            <a-table
              row-key="id"
              :show-index-column="false"
              :columns="columnFlow"
              :data-source="dataFlow"
              :max-height="300"
              :bordered="true"
              :pagination="false"
            >
              <template #businessKey="{ record }">
                <a @click="onDetail(record)">{{ record.businessKey }}</a>
              </template>
              <template #procInstName="{ record }">
                <a @click="onDetail(record)">{{ record.procInstName }}</a>
              </template>
              <template #action="{ record }">
                <a-button
                  v-if="record.statusCode === 'NOT_STARTED'"
                  type="link"
                  @click="onStart(record)"
                >
                  启动
                </a-button>
                <a-button
                  v-if="record.statusCode === 'RUNNING'"
                  type="link"
                  @click="onDrawith(record)"
                >
                  撤回
                </a-button>
                <a-button
                  v-if="record.statusCode !== 'RUNNING'"
                  type="link"
                  @click="onDelete(record)"
                >
                  删除
                </a-button>
              </template>
            </a-table>
            <br>
          </a-tab-pane>
        </a-tabs>
      </div>
    </Layout>
  </a-modal>
  <SelectCluster
    v-if="selectCluster.visible"
    :data="selectCluster"
    @submit="submitSelectCluster"
  />
  <SelectObject
    v-if="selectObject.visible"
    :data="selectObject"
    @submit="submitSelectObject"
  />
  <SelectProperties
    v-if="selectProperties.visible"
    :data="selectProperties"
    @submit="submitSelectProperties"
  />
  <Preview
    v-if="preview.visible"
    :data="preview"
  />
  <Template
    v-if="template.visible"
    :data="template"
    @save="saveTemplate"
  />
  <FlowModal
    :id="father.form.id"
    :width="1000"
    data-type="KnowledgeInfo"
    @register="registerFlow"
    @update="onUpdateFlow"
  />
  <SelectLabels
    v-if="selectLabels.visible"
    :data="selectLabels"
    @submit="submitSelectLabels"
  />
</template>

<script lang="ts">
import {
  handleDetailController,
  preview as previewView,
  stringIntercept,
  roleController,
  addValueLabel,
} from '/@/views/pms/projectLaborer/utils';
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  computed,
  onMounted,
  getCurrentInstance,
} from 'vue';
import {
  Modal,
  Button,
  Space,
  Upload,
  Tabs,
  Form,
  Input,
  Select,
  Checkbox,
  Tag as ATag,
  message,
  DatePicker,
  InputNumber,
  Table as ATable,
  Row,
  Col,
} from 'ant-design-vue';
import { CloseOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import {
  Layout, useModal, Icon, BasicUpload, useProjectPower, isPower,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
import { Tinymce } from '/@/views/pms/projectLaborer/components/Tinymce';
// import { BasicUpload } from '/@/components/BasicUpload';
import { edit, getFileObj } from '/@/views/pms/projectLaborer/knowledgeEditData/data';
import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api';
import SelectCluster from './SelectCluster.vue';
// import { Icon } from '/@/components/IconSelect';
// import SelectObject from '/@/views/kms/infrastructure/knowledge-2/modal/SelectObject.vue';
import SelectObject from './SelectObject.vue';

import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import FlowModal from './Flow.vue';
// import { useModal } from '/@/components/Modal';
import { useRouter } from 'vue-router';
import Preview from './Preview.vue';
import SelectLabel from './SelectLabel.vue';
import Template from './Template.vue';
import SelectProperties from './SelectProperties.vue';
import SelectLabels from './SelectLabels.vue';

// import { useProjectPower, isPower } from '/@/hooks/power/useBusinessPrivilege';

export default defineComponent({
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    DownloadOutlined,
    SelectLabel,
    CloseOutlined,
    SelectLabels,
    SelectProperties,
    FlowModal,
    Preview,
    Template,
    ATable,
    AInputNumber: InputNumber,
    ADatePicker: DatePicker,
    ARangePicker: DatePicker.RangePicker,
    AModal: Modal,
    AButton: Button,
    ASpace: Space,
    AUpload: Upload,
    ATabs: Tabs,
    ATabPane: Tabs.TabPane,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
    ASelect: Select,
    ASelectOption: Select.Option,
    ACheckbox: Checkbox,
    ATag,
    Layout,
    Tinymce,
    BasicUpload,
    SelectCluster,
    SelectObject,
    Icon,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['edit'],
  setup(props, { emit }) {
    const router = useRouter();
    const [registerFlow, { openModal: openModalFlow }] = useModal();
    const formRef = ref();
    const userStore = useUserStore();
    const state = reactive({
      father: props.data,
      powerData: [],
      selectLabelList: [],
      selectLabels: {},
      loading: false,
      valueModel: undefined,
      selectProperties: {},
      template: {},
      preview: {},
      attrList: [],
      modelList: [],
      selectObject: {},
      fileDataSource: [],
      isSecurityLimit: computed(() => {
        const secretLevel = state.father.form.secretLevel;
        if (secretLevel && state.secretLevelList.length) {
          return state.secretLevelList.find((s) => s.value === secretLevel).isSecurityLimit;
        }
        return false;
      }),
      selectCluster: {},
      columnsSetting: edit.columns1,
      columnFlow: edit.columns,
      dataFlow: [],
      secretLevelList: [],
      knowledgeIdList: [],
      kmsLabelList: [],
      deadlinelList: [],
      rules: {
        name: [
          {
            required: true,
            message: '请输入知识标题',
            trigger: 'blur',
          },
        ],
        secretLevel: [
          {
            required: true,
            message: '请选择知识密级',
            trigger: 'change',
          },
        ],
      },
    });
    function zwbeforeUpload(file) {
      const sizeM = file.size / (1024 * 1024);
      if (sizeM > 2) {
        return message.warning('文件过大，只能解析2M以下的文件');
      }
      const formData = new FormData();
      formData.append('file', file);
      formData.append('dataId', state.father.form.id);
      new Api('/kms').fetch(formData, 'knowledgeInfo/upload', 'POST').then((res) => {
        message.success('操作成功');
        state.father.form.content = res.content;
        state.father.form.summary = res.paragraph;
        if (!state.father.form.name) {
          state.father.form.name = res.title;
        }
      });
    }
    function deWeightFour(arr, id = 'id') {
      let obj = {};
      return arr.reduce((a, b) => {
        obj[b[id]] ? '' : (obj[b[id]] = a.push(b));
        return a;
      }, []);
    }

    async function getDetailModel1(id) {
      const url_attr = `attributeModel/attributeList/${id}`;
      const url_detail = `knowledgeModel/modelDetail/${id}`;
      state.attrList = await new Api('/kms').fetch('', url_attr, 'GET');
      const modelDetail = await new Api('/kms').fetch('', url_detail, 'GET');
      if (modelDetail) {
        bindAttr(modelDetail);
      } else {
        state.father.form.attributeParamDtoList = state.attrList;
        state.father.form.modelId = '';
        state.father.form.modelName = '';
        message.warning('当前分类暂无绑定模板');
      }
    }
    async function getDetailModel2(id) {
      const url_detail = `knowledgeModel/detail/${id}`;
      const modelDetail = await new Api('/kms').fetch('', url_detail, 'GET');
      bindAttr(modelDetail);
    }
    function bindAttr(modelDetail) {
      state.father.form.content = modelDetail.content;
      state.father.form.permissionJsonDtoList = modelDetail.permissionJsonDtoList;
      state.father.form.secretLevel = modelDetail.secretLevel;
      state.father.form.securityLimit = modelDetail.securityLimit;
      // 分类属性和分类下模板属性合并
      const attributeParamDtoList = modelDetail.attributeDtoList.concat(state.attrList);
      // 通过id去除重复的属性并绑定到页面
      state.father.form.attributeParamDtoList = deWeightFour(attributeParamDtoList);
      state.father.form.labelDtoList = modelDetail.labelList.map((s) => s.name);
      state.father.form.modelId = modelDetail.id;
      state.father.form.modelName = modelDetail.name;
      message.success('操作成功');
    }

    function handleSaveFile(successAll, cb) {
      const files = successAll.map((item) => {
        const { classification, securityLimit } = item;
        const {
          filePath, filePostfix, fileSize, name,
        } = item.result;
        return {
          dataId: state.father.form.id,
          fileTool: item.openTool,
          filePath,
          filePostfix,
          fileSize,
          name,
          secretLevel: classification,
          securityLimit,
        };
      });
      const api = new Api('/res/file/batch').fetch(files, '', 'POST');
      cb(api).then(() => {
        getFileList();
      });
    }
    function getFileList() {
      new Api('').fetch('', `/res/file/all/${state.father.form.id}`, 'GET').then((res) => {
        state.fileDataSource = res;
      });
    }

    function init() {
      if (state.father.type === 'add') {
        getDetailModel1(state.father.form.classifyId);
      }

      getFileList(); // 获取当前附件
      onUpdateFlow(); // 获取当前流程

      new Api(`/pmi/data-classification/user/${userStore.getUserInfo.id}`)
        .fetch('', '', 'GET')
        .then((res) => {
          state.secretLevelList = addValueLabel(res);
        });
      new Api('/kms/knowledgeInfo/list').fetch({ keyword: '' }, '', 'GET').then((res) => {
        state.knowledgeIdList = addValueLabel(res);
      });
      new Api('/kms/kmsLabel/list/1').fetch({}, '', 'GET').then((res) => {
        state.kmsLabelList = addValueLabel(res);
      });
      new Api('/kms').fetch('', 'kmsLabel/list/all', 'GET').then((res) => {
        state.selectLabelList = res.map((s) => ({
          value: s.id,
          label: s.name,
        }));
      });

      new Api('/pmi/dict/dictd85e24b99d23444f8ccf3ae59bcde2d3')
        .fetch({}, '', 'GET')
        .then((res) => {
          res.map((s) => {
            s.value = s.id;
            s.label = s.description;
          });
          state.deadlinelList = res;
        });
      new Api('/kms').fetch('', 'kmsDocumentModel/list', 'POST').then((res) => {
        state.modelList = res.map((s) => ({
          ...s,
          value: s.id,
          label: s.name,
          disabled: !s.rtcHtml,
        }));
      });
    }
    function selectClass(title, type) {
      state.selectCluster = {
        visible: true,
        title,
        type,
      };
    }
    function submitSelectCluster(val) {
      state.father.form.classifyId = val.id;
      state.father.form.classifyName = val.name;
      formRef.value.resetFields();
      getDetailModel1(val.id);
    }

    function getBase64(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function () {};
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(reader.result);
        };
      });
    }

    function beforeUpload(file) {
      const sizeM = file.size / (1024 * 1024);
      if (sizeM > 5) {
        return message.warning('文件过大，只能上传5M以内的图片或视频');
      }
      getBase64(file).then((res) => {
        state.father.form.thumb = res;
      });
    }

    function deleteAttrSelect(i) {
      state.father.form.attributeParamDtoList.splice(i, 1);
    }

    function onSubmit() {
      // 获取当前流程id
      const threadId = state.dataFlow.length ? state.dataFlow[0].id : '';
      if (!threadId) {
        return message.warning('流程必须选择');
      }
      state.father.form.threadId = threadId;
      formRef.value
        .validate()
        .then(() => {
          if (state.isSecurityLimit && !state.father.form.securityLimit) {
            return message.warning('请选择密级期限');
          }
          const arr = state.father.form.attributeParamDtoList;
          if (arr !== null) {
            for (const s of arr) {
              if (s.required) {
                // 单选0 多选1 输入项2 日期 3 时间范围 4
                if (s.selectType === 0 && !s.value) {
                  return message.warning(`${s.name}不能为空`);
                }
                if (s.selectType === 1 && !s.valueList.length) {
                  return message.warning(`${s.name}不能为空`);
                }
                if (s.selectType === 2 && (s.value === null || s.value === '')) {
                  return message.warning(`${s.name}不能为空`);
                }
                if (s.selectType === 3 && !s.value) {
                  return message.warning(`${s.name}不能为空`);
                }
                if (s.selectType === 4 && !s.valueList.length) {
                  return message.warning(`${s.name}不能为空`);
                }
              }
            }
          }
          state.loading = true;
          if (state.father.type === 'add' || state.father.type === 'share') {
            const operateLog: any = {
              id: state.father.form.id,
              name: state.father.form.name,
              className: 'KnowledgeInfo',
              moduleName: '知识管理',
              type: 'SAVE',
              remark: `【知识管理】创建了【${state.father.form.name}】`,
            };
            new Api('/kms', operateLog)
              .fetch(state.father.form, 'knowledgeInfo/new', 'POST')
              .then(() => {
                message.success('操作成功');
                emit('edit');
                state.loading = false;
                state.father.visible = false;
              })
              .catch((_) => {
                state.loading = false;
              });
          }
          if (state.father.type === 'edit') {
            const operateLog: any = {
              id: state.father.form.id,
              name: state.father.form.name,
              className: 'KnowledgeInfo',
              moduleName: '知识管理',
              type: 'UPDATE',
              remark: `【知识管理】修改了【${state.father.form.name}】`,
            };
            new Api('/kms', operateLog)
              .fetch(state.father.form, 'knowledgeInfo/new', 'PUT')
              .then(() => {
                message.success('操作成功');
                emit('edit');
                state.loading = false;
                state.father.visible = false;
              })
              .catch((_) => {
                state.loading = false;
              });
          }
          if (state.father.type === 'upgraded') {
            const operateLog: any = {
              id: state.father.form.id,
              name: state.father.form.name,
              className: 'KnowledgeInfo',
              moduleName: '知识管理',
              type: 'UPDATE',
              remark: `【知识管理】升级了【${state.father.form.name}】`,
            };
            new Api('/kms', operateLog)
              .fetch(state.father.form, 'knowledgeInfo/upgrade/new', 'PUT')
              .then((res) => {
                message.success('操作成功');
                // window.open(location.origin + '/#/' + 'knowledge/detailNew?id=' + res.id);
                handleDetailController(res.id);
                emit('edit');
                state.loading = false;
                state.father.visible = false;
              })
              .catch((_) => {
                state.loading = false;
              });
          }
        })
        .catch(() => {
          message.warning('请检查提交内容');
        });
    }

    async function downLoad(id) {
      if (await roleController(state.father.form.id, 'download')) {
        downLoadById(id);
      }
    }
    function deleteHandle(id) {
      new Api('/res/file').fetch({}, id, 'DELETE').finally(() => {
        message.success('删除成功');
        getFileList();
      });
    }

    function filterOption(inputValue, option) {
      return option.label.includes(inputValue);
    }

    function handleChooseObject(row) {
      state.selectObject = {
        visible: true,
        form: JSON.parse(JSON.stringify(row)),
      };
    }
    function submitSelectObject(row) {
      state.father.form.permissionJsonDtoList[row.type - 1] = JSON.parse(JSON.stringify(row));
    }

    function handleFlow() {
      if (state.dataFlow.length) {
        return message.warning('只能选择一个流程');
      }
      openModalFlow(true, {});
    }
    async function onUpdateFlow() {
      const query = {
        query: { deliveries: [{ deliveryId: state.father.form.id }] },
        userId: userStore.getUserInfo.id,
        pageNum: 1,
        pageSize: 10,
      };
      const data = await new Api('/workflow').fetch(query, 'act-prearranged/all/page', 'POST');
      state.dataFlow = data.content;
    }

    function onDetail(row) {
      router.push({
        path: '/flowcenter/detail',
        query: {
          id: row.id,
          processInstanceId: row.procInstId,
          processDefinitionId: row.procDefId,
        },
      });
    }

    async function onStart(row) {
      const params = {
        ids: [row.id],
        userId: userStore.getUserInfo.id,
      };
      await new Api('/workflow/act-prearranged/start').fetch(params, '', 'PUT');
      message.success('启动成功');
      onUpdateFlow();
    }

    async function onDrawith(row) {
      const params = {
        procInstIds: [row.procInstId],
        userId: userStore.getUserInfo.id,
      };
      await new Api('/workflow/act-inst/retract').fetch(params, '', 'PUT');
      message.success('撤回成功');
      onUpdateFlow();
    }

    async function onDelete(row) {
      await new Api('/workflow/act-prearranged/delete').fetch([row.id], '', 'DELETE');
      message.success('删除成功');
      onUpdateFlow();
    }

    function handlePreview() {
      state.preview = {
        visible: true,
        title: '知识预览',
        form: state.father.form,
      };
    }

    async function replaceTemplate() {
      const list = await new Api('/kms/knowledgeModel/list').fetch('', '', 'GET');
      state.template = {
        visible: true,
        title: '更换模板',
        placeholder: '请选择模板',
        value: undefined,
        list: list.map((s) => ({
          value: s.id,
          label: s.name,
        })),
      };
    }
    function saveTemplate(val) {
      state.template.visible = false;
      if (val) {
        state.father.form.modelId = val.value;
        state.father.form.modelName = val.label;
        formRef.value.resetFields();
        getDetailModel2(val.value);
      } else {
        state.father.form.modelId = undefined;
        state.father.form.modelName = undefined;
        state.father.form.content = undefined;
        message.success('操作成功');
      }
    }
    function clickSelectProperties() {
      state.selectProperties = {
        visible: true,
        title: '选择属性',
        ids: state.father.form.attributeParamDtoList.map((s) => s.id),
        list: [],
      };
    }
    function submitSelectProperties(val) {
      const arr = state.father.form.attributeParamDtoList;
      state.father.form.attributeParamDtoList = arr.concat(val.list);
    }

    function handleEditModel(arr, id) {
      if (!id) {
        state.father.form.content = undefined;
      } else {
        let obj = arr.find((s) => s.id === id);
        state.father.form.content = obj.rtcHtml;
      }
      message.success('操作成功');
    }

    async function handleAdd() {
      // 标签保存到本地 arr方便操作
      const arr = state.father.form.labelDtoList;
      // 初始化穿梭框数据 已存在的标签 需要设置禁用状态
      const arr1 = state.kmsLabelList.map((s) => ({
        id: s.id,
        name: s.name,
        disabled: arr.includes(s.name),
      }));

      state.selectLabels = {
        visible: true,
        title: '选择标签',
        titles: ['可选标签', '已选标签'],
        ids: [],
        list: arr1,
      };
    }

    function submitSelectLabels() {
      const obj = state.selectLabels;
      const arr = obj.list.filter((s) => obj.ids.some((v) => v === s.id)).map((s) => s.name);
      state.father.form.labelDtoList = arr.concat(state.father.form.labelDtoList);
      obj.visible = false;
      state.selectLabels = {}; // 清空内存，回到初始状态
    }

    async function getProjectPower() {
      return new Promise((resolve, _) => {
        useProjectPower(
          { pageCode: 'KMS009' },
          (powerList) => {
            resolve(powerList || []);
          },
          getCurrentInstance(),
        );
      });
    }
    onMounted(async () => {
      init();
      state.powerData = await getProjectPower();
    });
    return {
      ...toRefs(state),
      submitSelectLabels,
      zwbeforeUpload,
      selectClass,
      submitSelectCluster,
      getDetailModel1,
      getDetailModel2,
      getFileList,
      handleSaveFile,
      beforeUpload,
      deleteAttrSelect,
      onSubmit,
      downLoad,
      deleteHandle,
      filterOption,
      formRef,
      handleChooseObject,
      submitSelectObject,
      submitSelectProperties,
      registerFlow,
      onUpdateFlow,
      handleFlow,
      onStart,
      onDrawith,
      onDelete,
      onDetail,
      handlePreview,
      replaceTemplate,
      saveTemplate,
      clickSelectProperties,
      handleEditModel,
      handleAdd,
      previewView,
      stringIntercept,
      getFileObj,
      isPower,
    };
  },
});
</script>

<style lang="less" scoped>
  .fileItem-text {
    margin-left: 10px;
    font-size: 15px;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }

  .attr-width {
    width: 200px !important;
    margin-right: 10px;
  }

  .breadcrumb-box {
    border: 1px solid rgba(219, 224, 235, 1);
    padding: 5px 12px;
    margin: 12px 0;
    background-color: rgb(233, 238, 241);
  }

  .edit-content {
    height: calc(100vh - 130px);

    .ant-upload,
    .ant-btn-block {
      width: 100%;
    }

    .allUpload {
      .ant-btn {
        width: 100%;
        margin: 6px 0;
      }
    }

    .file-box {
      display: flex;
      margin: 10px 0;
      border: 1px solid #eeeeee;
      padding: 10px 10px;
      background: #eef2f3;
      border-radius: 2px;

      .accessory {
        width: 70px;
        line-height: 30px;
        font-weight: 600;
      }
      .content {
        flex: 1;
      }
    }
  }
</style>
