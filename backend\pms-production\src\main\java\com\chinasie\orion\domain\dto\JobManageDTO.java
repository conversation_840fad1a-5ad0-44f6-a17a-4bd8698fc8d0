package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:49
 * @description:
 */

@ApiModel(value = "JobManageDTO对象", description = "作业管理")
@Data
@ExcelIgnoreUnannotated
public class JobManageDTO extends ObjectDTO implements Serializable {
    @ApiModelProperty(value = "来源ID")
    private String  sourceId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String planSchemeId;
    @ApiModelProperty(value = "项目计划名称（冗余）")
    private String planSchemeName;
    /**
     * 作业类型（大修作业。日常作业）
     */
    @ApiModelProperty(value = "作业类型（大修作业。日常作业）")
    private String type;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号 ", index = 0)
    private String number;

    /**
     * 作业名称"
     */
    @ApiModelProperty(value = "作业名称")
    private String name;
    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id")
    private String rspUserId;

    /**
     * 项目负责人编号（工号）
     */
    @ApiModelProperty(value = "项目负责人编号（工号）")
    @ExcelProperty(value = "项目负责人编号（工号） ", index = 4)
    private String rspUserCode;

    /**
     * 项目负责人姓名
     */
    @ApiModelProperty(value = "项目负责人姓名")
    @ExcelProperty(value = "项目负责人姓名 ", index = 5)
    private String rspUserName;

    /**
     * 高风险（字典）
     */
    @ApiModelProperty(value = "高风险（字典）")
    private String heightRisk;

    /**
     * 是否高风险
     */
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;

    @ApiModelProperty(value = "是否高风险")
    @ExcelProperty(value = "高风险（字典） ", index = 1)
    private String highRiskName;

    /**
     * 是否重要作业
     */
    @ApiModelProperty(value = "是否重要作业")
    private Boolean isImportant;

    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    private String nOrO;

    /**
     * 工作中心(默认值：SNPI)
     */
    @ApiModelProperty(value = "工作中心(默认值：SNPI)")
    private String workCenter;

    /**
     * 是否自带工器具（如果为是：需要去详情 新增物资）
     */
    @ApiModelProperty(value = "是否自带工器具（如果为是：需要去详情 新增物资）")
    private Boolean isCarryTool;

    /**
     * 防异物等级
     */
    @ApiModelProperty(value = "防异物等级")
    @ExcelProperty(value = "防异物等级 ", index = 2)
    private String antiForfeignLevel;

    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    @ExcelProperty(value = "首次执行 ", index = 3)
    private String firstExecute;

    /**
     * 新人参与
     */
    @ApiModelProperty(value = "新人参与")
    private Boolean newParticipants;

    /**
     * 责任中心（部门）
     */
    @ApiModelProperty(value = "责任中心（部门）")
    private String rspDept;

    /**
     * 作业基地（编号）
     */
    @ApiModelProperty(value = "作业基地（编号）")
    private String jobBase;

    /**
     * 作业基地名称
     */
    @ApiModelProperty(value = "作业基地名称")
    private String jobBaseName;

    /**
     * 开工审查（字典）
     */
    @ApiModelProperty(value = "开工审查（字典）")
    private String startExamine;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;

    /**
     * 研读审查状态
     */
    @ApiModelProperty(value = "研读审查状态")
    private String studyExamineStatus;

    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    private Date closeDate;

    /**
     * 工作包状态
     */
    @ApiModelProperty(value = "工作包状态")
    private String workPackageStatus;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @ExcelProperty(value = "计划工期 ", index = 6)
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;



    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")

    private Date statisticTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @ExcelProperty(value = "计划工期 ", index = 7)
    private Integer workDuration;

    /**
     * 作业描述
     */
    @ApiModelProperty(value = "作业描述")
    private String jobDesc;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @NotNull(message = "大修轮次不能为空")
    private String repairRound;

    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    @ApiModelProperty(value = "重要项目")
    private String importantProject;

    /**
     * 工作抬头
     */
    @ApiModelProperty(value = "工作抬头")
    private String workJobTitle;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    private String functionalLocation;

    /**
     * 作业状态
     */
    @ApiModelProperty(value = "作业状态")
    private String busStatus;

    @ApiModelProperty(value = "作业状态名称")
    private String busStatusName;


    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;

    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;

    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;


    @ApiModelProperty(value = "监管人员Id")
    private String managePersonId;

    @ApiModelProperty(value = "监管人员工号")
    private String managePersonCode;

    @ApiModelProperty(value = "监管人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作地点")
    private String workPlace;

    @ApiModelProperty(value = "作业部门ID")
    private String jobDeptId;

    @ApiModelProperty(value = "作业部门编码")
    private String jobDeptCode;

    @ApiModelProperty(value = "作业部门名称")
    private String jobDeptName;
    @ApiModelProperty(value = "关键词")
    private String keyword;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "sql条件列表")
    private List<String> sqlConditionList;

    @ApiModelProperty(value = "大修所属部门")
    private String repairOrgId;

    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    private Integer matchUp;

    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    private String phase;
}

