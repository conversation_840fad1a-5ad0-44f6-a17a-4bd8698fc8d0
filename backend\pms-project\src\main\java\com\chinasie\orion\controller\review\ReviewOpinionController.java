package com.chinasie.orion.controller.review;

import com.chinasie.orion.domain.dto.review.ReviewOpinionDTO;
import com.chinasie.orion.domain.vo.review.ReviewOpinionVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewOpinionService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ReviewOpinion 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@RestController
@RequestMapping("/reviewOpinion")
@Api(tags = "评审意见")
public class ReviewOpinionController {

    @Autowired
    private ReviewOpinionService reviewOpinionService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审意见】数据详情", type = "Review", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ReviewOpinionVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ReviewOpinionVO rsp = reviewOpinionService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 专家文审意见
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "意见新增")
    @RequestMapping(value = "/add", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【评审意见】数据", type = "ReviewOpinion", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> lotus(@RequestParam("type")Integer type,@RequestParam("mainTableId")String mainTableId) throws Exception {
        String rsp =  reviewOpinionService.lotus(type,mainTableId);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param reviewOpinionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【评审意见】数据", type = "ReviewOpinion", subType = "编辑", bizNo = "{{#reviewOpinionDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ReviewOpinionDTO reviewOpinionDTO) throws Exception {
        Boolean rsp = reviewOpinionService.edit(reviewOpinionDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【评审意见】数据", type = "ReviewOpinion", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = reviewOpinionService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【评审意见】数据", type = "ReviewOpinion", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewOpinionService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审意见】数据", type = "ReviewOpinion", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ReviewOpinionVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<ReviewOpinionDTO> pageRequest) throws Exception {
        Page<ReviewOpinionVO> rsp =  reviewOpinionService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("评审意见导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【评审意见】导入模板", type = "ReviewOpinion", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        reviewOpinionService.downloadExcelTpl(response);
    }

    @ApiOperation("评审意见导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【评审意见】导入", type = "ReviewOpinion", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = reviewOpinionService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("评审意见导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}/{adminTableId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【评审意见】导入", type = "ReviewOpinion", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId, @PathVariable("adminTableId") String adminTableId) throws Exception {
        Boolean rsp =  reviewOpinionService.importByExcel(importId,adminTableId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消评审意见导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【评审意见】导入", type = "ReviewOpinion", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  reviewOpinionService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("评审意见导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【评审意见】数据", type = "ReviewOpinion", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions,
                              HttpServletResponse response,
                              @RequestParam("mainTableId")String mainTableId) throws Exception {
        reviewOpinionService.exportByExcel(searchConditions, response, mainTableId);
    }


    /**
     * 专家文审意见
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "专家文审意见查询")
    @RequestMapping(value = "/list/lotus/{adminTableId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审意见】专家文审意见", type = "ReviewOpinion", subType = "编辑", bizNo = "")
    public ResponseDTO<List<ReviewOpinionVO>> lotusList(@PathVariable("adminTableId")String adminTableId) throws Exception {
        List<ReviewOpinionVO> rsp = reviewOpinionService.lotusList(adminTableId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 评审意见
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "评审意见查询")
    @RequestMapping(value = "/list/appraisal/{adminTableId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审意见】", type = "ReviewOpinion", subType = "编辑", bizNo = "")
    public ResponseDTO<List<ReviewOpinionVO>> appraisalList(@PathVariable("adminTableId")String adminTableId) throws Exception {
        List<ReviewOpinionVO> rsp = reviewOpinionService.appraisalList(adminTableId);
        return new ResponseDTO<>(rsp);
    }
}
