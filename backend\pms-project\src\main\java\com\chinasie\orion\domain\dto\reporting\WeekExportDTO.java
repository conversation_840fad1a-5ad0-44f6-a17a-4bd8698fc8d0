package com.chinasie.orion.domain.dto.reporting;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeekExportDTO {
    @ExcelProperty("序号*")
    private String sort;
    @ExcelProperty("汇报时间*")
    private String week;
    @ExcelProperty("工作内容*")
    private String content;
    @ExcelProperty(value = "关联对象")
    private String relationObj;
    @ExcelProperty("责任人*")
    private String respName;
    @ExcelProperty("工时*")
    private BigDecimal taskTime;
    @ExcelProperty("整体进度*")
    private String overallProgressName;
    @ExcelProperty("状态*")
    private String statusName;
    @ExcelProperty("审核人*")
    private String reviewedByName;
    @ExcelProperty(value = "评分")
    private BigDecimal score;
    @ExcelProperty(value = "评价")
    private String evaluate;
}
