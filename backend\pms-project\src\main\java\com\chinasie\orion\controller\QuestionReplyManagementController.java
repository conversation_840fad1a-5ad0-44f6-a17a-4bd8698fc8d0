package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.QuestionReplyManagementDTO;
import com.chinasie.orion.domain.vo.QuestionReplyManagementVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionReplyManagementService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * QuestionReplyManagement 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
@RestController
@RequestMapping("/questionReplyManagement")
@Api(tags = "问题答复")
public class QuestionReplyManagementController {

    @Autowired
    private QuestionReplyManagementService questionReplyManagementService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#questionReplyManagementDTO.name}}】", type = "问题答复", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<QuestionReplyManagementVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        QuestionReplyManagementVO rsp = questionReplyManagementService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param questionReplyManagementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#questionReplyManagementDTO.name}}】", type = "问题答复", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception {
        String rsp = questionReplyManagementService.create(questionReplyManagementDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 批量新增
     *
     * @param questionReplyManagementDTOS
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增")
    @RequestMapping(value = "/addList", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#questionReplyManagementDTO.name}}】", type = "问题答复", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> addList(@RequestBody List<QuestionReplyManagementDTO> questionReplyManagementDTOS) throws Exception {
        boolean rsp = questionReplyManagementService.addList(questionReplyManagementDTOS);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据问题编码查询
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据问题编码查询")
    @RequestMapping(value = "/getListByNumber", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】根据问题编码查询了数据【{{#questionReplyManagementDTO.name}}】", type = "问题答复", subType = "根据问题编码查询", bizNo = "")
    public ResponseDTO<List<QuestionReplyManagementVO>> getListByNumber(@RequestBody QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception {
        List<QuestionReplyManagementVO> rsp = questionReplyManagementService.getListByNumber(questionReplyManagementDTO.getQuestionNumber());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param questionReplyManagementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#questionReplyManagementDTO.name}}】", type = "问题答复", subType = "编辑", bizNo = "{{#questionReplyManagementDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody QuestionReplyManagementDTO questionReplyManagementDTO) throws Exception {
        Boolean rsp = questionReplyManagementService.edit(questionReplyManagementDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "问题答复", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = questionReplyManagementService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "问题答复", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = questionReplyManagementService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "问题答复", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<QuestionReplyManagementVO>> pages(@RequestBody Page<QuestionReplyManagementDTO> pageRequest) throws Exception {
        Page<QuestionReplyManagementVO> rsp = questionReplyManagementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("问题答复导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "问题答复", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        questionReplyManagementService.downloadExcelTpl(response);
    }

    @ApiOperation("问题答复导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "问题答复", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = questionReplyManagementService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("问题答复导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "问题答复", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = questionReplyManagementService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消问题答复导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "问题答复", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = questionReplyManagementService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("问题答复导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "问题答复", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        questionReplyManagementService.exportByExcel(searchConditions, response);
    }
}
