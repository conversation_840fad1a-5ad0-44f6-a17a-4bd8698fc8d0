import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 风险分页 */
  questionPage = 'question-management/getPage',
  /* 新增 */
  addQuestion = 'question-management/save',
  /* 批量删除 */
  deleteQuestion = 'question-management/removeBatch',
  // 编辑
  editQuestion = 'question-management/edit',

  // modal 问题来源
  questionSource = 'question-management/questionSourceList',
  // modal 问题类型
  questionType = 'question-management/questionTypeList',
  // modal 严重程度
  questionLevel = 'question-management/seriousLevelList',
  // 打开 问题详情页详情
  questionDetailsPage = 'question-management/detail',
  // 打开 问题关联列表
  getQuestionContactPage = 'question-management/relation/plan',
  // 打开 删除关联列表
  delQuestionContactPage = 'question-management/relation/plan/batch',
  // 风险转问题
  riskToQuestion = 'risk-management/riskChangeQuestion',
  // 关联风险
  contactRiskTable = 'question-management/relation/risk'
}

/**
 * @description: 风险管理
 */
// 关联风险
export function contactRiskTableApi(params, love, hah = {}) {
  return new Api(base, love).fetch(hah, `${zApi.contactRiskTable}/${params}`, 'POST');
}
// 分页
export function questionPageApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.questionPage}/`, 'POST');
}
// ++
export function addquestionApi(params) {
  const love = {
    name: params?.name,
    className: 'QuestionManagement',
    moduleName: '项目管理-问题管理',
    type: 'SAVE',
    remark: `新增了【${params?.name}】`,
  };
  return new Api(base, love).fetch(params, `${zApi.addQuestion}/`, 'POST');
}
// --
export function deleteQuestionApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.deleteQuestion}/`, 'DELETE');
}
// 编辑
export function editQuestionApi(params) {
  const love = {
    id: params?.id,
    name: params?.name,
    className: 'QuestionManagement',
    moduleName: '项目管理-问题管理',
    type: 'UPDATE',
    remark: `编辑了【${params?.id}】`,
  };
  return new Api(base, love).fetch(params, `${zApi.editQuestion}/`, 'PUT');
}
// modal 问题来源
export function questionSourceApi() {
  return new Api(base).fetch('', `${zApi.questionSource}/`, 'GET');
}
// modal问题类型
export function questionTypeApi() {
  return new Api(base).fetch('', `${zApi.questionType}/`, 'GET');
}
// modal严重程度
export function questionLevelApi() {
  return new Api(base).fetch('', `${zApi.questionLevel}/`, 'GET');
}
// 打开 问题详情页详情
export function questionDetailsPageApi(params, love) {
  return new Api(base, love).fetch('', `${zApi.questionDetailsPage}/${params}`, 'GET');
}
// 打开 问题关联计划
export function getQuestionContactPageApi(params, love, obj = {}) {
  return new Api(base, love).fetch(obj, `${zApi.getQuestionContactPage}/${params}`, 'POST');
}
//  --问题关联计划
export function delQuestionContactPageApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.delQuestionContactPage}/`, 'DELETE');
}
// 风险转问题
export function riskToQuestionApi(params, objarr) {
  const love = {
    className: 'RiskManagement',
    moduleName: '项目管理-风险管理-实际风险',
    type: 'UPDATE',
    remark: `分解了【${objarr.map((item) => item.name)}】`,
  };
  return new Api(base, love).fetch(objarr, `${zApi.riskToQuestion}/${params}/`, 'POST');
}
