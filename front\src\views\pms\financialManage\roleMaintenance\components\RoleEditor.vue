<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm, BasicCard,
} from 'lyra-component-vue3';
import {
  nextTick,
  onMounted, Ref, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas: FormSchema[] = [
  {
    field: 'expertiseCenter',
    label: '',
    component: 'Input',
    colProps: {
      span: 12,
    },
    show: () => false,
  },
  {
    field: 'expertiseCenterTitle',
    label: '专业中心',
    component: 'ApiSelect',
    rules: [
      {
        required: true,
        trigger: 'change',
      },
    ],
    componentProps(params) {
      return {
        api() {
          return new Api('/pmi/organization/dept/list').fetch('', '20', 'GET').then((data) => data?.map((item) => {
            const { id, name } = item;
            return {
              ...item,
              label: name,
              value: id,
            };
          }) || []);
        },
        onChange(value, record) {
          nextTick(() => {
            setFieldsValue({
              expertiseCenter: record.value,
              expertiseCenterTitle: record.label,
            });
            if (params.formModel.expertiseStation && params.formModel.expertiseStationTitle) {
              setFieldsValue({
                expertiseStation: '',
                expertiseStationTitle: '',
              });
            }
          });
        },
      };
    },
  },
  {
    field: 'expertiseCenterCode',
    label: '专业中心审核人员',
    component: 'SelectUser',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      selectUserModalProps: {
        selectType: 'checkbox',
      },
    },
  },
  {
    field: 'expertiseStationTitle',
    label: '专业所',
    component: 'ApiSelect',
    rules: [
      {
        required: false,
        trigger: 'change',
      },
    ],
    componentProps(params) {
      return {
        disabled: !params.formModel.expertiseCenter,
        api() {
          if (params.formModel.expertiseCenter) {
            return new Api(`/pms/personRoleMaintenance/searchStationName/${params.formModel.expertiseCenter}`).fetch('', '', 'POST').then((data) => data?.map((item) => {
              const { expertiseStationTitle, expertiseStation } = item;
              return {
                ...item,
                label: expertiseStationTitle,
                value: expertiseStation,
              };
            }) || []);
          }
          return null;
        },
        onChange(value, record) {
          nextTick(() => {
            setFieldsValue({
              expertiseStation: record.value,
              expertiseStationTitle: record.label,
            });
          });
        },
      };
    },
  },
  {
    field: 'expertiseStation',
    label: '',
    component: 'Input',
    colProps: {
      span: 12,
    },
    show: () => false,
  },
  {
    field: 'expertiseStationCode',
    label: '专业所审核人员',
    component: 'SelectUser',
    rules: [
      {
        required: false,
        type: 'array',
      },
    ],
    componentProps: {
      selectUserModalProps: {
        selectType: 'checkbox',
      },
    },
  },
  {
    field: 'financialStaffCode',
    label: '财务人员',
    component: 'SelectUser',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      selectUserModalProps: {
        selectType: 'checkbox',
      },
    },
  },
  {
    field: 'changeReason',
    label: '变更原因',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    componentProps() {
      return {
        rows: 4,
        showCount: true,
        maxlength: 200,
      };
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData(props?.record?.id);
});

const loading: Ref<boolean> = ref(false);

// 定义一个函数来合并字符串数组
function mergeArrays(codes, names) {
  return codes?.map((code, index) => ({
    id: code?.trim(),
    name: names?.[index].trim(),
  }));
}

async function getFormData(id: string) {
  loading.value = true;
  try {
    const result = await new Api(`/pms/personRoleMaintenance/${id}`).fetch('', '', 'GET');
    if (result?.id) {
      // 处理 null 或 undefined 情况
      const expertiseCenterCodes = result.expertiseCenterCode ? result?.expertiseCenterCode.split('、') : [];
      const expertiseCenterNames = result.expertiseCenterName ? result?.expertiseCenterName.split('、') : [];
      const expertiseStationCodes = result.expertiseStationCode ? result?.expertiseStationCode.split('、') : [];
      const expertiseStationNames = result.expertiseStationName ? result?.expertiseStationName.split('、') : [];
      const financialStaffCodes = result.financialStaffCode ? result?.financialStaffCode.split('、') : [];
      const financialStaffNames = result.financialStaffName ? result?.financialStaffName.split('、') : [];

      // 合并成对象数组
      const mergedData = mergeArrays(expertiseCenterCodes, expertiseCenterNames);
      const mergedStationData = mergeArrays(expertiseStationCodes, expertiseStationNames);
      const mergedFinancialData = mergeArrays(financialStaffCodes, financialStaffNames);

      const arr = {
        ...result,
        expertiseCenterCode: mergedData,
        expertiseStationCode: mergedStationData,
        financialStaffCode: mergedFinancialData,
      };
      setFieldsValue({ ...arr });
    }
  } catch (error) {
    // console.error('Error occurred during fetching form data:', error);
  } finally {
    loading.value = false;
  }
}

// 定义处理函数
function processItems(items, idsArray, namesArray) {
  items.forEach((item) => {
    if (item.id) {
      idsArray.push(item.id);
      namesArray.push(item.name);
    }
  });
}

// 定义字符串拼接函数
function joinIfNotEmpty(array, separator) {
  return array.length > 0 ? array.join(separator) : '';
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    // 初始化数组
    const centerIds = [];
    const centerNames = [];
    const stationIds = [];
    const stationNames = [];
    const staffIds = [];
    const staffNames = [];
    // 处理各项数据
    if (formValues.expertiseCenterCode) {
      processItems(formValues.expertiseCenterCode, centerIds, centerNames);
    }
    if (formValues.expertiseStationCode) {
      processItems(formValues.expertiseStationCode, stationIds, stationNames);
    }
    if (formValues.financialStaffCode) {
      processItems(formValues.financialStaffCode, staffIds, staffNames);
    }

    const params = {
      ...formValues,
      id: props?.record?.id || formValues.id,
      expertiseCenterCode: joinIfNotEmpty(centerIds, '、'),
      expertiseCenterName: joinIfNotEmpty(centerNames, '、'),
      expertiseStationCode: joinIfNotEmpty(stationIds, '、'),
      expertiseStationName: joinIfNotEmpty(stationNames, '、'),
      financialStaffCode: joinIfNotEmpty(staffIds, '、'),
      financialStaffName: joinIfNotEmpty(staffNames, '、'),
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/personRoleMaintenance').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then((res) => {
        if (res) {
          message.success(props?.record?.id ? '编辑成功' : '保存成功');
        }
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicCard title="人员角色维护">
    <BasicForm
      v-loading="loading"
      @register="register"
    />
  </BasicCard>
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
