import { ConfigProvider } from 'ant-design-vue';
import { setTheme } from 'lyra-component-vue3';
import { isQianKun } from '../qiankun/useQiankun';

// header插入link css src
function includeLinkStyle(url) {
  let link = document.createElement('link');
  link.rel = 'stylesheet';
  link.type = 'text/css';
  link.href = url;
  document.getElementsByTagName('head')[0].appendChild(link);
}

export function useSetTheme() {
  if (!isQianKun()) {
    includeLinkStyle('/css/antd.variable.min.css');
    ConfigProvider.config({
      theme: {
        primaryColor: 'rgb(13 154 0)',
      },
    });

    setTheme({
      'header-height': '46px',
      'sider-dark-bg-color': '#000',
      white: '#fff',
      // 'text-color-base': '#c9d1d9',
      'font-size-base': '14px',
      'breadcrumb-item-normal-color': '#999',
      'logo-width': '32px',
      'content-bg': '#f4f7f9',
    });
  }
}
