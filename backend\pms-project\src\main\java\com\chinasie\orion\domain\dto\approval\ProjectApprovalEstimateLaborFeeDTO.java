package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.math.BigDecimal;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateLaborFees DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateLaborFeesDTO对象", description = "概算工资及劳务费")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateLaborFeeDTO extends ObjectDTO implements Serializable{

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @ExcelProperty(value = "用户id ", index = 0)
    private String userId;


    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    @ExcelProperty(value = "职级 ", index = 1)
    private String jobPositionName;

    /**
     * 职级费率
     */
    @ApiModelProperty(value = "职级费率")
    @ExcelProperty(value = "职级费率 ", index = 2)
    private String jobPositionRate;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "岗位名称 ", index = 3)
    private String jobName;

    /**
     * 职级id
     */
    @ApiModelProperty(value = "职级id")
    @ExcelProperty(value = "职级id ", index = 4)
    private String jobPositionId;


    /**
     * 岗位id
     */
    @ApiModelProperty(value = "岗位id")
    @ExcelProperty(value = "岗位id ", index = 5)
    private String jobId;

    /**
     * 人员需求数量
     */
    @ApiModelProperty(value = "人员需求数量")
    @ExcelProperty(value = "人员需求数量 ", index = 6)
    private BigDecimal requiredNum;

    /**
     * 人天
     */
    @ApiModelProperty(value = "人天")
    @ExcelProperty(value = "人天 ", index = 7)
    private BigDecimal peopleDays;

    /**
     * 工资费用
     */
    @ApiModelProperty(value = "工资费用")
    @ExcelProperty(value = "工资费用 ", index = 8)
    private BigDecimal laborFee;



    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @ExcelProperty(value = "项目立项id ", index = 9)
    private String projectApprovalId;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    @ExcelProperty(value = "员工名称 ", index = 10)
    private String name;


}
