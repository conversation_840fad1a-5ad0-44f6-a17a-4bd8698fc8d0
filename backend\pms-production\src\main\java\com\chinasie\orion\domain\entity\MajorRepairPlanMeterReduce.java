package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:17
 * @description:
 */

@TableName(value = "pmsx_major_repair_plan_meter_reduce")
@ApiModel(value = "MajorRepairPlanMeterReduceEntity对象", description = "大修计划集体剂量降低")
@Data

public class MajorRepairPlanMeterReduce extends ObjectEntity implements Serializable {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "job_manage_number")
    private String jobManageNumber;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "major_repair_turn")
    private String majorRepairTurn;

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_manage_id")
    private String jobManageId;

    /**
     * is_reduce
     */
    @ApiModelProperty(value = "is_reduce")
    @TableField(value = "is_reduce")
    private Boolean isReduce;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 领域
     */
    @ApiModelProperty(value = "领域")
    @TableField(value = "belong_field")
    private String belongField;

    /**
     * 技术应用窗口
     */
    @ApiModelProperty(value = "技术应用窗口")
    @TableField(value = "application_occasion")
    private String applicationOccasion;

    /**
     * 落地电厂
     */
    @ApiModelProperty(value = "落地电厂")
    @TableField(value = "application_base")
    private String applicationBase;

    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    @TableField(value = "application_crew")
    private String applicationCrew;

    /**
     * 现场环境计量率
     */
    @ApiModelProperty(value = "现场环境计量率")
    @TableField(value = "environment_meter_rate")
    private BigDecimal environmentMeterRate;

    /**
     * 减少工时
     */
    @ApiModelProperty(value = "减少工时")
    @TableField(value = "reduce_hour")
    private BigDecimal reduceHour;

    /**
     * 节约集体剂量
     */
    @ApiModelProperty(value = "节约集体剂量")
    @TableField(value = "conserve_meter")
    private BigDecimal conserveMeter;

    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    @TableField(value = "create_excellence")
    private String createExcellence;

    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    @TableField(value = "content")
    private String content;

    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    @TableField(value = "is_continue_use")
    private Boolean isContinueUse;

}
