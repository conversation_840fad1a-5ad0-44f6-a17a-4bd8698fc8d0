package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ReqClarificationRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-27 09:31:59
 */
@ApiModel(value = "ReqClarificationRecordDTO对象", description = "需求澄清记录")
@Data
@ExcelIgnoreUnannotated
public class ReqClarificationRecordDTO extends  ObjectDTO   implements Serializable{

    /**
     * 澄清主题
     */
    @ApiModelProperty(value = "澄清主题")
    @ExcelProperty(value = "澄清主题 ", index = 0)
    private String clarificationTheme;

    /**
     * 澄清类型
     */
    @ApiModelProperty(value = "澄清类型")
    @ExcelProperty(value = "澄清类型 ", index = 1)
    private String clarificationType;

    /**
     * 澄清批次
     */
    @ApiModelProperty(value = "澄清批次")
    @ExcelProperty(value = "澄清批次 ", index = 2)
    private String clarificationLot;

    /**
     * 澄清内容
     */
    @ApiModelProperty(value = "澄清内容")
    @ExcelProperty(value = "澄清内容 ", index = 3)
    private String clarificationContext;

    /**
     * 是否更新报价时间
     */
    @ApiModelProperty(value = "是否更新报价时间")
    @ExcelProperty(value = "是否更新报价时间 ", index = 4)
    private String IsUpdateTime;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @ExcelProperty(value = "需求编号 ", index = 5)
    private String requirementId;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @ExcelProperty(value = "发布时间 ", index = 6)
    private Date ReleaseTime;

    /**
     * 查看时间
     */
    @ApiModelProperty(value = "查看时间")
    @ExcelProperty(value = "查看时间 ", index = 7)
    private Date checkTime;

    /**
     * 查看状态
     */
    @ApiModelProperty(value = "查看状态")
    @ExcelProperty(value = "查看状态 ", index = 8)
    private String checkStatus;

    /**
     * 原报价开始时间
     */
    @ApiModelProperty(value = "原报价开始时间")
    @ExcelProperty(value = "原报价开始时间 ", index = 9)
    private Date originQuoteStartTime;

    /**
     * 原报价截止时间
     */
    @ApiModelProperty(value = "原报价截止时间")
    @ExcelProperty(value = "原报价截止时间 ", index = 10)
    private Date originQuoteDlTime;

    /**
     * 原开启报价时间
     */
    @ApiModelProperty(value = "原开启报价时间")
    @ExcelProperty(value = "原开启报价时间 ", index = 11)
    private Date originQuoteOpenTime;

    /**
     * 新开启报价时间
     */
    @ApiModelProperty(value = "新开启报价时间")
    @ExcelProperty(value = "新开启报价时间 ", index = 12)
    private Date newQuoteOpenTime;

    /**
     * 新报价截止时间
     */
    @ApiModelProperty(value = "新报价截止时间")
    @ExcelProperty(value = "新报价截止时间 ", index = 13)
    private Date newQuoteDlTime;

    /**
     * 新报价开始时间
     */
    @ApiModelProperty(value = "新报价开始时间")
    @ExcelProperty(value = "新报价开始时间 ", index = 14)
    private Date newQuoteStartTime;

    /**
     * 澄清阶段
     */
    @ApiModelProperty(value = "澄清阶段")
    @ExcelProperty(value = "澄清阶段 ", index = 15)
    private String clarificationStage;


}
