<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1000"
    :showContinue="state.isHeadAddAll"
    :title="state.drawerName"
    :showFooter="true"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="confirmDrawer"
    @isContinueChange="isContinueChange"
  >
    <WarehousingListDrawerFrom
      v-if="state.visibleStatus"
      ref="formRef"
      :fromData="state.fromData"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  ref, defineEmits, computed,
} from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import * as fs from 'fs';
import WarehousingListDrawerFrom from './warehousingListDrawerFrom.vue';
import Api from '/@/api';
import login from '/@/views/sys/login/Login.vue';
const emits = defineEmits(['upTableDate']);
const formRef = ref();
const state = ref({
  visibleStatus: false,
  type: '',
  drawerName: '',
  id: '',
  projectId: '',
  fromData: {},
  isHeadAddAll: false,
});

function isContinueChange() {
  formRef.value.resetFields();
}
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, itemData, projectId}) => {
    state.value.projectId = openProps.projectId;
    state.value.drawerName = openProps.type === 'edit' ? '编辑物资/服务入库' : '添加入库';
    state.value.type = openProps.type;
    state.value.id = openProps.itemData?.id;
    state.value.fromData = {
      ...openProps.itemData,
      projectId: openProps.projectId,
      typeSelection: openProps.type,
    };
    // 设置为已打开状态
    state.value.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.value.visibleStatus = visible);
}
async function confirmDrawer() {
  const formValues = await formRef.value.validate();
  changeOkLoading(true);
  if (state.value.type === 'headAdd') {
    const { number, ...data } = formValues;
    data.projectId = state.value.projectId;
    await new Api('/pms/goods-service-store').fetch(data, '', 'POST').then(() => {

    }).finally(() => {
      changeOkLoading(false);
      // state.value.isHeadAddAll = true;
      closeDrawer();
      emits('upTableDate');
    });
  } else {
    if (state.value.type === 'add') {
      const { remark, storeAmount, storeTime } = formValues;
      const data = {
        remark,
        storeAmount,
        storeTime,
        id: state.value.id,
      };
      await new Api('/pms/goods-service-store/add').fetch(data, '', 'POST').then(() => {

      }).finally(() => {
        changeOkLoading(false);
      });
    }
    if (state.value.type === 'edit') {
      const { number, ...data } = formValues;
      data.projectId = state.value.projectId;
      data.id = state.value.id;
      await new Api('/pms/goods-service-store').fetch(data, '', 'PUT').then(() => {

      }).finally(() => {
        changeOkLoading(false);
      });
    }
    closeDrawer();
    emits('upTableDate');// 更新父组件数据
  }
}

</script>
<style scoped lang="less">
</style>
