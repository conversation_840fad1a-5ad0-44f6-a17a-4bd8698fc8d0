.productLibraryDetails .productLibraryDetails_content {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
}
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_left,
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_right {
  width: calc(50% - 10px);
}
.productLibraryDetails .productLibraryDetails_content .detailsImg {
  height: 400px;
  width: 100%;
}
.productLibraryDetails .productLibraryDetails_content .detailsImg img {
  width: 100%;
  height: 80%;
  padding-bottom: 10px;
}
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_right {
  padding-left: 16px;
}
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_left_title,
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_right_title {
  height: 40px;
  background-color: #f0f2f5;
  line-height: 40px;
  color: #444b5e;
  padding-left: 16px;
  margin-bottom: 10px;
}
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_left_title .anticon-caret-down,
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_right_title .anticon-caret-down {
  color: #969eb4;
  cursor: pointer;
}
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_left_title span,
.productLibraryDetails .productLibraryDetails_content .productLibraryDetails_right_title span {
  padding-left: 6px;
}
.productLibraryDetails .productLibraryDetails_content .ant-form-item-label {
  text-align: left;
}
.productLibraryDetails .productLibraryDetails_content .coverImg {
  width: 100px;
  height: 50px;
  border-radius: 5px;
}
.productLibraryDetails .productLibraryDetails_btn {
  background: #ffffff;
}
