package com.chinasie.orion.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeMilestoneNodePrePost Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-10 14:29:13
 */
@ApiModel(value = "ProjectSchemeMilestoneNodePrePostDTO对象", description = "项目计划模板前后置关系")
@Data
public class ProjectSchemeMilestoneNodePrePostDTO extends ObjectDTO implements Serializable {


    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    private String preSchemeId;

    /**
     *后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    private String postSchemeId;


    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;


    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目id")
    private String templateId;
    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    private Integer type;
}
