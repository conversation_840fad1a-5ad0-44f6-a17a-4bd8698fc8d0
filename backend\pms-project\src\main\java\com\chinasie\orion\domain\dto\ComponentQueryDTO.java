package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/10 15:22
 */
@Data
public class ComponentQueryDTO implements Serializable {

    @ApiModelProperty(value = "ids")
    private List<String> ids;

    @ApiModelProperty(value = "搜索关键字")
    private String searchText;

    @ApiModelProperty(value = "产品id")
    private String productId;
}
