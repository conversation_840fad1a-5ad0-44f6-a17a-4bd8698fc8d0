package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * LaborCostAcceptanceAssessmentStandard Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
@TableName(value = "pmsx_labor_cost_acceptance_assessment_standard")
@ApiModel(value = "LaborCostAcceptanceAssessmentStandardEntity对象", description = "验收人力成本与审核标准关联")
@Data

public class LaborCostAcceptanceAssessmentStandard extends  ObjectEntity  implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    @TableField(value = "acceptance_id")
    private String acceptanceId;

    /**
     * 审核标准Id
     */
    @ApiModelProperty(value = "审核标准Id")
    @TableField(value = "assessment_atandard_id")
    private String assessmentAtandardId;

}
