package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * DeliverGoals Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@TableName(value = "pms_deliver_goals")
@ApiModel(value = "DeliverGoalsEntity对象", description = "交付目标（ied）")
@Data
public class DeliverGoals extends ObjectEntity implements Serializable{

    /**
     * 计划提交时间
     */
    @ApiModelProperty(value = "计划提交时间")
    @TableField(value = "plan_submit_time")
    private Date planSubmitTime;

    /**
     * 编写人
     */
    @ApiModelProperty(value = "编写人")
    @TableField(value = "writer")
    private String writer;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "res_person")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private String type;

    /**
     * 文件状态
     */
    @ApiModelProperty(value = "文件状态")
    @TableField(value = "file_status")
    private String fileStatus;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "rev_id")
    private String revId;

    /**
     *  项目Id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;
}
