package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/18 19:12
 * @description:
 */
public enum ProjectDeclareFileTypeEnum {
    MEETING("meeting", "会议记录附件"),
    MATERIAL("material", "支持性材料"),
    RELATED("related", "相关附件"),
    ;

    private String code;

    private String description;

    ProjectDeclareFileTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
