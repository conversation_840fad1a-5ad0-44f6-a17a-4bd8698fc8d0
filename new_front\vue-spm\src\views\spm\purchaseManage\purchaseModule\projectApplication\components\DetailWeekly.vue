<script setup lang="ts">
import {
  ref, h, inject, computed,
} from 'vue';
import {
  BasicButton, OrionTable, BasicTableAction, openDrawer,
  BasicCard, isPower,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';

import { Modal } from 'ant-design-vue';

import WeeklyDrawer from './WeeklyDrawer.vue';

const tableRef = ref();
const loading = ref(false);

const basicInfo = inject('projectApplicationItem');
const columns = [

  {
    title: '时间',
    dataIndex: 'week',
    width: 120,
    customRender: ({ record }) => `${record.year}年第${record.week}周`,
  },
  {
    title: '上周工作安排',
    dataIndex: 'lastWorkPlan',
    width: 180,
  },
  {
    title: '上周工作完成情况',
    dataIndex: 'lastWorkContent',
    width: 180,
  },
  {
    title: '下周工作安排',
    dataIndex: 'nextWorkPlan',
    width: 180,
  },
  {
    title: '需技术部门领导关注内容',
    dataIndex: 'techLeaderAttentionContent',
    width: 180,
  },
  {
    title: '需商务部门领导关注内容',
    dataIndex: 'busLeaderAttentionContent',
    width: 180,
  },
  {
    title: '上周工作是否按计划完成',
    dataIndex: 'isLastComplete',
    width: 120,
    customRender: ({ record }) => (record.isLastComplete ? '是' : '否'),
  },
  {
    title: '预计下周是否能完成',
    dataIndex: 'isNextComplete',
    width: 120,
    customRender: ({ text }) => (text ? '是' : '否'),
  },
  {
    title: '是否已完成合同签章',
    dataIndex: 'isSign',
    width: 120,
    customRender: ({ text }) => (text ? '是' : '否'),
  },
  {
    title: '截至本周合同状态',
    dataIndex: 'contractStatus',
    width: 120,
  },
  {
    title: '截至本周已经耗时',
    dataIndex: 'alreadyTime',
    width: 120,
  },
  {
    title: '填写人',
    dataIndex: 'creatorName',
    width: 120,
  },
  {
    title: '填写时间',
    dataIndex: 'createTime',
    width: 160,
    customRender: ({ record }) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm') : ''),
  },
  {
    title: '操作',
    dataIndex: 'actions',
    width: 120,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
];
const actions = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => computed(() => isPower('PMS_CGLXXQXQ_container_02_03_button_01', record.rdAuthList)),
  },
  {
    text: '删除',
    event: 'delete',
    danger: true,
    isShow: (record) => computed(() => isPower('PMS_CGLXXQXQ_container_02_03_button_02', record.rdAuthList)),
  },
];
const tableOptions = {
  columns,
  rowKey: 'id',
  deleteToolButton: 'add|enable|disable|delete',
  smallSearchField: ['smallSearchField'],
  api: (params) => {
    loading.value = true;
    const query = {
      query: {
        lastWorkContent: keyword.value,
        purchReqDocCode: basicInfo?.value?.code || '',
      },
      dataSource: '0',
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10,
    };
    return new Api('/spm/purchProjectWeekly/page').fetch(query, '', 'POST').finally(() => {
      loading.value = false;
    });
  },
  pagination: true,

};

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      const drawerRef = ref();
      openDrawer({
        title: '编辑',
        width: 1000,
        content: () => h(WeeklyDrawer, {
          ref: drawerRef,
          record,
          isEdit: true,
          detailId: basicInfo?.value?.id,
        }),
        async onOk() {
          await drawerRef.value.onSubmit();
          tableRef.value?.reload();
        },
      });
      break;

    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteWeekly([record.id]),
      });
      break;
  }
}
function deleteWeekly(ids) {
  return new Promise((resolve) => {
    new Api('/spm/purchProjectWeekly').fetch(ids, 'remove', 'DELETE').then(() => {
      tableRef.value?.reload();
    }).finally(() => {
      resolve('');
    });
  });
}
const keyword = ref('');
function keywordSearch(val: string) {
  keyword.value = val;
  tableRef.value.reload();
}
</script>

<template>
  <BasicCard
    title="周报信息"
    :isBorder="false"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :loading="loading"
      @smallSearch="keywordSearch"
    >
      <template #actions="{ record }">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event, record)"
        />
      </template>
    </OrionTable>
  </BasicCard>
</template>

<style scoped lang="less">

</style>