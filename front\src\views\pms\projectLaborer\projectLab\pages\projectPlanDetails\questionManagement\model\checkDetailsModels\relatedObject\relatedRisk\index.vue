<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, h, reactive, toRefs,
} from 'vue';
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
export default defineComponent({
  name: 'Index',
  components: { OrionTable },
  props: {
    id: {},
  },
  emits: [],
  setup(props) {
    const state = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: false,
        showSmallSearch: false,
        showIndexColumn: false,
        smallSearchField: ['name'],
        pagination: false,
        // auto: {
        //   url: '/pas/question-type-attribute',
        //   params: {
        //     query: {
        //     },
        //   },
        // },
        // api:()=>new Api(`/pas/question-management/relation/plan/${props.id}`).fetch('','','POST'),
        api: (P) => new Api(`/pas/question-management/relation/risk/${props.id}`).fetch(P, '', 'POST'),
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            key: 'number',

            width: '120px',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            // customRender: function ({ record, text }) {
            //   return h(
            //       'span',
            //       {
            //         class: computed(()=>isPower('WT_container_button_03', state.powerData)) ?'action-btn':'',
            //         title: text,
            //         onClick: function (e) {
            //           if(isPower('WT_container_button_03', state.powerData)){
            //             checkData2(record)
            //           }
            //           e.stopPropagation();
            //         }
            //       },
            //       text
            //   );
            // },

            width: '240px',
            align: 'left',
            // slots: { customRender: 'name' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '风险类型',
            dataIndex: 'riskTypeName',
            key: 'riskType',
            width: '80px',
            margin: '0 20px 0 0',
            align: 'left',
            slots: { customRender: 'riskTypeName' },
            // sorter: true
            ellipsis: true,
          },
          {
            title: '发生概率',
            dataIndex: 'riskProbabilityName',
            key: 'riskProbabilityName',
            width: '100px',
            align: 'left',
            slots: { customRender: 'riskProbabilityName' },

            // sorter: true,
            ellipsis: true,
          },
          {
            title: '影响程度',
            dataIndex: 'riskInfluenceName',
            key: 'riskInfluenceName',

            width: '120px',
            align: 'left',
            slots: { customRender: 'riskInfluenceName' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '预估发生时间',
            dataIndex: 'predictStartTimeName',
            key: 'predictStartTimeName',
            width: '120px',
            align: 'left',
            slots: { customRender: 'predictStartTimeName' },
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '应对策略',
            dataIndex: 'copingStrategyName',
            key: 'copingStrategyName',

            width: '120px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            slots: { customRender: 'copingStrategyName' },
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            key: 'dataStatus',

            width: '120px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            key: 'principalName',

            width: '120px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            slots: { customRender: 'principalName' },
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',

            width: '170px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
            slots: { customRender: 'createTime' },
          },
        ],
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less"></style>
