CREATE TABLE IF NOT EXISTS `pmsx_person_job_post_Equ` (
                                            `id` varchar(64) NOT NULL  COMMENT '主键',
                                            `class_name` varchar(64)   COMMENT '创建人',
                                            `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                            `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                            `owner_id` varchar(64)   COMMENT '拥有者',
                                            `create_time` datetime NOT NULL  COMMENT '创建时间',
                                            `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                            `remark` varchar(1024)   COMMENT '备注',
                                            `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                            `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                            `status` int NOT NULL  COMMENT '状态',
                                            `logic_status` int   COMMENT '逻辑删除字段',
                                            `base_code` varchar(64)   COMMENT '基地编码',
                                            `base_name` varchar(64)   COMMENT '基地名称',
                                            `equivalent_date` datetime   COMMENT '等效认定时间',
                                            `user_code` varchar(64)   COMMENT '用户编号',
                                            `job_post_code` varchar(64)   COMMENT '岗位编号',
                                            `form_base_code` varchar(64)   COMMENT '被等效基地编号',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='人员岗位等效记录落地';



CREATE TABLE IF NOT EXISTS `pmsx_person_job_post_authorize` (
                                                  `id` varchar(64) NOT NULL  COMMENT '主键',
                                                  `class_name` varchar(64)   COMMENT '创建人',
                                                  `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                  `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                  `owner_id` varchar(64)   COMMENT '拥有者',
                                                  `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                  `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                  `remark` varchar(1024)   COMMENT '备注',
                                                  `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                  `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                  `status` int NOT NULL  COMMENT '状态',
                                                  `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                  `job_post_code` varchar(64)   COMMENT '岗位编号',
                                                  `job_post_name` varchar(128)   COMMENT '岗位名称',
                                                  `base_code` varchar(64)   COMMENT '基地编码',
                                                  `base_name` varchar(128)   COMMENT '基地名称',
                                                  `end_date` datetime   COMMENT '授权到期日期',
                                                  `authorize_status` int   COMMENT '授权状态',
                                                  `authorize_status_name` varchar(64)   COMMENT '授权状态（100-未授权，111-已授权）',
                                                  `is_equivalent` bit   COMMENT '是否等效',
                                                  `job_code` varchar(64)   COMMENT '作业编号',
                                                  `repair_round` varchar(64)   COMMENT '大修轮次',
                                                  `user_code` varchar(64)   COMMENT '人员编号',
                                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='人员岗位授权记录落地';


CREATE TABLE IF NOT EXISTS `pmsx_person_train_equ_record` (
                                                `id` varchar(64) NOT NULL  COMMENT '主键',
                                                `class_name` varchar(64)   COMMENT '创建人',
                                                `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                `owner_id` varchar(64)   COMMENT '拥有者',
                                                `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                `remark` varchar(1024)   COMMENT '备注',
                                                `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                `status` int NOT NULL  COMMENT '状态',
                                                `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                `equivalent_base_code` varchar(64)   COMMENT '等效基地编号',
                                                `equivalent_base_name` varchar(128)   COMMENT '等效基地名称',
                                                `equivalent_date` datetime   COMMENT '等效认定时间',
                                                `user_code` varchar(64)   COMMENT '人员编号',
                                                `train_number` varchar(64)   COMMENT '培训编号',
                                                PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='人员培训等效信息记录';



CREATE TABLE IF NOT EXISTS `pmsx_person_train_info_record` (
                                                 `id` varchar(64) NOT NULL  COMMENT '主键',
                                                 `class_name` varchar(64)   COMMENT '创建人',
                                                 `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                                 `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                                 `owner_id` varchar(64)   COMMENT '拥有者',
                                                 `create_time` datetime NOT NULL  COMMENT '创建时间',
                                                 `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                                 `remark` varchar(1024)   COMMENT '备注',
                                                 `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                                 `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                                 `status` int NOT NULL  COMMENT '状态',
                                                 `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                                 `train_number` varchar(64)   COMMENT '培训编码',
                                                 `train_name` varchar(64)   COMMENT '培训名称',
                                                 `base_name` varchar(128)   COMMENT '培训基地名称',
                                                 `base_code` varchar(64)   COMMENT '培训基地编码',
                                                 `lesson_hour` decimal   COMMENT '培训课时',
                                                 `end_date` datetime   COMMENT '完成时间',
                                                 `is_equivalent` bit   COMMENT '是否等效',
                                                 `expire_time` datetime   COMMENT '到期时间时间',
                                                 `train_lecturer` varchar(128)   COMMENT '培训讲师',
                                                 `content` text(0)   COMMENT '培训内容',
                                                 `user_code` varchar(64)   COMMENT '人员编号',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户培训信息落地';

DROP TABLE IF EXISTS `pas_relation_lead_base`;
CREATE TABLE `pas_relation_lead_base`  (
                                           `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                           `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                           `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                           `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                           `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                           `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                           `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                           `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                           `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                           `status` int(11) NOT NULL COMMENT '状态',
                                           `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                           `lead_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '线索ID',
                                           `base_place_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '基地code',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '线索基地关系表' ROW_FORMAT = Dynamic;



-- 生产
alter table pmsx_basic_user CHANGE join_word_time join_work_time datetime(0)  COMMENT '参加工作时间';

