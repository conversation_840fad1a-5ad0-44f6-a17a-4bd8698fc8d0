package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.IndicatorStatusEnum;
import com.chinasie.orion.constant.SchemeMilestoneTemplateStatusEnum;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneTemplateDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneTemplate;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneTemplateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.ProjectSchemeMilestoneTemplateRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeMilestoneTemplateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectSchemeMilestoneTemplate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
@Service
public class ProjectSchemeMilestoneTemplateServiceImpl extends OrionBaseServiceImpl<ProjectSchemeMilestoneTemplateRepository, ProjectSchemeMilestoneTemplate> implements ProjectSchemeMilestoneTemplateService {


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectSchemeMilestoneTemplateVO detail(String id) throws Exception {
        ProjectSchemeMilestoneTemplate projectSchemeMilestoneTemplate = this.getById(id);
        ProjectSchemeMilestoneTemplateVO result = BeanCopyUtils.convertTo(projectSchemeMilestoneTemplate, ProjectSchemeMilestoneTemplateVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectSchemeMilestoneTemplateDTO
     */
    @Override
    public ProjectSchemeMilestoneTemplateVO create(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        ProjectSchemeMilestoneTemplate projectSchemeMilestoneTemplate = BeanCopyUtils.convertTo(projectSchemeMilestoneTemplateDTO, ProjectSchemeMilestoneTemplate::new);
        nameRepeatHandle(projectSchemeMilestoneTemplate, CollUtil.toList());
        this.save(projectSchemeMilestoneTemplate);
        ProjectSchemeMilestoneTemplateVO rsp = BeanCopyUtils.convertTo(projectSchemeMilestoneTemplate, ProjectSchemeMilestoneTemplateVO::new);
        return rsp;
    }

    @Override
    public ProjectSchemeMilestoneTemplateVO saveNoExist(String templateName) throws Exception {
        LambdaQueryWrapperX<ProjectSchemeMilestoneTemplate> wrapper = new LambdaQueryWrapperX();
        wrapper.selectAll(ProjectSchemeMilestoneTemplate.class);
        wrapper.eq(ProjectSchemeMilestoneTemplate::getTemplateName, templateName);
        List<ProjectSchemeMilestoneTemplate> list = this.list(wrapper);
        ProjectSchemeMilestoneTemplate result = null;
        if (CollectionUtils.isBlank(list)) {
            ProjectSchemeMilestoneTemplate projectSchemeMilestoneTemplate = new ProjectSchemeMilestoneTemplate();
            projectSchemeMilestoneTemplate.setTemplateName(templateName);
            this.save(projectSchemeMilestoneTemplate);
            result = projectSchemeMilestoneTemplate;
        } else {
            result = list.get(0);
        }
        ProjectSchemeMilestoneTemplateVO rsp = BeanCopyUtils.convertTo(result, ProjectSchemeMilestoneTemplateVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectSchemeMilestoneTemplateDTO
     */
    @Override
    public Boolean edit(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        ProjectSchemeMilestoneTemplate projectSchemeMilestoneTemplate = BeanCopyUtils.convertTo(projectSchemeMilestoneTemplateDTO, ProjectSchemeMilestoneTemplate::new);
        LambdaQueryWrapperX<ProjectSchemeMilestoneTemplate> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(ProjectSchemeMilestoneTemplate.class);
        wrapper.eq(ProjectSchemeMilestoneTemplate::getTemplateName, projectSchemeMilestoneTemplate.getTemplateName());
        wrapper.ne(ProjectSchemeMilestoneTemplate::getId, projectSchemeMilestoneTemplateDTO.getId());
        List<ProjectSchemeMilestoneTemplate> list = this.list(wrapper);
        if (CollectionUtils.isBlank(list)) {
            if (list.size() > 0) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "里程碑模版名称已经存在");
            }
        }
        nameRepeatHandle(projectSchemeMilestoneTemplate, CollUtil.toList());
        Boolean result = this.updateById(projectSchemeMilestoneTemplate);
        return result;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        Boolean result = this.removeBatchByIds(ids);
        return result;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectSchemeMilestoneTemplateVO> pages(Page<ProjectSchemeMilestoneTemplateDTO> pageRequest) throws Exception {
        com.baomidou.mybatisplus.core.metadata.IPage<ProjectSchemeMilestoneTemplate> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());


        LambdaQueryWrapperX<ProjectSchemeMilestoneTemplate> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(ProjectSchemeMilestoneTemplate.class);
        wrapper.orderByAsc(ProjectSchemeMilestoneTemplate::getSort);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(),wrapper);


        com.baomidou.mybatisplus.core.metadata.IPage<ProjectSchemeMilestoneTemplate> page = this.page(realPageRequest, wrapper);

        Page<ProjectSchemeMilestoneTemplateVO> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ProjectSchemeMilestoneTemplateVO> vos = BeanCopyUtils.convertListTo(page.getRecords(), ProjectSchemeMilestoneTemplateVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }


    /**
     * 列表
     * <p>
     * * @param projectSchemeMilestoneTemplateDTO
     */
    @Override
    public List<ProjectSchemeMilestoneTemplateVO> lists(ProjectSchemeMilestoneTemplateDTO projectSchemeMilestoneTemplateDTO) throws Exception {
        ProjectSchemeMilestoneTemplate projectSchemeMilestoneTemplate = BeanCopyUtils.convertTo(projectSchemeMilestoneTemplateDTO, ProjectSchemeMilestoneTemplate::new);
        if (projectSchemeMilestoneTemplate == null) {
            projectSchemeMilestoneTemplate = new ProjectSchemeMilestoneTemplate();
        }
        LambdaQueryWrapperX<ProjectSchemeMilestoneTemplate> lambdaQueryWrapperX =  new LambdaQueryWrapperX<>(ProjectSchemeMilestoneTemplate.class);
        if(ObjectUtil.isNotEmpty(projectSchemeMilestoneTemplateDTO.getStatus())){
            lambdaQueryWrapperX.eq(ProjectSchemeMilestoneTemplate::getStatus,projectSchemeMilestoneTemplateDTO.getStatus());
        }
        lambdaQueryWrapperX.orderByAsc(ProjectSchemeMilestoneTemplate::getSort);
        List<ProjectSchemeMilestoneTemplate> list = this.list(lambdaQueryWrapperX);

        List<ProjectSchemeMilestoneTemplateVO> vos = BeanCopyUtils.convertListTo(list, ProjectSchemeMilestoneTemplateVO::new);
        return vos;
    }

    @Override
    @Transactional
    public Boolean enable(String id){
        LambdaUpdateWrapper<ProjectSchemeMilestoneTemplate> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ProjectSchemeMilestoneTemplate::getStatus, SchemeMilestoneTemplateStatusEnum.ENABLE.getValue());
        updateWrapper.eq(ProjectSchemeMilestoneTemplate::getId, id);
        return this.update(updateWrapper);
    }

    @Override
    @Transactional
    public Boolean disEnable(String id){
        LambdaUpdateWrapper<ProjectSchemeMilestoneTemplate> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ProjectSchemeMilestoneTemplate::getStatus, SchemeMilestoneTemplateStatusEnum.DISENABLE.getValue());
        updateWrapper.eq(ProjectSchemeMilestoneTemplate::getId, id);
        return this.update(updateWrapper);
    }

    private void nameRepeatHandle(ProjectSchemeMilestoneTemplate node, List<String> names) {
        List<ProjectSchemeMilestoneTemplate> templateList = CollUtil.toList();
        try {
            templateList = list();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollUtil.isEmpty(templateList)) {
            return;
        }
        int num = 0;
        List<ProjectSchemeMilestoneTemplate> repeatNames = templateList.stream().filter(Objects::nonNull)
                .filter(item -> {
                    if (StrUtil.isNotBlank(item.getTemplateName()) && StrUtil.contains(item.getTemplateName(), "(")) {
                        return item.getTemplateName().substring(0, item.getTemplateName().lastIndexOf("(")).equals(node.getTemplateName());
                    }
                    return node.getTemplateName().equals(item.getTemplateName());
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(repeatNames)) {
            num += repeatNames.size();
        }
        if (names.contains(node.getTemplateName())) {
            num += 1;
        } else {
            names.add(node.getTemplateName());
        }
        if (num != 0) {
            node.setTemplateName(node.getTemplateName() + "(" + num + ")");
        }
    }
}
