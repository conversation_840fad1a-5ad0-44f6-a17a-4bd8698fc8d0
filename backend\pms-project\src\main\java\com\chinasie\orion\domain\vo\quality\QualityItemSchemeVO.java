package com.chinasie.orion.domain.vo.quality;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * QualityItemScheme VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:47
 */
@ApiModel(value = "QualityItemSchemeVO对象", description = "质量管控项和计划关联关系")
@Data
public class QualityItemSchemeVO extends ObjectVO implements Serializable {

    /**
     * 管控项id
     */
    @ApiModelProperty(value = "管控项id")
    private String qualityItemId;


    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private String projectSchemeId;


}
