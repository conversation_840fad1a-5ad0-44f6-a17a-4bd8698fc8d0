<template>
  <Card
    content-title="常用模板"
    class="templates-wrap"
  >
    <ul class="list-wrap flex flex-bm">
      <li
        v-for="(item, index) in templateList"
        :key="index"
        class="flex-te"
        :title="item?.name + item?.filePostfix"
      >
        <i :class="filterFileIcon(item?.filePostfix)" />
        <span>{{ item?.name + item?.filePostfix }}</span>
      </li>
    </ul>
  </Card>
</template>

<script lang="ts">
import {
  defineComponent, reactive, onMounted, toRefs,
} from 'vue';
import Card from './Card.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'CommonTemplates',
  components: {
    Card,
  },
  setup() {
    const state = reactive({
      templateList: [],
    });
    function loadList() {
      // 常用模版
      new Api('/home').fetch({}, 'home/newTop', 'POST').then((res) => {
        state.templateList = res.length > 12 ? res.slice(0, 12) : res;
      });
    }

    onMounted(() => {
      loadList();
    });

    return {
      ...toRefs(state),
      filterFileIcon(filePostfix) {
        if (filePostfix === '.doc' || filePostfix === '.docx') {
          return 'word';
        } if (filePostfix === '.xls' || filePostfix === '.xlsx') {
          return 'excel';
        } if (filePostfix === '.ppt' || filePostfix === '.pptx') {
          return 'ppt';
        } if (filePostfix === '.pdf') {
          return 'pdf';
        } if (
          filePostfix === '.jpg'
            || filePostfix === '.jpeg'
            || filePostfix === '.gif'
            || filePostfix === '.png'
            || filePostfix === '.bmp'
        ) {
          return 'picture';
        }
        return 'other';
      },
    };
  },
});
</script>

<style scoped lang="less">
  .templates-wrap {
    height: 210px;
    margin-top: 16px;
  }

  .list-wrap {
    padding-top: 6px;
    > li {
      width: 50%;
      padding-right: 20px;
      padding-left: 30px;
      position: relative;
      line-height: 40px;

      > span {
        cursor: pointer;

        &:hover {
          color: ~`getPrefixVar('primary-color')`;
        }
      }

      > i {
        width: 24px;
        height: 24px;
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -12px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 16px;

        //&.excel {
        //  background-image: url(../img/filesicon/excel.png);
        //}
        //
        //&.other {
        //  background-image: url(../img/filesicon/other.png);
        //}
        //
        //&.pdf {
        //  background-image: url(/src/assets/images/filesicon/pdf.png);
        //  background-color: #000;
        //}
        //
        //&.picture {
        //  background-image: url(../img/filesicon/picture.png);
        //}
        //
        //&.ppt {
        //  background-image: url(../img/filesicon/ppt.png);
        //}
        //
        //&.word {
        //  background-image: url(../img/filesicon/word.png);
        //}
      }
    }
  }
</style>
