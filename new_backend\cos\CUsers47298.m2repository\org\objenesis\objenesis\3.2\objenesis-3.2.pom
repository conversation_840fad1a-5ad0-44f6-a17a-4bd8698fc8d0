<?xml version="1.0" encoding="ISO-8859-1"?>
<!--

    Copyright 2006-2021 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.objenesis</groupId>
    <artifactId>objenesis-parent</artifactId>
    <version>3.2</version>
  </parent>
  <artifactId>objenesis</artifactId>

  <name>Objenesis</name>
  <description>A library for instantiating Java objects</description>

  <dependencies>
    <dependency>
      <groupId>org.objenesis</groupId>
      <artifactId>objenesis-test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            <manifestEntries>
              <Automatic-Module-Name>org.objenesis</Automatic-Module-Name>
              <Dependencies>jdk.unsupported</Dependencies>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.keyboardsamurais.maven</groupId>
        <artifactId>maven-timestamp-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-remote-resources-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Import-Package>
            COM.newmonics.PercClassLoader;resolution:=optional,
            sun.misc;resolution:=optional,
            sun.reflect;resolution:=optional
            </Import-Package>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
