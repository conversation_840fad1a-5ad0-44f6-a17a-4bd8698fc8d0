<template>
  <div class="header">
    <slot name="header">
      <div class="header-wrap flex flex-pac">
        <div class="project-icon">
          <Icon
            color="#3d7dce"
            size="30"
            icon="fa-cubes"
          />
        </div>
        <slot name="header-title">
          <div class="project-title">
            <slot name="name">
              <h2 class="flex-te">
                {{ projectData?.name }}
              </h2>
            </slot>
            <slot name="code">
              <p class="flex-te">
                {{ projectData?.projectCode }}
              </p>
            </slot>
          </div>
        </slot>
        <slot name="header-info">
          <div class="info">
            <div>
              状态：<DataStatusTag
                v-if="projectData?.dataStatus"
                :status-data="projectData.dataStatus"
              /><ProjectStatusTag
                v-else
                :status="projectData?.status"
              />
            </div>
            <div v-if="isHeaderSecretLevel">
              密级：{{ projectData?.secretLevelName }}
            </div>
            <div>所有者：{{ projectData?.ownerName }}</div>
            <div>
              修改时间：{{
                projectData?.modifyTime ? dayjs(projectData.modifyTime).format('YYYY-MM-DD') : '-'
              }}
            </div>
          </div>
        </slot>
        <div class="project-more">
          <slot name="header-more" />
        </div>
        <div class="mr20">
          <slot name="header-right" />
        </div>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
// import { Icon } from '/@/components/Icon';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, DataStatusTag, Icon,
} from 'lyra-component-vue3';
// import { DataStatusTag } from '/@/components/StatusTag';
import dayjs from 'dayjs';
import ProjectStatusTag from './ProjectStatusTag.vue';
export default defineComponent({
  name: 'ProjectLayoutHeader',
  components: {
    Icon,
    ProjectStatusTag,
    DataStatusTag,
  },
  props: {
    projectData: {
      type: Object,
      default: () => {},
    },
    isHeaderSecretLevel: {
      type: Boolean,
      default: true,
    },
  },
  setup() {
    return {
      dayjs,
    };
  },
});
</script>

<style lang="less" scoped>
  .header {
    border-bottom: 1px solid #e9ecf2;
  }
  .header-wrap {
    height: 78px;

    > .project-icon {
      margin: 0 10px 0 20px;
    }

    > .project-title {
      min-width: 100px;
      margin-right: 30px;

      > * {
        margin: 0;
      }

      > h2 {
        font-size: 18px;
        font-weight: bolder;
      }

      > p {
        font-size: 14px;
        font-weight: normal;
      }
    }

    > .info {
      width: 420px;
      display: flex;
      flex-wrap: wrap;

      > div {
        width: 50%;
      }
    }

    > .project-more {
      flex: 1;
    }
  }
</style>
