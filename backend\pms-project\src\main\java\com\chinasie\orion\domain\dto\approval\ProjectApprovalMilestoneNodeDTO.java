package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.tree.OrionTreeNodeDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalMilestoneNode DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:59
 */
@ApiModel(value = "ProjectApprovalMilestoneNodeDTO对象", description = "项目立项里程碑节点")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalMilestoneNodeDTO extends  OrionTreeNodeDTO   implements Serializable{

/**
 * 模板id
 */
@ApiModelProperty(value = "模板id")
@ExcelProperty(value = "模板id ", index = 0)
private String templateId;

/**
 * 描述
 */
@ApiModelProperty(value = "描述")
@ExcelProperty(value = "描述 ", index = 1)
private String description;

/**
 * 父级链
 */
@ApiModelProperty(value = "父级链")
@ExcelProperty(value = "父级链 ", index = 2)
private String nodeChain;

/**
 * 节点类型
 */
@ApiModelProperty(value = "节点类型")
@ExcelProperty(value = "节点类型 ", index = 3)
private String nodeType;

/**
 * 责任部门id
 */
@ApiModelProperty(value = "责任部门id")
@ExcelProperty(value = "责任部门id ", index = 4)
private String rspDeptId;

/**
 * 计划工期
 */
@ApiModelProperty(value = "计划工期")
@ExcelProperty(value = "计划工期 ", index = 5)
private Integer durationDays;

/**
 * 项目启动后n天开始
 */
@ApiModelProperty(value = "项目启动后n天开始")
@ExcelProperty(value = "项目启动后n天开始 ", index = 6)
private Integer delayDays;

/**
 * 是否关联流程
 */
@ApiModelProperty(value = "是否关联流程")
@ExcelProperty(value = "是否关联流程 ", index = 7)
private Boolean processFlag;

/**
 * 编码
 */
@ApiModelProperty(value = "编码")
@ExcelProperty(value = "编码 ", index = 8)
private String number;




}
