package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectSetUpDTO;
import com.chinasie.orion.domain.vo.ProjectSetUpVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectSetUpService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * ProjectSetUp 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
@RestController
@RequestMapping("/projectSetUp")
@Api(tags = "项目设置")
public class ProjectSetUpController {

    @Autowired
    private ProjectSetUpService projectSetUpService;


    /**
     * 详情
     *
     * @param projectId
     * @param key
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{projectId}/{key}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目设置", subType = "详情", bizNo = "{{#projectId}}")
    public ResponseDTO<ProjectSetUpVO> detail(@PathVariable(value = "projectId") String projectId, @PathVariable(value = "key") String key) throws Exception {
        ProjectSetUpVO rsp = projectSetUpService.detail(projectId, key);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectSetUpDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目设置", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectSetUpDTO projectSetUpDTO) throws Exception {
        Boolean rsp = projectSetUpService.edit(projectSetUpDTO);
        return new ResponseDTO<>(rsp);
    }
}
