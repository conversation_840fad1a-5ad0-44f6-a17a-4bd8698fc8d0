import type { RouteRecordRaw, RouteLocation } from 'vue-router';
import { RoleEnum } from '/@/enums/roleEnum';

import { defineComponent } from 'vue';

export type Component<T extends any = any> =
  | ReturnType<typeof defineComponent>
  | (() => Promise<typeof import('*.vue')>)
  | (() => Promise<T>);

export interface RouteMeta {
  id: string;
  // title
  title: string;
  // Whether to ignore permissions
  ignoreAuth?: boolean;
  // role info
  roles?: RoleEnum[];
  // Whether not to cache
  ignoreKeepAlive?: boolean;
  // Is it fixed on tab
  affix?: boolean;
  // icon on tab
  icon?: string;

  // 网址展示， 可以与 isLink 进行配合
  iframeUrl?: string;

  // current page transition
  transitionName?: string;

  // Whether the route has been dynamically added
  hideBreadcrumb?: boolean;

  // Hide submenu
  hideChildrenInMenu?: boolean;

  // Carrying parameters
  carryParam?: boolean;

  // Used internally to mark single-level menus
  single?: boolean;

  // Currently active menu
  currentActiveMenu?: string;

  // Never show in tab
  hideTab?: boolean;

  // Never show in menu
  hideMenu?: boolean;

  // 是否链接, false 在内部iframe展示, true 新页面展示
  isLink?: boolean;

  // 子系统名
  microWebName: string,

  // 页面缓存key
  keepAliveKey?: string
}

// @ts-ignore
export interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'meta'> {
  name: string;
  meta: RouteMeta;
  component?: Component | string;
  components?: Component;
  children?: AppRouteRecordRaw[];
  props?: Recordable;
  fullPath?: string;
}
export interface MenuTag {
  type?: 'primary' | 'error' | 'warn' | 'success';
  content?: string;
  dot?: boolean;
}

export interface Menu {
  id?: string;
  name?: string;

  icon?: string;

  path?: string;

  disabled?: boolean;

  children?: Menu[];

  orderNo?: number;

  roles?: RoleEnum[];

  meta?: Partial<RouteMeta>;

  tag?: MenuTag;

  hideMenu?: boolean;

  description?: string;

  component?: string;

  parentId?:string;

  isLeaf?: boolean;

  sort?: number;

  remark?: string;

  label?: string;

  status?: number;

  chain?: string;

  layer?: string;

  parentIds?: string;

  pageId?: string;

  pageCode?: string;

  pageName?: string;

  ignoreKeepAlive?: boolean;

  isPageTitle?: boolean
}

export interface MenuModule {
  orderNo?: number;
  menu: Menu;
}

// 路由信息
export interface RouteItem extends Omit<RouteLocation, 'meta'> {
  meta:RouteMeta
}

// export type AppRouteModule = RouteModule | AppRouteRecordRaw;
export type AppRouteModule = AppRouteRecordRaw;
