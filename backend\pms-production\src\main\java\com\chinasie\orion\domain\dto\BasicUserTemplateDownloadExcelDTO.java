package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasicUserTemplateDownloadExcelDTO implements Serializable {

    @ExcelProperty("*身份证号")
    private String idCard;

    @ExcelProperty("*姓名")
    private String fullName;

    @ExcelProperty("*公司")
    private String companyName;

    private String companyCode;

    @ExcelProperty("*性别")
    private String sex;

    @ExcelProperty("*民族")
    private String nation;

    @ExcelProperty("*出生日期")
    private String dateOfBirth;

    @ExcelProperty("政治面貌")
    private String politicalAffiliation;

    @ExcelProperty("参加工作时间")
    private String joinWorkTime;

    @ExcelProperty("籍贯")
    private String homeTown;

    @ExcelProperty("出生地")
    private String birthPlace;

    @ExcelProperty("加入本单位时间")
    private String addUnitTime;

    @ExcelProperty("*用人部门")
    private String departmentName;

    private String departmentCode;

    private String departmentId;

    @ExcelProperty("研究所")
    private String instituteName;

    private String instituteCode;

    @ExcelProperty("联系电话")
    private String phone;

    @ExcelProperty("*到岗时间")
    private String addWorkTime;
}
