<template>
  <div class="planDetailForm">
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <a-form-item label="计划名称">
            {{ formData.name }}
          </a-form-item>
          <a-form-item label="计划编号">
            {{ formData.number }}
          </a-form-item>
          <a-form-item label="所属项目">
            {{ formData.projectName }}
          </a-form-item>
          <a-form-item label="所属类型">
            {{ formData.planTypeName }}
          </a-form-item>
          <a-form-item label="管理节点">
            {{ formData.manageNode }}
          </a-form-item>
          <a-form-item label="风险项">
            {{ formData.riskItem }}
          </a-form-item>
          <a-form-item label="责任单位">
            {{ formData.resOrgName }}
          </a-form-item>
          <a-form-item label="责任科室">
            {{ formData.resDeptName }}
          </a-form-item>
          <a-form-item label="责任人">
            {{ formData.resPersonName }}
          </a-form-item>
          <a-form-item label="参与单位">
            {{ Array.isArray(formData.joinOrgsName) ? text.join(',') : '' }}
          </a-form-item>
          <a-form-item label="参与科室">
            {{ formData.resDeptName }}
          </a-form-item>
          <a-form-item label="参与者">
            {{ Array.isArray(formData.participantName) ?formData.participantName.join(';'):'' }}
          </a-form-item>
          <a-form-item label="优先级">
            {{ formData.priorityLevelName }}
          </a-form-item>
          <a-form-item label="开始日期">
            {{ formatDate(formData.startTime) }}
          </a-form-item>
          <a-form-item label="结束日期">
            {{ formatDate(formData.endTime) }}
          </a-form-item>
          <a-form-item label="预估工时">
            {{ formData.manHour }}
          </a-form-item>
          <a-form-item label="状态">
            {{ formData.statusName }}
          </a-form-item>
          <a-form-item label="描述">
            {{ formData.remark }}
          </a-form-item>
          <a-form-item label="修改人">
            {{ formData.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ formatDate(formData.modifyTime) }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ formData.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ formatDate(formData.createTime) }}
          </a-form-item>
        </BasicTitle>
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="计划属性">
          计划属性
        </BasicTitle>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { Row, Col, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import dayjs from 'dayjs';
import {
  inject, reactive, toRefs, watch,
} from 'vue';
export default {
  name: 'View',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
  },
  setup(props) {
    const formData1 = inject('formData', {});
    const state = reactive({
      // father: props.data,
      formData: formData1.value || {},
    });
    watch(
      () => formData1?.value,
      (val) => {
        console.log(val);
        state.formData = val;
      },
    );
    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }

    return {
      ...toRefs(state),
      formatDate,
    };
  },
};
</script>

<style scoped lang="less">
.planDetailForm{
  padding: 10px;
  height: 100%;

  :deep(>.ant-row) {
    height: 100%;

    >.ant-col {
      height: 100%;
    }
  }
}
:deep(.basicTitle) {
  height: 100%;
  display: flex;
  flex-direction: column;
  .basicTitle_content {
    padding-left: 40px !important;
    flex:1;
    //height: calc(100vh - 300px);
    overflow: auto;
  }
  .ant-form-item {
    margin-bottom: 10px;
  }
}
</style>
