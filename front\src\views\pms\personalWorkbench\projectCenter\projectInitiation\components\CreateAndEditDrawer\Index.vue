<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  onUnmounted, provide, readonly, ref, Ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { message, Spin } from 'ant-design-vue';
import FormMain from './FormMain.vue';
import Api from '/@/api';

const emits = defineEmits<{
  (e: 'confirmCallback', result: any): void
}>();

/**
 * @param openProps
 * @param openProps.operationType   值为:fixed 代表此次打开抽屉操作携带部分固定参数，不可更改
 */
const [register, { setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner(async (openProps) => {
  detail.value = openProps;
  if (openProps.id) {
    setDrawerProps({
      title: '编辑立项',
    });
    await getDetail();
  } else {
    setDrawerProps({
      title: '创建立项',
    });
  }
  if (openProps?.standProjectId) {
    await new Api('/pms/projectApproval/getProjectById').fetch({ id: openProps?.standProjectId }, '', 'GET').then((res) => {
      detail.value = {
        ...res,
        projectNumber: res?.number,
        projectName: res?.name,
        type: openProps.type,
        standProjectId: openProps.standProjectId,
      } || {};
    });
  }
  setDrawerProps({
    showFooter: true,
  });
  visibleDrawer.value = true;
});
const route = useRoute();
const router = useRouter();
const formRef: Ref = ref();
const detail: Ref = ref();
provide('detail', readonly(detail));
const loading: Ref<boolean> = ref(false);
const visibleDrawer: Ref<boolean> = ref(false);

async function getDetail() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectApproval').fetch('', detail.value.id, 'GET');
    // 获取附件
    const query = {
      dataId: detail.value.id,
      dataType: 'ProjectApprovalFile',
    };
    const materialList = await new Api('/res/manage/file/new/getfilelist').fetch(query, '', 'GET');
    result.materialList = materialList;
    detail.value = result || {};
  } finally {
    loading.value = false;
  }
}

function visibleChange(visible: boolean) {
  if (!visible) {
    visibleDrawer.value = visible;
    setDrawerProps({
      showFooter: visible,
    });
  }
}

async function onOk() {
  const formValues = await formRef.value.validate();
  let params;
  if (detail.value?.type === 'goDetails') {
    params = {
      ...formValues,
      projectId: detail.value.id, // 项目id standProjectId
      attachments: formRef.value.getTableData(), // 文件
      estimateAmt: formValues.estimateMoney, // 金额
      resPerson: formRef.value.selectUser?.id, // 责任人
      createTime: formValues.createTime ? dayjs(formValues.createTime).format('YYYY-MM-DD HH:mm:ss') : '',
      projectStartTime: formValues.projectStartTime ? dayjs(formValues.projectStartTime).format('YYYY-MM-DD') : '',
      projectEndTime: formValues.projectEndTime ? dayjs(formValues.projectEndTime).format('YYYY-MM-DD') : '',
    };
    delete params.resPersonName;
    detail.value.id = undefined;
  } else {
    params = {
      ...formValues,
      id: detail.value.id, // 当前项id
      // fileInfoDTOList: formRef.value.getTableData(),
      attachments: formRef.value.getTableData(), // 文件
      projectId: formRef.value.selectProjectId, // 项目id
      // resUserId: formRef.value.selectUser?.id,
      estimateAmt: formValues.estimateMoney, // 金额
      resPerson: formRef.value.selectUser?.id, // 责任人
      createTime: formValues.createTime ? dayjs(formValues.createTime).format('YYYY-MM-DD HH:mm:ss') : '',
      projectStartTime: formValues.projectStartTime ? dayjs(formValues.projectStartTime).format('YYYY-MM-DD') : '',
      projectEndTime: formValues.projectEndTime ? dayjs(formValues.projectEndTime).format('YYYY-MM-DD') : '',
    };
    delete params.resPersonName;
  }
  changeOkLoading(true);
  try {
    const result = await new Api('/pms/projectApproval').fetch(params, '', detail.value.id ? 'PUT' : 'POST');
    message.success('操作完成');
    closeDrawer();
    emits('confirmCallback', result);
    // 如果是项目内新增则成功新增后跳转到详情
    if (detail.value?.type === 'goDetails') {
      router.push({
        name: 'ProjectInitiationDetail',
        params: {
          id: result.id,
        },
        query: {
          projectId: result.projectId,
        },
      });
    }
  } finally {
    changeOkLoading(false);
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    width="800px"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>

    <FormMain
      v-else-if="visibleDrawer"
      ref="formRef"
      :detail="detail"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
