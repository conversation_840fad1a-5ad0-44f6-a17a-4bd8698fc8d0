INSERT INTO msc_business_node
(id, name, code, title_template_id, template_id, `type`, send_method, show_method, open_method, node_variables, classify_id, status, remark, creator_id, create_time, modify_id, modify_time, platform_id, org_id, unique_key, logic_status, share, build_in)
VALUES('0a0y1904121713113210880', '市场经营需求分发确认（商务负责人）', 'NODE_REQUIREMENT_CONFIRM', 'vub01848901829504131072', 'vub01831656513515290624', 1, 'SYS', '1,2', '1', NULL, 'u0rn1820705362382827520', 1, '1', '314j1000000000000000000', '2025-03-24 18:42:48', '314j1000000000000000000', '2025-03-25 10:09:26', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1789200490531569664', NULL, 1, 1, 1);
UPDATE msc_business_node
SET name='市场经营需求分发',  title_template_id='vub01848901829504131072', template_id='vub01848902545962557440', `type`=0, send_method='SYS', show_method='1,2', open_method='1', node_variables=NULL, classify_id='u0rn1820705362382827520', status=1, remark=NULL, creator_id='314j1000000000000000000', create_time='2024-10-23 09:51:52', modify_id='314j1000000000000000000', modify_time='2025-03-25 10:51:32',  unique_key=NULL, logic_status=1, share=1, build_in=1
WHERE id='0a0y1848905145265020928';
