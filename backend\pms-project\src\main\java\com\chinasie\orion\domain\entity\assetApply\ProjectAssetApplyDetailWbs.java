package com.chinasie.orion.domain.entity.assetApply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectAssetApplyDetailWbs Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:30:30
 */
@TableName(value = "pmsx_project_asset_apply_detail_wbs")
@ApiModel(value = "ProjectAssetApplyDetailWbsEntity对象", description = "资产转固申请详情表-WBS")
@Data

public class ProjectAssetApplyDetailWbs extends  ObjectEntity  implements Serializable{

    /**
     * WBS预算数据信息Id
     */
    @ApiModelProperty(value = "WBS预算数据信息Id")
    @TableField(value = "wbs_id")
    private String wbsId;

    /**
     * 转固主表id
     */
    @ApiModelProperty(value = "转固主表id")
    @TableField(value = "asset_apply_id")
    private String assetApplyId;

}
