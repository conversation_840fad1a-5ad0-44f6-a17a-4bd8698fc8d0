package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: yk
 * @date: 2023/10/19 15:41
 * @description:
 */
@Data
@ApiModel(value = "ProjectUpdateStatusDTO对象", description = "更新项目状态")
public class ProjectUpdateStatusDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @NotNull(message = "项目id不能为空")
    private Integer status;
}
