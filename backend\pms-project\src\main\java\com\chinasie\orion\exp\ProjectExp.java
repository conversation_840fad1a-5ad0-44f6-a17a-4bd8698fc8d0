package com.chinasie.orion.exp;

import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/26/14:10
 * @description:
 */
@Component
@Slf4j
public class ProjectExp implements IExp {

    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "项目立项权限-当前人";
    }

    @Override
    public List<String> exp(String s) {
        log.info("查找当前人及其下属机构");
        String userId = CurrentUserHelper.getCurrentUserId();
        UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), userId);
        List<DeptVO> organizations = userVO.getOrganizations();
        List<String> leaderDeptIds = organizations.stream().map(DeptVO::getId).distinct().collect(Collectors.toList());
        Map<String, DeptVO> deptVOMap = deptRedisHelper.mapAllDept();

        List<DeptVO> deptVOS = Lists.newArrayList();

        deptVOMap.forEach((k, vo) -> deptVOS.add(vo));

        Map<String, List<DeptVO>> parentDeptVoMap = deptVOS.stream().collect(Collectors.groupingBy(DeptVO::getParentId));

        List<String> deptIds = Lists.newArrayList();
        leaderDeptIds.forEach(parentId->{
            findSubDept(parentId,deptIds, parentDeptVoMap, deptVOMap);
        });
        if (CollectionUtils.isEmpty(deptIds)) {
            log.info("当前领导用户的机构为空");
            return Arrays.asList(s);
        }
        return deptIds;
    }

    /**
     * 查找下级
     *
     * @param parentId
     * @param deptIds
     * @param parentDeptVoMap
     * @param deptVOMap
     */
    private void findSubDept(String parentId, List<String> deptIds, Map<String, List<DeptVO>> parentDeptVoMap, Map<String, DeptVO> deptVOMap) {
        if (deptVOMap.containsKey(parentId)) {
            DeptVO deptVO = deptVOMap.get(parentId);
            deptIds.add(deptVO.getId());
            if (parentDeptVoMap.containsKey(parentId)) {
                List<DeptVO> deptVOS = parentDeptVoMap.get(parentId);
                List<String> tempDeptIds = deptVOS.stream().map(DeptVO::getId).collect(Collectors.toList());
                tempDeptIds.forEach(tempDeptId -> {
                    findSubDept(tempDeptId, deptIds, parentDeptVoMap, deptVOMap);
                });
            }
        }

    }

    @Override
    public Boolean apply() {
        return Boolean.TRUE;
    }
}
