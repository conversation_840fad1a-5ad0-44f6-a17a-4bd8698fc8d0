package com.chinasie.orion.init;

import com.chinasie.orion.init.service.ProjectLifeCycleTenantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * @Description: 多租户初始化数据项目管理生命周期
 * @author:
 * @date: 2024/3/6 15:35
 * 版权：SIE
 */
@Component
public class ProjectLifeCycleTenantInitializerRunner implements CommandLineRunner {
    private Logger logger = LoggerFactory.getLogger(ProjectLifeCycleTenantInitializerRunner.class);

    @Autowired
    private ProjectLifeCycleTenantService projectLifeCycleTenantService;
    @Override
    public void run(String... args) throws Exception {
        logger.info("多租户初始化数据项目管理生命周期开始！");
//        projectLifeCycleTenantService.projectLifeCycleTenantInit();
        logger.info("多租户初始化数据项目管理生命周期结束！");

    }
}
