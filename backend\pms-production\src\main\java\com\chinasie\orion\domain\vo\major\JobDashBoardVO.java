package com.chinasie.orion.domain.vo.major;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/19:08
 * @description:
 */
@Data
@Builder
public class JobDashBoardVO implements Serializable {

    @ApiModelProperty(value = "普通作业风险统计")
    private JobRiskCountVO jobRiskCountVO;
    @ApiModelProperty(value = "重大项目统计")
    private ImportantJobCountVO importantJobCountVO;
}
