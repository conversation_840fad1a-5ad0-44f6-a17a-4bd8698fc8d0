<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, OrionTable, UploadList, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';

const props = defineProps<{
  record: any
}>();

const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
  maxHeight: 300,
};

const customRow = (record: Record<string, any>) => ({
  onClick() {
    securityMeasureVOList.value = record?.securityMeasureVOList || [];
  },
});
const securityMeasureVOList: Ref<any[]> = ref([]);

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '风险措施信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  // {
  //   field: 'heightRisk',
  //   component: 'SelectDictVal',
  //   label: '高风险',
  //   componentProps: {
  //     disabled: true,
  //     dictNumber: 'pms_height_level',
  //   },
  // },
  {
    field: 'firstExecute',
    component: 'SelectDictVal',
    label: '首次执行',
    componentProps: {
      disabled: true,
      dictNumber: 'pms_first_execute',
    },
  },
  {
    field: 'newParticipants',
    component: 'Select',
    label: '新人参与',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'importantProject',
    component: 'SelectDictVal',
    label: '重要项目',
    componentProps: {
      disabled: true,
      dictNumber: 'pms_important_project',
    },
  },
  {
    field: 'rspUserName',
    component: 'Input',
    label: '项目/工作负责人',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'supervisoryStaffName',
    component: 'Input',
    label: '监督人员',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'managePersonName',
    component: 'Input',
    label: '管理人员',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'riskLevel',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    colSlot: 'riskLevel',
  },
  {
    field: 'riskVOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '风险信息',
        isSpacing: false,
        isBorder: false,
      }, h('div', null, h(OrionTable, {
        options: tableOptions,
        key: field,
        dataSource: model[field] || [],
        customRow,
        columns: [
          {
            title: '功能位置',
            dataIndex: 'functionalLocation',
          },
          {
            title: '风险号',
            dataIndex: 'riskNumber',
          },
          {
            title: '风险描述',
            dataIndex: 'riskDesc',
          },
          {
            title: '风险类型',
            dataIndex: 'riskType',
          },
          {
            title: '风险长文本',
            dataIndex: 'riskText',
          },
        ],
      })));
    },
  },
  {
    field: 'securityMeasureVOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '安措信息',
        isSpacing: false,
        isBorder: false,
      }, h('div', null, h(OrionTable, {
        options: tableOptions,
        dataSource: securityMeasureVOList.value || [],
        columns: [
          {
            title: '安全措施',
            dataIndex: 'measureCode',
          },
          {
            title: '安全措施描述',
            dataIndex: 'measureDesc',
          },
          {
            title: '措施类型',
            dataIndex: 'measureType',
          },
          {
            title: '措施长文本',
            dataIndex: 'measureText',
          },
        ],
      })));
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    colSlot: 'fileDTOList',
  },
];

const riskTableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  isSpacing: false,
  pagination: false,
  maxHeight: 300,
  api: () => new Api('/pms/jobHeightRisk/list').fetch({
    query: {
      jobNumber: props.record?.number,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '风险级别',
      dataIndex: 'riskLevel',
      width: '20%',
    },
    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      width: '30%',
    },
    {
      title: '判断标准',
      dataIndex: 'judgmentStandards',
    },
  ],
};

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.jobId && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage/risk/measure/detail').fetch('', props?.record?.jobId, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList?.map((item) => {
        delete item?.children;
        return item;
      }) || [],
    });
    securityMeasureVOList.value = result?.riskVOList?.[0]?.securityMeasureVOList || [];
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      new Api('/pms/job-manage/risk/measure').fetch({
        fileDTOList: formValues?.fileDTOList || [],
        jobId: props?.record?.jobId,
      }, '', 'PUT').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

function changeFile(fileDTOList: any[]) {
  setFieldsValue({
    fileDTOList,
  });
}
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  >
    <template #riskLevel>
      <BasicCard
        :isBorder="false"
        :isSpacing="false"
        :isContentSpacing="false"
        title="风险级别及判断标准"
      >
        <div>
          <OrionTable
            ref="tableRef"
            :options="riskTableOptions"
          />
        </div>
      </BasicCard>
    </template>
    <template #fileDTOList="{model,field}">
      <BasicCard
        title="管理措施落实证明"
        :isSpacing="false"
        :isBorder="false"
      />
      <UploadList
        :height="300"
        :isSpacing="false"
        type="modal"
        :listData="model[field]"
        @change="changeFile"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
