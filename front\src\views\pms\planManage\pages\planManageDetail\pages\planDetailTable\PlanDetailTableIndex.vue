<template>
  <OrionTable
    :options="getTableOptions()"
    :dataSource="tableData"
  />

  <!--创建计划抽屉-->
  <CreatePlanDrawerIndex @register="createPlanDrawerRegister" />
  <RelevancePlanDrawerIndex @register="relevancePlanDrawerRegister" />
</template>

<script setup lang="ts">
import { OrionTable, useDrawer } from 'lyra-component-vue3';
import { CreatePlanDrawerIndex } from '../../../../components';
import { RelevancePlanDrawerIndex } from './components';
import { useProjectPlan } from './hooks';

const props = defineProps<{
  planId: string
}>();

const [createPlanDrawerRegister, createPlanDrawerMethods] = useDrawer();
const [relevancePlanDrawerRegister, relevancePlanDrawerMethods] = useDrawer();
const { getTableOptions, tableData } = useProjectPlan({
  createPlanDrawerMethods,
  relevancePlanDrawerMethods,
  planId: props.planId,
});
</script>

<style scoped>

</style>
