@startuml 客户管理数据库模型图
!theme cerulean-outline
title 客户管理数据库模型图

skinparam class {
    BackgroundColor lightblue
    BorderColor black
    FontSize 9
}

!define table(name) class name << (T,#FFAAAA) >>
!define pk(x) <u>x</u>
!define fk(x) <i>x</i>

table(pms_customer_info) {
    pk(id) : varchar(64)
    --
    comnumber : varchar(256)
    bus_register_code : varchar(256)
    tax_id_code : varchar(256)
    cus_full_name : varchar(256)
    cus_level : varchar(256)
    country : varchar(256)
    organizatioin_code : varchar(256)
    biz_period : varchar(256)
    registered_capital : varchar(256)
    uniform_credit_code : varchar(256)
    cus_category : varchar(256)
    registration_time : varchar(256)
    business_scope : longtext
    comtaxnumber : varchar(256)
    registered_address : varchar(256)
    legal_repr : varchar(256)
    cus_ceate_time : varchar(256)
    public_account_info : varchar(256)
    cus_num_count : varchar(256)
    cus_address : varchar(256)
    ywsrlx : varchar(256)
    industry : varchar(256)
    bus_scope : varchar(1024)
    group_in_out : varchar(256)
    cus_remark : tinytext
    cus_status : varchar(256)
    customerdepartmentent : varchar(256)
    group_info : varchar(256)
    cus_number : varchar(256)
    cus_name : varchar(2048)
    number : varchar(64)
    zzxx : varchar(256)
    comtpye : varchar(256)
    regist_status : varchar(255)
    paid_in_capital : varchar(255)
    approved_date : datetime
    province : varchar(255)
    city : varchar(255)
    county : varchar(255)
    category : varchar(255)
    large_category : varchar(255)
    middle_category : varchar(255)
    english_name : varchar(255)
    tel : varchar(255)
    other_tel : varchar(255)
    email : varchar(255)
    is_used : varchar(64)
    home_base : varchar(64)
    sales_class : varchar(64)
    is_person : varchar(10)
    once_name : varchar(200)
    --
    class_name : varchar(64)
    creator_id : varchar(64)
    modify_time : datetime
    owner_id : varchar(64)
    create_time : datetime
    modify_id : varchar(64)
    remark : varchar(1024)
    platform_id : varchar(64)
    org_id : varchar(64)
    status : int
    logic_status : int
}

table(pms_mk_alias) {
    pk(id) : varchar(64)
    --
    company_name : varchar(100)
    fk(custom_id) : varchar(64)
    --
    class_name : varchar(64)
    creator_id : varchar(64)
    modify_time : datetime
    owner_id : varchar(64)
    create_time : datetime
    modify_id : varchar(64)
    remark : varchar(1024)
    platform_id : varchar(64)
    org_id : varchar(64)
    status : int
    logic_status : int
}

pms_customer_info ||--o{ pms_mk_alias : "一对多\n客户曾用名"

note top of pms_customer_info
<b>客户信息主表</b>
- 存储客户的基本信息和工商注册信息
- 包含客户联系方式和地址信息
- 客户名称全局唯一性约束
- 支持字典字段编码值存储
- 包含审计字段和逻辑删除标识
end note

note top of pms_mk_alias
<b>客户曾用名表</b>
- 存储客户的历史名称信息
- 与客户信息表一对多关系
- 曾用名全局唯一性校验
- 包含审计字段和逻辑删除标识
end note

@enduml 