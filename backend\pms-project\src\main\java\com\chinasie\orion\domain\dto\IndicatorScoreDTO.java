package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * IndicatorScore DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@ApiModel(value = "IndicatorScoreDTO对象", description = "指标评分表")
@Data
public class IndicatorScoreDTO extends ObjectDTO implements Serializable{

/**
 * 项目和指标关联外键
 */
@ApiModelProperty(value = "项目和指标关联外键")
private String PerformanceInId;

}
