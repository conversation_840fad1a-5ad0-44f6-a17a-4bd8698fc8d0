package com.chinasie.orion.domain.vo.approval;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectApprovalEstimateTemplate VO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateVO对象", description = "概算模板")
@Data
public class ProjectApprovalEstimateTemplateVO extends ObjectVO implements Serializable{

        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 科目数量
         */
        @ApiModelProperty(value = "科目数量")
        private Integer subjectNumber;

        /**
         * 所属分类ID
         */
        @ApiModelProperty(value = "所属分类ID")
        private String templateClassifyId;

        /**
         * 科目树
         */
        @ApiModelProperty(value = "科目树")
        private List<ProjectApprovalEstimateTemplateExpenseSubjectVO> subjectTree;

}
