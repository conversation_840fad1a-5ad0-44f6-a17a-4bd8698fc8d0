<template>
  <div>
    <!--    {{ weeksInCurrentMonth?.weeks }}-->
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="state.isTable"
      @selectionChange="selectionChange"
      @smallSearch="smallSearch"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_03_01_button_01',powerData)"
          :disabled="!state.isTable"
          icon="add"
          type="primary"
          @click="handle('add')"
        >
          添加周报
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_03_01_button_02',powerData)"
          icon="delete"
          :disabled="!state.rows.length"
          @click="multiDelete"
        >
          删除
        </BasicButton>
      </template>
      <template #toolbarRight>
        <a-range-picker
          v-if="state.isTable"
          v-model:value="state.searchTime"
          class="mr10"
        />
        <a-radio-group
          v-if="state.isTable"
          v-model:value="state.searchWeekRadio"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="ALL">
            全部
          </a-radio-button>
          <a-radio-button value="LAST_WEEK">
            上周
          </a-radio-button>
          <a-radio-button value="THIS_WEEK">
            本周
          </a-radio-button>
          <a-radio-button value="THIS_MONTH">
            本月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-if="!state.isTable"
          v-model:value="state.searchCardWeekRadio"
          button-style="solid"
          class="mr10 ml10"
        >
          <a-radio-button value="CARD_LAST_WEEK">
            上周
          </a-radio-button>
          <a-radio-button value="CARD_WEEK">
            本周
          </a-radio-button>
          <a-radio-button
            value="CARD_MONTH"
          >
            本月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-model:value="state.tableType"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="list">
            <AlignLeftOutlined />
          </a-radio-button>
          <a-radio-button value="content">
            <AppstoreOutlined />
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #otherContent>
        <div
          v-show="!state.isTable"
        >
          <MonthFormat
            ref="MonthFormatTime"
            @timeChange="timeChange"
          />
          <div
            class="week_cont"
          >
            <div
              v-for="(item,index) in weeksInCurrentMonth?.weeks"
              :key="index"
              :class="{'weekly_cont':true,'mark':item.weekNumber==weekIndexNum}"
            >
              <div class="week_title">
                <div class="week_title_lt">
                  {{ `${item.weekNumber}W` }}
                </div>
                <div class="week_title_rt">
                  <div
                    v-if="item.score?1:0"
                    :class="{operate:true,green:item.score==3,yellow:item.score>3,grey:item.score<3}"
                  >
                    {{ item.score }}
                  </div>
                  <div
                    v-else-if="item.status=== 120"
                  >
                    待审核
                  </div>
                  <div
                    v-else-if="item.status=== 101"
                    class="card-footer"
                  >
                    <div>
                      <Icon
                        v-if="item?.contentVOList?.length"
                        class="icon"
                        icon="sie-icon-bianji"
                        size="16"
                        @click="state.addOrEditRef.openDrawer({ action: 'edit', info: item })"
                      />
                      <Icon
                        v-else
                        class="disabled-icon"
                        icon="sie-icon-bianji"
                        size="16"
                      />
                    </div>
                    <div>
                      <Icon
                        v-if="item?.contentVOList?.length"
                        class="icon"
                        icon="sie-icon-del"
                        size="16"
                        @click="goDel([item])"
                      />
                      <Icon
                        v-else
                        class="disabled-icon"
                        icon="sie-icon-del"
                        size="16"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="week_list"
                @click="goDetails(item)"
              >
                <div
                  v-for="(j,index) in item.contentVOList"
                  :key="index"
                  class="list_item_cont"
                >
                  <h4 class="time">
                    {{ `${j?.taskTime}H` }}
                  </h4>
                  <Tooltip
                    placement="top"
                    trigger="hover"
                    mouseEnterDelay="1.5"
                    :title="j?.content"
                    :auto-adjust-overflow="true"
                  >
                    <p
                      class="list_item"
                    >
                      {{ j?.content }}
                    </p>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actionList"
          :record="record"
        />
      </template>
    </OrionTable>
    <AddOrEditDrawer
      ref="addOrEditRef"
      @update="updateTable"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, onMounted, watchEffect, Ref, inject,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction, Icon, isPower,
} from 'lyra-component-vue3';
import {
  Button, DatePicker, Radio, Calendar, Popover, Modal, message, Tooltip,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useRoute, useRouter } from 'vue-router';
import { getColumns, getActionsList } from './config/index';
import AddOrEditDrawer from './component/addOrEdit/addOrEditDrawer.vue';
import PeopleList from './component/cardList/PeopleList.vue';
// import Icon from '/@/components/Icon/src/Icon.vue';
import DayFormat from './component/timeFormat/DayFormat.vue';
import MonthFormat from './component/timeFormat/MonthFormat.vue';

const route = useRoute();
const router = useRouter();
const powerData = inject('powerData', []);

const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const ARangePicker = DatePicker.RangePicker;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const weeksInCurrentMonth = ref();
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const weekIndexNum = ref(); // 当前日期为多少周
const weekIndexNumCopy = ref(); // 当前日期为多少周
const MonthFormatTime = ref(null);
const state = reactive({
  tableRef,
  addOrEditRef,
  isTable: true,
  tableType: 'list',
  searchWeekRadio: 'ALL',
  searchCardWeekRadio: 'CARD_MONTH',
  searchTime: undefined,
  dataSource: [] as any,
  timeEmit: null,
  rows: [],
  canDel: false,
  keyword: '',
});
const actionList = getActionsList({ state });

const dataModel = ref<Dayjs>();
watch(() => state.tableType, () => {
  state.isTable = state.tableType === 'list';
  state.tableRef.reload({ page: 1 });
});
const headAuthList: Ref<any[]> = ref([]);
const tableOptions = computed(() => (state.isTable ? {
  showToolButton: false,
  rowSelection: {},
  api: (params) => {
    params.query = {
      keyword: state.keyword,
      summary: '',
      timeType: state.searchWeekRadio,
      projectId: route.query.id,
      ...getTime(),
      pageType: false,
    };
    params.power = {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_21_03_01',
      containerCode: 'PMS_XMXQ_container_21_03_02',
    };
    return new Api('/pms/projectWeekly/pages').fetch(params, '', 'POST').then((res) => {
      if (res?.content?.length) {
        state.dataSource = res.content;
      } else {
        state.dataSource = [];
      }
      state.rows = [];
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  showSmallSearch: true,
  pagination: true,
  columns: getColumns({ router }),
} : {
  showToolButton: false,
  rowSelection: {},
  api: (params) => {
    params.query = {
      keyword: state.keyword,
      summary: '',
      timeType: state.searchWeekRadio,
      projectId: route.query.id,
      ...getTime(),
    };
    return new Api('/pms/projectWeekly/pages').fetch(params, '', 'POST').then((res) => {
      state.dataSource = res.content;
      state.rows = [];
      return res;
    });
  },
  pagination: false,
  showSmallSearch: false,
  columns: getColumns({ router }),
}));

function getTime() {
  if (state.isTable) {
    return {
      startTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[0].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      endTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[1].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
    };
  }
  return {
    startTime: state.timeEmit ? dayjs(state.timeEmit).startOf('month').format('YYYY-MM-DD') : '',
    endTime: state.timeEmit ? dayjs(state.timeEmit).endOf('month').format('YYYY-MM-DD') : '',
  };
}
onMounted(() => {
  getWeeksInMonth(currentYear.value, currentMonth.value + 1);
  weekIndexNum.value = weeksInCurrentMonth.value.currentWeekNumber;
  weekIndexNumCopy.value = weeksInCurrentMonth.value.currentWeekNumber;
});

// watchEffect(() => {
//   if (state.dataSource.length) {
//     state.dataSource.forEach((i) => {
//       weeksInCurrentMonth.value?.weeks.forEach((j, index) => {
//         if (i.week === j.weekNumber) {
//           weeksInCurrentMonth.value.weeks[index] = { ...j, ...i };
//         } else {
//           weeksInCurrentMonth.value.weeks[index] = { startDate: j.startDate, endDate: j.endDate, weekNumber: j.weekNumber };
//         }
//       });
//     });
//   } else {
//     weeksInCurrentMonth.value?.weeks.forEach((j, index) => {
//       weeksInCurrentMonth.value.weeks[index] = { startDate: j.startDate, endDate: j.endDate, weekNumber: j.weekNumber };
//     });
//   }
// });
// watchEffect(() => {
//   if (state.dataSource.length) {
//     console.log('获取的新数据', state.dataSource);
//     if (weeksInCurrentMonth.value && weeksInCurrentMonth.value.weeks) {
//       for (let index = 0; index < weeksInCurrentMonth.value.weeks.length; index++) {
//         const j = weeksInCurrentMonth.value.weeks[index];
//         const matchingData = state.dataSource.find((i) => i.week === j.weekNumber);
//         if (matchingData) {
//           weeksInCurrentMonth.value.weeks[index] = { ...j, ...matchingData };
//         } else {
//           weeksInCurrentMonth.value.weeks[index] = { ...j };
//         }
//       }
//     }
//   } else if (weeksInCurrentMonth.value && weeksInCurrentMonth.value.weeks) {
//     for (let index = 0; index < weeksInCurrentMonth.value.weeks.length; index++) {
//       const j = weeksInCurrentMonth.value.weeks[index];
//       weeksInCurrentMonth.value.weeks[index] = { ...j };
//     }
//   }
// });
watch(
  () => state.dataSource,
  (newDataSource) => {
    const weeks = weeksInCurrentMonth.value?.weeks;
    if (weeks && weeks.length) {
      for (let index = 0; index < weeks.length; index++) {
        const j = weeks[index];
        const matchingData = newDataSource.find((i) => i.week === j.weekNumber);
        if (matchingData) {
          weeks[index] = {
            ...j,
            ...matchingData,
          };
        } else {
          weeks[index] = { ...j };
        }
      }
    }
  },
  { immediate: true },
);

watch(() => [state.searchWeekRadio, state.searchTime], () => {
  state.tableRef.reload({ page: 1 });
}, { deep: true });
// 监听卡片视图选择的筛选条件
watch(() => state.searchCardWeekRadio, () => {
  if (state.searchCardWeekRadio === 'CARD_LAST_WEEK') {
    weekIndexNum.value -= 1;
  }
  if (state.searchCardWeekRadio === 'CARD_WEEK') {
    weekIndexNum.value = weekIndexNumCopy.value;
  }
  if (state.searchCardWeekRadio === 'CARD_MONTH') {
    getWeeksInMonth(currentYear.value, currentMonth.value + 1);
    MonthFormatTime.value.setTime(dayjs().format('YYYY-MM'));
  }
});
function getWeeksInMonth(year, month) {
  // 创建一个数组来存储结果
  const weeks = [];
  // 获取指定年月的第一天和最后一天
  const firstDay = new Date(year, month ? month - 1 : 0, 1);
  const lastDay = new Date(year, month || 12, 0);
  // 获取第一周的起始和结束日期以及周数
  const getFirstWeek = (date) => {
    const start = new Date(date);
    start.setDate(date.getDate() - date.getDay() + 1);
    const end = new Date(date);
    end.setDate(date.getDate() + (6 - date.getDay()));
    const weekNumber = getWeekNumber(start);
    return {
      startDate: start,
      endDate: end,
      weekNumber,
    };
  };

  // 获取下一周的起始和结束日期以及周数
  const getNextWeek = (date, weekNumber) => {
    const start = new Date(date);
    start.setDate(date.getDate());
    const end = new Date(date);
    end.setDate(date.getDate() + 6);
    weekNumber++;
    return {
      startDate: start,
      endDate: end,
      weekNumber,
    };
  };

  // 获取指定日期所在周是当年的第几周
  const getWeekNumber = (date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const daysOffset = firstDayOfYear.getDay() - 1;
    const firstMondayOfYear = new Date(
      date.getFullYear(),
      0,
      1 + (daysOffset > 0 ? 7 - daysOffset : 0),
    );
    const diff = date - firstMondayOfYear;
    const weekNumber = Math.ceil(diff / (7 * 24 * 60 * 60 * 1000)); // 修正周数
    return weekNumber;
  };

  // 初始化起始日期和结束日期
  let startDate = getFirstWeek(firstDay).startDate;
  let endDate = getFirstWeek(firstDay).endDate;
  let weekNumber = getFirstWeek(firstDay).weekNumber;

  // 循环直到结束日期大于最后一天
  while (endDate <= lastDay) {
    weeks.push(getNextWeek(startDate, weekNumber));
    startDate.setDate(startDate.getDate() + 7);
    endDate.setDate(endDate.getDate() + 7);
    weekNumber++;
  }
  // 获取当前日期所在周的周数
  const currentWeekNumber = getWeekNumber(new Date()) + 1;
  weeksInCurrentMonth.value = {
    weeks,
    totalWeeks: weeks.length,
    currentWeekNumber,
  };
}

function handle(type) {
  switch (type) {
    case 'add':
      addOrEditRef.value.openDrawer({ action: 'add' });
      break;
    case 'edit':
      addOrEditRef.value.openDrawer({ action: 'edit' });
      break;
  }
}

watch(() => [state.searchWeekRadio, state.searchTime], () => {
  state.tableRef.reload({ page: 1 });
}, { deep: true });
watch(() => [state.isTable], () => {
  // state.timeEmit = null;
  // state.searchTime = null;
  state.tableRef.reload({ page: 1 });
}, { deep: true });

// 判断是不是周六周日
function isWeekend(date) {
  const dayOfWeek = dayjs(date).day();
  return dayOfWeek === 0 || dayOfWeek === 6;
}

// 是不是今天
function isToday(someDate) {
  const today = dayjs();
  const inputDate = dayjs(someDate);
  return today.isSame(inputDate, 'day');
}

function timeChange(time) {
  state.timeEmit = time;
  getWeeksInMonth(Number(dayjs(time).format('YYYY')), Number(dayjs(time).format('MM')));
  dataModel.value = dayjs(time);
  state.tableRef.reload({ page: 1 });
}
watch(() => state.timeEmit, () => {
  if (Number(dayjs(state.timeEmit).format('YYYY')) === currentYear.value
      && Number(dayjs(state.timeEmit).format('MM')) === currentMonth.value + 1) {
    state.searchCardWeekRadio = 'CARD_MONTH';
  } else {
    state.searchCardWeekRadio = '';
  }
});

function goDetails(item) {
  if (item?.id) {
    router.push({
      name: 'WeekReportDetails',
      query: {
        id: item.projectId,
        curId: item.id,
      },
    });
  } else {
    handle('add');
  }
}

function goAdd(current) {
  let time = dayjs(current).format('YYYY-MM-DD');
  if (state.dataSource?.length) {
    let index = state.dataSource?.findIndex((item) => item.daily === time);
    if (index < 0) {
      addOrEditRef.value.openDrawer({
        action: 'add',
        info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
      });
    } else if (state.dataSource[index].projectDailyStatementContentVOList?.length) {
      if (state.dataSource[index].edit) {
        state.addOrEditRef.openDrawer({
          action: 'edit',
          info: { id: state.dataSource[index].id },
        });
      } else {
        router.push({
          name: 'WeekReportDetails',
          query: {
            id: state.dataSource[index]?.projectId,
            curId: state.dataSource[index]?.id,
          },
        });
      }
    } else {
      addOrEditRef.value.openDrawer({
        action: 'add',
        info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
      });
    }
  } else {
    addOrEditRef.value.openDrawer({
      action: 'add',
      info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
    });
  }
}

function goEdit(data) {
  if (!data.edit) {
    router.push({
      name: 'WeekReportDetails',
      query: {
        id: data?.projectId,
        curId: data?.id,
      },
    });
  }
  state.addOrEditRef.openDrawer({
    action: 'edit',
    info: { id: data.id },
  });
}

function goDel(data) {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除该条信息？',
    onOk() {
      deleteRows([data.id]);

      // return new Api('/pms').fetch(data ? [data.id] : state.tableRef.getSelectRows().map((item) => item.id), 'projectDaily-statement', 'DELETE').then(() => {
      //   state.tableRef.reload({ page: 1 });
      // });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}
function multiDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除该条信息？',
    onOk() {
      deleteRows(state.tableRef.getSelectRows().map((item) => item.id));
    },
  });
}
function deleteRows(ids) {
  return new Api('/pms').fetch(ids, 'projectWeekly', 'DELETE').then(() => {
    state.tableRef.reload({ page: 1 });
  });
}

function updateTable() {
  state.tableRef.reload({ page: 1 });
}

function selectionChange({ rows }) {
  state.rows = JSON.parse(JSON.stringify(rows));
}

// 对每条数据校验是否可以批量删除
watch(() => state.rows, () => {
  if (state.rows?.length) {
    state.rows.forEach((item) => {
      if (!item.edit) {
        state.canDel = false;
      }
    });
  } else {
    state.canDel = false;
  }
});

function smallSearch(val) {
  state.keyword = val;
  state.tableRef.reload({ page: 1 });
}
</script>

<style scoped lang="less">
.week_cont{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-right: 20px;
  margin-top: 20px;
  .weekly_cont{
    width: 49%;
    height: 200px;
    border:solid 1px #ccc;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
    .week_title{
      height: 40px;
      display: flex;
      padding:0 10px;
      .week_title_lt{
        width:20%;
        height: 40px;
        display: flex;
        align-items: center;
        font-weight: bold;
      }
      .week_title_rt{
        width:80%;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .operate{
          width:26px;
          height:26px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
        }
        .green{
          background-color: #2BCFCD;
        }
        .yellow{
          background-color: #FFC900;
        }
        .grey{
          background-color: #CCCCCC;
        }
      }
    }
    .week_list{
      height:160px;
      padding:10px;
      overflow-y: auto;
      .list_item_cont {
        display: flex;
        .time{
          height: 24px;
          margin-right: 10px;
          color:~`getPrefixVar('primary-color')`;
        }
        .list_item {
          height: 24px;
          flex:1;
          line-height: 24px;
          text-overflow: ellipsis; /* 溢出显示省略号 */
          overflow: hidden; /* 溢出隐藏 */
          white-space: nowrap;  /* 强制不换行 */
        }
      }
    }
  }
}
.popBox {
  max-height: 250px;
  overflow-y: auto;
}

.monthBoxItem {
  height: 150px;
  margin-bottom: 4px;
  margin-right: 4px;
  border-radius: 3px;
  box-sizing: border-box;
  border: 1px solid #e3e3e3;
  padding: 28px 10px 10px 10px;
  position: relative;

  &:nth-child(7n) {
    margin-right: 0;
  }

  .itemTop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: red;
  }

  .circleCore {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .topRightBtn {
    position: absolute;
    height: 20px;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .itemContent {
    overflow: auto;
    height: 100%;
    width: 100%;
  }
}

.weekNumber {
  position: absolute;
  height: 20px;
  line-height: 20px;
  left: 5px;
  top: 5px;
  color: #545454;
  background-color:palegreen ;
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}

.bgcCCC {
  //background-color: #c0c0c0;
}

.currentDayBgc {
  border-color: ~`getPrefixVar('primary-color')` !important;
  border-width: 2px !important;
}
//气泡卡片样式
.list_item_card{
  width:300px;
  height: 200px;
  word-wrap:break-word;
  background-color: #2BCFCD;
}
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 40px;
  width:100px;
  > div {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex: 1;

    .icon:hover {
      color: ~`getPrefixVar('primary-color')`;
      cursor: pointer;
    }
  }

  div + div::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
    width: 1px;
    height: 14px;
    background-color: #E9E9E9;
  }
}
.mark{
  border:2px solid ~`getPrefixVar('primary-color')` !important;
}
.disabled-icon{
  opacity: 0.25;
  cursor: not-allowed;
}
</style>
