//package com.chinasie.orion.management.constant.caigouUtil;
//
//import cn.hutool.core.codec.Base64Decoder;
//
//import java.security.MessageDigest;
//import java.text.ParseException;
//
//public class OrgCodeTest {
//
//
//    public static void test(){
//        String emails = "x0Iy1j9ay2a4PjbtFDS7sQ==$$$$,x0Iy1j9ay2a4PjbtFDS7sQ==$$$$,x0Iy1j9ay2a4PjbtFDS7sQ==$$$$,x0Iy1j9ay2a4PjbtFDS7sQ==$$$$";
//
//        String[] emaStr = emails.split(",");
//
//        for (int i = 0;i<emaStr.length;i++){
//            if (emaStr[i].endsWith("=") || emaStr[i].endsWith("$")){
//                if (emaStr[i].endsWith("$$$$")){
//                    System.out.println(decryptData_ECB(emaStr[i].replace("$$$$","")));
//                }else {
//                    System.out.println(decryptData_ECB(emaStr[i]));
//                }
//            }else {
//                System.out.println(emaStr[i]);
//            }
//        }
//    }
//
//
//    public static void main(String[] args) throws ParseException {
//        test();
//
//    }
//
//    /**SM4加密*/
//    public static String encryptData_ECB(String plainText)
//    {
//        try
//        {
//
//            String secretKey = "hCm7JwWuZ741QnIX";
//            Boolean hexString = false;
//            SM4_Context ctx = new SM4_Context();
//            ctx.isPadding = true;
//            ctx.mode = SM4.SM4_ENCRYPT;
//
//            byte[] keyBytes;
//            if (hexString)
//            {
//                keyBytes = SM4Util.hexStringToBytes(secretKey);
//            }
//            else
//            {
//                keyBytes = secretKey.getBytes();
//            }
//
//            SM4 sm4 = new SM4();
//            sm4.sm4_setkey_enc(ctx, keyBytes);
//            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes("GBK"));
//            String cipherText = new BASE64Encoder().encode(encrypted);
//            if (cipherText != null && cipherText.trim().length() > 0)
//            {
//                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
//                Matcher m = p.matcher(cipherText);
//                cipherText = m.replaceAll("");
//            }
//            return cipherText;
//        }
//        catch (Exception e)
//        {
////			e.printStackTrace();
//            return plainText;
//        }
//    }
//
//    public static String decryptData_ECB(String cipherText)
//    {
//        try
//        {
//            String secretKey = "hCm7JwWuZ741QnIX";
//            Boolean hexString = false;
//            SM4_Context ctx = new SM4_Context();
//            ctx.isPadding = true;
//            ctx.mode = SM4.SM4_DECRYPT;
//
//            byte[] keyBytes;
//            if (hexString)
//            {
//                keyBytes = hexStringToBytes(secretKey);
//            }
//            else
//            {
//                keyBytes = secretKey.getBytes();
//            }
//
//            SM4 sm4 = new SM4();
//            sm4.sm4_setkey_dec(ctx, keyBytes);
//            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, new Base64Decoder().decodeBuffer(cipherText));
//            return new String(decrypted, "GBK");
//        }
//        catch (Exception e)
//        {
////			e.printStackTrace();
//            return cipherText;
//        }
//    }
//
//    public static byte[] hexStringToBytes(String hexString)
//    {
//        if (hexString == null || hexString.equals(""))
//        {
//            return null;
//        }
//
//        hexString = hexString.toUpperCase();
//        int length = hexString.length() / 2;
//        char[] hexChars = hexString.toCharArray();
//        byte[] d = new byte[length];
//        for (int i = 0; i < length; i++)
//        {
//            int pos = i * 2;
//            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
//        }
//        return d;
//    }
//
//    public static byte charToByte(char c)
//    {
//        return (byte) "0123456789ABCDEF".indexOf(c);
//    }
//
//    public static String string2MD5(String inStr) {
//        MessageDigest md5 = null;
//        try {
//            md5 = MessageDigest.getInstance("MD5");
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "";
//        }
//        char[] charArray = inStr.toCharArray();
//        byte[] byteArray = new byte[charArray.length];
//        for (int i = 0; i < charArray.length; i++)
//            byteArray[i] = (byte) charArray[i];
//        byte[] md5Bytes = md5.digest(byteArray);
//        StringBuffer hexValue = new StringBuffer();
//        for (int i = 0; i < md5Bytes.length; i++) {
//            int val = ((int) md5Bytes[i]) & 0xff;
//            if (val < 16)
//                hexValue.append("0");
//            hexValue.append(Integer.toHexString(val));
//        }
//        return hexValue.toString();
//    }
//}
