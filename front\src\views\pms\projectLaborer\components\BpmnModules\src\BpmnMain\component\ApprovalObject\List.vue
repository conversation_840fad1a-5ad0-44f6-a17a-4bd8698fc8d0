<template>
  <div class="list-wrap">
    <div class="title">
      审批物列表
    </div>
    <div>
      <OrionTable
        ref="tableRef"
        :options="approveOptions"
      >
        <template #status="{ record }">
          <DataStatusTag :status-data="record?.dataStatus" />
        </template>
        <template #modifyTime="{ record }">
          <span>{{ stampDate(record.modifyTime) }}</span>
        </template>
      </OrionTable>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, DataStatusTag,
} from 'lyra-component-vue3';
// import OrionTable from '/@/components/OrionTable';
// import { DataStatusTag } from '/@/components/StatusTag';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
import { workflowApi } from '../../../util/config';

export default defineComponent({
  name: '',
  components: {
    OrionTable,
    DataStatusTag,
  },
  emits: ['approvalObjectRegister'],
  setup(_, { emit }) {
    const { userId, menuActionItem } = inject('bpmnModuleData')?.value;
    const state: any = reactive({
      tableRef: null,
      approveOptions: {
        canResize: false,
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        rowSelection: {},
        auto: {
          url: `${workflowApi}/act-prearranged/delivery`,
          params: {
            query: {
              userId,
              ids: [menuActionItem.id],
            },
          },
        },
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
          },
          {
            title: '编号',
            dataIndex: 'number',
          },
          {
            title: '密级',
            dataIndex: 'secretLevelName',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },
          },
          {
            title: '所有者',
            dataIndex: 'ownerName',
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            slots: { customRender: 'modifyTime' },
          },
        ],
      },
    });

    onMounted(() => {
      emit('approvalObjectRegister', {
        tableRef: state.tableRef,
      });
    });

    return {
      ...toRefs(state),
      stampDate,
    };
  },
});
</script>

<style scoped lang="less">
  .list-wrap {
    > .title {
      height: 40px;
      line-height: 40px;
      padding: 0 15px 0 35px;
      box-sizing: border-box;
      background: #f0f2f5;
      position: relative;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-width: 6px;
        border-style: solid;
        border-color: #969eb4 transparent transparent transparent;
        position: absolute;
        left: 15px;
        top: 18px;
      }
    }
  }
</style>
