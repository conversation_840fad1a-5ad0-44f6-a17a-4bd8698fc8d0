<script setup lang="ts">
import {
  computed,
  onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  isPower, Layout3, Layout3Content,
} from 'lyra-component-vue3';
import Information from './information/index.vue';
import Document from './document/index.vue';
import TaskDecomposition from './taskDecomposition/index.vue';
import VersionInfo from './versionInfo/index.vue';
import { documentModelLibraryById } from '/@/views/pms/api/documentModelLibrary';
const powerData = ref([]);
// 权限分发
provide('powerData', computed(() => powerData.value));
const route = useRoute();
const id:string = (route.params.id) as string;
const defaultActionId: Ref<string> = ref('information');
const menuData: Ref<any[]> = ref([
  {
    id: 'information',
    name: '基本信息',
    powerCode: 'WDMBKXQ_container_01',
  },
  {
    id: 'document',
    name: '文档分解',
    powerCode: 'WDMBKXQ_container_02',
  },
  {
    id: 'taskDecomposition',
    name: '任务分解',
    powerCode: 'WDMBKXQ_container_03',
  },
  {
    id: 'versionInfo',
    name: '版本记录',
    powerCode: 'WDMBKXQ_container_04',
  },
]);

const loading: Ref<boolean> = ref(false);
const detailsInfo :any = ref({});

const getDetailData = async () => {
  try {
    loading.value = true;
    const result = await documentModelLibraryById(id, 'PMS_WDMBK_DETAIL') || {};
    result.projectCode = result.number;// 显示编号
    detailsInfo.value = result;
    powerData.value = result.detailAuthList || [];
  } finally {
    loading.value = false;
  }
};

const menuChange = ({ id }) => {
  defaultActionId.value = id;
};

onMounted(() => {
  getDetailData();
});

</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="defaultActionId"
    :projectData="detailsInfo"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <Layout3Content v-if="detailsInfo?.id">
      <!--基本信息-->
      <Information
        v-if="defaultActionId==='information' && isPower('WDMBKXQ_container_01',powerData)"
        :data="detailsInfo"
      />
      <!--文档分解-->
      <Document
        v-if="defaultActionId==='document' && isPower('WDMBKXQ_container_02',powerData)"
        :id="id"
      />
      <!--任务分解-->
      <TaskDecomposition
        v-if="defaultActionId==='taskDecomposition' && isPower('WDMBKXQ_container_03',powerData)"
        :id="id"
      />
      <!--版本记录-->
      <VersionInfo
        v-if="defaultActionId==='versionInfo' && isPower('WDMBKXQ_container_04',powerData)"
        :id="id"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">

</style>
