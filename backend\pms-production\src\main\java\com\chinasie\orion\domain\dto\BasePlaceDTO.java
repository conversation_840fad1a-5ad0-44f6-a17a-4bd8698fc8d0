package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * NcfFormHQJHpsfrK DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
@ApiModel(value = "BasePlaceDTO对象", description = "基地库")
@Data
@ExcelIgnoreUnannotated
public class BasePlaceDTO extends ObjectDTO implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 3)
    private String number;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 4)
    private String code;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @ExcelProperty(value = "基地名称 ", index = 5)
    private String name;

    /**
     * 基地所在城市
     */
    @ApiModelProperty(value = "基地所在城市")
    @ExcelProperty(value = "基地所在城市 ", index = 6)
    private String city;

    /**
     * 对应项目部id
     */
    @ApiModelProperty(value = "对应项目部id")
    private String projectDeptId;

    /**
     * 对应项目部名称
     */
    @ApiModelProperty(value = "对应项目部名称")
    private String projectDeptName;

    /**
     * 对应项目部编号
     */
    @ApiModelProperty(value = "对应项目部编号")
    private String projectDeptNumber;
}
