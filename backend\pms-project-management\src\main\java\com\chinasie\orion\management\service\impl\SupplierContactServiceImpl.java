package com.chinasie.orion.management.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.constant.caigouUtil.Sm4Util;
import com.chinasie.orion.management.domain.dto.SupplierContactDTO;
import com.chinasie.orion.management.domain.entity.SupplierContact;
import com.chinasie.orion.management.domain.vo.SupplierContactVO;
import com.chinasie.orion.management.repository.SupplierContactMapper;
import com.chinasie.orion.management.service.SupplierContactService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SupplierContact 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierContactServiceImpl extends OrionBaseServiceImpl<SupplierContactMapper, SupplierContact> implements SupplierContactService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierContactVO detail(String id, String pageCode) throws Exception {
        SupplierContact supplierContact = this.getById(id);
        SupplierContactVO result = BeanCopyUtils.convertTo(supplierContact, SupplierContactVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierContactDTO
     */
    @Override
    public String create(SupplierContactDTO supplierContactDTO) throws Exception {
        SupplierContact supplierContact = BeanCopyUtils.convertTo(supplierContactDTO, SupplierContact::new);
        this.save(supplierContact);
        String rsp = supplierContact.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierContactDTO
     */
    @Override
    public Boolean edit(SupplierContactDTO supplierContactDTO) throws Exception {
        SupplierContact supplierContact = BeanCopyUtils.convertTo(supplierContactDTO, SupplierContact::new);
        this.updateById(supplierContact);
        String rsp = supplierContact.getId();
        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierContactVO> pages(String mainTableId, Page<SupplierContactDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierContact> condition = new LambdaQueryWrapperX<>(SupplierContact.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierContact::getCreateTime);
        condition.eq(SupplierContact::getMainTableId, mainTableId);
        Page<SupplierContact> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierContact::new));
        PageResult<SupplierContact> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String fileName = "供应商联系人导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierContactDTO.class, new ArrayList<>());
    }

    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierContactExcelListener excelReadListener = new SupplierContactExcelListener();
        EasyExcel.read(inputStream, SupplierContactDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierContactDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("供应商联系人导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierContact> supplierContactes = BeanCopyUtils.convertListTo(dtoS, SupplierContact::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierContact-import::id", importId, supplierContactes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);
        return result;
    }

    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierContact> supplierContactes = (List<SupplierContact>) orionJ2CacheService.get("ncf::SupplierContact-import::id", importId);
        log.info("供应商联系人导入的入库数据={}", JSONUtil.toJsonStr(supplierContactes));

        this.saveBatch(supplierContactes);
        orionJ2CacheService.delete("ncf::SupplierContact-import::id", importId);
        return true;
    }

    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierContact-import::id", importId);
        return true;
    }

    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierContact> condition = new LambdaQueryWrapperX<>(SupplierContact.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SupplierContact::getCreateTime);
        List<SupplierContact> supplierContactes = this.list(condition);
        List<SupplierContactDTO> dtos = BeanCopyUtils.convertListTo(supplierContactes, SupplierContactDTO::new);
        String fileName = "供应商联系人数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierContactDTO.class, dtos);
    }

    @Override
    public void setEveryName(List<SupplierContactVO> vos) throws Exception {
        vos.forEach(vo -> {
            vo.setDepartment(Sm4Util.decoder(vo.getDepartment()));
            vo.setPosition(Sm4Util.decoder(vo.getPosition()));
            vo.setLandline(Sm4Util.decoder(vo.getLandline()));
            vo.setMobile(Sm4Util.decoder(vo.getMobile()));
            vo.setEmail(Sm4Util.decoder(vo.getEmail()));
            vo.setResponsibleArea(Sm4Util.decoder(vo.getResponsibleArea()));
        });
    }

    @Override
    public Page<SupplierContactVO> getByCode(Page<SupplierContactDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierContact> condition = new LambdaQueryWrapperX<>(SupplierContact.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierContact::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }
        condition.eq(SupplierContact::getSupplierCode, pageRequest.getQuery().getSupplierCode());
        Page<SupplierContact> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierContact::new));
        PageResult<SupplierContact> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<SupplierContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierContactVO::new);
        setEveryName(vos);
        //解密字段
        vos.forEach(x -> {
            x.setDepartment(Sm4Util.decoder(x.getDepartment()));
            x.setPosition(Sm4Util.decoder(x.getPosition()));
            x.setLandline(Sm4Util.decoder(x.getLandline()));
            x.setMobile(Sm4Util.decoder(x.getMobile()));
            x.setEmail(Sm4Util.decoder(x.getEmail()));
        });
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public List<SupplierContactVO> getBySupplierCode(List<String> supplierCodes) throws Exception {
        LambdaQueryWrapperX<SupplierContact> condition = new LambdaQueryWrapperX<>(SupplierContact.class);
        condition.eq(SupplierContact::getDefaultContact, "是");
        condition.in(SupplierContact::getSupplierCode, supplierCodes);
        List<SupplierContact> supplierContactes = this.list(condition);
        List<SupplierContactVO> vos = BeanCopyUtils.convertListTo(supplierContactes, SupplierContactVO::new);
        return vos;
    }

    public static class SupplierContactExcelListener extends AnalysisEventListener<SupplierContactDTO> {

        private final List<SupplierContactDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierContactDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierContactDTO> getData() {
            return data;
        }
    }


}
