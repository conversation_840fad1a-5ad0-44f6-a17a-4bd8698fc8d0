package com.chinasie.orion.controller;

import com.chinasie.orion.constant.MajorRepairStatusEnum;
import com.chinasie.orion.domain.dto.MajorRepairPlanDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.domain.vo.SimStatusVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:44
 * @description:
 */

@RestController
@RequestMapping("/major-repair-plan")
@Api(tags = "大修计划")
public class  MajorRepairPlanController  {

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【{{#repairRound}}】详情", type = "MajorRepairPlan", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<MajorRepairPlanVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairPlanVO rsp = majorRepairPlanService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增大修【{{#majorRepairPlanDTO.name}}】", type = "MajorRepairPlan", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        String rsp =  majorRepairPlanService.create(majorRepairPlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param majorRepairPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑大修【{{#majorRepairPlanDTO.name}}】信息", type = "MajorRepairPlan", subType = "编辑", bizNo = "{{#majorRepairPlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        Boolean rsp = majorRepairPlanService.edit(majorRepairPlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除大修【{{#repairRounds}}】Id信息【{{#ids}}】数据", type = "MajorRepairPlan", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairPlanService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除大修【{{#repairRounds}}】Id信息【{{#ids}}】数据", type = "MajorRepairPlan", subType = "删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairPlanService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询大修分页数据", type = "MajorRepairPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanVO>> pages(@RequestBody Page<MajorRepairPlanDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanVO> rsp =  majorRepairPlanService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation(value = "获取状态列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取大修状态列表", type = "MajorRepairPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/status/list", method = RequestMethod.POST)
    public ResponseDTO<List<SimStatusVO>> statusList() throws Exception {
        List<SimStatusVO> rsp =  majorRepairPlanService.statusList( );
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询大修列表数据", type = "MajorRepairPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<MajorRepairPlanVO>> list(@RequestBody MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        List<MajorRepairPlanVO> rsp =  majorRepairPlanService.listByEntity( majorRepairPlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 小于当前时间两年内的大修数据
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "小于当前时间两年内的大修数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】两年内的大修加护数据", type = "MajorRepairPlan", subType = "查询", bizNo = "")
    @RequestMapping(value = "/le/two/year/list", method = RequestMethod.POST)
    public ResponseDTO<List<SimpleVO>> leTwolist() throws Exception {
        List<SimpleVO> rsp =  majorRepairPlanService.leTowlist();
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取未结束的大修列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询未结束的大修列表", type = "MajorRepairPlan", subType = "查询", bizNo = "")
    @RequestMapping(value = "/unfinish/list", method = RequestMethod.POST)
    public ResponseDTO<List<MajorRepairPlanVO>> unfinishList() throws Exception {
        List<MajorRepairPlanVO> rsp =  majorRepairPlanService.unfinishList( );
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "判断大修轮次数据是否存在")
    @RequestMapping(value = "/isExist", method = RequestMethod.POST)
    public ResponseDTO<Boolean> isExist(@RequestBody MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        Boolean rsp =  majorRepairPlanService.isExist(majorRepairPlanDTO.getRepairRound(),majorRepairPlanDTO.getId());
        return new ResponseDTO<>(rsp);
    }

}
