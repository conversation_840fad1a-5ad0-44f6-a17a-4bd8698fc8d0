package com.chinasie.orion.controller.approval;

import com.chinasie.orion.domain.dto.approval.FormulaDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateExpenseSubjectDTO;
import com.chinasie.orion.domain.vo.approval.FunctionVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateExpenseSubjectVO;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateExpenseSubjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectApprovalEstimateTemplateExpenseSubject 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@RestController
@RequestMapping("/projectApprovalEstimateTemplateExpenseSubject")
@Api(tags = "概算模板科目")
public class ProjectApprovalEstimateTemplateExpenseSubjectController {

    @Autowired
    private ProjectApprovalEstimateTemplateExpenseSubjectService projectApprovalEstimateTemplateExpenseSubjectService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【概算模板科目】【{{#name}}】】-【{{#number}}】详情", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalEstimateTemplateExpenseSubjectVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubjectVO rsp = projectApprovalEstimateTemplateExpenseSubjectService.detail(id,pageCode);
        LogRecordContext.putVariable("name",rsp.getName());
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【概算模板科目】数据【{{#projectApprovalEstimateTemplateExpenseSubjectDTO.name}}】", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO) throws Exception {
        String rsp =  projectApprovalEstimateTemplateExpenseSubjectService.create(projectApprovalEstimateTemplateExpenseSubjectDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 批量新增
     *
     * @param subjectList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增(批量)")
    @RequestMapping(value = "/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【概算模板科目】数据【{{#names}}】", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "批量新增", bizNo = "")
    public ResponseDTO createBatch(@RequestBody List<ProjectApprovalEstimateTemplateExpenseSubjectDTO> subjectList) throws Exception {
        LogRecordContext.putVariable("names", subjectList.stream().map(ProjectApprovalEstimateTemplateExpenseSubjectDTO::getName).collect(Collectors.joining(",")));
        return new ResponseDTO<>(projectApprovalEstimateTemplateExpenseSubjectService.createBatch(subjectList));
    }

    /**
     * 编辑
     *
     * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【概算模板科目】数据【{{#projectApprovalEstimateTemplateExpenseSubjectDTO.name}}】", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "编辑", bizNo = "{{#projectApprovalEstimateTemplateExpenseSubjectDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateExpenseSubjectService.edit(projectApprovalEstimateTemplateExpenseSubjectDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【概算模板科目】数据", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateExpenseSubjectService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【概算模板科目】数据", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateExpenseSubjectService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【概算模板科目】分页数据", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectApprovalEstimateTemplateExpenseSubjectVO>> pages(@RequestBody Page<ProjectApprovalEstimateTemplateExpenseSubjectDTO> pageRequest) throws Exception {
        Page<ProjectApprovalEstimateTemplateExpenseSubjectVO> rsp =  projectApprovalEstimateTemplateExpenseSubjectService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 设置公式
     * @param formulaDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "设置公式")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【概算模板科目】设置公式", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "设置公式", bizNo = "")
    @RequestMapping(value = "/setFormula", method = RequestMethod.PUT)
    public ResponseDTO<Boolean> setFormula(@RequestBody @Validated FormulaDTO formulaDTO) throws Exception {
        Boolean rsp =  projectApprovalEstimateTemplateExpenseSubjectService.setFormula( formulaDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 取消公式
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "取消公式")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【概算模板科目】取消公式", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "取消公式", bizNo = "{{#id}}")
    @RequestMapping(value = "/cancelFormula", method = RequestMethod.PUT)
    public ResponseDTO<Boolean> cancelFormula(@RequestParam("id") String id) throws Exception {
        Boolean rsp =  projectApprovalEstimateTemplateExpenseSubjectService.cancelFormula(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取公式函数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取公式函数")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/function/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【概算模板科目】公式函数", type = "Function", subType = "获取公式函数", bizNo = "")
    public ResponseDTO<List<FunctionVO>> getFunctionList() throws Exception {
        List<FunctionVO> rsp =  projectApprovalEstimateTemplateExpenseSubjectService.getFunctionList();
        return new ResponseDTO<>(rsp);
    }


    /**
     * 获取公式变量
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取公式变量")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/formulaVariable/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【概算模板科目】公式变量", type = "ProjectApprovalEstimateTemplateExpenseSubject", subType = "获取公式变量", bizNo = "")
    public ResponseDTO<List<ProjectApprovalEstimateTemplateExpenseSubjectVO>> getFormulaVariable(@RequestParam("estimateTemplateId") String estimateTemplateId) throws Exception {
        List<ProjectApprovalEstimateTemplateExpenseSubjectVO> rsp =  projectApprovalEstimateTemplateExpenseSubjectService.getFormulaVariable(estimateTemplateId);
        return new ResponseDTO<>(rsp);
    }
}
