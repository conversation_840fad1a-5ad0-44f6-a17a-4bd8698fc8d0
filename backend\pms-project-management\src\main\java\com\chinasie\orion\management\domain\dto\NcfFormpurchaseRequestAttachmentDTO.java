package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormpurchaseRequestAttachment DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
@ApiModel(value = "NcfFormpurchaseRequestAttachmentDTO对象", description = "采购申请附件")
@Data
@ExcelIgnoreUnannotated
public class NcfFormpurchaseRequestAttachmentDTO extends ObjectDTO implements Serializable {

    /**
     * 采购申请编号
     */
    @ApiModelProperty(value = "采购申请编号")
    @ExcelProperty(value = "采购申请编号 ", index = 0)
    private String code;

    /**
     * 节点
     */
    @ApiModelProperty(value = "节点")
    @ExcelProperty(value = "节点 ", index = 1)
    private String node;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @ExcelProperty(value = "文件名 ", index = 2)
    private String attachmentName;

    /**
     * 文件密级
     */
    @ApiModelProperty(value = "文件密级")
    @ExcelProperty(value = "文件密级 ", index = 3)
    private String classificationLevel;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    @ExcelProperty(value = "保密期限 ", index = 4)
    private String secrecyTerm;

    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型")
    @ExcelProperty(value = "文档类型 ", index = 5)
    private String documentType;


}
