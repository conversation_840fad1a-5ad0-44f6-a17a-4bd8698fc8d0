package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * MarketContractMilestoneAcceptance VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@ApiModel(value = "MarketContractMilestoneAcceptanceVO对象", description = "市场合同里程碑验收信息")
@Data
public class MarketContractMilestoneAcceptanceVO extends  ObjectVO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 验收人
     */
    @ApiModelProperty(value = "验收人")
    private String acceptanceUserId;

    /**
     * 验收人名称
     */
    @ApiModelProperty(value = "验收人名称")
    private String acceptanceUserName;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptDate;


    /**
     * 本次验收比例
     */
    @ApiModelProperty(value = "本次验收比例")
    private BigDecimal acceptanceRatio;

    /**
     * 本次验收金额
     */
    @ApiModelProperty(value = "本次验收金额")
    private BigDecimal acceptanceAmt;


}
