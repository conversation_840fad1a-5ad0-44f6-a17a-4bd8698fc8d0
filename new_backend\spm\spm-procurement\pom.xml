<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>spm</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <description>采购管理</description>
    <properties>
        <java.version>11</java.version>
    </properties>
    <artifactId>spm-procurement</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-file</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>reload4j</artifactId>
                    <groupId>ch.qos.reload4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-file-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-msc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-api</artifactId>
            <version>3.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pmi-api</artifactId>
            <version>${pmi-version}</version>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>spm-common</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.chinasie.orion</groupId>-->
        <!--            <artifactId>pms-project</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.chinasie.orion</groupId>-->
        <!--            <artifactId>pms-production</artifactId>-->
        <!--        </dependency>-->
    </dependencies>
<!--    <build>-->
<!--        <plugins>-->
<!--            &lt;!&ndash;当前module不允许deploy&ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-deploy-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <skip>true</skip>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>