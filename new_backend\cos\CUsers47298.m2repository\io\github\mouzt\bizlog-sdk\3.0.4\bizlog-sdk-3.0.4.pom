<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>bizlog-sdk</artifactId>
    <version>3.0.4</version>
    <parent>
        <groupId>io.github.mouzt</groupId>
        <artifactId>mzt-biz-log</artifactId>
        <version>1.0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!--    <properties>-->
    <!--        <maven.deploy.skip>true</maven.deploy.skip>-->
    <!--    </properties>-->
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <version>2.3.4.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>de.danielbechler</groupId>
            <artifactId>java-object-diff</artifactId>
            <version>0.95</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.12</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.1.5.Final</version>
        </dependency>
    </dependencies>

</project>