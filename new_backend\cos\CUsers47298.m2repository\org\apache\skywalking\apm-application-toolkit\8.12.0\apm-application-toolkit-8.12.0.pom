<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>java-agent</artifactId>
        <groupId>org.apache.skywalking</groupId>
        <version>8.12.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>apm-application-toolkit</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>apm-toolkit-log4j-1.x</module>
        <module>apm-toolkit-log4j-2.x</module>
        <module>apm-toolkit-logback-1.x</module>
        <module>apm-toolkit-opentracing</module>
        <module>apm-toolkit-trace</module>
        <module>apm-toolkit-meter</module>
        <module>apm-toolkit-micrometer-registry</module>
        <module>apm-toolkit-kafka</module>
    </modules>
</project>
