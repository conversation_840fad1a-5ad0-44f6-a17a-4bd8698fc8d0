export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    ellipsis: true,
  },
  {
    title: '标题',
    dataIndex: 'name',
    key: 'name',

    width: '200px',
    align: 'left',
    slots: { customRender: 'name' },
    ellipsis: true,
  },

  {
    title: '提出人',
    dataIndex: 'exhibitor',
    key: 'exhibitor',
    width: '60px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'exhibitor' },
    ellipsis: true,
  },
  {
    title: '提出日期',
    dataIndex: 'proposedTime',
    key: 'proposedTime',
    width: '100px',
    align: 'left',
    slots: { customRender: 'proposedTime' },
    ellipsis: true,
  },
  {
    title: '期望完成日期',
    dataIndex: 'predictEndTime',
    key: 'predictEndTime',

    width: '120px',
    align: 'left',
    slots: { customRender: 'predictEndTime' },
    ellipsis: true,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevelName',
    key: 'priorityLevel',

    width: '60px',
    align: 'left',
    slots: { customRender: 'priorityLevel' },
    ellipsis: true,
  },
  {
    title: '进度',
    dataIndex: 'schedule',
    key: 'schedule',

    width: '60px',
    align: 'left',
    ellipsis: true,
    slots: { customRender: 'schedule' },
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '60px',
    align: 'left',
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalId',

    width: '60px',
    align: 'left',
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    width: '150px',
    align: 'left',
    ellipsis: true,
    slots: { customRender: 'modifyTime' },
  },
];
