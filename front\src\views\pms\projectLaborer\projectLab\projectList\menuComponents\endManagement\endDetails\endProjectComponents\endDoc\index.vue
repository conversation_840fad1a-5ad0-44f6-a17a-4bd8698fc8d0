<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    class="pdmBasicTable"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if=" isPower('JX_container_button_04', powerData)"
        type="primary"
        icon="add"
        @click="uploadshow"
      >
        上传
      </BasicButton>
    </template>
    <template #modifyTime="{ text }">
      {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>

  <BasicUpload
    :max-number="100"
    :max-size="10"
    :show-up-modal="isShow"
    :is-button="false"
    :pagination="'false'"
    @saveChange="saveChange"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, BasicUpload, isPower, ITableActionItem, openFile, OrionTable,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
/* table */
import dayjs from 'dayjs';
import { columns } from './src/table.config';
import { getListDetailsApi, removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import { upDocApi } from '/@/views/pms/projectLaborer/api/docManagement';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    BasicButton,
    BasicTableAction,
    BasicUpload,
    OrionTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const tableRef = ref(null);
    const state = reactive({
      addNodeModalData: {},
      showVisible: false,
      powerData: [],
      fileId: undefined,
    });
    state.powerData = inject('powerData');

    let projectId: any = inject('projectId');
    function saveChange(successAll) {
      const fileArr = successAll.map((item) => ({
        filePath: item.result.filePath,
        filePostfix: item.result.filePostfix,
        fileSize: item.result.fileSize,
        name: item.result.name,
        dataId: props.id,
        fileTool: item.openTool,
        secretLevel: item.result.secretLevel,
        securityLimit: item.securityLimit,
        projectId: projectId.value,
      }));
      upDocApi(fileArr).then(() => {
        message.success('上传成功');
        getFormData();
      });
    }

    const isShow = ref(false);
    /* 上传 */
    const uploadshow = () => {
      isShow.value = !isShow.value;
    };

    const getFormData = async () => {
      tableRef.value?.reload();
    };

    const tableOptions = {
      deleteToolButton: 'add|enable|disable',
      rowSelection: {},
      showSmallSearch: false,
      api() {
        return getListDetailsApi(props.id);
      },
      batchDeleteApi({ ids }) {
        return removeBatchDetailsApi(ids)
          .then((res) => {
            message.success('删除成功');
            getFormData();
          });
      },
      columns,
    };

    const actionsBtn = computed(() => {
      const btns:ITableActionItem[] = [];

      if (isPower('JX_container_button_03', state.powerData)) {
        btns.push(
          {
            text: '打开',
            onClick(record:any) {
              openFile(record);
            },
          },
        );
      }

      if (isPower('JX_container_button_05', state.powerData)) {
        btns.push(
          {
            text: '下载',
            onClick(record:any) {
              downLoadById(record.id);
            },
          },
        );
      }
      if (isPower('JX_container_button_08', state.powerData)) {
        btns.push(
          {
            text: '删除',
            modal(record:any) {
              return removeBatchDetailsApi([record.id]).then(() => {
                message.success('删除成功');
                getFormData();
              });
            },
          },
        );
      }
      return btns;
    });

    return {
      tableRef,
      ...toRefs(state),
      /* 格式化时间 */
      formatterTime,
      /* 简易弹窗cb */
      confirm,

      dayjs,
      saveChange,
      isShow,
      tableOptions,
      actionsBtn,
      uploadshow,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
