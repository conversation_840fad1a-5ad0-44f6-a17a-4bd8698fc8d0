<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import { CSSProperties, ref, Ref } from 'vue';

const tableWrapStyle: CSSProperties = {
  height: '200px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();

const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: false,
  pagination: false,
  columns: [
    {
      title: '研读审查结论',
      dataIndex: '',
      width: '20%',
    },
    {
      title: '审查时间',
      dataIndex: '',
      width: '30%',
    },
    {
      title: '审查存在问题',
      dataIndex: '',
    },
    {
      title: '纠正行动',
      dataIndex: '',
    },
    {
      title: '完成时间',
      dataIndex: '',
    },
    {
      title: '程序版本',
      dataIndex: '',
    },
    {
      title: '技术交底记录',
      dataIndex: '',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick() {
      },
    },
    {
      text: '编辑',
      modal() {
      },
    },
  ],
};
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.orion-table-header-wrap) {
  display: none;
}
</style>
