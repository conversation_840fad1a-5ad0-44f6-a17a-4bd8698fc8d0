package com.chinasie.orion.util;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * @author: lsy
 * @date: 2024/4/28
 * @description:
 */
@Component
public class AviatorUtils {

    private static final Integer max = 15;

    @PostConstruct
    public void addFunctions() {
        AviatorEvaluator.addFunction(new AvgFunction());
        AviatorEvaluator.addFunction(new RoundFunction());
        AviatorEvaluator.addFunction(new SumFunction());
    }

    /**
     * 获取计算公式值
     * @param variableMap
     * @param formula
     * @return
     */
    public BigDecimal calculateFormula(Map<String, Object> variableMap, String formula) {
        double result = checkFormula(variableMap, formula, null, 0);
        return new BigDecimal(result);
//        BigDecimal bigDecimal = new BigDecimal(result);
//        return bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 校验公式
     * @param variableMap
     * @param formula
     * @param key
     * @param i
     * @return
     */
    public double checkFormula(Map<String, Object> variableMap, String formula, String key, int i) {
        i++;
        if (i > max) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "公式超出最大递归深度");
        }
        List<String> variableFullNames;
        try {
            variableFullNames = AviatorEvaluator.compile(formula).getVariableFullNames();
        } catch (Exception e) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "公式无法正确解析");
        }
        for (String variableFullName : variableFullNames) {
            if (variableMap.containsKey(variableFullName)) {
                Object o = variableMap.get(variableFullName);
                if (o == null) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "公式变量无值或未保存值");
                }
                if (o instanceof String) {
                    checkFormula(variableMap, o.toString(), variableFullName, i);
                } else if (o instanceof Number) {
                    variableMap.put(variableFullName, ((Number) o).doubleValue());
                }
            } else {
                throw new PMSException(PMSErrorCode.PMS_ERR, "公式存在无法识别的变量");
            }
        }
        double v = ((Number) AviatorEvaluator.compile(formula).execute(variableMap)).doubleValue();

        if (StrUtil.isNotBlank(key)) {
            variableMap.put(key, v);
        }
        return v;
    }

    /**
     * 平均值函数
     */
    public static class AvgFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
            double sum = 0;
            for (AviatorObject arg : args) {
                sum += ((Number) arg.getValue(env)).doubleValue();
            }
            double avg = sum/args.length;
            return new AviatorDecimal(avg);
        }

        @Override
        public String getName() {
            return "avg";
        }
    }

    /**
     * 四舍五入函数
     */
    public static class RoundFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
            if (args.length != 2) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "round函数需要两个参数");
            }
            double number = ((Number) args[0].getValue(env)).doubleValue();
            int scale = ((Number) args[1].getValue(env)).intValue();
            double result = Math.round(number * Math.pow(10, scale))/Math.pow(10, scale);
            return new AviatorDecimal(result);
        }

        @Override
        public String getName() {
            return "round";
        }
    }

    /**
     * 求和函数
     */
    public static class SumFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
            BigDecimal sum = BigDecimal.ZERO;
            for (AviatorObject arg : args) {
                BigDecimal num = new BigDecimal(arg.getValue(env).toString());
                sum = sum.add(num);
            }
            return new AviatorDecimal(sum);
        }

        @Override
        public String getName() {
            return "sum";
        }
    }

}
