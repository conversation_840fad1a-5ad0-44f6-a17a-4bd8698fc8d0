package com.chinasie.orion.domain.dto.allocation;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ResourceAllocationOf implements Serializable {

    private static final long serialVersionUID = 1L;


    private String id;
    private String name;
    private String costCenterCode;
    private String parentId;
    private String userCode;
    private String dataType;
    private Integer repairOrgCount;
    private String details;

    private String number;
    private String realStartDate;
    private String realEndDate;
    private String repairRoundName;
    private String repairRoundCode;
    private String relaStartDate;
    private String relaEndDate;
    private Boolean isBasePermanent;
    private String rowId;
    private String relationId;
    private String orgId;
    private String basePlaceCode;
    private String basePlaceName;
    private String teamCode;
    private String teamName;
    private String specialtyCode;
    private String specialtyName;
}
