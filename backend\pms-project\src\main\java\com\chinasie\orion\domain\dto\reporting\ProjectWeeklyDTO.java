package com.chinasie.orion.domain.dto.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.constant.reporting.ReportingBusEnum;
import com.chinasie.orion.domain.entity.reporting.ProjectWeeklyContent;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectWeekly Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
@ApiModel(value = "ProjectWeeklyDTO对象", description = "项目周报表")
@Data
public class ProjectWeeklyDTO extends ObjectDTO implements Serializable {

    /**
     * 所在年份第几周
     */
    @ApiModelProperty(value = "所在年份第几周")
    private Integer week;

    /**
     * 一周的开始时间
     */
    @ApiModelProperty(value = "一周的开始时间")
    private Date weekBegin;

    /**
     * 一周结束时间
     */
    @ApiModelProperty(value = "一周结束时间")
    private Date weekEnd;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resp;

    /**
     * 整体进度
     */
    @ApiModelProperty(value = "整体进度")
    private Integer overallProgress;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String reviewedBy;

    /**
     * 抄送人多个时用英文逗号分隔
     */
    @ApiModelProperty(value = "抄送人多个时用英文逗号分隔")
    private String carbonCopyBy;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 内容总结
     */
    @ApiModelProperty(value = "内容总结")
    private String contentSummarize;

    @ApiModelProperty(value = "日报状态：NOT_SUBMITTED(未提交) SUBMITTED（提交）")
    private ReportingBusEnum reportingKey;


    @ApiModelProperty(value = "本周周报内容")
    @Size(min = 1, message = "本周周报内容不能为空")
    private List<ProjectWeeklyContentDTO> currentWeekContent;

    @ApiModelProperty(value = "下周周报报内容")
    private List<ProjectWeeklyContentDTO> nextWeekContent;

    @ApiModelProperty(value = "周报附件")
    private List<FileDTO> attachments;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    private String summary;

}
