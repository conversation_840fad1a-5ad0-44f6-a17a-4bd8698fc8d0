package com.chinasie.orion.domain.entity;
import com.baomidou.mybatisplus.annotation.*;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.BusinessOrgDataBind;
import com.chinasie.orion.sdk.core.data.bind.StatusDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectPerformance Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@TableName(value = "pmsx_project_performance")
@ApiModel(value = "ProjectPerformanceEntity对象", description = "项目绩效与指标关联")
@Data
public class ProjectPerformance  implements Serializable {


    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "creatorName")
    private String creatorId;

    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String creatorName;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "ownerName")
    private String ownerId;

    @ApiModelProperty("拥有者名字")
    @TableField(exist = false)
    private String ownerName;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @FieldBind(dataBind = UserDataBind.class, target = "modifyName")
    private String modifyId;

    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String modifyName;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = BusinessOrgDataBind.class, target = "businessOrgName")
    private String orgId;

    @ApiModelProperty("业务组织名称")
    @TableField(exist = false)
    private String businessOrgName;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    @FieldBind(dataBind = StatusDataBind.class, target = "dataStatus")
    private Integer status;

    @ApiModelProperty("状态对象")
    @TableField(exist = false)
    private DataStatusVO dataStatus;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 考核类型ID（字典配置）
     */
    @ApiModelProperty(value = "考核类型ID（字典配置）")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 考核年份
     */
    @ApiModelProperty(value = "考核年份")
    @TableField(value = "year")
    private String year;

    /**
     * 考核时间，可以是月份、季度、半年度标识，对于月度考核，可以直接存储月份（如'01'表示1月）；对于季度考核，可以使用'Q1'、'Q2'、'Q3'、'Q4'表示第一至第四季度；对于半年度考核，可以使用'H1'、'H2'表示上半年和下半年；年度考核可以用'FY'表示全年。
     */
    @ApiModelProperty(value = "考核时间，可以是月份、季度、半年度标识，对于月度考核，可以直接存储月份（如'01'表示1月）；对于季度考核，可以使用'Q1'、'Q2'、'Q3'、'Q4'表示第一至第四季度；对于半年度考核，可以使用'H1'、'H2'表示上半年和下半年；年度考核可以用'FY'表示全年。")
    @TableField(value = "period")
    private String period;

    /**
     * 评价的用户ID
     */
    @ApiModelProperty(value = "评价的用户ID")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 总分
     */
    @ApiModelProperty(value = "总分")
    @TableField(value = "total_score")
    private BigDecimal totalScore;

    /**
     * 评分时间
     */
    @ApiModelProperty(value = "评分时间")
    @TableField(value = "score_date")
    private Date scoreDate;



}
