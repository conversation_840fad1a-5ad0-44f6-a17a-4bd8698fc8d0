package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.constant.GoodsStatusConstant;
import com.chinasie.orion.dict.GoodsServiceDict;
import com.chinasie.orion.domain.dto.GoodsServiceAddDTO;
import com.chinasie.orion.domain.dto.GoodsServiceStoreDTO;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.GoodsServiceStore;
import com.chinasie.orion.domain.entity.GoodsStoreRecord;
import com.chinasie.orion.domain.vo.GoodsServiceStoreVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.repository.GoodsServiceStoreMapper;
import com.chinasie.orion.repository.GoodsStoreRecordMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.GoodsServiceStoreService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * GoodsServiceStore 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 20:59:53
 */
@Service
public class GoodsServiceStoreServiceImpl extends OrionBaseServiceImpl<GoodsServiceStoreMapper, GoodsServiceStore> implements GoodsServiceStoreService {

    @Resource
    private GoodsServiceStoreMapper goodsServiceStoreMapper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private GoodsStoreRecordMapper goodsStoreRecordMapper;

    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public GoodsServiceStoreVO detail(String id) throws Exception {
        GoodsServiceStore goodsServiceStore =goodsServiceStoreMapper.selectById(id);
        GoodsServiceStoreVO result = BeanCopyUtils.convertTo(goodsServiceStore,GoodsServiceStoreVO::new);
        List<GoodsServicePlan> goodsServicePlans = goodsServicePlanMapper.selectList(new LambdaQueryWrapperX<GoodsServicePlan>()
                .eq(GoodsServicePlan::getGoodsServiceNumber, result.getGoodsServiceNumber())
                .eq(GoodsServicePlan::getProjectId,result.getProjectId()));
        if (CollectionUtil.isEmpty(goodsServicePlans)){
            result.setBuyPlanId(StrUtil.EMPTY);
            result.setPlanNumber(StrUtil.EMPTY);
        }else {
            result.setBuyPlanId(goodsServicePlans.get(0).getBuyPlanId());
            result.setPlanNumber(goodsServicePlans.get(0).getNumber());
        }

        HashMap<String, UserVO> userNameMap = this.getUserName();
        // 设置类型和计量单位
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);

        result.setType(goodsServiceTypeMap.getOrDefault(result.getTypeCode(),new DictValueVO()).getDescription());
        if (GoodsServiceDict.GOODS_TYPE_CODE.equals(result.getTypeCode())){
            result.setUnit(goodsUnitMap.getOrDefault(result.getUnitCode(),new DictValueVO()).getDescription());
        }else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(result.getTypeCode())){
            result.setUnit(serviceUnitMap.getOrDefault(result.getUnitCode(),new DictValueVO()).getDescription());
        }
        result.setDateCreatorName(userNameMap.getOrDefault(result.getCreatorId(),new UserVO()).getName());
        return result;
    }

    /**
     *  新增
     *
     * * @param goodsServiceStoreDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  GoodsServiceStoreVO create(GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception {
        String id = goodsServiceStoreDTO.getId();
        if (BeanUtil.isEmpty(id)){
            List<GoodsServiceStore> goodsServiceStores = list(new LambdaQueryWrapperX<GoodsServiceStore>()
                    .eq(GoodsServiceStore::getGoodsServiceNumber, goodsServiceStoreDTO.getGoodsServiceNumber())
                    .eq(GoodsServiceStore::getProjectId,goodsServiceStoreDTO.getProjectId()));
            if (CollectionUtil.isNotEmpty(goodsServiceStores)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "物资服务编码重复");
            }
            GoodsServiceStore goodsServiceStore =BeanCopyUtils.convertTo(goodsServiceStoreDTO,GoodsServiceStore::new);
            GoodsStoreRecord goodsStoreRecord = BeanCopyUtils.convertTo(goodsServiceStore, GoodsStoreRecord::new);
            goodsServiceStore.setTotalStoreAmount(goodsServiceStore.getStoreAmount());
            int insert = goodsServiceStoreMapper.insert(goodsServiceStore);
            goodsStoreRecord.setGoodsServiceStoreId(goodsServiceStore.getId());
            goodsStoreRecordMapper.insert(goodsStoreRecord);
            GoodsServiceStoreVO rsp = BeanCopyUtils.convertTo(goodsServiceStore,GoodsServiceStoreVO::new);
            return rsp;
        }else {
            GoodsServiceStore oldStore = getById(id);
            if (BeanUtil.isEmpty(oldStore)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前项不存在");
            }
            if (GoodsStatusConstant.P_OVER_STORAGE != oldStore.getStatus()){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前状态不支持直接入库");
            }
            // 先根据类型查看是物资还是服务
            String typeCode = oldStore.getTypeCode();
            if (!goodsServiceStoreDTO.getTypeCode().equals(typeCode)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "物资服务类型不匹配");
            }
            // 服务类型，更新记录
            if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(typeCode)){
                List<GoodsStoreRecord> goodsStoreRecords = goodsStoreRecordMapper.selectList(new LambdaQueryWrapperX<GoodsStoreRecord>()
                        .eq(GoodsStoreRecord::getGoodsServiceStoreId, id));
                if (!CollectionUtil.isEmpty(goodsStoreRecords)){
                    GoodsStoreRecord goodsStoreRecord = goodsStoreRecords.get(0);
                    BigDecimal oldStoreAmount = goodsStoreRecord.getStoreAmount();
                    BigDecimal newStoreAmount = oldStoreAmount.add(goodsServiceStoreDTO.getStoreAmount());
                    goodsStoreRecord.setStoreAmount(newStoreAmount);
                    goodsStoreRecord.setRemark(goodsServiceStoreDTO.getRemark());
                    goodsStoreRecord.setStoreTime(goodsServiceStoreDTO.getStoreTime());
                    goodsStoreRecordMapper.updateById(goodsStoreRecord);
                    oldStore.setTotalStoreAmount(oldStore.getTotalStoreAmount().add(goodsServiceStoreDTO.getStoreAmount()));
                    oldStore.setStoreTime(goodsServiceStoreDTO.getStoreTime());
                    oldStore.setRemark(goodsServiceStoreDTO.getRemark());
                    goodsServiceStoreMapper.updateById(oldStore);
                }else {
                    GoodsStoreRecord goodsStoreRecord = BeanCopyUtils.convertTo(goodsServiceStoreDTO, GoodsStoreRecord::new);
                    goodsStoreRecord.setGoodsServiceStoreId(oldStore.getId());
                    goodsStoreRecord.setId(null);
                    goodsStoreRecord.setStoreTime(goodsServiceStoreDTO.getStoreTime());
                    goodsStoreRecordMapper.insert(goodsStoreRecord);
                }
                // 物资类型 新增记录
            }else if (GoodsServiceDict.GOODS_TYPE_CODE.equals(typeCode)){
                GoodsStoreRecord goodsStoreRecord = BeanCopyUtils.convertTo(goodsServiceStoreDTO, GoodsStoreRecord::new);
                goodsStoreRecord.setGoodsServiceStoreId(oldStore.getId());
                goodsStoreRecord.setId(null);
//                goodsStoreRecord.setStatus(null);
//                goodsStoreRecord.setStoreTime(goodsServiceStoreDTO.getStoreTime());
                goodsStoreRecordMapper.insert(goodsStoreRecord);
                // 修改存储表值
                oldStore.setTotalStoreAmount(oldStore.getTotalStoreAmount().add(goodsServiceStoreDTO.getStoreAmount()));
                oldStore.setStoreTime(goodsServiceStoreDTO.getStoreTime());
                oldStore.setStoreAmount(goodsServiceStoreDTO.getStoreAmount());
                oldStore.setRemark(goodsServiceStoreDTO.getRemark());
                goodsServiceStoreMapper.updateById(oldStore);
            }else {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "物资服务类型有误");
            }
        }
        GoodsServiceStore goodsServiceStore =BeanCopyUtils.convertTo(goodsServiceStoreDTO,GoodsServiceStore::new);
        GoodsServiceStoreVO rsp = BeanCopyUtils.convertTo(goodsServiceStore,GoodsServiceStoreVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param goodsServiceStoreDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception {

        GoodsServiceStore store = getById(goodsServiceStoreDTO.getId());
        if (BeanUtil.isEmpty(store)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在");
        }
        if (GoodsStatusConstant.S_UN_AUDITED != store.getStatus()){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前存在物资服务状态不支持修改");
        }
        GoodsServiceStore goodsServiceStore =BeanCopyUtils.convertTo(goodsServiceStoreDTO,GoodsServiceStore::new);
        goodsServiceStore.setTotalStoreAmount(goodsServiceStoreDTO.getStoreAmount());
        int update =  goodsServiceStoreMapper.updateById(goodsServiceStore);
        List<GoodsStoreRecord> goodsStoreRecords = goodsStoreRecordMapper.selectList(new LambdaQueryWrapperX<GoodsStoreRecord>()
                .eq(GoodsStoreRecord::getGoodsServiceStoreId, goodsServiceStore.getId()));
        if (CollectionUtil.isNotEmpty(goodsStoreRecords) && goodsStoreRecords.size() == 1){
            GoodsStoreRecord goodsStoreRecord = goodsStoreRecords.get(0);
            goodsStoreRecord.setDescription(goodsServiceStoreDTO.getDescription());
            goodsStoreRecord.setGoodsServiceNumber(goodsServiceStoreDTO.getGoodsServiceNumber());
            goodsStoreRecord.setStoreAmount(goodsServiceStoreDTO.getStoreAmount());
            goodsStoreRecordMapper.updateById(goodsStoreRecord);
        }
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<GoodsServiceStore> serviceStores = list(new LambdaQueryWrapperX<GoodsServiceStore>()
                .ne(GoodsServiceStore::getStatus,GoodsStatusConstant.S_UN_AUDITED)
                .in(GoodsServiceStore::getId, ids));
        if (CollectionUtil.isNotEmpty(serviceStores)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前存在物资服务状态不支持删除");
        }
        List<GoodsStoreRecord> goodsStoreRecords = goodsStoreRecordMapper.selectList(new LambdaQueryWrapperX<GoodsStoreRecord>()
                .in(GoodsStoreRecord::getGoodsServiceStoreId, ids));
//        if (!CollectionUtil.isEmpty(goodsStoreRecords)){
//            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前存在物资服务存在记录，不支持删除");
//        }
        int delete = goodsServiceStoreMapper.deleteBatchIds(ids);
        goodsStoreRecordMapper.deleteBatchIds(goodsStoreRecords);
        return SqlHelper.retBool(delete);
    }

    @Override
    public PageResult<GoodsServiceStoreVO> getGoodsServiceStorePage(Page<GoodsServiceStoreDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<GoodsServiceStore> pageCondition = new LambdaQueryWrapperX<>(GoodsServiceStore.class);
        GoodsServiceStoreDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(GoodsServiceStore::getProjectId, projectId);
        }
//        pageCondition.orderByDesc(GoodsServiceStore::getCreateTime);
//        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
//        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(conditionItems)) {
//            conditionItems = new ArrayList<>();
//        }
//        Map<String, ConditionItem> conditionItemMap = QueryConditionUtil.conditionListTurnToMap(conditionItems);
//        ConditionItem nameOfConditionItem = conditionItemMap.getOrDefault("name", new ConditionItem());
//        Object value = nameOfConditionItem.getValue();
//        if (Objects.nonNull(value)) {
//            pageCondition.and(sub->sub.like(GoodsServiceStore::getDescription, value).or().like(GoodsServiceStore::getGoodsServiceNumber, value));
//        }
//        // 类型对应的字典编码
//        ConditionItem typeOfConditionItem = conditionItemMap.getOrDefault("typeCode", new ConditionItem());
//        Object typeValue = typeOfConditionItem.getValue();
//        if (Objects.nonNull(typeValue)) {
//            pageCondition.eq(GoodsServiceStore::getTypeCode, typeValue);
//        }
//        // 状态
//        ConditionItem statusOfConditionItem = conditionItemMap.getOrDefault("status", new ConditionItem());
//        Object statusValue = statusOfConditionItem.getValue();
//        if (Objects.nonNull(statusValue)) {
//            pageCondition.eq(GoodsServiceStore::getStatus, statusValue);
//        }
//        // 入库时间
//        ConditionItem storeTimeOfConditionItem = conditionItemMap.getOrDefault("storeTime", new ConditionItem());
//        Object storeTimeValue = storeTimeOfConditionItem.getValue();
//        if (Objects.nonNull(storeTimeValue)) {
//            pageCondition.eq(GoodsServiceStore::getStoreTime, storeTimeValue);
//        }
//        // 添加时间
//        ConditionItem createTimeOfConditionItem = conditionItemMap.getOrDefault("createTime", new ConditionItem());
//        Object createTimeValue = createTimeOfConditionItem.getValue();
//        if (Objects.nonNull(createTimeValue)) {
//            pageCondition.eq(GoodsServiceStore::getCreateTime, createTimeValue);
//        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,pageCondition);
        }
        Page<GoodsServiceStore> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<GoodsServiceStore> pageResult = goodsServiceStoreMapper.selectPage(realPageRequest, pageCondition);
        List<GoodsServiceStore> records = pageResult.getContent();
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
        HashMap<String, UserVO> userNameMap = this.getUserName();

        List<GoodsServiceStoreVO> goodsServiceStoreVOS = BeanCopyUtils.convertListTo(records, GoodsServiceStoreVO::new);
        goodsServiceStoreVOS.forEach(e->{
            e.setDateCreatorName(userNameMap.getOrDefault(e.getCreatorId(),new UserVO()).getName());
            if (GoodsServiceDict.GOODS_TYPE_CODE.equals(e.getTypeCode())){
                e.setUnit(goodsUnitMap.getOrDefault(e.getUnitCode(),new DictValueVO()).getDescription());
            }else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(e.getTypeCode())){
                e.setUnit(serviceUnitMap.getOrDefault(e.getUnitCode(),new DictValueVO()).getDescription());
            }
            e.setType(goodsServiceTypeMap.getOrDefault(e.getTypeCode(),new DictValueVO()).getDescription());
        });
        return new PageResult<>(goodsServiceStoreVOS, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotalSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoodsServiceStoreVO detailCreate(GoodsServiceAddDTO goodsServiceAddDTO) throws Exception {
        String id = goodsServiceAddDTO.getId();
        GoodsServiceStore oldStore = getById(id);
        oldStore.setStoreAmount(goodsServiceAddDTO.getStoreAmount());
        oldStore.setStoreTime(goodsServiceAddDTO.getStoreTime());
        oldStore.setRemark(goodsServiceAddDTO.getRemark());
        BigDecimal storeAmount = oldStore.getStoreAmount();
        String typeCode = oldStore.getTypeCode();
        if (GoodsStatusConstant.P_OVER_STORAGE != oldStore.getStatus()){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前状态不支持直接入库");
        }
        if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(typeCode)){
            List<GoodsStoreRecord> goodsStoreRecords = goodsStoreRecordMapper.selectList(new LambdaQueryWrapperX<GoodsStoreRecord>()
                    .eq(GoodsStoreRecord::getGoodsServiceStoreId, id));
            if (!CollectionUtil.isEmpty(goodsStoreRecords)){
                GoodsStoreRecord goodsStoreRecord = goodsStoreRecords.get(0);
                BigDecimal oldStoreAmount = goodsStoreRecord.getStoreAmount();
                BigDecimal newStoreAmount = oldStoreAmount.add(storeAmount);
                goodsStoreRecord.setStoreAmount(newStoreAmount);
                goodsStoreRecord.setStatus(goodsStoreRecord.getStatus());
                goodsStoreRecordMapper.updateById(goodsStoreRecord);
                oldStore.setTotalStoreAmount(oldStore.getTotalStoreAmount().add(storeAmount));
                goodsServiceStoreMapper.updateById(oldStore);
                updatePlan(oldStore);
            }else {
                GoodsStoreRecord goodsStoreRecord = insertRecord(oldStore, storeAmount);
                updatePlan(oldStore);
            }
        }else if (GoodsServiceDict.GOODS_TYPE_CODE.equals(typeCode)){
            GoodsStoreRecord goodsStoreRecord = insertRecord(oldStore, storeAmount);
            oldStore.setTotalStoreAmount(oldStore.getTotalStoreAmount().add(storeAmount));
            oldStore.setStoreTime(new Date());
            goodsServiceStoreMapper.updateById(oldStore);
            updatePlan(oldStore);
        }else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "物资服务类型有误");
        }
        GoodsServiceStoreVO rsp = BeanCopyUtils.convertTo(oldStore,GoodsServiceStoreVO::new);
        return rsp;
    }

    private void updatePlan(GoodsServiceStore oldStore) {
        List<GoodsServicePlan> goodsServicePlans = goodsServicePlanMapper.selectList(new LambdaQueryWrapperX<GoodsServicePlan>()
                .eq(GoodsServicePlan::getGoodsServiceNumber, oldStore.getGoodsServiceNumber())
                .eq(GoodsServicePlan::getProjectId,oldStore.getProjectId()));
        if (CollectionUtil.isNotEmpty(goodsServicePlans)){
            GoodsServicePlan goodsServicePlan = goodsServicePlans.get(0);
            goodsServicePlan.setTotalStoreAmount(oldStore.getTotalStoreAmount());
            goodsServicePlan.setStatus(GoodsStatusConstant.P_OVER_STORAGE);
            goodsServicePlanMapper.updateById(goodsServicePlan);
        }
    }

    private GoodsStoreRecord insertRecord(GoodsServiceStore oldStore, BigDecimal storeAmount) {
        GoodsStoreRecord goodsStoreRecord = new GoodsStoreRecord();
        goodsStoreRecord.setProjectId(oldStore.getProjectId());
        goodsStoreRecord.setRemark(oldStore.getRemark());
        goodsStoreRecord.setDescription(oldStore.getDescription());
        goodsStoreRecord.setDescription(oldStore.getDescription());
        goodsStoreRecord.setGoodsServiceStoreId(oldStore.getId());
        goodsStoreRecord.setStoreTime(new Date());
        goodsStoreRecord.setGoodsServiceNumber(oldStore.getGoodsServiceNumber());
        goodsStoreRecord.setStoreAmount(storeAmount);
        int insert = goodsStoreRecordMapper.insert(goodsStoreRecord);
        return goodsStoreRecord;
    }

    /**
     * 获取用户信息Map
     * @return 用户信息
     */
    private HashMap<String, UserVO> getUserName() {
        List<UserVO> users = userRedisHelper.getAllUser();
        HashMap<String, UserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)){
            Map<String, UserVO> collect = users.stream().collect(Collectors.toMap(UserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        return userMap;
    }


}
