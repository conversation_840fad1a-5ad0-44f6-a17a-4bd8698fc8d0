package com.chinasie.orion.constant;

import org.springframework.util.StringUtils;
/**
 * <AUTHOR>
 * @className ProjectTypeEnum
 * @description 项目类型枚举值
 * @since 2023/10/28
 */
public enum ProjectTypeEnum {
    PROJECT_TYPE_SELL("sell", "市场项目"),

    PROJECT_TYPE_PEAK_PLAN("尖峰计划", "peak_plan"),
    PROJECT_TYPE_SCIENTIFIC_RESEARCH("scientific_research", "科研项目"),
    PROJECT_TYPE_INVEST_SERVER("invest_server", "投资/服务类项目"),
    PROJECT_TYPE_ACTIVITY("project_type_activity", "活动类"),
    PROJECT_TYPE_RESEARCH("project_type_research", "研发类"),

    PROJECT_TYPE_ZZ("no_initiation_zz", "未立项项目(ZZ)")
    ;
    private final String value;
    private final String desc;

    ProjectTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByValue(String value) {
        if(!StringUtils.hasText(value)){
            return "";
        }
        ProjectTypeEnum[] values =  ProjectTypeEnum.values();
        for(ProjectTypeEnum item : values){
            if(value.equals(item.getValue())){
                return item.getDesc();
            }
        }
        return "";
    }
}
