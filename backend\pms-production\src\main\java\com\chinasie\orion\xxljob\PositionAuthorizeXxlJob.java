package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.repository.PersonJobPostAuthorizeMapper;
import com.chinasie.orion.service.BasicUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PositionAuthorizeXxlJob {

    @Autowired
    PersonJobPostAuthorizeMapper personJobPostAuthorizeMapper;

    @Autowired
    BasicUserService basicUserService;

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @XxlJob("personPositionAuthorizeJob")
    public void positionAuthorize() {
        List<PersonJobPostAuthorize> personJobPostAuthorizes = personJobPostAuthorizeMapper.positionAuthorize();
        if (CollectionUtils.isEmpty(personJobPostAuthorizes)){
            return;
        }
        BasicUserVO basicUserVO = basicUserService.detailUserCode(personJobPostAuthorizes.get(0).getUserCode());
        personJobPostAuthorizes.forEach(item->{
            //发送消息
            mscBuildHandlerManager.send(item, MessageNodeDict.NODE_POSITION_AUTHORIZE, Objects.isNull(basicUserVO)?"":basicUserVO.getId());
        });
    }



}
