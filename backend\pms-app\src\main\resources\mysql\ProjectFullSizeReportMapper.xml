<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ProjectFullSizeReportMapper">


    <select id="getProjectFullSizeReportList" resultType="com.chinasie.orion.domain.vo.ProjectFullSizeReportVO">
    SELECT
	r.id,r.project_id AS projectId,
	r.internal_external AS internalExternal,
	r.nuclear_power AS nuclearPower,
	r.base AS base,
	r.business_classification AS businessClassification,
	r.operating_income AS operatingIncome,
	r.direct_purchase_cost AS directPurchaseCost,r.direct_travel_cost AS directTravelCost,
	r.labor_cost AS laborCost ,
	r.technical_configuration AS technicalConfiguration,r.project_direct_cost_gross AS projectDirectCostGross,
	r.project_direct_cost_gross_margin AS projectDirectCostGrossMargin,
	r.daily_administrative_expenses AS dailyAdministrativeExpenses,
	r.software_usage_fee AS softwareUsageFee,
	r.taxe_surcharge AS taxeSurcharge,
	r.project_gross_profit AS projectGrossProfit,
	r.project_gross_margin AS projectGrossMargin,
	r.management_fee AS managementFee,
	r.project_profit AS projectProfit,
	r.project_profit_margin AS projectProfitMargin,
	r.YEAR,
	r.wbs_expertise_center AS wbsExpertiseCenter,
	r.company_id AS companyId,
	r.parent_id AS parentId,
	r.type,
	p.number AS projectNumber,
	p.name as projectName,
	p.res_person as pmName,
	p.project_start_time as projectBeginTime,
	p.project_end_time as projectEndTime,
	i.client_one as clientOneCode,
	i.client_two as clientTwoCode,
	i.client_three as clientThreeCode,
	i.client_one_name as clientOneName,
	i.client_two_name as clientTwoName,
	i.client_three_name as clientThreeName
    FROM
	pmsx_project_full_size_report r
	JOIN pms_project p ON r.project_id = p.id
	JOIN pmsx_project_initiation i ON i.project_id = p.id
	where r.year = #{param.year}
		<if test="param.projectName != null">
			AND p.name like CONCAT('%', #{param.projectName}, '%')
		</if>
		<if test="param.projectNumber != null">
			AND p.number like CONCAT('%', #{param.projectNumber}, '%')
		</if>
		<if test="param.base != null">
			AND r.base like CONCAT('%', #{param.base}, '%')
		</if>
		<if test="param.internalExternal != null">
			AND r.internal_external like CONCAT('%', #{param.internalExternal}, '%')
		</if>

    </select>





</mapper>