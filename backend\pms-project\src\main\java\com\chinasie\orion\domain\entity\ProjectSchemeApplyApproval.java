package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeApplyApproval
 *
 * @author: yangFy
 * @date: 2023/4/17 17:36
 * @description:
 * <p>
 * 项目计划申请审批entity
 * </p>
 */
@Data
@TableName(value = "pms_project_scheme_apply_approval")
@ApiModel(value = "ProjectSchemeApplyApproval对象", description = "项目计划申请审批")
public class ProjectSchemeApplyApproval extends ObjectEntity implements Serializable {

  /**
   *
   */
  @ApiModelProperty(value = "项目id")
  @TableField(value = "project_id")
  private String projectId;
  /**
   *
   */
  @ApiModelProperty(value = "项目计划id")
  @TableField(value = "project_scheme_id")
  private String projectSchemeId;
  /**
   *
   */
  @ApiModelProperty(value = "内容")
  @TableField(value = "content")
  private String content;
  /**
   *
   */
  @ApiModelProperty(value = "审批人")
  @TableField(value = "certifier")
  private String certifier;
  /**
   *
   */
  @ApiModelProperty(value = "是否通过")
  @TableField(value = "agreement")
  private Boolean agreement;
  /**
   *
   */
  @ApiModelProperty(value = "审批意见")
  @TableField(value = "feed_back")
  private String feedBack;

}
