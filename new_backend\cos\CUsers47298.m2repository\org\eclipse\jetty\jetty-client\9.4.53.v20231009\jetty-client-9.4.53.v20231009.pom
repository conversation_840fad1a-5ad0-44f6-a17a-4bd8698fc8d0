<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.eclipse.jetty</groupId>
    <artifactId>jetty-project</artifactId>
    <version>9.4.53.v20231009</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>jetty-client</artifactId>
  <name>Jetty :: Asynchronous HTTP Client</name>
  <properties>
    <bundle-symbolic-name>${project.groupId}.client</bundle-symbolic-name>
    <jetty.test.policy.loc>target/test-policy</jetty.test.policy.loc>
    <spotbugs.onlyAnalyze>org.eclipse.client.*</spotbugs.onlyAnalyze>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>unpack</id>
            <phase>generate-test-resources</phase>
            <goals>
              <goal>unpack</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>org.eclipse.jetty.toolchain</groupId>
                  <artifactId>jetty-test-policy</artifactId>
                  <version>${jetty-test-policy.version}</version>
                  <type>jar</type>
                  <overWrite>true</overWrite>
                  <includes>**/*.keystore,**/*.pem</includes>
                  <outputDirectory>${jetty.test.policy.loc}</outputDirectory>
                </artifactItem>
              </artifactItems>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <minimizeJar>true</minimizeJar>
              <shadedArtifactAttached>true</shadedArtifactAttached>
              <shadedClassifierName>hybrid</shadedClassifierName>
              <artifactSet>
                <includes>
                  <include>org.eclipse.jetty:jetty-http</include>
                  <include>org.eclipse.jetty:jetty-io</include>
                  <include>org.eclipse.jetty:jetty-util</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.eclipse.jetty.http</pattern>
                  <shadedPattern>org.eclipse.jetty.client.shaded.http</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.eclipse.jetty.io</pattern>
                  <shadedPattern>org.eclipse.jetty.client.shaded.io</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.eclipse.jetty.util</pattern>
                  <shadedPattern>org.eclipse.jetty.client.shaded.util</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-http</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-io</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-jmx</artifactId>
      <version>${project.version}</version>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-server</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-security</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.kerby</groupId>
      <artifactId>kerb-simplekdc</artifactId>
      <version>2.0.3</version>
      <scope>test</scope>
    </dependency>
    <!-- transitive dependency defined as a range and we don't want that -->
    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>json-smart</artifactId>
      <version>2.5.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty.toolchain</groupId>
      <artifactId>jetty-test-helper</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
    </dependency>
  </dependencies>
</project>
