package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.DemandManagementQueryDTO;
import com.chinasie.orion.domain.dto.DocumentTypeDTO;
import com.chinasie.orion.domain.entity.DocumentType;
import com.chinasie.orion.domain.vo.DocumentTypeTreeVO;
import com.chinasie.orion.domain.vo.TreeSimpleVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:39
 * @description:
 */
public interface DocumentTypeService extends OrionBaseService<DocumentType> {

    /**
     * 新增文档类型
     * @param documentTypeDTO
     * @return
     * @throws Exception
     */
    String saveDocumentType(DocumentTypeDTO documentTypeDTO) throws Exception;

    /**
     * 获取文档类型树
     * @param demandManagementQueryDTO
     * @return
     * @throws Exception
     */
    List<DocumentTypeTreeVO> getDocumentTypeTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception;

    /**
     * 获取文档类型树 简洁版
     * @param demandManagementQueryDTO
     * @return
     * @throws Exception
     */
    List<TreeSimpleVO> getDocumentTypeSimpleTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception;

    /**
     * 编辑文档类型
     * @param documentTypeDTO
     * @return
     * @throws Exception
     */
    Boolean editDocumentType(DocumentTypeDTO documentTypeDTO) throws Exception;

    /**
     * 删除文档类型
     * @param id
     * @return
     * @throws Exception
     */
    Boolean removeDocumentType(String id) throws Exception;
}
