<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive, watchEffect, ref,
} from 'vue';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
const baseInfoProps = reactive({
  list: [
    {
      label: '供应商编码',
      field: 'supplierNumber',
    },
    {
      label: '供应商名称',
      field: 'name',
    },
    {
      label: '供应商账号',
      field: 'account',
    },
    {
      label: '找回密码邮箱',
      field: 'findPassEamil',
    },
    {
      label: '供应商简称',
      field: 'simName',
    },
    {
      label: '供应商英文名称',
      field: 'ename',
    },
    {
      label: '邮政编码',
      field: 'emsNumber',
    },
    {
      label: '网址',
      field: 'url',
    },
    {
      label: '固定电话',
      field: 'landlinePhone',
    },
    {
      label: '分机',
      field: 'extension',
    },
    {
      label: '传真',
      field: 'fax',
    },
    {
      label: '组织类型',
      field: 'organizationType',
    },
    {
      label: '法人代表',
      field: 'legalrep',
    },
    {
      label: '企业性质',
      field: 'companyNature',
    },
    {
      label: '中广核集团参股或控股公司',
      field: 'zghChild',
    },
    {
      label: '上级主管单位',
      field: 'parentOrg',
    },
    {
      label: '主要控股公司',
      field: 'majorShareholder',
    },
    {
      label: '板块名称',
      field: 'sectorName',
    },
    {
      label: '供应商级别',
      field: 'supplierLevel',
    },
    {
      label: '资审有效期',
      field: 'qualValidity',
      valueRender: ({ record }) => (record.qualValidity ? dayjs(record.qualValidity).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '采购品类',
      field: 'procurementCat',
    },
    {
      label: '采购品类编码',
      field: 'procCatCode',
    },
    {
      label: '供应商缴费有效截止日期1',
      field: 'paymentEffectiveDeadline',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '供应商类别',
      field: 'supplierCategory',
    },
    {
      label: '可提供产品/服务文字描述',
      field: 'productsServicesDesc',
      gridColumn: '1 / span 4',
      wrap: true,
    },
    {
      label: '公司简介',
      field: 'companyOverview',
      gridColumn: '1 / span 4',
      wrap: true,
    },
    {
      label: '供货范围文本描述',
      field: 'deliveryScopeDesc',
      gridColumn: '1 / span 4',
      wrap: true,
    },
  ],
  column: 4,
  dataSource: null,
});

const supplierInfo = inject('supplierInfo');
watchEffect(() => {
  baseInfoProps.dataSource = supplierInfo;
});

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>