package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectFinanceIndex DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
@ApiModel(value = "ProjectFinanceIndexDTO对象", description = "项目-财务指标")
@Data
@ExcelIgnoreUnannotated
public class ProjectFinanceIndexDTO extends  ObjectDTO   implements Serializable{

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述")
    @ExcelProperty(value = "指标描述 ", index = 0)
    private String indexName;

    /**
     * 指标费用
     */
    @ApiModelProperty(value = "指标费用")
    @ExcelProperty(value = "指标费用 ", index = 1)
    private BigDecimal indexCost;

    /**
     * 指标计划金额
     */
    @ApiModelProperty(value = "指标计划金额")
    @ExcelProperty(value = "指标计划金额 ", index = 2)
    private BigDecimal indexPlan;

    /**
     * 达成率
     */
    @ApiModelProperty(value = "达成率")
    @ExcelProperty(value = "达成率 ", index = 3)
    private String achievementRate;

    /**
     * 结余费用
     */
    @ApiModelProperty(value = "结余费用")
    @ExcelProperty(value = "结余费用 ", index = 4)
    private BigDecimal residue;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 5)
    private String projectId;




}
