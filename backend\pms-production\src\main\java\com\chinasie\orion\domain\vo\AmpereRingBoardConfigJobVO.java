package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@Data
@ApiModel(value = "AmpereRingBoardConfigJobVO 对象",description = "安质环维护作业信息")
public class AmpereRingBoardConfigJobVO extends ObjectVO implements Serializable {
    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    /**
     * 是否看板展示
     */
    @ApiModelProperty(value = "是否看板展示")
    private Boolean isShow;

    /**
     * 作业编码
     */
    @ApiModelProperty(value = "作业编码")
    private String jobCode;
}
