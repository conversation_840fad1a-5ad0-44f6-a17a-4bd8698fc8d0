package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.BillingAccountInformationDTO;
import com.chinasie.orion.domain.entity.BillingAccountInformation;
import com.chinasie.orion.domain.vo.BillingAccountInformationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.BillingAccountInformationMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BillingAccountInformationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * BillingAccountInformation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 17:13:07
 */
@Service
@Slf4j
public class BillingAccountInformationServiceImpl extends  OrionBaseServiceImpl<BillingAccountInformationMapper, BillingAccountInformation>   implements BillingAccountInformationService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BillingAccountInformationVO detail(String id, String pageCode) throws Exception {
        BillingAccountInformation billingAccountInformation =this.getById(id);
        BillingAccountInformationVO result = BeanCopyUtils.convertTo(billingAccountInformation,BillingAccountInformationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param billingAccountInformationDTO
     */
    @Override
    public  String create(BillingAccountInformationDTO billingAccountInformationDTO) throws Exception {
        BillingAccountInformation billingAccountInformation =BeanCopyUtils.convertTo(billingAccountInformationDTO,BillingAccountInformation::new);
        this.save(billingAccountInformation);

        String rsp=billingAccountInformation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param billingAccountInformationDTO
     */
    @Override
    public Boolean edit(BillingAccountInformationDTO billingAccountInformationDTO) throws Exception {
        BillingAccountInformation billingAccountInformation =BeanCopyUtils.convertTo(billingAccountInformationDTO,BillingAccountInformation::new);

        this.updateById(billingAccountInformation);

        String rsp=billingAccountInformation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BillingAccountInformationVO> pages( Page<BillingAccountInformationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BillingAccountInformation> condition = new LambdaQueryWrapperX<>( BillingAccountInformation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BillingAccountInformation::getCreateTime);


        Page<BillingAccountInformation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BillingAccountInformation::new));

        PageResult<BillingAccountInformation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BillingAccountInformationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BillingAccountInformationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BillingAccountInformationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "开票核算信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BillingAccountInformationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            BillingAccountInformationExcelListener excelReadListener = new BillingAccountInformationExcelListener();
        EasyExcel.read(inputStream,BillingAccountInformationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BillingAccountInformationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("开票核算信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BillingAccountInformation> billingAccountInformationes =BeanCopyUtils.convertListTo(dtoS,BillingAccountInformation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BillingAccountInformation-import::id", importId, billingAccountInformationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BillingAccountInformation> billingAccountInformationes = (List<BillingAccountInformation>) orionJ2CacheService.get("pmsx::BillingAccountInformation-import::id", importId);
        log.info("开票核算信息表导入的入库数据={}", JSONUtil.toJsonStr(billingAccountInformationes));

        this.saveBatch(billingAccountInformationes);
        orionJ2CacheService.delete("pmsx::BillingAccountInformation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BillingAccountInformation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BillingAccountInformation> condition = new LambdaQueryWrapperX<>( BillingAccountInformation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BillingAccountInformation::getCreateTime);
        List<BillingAccountInformation> billingAccountInformationes =   this.list(condition);

        List<BillingAccountInformationDTO> dtos = BeanCopyUtils.convertListTo(billingAccountInformationes, BillingAccountInformationDTO::new);

        String fileName = "开票核算信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BillingAccountInformationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BillingAccountInformationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class BillingAccountInformationExcelListener extends AnalysisEventListener<BillingAccountInformationDTO> {

        private final List<BillingAccountInformationDTO> data = new ArrayList<>();

        @Override
        public void invoke(BillingAccountInformationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BillingAccountInformationDTO> getData() {
            return data;
        }
    }


}
