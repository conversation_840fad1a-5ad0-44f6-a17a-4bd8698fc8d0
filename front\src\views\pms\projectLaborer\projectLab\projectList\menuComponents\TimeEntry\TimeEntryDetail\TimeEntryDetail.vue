<template>
  <Layout3
    v-loading="state.loadingStatus"
    :defaultActionId="state.actionId"
    :menuData="state.tabsOption"
    :type="2"
    :projectData="state.basicData"
    @menu-change="onMenuChange"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ state.basicData?.title }}
        </div>
        <div class="numberStyle">
          {{ state.basicData?.number }}
        </div>
      </div>
    </template>

    <template #header-right>
      <div v-if="state.basicData?.id">
        <BasicTableAction
          type="button"
          :actions="headButtonActions"
        />
      </div>
    </template>

    <template #footer>
      <WorkflowAction
        v-if="state.basicData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template v-if="state.basicData">
      <!--合同详情-->
      <BasicInfoIndex
        v-if="state.actionId===1"
        :id="id"
      />
      <!--审批流程-->
      <WorkflowView
        v-if="state.actionId===2"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>
  </Layout3>
</template>

<script lang="ts" setup>
import {
  computed, onMounted, provide, reactive, readonly, Ref, ref,
} from 'vue';
import { BasicTableAction, Layout3 } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import BasicInfoIndex from './component/BasicInfo/index.vue';
import Api from '/@/api';

const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const route = useRoute();

const id = useRoute()?.query?.id as string;
const type = useRoute()?.query?.type as any;
const state = reactive({
  actionId: 1,
  tabsOption: Number(type) === 1 ? [
    {
      name: '工时填报',
      id: 1,
    },
    {
      name: '审批流程',
      id: 2,
    },
  ] : [
    {
      name: '工时填报',
      id: 1,
    },
  ],
  basicData: null,
  allData: null,
  loadingStatus: false,
});

const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: state.basicData,
  afterEvent() {
    workflowViewRef.value?.init();
    getData();
  },
}));

// 下发所有合同数据
provide(
  'allData',
  computed(() => state.allData),
);

onMounted(() => {
  init();
});

async function init() {
  await getData();
}

async function getData() {
  if (!id) {
    return;
  }
  state.loadingStatus = false;
  const allData = await new Api(route.query.type === 1 ? `/pms/workHourFill/${id}` : `/pms/workHourFill/${id}`).fetch('', '', 'GET').finally(() => {
    state.loadingStatus = false;
  });
  state.allData = allData;
  state.basicData = allData;
}

function onMenuChange(menuItem) {
  state.actionId = menuItem.id;
}

// 修改tab页的方法
provide('updateTabs', readonly(onMenuChange));

// 下发所有的菜单数据
provide(
  'tabsId',
  computed(() => state.actionId),
);
const headButtonActions = computed(() => [
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: workflowActionRef.value?.isAdd && state.basicData?.status === 101,
    onClick() {
      workflowActionRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
]);
</script>

<style scoped lang="less">
.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
