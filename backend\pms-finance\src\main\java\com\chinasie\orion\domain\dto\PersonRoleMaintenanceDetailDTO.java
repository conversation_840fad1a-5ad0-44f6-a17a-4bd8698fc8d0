package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * PersonRoleMaintenanceDetail DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
@ApiModel(value = "PersonRoleMaintenanceDetailDTO对象", description = "人员角色维护表人员明细")
@Data
@ExcelIgnoreUnannotated
public class PersonRoleMaintenanceDetailDTO extends  ObjectDTO   implements Serializable{

    /**
     * 主表Id
     */
    @ApiModelProperty(value = "主表Id")
    @ExcelProperty(value = "主表Id ", index = 0)
    private String mianTableId;

    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    @ExcelProperty(value = "人员类型 ", index = 1)
    private String personType;

    /**
     * 人员Id
     */
    @ApiModelProperty(value = "人员Id")
    @ExcelProperty(value = "人员Id ", index = 2)
    private String personId;
}
