package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierProducts Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_products")
@ApiModel(value = "SupplierProductsEntity对象", description = "可提供产品")
@Data

public class SupplierProducts extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 可提供产品一级
     */
    @ApiModelProperty(value = "可提供产品一级")
    @TableField(value = "product_level_one")
    private String productLevelOne;

    /**
     * 可提供产品二级
     */
    @ApiModelProperty(value = "可提供产品二级")
    @TableField(value = "product_level_two")
    private String productLevelTwo;

    /**
     * 可提供产品三级
     */
    @ApiModelProperty(value = "可提供产品三级")
    @TableField(value = "product_level_three")
    private String productLevelThree;

    /**
     * 可提供产品四级
     */
    @ApiModelProperty(value = "可提供产品四级")
    @TableField(value = "product_level_four")
    private String productLevelFour;

}
