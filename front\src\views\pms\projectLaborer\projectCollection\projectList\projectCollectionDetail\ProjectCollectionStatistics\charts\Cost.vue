<script setup lang="ts">
import { formatMoney } from '/@/views/pms/utils/utils';
import { useChart } from './useChart';
import {
  computed, inject, onMounted, ref, Ref, watch,
} from 'vue';
import Api from '/@/api';
import SpinView from '/@/views/pms/components/SpinView.vue';
import EmptyView from '/@/views/pms/components/EmptyView.vue';

const projectId: string = inject('projectId');
const type: Ref<string> = inject('type');
const loading:Ref<boolean> = ref(false);
const budgetInfo: Ref<Record<string, any>> = ref({});
const list: Ref<any[]> = computed(() => budgetInfo.value.projectBudgetTotalVOS || []);
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
  },
  color: ['#e1e1e1', '#3CB6E3'],
  grid: {
    top: 10,
    left: 20,
    right: 20,
    bottom: 20,
    containLabel: true,
  },
  dataZoom: [
    {
      show: list.value.length > 5,
      height: 0,
      moveHandleSize: 10,
      showDetail: false,
      maxValueSpan: 4,
      bottom: 10,
    },
  ],
  xAxis: {
    type: 'category',
    data: list.value.map((item) => item.budgetName),
    axisTick: {
      show: false,
    },
    axisLabel: {
      interval: 0,
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '预算',
      barWidth: 20,
      data: list.value.map((item) => item.budgetMoney),
      type: 'bar',
    },
    {
      name: '成本',
      barWidth: 20,
      data: list.value.map((item) => item.expendMoney),
      type: 'bar',
    },
  ],
}));
const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
  chartInstance.value.hideLoading();
}, { deep: true });

onMounted(() => {
  getBudgetInfo();
});

// 获取预算信息
async function getBudgetInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectCollectionStatistics/projectBudgetTotal').fetch({
      projectId,
      type: type.value,
    }, '', 'GET');
    budgetInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="flex flex-ac mt15 mb15">
    <div class="label-value-item">
      <span>成本支出：</span>
      <span class="value">{{ formatMoney(budgetInfo.expendMoney) }}元</span>
    </div>
    <div class="label-value-item">
      <span>成本超出部分：</span>
      <span class="value">{{ formatMoney(budgetInfo.overspendMoney) }}元</span>
    </div>
    <div class="right custom-legend-item">
      <span style="background-color: #e3e3e3" />
      <span>预算</span>
    </div>
    <div class="ml10 custom-legend-item">
      <span style="background-color:#3CB6E3" />
      <span>成本</span>
    </div>
  </div>
  <spin-view
    v-show="loading"
    class="chart"
  />
  <div
    v-show="!loading && list.length"
    ref="chartRef"
    class="chart"
  />
  <empty-view
    v-show="!loading && list.length===0"
    class="chart"
  />
</template>

<style scoped lang="less">
.chart {
  width: 100%;
  height: 200px;
}

.custom-legend-item {
  line-height: 22px;
}
</style>
