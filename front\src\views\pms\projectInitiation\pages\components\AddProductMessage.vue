<template>
  <div
    v-loading="loading"
    class="add-document"
  >
    <BasicForm @register="register" />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicForm, useForm, getDictByNumber,
} from 'lyra-component-vue3';
import { onMounted, ref, Ref } from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'name',
      label: '产品名称：',
      rules: [
        {
          required: true,
          message: '请输入产品名称',
        },
      ],
      colProps: { span: 24 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入产品名称',
        maxlength: 40,
        showCount: true,
      },
      component: 'Input',
    },
    {
      field: 'number',
      label: '产品编码：',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
    },
    {
      field: 'productSecondClassify',
      label: '产品二级分类：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        api: () => getDictByNumber('productSecondClassify'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'productGroup',
      label: '产品组：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        api: () => getDictByNumber('productGroup'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'productModelNumber',
      label: '产品型号：',
      component: 'Input',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
    },
    {
      field: 'productClassify',
      label: '产品类别：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        api: () => getDictByNumber('productClassify'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'materialLevel',
      label: '物料等级：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        api: () => getDictByNumber('material_level'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'highQualityLevel',
      label: '高质量等级：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        api: () => getDictByNumber('highQualityLevel'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'localizedControl',
      label: '国产化管控：',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        api: () => getDictByNumber('localizedControl'),
        labelField: 'name',
        valueField: 'value',
      },
    },
    {
      field: 'requiredUnitNum',
      label: '所需台套：',
      component: 'InputNumber',
      colProps: { span: 12 },
      componentProps: {
        allowClear: true,
        placeholder: '请输入',
      },
    },
  ],
});
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    if (props.drawerData.type === 'add') {
      formData.approvalId = props.drawerData.approvalId;
    } else {
      formData.id = props.drawerData.id;
    }
    await new Api('/pms').fetch(formData, props.drawerData.type === 'add' ? 'projectApprovalProduct/add' : 'projectApprovalProduct/edit', props.drawerData.type === 'add' ? 'POST' : 'PUT');
    message.success(props.drawerData.type === 'add' ? '新增产品成功' : '编辑产品成功');
  },
});
onMounted(() => {
  if (props.drawerData.type === 'edit') {
    getDetailsData();
  }
});
function getDetailsData() {
  loading.value = true;
  new Api('/pms').fetch('', `projectApprovalProduct/${props.drawerData.id}`, 'GET').then((res) => {
    setFieldsValue(res);
    loading.value = false;
  });
}
</script>
<style scoped lang="less">
.basic-card{
  margin: 0 !important;
  border: 0 !important;
  :deep(.card-content){
    margin: 10px !important;
  }
}
:deep(.ant-input-affix-wrapper){
  height: 100%;
}
</style>