package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum ProjectContractStatusEnum {
    CREATED(101, "编制中"),
    AUDITING(110, "审核中"),
    AUDITED(130, "已审核"),
    CHANGE_APPLY(104, "变更申请"),
    PERFORM(107, "合同履行"),
    CLOSE(111, "合同关闭"),
    CLOSE_APPLY(108, "关闭申请"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectContractStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
