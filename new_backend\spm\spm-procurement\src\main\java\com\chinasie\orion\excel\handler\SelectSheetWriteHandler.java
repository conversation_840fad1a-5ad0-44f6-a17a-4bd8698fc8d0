package com.chinasie.orion.excel.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SelectSheetWriteHandler implements SheetWriteHandler {

    private List<String> selections;

    private static final int COLUMN_INDEX = 3;

    public SelectSheetWriteHandler(List<String> dictNumbers) {
        this.selections = dictNumbers;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 获取工作表
        Sheet sheet = writeSheetHolder.getSheet();
        DataValidationHelper helper = sheet.getDataValidationHelper();

        // 从Redis中获取下拉框内容
        List<String> options = selections;

        // 创建单元格范围
        CellRangeAddressList addressList = new CellRangeAddressList(1, 1000, COLUMN_INDEX, COLUMN_INDEX);

        // 创建数据验证约束
        DataValidationConstraint constraint = helper.createExplicitListConstraint(options.toArray(new String[0]));

        // 创建数据验证对象
        DataValidation validation = helper.createValidation(constraint, addressList);

        // 添加数据验证到工作表
        sheet.addValidationData(validation);
        }
}
