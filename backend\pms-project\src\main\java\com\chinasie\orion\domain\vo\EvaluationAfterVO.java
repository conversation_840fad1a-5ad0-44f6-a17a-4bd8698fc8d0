package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "EvaluationAfterVO对象", description = "项目后评价详情")
@Data
public class EvaluationAfterVO extends ObjectVO implements Serializable {
    /**
     * 客户满意度
     */
    @ApiModelProperty(value = "客户满意度描述")
    private String customerSatisfaction;
    /**
     * 客户满意度评分
     */
    @ApiModelProperty(value = "客户满意度评分")
    private Integer customerSatisfactionScore;
    /**
     * 团队绩效
     */
    @ApiModelProperty(value = "团队绩效描述")
    private String teamPerformance;
    /**
     * 团队绩效评分
     */
    @ApiModelProperty(value = "团队绩效评分")
    private Integer teamPerformanceScore;
    /**
     * 反馈和建议
     */
    @ApiModelProperty(value = "反馈和建议描述")
    private String feedbackSuggestions;
    /**
     * 反馈和建议评分
     */
    @ApiModelProperty(value = "反馈和建议评分")
    private Integer feedbackSuggestionsScore;
    /**
     * 可持续性
     */
    @ApiModelProperty(value = "可持续性描述")
    private String sustainability;
    /**
     * 可持续性评分
     */
    @ApiModelProperty(value = "可持续性评分")
    private Integer sustainabilityScore;
    /**
     * 范围管理
     */
    @ApiModelProperty(value = "范围管理描述")
    private String scopeManage;
    /**
     * 范围管理评分
     */
    @ApiModelProperty(value = "范围管理评分")
    private Integer scopeManageScore;
    /**
     * 利益相关方满意度
     */
    @ApiModelProperty(value = "利益相关方满意度描述")
    private String stakeholderSatisfaction;
    /**
     * 利益相关方满意度评分
     */
    @ApiModelProperty(value = "利益相关方满意度评分")
    private Integer stakeholderSatisfactionScore;
    /**
     * 利益相关方参与度
     */
    @ApiModelProperty(value = "利益相关方参与度描述")
    private String stakeholderEngagement;
    /**
     * 利益相关方参与度评分
     */
    @ApiModelProperty(value = "利益相关方参与度评分")
    private Integer stakeholderEngagementScore;
    /**
     * 创新和改进
     */
    @ApiModelProperty(value = "创新和改进描述")
    private String innovationImprove;
    /**
     * 创新和改进评分
     */
    @ApiModelProperty(value = "创新和改进评分")
    private Integer innovationImproveScore;
    /**
     * 可行性分析
     */
    @ApiModelProperty(value = "可行性分析描述")
    private String feasibilityAnalysis;
    /**
     * 可行性分析评分
     */
    @ApiModelProperty(value = "可行性分析评分")
    private Integer feasibilityAnalysisScore;
    /**
     * 治理和监督
     */
    @ApiModelProperty(value = "治理和监督描述")
    private String governanceSupervision;
    /**
     * 治理和监督评分
     */
    @ApiModelProperty(value = "治理和监督评分")
    private Integer governanceSupervisionScore;
}
