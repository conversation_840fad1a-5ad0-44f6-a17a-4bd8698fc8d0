<template>
  <div class="other-wrap">
    <div class="pb10 change-cause-title">
      变更原因
    </div>
    <Textarea
      v-model:value="value"
      placeholder="请输入内容"
      maxlength="1000"
      :autosize="{minRows: 6}"
      showCount
    />
  </div>
</template>

<script setup lang="ts">
import { message, Textarea } from 'ant-design-vue';
import { ref, unref } from 'vue';
const value = ref();

async function getValues() {
  if (!unref(value)) {
    const errMsg = '请输入变更原因';
    message.error(errMsg);
    return Promise.reject(errMsg);
  }
  return {
    changeReason: unref(value),
  };
}

function setValues(values) {
  value.value = values?.changeReason;
}

defineExpose({
  setValues,
  getValues,
});
</script>

<style scoped lang="less">
.other-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
.change-cause-title {
  &:before {
    content: "*";
    display: inline-block;
    color: red
  }
}
</style>
