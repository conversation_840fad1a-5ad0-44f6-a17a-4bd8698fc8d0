package com.chinasie.orion.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.MsgTaskName;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.constant.SchemeMessageNode;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public abstract class SendMessageCommonAdapter {

    @Resource
    private MessageCenterApi messageCenterApi;

    @Resource
    protected CurrentUserHelper userHelper;
    @Resource
    protected PmsMQProducer mqProducer;


    protected final static Map<MsgBusinessTypeEnum, String> TYPE_CODE_MAP = MapUtil.newHashMap();
    protected final static Map<MsgBusinessTypeEnum, String> TYPE_FLOW_TYPE_MAP = MapUtil.newHashMap();


    protected void init() {
        if (MapUtil.isEmpty(TYPE_CODE_MAP)) {
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.SEND_DOWN, SchemeMessageNode.NODE_SEND_RSP_USER);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.SEND_DOWN_COPY, SchemeMessageNode.NODE_SEND_COPY_RSP_USER);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.EDIT, SchemeMessageNode.NODE_SCHEME_EDIT);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE, SchemeMessageNode.NODE_SCHEME_MODIFY_CREATOR);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE_AGREE, SchemeMessageNode.NODE_SCHEME_AGGRE);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE_REJECT, SchemeMessageNode.NODE_SCHEME_REJECT);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.PRE_FINISH,SchemeMessageNode.NODE_SCHEME_PRE_FINISH);
            TYPE_CODE_MAP.put(MsgBusinessTypeEnum.FALLBACK,SchemeMessageNode.NODE_SCHEME_FALLBACK);
        }
        if (MapUtil.isEmpty(TYPE_FLOW_TYPE_MAP)) {
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.SEND_DOWN, MsgTaskName.PROJECT_SCHEME_ISSUE);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.SEND_DOWN_COPY, MsgTaskName.PROJECT_SCHEME_ISSUE);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.EDIT, MsgTaskName.PROJECT_SCHEME_MODIFY);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE, MsgTaskName.PROJECT_SCHEME_MODIFY);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE_AGREE, MsgTaskName.PROJECT_SCHEME_MODIFY);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.MODIFY_APPROVE_REJECT, MsgTaskName.PROJECT_SCHEME_MODIFY);
            TYPE_FLOW_TYPE_MAP.put(MsgBusinessTypeEnum.FALLBACK, MsgTaskName.PROJECT_SCHEME_FALLBACK);
        }
    }

    /**
     * 构建消息发送体
     *
     * @param schemeMsgDTO
     * @param <T>
     * @return
     * @throws Exception
     */
    protected abstract <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception;


    protected <T> void handle(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> dtoList = buildMscMessageDTO(schemeMsgDTO);
        if (CollUtil.isNotEmpty(dtoList)) {
            dtoList.forEach(item -> mqProducer.sendPmsMessage(item));
        }
    }

    protected void clearToDo(MsgBusinessTypeEnum type, String businessId, String userId) {
        log.debug("【{}】消除待办参数：{}", type.getDesc(), packageBusinessId(type, businessId));
        ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(userId)
                .businessId(packageBusinessId(type, businessId))
                .build());
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));
    }

    protected String packageBusinessId(MsgBusinessTypeEnum type, String businessId) {
        return new StringBuilder(String.valueOf(type)).append(StrUtil.UNDERLINE).append(businessId).toString();
    }


    protected void clearToDo(MsgBusinessTypeEnum type, String businessId) {
        log.debug("【{}】消除待办参数：{}", type.getDesc(), packageBusinessId(type, businessId));
        ResponseDTO<?> responseDTO = messageCenterApi.todoMessageChangeStatusByBusinessId(packageBusinessId(type, businessId));
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));
    }

    protected void clearToDoBatch(MsgBusinessTypeEnum type, List<String> businessIdList) {
        log.debug("【{}】批量消除待办参数：{}", type.getDesc(), packageBusinessIds(type, businessIdList));
        ResponseDTO<?> responseDTO = messageCenterApi.todoMessageChangeStatusByBusinessIds(packageBusinessIds(type, businessIdList));
        log.debug("待办变更响应：{}", JSON.toJSONString(responseDTO));
    }

    protected List<String> packageBusinessIds(MsgBusinessTypeEnum type, List<String> businessIdList) {
        return businessIdList.stream().map(businessId -> String.format("%s_%s", String.valueOf(type), businessId)).collect(Collectors.toList());
    }
}


