package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.Date;
import java.util.List;
/**
 * SchemeToPerson VO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@ApiModel(value = "SchemeToPersonVO对象", description = "计划相关的人员")
@Data
public class SchemeToPersonVO extends  ObjectVO   implements Serializable{

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String planSchemeId;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;


    /**
     * 人员ID： 人员管理ID
     */
    @ApiModelProperty(value = "人员ID： 人员管理ID")
    private String personId;


    /**
     * 用户工号
     */
    @ApiModelProperty(value = "用户工号")
    private String userCode;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    private String baseCode;



    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员性别")
    private String sex;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    private String nowPosition;


    /**
     * 研究所编号
     */
    @ApiModelProperty(value = "研究所编号")
    private String instituteCode;


    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;


    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    private String companyCode;



    /**
     * 人员性质
     */
    @ApiModelProperty(value = "人员性质")
    private String personnelNature;


    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String companyName;


    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String deptName;


    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    private String instituteName;


    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "人员管理")
    private PersonMangeVO personMangeVO;

    @ApiModelProperty(value = "人员管理ID")
    private String personManageId;

    @ApiModelProperty(value = "人员台账ID")
    private String personLedgerId;
    @ApiModelProperty(value = "参与作业数")
    private Integer jobNum;

    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;


    @ApiModelProperty(value = "有无项目:默认有")
    private Boolean isHaveProject;

        @ApiModelProperty(value = "计划入场日期")
        private Date inDate;

        @ApiModelProperty(value = "计划离场日期")
        private Date outDate;

    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "项目计划名称")
    private String planSchemeName;
    @ApiModelProperty(value = "项目id")
    private String projectId;
}
