package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PlanToReview Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-23 11:13:31
 */
@TableName(value = "pms_plan_to_review")
@ApiModel(value = "PlanToReviewEntity对象", description = "计划与评审关系")
@Data
public class PlanToReview extends ObjectEntity implements Serializable{

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    @TableField(value = "to_class")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    @TableField(value = "from_class")
    private String fromClass;

}
