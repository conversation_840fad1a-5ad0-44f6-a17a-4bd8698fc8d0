package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(
        value = "LyraSimpleEntity",
        description = "LyraSimpleEntity"
)
public class LyraSimpleEntity  implements Serializable {
    @ApiModelProperty("ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    @ApiModelProperty("类名称")
    @TableField(
            fill = FieldFill.INSERT,
            value = "class_name"
    )
    private String className;
    @ApiModelProperty("创建人")
    @TableField(
            fill = FieldFill.INSERT,
            value = "creator_id"
    )
    private String creatorId;
    @ApiModelProperty("创建人名字")
    @TableField(
            exist = false
    )
    private String creatorName;
    @ApiModelProperty("拥有者")
    @TableField(
            fill = FieldFill.INSERT,
            value = "owner_id"
    )
    private String ownerId;
    @ApiModelProperty("拥有者名字")
    @TableField(
            exist = false
    )
    private String ownerName;
    @ApiModelProperty("创建时间")
    @TableField(
            fill = FieldFill.INSERT,
            value = "create_time"
    )
    private Date createTime;
    @ApiModelProperty("修改人")
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "modify_id"
    )

    private String modifyId;
    @ApiModelProperty("修改人名字")
    @TableField(
            exist = false
    )
    private String modifyName;
    @ApiModelProperty("修改时间")
    @TableField(
            fill = FieldFill.INSERT_UPDATE,
            value = "modify_time"
    )
    private Date modifyTime;
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;
    @ApiModelProperty("平台ID")
    @TableField(
            fill = FieldFill.INSERT,
            value = "platform_id"
    )
    private String platformId;
    @ApiModelProperty("业务组织名称")
    @TableField(
            exist = false
    )
    private String businessOrgName;
    @ApiModelProperty("状态")
    @TableField(
            value = "status",
            fill = FieldFill.INSERT
    )

    private Integer status;
    @ApiModelProperty("状态对象")
    @TableField(
            exist = false
    )
    private DataStatusVO dataStatus;
    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(
            fill = FieldFill.INSERT,
            value = "logic_status"
    )
    @TableLogic
    private Integer logicStatus;

    public LyraSimpleEntity() {
    }

    public String getId() {
        return this.id;
    }

    public String getClassName() {
        return this.className;
    }

    public String getCreatorId() {
        return this.creatorId;
    }

    public String getCreatorName() {
        return this.creatorName;
    }

    public String getOwnerId() {
        return this.ownerId;
    }

    public String getOwnerName() {
        return this.ownerName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public String getModifyId() {
        return this.modifyId;
    }

    public String getModifyName() {
        return this.modifyName;
    }

    public Date getModifyTime() {
        return this.modifyTime;
    }

    public String getRemark() {
        return this.remark;
    }

    public String getPlatformId() {
        return this.platformId;
    }

    public String getBusinessOrgName() {
        return this.businessOrgName;
    }

    public Integer getStatus() {
        return this.status;
    }

    public DataStatusVO getDataStatus() {
        return this.dataStatus;
    }

    public Integer getLogicStatus() {
        return this.logicStatus;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public void setBusinessOrgName(String businessOrgName) {
        this.businessOrgName = businessOrgName;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setDataStatus(DataStatusVO dataStatus) {
        this.dataStatus = dataStatus;
    }

    public void setLogicStatus(Integer logicStatus) {
        this.logicStatus = logicStatus;
    }


    public String toString() {
        String var10000 = this.getId();
        return "LyraSimpleEntity(id=" + var10000 + ", className=" + this.getClassName() + ", creatorId=" + this.getCreatorId() + ", creatorName=" + this.getCreatorName() + ", ownerId=" + this.getOwnerId() + ", ownerName=" + this.getOwnerName() + ", createTime=" + this.getCreateTime() + ", modifyId=" + this.getModifyId() + ", modifyName=" + this.getModifyName() + ", modifyTime=" + this.getModifyTime() + ", remark=" + this.getRemark() + ", platformId=" + this.getPlatformId() + ", businessOrgName=" + this.getBusinessOrgName() + ", status=" + this.getStatus() + ", dataStatus=" + this.getDataStatus() + ", logicStatus=" + this.getLogicStatus() + ")";
    }
}
