package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class ContractMainImportDTO implements Serializable {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private String number;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 1)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 2)
    private String contractName;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @ExcelProperty(value = "成本类型 ", index = 3)
    private String costTypeName;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    @ExcelProperty(value = "成本名称 ", index = 4)
    private String costName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 5)
    private BigDecimal unitPrice;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注 ", index = 6)
    private String remark;
}
