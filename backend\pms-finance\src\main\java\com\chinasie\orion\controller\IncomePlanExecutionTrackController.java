package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomePlanExecutionTrackDTO;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.InTransactionPreReconciliationService;
import com.chinasie.orion.service.IncomePlanExecutionTrackService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/incomePlanExecutionTrack")
@Api(tags = "收入计划执行跟踪报表")
public class IncomePlanExecutionTrackController {

    @Autowired
    private IncomePlanExecutionTrackService incomePlanExecutionTrackService;

    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入计划执行跟踪报表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanExecutionTrackVO>> pages(@RequestBody Page<IncomePlanExecutionTrackDTO> pageRequest) throws Exception {
        Page<IncomePlanExecutionTrackVO> rsp =  incomePlanExecutionTrackService.getPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "导出")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入计划执行跟踪报表", subType = "导出数据", bizNo = "")
    @RequestMapping(value = "/export/excel", method = RequestMethod.POST)
    public void pages(@RequestBody IncomePlanExecutionTrackDTO in, HttpServletResponse response) throws Exception {
        incomePlanExecutionTrackService.exportByExcel(in, response);
    }
}
