package com.chinasie.orion.domain.vo.job;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class BeforeAndAfterFourDay implements Serializable {

    @ApiModelProperty(value = "开工ID")
    private String workId;

    @ApiModelProperty(value = "日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date dateDay;
    @ApiModelProperty(value = "时间选择Map")
    private Map<String,Boolean> dateSelectedMap;
    @ApiModelProperty(value = "是否可以修改")
    private Boolean edit;
}
