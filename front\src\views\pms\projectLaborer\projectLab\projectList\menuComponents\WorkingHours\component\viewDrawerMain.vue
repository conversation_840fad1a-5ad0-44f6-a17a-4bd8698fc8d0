<template>
  <div class="viewDrawerMain">
    <BasicForm @register="register" />
    <OrionTable
      v-if="showTable"
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed, h, onMounted, ref,
} from 'vue';
import {
  BasicForm, BasicTabs, OrionTable, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { InputNumber } from 'ant-design-vue';
import dayjs from 'dayjs';
const props = defineProps<{
  record: any,
  id:string,
}>();
const [register, { setFieldsValue, getFieldsValue }] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'number',
      component: 'Input',
      label: '单据编号:',
      componentProps: {
        disabled: true,
      },
      colProps: {
        span: 12,
      },
    },
    {
      field: 'memberName',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      label: '成员姓名:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'memberRoleName',
      componentProps: {
        disabled: true,
      },
      component: 'Input',
      label: '成员角色:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'startDate',
      component: 'Input',
      formatTime: 'YYYY-MM-DD',
      componentProps: {
        disabled: true,
      },
      label: '开始时间:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'endDate',
      component: 'Input',
      formatTime: 'YYYY-MM-DD',
      componentProps: {
        disabled: true,
      },
      label: '结束时间:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'workHour',
      componentProps: {
        disabled: true,
      },
      component: 'Input',
      label: '工时时长（小时）:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'dataStatus',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      label: '单据状态:',
      colProps: {
        span: 12,
      },
    },
    {
      field: 'createTime',
      component: 'Input',
      formatTime: 'YYYY-MM-DD',
      label: '创建时间:',
      componentProps: {
        disabled: true,
      },
      colProps: {
        span: 12,
      },
    },

  ],
});
const currentWorkContent = ref([]);
const tableSource = ref([]);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: false,
  smallSearchField: false,
  showSmallSearch: false,
  dataSource: [
    {
      id: 1,
      name: 111,
    },
  ],
  showIndexColumn: false,
  pagination: false,
  columns: [],
});

const tabsIndex = ref(0);
const showTable = ref(false);
const tableRef = ref(null);
const tabsArr = ref([]);
onMounted(() => {
  getDetail(props.id);
});

function getDetail(id) {
  new Api('/pms/workHourEstimate').fetch('', id, 'GET').then((res) => {
    setFieldsValue({
      number: res?.number || '',
      memberName: res?.memberName || '',
      memberRoleName: res?.memberRoleName || '',
      startDate: dayjs(res?.startDate).format('YYYY-MM-DD') || '',
      endDate: dayjs(res?.endDate).format('YYYY-MM-DD') || '',
      dataStatus: res?.dataStatus.name || '',
      createTime: dayjs(res?.createTime).format('YYYY-MM-DD') || '',
      workHour: res?.workHour || '',
    });

    currentWorkContent.value = res.detailList;
    let workArr = [];
    for (let i in res.detailList) {
      tableOptions.value.columns.push({
        title: res.detailList[i].workMonth,
        customRender({
          record, index, column,
        }) {
          return h(InputNumber, {
            disabled: true,
            value: computed(() => record[res.detailList[i].workMonth]),
          });
        },
      });
      workArr.push ({
        [res.detailList[i].workMonth]: res.detailList[i].workHour,
      });
    }
    let mergedObj = workArr.reduce((result, currentObj) => {
      Object.assign(result, currentObj);
      return result;
    }, {});
    let resultArr = [mergedObj];
    showTable.value = true;
    tableSource.value = resultArr;
    setTimeout(() => {
      tableRef.value.setTableData(resultArr);
    }, 20);
  });
}
function handleTabChange(e) {
  showTable.value = false;
  tableOptions.value.columns = [];
  let colunms = [];
  for (let i in tabsArr.value[e].detailList) {
    colunms.push({
      title: tabsArr.value[e].detailList[i].workMonth,
      dataIndex: `workHour${i}`,
      customRender({
        record, index, column,
      }) {
        return h(InputNumber, {
          value: tabsArr.value[e].detailList[i].workHour,
          disabled: true,
        });
      },
    });
  }
  tableRef.value.setColumns(colunms);
  showTable.value = true;
}

</script>
<style lang="less">
.viewDrawerMain {
  height:100%;
}
</style>
