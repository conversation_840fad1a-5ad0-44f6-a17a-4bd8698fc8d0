package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectSetUpDTO;
import com.chinasie.orion.domain.entity.ProjectSetUp;
import com.chinasie.orion.domain.vo.ProjectSetUpVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * ProjectSetUp 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
public interface ProjectSetUpService extends OrionBaseService<ProjectSetUp> {

    String prePlanTimeRule = "prePlanTimeRule";

    Map<String, String> PROJECT_SETUP_MAP = new ConcurrentHashMap<>() {{
        //前置计划时间规则
        put("prePlanTimeRule", "false");
    }};


    /**
     * 详情
     * <p>
     * * @param projectId
     */
    ProjectSetUpVO detail(String projectId, String key) throws Exception;


    /**
     * 编辑
     * <p>
     * * @param projectSetUpDTO
     */
    Boolean edit(ProjectSetUpDTO projectSetUpDTO) throws Exception;

}
