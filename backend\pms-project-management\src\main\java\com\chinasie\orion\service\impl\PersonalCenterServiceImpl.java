package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinasie.orion.constant.MarketContractStatusEnum;
import com.chinasie.orion.constant.MarketContractTypeEnums;
import com.chinasie.orion.constant.PersonInitialEnum;
import com.chinasie.orion.constant.SchemeStatusEnum;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.MyInitiationVO;
import com.chinasie.orion.domain.vo.SchemeVO;
import com.chinasie.orion.management.constant.IncomeTypeEnum;
import com.chinasie.orion.management.constant.RequirementManagementStatusEnum;
import com.chinasie.orion.management.domain.dto.*;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.PredictLeadVO;
import com.chinasie.orion.management.domain.vo.QuoteOutbidVO;
import com.chinasie.orion.management.repository.ManagementStaticsMapper;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.management.service.ManagementStaticsService;
import com.chinasie.orion.management.service.QuotationManagementService;
import com.chinasie.orion.management.service.RequirementMangementService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.PersonalCenterMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.service.MarketContractSignService;
import com.chinasie.orion.service.PersonalCenterService;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.springframework.test.web.client.match.MockRestRequestMatchers.content;


@Service
@Slf4j
public class PersonalCenterServiceImpl extends OrionBaseServiceImpl<PersonalCenterMapper, MyInitiationVO> implements PersonalCenterService {

    @Autowired
    private PersonalCenterMapper personalCenterMapper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private CurrentUserHelper currentUserHelper;

    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private MarketContractSignService marketContractSignService;

    @Autowired
    private QuotationManagementService quotationManagementService;

    @Autowired
    private ManagementStaticsService managementStaticsService;

    @Autowired
    ManagementStaticsMapper managementStaticsMapper;

    @Autowired
    RequirementMangementService requirementMangementService;

    @Autowired
    DeptRedisHelper deptRedisHelper;

    @Autowired
    DictRedisHelper dictRedisHelper;






    @Override
    public Page<MyInitiationVO> getMyInitiationVOs(Page<MyInitiationVO> pageRequest) {
        List<MyInitiationVO> myInitiationVOs = personalCenterMapper.getMyInitiationVOs(CurrentUserHelper.getCurrentUserId());
        List<MyInitiationVO> filteredInitiationVOs = myInitiationVOs.stream()
                .filter(vo -> vo.getSince() != 2)
                .collect(Collectors.toList());
        filteredInitiationVOs.forEach(item -> {
            if((PersonInitialEnum.PROJECT_RISK.getCode().equals(item.getTaskType()) && (item.getSince() == 2))){
                item.setDetailUrl(PersonInitialEnum.PROJECT_RISK_URL.getCode());
            }
            if (item.getParentChain() != null && !item.getParentChain().isEmpty()) {
                String parentChain = item.getParentChain();
                String[] ids = parentChain.split(",");
                // ids[0] 永远是 "0"
                if (ids.length > 1) {
                    boolean hasValidParentId = false;
                    for (int i = 1; i < ids.length; i++) {
                        String id = ids[i];
                        if (!id.equals("0") && !id.isEmpty()) {
                            // 找到第一个有效的非零ID，设置为 parentId
                            item.setParentId(id);
                            hasValidParentId = true;
                            break;
                        }
                    }
                    if (!hasValidParentId) {
                        item.setParentId(item.getSourceId());
                    }
                } else {
                    item.setParentId(item.getSourceId());
                }
            } else {
                item.setParentId(item.getSourceId());
            }
        });

        int sum = personalCenterMapper.countMyInitiation(CurrentUserHelper.getCurrentUserId());
        String name = userRedisHelper.getUserBaseCacheById(CurrentUserHelper.getCurrentUserId()).getName();
        PageResult<MyInitiationVO> myInitiationVOPageResult = new PageResult<>();
        myInitiationVOPageResult.setContent(myInitiationVOs);
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        myInitiationVOPageResult.getContent().forEach(itms -> {
            itms.setMessageUrl(itms.getDetailUrl() + itms.getSourceId());
            itms.setUserName(name);
            ZonedDateTime zonedDateTime = itms.getAcceptTime().atZone(zoneId);
            DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
            String isoDateTimeStr = zonedDateTime.format(formatter);
            itms.setFormatAcceptTime(isoDateTimeStr);
        });
        Page<MyInitiationVO> pageResult = new Page<>(myInitiationVOPageResult.getPageNum(), myInitiationVOPageResult.getPageSize(), sum);
        pageResult.setContent(myInitiationVOPageResult.getContent());
        return pageResult;

    }

    @Override
    public ContractMilestonesStatisticDTO getStatistic() throws Exception {
        LambdaQueryWrapper<MarketContract> condition = new LambdaQueryWrapper<>();
        condition.eq(MarketContract::getLogicStatus, "1");
        condition.and(wrapper -> {
            wrapper
                    .eq(MarketContract::getContractType, MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode())
                    .or()
                    .eq(MarketContract::getContractType, MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode())
                    .or()
                    .eq(MarketContract::getContractType, MarketContractTypeEnums.FRAME_CONTRACT.getCode());
        });
        condition.and(wrapper -> {
            wrapper
                    .eq(MarketContract::getStatus, MarketContractStatusEnum.FULFIL.getStatus())
                    .or()
                    .eq(MarketContract::getStatus, MarketContractStatusEnum.COMPLATED.getStatus());
        });
        // 筛选合同状态，合同类型，合同关联客户id（关联核与非核），金额。
        condition.select(MarketContract::getStatus, MarketContract::getContractType
                , MarketContract::getCustPersonId, MarketContract::getContractAmt
        );
        List<MarketContract> marketContracts = marketContractService.list(condition);
        Map<String, List<MarketContract>> stringListMap = classifyContracts(marketContracts);
        List<MarketContract> nuclearEnergyContracts = stringListMap.get("nuclearEnergyContracts"); //核能收入合同列表
        List<MarketContract> nonNuclearEnergyContracts = stringListMap.get("nonNuclearEnergyContracts"); // 非核能收入合同列表
        Map<String, Map<Integer, BigDecimal>> nuclearEnergySignedCountMap = new HashMap<>();
        Map<String, Map<Integer, BigDecimal>> otherSignedCountMap = new HashMap<>();
        AtomicInteger countTotalPrice = new AtomicInteger(0);
        BigDecimal sumTotalPriceAmount = BigDecimal.ZERO;
        AtomicInteger countComposite = new AtomicInteger(0);
        BigDecimal sumCompositeAmount = BigDecimal.ZERO;
        AtomicInteger countFrame = new AtomicInteger(0);
        BigDecimal sumFrameAmount = BigDecimal.ZERO;
        ContractMilestonesStatisticDTO contractMilestonesStatisticDTO = new ContractMilestonesStatisticDTO();
        for (MarketContract item : nuclearEnergyContracts) {
            String contractType = item.getContractType();
            BigDecimal contractAmt = item.getContractAmt() != null ? item.getContractAmt() : BigDecimal.ZERO;
            if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType)) {
                countTotalPrice.incrementAndGet();
                sumTotalPriceAmount = sumTotalPriceAmount.add(contractAmt);
            }
            if (MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
                countComposite.incrementAndGet();
                sumCompositeAmount = sumCompositeAmount.add(contractAmt);
            }
            if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)) {
                countFrame.incrementAndGet();
                sumFrameAmount = sumFrameAmount.add(contractAmt);
            }
        }
        contractMilestonesStatisticDTO.setNuclearTotalPriceContractCount(countTotalPrice.intValue());
        contractMilestonesStatisticDTO.setNuclearTotalPriceContractAmount(sumTotalPriceAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNuclearCompositeContractCount(countComposite.intValue());
        contractMilestonesStatisticDTO.setNuclearCompositeContractAmount(sumCompositeAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNuclearFrameworkContractCount(countFrame.intValue());
        contractMilestonesStatisticDTO.setNuclearFrameworkContractAmount(sumFrameAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        AtomicInteger countTotalPrice1 = new AtomicInteger(0);
        BigDecimal sumTotalPriceAmount1 = BigDecimal.ZERO;
        AtomicInteger countComposite1 = new AtomicInteger(0);
        BigDecimal sumCompositeAmount1 = BigDecimal.ZERO;
        AtomicInteger countFrame1 = new AtomicInteger(0);
        BigDecimal sumFrameAmount1 = BigDecimal.ZERO;
        for (MarketContract item : nonNuclearEnergyContracts) {
            String contractType = item.getContractType();
            BigDecimal contractAmt = item.getContractAmt() != null ? item.getContractAmt() : BigDecimal.ZERO;
            if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType)) {
                countTotalPrice1.incrementAndGet();
                sumTotalPriceAmount1 = sumTotalPriceAmount.add(contractAmt);
            }
            if (MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType)) {
                countComposite1.incrementAndGet();
                sumCompositeAmount1 = sumCompositeAmount.add(contractAmt);
            }
            if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)) {
                countFrame1.incrementAndGet();
                sumFrameAmount1 = sumFrameAmount.add(contractAmt);
            }
        }
        contractMilestonesStatisticDTO.setNonNuclearTotalPriceContractCount(countTotalPrice1.intValue());
        contractMilestonesStatisticDTO.setNonNuclearTotalPriceContractAmount(sumTotalPriceAmount1.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNonNuclearCompositeContractCount(countComposite1.intValue());
        contractMilestonesStatisticDTO.setNonNuclearCompositeContractAmount(sumCompositeAmount1.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNonNuclearFrameworkContractCount(countFrame1.intValue());
        contractMilestonesStatisticDTO.setNonNuclearFrameworkContractAmount(sumFrameAmount1.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        // 历史：合同状态为编制中，待分发，审核中，待签署，并且合同关联的报价表的报价结果为中标，修改时间为去年。同时还有中标未进入合同状态的。
        // 新签：合同状态为履行中，已完成
        // 已中标未签署：合同状态为编制中，待分发，审核中，待签署，并且合同关联的报价表的报价结果为中标，修改时间为今年。同时还有中标未进入合同状态的。
        // 已签署：合同状态为履行中，已完成
        // 分别统计核能和非核能的新签

        // 已中标未签署
        LambdaQueryWrapperX<MarketContract> condition1 = new LambdaQueryWrapperX<>(MarketContract.class);
        condition1.eq(MarketContract::getLogicStatus, "1");
        condition1.eq(MarketContract::getStatus, MarketContractStatusEnum.CREATED.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.AUDITING.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.SIGNING.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.PREDISTRIBUTE.getStatus());
        LocalDate thisYearStart = LocalDate.now().withDayOfYear(1);
        LocalDate now = LocalDate.now();
        Date startDate1 = Date.from(thisYearStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate1 = Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant());

        LambdaQueryWrapper<QuotationManagement> quotationWrapper1 = Wrappers.lambdaQuery(QuotationManagement.class)
                .eq(QuotationManagement::getStatus, 1)
                .ge(QuotationManagement::getModifyTime, startDate1)
                .lt(QuotationManagement::getModifyTime, endDate1); // 修改时间小于当前日期

        quotationWrapper1.select(QuotationManagement::getId);
        List<QuotationManagement> bidQuotationManagements = quotationManagementService.list(quotationWrapper1);
        List<String> bidQuotationIds = bidQuotationManagements.stream()
                .map(QuotationManagement::getId)
                .collect(Collectors.toList());
        if(!bidQuotationIds.isEmpty()) {
            condition1.in(MarketContract::getQuoteId, bidQuotationIds);
        }

        // 筛选合同状态，合同类型，合同关联客户id（关联核与非核），金额。
        condition1.select(MarketContract::getStatus, MarketContract::getContractType
                ,MarketContract::getCustPersonId, MarketContract::getContractAmt
        );
        List<MarketContract> marketContractList1 = marketContractService.list(condition1);
        Map<String, List<MarketContract>> stringListMap2 = classifyContracts(marketContractList1);
        List<MarketContract> nuclearEnergyContracts2 = stringListMap2.get("nuclearEnergyContracts"); //历史核能收入合同列表
        List<MarketContract> nonNuclearEnergyContracts2 = stringListMap2.get("nonNuclearEnergyContracts"); // 历史非核能收入合同列表
        BigDecimal totalContractAmt2 = nuclearEnergyContracts2.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 核能累加金额
        BigDecimal totalContractAmt3 = nonNuclearEnergyContracts2.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 非核累加金额

         // 反查中标未进入合同流程数据
        List<ContractSignStatisticsDTO> quotations = managementStaticsMapper.findQuotations();
        int currentYear = LocalDate.now().getYear();
        LocalDate startDate2 = LocalDate.of(currentYear, 1, 1);
        LocalDate endDate2 = LocalDate.of(currentYear, 12, 31);
        List<ContractSignStatisticsDTO> nuclearEnergyContractsList = new ArrayList<>();
        List<ContractSignStatisticsDTO> nonNuclearEnergyContractsList = new ArrayList<>();
        Calendar startCal = Calendar.getInstance();
        startCal.set(startDate2.getYear(), startDate2.getMonthValue() - 1, startDate2.getDayOfMonth());

        Calendar endCal = Calendar.getInstance();
        endCal.set(endDate2.getYear(), endDate2.getMonthValue() - 1, endDate2.getDayOfMonth());

        quotations.stream()
                .filter(q -> q.getModifyTime() != null && // 空检查
                        !isBefore((Date) q.getModifyTime(), startDate2, "before") &&
                        !isAfter((Date) q.getModifyTime(), endDate2, "after"))
                .forEach(q -> {
                    if ("nuclearEnergyContracts".equals(q.getYwsrlx())) {
                        nuclearEnergyContractsList.add(q);
                    } else if ("nonNuclearEnergyContracts".equals(q.getYwsrlx())) {
                        nonNuclearEnergyContractsList.add(q);
                    }
                });

        // 把两部分的值相加，思路：合同处于签署之前的状态可能会去关联报价表，但是中标的可能还不会去关联合同，因此分为两部分求解
        BigDecimal totalNuclearEnergyAmount = nuclearEnergyContractsList.stream()
                .map(ContractSignStatisticsDTO::getQuoteAmt)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalNonNuclearEnergyAmount = nonNuclearEnergyContractsList.stream()
                .map(ContractSignStatisticsDTO::getQuoteAmt)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        contractMilestonesStatisticDTO.setNuclearPendingContractCount(nuclearEnergyContracts2.size() + nuclearEnergyContractsList.size());
        contractMilestonesStatisticDTO.setNuclearPendingContractAmount(totalContractAmt2.add(totalNuclearEnergyAmount).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNonNuclearPendingContractCount(nonNuclearEnergyContracts2.size() + nonNuclearEnergyContractsList.size());
        contractMilestonesStatisticDTO.setNonNuclearPendingContractAmount(totalNonNuclearEnergyAmount.add(totalContractAmt3).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        // 已签
        LocalDate startOfYear1 = LocalDate.now().withDayOfYear(1);
        LocalDate endOfYear1 = startOfYear1.plusYears(1).withDayOfYear(1).minusDays(1);
        Date startDate3 = Date.from(startOfYear1.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate3 = Date.from(endOfYear1.atStartOfDay(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapperX<MarketContract> queryWrapper1 = new LambdaQueryWrapperX<>(MarketContract.class);
        queryWrapper1
                .leftJoin(MarketContractSign.class, MarketContractSign::getContractId, MarketContract::getId)
                .eq(MarketContractSign::getSignResult, true)
                .between(MarketContractSign::getSignDate, startDate3, endDate3)
                .select(MarketContract::getStatus, MarketContract::getContractType
                        ,MarketContract::getCustPersonId, MarketContract::getContractAmt
                );
        List<MarketContract> marketContractsNew = marketContractService.list(queryWrapper1);

        Map<String, List<MarketContract>> stringNewListMap = classifyContracts(marketContractsNew);

        List<MarketContract> nuclearEnergyContractsNew = stringNewListMap.get("nuclearEnergyContracts"); //历史核能收入合同列表
        List<MarketContract> nonNuclearEnergyContractsNew = stringNewListMap.get("nonNuclearEnergyContracts"); // 历史非核能收入合同列表

        BigDecimal newTotalContractAmt = nuclearEnergyContractsNew.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加所有的contractAmt

        BigDecimal totalContractAmt5 = nonNuclearEnergyContractsNew.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加所有的contractAmt
        HashMap<Integer, BigDecimal> integerBigDecimalHashMap6 = new HashMap<>();

        contractMilestonesStatisticDTO.setNonNuclearSignedContractCount(nonNuclearEnergyContractsNew.size());
        contractMilestonesStatisticDTO.setNonNuclearSignedContractAmount(totalContractAmt5.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractMilestonesStatisticDTO.setNuclearSignedContractCount(nuclearEnergyContractsNew.size());
        contractMilestonesStatisticDTO.setNuclearSignedContractAmount(newTotalContractAmt.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        return contractMilestonesStatisticDTO;
    }


    @Override
    public ContractSignedStatisticDTO getContractSign(){
        // 已中标未签署
        LambdaQueryWrapperX<MarketContract> condition1 = new LambdaQueryWrapperX<>(MarketContract.class);
        condition1.eq(MarketContract::getLogicStatus, "1");
        condition1.eq(MarketContract::getStatus, MarketContractStatusEnum.CREATED.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.AUDITING.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.SIGNING.getStatus())
                .or().eq(MarketContract::getStatus, MarketContractStatusEnum.PREDISTRIBUTE.getStatus());
        LocalDate thisYearStart = LocalDate.now().withDayOfYear(1);
        LocalDate now = LocalDate.now();
        Date startDate1 = Date.from(thisYearStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate1 = Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant());

        LambdaQueryWrapper<QuotationManagement> quotationWrapper1 = Wrappers.lambdaQuery(QuotationManagement.class)
                .eq(QuotationManagement::getStatus, 1)
                .ge(QuotationManagement::getModifyTime, startDate1)
                .lt(QuotationManagement::getModifyTime, endDate1); // 修改时间小于当前日期

        quotationWrapper1.select(QuotationManagement::getId);
        List<QuotationManagement> bidQuotationManagements = quotationManagementService.list(quotationWrapper1);
        List<String> bidQuotationIds = bidQuotationManagements.stream()
                .map(QuotationManagement::getId)
                .collect(Collectors.toList());
        if(!bidQuotationIds.isEmpty()) {
            condition1.in(MarketContract::getQuoteId, bidQuotationIds);
        }

        // 筛选合同状态，合同类型，合同关联客户id（关联核与非核），金额。
        condition1.select(MarketContract::getStatus, MarketContract::getContractType
                ,MarketContract::getCustPersonId, MarketContract::getContractAmt
        );
        List<MarketContract> marketContractList1 = marketContractService.list(condition1);
        Map<String, List<MarketContract>> stringListMap2 = classifyContracts(marketContractList1);
        List<MarketContract> nuclearEnergyContracts2 = stringListMap2.get("nuclearEnergyContracts"); //历史核能收入合同列表
        List<MarketContract> nonNuclearEnergyContracts2 = stringListMap2.get("nonNuclearEnergyContracts"); // 历史非核能收入合同列表
        BigDecimal totalContractAmt2 = nuclearEnergyContracts2.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 核能累加金额
        BigDecimal totalContractAmt3 = nonNuclearEnergyContracts2.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 非核累加金额

        // 反查中标未进入合同流程数据
        List<ContractSignStatisticsDTO> quotations = managementStaticsMapper.findQuotations();
        int currentYear = LocalDate.now().getYear();
        LocalDate startDate2 = LocalDate.of(currentYear-1, 1, 1);
        LocalDate endDate2 = LocalDate.of(currentYear-1, 12, 31);
        List<ContractSignStatisticsDTO> nuclearEnergyContractsList = new ArrayList<>();
        List<ContractSignStatisticsDTO> nonNuclearEnergyContractsList = new ArrayList<>();
        Calendar startCal = Calendar.getInstance();
        startCal.set(startDate2.getYear(), startDate2.getMonthValue() - 1, startDate2.getDayOfMonth());

        Calendar endCal = Calendar.getInstance();
        endCal.set(endDate2.getYear(), endDate2.getMonthValue() - 1, endDate2.getDayOfMonth());

        quotations.stream()
                .filter(q -> q.getModifyTime() != null && // 空检查
                        !isBefore((Date) q.getModifyTime(), startDate2, "before") &&
                        !isAfter((Date) q.getModifyTime(), endDate2, "after"))
                .forEach(q -> {
                    if ("nuclearEnergyContracts".equals(q.getYwsrlx())) {
                        nuclearEnergyContractsList.add(q);
                    } else if ("nonNuclearEnergyContracts".equals(q.getYwsrlx())) {
                        nonNuclearEnergyContractsList.add(q);
                    }
                });

        // 把两部分的值相加，思路：合同处于签署之前的状态可能会去关联报价表，但是中标的可能还不会去关联合同，因此分为两部分求解
        BigDecimal totalNuclearEnergyAmount = nuclearEnergyContractsList.stream()
                .map(ContractSignStatisticsDTO::getQuoteAmt)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalNonNuclearEnergyAmount = nonNuclearEnergyContractsList.stream()
                .map(ContractSignStatisticsDTO::getQuoteAmt)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        ContractSignedStatisticDTO contractSignedStatisticDTO = new ContractSignedStatisticDTO();
        contractSignedStatisticDTO.setNuclearHistoricalAmount(totalContractAmt2.add(totalNuclearEnergyAmount).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractSignedStatisticDTO.setNonNuclearHistoricalAmount(totalContractAmt3.add(totalNonNuclearEnergyAmount).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractSignedStatisticDTO.setHistoricalTotalAmount(contractSignedStatisticDTO.getNuclearHistoricalAmount()
                .add(contractSignedStatisticDTO.getNonNuclearHistoricalAmount()).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        // 新签
        LocalDate startOfYear1 = LocalDate.now().withDayOfYear(1);
        LocalDate endOfYear1 = startOfYear1.plusYears(1).withDayOfYear(1).minusDays(1);
        Date startDate3 = Date.from(startOfYear1.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate3 = Date.from(endOfYear1.atStartOfDay(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapperX<MarketContract> queryWrapper1 = new LambdaQueryWrapperX<>(MarketContract.class);
        queryWrapper1
                .leftJoin(MarketContractSign.class, MarketContractSign::getContractId, MarketContract::getId)
                .eq(MarketContractSign::getSignResult, true)
                .between(MarketContractSign::getSignDate, startDate3, endDate3)
                .select(MarketContract::getStatus, MarketContract::getContractType
                ,MarketContract::getCustPersonId, MarketContract::getContractAmt
        );
        List<MarketContract> marketContractsNew = marketContractService.list(queryWrapper1);
        Map<String, List<MarketContract>> stringNewListMap = classifyContracts(marketContractsNew);

        List<MarketContract> nuclearEnergyContractsNew = stringNewListMap.get("nuclearEnergyContracts"); //历史核能收入合同列表
        List<MarketContract> nonNuclearEnergyContractsNew = stringNewListMap.get("nonNuclearEnergyContracts"); // 历史非核能收入合同列表

        BigDecimal newTotalContractAmt = nuclearEnergyContractsNew.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加所有的contractAmt

        BigDecimal totalContractAmt5 = nonNuclearEnergyContractsNew.stream()
                .map(MarketContract::getContractAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加所有的contractAmt

        contractSignedStatisticDTO.setNuclearNewSignAmount(newTotalContractAmt.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractSignedStatisticDTO.setNonNuclearNewSignAmount(totalContractAmt5.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        contractSignedStatisticDTO.setNewSignTotalAmount(newTotalContractAmt.add(totalContractAmt5).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        return contractSignedStatisticDTO;

    }


    public Map<String, List<MarketContract>> classifyContracts(List<MarketContract> marketContracts) {
        if(marketContracts.isEmpty()) {
            return new HashMap<String, List<MarketContract>>();
        }


        // 获取非空客户ID列表
        List<String> custPersonIds = marketContracts.stream()
                .map(MarketContract::getCustPersonId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 查询客户信息
        LambdaQueryWrapperX<CustomerInfo> condition1 = new LambdaQueryWrapperX<>(CustomerInfo.class);
        if (!custPersonIds.isEmpty()) {
            condition1.in(CustomerInfo::getId, custPersonIds);
        }
        condition1.eq(CustomerInfo::getLogicStatus,1);
        condition1.select(CustomerInfo::getYwsrlx, CustomerInfo::getId);
        List<CustomerInfo> customerInfos = customerInfoService.list(condition1);

        // 获取核能客户ID
        Set<String> nuclearEnergyIds = customerInfos.stream()
                .filter(customerInfo -> "nuclear_nergy".equals(customerInfo.getYwsrlx()))
                .map(CustomerInfo::getId)
                .collect(Collectors.toSet());

        // 获取非核能客户ID
        Set<String> nonNuclearEnergyIds = customerInfos.stream()
                .filter(customerInfo -> "non_nuclear_nergy".equals(customerInfo.getYwsrlx()))
                .map(CustomerInfo::getId)
                .collect(Collectors.toSet());

        // 分类合同
        List<MarketContract> nuclearEnergyContracts = marketContracts.stream()
                .filter(contract -> nuclearEnergyIds.contains(contract.getCustPersonId()))
                .collect(Collectors.toList());

        List<MarketContract> nonNuclearEnergyContracts = marketContracts.stream()
                .filter(contract -> nonNuclearEnergyIds.contains(contract.getCustPersonId()))
                .collect(Collectors.toList());

        // 返回结果
        return Map.of(
                "nuclearEnergyContracts", nuclearEnergyContracts,
                "nonNuclearEnergyContracts", nonNuclearEnergyContracts
        );
    }


    @Override
    public QuoteOutbidDTO getQuoteOutbid(PersonManagementStaticsDTO managementStaticsReqDTO) {
        String orgId = userRedisHelper.getSimpleUserById(currentUserHelper.getUserId()).getOrgId();
        orgId="";
        Integer filterYear =managementStaticsReqDTO.getFilterYear();
        QuoteOutbidVO quoteOutbidVO = quoteOutbid(filterYear);
        QuoteOutbidDTO quoteOutbidDTO = new QuoteOutbidDTO();
        BeanCopyUtils.copyProperties(quoteOutbidVO, quoteOutbidDTO);

        LocalDate startTime = null;
        LocalDate endTime = null;
        if (filterYear != null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);

            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }

        BigDecimal totalQuoteAmt = new BigDecimal(0);
        BigDecimal totalQuoteAmt1 = new BigDecimal(0);

        List<QuotationManagement> quotationManagements = managementStaticsMapper.sendQuoteAmount(orgId, startTime, endTime,
                IncomeTypeEnum.NUCLEAR_NERGY.getName());

        List<QuotationManagement> quotationManagements1 = managementStaticsMapper.sendQuoteAmount(orgId, startTime, endTime,
                IncomeTypeEnum.NON_NUCLEAR_NERGY.getName());


        if (quotationManagements != null && !quotationManagements.isEmpty()) {
            totalQuoteAmt = quotationManagements.stream()
                    .filter(Objects::nonNull)
                    .map(QuotationManagement::getQuoteAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            quoteOutbidDTO.setNuclearEnergyTotal(totalQuoteAmt.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        }
          if (quotationManagements1 != null) {
            totalQuoteAmt1 = quotationManagements1.stream()
                    .filter(Objects::nonNull)
                    .map(QuotationManagement::getQuoteAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            quoteOutbidDTO.setNonNuclearEnergyTotal(totalQuoteAmt1.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        }
        quoteOutbidDTO.setSumTotal(totalQuoteAmt1.add(totalQuoteAmt).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        quoteOutbidDTO.setNumber(quoteOutbidVO.getOtherSendQuoteNum() + quoteOutbidVO.getNuclearEnergySendQuoteNum());
        return quoteOutbidDTO;

    }



    public QuoteOutbidVO quoteOutbid(Integer filterYear) {
        QuoteOutbidVO quoteOutbidVO = new QuoteOutbidVO();
        String ordId="";
        LocalDate startTime = null;
        LocalDate endTime = null;
        if (filterYear != null) {
            startTime = YearMonth.of(filterYear, 1).atDay(1);

            endTime = YearMonth.of(filterYear, 12).atEndOfMonth();
        }
        //核能
        Integer  nuclearSendQuoteNum = managementStaticsMapper.sendQuoteNum(ordId, startTime, endTime, IncomeTypeEnum.NUCLEAR_NERGY.getName());
        Integer  nuclearBiddingQuotNum = managementStaticsMapper.biddingQuotNum(ordId, startTime, endTime, IncomeTypeEnum.NUCLEAR_NERGY.getName());
        //核能中标率
        Double nuclearQuoteOutbidNum = 0.0;
        if (nuclearSendQuoteNum != 0) {
            BigDecimal nuclearBiddingQuotNumBD = BigDecimal.valueOf(nuclearBiddingQuotNum != null ? nuclearBiddingQuotNum : 0);
            BigDecimal nuclearSendQuoteNumBD = BigDecimal.valueOf(nuclearSendQuoteNum);
            nuclearQuoteOutbidNum = nuclearBiddingQuotNumBD.divide(nuclearSendQuoteNumBD, 4, BigDecimal.ROUND_HALF_UP) // 保留四位小数
                    .doubleValue();
        }
        //非核能
        Integer  notSendQuoteNum = managementStaticsMapper.sendQuoteNum(ordId, startTime, endTime, IncomeTypeEnum.NON_NUCLEAR_NERGY.getName());
        Integer  notBiddingQuotNum = managementStaticsMapper.biddingQuotNum(ordId, startTime, endTime, IncomeTypeEnum.NON_NUCLEAR_NERGY.getName());
        //非核能中标率
        Double notQuoteOutbidNum = 0.0;
        if (notSendQuoteNum != 0) {
            BigDecimal notBiddingQuotNumBD = BigDecimal.valueOf(notBiddingQuotNum != null ? notBiddingQuotNum : 0);
            BigDecimal notSendQuoteNumBD = BigDecimal.valueOf(notSendQuoteNum);
            notQuoteOutbidNum = notBiddingQuotNumBD.divide(notSendQuoteNumBD, 4, BigDecimal.ROUND_HALF_UP) // 保留四位小数
                    .doubleValue();
        }

        quoteOutbidVO.setOtherQuoteOutbidNum(notQuoteOutbidNum*100);
        quoteOutbidVO.setNuclearEnergyQuoteOutbidNum(nuclearQuoteOutbidNum*100);
        quoteOutbidVO.setNuclearEnergySendQuoteNum(nuclearSendQuoteNum);
        quoteOutbidVO.setOtherSendQuoteNum(notSendQuoteNum);
        return quoteOutbidVO;
    }




    @Override
    public RequirementRatioDTO   getRequirement() {
        LambdaQueryWrapperX<RequirementMangement> condition = new LambdaQueryWrapperX<>(RequirementMangement.class);
        int currentYear = Year.now().getValue();
        LocalDateTime startOfYear = LocalDateTime.of(currentYear, 1, 1, 0, 0);
        LocalDateTime endOfYear = LocalDateTime.of(currentYear, 12, 31, 23, 59, 59);
        condition
                .leftJoin(CustomerInfo.class,CustomerInfo::getId,RequirementMangement::getCustPerson)
                .eq(RequirementMangement::getLogicStatus,1)
                .and(q -> q
                        .eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus())
                        .or().eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.TO_BE_DECIDED.getStatus())
                        .or().eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.NO_RESPONSE.getStatus())
                )
                .and(q -> q
                        .ge(RequirementMangement::getCreateTime, startOfYear) // 筛选开始时间
                        .le(RequirementMangement::getCreateTime, endOfYear)   // 筛选结束时间
                )
                .eq(CustomerInfo::getYwsrlx,IncomeTypeEnum.NUCLEAR_NERGY.getName())
                .select(RequirementMangement::getId,RequirementMangement::getStatus);
        List<RequirementMangement> requirementMangements = requirementMangementService.list(condition);
        RequirementRatioDTO requirementRatioDTO = new RequirementRatioDTO();
        requirementRatioDTO.setNuclearReception(requirementMangements.size());
        Double nuclearCount = 0.0;
        for(RequirementMangement requirementMangement : requirementMangements){
            if(RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus().equals(requirementMangement.getStatus())){
                nuclearCount++;
            }
        }
        requirementRatioDTO.setNuclearResponse(nuclearCount);
        if(requirementMangements.size() !=0) {
            requirementRatioDTO.setNuclearRatio((nuclearCount/(double)requirementMangements.size())*100);
        }

        LambdaQueryWrapperX<RequirementMangement> condition1 = new LambdaQueryWrapperX<>(RequirementMangement.class);
        condition1
                .leftJoin(CustomerInfo.class,CustomerInfo::getId,RequirementMangement::getCustPerson)
                .eq(RequirementMangement::getLogicStatus,1)
                .and(q -> q
                        .eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus())
                        .or().eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.TO_BE_DECIDED.getStatus())
                        .or().eq(RequirementMangement::getStatus, RequirementManagementStatusEnum.NO_RESPONSE.getStatus())
                )
                .and(q -> q
                        .ge(RequirementMangement::getCreateTime, startOfYear) // 筛选开始时间
                        .le(RequirementMangement::getCreateTime, endOfYear)   // 筛选结束时间
                )
                .eq(CustomerInfo::getYwsrlx,IncomeTypeEnum.NON_NUCLEAR_NERGY.getName())
                .select(RequirementMangement::getId,RequirementMangement::getStatus);
        List<RequirementMangement> requirementMangements1 = requirementMangementService.list(condition1);
        requirementRatioDTO.setNonNuclearReception(requirementMangements1.size());
        Double nonNuclearCount = 0.0;
        for(RequirementMangement requirementMangement : requirementMangements1){
            if(RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus().equals(requirementMangement.getStatus())){
                nonNuclearCount++;
            }
        }
        requirementRatioDTO.setNonNuclearResponse(nonNuclearCount);
        if(requirementMangements1.size() !=0) {
            requirementRatioDTO.setNonNuclearRatio((nonNuclearCount/(double)(requirementMangements1.size()))*100);
        }
        return requirementRatioDTO;
    }


    @Override
    public PredictLeadDTO getPredictLead() {
        List<PredictLeadVO> predictLeadVOS = personalCenterMapper.selectEstimated();
        PredictLeadDTO predictLeadDTO = new PredictLeadDTO();
        if (predictLeadVOS.isEmpty()) {
            predictLeadDTO.setAccount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            predictLeadDTO.setSum(0);
            return predictLeadDTO;
        }
        int thisYear = LocalDate.now().getYear();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        List<PredictLeadVO> thisYearPredictLeadVOS = predictLeadVOS.stream()
                .filter(vo -> {
                    String time = vo.getTime();
                    try {
                        if (time != null && !time.isEmpty()) {
                            LocalDate dateTime = LocalDate.parse(time, formatter);
                            return thisYear == dateTime.getYear();
                        }
                    } catch (DateTimeParseException e) {

                    }
                    return false;
                })
                .collect(Collectors.toList());
        double totalAmount = thisYearPredictLeadVOS.stream()
                .mapToDouble(PredictLeadVO::getAccount)
                .sum();
        BigDecimal result = BigDecimal.valueOf(totalAmount).divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
        predictLeadDTO.setAccount(result);
        predictLeadDTO.setSum(thisYearPredictLeadVOS.size());
        return predictLeadDTO;
    }



    @Override
    public QuoteOutbidDTO getAddressableMarket(PersonManagementStaticsDTO managementStaticsReqDTO) {
        return this.getQuoteOutbid(managementStaticsReqDTO);
    }


    private boolean isBefore(Date modifyTime, LocalDate startDate, String context) {
        LocalDate modifyLocalDate = modifyTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return modifyLocalDate.isBefore(startDate);
    }


    private boolean isAfter(Date modifyTime, LocalDate endDate, String context) {
        LocalDate modifyLocalDate = modifyTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return modifyLocalDate.isAfter(endDate);
    }

    @Override
    public TaskStatisticDTO taskStatistics() {
        String orgId = TenantContextHolder.getTenantId();
        List<Scheme> schemes = managementStaticsMapper.selectStatistic(CurrentUserHelper.getCurrentUserId(),orgId);
        AtomicInteger operation= new AtomicInteger();
        AtomicInteger closed= new AtomicInteger();
        AtomicInteger overDue= new AtomicInteger();
        schemes.forEach(item ->{
            if(SchemeStatusEnum.DOING.getStatus().equals(item.getStatus())){
                operation.getAndIncrement();
            }
            if( SchemeStatusEnum.CLOSED.getStatus().equals(item.getStatus())){
                closed.getAndIncrement();
            }
            Date endTime = item.getEndTime();
            if (endTime != null && new Date(System.currentTimeMillis()).after(endTime) &&
                    (SchemeStatusEnum.DOING.getStatus().equals(item.getStatus()))){
                overDue.getAndIncrement();
            }
        });
        TaskStatisticDTO taskStatisticDTO = new TaskStatisticDTO();
        HashMap<String, Integer> stringHashMap = new HashMap<String, Integer>();
        stringHashMap.put("doing",Integer.valueOf(operation.get()));
        stringHashMap.put("closed",Integer.valueOf(closed.get()));
        stringHashMap.put("overDue", Integer.valueOf(overDue.get()));
        stringHashMap.put("sum", Integer.valueOf(schemes.size()));
        taskStatisticDTO.setTaskStatistic(stringHashMap);
        return taskStatisticDTO;
    }
}