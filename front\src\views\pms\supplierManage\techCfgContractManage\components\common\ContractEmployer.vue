<script setup lang="ts">
import { IOrionTableOptions, OrionTable, TreeSelectOrg } from 'lyra-component-vue3';
import {
  computed, createVNode, h, inject, ref,
} from 'vue';
import { parseTableHeaderButton } from '/@/views/pms/utils/utils';
import Api from '/@/api';
import {
  filter, get, map, set,
} from 'lodash-es';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

const props = withDefaults(defineProps<{
  isOperable?:boolean,
}>(), {
  isOperable: false,
});

const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const tableRef = ref();
const tableOptions:IOrionTableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  ...(props?.isOperable
    ? {
      rowSelection: {},
    }
    : {}),
  api: () => new Api('/pms/contractCenter/list')
    .fetch({}, get(basicContractEmployerPlan, 'contractNumber'), 'GET')
    .then((res) => map(res, (row) => ({
      ...row,
      centerCfg: {
        centerName: row.centerName,
        centerCode: row.centerCode,
      },
    }))),
  isRowAdd: computed(() => props.isOperable),
  addRowApi(record, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回新数据对象，或者新数据ID
        resolve(new Date().valueOf().toString());
      }, 100);
    });
  },
  editRowApi(record, oldRecord, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve();
      }, 100);
    });
  },
  batchDeleteApi({ ids, rows }) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve();
      }, 100);
    });
  },
  columns: filter([
    {
      dataIndex: 'centerCfg',
      title: '用人单位名称',
      edit: (text, record) => computed(() => props.isOperable).value,
      editValueFormat: (text, record) => get(record, 'centerCfg.centerName') || get(record, 'centerName'),
      editComponentValueFormat: ({ text, record }) => get(record, 'centerCode'),
      editComponent(methods) {
        // eslint-disable-next-line prefer-rest-params
        const parentArgs = Array.from(arguments);
        const originRecord = get(parentArgs, '0.record');
        return h(TreeSelectOrg, {
          fieldNames: {
            value: 'deptCode',
          },
          value: get(originRecord, 'centerCfg.centerCode'),
          async onChange(value, label, extra) {
            methods.edit({
              centerName: get(label, 0),
              centerCode: value,
            });
            methods.save();
            try {
              const result = await new Api('/pms/contractMain/judge').fetch({
                deptCode: value,
              }, '', 'GET');
              set(originRecord, 'centerType', result);
              tableRef.value?.updateRowData(originRecord, {
                isReplace: true,
              });
              // methods.setRowValue({
              //   centerType: result,
              // });
            } catch (e) {
            }
          },
        });
      },
      editComponentProps: {
        showSearch: true,
        placeholder: '请选择用人单位',
        style: {
          width: '100%',
        },
      },
      show: true,
    },
    {
      dataIndex: 'centerType',
      title: '用人单位类型',
      show: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
      show: computed(() => props.isOperable).value,
    },
  ], (item) => item.show),
  deleteToolButton: computed(() => parseTableHeaderButton('add|enable|disable|delete', {
    add: props.isOperable,
    delete: props.isOperable,
  })),
  canResize: false,
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '警告',
          icon: h(ExclamationCircleOutlined, {
            style: {
              color: '#1890ff',
            },
          }),
          content: '确定要移除这条数据',
          centered: true,
          onOk() {
            return new Promise((resolve, reject) => {
              tableRef.value?.deleteRowDataById(record.id);
              resolve({});
            }).catch(() => {});
          },
          onCancel() {},
        });
      },
    },
  ],
};

defineExpose({
  exportTableData() {
    const data = tableRef.value?.getTableData();
    return map(data, (item) => ({
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
      centerName: get(item, 'centerCfg.centerName'),
      centerCode: get(item, 'centerCfg.centerCode'),
      centerType: get(item, 'centerType'),
    }));
  },
});
</script>

<template>
  <div class="contract-employer">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.contract-employer{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>