package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectAchievementFloder Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 13:43:27
 */
@ApiModel(value = "ProjectAchievementFloderDTO对象", description = "项目成果文件夹表")
@Data
public class ProjectAchievementFloderDTO extends ObjectDTO implements Serializable{

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 文件夹名称
     */
    @ApiModelProperty(value = "文件夹名称")
    private String name;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private String approvalId;

}
