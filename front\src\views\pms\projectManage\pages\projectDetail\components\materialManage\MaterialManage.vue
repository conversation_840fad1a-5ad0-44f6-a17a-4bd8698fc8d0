<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    :dataSource="tableData"
    @smallSearch="smallSearch"
  >
    <template #toolbarLeft>
      <Button
        type="primary"
        :disabled="disabledBtn"
        @click="onRefreshData"
      >
        <template #icon>
          <SyncOutlined />
        </template>
        刷新数据
      </Button>
    </template>

    <template #stage>
      <Tags :data="[]" />
    </template>

    <template #demandDate="{text,record}">
      <div class="flex flex-ac">
        <span class="date m-b-r">
          {{ text }}
        </span>
        <Tags :data="dateStatus(record)" />
      </div>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import { SyncOutlined } from '@ant-design/icons-vue';
import { Button } from 'ant-design-vue';
import { computed, ref } from 'vue';
import Tags from '/@/views/pms/projectManage/components/tags/Tags.vue';
import dayjs from 'dayjs';

const tableData = [
  {
    id: 1,
    isLibrary: false,
    demandDate: '2023-04-15',
  },
  {
    id: 2,
    isLibrary: false,
    demandDate: '2023-03-28',
  },
  {
    id: 3,
    isLibrary: true,
    demandDate: '2023-04-05',
  },
];
const tableRef = ref();
const columns = [
  {
    title: '计划编号',
    fixed: 'left',
    dataIndex: 'address',
  },
  {
    title: '计划行号',
    fixed: 'left',
    dataIndex: 'address',
  },
  {
    title: '所属阶段',
    fixed: 'left',
    dataIndex: 'stage',
    slots: { customRender: 'stage' },
  },
  {
    title: '阶段状态',
    fixed: 'left',
    dataIndex: 'address',
  },
  {
    title: '物料/服务编码',
    fixed: 'left',
    dataIndex: 'address',
  },
  {
    title: '物料/服务描述',
    fixed: 'left',
    dataIndex: 'address',
  },
  {
    title: '规格型号',
    dataIndex: 'address',
  },
  {
    title: '需求数量(单位)',
    dataIndex: 'address',
  },
  {
    title: '需求人',
    dataIndex: 'address',
  },
  {
    title: '需求类型',
    dataIndex: 'address',
  },
  {
    title: 'QA等级',
    dataIndex: 'address',
  },
  {
    title: '核安全等级',
    dataIndex: 'address',
  },
  {
    title: '备件分级（SPV）',
    dataIndex: 'address',
  },
  {
    title: '合同到货日期',
    dataIndex: 'address',
  },
  {
    title: '采购工程师',
    dataIndex: 'address',
  },
  {
    title: '是否入库',
    fixed: 'right',
    dataIndex: 'isLibrary',
    width: 100,
  },
  {
    title: '入库数量(单位)',
    fixed: 'right',
    dataIndex: 'address',
  },
  {
    title: '需求日期',
    fixed: 'right',
    dataIndex: 'demandDate',
    width: 200,
    slots: { customRender: 'demandDate' },
  },
];
const filterConfig = {
  fields: [
    {
      field: 'q',
      fieldName: '是否入库',
      component: 'Select',
      hidden: false,
    },
    {
      field: 'w',
      fieldName: '所属阶段',
      component: 'Checkbox',
      hidden: false,
    },
    {
      field: 'e',
      fieldName: '所属状态',
      component: 'Checkbox',
      hidden: false,
    },
    {
      field: 'r',
      fieldName: '需求日期',
      component: 'Date',
      hidden: false,
    },
    {
      field: 't',
      fieldName: '合同到期日期',
      component: 'Date',
      hidden: false,
    },
  ],
};
const disabledBtn = ref(false);

// 刷新数据
function onRefreshData() {
  disabledBtn.value = true;
  setTimeout(() => {
    disabledBtn.value = false;
  }, 10 * 1000);
}

const tableOptions = computed(() => ({
  rowSelection: {},
  columns,
  showToolButton: false,
  filterConfig,
  filter2Change(data) {
    console.log('值有变化了', data);
  },
}));

function smallSearch(keyword) {
  console.log(keyword);
}

const dateStatus = computed(() => (record) => {
  const { isLibrary, demandDate } = record;
  if (isLibrary || !demandDate) {
    return [];
  }
  if (dayjs(demandDate) <= dayjs()) {
    return [
      {
        color: 'error',
        label: `逾期${dayjs().diff(dayjs(demandDate), 'day')}天`,
      },
    ];
  }
  if (dayjs(demandDate).subtract(15, 'day') <= dayjs()) {
    return [
      {
        color: 'warning',
        label: '逾期预警',
      },
    ];
  }
});

</script>

<style scoped lang="less">

</style>
