import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';

// 表格组件新增、编辑抽屉
export function openFormDrawer(component: any, title: string, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? `编辑${title}` : `新增${title}`,
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}
