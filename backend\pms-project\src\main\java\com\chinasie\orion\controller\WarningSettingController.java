package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.WarningSettingDTO;
import com.chinasie.orion.domain.dto.WarningSettingQueryDTO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.WarningSettingVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.WarningSettingService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/03/14:02
 * @description:
 */
@RestController
@RequestMapping("/warning-setting")
@Api(tags = "预警设置")
public class WarningSettingController {

    @Resource
    private WarningSettingService warningSettingService;
    @Resource
    private DictBo dictBo;

    @ApiOperation("获取预警类型列表")
    @GetMapping(value = "/typeList")
    @LogRecord(success = "【{USER{#logUserId}}】获取预警类型列表", type = "预警设置", subType = "获取预警类型列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getWarningTypeList() {
        return new ResponseDTO<>(warningSettingService.getWarningTypeList());
    }

    @ApiOperation("获取预警设置列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warningSettingQueryDTO", dataType = "WarningSettingQueryDTO")
    })
    @PostMapping(value = "/getList")
    @LogRecord(success = "【{USER{#logUserId}}】获取预警设置列表", type = "预警设置", subType = "获取预警设置列表", bizNo = "")
    public ResponseDTO<List<WarningSettingVO>> getWarningSettingList(@RequestBody WarningSettingQueryDTO warningSettingQueryDTO) throws Exception {
        return new ResponseDTO<>(warningSettingService.getWarningSettingList(warningSettingQueryDTO));
    }

    @ApiOperation("获取预警设置详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取预警设置详情", type = "预警设置", subType = "获取预警设置详情", bizNo = "{{#id}}")
    public ResponseDTO<WarningSettingVO> getWarningSettingDetail(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(warningSettingService.getWarningSettingDetail(id));
    }

    @ApiOperation("编辑预警设置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "warningSettingDTO", dataType = "WarningSettingDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑预警设置", type = "预警设置", subType = "编辑预警设置", bizNo = "")
    public ResponseDTO<Boolean> editWarningSetting(@RequestBody WarningSettingDTO warningSettingDTO) throws Exception {
        return new ResponseDTO<>(warningSettingService.editWarningSetting(warningSettingDTO));
    }

    @ApiOperation("批量启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "takeEffectDTO", dataType = "TakeEffectDTO")
    })
    @PutMapping(value = "/takeEffectBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量启用禁用", type = "预警设置", subType = "批量启用禁用", bizNo = "")
    public ResponseDTO<Boolean> takeEffectWarningSetting(@RequestBody TakeEffectDTO takeEffectDTO) throws Exception {
        return new ResponseDTO<>(warningSettingService.takeEffectWarningSetting(takeEffectDTO));
    }

    @ApiOperation("获取预警提醒方式")
    @GetMapping(value = "/warningWayList")
    @LogRecord(success = "【{USER{#logUserId}}】获取预警提醒方式", type = "预警设置", subType = "获取预警提醒方式", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getWarningWayList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.Warning_Way));
    }

    @ApiOperation("获取预警提醒频率")
    @GetMapping(value = "/warningFrequencyList")
    @LogRecord(success = "【{USER{#logUserId}}】获取预警提醒频率", type = "预警设置", subType = "获取预警提醒频率", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getWarningFrequencyList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.Warning_Frequency));
    }

    @ApiOperation("获取项目角色列表")
    @GetMapping(value = "/role/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目角色列表", type = "预警设置", subType = "获取项目角色列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SimpleVo>> getProjectRoleList(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(warningSettingService.getProjectRoleList(projectId));
    }
}
