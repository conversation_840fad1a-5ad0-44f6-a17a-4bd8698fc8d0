<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2015, Yahoo! Inc.
     Licensed under the terms of the Apache License 2.0.
     See LICENSE file at the project root for terms. -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yahoo.datasketches</groupId>
    <artifactId>sketches</artifactId>
    <version>0.9.0</version>
    <packaging>pom</packaging>

    <!-- Project Information -->
    <name>${project.groupId}:${project.artifactId}</name>
    <description>Sketches contains the core algorithms used by other repositories in the DataSketches library.</description>
    <url>https://datasketches.github.io/</url>
    <inceptionYear>2015</inceptionYear>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <organization>
        <name>Yahoo! Inc.</name>
        <url>https://www.yahoo.com</url>
    </organization>

    <developers>
        <developer>
            <name>Lee Rhodes</name>
            <roles>
                <role>founder, project admin, lead developer</role>
            </roles>
            <url>https://github.com/leerho</url>
        </developer>
        <developer>
            <name>Kevin Lang</name>
            <roles>
                <role>co-founder, lead research scientist, developer</role>
            </roles>
            <url>https://github.com/kevinjlang</url>
        </developer>
        <developer>
            <name>Alexander Saydakov</name>
            <roles>
                <role>developer</role>
            </roles>
            <url>https://github.com/AlexanderSaydakov</url>
        </developer>
        <developer>
            <name>Justin Thaler</name>
            <roles>
                <role>research scientist, developer</role>
            </roles>
            <url>https://github.com/Justin8712</url>
        </developer>
        <developer>
            <name>Edo Liberty</name>
            <roles>
                <role>research scientist, developer</role>
            </roles>
            <url>https://github.com/edoliberty</url>
        </developer>
        <developer>
            <name>Jon Malkin</name>
            <roles>
                <role>developer</role>
            </roles>
            <url>https://github.com/jmalkin</url>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <url>https://github.com/DataSketches/sketches-core/graphs/contributors</url>
        </contributor>
    </contributors>
    <!-- End Project Information -->

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <argLine>-Xmx1024m -Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8</argLine>
    </properties>

    <!-- Environment Settings -->
    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/DataSketches/sketches-core/issues</url>
    </issueManagement>

    <ciManagement>
        <system>travis</system>
        <url>https://travis-ci.org/DataSketches/sketches-core</url>
    </ciManagement>

    <mailingLists>
        <mailingList>
            <name>sketches-user</name>
            <archive>https://groups.google.com/forum/#!forum/sketches-user</archive>
            <subscribe>mailto:<EMAIL></subscribe>
            <unsubscribe>mailto:<EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
        </mailingList>
    </mailingLists>

    <scm>
        <connection>scm:git:ssh://**************/DataSketches/sketches-core.git</connection>
        <developerConnection>scm:git:ssh://**************/DataSketches/sketches-core.git</developerConnection>
        <url>https://github.com/DataSketches/sketches-core.git</url>
        <tag>sketches-0.9.0</tag>
    </scm>

    <prerequisites>
        <maven>3.0.4</maven>
    </prerequisites>

    <repositories>
        <repository>
            <id>jcenter</id>
            <name>bintray</name>
            <url>https://jcenter.bintray.com</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <!-- This is optional -->
    <profiles>
        <profile>
            <id>strict</id>
            <build>
                <pluginManagement>
                    <plugins>

                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-compiler-plugin</artifactId>
                            <configuration>
                                <source>${maven.compiler.source}</source>
                                <target>${maven.compiler.target}</target>
                                <compilerId>javac-with-errorprone</compilerId>
                                <forceJavacCompilerUse>true</forceJavacCompilerUse>
                            </configuration>
                            <dependencies>
                                <dependency>
                                    <groupId>org.codehaus.plexus</groupId>
                                    <artifactId>plexus-compiler-javac-errorprone</artifactId>
                                    <version>2.8.1</version>
                                </dependency>
                            </dependencies>
                        </plugin>

                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>
    <!-- End of Environment Settings -->

    <modules>
        <module>memory</module>
        <module>sketches</module>
    </modules>

    <dependencies>
        <!-- Test Scope -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.11</version>
            <scope>test</scope>
        </dependency>
        <!-- run locally for now
        <dependency>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>findbugs</artifactId>
          <version>3.0.1</version>
          <scope>test</scope>
        </dependency>
        -->
    </dependencies>

    <build>
        <plugins>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                  <!--
                    <show>private</show>
                  -->
                  <stylesheetfile>sketches/src/main/javadoc/stylesheet.css</stylesheetfile>
                  <docfilessubdirs>true</docfilessubdirs>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                          </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                            <goal>test-jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <serverId>sonatype-nexus-staging</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>false</autoReleaseAfterClose>
                </configuration>
            </plugin>
            
            <!-- Code coverage plugin, generates coverage report to target/site/jacoco/
                 To skip coverage generation add -Djacoco.skip=true -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <!-- Prepares the property pointing to the JaCoCo runtime agent which
                         is passed as VM argument when Maven the Surefire plugin is executed. -->
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <!-- Sets the path to the file which contains the execution data. -->
                            <destFile>${jacoco.ut.execution.data.file}</destFile>
                        </configuration>
                    </execution>
                    <!-- Ensures that the code coverage report for unit tests is created after
                         unit tests have been run. -->
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <!-- Sets the path to the file which contains the execution data. -->
                            <dataFile>${jacoco.ut.execution.data.file}</dataFile>
                            <!-- Sets the output directory for the code coverage report. -->
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <!-- implementation is needed only for Maven 2 -->
                                <rule implementation="org.jacoco.maven.RuleConfiguration">
                                    <element>BUNDLE</element>
                                    <limits>
                                        <!-- implementation is needed only for Maven 2 -->
                                        <limit implementation="org.jacoco.report.check.Limit">
                                            <counter>INSTRUCTION</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.89</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Coveralls is a online code coverage reporting tool that leverages JaCoCo -->
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <configuration>
                    <repoToken />
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <executions>
                    <execution>
                        <id>sign-artifacts</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>sign</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
        </plugins>

        <pluginManagement>
            <plugins>
              <!-- ORG.APACHE.MAVEN -->
              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-assembly-plugin</artifactId>
                  <version>3.0.0</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-clean-plugin</artifactId>
                  <version>3.0.0</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-compiler-plugin</artifactId>
                  <version>3.6.1</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-dependency-plugin</artifactId>
                  <version>3.0.0</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-deploy-plugin</artifactId>
                  <version>2.8.2</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-gpg-plugin</artifactId>
                  <version>1.6</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-help-plugin</artifactId>
                  <version>2.2</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-install-plugin</artifactId>
                  <version>2.5.2</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-javadoc-plugin</artifactId>
                  <version>2.10.4</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-release-plugin</artifactId>
                  <version>2.5.3</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-resources-plugin</artifactId>
                  <version>3.0.2</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-shade-plugin</artifactId>
                  <version>3.0.0</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-source-plugin</artifactId>
                  <version>3.0.1</version>
              </plugin>

              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-surefire-plugin</artifactId>
                  <version>2.19.1</version>
              </plugin>

              <!-- OTHER -->
              <plugin>
                  <groupId>org.codehaus.mojo</groupId>
                  <artifactId>exec-maven-plugin</artifactId>
                  <version>1.6.0</version>
              </plugin>

              <plugin>
                  <groupId>org.codehaus.mojo</groupId>
                  <artifactId>license-maven-plugin</artifactId>
                  <version>1.12</version>
              </plugin>

              <plugin>
                  <groupId>org.eluder.coveralls</groupId>
                  <artifactId>coveralls-maven-plugin</artifactId>
                  <version>4.3.0</version>
              </plugin>

              <plugin>
                  <groupId>org.jacoco</groupId>
                  <artifactId>jacoco-maven-plugin</artifactId>
                  <version>0.7.9</version>
              </plugin>

              <plugin>
                  <groupId>org.sonatype.plugins</groupId>
                  <artifactId>nexus-staging-maven-plugin</artifactId>
                  <version>1.6.8</version>
              </plugin>

              <plugin>
                <groupId>com.versioneye</groupId>
                <artifactId>versioneye-maven-plugin</artifactId>
                <version>3.11.1</version>
              </plugin>

            </plugins>
        </pluginManagement>
    </build>
</project>