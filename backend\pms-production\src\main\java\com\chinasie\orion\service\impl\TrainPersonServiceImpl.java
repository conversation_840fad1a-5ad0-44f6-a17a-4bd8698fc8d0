package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.OrionRoleConfig;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.TrainPersonDTO;
import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.dto.train.SettingTrainPersonScoreDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterUserDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.TrainPersonVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.domain.vo.train.TrainPersonExportVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.TrainPersonMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * TrainPerson 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:14
 */
@Service
@Slf4j
public class TrainPersonServiceImpl extends OrionBaseServiceImpl<TrainPersonMapper, TrainPerson> implements TrainPersonService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    private FileApiService fileApiService;

    private TrainManageService trainManageService;

    private TrainEquivalentService trainEquivalentService;

    private BasicUserService basicUserService;

    private TrainCenterService trainCenterService;

    private UserRedisHelper userRedisHelper;


    private PersonTrainInfoService personTrainInfoService;

    private PersonTrainInfoRecordService personTrainInfoRecordService;
    @Autowired
    private TrainContactService trainContactService;
    @Autowired
    private OrionRoleConfig orionRoleConfig;
    @Autowired
    public void setPersonTrainInfoRecordService(PersonTrainInfoRecordService personTrainInfoRecordService) {
        this.personTrainInfoRecordService = personTrainInfoRecordService;
    }

    @Autowired
    public void setPersonTrainInfoService(PersonTrainInfoService personTrainInfoService) {
        this.personTrainInfoService = personTrainInfoService;
    }

    @Autowired
    public void setUserRedisHelper(UserRedisHelper userRedisHelper) {
        this.userRedisHelper = userRedisHelper;
    }

    @Autowired
    public void setFileApiService(FileApiService fileApiService) {
        this.fileApiService = fileApiService;
    }

    @Autowired
    public void setTrainManageService(TrainManageService trainManageService) {
        this.trainManageService = trainManageService;
    }

    @Autowired
    public void setTrainEquivalentService(TrainEquivalentService trainEquivalentService) {
        this.trainEquivalentService = trainEquivalentService;
    }

    @Autowired
    public void setBasicUserService(BasicUserService basicUserService) {
        this.basicUserService = basicUserService;
    }

    @Autowired
    public void setTrainCenterService(TrainCenterService trainCenterService) {
        this.trainCenterService = trainCenterService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TrainPersonVO detail(String id, String pageCode) throws Exception {
        TrainPerson trainPerson = this.getById(id);
        TrainPersonVO result = BeanCopyUtils.convertTo(trainPerson, TrainPersonVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param trainPersonDTO
     */
    @Override
    public String create(TrainPersonDTO trainPersonDTO) throws Exception {
        TrainPerson trainPerson = BeanCopyUtils.convertTo(trainPersonDTO, TrainPerson::new);
        this.save(trainPerson);

        String rsp = trainPerson.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param trainPersonDTO
     */
    @Override
    public Boolean edit(TrainPersonDTO trainPersonDTO) throws Exception {
        TrainPerson trainPerson = BeanCopyUtils.convertTo(trainPersonDTO, TrainPerson::new);

        this.updateById(trainPerson);

        String rsp = trainPerson.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainPersonVO> pages(Page<TrainPersonDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TrainPerson> condition = new LambdaQueryWrapperX<>(TrainPerson.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainPerson::getCreateTime);


        Page<TrainPerson> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainPerson::new));

        PageResult<TrainPerson> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TrainPersonVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TrainPersonVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TrainPersonVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<TrainPersonVO> vos) throws Exception {
        vos.forEach(vo -> {

        });

    }
    @Override
    public List<TrainPersonVO> trainPersonList(TrainPersonDTO trainPersonDTO) {
        String trainId = trainPersonDTO.getTrainId();
        String trainNumber = trainPersonDTO.getTrainNumber();
        String trainCenterId = trainPersonDTO.getTrainCenterId();

        if(StrUtil.isEmpty(trainId)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "培训ID不能为空");
        }
        if(StrUtil.isEmpty(trainNumber)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "培训编码不能为空");
        }
        if(StrUtil.isEmpty(trainId)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "中心ID不能为空");
        }
        LambdaQueryWrapperX<TrainPerson> condition = new LambdaQueryWrapperX<>(TrainPerson.class);
        condition.eq(TrainPerson::getTrainId,trainId);
        condition.eq(TrainPerson::getTrainNumber,trainNumber);
        condition.eq(TrainPerson::getTrainCenterId,trainCenterId);

        String keyWord=trainPersonDTO.getKeyWord();
        if(StringUtils.hasText(keyWord)){
          condition.and(item->item.like(TrainPerson::getUserCode,keyWord)
                  .or().like(TrainPerson::getFullName,keyWord));
        }
        List<TrainPerson> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<String> userCodeList = list.stream().map(TrainPerson::getUserCode).distinct().collect(Collectors.toList());
        List<TrainPersonVO> trainPersonVOS = BeanCopyUtils.convertListTo(list, TrainPersonVO::new);
        this.packageTrainVO(trainCenterId,trainPersonVOS,userCodeList);

        //权限设置
        TrainManage trainManage = trainManageService.getById(trainId);
        if(trainManage.getStatus() == 1){
            trainPersonVOS.forEach(vo -> vo.setEdit(false));
        }else{
            String userId = CurrentUserHelper.getCurrentUserId();
            List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());
            trainPersonVOS.forEach(vo -> {
                vo.setEdit(baseCodeList.contains(trainManage.getBaseCode()));
            });
        }
        return trainPersonVOS;
    }


    public void packageTrainVO(String trainCenterId,List<TrainPersonVO> trainPersonVOS,List<String> userCodeList){
        TrainCenter trainCenter = trainCenterService.getById(trainCenterId);
        if(null ==trainCenter ){
            trainCenter = new TrainCenter();
        }
        Map<String,BasicUser> userMap = this.basicUserService.getMapByNumberList(userCodeList);
        TrainCenter finalTrainCenter = trainCenter;
        trainPersonVOS.forEach(item->{
            BasicUser basicUser =userMap.getOrDefault(item.getUserCode(),new BasicUser());
            if(null != basicUser){
                if(!basicUser.getIsBasic()){
                    item.setPersonId(basicUser.getId());
                }
                item.setNowPosition(basicUser.getNowPosition());
                item.setUserCode(basicUser.getUserCode());
                item.setFullName(basicUser.getFullName());
                item.setSex(basicUser.getSex());
                item.setNowPosition(basicUser.getNowPosition());
                item.setInstituteName(basicUser.getInstituteName());
                item.setCompanyName(basicUser.getCompanyName());
                item.setDeptName(basicUser.getDeptName());
            }
            item.setTrainCenterName(finalTrainCenter.getAttendCenterName());
        });

    }

    @Override
    public Boolean createBatch(TrainCenterUserDTO trainPersonDTO) {
        List<String> codeList = trainPersonDTO.getCodeList();
        String trainNumber = trainPersonDTO.getTrainNumber();
        String trainCenterId = trainPersonDTO.getTrainCenterId();
        String trainId = trainPersonDTO.getTrainId();
        LambdaQueryWrapperX<TrainPerson> wrapperX = new LambdaQueryWrapperX<>(TrainPerson.class);
        wrapperX.in(TrainPerson::getUserCode,codeList);
        wrapperX.eq(TrainPerson::getTrainNumber,trainNumber);
        wrapperX.eq(TrainPerson::getTrainCenterId,trainCenterId);
        wrapperX.eq(TrainPerson::getTrainId,trainId);
        wrapperX.select(TrainPerson::getUserCode,TrainPerson::getId);
        List<TrainPerson> list = this.list(wrapperX);
        List<String> addCodeList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(list)){
            List<String> userCodeList = list.stream().map(TrainPerson::getUserCode).distinct().collect(Collectors.toList());

            List<String> removeIdList = new ArrayList<>();
            for (TrainPerson trainPerson : list) {
                if(!codeList.contains(trainPerson.getUserCode())){
                    removeIdList.add(trainPerson.getId());
                }
            }
            for (String s : codeList) {
                if(!userCodeList.contains(s)){
                    addCodeList.add(s);
                }
            }
            if(!CollectionUtils.isEmpty(removeIdList)){
                this.removeBatchByIds(removeIdList);
            }
        }else{
            addCodeList.addAll(codeList);
        }
        if(addCodeList.isEmpty()){
            return  Boolean.TRUE;
        }
        Map<String, BasicUser> mapByNumberList = basicUserService.getMapByNumberList(addCodeList);
        List<TrainPerson> trainPersonList = new ArrayList<>();
        for (String code : codeList) {
            BasicUser basicUser = mapByNumberList.get(code);
            if(null != basicUser){
                TrainPerson trainPerson = new TrainPerson();
                trainPerson.setTrainId(trainId);
                trainPerson.setTrainNumber(trainNumber);
                trainPerson.setTrainCenterId(trainCenterId);
                trainPerson.setId(null);

                trainPerson.setDeptName(basicUser.getDeptName());
                trainPerson.setDeptCode(basicUser.getDeptCode());
                trainPerson.setSex(basicUser.getSex());
                trainPerson.setUserCode(code);
                trainPerson.setInstituteName(basicUser.getInstituteName());
                trainPerson.setInstituteCode(basicUser.getInstituteCode());

                trainPerson.setCompanyName(basicUser.getInstituteName());
                trainPerson.setCompanyCode(basicUser.getInstituteCode());

                trainPerson.setNowPosition(basicUser.getNowPosition());
                trainPerson.setFullName(basicUser.getFullName());
                trainPersonList.add(trainPerson);
            }
        }
        return this.saveBatch(trainPersonList);
    }

    @Override
    public List<PersonTrainVO> trainCurrentPersonList(EquivalentParamDTO equivalentParamDTO) {
        UserVO user = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        if(null ==user){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "当前用户不存在，请重新登录");
        }
        String code = user.getCode();
        return  this.getPersonTrainList(code,equivalentParamDTO.getBasePlaceCode());
    }

    @Override
    public List<PersonTrainVO> getPersonTrainList(String userCode, String baseCode){
        if(StrUtil.isEmpty(userCode)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数错误，请刷新");
        }

        List<PersonTrainInfoRecord> personTrainInfos= personTrainInfoRecordService.listByUserCodeAndBaseCode(userCode,baseCode, null);
        List<PersonTrainVO> personTrainEffectVOList = new ArrayList<>();
        for (PersonTrainInfoRecord personTrainInfo : personTrainInfos) {
            PersonTrainVO personTrainEffectVO = new PersonTrainVO();
            personTrainEffectVO.setId(personTrainInfo.getId());
            personTrainEffectVO.setTrainName(personTrainInfo.getTrainName());
            personTrainEffectVO.setTrainNumber(personTrainInfo.getTrainNumber());
            personTrainEffectVO.setTrainCenterId(personTrainInfo.getId());
            personTrainEffectVO.setBaseName(personTrainInfo.getBaseName());
            personTrainEffectVO.setEndDate(personTrainInfo.getEndDate());
            personTrainEffectVO.setExpireTime(personTrainInfo.getExpireTime());
            personTrainEffectVO.setLessonHour(personTrainInfo.getLessonHour());
            personTrainEffectVO.setBaseCode(personTrainInfo.getBaseCode());
            personTrainEffectVOList.add(personTrainEffectVO);
        }
        return  personTrainEffectVOList;
    }

    public List<PersonTrainVO> getTranListByUserCode(String keyWord,String code){
        LambdaQueryWrapperX<TrainPerson> wrapperX = new LambdaQueryWrapperX<>(TrainPerson.class);
        wrapperX.eq(TrainPerson::getUserCode,code);
        wrapperX.leftJoin(TrainManage.class,TrainManage::getTrainNumber,TrainPerson::getTrainNumber);
        wrapperX.leftJoin(TrainCenter.class,TrainCenter::getId,TrainPerson::getTrainCenterId);
        if(StringUtils.hasText(keyWord)){
            wrapperX.like(TrainManage::getName,keyWord);
        }
        wrapperX.isNotNull(TrainCenter::getEndDate);
        // 叫 人员必须控制在 以及完结的培训中，并且在有效期（处于中心表（含有中心表和非中心的表）的有效期），并且所属于该中心的参培人员
//        wrapperX.gt(TrainCenter::getExpireTime,new Date());
        List<TrainPerson> content = this.list(wrapperX);
        if (CollectionUtils.isEmpty(content)) {
            return new ArrayList<>();
        }
        List<String> centerIdList = new ArrayList<>();
        Map<String, String> trainNumberToCenterIdMap = new HashMap<>();
        List<String> trainerList = new ArrayList<>();
        List<String> keyList = new ArrayList<>();
        content.forEach(trainPerson -> {
            String trainCenterId = trainPerson.getTrainCenterId();
            if (StringUtils.hasText(trainCenterId)) {
                centerIdList.add(trainCenterId);
            }
            trainNumberToCenterIdMap.put(trainPerson.getTrainNumber(), trainPerson.getTrainCenterId());
            trainerList.add(trainPerson.getTrainId());
        });

        List<TrainManage> trainManages = trainManageService.listByIds(trainerList);
        Map<String, TrainManage> idToEntity = trainManages.stream().collect(Collectors.toMap(TrainManage::getTrainNumber, Function.identity(), (k1, k2) -> k2));
        List<PersonTrainVO> personTrainVOList = new ArrayList<>();
        for (TrainPerson trainPerson : content) {
            PersonTrainVO personTrainVO = new PersonTrainVO();
            TrainManage trainManage = idToEntity.get(trainPerson.getTrainNumber());
            personTrainVO.setTrainNumber(trainPerson.getTrainNumber());
            personTrainVO.setTrainName(trainManage.getName());
            personTrainVO.setExpireTime(trainManage.getEndDate());
            personTrainVO.setEndDate(trainManage.getEndDate());
            personTrainVO.setLessonHour(trainManage.getLessonHour());
            personTrainVOList.add(personTrainVO);
        }
        return personTrainVOList;
    }

    @Override
    public Boolean scoreOK(List<String> idList) {
        List<TrainPerson> trainPersonList = this.listByIds(idList);
        if(CollectionUtils.isEmpty(trainPersonList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除");
        }
        LogRecordContext.putVariable("trainNumber", trainPersonList.get(0).getTrainNumber());
        LogRecordContext.putVariable("numbers",trainPersonList.stream().map(TrainPerson::getUserCode).collect(Collectors.joining(",")));
        LambdaUpdateWrapper<TrainPerson> personLambdaUpdateWrapper = new LambdaUpdateWrapper<>(TrainPerson.class);
        personLambdaUpdateWrapper.in(LyraEntity::getId,idList);
        personLambdaUpdateWrapper.set(TrainPerson::getIsOK,Boolean.TRUE);
        return  this.update(personLambdaUpdateWrapper);
    }

    @Override
    public Boolean scoreNotOK(List<String> idList) {
        List<TrainPerson> trainPersonList = this.listByIds(idList);
        if(CollectionUtils.isEmpty(trainPersonList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除");
        }
        LogRecordContext.putVariable("trainNumber", trainPersonList.get(0).getTrainNumber());
        LogRecordContext.putVariable("numbers",trainPersonList.stream().map(TrainPerson::getUserCode).collect(Collectors.joining(",")));

        LambdaUpdateWrapper<TrainPerson> personLambdaUpdateWrapper = new LambdaUpdateWrapper<>(TrainPerson.class);
        personLambdaUpdateWrapper.in(LyraEntity::getId,idList);
        personLambdaUpdateWrapper.set(TrainPerson::getIsOK,Boolean.FALSE);
        return  this.update(personLambdaUpdateWrapper);
    }

    @Override
    public Boolean settingScore(SettingTrainPersonScoreDTO scoreDTO) {
        String id = scoreDTO.getId();
        BigDecimal score = scoreDTO.getScore();
        if(StrUtil.isEmpty(id)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "请选择数据");
        }
        if(Objects.isNull(score)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "未填写分值");
        }
        TrainPerson trainPerson =   this.getById(id);
        if(Objects.isNull(trainPerson)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除");
        }
        LogRecordContext.putVariable("number", trainPerson.getUserCode());
        LogRecordContext.putVariable("score", score);
        LambdaUpdateWrapper<TrainPerson> personLambdaUpdateWrapper = new LambdaUpdateWrapper<>(TrainPerson.class);
        personLambdaUpdateWrapper.eq(LyraEntity::getId,id);
        personLambdaUpdateWrapper.set(TrainPerson::getScore,score);
        return  this.update(personLambdaUpdateWrapper);
    }

    @Override
    public List<String> userCodeByCenterId(String centerId,Boolean isCheck) {
        LambdaQueryWrapperX<TrainPerson> wrapperX = new LambdaQueryWrapperX<>(TrainPerson.class);
        wrapperX.eq(TrainPerson::getTrainCenterId,centerId);
        //如果该次培训需要考核的，就只筛选合格的
        if(isCheck){
            wrapperX.eq(TrainPerson::getIsOK,Boolean.TRUE);
        }
        wrapperX.select(TrainPerson::getUserCode);
        List<TrainPerson> personList = this.list(wrapperX);
        if(CollectionUtils.isEmpty(personList)){
            return new ArrayList<>();
        }
        return personList.stream().map(TrainPerson::getUserCode).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, Long> numMapToCenterIdList(List<String> idList) {
        LambdaQueryWrapperX<TrainPerson> wrapperX = new LambdaQueryWrapperX<>(TrainPerson.class);
        wrapperX.in(TrainPerson::getTrainCenterId,idList);
        wrapperX.select(TrainPerson::getTrainCenterId);
        List<TrainPerson> list= this.list();
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        return  list.stream().collect(Collectors.groupingBy(TrainPerson::getTrainCenterId, Collectors.counting()));
    }

    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response,String centerId) throws Exception {
        LambdaQueryWrapperX<TrainPerson> condition = new LambdaQueryWrapperX<>( TrainPerson. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.eq(TrainPerson::getTrainCenterId,centerId);
        condition.orderByDesc(TrainManage::getCreateTime);
        List<TrainPerson> trainManagees =   this.list(condition);

        String fileName = "参培人员数据导出.xlsx";
        if(CollectionUtils.isEmpty(trainManagees)){
            ExcelUtils.write(response, fileName, "sheet1", TrainPersonExportVO.class,new ArrayList<>() );
            return;
        }
        List<TrainPersonVO> trainPersonVOS = BeanCopyUtils.convertListTo(trainManagees, TrainPersonVO::new);
        this.packageTrainVO(centerId,trainPersonVOS,trainManagees.stream().map(TrainPerson::getUserCode).distinct().collect(Collectors.toList()));

        List<TrainPersonExportVO> trainPersonExportVOS = new ArrayList<>();
        for (TrainPersonVO trainPersonVO : trainPersonVOS) {
            TrainPersonExportVO trainPersonExportVO = new TrainPersonExportVO();
            BeanCopyUtils.copyProperties(trainPersonVO,trainPersonExportVO);
            if(!Objects.isNull(trainPersonVO.getIsOK())){
                if(trainPersonVO.getIsOK()){
                    trainPersonExportVO.setIsOK("合格");
                }else{
                    trainPersonExportVO.setIsOK("不合格");
                }
            }
            trainPersonExportVOS.add(trainPersonExportVO);
        }
        ExcelUtils.write(response, fileName, "sheet1", TrainPersonExportVO.class,trainPersonExportVOS );
    }


}
