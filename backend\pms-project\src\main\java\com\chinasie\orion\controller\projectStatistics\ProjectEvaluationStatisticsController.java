package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.projectStatistics.ProjectEvaluationStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/projectEvaluationStatistics")
@Api(tags = "项目评价统计")
public class ProjectEvaluationStatisticsController {

    @Autowired
    private ProjectEvaluationStatisticsService projectEvaluationStatisticsService;

    /**
     * 获取里程碑达成得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取里程碑达成得分")
    @RequestMapping(value = "/milestoneScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】里程碑达成得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Float> getMilestoneScore(@RequestParam("projectId") String projectId) throws Exception {
        Float rsp = projectEvaluationStatisticsService.getMilestoneScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取计划碑达成得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取计划碑达成得分")
    @RequestMapping(value = "/planScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】计划达成得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Float> getPlanScore(@RequestParam("projectId") String projectId) throws Exception {
        Float rsp = projectEvaluationStatisticsService.getPlanScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取预算使用得分")
    @RequestMapping(value = "/budgetScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】预算使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Float> getBudgetScore(@RequestParam("projectId") String projectId) throws Exception {
        Float rsp = projectEvaluationStatisticsService.getBudgetScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取研发资源使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取研发资源使用得分")
    @RequestMapping(value = "/workHoursScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】研发资源使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Integer> getWorkHoursScore(@RequestParam("projectId") String projectId) throws Exception {
        Integer rsp = projectEvaluationStatisticsService.getWorkHoursScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取材料成本预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取材料成本预算使用得分")
    @RequestMapping(value = "/materialFeeScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】材料成本预算使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Integer> getMaterialFeeScore(@RequestParam("projectId") String projectId) throws Exception {
        Integer rsp = projectEvaluationStatisticsService.getMaterialFeeScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取专用费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取专用费预算使用得分")
    @RequestMapping(value = "/dedicatedFeeScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】专用费预算使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Integer> getDedicatedFeeScore(@RequestParam("projectId") String projectId) throws Exception {
        Integer rsp = projectEvaluationStatisticsService.getDedicatedFeeScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取外协费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取外协费预算使用得分")
    @RequestMapping(value = "/outSourceFeeScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】外协费预算使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Integer> getOutSourceFeeScore(@RequestParam("projectId") String projectId) throws Exception {
        Integer rsp = projectEvaluationStatisticsService.getOutSourceFeeScore(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取事务费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取事务费预算使用得分")
    @RequestMapping(value = "/affairsFeeScore", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目评价统计】事务费预算使用得分", type = "项目评价", bizNo = "#{{projectId}}")
    public ResponseDTO<Integer> getAffairsFeeScore(@RequestParam("projectId") String projectId) throws Exception {
        Integer rsp = projectEvaluationStatisticsService.getAffairsFeeScore(projectId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "问题管理->产品研发问题->样机试验")
    @RequestMapping(value = "/getQuestionForValidate/{projectId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题管理统计】问题管理->产品研发问题->样机试验", type = "项目评价模型", bizNo = "#{{projectId}}")
    public ResponseDTO getQuestionForValidate(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO(projectEvaluationStatisticsService.getQuestionForValidate(projectId));
    }

    @ApiOperation(value = "问题管理->产品研发问题->验证测试")
    @RequestMapping(value = "/getQuestionForTest/{projectId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题管理统计】问题管理->产品研发问题->验证测试", type = "项目评价模型", bizNo = "#{{projectId}}")
    public ResponseDTO getQuestionForTest(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO(projectEvaluationStatisticsService.getQuestionForTest(projectId));
    }

    @ApiOperation(value = "问题管理->生态库问题数量分数")
    @RequestMapping(value = "/getQuestionAllIsEcologicalIssues/{projectId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【问题管理统计】生态库问题数量分数", type = "项目评价模型", bizNo = "#{{projectId}}")
    public ResponseDTO getQuestionAllIsEcologicalIssues(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO(projectEvaluationStatisticsService.getQuestionAllIsEcologicalIssues(projectId));
    }

}
