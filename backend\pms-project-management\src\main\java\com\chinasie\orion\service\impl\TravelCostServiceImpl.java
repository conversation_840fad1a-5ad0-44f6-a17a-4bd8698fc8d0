package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.TravelCostDTO;
import com.chinasie.orion.domain.entity.TravelCost;
import com.chinasie.orion.domain.vo.TravelCostVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TravelCostMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.TravelCostService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * TravelCost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:45:17
 */
@Service
@Slf4j
public class TravelCostServiceImpl extends  OrionBaseServiceImpl<TravelCostMapper, TravelCost>   implements TravelCostService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TravelCostVO detail(String id, String pageCode) throws Exception {
        TravelCost travelCost =this.getById(id);
        TravelCostVO result = BeanCopyUtils.convertTo(travelCost,TravelCostVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param travelCostDTO
     */
    @Override
    public  String create(TravelCostDTO travelCostDTO) throws Exception {
        TravelCost travelCost =BeanCopyUtils.convertTo(travelCostDTO,TravelCost::new);
        this.save(travelCost);

        String rsp=travelCost.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param travelCostDTO
     */
    @Override
    public Boolean edit(TravelCostDTO travelCostDTO) throws Exception {
        TravelCost travelCost =BeanCopyUtils.convertTo(travelCostDTO,TravelCost::new);

        this.updateById(travelCost);

        String rsp=travelCost.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TravelCostVO> pages( Page<TravelCostDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TravelCost> condition = new LambdaQueryWrapperX<>( TravelCost. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TravelCost::getCreateTime);


        Page<TravelCost> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TravelCost::new));

        PageResult<TravelCost> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TravelCostVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TravelCostVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TravelCostVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "差旅费用表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TravelCostDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        TravelCostExcelListener excelReadListener = new TravelCostExcelListener();
        EasyExcel.read(inputStream,TravelCostDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<TravelCostDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("差旅费用表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<TravelCost> travelCostes =BeanCopyUtils.convertListTo(dtoS,TravelCost::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::TravelCost-import::id", importId, travelCostes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<TravelCost> travelCostes = (List<TravelCost>) orionJ2CacheService.get("pmsx::TravelCost-import::id", importId);
        log.info("差旅费用表导入的入库数据={}", JSONUtil.toJsonStr(travelCostes));

        this.saveBatch(travelCostes);
        orionJ2CacheService.delete("pmsx::TravelCost-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::TravelCost-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<TravelCost> condition = new LambdaQueryWrapperX<>( TravelCost. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(TravelCost::getCreateTime);
        List<TravelCost> travelCostes =   this.list(condition);

        List<TravelCostDTO> dtos = BeanCopyUtils.convertListTo(travelCostes, TravelCostDTO::new);

        String fileName = "差旅费用表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TravelCostDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<TravelCostVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class TravelCostExcelListener extends AnalysisEventListener<TravelCostDTO> {

        private final List<TravelCostDTO> data = new ArrayList<>();

        @Override
        public void invoke(TravelCostDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<TravelCostDTO> getData() {
            return data;
        }
    }


}
