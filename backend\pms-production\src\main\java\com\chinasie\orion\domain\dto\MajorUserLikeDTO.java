package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MajorUserLike DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
@ApiModel(value = "MajorUserLikeDTO对象", description = "用户关注的大修")
@Data
@ExcelIgnoreUnannotated
public class MajorUserLikeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @ExcelProperty(value = "用户ID ", index = 0)
    private String userId;

    /**repairRound
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String repairRound;




}
