package com.chinasie.orion.domain.entity.projectStatistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * DemandStatusStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2023-12-21 13:26:54
 */
@TableName(value = "pmsx_demand_status_statistics")
@ApiModel(value = "DemandStatusStatisticsEntity对象", description = "需求状态趋势统计表")
@Data
public class DemandStatusStatistics  implements Serializable{

    /**
     * 统计ID
     */
    @ApiModelProperty(value = "统计ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    @TableField(value = "date_str")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    @TableField(value = "uk")
    private String uk;

    /**
     * 状态id
     */
    @ApiModelProperty(value = "状态id")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    @TableField(value = "no_start_count")
    private Integer noStartCount;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    @TableField(value = "underway_count")
    private Integer underwayCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    @TableField(value = "complete_count")
    private Integer completeCount;

}
