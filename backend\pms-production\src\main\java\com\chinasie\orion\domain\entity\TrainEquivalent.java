package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * TrainEquivalent Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:24
 */
@TableName(value = "pmsx_train_equivalent")
@ApiModel(value = "TrainEquivalentEntity对象", description = "培训等效")
@Data

public class TrainEquivalent extends  ObjectEntity  implements Serializable{

    /**
     * 等效基地编号
     */
    @ApiModelProperty(value = "等效申请基地编号")
    @TableField(value = "equivalent_base_code")
    private String equivalentBaseCode;

    /**
     * 等效基地名称
     */
    @ApiModelProperty(value = "等效申请基地名称")
    @TableField(value = "equivalent_base_name")
    private String equivalentBaseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    @TableField(value = "equivalent_date")
    private Date equivalentDate;

    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    @TableField(value = "user_code")
    private String userCode;
    @ApiModelProperty(value = "培训编号")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @TableField(value = "is_equivalent")
    private Boolean isEquivalent;

    @ApiModelProperty(value = "人员编号和培训编号")
    @TableField(value = "uk_key")
    private String  ukKey;

    @ApiModelProperty(value = "参培中心关联表Id/唯一ID")
    @TableField(value = "train_center_id")
    private String trainCenterId;

    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    @TableField(value = "base_name")
    private String baseName;
}
