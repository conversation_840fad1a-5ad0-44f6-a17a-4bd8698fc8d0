<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { inject, ref, Ref } from 'vue';
import dayjs from 'dayjs';
import { formatMoney } from '/@/views/pms/utils/utils';

const detailData: Ref<Record<string, Record<string, any>>> = inject('detailData');
const basicInfo: Ref = ref([
  {
    label: '订单编号',
    field: 'number',
  },
  {
    label: '订单名称',
    field: 'name',
  },
  {
    label: '采购类型',
    field: 'purchaseTypeName',
  },
  {
    label: '采购员',
    field: 'resUserName',
  },
  {
    label: '采购员工号',
    field: 'resUserCode',
  },
  {
    label: '采购负责部门',
    field: 'rspDeptName',
  },
  {
    label: '含税总金额',
    field: 'haveTaxTotalAmt',
    isMoney: true,
    unit: '元',
  },
  {
    label: '币种',
    field: 'currency',
  },
  {
    label: '采购总数量',
    field: 'purchaseTotalAmount',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建人工号',
    field: 'creatorCode',
  },
  {
    label: '创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '交货日期',
    field: 'orderArrivalDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '订单说明',
    field: 'orderDesc',
    wrap: true,
    gridColumn: '2/3',
  },
]);

const supplyInfo: Ref = ref([
  {
    label: '供应商名称',
    field: 'supplierName',
  },
  {
    label: '收货人姓名',
    field: 'receivePerson',
    gridColumn: '2/4',
  },
  {
    label: '供应商联系人',
    field: 'contactPerson',
  },
  {
    label: '收货人电话',
    field: 'receivePhone',
    gridColumn: '2/4',
  },
  {
    label: '联系人电话',
    field: 'contactPhone',
  },
  {
    label: '收货地址',
    field: 'receiveAddress',
    gridColumn: '2/4',
  },
  {
    label: '联系人邮箱',
    field: 'contactEmail',
  },
  {
    label: '收货人邮箱',
    field: 'receiveEmail',
    gridColumn: '2/4',
  },
]);

const title = detailData.value?.projectPurchaseOrderInfoVO?.purchaseTypeName;
const tableRef: Ref = ref();
const columns: Ref<any[]> = ref([
  {
    title: `${title}计划编号`,
    dataIndex: 'planNumber',
  },
  {
    title: `${title}编码`,
    dataIndex: 'goodsServiceNumber',
  },
  {
    title: `${title}描述`,
    dataIndex: 'description',
  },
  {
    title: title === '物资' ? '规格型号' : '服务期限',
    dataIndex: 'normsModel',
  },
  {
    title: '计量单位',
    dataIndex: 'unitCodeName',
  },
  {
    title: '采购数量',
    dataIndex: 'purchaseAmount',
  },
  {
    title: '需求日期',
    dataIndex: 'demandDate',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '单价（不含税）',
    dataIndex: 'noTaxPrice',
    customRender({ text }) {
      return formatMoney(text);
    },
  },
  {
    title: '总金额（不含税）',
    dataIndex: 'noTaxTotalAmt',
    customRender({ text }) {
      return formatMoney(text);
    },
  },
  {
    title: '税率',
    dataIndex: 'taxRate',
    customRender({ text }) {
      return `${text || 0}%`;
    },
  },
  {
    title: '单价（含税）',
    dataIndex: 'haveTaxPrice',
    customRender({ text }) {
      return formatMoney(text);
    },
  },
  {
    title: '总金额（含税）',
    dataIndex: 'haveTaxTotalAmt',
    customRender({ text }) {
      return formatMoney(text);
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
]);
const tableOptions = {
  showSmallSearch: false,
  showToolButton: false,
  dataSource: detailData.value?.projectPurchaseOrderDetailVOList ?? [],
  columns,
};
</script>

<template>
  <div>
    <DetailsLayout
      title="采购订单基本信息"
      :column="3"
      :list="basicInfo"
      :data-source="detailData?.projectPurchaseOrderInfoVO??{}"
    />
    <DetailsLayout
      title="采购订单供收信息"
      :column="3"
      :list="supplyInfo"
      :data-source="{
        ...(detailData?.projectPurchaseReceiveInfoVO??{}),
        ...(detailData?.projectPurchaseSupplierInfoVO??{})
      }"
    />
    <DetailsLayout title="采购订单明细">
      <div style="height: 300px;overflow: hidden">
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        />
      </div>
    </DetailsLayout>
  </div>
</template>

<style scoped lang="less">

</style>
