package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * InvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@TableName(value = "pms_investment_scheme")
@ApiModel(value = "InvestmentScheme对象", description = "投资计划")
@Data
public class InvestmentScheme extends ObjectEntity implements Serializable {

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    @TableField(value = "name")
    private String name;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "number")
    private String number;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;


    /**
     * 是否关闭
     */
    @ApiModelProperty(value = "是否关闭")
    @TableField(value = "close_flag")
    private Boolean closeFlag=false;
}
