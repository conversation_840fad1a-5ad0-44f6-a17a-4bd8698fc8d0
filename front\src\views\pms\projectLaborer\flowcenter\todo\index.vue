<template>
  <Layout
    :options="{ body: { scroll: true } }"
    content-title="待办流程"
  >
    <orion-table
      ref="instanceTableRef"
      :options="options"
      :filter-schemas="filterSchemas"
    >
      <template #name="{ record }">
        <a @click="goDetail(record)"> {{ record.procInstName }}</a>
      </template>
      <template #action="{ record }">
        <a @click="goDetailTodo(record)">办理</a>
      </template>
    </orion-table>
  </Layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref,
} from 'vue';
import { Input, Button } from 'ant-design-vue';
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';
import Api from '/@/api/index';
import { workflowApi } from '../util/apiConfig';
export default defineComponent({
  name: 'Todo',
  components: {
    Layout,
    OrionTable,
    Input,
    AButton: Button,
  },
  setup() {
    const userStore: any = useUserStore();
    const instanceTableRef = ref<Nullable<any>>(null); // table的ref
    const router = useRouter();
    const state = reactive({
      options: {
        rowSelection: {},
        deleteToolButton: 'add|delete|enable|disable',
        smallSearchField: ['proc_inst_name'],
        auto: {
          url: `${workflowApi}/act-task/prearrange`,
          params: {
            query: {
              assigneeIds: [userStore.getUserInfo.id],
              userId: userStore.getUserInfo.id,
              search: '',
              suspended: false,
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [],
          },
        },
        columns: [
          {
            title: '流程编号',
            dataIndex: 'businessKey',
          },
          {
            title: '实例标题',
            dataIndex: 'processInstanceName',
            slots: { customRender: 'name' },
          },
          {
            title: '流程名称',
            dataIndex: 'procInstName',
          },
          {
            title: '发起人',
            dataIndex: 'creatorName',
          },
          {
            title: '当前节点',
            dataIndex: 'currentTask',
          },
          {
            title: '发起时间',
            dataIndex: 'createTime',
          },
          {
            title: '操作',
            dataIndex: 'action',
            slots: { customRender: 'action' },
          },
        ],
      },
      filterSchemas: [
        {
          field: 'creator_id',
          component: 'ApiSelect',
          label: '发布人',
          colProps: {
            span: 8,
          },
          componentProps: {
            mode: 'multiple',
            api: () => new Api(workflowApi).fetch({}, 'act-system/publisher/page', 'POST'),
            labelField: 'name',
            valueField: 'id',
          },
        },
        {
          field: 'create_time',
          component: 'RangePicker',
          label: '发布时间',
          colProps: {
            span: 12,
          },
          componentProps: {},
        },
      ],
    });

    function goDetail(row) {
      router.push({
        path: '/flowcenter/detail',
        query: {
          id: row.id,
          taskId: row.currentTaskId,
          processInstanceId: row.procInstId,
          isTodo: '1',
          taskDefinitionKey: row.currentTaskDefinitionKey,
          processDefinitionId: row.procDefId,
        },
      });
    }
    function goDetailTodo(row) {
      router.push({
        path: '/flowcenter/detail',
        query: {
          id: row.id,
          taskId: row.currentTaskId,
          processInstanceId: row.procInstId,
          from: 'todo',
          taskDefinitionKey: row.currentTaskDefinitionKey,
          processDefinitionId: row.procDefId,
        },
      });
    }

    return {
      ...toRefs(state),
      instanceTableRef,
      goDetail,
      goDetailTodo,
    };
  },
});
</script>
<style scoped lang="less">
.btns-header {
  display: flex;
  flex-direction: row;
}
</style>
