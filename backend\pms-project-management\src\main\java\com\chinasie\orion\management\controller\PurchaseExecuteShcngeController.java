package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.PurchaseExecuteShcngeDTO;
import com.chinasie.orion.management.domain.vo.PurchaseExecuteShcngeVO;
import com.chinasie.orion.management.service.PurchaseExecuteShcngeService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * PurchaseExecuteShcnge 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/purchaseExecuteShcnge")
@Api(tags = "采购执行变更")
public class PurchaseExecuteShcngeController {

    @Autowired
    private PurchaseExecuteShcngeService purchaseExecuteShcngeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购执行变更", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PurchaseExecuteShcngeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        PurchaseExecuteShcngeVO rsp = purchaseExecuteShcngeService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param purchaseExecuteShcngeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#purchaseExecuteShcngeDTO.name}}】", type = "采购执行变更", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception {
        String rsp = purchaseExecuteShcngeService.create(purchaseExecuteShcngeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param purchaseExecuteShcngeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#purchaseExecuteShcngeDTO.name}}】", type = "采购执行变更", subType = "编辑", bizNo = "{{#purchaseExecuteShcngeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception {
        Boolean rsp = purchaseExecuteShcngeService.edit(purchaseExecuteShcngeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购执行变更", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = purchaseExecuteShcngeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购执行变更", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = purchaseExecuteShcngeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购执行变更", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<PurchaseExecuteShcngeVO>> pages(@PathVariable("mainTableId") String mainTableId, @RequestBody Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception {
        Page<PurchaseExecuteShcngeVO> rsp = purchaseExecuteShcngeService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号查询采购执行变更信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号查询采购执行变更信息")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号查询采购执行变更信息", type = "ContractLineInfo", subType = "根据合同编号查询采购执行变更信息", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<PurchaseExecuteShcngeVO>> getByCode(@RequestBody Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception {
        Page<PurchaseExecuteShcngeVO> rsp = purchaseExecuteShcngeService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购执行变更导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购执行变更", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        purchaseExecuteShcngeService.downloadExcelTpl(response);
    }

    @ApiOperation("采购执行变更导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购执行变更", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = purchaseExecuteShcngeService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购执行变更导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购执行变更", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = purchaseExecuteShcngeService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购执行变更导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购执行变更", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = purchaseExecuteShcngeService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购执行变更导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购执行变更", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        purchaseExecuteShcngeService.exportByExcel(searchConditions, response);
    }
}
