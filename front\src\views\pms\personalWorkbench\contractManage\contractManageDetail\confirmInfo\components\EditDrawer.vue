<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <SpinMain :loading="state.loading" />
    <EditDrawerMain
      v-if="state.visibleStatus"
      ref="formRef"
      :record="state.record"
      @loading-change="loadingChange"
    />
    <template #footer>
      <div
        v-if="state.visibleStatus"
        class="flex flex-pe"
      >
        <BasicButton @click="closeDrawer()">
          取消
        </BasicButton>
        <BasicButton
          v-if="state.operationType!=='submit'"
          :loading="state.btnLoading"
          @click="okHandle('0')"
        >
          保存
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="state.btnLoading"
          @click="okHandle('1')"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useD<PERSON><PERSON><PERSON><PERSON>, BasicButton } from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref,
} from 'vue';
// import {
//   postNotarizeAddBatch, getOrderNodeNotarize, postNotarizeEditBatch, putAgainCommit,
// } from '/@/views/pms/api';
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import EditDrawerMain from './EditDrawerMain.vue';
import {
  contractDetailKey,
} from '../../types';
import SpinMain from '/@/views/pms/projectLaborer/components/SpanMain/SpinMain.vue';
import {
  getContractPayNodeConfirm,
  postContractPayNodeConfirm,
  postContractPayNodeConfirmSubmit, putContractPayNodeConfirm,
} from '/@/views/pms/projectLaborer/projectLab/api';

const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  state.currentId = openProps?.id;
  state.operationType = openProps?.operationType;
  state.record.orderAndNodeParamDTOList = openProps?.orderAndNodeParamDTOList;
  setDrawerProps({
    title: openProps?.operationType === 'submit' ? '重新提交' : '支付节点确认',
  });
  await getDetail(openProps?.id);
  state.visibleStatus = true;
});
const route = useRoute();
const formRef: Ref = ref();
const updateTabs: (option: { id: string }) => void = inject('updateTabs');
const updateNodePages: ()=>void = inject('updateNodePages');
const state = reactive({
  contractDetail: inject(contractDetailKey),
  visibleStatus: false,
  record: {
    id: undefined,
    auditNumber: undefined,
    orderAndNodeParamDTOList: [],
  },
  loading: false,
  btnLoading: false,
  operationType: 'add',
  currentId: '',
  tabsId: inject('tabsId'),
  projectNumber: inject('projectNumber'),
});

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

// 加载状态改变
function loadingChange(loading:boolean) {
  state.loading = loading;
}

// 获取订单确认详情
async function getDetail(id) {
  state.loading = true;
  if (id && state.operationType !== 'add') {
    state.record = await getContractPayNodeConfirm(id);
  }
}

async function okHandle(type) {
  await formRef.value.formMethods.validate();
  state.btnLoading = true;
  const {
    orderList, user, isFinish, deleteFileIdList, fileDTOList, notarizeDescription, orderAndNodeParamDTOList, nodeSortDTOList,
  } = formRef.value.state;
  const params = {
    type,
    orderAndNodeParamDTOList: orderList?.map((item, index) => ({
      nodeSortDTOList: item.nodeSortDTOList?.map((v, i) => ({
        nodeId: v,
        nodeSort: (i + 1)?.toString(),
        serialNumber: item.nodeOptions.filter((c) => c.value === v)[0]?.serialNumber,
      })),
      orderNumber: item?.orderNumber,
      orderSort: (index + 1)?.toString(),
    })),
    auditUserId: user?.materialsAuditorId,
    materialsAuditorName: user?.materialsAuditorName,
    serviceComplete: isFinish,
    deleteFileIdList,
    fileInfoDTOList: fileDTOList,
    confirmDesc: notarizeDescription,
    contractId: route?.query?.id,
    id: state.currentId,
    projectNumber: state?.projectNumber,
    auditNumber: state.record?.auditNumber,
    contractNumber: state.contractDetail?.number,
    nodeIdList: nodeSortDTOList,
  };

  // try {
  //   if (state.operationType === 'add') {
  //     if (type === '0') {
  //       await postContractPayNodeConfirm(params);
  //       message.success('保存成功，可在节点确认记录中继续编辑');
  //     } else {
  //       await postContractPayNodeConfirmSubmit(params);
  //       message.success('支付节点确认材料提交成功');
  //     }
  //   } else {
  //     await putContractPayNodeConfirm(params);
  //   }
  try {
    await (state.operationType === 'edit' ? postContractPayNodeConfirmSubmit : state.operationType === 'submit' ? postContractPayNodeConfirmSubmit : putContractPayNodeConfirm)(params);

    if (state.tabsId === 4) {
      updateNodePages();
    } else {
      updateTabs({ id: 4 });
    }
    if (type === '0') {
      message.success('保存成功，可在节点确认记录中继续编辑');
    } else {
      message.success('支付节点确认材料提交成功');
    }
  } finally {
    state.btnLoading = false;
    closeDrawer();
  }
}

</script>

<style scoped>

</style>
