package com.chinasie.orion.management.service;


import com.chinasie.orion.management.domain.dto.ProcurementDashboardDTO;
import com.chinasie.orion.management.domain.entity.ProcurementBarDashboard;
import com.chinasie.orion.management.domain.entity.ProcurementColumnDashboard;
import com.chinasie.orion.management.domain.entity.ProcurementDashboard;
import com.chinasie.orion.management.domain.vo.ProcurementBarDashboardVO;
import com.chinasie.orion.management.domain.vo.ProcurementDashboardVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * ProcurementDashboard 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
public interface ProcurementDashboardService extends OrionBaseService<ProcurementDashboard> {

    /**
     * 查询看板块图数据
     * <p>
     * * @param id
     */
    List<ProcurementDashboardVO> getLumpData(ProcurementDashboardDTO procurementDashboard);


    /**
     * 查询看板柱状图数据
     * <p>
     * * @param id
     */
    List<ProcurementBarDashboardVO> getColumnData(ProcurementDashboardDTO procurementDashboard);
}
