<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :isTable="isTable"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openDrawerCreate(true, { type: 'add' })"
        >
          创建项目
        </BasicButton>
        <RadioGroup
          v-model:value="tableType"
        >
          <RadioButton value="1">
            列表视图
          </RadioButton>
          <RadioButton value="2">
            卡片视图
          </RadioButton>
        </RadioGroup>
      </template>
      <template #actions="{record}">
        <ActionButtons
          :record="record"
          :actions="actions"
          :actionClick="actionClick"
        />
      </template>
      <template #otherContent>
        <div
          v-show="!isTable"
          class="card-grid"
        >
          <CardItem
            v-for="(item,index) in 10"
            :key="index"
            @click="rowClick"
          />
        </div>
      </template>
    </OrionTable>

    <!--创建项目-->
    <CreateProjectDrawer @register="registerCreate" />
  </Layout>
</template>

<script setup lang="ts">
import { h, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton,
  DataStatusTag, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { ActionButtons } from '/@/views/pms/planManage/components';
import dayjs from 'dayjs';
import { RadioButton, RadioGroup } from 'ant-design-vue';
import CreateProjectDrawer from './components/createProjectDrawer/CreateProjectDrawer.vue';
import CardItem from './components/CardItem.vue';
import { postPages } from '/@/views/pms/api';

const [registerCreate, { openDrawer: openDrawerCreate }] = useDrawer();
const router = useRouter();
const isTable = ref(true);
const tableType = ref('1');
watch(
  () => tableType.value,
  (newValue, oldValue) => {
    switch (newValue) {
      case '1':
        isTable.value = true;
        break;
      case '2':
        isTable.value = false;
        break;
    }
  },
);
const tableRef = ref(null);
const columns = [
  {
    title: '项目名称',
    dataIndex: 'name',
  },
  {
    title: '项目编码',
    dataIndex: 'number',
  },
  {
    title: '预算金额',
    dataIndex: 'budgetMoney',
  },
  {
    title: '项目状态',
    dataIndex: 'dataStatus',
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },
  {
    title: '项目类别',
    dataIndex: 'typeName',
  },
  {
    title: '预估金额',
    dataIndex: 'estimateMoney',
  },
  {
    title: '项目负责人',
    dataIndex: 'resPersonName',
  },
  {
    title: '项目责任部门',
    dataIndex: 'resOrgName',
  },
  {
    title: '项目预估开始时间',
    dataIndex: 'predictStartTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '项目预估结束时间',
    dataIndex: 'predictEndTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 120,
    slots: { customRender: 'actions' },
  },
];

const filterConfig = {
  fields: [
    {
      field: 'field1',
      fieldName: '输入框',
      component: 'Input',
      hidden: false,
    },
    {
      field: 'field2',
      fieldName: 'Select下拉框',
      component: 'Select',
      hidden: false,
    },
  ],
};
const baseTableOption = {
  rowSelection: {},
  columns,
  api: (params) => postPages(params),
  showToolButton: false,
  isFilter2: true,
  filterConfig,
  filter2Change(data) {
    // console.log('值有变化了', data);
  },
  // tool: [
  //   {
  //     type: 'button',
  //     position: 'before', // 展现位置 不配置为空默认在后面，配置为'before' 为前面
  //     buttonGroup: [
  //       [
  //         {
  //           icon: 'fa-plus',
  //           name: '创建项目',
  //           enable: true,
  //           componentProps: {
  //             type: 'primary',
  //           },
  //           cb: (e: any) => {
  //             openDrawerCreate(true, { type: 'add' });
  //           },
  //         },
  //       ],
  //     ],
  //   },
  //   {
  //     type: 'button',
  //     buttonGroup: [
  //       [
  //         {
  //           name: '卡片视图',
  //           enable: true,
  //           cb: (e: any) => {
  //             isTable.value = false;
  //           },
  //         },
  //         {
  //           enable: true,
  //           name: '列表视图',
  //           cb: (e: any) => {
  //             isTable.value = true;
  //           },
  //         },
  //       ],
  //     ],
  //   },
  // ],
};

const actions = [
  {
    label: '编辑',
    key: 'edit',
  },
  {
    label: '项目申报',
    key: 'declare',
  },
  {
    label: '删除',
    key: 'delete',
  },
  {
    label: 'ERP采购计划',
    key: 'erp',
  },
];

// 操作区点击事件
const actionClick = (key, record) => {
  // console.log(key, record);
  switch (key) {
    case 'edit':
      openDrawerCreate(true, {
        ...record,
        operationType: 'edit',
      });
      break;
    case 'delete':
      break;
    case 'declare':
      break;
    case 'erp':
      break;
  }
};

function rowClick() {
  router.push('/projectDetail');
}
</script>

<style scoped lang="less">
.card-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px 20px;

}
</style>
