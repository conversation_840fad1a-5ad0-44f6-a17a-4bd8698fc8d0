import { addRoleApi, edittRoleApi } from '/@/views/pms/projectLaborer/api/projectList';
export const formItemArr = [
  {
    type: 'input',
    label: '名称',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
      {
        min: 1,
        max: 25,
        message: '名称长度应在1~25位',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入名称',
    },
  },
  {
    type: 'textarea',
    label: '描述',
    field: 'remark',
    apiConfig: {
      placeholder: '请输入描述',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },
];
export const otherApi = {
  zkEdit: edittRoleApi,
  zkAdd: addRoleApi,
};
