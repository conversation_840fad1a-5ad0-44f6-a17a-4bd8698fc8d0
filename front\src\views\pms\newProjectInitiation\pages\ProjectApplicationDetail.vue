<script setup lang="ts">
import {
  Layout3, Layout3<PERSON>ontent, DataStatusTag, isPower,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, readonly, ref, Ref, unref,
} from 'vue';
import { Spin } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { filter as _filter } from 'lodash-es';
import ProjectInfo from './components/ProjectInfo.vue';
import { declarationData, declarationDataId, updateDeclarationData } from './keys';
import Api from '/@/api';
import AssociatedContent from './components/AssociatedContent.vue';

const route = useRoute();
// 立项数据id
const dataId: Ref<string> = ref(route.params.id as string);
provide(declarationDataId, readonly(dataId));
const projectData: Ref = ref({});
const powerData: Ref = ref();
provide(declarationData, projectData);
provide('powerData', powerData);
const defaultActionId: Ref<string> = ref('lXXI');
const loading: Ref<boolean> = ref(false);
// 详情顶部数据
const layoutData = computed(() => ({
  ownerName: projectData.value?.projectInitiation?.projectPerson,
  dataStatus: projectData.value?.projectInitiation?.status,
  projectCode: projectData.value?.projectInitiation?.projectNumber,
}));

interface MenuItem {
  id: string,
  name: string,
  children?: MenuItem[]
}

const menuData: Ref<any[]> = computed(() => _filter([
  {
    id: 'lXXI',
    name: '立项信息',
    code: 'PMS_ZGHXMLXXQ_container_TAB_01',
  },
  {
    id: 'gLNR',
    name: '关联内容',
    code: 'PMS_ZGHXMLXXQ_container_TAB_02',
  },
], (item) => isPower(item.code, powerData.value)));

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectInitiation').fetch('', unref(dataId), 'GET');
    projectData.value = result || {};
  } finally {
    loading.value = false;
  }
}
function getPowerDataHandle(data) {
  powerData.value = data;
}
provide(updateDeclarationData, getDetailData);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

onMounted(() => {
});
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'ZghProjectInitiationDetail001',getPowerDataHandle}"
    v-loading="loading"
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #code>
      <h2
        class="page-title"
        :title="projectData?.projectInitiation?.projectName"
      >
        {{ projectData?.projectInitiation?.projectName??'--' }}
      </h2>
      <div class="page-subtitle">
        <span>编号：{{ projectData?.projectInitiation?.projectNumber??"--" }}</span>
      </div>
    </template>
    <template #header-info>
      <div class="type-map">
        <h2 class="type-h2-enum">
          {{ projectData?.projectInitiation?.projectPerson }}
        </h2>
        <DataStatusTag
          v-if="projectData?.projectInitiation?.dataStatus"
          :status-data="projectData?.projectInitiation?.dataStatus"
        />
      </div>
    </template>
    <Layout3Content>
      <!--立项信息-->
      <ProjectInfo v-if="defaultActionId==='lXXI'" />
      <AssociatedContent v-if="defaultActionId==='gLNR'" />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  height: 100px !important;

  .project-title {
    width: 400px !important;
  }

  .layout-menu-warp {
    display: flex;
    align-items: center;

    .ant-menu {
      height: 60px !important;
    }
  }
}

.page-title {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 18px;
  color: #000000D9;
  margin-bottom: 5px;
}

.page-subtitle {
  display: flex;
  flex-wrap: wrap;
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;

  span {
    margin-right: 20px;
  }
}

.type-map {
  display: flex;
  align-items: center;
  .type-wrapper{
    display: flex;
    flex-direction: column;
  }

  .type-h2-enum {
    font-size: 14px;
    color: #000000D9;
    margin-bottom: 3px;
    margin-left: 12px;
    margin-right: 12px;
  }

  .type-p-enum {
    font-size: 12px;
    color: #000000D9;
    margin-bottom: 0;
  }
}
</style>
