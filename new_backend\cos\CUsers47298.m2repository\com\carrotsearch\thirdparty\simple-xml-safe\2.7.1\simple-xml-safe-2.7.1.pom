<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.carrotsearch.thirdparty</groupId>
  <artifactId>simple-xml-safe</artifactId>
  <version>2.7.1</version>
  <name>Simple XML (safe)</name>
  <description>A fork of <PERSON>'s simple-xml library with web-safe defaults</description>
  <url>https://github.com/dweiss/simplexml</url>
  <inceptionYear>2019</inceptionYear>
  <organization>
    <name>Carrot Search s.c.</name>
    <url>https://www.carrotsearch.com</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>niallg</id>
      <name>Niall Gallagher</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>dawid.weiss</id>
      <name>Dawid Weiss</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:dweiss/simplexml.git</connection>
    <developerConnection>scm:git:**************:dweiss/simplexml.git</developerConnection>
    <url>https://github.com/dweiss/simplexml</url>
  </scm>
</project>
