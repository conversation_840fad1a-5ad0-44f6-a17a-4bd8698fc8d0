package com.chinasie.orion.domain.vo.review;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewEssentials VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
@ApiModel(value = "ReviewEssentialsVO对象", description = "评审要点")
@Data
public class ReviewEssentialsVO extends ObjectVO implements Serializable {

    /**
     * 评审要点内容
     */
    @ApiModelProperty(value = "评审要点内容")
    private String content;


    /**
     * 评审阶段
     */
    @ApiModelProperty(value = "评审阶段")
    private String reviewPhase;
    @ApiModelProperty(value = "评审阶段名称")
    private String reviewPhaseName;


    /**
     * 要点类型
     */
    @ApiModelProperty(value = "要点类型")
    private String essentialsType;
    @ApiModelProperty(value = "要点类型名称")
    private String essentialsTypeName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
