package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.PersonDownDTO;
import com.chinasie.orion.domain.entity.RelationOrgToPerson;
import com.chinasie.orion.domain.vo.*;

import java.lang.String;
import java.util.List;
import java.util.Map;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * RelationOrgToPerson 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
public interface RelationOrgToPersonService  extends  OrionBaseService<RelationOrgToPerson>  {


        /**
         *  详情
         *
         * * @param id
         */
    RelationOrgToPersonVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param relationOrgToPersonDTO
         */
        String create(RelationOrgToPersonDTO relationOrgToPersonDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param relationOrgToPersonDTO
         */
        Boolean edit(RelationOrgToPersonDTO relationOrgToPersonDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<RelationOrgToPersonVO> pages( Page<RelationOrgToPersonDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<RelationOrgToPersonVO> vos)throws Exception;

    void initData();

    Map<String, RelationOrgToPerson> addBatchByCodeList(AddParamDTO addParamDTO);

    /**
     * 编辑准备
     * @param param
     * @return
     */
    PersonPrepareVO editPrepare(PersonPrepareDTO param);

    /**
     *  编辑实施
     * @param param
     * @return
     */
    PersonExecuteVO editExecute(PersonExecuteDTO param);

    void removeNew(List<PersonRemoveDTO> param);

    /**
     *  下钻
     * @param personDownDTO
     * @return
     */
    List<PersonTmpVO> personDown(PersonDownDTO personDownDTO) throws Exception;

    /**
     *  准备树
     * @param param
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> getPrepareTree(TreeSelectDTO param) throws Exception;

    /**
     *  实施树
     * @param param
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> getExecuteTree(TreeSelectDTO param) throws Exception;
}
