<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    />

    <div class="upload-list">
      <UploadList
        ref="tableRef"
        type="modal"
        :listData="listData"
        height="300px"
        :onChange="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList, getDictByNumber,
} from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const listData:Ref<Record<any, any>[]> = ref([]);
const baseLineOptions:Ref<Record<any, any>[]> = ref([]);
const productOptions:Ref<Record<any, any>[]> = ref([]);
let validateTime = async (rule, value) => {
  if (!value) {
    return Promise.reject('请选择时间');
  }
  return Promise.resolve();
};
const loading:Ref<boolean> = ref(false);
// 产品、编码、基线暂时隐藏。由于是从超越挪过来的
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'actualEndTime',
      label: '计划实际结束时间：',
      colProps: { span: 12 },
      rules: [
        {
          required: true,
          trigger: 'change',
          validator: validateTime,
        },
      ],
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
      component: 'DatePicker',
    },
    // {
    //   field: 'isProcess',
    //   label: '是否需要审核：',
    //   colProps: { span: 12 },
    //   rules: [
    //     {
    //       required: true,
    //       type: 'boolean',
    //       message: '请选择是否需要审核',
    //     },
    //   ],
    //   componentProps: {
    //     disabled: computed(() => props.record.nodeType === 'milestone'),
    //     placeholder: '请选择',
    //     options: [
    //       {
    //         label: '是',
    //         value: true,
    //       },
    //       {
    //         label: '否',
    //         value: false,
    //       },
    //     ],
    //   },
    //   component: 'Select',
    //   ifShow: props.record.processType === 'workProcess',
    // },
    // {
    //   field: 'productId',
    //   label: '产品：',
    //   colProps: { span: 12 },
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择产品',
    //     },
    //   ],
    //   componentProps: {
    //     placeholder: '请选择',
    //     options: computed(() => productOptions.value),
    //     fieldNames: {
    //       label: 'name',
    //       value: 'id',
    //     },
    //   },
    //   component: 'Select',
    //   ifShow: props.record.planActive !== null && props.record.planActive.indexOf('designPlan') >= 0,
    // },

    // {
    //   field: 'designTaskNumber',
    //   label: '编码：',
    //   colProps: { span: 12 },
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入编码',
    //     },
    //   ],
    //   componentProps: {
    //     placeholder: '请输入编码',
    //   },
    //   component: 'Input',
    //   ifShow: props.record.planActive !== null && props.record.planActive.indexOf('designPlan') >= 0,
    // },

    // {
    //   field: 'baseLine',
    //   label: '基线',
    //   colProps: { span: 12 },
    //   required: true,
    //   componentProps: {
    //     placeholder: '请选择',
    //     options: computed(() => baseLineOptions.value),
    //     fieldNames: {
    //       label: 'name',
    //       value: 'value',
    //     },
    //   },
    //   component: 'Select',
    //   ifShow: props.record.planActive !== null && props.record.planActive.indexOf('designPlan') >= 0,
    // },
    {
      field: 'executeDesc',
      label: '计划执行情况说明：',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
  ],
});

function onChange(data) {
  listData.value = data;
}
onMounted(async () => {
  if (props.record.nodeType === 'milestone' && props.record.processType === 'workProcess') {
    await setFieldsValue({ isProcess: true });
  }
  baseLineOptions.value = await getDictByNumber('baseLine');
  productOptions.value = await getProductOptions();
});
function getProductOptions() {
  return new Api('/pms').fetch('', `projectToProduct/getProductEstimateMaterialList?projectId=${props.record.projectId}`, 'GET');
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    params.attachments = listData.value;
    await new Api('/pms').fetch(params, 'projectScheme/finish', 'PUT');
    message.success('执行完成成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
//.upload-list{
//  height: 200px;
//  overflow: hidden;
//}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
</style>