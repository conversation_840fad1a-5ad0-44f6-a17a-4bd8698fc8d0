package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/23/11:29
 * @description:
 */
@Data
@ApiModel(value = "StatusCountDTO对象", description = "每日状态统计")
public class StatusCountDTO implements Serializable {

    /**
     * 类型（任务、需求、问题、风险......）
     */
    @ApiModelProperty(value = "类型（任务、需求、问题、风险......）")
    private Integer type;

    /**
     * 当前日期
     */
    @ApiModelProperty(value = "当前日期")
    private Date nowDate;

    /**
     *
     */
    private String projectId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id ")
    private String id;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer finishingCount;

}
