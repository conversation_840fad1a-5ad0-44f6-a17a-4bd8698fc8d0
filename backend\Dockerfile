FROM openjdk:11-jre

MAINTAINER orion

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN mkdir -p /app

ENV PROFILE=prd_test
ENV NACOS_USERNAME=devops
ENV NACOS_PASSWORD=devops123
ENV SERVER_ADDR=10.60.16.206:8848

ARG JAR_FILE
COPY ${JAR_FILE} /app/app.jar


ENTRYPOINT ["java","-jar","/app/app.jar","--spring.profiles.active=${PROFILE}","--spring.cloud.nacos.username=${NACOS_USERNAME}","--spring.cloud.nacos.password=${NACOS_PASSWORD}","--spring.cloud.nacos.config.server-addr=${SERVER_ADDR}"]








