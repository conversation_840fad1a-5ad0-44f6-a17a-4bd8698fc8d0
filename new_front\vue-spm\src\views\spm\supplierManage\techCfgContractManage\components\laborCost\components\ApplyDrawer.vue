<script setup lang="ts">
import { useBusinessWorkflow, UseBusinessWorkflowReturn, type WorkflowProps } from 'lyra-workflow-component-vue3';
import {
  BasicCard, BasicForm, FormSchema, openDrawer, UploadList, useForm,
} from 'lyra-component-vue3';
import {
  computed, h, nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import CheckLaborCost from './CheckLaborCost.vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any>
  fetching: Ref<boolean>
}>();

const emits = defineEmits<{
  (e: 'updateFetching', value: boolean): void
}>();

const deptIds: Ref<string[]> = ref([]);
const deptList: Ref<any[]> = ref([]);

async function getDeptList() {
  try {
    deptList.value = await new Api('/pmi/organization/dept/list').fetch({}, '20', 'GET');
  } finally {

  }
}

const checkLaborCostRef: Ref = ref();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '基本信息',
        isSpacing: false,
      });
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '申请单名称',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '系统自动生成',
      disabled: true,
      maxlength: 100,
    },
  },
  {
    field: 'contractNo',
    component: 'Input',
    label: '验收合同编号',
    defaultValue: props.record.contractNumber,
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'contractName',
    component: 'Input',
    label: '验收合同名称',
    defaultValue: props.record.contractName,
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'acceptance',
    component: 'DatePicker',
    label: '验收时间段',
    componentProps: {
      placeholder: ' ',
      disabled: true,
      valueFormat: 'YYYY-Q',
      picker: 'quarter',
      format(date) {
        return `${dayjs(date).format('YYYY')} 年 第 ${Math.ceil((dayjs(date).month() + 1) / 3)} 季度`;
      },
    },
  },
  {
    field: 'employDeptCode',
    component: 'Select',
    label: '验收用人单位',
    componentProps: {
      placeholder: ' ',
      disabled: true,
      fieldNames: {
        label: 'name',
        value: 'deptCode',
      },
      options: deptList,
      onChange(deptCode: string, option: { id: string }) {
        deptIds.value = option?.id?.split(',');
      },
    },
  },
  {
    field: 'projectReviewer',
    component: 'SelectUser',
    label: '项目组审核人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {},
  },
  {
    field: 'deptReviewer',
    component: 'SelectUser',
    label: '研究所审核人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps({ formModel }) {
      return {
        selectUserModalProps: {
          selectType: 'radio',
          treeDataApi: () => new Api('/pmi/organization/current/org/tree').fetch(
            deptIds.value,
            '',
            'POST',
          ),
        },
      };
    },
  },
  {
    field: 'orgReviewer',
    component: 'SelectUser',
    label: '中心/部门审核人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps({ formModel }) {
      return {
        selectUserModalProps: {
          selectType: 'radio',
          treeDataApi: () => new Api('/pmi/organization/current/org/tree').fetch(
            deptIds.value,
            '',
            'POST',
          ),
        },
      };
    },
  },
  {
    field: 'belongDeptReviewer',
    component: 'SelectUser',
    label: '归口部门审核人',
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 1000,
      autoSize: false,
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '验收人力成本费用',
        isSpacing: false,
      }, {
        default: () => h(CheckLaborCost, {
          isModal: true,
          list: list.value,
          row: row.value,
          ref: checkLaborCostRef,
        }),
      });
    },
  },
  {
    field: 'fileList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '相关附件',
        isSpacing: false,
      }, {
        default: () => h(UploadList, {
          height: 300,
          isSpacing: false,
          type: 'modal',
          listData: model[field],
          onChange(fileList: any[]) {
            setFieldsValue({
              fileList,
            });
          },
        }),
      });
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  Promise.allSettled([getDeptList(), getDetail()]).finally(() => {
    emits('updateFetching', false);
  });
});

const detail: Record<string, any> = reactive({});

async function getDetail() {
  try {
    if (props.record.accepttanceId) {
      const result = await new Api('/spm/laborCostAcceptance').fetch({}, props.record.accepttanceId, 'GET');

      Object.assign(detail, result || {});

      deptIds.value = deptList.value.filter((item) => item.deptCode === result.employDeptCode).map((item) => item.id);

      await setFieldsValue({
        name: result.name,
        acceptance: `${result.acceptanceYear}-${result.acceptanceQuarter * 3}`,
        employDeptCode: result.employDeptCode,
        projectReviewer: result.projectReviewerName ? result.projectReviewerName.split(',').map((name, index) => ({
          name,
          id: result.projectReviewer.split(',')[index],
        })) : [],
        deptReviewer: result.deptReviewer ? [
          {
            id: result.deptReviewer,
            name: result.deptReviewerName,
          },
        ] : [],
        orgReviewer: result.orgReviewer ? [
          {
            id: result.orgReviewer,
            name: result.orgReviewerName,
          },
        ] : [],
        belongDeptReviewer: result.belongDeptReviewer ? [
          {
            id: result.belongDeptReviewer,
            name: result.belongDeptReviewerName,
          },
        ] : [],
        remark: result.remark,
        fileList: result.fileList,
      });
    } else {
      const result = await new Api('/spm/attendanceSign/list').fetch({
        orgCode: props.record.orgCode,
        attandanceYear: props.record.year,
        attandanceQuarter: props.record.quarter,
        contractNo: props.record.contractNumber,
      }, '', 'POST');
      Object.assign(detail, result || {});
      await nextTick();
      setFieldsValue({
        acceptance: `${props.record.year}-${props.record.quarter * 3}`,
        employDeptCode: props.record.orgCode,
      });
    }
  } catch (e) {
    console.error(e);
  }
}

const list = computed(() => detail?.attendanceResultCostVO?.list || []);

const row = computed(() => detail?.laborCostStatisticsSingleVO);

function getParams(formValues: Record<string, any>) {
  const attendanceSignList = checkLaborCostRef.value?.getData();
  return {
    attendanceSignList,
    contractNo: props.record.contractNumber,
    contractName: props.record.contractName,
    id: props?.record?.accepttanceId,
    acceptanceDeptCode: props.record.orgCode,
    acceptanceYear: formValues.acceptance?.split('-')?.[0],
    acceptanceQuarter: formValues.acceptance?.split('-')?.[1],
    belongDeptReviewer: formValues.belongDeptReviewer?.[0]?.id,
    deptReviewer: formValues.deptReviewer?.[0]?.id,
    orgReviewer: formValues.orgReviewer?.[0]?.id,
    employDeptCode: formValues.employDeptCode,
    projectReviewer: formValues.projectReviewer?.map((item) => item.id)?.join?.(','),
    remark: formValues.remark,
    fileList: formValues.fileList,
  };
}

function format(date) {
  const dateStr = dayjs(date).format('YYYY-Q');
  return `${dateStr.split('-')[0]} 年 第 ${dateStr.split('-')[1]} 季度`;
}

defineExpose({
  // 保存
  async save(loading: Ref<boolean>) {
    await validate();
    const formValues = getFieldsValue();
    return new Promise((resolve, reject) => {
      loading.value = true;
      new Api('/spm/laborCostAcceptance').fetch(getParams(formValues), props?.record?.accepttanceId ? 'edit' : 'add', props?.record?.accepttanceId ? 'PUT' : 'POST').then((res) => {
        if (props.record.accepttanceId) {
          resolve({
            id: detail.id,
            name: detail.name,
            className: detail.className,
          });
        } else {
          resolve(res);
        }
      }).catch((err) => {
        reject(err);
      })
        .finally(() => {
          loading.value = true;
        });
    });
  },
  async initiate(businessData, loading: Ref<boolean>) {
    // @ts-ignore
    const { onAddTemplate }: UseBusinessWorkflowReturn = useBusinessWorkflow<WorkflowProps>({
      Api,
      openDrawer,
      getContainer() {
        return props.record?.containerRef;
      },
      businessData,
      openCCModal() {
        return Promise.resolve();
      },
    });
    await onAddTemplate({
      messageUrl: `/spm/LaborCostDetail/${businessData.id}`,
    }, 'start', loading);
  },
});
</script>

<template>
  <div
    v-loading="fetching"
    class="form-wrap"
    :style="{
      overflow: fetching? 'hidden' : 'auto'
    }"
  >
    <BasicForm
      @register="register"
    />
  </div>
</template>

<style scoped lang="less">
.form-wrap {
  height: 100%;
  position: relative;
}
</style>
