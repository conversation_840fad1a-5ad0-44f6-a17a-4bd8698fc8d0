<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    left-title="文档类型"
  >
    <template #leftTitleRight>
      <div
        v-if="btnShow && isPower('PMS_XMXQ_container_09_01_01_button_03', powerData)"
        :style="{ margin: '0 10px 0 0', fontSize: '18px' }"
        @click="addClick"
      >
        <PlusCircleOutlined />
      </div>
    </template>
    <template #left>
      <div>
        <div :style="{ margin: '0 0 5px 0' }">
          <aInputSearch
            v-model:value="searchValue"
            placeholder="请输入内容"
            @search="searchData"
          />
        </div>
        <div :style="{ padding: '0 0 0 0' }">
          <basicTitle
            :title="'所有类型文档'"
            class="treeList_title"
          >
            <a-tree
              v-model:selectedKeys="selectedKeys"
              :tree-data="treeData"
              show-icon
              block-node
              class="treeList_content"
              :expanded-keys="expandedKeys"
              :auto-expand-parent="autoExpandParent"
              @select="select"
              @expand="onExpand"
            >
              <template #xxx="record">
                <span v-if="record.title.indexOf(searchValue) > -1">
                  {{ record.title.substr(0, record.title.indexOf(searchValue)) }}
                  <span style="color: #f50">{{ searchValue }}</span>
                  {{
                    record.title.substr(record.title.indexOf(searchValue) + searchValue.length)
                  }}({{ record.count }})
                </span>
                <span v-else>{{ record.title }}</span>
                <div
                  v-if="!addIcon.includes(record.title)"
                  :style="{ float: 'right', position: 'absolute', right: '10px', top: 0 }"
                >
                  <a-popover trigger="hover">
                    <template #content>
                      <div
                        v-if="isPower('PMS_XMXQ_container_09_01_01_button_06', powerData)"
                        class="action-btn"
                        @click="editPopver(record)"
                      >
                        修改信息
                      </div>
                      <div
                        v-if="isPower('PMS_XMXQ_container_09_01_01_button_07', powerData)"
                        class="action-btn"
                        @click="DeletePopver(record.key)"
                      >
                        删除信息
                      </div>
                    </template>
                    <div
                      :style="{ fontSize: '18px' }"
                      @click="addClick2(99999)"
                    >
                      <MoreOutlined />
                    </div>
                  </a-popover>
                </div>
              </template>
            </a-tree>
          </basicTitle>
        </div>
      </div>
    </template>
    <PeopleTable
      v-if="tabsIndex === 0"
      :role-id="roleId"
      :btn-show="btnShow"
      :pageType="pageType"
      :treeData="treeData"
      @updateTree="updateTree"
    />
    <!-- 新建/编辑抽屉 -->
    <addProjectModal
      v-if="pageType==='page'"
      :id="id"
      :data="addNodeModalData"
      :list-data="editdataSource"
      @success="successSave"
    />
  </Layout2>
</template>

<script lang="ts">
import addProjectModal from './addProjectModal.vue';
import {
  defineComponent, inject, onMounted, reactive, ref, toRefs, watch,
} from 'vue';
import { MoreOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { isPower, Layout2 } from 'lyra-component-vue3';
import {
  Input, message, Modal, Popover, Tree,
} from 'ant-design-vue';
import PeopleTable from './ManegeComponents/peopleTable.vue';
import { deleteDocApi, getTreeApi } from '/@/views/pms/projectLaborer/api/docManagement';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

export default defineComponent({
  name: 'PeopleManege',
  components: {
    Layout2,
    PeopleTable,
    PlusCircleOutlined,
    MoreOutlined,
    ATree: Tree,
    aInputSearch: Input.Search,
    addProjectModal,
    basicTitle,
    APopover: Popover,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    keynumber: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const state = reactive({
      powerData: [],
      nameValue: '',
      tabsIndex: 0,
      rootSubmenuKeys: ['sub1'],
      openKeys: ['sub1'],
      selectedKeys: [],
      menuOptions: [],
      roleId: '',
      tableHeight: 500,
      treeData: [],
      addNodeModalData: {},
      editdataSource: {},
      // 全部扁平数据
      dataList: [],
      defaultKeys: '',
      // 项目数据区扁平数据
      processItem: [],
      addIcon: [],
      btnShow: true,
    });
    state.powerData = inject('powerData');
    const visible = ref<boolean>(false);

    watch(
      () => props.keynumber,
      () => getManege(),
    );

    onMounted(async () => {
      state.tableHeight = document.body.clientHeight - 365;

      await getManege();
    });

    const getManege = async () => {
      const love = {
        id: props.id,
        className: 'DocumentType',
        moduleName: '项目管理-文档管理-文档类型',
        type: 'GET',
        remark: `获取/搜索了【${props.id}】文档类型`,
      };
      const res = await getTreeApi({ projectId: props.id }, love);
      state.treeData = endTree(convertTree(res));
      // 进入页面选中项目数据区处理
      state.treeData.forEach((ele) => {
        if (!state.roleId) {
          if (ele.title === '项目数据区') {
            state.roleId = ele.key;
            expandedKeys.value = [ele.key];
            state.selectedKeys = [ele.key];
          }
        }
      });

      generateList(state.treeData);

      const processData = state.treeData.find((item) => item.title === '过程数据区');

      genitemList(processData ? [processData] : []);
      // 获取过程数据区标识,用于判断是否添加操作按钮
      state.addIcon = state.processItem.map((item) => item.title);
    };
    // 判断是否选中过程数据区
    watch(
      () => state.roleId,
      (newvalue) => {
        if (
          state.processItem.findIndex((item) => item.key === state.selectedKeys[0]) > -1
        ) {
          state.btnShow = false;
        } else {
          state.btnShow = true;
        }
      },
    );
    const onOpenChange = (openKeys: string[]) => {
      const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
      if (state.rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
        state.openKeys = openKeys;
      } else {
        state.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    };
    const select = (selectedKeys) => {
      if (selectedKeys.length === 0) {
        state.selectedKeys = [state.roleId];
        return;
      }
      state.roleId = selectedKeys[0];
    };
    const addClick = () => {
      /* 新建项目 */
      state.addNodeModalData = {
        formType: 'add',
        defaultKey: state.roleId,
      };
    };
    const addClick2 = (qq) => {
    };
    const searchData = () => {
    };
    const convertTree = (tree) => {
      const result = [];
      tree.forEach((item) => {
        let children = item.child || [];
        let {
          name: title, id: key, parentId, count, projectId, remark,
        } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          key,
          parentId,
          count,
          projectId,
          remark,
        });
      });
      return result;
    };

    const endTree = (sb) => {
      sb.forEach((ele) => {
        if (ele.children) {
          endTree(ele.children);
          ele.children.forEach((item) => {
            item.slots = {
              title: 'xxx',
            };
          });
        }
      });
      return sb;
    };

    const DeletePopver = (keynumber) => {
      Modal.confirm({
        title: '是否删除该数据？',
        onOk() {
          deleteDocApi(keynumber).then((res) => {
            message.success('删除成功');
            state.roleId = '';
            getManege();
          });
        },
        // confirm: async () => {
        //   await deleteDocApi(keynumber);
        //   getManege();
        // },
      });
    };

    const editPopver = ({
      key, title, remark, parentId,
    }) => {
      state.addNodeModalData = { formType: 'edit' };
      state.editdataSource = {
        parentId,
        name: title,
        remark,
        id: key,
      };
    };
    const successSave = () => {
      state.roleId = '';
      getManege();
    };

    /* ----------------------------- */
    const searchValue = ref('');
    const expandedKeys = ref([]);
    const autoExpandParent = ref(true);
    //   const dataList = [];
    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        //   const key = node.key;
        state.dataList.push(node);

        if (node.children) {
          generateList(node.children);
        }
      }
    };
    // 获取项目数据区扁平数据
    const genitemList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        //   const key = node.key;
        state.processItem.push(node);

        if (node?.children) {
          genitemList(node?.children);
        }
      }
    };

    const onExpand = (keys) => {
      expandedKeys.value = keys;
      autoExpandParent.value = false;
    };
    const getParentKey = (key, tree) => {
      let parentKey;

      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];

        if (node.children) {
          if (node.children.some((item) => item.key === key)) {
            parentKey = node.key;
          } else if (getParentKey(key, node.children)) {
            parentKey = getParentKey(key, node.children);
          }
        }
      }

      return parentKey;
    };
    watch(searchValue, (value) => {
      const expanded = state.dataList
        .map((item) => {
          if (item.title.indexOf(value) > -1) {
            return getParentKey(item.key, state.treeData);
          }
          return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      expandedKeys.value = expanded;
      searchValue.value = value;
      autoExpandParent.value = true;
    });

    /* --------------------------- */
    const updateTree = () => {
      getManege();
    };
    return {
      ...toRefs(state),
      updateTree,
      onOpenChange,
      select,
      addClick,
      searchData,
      addClick2,
      DeletePopver,
      visible,
      editPopver,
      successSave,
      searchValue,
      expandedKeys,
      autoExpandParent,
      onExpand,
      isPower,
    };
  },
});
</script>

<style lang="less" scoped>
:deep(.treeList_title) {
  height: calc(~'100% - 90px');

  .basicTitle_title {
    padding-left: 5px;

    .anticon {
      padding-left: 0px;
    }
  }

  .basicTitle_content {
    height: calc(~'100% - 50px');
    overflow: auto;

    .ant-tree-node-content-wrapper {
      width: calc(~'100% - 24px');
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-tree-switcher {
      //line-height: 44px !important;
      .ant-tree-switcher-icon {
        font-size: 12px !important;
      }
    }

    .treeList_content {
      & > li {
        padding-left: 10px;

        .ant-tree-node-content-wrapper {
          position: relative;

          .ant-tree-title {
            display: inline-block;
            width: 95%;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}
</style>
