package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectSchemeDTO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:17
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeForUsersDTO对象", description = "用户已关联的项目计划")
public class ProjectSchemeForUsersDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "用户Ids")
    private List<String> userIds;

    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;
}
