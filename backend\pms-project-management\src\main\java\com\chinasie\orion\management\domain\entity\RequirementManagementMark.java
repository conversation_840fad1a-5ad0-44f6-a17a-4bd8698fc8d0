package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * RequirementManagementMark Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-05 20:04:54
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "pmsx_requirement_management_mark")
@ApiModel(value = "RequirementManagementMarkEntity对象", description = "需求管理标注")
@Data

public class RequirementManagementMark extends ObjectEntity implements Serializable {

    /**
     * 需求管理id，关联 pms_requirement_mangement id
     */
    @ApiModelProperty(value = "需求管理id，关联 pms_requirement_mangement id")
    @TableField(value = "requirement_id")
    private String requirementId;

}
