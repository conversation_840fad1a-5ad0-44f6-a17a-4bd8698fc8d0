package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import java.io.Serializable;

/**
 * ProjectPurchaseReceiveInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:16:22
 */
@TableName(value = "pms_project_purchase_receive_info")
@ApiModel(value = "ProjectPurchaseReceiveInfo对象", description = "项目采购收货方信息")
@Data
public class ProjectPurchaseReceiveInfo extends ObjectEntity implements Serializable{

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    @TableField(value = "receive_person" )
    private String receivePerson;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    @TableField(value = "receive_phone" )
    private String receivePhone;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    @TableField(value = "receive_address" )
    private String receiveAddress;

    /**
     * 收货人邮箱
     */
    @ApiModelProperty(value = "收货人邮箱")
    @TableField(value = "receive_email" )
    private String receiveEmail;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    @TableField(value = "purchase_id" )
    private String purchaseId;

}
