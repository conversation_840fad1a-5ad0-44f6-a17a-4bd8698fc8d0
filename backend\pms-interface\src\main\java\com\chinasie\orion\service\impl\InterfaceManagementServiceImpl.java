package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DataClassNameBO;
import com.chinasie.orion.constant.InterfaceDict;
import com.chinasie.orion.constant.InterfaceManagementStatusEnum;
import com.chinasie.orion.domain.dto.RelationDTO;
import com.chinasie.orion.domain.entity.InterfaceManagement;
import com.chinasie.orion.domain.dto.InterfaceManagementDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.workflow.ActivityVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.InterfaceManagementMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.IdeaFormService;
import com.chinasie.orion.service.InterfaceManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * InterfaceManagement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@Service
@Slf4j
public class InterfaceManagementServiceImpl extends OrionBaseServiceImpl<InterfaceManagementMapper, InterfaceManagement> implements InterfaceManagementService {

    @Autowired
    private InterfaceManagementMapper interfaceManagementMapper;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    private IdeaFormService ideaFormService;
    private WorkflowFeignService workflowFeignService;

    @Autowired
    private DataClassNameBO dataClassNameBO;

    @Autowired
    private PasFeignService pasFeignService;


    @Autowired
    public void setWorkflowFeignService(WorkflowFeignService workflowFeignService) {
        this.workflowFeignService = workflowFeignService;
    }

    @Autowired
    public void setIdeaFormService(IdeaFormService ideaFormService) {
        this.ideaFormService = ideaFormService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public InterfaceManagementVO detail(String id) throws Exception {
        InterfaceManagement interfaceManagement = interfaceManagementMapper.selectById(id);
        InterfaceManagementVO result = BeanCopyUtils.convertTo(interfaceManagement, InterfaceManagementVO::new);

        List<String> deptList = Arrays.stream(interfaceManagement.getReviewDeptIds().split(",")).collect(Collectors.toList());
        deptList.add(interfaceManagement.getPublishDeptId());

        Map<String, String> idToName = getDeptIdToName(deptList);
        result.setPublishDeptName(idToName.getOrDefault(interfaceManagement.getPublishDeptId(), ""));
        deptList.remove(interfaceManagement.getPublishDeptId());
        List<String> nameList = new ArrayList<>();
        deptList.stream().forEach(key -> {
            nameList.add(idToName.getOrDefault(key, ""));
        });
        result.setPublishDeptName(idToName.getOrDefault(interfaceManagement.getPublishDeptId(), ""));
        result.setReviewDeptNames(nameList.stream().collect(Collectors.joining(",")));

        result.setTypeName(InterfaceDict.keyToDescMap.getOrDefault(result.getType(), ""));
        result.setThirdVerifyName(InterfaceDict.keyToDescMap.getOrDefault(result.getThirdVerify(), ""));


        List<String> cooperationUsers = Arrays.stream(result.getCooperationUsers().split(",")).collect(Collectors.toList());
        cooperationUsers.add(result.getManUser());
        Map<String, String> userIdToName = getUserIdToName(cooperationUsers);

        result.setManUserName(userIdToName.getOrDefault(result.getManUser(), ""));

        List<String> userNameList = new ArrayList<>();
        cooperationUsers.remove(result.getManUser());
        cooperationUsers.stream().forEach(key -> userNameList.add(userIdToName.getOrDefault(key, "")));
        result.setCooperationUsers(interfaceManagement.getCooperationUsers());
        result.setCooperationUserNames(userNameList.stream().collect(Collectors.joining(",")));
        result.setCooperationUserIdList(cooperationUsers);
        Map<String,List<String>> idToUserList = new HashMap<>();
        this.getCurrentWorkflow(idToUserList, ListUtil.toList(id));

        String creatorId = interfaceManagement.getCreatorId();
        SimpleUser simpleUser = userRedisHelper.getSimpleUserById(creatorId);
        if(!ObjectUtils.isEmpty(simpleUser)){
            result.setCreatorDeptId(simpleUser.getOrgId());
            result.setCreatorDeptName(simpleUser.getOrgName());
        }
        List<String> userIdList = idToUserList.get(result.getId());
        if(!CollectionUtils.isEmpty(userIdList)){
            List<SimpleUser> simpleUserByIds = userRedisHelper.getSimpleUserByIds(userIdList);
            if(!CollectionUtils.isEmpty(simpleUserByIds)){
                result.setNowRspDeptName(simpleUserByIds.stream().map(SimpleUser::getOrgName).distinct().collect(Collectors.joining(",")));
            }
        }
        return result;
    }


    public Map<String, String> getDeptIdToName(List<String> deptList) {
        List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(deptList);
        if(CollectionUtils.isEmpty(deptVOList)){
            return  new HashMap<>();
        }
        return deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName,(k1,k2)->k1));
    }

    public Map<String, String> getUserIdToName(List<String> userList) {
        List<SimpleUser> simpleUsers = userRedisHelper.getSimpleUserByIds(userList);
        if(CollectionUtils.isEmpty(simpleUsers)){
            return  new HashMap<>();
        }
        return simpleUsers.stream().collect(Collectors.toMap(SimpleUser::getId, SimpleUser::getName,(k1,k2)->k1));
    }

    /**
     * 新增
     * <p>
     * * @param interfaceManagementDTO
     */
    @Override
    public InterfaceManagementVO create(InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        InterfaceManagement interfaceManagement = BeanCopyUtils.convertTo(interfaceManagementDTO, InterfaceManagement::new);
        if (!StringUtils.hasText(interfaceManagementDTO.getNumber())) {
            this.getInterfaceNumber();
        }

        List<String> reviewDeptIdList = interfaceManagementDTO.getReviewDeptIdList();
        if (!CollectionUtils.isEmpty(reviewDeptIdList)) {
            interfaceManagement.setReviewDeptIds(reviewDeptIdList.stream().collect(Collectors.joining(",")));
        }else{
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "接收方部门不能为空");
        }

        List<String> cooperationUserIdList = interfaceManagementDTO.getCooperationUserIdList();
        if (!CollectionUtils.isEmpty(cooperationUserIdList)) {
            interfaceManagement.setCooperationUsers(cooperationUserIdList.stream().collect(Collectors.joining(",")));
        }else{
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "协办人不能为空");
        }
        // 默认传递单
        interfaceManagement.setFormType(InterfaceDict.FormTypeDict.IM_TRANSMISSION_FORM.getName());
        interfaceManagement.setStatus(InterfaceManagementStatusEnum.IM_APPLY_FOR.getStatus());
        String dataId = interfaceManagementDTO.getDataId();
        String dataClassName = "";
        if(StringUtils.hasText(dataId)){
            dataClassName= dataClassNameBO.getClassNameByDataId(dataId);
            interfaceManagement.setDataClassName(dataClassName);
        }
        interfaceManagementMapper.insert(interfaceManagement);
        if(StringUtils.hasText(dataClassName)){
            RelationDTO relationDTO = new RelationDTO();
            relationDTO.setFromId(dataId);
            relationDTO.setFromClass(dataClassName);
            relationDTO.setToClass(interfaceManagement.getClassName());
            relationDTO.setToId(interfaceManagement.getId());
            ResponseDTO<Boolean> responseDTO = pasFeignService.addRelationToIm(relationDTO);
            if(responseDTO.getCode() != HttpServletResponse.SC_OK){
                throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "关联关系新增失败");
            }
        }
        InterfaceManagementVO rsp = BeanCopyUtils.convertTo(interfaceManagement, InterfaceManagementVO::new);
        return rsp;
    }

    public String getInterfaceNumber() throws Exception {
        ResponseDTO<String> numberRes = sysCodeApi.rulesAndSegmentCreate("InterfaceManagement", "number", Boolean.FALSE, "");
        if (ResponseUtils.success(numberRes)) {
            if (StringUtils.hasText(numberRes.getResult())) {
                return numberRes.getResult();
            }
            return "XMJK" + IdUtil.objectId();
        } else {
            return "XMJK" + IdUtil.objectId();
        }
    }

    @Override
    public List<SimpleDictVO> getTypeDict() {
        InterfaceDict.InterfaceTypeDict[] values = InterfaceDict.InterfaceTypeDict.values();
        List<SimpleDictVO> simpleDictVOS = new ArrayList<>();
        for (InterfaceDict.InterfaceTypeDict value : values) {
            simpleDictVOS.add(new SimpleDictVO(value.getName(), value.getDesc()));
        }
        return simpleDictVOS;
    }

    @Override
    public List<SimpleDictVO> thirdVerifyDict() {
        InterfaceDict.InterfaceThirdVerifyDict[] values = InterfaceDict.InterfaceThirdVerifyDict.values();
        List<SimpleDictVO> simpleDictVOS = new ArrayList<>();
        for (InterfaceDict.InterfaceThirdVerifyDict value : values) {
            simpleDictVOS.add(new SimpleDictVO(value.getName(), value.getDesc()));
        }
        return simpleDictVOS;
    }

    @Override
    public List<SimpleDictVO> auditStatusDict() {
        InterfaceManagementStatusEnum[] values = InterfaceManagementStatusEnum.values();
        List<SimpleDictVO> simpleDictVOS = new ArrayList<>();
        for (InterfaceManagementStatusEnum value : values) {
            simpleDictVOS.add(new SimpleDictVO(value.getDesc(), value.getStatus()));
        }
        return simpleDictVOS;
    }

    @Override
    public Boolean close(String id) throws Exception {
        InterfaceManagement byId = this.getById(id);
        if(Objects.isNull(byId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前接口不存在，请刷新页面后重试");
        }
        String publishDeptId = byId.getPublishDeptId();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        SimpleUser simpleUser = userRedisHelper.getSimpleUserById(currentUserId);
        String orgId = simpleUser.getOrgId();
//        boolean b = allOrganizationList.stream().anyMatch(i -> Objects.equals(publishDeptId, i.getId()));
        if(!Objects.equals(orgId,publishDeptId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "当前人不存在传递单的发布方部门中，无权关闭");
        }
        Integer status = byId.getStatus();
        if(Objects.equals(status,InterfaceManagementStatusEnum.IM_CLOSE.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "传递单数据已关闭，不能操作");
        }
        if(!Objects.equals(status,InterfaceManagementStatusEnum.IM_APPROVAL_FINISH.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "传递单数据还未走完审批，不能关闭");
        }
        boolean isHave = ideaFormService.isHaveRunningForm(id);
        if(isHave){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "传递单相关的意见单还未走完审批，不能关闭");
        }
        byId.setStatus(InterfaceManagementStatusEnum.IM_CLOSE.getStatus());
        // 级联关闭 相关的意见单
        ideaFormService.closeByImId(id);
        return this.updateById(byId);
    }

    @Override
    public List<String> stateDictList() {
        InterfaceDict.StateDict[] values = InterfaceDict.StateDict.values();
        List<String> stateList = new ArrayList<>();
        for (InterfaceDict.StateDict value : values) {
            stateList.add(value.getKey());
        }
        return stateList;
    }

    @Override
    public List<InterfacePageDataVO> detailListByIdList(List<String> imIdList) throws Exception {
        List<InterfaceManagement> interfaceManagements = this.listByIds(imIdList);
        List<InterfacePageDataVO> vos = BeanCopyUtils.convertListTo(interfaceManagements, InterfacePageDataVO::new);
        this.packageDetailDataByList(vos);
        return vos;
    }

    @Override
    public List<InterfacePageDataVO> list(InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        String projectId = interfaceManagementDTO.getProjectId();
        String number = interfaceManagementDTO.getNumber();
        String desc = interfaceManagementDTO.getDesc();
        LambdaQueryWrapperX<InterfaceManagement> interfaceManagementLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InterfaceManagement.class);
        if(StringUtils.hasText(projectId)){
            interfaceManagementLambdaQueryWrapperX.eq(interfaceManagementDTO.getProjectId(), projectId);
        }
        if(StringUtils.hasText(number) && StringUtils.hasText(desc)){
            interfaceManagementLambdaQueryWrapperX.and(wrapper->{
                wrapper.like(InterfaceManagement::getNumber,number).or().like(InterfaceManagement::getDesc,desc);
            });
        }
        List<InterfaceManagement> list = this.list(interfaceManagementLambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<InterfacePageDataVO> vos = BeanCopyUtils.convertListTo(list, InterfacePageDataVO::new);
        this.packageDetailDataByList(vos);
        return vos;
    }

    /**
     * 编辑
     * <p>
     * * @param interfaceManagementDTO
     */
    @Override
    public Boolean edit(InterfaceManagementDTO interfaceManagementDTO) throws Exception {
        String id = interfaceManagementDTO.getId();
        if (StrUtil.isEmpty(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "数据唯一ID不能为空");
        }
        InterfaceManagement byId = this.getById(id);
        if (!Objects.equals(byId.getStatus(), InterfaceManagementStatusEnum.IM_APPLY_FOR.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "只有已创建的数据能进行修改操作");
        }
        InterfaceManagement interfaceManagement = BeanCopyUtils.convertTo(interfaceManagementDTO, InterfaceManagement::new);
        List<String> reviewDeptIdList = interfaceManagementDTO.getReviewDeptIdList();
        if (!CollectionUtils.isEmpty(reviewDeptIdList)) {
            interfaceManagement.setReviewDeptIds(reviewDeptIdList.stream().collect(Collectors.joining(",")));
        }else{
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "接收方部门不能为空");
        }

        List<String> cooperationUserIdList = interfaceManagementDTO.getCooperationUserIdList();
        if (!CollectionUtils.isEmpty(cooperationUserIdList)) {
            interfaceManagement.setCooperationUsers(cooperationUserIdList.stream().collect(Collectors.joining(",")));
        }else{
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "协办人不能为空");
        }
        int update = interfaceManagementMapper.updateById(interfaceManagement);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<InterfaceManagement> interfaceManagements = this.listByIds(ids);
        boolean b = interfaceManagements.stream().anyMatch(interfaceManagement -> {
            if (!Objects.equals(interfaceManagement.getStatus(), InterfaceManagementStatusEnum.IM_APPLY_FOR.getStatus())) {
                return true;
            }
            return false;
        });
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "只有已创建的数据能进行删除操作");
        }
        int delete = interfaceManagementMapper.deleteBatchIds(ids);
        String dataId="";
        List<String> toIdList = new ArrayList<>();
        for (InterfaceManagement interfaceManagement : interfaceManagements) {
            String dataClassName = interfaceManagement.getDataClassName();
            if(StringUtils.hasText(interfaceManagement.getDataId()) && StringUtils.hasText(dataClassName)){
                dataId = interfaceManagement.getDataId();
                toIdList.add(interfaceManagement.getId());
            }
        }
        if(!CollectionUtils.isEmpty(toIdList)){
            pasFeignService.removeListByFromId(dataId,toIdList);
        }
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<InterfacePageDataVO> pages(Page<InterfaceManagementDTO> pageRequest) throws Exception {
        Page<InterfaceManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), InterfaceManagement::new));
        InterfaceManagementDTO query = pageRequest.getQuery();
        PageResult<InterfaceManagement> page;
        LambdaQueryWrapperX<InterfaceManagement> interfaceManagementLambdaQueryWrapperX = new LambdaQueryWrapperX<>(InterfaceManagement.class);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), interfaceManagementLambdaQueryWrapperX);
        if(!ObjectUtils.isEmpty(query)){
            String projectId = query.getProjectId();
            String number = query.getNumber();
            String desc = query.getDesc();
            if(StringUtils.hasText(projectId)){
                interfaceManagementLambdaQueryWrapperX.eq(InterfaceManagement::getProjectId,projectId);
            }
            String dataId = query.getDataId();
            if(StringUtils.hasText(dataId)){
                interfaceManagementLambdaQueryWrapperX.eq(InterfaceManagement::getDataId,dataId);
            }

            if(StringUtils.hasText(number)&&StringUtils.hasText(desc)){
                interfaceManagementLambdaQueryWrapperX.and(interfaceManagementMPJLambdaWrapper -> {
                    interfaceManagementMPJLambdaWrapper.like(InterfaceManagement::getNumber,number)
                            .or().like(InterfaceManagement::getDesc,desc);
                });
            }else {
                if(StringUtils.hasText(number)){
                    interfaceManagementLambdaQueryWrapperX.like(InterfaceManagement::getNumber,number);
                }
                if(StringUtils.hasText(desc)){
                    interfaceManagementLambdaQueryWrapperX.like(InterfaceManagement::getNumber,desc);
                }
            }
        }
        page =interfaceManagementMapper.selectPage(realPageRequest, interfaceManagementLambdaQueryWrapperX);
//        PageResult<InterfaceManagement> page = interfaceManagementMapper.selectPage(realPageRequest, null);
        Page<InterfacePageDataVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if(CollectionUtils.isEmpty(page.getContent())){
            pageResult.setTotalSize(0);
            return  pageResult;
        }
        List<InterfacePageDataVO> vos = BeanCopyUtils.convertListTo(page.getContent(), InterfacePageDataVO::new);
        pageResult.setContent(vos);
        this.packageDetailDataByList(vos);
        // 流程节点获取 --意见单数据获取
        return pageResult;
    }

    public void packageDetailDataByList(List<InterfacePageDataVO> vos) throws Exception {
        List<String> deptList = new ArrayList<>();
        List<String> userAllList = new ArrayList<>();
        List<String> businessIdList = new ArrayList<>();
        vos.forEach(i-> {
            deptList.add(i.getPublishDeptId());
            List<String> reviewDeptList = new ArrayList<>();
            if(StringUtils.hasText(i.getReviewDeptIds())){
                reviewDeptList = Arrays.stream(i.getReviewDeptIds().split(",")).collect(Collectors.toList());
            }
            if(!CollectionUtils.isEmpty(reviewDeptList)){
                deptList.addAll(reviewDeptList);
            }
            List<String> userIdList = new ArrayList<>();
            if(StringUtils.hasText(i.getCooperationUsers())){
                userIdList = Arrays.stream(i.getCooperationUsers().split(",")).collect(Collectors.toList());
            }
            if(StringUtils.hasText(i.getManUser())){
                userIdList.add(i.getManUser());
            }
            if(!CollectionUtils.isEmpty(userIdList)){
                userAllList.addAll(userIdList);
            }
            businessIdList.add(i.getId());
        });
        Map<String, String> deptIdToName = getDeptIdToName(deptList);
        Map<String, String> userIdToName = getUserIdToName(userAllList);
        Map<String, IdeaFormVO> idToEntity = ideaFormService.getDataToByIdList(businessIdList);
        Map<String,List<String>> idToUserList = new HashMap<>();
        this.getCurrentWorkflow(idToUserList,businessIdList);
        Map<String, String> idToNodeName = new HashMap<>();
        this.getWorkflowNodeIdToName(idToNodeName,businessIdList);
        vos.forEach(result-> {
            if(StringUtils.hasText(result.getReviewDeptIds())){
                List<String> reviewDeptList = Arrays.stream(result.getReviewDeptIds().split(",")).collect(Collectors.toList());
                List<String> deptNameList = new ArrayList<>();
                for (String s : reviewDeptList) {
                    deptNameList.add(deptIdToName.getOrDefault(s, ""));
                }
                result.setReviewDeptNames(deptNameList.stream().collect(Collectors.joining(",")));
            }
            String publishDeptId = result.getPublishDeptId();
            result.setPublishDeptName(deptIdToName.getOrDefault(publishDeptId,""));
            result.setTypeName(InterfaceDict.keyToDescMap.getOrDefault(result.getType(), ""));
            result.setThirdVerifyName(InterfaceDict.keyToDescMap.getOrDefault(result.getThirdVerify(), ""));
            result.setManUserName(userIdToName.getOrDefault(result.getManUser(), ""));
            if(StringUtils.hasText(result.getCooperationUsers())){
                List<String> coopList = Arrays.stream(result.getCooperationUsers().split(",")).collect(Collectors.toList());
                List<String> userNameList = new ArrayList<>();
                coopList.stream().forEach(key -> {
                    userNameList.add(userIdToName.getOrDefault(key, ""));
                });
                result.setCooperationUserIdList(coopList);
                result.setCooperationUserNames(userNameList.stream().collect(Collectors.joining(",")));
            }
            String businessId = result.getId();
            IdeaFormVO orDefault = idToEntity.getOrDefault(businessId, new IdeaFormVO());
            result.setReplySuggest(orDefault.getReplySuggest());
            result.setNewReplyTime(orDefault.getReplyTime());
            result.setIdeaFormNumber(orDefault.getNumber());
            List<String> userIdList = idToUserList.get(businessId);
            if(!CollectionUtils.isEmpty(userIdList)){
                List<SimpleUser> simpleUserByIds = userRedisHelper.getSimpleUserByIds(userIdList);
                if(!CollectionUtils.isEmpty(simpleUserByIds)){
                    result.setNowRspDeptName(simpleUserByIds.stream().map(SimpleUser::getOrgName).distinct().collect(Collectors.joining(",")));
                }
            }
            String nodeName = idToNodeName.getOrDefault(businessId, "");
            result.setNowFlowNode(nodeName);
        });
    }

    public void getCurrentWorkflow(Map<String,List<String>> idToUserList,List<String> businessIdList){
        try {
            ResponseDTO<List<ProcessInstanceAssigneeListVO>> listResponseDTO = workflowFeignService.assigneeList(businessIdList);
            if(ResponseUtils.success(listResponseDTO)){
                List<ProcessInstanceAssigneeListVO> result = listResponseDTO.getResult();
                if(!CollectionUtils.isEmpty(result)){
                    for (ProcessInstanceAssigneeListVO processInstanceAssigneeListVO : result) {
                        if(!CollectionUtils.isEmpty(processInstanceAssigneeListVO.getCurrentAssigneeIds())){
                            idToUserList.put(processInstanceAssigneeListVO.getBusinessId(),processInstanceAssigneeListVO.getCurrentAssigneeIds());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("获取流程当前责任方异常：{}",e);
        }
    }


    public void getWorkflowNodeIdToName(Map<String,String> idToNodeName,List<String> businessIdList){
        try {
            ResponseDTO<List<ActivityVO>> responseDTO = workflowFeignService.getCurrentNodeInfoByBusiness(businessIdList);
            if(ResponseUtils.success(responseDTO)){
                List<ActivityVO> activityVOList = responseDTO.getResult();
                if(!CollectionUtils.isEmpty(activityVOList)){
                    for (ActivityVO activityVO : activityVOList) {
                        idToNodeName.put(activityVO.getBusinessId(),activityVO.getNodeName());
                    }
                }
            }
        }catch (Exception e){
            log.info("获取流程当前节点异常：{}",e);
        }
    }

}
