<template>
  <div>
    <BasicForm @register="registerOur" />
    <BasicForm @register="registerSupplier" />
  </div>
</template>

<script setup lang="ts">
import { BasicForm, useForm, BasicTitle1 } from 'lyra-component-vue3';
import { defineExpose, h } from 'vue';

function getSchemas(type: 'our' | 'supplier') {
  return [
    {
      field: 'signedMainName',
      component: 'Input',
      label: `${type === 'our' ? '甲方' : '乙方'}签约主体全称`,
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'companyDutyParagraph',
      component: 'Input',
      label: '公司税号',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'string',
        },
      ],
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'busContactPerson',
      component: 'Input',
      label: '商务联系人',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'busContactPhone',
      component: 'Input',
      label: '商务联系人电话',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'projectContactPerson',
      component: 'Input',
      label: '项目联系人',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'projectContactPhone',
      component: 'Input',
      label: '项目联系人电话',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'contractEmail',
      component: 'Input',
      label: '联系邮箱',
      componentProps: {
        maxlength: 200,
      },
    },
    {
      field: 'contactAddress',
      component: 'Input',
      label: '联系地址',
      componentProps: {
        maxlength: 300,
      },
    },
  ];
}

const [registerOur, { validate: validateOur, setFieldsValue: setFieldsValueOur }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'title1',
      component: 'Input',
      colProps: {
        span: 24,
      },
      renderColContent() {
        return h(BasicTitle1, {
          title: '甲方签约主体信息',
          style: {
            marginBottom: '20px',
          },
        });
      },
    },
    ...getSchemas('our'),
  ],
});

const [registerSupplier, { validate: validateSupplier, setFieldsValue: setFieldsSupplier }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'title1',
      component: 'Input',
      colProps: {
        span: 24,
      },
      renderColContent() {
        return h(BasicTitle1, {
          title: '乙方签约主体信息',
          style: {
            marginBottom: '20px',
          },
        });
      },
    },
    ...getSchemas('supplier'),
  ],
});

async function getValues() {
  const [contractOurSignedMainDTO, contractSupplierSignedMainDTO] = await Promise.all([validateOur(), validateSupplier()]);

  return {
    contractOurSignedMainDTO,
    contractSupplierSignedMainDTO,
  };
}

function setValues(values) {
  const {
    // 甲方信息
    contractOurSignedMainVO,
    // 乙方信息
    contractSupplierSignedMainVO,
  } = values;

  setFieldsValueOur(contractOurSignedMainVO);

  setFieldsSupplier(contractSupplierSignedMainVO);
}

defineExpose({
  getValues,
  setValues,
});
</script>

<style scoped lang="less">

</style>
