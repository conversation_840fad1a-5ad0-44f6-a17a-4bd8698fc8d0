package com.chinasie.orion.dict;

/**
 * SchemeDict
 *
 * @author: yangFy
 * @date: 2023/4/23 10:46
 * @description
 * <p>
 *项目计划字典枚举
 * </p>
 */
public class SchemeDict {
    /**
     * 项目计划层级
     */
    public static final String LEVEL="project_scheme_level";
    public static final String BASE_LINE="baseLine";
    public static final String PLAN_ACTIVE="planActive";
    public static final String ISSUE_PROCESS="10";
    public static final String COMPLETE_PROCESS="30";
    public static final String SUSPEND_PROCESS="20";
    public static final String TERMINATION_PROCESS="40";
    public static final String START_PROCESS="50";


    /**
     *  pms_enforce_Type 实施类型
     * pms_enforce_scope 实施区域
     * pms_work_content  工作内容
     */
    public static final String ENFORECE_TYPE="pms_enforce_Type";

    public static final String ENFORECE_SCORE="pms_enforce_scope";

    public static final String WORK_CONTENT="pms_work_content";




}
