<script setup lang="ts">
import { ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { materialPage } from '/@/views/pms/api/projectMaterialPlan';
const props = defineProps({
  type: {
    type: String,
    default: 'check',
  },
  columns: {
    type: Array,
    default: () => [
      {
        title: '序号',
        dataIndex: 'index',
        width: 60,
        fixed: 'left',
        slots: { customRender: 'index' },
      },
      {
        title: '物料名称',
        dataIndex: 'name',
        fixed: 'left',
        minWidth: 250,
      },
      {
        title: '物料编码',
        dataIndex: 'number',
      },
      {
        title: '基本单位',
        dataIndex: 'basicUnit',
      },
      {
        title: '物料描述',
        dataIndex: 'description',
      },

    ],
  },
});
const tableRef = ref(null);

const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['number', 'name'],
  rowSelection: {
    type: props.type,
  },
  pagination: true,
  isFilter2: false,
  api: (params) => materialPage(params),
  columns: props.columns,
});

// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const keys = tableRef.value?.getSelectRows();
  if (keys.length > 0) {
    resolve(keys);
  } else {
    message.warning('请选择数据');
    reject();
  }
});

defineExpose({
  isSelectedAndGetData,
});
</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
