package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormpurchaseRequest Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@TableName(value = "ncf_form_purchase_request")
@ApiModel(value = "NcfFormpurchaseRequestEntity对象", description = "采购申请主表")
@Data
public class NcfFormpurchaseRequest extends ObjectEntity implements Serializable {

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    @TableField(value = "code")
    private String code;

    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    @TableField(value = "name")
    private String name;

    /**
     * 采购立项完成时间
     */
    @ApiModelProperty(value = "采购立项完成时间")
    @TableField(value = "project_end_time")
    private Date projectEndTime;

    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 申请单状态
     */
    @ApiModelProperty(value = "申请单状态")
    @TableField(value = "state")
    private String state;

    /**
     * 申请单类型
     */
    @ApiModelProperty(value = "申请单类型")
    @TableField(value = "type")
    private String type;

    /**
     * 申请单来源
     */
    @ApiModelProperty(value = "申请单来源")
    @TableField(value = "source")
    private String source;

    /**
     * 采购申请金额（元）
     */
    @ApiModelProperty(value = "采购申请金额（元）")
    @TableField(value = "money")
    private BigDecimal money;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    @TableField(value = "rate")
    private BigDecimal rate;

    /**
     * 预计开工时间
     */
    @ApiModelProperty(value = "预计开工时间")
    @TableField(value = "estimated_begin_time")
    private Date estimatedBeginTime;

    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    @TableField(value = "warranty_level")
    private String warrantyLevel;

    /**
     * 申请部门id
     */
    @ApiModelProperty(value = "申请部门id")
    @TableField(value = "applicant_dept_id")
    private String applicantDeptId;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @TableField(value = "applicant_dept")
    private String applicantDept;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    @TableField(value = "applicant_user_id")
    private String applicantUserId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant_user")
    private String applicantUser;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 与现场安全相关
     */
    @ApiModelProperty(value = "与现场安全相关")
    @TableField(value = "with_safety")
    private String withSafety;

    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    @TableField(value = "purchase_plan_code")
    private String purchasePlanCode;

    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    @TableField(value = "bk_dept")
    private String bkDept;

    /**
     * 归口管理
     */
    @ApiModelProperty(value = "归口管理")
    @TableField(value = "bk_manage")
    private String bkManage;

    /**
     * 建议采购方式
     */
    @ApiModelProperty(value = "建议采购方式")
    @TableField(value = "suggest_purchase_way")
    private String suggestPurchaseWay;

    /**
     * 采购内容
     */
    @ApiModelProperty(value = "采购内容")
    @TableField(value = "purchase_content")
    private String purchaseContent;

    /**
     * 推荐供应商名单
     */
    @ApiModelProperty(value = "推荐供应商名单")
    @TableField(value = "rec_sup_list")
    private String recSupList;

    /**
     * 推荐潜在供应商名单
     */
    @ApiModelProperty(value = "推荐潜在供应商名单")
    @TableField(value = "rec_pt_sup_list")
    private String recPtSupList;

    /**
     * 是否有匹配的框架合同
     */
    @ApiModelProperty(value = "是否有匹配的框架合同")
    @TableField(value = "is_frame_contrac")
    private String isFrameContrac;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 框架合同编码
     */
    @ApiModelProperty(value = "框架合同编码")
    @TableField(value = "contract_id")
    private String contractId;


    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "business_leader")
    private String businessLeader;


    @ApiModelProperty(value = "采购申请发起时间")
    @TableField(value = "purchase_request_init_time")
    private Date purchaseRequestInitTime;

    @ApiModelProperty(value = "是否ECP建档 0 否 1 是")
    @TableField(value = "is_ecp_record")
    private Boolean isEcpRecord;

}
