ALTER TABLE pmsx_invoicing_revenue_accounting CHANGE `text` con_text varchar(255) ;
ALTER TABLE pmsx_invoicing_revenue_accounting CHANGE mile_stone_name milestone_name varchar(255) ;
ALTER TABLE pmsx_invoicing_revenue_accounting ADD `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑id';

ALTER TABLE pmsx_advance_payment_invoiced CHANGE `text` con_text varchar(255) ;
ALTER TABLE pmsx_advance_payment_invoiced CHANGE mile_stone_name milestone_name varchar(255) ;
ALTER TABLE pmsx_advance_payment_invoiced ADD `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑id';


ALTER TABLE pmsx_provisional_income_accounting CHANGE `text` con_text varchar(255) ;
ALTER TABLE pmsx_provisional_income_accounting CHANGE mile_stone_name milestone_name varchar(255) ;
ALTER TABLE pmsx_provisional_income_accounting ADD `milestone_id` varchar(64) DEFAULT NULL COMMENT '里程碑id';

