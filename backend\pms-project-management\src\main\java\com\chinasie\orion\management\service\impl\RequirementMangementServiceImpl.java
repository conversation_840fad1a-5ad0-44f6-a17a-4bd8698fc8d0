package com.chinasie.orion.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.enums.TodoMessageTypeEnum;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.bo.UserDeptBo;
import com.chinasie.orion.constant.MarketContractBusinessTypeEnums;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.RequirementExcelExportDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.constant.*;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.RequirementManageCustContactVO;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.management.repository.*;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.DeptLeaderRelationVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.*;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * RequirementMangement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@Service
@Slf4j
public class RequirementMangementServiceImpl extends OrionBaseServiceImpl<RequirementMangementMapper, RequirementMangement> implements RequirementMangementService {


    @Autowired
    private DocumentService documentService;
    @Autowired
    private ReqDetailService reqDetailService;
    @Autowired
    private ReqDetailMapper reqDetailMapper;
    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    protected PmsMQProducer mqProducer;

    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserBaseApiService userBaseApiService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private CustomerContactService customerContactService;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Autowired
    private CustomerContactMapper customerContactMapper;
    @Autowired
    private ProcurePkgGroupService procurePkgGroupService;
    @Autowired
    private ReqClarificationRecordMapper reqClarificationRecordMapper;
    @Autowired
    private FileApiService fileApi;
    @Autowired
    private LockTemplate lockTemplate;
    @Autowired
    private DictRedisHelper dicthelper;
    @Autowired
    private SysCodeApi sysCodeApi;
    @Autowired
    private RequirementPayMangementService payMangementService;
    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Autowired
    private RequirementMangementMapper requirementMangementMapper;
    @Autowired
    private DeptLeaderHelper deptLeaderHelper;
    @Autowired
    private RequirementManageCustContactService requirementManageCustContactService;

    @Autowired
    RequirementsManagementLogsService requirementsManagementLogsService;

    @Autowired
    CurrentUserHelper currentUserHelper;


    @Autowired
    private RoleUserHelper roleUserHelper;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private DeptUserHelper deptUserHelper;

    @Autowired
    private DeptUserDOMapper deptUserDOMapper;
    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private RoleRedisHelper roleRedisHelper;
    private final Logger logger = LoggerFactory.getLogger(RequirementMangementServiceImpl.class);

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Resource
    private MessageCenterApi messageCenterApi;

    @Autowired
    private UserDeptBo userDeptBo;

    @Autowired
    private DataStatusNBO dataStatusBO;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RequirementMangementVO detail(String id, String pageCode) throws Exception {
        RequirementMangement requirementMangement = this.getById(id);
        RequirementMangementVO result = BeanCopyUtils.convertTo(requirementMangement, RequirementMangementVO::new);
        setEveryName(Collections.singletonList(result));
        LambdaQueryWrapper<ReqDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ReqDetail::getReqId, id);
        List<ReqDetail> reqDetails = reqDetailMapper.selectList(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(reqDetails)) {
            result.setProjectDetail(reqDetails.get(0).getContext());
        }
        List<FileVO> fileVOList = fileApi.getFilesByDataId(id);
        //文件上传人
        Map<String, String> collectFileUserMap=new HashMap<>();
        List<String> collectFileUser = fileVOList.stream().map(FileVO::getCreatorId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(collectFileUser)){
            List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(collectFileUser);
            collectFileUserMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        }
        for (FileVO fileVO : fileVOList) {
            fileVO.setCreatorName(collectFileUserMap.getOrDefault(fileVO.getCreatorId(),""));
        }
        result.setFileList(fileVOList);
        List<String> userIds = new ArrayList<>();
        userIds.add(requirementMangement.getOfficeLeader());
        userIds.add(requirementMangement.getBusinessPerson());
        userIds.add(requirementMangement.getTechRes());
        userIds.add(requirementMangement.getApplicantUser());
        userIds.add(requirementMangement.getCreatorId());
        userIds.add(requirementMangement.getModifyId());

        userIds = userIds.stream().distinct().filter(item -> StrUtil.isNotBlank(item)).collect(Collectors.toList());
        List<SimpleUserVO> userVOS = userBaseApiService.getUserByIds(userIds);
        if (CollUtil.isNotEmpty(userVOS)) {
            Map<String, String> userMap = userVOS.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));
            if (StrUtil.isNotBlank(requirementMangement.getOfficeLeader())) {
                result.setOfficeLeaderName(userMap.get(requirementMangement.getOfficeLeader()));
            }
            if (StrUtil.isNotBlank(requirementMangement.getBusinessPerson())) {
                result.setBusinessPersonName(userMap.get(requirementMangement.getBusinessPerson()));
            }
            if (StrUtil.isNotBlank(requirementMangement.getTechRes())) {
                result.setTechResName(userMap.get(requirementMangement.getTechRes()));
            }
            if (StrUtil.isNotBlank(requirementMangement.getApplicantUser())) {
                result.setApplicantUserName(userMap.get(requirementMangement.getApplicantUser()));
            }
            if(StrUtil.isNotBlank(requirementMangement.getCreatorId())) {
                result.setCreatorName(userMap.get(requirementMangement.getCreatorId()));
            }
            if(StrUtil.isNotBlank(requirementMangement.getModifyId())) {
                result.setModifyName(userMap.get(requirementMangement.getModifyId()));
            }
        }
        List<String> deptIds = new ArrayList<>();
        deptIds.add(requirementMangement.getReqOwnership());
        deptIds.add(requirementMangement.getApplicantDept());
        deptIds = deptIds.stream().distinct().filter(item -> StrUtil.isNotBlank(item)).collect(Collectors.toList());
        List<DeptVO> deptVOS = deptRedisHelper.getDeptByIds(deptIds);
        if (CollUtil.isNotEmpty(deptVOS)) {
            Map<String, String> deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));

            if (StrUtil.isNotBlank(result.getReqOwnership())) {
                result.setReqOwnershipName(deptMap.get(requirementMangement.getReqOwnership()));
            }
            if (StrUtil.isNotBlank(result.getApplicantDept())) {
                result.setApplicantDeptName(deptMap.get(result.getApplicantDept()));
            }
        }


        //获取客户名称
        if (StringUtils.hasText(requirementMangement.getCustPerson())) {
            CustomerInfo customerInfo = customerInfoService.getById(requirementMangement.getCustPerson());
            if (customerInfo != null) {
                result.setCustPersonName(customerInfo.getCusName());
                result.setIsPerson(customerInfo.getIsPerson());
                result.setSalesClassification(customerInfo.getYwsrlx() + customerInfo.getIndustry());
                result.setYwsrlx(customerInfo.getYwsrlx());
                result.setIndustry(customerInfo.getIndustry());
                result.setBusScope(customerInfo.getBusScope());
                result.setGroupInOut(customerInfo.getGroupInOut());
                result.setYwsrlxName(IncomeTypeEnum.getDesc(customerInfo.getYwsrlx()));
                result.setCusLevelName(CustomerLevelEnum.getDesc(customerInfo.getCusLevel()));
                result.setBusScopeName(CustomerScopeEnum.getDesc(customerInfo.getBusScope()));
                result.setCusStatusName(CustomerStatusEnum.getDesc(customerInfo.getCusStatus()));
                result.setIndustryName(CustomerIndustryEnum.getDesc(customerInfo.getIndustry()));
                result.setGroupInOutName(CustomerRelationshipEnum.getDesc(customerInfo.getGroupInOut()));
            }
        }

        if (StringUtils.hasText(requirementMangement.getCooperatePerson())) {
            List<String> cooperatePerson = Arrays.stream(requirementMangement.getCooperatePerson().split(",")).collect(Collectors.toList());
            result.setCooperatePerson(cooperatePerson);
            if (!CollectionUtils.isEmpty(cooperatePerson)) {
                List<UserVO> users = userRedisHelper.getUserByIds(cooperatePerson);
                List<String> userNames = users.stream().map(UserVO::getName).collect(Collectors.toList());
                result.setCooperatePersonName(userNames);
            }
        }

        if (StringUtils.hasText(requirementMangement.getCooperateDpt())) {
            List<String> cooperateDpt = Arrays.stream(requirementMangement.getCooperateDpt().split(",")).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(cooperateDpt)) {
                LinkedHashSet<String> linkedHashSet = new LinkedHashSet<>(cooperateDpt);
                cooperateDpt = new ArrayList<>(linkedHashSet);
            }
            result.setCooperateDpt(cooperateDpt);
            if (!CollectionUtils.isEmpty(cooperateDpt)) {
                List<DeptVO> depts = deptRedisHelper.getDeptByIds(cooperateDpt);
                List<String> deptNames = depts.stream().map(DeptVO::getName).collect(Collectors.toList());
                LinkedHashSet<String> linkedHashSet = new LinkedHashSet<>(deptNames);
                deptNames = new ArrayList<>(linkedHashSet);
                result.setCooperateDptName(deptNames);
            }
        }


        if (StringUtils.hasText(requirementMangement.getBusinessType())) {
            DictValueVO dictValueVO = dicthelper.getDictValueInfoByCode(requirementMangement.getBusinessType());
            if (dictValueVO != null) {
                result.setBusinessTypeName(dictValueVO.getDescription());
            }
        }

        result.setResSourceName(RequirementResouceEnum.getDesc(result.getResSource()));
        LambdaQueryWrapper<ProcurePkgGroup> procurePkgGroupQueryWrapper = new LambdaQueryWrapper<>();
        //项目包组数据
        procurePkgGroupQueryWrapper.eq(ProcurePkgGroup::getRequirementId, result.getId());
        List<ProcurePkgGroup> procurePkgGroupList = procurePkgGroupService.list(procurePkgGroupQueryWrapper);
        result.setProcurePkgGroupList(procurePkgGroupList);
        //需求澄清记录
        LambdaQueryWrapper<ReqClarificationRecord> reqClarificationRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        reqClarificationRecordLambdaQueryWrapper.eq(ReqClarificationRecord::getRequirementId, result.getId());
        List<ReqClarificationRecord> reqClarificationRecords = reqClarificationRecordMapper.selectList(reqClarificationRecordLambdaQueryWrapper);
        result.setReqClarificationRecordList(reqClarificationRecords);
        //保证金信息

        if (StringUtils.hasText(result.getResSource()) &&
                (result.getResSourceName().equals(RequirementResouceEnum.ECP_OPEN_TE.getDesc()) ||
                        result.getResSourceName().equals(RequirementResouceEnum.ECP_ENQ.getDesc())) &&
                StringUtils.hasText(result.getRequirementNumber())) {
            LambdaQueryWrapper<RequirementPayMangement> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RequirementPayMangement::getRequirementNumber, result.getRequirementNumber());
            RequirementPayMangement payMangement = payMangementService.getOne(wrapper);
            result.setRequirementPayMangement(payMangement);
        }

        final LambdaQueryWrapper<RequirementManageCustContact> contactQuery = new LambdaQueryWrapper<>();
        contactQuery.eq(RequirementManageCustContact::getRequirementId, result.getId());
        final List<RequirementManageCustContact> contacts = requirementManageCustContactService.list(contactQuery);
        if (!CollectionUtils.isEmpty(contacts)) {
            final List<RequirementManageCustContactVO> contactVos =
                    BeanCopyUtils.convertListTo(contacts, RequirementManageCustContactVO::new);
            contactVos.forEach(e -> {
                e.setContactTypeName(CustContactTypeEnum.getDesc(e.getContactType()));
            });
            result.setCustContacts(contactVos);
        }


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param requirementMangementDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(RequirementMangementDTO requirementMangementDTO) throws Exception {
        RequirementMangement requirementMangement = BeanCopyUtils.convertTo(requirementMangementDTO, RequirementMangement::new);
        String userId = "";
        //要把所级负责人也加上 取值逻辑是单据技术负责人的所级领导
        String techRes = requirementMangement.getTechRes();
        List<String> deptByUserId = deptUserHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), techRes);
        logger.info("deptByUserId的值：" + deptByUserId);
        if (ObjectUtil.isNotEmpty(deptByUserId)) {
            String deptId = deptByUserId.get(0);
            logger.info("deptId的值：" + deptId);
            List<DeptLeaderRelationVO> deptLeaderRelationVOS = deptLeaderHelper.getDeptLeaderRelationByDeptId(CurrentUserHelper.getOrgId(), deptId);
            logger.info("deptLeaderRelationVOS的值：" + deptLeaderRelationVOS.toString());
            for (DeptLeaderRelationVO deptLeaderRelationVO : deptLeaderRelationVOS) {
                String type = deptLeaderRelationVO.getType();
                if ("main".equals(type)) {
                    userId = deptLeaderRelationVO.getUserId();
                    break;
                }
            }
        }
        requirementMangement.setOfficeLeader(userId);

        requirementMangement.setProjectStatus(RequirementManagementStatusEnum.TO_BE_DISTRIBUTED.getStatus().toString());

        if (!CollectionUtils.isEmpty(requirementMangementDTO.getCooperatePerson())) {
            requirementMangement.setCooperatePerson(String.join(",", requirementMangementDTO.getCooperatePerson()));
        }
        if (!CollectionUtils.isEmpty(requirementMangementDTO.getCooperateDpt())) {
            requirementMangement.setCooperateDpt(String.join(",", requirementMangementDTO.getCooperateDpt()));
        }

        final List<RequirementManageCustContact> contacts = requirementMangementDTO.getCustContacts();

        if (null == requirementMangement.getId() || !StringUtils.hasText(requirementMangement.getId())) {
            //获取分布式锁
            final LockInfo lock = lockTemplate.lock("getNumberManageLock", 10000L, 3000L, RedisTemplateLockExecutor.class);
            if (lock == null) {
                throw new RuntimeException("业务处理中,请稍后再试");
            }
            if (StringUtils.hasText(requirementMangementDTO.getRequirementNumber())) {
                //获取到锁，开始执行业务
                LambdaQueryWrapperX<RequirementMangement> wrapperX = new LambdaQueryWrapperX<>(RequirementMangement.class);
                wrapperX.eq(RequirementMangement::getRequirementNumber, requirementMangementDTO.getRequirementNumber());
                wrapperX.select(RequirementMangement::getRequirementNumber);
                List<RequirementMangement> requirementMangements = this.list(wrapperX);
                if (!CollectionUtils.isEmpty(requirementMangements)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
                }
            }
            try {
                if (!StringUtils.hasText(requirementMangementDTO.getRequirementNumber())) {
                    requirementMangement.setRequirementNumber(sysCodeApi.rulesAndSegmentCreate("RequirementMangement",
                            "requirementNumber", false, "").getResult());
                }
                this.save(requirementMangement);
            } catch (Exception e) {
                throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
            } finally {
                lockTemplate.releaseLock(lock);
            }
            //新增 如果custPerson被使用了，在表里增加标识
            String custPerson = requirementMangementDTO.getCustPerson();
            if (ObjectUtil.isNotEmpty(custPerson)) {
                CustomerInfo customerInfo = customerInfoService.getById(custPerson);
                if (ObjectUtil.isNotEmpty(customerInfo)) {
                    customerInfo.setIsUsed("1");
                    customerInfoService.updateById(customerInfo);
                }
            }
            String rsp = requirementMangement.getId();
            requirementManageCustContactService.saveRequirementContracts(contacts, requirementMangement);

            //富文本内容单独存表，附件文件修改id
            List<FileDTO> fileInfoDTOList = requirementMangementDTO.getFileList();
            if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
                fileInfoDTOList.forEach(p -> {
                    p.setOrgId(requirementMangementDTO.getOrgId());
                    p.setDataId(rsp);
                });
                List<FileDTO> insertInfoDTOList = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());

                List<FileDTO> updateInfoDTOList = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                if (!com.chinasie.orion.util.CollectionUtils.isBlank(insertInfoDTOList)) {
                    fileApi.batchSaveFile(fileInfoDTOList);
                }

                if (!com.chinasie.orion.util.CollectionUtils.isBlank(updateInfoDTOList)) {
                    List<FileDTO> updateFileDtoList = BeanCopyUtils.convertListTo(updateInfoDTOList, FileDTO::new);
                    documentService.updateBatchDocument(updateFileDtoList);
                }

            }

            if (StringUtils.hasText(requirementMangementDTO.getProjectDetail())) {
                ReqDetail reqDetail = new ReqDetail();
                BeanCopyUtils.copyProperties(requirementMangementDTO, reqDetail);
                reqDetail.setContext(requirementMangementDTO.getProjectDetail());
                reqDetail.setOrgId(requirementMangement.getOrgId());
                reqDetail.setReqId(rsp);
                reqDetailService.save(reqDetail);
            }
        } else {
            this.updateById(requirementMangement);
            // 编辑的时候，才去更新客户联系人
            if (RequirementManagementStatusEnum.TO_BE_DISTRIBUTED.getStatus().equals(requirementMangementDTO.getStatus())) {
                requirementManageCustContactService.saveRequirementContracts(contacts, requirementMangement);
            }

            String rsp = requirementMangement.getId();

            //编辑附件
            List<FileDTO> fileDTOList = requirementMangementDTO.getFileList();
            List<FileVO> existFileList = fileApi.getFilesByDataId(requirementMangementDTO.getId());
            // 优先移除
            if (Objects.nonNull(existFileList)) {
                // existFileList 中不包含 fileDTOList的删除
                List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileDTOList.stream()
                                .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filesIds)) {
                    fileApi.removeBatchByIds(filesIds);
                }
            }
            // 批量新增
            if (!CollectionUtils.isEmpty(fileDTOList)) {
                fileDTOList.forEach(item -> {
                    item.setDataId(requirementMangementDTO.getId());
                    item.setDataType("RequirementMangement");
                });
                fileApi.batchSaveFile(fileDTOList);
            }


            LambdaQueryWrapper<ReqDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ReqDetail::getReqId, requirementMangementDTO.getId());
            List<ReqDetail> reqDetails = reqDetailMapper.selectList(lambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(reqDetails) && StringUtils.hasText(requirementMangementDTO.getProjectDetail())) {
                ReqDetail reqDetail = reqDetails.get(0);
                reqDetail.setContext(requirementMangementDTO.getProjectDetail());
                reqDetailService.updateById(reqDetail);
            } else if (CollectionUtils.isEmpty(reqDetails) && StringUtils.hasText(requirementMangementDTO.getProjectDetail())) {
                ReqDetail reqDetail = new ReqDetail();
                reqDetail.setContext(requirementMangementDTO.getProjectDetail());
                reqDetail.setOrgId(requirementMangementDTO.getOrgId());
                reqDetail.setReqId(rsp);
                reqDetailService.save(reqDetail);
            }
        }

        // 发送消息通知
        sendMessage(requirementMangement.getId(),
                "/pas/MarketDemandManagementDetails/" + requirementMangement.getId(),
                "收到新需求：" + requirementMangement.getRequirementName() + "，请确认分发;",
                List.of(requirementMangement.getBusinessPerson()),
                requirementMangement.getPlatformId(),
                requirementMangement.getOrgId(),
                RequirementMscNodeEnum.NODE_REQUIREMENT_ADD.getCode()
        );
        return requirementMangement.getId();
    }

    /**
     * 需求单分发
     *
     * @param requirementManagementDTO 需求单内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distribute(RequirementMangementDTO requirementManagementDTO) {
        String custPerson = requirementManagementDTO.getCustPerson();
        CustomerInfo customerInfo = customerInfoService.getById(custPerson);
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该需求的客户不存在，请重新选择客户");
        }
        final RequirementMangement requirement = BeanCopyUtils.convertTo(requirementManagementDTO, RequirementMangement::new);
        if (!CollectionUtils.isEmpty(requirementManagementDTO.getCooperatePerson())) {
            requirement.setCooperatePerson(String.join(",", requirementManagementDTO.getCooperatePerson()));
        }
        if (!CollectionUtils.isEmpty(requirementManagementDTO.getCooperateDpt())) {
            requirement.setCooperateDpt(String.join(",", requirementManagementDTO.getCooperateDpt()));
        }

        final RequirementMangement check = this.getById(requirement.getId());
        if (null == check) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "参数错误，未找到数据");
        }
        requirement.setDistributeTime(new Date());
        requirement.setStatus(RequirementManagementStatusEnum.TO_BE_DECIDED.getStatus());
        recordDistributeLogging(requirementManagementDTO);


        String userId = "";
        //要把所级负责人也加上 取值逻辑是单据技术负责人的所级领导
        String techRes = requirement.getTechRes();
        List<String> deptByUserId = deptUserHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), techRes);
        logger.info("deptByUserId的值：" + deptByUserId);
        if (ObjectUtil.isNotEmpty(deptByUserId)) {
            String deptId = deptByUserId.get(0);
            logger.info("deptId的值：" + deptId);
            List<DeptLeaderRelationVO> deptLeaderRelationVOS = deptLeaderHelper.getDeptLeaderRelationByDeptId(CurrentUserHelper.getOrgId(), deptId);
            logger.info("deptLeaderRelationVOS的值：" + deptLeaderRelationVOS.toString());
            for (DeptLeaderRelationVO deptLeaderRelationVO : deptLeaderRelationVOS) {
                String type = deptLeaderRelationVO.getType();
                if ("main".equals(type)) {
                    userId = deptLeaderRelationVO.getUserId();
                    break;
                }
            }
        }
        requirement.setOfficeLeader(userId);

        this.updateById(requirement);
    }

    /**
     * 发送消息通知
     */
    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String platformId, String orgId, String code) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$desc$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    /**
     * 需求单分发确认
     *
     * @param requirementMangementDTO 需求单内容
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributeConfirm(RequirementMangementDTO requirementMangementDTO) {
        final String userId = CurrentUserHelper.getCurrentUserId();
        final String orgId = CurrentUserHelper.getOrgId();

        RequirementMangement requirement = this.baseMapper.selectById(requirementMangementDTO.getId());
        if (!Objects.equals(requirement.getStatus(), RequirementManagementStatusEnum.TO_BE_DECIDED.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅待确认状态可以做此操作");
        }

        String officeLeader = "";
        //要把所级负责人也加上 取值逻辑是单据技术负责人的所级领导
        String techRes = requirement.getTechRes();
        List<String> deptByUserId = deptUserHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), techRes);
        logger.info("deptByUserId的值：" + deptByUserId);
        if (ObjectUtil.isNotEmpty(deptByUserId)) {
            String deptId = deptByUserId.get(0);
            logger.info("deptId的值：" + deptId);
            List<DeptLeaderRelationVO> deptLeaderRelationVOS = deptLeaderHelper.getDeptLeaderRelationByDeptId(CurrentUserHelper.getOrgId(), deptId);
            logger.info("deptLeaderRelationVOS的值：" + deptLeaderRelationVOS.toString());
            for (DeptLeaderRelationVO deptLeaderRelationVO : deptLeaderRelationVOS) {
                String type = deptLeaderRelationVO.getType();
                if ("main".equals(type)) {
                    officeLeader = deptLeaderRelationVO.getUserId();
                    break;
                }
            }
        }


        final LambdaUpdateWrapper<RequirementMangement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RequirementMangement::getId, requirementMangementDTO.getId())
                .set(RequirementMangement::getConfirmRemark, requirementMangementDTO.getConfirmRemark())
                .set(RequirementMangement::getReqOwnership, requirementMangementDTO.getReqOwnership())
                .set(RequirementMangement::getStatus, requirementMangementDTO.getStatus())
                .set(RequirementMangement::getOfficeLeader, officeLeader)
                .set(RequirementMangement::getTechRes, requirementMangementDTO.getTechRes());
        this.update(updateWrapper);

        //清除对应分发给商务接口人的待办
        ResponseDTO<?> dto = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                .userId(CurrentUserHelper.getCurrentUserId())
                .businessId(requirementMangementDTO.getId())
                .build());
        logger.info("消除待办参数：{}：", dto);

        // 如果确认的时候，选了拒绝，那么就需要发送通知，收件人为该用户的直属领导及20级部门的主管领导
        if (Objects.equals(requirementMangementDTO.getStatus(), RequirementManagementStatusEnum.NO_RESPONSE.getStatus())) {
            final SimpleUser user = userRedisHelper.getSimplerUser(orgId, userId);
            final List<String> toUser = Lists.newArrayList();

            // 直属部门领导id
            List<DeptLeaderRelationVO> deptLeads = deptLeaderHelper
                    .getDeptLeaderRelationByDeptId(orgId, user.getDeptId());
            toUser.addAll(deptLeads.stream().map(DeptLeaderRelationVO::getUserId).collect(Collectors.toList()));
            // 查找所属20部门的主管领导
            List<DeptLeaderRelationVO> orgLeads = deptLeaderHelper
                    .getDeptLeaderRelationByDeptId(orgId, user.getOrgId());
            toUser.addAll(orgLeads.stream().map(DeptLeaderRelationVO::getUserId).collect(Collectors.toList()));

            final Map<String, Object> params = new HashMap<>();
            params.put("$name$", requirement.getRequirementName());
            params.put("$userName$", user.getName());
            params.put("$desc$", requirementMangementDTO.getConfirmRemark());

            String id = requirement.getId();
            String url = "/pas/MarketDemandManagementDetails/" + id + "?query=" + new Date().getTime();
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData("")
                    .businessId("RequirementMangement_" + id)
                    .todoStatus(0)
                    .todoType(TodoMessageTypeEnum.MESSAGE.getType())
                    .urgencyLevel(0)
                    .businessNodeCode(RequirementMscNodeEnum.PMS_REQUIREMENT_CONFIRM.getCode())
                    .titleMap(params)
                    .messageMap(params)
                    .messageUrl(String.format(url, id))
                    .messageUrlName("需求分发确认通知")
                    .businessTypeCode(RequirementMangement.class.getSimpleName())
                    .businessTypeName("需求管理")
                    .recipientIdList(toUser.stream().distinct().collect(Collectors.toList()))
                    .senderTime(new Date())
                    .senderId(userId)
                    .platformId(requirement.getPlatformId())
                    .orgId(requirement.getOrgId())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        }
        recordConfirmLogging(requirementMangementDTO);
    }

    /**
     * 需求单作废
     *
     * @param requirement 需求单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(RequirementMangement requirement) {
        RequirementMangement check = this.baseMapper.selectById(requirement.getId());
        if (null == check) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未获取需求单数据");
        }
        if (!Objects.equals(check.getStatus(), RequirementManagementStatusEnum.TO_BE_DECIDED.getStatus())
                && !Objects.equals(check.getStatus(), RequirementManagementStatusEnum.NO_RESPONSE.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "仅不响应和待确定状态可以作废");
        }

        final LambdaUpdateWrapper<RequirementMangement> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RequirementMangement::getId, requirement.getId())
                .set(RequirementMangement::getStatus, RequirementManagementStatusEnum.CANCELED.getStatus());
        this.update(updateWrapper);
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<RequirementMangement> requirementMangements = this.baseMapper.selectBatchIds(ids);
        Map<Integer, List<String>> collect = requirementMangements.stream()
                .collect(Collectors.groupingBy(RequirementMangement::getStatus,
                        Collectors.mapping(RequirementMangement::getId, Collectors.toList())));
        List<String> confirm = collect.get(RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus());
        if (!CollectionUtils.isEmpty(confirm)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "已确认需求不能被删除");
        }

        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequirementMangementVO> pages(Page<RequirementMangementDTO> pageRequest) throws Exception {
        StopWatch watch = new StopWatch();
        watch.start();
        LambdaQueryWrapperX<RequirementMangement> condition = new LambdaQueryWrapperX<>(RequirementMangement.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        RequirementMangementDTO query = pageRequest.getQuery();
        if (query != null) {
            if (query.getRequirementType() != null) {
                if ("1".equals(query.getRequirementType())) {
                    condition.and(wq -> wq.isNull(RequirementMangement::getEcpStatus).or().ne(RequirementMangement::getEcpStatus, "可报名项目"));
                    if (ObjectUtil.isNotEmpty(query.getRequirementNumber())) {
                        condition.and(item -> item.like(RequirementMangement::getRequirementName, query.getRequirementName())
                                .or().like(RequirementMangement::getRequirementNumber, query.getRequirementNumber())
                                .or().like(RequirementMangement::getCustPersonName, query.getCustPersonName())
                                .or().like(RequirementMangement::getTechResName, query.getTechResName())
                                .or().like(RequirementMangement::getBusinessPersonName, query.getBusinessPersonName()));
                    }
                    condition.and(item -> item.notIn(RequirementMangement::getResSource, Arrays.asList("ECP_open_te", "ECP_enq"))
                            .or().ge(RequirementMangement::getSignEndTime, LocalDate.of(2025, 1, 1)).or().isNull(RequirementMangement::getSignEndTime));
                } else {
                    condition.eq(RequirementMangement::getEcpStatus, "可报名项目");
                    if (ObjectUtil.isNotEmpty(query.getRequirementNumber())) {
                        condition.and(item -> item.like(RequirementMangement::getRequirementName, query.getRequirementName())
                                .or().like(RequirementMangement::getRequirementNumber, query.getRequirementNumber()));
                    }
                    condition.and(item -> item.notIn(RequirementMangement::getResSource, Arrays.asList("ECP_open_te", "ECP_enq"))
                            .or().ge(RequirementMangement::getSignEndTime, LocalDate.of(2025, 1, 1)).or().isNull(RequirementMangement::getSignEndTime));
                }
            }

            if (null != query.getFilterYear()) {
                LocalDate startTime = YearMonth.of(query.getFilterYear(), 1).atDay(1);
                LocalDate endTime = YearMonth.of(query.getFilterYear(), 12).atEndOfMonth();
                condition.between(RequirementMangement::getCreateTime, startTime, endTime);
            }
            if (ObjectUtil.isNotEmpty(query.getPriority())) {
                condition.eq(RequirementMangement::getPriority, query.getPriority());
            }
            if (ObjectUtil.isNotEmpty(query.getPrioritySort())) {
                if ("0".equals(query.getPrioritySort())) {
                    condition.orderByAsc(RequirementMangement::getPriority);
                } else if ("1".equals(query.getPrioritySort())) {
                    condition.orderByDesc(RequirementMangement::getPriority);
                }
            }
        }
        condition.orderByDesc(RequirementMangement::getCreateTime);
        condition.orderByDesc(RequirementMangement::getSignDeadlnTime);


        Page<RequirementMangement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RequirementMangement::new));

        IPage<RequirementMangement> mPage = MyBatisUtils.buildPage(realPageRequest);

        List<RequirementMangementVO> vos;
        Page<RequirementMangementVO> pageResult;
        if (Objects.nonNull(query) && "1".equals(query.getRequirementType())) {
            //需求列表
            IPage<RequirementMangement> requirementMangementIPage = requirementMangementMapper.selectDataPermissionPage(mPage, RequirementMangement.class, condition);
            pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), requirementMangementIPage.getTotal());
            vos = BeanCopyUtils.convertListTo(requirementMangementIPage.getRecords(), RequirementMangementVO::new);
        } else {
            //可参与项目
            PageResult<RequirementMangement> page = this.getBaseMapper().selectPage(realPageRequest, condition);

            pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
            vos = BeanCopyUtils.convertListTo(page.getContent(), RequirementMangementVO::new);
        }
        watch.stop();
        log.info("===耗时==[分页查询]耗时 {}=====", watch.getLastTaskTimeMillis());
        watch.start();
        List<String> userIds = vos.stream()
                .flatMap(item -> Stream.of(
                        item.getBusinessPerson(),
                        item.getTechRes(),
                        item.getApplicantUser(),
                        item.getCreatorId(),
                        item.getModifyId()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<String> custConPersonIds = vos.stream()
                .map(RequirementMangementVO::getCustPerson)
                .distinct()
                .collect(Collectors.toList());
        List<String> dptIds = vos.stream()
                .flatMap(item -> Stream.of(
                        item.getReqOwnership(),
                        item.getApplicantDept()
                ))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
//        List<String> userIds = vos.stream().map(RequirementMangementVO::getBusinessPerson).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
//        List<String> dptIds = vos.stream().map(RequirementMangementVO::getReqOwnership).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
//        List<String> custConPersonIds = vos.stream().map(RequirementMangementVO::getCustPerson).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());

        List<String> custConPersonIdList = vos.stream().map(RequirementMangementVO::getCustConPerson).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<CustomerContact> customerContactList;
        if (!CollectionUtils.isEmpty(custConPersonIdList)) {
            customerContactList = customerContactService.list(new LambdaQueryWrapperX<>(CustomerContact.class).in(CustomerContact::getId, custConPersonIdList).select(CustomerContact::getId, CustomerContact::getName));
        } else {
            customerContactList = new ArrayList<>();
        }
        Map<String, CustomerInfo> customerInfoMap;
        if (!CollectionUtils.isEmpty(custConPersonIds)) {
            List<CustomerInfo> customerInfos = customerInfoMapper.selectBatchIds(custConPersonIds);
            customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, c -> c));

        } else {
            customerInfoMap = Map.of();
        }
        // Map<String, SimpleUser> users = userRedisHelper.getSimpleUserMapByUserIds(userIds);
        Map<String, String> userMap;
        if (!CollectionUtils.isEmpty(userIds)) {
            List<UserBaseCacheVO> users = userRedisHelper.getUserBaseCacheByIds(userIds);
            userMap = users.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        } else {
            userMap = Map.of();
        }

        List<DeptVO> depts = deptRedisHelper.getDeptByIds(dptIds);
        Map<String, String> deptMap = depts.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        Map<String, String> customerContactMap = customerContactList.stream().collect(Collectors.toMap(CustomerContact::getId, CustomerContact::getName));
        watch.stop();
        log.info("===耗时==[分页查询2]耗时 {}=====", watch.getLastTaskTimeMillis());

        watch.start();
        vos.forEach(vo -> {
            if (StringUtils.hasText(vo.getBusinessPerson())) {
                vo.setBusinessPerson(userMap.get(vo.getBusinessPerson()));
            }
            if (StringUtils.hasText(vo.getTechRes())) {
                vo.setTechResName(userMap.get(vo.getTechRes()));
            }
            if (StringUtils.hasText(vo.getCreatorId())) {
                vo.setCreatorName(userMap.get(vo.getCreatorId()));
            }
            if (StringUtils.hasText(vo.getModifyId())) {
                vo.setModifyName(userMap.get(vo.getModifyId()));
            }
            if (StringUtils.hasText(vo.getApplicantUser())) {
                vo.setApplicantUser(userMap.get(vo.getApplicantUser()));
            }
            if (StringUtils.hasText(vo.getCustConPerson())) {
                vo.setCustConPersonName(customerContactMap.get(vo.getCustConPerson()));
            }
            if (StringUtils.hasText(vo.getCustPerson()) && customerInfoMap.containsKey(vo.getCustPerson())) {
                vo.setCustPersonName(customerInfoMap.get(vo.getCustPerson()).getCusName());
            }
            if (StringUtils.hasText(vo.getReqOwnership())) {
                vo.setReqOwnershipName(deptMap.get(vo.getReqOwnership()));
            }
            if (StringUtils.hasText(vo.getApplicantDept())) {
                vo.setApplicantDeptName(deptMap.get(vo.getApplicantDept()));
            }
            vo.setResSourceName(RequirementResouceEnum.getDesc(vo.getResSource()));
            vo.setYwsrlxName(IncomeTypeEnum.getDesc(vo.getYwsrlx()));
            vo.setBusScopeName(CustomerScopeEnum.getDesc(vo.getBusScope()));
            vo.setGroupInOutName(CustomerRelationshipEnum.getDesc(vo.getGroupInOut()));
            vo.setIndustryName(CustomerIndustryEnum.getDesc(vo.getIndustry()));
        });
        watch.stop();
        log.info("===耗时==[分页查询3]耗时 {}=====", watch.getLastTaskTimeMillis());
        watch.start();
        setEveryName(vos);
        watch.stop();
        log.info("===耗时==[分页查询4]耗时 {}=====", watch.getLastTaskTimeMillis());
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<RequirementMangementVO> vos) throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        StopWatch watch = new StopWatch();
        watch.start();
        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<String> centerRoleIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(centerRoleDOS)) {
            centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
        }
        watch.stop();
        log.info("===耗时==[分页查询5]耗时 {}=====", watch.getLastTaskTimeMillis());
        long startTime = System.currentTimeMillis();
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> existRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
        }
        List<String> ids = vos.stream().map(RequirementMangementVO::getId).collect(Collectors.toList());
        log.info("===耗时==[分页查询6.]耗时 {}=====", System.currentTimeMillis() - startTime);
        long startTime2 = System.currentTimeMillis();
        List<String> finalExistRoleIds = existRoleIds;
        UserPartTimeVO userPartTimeVO = userDeptBo.getUserDeptInfo(CurrentUserHelper.getCurrentUserId());
        // SimpleUser simplerUser = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgId = userPartTimeVO.getOrgId();//当前登陆人的20级部门
        log.info("===耗时==[分页查询6.2]耗时 {}=====", System.currentTimeMillis() - startTime2);
        watch.start();
        LambdaQueryWrapper<RequirementsManagementLogs> requirementsManagementLogsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        requirementsManagementLogsLambdaQueryWrapper.select(RequirementsManagementLogs::getCreatorId, RequirementsManagementLogs::getReqiurementsId);
        if (ObjectUtil.isNotEmpty(ids)) {
            requirementsManagementLogsLambdaQueryWrapper.in(RequirementsManagementLogs::getReqiurementsId, ids);
        }
        List<RequirementsManagementLogs> logs = requirementsManagementLogsService.list(requirementsManagementLogsLambdaQueryWrapper);
        watch.stop();
        log.info("===耗时==[分页查询7]耗时 {}=====", watch.getLastTaskTimeMillis());
        long startTime1 = System.currentTimeMillis();
        Map<String, List<RequirementsManagementLogs>> requirementsManagementLogsMap = logs.stream().collect(Collectors.groupingBy(RequirementsManagementLogs::getReqiurementsId));
        Map<Integer, DataStatusVO> dataStatusMap = dataStatusBO.getDataStatusMapByClassName(RequirementMangement.class.getSimpleName());
        for (RequirementMangementVO vo : vos) {
            //todo  获取状态对象
            if (ObjectUtil.isNotEmpty(vo.getStatus())){
                vo.setDataStatus(dataStatusMap.getOrDefault(vo.getStatus(),null));
            }
            //需要判断当前登陆人是否是单据的中心商务角色
            boolean isCenterBusiness = false; //是否中心商务
            if (!CollectionUtils.isEmpty(centerRoleIds)) {
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                } else {
                    vo.setIsCenterBusiness(false);
                }
            }
            if (isCenterBusiness) {
                String reqOwnership = vo.getReqOwnership();
                if (Objects.equals(reqOwnership, orgId)) {
                    vo.setIsCenterBusiness(true);
                } else {
                    vo.setIsCenterBusiness(false);
                }
            }
            //返回给前端展示的是删除按钮还是关闭按钮
            String techRes = vo.getTechRes();
            String businessPerson = vo.getBusinessPerson();
            String creatorId = vo.getCreatorId();
            Boolean flag = repeat(techRes, businessPerson, creatorId);//判断创建人 商务负责人 技术负责人是否是一个人
            vo.setCloseFlag(false);
            if (!flag) { //不为同一个人
                vo.setCloseFlag(true);
            }
            //另一种情况看反馈意见 如果存在不是创建者的也设为true
            List<RequirementsManagementLogs> requirementsManagementLogs = requirementsManagementLogsMap.get(vo.getId());
            if (ObjectUtil.isNotEmpty(requirementsManagementLogs)) {
                for (RequirementsManagementLogs requirementsManagementLog : requirementsManagementLogs) {
                    String creatorId1 = requirementsManagementLog.getCreatorId();
                    if (ObjectUtil.isNotEmpty(creatorId1)) {
                        if (!creatorId1.equals(creatorId)) {
                            vo.setCloseFlag(true);
                            break;
                        }
                    }

                }
            }

            //如果当前的状态是已确认时不显示
            if (vo.getStatus().equals(RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus())) {
                vo.setCloseFlag(false);
            }
        }
        log.info("===耗时==[分页查询8]耗时 {}=====", System.currentTimeMillis() - startTime1);
    }

    private Boolean repeat(String techRes, String businessPerson, String creatorId) {
        Set<String> uniqueNonNullStrings = new HashSet<>();
        if (techRes != null) {
            uniqueNonNullStrings.add(techRes);
        }
        if (businessPerson != null) {
            uniqueNonNullStrings.add(businessPerson);
        }
        if (creatorId != null) {
            uniqueNonNullStrings.add(creatorId);
        }
        // 如果集合的大小为0（即所有字符串都是null），或者为1（即所有非null字符串都相等），则返回true
        return uniqueNonNullStrings.size() <= 1;
    }

    /**
     * 查询主数据的dataStatus状态，用于列表筛选查询
     *
     * @return list
     */
    @Override
    public List<DataStatusVO> listDataStatus() {
        return statusRedisHelper.getStatusInfoListByClassName(RequirementMangement.class.getSimpleName());
    }

    @Override
    public List<DeptVO> getDeptList() {
        String userId = CurrentUserHelper.getCurrentUserId();
        String orgId = CurrentUserHelper.getOrgId();
        List<String> roleIdsOfUserId = roleUserHelper.getRoleIdsOfUserId(orgId, userId);
        LambdaQueryWrapperX<DeptDO> deptCenterQuery = new LambdaQueryWrapperX<>(DeptDO.class);
        if (roleIdsOfUserId.isEmpty()) {
            return new ArrayList<>();
        }
        if (roleIdsOfUserId.contains("nc8h1831588711351623680")) {
            deptCenterQuery.eq(DeptDO::getType, "20");
            deptCenterQuery.eq(DeptDO::getLogicStatus, 1);
            List<DeptDO> deptDos = deptDOMapper.selectList(deptCenterQuery);
            return BeanCopyUtils.convertListTo(deptDos, DeptVO::new);
        } else {
            List<String> deptIds = deptUserHelper.getDeptByUserId(orgId, userId);
            List<SimpleDeptVO> deptList = deptRedisHelper.getSimpleDeptByIds(deptIds);
            List<DeptVO> deptVOS = BeanCopyUtils.convertListTo(deptList, DeptVO::new);
            List<DeptVO> res = deptVOS.stream().filter(d -> d.getType().equals("20")).collect(Collectors.toList());
            return res;
        }
    }

    public void recordDistributeLogging(RequirementMangementDTO requirementMangementDTO) {
        String userId = currentUserHelper.getUserId();

        RequirementsManagementLogs requirementsManagementLogs = new RequirementsManagementLogs();
        String reqOwnership = requirementMangementDTO.getReqOwnership();
        LambdaQueryWrapperX<RequirementsManagementLogs> requirementsManagementLogsLambdaQueryWrapperX = new LambdaQueryWrapperX<>(RequirementsManagementLogs.class);
        requirementsManagementLogsLambdaQueryWrapperX.eq(RequirementsManagementLogs::getReqOwnership, reqOwnership);
        requirementsManagementLogsLambdaQueryWrapperX.orderByDesc("modify_time");
        requirementMangementDTO.setOrgId(CurrentUserHelper.getOrgId());
        requirementMangementDTO.setPlatformId("ykovb40e9fb1061b46fb96c4d0d3333dcc13");
        RequirementMangement requirementMangement = this.getById(requirementMangementDTO.getId());
        requirementMangementDTO.setRequirementName(requirementMangement.getRequirementName());
        requirementsManagementLogsLambdaQueryWrapperX.eq(RequirementsManagementLogs::getReqiurementsId, requirementMangementDTO.getId());
        List<RequirementsManagementLogs> managementLogs = requirementsManagementLogsService.list(requirementsManagementLogsLambdaQueryWrapperX);
        List<String> rdList = new ArrayList<>();
        List<String> rcList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(managementLogs)) {
            //如果技术接口人有变更 发信息给新的技术接口人
            RequirementsManagementLogs requirementsManagementLogs1 = managementLogs.get(0);
            String techRes = requirementMangementDTO.getTechRes();
            String person = requirementsManagementLogs1.getCustTecPerson();
            if (ObjectUtil.isNotEmpty(techRes) && !StrUtil.equals(person, techRes)) {
                //消息接收人添加技术接口人
                rdList.add(techRes);
            }
            //如果商务接口人有变更 发信息给新的商务接口人
            String businessPerson = requirementMangementDTO.getBusinessPerson();
            String custBsPerson = requirementsManagementLogs1.getCustBsPerson();

            //商务接口人变更时推送消息
            if ((ObjectUtil.isNotEmpty(businessPerson) && !StrUtil.equals(custBsPerson, businessPerson))) {
                //消息接收人添加商务接口人
                rdList.add(businessPerson);
                //待办接收人添加商务接口人
                rcList.add(businessPerson);
            }
        } else {
            String techRes = requirementMangementDTO.getTechRes();
            String businessPerson = requirementMangementDTO.getBusinessPerson();
            if (ObjectUtil.isNotEmpty(businessPerson)) {
                rdList.add(businessPerson);
                rcList.add(businessPerson);
            }
            if (ObjectUtil.isNotEmpty(techRes)) {
                rdList.add(techRes);
            }
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("$name$", requirementMangementDTO.getRequirementName());
        paramMap.put("$userName$", userRedisHelper.getUserBaseCacheById(userId).getName());
        mscBuildHandlerManager.send(requirementMangementDTO, RequirementNodeDict.NODE_REQUIREMENT_DISTRIBUTE, rdList, paramMap);
        mscBuildHandlerManager.send(requirementMangementDTO, RequirementNodeDict.NODE_REQUIREMENT_CONFIRM, rcList, paramMap);

        requirementsManagementLogs.setFeedbackTime(new Date());
        requirementsManagementLogs.setOpUser(userRedisHelper.getUserById(userId).getCode());
        requirementsManagementLogs.setOpUserName(userRedisHelper.getUserById(userId).getName());
        requirementsManagementLogs.setCustBsPerson(requirementMangementDTO.getBusinessPerson());
        requirementsManagementLogs.setCustBsPersonName(userRedisHelper.getUserById(requirementMangementDTO.getBusinessPerson()).getName());
        requirementsManagementLogs.setReqiurementsId(requirementMangementDTO.getId());

        String techRes = requirementMangementDTO.getTechRes();
        if (techRes != null && !techRes.isEmpty()) {
            requirementsManagementLogs.setCustTecPerson(techRes);
            requirementsManagementLogs.setCustTecPersonName(userRedisHelper.getUserById(techRes).getName());
        } else {
            requirementsManagementLogs.setCustTecPerson(null);
            requirementsManagementLogs.setCustTecPersonName("");
        }
        if (reqOwnership != null && !reqOwnership.isEmpty()) {
            requirementsManagementLogs.setReqOwnership(reqOwnership);
            requirementsManagementLogs.setReqOwnershipName(deptRedisHelper.getDeptById(reqOwnership).getName());
        } else {
            requirementsManagementLogs.setReqOwnership(null);
            requirementsManagementLogs.setReqOwnershipName("");
        }

        requirementsManagementLogs.setRemarkType(0);
        StringBuilder sb = new StringBuilder();
        sb.append(RemarkEnum.DISTRIBUTION.getDescription());
        sb.append(requirementsManagementLogs.getCustBsPersonName());
        sb.append("、");
        sb.append(requirementsManagementLogs.getCustTecPersonName());
        String result = sb.toString();
        requirementsManagementLogs.setRemark(result);
        requirementsManagementLogs.setStatus(requirementMangementDTO.getStatus());
        requirementsManagementLogsService.save(requirementsManagementLogs);
    }

    public void recordConfirmLogging(RequirementMangementDTO requirementMangementDTO) {
        RequirementsManagementLogs requirementsManagementLogs = new RequirementsManagementLogs();
        requirementsManagementLogs.setFeedbackTime(new Date());
        String userId = currentUserHelper.getUserId();
        requirementsManagementLogs.setOpUser(userRedisHelper.getUserById(userId).getCode());
        requirementsManagementLogs.setOpUserName(userRedisHelper.getUserById(userId).getName());
        requirementsManagementLogs.setCustTecPerson(requirementMangementDTO.getTechRes());
        requirementsManagementLogs.setCustTecPersonName(userRedisHelper.getUserById(requirementMangementDTO.getTechRes()).getName());
        requirementsManagementLogs.setReqiurementsId(requirementMangementDTO.getId());

        String reqOwnership = requirementMangementDTO.getReqOwnership();
        if (reqOwnership != null && !reqOwnership.isEmpty()) {
            requirementsManagementLogs.setReqOwnership(reqOwnership);
            requirementsManagementLogs.setReqOwnershipName(deptRedisHelper.getDeptById(reqOwnership).getName());
        } else {
            requirementsManagementLogs.setReqOwnership(null);
            requirementsManagementLogs.setReqOwnershipName("");
        }

        requirementsManagementLogs.setStatus(requirementMangementDTO.getStatus());
        requirementsManagementLogs.setRemarkType(0);
        if (RequirementStatusEnum.CONFIRMED.getStatus().equals(requirementMangementDTO.getStatus())) {
            RequirementsManagementLogs requirementsManagementLogs1 = new RequirementsManagementLogs();
            BeanCopyUtils.copyProperties(requirementsManagementLogs, requirementsManagementLogs1);
            String confirmRemark = requirementMangementDTO.getConfirmRemark();  // 记录确认时的备注
            StringBuilder sb = new StringBuilder();
            sb.append(RemarkEnum.CONFIRMED_REMARK.getDescription());
            sb.append(confirmRemark);


            requirementsManagementLogs.setRemark(RemarkEnum.CONFIRMED.getDescription());
            requirementsManagementLogs.setId("");
            //requirementsManagementLogsService.save(requirementsManagementLogs);

            requirementsManagementLogs1.setRemark(sb.toString());
            requirementsManagementLogs1.setId("");
            requirementsManagementLogsService.save(requirementsManagementLogs1);


        } else if (RequirementStatusEnum.REDISTRIBUTION.getStatus().equals(requirementMangementDTO.getStatus())) {
            requirementsManagementLogs.setRemark(RemarkEnum.REDISTRIBUTION.getDescription());
            requirementsManagementLogsService.save(requirementsManagementLogs);
        } else {


            RequirementsManagementLogs requirementsManagementLogs1 = new RequirementsManagementLogs();
            BeanCopyUtils.copyProperties(requirementsManagementLogs, requirementsManagementLogs1);
            String confirmRemark = requirementMangementDTO.getConfirmRemark();  // 记录不响应时的备注
            StringBuilder sb1 = new StringBuilder();
            sb1.append(RemarkEnum.UNCONFIRMED_REMARK.getDescription());
            sb1.append(confirmRemark);
            requirementsManagementLogs1.setRemark(sb1.toString());
            requirementsManagementLogs1.setId("");
            requirementsManagementLogs.setId("");
            requirementsManagementLogsService.save(requirementsManagementLogs1);
//            requirementsManagementLogs.setRemark(RemarkEnum.UNCONFIRMED.getDescription());
//            requirementsManagementLogsService.save(requirementsManagementLogs);
        }

    }

    @Override
    public Boolean applicant(List<String> ids) {
        List<RequirementMangement> requirementMangements = this.getBaseMapper().selectList(new LambdaQueryWrapperX<>(RequirementMangement.class)
                .in(RequirementMangement::getId, ids));
        requirementMangements.forEach(item -> {
            if (Objects.nonNull(item.getApplicantUser()) && Objects.nonNull(item.getApplicantDept())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在已报名的项目，请检查勾选的项目");
            }
        });
        Date date = new Date();
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<DeptUserDO> wrapperX = new LambdaQueryWrapperX<>(DeptUserDO.class);
        wrapperX.eq(DeptUserDO::getUserId, userId)
                .eq(DeptUserDO::getPartTime, false)
                .select(DeptUserDO::getDeptId);
        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(wrapperX);
        if (deptUserDOS == null || deptUserDOS.isEmpty()) {
            return false;
        }
        String deptId = deptUserDOS.get(0).getDeptId();
        requirementMangements.forEach(item -> {
            item.setApplicantUser(userId);
            item.setApplicantDept(deptId);
            item.setApplicantTime(date);
        });
        this.updateBatchById(requirementMangements);
        return true;
    }

    @Override
    public Boolean cancelApplicant(List<String> ids) {
        List<RequirementMangement> requirementMangements = this.getBaseMapper().selectList(new LambdaQueryWrapperX<>(RequirementMangement.class)
                .in(RequirementMangement::getId, ids));
        requirementMangements.forEach(item -> {
            item.setApplicantUser(null);
            item.setApplicantDept(null);
            item.setApplicantTime(null);
        });
        this.updateBatchById(requirementMangements);
        return true;
    }

    @Override
    public String close(RequirementMangementDTO requirementMangementDTO) {
        String closeReason = requirementMangementDTO.getCloseReason();
        String id = requirementMangementDTO.getId();
        RequirementMangement requirementMangement = this.getById(id);
        if (requirementMangement == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需求未找到或已被删除！");
        }
        if (RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus().equals(requirementMangement.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需求状态为已确认时不可关闭！");
        }
        if (ObjectUtil.isEmpty(closeReason)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关闭原因必须填写！");
        }
        requirementMangement.setStatus(RequirementManagementStatusEnum.CANCELED.getStatus());
        requirementMangement.setCloseReason(closeReason);
        boolean result = this.updateById(requirementMangement);
        if (!result) {
            return "false";
        }
        return "success";
    }

    @Override
    public String closeBatch(List<RequirementMangementDTO> requirementMangementDTOs) {
        List<String> list = new ArrayList<>();
        for (RequirementMangementDTO requirementMangementDTO : requirementMangementDTOs) {
            String id = requirementMangementDTO.getId();
            if (ObjectUtil.isNotEmpty(id)) {
                list.add(id);
            }
        }
        LambdaQueryWrapperX<RequirementMangement> requirementMangementLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        requirementMangementLambdaQueryWrapperX.in(RequirementMangement::getId, list);
        List<RequirementMangement> requirementMangementList = this.list(requirementMangementLambdaQueryWrapperX);
        for (RequirementMangement requirementMangement : requirementMangementList) {
            Integer status = requirementMangement.getStatus();
            if (RequirementManagementStatusEnum.HAVE_CONFIRMED.getStatus().equals(status)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需求状态为已确认时不可关闭！");
            }
        }

        RequirementMangementDTO requirementMangementDTO = requirementMangementDTOs.get(0);
        String closeReason = requirementMangementDTO.getCloseReason();
        if (ObjectUtil.isEmpty(closeReason)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关闭原因必须填写！");
        }
        LambdaUpdateWrapper<RequirementMangement> requirementMangementLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        requirementMangementLambdaUpdateWrapper.in(RequirementMangement::getId, list);
        requirementMangementLambdaUpdateWrapper.set(RequirementMangement::getCloseReason, closeReason);
        requirementMangementLambdaUpdateWrapper.set(RequirementMangement::getStatus, RequirementManagementStatusEnum.CANCELED.getStatus());
        boolean result = this.update(requirementMangementLambdaUpdateWrapper);
        if (!result) {
            return "false";
        }
        return "success";
    }

    @Override
    public void exportExcelData(Page<RequirementMangementDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RequirementMangement> condition = new LambdaQueryWrapperX<>(RequirementMangement.class);
        Map<String, UserBaseCacheVO> nameMap = new HashMap<>();
        Map<String, String> deptMap = new HashMap<>();
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(RequirementMangement.class);
        condition.leftJoin("(select remark,feedback_time,reqiurements_id from pmsx_requirements_management_logs where remark like '需求确认%' and  remark  not like  '需求确认为不响应%') t1 on t1.reqiurements_id = t.id");
//        condition.leftJoin(RequirementsManagementLogs.class,RequirementsManagementLogs::getReqiurementsId,RequirementMangement::getId)
//                .and(w->w.likeRight(RequirementsManagementLogs::getRemark,"需求确认").notLikeRight(RequirementsManagementLogs::getRemark,"需求确认为不响应"));
        condition.select("t1.remark,t1.feedback_time");
        // condition.selectAs(RequirementsManagementLogs::getFeedbackTime,RequirementMangement::getFeedbackTime);
        ;

        condition.orderByDesc(RequirementMangement::getCreateTime);
        List<RequirementMangement> requirementMangementsList = requirementMangementMapper.selectList(condition);
        requirementMangementsList = requirementMangementsList.stream().collect(Collectors.collectingAndThen(
                Collectors.toMap(
                        RequirementMangement::getId,
                        item -> item,
                        (existing, replacement) -> existing // 保留第一个遇到的对象
                ),
                map -> new ArrayList<>(map.values())
        ));
        List<RequirementExcelExportDTO> requirementMangementDTOS = BeanCopyUtils.convertListTo(requirementMangementsList, RequirementExcelExportDTO::new);

        List<String> uniqueReqOwnershipList = requirementMangementsList.stream()
                .map(RequirementMangement::getReqOwnership)
                .filter(item -> StrUtil.isNotBlank(item))
                .distinct()
                .collect(Collectors.toList());

        List<String> creatorId = requirementMangementsList.stream()
                .map(RequirementMangement::getCreatorId)
                .filter(item -> StrUtil.isNotBlank(item))
                .distinct()
                .collect(Collectors.toList());
        List<String> businessPerson = requirementMangementsList.stream()
                .map(RequirementMangement::getBusinessPerson)
                .filter(item -> StrUtil.isNotBlank(item))
                .distinct()
                .collect(Collectors.toList());
        List<String> techRes = requirementMangementsList.stream()
                .map(RequirementMangement::getTechRes)
                .filter(item -> StrUtil.isNotBlank(item))
                .distinct()
                .collect(Collectors.toList());
        creatorId.addAll(businessPerson);
        creatorId.addAll(techRes);
        if (CollUtil.isNotEmpty(creatorId)) {
            List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(creatorId);
            nameMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, Function.identity()));
        }
        List<String> custPerson = requirementMangementsList.stream()
                .map(RequirementMangement::getCustPerson)
                .filter(item -> StrUtil.isNotBlank(item))
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(custPerson)) {
            LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            customerInfoLambdaQueryWrapper.in(CustomerInfo::getId, custPerson);
            customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getGroupInOut, CustomerInfo::getIndustry);
            List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapper);
            customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
        }

        if (CollUtil.isNotEmpty(uniqueReqOwnershipList)) {
            List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(uniqueReqOwnershipList);
            deptMap = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
        }
        List<DictValueVO> resSourceList = dictRedisHelper.getDictList("dict1795720778296483840");//需求来源

        Map<String, String> resSourceMap = resSourceList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        extracted(requirementMangementDTOS, null, deptMap, resSourceMap, customerInfoMap, nameMap);

        String fileName = URLEncoder.encode("需求报表.xlsx", StandardCharsets.UTF_8.toString()).replace("+", "%20");
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        EasyExcel.write(response.getOutputStream()).head(RequirementExcelExportDTO.class).sheet("报价报表").doWrite(requirementMangementDTOS);

    }

    @Override
    public Page<RequirementMangementVO> pagesMenu(Page<RequirementMangementDTO> pageRequest) throws Exception {
        long startTime = System.currentTimeMillis();
        Map<String, UserBaseCacheVO> nameMap = new HashMap<>();
        Map<String, String> deptMap = new HashMap<>();
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        LambdaQueryWrapperX<RequirementMangement> condition = new LambdaQueryWrapperX<>(RequirementMangement.class);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(RequirementMangement.class);
        condition.orderByDesc(RequirementMangement::getCreateTime);
        Page<RequirementMangement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RequirementMangement::new));
        IPage<RequirementMangement> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<RequirementMangement> result = requirementMangementMapper.selectPage(mPage, condition);
        Page<RequirementMangementVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        log.info("===耗时==[报表查询]耗时 {}=====", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        List<RequirementMangementVO> requirementMangementVOS = BeanCopyUtils.convertListTo(result.getRecords(), RequirementMangementVO::new);
        List<RequirementExcelExportDTO> requirementExcelExportDTOS = BeanCopyUtils.convertListTo(requirementMangementVOS, RequirementExcelExportDTO::new);
        if (ObjectUtil.isNotEmpty(requirementExcelExportDTOS)) {

            List<String> ids = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getId)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> uniqueReqOwnershipList = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getReqOwnership)
                    .filter(item -> StrUtil.isNotBlank(item))
                    .distinct()
                    .collect(Collectors.toList());

            List<String> custPerson = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getCustPerson)
                    .filter(item -> StrUtil.isNotBlank(item))
                    .distinct()
                    .collect(Collectors.toList());

            List<String> creatorId = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getCreatorId)
                    .filter(item -> StrUtil.isNotBlank(item))
                    .distinct()
                    .collect(Collectors.toList());
            List<String> businessPerson = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getBusinessPerson)
                    .filter(item -> StrUtil.isNotBlank(item))
                    .distinct()
                    .collect(Collectors.toList());
            List<String> techRes = requirementMangementVOS.stream()
                    .map(RequirementMangementVO::getTechRes)
                    .filter(item -> StrUtil.isNotBlank(item))
                    .distinct()
                    .collect(Collectors.toList());
            creatorId.addAll(businessPerson);
            creatorId.addAll(techRes);
            if (CollUtil.isNotEmpty(creatorId)) {
                List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(creatorId);
                nameMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, Function.identity()));
            }

            LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (!CollectionUtils.isEmpty(custPerson)) {
                customerInfoLambdaQueryWrapper.in(CustomerInfo::getId, custPerson);
                customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getGroupInOut, CustomerInfo::getIndustry);
                List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapper);
                customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
            }


            if (CollUtil.isNotEmpty(uniqueReqOwnershipList)) {
                List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(uniqueReqOwnershipList);
                deptMap = deptByIds.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
            }
            List<DictValueVO> resSourceList = dictRedisHelper.getDictList("dict1795720778296483840");//需求来源
            Map<String, String> resSourceMap = resSourceList.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
            List<RequirementsManagementLogs> logs = requirementsManagementLogsService.list(new LambdaQueryWrapperX<>(RequirementsManagementLogs.class).in(RequirementsManagementLogs::getReqiurementsId, ids));
            Map<String, List<RequirementsManagementLogs>> map = logs.stream().collect(Collectors.groupingBy(RequirementsManagementLogs::getReqiurementsId));
            log.info("===耗时==[报表查询2]耗时 {}=====", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            extracted(requirementExcelExportDTOS, map, deptMap, resSourceMap, customerInfoMap, nameMap);
            log.info("===耗时==[报表查询3]耗时 {}=====", System.currentTimeMillis() - startTime);
        }
        List<RequirementMangementVO> requirementMangementVOList = BeanCopyUtils.convertListTo(requirementExcelExportDTOS, RequirementMangementVO::new);

        pageResult.setContent(requirementMangementVOList);
        return pageResult;
    }

    private static void extracted(List<RequirementExcelExportDTO> requirementExcelExportDTOS, Map<String, List<RequirementsManagementLogs>> map, Map<String, String> deptMap, Map<String, String> resSourceMap, Map<String, CustomerInfo> customerInfoMap, Map<String, UserBaseCacheVO> nameMap) {
        Map<String, String> businessTypeMap = Map.of(
                MarketContractBusinessTypeEnums.DOMESTIC_MAIN_BUSINESS.getKey(), MarketContractBusinessTypeEnums.DOMESTIC_MAIN_BUSINESS.getDesc(),
                MarketContractBusinessTypeEnums.OVERSEAS_MAIN_BUSINESS.getKey(), MarketContractBusinessTypeEnums.OVERSEAS_MAIN_BUSINESS.getDesc(),
                MarketContractBusinessTypeEnums.WASTE_MATERIALS_DISPOSAL.getKey(), MarketContractBusinessTypeEnums.WASTE_MATERIALS_DISPOSAL.getDesc(),
                MarketContractBusinessTypeEnums.OTHER_SALES_CONTRACTS.getKey(), MarketContractBusinessTypeEnums.OTHER_SALES_CONTRACTS.getDesc()
        );
        for (RequirementExcelExportDTO requirementMangementDTO : requirementExcelExportDTOS) {
            if (ObjectUtil.isNotEmpty(map)) {
                List<RequirementsManagementLogs> requirementsManagementLogs = map.get(requirementMangementDTO.getId());
                if (ObjectUtil.isNotEmpty(requirementsManagementLogs)) {
                    for (RequirementsManagementLogs requirementsManagementLog : requirementsManagementLogs) {
                        String remark = requirementsManagementLog.getRemark();
                        if (remark != null && remark.contains("需求确认") && !remark.contains("需求确认为不响应")) {
                            requirementMangementDTO.setFeedBackTime(requirementsManagementLog.getFeedbackTime());
                            requirementMangementDTO.setRemark(remark);
                            break;
                        }
                    }
                }
            }
            //承担部门
            requirementMangementDTO.setReqOwnership(deptMap.getOrDefault(requirementMangementDTO.getReqOwnership(), ""));
            // 设置资源来源，默认为空字符串
            requirementMangementDTO.setResSource(resSourceMap.getOrDefault(requirementMangementDTO.getResSource(), ""));
            //创建人
            String creatorId = requirementMangementDTO.getCreatorId();
            if (ObjectUtil.isNotEmpty(creatorId) && ObjectUtil.isNotEmpty(nameMap.get(creatorId))) {
                requirementMangementDTO.setCreatorName("[" + nameMap.get(creatorId).getCode() + "]" + nameMap.get(creatorId).getName());
            }
            String businessPerson = requirementMangementDTO.getBusinessPerson();
            if (ObjectUtil.isNotEmpty(nameMap.get(businessPerson))) {
                requirementMangementDTO.setBusinessPersonName("[" + nameMap.get(businessPerson).getCode() + "]" + nameMap.get(businessPerson).getName());
            }
            String techRes = requirementMangementDTO.getTechRes();
            if (ObjectUtil.isNotEmpty(nameMap.get(techRes))) {
                requirementMangementDTO.setTechResName("[" + nameMap.get(techRes).getCode() + "]" + nameMap.get(techRes).getName());
            }
            // 设置销售分类
            String custId = requirementMangementDTO.getCustPerson();
            if (ObjectUtil.isNotEmpty(custId)) {
                CustomerInfo customerInfo = customerInfoMap.get(custId);
                if (ObjectUtil.isNotEmpty(customerInfo)) {
                    String groupInOut = customerInfo.getGroupInOut();
                    if (ObjectUtil.isNotEmpty(groupInOut) && CustomerRelationshipEnum.GROUP_EXTERNAL.getName().equals(groupInOut)) {
                        requirementMangementDTO.setGroupInOut(CustomerRelationshipEnum.GROUP_EXTERNAL.getDesc());
                    } else if (ObjectUtil.isNotEmpty(groupInOut) && CustomerRelationshipEnum.GROUP_WIDE.getName().equals(groupInOut)) {
                        requirementMangementDTO.setGroupInOut(CustomerRelationshipEnum.GROUP_WIDE.getDesc());
                    }
                    String industry = customerInfo.getIndustry();
                    if (ObjectUtil.isNotEmpty(industry) && CustomerIndustryEnum.NEW_ENERGY.getName().equals(industry)) {
                        requirementMangementDTO.setIndustryName(CustomerIndustryEnum.NEW_ENERGY.getDesc());
                    } else if (ObjectUtil.isNotEmpty(industry) && CustomerIndustryEnum.NUCLEAR_ENERGY.getName().equals(industry)) {
                        requirementMangementDTO.setIndustryName(CustomerIndustryEnum.NUCLEAR_ENERGY.getDesc());
                    } else if (ObjectUtil.isNotEmpty(industry) && CustomerIndustryEnum.WIND_ENERGY.getName().equals(industry)) {
                        requirementMangementDTO.setIndustryName(CustomerIndustryEnum.WIND_ENERGY.getDesc());
                    } else if (ObjectUtil.isNotEmpty(industry) && CustomerIndustryEnum.FOSSIL_FUEL_ENERGY.getName().equals(industry)) {
                        requirementMangementDTO.setIndustryName(CustomerIndustryEnum.FOSSIL_FUEL_ENERGY.getDesc());
                    } else if (ObjectUtil.isNotEmpty(industry) && CustomerIndustryEnum.OTHER.getName().equals(industry)) {
                        requirementMangementDTO.setIndustryName(CustomerIndustryEnum.OTHER.getDesc());
                    }
                    requirementMangementDTO.setSalesClassification(requirementMangementDTO.getGroupInOut() + requirementMangementDTO.getIndustryName());
                }
            }

            // 设置客户范围
            String custScope = requirementMangementDTO.getCustScope();
            if (ObjectUtil.isNotEmpty(custScope)) {
                if (CustomerScopeEnum.DOMESTIC.getName().equals(custScope) || CustomerScopeEnum.OVERSEAS.getName().equals(custScope)) {
                    requirementMangementDTO.setCustScope(CustomerScopeEnum.getDesc(custScope));
                }
            }
            //已报价
            Integer hadQuotation = requirementMangementDTO.getHadQuotation();
            if (1 == hadQuotation) {
                requirementMangementDTO.setHadQuotationName("是");
            } else if (0 == hadQuotation) {
                requirementMangementDTO.setHadQuotationName("否");
            }
            // 设置状态名称
            Integer status = requirementMangementDTO.getStatus();
            if (status != null) {
                switch (status) {
                    case 121:
                        requirementMangementDTO.setStatusName("待分发");
                        break;
                    case 130:
                        requirementMangementDTO.setStatusName("已确认");
                        break;
                    case 120:
                        requirementMangementDTO.setStatusName("待确定");
                        break;
                    case 140:
                        requirementMangementDTO.setStatusName("不响应");
                        break;
                    case 111:
                        requirementMangementDTO.setStatusName("已作废");
                        break;
                    default:
                        // 处理未知状态
                        requirementMangementDTO.setStatusName("未知状态");
                        break;
                }
            }

            // 设置业务类型
            String businessType = requirementMangementDTO.getBusinessType();
            if (ObjectUtil.isNotEmpty(businessType)) {
                requirementMangementDTO.setBusinessType(businessTypeMap.getOrDefault(businessType, ""));
            }
        }
    }

    @Override
    public RequirementMangement getSimpleEntityById(String id) {
        LambdaQueryWrapper<RequirementMangement> queryWrapper = new LambdaQueryWrapper<>(RequirementMangement.class);
        queryWrapper.eq(RequirementMangement::getId, id);
        queryWrapper.select(RequirementMangement::getId,
                RequirementMangement::getBusinessType,
                RequirementMangement::getBusinessPerson,
                RequirementMangement::getTechRes,
                RequirementMangement::getStatus,
                RequirementMangement::getHadQuotation);
        return this.getOne(queryWrapper);
    }


}
