package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BasicUserLedger DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-11 09:54:35
 */
@ApiModel(value = "BasicUserLedgerDTO对象", description = "技术支持人员台账记录")
@Data
@ExcelIgnoreUnannotated
public class BasicUserLedgerDTO extends  ObjectDTO   implements Serializable{

    /**
     * 技术支持人员id
     */
    @ApiModelProperty(value = "技术支持人员id")
    @ExcelProperty(value = "技术支持人员id ", index = 0)
    private String basicUserId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 1)
    private String userName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @ExcelProperty(value = "公司名称 ", index = 2)
    private String companyName;

    /**
     * 用人部门
     */
    @ApiModelProperty(value = "用人部门")
    @ExcelProperty(value = "用人部门 ", index = 3)
    private String departmentName;

    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    @ExcelProperty(value = "研究所 ", index = 4)
    private String instituteName;

    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    @ExcelProperty(value = "加入本单位时间 ", index = 5)
    private Date addUnittime;

    /**
     * 到岗时间
     */
    @ApiModelProperty(value = "到岗时间")
    @ExcelProperty(value = "到岗时间 ", index = 6)
    private Date addWorkTime;




}
