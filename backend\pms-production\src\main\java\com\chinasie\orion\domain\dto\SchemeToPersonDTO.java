package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * SchemeToPerson DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@ApiModel(value = "SchemeToPersonDTO对象", description = "计划相关的人员")
@Data
@ExcelIgnoreUnannotated
public class SchemeToPersonDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    @ExcelProperty(value = "项目计划id ", index = 0)
    private String planSchemeId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String repairRound;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @ExcelProperty(value = "用户ID ", index = 2)
    private String userId;

    /**
     * 人员ID： 人员管理ID
     */
    @ApiModelProperty(value = "人员ID： 人员管理ID")
    @ExcelProperty(value = "人员ID： 人员管理ID ", index = 3)
    private String personId;

    /**
     * 用户工号
     */
    @ApiModelProperty(value = "用户工号")
    @ExcelProperty(value = "用户工号 ", index = 4)
    private String userCode;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @ExcelProperty(value = "用户名称 ", index = 5)
    private String userName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 6)
    private String baseCode;


    @ApiModelProperty(value = "有无项目:默认有")
    private Boolean isHaveProject;
    @ApiModelProperty(value = "人员管理ID列表")
    private List<String> personIdList;

    @ApiModelProperty(value = "员工号查询")
    private List<String> userCodes;

    @ApiModelProperty(value = "员工号查询")
    private List<Integer> stateList;
    @ApiModelProperty(value = "关键词")
    private String keyword;

}
