package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * IdeaFormToIdeaForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "IdeaFormToIdeaFormDTO对象", description = "意见单和意见单的关系")
@Data
public class IdeaFormToIdeaFormDTO extends ObjectDTO implements Serializable{

/**
 * 来源id
 */
@ApiModelProperty(value = "来源id")
private String sourceId;

/**
 * 目标ID
 */
@ApiModelProperty(value = "目标ID")
private String targetId;

}
