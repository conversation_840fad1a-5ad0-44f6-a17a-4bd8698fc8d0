package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;
/**
 * AuthorizeJobPostRequirement VO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:47
 */
@ApiModel(value = "AuthorizeJobPostRequirementVO对象", description = "岗位授权要求检验表")
@Data
public class AuthorizeJobPostRequirementVO extends  ObjectVO   implements Serializable{

            /**
         * 岗位授权管理Id
         */
        @ApiModelProperty(value = "岗位授权管理Id")
        private String authorizeManageId;


        /**
         * 岗位id
         */
        @ApiModelProperty(value = "岗位id")
        private String jobPostId;


        /**
         * 要求类型(取得证书、通过培训)（冗余）
         */
        @ApiModelProperty(value = "要求类型(取得证书、通过培训)（冗余）")
        private String type;
        /**
         * 要求类型
         */
        @ApiModelProperty(value = "要求类型名称")
        private String typeName;

        /**
         * 要求名称（冗余）
         */
        @ApiModelProperty(value = "要求名称（冗余）")
        private String name;


        /**
         * 应取得证书number（冗余）
         */
        @ApiModelProperty(value = "应取得证书number（冗余）")
        private String certificateNumber;
        @ApiModelProperty(value = "证书名称")
        private String certificateName;

        /**
         * 应通过培训number
         */
        @ApiModelProperty(value = "应通过培训number")
        private String trainNumber;
        @ApiModelProperty(value = "培训名称")
        private String trainName;

        /**
         * 是否满足要求
         */
        @ApiModelProperty(value = "是否满足要求")
        private Boolean isSatisfy;

        /**
         * 证书编号
         */
        @ApiModelProperty(value = "证书库ID")
        private String certificateId;

        @ApiModelProperty(value = "基地编码")
        private String baseCode;
        @ApiModelProperty(value = "基地名称")
        private String baseName;
}
