package com.chinasie.orion.management.service;


import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.management.domain.dto.ManagementStaticsReqDTO;
import com.chinasie.orion.management.domain.vo.*;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ManagementStaticsService {

    MilestoneCompletionVO milestoneCompletion(ManagementStaticsReqDTO managementStaticsReqDTO);

    ContractDistributionVO contractDistribution(ManagementStaticsReqDTO managementStaticsReqDTO);

    QuoteOutbidVO quoteOutbid(ManagementStaticsReqDTO managementStaticsReqDTO);

    MarketManagementTotalVO eachStateStatistics(ManagementStaticsReqDTO managementStaticsReqDTO);

    MilestoneLineChartVO milestoneLineChart(ManagementStaticsReqDTO managementStaticsReqDTO);

    Page<ContractMilestoneVO> pages(Page<ContractMilestoneDTO> pageRequest)throws Exception;
}