package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
/**
 * LaborCostAcceptance VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
@ApiModel(value = "LaborCostAcceptanceVO对象", description = "人力成本验收单")
@Data
public class LaborCostAcceptanceVO extends  ObjectVO   implements Serializable{

    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    private String name;

    /**
     * 申请单编号
     */
    @ApiModelProperty(value = "申请单编号")
    private String number;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 验收年份
     */
    @ApiModelProperty(value = "验收年份")
    private Integer acceptanceYear;

    /**
     * 中心
     */
    @ApiModelProperty(value = "中心")
    private String acceptanceDeptCode;

    /**
     * 验收季度
     */
    @ApiModelProperty(value = "验收季度")
    private Integer acceptanceQuarter;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String supplierName;

    /**
     * 验收用人单位
     */
    @ApiModelProperty(value = "验收用人单位")
    private String employDeptCode;

    /**
     * 验收用人单位名称
     */
    @ApiModelProperty(value = "验收用人单位名称")
    private String employDeptName;

    /**
     * 项目组审核人
     */
    @ApiModelProperty(value = "项目组审核人")
    private String projectReviewer;

    /**
     * 项目组审核人名称
     */
    @ApiModelProperty(value = "项目组审核人名称")
    private String projectReviewerName;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 预计合同开始日期
     */
    @ApiModelProperty(value = "预计合同开始日期")
    private Date estimatedStartTime;

    /**
     * 预计合同结束日期
     */
    @ApiModelProperty(value = "预计合同结束日期")
    private Date estimatedEndTime;


    @ApiModelProperty("合同状态")
    private String contractStatus;


    /**
     * 研究所审核人
     */
    @ApiModelProperty(value = "研究所审核人")
    private String deptReviewer;

    /**
     * 研究所审核人名称
     */
    @ApiModelProperty(value = "研究所审核人名称")
    private String deptReviewerName;

    /**
     * 中心/部门审核人
     */
    @ApiModelProperty(value = "中心/部门审核人")
    private String orgReviewer;

    /**
     * 中心/部门审核人名称
     */
    @ApiModelProperty(value = "中心/部门审核人名称")
    private String orgReviewerName;


    /**
     * 归口部门审核人
     */
    @ApiModelProperty(value = "归口部门审核人")
    private String belongDeptReviewer;


    /**
     * 归口部门审核人名称
     */
    @ApiModelProperty(value = "归口部门审核人名称")
    private String belongDeptReviewerName;

    /**
     * 验收人力成本费用
     */
    @ApiModelProperty(value = "验收人力成本费用")
    private AttendanceResultCostVO attendanceResultCostVO;


    /**
     * 验收人力成本费用统计
     */
    @ApiModelProperty(value = "验收人力成本费用统计")
    private LaborCostStatisticsSingleVO laborCostStatisticsSingleVO;

    /**
     * 合同考核条款
     */
    @ApiModelProperty(value = "合同考核条款")
    private List<ContractAssessmentStandardVO> assessmentStandardVOS;

    /**
     * 验收明细表
     */
    @ApiModelProperty(value = "验收明细表")
    private LaborCostAcceptanceDetailVO acceptanceDetail;

    @ApiModelProperty(value = "合同附件")
    private List<FileVO> fileList;
}
