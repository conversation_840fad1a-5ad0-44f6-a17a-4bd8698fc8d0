package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划执行跟踪报表")
@Data
public class IncomePlanExecutionTrackDTO implements Serializable {
    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;

    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterName;

    @ApiModelProperty(value = "专业所")
    private String expertiseStation;

    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationName;

    @ApiModelProperty(value = "收入计划月份")
    private String workTopics;

    @ApiModelProperty(value = "工作主题名称")
    private String workTopicsName;

    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanDataNumber;


    @ApiModelProperty(value = "开票/收入确认公司")
    private String billingCompany;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    private String billingCompanyName;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    private String partyADeptId;

    @ApiModelProperty(value = "甲方单位名称")
    private String partyADeptIdName;

    @ApiModelProperty(value = "甲方纳税人识别号")
    private String taxIdCode;

    @ApiModelProperty(value = "合同ID")
    private String contractId;

    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmt;

    @ApiModelProperty(value = "合同约定验收日期")
    private Date planAcceptDate;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "集团内（基地）/外")
    private String internalExternal;



    @ApiModelProperty(value = "合同里程碑id")
    private String milestoneId;

    @ApiModelProperty(value = "合同里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "当前里程碑状态")
    private String milestoneType;

    @ApiModelProperty(value = "预计/实际验收日期")
    private Date acceptDate;

    @ApiModelProperty(value = "预计/实际验收金额")
    private BigDecimal acceptAmt;

    @ApiModelProperty(value = "收入确认类型")
    private String incomeConfirmType;

    @ApiModelProperty(value = "预计开票/暂估日期")
    private Date estimateInvoiceDate;

    @ApiModelProperty(value = "收入计划执行状态")
    private String incomePlanDataStatus;



    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;

    @ApiModelProperty(value = "税率")
    private String taxRate;


    @ApiModelProperty(value = "甲方所属二级单位")
    private String partASecondDept;

    @ApiModelProperty(value = "合同类型")
    private String contractType;

    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;

    @ApiModelProperty(value = "合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "合同状态名称")
    private String contractSatusName;

    @ApiModelProperty(value = "甲方合同号/订单号")
    private String orderNumber;

    @ApiModelProperty(value = "电厂项目性质")
    private String powerProjectPlant;

    @ApiModelProperty(value = "电厂项目性质名称")
    private String powerProjectPlantName;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String busRspUser;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String busRspUserName;


    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    private BigDecimal estimateAmt;

    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "本次开票金额（价税合计）")
    private BigDecimal invAmt;

    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "作废发票合计（价税合计）")
    private BigDecimal cancelInvAmt;


    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    private BigDecimal writeOffAmt;

    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    private BigDecimal advancePayIncomeAmt;

    @ApiModelProperty(value = "六位码")
    private String sixCode;

    @ApiModelProperty(value = "UPM/FIS流程主题")
    private String processTopics;

    @ApiModelProperty(value = "当前环节")
    private String currentSession;

    @ApiModelProperty(value = "当前环节执行人")
    private String currentLinkExecutor;

    @ApiModelProperty(value = "流程收入金额")
    private BigDecimal processRevenueAmt;

    @ApiModelProperty(value = "凭证编号")
    private String certificateNumber;

    @ApiModelProperty(value = "过帐日期")
    private Date postDate;

    @ApiModelProperty(value = "确认收入金额")
        private BigDecimal recognitionRevenueAmt;

    @ApiModelProperty(value = "未确认收入金额")
    private BigDecimal NoRecognitionRevenueAmt;


    @ApiModelProperty(value = "本次冲销暂估金额")
    private BigDecimal writeOffFisAmt;


    @ApiModelProperty(value = "风险状态")
    private String riskType;

    @ApiModelProperty(value = "风险环节")
    private String riskLink;

    @ApiModelProperty(value = "其他说明")
    private String otherNotes;

    @ApiModelProperty(value = "项目编码")
    private String projectNumber;

    @ApiModelProperty(value = "项目负责人")
    private String projectRspUserId;

    @ApiModelProperty(value = "项目负责人")
    private String projectRspUserName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "专业中心")
    private List<String> centers;

    @ApiModelProperty(value = "专业所")
    private List<String> stations;

    @ApiModelProperty(value = "是否拥有全部查看权限")
    private Boolean isPermission;


    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "导出id")
    private List<String> ids;
}
