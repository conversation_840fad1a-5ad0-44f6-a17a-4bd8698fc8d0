<template>
  <div class="wrap_cont">
    <!--    筛选条件-->
    <div
      class="form_wrap"
    >
      <Form
        ref="formRef"
        class="investment-form-inline"
        :model="modal"
      >
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.planReport"
            :getPopupContainer="getPopupContainer"
            allowClear
            placeholder="请选择"
            :options="[
              {
                label:'物资状态分布统计',
                value:'101'
              },
              {
                label:'物资状态-负责人分布统计',
                value:'102'
              },
              {
                label:'物资状态趋势',
                value:'103'
              },
              {
                label:'物资新增趋势',
                value:'104'
              },
            ]"
          />
        </FormItem>
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.isAll"
            :getPopupContainer="getPopupContainer"
            placeholder="请选择"
            allowClear
            :options="[
              {
                label:'全部',
                value:'ALL'
              },
              {
                label:'物资',
                value:'goodsType'
              },
              {
                label:'服务',
                value:'serviceType'
              },
            ]"
          />
        </FormItem>
      </Form>
    </div>
    <!--    Echarts图标-->
    <div>
      <!--    趋势时间选择-->
      <div
        v-if="modal.planReport!=='101'&& modal.planReport!=='102'"
        class="time_cont"
      >
        <RadioGroup
          v-model:value="modal.timeKeys"
        >
          <RadioButton value="YEAR">
            年
          </RadioButton>
          <RadioButton value="QUARTER">
            季度
          </RadioButton>
          <RadioButton value="MONTH">
            月
          </RadioButton>
          <RadioButton value="WEEK">
            周
          </RadioButton>
          <RadioButton value="DAY">
            日
          </RadioButton>
        </RadioGroup>
      </div>
      <BarEcharts
        v-if="modal.planReport==='101'"
        :data="echartsData"
      />
      <ThreeBarEcharts
        v-if="modal.planReport==='102'"
        :data="echartsData"
        @getPeople="getPeople"
      />
      <LineStateEcharts
        v-if="modal.planReport==='103'"
        :data="echartsData"
        @getStateLine="getStateLine"
      />
      <LineAddEcharts
        v-if="modal.planReport==='104'"
        :data="echartsData"
        @getAddLine="getAddLine"
      />
    </div>
    <!--    表格-->
    <Divider
      v-if="modal.planReport=='101'"
    >
      <RadioGroup
        v-model:value="modal.tableKeys"
      >
        <RadioButton value="120">
          未审核
        </RadioButton>
        <RadioButton value="110">
          审核中
        </RadioButton>
        <RadioButton value="130">
          已审核
        </RadioButton>
        <RadioButton value="160">
          已入库
        </RadioButton>
      </RadioGroup>
    </Divider>
  </div>
  <div class="table_cont">
    <ResourceTable
      v-if="modal.planReport!=='103'"
      :tableData="tableData"
    />
    <ResourceStatusTable
      v-else
      :tableData="echartsData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  ref, Ref, onMounted, watch, inject,
} from 'vue';
import {
  Form, FormItem, Select, Divider, RadioGroup, RadioButton,

} from 'ant-design-vue';
import Api from '/@/api';
import { Layout } from 'lyra-component-vue3';
import BarEcharts from './EchartComponents/BarEchart/index.vue';
import ThreeBarEcharts from './EchartComponents/ThreeBarEchart/index.vue';
import LineStateEcharts from './EchartComponents/LineStateEchart/index.vue';
import LineAddEcharts from './EchartComponents/LineAddEchart/index.vue';
import ResourceTable from './ResourceTable.vue';
import ResourceStatusTable from './ResourceStatusTable.vue';
import DemandStatusTable
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/staticsticManagement/components/DemandReport/DemandStatusTable.vue';
const projectId = inject('projectId');
const formRef: Ref = ref();
const modal = ref({
  planReport: '101',
  isAll: 'ALL',
  tableKeys: '120',
  timeKeys: 'YEAR',
});
const echartsData = ref();
const tableData = ref();

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
function getPopupContainer(): Element {
  return document.querySelector('.wrap_cont');
}
onMounted(() => {
  getPlanData();
});
watch(
  () => modal.value.planReport,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.isAll,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.tableKeys,
  (newValue, oldValue) => {
    getPlanTableData();
  },
);
watch(
  () => modal.value.timeKeys,
  (newValue, oldValue) => {
    getPlanData();
  },
);

function getPlanData() {
  let planReportType: string;
  switch (modal.value.planReport) {
    case '101':
      planReportType = 'getProjectGoodsStatusStatistics';
      break;
    case '102':
      planReportType = 'getProjectGoodsRspUserStatistics';
      break;
    case '103':
      planReportType = 'getProjectGoodsChangeStatusStatistics';
      break;
    case '104':
      planReportType = 'getProjectGoodsCreateStatistics';
      break;
  }
  const data = {
    projectId,
    goodsType: modal.value.isAll === 'ALL' ? undefined : modal.value.isAll,
    timeType: modal.value.planReport !== '101' && modal.value.planReport !== '102' ? modal.value.timeKeys : undefined,
  };
  new Api('/pms/projectGoodsStatistics').fetch(data, planReportType, 'POST').then((res) => {
    if (Array.isArray(res) && res?.length === 0) {
      return echartsData.value = [];
    }
    switch (modal.value.planReport) {
      case '101':
        const {
          noAuditCount, underReviewCount, reviewedCount, storeCount,
        } = res;
        echartsData.value = [
          {
            name: '未审核',
            number: noAuditCount,
          },
          {
            name: '审核中',
            number: underReviewCount,
          },
          {
            name: '已审核',
            number: reviewedCount,
          },
          {
            name: '已入库',
            number: storeCount,
          },
        ];
        break;
      case '102':
        echartsData.value = res;
        break;
      case '103':
        echartsData.value = res;
        break;
      case '104':
        echartsData.value = res;
        break;
    }
  });
}
getPlanTableData();
function getPlanTableData(params?) {
  tableData.value = {
    projectId,
    goodsType: modal.value.isAll === 'ALL' ? undefined : modal.value.isAll,
    status: modal.value.planReport === '101' ? modal.value.tableKeys : undefined,
    ...params,
  };
}
// 点击堆叠柱状图人名
function getPeople(val) {
  const peopleData = { rspUser: val?.rspuser };
  getPlanTableData(peopleData);
}
// 点击堆叠折现图
function getStateLine(val) {
  // const timeData = { timeType: val?.showTime };
  // getPlanTableData(timeData);
}
// 点击新增折现图
function getAddLine(val) {
  const timeData = {
    createTime: val?.timeValue,
    timeType: modal.value.timeKeys,
  };
  getPlanTableData(timeData);
}
</script>
<style scoped lang="less">
.wrap_cont {
  padding: 16px 30px 0 30px;
  display: flex;
  flex-direction: column;
  .form_wrap {
    :deep(.investment-form-inline) {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .ant-form-item {
        width: 30%;
      }
    }

  }
  .tableKeys_wrap{
    width:100%

  }
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  box-shadow: none;
}
.time_cont{
  margin-bottom: 20px;
}

</style>
