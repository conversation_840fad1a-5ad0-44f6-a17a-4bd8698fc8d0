package com.chinasie.orion.xxljob;

import com.chinasie.orion.service.ProdActionItemService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/30/14:21
 * @description:
 */
@Component
public class ActionItemMessageRemindXxlJob {
    @Autowired
    private ProdActionItemService prodActionItemService;

    @XxlJob(value = "actionItemMessageRemindXxlJob")
    public void actionItemMessageRemindXxlJob(String param) {
        // 临期提醒
        prodActionItemService.drawingNear(param);
    }
}
