package com.chinasie.orion.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 作业管理 更新作业状态（phase） excel 导入模板
 */
@ApiModel(value = "JobManageWPStatusExcelTpl", description = "作业管理 更新作业状态（phase） excel 导入模板")
@Data
@ExcelIgnoreUnannotated
public class JobManageWPStatusExcelTpl implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "作业工单号")
    @ColumnWidth(22)
    private String number;

    @ApiModelProperty(value = "作业阶段")
    @ExcelProperty(value = "作业状态")
    @ColumnWidth(22)
    private String phase;

}

