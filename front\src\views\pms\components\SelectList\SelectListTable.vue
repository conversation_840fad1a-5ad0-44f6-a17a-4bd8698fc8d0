<template>
  <div
    class="add-role-user"
  >
    <div
      v-if="showLeftTree"
      class="add-role-user-left"
      :class="{'add-role-user-left-search':showTreeSearch}"
    >
      <AInputSearch
        v-if="showTreeSearch"
        v-model:value="state.searchTree"
        style="margin-bottom: 5px"
        @search="searchTreeBtn"
      />
      <BasicScrollbar>
        <ATree
          v-model:expandedKeys="state.expandedKeys"
          v-model:selectedKeys="state.selectedTreeKeys"
          :tree-data="state.treeData"
          class="modalTreeContent"
          :field-names="{title:'name',key:'id'}"
          @select="selectNode"
        />
      </BasicScrollbar>
    </div>
    <div
      class="add-role-user-content"
    >
      <orion-table
        ref="tableRef"
        :options="options"
        @smallSearch="keywordSearch"
      />
    </div>
    <div class="add-role-user-right">
      <RightList
        :selectedUser="selectTableData"
        @deleteUser="deleteSelectedUser"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  OrionTable, BasicScrollbar,
} from 'lyra-component-vue3';
import {
  Ref, ref, computed, defineProps, reactive, onMounted, nextTick,
} from 'vue';
import { Tree as ATree, InputSearch as AInputSearch, message } from 'ant-design-vue';
import RightList from './RightList.vue';

const props = defineProps({
  getTableData: {
    type: Function,
    default: null,
  },
  showTreeSearch: {
    type: Boolean,
    default: false,
  },
  getTreeApi: {
    type: Function,
    default: null,
  },
  columns: {
    type: Array,
    default: () => [],
  },
  showLeftTree: {
    type: Boolean,
    default: false,
  },
  isTableTree: {
    type: Boolean,
    default: false,
  },
  selectType: {
    type: String,
    default: 'check',
  },
  selectData: {
    type: Array,
    default: () => [],
  },
  smallSearchField: {
    type: Array,
    default: () => ['name'],
  },
});
const selectTableData:Ref<Record<any, any>[]> = ref([]);
const selectedRowKeys:Ref<string[]> = ref([]);
const state = reactive({
  treeData: [],
  selectedTreeKeys: [],
  expandedKeys: [],
  searchTree: '',
});
const keyword:Ref<string> = ref('');
const tableRef = ref();
function keywordSearch(val: string) {
  keyword.value = val;
  tableRef.value.reload();
}
const options = ref({
  deleteToolButton: 'add|enable|disable|delete',
  pagination: !props.isTableTree,
  showIndexColumn: !props.isTableTree,
  smallSearchField: props.smallSearchField,
  rowSelection: {
    type: computed(() => props.selectType),
    selectedRowKeys: computed(() => selectedRowKeys.value),
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      if (selected) {
        if (props.selectType === 'radio') {
          selectedRowKeys.value = [record.id];
          selectTableData.value = [record];
        } else {
          selectedRowKeys.value.push(record.id);
          selectTableData.value.push(record);
        }
      } else {
        selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
        selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      let tableData = tableRef.value.getDataSource();
      if (selected) {
        tableData.forEach((item) => {
          if (selectedRowKeys.value.indexOf(item.id) < 0) {
            selectedRowKeys.value.push(item.id);
            selectTableData.value.push(item);
          }
        });
      } else {
        tableData.forEach((item) => {
          selectTableData.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
          selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
        });
        // let tableData=tabl
      }
    },
  },
  showTableSetting: false,
  // 数据接口
  api: (params) => {
    if (!props.getTableData) {
      return new Promise((resolve, reject) => resolve([]));
    }
    if (props.showLeftTree) {
      if (state.selectedTreeKeys.length === 0) {
        return new Promise((resolve, reject) => resolve([]));
      }
      return props.getTableData(state.selectedTreeKeys[0], params);
    }
    return props.getTableData(params);
  },
  // 展示的列数据

  columns: props.columns.filter((item) => item.dataIndex !== 'action') || [],
});
function deleteSelectedUser(record) {
  if (record) {
    selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
    selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
  } else {
    selectTableData.value = [];
    selectedRowKeys.value = [];
  }
}
onMounted(() => {
  if (props.showLeftTree && props.getTreeApi) {
    props.getTreeApi().then((res) => {
      state.treeData = res;
      state.expandedKeys = getExpandedKeys(res);
      nextTick(() => {
        if (state.treeData.length > 0) {
          state.selectedTreeKeys = [state.treeData[0].id];
          tableRef.value.reload({ page: 1 });
        }
      });
    });
  }
  if (props.selectData.length > 0) {
    selectedRowKeys.value = props.selectData.map((item) => item.id);
    selectTableData.value = props.selectData;
  }
});
function searchTreeBtn() {
  props.getTreeApi(state.searchTree).then((res) => {
    state.treeData = res;
    state.expandedKeys = getExpandedKeys(res);
    if (state.treeData.length > 0) {
      state.selectedTreeKeys = [state.treeData[0].id];
    } else {
      state.selectedTreeKeys = [];
    }
    selectedRowKeys.value = [];
    selectTableData.value = [];
    tableRef.value.reload({ page: 1 });
  });
}
function getExpandedKeys(data:any[]) {
  let expandedKeys = [];
  data.forEach((item) => {
    if (Array.isArray(item.children) && item.children.length > 0) {
      expandedKeys.push(item.id);
      let childrenKeys = getExpandedKeys(item.children);
      if (childrenKeys.length > 0) {
        expandedKeys = expandedKeys.concat(childrenKeys);
      }
    }
  });
  return expandedKeys;
}
const selectNode = (selectedKeys, {
  selected, selectedNodes, node, event,
}) => {
  if (!selected) {
    state.selectedTreeKeys = [node.id];
    return;
  }
  tableRef.value.reload({ page: 1 });
};
defineExpose({
  getFormData,
});
function getFormData() {
  return new Promise((resolve, reject) => {
    if (selectedRowKeys.value.length > 0) {
      return resolve({
        selectedRowKeys: selectedRowKeys.value,
        selectTableData: selectTableData.value,
      });
    }
    message.warning('请选择数据');
    return reject('请选择数据');
  });
}
</script>
<style lang="less" scoped>
.add-role-user{
  display: flex;
  height: 100%;
  width: 100%;
  justify-content: space-between;
  .add-role-user-left{
    width: 250px;
    border-right: 1px solid #dddddd;
    padding: 10px;
    :deep(.modalTreeContent){
      height: 100%;
      position: relative;
      .ant-tree-treenode {
        width: 100%;
        padding: 5px 0px;
        position: relative;
        &:hover {
          color: ~`getPrefixVar('primary-color')`;
        }
      }

      .ant-tree-node-content-wrapper {
        width: 100%;
        &:hover {
          background: none;
        }
      }
      .ant-tree-treenode-selected{
        background: ~`getPrefixVar('primary-color-deprecated-f-12')`;
        color: ~`getPrefixVar('primary-color')`;
        .ant-tree-node-selected {
          background: none;
        }
      }
    }
  }
  .add-role-user-left-search{
    :deep(.basic-scrollbar){
      height: calc(~'100% - 30px');
    }
  }
  .add-role-user-content{
    flex:1;
    width: 1px;
    height: 100%;
    overflow: hidden;
  }
  .add-role-user-right{
    border-left: 1px solid ~`getPrefixVar('border-color-base')`;
    width: 220px;
  }
}
.basic-button{
   margin-left: 0 !important;
   &:last-child{
     margin-right: 0;
   }
 }
</style>