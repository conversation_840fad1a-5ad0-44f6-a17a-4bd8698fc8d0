<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>2</version>
  </parent>

  <groupId>com.googlecode.aviator</groupId>
  <artifactId>aviator</artifactId>
  <version>5.4.1</version>
  <name>aviator</name>
  <description>A lightweight, high performance expression evaluator for java</description>
  <url>https://github.com/killme2008/aviator</url>
  <inceptionYear>2010</inceptionYear>

  <developers>
    <developer>
      <name>dennis zhuang</name>
      <url>http://fnil.net/</url>
      <timezone>8</timezone>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:killme2008/aviator.git</connection>
    <developerConnection>scm:git:**************:killme2008/aviator.git</developerConnection>
    <url>**************:killme2008/aviator.git</url>
  </scm>
  <licenses>
    <license>
      <name>GNU LESSER GENERAL PUBLIC LICENSE</name>
      <url>http://www.gnu.org/licenses/lgpl.html</url>
    </license>
  </licenses>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-all</artifactId>
      <version>1.10.19</version>
      <scope>test</scope>
    </dependency>
    <!-- https://mvnrepository.com/artifact/org.springframework/spring-core -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>4.3.16.RELEASE</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
      <version>1.20</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
      <version>1.20</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.ibeetl</groupId>
      <artifactId>beetl</artifactId>
      <version>3.1.7.RELEASE</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!--
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>run-benchmarks</id>
            <phase>integration-test</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <classpathScope>test</classpathScope>
              <executable>java</executable>
              <arguments>
                <argument>-classpath</argument>
                <classpath />
                <argument>org.openjdk.jmh.Main</argument>
                <argument>.*</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
        </plugin>
      -->
      <plugin>
	<artifactId>maven-source-plugin</artifactId>
	<version>3.1.0</version>
	<executions>
	  <execution>
	    <id>attach-sources</id>
	    <phase>verify</phase>
	    <goals>
	      <goal>jar</goal>
	    </goals>
	  </execution>
	</executions>
      </plugin>
      <plugin>
	<artifactId>maven-compiler-plugin</artifactId>
	<version>3.8.1</version>
	<configuration>
	  <source>1.7</source>
	  <target>1.7</target>
	  <encoding>UTF-8</encoding>
	</configuration>
      </plugin>
      <plugin>
	<artifactId>maven-assembly-plugin</artifactId>
	<version>3.1.1</version>
	<configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
	  <descriptors>
	    <descriptor>src/assemble/src.xml</descriptor>
	    <descriptor>src/assemble/distribution.xml</descriptor>
	  </descriptors>
	  <archive>
	    <manifest>
	      <mainClass>com.googlecode.aviator.Main</mainClass>
	    </manifest>
	  </archive>
	</configuration>
      </plugin>
      <plugin>
	<artifactId>maven-jar-plugin</artifactId>
	<version>2.3.1</version>
	<configuration>
	  <archive>
	    <manifest>
	      <mainClass>com.googlecode.aviator.Main</mainClass>
	    </manifest>
	  </archive>
	</configuration>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
	<plugin>
	  <artifactId>maven-deploy-plugin</artifactId>
	  <version>2.8.2</version>
	</plugin>
      </plugins>
    </pluginManagement>
  </build>
  <distributionManagement>
    <snapshotRepository>
      <id>oss</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>oss</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
  <profiles>
    <profile>
      <id>auto-activation-for-jdk8+</id>
      <activation>
	<jdk>[1.8,)</jdk>
      </activation>
      <build>
	<!-- blow plugins require jdk8+ -->
	<plugins>
	  <!-- formatter and sort pom -->
	  <plugin>
	    <groupId>net.revelc.code.formatter</groupId>
	    <artifactId>formatter-maven-plugin</artifactId>
	    <version>2.9.0</version>
	    <configuration>
	      <configFile>${project.basedir}/eclipse-java-google-style.xml</configFile>
	      <lineEnding>LF</lineEnding>
	      <encoding>utf-8</encoding>
	    </configuration>
	    <executions>
	      <execution>
		<goals>
		  <goal>format</goal>
		</goals>
	      </execution>
	    </executions>
	  </plugin>
	  <plugin>
	    <artifactId>maven-javadoc-plugin</artifactId>
	    <version>3.1.0</version>
	    <executions>
	      <execution>
		<id>attach-javadoc</id>
		<phase>verify</phase>
		<goals>
		  <goal>jar</goal>
		</goals>
	      </execution>
	    </executions>
	    <configuration>
	      <additionalJOptions>
		<additionalJOption>-J-Duser.language=en</additionalJOption>
		<additionalJOption>-J-Duser.country=US</additionalJOption>
		<additionalJOption>-Xdoclint:none</additionalJOption>
	      </additionalJOptions>
	    </configuration>
	  </plugin>
	  <plugin>
	    <groupId>org.apache.maven.plugins</groupId>
	    <artifactId>maven-shade-plugin</artifactId>
	    <version>3.1.0</version>
	    <executions>
	      <execution>
		<phase>package</phase>
		<goals>
		  <goal>shade</goal>
		</goals>
		<configuration>
		  <transformers>
		    <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
		      <mainClass>com.googlecode.aviator.Main</mainClass>
		    </transformer>
		  </transformers>
		  <outputFile>aviator.jar</outputFile>
		</configuration>
	      </execution>
	    </executions>
	  </plugin>
	</plugins>
      </build>
    </profile>
    <profile>
      <id>release</id>
      <activation>
	<activeByDefault>false</activeByDefault>
	<property>
	  <!-- where property `performRelease` comes from? - maven-release-plugin
	       Whether to use the release profile that adds sources and javadocs to the
	       released artifact, if appropriate. If set to true, the release plugin sets
	       the property "performRelease" to true, which activates the profile "release-profile",
	       which is inherited from the super pom. Default value is: true. https://maven.apache.org/plugins-archives/maven-release-plugin-2.2.2/perform-mojo.html
	       - All models implicitly inherit from a super-POM which added maven-source-plugin
	       and maven-javadoc-plugin https://maven.apache.org/ref/3.0.4/maven-model-builder/super-pom.html -->
	  <name>performRelease</name>
	  <value>true</value>
	</property>
      </activation>
      <build>
	<plugins>
	  <plugin>
	    <artifactId>maven-gpg-plugin</artifactId>
	    <version>1.6</version>
	    <executions>
	      <execution>
		<id>sign-artifacts</id>
		<phase>verify</phase>
		<goals>
		  <goal>sign</goal>
		</goals>
	      </execution>
	    </executions>
	  </plugin>
	  <!-- Maven plugin which includes build-time git repository information
	       into an POJO / *.properties). Make your apps tell you which version exactly
	       they were built from! Priceless in large distributed deployments. https://github.com/ktoso/maven-git-commit-id-plugin -->
	  <plugin>
	    <groupId>pl.project13.maven</groupId>
	    <artifactId>git-commit-id-plugin</artifactId>
	    <version>3.0.0</version>
	    <executions>
	      <execution>
		<id>get-the-git-infos</id>
		<goals>
		  <goal>revision</goal>
		</goals>
	      </execution>
	      <execution>
		<id>validate-the-git-infos</id>
		<goals>
		  <goal>validateRevision</goal>
		</goals>
	      </execution>
	    </executions>
	    <configuration>
	      <validationProperties>
		<!-- verify that the current repository is not dirty -->
		<validationProperty>
		  <name>validating git dirty</name>
		  <value>${git.dirty}</value>
		  <shouldMatchTo>false</shouldMatchTo>
		</validationProperty>
	      </validationProperties>
	      <generateGitPropertiesFile>true</generateGitPropertiesFile>
	      <generateGitPropertiesFilename>${project.build.outputDirectory}/META-INF/scm/${project.groupId}/${project.artifactId}/git.properties</generateGitPropertiesFilename>
	    </configuration>
	  </plugin>
	</plugins>
      </build>
    </profile>
  </profiles>
  <reporting>
    <plugins>
      <plugin>
	<artifactId>maven-javadoc-plugin</artifactId>
	<configuration>
	  <encoding>utf-8</encoding>
	  <charset>utf-8</charset>
	</configuration>
      </plugin>
    </plugins>
  </reporting>
</project>
