<template>
  <BasicModal
    title="添加项目来源"
    width="1200px"
    :height="500"
    @register="registerModel"
    @cancel="cancelHandle"
    @ok="okHandle"
  >
    <div class="modal-main-wrap">
      <div class="left-wrap">
        <Menu
          v-model:selectedKeys="selectedKeys"
          class="menu-class"
          @click="handleClick"
        >
          <MenuItem
            v-for="item in menuOptions"
            :key="item.id"
            class="pl50"
          >
            {{ item.name }}
          </MenuItem>
        </Menu>
      </div>
      <div class="center-wrap">
        <div
          class="table-wrap"
        >
          <OrionTable
            v-if="state.visible"
            ref="tableRef"
            :options="tableOption"
            @selection-change="onSelectionChange"
          />
        </div>
        <!--        <div-->
        <!--          class="w-full h-full flex flex-pac"-->
        <!--        >-->
        <!--          <Empty />-->
        <!--        </div>-->
      </div>
      <div class="right-wrap">
        <SelectedList
          v-if="state.visible"
          ref="selectedRef"
          @updateTableSelect="updateTableSelect"
        />
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import {
  ref, reactive, unref, h, Ref,
} from 'vue';
import {
  BasicModal, DataStatusTag, Icon, useModalInner, OrionTable,
} from 'lyra-component-vue3';

import { Menu, MenuItem, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import SelectedList from './SelectedList.vue';
import Api from '/@/api';
import { postProjectSchemeMilestoneNodePages } from '/@/views/pms/planTemplateManagement/api';
const emit = defineEmits(['pushRows']);
const selectedKeys = ref(['plan']);
const tableRef = ref(null);
const selectedRef = ref(null);
const isShowTable = ref(false);
const menuOptions = ref([
  {
    name: '综合计划',
    id: 'plan',
  },
  {
    name: '销售合同',
    id: 'contract',
  },
  {
    name: '科研申报',
    id: 'declare',
  },
  {
    name: '线索响应',
    id: 'lead',
  },
  {
    name: '商机',
    id: 'businessOpportunity',
  },
  {
    name: '项目',
    id: 'project',
  },
]);
// const EnumTypeName = Object.freeze({
//   plan: '综合计划',
//   contract: '销售合同',
//   declare: '科研申报',
//   lead: '线索响应',
//   businessOpportunity: '商机',
//   project: '项目',
// });
enum EnumTypeName {
  plan= '综合计划',
  contract= '销售合同',
  declare= '科研申报',
  lead= '线索响应',
  businessOpportunity= '商机',
  project= '项目',
}
const columns = ref([
  {
    title: '编号',
    dataIndex: 'number',
    width: 100,
  },
  {
    title: '名称',
    dataIndex: 'name',
  },

  {
    title: '状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender({ text }) {
      return text ? h(DataStatusTag, {
        statusData: text,
      }) : '';
    },
  },
  {
    title: '责任人',
    width: 100,
    dataIndex: 'rspUserName',
  },
]);
const dataSource = ref([]);
const selectRows: Ref<string[]> = ref([]);
const tableOption = ref({});

const state = reactive({
  visible: false,
  tableRef: null,
});

const [registerModel, { closeModal }] = useModalInner(() => {
  state.visible = true;
  // selectedRef.value.setData([]);
  handleClick({
    key: 'plan',
    keyPath: ['plan'],
  });
});

// 获取表格方法
function initTable(ref) {
  state.tableRef = ref;
}

function cancelHandle() {
  state.visible = false;
}
function onSelectionChange({ rows }) {
  selectedRef.value.setData(rows);
}
function okHandle() {
  const selectList = selectedRef.value.getData();

  if (selectList.length) {
    emit('pushRows', selectList);
    closeModal();
    cancelHandle();
  } else {
    message.warning('请选择项目来源');
  }
}

function updateTableSelect(rows: Record<string, any>[] = []) {
  tableRef.value?.setSelectedRowKeys(rows.map((item) => item?.id));
}
function analyticReturnValue(result, field) {
  result.content = result.content.map((item) => ({
    ...item,
    basePlanId: item.id,
    rspUserName: item[field],
    sourceType: selectedKeys.value[0],
    type: EnumTypeName[selectedKeys.value[0]],
  }));
  return result;
}
function handleClick(e) {
  // updateTableSelect([]);
  selectedKeys.value = e.keyPath;

  if (e.key === 'plan') {
    tableOption.value = {
      api: (params) => new Api('/plan/scheme/pages').fetch(params, '', 'POST').then((result) => analyticReturnValue(result, 'rspUserName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  } else if (e.key === 'contract') {
    tableOption.value = {
      api: (params) => new Api('/pms/projectContract/page').fetch({

        ...params,
        query: {
          contractCategory: 'saleContract',
        },
      }, '', 'POST').then((result) => analyticReturnValue(result, 'principalName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  } else if (e.key === 'declare') {
    tableOption.value = {
      api: (params) => new Api('/pms/scientificResearchDemandDeclare/pages').fetch({
        ...params,
        query: {
          status: 130,
        },
      }, '', 'POST').then((result) => analyticReturnValue(result, 'resPersonName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  } else if (e.key === 'lead') {
    tableOption.value = {
      api: (params) => new Api('/pas/lead-management-conversion/pages').fetch(params, '', 'POST').then((result) => analyticReturnValue(result, 'assistUserName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  } else if (e.key === 'businessOpportunity') {
    tableOption.value = {
      api: (params) => new Api('/pas/businessOpportunity/pages').fetch(params, '', 'POST').then((result) => analyticReturnValue(result, 'creatorName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  } else if (e.key === 'project') {
    tableOption.value = {
      api: (params) => new Api('/pms/project/getPage').fetch(params, '', 'POST').then((result) => analyticReturnValue(result, 'resPersonName')),
      rowSelection: {},
      columns: columns.value,
      showSmallSearch: false,
      showToolButton: false,
      showTableSetting: false,
    };
  }
  tableRef.value?.setColumns(columns.value);
}

</script>
<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 220px;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}
.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
