package com.chinasie.orion.conts;

public enum IncomePlanDataSourceEnum {
    MANUAL_CONTRACT("manual_contract", "手工选择合同数据"),
    CONTRACT_MILESTONE("contract_milestone", "合同里程碑数据"),
    MANUAL_CLUE("manual_clue", "手工选择线索数据");
    private String value;

    private String description;
    IncomePlanDataSourceEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
