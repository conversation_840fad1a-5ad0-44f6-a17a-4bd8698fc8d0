package com.chinasie.orion.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.conts.IncomePlanDataStatusEnum;
import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.IncomePlanDataService;
import com.chinasie.orion.service.IncomePlanService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class IncomePlanDataXxJob {
    @Autowired
    private IncomePlanService incomePlanService;
    @Autowired
    private IncomePlanDataService incomePlanDataService;

    @XxlJob("incomePlanDataClose")
    public void incomePlanDataClose() throws JsonProcessingException {
        //获取上月时间
        Date date = DateUtil.lastMonth();
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String dateFormat = originalFormat.format(date);
        List<IncomePlan> incomePlans = incomePlanService.list(new LambdaQueryWrapperX<>(IncomePlan.class)
                .eq(IncomePlan::getWorkTopics,dateFormat));
        if(CollUtil.isNotEmpty(incomePlans)){
            List<String> ids = incomePlans.stream().map(IncomePlan::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<IncomePlanData> lambdaUpdateWrapper = new LambdaUpdateWrapper();
            lambdaUpdateWrapper.in(IncomePlanData::getId,ids);
            lambdaUpdateWrapper.and(wrapper->wrapper.isNull(IncomePlanData::getNumber).or().eq(IncomePlanData::getNumber,"未生成编号"));
            lambdaUpdateWrapper.set(IncomePlanData::getStatus, IncomePlanDataStatusEnum.CLOSED.getCode());
            incomePlanDataService.update(lambdaUpdateWrapper);
        }

    }
}
