package com.chinasie.orion.constant.review;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum MemberTypeEnum {
    EXPERT(0, "文审专家"),
    CREW(1, "评审组员");
    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    MemberTypeEnum(Integer status, String desc) {
        this.value = status;
        this.desc = desc;
    }
    public static MemberTypeEnum MemberEnumMapping(int code) {
        switch (code) {
            case 0:
                return MemberTypeEnum.EXPERT;
            case 1:
                return MemberTypeEnum.CREW;
        }
        return MemberTypeEnum.CREW;
    }
}
