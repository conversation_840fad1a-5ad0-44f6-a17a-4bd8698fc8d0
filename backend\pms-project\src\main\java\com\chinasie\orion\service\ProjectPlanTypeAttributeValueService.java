package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ProjectPlanTypeAttributeValue;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/11 13:48
 */
public interface ProjectPlanTypeAttributeValueService extends OrionBaseService<ProjectPlanTypeAttributeValue> {

    /**
     * 通过风险id删除项目计划类型属性值
     * @param riskIds
     * @return
     * @throws Exception
     */
    Boolean deleteByRiskIds(List<String> riskIds) throws Exception;
}
