package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/15:50
 * @description:
 */
@Data
@TableName(value = "pms_plan")
@ApiModel(value = "Plan对象", description = "计划")
public class Plan extends ObjectEntity {
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 预计开始时间
     */
    @ApiModelProperty(value = "预计开始时间")
    @TableField(value = "plan_predict_start_time")
    private Date planPredictStartTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority_level")
    private String priorityLevel;
    /**
     * 计划进度
     */
    @ApiModelProperty(value = "计划进度")
    private BigDecimal schedule;

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "plan_end_time")
    private Date planEndTime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "plan_start_time")
    private Date planStartTime;

    /**
     * 预计结束时间
     */
    @ApiModelProperty(value = "预计结束时间")
    @TableField(value = "plan_predict_end_time")
    private Date planPredictEndTime;

    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id")
    @TableField(value = "principal_id")
    private String principalId;
    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 计划图片地址
     */
    @ApiModelProperty(value = "计划图片地址")
    @TableField(value = "plan_image")
    private String planImage;


    /**
     * 计划类型ID
     */
    @ApiModelProperty(value = "计划类型ID")
    @TableField(value = "plan_type")
    private String planType;


    @ApiModelProperty(value = "状态Id")
    @TableField(value = "task_status_id")
    private String taskStatusId;


    /**
     * 计划壳Id
     */
    @ApiModelProperty(value = "计划壳Id")
    @TableField(value = "pm_id")
    private String pmId;


    @ApiModelProperty(value = "预计工时")
    @TableField(value = "man_hour")
    private BigDecimal manHour;


    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    @TableField(value = "reality_man_hour")
    private BigDecimal realityManHour;

    /**
     * 剩余工时
     */
    @ApiModelProperty(value = "剩余工时")
    @TableField(value = "residue_man_hour")
    private BigDecimal residueManHour;


    /**
     * 工时进度
     */
    @ApiModelProperty(value = "工时进度")
    @TableField(value = "man_hour_schedule")
    private BigDecimal manHourSchedule;


    /**
     * 偏差工时
     */
    @ApiModelProperty(value = "偏差工时")
    @TableField(value = "deviation_man_hour")
    private BigDecimal deviationManHour;


    /**
     * 文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;


    /**
     * 管理节点
     */
    @ApiModelProperty(value = "管理节点")
    @TableField(value = "manage_node")
    private String manageNode;

    /**
     * 风险项
     */
    @ApiModelProperty(value = "风险项")
    @TableField(value = "risk_item")
    private String riskItem;

    /**
     * 进度状态
     */
    @ApiModelProperty(value = "进度状态")
    @TableField(value = "speed_status")
    private Integer speedStatus;

    /**
     * 责任单位
     */
    @ApiModelProperty(value = "责任单位")
    @TableField(value = "res_org")
    private String resOrg;

    /**
     * 参与单位
     */
    @ApiModelProperty(value = "参与单位")
    @TableField(value = "join_org")
    private String joinOrg;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "res_user")
    private String resUser;

    /**
     * 参与科室
     */
    @ApiModelProperty(value = "参与科室")
    @TableField(value = "join_dept")
    private String joinDept;

}
