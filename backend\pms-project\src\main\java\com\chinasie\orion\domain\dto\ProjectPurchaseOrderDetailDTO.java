package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * PojectPurchaseOrderDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:20:41
 */
@ApiModel(value = "PojectPurchaseOrderDetailDTO对象", description = "项目采购订单明细")
@Data
public class ProjectPurchaseOrderDetailDTO extends ObjectDTO implements Serializable{

    /**
     * 来源单号
     */
    @ApiModelProperty(value = "来源单号")
    private String sourceNumber;

    /**
     * 物资/服务计划编号
     */
    @ApiModelProperty(value = "物资/服务计划编号")
    private String planNumber;

    /**
     * 物资/服务编码
     */
    @ApiModelProperty(value = "物资/服务编码")
    @NotBlank(message = "物资/服务编码不能为空")
    private String goodsServiceNumber;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @NotBlank(message = "物资/服务描述不能为空")
    @Size(max = 100, message = "物资/服务描述过长，建议控制在100字符以内")
    private String description;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @NotBlank(message = "规格型号不能为空")
    @Size(max = 100, message = "规格型号过长，建议控制在100字符以内")
    private String normsModel;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @NotBlank(message = "计量单位不能为空")
    private String unitCode;


    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @NotNull(message = "采购数量不能为空")
    private BigDecimal purchaseAmount;

    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    @NotNull(message = "需求日期不能为空")
    private Date demandDate;

    /**
     * 单价（不含税）
     */
    @ApiModelProperty(value = "单价（不含税）")
    @NotNull(message = "单价（不含税）不能为空")
    private BigDecimal noTaxPrice;

    /**
     * 总金额（不含税）
     */
    @ApiModelProperty(value = "总金额（不含税）")
    @NotNull(message = "总金额（不含税）不能为空")
    private BigDecimal noTaxTotalAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @NotNull(message = "税率不能为空")
    private BigDecimal taxRate;

    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    @NotNull(message = "单价（含税）不能为空")
    private BigDecimal haveTaxPrice;

    /**
     * 总金额（含税）
     */
    @ApiModelProperty(value = "总金额（含税）")
    @NotNull(message = "总金额（含税）不能为空")
    private BigDecimal haveTaxTotalAmt;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    private String purchaseId;

}
