package com.chinasie.orion.domain.vo.statics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/06/13/16:17
 * @description:
 */
@Data
@ApiModel(value = "ContractStatisticsDTO对象", description = "合同统计对象")
public class ContractStatisticsDTO {
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractMoney;
}
