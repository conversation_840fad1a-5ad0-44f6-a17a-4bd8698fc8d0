<template>
  <div class="flex modalTable">
    <div class="modalTable-left ">
      <BasicTree
        v-bind="$attrs"
        :tree-data-api="treeDataApi"
        :is-toolbar="false"
        :check-strictly="true"
        :default-expand-all="true"
        :replace-fields="{ children: 'children', title: 'name', key: 'id' }"
        @select="select"
        @select-node="select"
      />
    </div>
    <div class="modalTable-center ">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        @selectionChange="selectionChange"
      />
    </div>

    <div class="modalTable-right">
      <SelectedList
        :currentAllSelected="state.currentAllSelected"
        :rowKey="tableOptions.rowKey"
        @deleteAfter="deleteAfter"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  reactive, ref, defineEmits, defineProps, defineExpose,
} from 'vue';
import { OrionTable, BasicTree } from 'lyra-component-vue3';
import Api from '/@/api/index';
import { differenceWith, isEqual } from 'lodash-es';

import SelectedList from './SelectedList.vue';

const props = defineProps({});
const emit = defineEmits({});
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  pagination: true,
  showToolButton: false,
  showSmallSearch: false,
  smallSearchField: ['name', 'number'],
  showTableSetting: false,
  isTreeTable: false,
  rowKey: 'id',
  resizeHeightOffset: 160,
  api: (params) => {
    if (!state.treeSelectId) {
      return new Promise((resolve, reject) => resolve([]));
    }
    return new Api('/pdm').fetch(params, `parameterPool/usepages/${state.treeSelectId}`, 'POST');
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      width: 120,
    },
    {
      title: '参数名称',
      align: 'left',
      dataIndex: 'name',
      minWidth: 150,
    },
    {
      title: '提供部门',
      align: 'left',
      dataIndex: 'providerDeptNames',
      minWidth: 100,
    },
  ],
});
const state = reactive({
  currentAllSelected: [],
  allSelect: [],
  treeSelectId: '',
});

function selectionChange({ rows }) {
  const result = differenceWith(rows, state.currentAllSelected, isEqual).concat(differenceWith(state.currentAllSelected, rows, isEqual));
  state.currentAllSelected = JSON.parse(JSON.stringify(rows));
  result.forEach((item) => {
    const index = state.currentAllSelected.findIndex((it) => it[tableOptions.value.rowKey] === item[tableOptions.value.rowKey]);
    if (index >= 0) {
      state.allSelect.splice(index, 1);
    } else {
      state.allSelect.push(item);
    }
  });
}

// 右侧删除item后的emit
function deleteAfter(arr) {
  if (arr?.length) {
    tableRef.value.setSelectedRowKeys(arr.map((item) => item.id));
  } else {
    tableRef.value.setSelectedRowKeys([]);
  }
}

async function treeDataApi() {
  return await new Api('/pdm').fetch('', 'parameterPoolDir/tree', 'GET');
}

function select(data) {
  state.treeSelectId = data;
  tableRef.value?.reload({ page: 1 });
}

defineExpose({
  state,
});
</script>

<style scoped lang="less">
.modalTable {
  height: 100%;

  .modalTable-left {
    width: 23%;
    background-color: #ccc;
    overflow: auto;
    align-items: stretch;
  }

  .modalTable-center {
    width: 57%;
    overflow: auto;
    align-items: stretch;
  }

  .modalTable-right {
    width: 20%;
    overflow: auto;
    background-color: #f3f2f2;
  }
}

:deep(.ant-basic-table) {
  padding: 0 !important;
}

:deep(.orion-table-header-wrap) {
  height: 0 !important;
}
</style>
