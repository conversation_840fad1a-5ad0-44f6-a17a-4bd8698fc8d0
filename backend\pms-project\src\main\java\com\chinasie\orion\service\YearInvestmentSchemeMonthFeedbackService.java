package com.chinasie.orion.service;


import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.domain.dto.YearInvestmentSchemeMonthFeedbackDTO;
import com.chinasie.orion.domain.entity.YearInvestmentSchemeMonthFeedback;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeMonthFeedbackVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;


import java.util.List;

/**
 * <p>
 * YearInvestmentSchemeMonthFeedback 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15 19:21:49
 */
public interface YearInvestmentSchemeMonthFeedbackService extends OrionBaseService<YearInvestmentSchemeMonthFeedback> {


    /**
     * 自动获取的值
     *
     * @param yearId
     * @return
     */
    YearInvestmentSchemeMonthFeedbackVO initValue(String yearId) throws Exception;

    /**
     * 详情
     * <p>
     * * @param id
     */
    YearInvestmentSchemeMonthFeedbackVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param yearInvestmentSchemeMonthFeedbackDTO
     */
    YearInvestmentSchemeMonthFeedbackVO create(YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param yearInvestmentSchemeMonthFeedbackDTO
     */
    YearInvestmentSchemeMonthFeedbackVO edit(YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    List<YearInvestmentSchemeMonthFeedbackVO> list(String yearId,String pageCode,String containerCode) throws Exception;

    /**
     * 状态变更
     *
     * @param message
     * @return
     */
    Boolean updateStatus(ChangeStatusMessageDTO message) throws Exception;

    /**
     * 每月15号需要给项目负责人发送待办提醒通知，引导其完成月度计划反馈的编制
     */
    void sendTodo() throws Exception;
}
