package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AdvancePaymentInformation Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:25:21
 */
@TableName(value = "pmsx_advance_payment_information")
@ApiModel(value = "AdvancePaymentInformationEntity对象", description = "预收款信息")
@Data

public class AdvancePaymentInformation extends  ObjectEntity  implements Serializable{

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year")
    private String year;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @TableField(value = "voucher_number")
    private String voucherNumber;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    @TableField(value = "accrued_amt")
    private BigDecimal accruedAmt;

    /**
     * 已清账金额
     */
    @ApiModelProperty(value = "已清账金额")
    @TableField(value = "cleared_amt")
    private BigDecimal clearedAmt;

    /**
     * 未结清预收款金额
     */
    @ApiModelProperty(value = "未结清预收款金额")
    @TableField(value = "un_adv_receivable_amt")
    private BigDecimal unAdvReceivableAmt;

    /**
     * 本次清账金额
     */
    @ApiModelProperty(value = "本次清账金额")
    @TableField(value = "current_clear_amt")
    private BigDecimal currentClearAmt;

    /**
     * 本次清账金额（不含税）
     */
    @ApiModelProperty(value = "本次清账金额（不含税）")
    @TableField(value = "current_clear_amt_ex_tax")
    private BigDecimal currentClearAmtExTax;

    /**
     * 不清账原因
     */
    @ApiModelProperty(value = "不清账原因")
    @TableField(value = "no_clear_reason")
    private String noClearReason;

    /**
     * 收入计划填报ID
     */
    @ApiModelProperty(value = "收入计划填报ID")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    /**
     * 收入计划填报数据Id
     */
    @ApiModelProperty(value = "收入计划填报数据Id")
    @TableField(value = "income_plan_data_id")
    private String incomePlanDataId;

}
