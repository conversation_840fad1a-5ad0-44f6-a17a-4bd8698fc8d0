package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(value = "MilestoneLineChartVO对象", description = "经营看板-里程碑折线图")
@Data
public class MilestoneLineChartQueryVO implements Serializable {


    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer saleMonth;


    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal acceptAmount;

    /**
     * 计划验收金额
     */
    @ApiModelProperty(value = "计划验收金额")
    private BigDecimal planAmount;

}

