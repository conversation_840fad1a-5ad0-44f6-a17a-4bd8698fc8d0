package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolInsVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/03/12/17:36
 * @description:
 */
@Data
public class RelationParamInsVO  implements Serializable {

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;


    @ApiModelProperty(value = "模板Id")
    private String modelId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    private String insId;
    @ApiModelProperty(value = "参数名称")
    private String paramName;

    @ApiModelProperty(value = "参数实列对象")
    private ParameterPoolInsVO insEntity;



}
