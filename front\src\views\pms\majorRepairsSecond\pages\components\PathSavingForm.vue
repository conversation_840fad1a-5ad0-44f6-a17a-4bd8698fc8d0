<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, openBasicSelectModal, UploadList, useForm,
} from 'lyra-component-vue3';
import {
  h, nextTick, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { getJobNoProps, setJobNoProps } from '/@/views/pms/materialManage/components/hooks';
import { message } from 'ant-design-vue';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '大修基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'jobManageNumber',
    label: '工单号',
    componentProps({ formModel }) {
      return {
        readonly: true,
        onClick() {
          openBasicSelectModal({
            title: '请选择',
            ...getJobNoProps('path', {
              repairRound: props?.record?.repairRound,
            }),
            onOk(records: any[]) {
              const numberItem = records?.[0];
              if (numberItem) {
                setJobNoProps(setFieldsValue, numberItem);
              }
            },
          } as any);
        },
        disabled: props?.record?.id,
      };
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'jobManageId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'workJobTitle',
    label: '工作抬头',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'jobManageName',
    label: '工作名称',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'majorRepairTurn',
    label: '大修轮次',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'isMajorProject',
    label: '是否重大项目',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    component: 'Select',
  },
  {
    field: 'actualEndTime',
    label: '结项日期',
    componentProps: {
      disabled: true,
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '关键路径节约信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'isEconomize',
    label: '关键路径是否节约',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
  {
    field: 'number',
    label: '编号',
    componentProps: {},
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'optimizeField',
    label: '优化领域',
    componentProps: {
      dictNumber: 'pms_area_of_optimization',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'majorRepairType',
    label: '大修类型',
    componentProps: {
      dictNumber: 'pms_major_repair_type',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'applicationCrew',
    label: '应用机组类型',
    componentProps: {
      dictNumber: 'pmx_application_unit_type',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'innTechOrWork',
    label: '创优技术或工作',
    componentProps: {},
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'planDuration',
    label: '计划工期（H）',
    componentProps({ formModel }) {
      return {
        min: 0,
        max: 99999,
        precision: 1,
        formatter(value) {
          return Number(value).toString();
        },
        async onChange() {
          await nextTick();
          const number = Number(formModel?.planDuration || 0) - Number(formModel?.actualExeDuration || 0);
          await setFieldsValue({
            economizeDuration: number < 0 ? 0 : number,
          });
        },
      };
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'actualExeDuration',
    label: '实际执行用时（H）',
    componentProps({ formModel }) {
      return {
        min: 0,
        max: formModel?.planDuration || 99999,
        precision: 0,
        async onChange() {
          await nextTick();
          const number = Number(formModel?.planDuration || 0) - Number(formModel?.actualExeDuration || 0);
          await setFieldsValue({
            economizeDuration: number < 0 ? 0 : number,
          });
        },
      };
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'economizeDuration',
    label: '节约（H）',
    componentProps: {
      min: 0,
      max: 99999,
      precision: 0,
      disabled: true,
    },
    component: 'InputNumber',
  },
  {
    field: 'delayReason',
    label: '延误原因',
    componentProps: {
      maxlength: 200,
    },
    component: 'Input',
  },
  {
    field: 'content',
    label: '内容介绍',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '关键路径节约附件',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/major-repair-plan-economize').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params: Record<string, any> = {
      id: props?.record?.id,
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      if (!params?.majorRepairTurn) {
        reject('');
        return message.info('该数据不存在大修信息，请重新选择');
      }
      new Api('/pms/major-repair-plan-economize').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
