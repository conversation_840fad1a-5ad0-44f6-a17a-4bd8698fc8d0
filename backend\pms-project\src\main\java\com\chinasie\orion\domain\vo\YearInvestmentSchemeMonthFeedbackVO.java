package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * YearInvestmentSchemeMonthFeedback Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-15 19:21:49
 */
@ApiModel(value = "YearInvestmentSchemeMonthFeedbackVO对象", description = "月度投资计划反馈")
@Data
public class YearInvestmentSchemeMonthFeedbackVO extends ObjectVO implements Serializable {

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;


    /**
     * 月度
     */
    @ApiModelProperty(value = "月度")
    private String month;


    /**
     * 年度形象进度
     */
    @ApiModelProperty(value = "年度形象进度")
    private String yearProcess;


    /**
     * 总体进度执行情况
     */
    @ApiModelProperty(value = "总体进度执行情况")
    private String totalProcess;


    /**
     * 项目总体进度滞后情况
     */
    @ApiModelProperty(value = "项目总体进度滞后情况")
    private String delayDesc;


    /**
     * 本月进度执行情况
     */
    @ApiModelProperty(value = "本月进度执行情况")
    private String monthProcess;

    /**
     * 本月投资计划执行状态
     */
    @ApiModelProperty(value = "本月投资计划执行状态")
    private Integer monthDoStatus;


    /**
     * 本月执行偏差原因及纠偏措施
     */
    @ApiModelProperty(value = "本月执行偏差原因及纠偏措施")
    private String reason;


    /**
     * 1-M月计划
     */
    @ApiModelProperty(value = "1-M月计划")
    private BigDecimal mplanDo=new BigDecimal("0");

    /**
     * 上月实际执行
     */
    @ApiModelProperty(value = "上月实际执行")
    private BigDecimal lastPracticeDo=new BigDecimal("0");

    /**
     * 1-M月实际执行
     */
    @ApiModelProperty(value = "1-M月实际执行")
    private BigDecimal mpracticeDo=new BigDecimal("0");

    /**
     * 1-M月计划执行率
     */
    @ApiModelProperty(value = "1-M月计划执行率")
    private String mpracticeDoRate;


    /**
     * 下月进度计划
     */
    @ApiModelProperty(value = "下月进度计划")
    private String nextProcess;


    /**
     * 年度完成率
     */
    @ApiModelProperty(value = "下月进度计划")
    private String yearCompleteRate;


    /**
     * Y年投资计划
     */
    @ApiModelProperty(value = "Y年投资计划")
    private BigDecimal yinvestmentPlan=new BigDecimal("0");


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;



    /**
     * 年度投资计划Id
     */
    @ApiModelProperty(value = "年度投资计划Id")
    private String yearInvestmentId;

}
