package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/10 16:41
 */
@Data
public class ComponentSimpleVo implements Serializable {

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 预览图
     */
    @ApiModelProperty(value = "预览图")
    private String imageId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

}
