package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectBudget Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 14:48:31
 */
@ApiModel(value = "ProjectBudgetDTO对象", description = "项目预算表")
@Data
public class ProjectBudgetDTO extends ObjectDTO implements Serializable{


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    @NotEmpty(message = "预算名称不能为空")
    private String name;

    /**
     * 成本中心ID
     */
    @ApiModelProperty(value = "成本中心ID")
    @NotEmpty(message = "成本中心ID不能为空")
    private String costCenterId;

    /**
     * 成本中心名字
     */
    @ApiModelProperty(value = "成本中心名字")
    private String costCenterName;

    /**
     * 费用科目ID
     */
    @ApiModelProperty(value = "费用科目ID")
    @NotEmpty(message = "费用项目ID不能为空")
    private String expenseAccountId;

    /**
     * 费用科目名
     */
    @ApiModelProperty(value = "费用科目名")
    private String expenseAccountName;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @NotEmpty(message = "年度不能为空")
    private String year;

    /**
     * 年度预算总费用
     */
    @ApiModelProperty(value = "年度预算总费用")
    @DecimalMax(value="************.00", message = "年度预算总费用必须小于100,000,000,000.00")
    private BigDecimal yearExpense;

    /**
     * 费用预算月度表ID
     */
    @ApiModelProperty(value = "费用预算月度表ID")
    private String monthBudgetId;

    /**
     * 实际总成本
     */
    @ApiModelProperty(value = "实际总成本")
    private BigDecimal totalCost;

    /**
     * 差价
     */
    @ApiModelProperty(value = "差价")
    private BigDecimal priceDifference;

    /**
     * 是否超出预算 0为未超预算，1为超出预算
     */
    @ApiModelProperty(value = "是否超出预算 0为未超预算，1为超出预算")
    private Integer isOut;

    /**
     * 执行进度
     */
    @ApiModelProperty(value = "执行进度")
    private Integer executionSchedule;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    private BigDecimal januaryMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    private BigDecimal februaryMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    private BigDecimal marchMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    private BigDecimal aprilMoney;

    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    private BigDecimal mayMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    private BigDecimal juneMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    private BigDecimal augustMoney;

    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    private BigDecimal decemberMoney;

}
