<template>
  <a-form
    label-align="left"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 14 }"
  >
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <a-form-item label="计划名称">
            {{ father.form.name }}
          </a-form-item>
          <a-form-item label="计划编号">
            {{ father.form.number }}
          </a-form-item>
          <a-form-item label="所属项目">
            {{ father.form.projectName }}
          </a-form-item>
          <a-form-item label="所属类型">
            {{ father.form.planTypeName }}
          </a-form-item>
          <a-form-item label="负责人">
            {{ father.form.principalName }}
          </a-form-item>
          <a-form-item label="结束日期">
            {{ formatDate(father.form.planPredictEndTime) }}
          </a-form-item>
          <a-form-item label="状态">
            {{ father.form.statusName }}
          </a-form-item>
          <a-form-item label="描述">
            {{ father.form.remark }}
          </a-form-item>
          <a-form-item label="修改人">
            {{ father.form.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ formatDate(father.form.modifyTime) }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ formatDate(father.form.createTime) }}
          </a-form-item>
        </BasicTitle>
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
&nbsp;
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import { Row, Col, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import dayjs from 'dayjs';
import { reactive, toRefs } from 'vue';
export default {
  name: 'View',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      father: props.data,
    });
    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }

    return {
      ...toRefs(state),
      formatDate,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.basicTitle) {
    .basicTitle_content {
      padding-left: 40px !important;
      height: calc(100vh - 300px);
      overflow: auto;
    }
    .ant-form-item {
      margin-bottom: 10px;
    }
  }
</style>
