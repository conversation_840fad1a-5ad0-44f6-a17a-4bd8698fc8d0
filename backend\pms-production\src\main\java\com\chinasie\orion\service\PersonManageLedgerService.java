package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PersonManageLedger;
import com.chinasie.orion.domain.dto.PersonManageLedgerDTO;
import com.chinasie.orion.domain.vo.PersonManageLedgerVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonManageLedger 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:49
 */
public interface PersonManageLedgerService extends OrionBaseService<PersonManageLedger> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    PersonManageLedgerVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param personManageLedgerDTO
     */
    String create(PersonManageLedgerDTO personManageLedgerDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param personManageLedgerDTO
     */
    Boolean edit(PersonManageLedgerDTO personManageLedgerDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PersonManageLedgerVO> pages(Page<PersonManageLedgerDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<PersonManageLedgerVO> vos) throws Exception;

    /**
     *  通过唯一ID获取信息
     * @param uniqueId
     * @return
     */
    PersonManageLedger singleByUniqueId(String uniqueId);
}
