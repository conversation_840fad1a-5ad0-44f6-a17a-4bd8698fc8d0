package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.GoodsServiceAddDTO;
import com.chinasie.orion.domain.dto.GoodsServiceStoreDTO;
import com.chinasie.orion.domain.entity.GoodsServiceStore;
import com.chinasie.orion.domain.vo.GoodsServiceStoreVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * GoodsServiceStore 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 20:59:53
 */
public interface GoodsServiceStoreService extends OrionBaseService<GoodsServiceStore> {
        /**
         *  详情
         *
         * * @param id
         */
        GoodsServiceStoreVO detail(String id)  throws Exception;

    /**
     * 新增
     * <p>
     * * @param goodsServiceStoreDTO
     */
    GoodsServiceStoreVO create(GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param goodsServiceStoreDTO
     */
    Boolean edit(GoodsServiceStoreDTO goodsServiceStoreDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    PageResult<GoodsServiceStoreVO> getGoodsServiceStorePage(Page<GoodsServiceStoreDTO> pageRequest) throws Exception;

    GoodsServiceStoreVO detailCreate(GoodsServiceAddDTO goodsServiceAddDTO) throws Exception;
}
