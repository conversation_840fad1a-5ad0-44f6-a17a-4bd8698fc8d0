import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 需求分页 */
  demandPage = 'demand-management/getTree',
  /* 需求简单分页 */
  demandSimplePage = 'demand-management/getSimpleTree',
  /* 详情 */
  itemDetails = 'demand-management/detail',
  /* 需求来源 */
  demandSource = 'demand-management/demandSource',
  /* 需求类型 */
  demandType = 'demand-management/demandType',
  /* 优先级 */
  priorityLevel = 'demand-management/priorityLevel',
  /* 添加 */
  addDemand = 'demand-management/save',
  /* 编辑 */
  editDemand = 'demand-management/edit',
  /* 编辑 */
  deletDemand = 'demand-management/removeBatch',
  /* 状态 */
  statusDemand = 'demand-management/status',
  /* 搜索 */
  queryDemand = 'demand-management/getPage',
  // 详情页 计划关联列表

  getDemandContactPage = 'demand-management/relation/plan',
  // 详情页 计划关联列表

  delDemandContactPage = 'demand-management/relation/plan/batch',
  // 需求状态

  getDemandStatus = 'project-task-status/policy/status/list'
}

/**
 * @description: 需求管理
 */
// 分页
export function getDemandStatusApi(params) {
  return new Api(base).fetch('', `${zApi.getDemandStatus}/${params}`, 'GET');
}
// 分页
export function demandPageApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.demandPage}/`, 'POST');
}
// 简单分页
export function demandSimplePageApi(params) {
  return new Api(base).fetch(params, `${zApi.demandSimplePage}/`, 'POST');
}
// 详情
export function itemDetailsApi(params, love) {
  return new Api(base, love).fetch('', `${zApi.itemDetails}/${params}/`, 'GET');
}
// 详情页 计划关联列表
export function getDemandContactPageApi(params, love, obj = {}) {
  return new Api(base, love).fetch(obj, `${zApi.getDemandContactPage}/${params}/`, 'POST');
}
// 需求来源
export function demandSourceApi() {
  return new Api(base).fetch('', `${zApi.demandSource}/`, 'GET');
}
// 需求类型
export function demandTypeApi() {
  return new Api(base).fetch('', `${zApi.demandType}/`, 'GET');
}
// 优先级
export function priorityLevelApi() {
  return new Api(base).fetch('', `${zApi.priorityLevel}/`, 'GET');
}
// 添加
export function addDemandApi(params) {
  const love = {
    name: params?.name,
    className: 'DemandManagement',
    moduleName: '需求管理', // 模块名称
    type: 'SAVE', // 操作类型
    remark: `添加了【${params?.name}】`,
  };
  return new Api(base, love).fetch(params, `${zApi.addDemand}/`, 'POST');
}
// 编辑
export function editDemandApi(params) {
  const love = {
    name: params?.name,
    className: 'DemandManagement',
    moduleName: '需求管理', // 模块名称
    type: 'UPDATE', // 操作类型
    remark: `编辑了【${params?.name}】`,
  };
  return new Api(base, love).fetch(params, `${zApi.editDemand}/`, 'PUT');
}
// 删除
export function deletDemandApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.deletDemand}/`, 'DELETE');
}
// 删除详情页关联计划
export function delDemandContactPageApi(params, love) {
  return new Api(base, love).fetch(params, `${zApi.delDemandContactPage}/`, 'DELETE');
}
// 状态
export function statusDemandApi() {
  return new Api(base).fetch('', `${zApi.statusDemand}/`, 'GET');
}
// 搜索
export function queryDemandApi(params) {
  return new Api(base).fetch(params, `${zApi.queryDemand}/`, 'POST');
}
