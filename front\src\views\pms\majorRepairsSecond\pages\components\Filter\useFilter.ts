import {
  computed, h, Ref, ref, unref,
} from 'vue';
import FilterButton from './FilterButton.vue';

export function useFilter(): {
    tableWrapRef: Ref<HTMLElement | undefined>;
    FilterButtonRender: (props: any) => any;
    } {
  const tableWrapRef: Ref<HTMLElement | undefined> = ref();

  const popoverWidth = computed(() => unref(tableWrapRef)?.getBoundingClientRect()?.width);

  return {
    tableWrapRef,
    FilterButtonRender: (props: {
            filterOptions: any[]
        }) => h(FilterButton, {
      ...props,
      popoverWidth: unref(popoverWidth),
    }),
  };
}
