<template>
  <Layout
    :options="{ body: { scroll: true } }"
    class="clue-board"
  >
    <div class="flex-row">
      <AmountSignedChart />
      <AssessmentTrendCard />
    </div>
  </Layout>
</template>
<script lang="ts" setup>
import { Layout } from 'lyra-component-vue3';
import {
  AmountSignedChart,
  AssessmentTrendCard,
} from './index';

</script>
<style lang="less" scoped>
.clue-board {
  position: relative;
  .flex-row{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: space-between;
    margin: 50px 0;
  }
  .header-top {
    position: relative;
    padding: 17px 0;
    .title-wrap-main{
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      flex: 1;
    }
    p{
      flex: none;
      margin: 0;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
    }
    .allBtn{
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 20px;
      p{
        margin: 0;
        color: #969EB4;
        font-size: 14px;
      }
    }
    .flex-right {
      position: absolute;
      top: 14px;
      right: -20px;
      height: 30px;
      margin: 0 20px;
      display: flex;
      .select-wd{
        margin: 0 5px;
      }
      .select-wd:nth-child(1){
        width: 118px!important;
      }
      .select-wd:nth-child(2){
        width: 96px!important;
      }
      .select-wd:nth-child(3){
        width: 100px!important;
      }
    }
  }
}
</style>