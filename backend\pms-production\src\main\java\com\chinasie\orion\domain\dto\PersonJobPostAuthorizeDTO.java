package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PersonJobPostAuthorize DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@ApiModel(value = "PersonJobPostAuthorizeDTO对象", description = "人员岗位授权记录落地")
@Data
@ExcelIgnoreUnannotated
public class PersonJobPostAuthorizeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 岗位编号
     */
    @ApiModelProperty(value = "岗位编号")
    @ExcelProperty(value = "岗位编号 ", index = 0)
    private String jobPostCode;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "岗位名称 ", index = 1)
    private String jobPostName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 2)
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @ExcelProperty(value = "基地名称 ", index = 3)
    private String baseName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @ExcelProperty(value = "授权到期日期 ", index = 4)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;

    /**
     * 授权状态
     */
    @ApiModelProperty(value = "授权状态")
    @ExcelProperty(value = "授权状态 ", index = 5)
    private Integer authorizeStatus;

    /**
     * 授权状态（101-未授权，130-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    @ExcelProperty(value = "授权状态（101-未授权，130-已授权）", index = 6)
    private String authorizeStatusName;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @ExcelProperty(value = "是否等效 ", index = 7)
    private Boolean isEquivalent;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    @ExcelProperty(value = "作业编号 ", index = 8)
    private String jobCode;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 9)
    private String repairRound;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userCode;

    @ApiModelProperty(value = "来源ID - 岗位授权id")
    private String sourceId;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileDTO> fileDTOList;

    @ApiModelProperty(value = "授权起始日期")
    private Date startData;
}
