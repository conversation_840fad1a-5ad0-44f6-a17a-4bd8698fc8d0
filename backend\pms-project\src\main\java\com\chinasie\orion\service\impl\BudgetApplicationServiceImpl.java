package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.dict.BudgetDict;
import com.chinasie.orion.domain.dto.BudgetApplicationDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationSaveDTO;
import com.chinasie.orion.domain.entity.BudgetApplication;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateExpenseSubject;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetApplicationMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetApplicationService;
import com.chinasie.orion.service.CostCenterDataService;
import com.chinasie.orion.service.ProjectApprovalService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateExpenseSubjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * BudgetApplication 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:09
 */
@Service
@Slf4j
public class BudgetApplicationServiceImpl extends OrionBaseServiceImpl<BudgetApplicationMapper, BudgetApplication> implements BudgetApplicationService {

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private CodeBo codeBo;

    @Resource
    private CostCenterDataService costCenterService;

    @Autowired
    private DictBo dictBo;

    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private ProjectApprovalEstimateExpenseSubjectService projectApprovalEstimateExpenseSubjectService;


    @Resource
    private ProjectService projectService;

    @Autowired
    private NumberApiService numberApiService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetApplicationVO detail(String id, String pageCode) throws Exception {
        BudgetApplication budgetApplication =this.getById(id);
        BudgetApplicationVO result = BeanCopyUtils.convertTo(budgetApplication,BudgetApplicationVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param budgetApplicationDTO
     */
    @Override
    public  String create(BudgetApplicationDTO budgetApplicationDTO) throws Exception {
        BudgetApplication budgetApplication =BeanCopyUtils.convertTo(budgetApplicationDTO,BudgetApplication::new);
//        String code = codeBo.createCode(ClassNameConstant.BUDGET_APPLICATION, ClassNameConstant.NUMBER, false, null);
//        budgetApplication.setNumber(code);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.BUDGET_APPLICATION);
        String number =  numberApiService.generate(request);
        budgetApplication.setNumber(number);
        this.save(budgetApplication);

        String rsp=budgetApplication.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param budgetApplicationDTO
     */
    @Override
    public Boolean edit(BudgetApplicationDTO budgetApplicationDTO) throws Exception {
        BudgetApplication budgetApplication =BeanCopyUtils.convertTo(budgetApplicationDTO,BudgetApplication::new);

        this.updateById(budgetApplication);

        String rsp=budgetApplication.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {

        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetApplicationVO> pages( Page<BudgetApplicationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetApplication> condition = new LambdaQueryWrapperX<>( BudgetApplication. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetApplication::getCreateTime);


        Page<BudgetApplication> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetApplication::new));

        PageResult<BudgetApplication> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BudgetApplicationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetApplicationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetApplicationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算申请表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetApplicationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            BudgetApplicationExcelListener excelReadListener = new BudgetApplicationExcelListener();
        EasyExcel.read(inputStream,BudgetApplicationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetApplicationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算申请表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetApplication> budgetApplicationes =BeanCopyUtils.convertListTo(dtoS,BudgetApplication::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetApplication-import::id", importId, budgetApplicationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetApplication> budgetApplicationes = (List<BudgetApplication>) orionJ2CacheService.get("pmsx::BudgetApplication-import::id", importId);
        log.info("预算申请表导入的入库数据={}", JSONUtil.toJsonStr(budgetApplicationes));

        this.saveBatch(budgetApplicationes);
        orionJ2CacheService.delete("pmsx::BudgetApplication-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetApplication-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetApplication> condition = new LambdaQueryWrapperX<>( BudgetApplication. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetApplication::getCreateTime);
        List<BudgetApplication> budgetApplicationes =   this.list(condition);

        List<BudgetApplicationDTO> dtos = BeanCopyUtils.convertListTo(budgetApplicationes, BudgetApplicationDTO::new);

        String fileName = "预算申请表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetApplicationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BudgetApplicationVO> vos)throws Exception {
       if(CollUtil.isNotEmpty(vos)) {
           String projectId = vos.get(0).getProjectId();
           Project project = projectService.getById(projectId);
           List<String> costCenterIds = vos.stream().map(BudgetApplicationVO::getCostCenterId).collect(Collectors.toList());
           List<CostCenterVO> costCenterVOS = costCenterService.getList(costCenterIds);
           Map<String, String> costCenterMap = costCenterVOS.stream().collect(Collectors.toMap(CostCenterVO::getId, CostCenterVO::getName));
           Map<String, String> objectTypeMap = dictBo.getDictValue(BudgetDict.BUDGET_OBJECT_TYPE);
           Map<String, String> periodTypeMap = dictBo.getDictValue(BudgetDict.BUDGET_PERIOD_TYPE);
           Map<String, String> currencyMap = dictBo.getDictValue(BudgetDict.BUDGET_CURRENCY);
           vos.forEach(vo -> {
               if (StrUtil.isNotBlank(costCenterMap.get(vo.getCostCenterId()))) {
                   vo.setCostCenterName(costCenterMap.get(vo.getCostCenterId()));
               }
               if (StrUtil.isNotBlank(objectTypeMap.get(vo.getBudgetObjectType()))) {
                   vo.setBudgetObjectTypeName(objectTypeMap.get(vo.getBudgetObjectType()));
               }
               if (StrUtil.isNotBlank(periodTypeMap.get(vo.getTimeType()))) {
                   vo.setTimeTypeName(periodTypeMap.get(vo.getTimeType()));
               }
               if (StrUtil.isNotBlank(currencyMap.get(vo.getCurrency()))) {
                   vo.setCurrencyName(currencyMap.get(vo.getCurrency()));
               }
               if (ObjectUtil.isNotEmpty(project)) {
                   vo.setBudgetObjectName(project.getName());
                   vo.setProjectName(project.getName());
               }
           });
       }


    }




    public static class BudgetApplicationExcelListener extends AnalysisEventListener<BudgetApplicationDTO> {

        private final List<BudgetApplicationDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetApplicationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BudgetApplicationDTO> getData() {
            return data;
        }
    }



    @Override
    public  Boolean saveBatchBudgetApplication(BudgetApplicationSaveDTO budgetApplicationDTO) throws Exception {
        if(CollectionUtil.isNotEmpty(budgetApplicationDTO.getDeleteIds())){
            this.removeBatchByIds(budgetApplicationDTO.getDeleteIds());
        }
        List<BudgetApplicationDTO> budgetApplicationDTOS = new ArrayList<>();
        budgetApplicationDTOS.addAll(budgetApplicationDTO.getCreateBudgetApplicationLists());
        budgetApplicationDTOS.addAll(budgetApplicationDTO.getUpdateBudgetApplicationLists());
        if (CollUtil.isNotEmpty(budgetApplicationDTOS)) {
            Map<List<String>, Long> budgetMap = budgetApplicationDTOS.stream()
                    .collect(Collectors.groupingBy(
                            b -> Arrays.asList(b.getCostCenterId(),b.getExpenseSubjectNumber(),b.getTimeType(),b.getBudgetTime(),b.getBudgetObjectType(),b.getBudgetObjectId()),
                            Collectors.counting()
                     ));
            boolean hasDuplicates = budgetMap.values().stream().anyMatch(count -> count > 1);
            if (hasDuplicates) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "存在相同的数据");
            }
        }



        if(CollectionUtil.isNotEmpty(budgetApplicationDTO.getCreateBudgetApplicationLists())){
            List<BudgetApplication> creates = BeanCopyUtils.convertListTo(budgetApplicationDTO.getCreateBudgetApplicationLists(),BudgetApplication::new);
            for(BudgetApplication application:creates){
                //TODO 6/12 审查  调整为新的编码生成方式，禁止循环生成 ，codeBo内部切换为新的调用方式 其他的调用方式都沿用新的BO,不要分散调用
                String code = codeBo.createCode(ClassNameConstant.BUDGET_APPLICATION, ClassNameConstant.NUMBER, false, null);
                application.setNumber(code);
            }
            this.saveBatch(creates);
        }
        if (CollectionUtil.isNotEmpty(budgetApplicationDTO.getUpdateBudgetApplicationLists())) {
            List<BudgetApplication> updates = BeanCopyUtils.convertListTo(budgetApplicationDTO.getUpdateBudgetApplicationLists(), BudgetApplication::new);
            this.updateBatchById(updates);
        }


        return true;
    }

    @Override
    public List<BudgetApplicationVO> getList(String formId) throws Exception {
        List<BudgetApplication>  budgetApplications = this.list(new LambdaQueryWrapperX<>(BudgetApplication.class).eq(BudgetApplication::getFormId,formId));
        List<BudgetApplicationVO> vos = BeanCopyUtils.convertListTo(budgetApplications, BudgetApplicationVO::new);
       if(CollUtil.isEmpty(vos)){
           return vos;
       }
        setEveryName(vos);
        return vos;
    }

    @Override
    public List<BudgetApplicationVO> getEstimateList(String projectId) throws Exception {
        ProjectApproval p = projectApprovalService.getOne(new LambdaQueryWrapperX<ProjectApproval>(ProjectApproval.class).eq(ProjectApproval::getProjectId,projectId));
        List<ProjectApprovalEstimateExpenseSubject> list = projectApprovalEstimateExpenseSubjectService.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateExpenseSubject.class)
                .eq(ProjectApprovalEstimateExpenseSubject::getProjectApprovalId, p.getId())
                .orderByDesc(ProjectApprovalEstimateExpenseSubject::getCreateTime));
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<BudgetApplicationVO> vos = new ArrayList<>();
        for(ProjectApprovalEstimateExpenseSubject projectApprovalEstimateExpenseSubject:list){
            BudgetApplicationVO budgetApplicationVO = new BudgetApplicationVO();
           // budgetApplicationVO.setCostCenterId();
            budgetApplicationVO.setIsEstimate("1");
            budgetApplicationVO.setExpenseSubjectNumber(projectApprovalEstimateExpenseSubject.getNumber());
            budgetApplicationVO.setExpenseSubjectName(projectApprovalEstimateExpenseSubject.getName());
            budgetApplicationVO.setBudgetMoney(projectApprovalEstimateExpenseSubject.getAmount());
            budgetApplicationVO.setName(projectApprovalEstimateExpenseSubject.getName()+"预算");
            vos.add(budgetApplicationVO);
        }
        setEveryName(vos);
        return vos;
    }


}
