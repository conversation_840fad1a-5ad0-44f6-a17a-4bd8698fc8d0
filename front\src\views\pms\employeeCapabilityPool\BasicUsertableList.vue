<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  smallSearchField: ['fullName'],
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 80,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '员工号',
      dataIndex: 'userCode',
      width: 92,
      customRender({ text, record }) {
        return h('div', {
          class: 'flex-te action-btn',
          onClick: () => navDetails(record?.id),
        }, text);
      },
    },
    {
      title: '姓名',
      width: 90,
      dataIndex: 'fullName',
    },
    {
      title: '性别',
      width: 60,
      dataIndex: 'sex',
    },
    {
      title: '人员性质',
      width: 90,
      dataIndex: 'personnelNature',
    },
    {
      title: '公司',
      width: 180,
      dataIndex: 'companyName',
    },
    {
      title: '部门',
      width: 140,
      dataIndex: 'deptName',
    },
    {
      title: '研究所',
      width: 115,
      dataIndex: 'instituteName',
    },
    {
      title: '民族',
      width: 70,
      dataIndex: 'nation',
    },
    {
      title: '身份证号',
      width: 178,
      dataIndex: 'idCard',
    },
    {
      title: '出生日期',
      width: 100,
      dataIndex: 'dateOfBirth',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '政治面貌',
      width: 90,
      dataIndex: 'politicalAffiliation',
    },
    {
      title: '籍贯',
      width: 90,
      dataIndex: 'homeTown',
    },
    {
      title: '出生地',
      width: 90,
      dataIndex: 'birthPlace',
    },
    {
      title: '职级',
      width: 70,
      dataIndex: 'jobLevel',
    },
    {
      title: '现任职务',
      width: 160,
      dataIndex: 'nowPosition',
    },
    {
      title: '职称',
      width: 110,
      dataIndex: 'jobTitle',
    },
    {
      title: '参加工作时间',
      width: 100,
      dataIndex: 'joinWorkTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '加入ZGH时间',
      width: 110,
      dataIndex: 'addZghTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '加入本单位时间',
      width: 130,
      dataIndex: 'addUnitTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/basic-user').fetch({
    ...params,
  }, 'page', 'POST'),
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_YGNLK_container_01_button_01', powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record?.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSEmployeeCapabilityPoolDetails',
    params: {
      id,
    },
  });
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSEmployeeCapabilityPool',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
