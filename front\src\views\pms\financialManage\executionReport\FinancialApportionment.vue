<script lang="ts" setup>
import {
  computed, ComputedRef, ref, Ref, unref,
} from 'vue';
import { Modal, DatePicker as ADatePicker } from 'ant-design-vue';
import {
  BasicButton, BasicImport, downloadByData, Layout, useModal, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { getFullAperture } from '../report';
import { openCostDrawer } from '../utils';
import { FinancialFilterConfig } from '../filterIndex';
import CostTable from '../components/CostTable.vue';

const tableRef: Ref = ref(null);
const loadStatus: Ref<boolean> = ref(false);
const dateValue: Ref<string> = ref(String(dayjs().year()));
const selectRowKeys = ref<string[]>([]);
const defaultExpandedRowKeys = ref<string[]>([]);
const [register, { openModal }] = useModal();
const powerData: Ref<any[]> = ref([]);
const data = ref([]);
const searchParams = ref();

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectRowKeys.value = _keys;
    },
  },
  expandIconColumnIndex: 2,
  showIndexColumn: false,
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  showSmallSearch: false,
  smallSearchField: [
    'projectName',
    'projectNumber',
    'base',
    'internalExternal',
  ],
  filterConfig: {
    fields: FinancialFilterConfig,
  },
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    searchParams.value = newParams;
    return new Api('/pms').fetch(newParams || [], 'projectFullSizeReport/getList', 'POST');
  },
  columns: getFullAperture(handlePopup),
};

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const conditions = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition));

    // 将 conditions 转换为 { field1: value1, field2: value2, ... } 的形式
    const query = conditions.reduce((acc, condition) => {
      if (condition.values && condition.values.length > 0) {
        acc[condition.field] = condition.values.join(','); // 假设 values 是一个数组，这里用逗号连接
      }
      return acc;
    }, {});

    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      ...query,
      year: dateValue.value,
      power: {
        pageCode: 'NBJYDZMXB_001',
      },
    };
  }
  return {
    ...params,
    year: dateValue.value,
    power: {
      pageCode: 'NBJYDZMXB_001',
    },
  };
}

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    powerCode: 'PMS_XMQKJMXBB_001_container_02_button_02',
  },
  {
    event: 'ImportIn',
    text: '导入',
    icon: 'sie-icon-daoru',
    powerCode: 'PMS_XMQKJMXBB_001_container_02_button_01',
  },
]);

// 弹框按钮
function handlePopup(record:any, costType:string) {
  const date = dateValue.value;
  openCostDrawer(CostTable, record, costType, date, powerData, updateTable);
}

// 导出
async function handleExport() {
  const obj = unref(searchParams);
  const params = {
    year: dateValue.value,
    base: obj?.base,
    internalExternal: obj?.internalExternal,
    projectName: obj?.projectName,
    projectNumber: obj?.projectNumber,
  };
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/projectFullSizeReport/export/excel', params, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 下载url
const downloadFileObj = {
  url: '/pms/costShare/download/excel/tpl',
  method: 'GET',
};

// 导入模版
function handleImport() {
  openModal(true, {});
}

// 导入报表
const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    new Api('/pms')
      .importFile(
        formData[0],
        '/api/pms/costShare/import/excel/check',
      )
      .then((res) => {
        const {
          code,
          message,
        } = res.data;
        if (code === 200) {
          // 转换oom   ---> message
          let newResultData = res.data.result;
          if (res.data.result.oom) {
            newResultData.message = res.data.result.oom;
          }
          resolve(newResultData);
        } else {
          const msg = message || '模版格式不对';
          resolve({
            code: 4000,
            msg,
          });
        }
      });
  });

const toolClick = (button: any) => {
  switch (button.event) {
    case 'export':
      handleExport();
      break;
    case 'ImportIn':
      handleImport();
      break;
  }
};

// 取消导入
function changeImportModalFlag({ succ, successImportFlag }) {
  if (!successImportFlag && succ) {
    return new Api(`/pms/costShare/import/excel/cancel/${succ}`).fetch('', '', 'POST');
  }
}

// 导入成功
const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/costShare/import/excel/${importId}`).fetch('', '', 'POST')
      .then(() => {
        updateTable();
        resolve({
          result: true,
        });
      });
  });

// 年份过滤
function changeDate(e) {
  dateValue.value = e;
  updateTable();
}

// 刷新列表
function updateTable() {
  tableRef.value?.reload();
}

// 获取权限
function getPowerDataHandle(power: any) {
  powerData.value = power;
}

function initData(data) {
  defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
}

function getDefaultExpandedRowKeys(data) {
  let rowKeys = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.children && item.children.length > 0) {
      rowKeys.push(item.id);
      let rowKeys1 = getDefaultExpandedRowKeys(item.children);
      if (rowKeys1) {
        rowKeys = rowKeys.concat(rowKeys1);
      }
    }
  }
  return rowKeys;
}

</script>
<template>
  <Layout
    v-get-power="{pageCode:'XMQKJMXBB_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :expandedRowKeys="defaultExpandedRowKeys"
      expand-row-by-click
      @initData="initData"
      xVirtual
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            type="primary"
            ghost
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #toolbarCenter>
        <div class="flex-row">
          <span class="flex-right">金额单位：万元</span>
          <ADatePicker
            v-model:value="dateValue"
            value-format="YYYY"
            picker="year"
            :allowClear="false"
            @change="changeDate"
          />
        </div>
      </template>
    </OrionTable>
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      :limitFileSize="50"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
  </Layout>
</template>

<style lang="less" scoped>
:deep(.action-num) {
  color: #0960bd;
  cursor: pointer;
}
.flex-right{
  margin-right: 20px;
}
.flex-row{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
