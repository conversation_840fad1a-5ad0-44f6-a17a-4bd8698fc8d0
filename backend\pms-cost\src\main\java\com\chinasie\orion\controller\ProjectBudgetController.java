package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectBudgetAnnualDTO;
import com.chinasie.orion.domain.dto.ProjectBudgetDTO;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.domain.vo.TotalCostVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectBudgetService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * ProjectBudget 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 14:48:31
 */
@RestController
@RequestMapping("/project-budget")
@Api(tags = "预算编制")
public class ProjectBudgetController {

    @Resource
    private ProjectBudgetService projectBudgetService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【预算编制】 【【{{#number}}-{{#name}}】详情", type = "ProjectBudget", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectBudgetVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectBudgetVO rsp = projectBudgetService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        LogRecordContext.putVariable("name", rsp.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectBudgetDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【预算编制】 【【{{#number}}-{{#name}}】数据", type = "ProjectBudget", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<ProjectBudgetVO> create(@Valid @RequestBody ProjectBudgetDTO projectBudgetDTO) throws Exception {
        ProjectBudgetVO rsp =  projectBudgetService.create(projectBudgetDTO);
        LogRecordContext.putVariable("number", rsp.getNumber());
        LogRecordContext.putVariable("name", rsp.getName());
        LogRecordContext.putVariable("id", rsp.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectBudgetDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【预算编制】 【【{{#number}}-{{#name}}】数据", type = "ProjectBudget", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> edit(@Valid @RequestBody  ProjectBudgetDTO projectBudgetDTO) throws Exception {
        Boolean rsp = projectBudgetService.edit(projectBudgetDTO);
        LogRecordContext.putVariable("number", projectBudgetDTO.getNumber());
        LogRecordContext.putVariable("name", projectBudgetDTO.getName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【预算编制】 【 【【{{#numbers}}-{{#names}}】数据", type = "ProjectBudget", subType = "删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectBudgetService.remove(ids);
        return new ResponseDTO(rsp);
    }


    @ApiOperation("预算编制分页（预算执行分页）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算编制】 预算执行分页", type = "ProjectBudget", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectBudgetVO>> getProjectBudgetVOPage(@RequestBody Page<ProjectBudgetDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectBudgetService.getProjectBudgetVOPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("成本创建时选择预算分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/page")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算编制】 预算分页", type = "ProjectBudget", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectBudgetVO>> getProjectBudgetVOPage1(@RequestBody Page<ProjectBudgetDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectBudgetService.getProjectBudgetVOPage1(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表查询")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算编制】 列表查询", type = "ProjectBudget", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseDTO<List<ProjectBudgetVO>> list() throws Exception {
       List<ProjectBudgetVO> rsp = projectBudgetService.listProjectBudgetVO();
        return new ResponseDTO<>(rsp);
    }

    /**
     * 总费用
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取总差异值")
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算编制】 总差异值", type = "ProjectBudget", subType = "查询", bizNo = "")
    @RequestMapping(value = "/total-cost/{projectId}", method = RequestMethod.GET)
    public ResponseDTO<TotalCostVO> getTotal(@PathVariable(value = "projectId") String projectId) throws Exception {
        TotalCostVO rsp = projectBudgetService.getTotal(projectId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取项目对应的预算信息-通过年度")
    @RequestMapping(value = "/budget/list/project/annual", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取【预算编制】 项目对应的预算信息-通过年度", type = "ProjectBudget", subType = "查询", bizNo = "")
    public ResponseDTO<List<ProjectBudgetVO>> getBudgetListByProjectIdListAndAnnual(@RequestBody ProjectBudgetAnnualDTO projectBudgetAnnualDTO) throws Exception {
        return new ResponseDTO(projectBudgetService.getBudgetListByProjectIdListAndAnnual(projectBudgetAnnualDTO));
    }

}