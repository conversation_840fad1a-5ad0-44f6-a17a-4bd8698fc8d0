package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "ProductEstimateMaterialVO对象", description = "产品物料")
@Data
public class ProjectBaseToProductVO extends ObjectVO{


    @ApiModelProperty(value = "商机产品编码")
    private String numberBOP;

    @ApiModelProperty(value = "PLM产品编码")
    private String numberPLM;

    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品型号")
    private String productModelNumber;

    @ApiModelProperty(value = "产品组")
    private String productGroup;

    @ApiModelProperty(value = "产品组名称")
    private String productGroupName;

    @ApiModelProperty(value = "物料类别")
    private String materialType;

    @ApiModelProperty(value = "物料类别名称")
    private String materialTypeName;

    @ApiModelProperty(value = "产品分类")
    private String productClassify;

    @ApiModelProperty(value = "产品分类名称")
    private String productClassifyName;

    @ApiModelProperty(value = "产品二级分类 ")
    private String productSecondClassify;

    @ApiModelProperty(value = "产品二级分类名称 ")
    private String productSecondClassifyName;

    @ApiModelProperty(value = "军/民品分类")
    private String MilitaryCivilian;

    @ApiModelProperty(value = "军/民品分类名称")
    private String MilitaryCivilianName;

}

