package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PersonTmpVO extends DataPermissionVO implements Serializable {

    private Date createTime;

    @ApiModelProperty(value = "大修组织id")
    private String repairOrgId;

    String personId;

    Integer status;

    String sex;

    @ApiModelProperty(value = "关系ID")
    private String  relationId;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "人员编号")
    String number;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门id")
    private String contactDept;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门编号")
    private String contactDeptCode;
    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门名称")
    private String contactDeptName;
    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室id")
    private String contactOffice;

    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室编号")
    private String contactOfficeCode;
    @ApiModelProperty(value = "接口科室名称")
    private String contactOfficeName;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    private String contactUser;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人编号")
    private String contactUserCode;
    @ApiModelProperty(value = "接口人名称")
    private String contactUserName;
    /**
     * 进入形式
     */
    @ApiModelProperty(value = "进入形式")
    private String enterMode;

    /**
     * 大修/日常
     */
    @ApiModelProperty(value = "大修/日常")
    private String workType;

    /**
     * 实际到厂时间
     */
//    @ApiModelProperty(value = "实际到厂时间")
//    @TableField(value = "actual_enter_date")
//    private Date actualEnterDate;
//
//    /**
//     * 实际离厂时间
//     */
//    @ApiModelProperty(value = "实际离厂时间")
//    @TableField(value = "actual_leave_date")
//    private Date actualLeaveDate;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    private String leaveReason;

    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    private String leaveRemark;

//    /**
//     * 计划到厂时间
//     */
//    @ApiModelProperty(value = "计划到厂时间")
//    @TableField(value = "plan_enter_date")
//    private Date planEnterDate;
//
//    /**
//     * 计划离厂时间
//     */
//    @ApiModelProperty(value = "计划离厂时间")
//    @TableField(value = "plan_leave_date")
//    private Date planLeaveDate;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    private String baseCode;



    @ApiModelProperty(value = "基地内承担的主要项目：项目ID")
    private String basePlaceProject;
    @ApiModelProperty(value = "基地内承担的主要项目：项目名称")
    private String basePlaceProjectName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "身高/米")
    private String heightStr;
    @ApiModelProperty(value = "体重")
    private String weightStr;
    @ApiModelProperty(value = "职业禁忌症：有/无")
    private String jobTaboos;

    @ApiModelProperty(value = "职业禁忌症名称")
    private String jobTaboosName;

    @ApiModelProperty(value = "涉及控制区作业: 是/否 true/false")
    private Boolean designCtrlZoneOp;


    @ApiModelProperty(value = "化学品/毒物使用或接触作业：是/否 true/false")
    private Boolean chemicalToxinUseJob;

    @ApiModelProperty(value = "工作负责人：是/否 true/false")
    private Boolean workResPerson;

    @ApiModelProperty(value = "准备工程师：是/否 true/false")
    private Boolean preparationEngineer;

    @ApiModelProperty(value = "QC：是/否 true/false ")
    private Boolean qcStr;

    @ApiModelProperty(value = "QC工作年限")
    private String qcWorkYear;

    @ApiModelProperty(value = "专职安全员：是/否 true/false ")
    private Boolean fuTiSafOff;

    @ApiModelProperty(value = "兼职安全员：是/否 true/false ")
    private Boolean paTiSafOff;

    @ApiModelProperty(value = "特种作业持证情况(含无损检测资质")
    private String speTaskCertSit;

    @ApiModelProperty(value = "一年内参与过集团内大修、高剂量人员(年个人剂量>8mSv为高剂量人员)")
    private String participateONot;


    @ApiModelProperty(value = "新人：是/否 true/false")
    private Boolean newcomer;

    @ApiModelProperty(value = "新人类型")
    private String newcomerType;


    @ApiModelProperty(value = "新人对口人")
    private String newcomerMatchPerson;
    @ApiModelProperty(value = "新人对口人编号")
    private String newcomerMatchPersonCode;

    @ApiModelProperty(value = "授权状态")
    private String authorizationStatus;

    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "新人对口人名称")
    private String newcomerMatchPersonName;



    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    private Boolean isJoinYearMajorRepair;

    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    private Boolean isHeightMeasurePerson;

    @ApiModelProperty(value = "主工作中心")
    private String mainWorkCenter;

    @ApiModelProperty(value = "是否作业")
    private Boolean isJob;


    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date planInDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date planOutDate;

  //   pmro.begin_time as 'majorOrgBeginTime',pmro.end_time as 'majorOrgEndTime',mrp.begin_time as 'majorBeginTime',mrp.end_time as 'majorEndTime'
    @ApiModelProperty(value = "所属组织的计划开始时间")
    private Date majorOrgBeginTime;
    @ApiModelProperty(value = "所属组织的计划结束时间")
    private Date majorOrgEndTime;
    @ApiModelProperty(value = "所属大修的计划开始时间")
    private Date majorBeginTime;
    @ApiModelProperty(value = "所属大修的计划结束时间")
    private Date majorEndTime;


    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    private Boolean isFinishOutHandover;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "基础用户id")
    private String basicUserId;

    @ApiModelProperty(value = "授权岗位信息")
    private String jobPostName;

    @ApiModelProperty(value = "人员名称")
    String userName;

}
