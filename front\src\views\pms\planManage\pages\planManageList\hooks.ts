import { ReturnDrawerMethods } from 'lyra-component-vue3';
import { reactive, unref } from 'vue';
import { useRouter } from 'vue-router';
import { getTableOptions as getTableOptionsFn } from './tableOptions';
import { ToolbarButtonEnums } from './types';
import { message } from 'ant-design-vue';
import { deletePlan } from '/@/views/pms/api';

interface IUseProjectPlan {
  createPlanDrawerMethods: ReturnDrawerMethods,
  tableRef: any
}

export function useProjectPlan(props: IUseProjectPlan) {
  const { createPlanDrawerMethods } = props;
  const router = useRouter();

  function toolbarClick(type) {
    switch (type) {
      // 创建计划
      case ToolbarButtonEnums.CREATE:
        createPlanDrawerMethods.openDrawer(true, {
          type: 'add',
          successChange() {
            message.success('操作成功');
            updateTableData();
          },
        });
        break;
    }
  }

  function nameClick(record) {
    router.push({
      name: 'ComprehensivePlanManageDetail',
      query: {
        id: record?.id,
      },
    });
  }

  function updateTableData() {
    unref(props.tableRef)?.reload();
  }

  // 点击操作栏
  function actionClick(key: string, record: any) {
    switch (key) {
      case 'delete':
        deletePlan([record.id]).then(() => {
          message.success('操作成功');
          updateTableData();
        });
        break;
      case 'edit':
        createPlanDrawerMethods.openDrawer(
          true,
          {
            type: 'edit',
            parentId: record?.id,
            record,
            successChange() {
              message.success('操作成功');
              updateTableData();
            },
          },
        );
        break;
    }
  }

  function getTableOptions() {
    return getTableOptionsFn({
      toolbarClick,
      nameClick,
      actionClick,
    });
  }

  return {
    getTableOptions,
  };
}
