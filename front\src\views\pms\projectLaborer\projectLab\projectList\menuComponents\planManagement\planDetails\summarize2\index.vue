<template>
  <Layout
    v-loading="loading"
    class="ui-2-0"
  >
    <View
      v-if="showDetails"
      :data="view"
    />
    <Edit
      v-else
      ref="editRef"
      :data="edit"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
</template>

<script>
import { message } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed,
} from 'vue';
import Api from '/@/api';
import {
  isPower,
  Layout,
} from 'lyra-component-vue3';
import Edit from './Edit.vue';
import View from './View.vue';

export default {
  name: 'Summarize',
  components: {
    Layout,
    NewButtonModal,
    Edit,
    View,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      editRef: ref(),
      view: { form: {} },
      edit: { form: {} },
      powerData: [],
      showDetails: true,
      btnConfig: {
        edit: { show: computed(() => isPower('LCB_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      },
      // btnConfig: {
      //   edit: { show: true },
      //   determine: { show: false },
      //   cancel: { show: false },
      // },
    });
    state.powerData = inject('powerData');
    function handleEdit() {
      state.edit = JSON.parse(JSON.stringify(state.view));
      state.showDetails = false;
      state.btnConfig = {
        edit: { show: false },
        determine: { show: true },
        cancel: { show: true },
      };
    }
    function submit(val) {
      const love = {
        id: val?.id,
        name: val?.name,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-概述', // 模块名称
        type: 'UPDATE', // 操作类型
        remark: `编辑了【${val?.name}】`,
      };
      state.loading = true;
      new Api('/pms', love)
        .fetch(val, 'milestone', 'PUT')
        .then(() => {
          message.success('操作成功');
          state.loading = false;
          state.showDetails = true;
          state.btnConfig = {
            edit: { show: computed(() => isPower('LCB_container_button_02', state.powerData)) },
            determine: { show: false },
            cancel: { show: false },
          };
          getForm();
        })
        .catch(() => {
          state.loading = false;
        });
    }
    function clickType(type) {
      type === 'edit' && handleEdit();
      type === 'determine' && state.editRef.submit(submit);
      if (type === 'cancel') {
        state.showDetails = true;
        state.btnConfig = {
          edit: { show: computed(() => isPower('LCB_container_button_02', state.powerData)) },
          determine: { show: false },
          cancel: { show: false },
        };
      }
    }

    function getForm() {
      state.loading = true;
      new Api('/pms')
        .fetch('', `plan/${props.formId}`, 'GET')
        .then((res) => {
          state.loading = false;
          state.view.form = res;
        })
        .catch((_) => {
          state.loading = false;
        });
    }

    onMounted(() => {
      getForm();
    });
    return {
      ...toRefs(state),
      clickType,
    };
  },
};
</script>

<style scoped lang="less">
.ui-2-0 {
  width: calc(100% - 60px);
  flex: 1;
}
</style>
