import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import { priorityLevelApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import {
  editQuestionApi,
  addquestionApi,
  questionTypeApi,
  questionLevelApi,
  questionSourceApi,
} from '/@/views/pms/projectLaborer/api/questionManage';

export const otherApi = {
  zkEdit: editQuestionApi,
  zkAdd: addquestionApi,
  zkPeopel: roleListApi,
  //   zkItemDetails: itemDetailsApi
  //   zkForType: demandSimplePageApi
};
export const formItemArr = [
  {
    type: 'input',
    label: '名称',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入名称',
    },
  },
  {
    type: 'textarea',
    label: '问题内容',
    field: 'content',
    apiConfig: {
      placeholder: '请输入问题内容',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '负责人',
    field: 'principalId',
    radioOptions: [],
    rules: [
      {
        required: true,
        message: '请选择负责人',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      placeholder: '请选择负责人',

      size: 'large',
    },
  },
  {
    type: 'select',
    label: '问题类型',
    field: 'questionType',
    options: [],
    getOptionFn: questionTypeApi,
    apiConfig: {
      placeholder: '请选择问题类型',
      size: 'large',
    },
  },
  {
    type: 'select',
    label: '问题来源',
    field: 'questionSource',

    getOptionFn: questionSourceApi,
    options: [],
    apiConfig: {
      size: 'large',
      placeholder: '请选择问题来源',
    },
  },
  {
    type: 'select',
    label: '严重程度',
    field: 'seriousLevel',

    getOptionFn: questionLevelApi,
    options: [],
    apiConfig: {
      size: 'large',
      placeholder: '请选择严重程度',
    },
  },
  {
    type: 'input',
    label: '提出人',
    field: 'exhibitor',
    apiConfig: {
      size: 'large',
      placeholder: '请输入提出人',
    },
  },
  {
    type: 'dataPicker',
    label: '提出时间',
    field: 'proposedTime',
    format: true,
    apiConfig: {
      placeholder: '请选择提出时间',
      size: 'large',
    },
  },
  {
    type: 'dataPicker',
    label: '期望完成时间',
    field: 'predictEndTime',
    format: true,
    apiConfig: {
      placeholder: '请选择期望完成时间',
      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '接收人',
    field: 'recipient',

    radioOptions: [],
    apiConfig: {
      placeholder: '请选择接收人',

      size: 'large',
    },
  },

  {
    type: 'select',
    label: '优先级',
    field: 'priorityLevel',
    getOptionFn: priorityLevelApi,
    options: [],

    apiConfig: {
      placeholder: '请选择优先级',

      size: 'large',
    },
  },
  {
    type: 'inputIcon',
    label: '进度',
    field: 'schedule',

    apiConfig: {
      placeholder: '请选择进度',
      size: 'large',
      suffix: '%',
    },
  },
];
