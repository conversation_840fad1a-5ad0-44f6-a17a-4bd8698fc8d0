package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractCenterPlan VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@ApiModel(value = "ContractCenterPlanVO对象", description = "中心用人计划")
@Data
public class ContractCenterPlanVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        @ApiModelProperty(value = "成本类型id")
        private String costTypeId;

        /**
         * 合同名称
         */
        @ApiModelProperty(value = "合同名称")
        private String contractName;


        /**
         * 用人单位代号
         */
        @ApiModelProperty(value = "用人单位代号")
        private String centerCode;


        /**
         * 用人单位名称
         */
        @ApiModelProperty(value = "用人单位名称")
        private String centerName;


        /**
         * 成本类型编号
         */
        @ApiModelProperty(value = "成本类型编号")
        private String costTypeNumber;

        /**
         * 成本类型名称
         */
        @ApiModelProperty(value = "成本类型名称")
        private String costTypeName;
        
        /**
         * 成本名称
         */
        @ApiModelProperty(value = "成本名称")
        private String costName;

        /**
         * 数量
         */
        @ApiModelProperty(value = "数量")
        private Integer num;

        @ApiModelProperty(value = "调整前数量")
        private Integer beforeNum;

        /**
         * 单位
         */
        @ApiModelProperty(value = "单位")
        private String unit;

        /**
         * 单价
         */
        @ApiModelProperty(value = "单价")
        private BigDecimal unitPrice;

        /**
         * 总计（金额）
         */
        @ApiModelProperty(value = "总计（金额）")
        private BigDecimal totalAmount;

        /**
         * 年份
         */
        @ApiModelProperty(value = "年份")
        private Date year;

        @ApiModelProperty(value = "调整前总计（金额）")
        private BigDecimal originalTotalAmount;
    

}
