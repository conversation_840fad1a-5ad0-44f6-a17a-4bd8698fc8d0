package com.chinasie.orion.domain.entity.humanResource;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectHumanResource Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@TableName(value = "pms_project_human_resource_setting")
@ApiModel(value = "ProjectHumanResourceEntity对象", description = "人力资源权限设置")
@Data
public class ProjectHumanResourceSetting extends ObjectEntity implements Serializable{

/**
 * 数据ID
 */
@ApiModelProperty(value = "数据ID")
@TableField(value = "data_id")
private String dataId;

/**
 * 数据类型
 */
@ApiModelProperty(value = "数据类型")
@TableField(value = "data_type")
private String dataType;

}
