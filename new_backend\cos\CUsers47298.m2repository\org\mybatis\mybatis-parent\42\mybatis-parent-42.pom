<?xml version="1.0" encoding="UTF-8"?>
<!--

       Copyright 2010-2024 the original author or authors.

       Licensed under the Apache License, Version 2.0 (the "License");
       you may not use this file except in compliance with the License.
       You may obtain a copy of the License at

          https://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing, software
       distributed under the License is distributed on an "AS IS" BASIS,
       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
       See the License for the specific language governing permissions and
       limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>org.mybatis</groupId>
  <artifactId>mybatis-parent</artifactId>
  <version>42</version>
  <packaging>pom</packaging>

  <name>mybatis-parent</name>
  <description>The MyBatis parent POM.</description>
  <url>https://www.mybatis.org/parent/</url>
  <inceptionYear>2010</inceptionYear>
  <organization>
    <name>MyBatis.org</name>
    <url>https://www.mybatis.org/</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <!-- committers are enlisted by name ASC order, except Clinton who is the founder -->
    <developer>
      <id>cbegin</id>
      <name>Clinton Begin</name>
      <email><EMAIL></email>
      <roles>
        <role>Owner</role>
        <role>Founder</role>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>agustafson</id>
      <name>Andrew Gustafson</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>brandon.goodin</id>
      <name>Brandon Goodin</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>christianpoitras</id>
      <name>Christian Poitras</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>

    <developer>
      <id>emacarron</id>
      <name>Eduardo Macarron</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>mnesarco</id>
      <name>Frank Martinez</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>hpresnall</id>
      <name>Hunter Presnall</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>harawata</id>
      <name>Iwao Ave</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>jeffgbutler</id>
      <name>Jeff Butler</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>hazendaz</id>
      <name>Jeremy Landis</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>

    <developer>
      <id><EMAIL></id>
      <name>Kai Grabfelder</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>lmeadors</id>
      <name>Larry Meadors</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>marcosperanza</id>
      <name>Marco Speranza</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>

    <developer>
      <id>nmaves</id>
      <name>Nathan Maves</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>pboonphong</id>
      <name>Putthiphong Boonphong</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>

    <developer>
      <id>simonetripodi</id>
      <name>Simone Tripodi</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>

    <developer>
      <id>h3adache</id>
      <name>Tim Chen</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>mybatis-dev</name>
      <subscribe>https://groups.google.com/forum/#!forum/mybatis-dev/join</subscribe>
      <unsubscribe>https://groups.google.com/forum/#!forum/mybatis-dev/join</unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mybatis-dev</archive>
    </mailingList>

    <mailingList>
      <name>mybatis-user</name>
      <subscribe>https://groups.google.com/forum/#!forum/mybatis-user/join</subscribe>
      <unsubscribe>https://groups.google.com/forum/#!forum/mybatis-user/join</unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mybatis-user</archive>
    </mailingList>

    <mailingList>
      <name>mybatis-commits</name>
      <subscribe>https://groups.google.com/forum/#!forum/mybatis-commits/join</subscribe>
      <unsubscribe>https://groups.google.com/forum/#!forum/mybatis-commits/join</unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mybatis-commits</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:ssh://**************/mybatis/parent.git</connection>
    <developerConnection>scm:git:ssh://**************/mybatis/parent.git</developerConnection>
    <tag>mybatis-parent-42</tag>
    <url>https://github.com/mybatis/parent/</url>
  </scm>
  <issueManagement>
    <system>GitHub Issue Management</system>
    <url>https://github.com/mybatis/parent/issues</url>
  </issueManagement>
  <ciManagement>
    <system>Github Actions</system>
    <url>https://github.com/mybatis/parent/actions</url>
  </ciManagement>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <site>
      <id>gh-pages-scm</id>
      <name>Mybatis GitHub Pages</name>
      <url>scm:git:ssh://**************/mybatis/parent.git</url>
    </site>
  </distributionManagement>

  <properties>
    <!-- Copyright -->
    <copyright>2024</copyright>

    <!-- General configuration -->
    <allowed.build.jdks>[11,12),[17,18),[21,22),[22,23)</allowed.build.jdks>
    <checkstyle.config>checkstyle.xml</checkstyle.config>
    <clirr.comparisonVersion>39</clirr.comparisonVersion>
    <formatter.config>eclipse-formatter-config-2space.xml</formatter.config>
    <gcu.product>${project.name}</gcu.product>
    <html.javadocType>-html5</html.javadocType>
    <impsortGroups>au,ch,com,config,de,examples,io,jakarta,java,javassist,javax,lombok,mockit,net,nl,ognl,org</impsortGroups>
    <module.name>org.mybatis.parent</module.name>
    <spotbugs.onlyAnalyze />

    <!-- Maven date configuration -->
    <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>

    <!-- Maven compiler options -->
    <java.version>8</java.version>
    <java.release.version>8</java.release.version>
    <java.test.version>11</java.test.version>
    <java.test.release.version>11</java.test.release.version>

    <!-- source/target are deprecated, remove in future release -->
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven.compiler.testSource>${java.test.version}</maven.compiler.testSource>
    <maven.compiler.testTarget>${java.test.version}</maven.compiler.testTarget>

    <maven.compiler.release>${java.release.version}</maven.compiler.release>
    <maven.compiler.testRelease>${java.test.release.version}</maven.compiler.testRelease>

    <!-- Maven minimum version -->
    <maven.min-version>3.9.6</maven.min-version>

    <!-- Project Encoding -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!-- Reproducible Builds -->
    <project.build.outputTimestamp>1706468785</project.build.outputTimestamp>

    <!-- Due to maven bug, fix timestamp -->
    <timestamp>${maven.build.timestamp}</timestamp>

    <!-- Dependency versions -->
    <asm.version>9.6</asm.version>
    <base-bundle.version>11</base-bundle.version>
    <bnd.version>7.0.0</bnd.version>
    <build-tools.version>1.3.1</build-tools.version>
    <checkstyle.version>10.13.0</checkstyle.version>
    <extra-enforcer-rules.version>1.7.0</extra-enforcer-rules.version>
    <fluido.version>2.0.0-M8</fluido.version>
    <license.version>4.3</license.version>

    <!-- Plugins versions -->
    <antrun.plugin>3.1.0</antrun.plugin>
    <assembly.plugin>3.6.0</assembly.plugin>
    <bnd.plugin>7.0.0</bnd.plugin>
    <checkstyle.plugin>3.3.1</checkstyle.plugin>
    <clean.plugin>3.3.2</clean.plugin>
    <clirr.plugin>2.8</clirr.plugin>
    <compiler.plugin>3.12.1</compiler.plugin>
    <coveralls.plugin>4.5.0-M3</coveralls.plugin>
    <dependency.plugin>3.6.1</dependency.plugin>
    <deploy.plugin>3.1.1</deploy.plugin>
    <enforcer.plugin>3.4.1</enforcer.plugin>
    <formatter.plugin>2.23.0</formatter.plugin>
    <git-commit.plugin>7.0.0</git-commit.plugin>
    <gpg.plugin>3.1.0</gpg.plugin>
    <impsort.plugin>1.9.0</impsort.plugin>
    <install.plugin>3.1.1</install.plugin>
    <jacoco.plugin>0.8.11</jacoco.plugin>
    <jar.plugin>3.3.0</jar.plugin>
    <javadoc.plugin>3.6.3</javadoc.plugin>
    <jxr.plugin>3.3.2</jxr.plugin>
    <license.plugin>4.3</license.plugin>
    <lifecycle.plugin>1.0.0</lifecycle.plugin>
    <modernizer.plugin>2.7.0</modernizer.plugin>
    <pdf.plugin>1.6.1</pdf.plugin>
    <pmd.plugin>3.21.2</pmd.plugin>
    <project-info.plugin>3.5.0</project-info.plugin>
    <release.plugin>3.0.1</release.plugin>
    <resources.plugin>3.3.1</resources.plugin>
    <rewrite.plugin>5.21.0</rewrite.plugin>
    <scm-publish.plugin>3.2.1</scm-publish.plugin>
    <shade.plugin>3.5.1</shade.plugin>
    <site.plugin>4.0.0-M13</site.plugin>
    <sortpom.plugin>3.3.0</sortpom.plugin>
    <source.plugin>3.3.0</source.plugin>
    <spotbugs.plugin>4.8.3.0</spotbugs.plugin>
    <surefire.plugin>3.2.5</surefire.plugin>
    <taglist.plugin>3.0.0</taglist.plugin>
    <versions.plugin>2.16.2</versions.plugin>
    <whitespace.plugin>1.3.1</whitespace.plugin>

    <!-- OSGi configuration properties -->
    <osgi.symbolicName>${project.groupId}.${project.artifactId}</osgi.symbolicName>
    <osgi.export>${project.groupId}.*;version=${project.version};-noimport:=true</osgi.export>
    <osgi.import>*</osgi.import>
    <osgi.dynamicImport />
    <osgi.private />

    <!-- Add slow test groups here and annotate classes similar to @Tag('groupName'). -->
    <!-- Excluded groups are ran on github ci, to force here, pass -D"excludedGroups=" -->
    <excludedGroups />

    <!-- Hack for single site builds so site:staging works as expected. Do not use with multi module!
         Instead adjust for fact that maven bug causes staging to be up one directory with 'reponame.git' -->
    <topSiteURL>${project.distributionManagement.site.url}</topSiteURL>
  </properties>

  <build>

    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>${project.basedir}/src/main/resources</directory>
      </resource>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>${project.basedir}</directory>
        <includes>
          <include>LICENSE</include>
          <include>NOTICE</include>
        </includes>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>

        <plugin>
          <groupId>com.github.hazendaz.maven</groupId>
          <artifactId>whitespace-maven-plugin</artifactId>
          <version>${whitespace.plugin}</version>
        </plugin>

        <plugin>
          <groupId>com.github.ekryd.sortpom</groupId>
          <artifactId>sortpom-maven-plugin</artifactId>
          <version>${sortpom.plugin}</version>
          <configuration>
            <predefinedSortOrder>recommended_2008_06</predefinedSortOrder>
            <createBackupFile>false</createBackupFile>
            <expandEmptyElements>false</expandEmptyElements>
            <keepBlankLines>true</keepBlankLines>
            <nrOfIndentSpace>2</nrOfIndentSpace>
            <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
          </configuration>
        </plugin>

        <plugin>
          <groupId>io.github.git-commit-id</groupId>
          <artifactId>git-commit-id-maven-plugin</artifactId>
          <version>${git-commit.plugin}</version>
          <configuration>
            <failOnNoGitDirectory>false</failOnNoGitDirectory>
            <!-- Fixes occassional issue in commit id plugin - see https://github.com/ktoso/maven-git-commit-id-plugin/issues/61 -->
            <gitDescribe>
              <always>true</always>
            </gitDescribe>
          </configuration>
        </plugin>

        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>${spotbugs.plugin}</version>
          <configuration>
            <xmlOutput>true</xmlOutput>
            <xmlOutputDirectory>${project.build.directory}/spotbugs-reports</xmlOutputDirectory>
            <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs-reports</spotbugsXmlOutputDirectory>
            <threshold>High</threshold>
            <effort>Max</effort>
            <visitors>FindDeadLocalStores,UnreadFields</visitors>
            <onlyAnalyze>${spotbugs.onlyAnalyze}</onlyAnalyze>
            <relaxed>true</relaxed>
          </configuration>
        </plugin>

        <plugin>
          <groupId>com.mycila</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${license.plugin}</version>
          <configuration>
            <licenseSets>
              <licenseSet>
                <header>${project.basedir}/LICENSE_HEADER</header>
                <excludes>
                  <!-- Default excludes located at https://github.com/mathieucarbou/license-maven-plugin/blob/master/license-maven-plugin/src/main/java/com/mycila/maven/plugin/license/Default.java -->

                  <!-- Exclude extra items used throughout mybatis -->
                  <exclude>**/*.ctrl</exclude>
                  <exclude>**/*.dat</exclude>
                  <exclude>ICLA</exclude>
                  <exclude>KEYS</exclude>
                  <exclude>NOTICE</exclude>

                  <!-- Exclude special folder to ignore license -->
                  <exclude>**/unlicensed/**</exclude>
                  <exclude>**/ibderby/**</exclude>
                </excludes>
              </licenseSet>
            </licenseSets>
            <mapping>
              <!-- Default mapping located at https://github.com/mathieucarbou/license-maven-plugin/blob/master/license-maven-plugin/src/main/java/com/mycila/maven/plugin/license/document/DocumentType.java -->
              <xml.vm>XML_STYLE</xml.vm>
            </mapping>
            <prohibitLegacyUse>true</prohibitLegacyUse>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>com.mycila</groupId>
              <artifactId>license-maven-plugin-git</artifactId>
              <version>${license.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>com.github.hazendaz.maven</groupId>
          <artifactId>coveralls-maven-plugin</artifactId>
          <version>${coveralls.plugin}</version>
        </plugin>

        <plugin>
          <groupId>net.revelc.code</groupId>
          <artifactId>impsort-maven-plugin</artifactId>
          <version>${impsort.plugin}</version>
          <configuration>
            <groups>${impsortGroups}</groups>
            <staticGroups>java,*</staticGroups>
            <removeUnused>true</removeUnused>
          </configuration>
        </plugin>

        <plugin>
          <groupId>net.revelc.code.formatter</groupId>
          <artifactId>formatter-maven-plugin</artifactId>
          <version>${formatter.plugin}</version>
          <configuration>
            <configFile>${formatter.config}</configFile>
            <skipXmlFormatting>true</skipXmlFormatting>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>com.github.hazendaz</groupId>
              <artifactId>build-tools</artifactId>
              <version>${build-tools.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>biz.aQute.bnd</groupId>
          <artifactId>bnd-maven-plugin</artifactId>
          <version>${bnd.plugin}</version>
          <configuration>
            <manifestPath>${project.build.directory}/osgi/MANIFEST.MF</manifestPath>
            <packagingTypes>jar,bundle,maven-plugin,war</packagingTypes>
            <bnd><![CDATA[
              // 'nouses' stops the "uses" clauses being added to "Export-Package" manifest entry
              -nouses: true
              // 'removeheaders' Stop the JAVA_1_n_HOME / JAVA_n_HOME variables from being treated as headers by Bnd if found in settings.xml (obsolete versions removed, if found, cleanup settings.xml)
              -removeheaders: JAVA_1_8_HOME,JAVA_8_HOME,JAVA_11_HOME,JAVA_17_HOME,JAVA_21_HOME,JAVA_22_HOME
              Bundle-Developers:
              Bundle-DocURL: ${project.url}
              Bundle-SymbolicName: ${osgi.symbolicName}
              DynamicImport-Package: ${osgi.dynamicImport}
              Export-Package: ${osgi.export}
              Private-Package: ${osgi.private}
              Import-Package: ${osgi.import}
            ]]></bnd>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>biz.aQute.bnd</groupId>
              <artifactId>biz.aQute.bndlib</artifactId>
              <version>${bnd.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <!-- Antrun here only to override eclipse settings -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${antrun.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${assembly.plugin}</version>
          <dependencies>
            <dependency>
              <groupId>org.mybatis</groupId>
              <artifactId>base-bundle-descriptor</artifactId>
              <version>${base-bundle.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${checkstyle.plugin}</version>
          <configuration>
            <failOnViolation>false</failOnViolation>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${clean.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${compiler.plugin}</version>
          <configuration>
            <parameters>true</parameters>
            <!-- Slightly faster builds, see https://issues.apache.org/jira/browse/MCOMPILER-209 -->
            <useIncrementalCompilation>false</useIncrementalCompilation>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${dependency.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${deploy.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${enforcer.plugin}</version>
          <configuration>
            <rules>
              <enforceBytecodeVersion>
                <maxJdkVersion>${java.version}</maxJdkVersion>
                <ignoredScopes>test</ignoredScopes>
              </enforceBytecodeVersion>
              <requireJavaVersion>
                <version>${allowed.build.jdks}</version>
              </requireJavaVersion>
              <requireMavenVersion>
                <version>[${maven.min-version},)</version>
              </requireMavenVersion>
              <requirePluginVersions>
                <message>[ERROR] Best Practice is to always define plugin versions!</message>
                <banLatest>true</banLatest>
                <banRelease>true</banRelease>
                <banSnapshots>true</banSnapshots>
                <phases>clean,deploy,site</phases>
              </requirePluginVersions>
            </rules>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>extra-enforcer-rules</artifactId>
              <version>${extra-enforcer-rules.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${gpg.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${install.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${jar.plugin}</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.directory}/osgi/MANIFEST.MF</manifestFile>
              <manifest>
                <addBuildEnvironmentEntries>false</addBuildEnvironmentEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
              <manifestEntries>
                <Automatic-Module-Name>${module.name}</Automatic-Module-Name>
                <Copyright>${copyright}</Copyright>
                <Git-Revision>${git.commit.id}</Git-Revision>
                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                <X-Compile-Release-JDK>${maven.compiler.release}</X-Compile-Release-JDK>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${javadoc.plugin}</version>
          <configuration>
            <additionalOptions>
              <additionalOption>${html.javadocType}</additionalOption>
            </additionalOptions>
            <archive>
              <manifest>
                <addBuildEnvironmentEntries>false</addBuildEnvironmentEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
              <manifestEntries>
                <Automatic-Module-Name>${module.name}</Automatic-Module-Name>
                <Copyright>${copyright}</Copyright>
                <Git-Revision>${git.commit.id}</Git-Revision>
                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                <X-Compile-Release-JDK>${maven.compiler.release}</X-Compile-Release-JDK>
              </manifestEntries>
            </archive>
            <legacyMode>true</legacyMode>
            <notimestamp>true</notimestamp>
            <quiet>true</quiet>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${jxr.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pdf-plugin</artifactId>
          <version>${pdf.plugin}</version>
          <!-- Execution such as this in plugin management requires plugin definition in build to work -->
          <executions>
            <execution>
              <id>pdf</id>
              <goals>
                <goal>pdf</goal>
              </goals>
              <phase>prepare-package</phase>
              <configuration>
                <includeReports>false</includeReports>
              </configuration>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>${pmd.plugin}</version>
          <configuration>
            <analysisCache>true</analysisCache>
            <linkXRef>true</linkXRef>
            <minimumTokens>100</minimumTokens>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${project-info.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${release.plugin}</version>
          <configuration>
            <arguments>-Daether.checksums.algorithms=SHA-512,SHA-256,SHA-1,MD5</arguments>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <mavenExecutorId>forked-path</mavenExecutorId>
            <releaseProfiles>release</releaseProfiles>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${resources.plugin}</version>
          <configuration>
            <!-- Defaults to source, use resources as this is a resource -->
            <propertiesEncoding>${project.build.resourceEncoding}</propertiesEncoding>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${scm-publish.plugin}</version>
          <configuration>
            <checkoutDirectory>${user.home}/maven-sites/${project.name}</checkoutDirectory>
            <!-- Single module project has no need for site:stage, use target/site or target/checkout/target/site on release. -->
            <content>${project.reporting.outputDirectory}</content>
            <scmBranch>gh-pages</scmBranch>
            <tryUpdate>true</tryUpdate>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${shade.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${site.plugin}</version>
          <configuration>
            <!-- don't deploy site with maven-site-plugin (instead use scm publish during release) -->
            <skipDeploy>true</skipDeploy>
          </configuration>
          <dependencies>
            <!-- Additional entries for version site tracking only -->
            <dependency>
              <groupId>org.apache.maven.skins</groupId>
              <artifactId>maven-fluido-skin</artifactId>
              <version>${fluido.version}</version>
            </dependency>
          </dependencies>
          <!-- Execution like this will pick up in the normal site cycle -->
          <executions>
            <execution>
              <id>attach-descriptor</id>
              <goals>
                <goal>attach-descriptor</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${source.plugin}</version>
          <configuration>
            <archive>
              <manifest>
                <addBuildEnvironmentEntries>false</addBuildEnvironmentEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
              <manifestEntries>
                <Automatic-Module-Name>${module.name}</Automatic-Module-Name>
                <Copyright>${copyright}</Copyright>
                <Git-Revision>${git.commit.id}</Git-Revision>
                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                <X-Compile-Release-JDK>${maven.compiler.release}</X-Compile-Release-JDK>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${surefire.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${surefire.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${jacoco.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${clirr.plugin}</version>
          <configuration>
            <comparisonVersion>${clirr.comparisonVersion}</comparisonVersion>
            <failOnError>false</failOnError>
            <failOnWarning>false</failOnWarning>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.bcel</groupId>
              <artifactId>bcel</artifactId>
              <version>6.8.1</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>${taglist.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions.plugin}</version>
        </plugin>

        <plugin>
          <groupId>org.gaul</groupId>
          <artifactId>modernizer-maven-plugin</artifactId>
          <version>${modernizer.plugin}</version>
          <configuration>
            <failOnViolations>false</failOnViolations>
            <javaVersion>${maven.compiler.target}</javaVersion>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.ow2.asm</groupId>
              <artifactId>asm</artifactId>
              <version>${asm.version}</version>
            </dependency>
          </dependencies>
        </plugin>

        <plugin>
          <groupId>org.openrewrite.maven</groupId>
          <artifactId>rewrite-maven-plugin</artifactId>
          <version>${rewrite.plugin}</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <!-- Checkstyle dependencies required here or they won't be used (per maven) -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>${checkstyle.version}</version>
          </dependency>
          <dependency>
            <groupId>com.github.hazendaz</groupId>
            <artifactId>build-tools</artifactId>
            <version>${build-tools.version}</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>bnd-process</id>
            <goals>
              <goal>bnd-process</goal>
            </goals>
            <phase>process-classes</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>prepare-agent</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>com.github.hazendaz.maven</groupId>
        <artifactId>whitespace-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>trim</goal>
            </goals>
            <phase>process-sources</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>io.github.git-commit-id</groupId>
        <artifactId>git-commit-id-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>git-commit-id</id>
            <goals>
              <goal>revision</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.gaul</groupId>
        <artifactId>modernizer-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>modernizer</id>
            <goals>
              <goal>modernizer</goal>
            </goals>
            <phase>verify</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <reportSets>
          <reportSet>
            <id>default</id>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <configLocation>${checkstyle.config}</configLocation>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <configuration>
          <tagListOptions>
            <tagClasses>
              <tagClass>
                <displayName>FIXME Work</displayName>
                <tags>
                  <tag>
                    <matchString>fixme</matchString>
                    <matchType>ignoreCase</matchType>
                  </tag>
                  <tag>
                    <matchString>@fixme</matchString>
                    <matchType>ignoreCase</matchType>
                  </tag>
                </tags>
              </tagClass>
              <tagClass>
                <displayName>Todo Work</displayName>
                <tags>
                  <tag>
                    <matchString>todo</matchString>
                    <matchType>ignoreCase</matchType>
                  </tag>
                  <tag>
                    <matchString>@todo</matchString>
                    <matchType>ignoreCase</matchType>
                  </tag>
                </tags>
              </tagClass>
              <tagClass>
                <displayName>Deprecated Work</displayName>
                <tags>
                  <tag>
                    <matchString>@deprecated</matchString>
                    <matchType>ignoreCase</matchType>
                  </tag>
                </tags>
              </tagClass>
            </tagClasses>
          </tagListOptions>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
      </plugin>

      <!-- This plugin will fail if any POM is marked as Byte Order Mark is UTF-8 (BOM).
         | If this occurs, create a new POM and move the contents in order to fix.
         | For reference, this was a problem with mybatis/mybatis-spring poms. -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <!-- select non-aggregate reports -->
              <report>report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>

    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>format</id>
      <activation>
        <file>
          <exists>${project.basedir}/format.xml</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>net.revelc.code.formatter</groupId>
            <artifactId>formatter-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>format</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>net.revelc.code</groupId>
            <artifactId>impsort-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>sort</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.openrewrite.maven</groupId>
            <artifactId>rewrite-maven-plugin</artifactId>
            <configuration>
              <activeRecipes>
                <recipe>org.openrewrite.java.RemoveUnusedImports</recipe>
              </activeRecipes>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>run</goal>
                </goals>
                <phase>process-sources</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>com.github.ekryd.sortpom</groupId>
            <artifactId>sortpom-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>sort</goal>
                </goals>
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>

          <!-- Skip license plugin during release as maven release is now shallow cloned -->
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
            <configuration>
              <skip>true</skip>
            </configuration>
          </plugin>

          <!-- deploy site with maven-scm-publish-plugin -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-scm-publish-plugin</artifactId>
            <executions>
              <execution>
                <id>scm-publish</id>
                <goals>
                  <goal>publish-scm</goal>
                </goals>
                <phase>site-deploy</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>bundle</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>single</goal>
                </goals>
                <phase>package</phase>
                <configuration>
                  <attach>true</attach>
                  <appendAssemblyId>false</appendAssemblyId>
                  <descriptorRefs>
                    <descriptorRef>base-bundle</descriptorRef>
                  </descriptorRefs>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>license</id>
      <activation>
        <file>
          <exists>${project.basedir}/LICENSE_HEADER</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>format</goal>
                </goals>
                <phase>process-sources</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>eclipse</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <properties>
        <!-- Make m2e use java at test revision level -->
        <maven.compiler.source>${java.test.version}</maven.compiler.source>
        <maven.compiler.target>${java.test.release.version}</maven.compiler.target>
        <maven.compiler.release>${java.test.release.version}</maven.compiler.release>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <!-- Keep version here as fake version and maven report info has problems defining it in dependency management -->
              <version>${lifecycle.plugin}</version>
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <!-- impsort m2e in plugin states to ignore, we will process -->
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>net.revelc.code</groupId>
                        <artifactId>impsort-maven-plugin</artifactId>
                        <versionRange>[${impsort.plugin},)</versionRange>
                        <goals>
                          <goal>sort</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <execute>
                          <runOnConfiguration>true</runOnConfiguration>
                          <runOnIncremental>true</runOnIncremental>
                        </execute>
                      </action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>

    <!-- Keeping for now for future split when bnd plugin supports 7.0.0 -->
    <profile>
      <id>pre17</id>
      <activation>
        <jdk>(,17)</jdk>
      </activation>
      <properties>
        <bnd.plugin>6.4.0</bnd.plugin>
        <bnd.version>6.4.1</bnd.version>
      </properties>
    </profile>
  </profiles>

</project>
