<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>net.bytebuddy</groupId>
    <artifactId>byte-buddy-parent</artifactId>
    <version>1.12.23</version>
    <packaging>pom</packaging>

    <inceptionYear>2014</inceptionYear>

    <name>Byte Buddy (parent)</name>
    <description>
        Byte Buddy is a Java library for creating Java classes at run time.
        The parent artifact contains configuration information that concern all modules.
    </description>
    <url>https://bytebuddy.net</url>

    <!--
      There are several build profiles available:
      - extras: Creates additional artifacts containing source files and javadoc. (activated on release)
      - gpg: Sign all artifacts using gpg. (activated on release)
      - checks: Applies style checks to the source files. (activated by default, activated on release)
      - integration: Runs additional unit tests that are long-running (activated on CI server)
      - analysis: Executes static code analysis (activated on CI server)
      - android: Builds an Android test application. An Android SDK is required for doing so. (excluded from release)
      - native-compile: Compiles the native extensions required by Byte Buddy agent.

      It is also possible to build Byte Buddy against a specific byte code level. By default, Byte Buddy is Java 5 compatible
      but requires Java 6 to build and to run tests: By activating a profile javaX where X is a specific version number,
      tests and source are compiled to a differing byte code level.

      Additionally, the following reports are available via Maven:
      - jacoco:prepare-agent verify jacoco:report - Computes coverage for test suite (all modules)
      - org.pitest:pitest-maven:mutationCoverage - Runs mutation tests (all modules)
      - spotbugs:spotbugs spotbugs:gui - Runs spotbugs and shows a report in a graphical interface (module specific)
      - com.github.ferstl:jitwatch-jarscan-maven-plugin:scan - Finds all methods above HotSpot's inlining threshold
      - versions:update-properties - Automated dependency version update.

      At last, two pseudo-profiles are available that allow for handling checksum data for all downloaded artifacts.
      These profiles are only effective if the Maven wrapper is used, which is configured to install the Maven
      checksum extension:
      - checksum-collect: collects checksums for all used artifacts.
      - checksum-enforce: enforces that all downloaded artifacts represent a known checksum
    -->

    <modules>
        <module>byte-buddy</module>
        <module>byte-buddy-dep</module>
        <module>byte-buddy-benchmark</module>
        <module>byte-buddy-agent</module>
        <module>byte-buddy-android</module>
        <module>byte-buddy-android-test</module>
        <module>byte-buddy-maven-plugin</module>
        <module>byte-buddy-gradle-plugin</module>
    </modules>

    <properties>
        <copyright.holder>Rafael Winterhalter</copyright.holder>
        <bytebuddy.extras>false</bytebuddy.extras>
        <bytebuddy.integration>false</bytebuddy.integration>
        <bytebuddy.experimental>false</bytebuddy.experimental>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.outputTimestamp>1675590562</project.build.outputTimestamp>
        <sourcecode.main.version>1.5</sourcecode.main.version>
        <sourcecode.test.version>1.6</sourcecode.test.version>
        <bytecode.main.version>1.5</bytecode.main.version>
        <bytecode.test.version>1.6</bytecode.test.version>
        <pitest.target>net.bytebuddy</pitest.target>
        <nexus.url>https://s01.oss.sonatype.org</nexus.url>
        <version.asm>9.4</version.asm>
        <version.jna>5.12.1</version.jna>
        <version.junit>4.13.2</version.junit>
        <version.mockito>2.28.2</version.mockito>
        <version.plugin.clean>3.2.0</version.plugin.clean>
        <version.plugin.bundle>5.1.7</version.plugin.bundle>
        <version.plugin.compiler>3.10.1</version.plugin.compiler>
        <version.plugin.install>3.0.1</version.plugin.install>
        <version.plugin.deploy>3.0.0</version.plugin.deploy>
        <version.plugin.staging>1.6.13</version.plugin.staging>
        <version.plugin.versions>2.11.0</version.plugin.versions>
        <version.plugin.javadoc>3.4.0</version.plugin.javadoc>
        <version.plugin.source>3.2.1</version.plugin.source>
        <version.plugin.shade>3.3.0</version.plugin.shade>
        <version.plugin.gpg>3.0.1</version.plugin.gpg>
        <version.plugin.jxr>3.2.0</version.plugin.jxr>
        <version.plugin.buildhelper>3.3.0</version.plugin.buildhelper>
        <version.plugin.jar>3.2.2</version.plugin.jar>
        <version.plugin.site>3.12.0</version.plugin.site>
        <version.plugin.exec>3.1.0</version.plugin.exec>
        <version.plugin.plugin>3.6.4</version.plugin.plugin>
        <version.plugin.release>3.0.0-M7</version.plugin.release>
        <version.plugin.resources>3.2.0</version.plugin.resources>
        <version.plugin.assembly>3.4.2</version.plugin.assembly>
        <version.plugin.dependency>3.3.0</version.plugin.dependency>
        <version.plugin.help>3.2.0</version.plugin.help>
        <version.plugin.surefire>2.22.2</version.plugin.surefire>
        <version.plugin.pitest>1.9.2</version.plugin.pitest>
        <version.plugin.animal-sniffer>1.21</version.plugin.animal-sniffer>
        <version.plugin.enforcer>3.1.0</version.plugin.enforcer>
        <version.plugin.jacoco>0.8.8</version.plugin.jacoco>
        <version.plugin.coveralls>4.3.0</version.plugin.coveralls>
        <version.plugin.checkstyle>3.1.2</version.plugin.checkstyle>
        <version.plugin.jitwatch>1.1</version.plugin.jitwatch>
        <version.plugin.spotbugs>4.2.3</version.plugin.spotbugs>
        <version.plugin.modulemaker>1.9</version.plugin.modulemaker>
        <version.plugin.license>3.0</version.plugin.license>
        <version.plugin.japicmp>0.15.7</version.plugin.japicmp>
        <version.plugin.antrun>3.1.0</version.plugin.antrun>
        <version.checkstyle>9.3</version.checkstyle>
        <version.android.sdk>*******</version.android.sdk>
        <version.utility.findbugs>3.0.1</version.utility.findbugs>
        <version.utility.jsr305>3.0.2</version.utility.jsr305>
        <version.jmh>1.35</version.jmh>
        <version.cglib>3.3.0</version.cglib>
        <version.javassist>3.29.0-GA</version.javassist>
        <spotbugs.skip>false</spotbugs.skip>
        <jacoco.skip>false</jacoco.skip>
        <japicmp.skip>false</japicmp.skip>
        <modulemaker.skip>false</modulemaker.skip>
        <javadoc.download.skip>false</javadoc.download.skip>
        <repository.url>**************:raphw/byte-buddy.git</repository.url>
    </properties>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>A business-friendly OSS license</comments>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>raphw</id>
            <name>Rafael Winterhalter</name>
            <email><EMAIL></email>
            <url>https://rafael.codes</url>
            <roles>
                <role>developer</role>
            </roles>
            <timezone>+1</timezone>
        </developer>
    </developers>

    <issueManagement>
        <system>github.com</system>
        <url>https://github.com/raphw/byte-buddy/issues</url>
    </issueManagement>

    <scm>
        <connection>scm:git:${repository.url}</connection>
        <developerConnection>scm:git:${repository.url}</developerConnection>
        <url>${repository.url}</url>
        <tag>byte-buddy-1.12.23</tag>
    </scm>

    <dependencies>
        <!-- Allows the suppression of spotbugs false-positives by annotations without adding an actual dependency. -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>findbugs-annotations</artifactId>
            <version>${version.utility.findbugs}</version>
            <scope>provided</scope>
        </dependency>
        <!-- Allow for marking nullability of values.-->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>${version.utility.jsr305}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Define release properties. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${version.plugin.release}</version>
                <configuration>
                    <useReleaseProfile>false</useReleaseProfile>
                    <releaseProfiles>extras,gpg,gradle-release</releaseProfiles>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <tagNameFormat>byte-buddy-@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
            <!-- Enable mutation testing. -->
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${version.plugin.pitest}</version>
                <configuration>
                    <targetClasses>
                        <param>${pitest.target}.*</param>
                    </targetClasses>
                    <targetTests>
                        <param>${pitest.target}.*</param>
                    </targetTests>
                </configuration>
            </plugin>
            <!-- Configure Jacoco support for evaluating test case coverage. -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${version.plugin.jacoco}</version>
                <configuration>
                    <skip>${jacoco.skip}</skip>
                    <includes>
                        <include>net/bytebuddy/**</include>
                    </includes>
                    <excludes>
                        <!-- Do not include JMH generated classes (both modern and legacy JMH).-->
                        <exclude>net/bytebuddy/benchmark/generated/*</exclude>
                        <exclude>net/bytebuddy/benchmark/jmh_generated/*</exclude>
                        <!-- Avoid adding synthetic members to test classes as test assert class members. -->
                        <exclude>*Test*</exclude>
                        <exclude>*test*</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!-- Generate coveralls reports from CI server. -->
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>${version.plugin.coveralls}</version>
                <configuration>
                    <sourceDirectories>
                        <sourceDirectory>${project.basedir}/byte-buddy-dep/src/main/java-6</sourceDirectory>
                    </sourceDirectories>
                </configuration>
            </plugin>
            <!-- Also allow for manual spotbugs execution. Note that the generated warnings do not always apply for Byte Buddy's use case. -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${version.plugin.spotbugs}</version>
                <configuration>
                    <skip>${spotbugs.skip}</skip>
                    <effort>Max</effort>
                    <threshold>Low</threshold>
                    <xmlOutput>true</xmlOutput>
                    <failOnError>false</failOnError>
                    <spotbugsXmlOutputDirectory>${project.build.directory}/spotbugs</spotbugsXmlOutputDirectory>
                    <excludeFilterFile>${project.basedir}/../spotbugs-exclude.xml</excludeFilterFile>
                </configuration>
            </plugin>
            <!-- Enable scanning for methods above the inlining threshold (JDK 7+) -->
            <plugin>
                <groupId>com.github.ferstl</groupId>
                <artifactId>jitwatch-jarscan-maven-plugin</artifactId>
                <version>${version.plugin.jitwatch}</version>
            </plugin>
            <!-- Add license headers to all files. -->
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>${version.plugin.license}</version>
                <inherited>false</inherited>
                <configuration>
                    <header>${project.basedir}/NOTICE</header>
                    <aggregate>true</aggregate>
                    <failIfMissing>true</failIfMissing>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <properties>
                        <copyright.holder>${copyright.holder}</copyright.holder>
                    </properties>
                    <includes>
                        <include>**/main/java/**/*.java</include>
                        <include>**/main/java-*/**/*.java</include>
                        <include>**/main/c/**/*.c</include>
                    </includes>
                    <strictCheck>true</strictCheck>
                    <mapping>
                        <java>SLASHSTAR_STYLE</java>
                    </mapping>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Setup plugin for deployment to Maven Central. -->
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>${version.plugin.staging}</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>central</serverId>
                    <nexusUrl>${nexus.url}</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                </configuration>
            </plugin>
            <!-- Allow for automated version updates. -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>${version.plugin.versions}</version>
                <configuration>
                    <rulesUri>file://${session.executionRootDirectory}/version-rules.xml</rulesUri>
                </configuration>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${version.plugin.clean}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${version.plugin.jar}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${version.plugin.resources}</version>
                    <configuration>
                        <!-- Setting this property suppresses a warning on implicit setting the filter encoding. -->
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${version.plugin.install}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${version.plugin.surefire}</version>
                    <configuration>
                        <systemPropertyVariables>
                            <net.bytebuddy.experimental>${bytebuddy.experimental}</net.bytebuddy.experimental>
                            <net.bytebuddy.test.integration>${bytebuddy.integration}</net.bytebuddy.test.integration>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${version.plugin.deploy}</version>
                    <configuration>
                        <updateReleaseInfo>true</updateReleaseInfo>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${version.plugin.site}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${version.plugin.compiler}</version>
                    <inherited>true</inherited>
                    <configuration>
                        <source>${sourcecode.main.version}</source>
                        <target>${bytecode.main.version}</target>
                        <testSource>${sourcecode.test.version}</testSource>
                        <testTarget>${bytecode.test.version}</testTarget>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <showWarnings>true</showWarnings>
                        <showDeprecation>true</showDeprecation>
                        <compilerArgument>-Xlint:all,-options,-processing</compilerArgument>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${version.plugin.javadoc}</version>
                    <configuration>
                        <source>${sourcecode.main.version}</source>
                        <failOnWarnings>true</failOnWarnings>
                        <detectOfflineLinks>false</detectOfflineLinks>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>${version.plugin.plugin}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${version.asm}</version>
                        </dependency>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm-commons</artifactId>
                            <version>${version.asm}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${version.plugin.assembly}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${version.plugin.dependency}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>${version.plugin.help}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- Define explicit version to overcome problem with generated reports. -->
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>${version.plugin.jxr}</version>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <snapshotRepository>
            <id>central</id>
            <url>${nexus.url}/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>central</id>
            <url>${nexus.url}/service/local/staging/deploy/maven2</url>
        </repository>
    </distributionManagement>

    <profiles>
        <!-- Runs the build with compatibility for Java 6 JVMs. -->
        <profile>
            <id>java6-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.6</jdk>
            </activation>
            <properties>
                <version.asm.deprecated>7.1</version.asm.deprecated>
                <version.plugin.clean>3.0.0</version.plugin.clean>
                <version.plugin.bundle>2.5.4</version.plugin.bundle>
                <version.plugin.compiler>3.6.2</version.plugin.compiler>
                <version.plugin.install>2.5.2</version.plugin.install>
                <version.plugin.deploy>2.8.2</version.plugin.deploy>
                <version.plugin.staging>1.6.8</version.plugin.staging>
                <version.plugin.versions>2.4</version.plugin.versions>
                <version.plugin.source>2.4</version.plugin.source>
                <version.plugin.gpg>1.6</version.plugin.gpg>
                <version.plugin.jxr>2.5</version.plugin.jxr>
                <version.plugin.buildhelper>1.12</version.plugin.buildhelper>
                <version.plugin.jar>3.0.2</version.plugin.jar>
                <version.plugin.site>3.7.1</version.plugin.site>
                <version.plugin.exec>1.5.0</version.plugin.exec>
                <version.plugin.plugin>3.5.2</version.plugin.plugin>
                <version.plugin.resources>3.0.2</version.plugin.resources>
                <version.plugin.assembly>2.6</version.plugin.assembly>
                <version.plugin.dependency>2.10</version.plugin.dependency>
                <version.plugin.help>2.2</version.plugin.help>
                <version.plugin.animal-sniffer>1.16</version.plugin.animal-sniffer>
                <version.plugin.enforcer>1.4.1</version.plugin.enforcer>
                <version.plugin.jacoco>0.7.9</version.plugin.jacoco>
                <version.plugin.checkstyle>2.15</version.plugin.checkstyle>
                <version.plugin.spotbugs>3.1.0-RC8</version.plugin.spotbugs>
                <version.plugin.license>3.0</version.plugin.license>
                <version.plugin.shade>3.1.1</version.plugin.shade>
                <version.plugin.surefire>2.22.1</version.plugin.surefire>
                <version.plugin.javadoc>2.10.4</version.plugin.javadoc>
                <version.plugin.antrun>1.8</version.plugin.antrun>
                <version.checkstyle>6.1.1</version.checkstyle>
                <version.jmh>1.16</version.jmh>
                <version.cglib>3.2.12</version.cglib>
                <version.javassist>3.22.0-GA</version.javassist>
                <modulemaker.skip>true</modulemaker.skip>
                <spotbugs.skip>true</spotbugs.skip>
                <javadoc.download.skip>true</javadoc.download.skip>
            </properties>
            <modules>
                <module>byte-buddy</module>
                <module>byte-buddy-dep</module>
                <module>byte-buddy-benchmark</module>
                <module>byte-buddy-agent</module>
                <module>byte-buddy-android</module>
                <module>byte-buddy-maven-plugin</module>
                <module>byte-buddy-gradle-plugin</module>
            </modules>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-plugin-plugin</artifactId>
                            <version>${version.plugin.plugin}</version>
                            <dependencies>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm-commons</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                                <dependency>
                                    <groupId>org.ow2.asm</groupId>
                                    <artifactId>asm-deprecated</artifactId>
                                    <version>${version.asm.deprecated}</version>
                                </dependency>
                            </dependencies>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <!-- Runs the build with compatibility for Java 7 JVMs. -->
        <profile>
            <id>java7-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.7</jdk>
            </activation>
            <properties>
                <version.plugin.clean>3.1.0</version.plugin.clean>
                <version.plugin.bundle>3.5.1</version.plugin.bundle>
                <version.plugin.compiler>3.8.1</version.plugin.compiler>
                <version.plugin.staging>1.6.8</version.plugin.staging>
                <version.plugin.versions>2.8.1</version.plugin.versions>
                <version.plugin.jxr>3.1.1</version.plugin.jxr>
                <version.plugin.buildhelper>3.2.0</version.plugin.buildhelper>
                <version.plugin.site>3.11.0</version.plugin.site>
                <version.plugin.exec>3.0.0</version.plugin.exec>
                <version.plugin.assembly>3.3.0</version.plugin.assembly>
                <version.plugin.spotbugs>3.1.0-RC8</version.plugin.spotbugs>
                <version.plugin.javadoc>3.2.0</version.plugin.javadoc>
                <version.plugin.animal-sniffer>1.17</version.plugin.animal-sniffer>
                <version.plugin.enforcer>1.4.1</version.plugin.enforcer>
                <version.plugin.jacoco>0.7.9</version.plugin.jacoco>
                <version.plugin.checkstyle>3.0.0</version.plugin.checkstyle>
                <version.plugin.japicmp>0.13.1</version.plugin.japicmp>
                <version.plugin.shade>3.2.4</version.plugin.shade>
                <version.plugin.antrun>3.0.0</version.plugin.antrun>
                <version.checkstyle>6.19</version.checkstyle>
                <version.cglib>3.2.12</version.cglib>
                <version.javassist>3.23.2-GA</version.javassist>
                <spotbugs.skip>true</spotbugs.skip>
                <javadoc.download.skip>true</javadoc.download.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 9 JVMs. -->
        <profile>
            <id>java9-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>9</jdk>
            </activation>
            <properties>
                <project.build.outputTimestamp />
                <sourcecode.main.version>1.6</sourcecode.main.version>
                <sourcecode.test.version>1.6</sourcecode.test.version>
                <bytecode.main.version>1.6</bytecode.main.version>
                <bytecode.test.version>1.6</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 10 JVMs. -->
        <profile>
            <id>java10-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>10</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 11 JVMs. -->
        <profile>
            <id>java11-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>11</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 12 JVMs. -->
        <profile>
            <id>java12-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>12</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 13 JVMs. -->
        <profile>
            <id>java13-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>13</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 14 JVMs. -->
        <profile>
            <id>java14-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>14</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>1.7</sourcecode.main.version>
                <sourcecode.test.version>1.7</sourcecode.test.version>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 15 JVMs. -->
        <profile>
            <id>java15-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>15</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 16 JVMs. -->
        <profile>
            <id>java16-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>16</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 17 JVMs. -->
        <profile>
            <id>java17-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>17</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 18 JVMs. -->
        <profile>
            <id>java18-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>18</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 19 JVMs. -->
        <profile>
            <id>java19-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>19</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
            </properties>
        </profile>
        <!-- Runs the build with compatibility for Java 20 JVMs. -->
        <profile>
            <id>java20-compatibility</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>20</jdk>
            </activation>
            <properties>
                <sourcecode.main.version>8</sourcecode.main.version>
                <sourcecode.test.version>8</sourcecode.test.version>
                <bytecode.main.version>8</bytecode.main.version>
                <bytecode.test.version>8</bytecode.test.version>
                <jacoco.skip>true</jacoco.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 6. -->
        <profile>
            <id>java6</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.6</bytecode.main.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 7. -->
        <profile>
            <id>java7</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.7</bytecode.main.version>
                <bytecode.test.version>1.7</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 8. -->
        <profile>
            <id>java8</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>1.8</bytecode.main.version>
                <bytecode.test.version>1.8</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 9. -->
        <profile>
            <id>java9</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>9</bytecode.main.version>
                <bytecode.test.version>9</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 10. -->
        <profile>
            <id>java10</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>10</bytecode.main.version>
                <bytecode.test.version>10</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 11. -->
        <profile>
            <id>java11</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>11</bytecode.main.version>
                <bytecode.test.version>11</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 12. -->
        <profile>
            <id>java12</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>12</bytecode.main.version>
                <bytecode.test.version>12</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 13. -->
        <profile>
            <id>java13</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>13</bytecode.main.version>
                <bytecode.test.version>13</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 14. -->
        <profile>
            <id>java14</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>14</bytecode.main.version>
                <bytecode.test.version>14</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 15. -->
        <profile>
            <id>java15</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>15</bytecode.main.version>
                <bytecode.test.version>15</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 16. -->
        <profile>
            <id>java16</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>16</bytecode.main.version>
                <bytecode.test.version>16</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 17. -->
        <profile>
            <id>java17</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>17</bytecode.main.version>
                <bytecode.test.version>17</bytecode.test.version>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 18. -->
        <profile>
            <id>java18</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>18</bytecode.main.version>
                <bytecode.test.version>18</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 19. -->
        <profile>
            <id>java19</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>19</bytecode.main.version>
                <bytecode.test.version>19</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Builds using a byte code target for Java 20. -->
        <profile>
            <id>java20</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytecode.main.version>20</bytecode.main.version>
                <bytecode.test.version>20</bytecode.test.version>
                <spotbugs.skip>true</spotbugs.skip>
            </properties>
        </profile>
        <!-- Creates additional artifacts that are required for deployment. -->
        <profile>
            <id>extras</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytebuddy.extras>true</bytebuddy.extras>
            </properties>
            <build>
                <plugins>
                    <!-- Create source code artifact. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${version.plugin.source}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Create javadoc artifact. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${version.plugin.javadoc}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Sign any created artifact. (Requires configuration of gpg on the executing machine.) -->
        <profile>
            <id>gpg</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- Sign artifacts. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${version.plugin.gpg}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Basic checks that are not requiring too much runtime. -->
        <profile>
            <id>checks</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- Check style on build. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <version>${version.plugin.checkstyle}</version>
                        <executions>
                            <execution>
                                <phase>validate</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <configLocation>checkstyle.xml</configLocation>
                                    <consoleOutput>true</consoleOutput>
                                    <failsOnError>true</failsOnError>
                                    <excludes>**/generated/**/*</excludes>
                                    <includeResources>false</includeResources>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>com.puppycrawl.tools</groupId>
                                <artifactId>checkstyle</artifactId>
                                <version>${version.checkstyle}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <!-- Check API compatibility. -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>animal-sniffer-maven-plugin</artifactId>
                        <version>${version.plugin.animal-sniffer}</version>
                        <executions>
                            <execution>
                                <phase>test</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <signature>
                                        <groupId>org.codehaus.mojo.signature</groupId>
                                        <artifactId>java15</artifactId>
                                        <version>1.0</version>
                                    </signature>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>org.ow2.asm</groupId>
                                <artifactId>asm</artifactId>
                                <version>${version.asm}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <!-- Make sure that Byte Buddy does never depend on ASM's tree API. -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${version.plugin.enforcer}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <fail>true</fail>
                                    <rules>
                                        <bannedDependencies>
                                            <includes>
                                                <include>org.ow2.asm:asm-tree</include>
                                            </includes>
                                        </bannedDependencies>
                                        <requireMavenVersion>
                                            <version>[3.2.5,)</version>
                                        </requireMavenVersion>
                                        <requireJavaVersion>
                                            <version>[1.6,)</version>
                                        </requireJavaVersion>
                                    </rules>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Integration profile that executes long-running tasks. -->
        <profile>
            <id>integration</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <bytebuddy.integration>true</bytebuddy.integration>
            </properties>
        </profile>
        <!-- Integration profile that executes static code analysis. -->
        <profile>
            <id>analysis</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <!-- Run spotbugs if not specified differently in a module.-->
                    <plugin>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs-maven-plugin</artifactId>
                        <version>${version.plugin.spotbugs}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <skip>${spotbugs.skip}</skip>
                                    <effort>Max</effort>
                                    <threshold>Low</threshold>
                                    <xmlOutput>true</xmlOutput>
                                    <failOnError>true</failOnError>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Avoid violating semantic versioning. -->
                    <plugin>
                        <groupId>com.github.siom79.japicmp</groupId>
                        <artifactId>japicmp-maven-plugin</artifactId>
                        <version>${version.plugin.japicmp}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>cmp</goal>
                                </goals>
                                <configuration>
                                    <skip>${japicmp.skip}</skip>
                                    <newVersion>
                                        <file>
                                            <path>${project.build.directory}/${project.artifactId}-${project.version}.jar</path>
                                        </file>
                                    </newVersion>
                                    <parameter>
                                        <accessModifier>public</accessModifier>
                                        <oldVersionPattern>\d+\.\d+\.\d+</oldVersionPattern>
                                        <ignoreMissingClasses>true</ignoreMissingClasses>
                                        <onlyBinaryIncompatible>true</onlyBinaryIncompatible>
                                        <breakBuildBasedOnSemanticVersioning>true</breakBuildBasedOnSemanticVersioning>
                                        <excludes>
                                            <exclude>net.bytebuddy.agent.builder.AgentBuilder$Default$ExecutingTransformer$Factory$ForJava9CapableVm</exclude>
                                            <exclude>net.bytebuddy.agent.builder.AgentBuilder$Default$ExecutingTransformer$Factory$ForLegacyVm</exclude>
                                        </excludes>
                                    </parameter>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Pseudo profiles for checksum collection (only available via Maven wrapper). -->
        <profile>
            <id>checksum-collect</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>${version.plugin.antrun}</version>
                        <inherited>false</inherited>
                        <executions>
                            <execution>
                                <id>checksum-collect</id>
                                <phase>initialize</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo level="info" message="Checksum collection is enabled (only if using mvnw)." />
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <!-- Pseudo profiles for checksum collection (only available via Maven wrapper). -->
        <profile>
            <id>checksum-enforce</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>${version.plugin.antrun}</version>
                        <inherited>false</inherited>
                        <executions>
                            <execution>
                                <id>checksum-enforce</id>
                                <phase>initialize</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <echo level="info" message="Checksum enforcement is enabled (only if using mvnw)." />
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-dep</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-benchmark</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-android</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-maven-plugin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>byte-buddy-gradle-plugin</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
