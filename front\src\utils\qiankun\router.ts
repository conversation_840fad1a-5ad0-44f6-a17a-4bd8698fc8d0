import { computed, ref } from 'vue';
import { RouteItem } from '/@/api/sys/model/menuModel';
import type { Router } from 'vue-router';
import { getMicroName, MICRO_NAME, useQiankun } from '.';

export const keepAlive = ref<string[]>([]);
export const keepAliveKeys = ref({});
export const keepAliveTabs = ref<RouteItem[]>([]);

export function setKeepAlive(keepAliveNames: string[], tabs: any[]) {
  keepAliveTabs.value = tabs;
  keepAlive.value = keepAliveNames;
  const keys = {};
  tabs.forEach((item) => {
    keys[item.fullPath] = item;
  });
  keepAliveKeys.value = keys;
}

export const getKeepAliveKey = computed(() => (route) => keepAliveKeys.value[route.fullPath]?.meta?.keepAliveKey || keepAliveKeys.value[route.fullPath]?.query?.keepAliveKey);

/**
 * 重写路由push方法
 * @param router
 */
export function rewriteRouterPush(router: Router) {
  const rawRouter = router.push;
  const { mainRouter } = useQiankun();
  // @ts-ignore
  router.push = (to: any) => {
    if (getMicroName?.(to) === MICRO_NAME) {
      rawRouter(to);
    } else {
      mainRouter.push(to);
    }
  };
}
