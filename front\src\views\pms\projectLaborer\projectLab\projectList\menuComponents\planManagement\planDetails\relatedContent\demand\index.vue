<template>
  <Layout class="ui-2-0">
    <BasicTable
      ref="tableRef"
      :row-selection="pageType==='page'?{ type: 'checkbox' }:false"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
  <NewButtonModal
    v-if="pageType==='page'"
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
  <ViewDetails
    v-if="viewDetails.visible"
    :data="viewDetails"
  />
  <SearchModal
    v-if="pageType==='page'"
    @register="searchRegister"
    @searchEmit="searchEmit"
  />
</template>

<script>
import {
  BasicTable, Layout, isPower, useDrawer,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed, h,
} from 'vue';
import Api from '/@/api';
import { formatDate } from '/@/views/pms/projectLaborer/utils';
import ViewDetails from './ViewDetails.vue';
import SearchModal from './SearchModal.vue';
export default {
  name: 'Index',
  components: {
    NewButtonModal,
    Layout,
    BasicTable,
    ViewDetails,
    SearchModal,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      viewDetails: {},
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 100,
        },
        {
          title: '标题',
          dataIndex: 'name',
          align: 'left',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('RWX_container_button_27', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('RWX_container_button_27', state.powerData)) {
                    handleOpen(record.id);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
          minWidth: 250,
        },
        {
          title: '提出人',
          dataIndex: 'exhibitor',
          align: 'left',
          width: 150,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          width: 150,
          customRender: ({ text }) => formatDate(text, 'YYYY-MM-DD'),
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          width: 150,
          customRender: ({ text }) => formatDate(text, 'YYYY-MM-DD'),
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 150,
        },
        {
          title: '进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          width: 150,
        },
      ],
      dataSource: [],
      loading: false,
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('RWX_container_button_26', state.powerData)) },
        open: { show: computed(() => isPower('RWX_container_button_27', state.powerData)) },
        search: { show: computed(() => isPower('RWX_container_button_28', state.powerData)) },
      },
    });
    function getPage(obj = {}) {
      state.loading = true;
      const love = {
        id: props.formId,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联需求', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${props.formId}】关联需求列表`,
      };
      const url = `plan/relation/demand/${props.formId}`;
      new Api('/pms', love)
        .fetch(obj, url, 'POST')
        .then((res) => {
          state.dataSource = res;
          state.tableRef.clearSelectedRowKeys();
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    }
    function searchEmit(data) {
      getPage(data);
    }
    function isSelectCheck() {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return true;
      }
      if (selectedRowKeys.length > 1) {
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleCheck(id) {
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联需求', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      new Api('/pms', love).fetch('', `demand-management/detail/${id}`).then((res) => {
        state.viewDetails = {
          visible: true,
          title: '查看信息',
          form: res,
        };
      });
    }
    function clickType(type) {
      if (type === 'open') {
        const bool = isSelectCheck();
        if (bool) {
          handleOpen(state.tableRef.getSelectRowKeys()[0]);
        }
      }
      if (type === 'search') {
        openSearchDrawer(true);
      }
      if (type === 'check') {
        const bool = isSelectCheck();
        if (bool) {
          handleCheck(state.tableRef.getSelectRowKeys()[0]);
        }
      }
    }
    function handleOpen(id) {
      const projectId = props.formId;
      const url = `pas/demandManagementDetails?itemId=${id}`;
      window.open(`/${url}`);
    }

    onMounted(() => {
      getPage();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      getPage,
      searchRegister,
      searchEmit,
    };
  },
};
</script>

<style scoped>
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
    height: 500px;
  }
</style>
