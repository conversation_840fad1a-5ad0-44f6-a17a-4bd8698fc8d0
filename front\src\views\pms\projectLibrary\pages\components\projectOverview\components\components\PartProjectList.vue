<template>
  <div>
    <div class="search_parent flex">
      <div
        class="search flex-f1"
      >
        <div
          v-for="(obj,index) in leftOptions"
          :key="obj.value"
          :class="divValue-1===index?'act': ''"
          @click="divChange(obj.value)"
        >
          {{ obj.name }}（{{ obj.count }}）
        </div>
      </div>

      <div class="flex flex-ac">
        <div>
          任务类型:
        </div>
        <RadioGroup
          v-model:value="radioValue"
          class="mr10 self-style"
          button-style="solid"
          @change="radioChange(radioValue)"
        >
          <RadioButton
            v-for="(obj,idx) in rightOptions"
            :key="idx"
            :value="obj.value"
            class="click-button"
          >
            {{ obj.name }}
          </RadioButton>
          <div
            class="more action-btn"
            @click="todoMore"
          >
            <span>更多</span>
            <Icon
              icon="orion-icon-right"
              size="14"
            />
          </div>
        </RadioGroup>
      </div>
    </div>
    <div class="table-box">
      <Spin
        :delay="300"
        :spinning="loading"
      >
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        >
          <template #actions="{record}">
            <BasicTableAction
              :actions="actions"
              :record="record"
              @actionClick="actionClick($event,record)"
            />
          </template>
        </OrionTable>
      </Spin>
    </div>
    <AddModal
      :planActiveOptions="planActiveOptions"
      @register="registerAdd"
      @handleColse="() => addModalVisibleChange(false, {})"
    />
  </div>
</template>
<script lang='ts' setup>
import {
  computed, h, inject, onMounted, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import { openInForm, openReasonForm } from '../../utils';
import {
  Icon,
  OrionTable,
  IOrionTableActionItem,
  isPower,
  BasicTableAction,
  useModal,
  getDictByNumber,
  openModal as openModalNew,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { RadioButton, RadioGroup, Spin } from 'ant-design-vue';
import Api from '/@/api';
import {
  openFormDrawer,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/utils';
import AddTableModal
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/components/AddTableModal.vue';
import AddModal
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/components/AddPlan.vue';
import ProjectPlanDetail from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails.vue';
const [registerAdd, { openModal: setAddPlan }] = useModal();

const powerData = inject('powerData');
const updateAction = inject('updateAction') as (id:string)=>void;
const router = useRouter();
const loading = ref<boolean>(false);
const planActiveOptions: Ref<Record<any, any>[]> = ref([]);

// 左边边按钮点击改变事件
const divValue = ref<number>(1);
const OverdueColumns = [
  {
    title: '项目计划状态',
    width: 100,
    dataIndex: 'status',
    customRender({ text, record }) {
      return h('div', {
        class: 'box-big',
      }, [
        h('Icon', {
          class: 'fa-circle red',
          size: 2,
        }),
        h('div', {
          class: 'status-show',
        }, [
          h('span', {
            class: 'status-text',
          }, record.circumstanceName),
        ]),
      ]);
    },
  },
  {
    title: '计划名称',
    dataIndex: 'name',
    customRender({ text, record }) {
      return h('span', {
        class: 'plan-name',
        onClick: () => onClickBulletFame(record),
      }, text || '');
    },
  },
  {
    title: '计划完成时间',
    width: 115,
    dataIndex: 'endTime',
    customRender({ record }) {
      return record.endTime ? dayjs(record.endTime).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '逾期天数',
    width: 100,
    dataIndex: 'overdueDays',
    customRender({ text }) {
      return h('span', {
        class: 'days-lay',
      }, `逾期${text || 0}天` || '');
    },
  },
  {
    title: '逾期次数',
    width: 100,
    dataIndex: 'overdueCount',
    customRender({ text, record }) {
      return h('span', {
        class: 'days-lay',
      }, text || '');
    },
  },
  {
    title: '逾期原因',
    width: 200,
    dataIndex: 'delayEndReason',
  },
  {
    title: '责任部门',
    width: 120,
    dataIndex: 'rspSubDeptName',
  },
  {
    title: '计划责任人',
    dataIndex: 'rspUserName', // 到时候要改
    width: 100,
  },
  {
    title: '管理介入/改进',
    dataIndex: 'actions',
    fixed: 'right',
    slots: { customRender: 'actions' },
    width: 180,
  },
];
const InterimPeriodColumns = [
  {
    title: '项目计划状态',
    width: 100,
    dataIndex: 'status',
    customRender({ text, record }) {
      return h('div', {
        class: 'box-big',
      }, [
        h('Icon', {
          class: 'fa-circle red',
          size: 2,
        }),
        h('div', {
          class: 'status-show',
        }, [
          h('span', {
            class: 'status-text',
          }, record.dataStatus?.name),
        ]),
      ]);
    },
  },
  {
    title: '计划名称',
    dataIndex: 'name',
    customRender({ text, record }) {
      return h('span', {
        class: 'plan-name',
        onClick: () => onClickBulletFame(record),
      }, text || '');
    },
  },
  {
    title: '计划开始时间',
    dataIndex: 'beginTime',
    width: 115,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '计划完成时间',
    width: 115,
    dataIndex: 'endTime',
    customRender({ record }) {
      return record.endTime ? dayjs(record.endTime).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '临期剩余天数',
    width: 120,
    dataIndex: 'overdueDays',
    customRender({ text }) {
      return h('span', {
        class: 'days-lay',
      }, `临期剩余${text || 0}天` || '');
    },
  },
  {
    title: '责任部门',
    width: 120,
    dataIndex: 'rspSubDeptName',
  },
  {
    title: '计划责任人',
    dataIndex: 'rspUserName',
    width: 100,
  },
];
const RiskColumns = [
  {
    title: '项目风险状态',
    width: 100,
    dataIndex: 'status',
    customRender({ text, record }) {
      return h('div', {
        class: 'box-big',
      }, [
        h('Icon', {
          class: 'fa-circle red',
          size: 2,
        }),
        h('div', {
          class: 'status-show',
        }, [
          h('span', {
            class: 'status-text',
          }, record.dataStatus?.name),
        ]),
      ]);
    },
  },
  {
    title: '风险名称',
    dataIndex: 'name',
    customRender({ text, record }) {
      return h('span', {
        class: 'plan-name',
        onClick: () => openDetails(record),
      }, text || '');
    },
  },
  {
    title: '风险完成时间',
    width: 115,
    dataIndex: 'endTime',
    customRender({ record }) {
      return record.predictEndTime ? dayjs(record.predictEndTime).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '发生概率',
    width: 115,
    dataIndex: 'riskProbabilityName',
    customRender({ text, record }) {
      return h('span', {
        class: 'plan-name-red',
      }, text || '');
    },
  },
  {
    title: '影响程度',
    width: 115,
    dataIndex: 'riskInfluenceName',
    customRender({ text }) {
      return h('span', {
        class: 'plan-name-red',
      }, text || '');
    },
  },
  {
    title: '应对策略',
    width: 120,
    dataIndex: 'copingStrategyName',
  },
  {
    title: '责任人',
    dataIndex: 'principalName',
    width: 100,
  },
  {
    title: '是否转问题',
    dataIndex: 'isToQuestion',
    width: 100,
    customRender({ text }) {
      return text ? '是' : '否';
    },
  },
  {
    title: '管理介入/改进',
    dataIndex: 'actions',
    fixed: 'right',
    width: 180,
    slots: { customRender: 'actions' },
  },
];
const ProblemColumns = [
  {
    title: '项目问题状态',
    width: 100,
    dataIndex: 'status',
    customRender({ text, record }) {
      return h('div', {
        class: 'box-big',
      }, [
        h('Icon', {
          class: 'fa-circle red',
          size: 2,
        }),
        h('div', {
          class: 'status-show',
        }, [
          h('span', {
            class: 'status-text',
          }, record.circumstanceName || record.dataStatus?.name),
        ]),
      ]);
    },
  },
  {
    title: '问题名称',
    dataIndex: 'name',
    customRender({ text, record }) {
      return h('span', {
        class: 'plan-name',
        onClick: () => openQuestionDetails(record),
      }, text || '');
    },
  },
  {
    title: '问题完成时间',
    width: 115,
    dataIndex: 'endTime',
    customRender({ record }) {
      return record.endTime || record.predictEndTime ? dayjs(record.endTime || record.predictEndTime).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '问题类型',
    width: 115,
    dataIndex: 'questionTypeName',
    customRender({ text }) {
      return h('span', {
        class: 'plan-name-red',
      }, text || '');
    },
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevelName',
    width: 120,
    customRender({ text }) {
      return h('span', {
        class: 'plan-name-red',
      }, text || '');
    },
  },
  {
    title: '责任人',
    dataIndex: 'ownerName',
    width: 100,
  },
  {
    title: '是否转计划',
    dataIndex: 'isPlan',
    width: 100,
    customRender({ text }) {
      return text ? '是' : '否';
    },
  },
  {
    title: '管理介入/改进',
    dataIndex: 'actions',
    fixed: 'right',
    width: 180,
    slots: { customRender: 'actions' },
  },
];
const baseColumns = ref([]);
// 表格配置项
const tableOptions = computed(() => ({
  isSpacing: false,
  columns: baseColumns,
  dataSource,
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: { pageSize: 10 },
  showIndexColumn: false,
  height: 500,
}));
// 左边按钮配置项
const leftOptions = ref<any>([
  {
    name: '逾期',
    value: 1,
    count: 0,
  },
  {
    name: '临期',
    value: 2,
    count: 0,
  },
  {
    name: '风险',
    value: 3,
    count: 0,
  },
  {
    name: '问题',
    value: 4,
    count: 0,
  },
]);
// 右边按钮配置项
const rightOptions = ref<any>([
  {
    name: '当天任务',
    value: 0,
  },
  {
    name: '过去任务',
    value: 1,
  },
  {
    name: '本周任务',
    value: 2,
  },
  {
    name: '本月任务',
    value: 3,
  },
  {
    name: '全部',
    value: 4,
  },
]);
const actions = computed(() => (divValue.value === 1 ? overdueActions : divValue.value === 3 ? riskActions : divValue.value === 4 ? questionActions : []));
// 操作区域按钮定义
const overdueActions = [
  {
    text: '管理介入',
    event: 'intervene',
    isShow: () => isPower('PMS_XMGK_container_02_button_01', powerData),
  },
  {
    text: '逾期原因',
    event: 'Overdue',
    isShow: () => isPower('PMS_XMGK_container_02_button_02', powerData),
  },
];

// 操作区域按钮定义
const riskActions = [
  {
    text: '转问题',
    event: 'question',
    isShow: (record) => isPower('PMS_XMGK_container_02_button_03', powerData) && !record.isToQuestion,
  },
];

// 操作区域按钮定义
const questionActions = [
  {
    text: '转计划',
    event: 'plan',
    isShow: (record) => isPower('PMS_XMGK_container_02_button_04', powerData) && !record.isPlan,
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'intervene':
      openInForm({
        id: record.id,
        projectId: record.projectId,
        type: 'edit',
      }, addSuccess);
      break;
    case 'Overdue':
      openReasonForm({
        id: record.id,
        type: 'edit',
      }, addSuccess);
      break;
    case 'question':
      openFormDrawer(AddTableModal, {
        id: record.id,
        projectId: record.projectId,
        fromObjName: record?.name,
        modelName: 'pms',
        isQuestion: true,
        formType: 'Add',
        dataSource: record,
      }, addSuccess);
      break;
    case 'plan':
      addModalVisibleChange(true, record);
      break;
  }
}

function addModalVisibleChange(value: boolean, record: any) {
  if (value) {
    setAddPlan(true, {
      parentIds: record.id,
      parentData: [record],
      projectData: record,
      from: '',
      isPlan: true,
    });
  }
  if (!value) {
    addSuccess();
  }
}

// 项目计划详情
function onClickBulletFame(record) {
  const modalRef = ref();
  const modalData = {
    id: record.id,
  };
  openModalNew({
    title: '项目计划详情',
    width: 1200,
    height: 800,
    isFullScreen: false,
    content(h) {
      return h(ProjectPlanDetail, {
        ref: modalRef,
        modalData,
        router,
      });
    },
    footer: null,
  });
}

// 风险详情
const openDetails = (record) => {
  router.push({
    name: 'PMSRiskManagementDetails',
    params: {
      id: record.id,
    },
  });
};

// 问题详情
const openQuestionDetails = (record) => {
  router.push({
    name: 'PMSQuestionManagementDetails',
    params: {
      id: record.id,
    },
  });
};

// 更多按钮跳转点击事件
function todoMore() {
  updateAction('project_plan');
}

const tableRef = ref();

// 右边按钮点击改变事件
const radioValue = ref<number>(4);
const radioChange = async (value) => {
  radioValue.value = value;
  await getAbnormalInfo(); // 重新加载数据
};

const divChange = async (value) => {
  loading.value = true;
  baseColumns.value = value === 1 ? OverdueColumns : value === 2 ? InterimPeriodColumns : value === 3 ? RiskColumns : ProblemColumns;
  divValue.value = value;
  tableRef.value.reload();
  await getAbnormalInfo(); // 重新加载数据
};

const dataSource = ref([]);

// 异常计划信息接口
const abnormalInfo: Ref<{
  [propName: string]: any
}> = ref({});
const projectId: string = inject('projectId');

onMounted(async () => {
  baseColumns.value = OverdueColumns;
  await getAbnormalInfo();
  planActiveOptions.value = await getDictByNumber('planActive');
});

async function getAbnormalInfo() {
  const result = await new Api(`/pms/projectOverview/zgh/projectPlanOverdueCount/${projectId}`).fetch({
    status: divValue.value === 3 ? 4 : divValue.value === 4 ? 5 : divValue.value,
    dateType: radioValue.value,
  }, '', 'GET');
  abnormalInfo.value = result || {};
  dataSource.value = divValue.value === 1 || divValue.value === 2 ? result.projectSchemeVOS : divValue.value === 3 ? result.riskManagementVos : result.questionManagementVOs;
  // 更新leftOptions中的count值
  leftOptions.value = leftOptions.value.map((option) => {
    switch (option.value) {
      case 1:
        option.count = abnormalInfo.value.overdue || 0;
        break;
      case 2:
        option.count = abnormalInfo.value.doing || 0;
        break;
      case 3:
        option.count = abnormalInfo.value.riskCount || 0;
        break;
      case 4:
        option.count = abnormalInfo.value.questionCount || 0;
        break;
      default:
        option.count = 0;
    }
    return option;
  });
  loading.value = false;
}

function addSuccess() {
  radioChange(4);
  tableRef.value.reload();
}
</script>

<style lang='less' scoped>
.table-box {
  width: 100%;
}

.more {
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  color: #595959;
  line-height: 22px;
  cursor: pointer;
  transition: 0.2s;
  > span {
    margin-right: 10px;
  }
}
.plan-btn{
  color: #dddddd!important;
}
.search_parent{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search{
    display:flex;
    align-items: center;
    font-weight: bold;
    color: #979FB5;
    >div{
      margin-right: 6px;
      font-weight: bold;
      color: #979FB5;
      min-width: 86px;
    }
    >div:hover{
      font-weight: bold;
      font-size: 16px;
      color: #000;
      cursor: pointer;
    }
    .act{
      font-weight: bold;
      font-size: 16px;
      color: #000;
      cursor: pointer;
    }
  }
  .self-style{
    display: flex;
    padding-left: 5px;
    align-items: center;
    justify-content: flex-end;
    .show-button{
      background-color: ~`getPrefixVar('primary-color')`;
      color: #fff;
      cursor: pointer;
    }
  }
}

.more{
  margin-left: 10px;
}

:deep(.plan-name){
  color: ~`getPrefixVar('info-color')` !important;
  cursor: pointer;
}
:deep(.days-lay){
  color: ~`getPrefixVar('error-color')` !important;
}
:deep(.box-big){
  display: flex;
  align-items: center;
}
:deep(.red){
  width: 10px;
  height: 10px;
  background-color: ~`getPrefixVar('error-color')`;
  border-radius: 50%;
  overflow: hidden;
}
:deep(.status-show){
  margin-left: 10px;
  width: 80px;
  height: 20px;
  color: #fff;
  background-color: ~`getPrefixVar('error-color')`;
}
:deep(.status-text){
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.ant-basic-table) {
  padding: 0 !important;
}

:deep(.show-button){
  background-color: #5172DC;
  color: #fff;
}
</style>
