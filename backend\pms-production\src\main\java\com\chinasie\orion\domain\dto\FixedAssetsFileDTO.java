package com.chinasie.orion.domain.dto;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FixedAssetsFileDTO implements Serializable {

    /**
     * 数据id
     */
    @ApiModelProperty(value = "固定资产id")
    String dataId;

    /**
     * 附件列表
     */
    @ApiModelProperty(value = "附件对象列表")
    List<FileDTO> fileDTOList;

}
