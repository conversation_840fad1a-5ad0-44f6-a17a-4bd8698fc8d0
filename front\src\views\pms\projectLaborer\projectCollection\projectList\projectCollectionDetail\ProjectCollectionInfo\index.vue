<script setup lang="ts">
import {
  OrionTable, BasicButton, BasicCard, getDict,
} from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed, inject, h, reactive,
} from 'vue';
// import { openFormDrawer } from './utils';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectCollectionId = inject('projectId');
const state = reactive({
  loading: false,
  projectCollectionTypeDict: [],
  projectCollectionLevelDict: [],
});
const gridContentProps = reactive({
  list: [
    {
      label: '项目组合名称',
      field: 'name',
    },
    {
      label: '责任人',
      field: 'resPersonName',
    },
    {
      label: '负责部门',
      field: 'resDeptName',
    },
    // {
    //   label: '项目集类型',
    //   field: 'projectCollectionType',
    //   valueRender({ text }) {
    //     return h('span', state.projectCollectionTypeDict.find((item) => item.value === text)?.description);
    //   },
    // },
    {
      label: '项目集级别',
      field: 'projectCollectionLevel',
      valueRender({ text }) {
        return h('span', state.projectCollectionLevelDict.find((item) => item.value === text)?.description);
      },
    },
    {
      label: '项目干系人',
      field: 'relatedPersonListStr',
    },
    {
      label: '描述',
      field: 'remark',
      wrap: true,
    },
  ],
  dataSource: {},
});

onMounted(() => {
  getBasicInfo();
  getAllDict();
});

async function getBasicInfo() {
  state.loading = true;
  const result = await new Api(`/pms/projectCollection/${projectCollectionId}`).fetch({}, '', 'GET');

  result.relatedPersonListStr = result.relatedPersonList?.map((item) => item.name).join();
  gridContentProps.dataSource = result || {};
  state.loading = false;
}

async function getAllDict() {
  state.projectCollectionTypeDict = await getDict('dict1774974536895303680').then((res) => res?.map((item) => ({
    ...item,
    label: item.description,
    value: item.value,
  })) ?? []);

  state.projectCollectionLevelDict = await getDict('dict1774974891121053696').then((res) => res?.map((item) => ({
    ...item,
    label: item.description,
    value: item.value,
  })) ?? []);
}
</script>

<template>
  <div

    style="height: 100%"
  >
    <BasicCard
      height="100%"
      title="基本信息"
      :gridContentProps="gridContentProps"
      :column="4"
    />
  </div>
</template>

<style scoped lang="less">

</style>
