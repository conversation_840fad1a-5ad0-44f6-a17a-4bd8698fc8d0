<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ResourceAllocationOfMapper">


    <update id="setGroupConcatMaxLen">
        SET SESSION group_concat_max_len = 100000000;
    </update>


    <select id="getDepts" resultType="com.chinasie.orion.domain.dto.allocation.ResourceAllocationOf">
        select id,
               name      as `name`,
               parent_id AS parentId,
               ''        as number,
               ''        AS userCode,
               ''        AS repairOrgCount,
               ''        AS details
        FROM pmi_dept
    </select>

    <select id="getDeptIdByCode" resultType="java.lang.String">
        select id
        FROM pmi_dept
        where dept_code = #{deptCode}
    </select>

    <select id="getResourceAllocationOfPerson" resultType="com.chinasie.orion.domain.dto.allocation.ResourceAllocationOf">
        SELECT DISTINCT
        mange.number AS `number`,
        IF(mange.is_base_permanent = 1,to_person.plan_begin_time,mange.in_date) as realStartDate,
        IF(mange.is_base_permanent = 1,to_person.plan_end_time,mange.out_date) as realEndDate,
        IF(mange.is_base_permanent is null,0,mange.is_base_permanent) as isBasePermanent,
        repair_plan.base_code as basePlaceCode,
        repair_plan.base_name as basePlaceName,
        mange.id as rowId,
        to_person.id as relationId,
        repair_org.id as orgId,
        repair_org.code as teamCode,
        repair_org.name as teamName,
        repair_org_zy.code as specialtyCode,
        repair_org_zy.name as specialtyName,
        repair_plan.repair_round as repairRoundCode,
        repair_plan.name as repairRoundName
        FROM pmsx_person_mange mange
        LEFT JOIN
        pmsx_relation_org_to_person to_person ON mange.id = to_person.person_id
        LEFT JOIN
        pmsx_major_repair_org repair_org ON repair_org.id = to_person.repair_org_id AND repair_org.level = '3'
        LEFT JOIN
        pmsx_major_repair_org repair_org_zy
        ON repair_org_zy.id = repair_org.parent_id AND repair_org_zy.level = '2'
        LEFT JOIN
        pmsx_major_repair_plan repair_plan ON repair_plan.repair_round = repair_org.repair_round
        where ((mange.in_date IS NOT NULL
        and mange.out_date IS NOT NULL)
        or (to_person.plan_begin_time IS NOT NULL
        and to_person.plan_end_time IS NOT NULL))
        and mange.logic_status = 1
        and repair_org.logic_status = 1
        and repair_plan.logic_status = 1
            <![CDATA[
                AND (((#{paramMap.realStartDate} <= mange.in_date
                    and mange.out_date >= #{paramMap.realStartDate})
                OR (mange.out_date >= #{paramMap.realEndDate}
                    and mange.in_date <= #{paramMap.realEndDate}))
                OR ((to_person.plan_end_time >= #{paramMap.realStartDate}
                    AND to_person.plan_begin_time <= #{paramMap.realStartDate})
                OR (to_person.plan_end_time >= #{paramMap.realEndDate}
                    and to_person.plan_begin_time <= #{paramMap.realEndDate})))
            ]]>
        <if test="paramMap.keyWord != null and paramMap.keyWord != ''">
            AND (repair_plan.repair_round LIKE CONCAT('%',#{paramMap.keyWord},'%')
            or repair_plan.base_code LIKE CONCAT('%',#{paramMap.keyWord},'%')
            or repair_plan.base_name LIKE CONCAT('%',#{paramMap.keyWord},'%'))
        </if>
    </select>

    <select id="getResourceAllocationOfMaterial" resultType="com.chinasie.orion.domain.dto.allocation.ResourceAllocationOf">
        SELECT DISTINCT
        material_manage.number AS `number`,
        material_manage.in_date as realStartDate,
        material_manage.out_date as realEndDate,
        repair_plan.base_code as basePlaceCode,
        repair_plan.base_name as basePlaceName,
        material_manage.id as rowId,
        org_to_material.id as relationId,
        repair_org.id as orgId,
        material_manage.number as staffNo,
        repair_org.code as teamCode,
        repair_org.name as teamName,
        repair_org_zy.code as specialtyCode,
        repair_org_zy.name as specialtyName,
        repair_plan.repair_round as repairRoundCode,
        repair_plan.name as repairRoundName
        FROM pmsx_material_manage material_manage
        LEFT JOIN pmsx_relation_org_to_material org_to_material
        ON org_to_material.material_id = material_manage.id
        LEFT JOIN pmsx_major_repair_org repair_org
        ON repair_org.id = org_to_material.repair_org_id AND repair_org.level = '3'
        LEFT JOIN pmsx_major_repair_org repair_org_zy
        ON repair_org_zy.id = repair_org.parent_id AND repair_org_zy.level = '2'
        LEFT JOIN pmsx_major_repair_plan repair_plan ON repair_plan.repair_round = repair_org.repair_round
        WHERE material_manage.in_date IS NOT NULL
        and material_manage.out_date IS NOT NULL
        and material_manage.logic_status = 1
        and repair_org.logic_status = 1
        and repair_plan.logic_status = 1
        <![CDATA[
             AND ((#{paramMap.realStartDate} <= material_manage.in_date
                and material_manage.out_date >= #{paramMap.realStartDate})
             or (material_manage.out_date >= #{paramMap.realEndDate}
                and material_manage.in_date <= #{paramMap.realEndDate}))
         ]]>
        <if test="paramMap.keyWord != null and paramMap.keyWord != ''">
            AND (repair_plan.repair_round LIKE CONCAT('%',#{paramMap.keyWord},'%')
            or repair_plan.base_code LIKE CONCAT('%',#{paramMap.keyWord},'%')
            or repair_plan.base_name LIKE CONCAT('%',#{paramMap.keyWord},'%'))
        </if>
    </select>

    <delete id="deleteAllocationPerson" parameterType="java.util.List">
        DELETE FROM pmsx_person_mange WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePerson" parameterType="java.util.List">
        DELETE
        FROM pmsx_person_mange
        WHERE id = #{id}
    </delete>

    <update id="updateAllocationPerson">
        UPDATE pmsx_person_mange
        SET in_date  = #{paramMap.realStartDate},
            out_date = #{paramMap.realEndDate}
        WHERE id = #{id}
    </update>

    <delete id="deleteAllocationMaterial" parameterType="java.util.List">
        DELETE FROM pmsx_material_manage WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMaterial" parameterType="java.util.List">
        DELETE
        FROM pmsx_material_manage
        WHERE id = #{id}
    </delete>

    <update id="updateAllocationMaterial">
        UPDATE pmsx_material_manage
        SET in_date  = #{paramMap.realStartDate},
            out_date = #{paramMap.realEndDate}
        WHERE id = #{id}
    </update>


    <!--    <select id="queryRepairPlanByRepairRound" resultType="com.chinasie.orion.domain.vo.resourceAllocation.RepairPlanVO">-->
    <!--        SELECT-->
    <!--            repair_round as repairRound,-->
    <!--            name as repairName,-->
    <!--            begin_time as beginTime,-->
    <!--            end_time as endTime,-->
    <!--            base_code as baseCode,-->
    <!--            base_name as baseName-->
    <!--        FROM pmsx_major_repair_plan-->
    <!--        WHERE logic_status = 1-->
    <!--          AND repair_round = #{repairRound}-->
    <!--    </select>-->

    <select id="queryRepairPlan" resultType="com.chinasie.orion.domain.dto.allocation.RepairRoundAnd">
        SELECT
        repair_round as repairRound,
        name as repairName,
        begin_time as beginTime,
        end_time as endTime,
        base_code as baseCode,
        base_name as baseName
        FROM pmsx_major_repair_plan
        WHERE logic_status = 1
        and begin_time >= (SELECT DATE_FORMAT(NOW(),'%Y-%m-01') as first_day_of_current_month)
        <if test="repairRound!= null and repairRound!= ''">
            AND repair_round LIKE CONCAT ('%', #{repairRound}, '%')
        </if>
    </select>


</mapper>
