<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    />
  </div>
</template>
<script lang="ts" setup>
import { BasicForm, useForm, getDictByNumber } from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({
    nodeType: '',
  }),
});
const baseLineOptions:Ref<Record<any, any>[]> = ref([]);
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'baseLine',
      label: '基线',
      colProps: { span: 24 },
      required: true,
      componentProps: {
        placeholder: '请选择',
        options: computed(() => baseLineOptions.value),
        fieldNames: {
          label: 'name',
          value: 'value',
        },
      },
      component: 'Select',
    },
  ],
});
onMounted(async () => {
  baseLineOptions.value = await getDictByNumber('baseLine');
});

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    await new Api('/pms').fetch(params, 'projectScheme/projectScheme/getBaseLine', 'PUT');
    message.success('选择基线成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
</style>