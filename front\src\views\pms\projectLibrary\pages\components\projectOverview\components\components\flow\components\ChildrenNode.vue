<template>
  <div
    ref="wrapper"
    class="wrapper"
  >
    <div class="hover-box">
      <div
        class="current-button"
        :class="data?.data?.isFinish || data?.data?.isOver ? 'c-highlight' : ''"
        :style="{width: data?.data?.width + 'px', height: data?.data?.height + 'px', boxShadow: data?.id === '3-2' || data?.id === '3-2-1' || data?.id === '3-2-2' ? 'none' : ''}"
        @click="handleClick(data)"
      >
        <div
          class="action-table"
        >
          <div
            v-if="data?.id === '3-2-3'"
            class="action-table-icon"
          >
            <div>
              <img
                src="../../assets/start.jpg"
                class="w-img"
              >
            </div>
          </div>
          <div
            v-else
            class="action-table-icon"
          >
            <!--进行中-->
            <div
              v-if="data?.data?.isOver"
              :class="data?.id === '3-2-1' || data?.id === '3-2-2' ? 'circle4' : 'circle3'"
              :style="{borderColor: data?.data?.fill}"
            />
            <!--已完成-->
            <div
              v-else-if="(data?.id === '3-2-1' || data?.id === '3-2-2') && data?.data?.isFinish"
              :class="data?.id === '3-2-1' || data?.id === '3-2-2' ? 'circle4' : 'circle3'"
              :style="{borderColor: data?.data?.fill}"
            />
            <CheckCircleFilled
              v-else-if="data?.data?.isFinish"
              class="circle2"
              :style="{borderColor: data?.data?.fill}"
            />
            <!--未开始-->
            <div
              v-else
              :class="data?.id === '3-2-1' || data?.id === '3-2-2' ? 'circle4' : 'circle1'"
              :style="{borderColor: data?.data?.fill}"
            />
          </div>
        </div>
        <span
          class="fs-color"
          :class="data?.id === '3-2-1' || data?.id === '3-2-2' || data?.id === '3-2-3' ? 'fs' : ''"
        >
          {{ data?.data?.label }}
        </span>
      </div>
      <div
        class="line"
        :style="{backgroundColor: data?.data?.fill}"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  inject, onMounted, provide, ref,
} from 'vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';

const data:any = ref({});
const getNode:any = inject('getNode');
const wrapper = ref();
const visible = ref(false);

const handleActionClickNode = inject<((action: any) => void)>('handleActionClick');
const handleClick = (item) => {
  handleActionClickNode(item);
};
provide('handleActionClickNode', handleActionClickNode);

onMounted(() => {
  data.value = getNode();
});

</script>
<style lang="less" scoped>
.ant-spin-container{
  .parents{
    .wrapper{
      .hover-box{
        width: 100%!important;
        div{
          width: 100%!important;
        }
        .line{
          width: 5px!important;
        }
        .action-table{
          width: auto!important;
        }
        .current-button{
          width: 100%!important;
        }
        .circle1,.circle3{
          width: 15px!important;
        }
        .circle4{
          width: 12px!important;
        }
      }
    }
  }
}
.current-button{
  display: flex;
  align-items: center;
  padding-left: 10px;
  position: relative;
  width: 100%;
  height: 40px;
  border-radius: 5px;
  background: transparent;
  border: none;
  box-shadow: 1px 2px 5px rgba(21, 126, 223, 0.149019607843137);
  .action-table-icon{
    padding: 10px 10px 10px 15px;
    cursor: pointer;
    color: #999999;
    font-weight: 400;
    font-style: normal;
    .action-table{
      height: 28px;
    }
  }
  .circle1{
    width: 15px;
    height: 15px;
    background: #fff;
    border:1px solid #999;
    border-radius: 50%;
    position: absolute;
    left: 15px;
    top: 13px;
  }
  .circle2{
    width: 15px;
    height: 15px;
    color:#1890ff;
    border-radius: 50%;
    position: absolute;
    left: 15px;
    top: 13px;
    background-color: #fff;
  }
  .circle3{
    width: 15px;
    height: 15px;
    background: #fff;
    border-radius: 50%;
    border:1px solid #F09509;
    position: absolute;
    left: 15px;
    top: 13px;
  }
  .circle4 {
    width: 12px;
    height: 12px;
    border: 2px solid rgb(24, 144, 255);
    border-radius: 50%;
    position: absolute;
    left: 17px;
    top: 15px;
  }
  span{
    font-size: 14px;
    color: #999;
    font-weight: 400;
  }
  .fs{
    font-size: 12px;
  }
}
.current-button.c-highlight {
  .fs-color{
    color: #39496B;
  }
}
.line{
  position: absolute;
  left: 0;
  top: 0;
  width: 5px;
  height: 40px;
  border: none;
  border-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: #ccc;
}
.w-img{
  width: 19px;
  height: 19px;
  position: absolute;
  left: 13px;
  top: 13px;
}
</style>
