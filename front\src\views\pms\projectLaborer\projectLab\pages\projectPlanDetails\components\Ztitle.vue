<template>
  <div>
    <div class="box">
      <div class="boxin">
        <div class="boxin-item" />
        <div class="item">
          {{ title }}
        </div>
      </div>
      <div class="rightF">
        <!--        <a-button type="link" @click="addPeopel">-->
        <!--          <template #icon><PlusOutlined /></template>-->
        <!--          添加-->
        <!--        </a-button>-->
        <!--        <a-button type="link" @click="openAddModal">-->
        <!--          <template #icon><SendOutlined /></template>-->
        <!--          通知参会-->
        <!--        </a-button>-->
        <!--        <a-button type="link" @click="openAddModal">-->
        <!--          <template #icon><CheckOutlined /></template>-->
        <!--          签到确认-->
        <!--        </a-button>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
// import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({
  name: 'Index',
  components: {},
  props: {
    title: {},
  },
  emits: [],
  setup() {
    const state = reactive({});
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less">
.box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  //border-bottom: 1px solid rgba(204, 204, 204, 0.49);
  .boxin {
    display: flex;
    align-items: center;
    .boxin-item {
      width: 5px;
      height: 14px;
      border-left: 4px solid ~`getPrefixVar('primary-color')`;
      margin-right: 10px;
    }
    .item {
      font-weight: 600;
      font-size: 14px;
    }
  }
  .rightF {
    display: flex;
    justify-content: right;
    align-items: center;
  }
}
</style>
