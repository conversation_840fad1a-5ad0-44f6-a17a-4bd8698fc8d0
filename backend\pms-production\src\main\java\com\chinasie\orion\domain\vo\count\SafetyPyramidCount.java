package com.chinasie.orion.domain.vo.count;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/9:02
 * @description:
 */
@Data
public class SafetyPyramidCount implements Serializable {
    @ApiModelProperty(value = "时间类型：安全、质量")
    private String key;
    @ApiModelProperty(value = "统计数列表")
    private List<SafetyPyramid> countList;


    @Data
    public static  class  SafetyPyramid{
        @ApiModelProperty(value = "级别")
        private String assessmentLevel;

        @ApiModelProperty(value = "统计数")
        private Long count;
    }
}
