<template>
  <div class="files-wrap">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :dataSource="dataSource"
      class="pay-node-table"
      @buttonClick="onButtonClick"
    >
      <template #toolbarLeft>
        <div>
          <BasicUpload
            button-text="上传附件"
            :max-number="100"
            @saveChange="saveChange"
          />
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script setup lang="ts">
import {
  BasicUpload, downLoadByFilePath, OrionTable, randomString,
} from 'lyra-component-vue3';
import {
  inject,
  onMounted, Ref, ref, unref,
} from 'vue';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';

const dataId:Ref = inject('dataId');
const detailsData:Ref<Record<string, any>> = inject('detailsData');
const userStore: Record<string, any> = useUserStore();
const dataSource = ref([]);
const tableRef: Ref = ref();

onMounted(() => {
  if (dataId.value) {
    setData(detailsData.value?.documentVOList ?? []);
  }
});

function saveChange(filesRes: any[]) {
  const files = filesRes.map((item: Record<string, any>) => {
    const fileRes = item.result;
    fileRes.id = `add${randomString()}`;
    fileRes.creatorName = userStore.getUserInfo?.name;
    fileRes.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    return item.result;
  });

  dataSource.value = [...unref(dataSource), ...files];
}

// 删除
function onButtonClick({ type, selectColumns }) {
  if (type === 'delete') {
    dataSource.value = dataSource.value.filter((item) => !selectColumns.keys.some((key: string) => item.id === key));
  }
}

async function deleteRecordById(id: string) {
  const index = unref(dataSource).findIndex((item) => item.id === id);
  unref(dataSource).splice(index, 1);
}

const tableOptions = {
  showSmallSearch: false,
  deleteToolButton: 'add|enable|disable',
  rowSelection: {},
  pagination: false,
  actions: [
    {
      text: '下载',
      onClick(record: Record<string, any>) {
        downLoadByFilePath({
          filePath: record.filePath,
          filePostfix: record.filePostfix,
          name: record.name,
        });
      },
    },
    {
      text: '删除',
      modal(record: Record<string, any>) {
        return deleteRecordById(record.id);
      },
    },
  ],
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
};

function getData() {
  let params = unref(dataSource);
  params = JSON.parse(JSON.stringify(params ?? []));

  params.forEach((item) => {
    if (item.id.indexOf('add') >= 0) {
      delete item.id;
    }
  });
  return params;
}

function setData(values: any[]) {
  dataSource.value = values;
}

defineExpose({
  getData,
});
</script>

<style scoped lang="less">

.files-wrap {
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>
