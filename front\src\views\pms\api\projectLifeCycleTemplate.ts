import Api from '/@/api';
/**
 * 新增
 * @param params
 */
export async function add(params) {
  return new Api('/pms/projectLifeCycleTemplate/add').fetch(params, '', 'POST');
}
/**
 * 复制
 * @param ids
 */
export async function copy(ids) {
  return new Api('/pms/projectLifeCycleTemplate/copy').fetch(ids, '', 'POST');
}
/**
 * 编辑
 * @param params
 */
export async function edit(params) {
  return new Api('/pms/projectLifeCycleTemplate/edit').fetch(params, '', 'PUT');
}

/**
 * 分页
 * @param params
 */
export async function page(params) {
  return new Api('/pms/projectLifeCycleTemplate/page').fetch(params, '', 'POST');
}
/**
 * 删除（批量）
 * @param ids
 */
export async function remove(ids) {
  return new Api('/pms/projectLifeCycleTemplate/remove').fetch(ids, '', 'DELETE');
}
/**
 * 启用禁用
 * @param params
 */
export async function takeEffectBatch(params) {
  return new Api('/pms/projectLifeCycleTemplate/takeEffectBatch').fetch(params, '', 'POST');
}
/**
 * 详情
 * @param id
 */
export async function projectLifeCycleTemplate(id) {
  return new Api(`/pms/projectLifeCycleTemplate/${id}`).fetch('', '', 'GET');
}
