{"compilerOptions": {"baseUrl": "./", "target": "esnext", "module": "esnext", "moduleResolution": "node", "sourceMap": true, "jsx": "preserve", "resolveJsonModule": true, "paths": {"/@/*": ["./src/*"], "/#/*": ["./types/*"]}, "types": ["node", "webpack-env"], "allowSyntheticDefaultImports": true}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist", "**/*.js"]}