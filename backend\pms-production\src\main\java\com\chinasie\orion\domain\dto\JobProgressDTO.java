package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobProgress DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
@ApiModel(value = "JobProgressDTO对象", description = "作业工作进展")
@Data
@ExcelIgnoreUnannotated
public class JobProgressDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @ExcelProperty(value = "作业id ", index = 0)
    private String jobId;

    /**
     * 工作日期
     */
    @ApiModelProperty(value = "工作日期")
    @ExcelProperty(value = "工作日期 ", index = 1)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date workDate;

    /**
     * 总体进展
     */
    @ApiModelProperty(value = "总体进展")
    @ExcelProperty(value = "总体进展 ", index = 2)
    private BigDecimal progressSchedule;

    /**
     * 工作进展
     */
    @ApiModelProperty(value = "工作进展")
    @ExcelProperty(value = "工作进展 ", index = 3)
    private String progressDetail;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目id")
    private String projectName;

    @ApiModelProperty(value = "大修组织id")
    private String repairOrgId;


}
