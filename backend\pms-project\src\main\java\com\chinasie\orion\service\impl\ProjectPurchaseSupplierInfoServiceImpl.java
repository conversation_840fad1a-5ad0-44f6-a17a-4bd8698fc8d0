package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ProjectPurchaseSupplierInfoDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseSupplierInfo;
import com.chinasie.orion.domain.vo.ProjectPurchaseSupplierInfoVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPurchaseSupplierInfoRepository;
import com.chinasie.orion.service.ProjectPurchaseSupplierInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ProjectPurchaseSupplierInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:11:33
 */
@Service
public class ProjectPurchaseSupplierInfoServiceImpl extends OrionBaseServiceImpl<ProjectPurchaseSupplierInfoRepository, ProjectPurchaseSupplierInfo>  implements ProjectPurchaseSupplierInfoService {

    @Autowired
    private ProjectPurchaseSupplierInfoRepository projectPurchaseSupplierInfoRepository;

    /**
     *  根据采购订单id获取详情
     *
     * * @param id
     */
    @Override
    public  ProjectPurchaseSupplierInfoVO getByPurchaseId(String purchaseId) throws Exception {
        ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo = projectPurchaseSupplierInfoRepository.selectOne(ProjectPurchaseSupplierInfo :: getPurchaseId, purchaseId);
        ProjectPurchaseSupplierInfoVO result = BeanCopyUtils.convertTo(projectPurchaseSupplierInfo, ProjectPurchaseSupplierInfoVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param projectPurchaseSupplierInfoDTO
     */
    @Override
    public  ProjectPurchaseSupplierInfoVO create(ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO) throws Exception {
        ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo =BeanCopyUtils.convertTo(projectPurchaseSupplierInfoDTO,ProjectPurchaseSupplierInfo::new);
        int insert = projectPurchaseSupplierInfoRepository.insert(projectPurchaseSupplierInfo);
        ProjectPurchaseSupplierInfoVO rsp = BeanCopyUtils.convertTo(projectPurchaseSupplierInfo,ProjectPurchaseSupplierInfoVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPurchaseSupplierInfoDTO
     */
    @Override
    public Boolean edit(ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO) throws Exception {
        ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo =BeanCopyUtils.convertTo(projectPurchaseSupplierInfoDTO,ProjectPurchaseSupplierInfo::new);
        int update =  projectPurchaseSupplierInfoRepository.updateById(projectPurchaseSupplierInfo);
        return SqlHelper.retBool(update);
    }
}
