<?xml version="1.0" encoding="UTF-8"?>

<!--
 ~ Copyright (c) 2010-2011 Sonatype, Inc.
 ~ All rights reserved. This program and the accompanying materials
 ~ are made available under the terms of the Eclipse Public License v1.0
 ~ which accompanies this distribution, and is available at
 ~   http://www.eclipse.org/legal/epl-v10.html
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu.inject</groupId>
    <artifactId>guice-plexus</artifactId>
    <version>2.3.0</version>
  </parent>

  <packaging>bundle</packaging>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-inject-plexus</artifactId>

  <name>Sisu-Inject-Plexus : Aggregate OSGi bundle</name>

  <dependencies>
    <!-- external dependencies -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-classworlds</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>org.sonatype.sisu</groupId>
      <artifactId>sisu-inject-bean</artifactId>
    </dependency>
    <!-- embedded dependencies -->
    <dependency>
      <groupId>org.sonatype.sisu.inject</groupId>
      <artifactId>guice-plexus-shim</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- optional dependencies -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <excludeDependencies>
            <!-- external dependencies -->
            *;groupId=org.codehaus.plexus
          </excludeDependencies>
          <instructions>
            <Bundle-SymbolicName>
              org.sonatype.inject.plexus;singleton:=true
            </Bundle-SymbolicName>
            <DynamicImport-Package>
              org.slf4j
            </DynamicImport-Package>
            <Export-Package>
              <!-- our Plexus shim API -->
              org.codehaus.plexus.*;-split-package:=merge-first,
              <!-- our implementation APIs -->
              org.sonatype.guice.plexus.*;x-internal:=true;version=${project.version}
            </Export-Package>
            <Require-Bundle>
              org.sonatype.sisu.guava,
              org.sonatype.sisu.guice,
              org.sonatype.inject
            </Require-Bundle>
            <Import-Package>
              <!-- Plexus APIs -->
              org.codehaus.*,
              <!-- available via Require-Bundle -->
              !com.google.*,!org.sonatype.*,
              <!-- optional generated imports -->
              *;resolution:=optional
            </Import-Package>
            <Private-Package>
              META-INF.plexus
            </Private-Package>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <property>
          <name>aggregate-sources</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <execution>
                <id>aggregate-sources</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <classifier>sources</classifier>
                  <failOnMissingClassifierArtifact>false</failOnMissingClassifierArtifact>
                  <outputDirectory>${project.build.directory}/aggregate-sources</outputDirectory>
                  <excludeGroupIds>org.codehaus.plexus</excludeGroupIds>
                  <includes>
                    org/codehaus/plexus/**,org/sonatype/guice/plexus/**
                  </includes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classifier>sources</classifier>
                  <classesDirectory>${project.build.directory}/aggregate-sources</classesDirectory>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-javadoc-plugin</artifactId>
              <configuration>
                <sourcepath>${project.build.directory}/aggregate-sources</sourcepath>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <!--
       | m2e profile - expose embedded dependencies
      -->
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <dependencies>
        <dependency>
          <groupId>org.sonatype.sisu.inject</groupId>
          <artifactId>guice-plexus-shim</artifactId>
          <optional>false</optional>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

</project>
