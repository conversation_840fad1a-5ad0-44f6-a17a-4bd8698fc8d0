package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BillingAccountInformation VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:13:07
 */
@ApiModel(value = "BillingAccountInformationVO对象", description = "开票核算信息表")
@Data
public class BillingAccountInformationVO extends ObjectVO implements Serializable {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;


    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 项目责任人Id
     */
    @ApiModelProperty(value = "项目责任人Id")
    private String projectRspUserId;

    @ApiModelProperty(value = "项目责任人Id名称")
    private String projectRspUserName;


    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectType;


    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualAcceptanceAmt;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;


    /**
     * 价税合计金额
     */
    @ApiModelProperty(value = "价税合计金额")
    private BigDecimal totalAmtTax;


    /**
     * 税额（增值税）
     */
    @ApiModelProperty(value = "税额（增值税）")
    private BigDecimal vatAmt;


    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtExTax;


    /**
     * 收入计划填报ID
     */
    @ApiModelProperty(value = "收入计划填报ID")
    private String incomePlanId;


    /**
     * 收入填报数据Id
     */
    @ApiModelProperty(value = "收入填报数据Id")
    private String incomePlanDataId;

    @ApiModelProperty(value = "收入wbs编码")
    private String incomeWbsNumber;
}
