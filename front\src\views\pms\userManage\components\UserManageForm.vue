<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';
import { disabledEndDate, disabledStartDate } from '/@/views/pms/utils/utils';

const props = defineProps<{
  record: any
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '人员基础信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'nature',
    component: 'Input',
    label: '人员性质',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '员工号',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '姓名',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'sex',
    component: 'Input',
    label: '性别',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'companyName',
    component: 'Input',
    label: '公司',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '部门',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'instituteName',
    component: 'Input',
    label: '研究所',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'idCard',
    component: 'Input',
    label: '身份证号',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'nowPosition',
    component: 'Input',
    label: '现任职务',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'politicalAffiliation',
    component: 'Input',
    label: '政治面貌',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '人员管理信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'contactDeptName',
    component: 'Input',
    label: '接口部门',
    required: true,
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'contactOfficeName',
    component: 'Input',
    label: '接口科室',
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'contactUserName',
    component: 'Input',
    label: '接口人',
    required: true,
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'basePlaceProject',
    component: 'SelectProject',
    label: '基地内承担的主要项目',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
  },
  {
    field: 'workType',
    component: 'SelectDictVal',
    label: '大修/日常',
    required: true,
    componentProps: {
      dictNumber: 'pms_technical_application',
    },
  },
  {
    field: 'repairRound',
    component: 'ApiSelect',
    label: '大修轮次',
    componentProps: {
      api: () => new Api('/pms/major-repair-plan/list').fetch('', '', 'POST'),
      labelField: 'repairRound',
      valueField: 'repairRound',
    },
  },
  {
    field: 'heightStr',
    component: 'Input',
    label: '身高/米',
    required: true,
    componentProps: {
      maxlength: 200,
      // min: 0,
      // max: 300,
      // precision: 0,
    },
  },
  {
    field: 'weightStr',
    component: 'Input',
    label: '体重/千克',
    required: true,
    componentProps: {
      maxlength: 200,
      // min: 0,
      // max: 500,
      // precision: 0,
    },
  },
  {
    field: 'jobTaboos',
    component: 'Select',
    label: '职业禁忌症',
    required: true,
    componentProps: {
      options: [
        {
          label: '有',
          value: '有',
        },
        {
          label: '无',
          value: '无',
        },
      ],
    },
  },
  {
    field: 'jobTaboosName',
    component: 'Input',
    label: '职业禁忌症名称',
    ifShow({ model }) {
      return model.jobTaboos === '有';
    },
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'designCtrlZoneOp',
    component: 'Select',
    label: '涉及控制区作业',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'chemicalToxinUseJob',
    component: 'Select',
    label: '化学品/毒物使用或接触作业',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'workResPerson',
    component: 'Select',
    label: '工作负责人',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'preparationEngineer',
    component: 'Select',
    label: '准备工程师',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'qcStr',
    component: 'Select',
    label: 'QC',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'qcWorkYear',
    component: 'InputNumber',
    label: 'QC工作年限',
    ifShow({ model }) {
      return model?.qcStr;
    },
    componentProps: {
      min: 0,
      max: 99999,
      precision: 0,
    },
  },
  {
    field: 'fuTiSafOff',
    component: 'Select',
    label: '专职安全员',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'paTiSafOff',
    component: 'Select',
    label: '兼职安全员',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    ifShow({ model }) {
      return model?.fuTiSafOff === false;
    },
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'speTaskCertSit',
    component: 'SelectDictVal',
    label: '特种作业持证情况（含无损检测资质）',
    required: true,
    componentProps: {
      dictNumber: 'pms_speciali_op',
    },
  },
  {
    field: 'isJoinYearMajorRepair',
    component: 'Select',
    label: '一年内参与过集团内大修',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'isHeightMeasurePerson',
    component: 'Select',
    label: '高剂量人员(年个人剂量>8mSv为高剂量人员)',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'newcomer',
    component: 'Select',
    label: '新人',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'newcomerType',
    component: 'Input',
    label: '新人类型',
    ifShow({ model }) {
      return model?.newcomer;
    },
    componentProps: {
      maxlength: 200,
    },
  },
  {
    field: 'newcomerMatchPerson',
    component: 'SelectUser',
    label: '新人对口人',
    ifShow({ model }) {
      return model?.newcomer;
    },
    componentProps: {
      selectUserModalProps: {
        selectType: 'radio',
      },
    },
  },
  // {
  //   field: 'authorizationStatus',
  //   component: 'Input',
  //   label: '授权状态',
  //   defaultValue: '未授权',
  //   componentProps: {
  //     disabled: true,
  //     placeholder: ' ',
  //   },
  // },
  {
    field: 'isBasePermanent',
    component: 'Select',
    label: '是否基地常驻',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'inDate',
    component: 'DatePicker',
    label: '计划入场时间',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (startDate) => disabledStartDate(startDate, formModel.outDate),
      };
    },
  },
  {
    field: 'outDate',
    component: 'DatePicker',
    label: '计划离场时间',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (endDate) => disabledEndDate(endDate, formModel.inDate),
      };
    },
  },
  {
    field: 'actInDate',
    component: 'DatePicker',
    label: '实际入场时间',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (startDate) => disabledStartDate(startDate, formModel.actOutDate),
      };
    },
  },
  {
    field: 'actOutDate',
    component: 'DatePicker',
    label: '实际离场时间',
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (endDate) => disabledEndDate(endDate, formModel.actInDate),
      };
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/person-mange').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      basePlaceProject: result?.basePlaceProjectName ? [
        {
          id: result?.basePlaceProject,
          name: result?.basePlaceProjectName,
        },
      ] : [],
      newcomerMatchPerson: result?.newcomerMatchPersonCodeName ? [
        {
          id: result?.newcomerMatchPerson,
          name: result?.newcomerMatchPersonCodeName,
        },
      ] : [],
    });
    await clearValidate();
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      id: props?.record?.id,
      newcomerMatchPerson: formValues?.newcomerMatchPerson?.[0]?.id,
      basePlaceProject: formValues?.basePlaceProject?.[0]?.id,
      basePlaceProjectName: formValues?.basePlaceProject?.[0]?.name,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/person-mange').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
