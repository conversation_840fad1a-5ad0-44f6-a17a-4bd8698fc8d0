package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.dict.ProjectEvaluationDict;
import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.entity.EvaluationDetail;
import com.chinasie.orion.domain.entity.EvaluationProject;
import com.chinasie.orion.domain.entity.ExponseDetail;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.EvaluationProjectVO;
import com.chinasie.orion.domain.vo.EvaluationTypeVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.EvaluationDetailMapper;
import com.chinasie.orion.repository.EvaluationProjectMapper;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.EvaluationProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * EvaluationProject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@Service
public class EvaluationProjectServiceImpl extends OrionBaseServiceImpl<EvaluationProjectMapper, EvaluationProject> implements EvaluationProjectService {

    @Resource
    private EvaluationProjectMapper evaluationProjectMapper;

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private UserBaseApiService userBaseApiService;

    @Resource
    private ProjectRepository projectRepository;

    @Resource
    private CodeBo codeBo;


    /**
     * 新增
     * <p>
     * * @param evaluationProjectDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public EvaluationProjectVO create(EvaluationProjectDTO evaluationProjectDTO) throws Exception {
        EvaluationProject evaluationProject = BeanCopyUtils.convertTo(evaluationProjectDTO, EvaluationProject::new);
        List<EvaluationDetail> evaluationDetails = new ArrayList<>();

        if (ProjectEvaluationDict.SELF_EVALUATION.equals(evaluationProject.getEvaluationType())) {
            // 生成编码格式不同
            String code = codeBo.createCode(ProjectEvaluationDict.SELF_EVALUATION, ClassNameConstant.NUMBER, false, null);
            if (BeanUtil.isNotEmpty(code)) {
                evaluationProject.setNumber(code);
            }
            evaluationDetails = this.insertCommonDetail(evaluationDetails, evaluationProject);

        } else if (ProjectEvaluationDict.AFTER_EVALUATION.equals(evaluationProject.getEvaluationType())) {
            // 生成编码不同
            String code = codeBo.createCode(ProjectEvaluationDict.AFTER_EVALUATION, ClassNameConstant.NUMBER, false, null);
            if (BeanUtil.isNotEmpty(code)) {
                evaluationProject.setNumber(code);
            }
            evaluationDetails = this.insertCommonDetail(evaluationDetails, evaluationProject);
            evaluationDetails = this.insertAfterDetail(evaluationDetails, evaluationProject);
        } else if (ProjectEvaluationDict.PERFORMANCE_EVALUATION.equals(evaluationProject.getEvaluationType())) {
            // 生成编码不同
            String code = codeBo.createCode(ProjectEvaluationDict.PERFORMANCE_EVALUATION, ClassNameConstant.NUMBER, false, null);
            if (BeanUtil.isNotEmpty(code)) {
                evaluationProject.setNumber(code);
            }
            evaluationDetails = this.insertCommonDetail(evaluationDetails, evaluationProject);
            evaluationDetails = this.insertPerformanceDetail(evaluationDetails, evaluationProject);
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前评价类型不存在，请仔细检查");
        }
        int insert = evaluationProjectMapper.insert(evaluationProject);
        evaluationDetails.forEach(e -> e.setEvaluationId(evaluationProject.getId()));
        evaluationDetailMapper.insertBatch(evaluationDetails);
        EvaluationProjectVO rsp = BeanCopyUtils.convertTo(evaluationProject, EvaluationProjectVO::new);
        return rsp;
    }

    private List<EvaluationDetail> insertPerformanceDetail(List<EvaluationDetail> evaluationDetails, EvaluationProject evaluationProject) {
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(ProjectEvaluationDict.PERFORMANCE_EVALUATION_DETAIL);
        if (!CollectionUtils.isEmpty(dictListByCode)) {
            dictListByCode.forEach(e -> {
                EvaluationDetail evaluationDetail = new EvaluationDetail();
                evaluationDetail.setScore(0);
                evaluationDetail.setEvaluationDetailType(e.getNumber());
                evaluationDetail.setSort(Long.valueOf(e.getSort()));
                evaluationDetail.setProjectId(evaluationProject.getProjectId());
                evaluationDetail.setName(e.getDescription());
                evaluationDetails.add(evaluationDetail);
            });
        }
        return evaluationDetails;
    }

    private List<EvaluationDetail> insertAfterDetail(List<EvaluationDetail> evaluationDetails, EvaluationProject evaluationProject) {
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(ProjectEvaluationDict.AFTER_EVALUATION_DETAIL);
        if (!CollectionUtils.isEmpty(dictListByCode)) {
            dictListByCode.forEach(e -> {
                EvaluationDetail evaluationDetail = new EvaluationDetail();
                evaluationDetail.setScore(0);
                evaluationDetail.setEvaluationDetailType(e.getNumber());
                evaluationDetail.setSort(Long.valueOf(e.getSort()));
                evaluationDetail.setProjectId(evaluationProject.getProjectId());
                evaluationDetail.setName(e.getDescription());
                evaluationDetails.add(evaluationDetail);
            });
        }
        return evaluationDetails;
    }

    private List<EvaluationDetail> insertCommonDetail(List<EvaluationDetail> evaluationDetails, EvaluationProject evaluationProject) {
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(ProjectEvaluationDict.COMMON_EVALUATION_DETAIL);
        if (!CollectionUtils.isEmpty(dictListByCode)) {
            dictListByCode.forEach(e -> {
                EvaluationDetail evaluationDetail = new EvaluationDetail();
                evaluationDetail.setScore(0);
                evaluationDetail.setEvaluationDetailType(e.getNumber());
                evaluationDetail.setSort(Long.valueOf(e.getSort()));
                evaluationDetail.setProjectId(evaluationProject.getProjectId());
                evaluationDetail.setName(e.getDescription());
                evaluationDetails.add(evaluationDetail);
            });
        }
        return evaluationDetails;
    }

    /**
     * 编辑
     * <p>
     * * @param evaluationProjectDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(EvaluationProjectDTO evaluationProjectDTO) throws Exception {
        EvaluationProject evaluationProject = BeanCopyUtils.convertTo(evaluationProjectDTO, EvaluationProject::new);
        EvaluationProject oldEvaluation = getById(evaluationProjectDTO.getId());
        if (BeanUtil.isEmpty(oldEvaluation)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在，请仔细检查");
        }
        if (!oldEvaluation.getEvaluationType().equals(evaluationProjectDTO.getEvaluationType())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目类型不支持修改");
        }
        int update = evaluationProjectMapper.updateById(evaluationProject);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {

        List<EvaluationDetail> evaluationDetails = evaluationDetailMapper.selectList(new LambdaQueryWrapperX<EvaluationDetail>().in(EvaluationDetail::getEvaluationId, ids));
        if (!CollectionUtils.isEmpty(evaluationDetails)) {
            List<String> idList = evaluationDetails.stream().map(EvaluationDetail::getId).collect(Collectors.toList());
            evaluationDetailMapper.deleteBatchIds(idList);
        }
        int delete = evaluationProjectMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public PageResult<EvaluationProjectVO> getProjectEvaluationPage(Page<EvaluationProjectDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<EvaluationProject> pageCondition = new LambdaQueryWrapperX<>(EvaluationProject.class);
        EvaluationProjectDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(ExponseDetail::getProjectId, projectId);
        }
        pageCondition.orderByDesc(EvaluationProject::getEvaluationTime);

        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, pageCondition);
        }
        Page<EvaluationProject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<EvaluationProject> pageResult = evaluationProjectMapper.selectPage(realPageRequest, pageCondition);


        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getContent())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }

        List<EvaluationProject> records = pageResult.getContent();

        String pm = "";
        Project project = projectRepository.selectById(projectId);
        if (Objects.nonNull(project)) {
            SimpleUserVO user = userBaseApiService.getUserById(project.getResPerson());
            if (Objects.nonNull(user)) {
                pm = user.getName();
            } else {
                pm = project.getPm();
            }

        }
        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(ProjectEvaluationDict.PROJECT_EVALUATE);

        List<String> userIds = records.stream().map(EvaluationProject::getEvaluationPersonId).distinct().collect(Collectors.toList());
        List<SimpleUserVO> users = userBaseApiService.getUserByIds(userIds);
        HashMap<String, SimpleUserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)) {
            Map<String, SimpleUserVO> collect = users.stream().collect(Collectors.toMap(SimpleUserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        List<EvaluationProjectVO> evaluationProjectVOs = BeanCopyUtils.convertListTo(records, EvaluationProjectVO::new);
        String finalPm = pm;
        evaluationProjectVOs.forEach(e -> {
            DictValueVO dictValueVO = dictMapByCode.getOrDefault(e.getEvaluationType(), new DictValueVO());
            SimpleUserVO evaluationPerson = userMap.getOrDefault(e.getEvaluationPersonId(), new SimpleUserVO());
//            UserVO manager = userMap.getOrDefault(e.getProjectManagerId(), new UserVO());
            e.setEvaluationTypeName(dictValueVO.getDescription());
            e.setEvaluationPersonName(evaluationPerson.getName());
            e.setProjectManagerName(finalPm);
        });
        return new PageResult<>(evaluationProjectVOs, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotalSize());
    }

    @Override
    public List<EvaluationTypeVO> getEvaluationType(String projectId) {
        List<DictValueVO> dictList = dictRedisHelper.getDictListByCode(ProjectEvaluationDict.PROJECT_EVALUATE);
        if (CollectionUtils.isEmpty(dictList)) {
            return null;
        }
        List<EvaluationTypeVO> evaluationTypeVOS = new ArrayList<>(dictList.size());
        dictList.forEach(e -> {
            EvaluationTypeVO evaluationTypeVO = new EvaluationTypeVO();
            evaluationTypeVO.setEvaluationType(e.getNumber());
            evaluationTypeVO.setEvaluationTypeName(e.getDescription());
            evaluationTypeVOS.add(evaluationTypeVO);
        });
        return evaluationTypeVOS;
    }

    @Override
    public EvaluationProjectVO getByDataId(String id) throws Exception {
        EvaluationProject evaluationProject = getById(id);
        if (BeanUtil.isNotEmpty(evaluationProject)) {
            EvaluationProjectVO evaluationProjectVO = BeanCopyUtils.convertTo(evaluationProject, EvaluationProjectVO::new);
            String pm = "";
            List<Project> projects = projectRepository.selectList(new LambdaQueryWrapperX<Project>()
                    .select(Project::getPm, Project::getProjectStartTime, Project::getProjectEndTime)
                    .eq(Project::getId, evaluationProject.getProjectId()));
            if (!CollectionUtils.isEmpty(projects)) {
                pm = projects.get(0).getPm();
                Date projectStartTime = projects.get(0).getProjectStartTime();
                Date projectEndTime = projects.get(0).getProjectEndTime();
                evaluationProjectVO.setProjectStartTime(projectStartTime);
                evaluationProjectVO.setProjectEndTime(projectEndTime);

            }
            Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(ProjectEvaluationDict.PROJECT_EVALUATE);

            DictValueVO dictValueVO = dictMapByCode.getOrDefault(evaluationProjectVO.getEvaluationType(), new DictValueVO());
            SimpleUserVO evaluationPerson = userBaseApiService.getUserById(evaluationProjectVO.getEvaluationPersonId());
            if(Objects.isNull(evaluationPerson)){
                evaluationPerson=new SimpleUserVO();
            }
            SimpleUserVO creator = userBaseApiService.getUserById(evaluationProjectVO.getCreatorId());
            if(Objects.isNull(creator)){
                creator=new SimpleUserVO();
            }
            evaluationProjectVO.setEvaluationTypeName(dictValueVO.getDescription());
            evaluationProjectVO.setEvaluationPersonName(evaluationPerson.getName());
            evaluationProjectVO.setCreatorName(creator.getName());
            evaluationProjectVO.setProjectManagerName(pm);
            return evaluationProjectVO;
        }


        return null;
    }


}
