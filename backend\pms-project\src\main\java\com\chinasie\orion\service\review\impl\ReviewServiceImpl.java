package com.chinasie.orion.service.review.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.vo.UserDeptVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.review.ReviewBaseDTO;
import com.chinasie.orion.domain.dto.review.ReviewDTO;
import com.chinasie.orion.domain.dto.review.ReviewDeliveryDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.vo.review.IdAndNameVO;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.repository.review.ReviewMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DeliverableService;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.review.ReviewService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * Review 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@Service
@Slf4j
public class ReviewServiceImpl extends OrionBaseServiceImpl<ReviewMapper, Review> implements ReviewService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DeliverableService deliverableService;
    @Autowired
    private DeliverableRepository deliverableRepository;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private FileInfoRepository fileInfoRepository;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private ProjectSchemeRepository schemeRepository;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private ProjectRoleUserRepository projectRoleUserRepository;
    @Autowired
    private DeptBaseApiService deptBaseApiService;
    @Autowired
    private RoleUserHelper roleUserHelper;
    @Autowired
    private RoleRedisHelper roleRedisHelper;
    @Autowired
    private ProjectRoleService projectRoleService;
    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    private static final String PMS_MANAGEMENT_ATTACHE = "CY_PME_P";
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReviewVO detail(String id, String pageCode) throws Exception {
        Review review = this.getById(id);
        ReviewVO result = BeanCopyUtils.convertTo(review, ReviewVO::new);
        setEveryName(Collections.singletonList(result));
        setDetailName(result);

        return result;
    }


    /**
     * 新增
     * <p>
     * * @param reviewDTO
     */
    @Override
    public String create(ReviewDTO reviewDTO) throws Exception {
        if (StrUtil.isBlank(reviewDTO.getPlanId())) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空！");
        }
        if (StrUtil.isBlank(reviewDTO.getProjectId())) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空！");
        }
        Review review = BeanCopyUtils.convertTo(reviewDTO, Review::new);

        String deliverableId = creatDeliverable(reviewDTO);
        if (!CollectionUtil.isEmpty(reviewDTO.getFiles())) {
            creatDocument(reviewDTO.getFiles(), deliverableId, reviewDTO.getProjectId());
        }
        review.setDeliverId(deliverableId);
        review.setNumber(getNumber());

        this.save(review);
        String rsp = review.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param reviewDTO
     */
    @Override
    public Boolean edit(ReviewDTO reviewDTO) throws Exception {
        Review review = BeanCopyUtils.convertTo(reviewDTO, Review::new);
        Review byId = this.getById(reviewDTO.getId());
        reviewDTO.setProjectId(byId.getProjectId());
        reviewDTO.setPlanId(byId.getPlanId());
        reviewDTO.setDeliverId(byId.getDeliverId());
        updateDeliverable(reviewDTO);
        updateDocument(reviewDTO.getFiles(),review.getDeliverId(),byId.getProjectId());
        this.updateById(review);

        String rsp = review.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReviewVO> pages(Page<ReviewDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<Review> condition = new LambdaQueryWrapperX<>(Review.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        ReviewDTO query = pageRequest.getQuery();
        if (!ObjectUtil.isNull(query)) {
            if (StrUtil.isNotBlank(query.getName())) {
                condition.like(Review::getName, query.getName());
            }
            condition.eq(Review::getProjectId,query.getProjectId());
        }
        condition.orderByDesc(Review::getCreateTime);

        Page<Review> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), Review::new));

        PageResult<Review> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReviewVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReviewVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReviewVO::new);
        if (!CollectionUtil.isEmpty(vos)){
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<ReviewVO> vos) throws Exception {
        List<String> planIds = vos.stream().map(ReviewVO::getPlanId).distinct().collect(Collectors.toList());
        List<ProjectScheme> plans = schemeRepository.selectBatchIds(planIds);
        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(DictConstant.PMS_REVIEW_TYPE);
        vos.forEach(vo -> {
            if (!CollectionUtil.isEmpty(plans)){
                Map<String, String> map = plans.stream().collect(Collectors.toMap(ProjectScheme::getId, ProjectScheme::getName));
                String name = map.get(vo.getPlanId());
                if (StrUtil.isNotBlank(name)){
                    vo.setPlanName(name);
                }
            }
            DictValueVO dictValueVO = dictMapByCode.get(vo.getReviewType());
            if (ObjectUtil.isNotNull(dictValueVO)){
                vo.setReviewTypeName(dictValueVO.getName());
            }
        });
    }

    @Override
    public IdAndNameVO manageUser(String projectId) throws Exception {
        if (StrUtil.isBlank(projectId)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL.getErrorCode(), "项目id不能为空！");
        }
        RoleVO role = roleRedisHelper.getRole(PMS_MANAGEMENT_ATTACHE, CurrentUserHelper.getOrgId());
        if (ObjectUtil.isNull(role)){
            return new IdAndNameVO();
        }
        LambdaQueryWrapperX<ProjectRole> queryWrapperX = new LambdaQueryWrapperX<>(ProjectRole.class);
        queryWrapperX.eq(ProjectRole::getProjectId,projectId)
                .eq(ProjectRole::getBusinessId,role.getId());
        List<ProjectRole> roles = projectRoleService.list(queryWrapperX);
        if (CollectionUtil.isEmpty(roles)){
            return new IdAndNameVO();
        }
        ProjectRole projectRole = roles.get(0);
        LambdaQueryWrapperX<ProjectRoleUser> wrapperX = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        wrapperX.eq(ProjectRoleUser::getProjectRoleId,projectRole.getId());
        List<ProjectRoleUser> roleUsers = projectRoleUserService.list(wrapperX);
        if (!CollectionUtil.isEmpty(roleUsers)){
            List<String> userIds = roleUsers.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
            List<SimpleUser> simpleUserByIds = userRedisHelper.getSimpleUserByIds(userIds);
            if (!CollectionUtil.isEmpty(simpleUserByIds)){
                IdAndNameVO idAndNameVO = new IdAndNameVO();
                SimpleUser user = simpleUserByIds.get(0);
                idAndNameVO.setId(user.getId());
                idAndNameVO.setName(user.getName());
                return idAndNameVO;
            }
        }
        return null;
    }

    @Override
    public Boolean baseEdit(ReviewBaseDTO reviewBaseDTO) {
        Review review = this.getById(reviewBaseDTO.getId());
        if (ObjectUtil.isNull(review)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目评审单不存在！");
        }
        review.setReviewAddress(reviewBaseDTO.getReviewAddress());
        review.setReviewTime(reviewBaseDTO.getReviewTime());
        review.setExamineState(reviewBaseDTO.getExamineState());
        boolean b = this.updateById(review);
        return b;
    }

    @Override
    public Boolean deliverableEdit(ReviewDeliveryDTO dto) {
        Review review = this.getById(dto.getId());
        String deliverId = review.getDeliverId();
        Deliverable deliverable = deliverableService.getById(deliverId);
        if (ObjectUtil.isNull(deliverable)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS,"交付物数据不存在！");
        }
        review.setMeetingNo(dto.getMeetingNo());
        review.setQuestionFileNo(dto.getQuestionFileNo());
        review.setReviewFileNo(dto.getReviewFileNo());
        review.setPlmNo(dto.getPlmNo());
        this.updateById(review);
        LambdaUpdateWrapper<Deliverable> updateWrapper = new LambdaUpdateWrapper<>(Deliverable.class);
        updateWrapper.set(Deliverable::getName,dto.getDeliverName())
                        .set(Deliverable::getDeliveryTime,dto.getDeliveryTime())
                                .set(Deliverable::getPrincipalId,dto.getPrincipalId())
                                        .eq(Deliverable::getId,deliverId);
//        deliverable.setName(dto.getDeliverName());
//        deliverable.setDeliveryTime(dto.getDeliveryTime());
//        deliverable.setPrincipalId(dto.getPrincipalId());
        deliverableRepository.update(updateWrapper);
        return true;
    }

    @Override
    public ReviewVO deliverableQuery(String id) {
        Review review = this.getById(id);
        ReviewVO reviewVO = BeanCopyUtils.convertTo(review, ReviewVO::new);
        Deliverable deliverable = deliverableService.getById(reviewVO.getDeliverId());
        reviewVO.setDeliveryTime(deliverable.getDeliveryTime());
        reviewVO.setDeliverName(deliverable.getName());
        String principalId = deliverable.getPrincipalId();
        if (StrUtil.isNotBlank(principalId)){
            reviewVO.setPrincipalId(principalId);
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(principalId);
            reviewVO.setPrincipalName(simpleUserById.getName());
        }
        return reviewVO;
    }


    public static class ReviewExcelListener extends AnalysisEventListener<ReviewDTO> {

        private final List<ReviewDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReviewDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReviewDTO> getData() {
            return data;
        }
    }


    private String creatDeliverable(ReviewDTO reviewDTO) throws Exception {
        DeliverableDTO deliverableDTO = new DeliverableDTO();
        deliverableDTO.setPlanId(reviewDTO.getPlanId());
        deliverableDTO.setProjectId(reviewDTO.getProjectId());
        deliverableDTO.setPrincipalId(CurrentUserHelper.getCurrentUserId());
        deliverableDTO.setName(reviewDTO.getDeliverName());
        String deliverableId = deliverableService.saveDeliver(deliverableDTO);
        return deliverableId;
    }

    private void updateDeliverable(ReviewDTO reviewDTO){
        Deliverable deliverable = deliverableService.getById(reviewDTO.getDeliverId());
        deliverable.setName(reviewDTO.getDeliverName());
        deliverableService.updateById(deliverable);
    };

    private void creatDocument(List<FileInfoDTO> fileInfoDTOS, String deliverableId, String projectId) throws Exception {
        if(!CollectionUtil.isEmpty(fileInfoDTOS)){
            fileInfoDTOS.forEach(fileInfoDTO -> {
                fileInfoDTO.setProjectId(projectId);
                fileInfoDTO.setDataId(deliverableId);
            });
            documentService.saveBatchAdd(fileInfoDTOS);
        }
    }

    private void updateDocument(List<FileInfoDTO> fileInfoDTOS, String deliverableId, String projectId) throws Exception {
        LambdaQueryWrapperX<FileInfo> wrapperX = new LambdaQueryWrapperX<>(FileInfo.class);
        wrapperX.eq(FileInfo::getDataId,deliverableId);
        List<FileInfo> fileInfos = fileInfoRepository.selectList(wrapperX);
        if(!CollectionUtil.isEmpty(fileInfos) && CollectionUtil.isEmpty(fileInfoDTOS)){
            List<String> deletedIds = fileInfos.stream().map(FileInfo::getId).collect(Collectors.toList());
            fileInfoRepository.deleteBatchIds(deletedIds);
            return;
        }
        if(CollectionUtil.isEmpty(fileInfoDTOS)){
            return;
        }

        List<FileInfoDTO> having = fileInfoDTOS.stream().filter(fileInfoDTO -> StrUtil.isNotBlank(fileInfoDTO.getId())).collect(Collectors.toList());
        List<FileInfoDTO> save = fileInfoDTOS.stream().filter(fileInfoDTO -> StrUtil.isBlank(fileInfoDTO.getId())).collect(Collectors.toList());
        //过滤需要删除的数据
        if (!CollectionUtil.isEmpty(having)){
            String idStr = having.stream().map(FileInfoDTO::getId).collect(Collectors.joining(","));
            List<String> deletedIds = new ArrayList<>();
            fileInfos.forEach(fileInfo -> {
                if (!idStr.contains(fileInfo.getId())){
                    deletedIds.add(fileInfo.getId());
                }
            });
            if (!CollectionUtil.isEmpty(deletedIds)){
                fileInfoRepository.deleteBatchIds(deletedIds);
            }
        }
        if (!CollectionUtil.isEmpty(save)){
            creatDocument(save, deliverableId , projectId);
        }
    }

    private String getNumber() throws Exception {
//        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
//        generateNumberRequest.setClazzName(ClassNameConstant.PMS_REVIEW);
//        generateNumberRequest.setEffectFlag(Boolean.TRUE);
//        String generate = numberApiService.generate(generateNumberRequest);
        String code = codeBo.createCode(ClassNameConstant.PMS_REVIEW, ClassNameConstant.NUMBER, false, "");
        if (StrUtil.isNotBlank(code)){
            return code;
        }
        Date date = new Date();
        return String.format("PR-%s",date.getTime());
    }

    private void setDetailName(ReviewVO result) throws Exception {
        Project project = projectRepository.selectById(result.getProjectId());
        Deliverable deliverable = deliverableService.getById(result.getDeliverId());
        List<UserDeptVO> deptByUserId = deptBaseApiService.getDeptByUserId(result.getCreatorId());
        if (ObjectUtil.isNotNull(project)){
            result.setProjectName(project.getName());
            result.setProjectNumber(project.getNumber());
            result.setPm(project.getPm());
            result.setUserListStr(getProjectUserListName(project.getId()));
        }
        if (ObjectUtil.isNotNull(deliverable)){
            result.setDeliverName(deliverable.getName());
        }
        if (!CollectionUtil.isEmpty(deptByUserId)){
            result.setDept(deptByUserId.get(0).getDeptName());
        }
        if (StrUtil.isNotBlank(result.getManageUser())){
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(result.getManageUser());
            result.setManageUserName(simpleUserById.getName());
        }
        List<FileInfoDTO> fileInfoList = documentService.getFileInfoList(deliverable.getId());
        result.setFiles(fileInfoList);
    }

    private String getProjectUserListName(String projectId){
        LambdaQueryWrapperX<ProjectRoleUser> wrapperX = new LambdaQueryWrapperX<>(ProjectRoleUser.class);
        wrapperX.eq(ProjectRoleUser::getProjectId,projectId);
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserRepository.selectList(wrapperX);
        if (CollectionUtil.isEmpty(projectRoleUsers)){
            return "";
        }
        List<String> ids = projectRoleUsers.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
        List<UserVO> userByIds = userRedisHelper.getUserByIds(ids);
        String nameListStr = userByIds.stream().map(UserVO::getName).collect(Collectors.joining("、"));
        return nameListStr;
    }
}
