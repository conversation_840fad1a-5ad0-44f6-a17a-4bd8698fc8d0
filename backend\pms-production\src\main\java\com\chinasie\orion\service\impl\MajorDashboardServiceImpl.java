package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.chinasie.orion.constant.JobManageBusStatusEnum;
import com.chinasie.orion.constant.JobStatusEnum;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;
import com.chinasie.orion.domain.vo.MaterialManageVO;
import com.chinasie.orion.domain.vo.major.*;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.repository.PersonManageLedgerMapper;
import com.chinasie.orion.repository.PersonMangeMapper;
import com.chinasie.orion.repository.SchemeToPersonMapper;
import com.chinasie.orion.service.*;
import de.danielbechler.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/18:52
 * @description:
 */
@Slf4j
@Service
public class MajorDashboardServiceImpl implements MajorDashboardService {

    @Resource
    private JobManageMapper jobManageMapper;

    @Resource
    private JobHeightRiskService heightRiskService;


    @Resource
    private PersonMangeService personMangeService;

    @Resource
    private MajorRepairPlanService majorRepairPlanService;

    @Resource
    private JobPostAuthorizeService jobPostAuthorizeService;

    @Resource
    private BasicUserService basicUserService;

    @Resource
    private PersonMangeMapper personMangeMapper;

    @Resource
    private PersonManageLedgerMapper personManageLedgerMapper;

    @Resource
    private MaterialManageService materialManageService;

    @Resource
    private JobMaterialService jobMaterialService;

    @Resource
    private SchemeToMaterialService schemeToMaterialService;

    @Resource
    private SchemeToPersonMapper schemeToPersonMapper;


    @Override
    public JobDashBoardVO jobDashboardCount(String repairRound) {
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.eq(JobManage::getRepairRound,repairRound);
//        wrapperX.and(item->{
//            item.isNotNull(JobManage::getProjectNumber).ne(JobManage::getProjectNumber,"");
//        });
//        wrapperX.and(item->{
//            item.isNotNull(JobManage::getPlanSchemeId).ne(JobManage::getPlanSchemeId,"");
//        });
//        wrapperX.and(item->{
//            item.isNotNull(JobManage::getNOrO).ne(JobManage::getNOrO,"");
//        });
        wrapperX.eq(JobManage::getMatchUp,StatusEnum.ENABLE.getIndex());
        wrapperX.select(JobManage::getIsMajorProject,JobManage::getNumber,JobManage::getPhase,JobManage::getStatus);
        List<JobManage> jobManageList =jobManageMapper.selectList(wrapperX);
        if(Collections.isEmpty(jobManageList)){
            return JobDashBoardVO.builder().importantJobCountVO(new ImportantJobCountVO())
                    .jobRiskCountVO(new JobRiskCountVO()).build();
        }

        // 准备完成数
        AtomicInteger parepCount= new AtomicInteger();
        // 总数
        AtomicInteger importAllSize = new AtomicInteger();
        // 获取准备完成的数量
        List<String> prepareFinishList = JobStatusEnum.prepareFinish();

        jobManageList.stream().forEach(item->{
            if(null != item.getIsMajorProject() && item.getIsMajorProject()){
                if(prepareFinishList.contains(item.getPhase())
                        ||Objects.equals(item.getStatus(), JobManageBusStatusEnum.READY_COMPLETE.getStatus())){
                    parepCount.getAndIncrement();
                }
                importAllSize.getAndIncrement();
            }
        });
        ImportantJobCountVO importantJobCountVO = new ImportantJobCountVO();
        if(parepCount.get()==0 || importAllSize.get()==0){
            importantJobCountVO.setPrepRate(BigDecimal.ZERO);
        }else{
            BigDecimal prepRate = BigDecimal.valueOf(parepCount.get()).divide(BigDecimal.valueOf(importAllSize.get()),2,BigDecimal.ROUND_HALF_UP);
            importantJobCountVO.setPrepRate(prepRate);
        }
        importantJobCountVO.setJobNum(importAllSize.get());
        importantJobCountVO.setFinishedNum(parepCount.get());
        importantJobCountVO.setNoFinishNum(importAllSize.get()-parepCount.get());


        List<String> numberList = new ArrayList<>();
        AtomicInteger parepCount1= new AtomicInteger();
        // filter(item-> Objects.isNull(item.getIsImportant())|| (Objects.nonNull(item.getIsImportant()) && !item.getIsImportant())).
        jobManageList.stream().forEach(item->{
            if(prepareFinishList.contains(item.getPhase())
                    ||Objects.equals(item.getStatus(), JobManageBusStatusEnum.READY_COMPLETE.getStatus())){
                parepCount1.getAndIncrement();
            }
            importAllSize.getAndIncrement();
            numberList.add(item.getNumber());
       });

        JobRiskCountVO jobRiskCountVO =new JobRiskCountVO();
        List<JobHeightRiskVO> jobHeightRiskVOS= heightRiskService.listByJobNumber(numberList);
        if(parepCount.get()==0 || importAllSize.get()==0){
            jobRiskCountVO.setPrepRate(BigDecimal.ZERO);
        }else{
            BigDecimal prepRate1 = BigDecimal.valueOf(parepCount1.get()).divide(BigDecimal.valueOf(numberList.size()),2,BigDecimal.ROUND_HALF_UP);
            jobRiskCountVO.setPrepRate(prepRate1);
        }
        jobRiskCountVO.setJobNum(numberList.size());
        if(CollectionUtils.isEmpty(jobHeightRiskVOS)){
            return JobDashBoardVO.builder().importantJobCountVO(importantJobCountVO)
                    .jobRiskCountVO(jobRiskCountVO).build();
        }
        jobHeightRiskVOS.forEach(item->{
            if(Objects.equals(item.getRiskLevel(),"一级")){
                jobRiskCountVO.setHeightOneNum(jobRiskCountVO.getHeightOneNum()+1);
            }else if(Objects.equals(item.getRiskLevel(),"二级")){
                jobRiskCountVO.setHeightTwoNum(jobRiskCountVO.getHeightTwoNum()+1);
            }else if(Objects.equals(item.getRiskLevel(),"三级")){
                jobRiskCountVO.setHeightThreeNum(jobRiskCountVO.getHeightThreeNum()+1);
            }
        });
//        jobRiskCountVO.setJobNum(numberList.size());
//        jobRiskCountVO.setPrepRate(prepRate1);
        return JobDashBoardVO.builder().importantJobCountVO(importantJobCountVO)
                .jobRiskCountVO(jobRiskCountVO).build();
    }

    @Override
    public PersonDashboardVO personDashboardCount(String repairRound) throws InterruptedException {
       MajorRepairPlan majorRepairPlan= majorRepairPlanService.getSimpleByRepairRound(repairRound);
        PersonDashboardVO personDashboardVO = new PersonDashboardVO();
       if(Objects.nonNull(majorRepairPlan)){
           CountDownLatch countDownLatch = new CountDownLatch(2);
           for (int i = 0; i < 2; i++) {
               int finalI = i;
               ThreadUtil.execAsync(() -> {
                  try {
                      switch (finalI){
                          case 0:
                              String baseCode= majorRepairPlan.getBaseCode();
                              BasePersonCountVO basePersonCountVO =personMangeService.listByBaseCode(baseCode);
                              personDashboardVO.setBasePersonCountVO(basePersonCountVO);
                              break;
                          case 1:
                              //asdasd
                              List<String> userCodeList= schemeToPersonMapper.getUserCodeListByRepairRound(repairRound);
                              List<JobPostAuthorize>  jobPostAuthorizeList=   jobPostAuthorizeService.listByRepairRound(repairRound);
                              BasePersonCountVO majorPersonCountVO = new BasePersonCountVO();
                              if(CollectionUtils.isEmpty(userCodeList)){
                                  personDashboardVO.setMajorPersonCountVO(majorPersonCountVO);
                              }else{
                                  majorPersonCountVO.setPersonTotal(userCodeList.size());
                                  List<BasicUser> basicUserList=   basicUserService.listByNumberList(userCodeList);
                                  if(CollectionUtils.isEmpty(basicUserList)){
                                      majorPersonCountVO.setAgeCompareFifthTotal(0);
                                  }else{
                                      // 获取年龄大于 50的人员
                                      long fifth =basicUserList.stream().filter(item-> this.getAge(item.getDateOfBirth()) > 50).count();
                                      majorPersonCountVO.setAgeCompareFifthTotal(Integer.valueOf(String.valueOf(fifth)));
                                  }
                                  List<String>  personManageIdList = jobPostAuthorizeList.stream().filter(item-> StringUtils.hasText(item.getPersonManageId()))
                                          .map(JobPostAuthorize::getPersonManageId).collect(Collectors.toList());
                                  if(!CollectionUtils.isEmpty(personManageIdList)){
                                      // 这里会获取到 所有的 数据 （入场，待入场，离场）
                                      List<PersonManageLedger> personManageLedgerList=personManageLedgerMapper.listBySouceIdList(personManageIdList);
                                      if(!CollectionUtils.isEmpty(personManageLedgerList)) {
                                          Map<String,Long> typeToCountMap= personManageLedgerList.stream().collect(Collectors.groupingBy(PersonManageLedger::getType,Collectors.counting()));
                                          majorPersonCountVO.setTodayInTotal(typeToCountMap.getOrDefault("input",0L).intValue());
                                          majorPersonCountVO.setTodayOutTotal(typeToCountMap.getOrDefault("out",0L).intValue());
                                      }
                                  }
                              }
                              personDashboardVO.setMajorPersonCountVO(majorPersonCountVO);
                              break;
                          default:
                              break;
                      }
                  }catch (Exception e){
                      log.error("异步调用报错：{}",e);
                  }finally {
                      countDownLatch.countDown();
                  }
               });
           }
           countDownLatch.await();
           //BasePersonCountVO basePersonCountVO =
       }
        return personDashboardVO;
    }

    @Override
    public MaterialDashboardVO materialDashboardCount(String repairRound) throws InterruptedException {
        MaterialDashboardVO materialDashboardVO = new MaterialDashboardVO();
        MajorRepairPlan majorRepairPlan= majorRepairPlanService.getSimpleByRepairRound(repairRound);
        if(Objects.nonNull(majorRepairPlan)){
            CountDownLatch countDownLatch = new CountDownLatch(2);
            for (int i = 0; i < 2; i++){
                int finalI = i;
                ThreadUtil.execAsync(() -> {
                    try {
                        switch (finalI){
                            case 0:
                                String baseCode= majorRepairPlan.getBaseCode();
                                BaseMaterialCountVO basePersonCountVO =materialManageService.countByBaseCode(baseCode);
                                materialDashboardVO.setBaseMaterialCountVO(basePersonCountVO);
                                break;
                            case 1:
                                BaseMaterialCountVO materialCountVO = new BaseMaterialCountVO();

                                List<String> materialIdList = schemeToMaterialService.listByRepairRound(repairRound);
                                if(CollectionUtils.isEmpty(materialIdList)){
                                    materialDashboardVO.setMajorMaterialCountVO(materialCountVO);
                                }else{
                                    materialCountVO.setMaterialTotal(materialIdList.size());
                                    List<MaterialManageVO> manageVOList= materialManageService.listByIdList(materialIdList);
                                    Date nowDate = new Date();
                                    AtomicInteger notValied = new AtomicInteger();
                                    AtomicInteger inNum = new AtomicInteger();
                                    AtomicInteger outNum = new AtomicInteger();
                                    manageVOList.forEach(item->{
                                        if(Objects.nonNull(item.getNextVerificationDate())){
                                            if(DateUtil.compare(nowDate,item.getNextVerificationDate())>0){
                                                notValied.getAndIncrement();
                                            }
                                        }
                                        if(Objects.nonNull(item.getActInDate()) && Objects.equals(item.getStatus(),StatusEnum.ENABLE.getIndex()) ){
                                            inNum.getAndIncrement();
                                        }

                                        if(Objects.equals(item.getStatus(),StatusEnum.DRAFT.getIndex()) ){
                                            outNum.getAndIncrement();
                                        }
                                    });
                                    materialCountVO.setTodayInTotal(inNum.get());
                                    materialCountVO.setNotValidTotal(notValied.get());
                                    materialCountVO.setTodayOutTotal(outNum.get());
                                    if(inNum.get() >0){
                                        materialCountVO.setInRate( BigDecimal.valueOf(inNum.get())
                                                .divide(BigDecimal.valueOf(materialCountVO.getMaterialTotal()),2,BigDecimal.ROUND_HALF_UP));
                                    }
                                    materialDashboardVO.setMajorMaterialCountVO(materialCountVO);
                                }
                                break;
                            default:
                                break;
                        }
                    }catch (Exception e){
                        log.error("异步调用报错：{}",e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
            return materialDashboardVO;
        }
        BaseMaterialCountVO materialCountVO= new BaseMaterialCountVO();
        materialDashboardVO.setBaseMaterialCountVO(materialCountVO);
        materialDashboardVO.setMajorMaterialCountVO(materialCountVO);
        return new MaterialDashboardVO();
    }

    private  boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return  Boolean.FALSE;
        }
        java.util.Calendar cal1 = java.util.Calendar.getInstance();
        java.util.Calendar cal2 = java.util.Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(java.util.Calendar.YEAR) == cal2.get(java.util.Calendar.YEAR) &&
                cal1.get(java.util.Calendar.DAY_OF_YEAR) == cal2.get(java.util.Calendar.DAY_OF_YEAR);
    }
    public  int getAge(Date birthDate) {
        if(Objects.isNull(birthDate)){
            return 0;
        }

        LocalDate dateOfBirth = birthDate.toInstant() // 转换为Instant
                .atZone(ZoneId.systemDefault())// 转换为ZonedDateTime
                .toLocalDate(); // 获取LocalDate

        // 定义日期格式
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串转换为LocalDate对象
//        LocalDate dateOfBirth = LocalDate.pa(birthDate, formatter);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算年龄
        Period period = Period.between(dateOfBirth, currentDate);
        // 返回年龄
        return period.getYears();
    }
}
