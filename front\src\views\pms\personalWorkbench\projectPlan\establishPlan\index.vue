<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <div class="plan-container">
      <BasicTitle1
        title="我执行的项目计划"
        class="title"
      />
      <OrionTable
        ref="tableRef"
        :class="{'plan':!$props.from}"
        :options="tableOptions"
        :expandIconColumnIndex="3"
        :rowKey="(record) => record.key"
        :expandedRowKeys="defaultExpandedRowKeys"
        @initData="initData"
      >
        <template #index="{record}">
          {{ indexData?.filter(v => v?.id === record?.id)[0]?.index }}
        <!--        {{ convertToFormat(slotProps.recordIndexs) }}-->
        </template>
        <template #name="{ record }">
          <div
            class="hover-link flex-te flex flex-ac"
            :title="record.name"
            @click="handleToDetail(record)"
          >
            <!--计划图标-->
            <Icon
              v-if="record['nodeType']==='plan'"
              icon="orion-icon-carryout"
              class="primary-color"
              size="16"
            />
            <!--里程碑图标-->
            <Icon
              v-if="record['nodeType']==='milestone'"
              color="#FFB118"
              size="16"
              icon="orion-icon-flag"
            />
            <Tooltip :getPopupContainer="getPopupContainer">
              <template #title>
                <div class="pre-post-tooltip">
                  <template v-if="record?.['schemePrePostVOList']?.length">
                    <span>前置任务：</span>
                    <span
                      v-for="(item,index) in record?.['schemePrePostVOList']"
                      :key="item.id"
                    >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                  </template>

                  <template v-if="record?.['schemePostVOList']?.length">
                    <span>后置任务：</span>
                    <span
                      v-for="(item,index) in record?.['schemePostVOList']"
                      :key="item.id"
                    >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                  </template>
                </div>
              </template>
              <!--前后置计划图标-->
              <Icon
                v-if="record?.['schemePostVOList']?.length || record?.['schemePrePostVOList']?.length"
                color="#D50072"
                icon="fa-sort-amount-asc"
              />
            </Tooltip>
            <span class="ml10">{{ record.name }}</span>
          </div>
        </template>
        <template #affiliation="{ record }">
          <span
            class="action-btn"
            @click="projectDetail(record)"
          >{{ record.projectName }}</span>
        </template>
        <template #level="{ record }">
          {{ record['level'] }}级
        </template>
        <template #action="{ record }">
          <BasicTableAction
            :actions="actions"
            :record="record"
          />
        </template>
      </OrionTable>
      <GantView
        v-if="mode === 'gant' && isPower('XM_container_04_01_02',powerData)"
        :projectId="id"
      />
      <Milestone
        v-if="mode === 'milestone' && isPower('XM_container_04_01_03',powerData)"
        :projectId="id"
      />
      <!-- 计划编制 -->
      <AddModal
        @register="registerAdd"
        @handleColse="() => addModalVisibleChange(false)"
      />
      <!-- 前置关系 -->
      <BeforeRelation

        @register="registerBefore"
        @close="updateForm()"
      />
      <!-- 编辑，变更 -->
      <EditModal
        :editType="editType"
        @register="registerEdit"
        @close="() => setEditPlanModal(false)"
      />
      <!-- 计划下发 -->
      <DistributePlan
        @updateForm="updateForm"
        @register="registerDistributePlan"
        @handleColse="() => updateForm()"
      />
      <!-- 计划记录 -->
      <PlanRecord
        @register="registerRecord"
        @close="() => setPlanRecord(false)"
      />
      <!-- 执行完成 -->
      <PlanDone

        @register="registerPlanDone"
        @handleColse="() => planDoneVisibleChange(false, null)"
      />
      <!-- 审批 -->
      <ApplyChange

        @register="registerApplyChange"
        @close="() => setApplyChange(false, null)"
      />
      <!-- 导入 -->
      <BasicImport

        :downloadFileObj="downloadFileObj"
        :requestBasicImport="requestBasicImport"
        :requestSuccessImport="requestSuccessImport"
        @register="register"
        @changeImportModalFlag="changeImportModalFlag"
      />

      <!-- 催办 -->
      <UrgePlanModal

        @updateForm="updateForm"
        @register="registerUrgePlanModal"
        @handleColse="() => updateForm()"
      />

      <!--
    超时-->
      <WriteDelayReasonModal

        @updateForm="updateForm"
        @register="registerWriteDelayReasonModal"
        @handleColse="() => updateForm()"
      />
    </div>
  </Layout>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, nextTick, onMounted, provide, Ref, ref,
} from 'vue';

import {
  BasicButton,
  BasicImport,
  BasicTableAction,
  DataStatusTag,
  downloadByData as basicDownloadByData,
  Icon,
  isPower,
  OrionTable,
  useDrawer,
  useModal,
  BasicTitle1,
  Layout,
} from 'lyra-component-vue3';
import {
  message, Modal, RadioButton, RadioGroup, Tag, Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { useRoute, useRouter } from 'vue-router';
import { getProjectRoleUser, postProjectSchemeList, myPostProjectSchemeList } from '/@/views/pms/projectLaborer/projectLab/api';
import { LevelEnum, SituationColorEnum, TypeEnum } from '/@/views/pms/projectLaborer/projectLab/enums';
import { projectIdKey } from '/@/views/pms/projectLaborer/projectLab/types';
import AddModal from './components/AddPlan.vue';
import BeforeRelation from './components/BeforeRelation.vue';
import GantView from './components/GanteView.vue';
import Milestone from './components/Milestone.vue';
import EditModal from './components/EditPlan.vue';
import PlanRecord from './components/PlanRecord.vue';
import PlanDone from './components/PlanDone.vue';
import ApplyChange from './components/ApplyChange.vue';
import DistributePlan from './components/DistributePlan.vue';
import UrgePlanModal from './components/UrgePlanModal.vue';

import WriteDelayReasonModal from './components/WriteDelayReasonModal.vue';
import { treeToList } from '/@/utils/helper/treeHelper';
import { useUserStore } from '/@/store/modules/user';
import record from '../../../projectLaborer/components/BpmnModules/src/BpmnMain/component/Record/index.vue';

export default defineComponent({
  name: 'ProjectPlanInfo',
  components: {
    Layout,
    Icon,
    OrionTable,
    BeforeRelation,
    GantView,
    Milestone,
    EditModal,
    PlanRecord,
    PlanDone,
    ApplyChange,
    BasicImport,
    DistributePlan,
    BasicTableAction,
    Tooltip,
    UrgePlanModal,
    WriteDelayReasonModal,
    BasicTitle1,
    AddModal,
  },
  props: {
    projectData: {
      type: Object,
      default: () => {
      },
    },
    id: {
      type: String,
      default: () => '',
    },
    from: {
      type: String,
      default: () => '',
    },
    // 不同页面引用的权限前缀
    prefix: {
      type: String,
      default: 'XM_container_button_',
    },
    // 不同页面引用的权限间隔
    interval: {
      type: Number,
      default: 21,
    },
  },

  setup(props) {
    const router = useRouter();
    const route = useRoute();
    const projectId = inject(projectIdKey) || inject('projectId');
    provide('projectId', projectId);
    const tableRef = ref(null);
    const selectedRowKeys = ref([]);
    const selectedRows = ref([]);
    const userStore = useUserStore();
    const mode: Ref<any> = ref('table');
    const defaultExpandedRowKeys = ref<string[]>([]);
    const downloadFileObj = {
      url: '/pms/projectScheme/template/download',
      method: 'POST',
    };
    const powerData: Ref = inject('powerData');

    const [register, { openModal }] = useModal();
    const [registerPlanDone, { openModal: openPlanDoneModal }] = useModal();
    const [registerDistributePlan, { openModal: setDistributePlan }] = useModal();

    const [registerUrgePlanModal, { openModal: openUrgePlanModal }] = useModal();

    const [registerWriteDelayReasonModal, { openModal: openWriteDelayReasonModal }] = useModal();
    const [registerAdd, { openModal: setAddPlan }] = useModal();

    const [registerBefore, { openDrawer: openBeforeRelation }] = useDrawer();
    const [registerApplyChange, { openDrawer: openApplyChange }] = useDrawer();
    const [registerEdit, { openDrawer: openEdit }] = useDrawer();
    const [registerRecord, { openDrawer: openRecord }] = useDrawer();
    const indexData: Ref<any[]> = ref([]);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys,
        onSelect: onSelectChange,
        onSelectAll,
      },
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PERSONALWORKBENCH_PROJECTPLAN_ESTABLISHPLAN_INDEX',
      // pagination: false,
      rowClassName: (record) => (record.topSort ? 'table-striped' : null),
      api: async (params) => {
        const result = await (props.from ? myPostProjectSchemeList({
          query: {
            // projectId,
            typeEnum: 'ACCEPTANCE_FROM',
            // power: {
            //   pageCode: props.prefix === 'XMYS_container_button_' ? 'PMS-ProjectAccpetanceDetail' : 'PMS-cghAccpetanceDetail',
            //   containerCode: props.prefix === 'XMYS_container_button_' ? 'XMYS_container_03' : 'CGYS_container_03',
            // },
            name: params?.searchConditions ? params?.searchConditions[0][0]?.className ? undefined : params?.searchConditions[0][0]?.values[0] : undefined,
          },
          ...params,
        }) : myPostProjectSchemeList({
          query: {
            // projectId,
            typeEnum: 'PROJECT_SCHEME',
            // power: {
            //   pageCode: 'PMS90002',
            //   containerCode: 'XM_container_04_01_01',
            // },
            name: params?.searchConditions ? params?.searchConditions[0][0]?.className ? undefined : params?.searchConditions[0][0]?.values[0] : undefined,
          },
          ...params,
        }));
        // let allData = formatTreeTop(JSON.parse(JSON.stringify(result)).reverse()).reverse();
        // indexData.value = treeToList(formatTreeKey(allData.filter((v) => v.key.substr(-3) !== 'Top'), [], ''));

        let allData = formatTreeTop(result.content);
        indexData.value = treeToList(formatTreeKey(allData.filter((v) => v.key.substr(-3) !== 'Top'), [], ''));
        return result;
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'index',
          width: 100,
          fixed: 'left',
          slots: { customRender: 'index' },
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          minWidth: 300,
          fixed: 'left',
          slots: { customRender: 'name' },
        },
        {
          title: '所属项目',
          dataIndex: 'projectName',
          width: 300,
          slots: { customRender: 'affiliation' },
        },
        {
          title: '层级',
          dataIndex: 'level',
          width: 100,
          slots: { customRender: 'level' },
        },
        {
          title: '计划类型',
          dataIndex: 'nodeType',
          width: 120,
          customRender({ text }) {
            return text === 'milestone' ? '里程碑节点' : '计划';
          },
        },
        {
          title: '计划状态',
          dataIndex: 'dataStatus',
          width: 100,
          customRender({ record }) {
            return record.dataStatus
              ? h(DataStatusTag, {
                statusData: record.dataStatus,
              })
              : '';
          },
        },
        // {
        //   title: '责任部门',
        //   dataIndex: 'rspSubDeptName',
        //   width: 120,
        // },
        // {
        //   title: '责任科室',
        //   dataIndex: 'rspSectionName',
        //   width: 120,
        // },
        {
          title: '计划责任人',
          dataIndex: 'rspUserName',
          width: 120,
        },
        // {
        //   title: '员工编号',
        //   dataIndex: 'rspUserCode',
        //   width: 120,
        // },
        // {
        //   title: '是否关联流程',
        //   dataIndex: 'processFlag',
        //   customRender({ text }) {
        //     return text ? '是' : '否';
        //   },
        // },
        {
          title: '计划开始日期',
          dataIndex: 'beginTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text)
              .format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划结束日期',
          dataIndex: 'endTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text)
              .format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '情况',
          dataIndex: 'circumstance',
          width: 120,
          customRender({ record }) {
            return h(Tag, { color: SituationColorEnum[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
          },
        },
        {
          title: '实际开始日期',
          dataIndex: 'actualBeginTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text)
              .format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '实际结束日期',
          dataIndex: 'actualEndTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text)
              .format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划下发时间',
          dataIndex: 'issueTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text)
              .format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '计划创建人',
          dataIndex: 'creatorName',
          width: 120,
        },

        {
          title: '是否超时完成',
          width: 120,
          customRender({ record }) {
            return !dayjs(record.endTime).isAfter(dayjs()) && record.status === 111 ? '是' : '否';
          },
        },
        // {
        //   title: '超时原因',
        //   width: 120,
        //   dataIndex: 'delayEndReason',
        // },
        {
          title: '操作',
          dataIndex: 'action',
          width: 240,
          fixed: 'right',
          slots: { customRender: 'action' },
          resizable: false,
        },
      ],
    });
    // 格式化置顶
    function formatTreeTop(list, ids = []) {
      return list.map((item) => {
        let obj = item;
        if (ids.includes(obj.id)) {
          obj.key = `${obj.id}Top`;
        } else {
          obj.key = obj.id;
          ids.push(obj.id);
        }
        if (item.children && item.children.length === 0) {
          delete obj.children;
        } else if (item.children && item.children.length) {
          return {
            ...obj,
            children: formatTreeTop(item.children, ids),
          };
        }
        return obj;
      });
    }

    // 格式化序号
    function formatTreeKey(list, ids = [], str?) {
      return list.map((item, index) => {
        let obj = item;
        if (ids.map((v) => v.id)
          .includes(obj.id)) {
          obj.index = ids.filter((v) => v.id === obj.id)[0].index;
        } else {
          obj.index = str + (index + 1);
          ids.push(obj);
        }

        if (item.children && item.children.length === 0) {
          delete obj.children;
        } else if (item.children && item.children.length) {
          return {
            ...obj,
            children: formatTreeKey(item.children, ids, `${obj.index}.`),
          };
        }
        return obj;
      });
    }

    // 自定义用户选择
    function onSelectChange(record, selected) {
      selectedRowKeys.value = selectedRowKeys.value.filter((item) => !item.endsWith('Top'));
      selectedRows.value = selectedRows.value.filter((item) => !item.key.endsWith('Top'));
      if (selected) {
        let rows = [];
        let rowKeys = [];
        if (record?.children) {
          rows = treeToList(record.children);
          rowKeys = rows.map((item) => item.id);
        }
        selectedRowKeys.value = [
          ...selectedRowKeys.value,
          record.id,
          ...rowKeys,
        ];
        selectedRows.value = [
          ...selectedRows.value,
          {
            ...record,
            key: record.id,
          },
          ...rows,
        ];
      } else if (record?.children) {
        let keys = [
          record.id,
          ...treeToList(record.children)
            .map((item) => item.id),
        ];
        selectedRowKeys.value = selectedRowKeys.value.filter((item) => !keys.includes(item));
        selectedRows.value = selectedRows.value.filter((item) => !keys.includes(item.id));
      } else {
        selectedRowKeys.value = selectedRowKeys.value.filter((item) => item !== record.id);
        selectedRows.value = selectedRows.value.filter((item) => item.id !== record.id);
      }
      let arrTop = [];
      let rowTop = [];
      for (let i = 0; i < selectedRowKeys.value.length; i++) {
        if (!arrTop.includes(`${selectedRowKeys.value[i]}Top`)) {
          arrTop.push(`${selectedRowKeys.value[i]}Top`);
        }
        if (rowTop.every((item) => item.key !== `${selectedRowKeys.value[i]}Top`)) {
          rowTop.push({
            ...selectedRows.value[i],
            key: `${selectedRowKeys.value[i]}Top`,
          });
        }
      }
      selectedRowKeys.value = selectedRowKeys.value.concat(arrTop);
      selectedRows.value = selectedRows.value.concat(rowTop);
    }

    // 用户全选回调
    function onSelectAll(selected, rows) {
      if (selected) {
        selectedRowKeys.value = rows.filter((item) => item)
          .map((item) => item.key);
        selectedRows.value = rows.filter((item) => item);
      } else {
        selectedRowKeys.value = [];
        selectedRows.value = [];
      }
    }

    const editItem = ref(null);
    const editType = ref();
    const fileTypeList = ref([]);

    const actions = ref([
      {
        text: '请补充超时原因',
        isShow: (record) => !dayjs(record.endTime).isAfter(dayjs()) && record.status === 111,
        //
        onClick: (record) => openWriteDelayReasonModal(true, record),
      },
      {
        text: '编辑',
        isShow: (record) => isPower(formatPowerCode('07') && record.status === 130, record?.rdAuthList ?? []),
        onClick: (record) => setEditPlanModal(true, record),
      },
      {
        text: '计划审批',
        isShow: (record) =>
          isPower(formatPowerCode('18'), record?.rdAuthList ?? [])
            && ((record.approveStatus === 0 && isSchemeCreator.value(record)) || (record.approveStatus === 1 && isPower('investment_admin', roleUserInfo.value))),
        onClick: (record) => setApplyChange(true, record),
      },
      {
        text: '计划记录',
        isShow: (record) =>
          isPower(formatPowerCode('14'), record?.rdAuthList ?? []),
        onClick: (record) => setPlanRecord(true, record),
      },
      {
        text: '执行完成',
        isShow: (record) =>
          (record.status !== 140)
            && isPower(formatPowerCode('15'), record?.rdAuthList ?? [])
            && record.approveStatus !== 0 && record.approveStatus !== 1 && record.processFlag,
        onClick: (record) => planDoneVisibleChange(true, record),
      },
      // {
      //   text: '变更',
      //   isShow: (record) => isSchemeCreator.value(record)
      //       && isPower(formatPowerCode('13'), record?.rdAuthList ?? [])
      //       && record.approveStatus !== 1 && record.approveStatus !== 0,
      //   onClick: (record) => setEditPlanModal(true, record),
      // },
      // {
      //   text: '置顶',
      //   isShow: (record) =>
      //     isPower(formatPowerCode('08'), record?.rdAuthList ?? [])
      //       && record.topSort === 0,
      //   onClick: (record) => pushRecord(record, 'top'),
      // },
      // {
      //   text: '取消置顶',
      //   isShow: (record) =>
      //     isPower(formatPowerCode('09'), record?.rdAuthList ?? [])
      //       && record.topSort !== 0,
      //   onClick: (record) => pushRecord(record, 'unTop'),
      // },
      {
        text: '调整申请',
        isShow: (record) =>
          !isSchemeCreator.value(record)
            && isSchemeRsp.value(record)
            && isPower(formatPowerCode('16'), record?.rdAuthList ?? [])
            && record.approveStatus !== 0 && record.approveStatus !== 1,
        onClick: (record) => setEditPlanModal(true, record, 'apply'),
      },
      {
        text: '完成确认',
        isShow: (record) =>
          isPower(formatPowerCode('17'), record?.rdAuthList ?? [])
            && record.approveStatus !== 0 && record.approveStatus !== 1,
        onClick: (record) => planDoneVisibleChange(true, record),
      },
      {
        text: '删除',
        isShow: (record) =>
          isPower(formatPowerCode('10'), record?.rdAuthList ?? []),
        onClick: (record) => onClick({ key: 'delete' }, record),
      },
      // {
      //   text: '上移',
      //   isShow: (record) =>
      //     record?.topSort === 0
      //       && isPower(formatPowerCode('11'), record?.rdAuthList ?? []),
      //   onClick: (record) => onClick({ key: 'up' }, record),
      // },
      // {
      //   text: '下移',
      //   isShow: (record) => record?.topSort === 0
      //       && isPower(formatPowerCode('12'), record?.rdAuthList ?? []),
      //   onClick: (record) => onClick({ key: 'down' }, record),
      // },
      {
        text: '催办',
        onClick: (record) => openUrgePlanModal(true, record),
      },
    ]);

    const addBtnDisabled = computed(
      () => (!isPower('PL', roleUserInfo.value) && !tableRows.value.length)
            || (tableRows.value.length
                && tableRows.value.some(
                  (val) =>
                    val.approveStatus === 0
                        || val.approveStatus === 1
                        || val?.dataStatus?.statusValue === 111
                        || (!isPower('PL', roleUserInfo.value)
                            && !isSchemeCreator.value(val)
                            && !isSchemeRsp.value(val)
                        ),
                )),
    );

    const beforeRelationDisabled = computed(
      () =>
        !tableRows.value.length
            || (tableRows.value.length
                && tableRows.value.some(
                  (val) =>
                    val.approveStatus === 0
                        || val.approveStatus === 1
                        || (!isPower('PL', roleUserInfo.value) && !isSchemeCreator.value(val)),
                )),
    );

    const distributesDisabled = computed(() =>
      !tableRows.value.length
        || (tableRows.value.length
            && tableRows.value.some(
              (val) =>
                val.approveStatus === 0
                    || val.approveStatus === 1
                    || val?.dataStatus?.statusValue === 111 || val?.dataStatus?.statusValue === 130
                    || (!isPower('PL', roleUserInfo.value) && !isSchemeCreator.value(val)),
            )));

    const importDisabled = computed(
      () =>
        (!tableRows.value.length && !isPower('PL', roleUserInfo.value))
            || (tableRows.value.length
                && tableRows.value.some(
                  (val) =>
                    val.approveStatus === 0
                        || val.approveStatus === 1
                        || val?.dataStatus?.statusValue === 111
                        || (!isPower('PL', roleUserInfo.value) && !isSchemeCreator.value(val)),
                )),
    );

    // 计划创建人
    const isSchemeCreator = computed(() => (row) => row.creatorId === useUserStore().getUserInfo.id);
    // 计划责任人
    const isSchemeRsp = computed(() => (row) => row.rspUser === useUserStore().getUserInfo.id);

    // 表格勾选数据
    const tableRows = computed(() => {
      let map = new Map();
      return selectedRows.value.filter((row) => !map.has(row.id) && map.set(row.id, 1));
    });

    // 获取当前项目用户的角色信息
    const roleUserInfo: Ref<any[]> = ref([]);

    async function reqProjectRoleUser() {
      roleUserInfo.value = await getProjectRoleUser(projectId); // 获取当前用户所在项目的角色信息
    }

    function initData(data) {
      defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
    }

    function getDefaultExpandedRowKeys(data) {
      let rowKeys = [];
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.children && item.children.length > 0) {
          rowKeys.push(item.id);
          let rowKeys1 = getDefaultExpandedRowKeys(item.children);
          if (rowKeys1) {
            rowKeys = rowKeys.concat(rowKeys1);
          }
        }
      }
      return rowKeys;
    }

    function addModalVisibleChange(value: boolean) {
      if (tableRows.value.length > 1) {
        message.error('计划编制只能选择一条项目计划');
        return;
      }

      if (value) {
        setAddPlan(true, {
          parentIds: tableRows.value.map((item) => item?.id),
          parentData: tableRows.value,
          projectData: props?.projectData,
          from: props?.from || '',
        });
      }
      if (!value) {
        updateForm();
      }
    }

    function changeImportModalFlag({
      successImportFlag,
      succ,
    }) {
      if (successImportFlag) {
        updateForm();
      } else if (succ) {
        new Api(`/pms/projectScheme/import/excel/cancel/${succ}`).fetch('', '', 'post');
      }
    }

    const handleImport = () => {
      if (tableRows.value.length > 1) {
        message.error('导入只能选择一条项目计划');
        return;
      }
      openModal(true, {});
    };

    const setBeForeRelation = (value) => {
      openBeforeRelation(value, {
        // 兼容一下之前的写法，
        projectId,
        parentIds: tableRows.value.map((item) => item.id),
        projectData: props.projectData,
        from: props.from,
      });
    };

    const setEditPlanModal = (value, record = null, type = 'edit') => {
      editItem.value = record;
      editType.value = type;

      if (value && record) {
        openEdit(value, {
          data: record,
          editType: type,
        });
      }
      if (!value) {
        updateForm();
      }
    };

    const pushRecord = (record, key) => {
      new Api('/pms')
        .fetch('', `projectScheme/${key}/${record.id}`, 'PUT')
        .then(() => {
          message.success(key === 'unTop' ? '取消' : '置顶成功');
          updateForm();
        });
    };

    // 计划下发
    const planDistribute = () => {
      setDistributePlan(true, {
        schemeIds: tableRows.value.map((item) => item.id),
        projectName: props?.projectData.name,
        informUserId: userStore.getUserInfo?.id,
        projectId,
        from: props.from === 'accpet' ? 'accpet' : '',
        fromId: props.from === 'accpet' ? route?.query?.id : '',
      });
    };

    const setPlanRecord = (value: boolean, record = null) => {
      editItem.value = record;
      if (value) {
        openRecord(value, { data: record });
      } else {
        updateForm();
      }
    };

    const planDoneVisibleChange = (value, row) => {
      editItem.value = row;
      if (value) {
        openPlanDoneModal(value, {
          data: row,
          from: props.from ? 'ACCEPTANCE_FORM' : '',
          fromId: props.id || '',
        });
      } else {
        updateForm();
      }
    };
    // 计划详情页
    const handleToDetail = (row) => {
      router.push({
        name: 'ProPlanDetails',
        params: { id: row.id },
      });
    };
    // 项目详情页
    const projectDetail = (row) => {
      router.push({
        name: 'MenuComponents',
        query: {
          id: row.projectId,
        },
      });
    };
    const setApplyChange = (value, record) => {
      editItem.value = record;
      if (value) {
        openApplyChange(value, { data: record });
      } else {
        updateForm();
      }
    };

    const updateForm = () => {
      nextTick(() => {
        tableRef.value.reload();
      });
    };

    // 上移
    const onClick = async (action, row) => {
      if (action.key === 'up') {
        upRecord(row.id);
      }
      if (action.key === 'down') {
        downRecord(row.id);
      }
      if (action.key === 'delete') {
        const id = Array.isArray(row) ? row : [row.id];
        deleteTable(id);
      }
      if (action.key === 'urgePlan') {
        const id = Array.isArray(row) ? row : [row.id];
        deleteTable(id);
      }
    };
    const deleteTable = (params: string[], title?: string) => {
      Modal.confirm({
        title: title || '是否删除该数据?',
        onOk() {
          return new Promise((resolve) => {
            new Api('/pms')
              .fetch(params, 'projectScheme', 'DELETE')
              .then(() => {
                message.success('删除成功');
                updateForm();
                selectedRows.value = selectedRows.value.filter((item) => !params.includes(item?.id));
                selectedRowKeys.value = selectedRowKeys.value.filter((item) => !params.includes(item) && !params.includes(`${item}Top`));
              })
              .finally(() => {
                resolve('');
              });
          });
        },
      });
    };

    const downRecord = (id) => {
      new Api('/pms')
        .fetch('', `projectScheme/down/${id}`, 'PUT')
        .then(() => {
          message.success('下移成功');
          updateForm();
        });
    };

    const upRecord = (id) => {
      new Api('/pms').fetch('', `projectScheme/up/${id}`, 'PUT')
        .then(() => {
          message.success('上移成功');
          updateForm();
        });
    };
    const requestBasicImport = async (formData) =>
      new Promise((resolve) => {
        new Api('/pms')
          .importFile(
            formData[0],
            `/api/pms/projectScheme/import/excel/${projectId}?pid=${
              tableRows.value?.[0]?.id || 0
            }`,
          )
          .then((res) => {
            const {
              code,
              message,
            } = res.data;
            if (code === 200) {
              // 转换oom   ---》 message
              let newResultData = res.data.result;
              if (res.data.result.oom) {
                newResultData.message = res.data.result.oom;
              }
              resolve(newResultData);
            } else {
              resolve({
                code: 4000,
                message,
              });
            }
          });
      });

    const exportFile = async () => {
      await basicDownloadByData(
        '/api/pms/projectScheme/export/excel',
        {
          projectId,
          projectSchemeIds: tableRows.value.map((item) => item.id),
        },
        '',
        'POST',
        false,
        false,
      );
    };

    const requestSuccessImport = (importId) =>
      new Promise((resolve) => {
        new Api(`/pms/projectScheme/import/excel/verify/${importId}`).fetch('', '', 'post')
          .then(() => {
            resolve({
              result: true,
            });
          });
      });

    function formatPowerCode(number: string | number) {
      return `${props.prefix}${((Number(props.interval) + Number(number)) < 10) ? `0${Number(props.interval) + Number(number)}` : (Number(props.interval) + Number(number))}`;
    }

    // 批量删除
    function handleBatchDel() {
      if (tableRows.value.every((item) => item.status === 101)) {
        if (tableRows.value.some((item) => item.children)) {
          deleteTable(tableRows.value.map((item) => item.id), '该计划下有子计划是否确定一并删除?');
        } else {
          deleteTable(tableRows.value.map((item) => item.id));
        }
      } else {
        message.info('选择计划存在不允许删除数据，请重新选择');
      }
    }

    // 设置tooltip挂载节点
    function getPopupContainer(): Element {
      return document.querySelector('.plan-container');
    }

    function convertToFormat(recordIndexes) {
      if (!recordIndexes) {
        return '';
      }
      if (recordIndexes && recordIndexes.length === 0) {
        return '';
      }

      const incrementedIndexes = recordIndexes.map((item, index) => {
        if (index === 0) {
          return parseInt(item) + 1;
        }
        return parseInt(item) + 1;
      });
      const formattedIndexes = incrementedIndexes.join('-');
      return formattedIndexes;
    }

    return {
      tableRef,
      tableOptions,
      initData,
      addModalVisibleChange,
      selectedRowKeys,
      setBeForeRelation,
      mode,
      editItem,
      setEditPlanModal,
      pushRecord,
      planDistribute,
      setPlanRecord,
      planDoneVisibleChange,
      handleToDetail,
      setApplyChange,
      selectedRows,
      onClick,
      LevelEnum,
      TypeEnum,
      editType,
      requestBasicImport,
      fileTypeList,
      openModal,
      register,
      exportFile,
      handleImport,
      registerBefore,
      updateForm,
      registerApplyChange,
      registerEdit,
      registerRecord,
      registerPlanDone,
      openPlanDoneModal,
      registerDistributePlan,
      registerUrgePlanModal,
      registerWriteDelayReasonModal,
      registerAdd,
      changeImportModalFlag,
      requestSuccessImport,
      addBtnDisabled,
      roleUserInfo,
      beforeRelationDisabled,
      distributesDisabled,
      importDisabled,
      actions,
      downloadFileObj,
      powerData,
      isPower,
      formatPowerCode,
      defaultExpandedRowKeys,
      indexData,
      tableRows,
      handleBatchDel,
      getPopupContainer,
      convertToFormat,
      projectDetail,
    };
  },
  computed: {
    record() {
      return record;
    },
  },
});
</script>
<style lang="less" scoped>
.title{
  margin-left: 20px;
}
:deep(.plan) .orion-table-header-wrap {
  & > .flex-f1.flex {
    min-height: 48px;
  }
}

.plan-container {
  width: 100%;
  position: relative;
}

.select-btn {
  position: absolute;
  top: 16px;
  right: 100px;
  z-index: 2;
}

.dis-body {
  padding: 22px 22px 30px;
}

.flex-box {
  display: flex;
  align-items: center;
  margin-top: 10px;

  > span {
    margin-right: 10px;
  }
}

.disable {
  color: #666;
}

.primary-color {
  color: ~`getPrefixVar('primary-color')`;
}

.hover-link {
  cursor: pointer;

  &:hover > span {
    color: ~`getPrefixVar('primary-color')`;
  }
}

.pre-post-tooltip {
  display: flex;
  flex-direction: column;
  font-size: 12px;
  padding: 0 5px;

  span {
    line-height: 1.5;
  }
}
</style>
