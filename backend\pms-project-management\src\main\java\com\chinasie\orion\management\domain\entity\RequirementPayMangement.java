package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * RequirementPayMangement Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-15 14:00:35
 */
@TableName(value = "pmsx_requirement_pay_mangement")
@ApiModel(value = "RequirementPayMangementEntity对象", description = "需求支付信息")
@Data

public class RequirementPayMangement extends ObjectEntity implements Serializable {

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @TableField(value = "bond")
    private BigDecimal bond;

    /**
     * 截标时间
     */
    @ApiModelProperty(value = "截标时间")
    @TableField(value = "tenders_end_time")
    private Date tendersEndTime;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @TableField(value = "pay_way")
    private String payWay;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @TableField(value = "apply_time")
    private Date applyTime;

    /**
     * 付款人姓名
     */
    @ApiModelProperty(value = "付款人姓名")
    @TableField(value = "payer")
    private String payer;

    /**
     * 投标有效期
     */
    @ApiModelProperty(value = "投标有效期")
    @TableField(value = "bid_validity")
    private String bidValidity;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @TableField(value = "requirement_number")
    private String requirementNumber;

}
