package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.MaterialOutManageDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.material.OutBoundDTO;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.dto.MaterialManageDTO;
import com.chinasie.orion.domain.vo.MaterialManageVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.major.BaseMaterialCountVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MaterialManage 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
public interface MaterialManageService extends OrionBaseService<MaterialManage> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MaterialManageVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param materialManageDTO
     */
    String create(MaterialManageDTO materialManageDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param materialManageDTO
     */
    Boolean edit(MaterialManageDTO materialManageDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MaterialManageVO> pages(Page<MaterialManageDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MaterialManageVO> vos) throws Exception;


    Boolean outBound(String id, MaterialOutManageDTO materialOutManageDTO);

    Boolean outBoundBatch(OutBoundDTO outBoundDTO);

    /**
     *  物资管理时间校验
     */
    void materialManageDateVerifyHandler();

    /**
     *  物资管理时间校验
     * @param materialIdList
     * @return
     */
    List<MaterialManageVO> listByIdList(List<String> materialIdList);

    /**
     *  编辑入场离场时间
     * @param inAndOutDTO
     * @return
     */
    Boolean editDate(InAndOutDTO inAndOutDTO);

    /**
     *  查询是否 加入物资管理 如果加入 那么将ID返回
     * @param assetType
     * @param assetCode
     * @param number
     * @param baseCode
     * @return
     */
    MaterialManage getMaterialManageId(String assetType, String assetCode, String number, String baseCode);

    /**
     *
     * @param baseCode
     * @return
     */
    BaseMaterialCountVO countByBaseCode(String baseCode);
}
