<script setup lang="ts">
import {
  BasicButton, BasicCard, BasicTableAction, OrionTable,
} from 'lyra-component-vue3';
import {
  h,
  inject, reactive, ref, Ref,
} from 'vue';
import Api from '/@/api';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '成本支出编码',
      field: 'number',
    },
    {
      label: '状态',
      field: 'dataStatus',
    },
    {
      label: '成本中心',
      field: 'costCenterName',
    },
    {
      label: '科目编码',
      field: 'expenseAccountNumber',
    },
    {
      label: '科目名称',
      field: 'expenseAccountName',
    },
    {
      label: '支出金额',
      field: 'expendMoney',
    },
    {
      label: '发生时间',
      field: 'modifyTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '发生人',
      field: 'modifyName',
    },
    {
      label: '摘要',
      field: 'remark',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
const tableOptions = {

  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showSmallSearch: false,
  columns: [
    {
      title: '预算申请编码',
      dataIndex: 'number',
    },
    {
      title: '预算名称',
      dataIndex: 'name',
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',
    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',
    },
    {
      title: '期间类型',
      dataIndex: 'timeTypeName',
      align: 'center',
    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectTypeName',
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },
    {
      title: '支出金额（元）',
      dataIndex: 'expendMoney',
    },
    {
      title: '预算余额（元）',
      dataIndex: 'residueMoney',
    },

  ],
  api: (params:Record<string, any>) => new Api('/pms/budgetExpend').fetch({
    formId: detailsData?.id,
    // power: {
    //   containerCode: 'table-list-container-demo111-KcBsejQf',
    //   pageCode: 'list-container-demo111',
    // },
  }, 'list', 'GET'),

};

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="成本使用预算明细"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
