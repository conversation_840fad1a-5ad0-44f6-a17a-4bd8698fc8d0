package com.chinasie.orion.management.service;


import com.chinasie.orion.management.domain.dto.RequirementManagementMarkDTO;
import com.chinasie.orion.management.domain.entity.RequirementManagementMark;
import com.chinasie.orion.management.domain.vo.RequirementManagementMarkVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;


/**
 * <p>
 * RequirementManagementMark 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05 19:52:08
 */
public interface RequirementManagementMarkService extends OrionBaseService<RequirementManagementMark> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    RequirementManagementMarkVO detail(String id, String pageCode);

    /**
     * 新增
     * <p>
     * * @param requirementManagementMarkDTO
     */
    String save(RequirementManagementMarkDTO requirementManagementMarkDTO);


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids);


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<RequirementManagementMarkVO> pages(Page<RequirementManagementMarkDTO> pageRequest);

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<RequirementManagementMarkVO> vos);
}
