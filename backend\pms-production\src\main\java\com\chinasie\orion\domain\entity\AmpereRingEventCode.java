package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 * 安质环事件码表
 **/
@ApiModel(value = "AmpereRingEventCode 对象",description = "安质环事件码表")
@TableName(value = "pms_amperering_event_code")
@Data
public class AmpereRingEventCode extends ObjectEntity implements Serializable {
    /**
     * 事件编码
     */
    @ApiModelProperty(value = "事件编码")
    @TableField(value = "event_code")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @TableField(value = "event_level")
    private String eventLevel;

    /**
     * 考核分数
     */
    @ApiModelProperty(value = "考核分数")
    @TableField(value = "score")
    private Double score;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "is_delete")
    private Boolean isDelete;

    /**
     * 是否集团考核指标
     */
    @ApiModelProperty(value = "是否集团考核指标")
    @TableField(value = "is_group_kpi")
    private Boolean isGroupKpi;

    /**
     * 是否监控指标：
     */
    @ApiModelProperty(value = "是否监控指标")
    @TableField(value = "is_monitor_index")
    private Boolean isMonitorIndex;

    /**
     * 是否计算天数
     */
    @ApiModelProperty(value = "是否计算天数")
    @TableField(value = "is_calculate_days")
    private Boolean isCalculateDays;

    /**
     * 事件分类
     */
    @ApiModelProperty(value = "事件分类")
    @TableField(value = "parent_name")
    private String parentName;

    /**
     * 分类code
     */
    @ApiModelProperty(value = "分类code")
    @TableField(value = "parent_id")
    private String parentId;
}
