package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.PlanBaseLineDTO;
import com.chinasie.orion.domain.entity.PlanBaseLine;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.BaseLineRepository;
import com.chinasie.orion.service.BaseLineService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/15:56
 * @description:
 */
@Service
public class BaseLineServiceImpl extends OrionBaseServiceImpl<BaseLineRepository, PlanBaseLine> implements BaseLineService {

    @Override
    public boolean copyPlanRecursion(List<PlanBaseLineDTO> baseLineDTOList) throws Exception {
        List<PlanBaseLine> planBaseLines = BeanCopyUtils.convertListTo(baseLineDTOList, PlanBaseLine::new);
        this.saveBatch(planBaseLines);
        return true;
    }

    @Override
    public List<PlanBaseLineDTO> getListByBaseId(String id) throws Exception {
        LambdaQueryWrapper<PlanBaseLine> baseLineDTOOrionChainWrapper = new LambdaQueryWrapper<>(PlanBaseLine.class);
        baseLineDTOOrionChainWrapper.eq(PlanBaseLine::getBaseLineId, id);
        List<PlanBaseLine> baseLines = this.list(baseLineDTOOrionChainWrapper);
        List<PlanBaseLineDTO> planBaseLineDTOS = BeanCopyUtils.convertListTo(baseLines, PlanBaseLineDTO::new);
        return planBaseLineDTOS;
    }


    @Override
    public boolean delById(String id) throws Exception {
        LambdaQueryWrapper<PlanBaseLine> baseLineDTOOrionChainWrapper = new LambdaQueryWrapper<>(PlanBaseLine.class);
        baseLineDTOOrionChainWrapper.eq(PlanBaseLine::getBaseLineId, id);
        return this.remove(baseLineDTOOrionChainWrapper);
    }
}
