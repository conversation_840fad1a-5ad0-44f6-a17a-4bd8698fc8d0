package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ProjectMaterialPreparation DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
@ApiModel(value = "ProjectMaterialPreparationDTO对象", description = "备料与加工申请")
@Data
@ExcelIgnoreUnannotated
public class ProjectMaterialPreparationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 20)
    @ExcelProperty(value = "名称 ", index = 0)
    private String name;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @NotBlank(message = "申请部门不能为空")
    @ExcelProperty(value = "申请部门 ", index = 1)
    private String applyDept;

    /**
     * 产品/电路编码
     */
    @ApiModelProperty(value = "产品/电路编码")
    @NotBlank(message = "产品/电路编码不能为空")
    @ExcelProperty(value = "产品/电路编码 ", index = 2)
    private String materialNumber;

    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    @NotNull(message = "需要完成时间不能为空")
    @ExcelProperty(value = "需要完成时间 ", index = 3)
    private Date requireCompleteTime;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    @Size(max = 50)
    @ExcelProperty(value = "申请原因 ", index = 4)
    private String applyReason;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    @ExcelProperty(value = "项目id ", index = 5)
    private String projectId;

    /**
     * 剩余材料费
     */
    @ApiModelProperty(value = "剩余材料费")
    @ExcelProperty(value = "剩余材料费 ", index = 6)
    private BigDecimal restMaterialFee;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @ExcelProperty(value = "版本 ", index = 8)
    private String revId;


    @ApiModelProperty(value = "备料信息")
    private List<ProjectMaterialPreparationInfoDTO> preparationInfoList;


}
