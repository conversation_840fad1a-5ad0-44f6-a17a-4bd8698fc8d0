package com.chinasie.orion.repository;

import com.chinasie.orion.domain.vo.resourceAllocation.RepairPlanVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RepairPlanMapper {

    RepairPlanVO queryRepairPlanByRepairRound(@Param("repairRound") String repairRound);

    List<RepairPlanVO> queryRepairPlan(@Param("repairRound") String repairRound);

}
