package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectSchemeDocument Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-26 13:33:08
 */
@ApiModel(value = "ProjectSchemeDocumentVO对象", description = "项目计划关联文档")
@Data
public class ProjectSchemeDocumentVO extends ObjectVO implements Serializable{

    /**
     * res文件Id
     */
    @ApiModelProperty(value = "res文件Id")
    private String fileId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;
}

