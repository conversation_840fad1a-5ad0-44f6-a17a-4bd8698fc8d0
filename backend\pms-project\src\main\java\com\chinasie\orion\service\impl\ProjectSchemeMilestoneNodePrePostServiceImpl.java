package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodePrePostDTO;
import com.chinasie.orion.domain.dto.TemplatePreSchemeDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemeMilestoneNodePrePostMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.service.ProjectSchemeMilestoneNodePrePostService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectSchemeMilestoneNodePrePost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10 14:29:13
 */
@Service
public class ProjectSchemeMilestoneNodePrePostServiceImpl extends OrionBaseServiceImpl<ProjectSchemeMilestoneNodePrePostMapper, ProjectSchemeMilestoneNodePrePost> implements ProjectSchemeMilestoneNodePrePostService {

    @Autowired
    private ClassRedisHelper classRedisHelper;

    /**
     * 删除(批量)
     *
     * @param ids 计划Id
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByIds(List<String> ids) throws Exception {
        return this.removeBatchByIds(ids);
    }

    /**
     * 添加前后置关系(批量)
     *
     * @param prePostDTO 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> createBatch(TemplatePreSchemeDTO prePostDTO) throws Exception {
        List<String> schemeIds = prePostDTO.getSchemeIds();
        if (CollectionUtil.isEmpty(schemeIds)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_SCHEME_NULL);
        }

        if (prePostDTO.getProjectSchemePrePostDTOS().stream()
                .anyMatch(a -> StrUtil.isBlank(a.getPostSchemeId()) && StrUtil.isBlank(a.getPreSchemeId()))) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "存在前后置关系未选择计划");
        }
        if (CollectionUtil.isEmpty(prePostDTO.getProjectSchemePrePostDTOS())) {
            this.remove(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class)
                    .in(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, schemeIds).or()
                    .in(ProjectSchemeMilestoneNodePrePost::getPreSchemeId, schemeIds).or()
                    .in(ProjectSchemeMilestoneNodePrePost::getPostSchemeId, schemeIds));

            return new ArrayList<>();
        }


        this.remove(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class)
                .in(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, schemeIds).or()
                .in(ProjectSchemeMilestoneNodePrePost::getPreSchemeId, schemeIds).or()
                .in(ProjectSchemeMilestoneNodePrePost::getPostSchemeId, schemeIds));
        List<ProjectSchemeMilestoneNodePrePost> list = new ArrayList<>();

        for (String schemeId : schemeIds) {
            for (ProjectSchemeMilestoneNodePrePostDTO nodePrePostDTO : prePostDTO.getProjectSchemePrePostDTOS()) {
                ProjectSchemeMilestoneNodePrePost prePost = new ProjectSchemeMilestoneNodePrePost();
                ProjectSchemeMilestoneNodePrePost relationPrePost = new ProjectSchemeMilestoneNodePrePost();
                if (StrUtil.isNotBlank(nodePrePostDTO.getPreSchemeId())) {
                    prePost.setType(Status.SCHEME_PRE.getCode());
                    prePost.setPreSchemeId(nodePrePostDTO.getPreSchemeId());
                    relationPrePost.setType(Status.SCHEME_POST.getCode());
                    relationPrePost.setProjectSchemeId(nodePrePostDTO.getPreSchemeId());
                    relationPrePost.setPostSchemeId(schemeId);
                } else {
                    prePost.setType(Status.SCHEME_POST.getCode());
                    prePost.setPostSchemeId(nodePrePostDTO.getPostSchemeId());
                    relationPrePost.setType(Status.SCHEME_PRE.getCode());
                    relationPrePost.setProjectSchemeId(nodePrePostDTO.getPostSchemeId());
                    relationPrePost.setPreSchemeId(schemeId);
                }
                prePost.setId(classRedisHelper.getUUID(ProjectSchemeMilestoneNodePrePost.class.getSimpleName()));
                prePost.setTemplateId(nodePrePostDTO.getTemplateId());
                prePost.setProjectSchemeId(schemeId);
                relationPrePost.setId(classRedisHelper.getUUID(ProjectSchemeMilestoneNodePrePost.class.getSimpleName()));
                relationPrePost.setTemplateId(nodePrePostDTO.getTemplateId());
                list.add(prePost);
                list.add(relationPrePost);
            }
        }
        this.saveBatch(list);
        return list.stream().map(ProjectSchemeMilestoneNodePrePost::getId).collect(Collectors.toList());
    }

    /**
     * 变更前置关系，删除后新增
     *
     * @param id          项目计划Id
     * @param prePostDTOS 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> modify(String id, List<ProjectSchemeMilestoneNodePrePostDTO> prePostDTOS) throws Exception {
        List<ProjectSchemeMilestoneNodePrePost> prePostList = this.list(new LambdaQueryWrapper<>(ProjectSchemeMilestoneNodePrePost.class).eq(ProjectSchemeMilestoneNodePrePost::getProjectSchemeId, id));
        List<String> oldPreIds = prePostList.stream().map(ProjectSchemeMilestoneNodePrePost::getId).collect(Collectors.toList());
        this.removeBatchByIds(oldPreIds);
        List<ProjectSchemeMilestoneNodePrePost> prePosts = prePostDTOS.stream().map(pre -> BeanCopyUtils.convertTo(pre, ProjectSchemeMilestoneNodePrePost::new))
                .peek(pre -> {
                    if (StrUtil.isNotBlank(pre.getPreSchemeId())) {
                        pre.setType(Status.SCHEME_PRE.getCode());
                    } else {
                        pre.setType(Status.SCHEME_POST.getCode());
                    }
                }).collect(Collectors.toList());
        this.saveBatch(prePosts);
        return prePosts.stream().map(ProjectSchemeMilestoneNodePrePost::getId).collect(Collectors.toList());
    }

}
