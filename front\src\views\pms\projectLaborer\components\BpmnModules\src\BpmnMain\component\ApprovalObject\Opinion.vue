<template>
  <div class="wrap">
    <div class="title">
      审批意见
    </div>
    <div class="ptb10">
      {{ approvalComments }}
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed, defineComponent, inject, onMounted, reactive, unref,
} from 'vue';
import Api from '/@/api';

export default defineComponent({
  setup() {
    const {
      userId,
      menuActionItem: { procInstId },
    } = inject('bpmnModuleData')?.value;

    const state = reactive({
      recordList: [],
    });
    onMounted(() => {
      load();
    });
    function load() {
      // state.loadingStatus = true;
      new Api('/workflow')
        .fetch(
          {
            userId,
            procInstId,
          },
          'act-inst-detail/journal',
          'GET',
        )
        .then((list) => {
          state.recordList = list || [];
        })
        .finally(() => {
          // state.loadingStatus = false;
        });
    }

    const approvalComments = computed(() => {
      const comment = state.recordList.find((item) => item?.status !== 'CREATED')?.comment;

      return comment || '无';
    });

    return {
      approvalComments,
    };
  },
});
</script>

<style scoped lang="less">
  .wrap {
    margin-top: 20px;
  }
</style>
