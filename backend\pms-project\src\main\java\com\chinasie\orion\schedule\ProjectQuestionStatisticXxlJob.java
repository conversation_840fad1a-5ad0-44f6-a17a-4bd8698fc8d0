package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.projectStatistics.QuestionStatusStatistics;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.QuestionManagementService;
import com.chinasie.orion.service.projectStatistics.QuestionStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ProjectQuestionStatisticXxlJob {
    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired
    private QuestionStatusStatisticsService questionStatusStatisticsService;

    @XxlJob("projectQuestionStatisticDailyCount")
    public void projectQuestionStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<QuestionStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(QuestionStatusStatistics :: getDateStr,nowDate);
        questionStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<QuestionManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(QuestionManagement :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(QuestionManagement :: getStatus);
        schemeLambdaQueryWrapper.groupBy(QuestionManagement :: getProjectId, QuestionManagement :: getQuestionType);
        schemeLambdaQueryWrapper.select(" project_id projectId, question_type questionType," +
                "IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) unFinishedCount ," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as finishedCount," +
                "IFNULL( sum( CASE  WHEN `status`=102 THEN 1 ELSE 0 END ), 0 ) as closeCount");
        List<Map<String, Object>> maps = questionManagementService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<QuestionStatusStatistics> questionStatusStatisticsList = new ArrayList<>();
        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            String type = String.valueOf(p.get("questionType"));
            QuestionStatusStatistics questionStatusStatistics =new QuestionStatusStatistics();
            questionStatusStatistics.setNowDay(new Date());
            questionStatusStatistics.setDateStr(nowDate);
            questionStatusStatistics.setProjectId(projectId);
            questionStatusStatistics.setUk(nowDate+":"+type+":"+projectId);
            questionStatusStatistics.setTypeId(type);
            questionStatusStatistics.setUnFinishedCount(Integer.parseInt(p.get("unFinishedCount").toString()));
            questionStatusStatistics.setFinishedCount(Integer.parseInt(p.get("finishedCount").toString()));
            questionStatusStatistics.setCloseCount(Integer.parseInt(p.get("closeCount").toString()));
            questionStatusStatisticsList.add(questionStatusStatistics);
        });
        questionStatusStatisticsService.saveBatch(questionStatusStatisticsList);
    }
}
