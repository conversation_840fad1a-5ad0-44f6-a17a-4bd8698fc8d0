package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.zhxu.bs.util.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.ContractPayNodeStatusConfirmDTO;
import com.chinasie.orion.domain.dto.ProjectFundsReceivedDTO;
import com.chinasie.orion.domain.entity.ProjectFundsReceived;
import com.chinasie.orion.domain.entity.ProjectReceivable;
import com.chinasie.orion.domain.entity.Stakeholder;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectFundsReceivedMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ProjectFundsReceivedService;
import com.chinasie.orion.service.ProjectReceivableService;
import com.chinasie.orion.service.StakeholderService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectFundsReceived 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:18:18
 */
@Service
public class ProjectFundsReceivedServiceImpl extends OrionBaseServiceImpl<ProjectFundsReceivedMapper, ProjectFundsReceived> implements ProjectFundsReceivedService {

    @Autowired
    private ProjectFundsReceivedMapper projectFundsReceivedMapper;

    @Autowired
    private ProjectReceivableService projectReceivableService;

    @Resource
    private LyraFileBO fileBo;

    @Autowired
    private StakeholderService stakeholderService;

    @Autowired
    private CodeBo codeBo;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    PasFeignService pasFeignService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private SearchHelper searchHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectFundsReceivedVO detail(String id, String pageCode) throws Exception {
        ProjectFundsReceived projectFundsReceived = projectFundsReceivedMapper.selectById(id);
        ProjectFundsReceivedVO result = BeanCopyUtils.convertTo(projectFundsReceived, ProjectFundsReceivedVO::new);
        if (StringUtils.isNotBlank(result.getStakeholderId())) {
            Stakeholder stakeholders = stakeholderService.getById(result.getStakeholderId());
            if (ObjectUtil.isNotEmpty(stakeholders)) {
                result.setStakeholderName(stakeholders.getName());
            }
        }
        if (StrUtil.isNotEmpty(result.getReceivableId())) {
            LambdaQueryWrapperX<ProjectReceivable> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
            lambdaQueryWrapperX1.in(ProjectReceivable::getId, result.getReceivableId());
            ProjectReceivable projectReceivable = projectReceivableService.getOne(lambdaQueryWrapperX1);
            if (ObjectUtil.isNotEmpty(projectReceivable)) {
                result.setReceivableNumber(projectReceivable.getNumber());
            }
        }
        if (StrUtil.isNotBlank(result.getContractNumber()) && StrUtil.isNotBlank(result.getCollectionPoint())) {
            List<ContractPayNodeVO> contractPayNodeVOs = pasFeignService.getContractPayNodeVO(Arrays.asList(result.getCollectionPoint())).getResult();
            Map<String, String> contractPayNodeVOMap = contractPayNodeVOs.stream().collect(Collectors.toMap(ContractPayNodeVO::getId, ContractPayNodeVO::getPayTypeName));

            List<ProjectContractVO> contractVOS = pasFeignService.listByIds(Arrays.asList(result.getContractNumber())).getResult();
            Map<String, String> contractVOMap = contractVOS.stream().collect(Collectors.toMap(ProjectContractVO::getId, ProjectContractVO::getNumber));

            if (StrUtil.isNotBlank(contractPayNodeVOMap.get(result.getCollectionPoint()))) {
                result.setCollectionPointName(contractPayNodeVOMap.get(result.getCollectionPoint()));
            }
            if (StrUtil.isNotBlank(contractVOMap.get(result.getContractNumber()))) {
                result.setContractNumber(contractVOMap.get(result.getContractNumber()));
            }
        }
        if (StrUtil.isNotBlank(result.getCreatorId())) {
            UserVO userVO = userRedisHelper.getUserById(result.getCreatorId());
            if (ObjectUtil.isNotEmpty(userVO)) {
                result.setCreatorName(userVO.getName());
            }
        }
        // 权限设置
        if (StrUtil.isNotBlank(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(result.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), ProjectFundsReceivedVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        }
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectFundsReceivedDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectFundsReceivedVO create(ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {
        ProjectFundsReceived projectFundsReceived = BeanCopyUtils.convertTo(projectFundsReceivedDTO, ProjectFundsReceived::new);
        if (projectFundsReceived.getFundsReceived().compareTo(BigDecimal.ZERO) != 0 && StrUtil.isNotBlank(projectFundsReceived.getReceivableId())) {
            saveProjectReceivable(projectFundsReceived, true);
        }
        String code = "";
        if (StrUtil.isNotBlank(projectFundsReceivedDTO.getNumber())) {
            code = codeBo.createCode(ClassNameConstant.PROJECT_FUNDS_RECEIVED, ClassNameConstant.NUMBER, true, projectFundsReceivedDTO.getNumber());
        } else {
            code = codeBo.createCode(ClassNameConstant.PROJECT_FUNDS_RECEIVED, ClassNameConstant.NUMBER, false, null);
        }
        projectFundsReceived.setNumber(code);
        int insert = projectFundsReceivedMapper.insert(projectFundsReceived);
        ProjectFundsReceivedVO rsp = BeanCopyUtils.convertTo(projectFundsReceived, ProjectFundsReceivedVO::new);
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectFundsReceivedDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(rsp.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTFUNDSRECEIVED_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
            searchHelper.sendDataChangeMessage(rsp.getId());
        }
        return rsp;
    }

    private void saveProjectReceivable(ProjectFundsReceived projectFundsReceived, Boolean isAdd) throws Exception {
        ProjectReceivable projectReceivable = projectReceivableService.getById(projectFundsReceived.getReceivableId());
        if (ObjectUtil.isNotEmpty(projectReceivable)) {
            BigDecimal fundsReceived = BigDecimal.ZERO;
            if (isAdd) {
                fundsReceived = projectReceivable.getFundsReceived().add(projectFundsReceived.getFundsReceived());
            } else {
                fundsReceived = projectReceivable.getFundsReceived().subtract(projectFundsReceived.getFundsReceived());
            }
            BigDecimal noAmountReceived = projectReceivable.getAmountReceivable().subtract(fundsReceived);
            projectReceivable.setFundsReceived(fundsReceived);
            projectReceivable.setNoAmountReceived(noAmountReceived);
            if (noAmountReceived.compareTo(BigDecimal.ZERO) == 1 && fundsReceived.compareTo(BigDecimal.ZERO) == 1) {
                projectReceivable.setStatus(110);
            } else if (noAmountReceived.compareTo(BigDecimal.ZERO) == 1 && fundsReceived.compareTo(BigDecimal.ZERO) == 0) {
                projectReceivable.setStatus(101);
            } else if (noAmountReceived.compareTo(BigDecimal.ZERO) != 1) {
                projectReceivable.setStatus(111);
                if (StringUtils.isNotBlank(projectReceivable.getCollectionPoint())) {
                    ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO = new ContractPayNodeStatusConfirmDTO();
                    contractPayNodeStatusConfirmDTO.setNodeIdList(Arrays.asList(projectReceivable.getCollectionPoint()));
                    contractPayNodeStatusConfirmDTO.setContractNumber(projectReceivable.getContractNumber());
                    ResponseDTO<Boolean> responseDTO = pasFeignService.statusChange(contractPayNodeStatusConfirmDTO);
                    if (ResponseUtils.fail(responseDTO)) {
                        throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "更新合同支付节点状态失败!");
                    }
                }
            }
            projectReceivableService.updateById(projectReceivable);
        }
    }


    /**
     * 编辑
     * <p>
     * * @param projectFundsReceivedDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {

        ProjectFundsReceived projectFundsReceived = BeanCopyUtils.convertTo(projectFundsReceivedDTO, ProjectFundsReceived::new);
        ProjectFundsReceived oldProjectFundsReceived = this.getById(projectFundsReceivedDTO.getId());
        if (ObjectUtil.isEmpty(oldProjectFundsReceived)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在，请仔细检查");
        }
        if (StrUtil.isNotBlank(projectFundsReceived.getReceivableId())) {
            if (oldProjectFundsReceived.getFundsReceived().compareTo(BigDecimal.ZERO) != 0 && StrUtil.isNotBlank(oldProjectFundsReceived.getReceivableId())) {
                saveProjectReceivable(oldProjectFundsReceived, false);
            }
            saveProjectReceivable(projectFundsReceived, true);
        } else {
            if (oldProjectFundsReceived.getFundsReceived().compareTo(BigDecimal.ZERO) != 0 && StrUtil.isNotBlank(oldProjectFundsReceived.getReceivableId())) {
                saveProjectReceivable(oldProjectFundsReceived, false);
            }
        }
        int update = projectFundsReceivedMapper.updateById(projectFundsReceived);
        List<FileDTO> attachments = Optional.ofNullable(projectFundsReceivedDTO.getAttachments()).orElse(new ArrayList<>());
        List<FileVO> oldFiles = fileBo.getFilesByDataId(projectFundsReceived.getId());
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
//        List<String> result = new ArrayList<>();
        attachments.forEach(f -> {
            f.setDataId(projectFundsReceived.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTFUNDSRECEIVED_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }
        searchHelper.sendDataChangeMessage(projectFundsReceived.getId());
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<ProjectFundsReceived> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.in(ProjectFundsReceived::getId, ids);
        List<ProjectFundsReceived> projectFundsReceiveds = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isEmpty(projectFundsReceiveds)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在，请仔细检查");
        }
        for (ProjectFundsReceived projectFundsReceived : projectFundsReceiveds) {
            saveProjectReceivable(projectFundsReceived, false);
        }
        int delete = projectFundsReceivedMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectFundsReceivedVO> pages(Page<ProjectFundsReceivedDTO> pageRequest) throws Exception {
        Page<ProjectFundsReceivedVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), Page::setHeadAuthList, new ArrayList<>());

        Page<ProjectFundsReceived> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectFundsReceived::new));
        LambdaQueryWrapperX<ProjectFundsReceived> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        ProjectFundsReceivedDTO projectFundsReceivedDTO = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(projectFundsReceivedDTO)) {
            lambdaQueryWrapperX.eqIfPresent(ProjectFundsReceived::getProjectId, projectFundsReceivedDTO.getProjectId());
            lambdaQueryWrapperX.eqIfPresent(ProjectFundsReceived::getReceivableId, projectFundsReceivedDTO.getReceivableId());
//            if(StrUtil.isNotBlank(projectFundsReceivedDTO.getName())){
//                lambdaQueryWrapperX.like(ProjectReceivable::getName,projectFundsReceivedDTO.getName()).or().like(ProjectReceivable::getNumber,projectFundsReceivedDTO.getName());
//            }
        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, lambdaQueryWrapperX);
        }
        PageResult<ProjectFundsReceived> page = projectFundsReceivedMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        // Page<ProjectFundsReceivedVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectFundsReceivedVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectFundsReceivedVO::new);
        if (CollectionUtil.isEmpty(vos)) {
            return resultPage;
        }
        //获取客户名称
        List<String> stakeholderIds = vos.stream().map(ProjectFundsReceivedVO::getStakeholderId).collect(Collectors.toList());
        List<Stakeholder> stakeholders = stakeholderService.listByIds(stakeholderIds);
        Map<String, String> stakeholderMap = stakeholders.stream().collect(Collectors.toMap(Stakeholder::getId, Stakeholder::getName));
        //获取应收编码
        List<String> receivableIds = vos.stream().filter(a -> StrUtil.isNotBlank(a.getReceivableId())).map(ProjectFundsReceivedVO::getReceivableId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(receivableIds)) {
            LambdaQueryWrapperX<ProjectReceivable> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
            lambdaQueryWrapperX1.in(ProjectReceivable::getId, receivableIds);
            List<ProjectReceivable> projectReceivables = projectReceivableService.list(lambdaQueryWrapperX1);
            if (CollectionUtil.isNotEmpty(projectReceivables)) {
                Map<String, String> map = projectReceivables.stream().collect(Collectors.toMap(ProjectReceivable::getId, ProjectReceivable::getNumber));
                List<String> stringList = vos.stream().map(ProjectFundsReceivedVO::getCollectionPoint).collect(Collectors.toList());
                List<ContractPayNodeVO> contractPayNodeVOs = pasFeignService.getContractPayNodeVO(stringList).getResult();
                Map<String, String> contractPayNodeVOMap = contractPayNodeVOs.stream().collect(Collectors.toMap(ContractPayNodeVO::getId, ContractPayNodeVO::getPayTypeName));
                vos.forEach(a -> {
                    if (StrUtil.isNotBlank(contractPayNodeVOMap.get(a.getCollectionPoint()))) {
                        a.setCollectionPoint(contractPayNodeVOMap.get(a.getCollectionPoint()));
                    }
                    if (StrUtil.isNotBlank(a.getReceivableId())) {
                        a.setReceivableNumber(map.get(a.getReceivableId()));
                    }
                    if (StrUtil.isNotBlank(a.getStakeholderId())) {
                        a.setStakeholderName(stakeholderMap.get(a.getStakeholderId()));
                    }
                });
            }
        }
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(vos);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), vos, ProjectFundsReceivedVO::getId, ProjectFundsReceivedVO::getDataStatus, ProjectFundsReceivedVO::setRdAuthList,
                ProjectFundsReceivedVO::getCreatorId,
                ProjectFundsReceivedVO::getModifyId,
                ProjectFundsReceivedVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setContent(vos);
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectFundsReceivedVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectFundsReceivedVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public List<ProjectFundsReceivedVO> getList(ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception {
        LambdaQueryWrapperX<ProjectFundsReceived> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eqIfPresent(ProjectFundsReceived::getProjectId, projectFundsReceivedDTO.getProjectId());
        List<ProjectFundsReceived> projectFundsReceiveds = this.list(lambdaQueryWrapperX);
        List<ProjectFundsReceivedVO> projectFundsReceivedVOS = BeanCopyUtils.convertListTo(projectFundsReceiveds, ProjectFundsReceivedVO::new);
        return projectFundsReceivedVOS;
    }


    @Override
    public List<String> importFiles(String id, List<FileDTO> files) throws Exception {
        List<FileVO> oldFiles = fileBo.getFilesByDataId(id);
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
//            List<FileVO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
//        List<String> result = new ArrayList<>();
        files.forEach(f -> {
            f.setDataId(id);
            f.setDataType(FileConstant.FILETYPE_PROJECTFUNDSRECEIVED_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
//        if (CollectionUtil.isNotEmpty(result)) {
//        }
        return fileBo.addBatch(files);
    }


}
