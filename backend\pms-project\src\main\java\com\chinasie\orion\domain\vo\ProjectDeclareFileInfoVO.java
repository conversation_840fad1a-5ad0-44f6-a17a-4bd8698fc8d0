package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectDeclareFileInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 18:02:59
 */
@ApiModel(value = "ProjectDeclareFileInfoVO对象", description = "项目申报文件信息")
@Data
public class ProjectDeclareFileInfoVO extends ObjectVO implements Serializable{

    /**
     * 项目申报id
     */
    @ApiModelProperty(value = "项目申报id")
    private String projectDeclareId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 文件数据id
     */
    @ApiModelProperty(value = "文件数据id")
    private String fileDataId;

}
