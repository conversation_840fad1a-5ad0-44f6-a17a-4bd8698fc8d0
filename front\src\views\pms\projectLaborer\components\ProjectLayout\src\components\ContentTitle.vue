<template>
  <div class="title-wrap">
    <slot name="title">
      <div
        v-if="contentTitle"
        class="title"
      >
        <h3>{{ contentTitle }}</h3>
      </div>
      <BasicTabs
        v-if="$attrs.tabs"
        v-bind="$attrs"
      >
        <template
          v-for="item in Object.keys($slots)"
          #[item]="data"
        >
          <template v-if="item !== 'default'">
            <slot
              v-bind="data"
              :name="item"
            />
          </template>
        </template>
      </BasicTabs>
    </slot>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicTabs,
} from 'lyra-component-vue3';
// import BasicTabs from '/@/components/BasicTabs';
export default defineComponent({
  name: 'ContentTitle',
  components: {
    BasicTabs,
  },
  props: {
    contentTitle: {
      type: String,
      default: '',
    },
  },
});
</script>

<style lang="less" scoped>
  .title {
    height: 50px;
    box-shadow: inset 0 -1px rgba(0, 0, 0, 0.1);

    > h3 {
      height: 50px;
      line-height: 50px;
      border-bottom: 3px solid ~`getPrefixVar('primary-color')`;
      display: inline-block;
      font-size: 16px;
      padding: 0 30px;
      color: ~`getPrefixVar('primary-color')`;
    }
  }

  .title-wrap {
    :deep(.tabs-wrap) {
      margin-bottom: 0;
    }
  }
</style>
