package com.chinasie.orion.service.quality;

import com.chinasie.orion.domain.dto.quality.QualityItemAffirmDTO;

import java.util.List;

public interface QualityItemWFService {

    default Boolean commit(List<String> ids){
        return null;
    }

    default Boolean affirm(List<String> ids){
        return null;
    }

    default Boolean reject(List<String> ids){
        return null;
    }

    default Boolean complete(QualityItemAffirmDTO qualityItemAffirmDTO){
        return null;
    }

    default Boolean correlation(String id, List<String> schemeIds){
        return null;
    }
}
