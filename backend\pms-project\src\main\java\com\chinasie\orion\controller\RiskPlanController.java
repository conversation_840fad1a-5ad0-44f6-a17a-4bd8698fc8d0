package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.vo.RiskPlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.RiskPlanService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/11:23
 * @description:
 */
@RestController
@RequestMapping("/risk-plan")
@Api(tags = "风险预案")
public class RiskPlanController {

    @Resource
    private RiskPlanService riskPlanService;

    @ApiOperation("新增风险预案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskPlanDTO", dataType = "RiskPlanDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增风险预案", type = "风险预案", subType = "新增风险预案", bizNo = "")
    public ResponseDTO<String> saveRiskPlan(@RequestBody RiskPlanDTO riskPlanDTO) throws Exception {
        return new ResponseDTO<>(riskPlanService.saveRiskPlan(riskPlanDTO));
    }

    @ApiOperation("批量新增风险预案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskPlanList", dataType = "RiskPlanDTO")
    })
    @PostMapping(value = "/save/batch/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】批量新增风险预案", type = "风险预案", subType = "批量新增风险预案", bizNo = "{{#projectId}}")
    public ResponseDTO saveBatchRiskPlan(@PathVariable("projectId") String projectId, @RequestBody List<RiskPlanDTO> riskPlanList) throws Exception {
        return new ResponseDTO<>(riskPlanService.saveBatchRiskPlan(projectId, riskPlanList));
    }

    @ApiOperation("同步策划风险")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", dataType = "projectId")
    })
    @PostMapping(value = "/syn/approval/riskPlan/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】同步策划风险", type = "风险预案", subType = "同步策划风险", bizNo = "{{#projectId}}")
    public ResponseDTO synApprovalRiskPlan(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(riskPlanService.synApprovalRiskPlan(projectId));
    }

    @ApiOperation("获取风险预案分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险预案分页", type = "风险预案", subType = "获取风险预案分页", bizNo = "")
    public ResponseDTO<Page<RiskPlanVO>> getRiskPlanPage(@RequestBody Page<RiskPlanDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(riskPlanService.getRiskPlanPage(pageRequest));
    }

    @ApiOperation("获取风险预案详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险预案详情", type = "风险预案", subType = "获取风险预案详情", bizNo = "{{#id}}")
    public ResponseDTO<RiskPlanVO> getRiskPlanDetail(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(riskPlanService.getRiskPlanDetail(id));
    }

    @ApiOperation("编辑风险预案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskPlanDTO", dataType = "RiskPlanDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑风险预案", type = "风险预案", subType = "编辑风险预案", bizNo = "{{#riskPlanDTO.id}}")
    public ResponseDTO<Boolean> editRiskPlan(@RequestBody RiskPlanDTO riskPlanDTO) throws Exception {
        return new ResponseDTO<>(riskPlanService.editRiskPlan(riskPlanDTO));
    }

    @ApiOperation("批量删除风险预案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除风险预案", type = "风险预案", subType = "批量删除风险预案", bizNo = "{{#ids.toString}}")
    public ResponseDTO<Boolean> removeRiskPlan(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(riskPlanService.removeBatchRiskPlan(ids));
    }
}
