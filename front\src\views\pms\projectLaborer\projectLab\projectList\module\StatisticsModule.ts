import Api from '/@/api';
import { unionBy } from 'lodash-es';

export const StatisticsModule = () => {
  const radioMap = new Map();
  radioMap.set('全部', {
    name: '全部',
    class: 'all-radio',
    value: 'all',
    reqNum: 0,
    mapKey: 'allNum',
  });
  radioMap.set('已创建', {
    class: 'create-radio',
    value: '',
    reqNum: '',
    mapKey: 'createNum',
  });
  radioMap.set('执行中', {
    class: 'runtime-radio',
    value: '',
    reqNum: '',
    mapKey: 'executeNum',
  });
  radioMap.set('已验收', {
    class: 'end-radio',
    value: '',
    reqNum: '',
    mapKey: 'receiveNum',
  });
  radioMap.set('项目结束', {
    class: 'end-radio',
    value: '',
    reqNum: '',
    mapKey: 'finishNum',
  });

  const initRadioOption = async () => {
    const res1 = await new Api('/pms/project-task-status/policy/status/list/project').fetch('', '', 'GET');
    const lastRes = [];
    if (res1) {
      res1.forEach((item) => {
        if (radioMap.has(item.name)) {
          const currOpt = radioMap.get(item.name);
          currOpt.value = item.value;
          currOpt.reqNum = 0;
          currOpt.name = item.name;
          radioMap.set(item.name, currOpt);
          lastRes.push(currOpt);
        }
      });
    }
    return unionBy(lastRes, 'value');
  };
  const setRadioOptValue = async (params?:any) => {
    const lastRes = [];
    const res2 = await new Api('/pms/project/getStatusPage').fetch({
      ...(params || {}),
    }, '', 'POST');
    radioMap.forEach((item, key) => {
      item.reqNum = res2[item.mapKey];
      radioMap.set(item.name, item);
      lastRes.push(item);
    });
    return unionBy(lastRes, 'value');
  };

  return {
    initRadioOption,
    setRadioOptValue,
  };
};