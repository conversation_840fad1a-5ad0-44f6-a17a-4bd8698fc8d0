package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProdActionItem DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
@ApiModel(value = "ProdActionItemDTO对象", description = "生产大修行动项")
@Data
@ExcelIgnoreUnannotated
public class ProdActionItemDTO extends  ObjectDTO   implements Serializable{

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 0)
    private String repairRound;

    /**
     * 责任人ID拼接
     */
    @ApiModelProperty(value = "责任人ID拼接")
    @ExcelProperty(value = "责任人ID拼接 ", index = 1)
    private String rspUserIds;

    /**
     * 责任人名称拼接
     */
    @ApiModelProperty(value = "责任人名称拼接")
    @ExcelProperty(value = "责任人名称拼接 ", index = 2)
    private String rspUserNames;

    /**
     * 完成时限
     */
    @ApiModelProperty(value = "完成时限")
    @ExcelProperty(value = "完成时限 ", index = 3)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date finishDeadline;

    /**
     * 维度字典
     */
    @ApiModelProperty(value = "维度字典")
    @ExcelProperty(value = "维度字典 ", index = 4)
    private String dimensionDict;

    /**
     * 验证人ID
     */
    @ApiModelProperty(value = "验证人ID")
    @ExcelProperty(value = "验证人ID ", index = 5)
    private String verifierId;

    /**
     * 验证人名称
     */
    @ApiModelProperty(value = "验证人名称")
    @ExcelProperty(value = "验证人名称 ", index = 6)
    private String verifierName;

    /**
     * 问题描述
     */
    @ApiModelProperty(value = "问题描述")
    @ExcelProperty(value = "问题描述 ", index = 7)
    private String problemDesc;

    @ApiModelProperty(value = "改进措施")
    @ExcelProperty(value = "改进措施 ", index = 8)
    private String updateSolutions;


    /**
     * 责任人ID拼接
     */
    @ApiModelProperty(value = "责任部门ID拼接")
    private String rspDeptIds;

    /**
     * 责任人名称拼接
     */
    @ApiModelProperty(value = "责任部门名称拼接")
    private String rspDeptNames;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileDTO> fileList;
}
