package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MajorPlanParamDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberLevelDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.dto.permission.RoleMemberParamDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MajorRepairPlanMember;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanMemberVO;

import com.chinasie.orion.service.MajorRepairPlanMemberService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.Map;

import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MajorRepairPlanMember 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
@RestController
@RequestMapping("/majorRepairPlanMember")
@Api(tags = "大修计划成员")
public class  MajorRepairPlanMemberController  {

    @Autowired
    private MajorRepairPlanMemberService majorRepairPlanMemberService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<MajorRepairPlanMemberVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairPlanMemberVO rsp = majorRepairPlanMemberService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairPlanMemberDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增用户")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#majorRepairPlanMemberDTO.name}}】", type = "MajorRepairPlanMember", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MajorRepairPlanMemberDTO majorRepairPlanMemberDTO) throws Exception {
        String rsp =  majorRepairPlanMemberService.create(majorRepairPlanMemberDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * @param roleMemberParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量新增-角色添加用户")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增成员数据【{{#majorRepairPlanMemberDTO.name}}】", type = "MajorRepairPlanMember", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> batchAdd(@RequestBody RoleMemberParamDTO roleMemberParamDTO) throws Exception {
        Boolean rsp =  majorRepairPlanMemberService.batchAdd(roleMemberParamDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param majorRepairPlanMemberDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#majorRepairPlanMemberDTO.name}}】", type = "MajorRepairPlanMember", subType = "编辑", bizNo = "{{#majorRepairPlanMemberDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorRepairPlanMemberDTO majorRepairPlanMemberDTO) throws Exception {
        Boolean rsp = majorRepairPlanMemberService.edit(majorRepairPlanMemberDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "MajorRepairPlanMember", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairPlanMemberService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "MajorRepairPlanMember", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairPlanMemberService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "MajorRepairPlanMember", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanMemberVO>> pages(@RequestBody Page<MajorRepairPlanMemberDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanMemberVO> rsp =  majorRepairPlanMemberService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 通过层级分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "层级分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "MajorRepairPlanMember", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/pageByLevel", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanMemberLevelDTO>> pagesByLevel(@RequestBody Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanMemberLevelDTO> rsp =  majorRepairPlanMemberService.pagesByLevel( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取大修下对应角色设置的用户")
    @RequestMapping(value = "/major/plan/role/user/list", method = RequestMethod.POST)
    public ResponseDTO<List<String>> getUserIdListByMajorParamDTO(@RequestBody MajorPlanParamDTO paramDTO){
        return new ResponseDTO<>(majorRepairPlanMemberService.getUserIdListByMajorParamDTO(paramDTO));
    }


    @ApiOperation(value = "获取大修下对应角色设置的用户列表")
    @RequestMapping(value = "/major/plan/role/code/user/list", method = RequestMethod.POST)
    public ResponseDTO<Map<String,List<String>>> getRoleCodeToUserIdListByMajorParamDTO(@RequestBody MajorPlanParamDTO paramDTO){
        return new ResponseDTO<>(majorRepairPlanMemberService.getRoleCodeToUserIdListByMajorParamDTO(paramDTO));
    }
}
