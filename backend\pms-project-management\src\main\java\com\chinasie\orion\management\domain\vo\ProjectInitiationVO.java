package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectInitiation VO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
@ApiModel(value = "ProjectInitiationVO对象", description = "项目立项")
@Data
public class ProjectInitiationVO extends ObjectVO implements Serializable {

    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    private String projectNumber;


    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    private String projectName;


    /**
     * 立项标签
     */
    @ApiModelProperty(value = "立项标签")
    private String projectLabel;

    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    private String projectType;


    /**
     * 立项状态
     */
    @ApiModelProperty(value = "立项状态")
    private String projectStatus;


    /**
     * 项目发起日期
     */
    @ApiModelProperty(value = "项目发起日期")
    private Date projectInitDate;


    /**
     * 项目开始日期
     */
    @ApiModelProperty(value = "项目开始日期")
    private Date projectStartDate;


    /**
     * 项目结束日期
     */
    @ApiModelProperty(value = "项目结束日期")
    private Date projectEndDate;


    /**
     * 项目责任人
     */
    @ApiModelProperty(value = "项目责任人")
    private String projectPerson;


    /**
     * 承担中心
     */
    @ApiModelProperty(value = "承担中心")
    private String projectAssumeCenter;

    /**
     * 项目责任人id
     */
    @ApiModelProperty(value = "项目责任人ID")
    private String projectPersonId;

    /**
     * 承担中心id
     */
    @ApiModelProperty(value = "承担中心id")
    private String projectAssumeCenterId;


    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    private String projectReson;


    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    private String contractNumbers;


    /**
     * 线索编号拼接
     */
    @ApiModelProperty(value = "线索编号拼接")
    private String clueNumbers;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String companyCode;

    /**
     * 项目的公司代码
     */
    @ApiModelProperty(value = "项目的公司代码")
    private String projectCompanyCode;

    /**
     * 项目公司名称
     */
    @ApiModelProperty(value = "项目公司名称")
    private String projectCompanyName;

    /**
     * 委托方代码1
     */
    @ApiModelProperty(value = "委托方代码1")
    private String clientOne;

    /**
     * 中文客户名称1
     */
    @ApiModelProperty(value = "中文客户名称1")
    private String clientOneName;

    /**
     * 委托方代码2
     */
    @ApiModelProperty(value = "委托方代码2")
    private String clientTwo;

    /**
     * 中文客户名称2
     */
    @ApiModelProperty(value = "中文客户名称2")
    private String clientTwoName;

    /**
     * 委托方代码3
     */
    @ApiModelProperty(value = "委托方代码3")
    private String clientThree;

    /**
     * 中文客户名称3
     */
    @ApiModelProperty(value = "中文客户名称3")
    private String clientThreeName;

    /**
     * 核电委托方
     */
    @ApiModelProperty(value = "核电委托方")
    private String nuclearClient;

    /**
     * 火电委托方
     */
    @ApiModelProperty(value = "火电委托方")
    private String fireClient;

    /**
     * 风电委托方
     */
    @ApiModelProperty(value = "风电委托方")
    private String windClient;

    /**
     * 其他委托方
     */
    @ApiModelProperty(value = "其他委托方")
    private String otherClient;

    /**
     * 外部合同号
     */
    @ApiModelProperty(value = "外部合同号")
    private String outContractNumber;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "立项时间")
    private Date initiationTime;

    /**
     * 销售合同号（原始）
     */
    @ApiModelProperty(value = "销售合同号（原始）")
    private String originalNumber;

}
