package com.chinasie.orion.domain.vo.job;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/21:34
 * @description:
 */
@Data
public class JobEquivalentInfoVO  implements Serializable {

    @ApiModelProperty(value = "岗位等效关系ID")
    private String id;
    @ApiModelProperty(value = "基地编码")
    private String baseCode;
    @ApiModelProperty(value = "基地名称")
    private String baseName;

    @ApiModelProperty(value = "岗位编号")
    private String jobCode;
    @ApiModelProperty(value = "岗位名称")
    private String jobName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    private Date endDate;
    /**
     * 授权状态（100-未授权，111-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    private Integer authorizeStatus;

    /**
     * 授权状态（100-未授权，111-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）名称")
    private String authorizeStatusName;
}
