package com.chinasie.orion.manager;

import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.vo.ProjectSchemePrePostVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;

import java.util.List;
import java.util.Map;

public interface SchemeCommonManager {

    List<ProjectScheme> listProjectSchemeByProjectId(String projectId, List<List<SearchCondition>> searchConditions, Boolean isManager, List<OrderItem> orderItems);

    Map<String, List<ProjectSchemePrePost>> initPreMap(List<String> schemeIds) throws Exception;

//    void codeMapping(ProjectSchemeVO projectSchemeVO);

    void codeMapping(List<ProjectSchemeVO> projectSchemeVOList);

    void checkTime(ProjectScheme pScheme, ProjectSchemeDTO projectSchemeDTO);

    List<ProjectSchemePrePostVO> getPreScheme(String projectId, String projectSchemeId);

    List<ProjectSchemePrePostVO> getPostScheme(String projectId, String projectSchemeId);

//    boolean completedInvest(String projectId) throws Exception;

    List<ProjectScheme> filterView(List<ProjectScheme> schemeList, SchemeListTypeEnum typeEnum) throws Exception;

    List<ProjectScheme> getList(List<ProjectScheme> filterList, List<ProjectScheme> allList) throws Exception;

    List<ProjectScheme> listByProjectId(String projectId, String type);

    void codeMapping1(List<ProjectSchemeVO> content);

    List<ProjectScheme> filterateByUser(List<ProjectScheme> projectSchemes, String type);
}
