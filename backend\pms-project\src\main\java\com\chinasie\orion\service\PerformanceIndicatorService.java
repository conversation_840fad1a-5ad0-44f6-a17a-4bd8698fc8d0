package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.PerformanceIndicatorDTO;
import com.chinasie.orion.domain.entity.PerformanceIndicator;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.vo.PerformanceIndicatorVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PerformanceIndicator 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
public interface PerformanceIndicatorService  extends OrionBaseService<PerformanceIndicator>{
        /**
         *  详情
         *
         * * @param id
         */
        PerformanceIndicatorVO detail(String id)  throws Exception;

        /**
         *  新增
         *
         * * @param performanceIndicatorDTO
         */
        PerformanceIndicatorVO create(PerformanceIndicatorDTO performanceIndicatorDTO)  throws Exception;

        /**
         *  编辑
         *
         * * @param performanceIndicatorDTO
         */
        Boolean edit(PerformanceIndicatorDTO performanceIndicatorDTO) throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         */
        Page<PerformanceIndicatorVO> pages(Page<PerformanceIndicatorDTO> pageRequest) throws Exception;

}
