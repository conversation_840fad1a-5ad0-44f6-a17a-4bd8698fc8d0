package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/18/11:11
 * @description:
 */
@Data
public class PlanResourceVo implements Serializable {



    @ApiModelProperty(value = "关系主键id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "工号")
    private String number;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "部门名称")
    private String dept;

    @ApiModelProperty(value = "成本")
    private Integer costNum;
    @ApiModelProperty(value = "数量")
    private Integer resourceCount;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;
    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人id")
    private String modifyId;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人名称")
    private String modifyName;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者人名称")
    private String creatorName;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;


}
