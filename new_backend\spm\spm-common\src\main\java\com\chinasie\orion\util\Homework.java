package com.chinasie.orion.util;

import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys  实列demo
 * @date: 2024/11/13/15:40
 * @description:
 */
public class Homework {

    private String id ;

    private String name;
    @StatisticField(value="oneErro" , type = StatisticType.SUM)
    private Integer oneErro;
    @StatisticField(value="twoErro" , type =StatisticType.SUM)
    private Integer twoErro;
    @StatisticField(value="threeErro" , type = StatisticType.SUM)
    private Integer threeErro;
    @StatisticField(value="fourErro" , type = StatisticType.PERCENTAGE,fields = { "twoErro","threeErro"})
    private Double fourErro;

    public Double getFourErro() {
        return fourErro;
    }

    public void setFourErro(Double fourErro) {
        this.fourErro = fourErro;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOneErro() {
        return oneErro;
    }

    public void setOneErro(Integer oneErro) {
        this.oneErro = oneErro;
    }

    public Integer getTwoErro() {
        return twoErro;
    }

    public void setTwoErro(Integer twoErro) {
        this.twoErro = twoErro;
    }

    public Integer getThreeErro() {
        return threeErro;
    }

    public void setThreeErro(Integer threeErro) {
        this.threeErro = threeErro;
    }

    public Homework(String id, String name, Integer oneErro, Integer twoErro, Integer threeErro,Double fourErro) {
        this.id = id;
        this.name = name;
        this.oneErro = oneErro;
        this.twoErro = twoErro;
        this.threeErro = threeErro;
        this.fourErro=fourErro;
    }



}
