package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CostShare DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@ApiModel(value = "CostShareDTO对象", description = "成本分摊")
@Data
@ExcelIgnoreUnannotated
public class CostShareDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @ExcelProperty(value = "项目Id ", index = 0)
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码 ", index = 1)
    private String projectNumber;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @ExcelProperty(value = "公司Id ", index = 2)
    private String companyId;

    @ApiModelProperty(value = "公司编码")
    private String companyNumber;

    /**
     * 集团内外
     */
    @ApiModelProperty(value = "集团内外")
    @ExcelProperty(value = "集团内外 ", index = 3)
    private String internalExternal;

    /**
     * 核电
     */
    @ApiModelProperty(value = "核电")
    @ExcelProperty(value = "核电 ", index = 4)
    private String nuclearPower;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @ExcelProperty(value = "基地 ", index = 5)
    private String base;

    /**
     * WBS对象
     */
    @ApiModelProperty(value = "WBS对象")
    @ExcelProperty(value = "WBS对象 ", index = 6)
    private String wbsObject;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @ExcelProperty(value = "年度 ", index = 7)
    private Integer year;

    @ApiModelProperty(value = "WBS所属利润中心")
    private String wbsExpertiseCenter;
    /**
     * WBS所属专业中心
     */
    @ApiModelProperty(value = "WBS所属专业中心")
    @ExcelProperty(value = "WBS所属专业中心 ", index = 8)
    private String wbsProfessionalCenter;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @ExcelProperty(value = "业务分类 ", index = 9)
    private String businessClassification;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @ExcelProperty(value = "金额 ", index = 10)
    private BigDecimal amount;

    /**
     * 分摊分类
     */
    @ApiModelProperty(value = "分摊分类")
    @ExcelProperty(value = "分摊分类 ", index = 11)
    private String apportionmentClassification;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @ExcelProperty(value = "成本类型 ", index = 12)
    private String costType;

    /**
     * 成本元素大类
     */
    @ApiModelProperty(value = "成本元素大类")
    @ExcelProperty(value = "成本元素大类 ", index = 13)
    private String costElementCategorie;

    /**
     * 成本元素
     */
    @ApiModelProperty(value = "成本元素")
    @ExcelProperty(value = "成本元素 ", index = 14)
    private String costElement;

    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    @ExcelProperty(value = "发送部门 ", index = 15)
    private String sendDeptId;

    /**
     * 源发送部门
     */
    @ApiModelProperty(value = "源发送部门")
    @ExcelProperty(value = "源发送部门 ", index = 16)
    private String sourceSendDept;

    /**
     * 期间
     */
    @ApiModelProperty(value = "期间")
    @ExcelProperty(value = "期间 ", index = 17)
    private String period;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value = "凭证编码")
    @ExcelProperty(value = "凭证编码 ", index = 18)
    private String credentialCode;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 19)
    private Date voucherDate;

    /**
     * 科目代码
     */
    @ApiModelProperty(value = "科目代码")
    @ExcelProperty(value = "科目代码 ", index = 20)
    private String subjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @ExcelProperty(value = "科目名称 ", index = 21)
    private String subejctName;

    /**
     * 凭证利润中心代码
     */
    @ApiModelProperty(value = "凭证利润中心代码")
    @ExcelProperty(value = "凭证利润中心代码 ", index = 22)
    private String voucherProfitCenterCode;

    /**
     * 凭证利润中心名称
     */
    @ApiModelProperty(value = "凭证利润中心名称")
    @ExcelProperty(value = "凭证利润中心名称 ", index = 23)
    private String voucherProfitCenterName;

    /**
     * 凭证成本中心代码
     */
    @ApiModelProperty(value = "凭证成本中心代码")
    @ExcelProperty(value = "凭证成本中心代码 ", index = 24)
    private String voucherCostCenterCode;

    /**
     * 凭证成本中心名称
     */
    @ApiModelProperty(value = "凭证成本中心名称")
    @ExcelProperty(value = "凭证成本中心名称 ", index = 25)
    private String voucherCostCenterName;


    @ApiModelProperty(value = "对象名称")
    @ExcelProperty(value = "对象名称 ", index = 17)
    private String wbsObjectName;

    @ApiModelProperty(value = "级别")
    private String type;

    @ApiModelProperty(value = "搜索条件")
    List<List<SearchCondition>> searchConditions;

    @ApiModelProperty(value = "ids")
    List<String> ids;




}
