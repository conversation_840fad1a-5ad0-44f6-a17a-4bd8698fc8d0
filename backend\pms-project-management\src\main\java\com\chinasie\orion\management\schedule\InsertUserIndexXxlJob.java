package com.chinasie.orion.management.schedule;

import com.chinasie.orion.management.service.ProjectGraphService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 插入人员指标数据
 */
@Component
public class InsertUserIndexXxlJob {
    @Autowired
    private ProjectGraphService projectGraphService;
    @XxlJob("InsertUserIndexJobHandler")
    public void changeStatus() throws Exception {
        //在场人数
        projectGraphService.insertAttendeesData();
        //离场人数
        projectGraphService.insertExitData();
        //黑名单人数
        projectGraphService.insertBlacklistData();
        //需求计划人数
        projectGraphService.insertPlanningData();
        //预算执行率
        projectGraphService.insertRateData();
    }
}
