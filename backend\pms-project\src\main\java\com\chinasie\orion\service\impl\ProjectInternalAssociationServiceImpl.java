package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ProjectInternalAssociationDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectInternalAssociation;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationVO;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectInternalAssociationMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ProjectInternalAssociationService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.lang.String;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * ProjectInternalAssociation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16 14:25:49
 */
@Service
public class ProjectInternalAssociationServiceImpl extends OrionBaseServiceImpl<ProjectInternalAssociationMapper, ProjectInternalAssociation> implements ProjectInternalAssociationService {

    @Autowired
    private ProjectInternalAssociationMapper projectInternalAssociationMapper;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private InternalAssociationRedisHelper internalAssociationRedisHelper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public ProjectInternalAssociationVO detail(String id) throws Exception {
        ProjectInternalAssociation projectInternalAssociation =projectInternalAssociationMapper.selectById(id);
        ProjectInternalAssociationVO result = BeanCopyUtils.convertTo(projectInternalAssociation,ProjectInternalAssociationVO::new);
        return result;
    }

    @Override
    public List<ProjectInternalAssociationRedisVO> list(List<String> ids) throws Exception {
        if(CollectionUtils.isBlank(ids)){
            return null;
        }
        List<ProjectInternalAssociation> list = this.listByIds(ids);
        return BeanCopyUtils.convertListTo(list, ProjectInternalAssociationRedisVO :: new);
    }

    /**
     *  新增
     *
     * * @param projectInternalAssociationDTO
     */
    @Override
    public  ProjectInternalAssociationVO create(ProjectInternalAssociationDTO projectInternalAssociationDTO) throws Exception {
        ProjectInternalAssociation projectInternalAssociation =BeanCopyUtils.convertTo(projectInternalAssociationDTO,ProjectInternalAssociation::new);
        Project project= projectService.getById(projectInternalAssociationDTO.getProjectId());
        if(project != null){
            projectInternalAssociation.setProjectName(project.getName());
        }


        LambdaQueryWrapperX<ProjectInternalAssociation> associationLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        associationLambdaQueryWrapperX.eq(ProjectInternalAssociation :: getInnerId, projectInternalAssociationDTO.getInnerId());
        associationLambdaQueryWrapperX.eqIfPresent(ProjectInternalAssociation :: getProjectId, projectInternalAssociationDTO.getProjectId());
         List<ProjectInternalAssociation> list =  projectInternalAssociationMapper.selectList(associationLambdaQueryWrapperX);
         if(CollectionUtils.isBlank(list)){
             int insert = projectInternalAssociationMapper.insert(projectInternalAssociation);
         }
         else{
             ProjectInternalAssociation oldProjectInternalAssociation = list.get(0);
             projectInternalAssociationDTO.setId(oldProjectInternalAssociation.getId());
             projectInternalAssociation = BeanCopyUtils.convertTo(projectInternalAssociationDTO,ProjectInternalAssociation::new);
             int update =  projectInternalAssociationMapper.updateById(projectInternalAssociation);
         }

        ProjectInternalAssociationVO rsp = BeanCopyUtils.convertTo(projectInternalAssociation,ProjectInternalAssociationVO::new);
        internalAssociationRedisHelper.cacheInternalAssociationEntity(projectInternalAssociation);
        return rsp;
    }



    /**
         *  编辑
         *
         * * @param projectInternalAssociationDTO
         */
    @Override
    public Boolean edit(ProjectInternalAssociationDTO projectInternalAssociationDTO) throws Exception {
        ProjectInternalAssociation projectInternalAssociation =BeanCopyUtils.convertTo(projectInternalAssociationDTO,ProjectInternalAssociation::new);
        int update =  projectInternalAssociationMapper.updateById(projectInternalAssociation);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = projectInternalAssociationMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProjectInternalAssociationVO> pages(Page<ProjectInternalAssociationDTO> pageRequest) throws Exception {
        Page<ProjectInternalAssociation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInternalAssociation::new));

        PageResult<ProjectInternalAssociation> page = projectInternalAssociationMapper.selectPage(realPageRequest,null);

        Page<ProjectInternalAssociationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectInternalAssociationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectInternalAssociationVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
