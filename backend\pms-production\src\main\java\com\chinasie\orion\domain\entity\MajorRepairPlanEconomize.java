package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * is_continue_use
 * @author: wys
 * @date: 2024/06/12/14:54
 * @description:
 */

@TableName(value = "pmsx_major_repair_plan_economize")
@ApiModel(value = "MajorRepairPlanEconomizeEntity对象", description = "大修计划关键路径节约")
@Data

public class MajorRepairPlanEconomize extends ObjectEntity implements Serializable {

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_manage_id")
    private String jobManageId;

    /**
     * 集体剂量是否降低
     */
    @ApiModelProperty(value = "集体剂量是否降低")
    @TableField(value = "is_reduce")
    private Boolean isReduce;

    /**
     * 结项日期
     */
    @ApiModelProperty(value = "结项日期")
    @TableField(value = "close_date")
    private Date closeDate;

    /**
     * 关键路径是否节约
     */
    @ApiModelProperty(value = "关键路径是否节约")
    @TableField(value = "is_economize")
    private Boolean isEconomize;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 优化领域
     */
    @ApiModelProperty(value = "优化领域")
    @TableField(value = "optimize_field")
    private String optimizeField;

    /**
     * 大修类型
     */
    @ApiModelProperty(value = "大修类型")
    @TableField(value = "major_repair_type")
    private String majorRepairType;

    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    @TableField(value = "application_crew")
    private String applicationCrew;

    /**
     * 计划工期(H)
     */
    @ApiModelProperty(value = "计划工期(H)")
    @TableField(value = "plan_duration")
    private BigDecimal planDuration;

    /**
     * 实际执行用时(H)
     */
    @ApiModelProperty(value = "实际执行用时(H)")
    @TableField(value = "actual_exe_duration")
    private BigDecimal actualExeDuration;

    /**
     * 节约(H)
     */
    @ApiModelProperty(value = "节约(H)")
    @TableField(value = "economize_duration")
    private BigDecimal economizeDuration;

    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    @TableField(value = "content")
    private String content;

    /**
     * 延误原因
     */
    @ApiModelProperty(value = "延误原因")
    @TableField(value = "delay_reason")
    private String delayReason;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "job_manage_number")
    private String jobManageNumber;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "major_repair_turn")
    private String majorRepairTurn;

    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    @TableField(value = "inn_tech_or_work")
    private String innTechOrWork;

    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    @TableField(value = "is_continue_use")
    private Boolean isContinueUse;
}
