<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower, BasicScrollbar, openDrawer,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect, ComputedRef, h,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import { message, Modal } from 'ant-design-vue';
import { openFormDrawer } from './utils';
import AddTableModal from './components/AddTableModal.vue';
import AddTableModalLur from './components/AddTableModalLur.vue';
import AddTableFlowModal from './components/AddTableFlowModal.vue';
import BasicInfo from './modal/BasicInfo.vue';
import Api from '/@/api';
import Process from '/@/views/pms/components/Process.vue';
import AssociationPlan from '/@/views/pms/components/associationPlan/associationPlan.vue';
import AssociationRisk from '/@/views/pms/components/associationRisk/associationRisk.vue';
import AssociationAlter from '/@/views/pms/components/associationAlter/associationAlter.vue';
import AssociationReview from '/@/views/pms/components/associationReview/associationReview.vue';
interface DetailsDataType {
  id?: string,
  name?: string,
  className?: string,
  projectCode?: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,
}

const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const powerData: Ref = ref({});
const planPowerCode = {
  addCode: 'PMS_WTGLXQ_container_02_button_01',
  deleteCode: 'PMS_WTGLXQ_container_02_button_02',
  checkCode: 'PMS_WTGLXQ_container_02_button_03',
};
const riskPowerCode = {
  addCode: 'PMS_WTGLXQ_container_03_button_01',
  quoteCode: 'PMS_WTGLXQ_container_03_button_02',
  removeCode: 'PMS_WTGLXQ_container_03_button_03',
  editCode: 'PMS_WTGLXQ_container_03_button_03',
  deleteCode: 'PMS_WTGLXQ_container_03_button_05',
  checkCode: 'PMS_WTGLXQ_container_03_button_06',
};
const alterPowerCode = {
  addCode: 'PMS_WTGLXQ_container_05_button_01',
  editCode: 'PMS_WTGLXQ_container_05_button_02',
  deleteCode: 'PMS_WTGLXQ_container_05_button_03',
  checkCode: 'PMS_WTGLXQ_container_05_button_04',
  closeCode: 'PMS_WTGLXQ_container_05_button_05',
};
const reviewPowerCode = {
  addCode: 'PMS_WTGLXQ_container_07_button_01',
  removeCode: 'PMS_WTGLXQ_container_07_button_02',
};
provide('powerData', powerData);
const detailsData: Ref<DetailsDataType> = ref({});
provide('formData', computed(() => detailsData.value));
const riskName = ref('');
const workflowActionRef = ref();
const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
  Api,
  businessData: detailsData.value,
  afterEvent: async () => {
    await getDetails();
    //  processViewRef.value?.init();
  },
}));
const menuData: Ref<any[]> = ref([]);

function menuChange({ id }) {
  actionId.value = id;
  // console.log(actionId.value);
}

onMounted(() => {
  getDetails();
});
provide('getDetails', getDetails);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  const result: Record<string, any> = await new Api('/pms').fetch({
    pageCode: 'detail-container-5e5296-RStTRGxJJ',
  }, `question-management/detail/${dataId.value}`, 'GET');
  loading.value = false;
  // powerData.value = result.detailAuthList;
  result.projectCode = result.number;
  result.ownerName = result.creatorName;
  detailsData.value = result;
  riskName.value = result.riskName;
  workflowActionRef.value?.setProps({
    businessData: detailsData.value,
  });
  if (!actionId.value) {
    if (route.query.tabsType === 'process') {
      actionId.value = 'process';
    } else {
      actionId.value = 'basicInfo';
    }
  }

  getPlanDetail();
}
// refDrawer
const refDrawer = ref();
const actions = computed(() => [
  // {
  //   text: '添加流程',
  //   icon: 'sie-icon-add',
  //   isShow: computed(() => workflowActionRef.value?.isAdd && isPower('PMS_WTGLXQ_container_04_button_01_01', powerData.value)),
  //   onClick() {
  //     workflowActionRef.value?.onAddTemplate({
  //       messageUrl: route.fullPath,
  //     });
  //   },
  // },
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => isPower('PMS_WTGLXQ_container_04_button_01_02', powerData.value),
    onClick() {
      openFormDrawer(AddTableModal, detailsData.value, getDetails, 'modal');

      // if (detailsData.value.status === 101) {
      //   openFormDrawer(AddTableModal, detailsData.value, getDetails, 'modal');
      // } else if (detailsData.value.questionType === 'questionType_1') {
      //   openFormDrawer(AddTableFlowModal, { id: dataId.value }, getDetails, 'flow');
      // } else {
      //   openFormDrawer(AddTableModal, { id: dataId.value }, getDetails, 'modal');
      // }
    },
  },
  {
    text: '执行记录',
    isShow: () => isPower('PMS_WTGLXQ_container_04_button_01_03', powerData.value),
    onClick() {
      openDrawer({
        title: '执行记录',
        content(h) {
          return h(AddTableModalLur, {
            ref: refDrawer,
            data: detailsData.value,
          });
        },
        onOk() {
          refDrawer.value.handleSubmit();
          getDetails();
        },
        onCancel() {
          getDetails();
        },
      });
    },
  },
  {
    text: '激活',
    // icon: 'sie-icon-bianji',
    isShow: () => isPower('PMS_WTGLXQ_container_04_button_01_04', powerData.value) && detailsData.value.status === 160,
    onClick() {
      Modal.confirm({
        title: '激活提示',
        content: '是否激活当前的数据',
        onOk() {
          new Api('/pms').fetch([dataId.value], 'question-management/open', 'PUT').then((res) => {
            message.success('激活成功。');
            getDetails();
          });
        },

      });
    },
  },
  {
    text: '关闭',
    // icon: 'sie-icon-bianji',
    isShow: () => isPower('PMS_WTGLXQ_container_04_button_01_05', powerData.value) && detailsData.value.status === 130,
    onClick() {
      Modal.confirm({
        title: '关闭提示',
        content: '是否关闭当前的数据',
        onOk() {
          new Api('/pms').fetch([dataId.value], 'question-management/close', 'PUT').then((res) => {
            message.success('关闭成功。');
            getDetails();
          });
        },
      });
    },
  },
]);
// 执行记录
const handleSubmit = async () => {
  // if (submitLoading.value) return;

  // const res = await formRef.value.validate();
  // if (res) {
  //   submitLoading.value = true;
  //   const params = form.record.map((item) => ({
  //     content: item.value,
  //     projectId: data.value?.projectId,
  //     projectSchemeId: data.value.id,
  //   }));
  //   const addRes = await new Api('/pms/schemeContent/createBatch').fetch(
  //     params,
  //     '',
  //     'POST',
  //   );
  //   if (addRes) {
  //     message.success('添加成功');
  //     context.emit('update');
  //     handleClose();
  //   }
  //   submitLoading.value = false;
  // }
};

// 关联计划
async function getPlanTableDataApi(params) {
  return new Api(`/pas/question-management/relation/plan/${detailsData.value.id}`).fetch(params, '', 'POST');
}
async function deletePlanBatchApi(params) {
  return new Api('/pas').fetch(params, 'question-management/relation/plan/batch', 'DELETE');
}
async function addPlanTableApi(params) {
  return new Api('/pas').fetch(params, 'question-management/relation/plan', 'POST');
}
// 关联风险
async function getRiskTableDataApi(params) {
  params.query = {
    questionId: detailsData.value.id,
  };
  return new Api('/pas').fetch(params, 'questionRelationRisk/relationRisk/page', 'POST');
}

// 关联变更
async function getAlterTableDataApi(params) {
  return new Api('/pas').fetch({
    ...params,
    query: {
      dataSourceId: detailsData.value.id,
    },
  }, 'ecr/questionRelationEcr/page', 'POST');
}
async function addAlterTableApi(params) {
  return new Api('/pas').fetch(params, 'ecr/questionRelationEcr/createEcr', 'POST');
}
// 关联评审
async function getReviewTableDataApi(params) {
  if (!detailsData.value.questionSource) {
    return Promise.resolve([]);
  }
  let res = await new Api('/pms').fetch(params, `review/${detailsData.value.questionSource}`, 'GET');
  return [res];
}
async function deleteReviewBatchApi(ids) {
  return new Api('/pms').fetch('', `question-management/relation/reviewForm?reviewFormId=${ids[0]}&questionId=${detailsData.value.id}`, 'DELETE');
}
async function addReviewTableApi(data) {
  return new Api('/pms').fetch('', `question-management/relation/reviewForm?questionId=${detailsData.value.id}&reviewFormId=${data[0].id}`, 'POST');
}

function getPowerDataHandle(data) {
  powerData.value = data;
  menuData.value = [
    {
      id: 'basicInfo',
      name: '基本信息',
      isShow: isPower('PMS_WTGLXQ_container_01', powerData),
    },
    //   {
    //     name: '关联内容',
    //     id: 'content',
    //     children: [
    //       {
    //         name: '关联计划',
    //         id: 'plan',
    //         isShow: isPower('PMS_WTGLXQ_container_02', powerData),
    //       },
    //       {
    //         name: '关联风险',
    //         id: 'risk',
    //         isShow: isPower('PMS_WTGLXQ_container_03', powerData),
    //       },
    //       // {
    //       //   name: '关联变更',
    //       //   id: 'changeApply',
    //       //   isShow: isPower('PMS_WTGLXQ_container_05', powerData),
    //       // },
    //       // {
    //       //   name: '评审单',
    //       //   id: 'review',
    //       //   isShow: isPower('PMS_WTGLXQ_container_07', powerData),
    //       // },
    //     ].filter((item) => typeof item?.isShow === 'undefined' || item?.isShow),
    //   },
    //   {
    //     name: '流程',
    //     id: 'process',
    //     isShow: isPower('PMS_WTGLXQ_container_08', powerData),
    //   },
  ].filter((item) => typeof item?.isShow === 'undefined' || item?.isShow);
  actionId.value = menuData.value[0].id;
}

const getPlanDetail = async () => {
  const res = await new Api('/pms/questionReplyManagement/getListByNumber').fetch(
    { questionNumber: detailsData.value.number },
    '',
    'POST',
  );
  detailsData.value.planDetail = res;
};

</script>

<template>
  <Layout3
    v-get-power="{ pageCode: 'PMS7450', getPowerDataHandle }"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="detailsData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <div
      v-loading="loading"
      class="layout-content"
    >
      <BasicInfo
        v-if="'basicInfo' === actionId && detailsData.id"
        :riskName="riskName"
      />
      <AssociationPlan
        v-if="actionId === 'plan'"
        :formId="detailsData.id"
        :getPlanTableDataApi="getPlanTableDataApi"
        :deletePlanBatchApi="deletePlanBatchApi"
        :addPlanTableApi="addPlanTableApi"
        page-type="question"
        :power-code="planPowerCode"
      />
      <AssociationRisk
        v-if="actionId === 'risk'"
        :formId="detailsData.id"
        page-type="question"
        :getRiskTableDataApi="getRiskTableDataApi"
        :power-code="riskPowerCode"
      />
      <AssociationAlter
        v-if="actionId === 'changeApply'"
        :formId="detailsData.id"
        page-type="question"
        :getAlterTableDataApi="getAlterTableDataApi"
        :addAlterTableApi="addAlterTableApi"
        :power-code="alterPowerCode"
      />
      <AssociationReview
        v-if="actionId === 'review'"
        :formId="detailsData.id"
        page-type="question"
        selectType="radio"
        :getReviewTableDataApi="getReviewTableDataApi"
        :deleteReviewBatchApi="deleteReviewBatchApi"
        :addReviewTableApi="addReviewTableApi"
        :power-code="reviewPowerCode"
      />
      <Process v-if="actionId === 'process'" />
    </div>
    <template #footer>
      <!-- <WorkflowAction
        v-if="detailsData.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      /> -->
    </template>
  </Layout3>
</template>

<style scoped lang="less">
.layout-content {
  height: 100%;
  padding-top: 1px;
}
</style>
