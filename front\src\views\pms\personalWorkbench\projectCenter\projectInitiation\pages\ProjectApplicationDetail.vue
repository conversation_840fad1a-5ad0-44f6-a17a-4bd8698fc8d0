<script setup lang="ts">
import {
  BasicButton, Layout3, Layout3Content, useDrawer,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, onMounted, provide, readonly, ref, Ref, unref,
} from 'vue';
import { Spin, Empty } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import ProjectInfo from './components/ProjectInfo.vue';
import MeetingMinutes from './components/MeetingMinutes.vue';
import RelatedAccessories from './components/RelatedAccessories.vue';
import Achievement from './modal/Achievement.vue';
import Resource from './modal/Resource.vue';
import Milestone from './components/milestone.vue';

import { declarationData, declarationDataId, updateDeclarationData } from './keys';
import Api from '/@/api';
import CreateAndEditDrawer from '../components/CreateAndEditDrawer/Index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const route = useRoute();
// 立项数据id
const dataId: Ref<string> = ref(route.params.id as string);
provide(declarationDataId, readonly(dataId));
const projectData: Ref = ref();
provide(declarationData, projectData);
const defaultActionId: Ref<string> = ref('lXXI');
const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: {
    ...projectData.value,
    name: projectData.value.projectName,
  },
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));
const loading: Ref<boolean> = ref(false);
// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && projectData.value?.status === 101);
// 详情顶部数据
const layoutData = computed(() => ({
  name: projectData.value?.projectName,
  ownerName: projectData.value?.resUserName,
  dataStatus: projectData.value?.dataStatus,
  projectCode: projectData.value?.projectNumber,
}));
interface MenuItem{
  id:string,
  name:string,
  children?:MenuItem[]
}

const menuData: Ref<MenuItem[]> = ref([
  {
    id: 'lXXI',
    name: '立项信息',
  },
  {
    id: 'lCB',
    name: '里程碑计划',
  },
  {
    id: 'cGZS',
    name: '成果策划',
  },
  {
    id: 'zYCH',
    name: '资源策划',
  },
  {
    id: 'xGFJ',
    name: '相关附件',
  },
  {
    id: 'sPLC',
    name: '审批流程',
  },
]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectApproval').fetch('', unref(dataId), 'GET');
    // 获取附件
    const query = {
      dataId: unref(dataId),
      dataType: 'ProjectApprovalFile',
    };
    const materialList = await new Api('/res/manage/file/new/getfilelist').fetch(query, '', 'GET');
    result.materialList = materialList;
    projectData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey, result.name);
  } finally {
    loading.value = false;
  }
}

provide(updateDeclarationData, getDetailData);

function menuChange(option: { id: string, index: number, item: MenuItem }): void {
  defaultActionId.value = option.id;
}

// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}
const isLoading = ref(false);
// 立项完成
function standOk() {
  isLoading.value = true;
  new Api('/pms/projectApproval/completeProjectApproval').fetch({ id: route.params.id }, '', 'GET').then(() => {
  }).finally(() => {
    isLoading.value = false;
  });
}
</script>

<template>
  <Layout3
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicButton
        v-if="projectData?.status===101"
        icon="sie-icon-bianji"
        type="primary"
        @click="openCreateAndEdit(true,{id:projectData?.id})"
      >
        编辑
      </BasicButton>
      <BasicButton
        v-if="showWorkflowAdd"
        type="primary"
        icon="sie-icon-qidongliucheng"
        @click="handleAddWorkflow"
      >
        发起流程
      </BasicButton>
      <BasicButton
        v-if="defaultActionId==='lCB'"
        type="primary"
        :loading="isLoading"
        @click="standOk"
      >
        立项完成
      </BasicButton>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-pc flex-ac"
    >
      <Spin />
    </div>
    <Layout3Content v-else>
      <!--立项信息-->
      <ProjectInfo v-if="defaultActionId==='lXXI'" />
      <!--里程碑计划-->
      <Milestone v-if="defaultActionId==='lCB'" />
      <!--成果策划-->
      <Achievement v-if="defaultActionId==='cGZS'" />
      <!--资源策划-->
      <Resource v-if="defaultActionId==='zYCH'" />
      <!--相关附件-->
      <RelatedAccessories v-if="defaultActionId==='xGFJ'" />
      <!--审批流程-->
      <WorkflowView
        v-if="defaultActionId==='sPLC'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </Layout3Content>

    <!--编辑立项-->
    <CreateAndEditDrawer
      :onConfirmCallback="getDetailData"
      @register="registerCreateAndEdit"
    />
  </Layout3>
</template>

<style scoped lang="less">

</style>
