<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PlanRepository">

    <select id="getPlanGroupByType" resultType="java.util.Map">
    SELECT
		p.plan_type AS planTypeId,
		p.STATUS statusId,
		count( p.id ) AS count
	FROM
		pms_plan p
	WHERE
		p.project_id = #{projectId}
	AND p.plan_type != '0'
	AND p.logic_status != - 1
	GROUP BY
		p.plan_type,
		p.STATUS
    </select>

    <select id="getPrincipalIdAndName" resultType="java.util.Map">
	SELECT
		principal_id AS principalId,
		principal_name AS principalName
	FROM
		pms_plan pp
	WHERE
		pp.logic_status = 1
	AND pp.project_id = #{projectId}
	AND pp.plan_type != '0'
	GROUP BY
		principal_id,
		principal_name
	</select>

    <select id="getCountPrincipalId" resultType="java.util.Map">
	SELECT
		COUNT(*) AS count,
		`status`
	FROM
		pms_plan pp
	WHERE
		pp.logic_status = 1
		AND pp.principal_id = #{principalId}
		AND pp.project_id = #{projectId}
		AND pp.plan_type != '0'
	GROUP BY
		`status`
	</select>


    <select id="getPlanEverydayIncreasedStatistic" resultType="java.util.Map">
		SELECT
		COUNT(*) AS count,
		DATE ( create_time ) AS createTime
		FROM
		pms_plan
		WHERE
		project_id = #{projectId}
		AND logic_status = 1
		AND plan_type != '0'
		AND create_time  &gt;=  ( NOW()- interval 14 day )
		AND create_time  &lt;= NOW()
		GROUP BY
		DATE ( create_time )
		ORDER BY
		DATE (
		create_time)
	</select>

</mapper>
