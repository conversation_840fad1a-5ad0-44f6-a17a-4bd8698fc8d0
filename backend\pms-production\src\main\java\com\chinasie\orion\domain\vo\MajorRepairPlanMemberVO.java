package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MajorRepairPlanMember VO对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
@ApiModel(value = "MajorRepairPlanMemberVO对象", description = "大修计划成员")
@Data
public class MajorRepairPlanMemberVO extends  ObjectVO   implements Serializable{

            /**
         * 用户编码（工号）
         */
        @ApiModelProperty(value = "用户编码（工号）")
        private String userCode;


        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String majorRepairTurn;


        /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID")
        private String userId;


        /**
         * 用户名称
         */
        @ApiModelProperty(value = "用户名称")
        private String userName;


        /**
         * 业务ID--角色业务ID
         */
        @ApiModelProperty(value = "业务ID--角色业务ID")
        private String businessId;
        @ApiModelProperty(value = "部门编码")
        private String deptCode;
        @ApiModelProperty(value = "部门名称")
        private String deptName;
        @ApiModelProperty(value = "电话")
        private String mobile;
        @ApiModelProperty(value = "邮箱")
        private String email;
        @ApiModelProperty(value = "性别")
        private String sex;

}
