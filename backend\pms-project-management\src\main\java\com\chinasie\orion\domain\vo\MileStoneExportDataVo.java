package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class MileStoneExportDataVo implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;


    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopic;



    @ApiModelProperty("流程发起人工号")
    @ExcelProperty(value = "发起人工号 ", index = 1)
    private String flowCreatePersonNumber;

    @ApiModelProperty("流程发起人")
    @ExcelProperty(value = "发起人姓名 ", index = 2)
    private String flowCreatePersonName;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty("流程状态")
    private Integer contractStatus;


    @ApiModelProperty("里程碑状态")
    private Integer status;

    @ApiModelProperty("合同执行状态")
    @ExcelProperty(value = "合同执行状态 ", index = 27)
    private String contractStatusName;


    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    private String parentId;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承担部门")
    private String undertDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    @ExcelProperty(value = "承担部门 ", index = 4)
    private String undertDeptName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "销售合同号")
    @ExcelProperty(value = "销售合同号 ", index = 5)
    private String contractNumber;


    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 6)
    private String contractName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户")
    @ExcelProperty(value = "客户名称 ", index = 7)
    private String cusPersonName;


    @ApiModelProperty(value = "币种")
    private String currency;


    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 8)
    private String currencyName;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @ExcelProperty(value = "合同金额 ", index = 9)
    private BigDecimal contractAmt;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    private String custSaleBusType;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    @ExcelProperty(value = "销售业务分类 ", index = 10)
    private String custSaleBusTypeName;



    @ApiModelProperty(value = "合同预计收入")
    @ExcelProperty(value = "合同预计收入 ", index = 11)
    private BigDecimal milestoneAmt;


    /**
     * 里程碑名称shui'k
     */
    @ApiModelProperty(value = "里程碑内容")
    @ExcelProperty(value = "里程碑内容 ", index = 13)
    private String milestoneName;


    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "计划验收日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "付款日期 ", index = 14)
    private Date planAcceptDate;


    @ApiModelProperty(value = "流程发起日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "流程发起时间", index = 15)
    private Date flowStartTime;

    @ApiModelProperty(value = "流程结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "流程结束时间", index = 16)
    private Date flowEndTime;

    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    private String departmental;

    /**
     * 所级
     */
    @ApiModelProperty(value = "所级")
    private String officeDept;

    /**
     * 技术接口人--所级名称
     */
    @ApiModelProperty(value = "所级--所级名称")
    @ExcelProperty(value = "所级", index = 17)
    private String departmentalName;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "业务类型")
    @FieldBind(dataBind = DictDataBind.class, type = "cos_business_type", target = "costBusTypeName")
    private String costBusType;

    /**
     * 成本业务分类名称
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型", index = 18)
    private String costBusTypeName;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率", index = 19)
    private String taxRateName;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级负责人")
    @ExcelProperty(value = "所级负责人", index = 20)
    private String officeLeaderName;


    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人", index = 21)
    private String creatorName;


    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间", index = 22)
    private Date createTime;

    @ApiModelProperty(value = "里程碑编号")
    @ExcelProperty(value = "里程碑编号", index = 12)
    private String number;

    @ApiModelProperty(value = "项目编号（立项）")
    @ExcelProperty(value = "项目编号（立项）", index = 23)
    private String projectCode;

    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "项目负责人", index = 24)
    private String projectPerson;


    @ApiModelProperty(value = "客户合同编号")
    @ExcelProperty(value = "客户合同编号", index = 25)
    private String customerContractNumber;



    @ApiModelProperty(value = "合同签订时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "合同签订时间", index = 26)
    private Date signTime;

    @ApiModelProperty("里程碑状态")
    @ExcelProperty(value = "里程碑状态 ", index = 28)
    private String statusName;

    @ApiModelProperty("里程碑调整维护记录")
    @ExcelProperty(value = "里程碑调整维护记录 ", index = 29)
    private String isHaveLog;


    @ApiModelProperty(value = "初始预估验收金额")
    @ExcelProperty(value = "初始预估验收金额 ", index = 30)
    private BigDecimal exceptAcceptanceAmt;


    @ApiModelProperty(value = "初始预估验收日期")
    @ExcelProperty(value = "初始预估验收日期 ", index = 31)
    private Date expectAcceptDate;

    @ApiModelProperty(value = "实际验收金额")
    @ExcelProperty(value = "实际验收金额 ", index = 32)
    private BigDecimal actualMilestoneAmt;


    @ApiModelProperty(value = "实际验收日期")
    @ExcelProperty(value = "实际验收日期 ", index = 33)
    private Date actualAcceptDate;

    @ApiModelProperty(value = "确认收入金额")
    @ExcelProperty(value = "确认收入金额 ", index = 34)
    private BigDecimal confirmIncomeSum;

    @ApiModelProperty(value = "剩余未确认收入")
    @ExcelProperty(value = "剩余未确认收入 ", index = 35)
    private BigDecimal noConfirmIncomeSum;

    @ApiModelProperty(value = "金额类型")
    private String ammountType;


    @ApiModelProperty(value = "已确认开票收入")
    @ExcelProperty(value = "已确认开票收入 ", index = 36)
    private BigDecimal confirmIncomeInvoicing;


    @ApiModelProperty(value = "已确认暂估收入")
    @ExcelProperty(value = "已确认暂估收入 ", index = 37)
    private BigDecimal confirmIncomeProvisionalEstimate;

    @ApiModelProperty(value = "已预收款开票金额")
    @ExcelProperty(value = "已预收款开票金额 ", index = 38)
    private BigDecimal milestoneAdvanceAmt;

    @ApiModelProperty("流程审批状态")
    @ExcelProperty(value = "流程审批状态 ", index = 3)
    private String flowStatus;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;


    /**
     * 合同技术负责人
     */
    @ApiModelProperty(value = "合同技术负责人")
    private String contractTechRes;

}
