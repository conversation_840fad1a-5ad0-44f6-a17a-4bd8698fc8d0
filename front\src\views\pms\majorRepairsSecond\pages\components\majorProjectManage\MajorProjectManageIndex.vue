<script setup lang="ts">
import dayjs from 'dayjs';
import Api from '/@/api';
import {
  BasicButton, downloadByData, OrionTable, randomString,
} from 'lyra-component-vue3';
import { h, inject, ref } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { get } from 'lodash-es';
import { useRoute, useRouter } from 'vue-router';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { openFormDrawerOrModal } from '../../utils/util';
import MajorProjectForm from './childComponent/MajorProjectForm.vue';
import { useMajorProject } from '/@/views/pms/majorRepairsSecond/pages/components/hooks/useMajorProject';

const route = useRoute();
const router = useRouter();
const tableRef = ref();
const detailsData: Record<string, any> = inject('detailsData');

const pageSearchConditions = ref({});
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  smallSearchField: ['projectName', 'rspUserName'],
  columns: [
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      customRender({ text, record }) {
        return h('span', {
          class: 'flex-te action-btn',
          title: text,
          onClick: () => handleToDetail(record),
        }, text);
      },
    },
    {
      title: '项目负责人',
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'deptName',
    },
    {
      title: '作业数',
      dataIndex: 'jobCount',
    },
    {
      title: '工作包审查进展',
      dataIndex: 'packageSchedule',
    },
    {
      title: '项目完成进度',
      dataIndex: 'schedule',
      customRender({ text, record }) {
        return h('div', {
          class: 'action-btn flex-te',
          onClick: () => record?.schedule !== '0%' && openMajorProjectModal({
            projectId: record?.id,
            projectName: record?.projectName,
            isSchedule: true,
            record,
          }, {
            repairRound: detailsData?.repairRound,
          }),
        }, {
          default: () => text,
        });
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'planStart',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'planEnd',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际开始日期',
      dataIndex: 'actureStart',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际结束日期',
      dataIndex: 'actureEnd',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
  ],
  api: (params: Record<string, any>) => {
    const searchConditionBody = {
      ...params,
      query: {
        repairRound: detailsData?.repairRound,
      },
    };
    pageSearchConditions.value = searchConditionBody;
    return new Api('/pms/importantProject').fetch(searchConditionBody, 'page', 'POST');
  },
  actions: [
    {
      text: '查看',
      event: 'view',
      isShow: true,
      onClick: (record) => handleToDetail(record),
    },
    {
      text: '编辑',
      event: 'edit',
      isShow: true,
      onClick(record) {
        handleUpdateMajorProject({
          title: '编辑项目',
          ...record,
        });
      },
    },
    {
      text: '移除',
      event: 'delete',
      isShow: true,
      onClick(record) {
        if (get(record, 'jobCount')) {
          message.info('该重大项目已选择重大项目作业，请移除后操作！');
          return;
        }
        Modal.confirm({
          title: '提示',
          icon: h(ExclamationCircleOutlined),
          content: h('div', { style: 'color:red;' }, '确定移除这条数据！'),
          onOk() {
            handleRemoveMajorProjectById(record);
          },
          onCancel() {
          },
          class: 'test',
        });
      },
    },
  ],
};

function handleRemoveMajorProjectById(record) {
  return new Promise((resolve, reject) => {
    new Api('/pms/importantProject/remove')
      .fetch([record.id], '', 'DELETE')
      .then((res) => {
        message.success('已成功移除');
        refreshTable();
        resolve('');
      })
      .catch((e) => {
        reject('');
      });
  });
}

function handleUpdateMajorProject(record) {
  openFormDrawerOrModal(MajorProjectForm, {
    position: 'right',
    repairRound: detailsData?.repairRound,
    ...record,
  }, () => {
    refreshTable();
  });
}

function handleToDetail({ id }) {
  router.push({
    name: 'MajorProjectManageDetail',
    params: {
      id,
    },
    query: {
      random: randomString(5),
      repairId: route.params.id,
    },
  });
}

function refreshTable() {
  tableRef.value?.reload?.();
}

async function handleExportApi() {
  Modal.confirm({
    title: '系统提示！',
    content: '确认导出所选数据？',
    onOk() {
      return new Promise((resolve) => {
        downloadByData('/pms/importantProject/export/excel', {
          ...pageSearchConditions.value,
        }).then(() => {
          resolve('');
        }).catch((e) => {
          resolve(e);
        });
      });
    },
  });
}

const { openMajorProjectModal } = useMajorProject();

</script>

<template>
  <div class="major-project-manage">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="handleUpdateMajorProject({ title: '新增项目'})"
        >
          添加
        </BasicButton>
        <BasicButton
          icon="sie-icon-daochu"
          @click="handleExportApi"
        >
          导出
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.major-project-manage {
  height: 500px;
  overflow: hidden;
}
</style>
