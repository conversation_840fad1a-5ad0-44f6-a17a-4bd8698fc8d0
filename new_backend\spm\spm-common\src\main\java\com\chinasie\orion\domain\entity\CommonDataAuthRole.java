package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * CommonDataAuthRole Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-23 15:09:06
 */
@TableName(value = "pmsx_common_data_auth_role")
@ApiModel(value = "CommonDataAuthRoleEntity对象", description = "通用数据权限")
@Data

public class CommonDataAuthRole extends  ObjectEntity  implements Serializable{



    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField(value = "business_type")
    private String businessType;


    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @TableField(value = "data_id")
    private String dataId;

    /**
     * 授权对象：Role:User
     */
    @ApiModelProperty(value = "授权对象：Role:User")
    @TableField(value = "auth_object")
    private String authObject;

    /**
     * 权限code：read,edit
     */
    @ApiModelProperty(value = "权限code：read,write")
    @TableField(value = "permission_code")
    private String permissionCode;

    /**
     * 对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID
     */
    @ApiModelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID")
    @TableField(value = "object_value")
    private String objectValue;

}
