<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>io.github.classgraph</groupId>
	<artifactId>classgraph</artifactId>
	<version>4.1.7</version>
	<name>ClassGraph</name>

	<description>
	Uber-fast, ultra-lightweight Java classpath and module path scanner (formerly named FastClasspathScanner). https://github.com/classgraph/classgraph
	</description>

	<properties>
		<javadocHtmlVersion /> <!-- Overridden in JDK9+ -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<issueManagement>
		<url>https://github.com/classgraph/classgraph/issues</url>
	</issueManagement>

	<licenses>
		<license>
			<name>The MIT License (MIT)</name>
			<url>http://opensource.org/licenses/MIT</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<developers>
		<developer>
			<name>Luke Hutchison</name>
			<email><EMAIL></email>
			<organization>--</organization>
			<organizationUrl>https://github.com/lukehutch</organizationUrl>
		</developer>
	</developers>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>

		<repository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<!-- See http://java.dzone.com/articles/deploy-maven-central and http://central.sonatype.org/pages/apache-maven.html -->
	<build>
		<plugins>
			<plugin>
				<artifactId>maven-enforcer-plugin</artifactId>
				<version>1.0-beta-1</version>
				<dependencies>
					<dependency>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>animal-sniffer-enforcer-rule</artifactId>
						<version>1.17</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>check-signatures</id>
						<phase>test</phase>
						<goals>
							<goal>enforce</goal>
						</goals>
						<configuration>
							<rules>
								<checkSignatureRule implementation="org.codehaus.mojo.animal_sniffer.enforcer.CheckSignatureRule">
									<signature>
										<groupId>org.codehaus.mojo.signature</groupId>
										<artifactId>java17</artifactId>
										<version>1.0</version>
									</signature>
								</checkSignatureRule>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<!-- In the future, rather than using ModiTect to compile module-info.java, 
					could use two execute rules: https://maven.apache.org/plugins/maven-compiler-plugin/examples/module-info.html -->
				<groupId>org.moditect</groupId>
				<artifactId>moditect-maven-plugin</artifactId>
				<version>1.0.0.Beta1</version>
				<executions>
					<execution>
						<id>add-module-infos</id>
						<phase>package</phase>
						<goals>
							<goal>add-module-info</goal>
						</goals>
						<configuration>
							<overwriteExistingFiles>true</overwriteExistingFiles>
							<module>
								<moduleInfoFile>
									src/moditect/module-info.java
								</moduleInfoFile>
							</module>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<doclint>none</doclint>
							<additionalOptions>${javadocHtmlVersion}</additionalOptions>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<archive>
						<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
						</manifest>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<executions>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<artifactId>maven-release-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>build-helper-maven-plugin</artifactId>
					<version>3.0.0</version>
					<executions>
						<execution>
							<id>add-test-source</id>
							<phase>generate-test-sources</phase>
							<goals>
								<goal>add-test-source</goal>
							</goals>
							<configuration>
								<sources>
									<source>src/test/perf</source>
								</sources>
							</configuration>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins</groupId>
										<artifactId>maven-enforcer-plugin</artifactId>
										<versionRange>[1.0.0,)</versionRange>
										<goals>
											<goal>enforce</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<execute>
											<runOnIncremental>false</runOnIncremental>
										</execute>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
				<plugin>
					<!-- This plugin must be inside pluginManagement -->
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.8.0</version>
					<configuration>
						<source>1.7</source>
						<target>1.7</target>
						<encoding>UTF-8</encoding>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.0</version>
				</plugin>
				<plugin>
					<groupId>org.apache.felix</groupId>
					<artifactId>maven-bundle-plugin</artifactId>
					<version>3.5.1</version>
					<extensions>true</extensions>
					<configuration>
						<instructions>
							<Bundle-Category>Utilities</Bundle-Category>
							<Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
							<Bundle-Description>${project.description}</Bundle-Description>
							<Bundle-Vendor>Luke Hutchison</Bundle-Vendor>
						</instructions>
					</configuration>
				</plugin>
				<plugin>
					<!-- This plugin doesn't work inside pluginManagement, for some reason -->
					<groupId>org.sonatype.plugins</groupId>
					<artifactId>nexus-staging-maven-plugin</artifactId>
					<version>1.6.8</version>
					<extensions>true</extensions>
					<configuration>
						<serverId>ossrh</serverId>
						<nexusUrl>https://oss.sonatype.org/</nexusUrl>
						<autoReleaseAfterClose>true</autoReleaseAfterClose>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-release-plugin</artifactId>
					<version>2.5.3</version>
					<configuration>
						<autoVersionSubmodules>true</autoVersionSubmodules>
						<useReleaseProfile>false</useReleaseProfile>
						<releaseProfiles>release</releaseProfiles>
						<goals>deploy</goals>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.openjdk.jmh</groupId>
			<artifactId>jmh-core</artifactId>
			<version>1.19</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.openjdk.jmh</groupId>
			<artifactId>jmh-generator-annprocess</artifactId>
			<version>1.19</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
			<version>3.10.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>javax.enterprise</groupId>
			<artifactId>cdi-api</artifactId>
			<version>1.0-SP4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.ops4j.pax.url</groupId>
			<artifactId>pax-url-aether</artifactId>
			<version>2.5.4</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.8.0-beta2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-jdk14</artifactId>
			<version>1.8.0-beta2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.1-api</artifactId>
			<version>1.0.2.Final</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<scm>
		<connection>scm:git:**************:classgraph/classgraph.git</connection>
		<developerConnection>scm:git:**************:classgraph/classgraph.git</developerConnection>
		<url>https://github.com/classgraph/classgraph</url>
		<tag>classgraph-4.1.7</tag>
	</scm>
	<url>https://github.com/classgraph/classgraph</url>

	<profiles>
		<!-- JDK9+ -->
		<profile>
			<id>java9</id>
			<properties>
				<javadocHtmlVersion>-html5</javadocHtmlVersion>
				<!-- coverall version 4.3.0 does not work with java 9, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
				<coveralls.skip>true</coveralls.skip>
			</properties>
			<activation>
				<jdk>[9,)</jdk>
			</activation>
		</profile>
		<profile>
			<id>release</id>
			<activation>
				<property>
					<name>performRelease</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.5</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
				<pluginManagement>
					<plugins>
						<plugin>
							<!-- This plugin must be inside pluginManagement -->
							<groupId>org.apache.maven.plugins</groupId>
							<artifactId>maven-compiler-plugin</artifactId>
							<configuration>
								<source>1.7</source>
								<target>1.7</target>
								<encoding>UTF-8</encoding>
							</configuration>
							<executions>
								<execution>
									<id>default-testCompile</id>
									<phase>test-compile</phase>
									<goals>
										<goal>testCompile</goal>
									</goals>
									<configuration>
										<!-- fork is needed if using different java versions for main and 
											test, see: https://goo.gl/l0qZKb -->
										<fork>true</fork>
										<executable>${testCompileJdkPath}/bin/javac</executable>
										<source>1.7</source>
										<target>1.7</target>
										<encoding>UTF-8</encoding>
									</configuration>
								</execution>
							</executions>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
		</profile>
	</profiles>
</project>
