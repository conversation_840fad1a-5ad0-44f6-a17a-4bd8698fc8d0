package com.chinasie.orion.domain.dto.source;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.min;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:50
 * @description:
 */
@Data
public class SearchDTO implements Serializable {
    @ApiModelProperty(value = "大修伦次")
    @NotEmpty(message = "大修伦次不能为空")
    private String repairRound;
    @ApiModelProperty(value = "关键词")
    private String keyword;
    @ApiModelProperty(value = "年度")
    @NotNull(message = "年度不能为空")
    private Integer yearNum;
    @ApiModelProperty(value = "季度")
    private Integer quarterNum;

}
