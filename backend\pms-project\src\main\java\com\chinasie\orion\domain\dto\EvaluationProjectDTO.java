package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@ApiModel(value = "EvaluationProjectDTO对象", description = "项目评价")
@Data
public class EvaluationProjectDTO extends ObjectDTO implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 项目评价名称
     */
    @ApiModelProperty(value = "项目评价名称")
    @NotEmpty(message = "项目评价名称不能为空")
    private String name;

    /**
     * 评价类型
     */
    @ApiModelProperty(value = "评价类型")
    @NotEmpty(message = "评价类型不能为空")
    private String evaluationType;

    /**
     * 项目评价责任人id
     */
    @ApiModelProperty(value = "项目评价责任人id")
    @NotEmpty(message = "项目评价责任人id不能为空")
    private String evaluationPersonId;

    /**
     * 发起项目评价时间
     */
    @ApiModelProperty(value = "发起项目评价时间")
//    @NotEmpty(message = "发起项目评价日期不能为空")
    private Date evaluationTime;

    /**
     * 评价对象
     */
    @ApiModelProperty(value = "评价对象")
    private String evaluationObject;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 项目经理id
     */
    @ApiModelProperty(value = "项目经理id")
    private String projectManagerId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;



}
