package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;

/**
 * ProjectMaterialPreparation Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:41:46
 */
@TableName(value = "pms_project_material_preparation")
@ApiModel(value = "ProjectMaterialPreparationEntity对象", description = "备料与加工申请")
@Data

public class ProjectMaterialPreparation extends  ObjectEntity  implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @TableField(value = "apply_dept")
    @FieldBind(dataBind = DeptDataBind.class, target = "applyDeptName")
    private String applyDept;

    @ApiModelProperty(value = "申请部门名称")
    @TableField(exist = false)
    private String applyDeptName;

    /**
     * 产品/电路编码
     */
    @ApiModelProperty(value = "产品/电路编码")
    @TableField(value = "material_number")
    private String materialNumber;

    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    @TableField(value = "require_complete_time")
    private Date requireCompleteTime;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    @TableField(value = "apply_reason")
    private String applyReason;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 剩余材料费
     */
    @ApiModelProperty(value = "剩余材料费")
    @TableField(value = "rest_material_fee")
    private BigDecimal restMaterialFee;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "rev_id")
    private String revId;

    /**
     * 是否是主版本
     */
    @ApiModelProperty(value = "是否是主版本")
    @TableField(value = "is_main_rev")
    private Boolean isMainRev;

    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    @TableField(value = "rev_key")
    private String revKey;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField(value = "audit_time")
    private Date auditTime;

    @TableField(exist = false)
    private Integer preparationNum;

}

