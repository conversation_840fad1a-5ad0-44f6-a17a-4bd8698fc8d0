<template>
  <div
    v-show="!loading && echartsData.length"
    ref="echartsRef"
    class="echarts_wrap"
  />
  <div
    v-if="loading"
    class="echarts_wrap flex flex-pac"
  >
    <Spin />
  </div>
  <div
    v-else-if="echartsData.length===0"
    class="echarts_wrap flex flex-pac"
  >
    <Empty />
  </div>
</template>
<script setup lang="ts">
import {
  computed, ref, onMounted, Ref, watch, onUpdated,
} from 'vue';
import * as echarts from 'echarts';
import { Empty } from 'lyra-component-vue3';
import { Spin } from 'ant-design-vue';
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});
const loading: Ref<boolean> = ref(true);
const echartsRef = ref();
const echartsData = ref([]);

onMounted(() => {
  initCharts();
});
onUpdated(() => {
  let myChart = echarts.init(echartsRef.value);
  myChart.resize();
});
watch(() => props.data, (newVal) => {
  echartsData.value = newVal;
  loading.value = false;
  initCharts();
});

function initCharts() {
  let myChart = echarts.init(echartsRef.value);
  // 绘制图表
  myChart.setOption(chartOption.value);
}

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'none',
    },

  },
  grid: {
    top: 20,
    left: 0,
    right: 0,
    bottom: 20,
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: echartsData.value.map((item) => item.name),
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}个',
    },

  },
  series: [
    {
      data: echartsData.value.map((item) => item.number),
      type: 'bar',
      barWidth: 50,
      label: {
        show: false,
        position: 'top',
      },
    },
  ],
}));
</script>
<style scoped lang="less">
.echarts_wrap{
  width: 100%;
  height: 300px;
}
</style>
