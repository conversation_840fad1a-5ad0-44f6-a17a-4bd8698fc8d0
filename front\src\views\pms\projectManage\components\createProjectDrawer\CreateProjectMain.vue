<template>
  <BasicForm
    layout="vertical"
    @register="registerForm"
  >
    <template #customSlot>
      <SearchCode v-if="showSearchCode" />
    </template>
  </BasicForm>

  <!--  选择项目计划-->
  <SelectProPlanIndex @register="registerModelPlan" />
  <!--  选择预算编码-->
  <SelectCodeModelIndex @register="registerModelCode" />
  <!--  选择负责人-->
  <SelectUserModal
    :on-ok="selectUserChange"
    @register="registerModelUser"
  />
</template>

<script setup lang="ts">
import {
  BasicForm, useForm, useModal, SelectUserModal,
} from 'lyra-component-vue3';
import { onMounted, reactive, ref } from 'vue';
import { getDictType } from '/@/views/pms/api';
import dayjs from 'dayjs';
import SelectProPlanIndex from '../selectProPlanModel/SelectProPlanIndex.vue';
import SelectCodeModelIndex from '../selectCodeModel/SelectCodeModelIndex.vue';
import SearchCode from './SearchCode.vue';

const emit = defineEmits(['onFormInit']);

const state = reactive({
  deptOptions: [],
});

const [registerModelPlan, { openModal: openModelPlan }] = useModal();
const [registerModelCode, { openModal: openModelCode }] = useModal();
const [registerModelUser, { openModal: openModelUser }] = useModal();

onMounted(() => {
  emit('onFormInit', formMethods);
});

const showSearchCode = ref(false);
// 类型数据
const typeList = ref([]);

const [registerForm, formMethods] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      colProps: {
        span: 12,
      },
      label: '项目名称:',
      rules: [
        {
          required: true,
          trigger: 'blur',
          type: 'string',
        },
      ],
      componentProps: {
        placeholder: '请输入项目名称',
        maxlength: 100,
      },
    },
    {
      field: 'basePlanMap',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '综合计划:',
      // rules: [
      //   { required: true, trigger: 'change', type: 'array' },
      // ],
      componentProps: {
        open: false,
        showArrow: false,
        placeholder: '请选择项目计划',
        mode: 'multiple',
        onFocus(e) {
          e.target.blur();
          openModelPlan(true, {});
        },
      },
    },
    {
      field: 'budget',
      component: 'RadioGroup',
      colProps: {
        span: 12,
      },
      label: '是否有预算编码:',
      rules: [
        {
          required: true,
          trigger: 'change',
          type: 'boolean',
        },
      ],
      defaultValue: true,
      componentProps: {
        options: [
          {
            label: '有预算编码',
            value: true,
          },
          {
            label: '无预算编码',
            value: false,
          },
        ],
      },
    },
    {
      field: 'estimateMoney',
      component: 'InputNumber',
      colProps: {
        span: 12,
      },
      label: '预估金额:',
      rules: [
        {
          required: true,
          trigger: 'blur',
          type: 'number',
        },
      ],
      componentProps: {
        placeholder: '请输入项目名称',
        maxlength: 14,
      },
    },
    {
      field: 'budgetMap',
      component: 'InputSearch',
      colProps: {
        span: 12,
      },
      label: '预算编码:',
      rules: [{ required: true }],
      ifShow: ({ model }) => model?.budget ?? false,
      componentProps: {
        placeholder: '请输入预算编码',
        onSearch(val) {
          if (val) {
            showSearchCode.value = true;
          }
        },
      },
    },
    {
      field: 'budgetMoney',
      component: 'InputNumber',
      colProps: {
        span: 12,
      },
      label: '预算金额:',
      ifShow: ({ model }) => model?.budget ?? false,
      componentProps: {
        placeholder: '请输入预算金额',
      },
    },
    {
      component: 'Input',
      colSlot: 'customSlot',
      colProps: {
        span: 24,
      },
    },
    {
      field: 'resOrg',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '项目责任公司:',
      rules: [{ required: true }],
      componentProps: {
        placeholder: '请选择项目责任公司',
      },
    },
    {
      field: 'resDept',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '项目责任部门:',
      rules: [{ required: true }],
      componentProps: {
        placeholder: '请选择项目责任部门',
        options: state.deptOptions,
      },
    },
    {
      field: 'resPerson',
      component: 'Input',
      colProps: {
        span: 12,
      },
      label: '项目经理:',
      rules: [{ required: true }],
      componentProps: {
        placeholder: '请输入项目经理',
        maxlength: 10,
      },
    },
    {
      field: 'contactNumber',
      component: 'Input',
      colProps: {
        span: 12,
      },
      label: '联系电话:',
      componentProps: {
        placeholder: '请输入联系电话',
        maxlength: 20,
      },
    },
    {
      field: 'predictStartTime',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '项目预估开始时间:',
      componentProps: {
        style: { width: '100%' },
        disabledDate: (current) => {
          const { predictEndTime } = formMethods.getFieldsValue();
          return predictEndTime ? current > predictEndTime : false;
        },
      },
    },
    {
      field: 'predictEndTime',
      component: 'DatePicker',
      colProps: {
        span: 12,
      },
      label: '项目预估结束时间:',
      componentProps: {
        style: { width: '100%' },
        disabledDate: (current) => {
          const { predictStartTime } = formMethods.getFieldsValue();
          return predictStartTime ? current < predictStartTime : false;
        },
      },
    },
    {
      field: 'type',
      component: 'ApiSelect',
      colProps: {
        span: 12,
      },
      label: '项目类别:',
      componentProps: {
        api: () => getDictType().then((res) => {
          typeList.value = res;
          return res;
        }),
        labelField: 'description',
        valueField: 'value',
      },
    },
    {
      field: 'investment',
      component: 'Select',
      colProps: {
        span: 12,
      },
      label: '投资性:',
      ifShow: ({ model }) => !!typeList.value.filter((item) => item.value === model.type)[0]?.subDictId,
      rules: [{ required: true }],
      componentProps: {
        options: [
          {
            label: '生产',
            value: '生产',
          },
          {
            label: '非生产',
            value: '非生产',
          },
        ],
      },
    },
  ],
});

function selectUserChange(data) {
  console.log(data);
}

</script>

<style scoped>

</style>
