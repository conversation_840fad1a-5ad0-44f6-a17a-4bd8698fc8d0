.circle {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 12px;

  > span.icon-main-wrap {
    font-size: 18px !important;
  }

  &.default {
    background-color: #F1F4F5;
    color: #6081eb;
  }
  &.primary{
    background-color: #6081eb;
    color: #fff;
  }
  &.info{
    background-color: #1890ff;
    color: #fff;
  }
  &.warning{
    background-color: #faad14;
    color: #fff;
  }
  &.success{
    background-color: #52c41a;
    color: #fff;
  }
}

.fg1{
  flex-grow: 1;
}

.circle-flex-item {
  display: flex;
  align-items: center;

  .circle {
    flex-shrink: 0;
  }

  >:last-child {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    width: 0;

    .value {
      font-size: 18px;
      font-weight: bold;
    }

    span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      color: ~`getPrefixVar('text-color-second')`;
    }
  }
}

.fs0{
  flex-shrink: 0;
}

.project-scheme-chart {
  flex-grow: 1;
  width: 0;
  flex-shrink: 0;
  height: 200px;
}

.custom-legend-title {
  display: flex;
  align-items: center;
  font-size: 12px;

  .value {
    font-weight: bold;
  }
}

.custom-legend-item {
  position: relative;
  line-height: 32px;
  padding-left: 25px;

  >:first-child{
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    content: '';
    width: 15px;
    height: 15px;
  }
}

.label-value-item{
  display: flex;
  align-items: center;

  &+.label-value-item{
    margin-left: 20px;
  }
  .value{
    font-weight: bold;
  }
}

.right{
  margin-left: auto;
}

.container-material {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title{
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .chart {
    width: 100%;
    height: 120px;
  }

  .legend-material {

    > div {
      display: flex;
      align-items: center;
      position: relative;
      padding-left: 20px;

      .icon{
        position: absolute;
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }

      span{
        font-size: 12px;
      }
    }
  }
}
