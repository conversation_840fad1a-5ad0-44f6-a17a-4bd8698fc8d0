package com.chinasie.orion.bo;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/08/15:28
 * @description:
 */
@Component
public class IdAnalysisBo {

    @Autowired
    private ClassRedisHelper classRedisHelper;

    /**
     *  通过ID列表反向解析出 className 信息
     * @param idList
     * @return
     */
    public Map<String, String> getClassName(List<String> idList) {
        List<ClassVO> classList = classRedisHelper.getClassList();
        Map<String, String> codeToName = new HashMap<>();
        for (ClassVO classVO : classList) {
            codeToName.put(classVO.getCode(), classVO.getClassName());
        }
        Map<String, String> idToName = new HashMap<>();
        for (String s : idList) {
            String code = s.substring(0, 4);
            String s1 = codeToName.get(code);
            if ( StrUtil.isBlank(s1)) {
                continue;
            }
            idToName.put(s, s1);
        }
        return idToName;
    }
}
