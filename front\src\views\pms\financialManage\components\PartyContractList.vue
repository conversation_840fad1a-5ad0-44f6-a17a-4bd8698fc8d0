<script lang="ts" setup>
import {
  ref, h,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import { message, Input } from 'ant-design-vue';
import Api from '/@/api';

const tableRef = ref(null);

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const valuesList = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition.values));

    // 如果需要将所有 values 合并成一个扁平的数组
    const flattenedValues = valuesList.flat();
    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: { cusName: flattenedValues[0] },
    };
  }
  return params;
}

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: { type: 'radio' },
  showSmallSearch: true,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  maxHeight: 470,
  smallSearchField: ['cusName'],
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams || [], 'customerInfo/getIncomeCustomerInfoPages', 'POST');
  },
  columns: [
    {
      title: '客户编码',
      dataIndex: 'cusNumber',
      width: 180,
    },
    {
      title: '客户名称',
      dataIndex: 'cusName',
    },
    {
      title: '临时客户名称',
      dataIndex: 'temporary',
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender({ record }) {
        return record?.id === '0' ? h(Input, {
          value: record.temporary,
          placeholder: '请输入临时客户名称',
          onChange(e) {
            record.temporary = e.target.value;
          },
        }) : '';
      },
    },
    {
      title: '客户状态',
      dataIndex: 'cusStatusName',
      width: 160,
    },
    {
      title: '客户关系',
      dataIndex: 'groupInOutName',
      width: 160,
    },
    {
      title: '客户范围',
      dataIndex: 'busScopeName',
      width: 160,
    },
    {
      title: '所属行业',
      dataIndex: 'industryName',
      width: 160,
    },
  ],
};

function updateTable() {
  tableRef.value?.reload();
}

defineExpose({
  async onSubmit() {
    const selectRow = tableRef.value.getSelectRows();
    return new Promise((resolve, reject) => {
      if (selectRow.length === 0) {
        message.warning('请选择一条数据');
        reject('请选择一条数据')
        return;
      }
      if(selectRow[0].id === '0' && selectRow[0].temporary === undefined) {
        message.warning('请输入临时客户名称');
        reject('请输入临时客户名称')
        return;
      }
      resolve(selectRow?.[0]);    
    });
  },
});
</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped></style>
