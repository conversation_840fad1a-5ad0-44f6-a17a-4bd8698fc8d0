package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.ProductionIndexDTO;
import com.chinasie.orion.domain.entity.ProductionIndex;
import com.chinasie.orion.domain.vo.ProductionIndexVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProductionIndexMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProductionIndexService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProductionIndex 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@Service
@Slf4j
public class ProductionIndexServiceImpl extends OrionBaseServiceImpl<ProductionIndexMapper, ProductionIndex> implements ProductionIndexService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProductionIndexVO detail(String id, String pageCode) throws Exception {
        ProductionIndex productionIndex = this.getById(id);
        ProductionIndexVO result = BeanCopyUtils.convertTo(productionIndex, ProductionIndexVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param productionIndexDTO
     */
    @Override
    public String create(ProductionIndexDTO productionIndexDTO) throws Exception {
        ProductionIndex productionIndex = BeanCopyUtils.convertTo(productionIndexDTO, ProductionIndex::new);
        this.save(productionIndex);

        String rsp = productionIndex.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param productionIndexDTO
     */
    @Override
    public Boolean edit(ProductionIndexDTO productionIndexDTO) throws Exception {
        ProductionIndex productionIndex = BeanCopyUtils.convertTo(productionIndexDTO, ProductionIndex::new);

        this.updateById(productionIndex);

        String rsp = productionIndex.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProductionIndexVO> pages(Page<ProductionIndexDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProductionIndex> condition = new LambdaQueryWrapperX<>(ProductionIndex.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProductionIndex::getCreateTime);


        Page<ProductionIndex> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProductionIndex::new));

        PageResult<ProductionIndex> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProductionIndexVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProductionIndexVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProductionIndexVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProductionIndexVO> getList() {
        LambdaQueryWrapperX<ProductionIndex> condition = new LambdaQueryWrapperX<>(ProductionIndex.class);
        condition.orderByDesc(ProductionIndex::getCreateTime);
        List<ProductionIndex> productionIndexes = this.list(condition);

        return BeanCopyUtils.convertListTo(productionIndexes, ProductionIndexVO::new);
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "生产看板指标维护导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProductionIndexDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProductionIndexExcelListener excelReadListener = new ProductionIndexExcelListener();
        EasyExcel.read(inputStream, ProductionIndexDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProductionIndexDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("生产看板指标维护导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProductionIndex> productionIndexes = BeanCopyUtils.convertListTo(dtoS, ProductionIndex::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProductionIndex-import::id", importId, productionIndexes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProductionIndex> productionIndexes = (List<ProductionIndex>) orionJ2CacheService.get("pmsx::ProductionIndex-import::id", importId);
        log.info("生产看板指标维护导入的入库数据={}", JSONUtil.toJsonStr(productionIndexes));

        this.saveBatch(productionIndexes);
        orionJ2CacheService.delete("pmsx::ProductionIndex-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProductionIndex-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProductionIndex> condition = new LambdaQueryWrapperX<>(ProductionIndex.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProductionIndex::getCreateTime);
        List<ProductionIndex> productionIndexes = this.list(condition);

        List<ProductionIndexDTO> dtos = BeanCopyUtils.convertListTo(productionIndexes, ProductionIndexDTO::new);

        String fileName = "生产看板指标维护数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProductionIndexDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProductionIndexVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ProductionIndexExcelListener extends AnalysisEventListener<ProductionIndexDTO> {

        private final List<ProductionIndexDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProductionIndexDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProductionIndexDTO> getData() {
            return data;
        }
    }


}
