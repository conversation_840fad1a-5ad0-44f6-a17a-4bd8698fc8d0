package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * PerformanceIndicator VO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@ApiModel(value = "PerformanceIndicatorVO对象", description = "项目绩效和指标关联")
@Data
public class PerformanceIndicatorVO extends ObjectVO implements Serializable{

        /**
         * 绩效ID
         */
        @ApiModelProperty(value = "绩效ID")
        private String performanceId;

        /**
         * 指标ID，如果是自定义的就没有
         */
        @ApiModelProperty(value = "指标ID，如果是自定义的就没有")
        private String IndicatorId;

        /**
         * 该指标权重占比
         */
        @ApiModelProperty(value = "该指标权重占比")
        private BigDecimal weight;

        /**
         * 指标名称
         */
        @ApiModelProperty(value = "指标名称")
        private String indicatorName;

}
