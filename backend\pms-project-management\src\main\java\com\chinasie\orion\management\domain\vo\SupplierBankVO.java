package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierBank VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierBankVO对象", description = "银行信息")
@Data
public class SupplierBankVO extends ObjectVO implements Serializable {

    /**
     * 银行代码
     */
    @ApiModelProperty(value = "银行代码")
    private String bankCode;


    /**
     * 银行网点
     */
    @ApiModelProperty(value = "银行网点")
    private String bankBranch;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 默认银行账号
     */
    @ApiModelProperty(value = "默认银行账号")
    private String defaultAccount;


    /**
     * 账号名称（受益人）
     */
    @ApiModelProperty(value = "账号名称（受益人）")
    private String accountHolder;


    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccount;


    /**
     * 国际银行代码（SWIFT)
     */
    @ApiModelProperty(value = "国际银行代码（SWIFT)")
    private String swiftCode;


    /**
     * 国际银行账户号码（IBAN)
     */
    @ApiModelProperty(value = "国际银行账户号码（IBAN)	")
    private String iban;


    /**
     * 分理处/营业点
     */
    @ApiModelProperty(value = "分理处/营业点")
    private String tellerOffice;


    /**
     * 支行
     */
    @ApiModelProperty(value = "支行")
    private String subBranch;


    /**
     * 分行
     */
    @ApiModelProperty(value = "分行")
    private String branch;


    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;


    /**
     * 开户行地区
     */
    @ApiModelProperty(value = "开户行地区")
    private String bankDistrict;


    /**
     * 开户行城市
     */
    @ApiModelProperty(value = "开户行城市")
    private String bankCity;


    /**
     * 开户行省份
     */
    @ApiModelProperty(value = "开户行省份")
    private String bankProvince;


    /**
     * 开户行国家和地区
     */
    @ApiModelProperty(value = "开户行国家和地区")
    private String bankCountryArea;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNumber;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
