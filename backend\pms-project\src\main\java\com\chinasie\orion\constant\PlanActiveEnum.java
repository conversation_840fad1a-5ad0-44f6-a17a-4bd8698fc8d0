package com.chinasie.orion.constant;

/**
 * @author: lsy
 * @date: 2024/5/24
 * @description:
 */
public enum  PlanActiveEnum {
    DESIGN_PLAN("designPlan", "设计计划"),
    ASSESSMENT_PLAN("assessmentPlan", "评审计划"),
    TEST_PLAN("testPlan", "测试计划"),
    TEST_ASSESSMENT("testAssessment", "测试评审"),
    QUALITY_PLAN("qualityPlan", "质量计划"),
    RISK_PLAN("riskPlan", "风险计划"),
            ;


    private String value;

    private String desc;


    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    PlanActiveEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
