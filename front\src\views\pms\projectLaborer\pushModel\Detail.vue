<template>
  <div>
    <a-row class="mb-4 p5">
      <a-col :span="12">
        <a-button @click="$props.data.visible = false">
          <template #icon>
            <RollbackOutlined />
          </template>返回
        </a-button>
      </a-col>
      <a-col
        :span="12"
        class="text-right"
      >
        <a-button @click="handleDetailController($props.data.id)">
          <template #icon>
            <SelectOutlined />
          </template>打开
        </a-button>
      </a-col>
    </a-row>
    <div class="box">
      <div class="font-bold text-2xl">
        {{ data.form.name }}
      </div>
      <div class="mt-2 pb-2 border-b-2 border-gray-400">
        <span class="font-bold">创建人：{{ data.form.creatorName }}</span>
        <span class="ml-2 text-gray-500">创建日期： {{ formatDefault(data.form.createTime) }}</span>
      </div>
      <div class="mt-2 text-gray-500">
        描述：{{ data.form.summary || '暂无描述' }}
      </div>
      <div
        class="mt-4"
        v-html="data.form.content"
      />
    </div>
  </div>
</template>

<script>
import { reactive, toRefs } from 'vue';
import dayjs from 'dayjs';
import { Row, Col, Button } from 'ant-design-vue';
import { RollbackOutlined, SelectOutlined } from '@ant-design/icons-vue';
import { handleDetailController } from '/@/views/pms/projectLaborer/utils';
export default {
  name: 'Detail',
  components: {
    ARow: Row,
    ACol: Col,
    AButton: Button,
    RollbackOutlined,
    SelectOutlined,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      father: props.data,
    });
    function formatDefault(val) {
      return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
    return {
      ...toRefs(state),
      dayjs,
      handleDetailController,
      formatDefault,
    };
  },
};
</script>

<style scoped lang="less">
  .box {
    height: calc(100vh - 150px);
    overflow: auto;
    padding: 0 10px;
  }
</style>
