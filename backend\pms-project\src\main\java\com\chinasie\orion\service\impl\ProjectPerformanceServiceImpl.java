package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.PerformanceIndicatorDTO;
import com.chinasie.orion.domain.dto.ProjectPerformanceDTO;
import com.chinasie.orion.domain.entity.PerformanceIndicator;
import com.chinasie.orion.domain.entity.PerformanceTemplate;
import com.chinasie.orion.domain.entity.ProjectPerformance;
import com.chinasie.orion.domain.vo.ProjectPerformanceVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.ProjectPerformanceMapper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.PerformanceIndicatorService;
import com.chinasie.orion.service.PerformanceTemplateService;
import com.chinasie.orion.service.ProjectPerformanceService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectPerformance 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@Service
public class ProjectPerformanceServiceImpl extends OrionBaseServiceImpl<ProjectPerformanceMapper, ProjectPerformance> implements ProjectPerformanceService {

    @Autowired
    private ProjectPerformanceMapper projectPerformanceMapper;

    @Autowired
    private PerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private PerformanceTemplateService performanceTemplateService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectPerformanceVO detail(String id) throws Exception {
        ProjectPerformance projectPerformance = projectPerformanceMapper.selectById(id);
        if (Objects.isNull(projectPerformance)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "数据不存在！");
        }
        ProjectPerformanceVO result = BeanCopyUtils.convertTo(projectPerformance, ProjectPerformanceVO::new);
        List<PerformanceIndicator> performanceIndicatorList = performanceIndicatorService.list(new LambdaQueryWrapperX<>(PerformanceIndicator.class)
                .eq(PerformanceIndicator::getPerformanceId, id));
        List<PerformanceIndicatorDTO> performanceIndicators = BeanCopyUtils.convertListTo(performanceIndicatorList, PerformanceIndicatorDTO::new);
        result.setIndicatorDTOList(performanceIndicators);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectPerformanceDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectPerformanceVO create(ProjectPerformanceDTO projectPerformanceDTO) throws Exception {
        ProjectPerformance projectPerformance = BeanCopyUtils.convertTo(projectPerformanceDTO, ProjectPerformance::new);
        if (StringUtils.isBlank(projectPerformanceDTO.getName())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "绩效名称不能为空！");
        }
        LambdaQueryWrapperX<ProjectPerformance> condition = new LambdaQueryWrapperX<>(ProjectPerformance.class);
        condition.eq(ProjectPerformance::getName, projectPerformanceDTO.getName());
        condition.eq(ProjectPerformance::getProjectId, projectPerformanceDTO.getProjectId());
        List<ProjectPerformance> projectPerformances = this.list(condition);
        if (!CollectionUtils.isBlank(projectPerformances)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "【" + projectPerformanceDTO.getName() + "】绩效名称不能重复！");
        }
        if (CollectionUtils.isBlank(projectPerformanceDTO.getIndicatorDTOList())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "指标数据不能为空！");
        }
        if (Objects.isNull(projectPerformanceDTO.getProjectId())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "项目的ID不能为空！");
        }
        if (Objects.isNull(projectPerformanceDTO.getTypeId())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "考核类型不能为空！");
        }

        //指标列表数据
        List<PerformanceIndicatorDTO> indicatorDTOList = projectPerformanceDTO.getIndicatorDTOList();
        boolean hasNullScore = indicatorDTOList.stream()
                .anyMatch(indicator -> Objects.nonNull(indicator) && indicator.getScore() == null);
        if (hasNullScore) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "指标评分不能为空！");
        }

        boolean hasNullWeight = indicatorDTOList.stream()
                .anyMatch(indicator -> Objects.nonNull(indicator) && indicator.getWeight() == null);
        if (hasNullWeight) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "权重数据不能为空！");
        }
        BigDecimal totalWeight = indicatorDTOList.stream().map(PerformanceIndicatorDTO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalWeight.compareTo(BigDecimal.valueOf(100)) != 0) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "所有的指标权重加起来必须等于100");
        }
        // 计算加权总分
        BigDecimal totalScore = indicatorDTOList.stream()
                .map(indicator -> indicator.getScore().multiply(indicator.getWeight()).divide(BigDecimal.valueOf(100)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 设置总分
        projectPerformance.setTotalScore(totalScore);
        //评分时间
        projectPerformance.setScoreDate(new Date());
        //评分人
        String userId = CurrentUserHelper.getCurrentUserId();
        projectPerformance.setUserId(userId);
        projectPerformanceMapper.insert(projectPerformance);
        //设置
        indicatorDTOList.forEach(item -> {
            //绩效主键ID
            item.setPerformanceId(projectPerformance.getId());
            item.setClassName("PerformanceIndicator");
            //评价人
            item.setUserId(userId);
        });
        //保存指标数据
        List<PerformanceIndicator> performanceIndicators = BeanCopyUtils.convertListTo(indicatorDTOList, PerformanceIndicator::new);
        performanceIndicatorService.saveBatch(performanceIndicators);
        ProjectPerformanceVO rsp = BeanCopyUtils.convertTo(projectPerformance, ProjectPerformanceVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectPerformanceDTO
     */
    @Override
    public Boolean edit(ProjectPerformanceDTO projectPerformanceDTO) throws Exception {
        ProjectPerformance projectPerformance = BeanCopyUtils.convertTo(projectPerformanceDTO, ProjectPerformance::new);
        ProjectPerformance checkProjectPerformance = projectPerformanceMapper.selectById(projectPerformanceDTO.getId());
        if (Objects.isNull(checkProjectPerformance)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "数据对象不存在！");
        }
        if (CollectionUtils.isBlank(projectPerformanceDTO.getIndicatorDTOList())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "指标数据不能为空！");
        }
        if (Objects.isNull(projectPerformanceDTO.getProjectId())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "项目的ID不能为空！");
        }
        if (Objects.isNull(projectPerformanceDTO.getTypeId())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "考核类型不能为空！");
        }
        //指标列表数据
        List<PerformanceIndicatorDTO> indicatorDTOList = projectPerformanceDTO.getIndicatorDTOList();
        boolean hasNullScore = indicatorDTOList.stream()
                .anyMatch(indicator -> Objects.nonNull(indicator) && indicator.getScore() == null);
        if (hasNullScore) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "指标评分不能为空！");
        }

        boolean hasNullWeight = indicatorDTOList.stream()
                .anyMatch(indicator -> Objects.nonNull(indicator) && indicator.getWeight() == null);
        if (hasNullWeight) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(), "权重不能为空！");
        }

        BigDecimal total = indicatorDTOList.stream().map(PerformanceIndicatorDTO::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (total.compareTo(BigDecimal.valueOf(100)) != 0) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "所有的指标权重加起来必须等于100");
        }
        // 计算加权总分
        BigDecimal totalScore = indicatorDTOList.stream()
                .map(indicator -> indicator.getScore().multiply(indicator.getWeight()).divide(BigDecimal.valueOf(100)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 设置总分
        projectPerformance.setTotalScore(totalScore);
        //评分时间
        projectPerformance.setScoreDate(new Date());
        //评分人
        String userId = CurrentUserHelper.getCurrentUserId();
        projectPerformance.setUserId(userId);
        //设置
        indicatorDTOList.forEach(item -> {
            //绩效主键ID
            item.setPerformanceId(projectPerformance.getId());
            item.setClassName("PerformanceIndicator");
            //评价人
            item.setUserId(userId);
        });
        //保存指标数据
        List<PerformanceIndicator> performanceIndicators = BeanCopyUtils.convertListTo(indicatorDTOList, PerformanceIndicator::new);
        //todo 移除之前的数据
        performanceIndicatorService.remove(new LambdaQueryWrapperX<>(PerformanceIndicator.class)
                .eq(PerformanceIndicator::getPerformanceId, projectPerformanceDTO.getId()));
        performanceIndicatorService.saveBatch(performanceIndicators);
        ProjectPerformanceVO rsp = BeanCopyUtils.convertTo(projectPerformance, ProjectPerformanceVO::new);
        int update = projectPerformanceMapper.updateById(projectPerformance);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "要删除的ID不能为空！");
        }
        int delete = projectPerformanceMapper.deleteBatchIds(ids);
        //删除下面的指标关联和评分数据
        LambdaQueryWrapperX<PerformanceIndicator> condition = new LambdaQueryWrapperX<>(PerformanceIndicator.class);
        condition.in(PerformanceIndicator::getPerformanceId, ids);
        List<PerformanceIndicator> performanceIndicatorList = performanceIndicatorService.list(condition);
        List<String> performanceIndicatorIds = performanceIndicatorList.stream().map(PerformanceIndicator::getId).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(performanceIndicatorIds)) {
            performanceIndicatorService.removeBatchByIds(performanceIndicatorList);
        }
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectPerformanceVO> pages(Page<ProjectPerformanceDTO> pageRequest, String projectId) throws Exception {
        LambdaQueryWrapperX<ProjectPerformance> condition = new LambdaQueryWrapperX<>(ProjectPerformance.class);
        //项目ID
        condition.eq(ProjectPerformance::getProjectId, projectId);
        Page<ProjectPerformance> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPerformance::new));
        condition.orderByDesc(ProjectPerformance::getCreateTime);
        if (!org.springframework.util.CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        PageResult<ProjectPerformance> page = projectPerformanceMapper.selectPage(realPageRequest, condition);
        Page<ProjectPerformanceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPerformanceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPerformanceVO::new);
        if (CollectionUtils.isBlank(vos)) {
            return new Page<>();
        }
        final List<String> typeIds = vos.stream().map(ProjectPerformanceVO::getTypeId).distinct().collect(Collectors.toList());
        Map<String, String> typeKV = new HashMap<>();
        if (!CollectionUtils.isBlank(typeIds)) {
            List<PerformanceTemplate> performanceTemplates = performanceTemplateService.querylistByIds(typeIds);
            typeKV = performanceTemplates.stream().collect(Collectors.toMap(PerformanceTemplate::getId, PerformanceTemplate::getName));
        }
        final Map<String, String> finalTypeKV = typeKV;
        vos.forEach(item -> {
            item.setTypeName(finalTypeKV.getOrDefault(item.getTypeId(), ""));
        });
        pageResult.setContent(vos);
        return pageResult;
    }

}
