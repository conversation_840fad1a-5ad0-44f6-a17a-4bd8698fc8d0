package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QuestionReplyManagement DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-24 14:05:04
 */
@ApiModel(value = "QuestionReplyManagementDTO对象", description = "问题答复")
@Data
@ExcelIgnoreUnannotated
public class QuestionReplyManagementDTO extends ObjectDTO implements Serializable {

    /**
     * 问题编号
     */
    @ApiModelProperty(value = "问题编号")
    @ExcelProperty(value = "问题编号 ", index = 0)
    private String questionNumber;

    /**
     * 答复详情
     */
    @ApiModelProperty(value = "答复详情")
    @ExcelProperty(value = "答复详情 ", index = 1)
    private String reply;


}
