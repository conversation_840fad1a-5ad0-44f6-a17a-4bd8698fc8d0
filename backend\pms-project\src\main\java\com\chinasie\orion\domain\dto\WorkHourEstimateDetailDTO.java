package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourEstimateDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 17:47:13
 */
@ApiModel(value = "WorkHourEstimateDetailDTO对象", description = "工时预估明细")
@Data
public class WorkHourEstimateDetailDTO extends ObjectDTO implements Serializable{

    /**
     * 工时id
     */
    @ApiModelProperty(value = "工时id")
    private String workHourId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @NotBlank(message = "月份不能为空")
    private String workMonth;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @NotNull(message = "每月工时不能为空")
    @Max(value = 744, message = "工时时长不能超过744小时")
    private Integer workHour;

}
