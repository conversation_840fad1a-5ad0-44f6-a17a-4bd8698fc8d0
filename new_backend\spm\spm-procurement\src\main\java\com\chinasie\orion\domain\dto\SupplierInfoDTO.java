package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * SupplierInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierInfoDTO对象", description = "供应商管理")
@Data
@ExcelIgnoreUnannotated
public class SupplierInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String name;

    /**
     * 供应商账号
     */
    @ApiModelProperty(value = "供应商账号")
    @ExcelProperty(value = "供应商账号 ", index = 2)
    private String account;

    /**
     * 找回密码邮箱
     */
    @ApiModelProperty(value = "找回密码邮箱")
    @ExcelProperty(value = "找回密码邮箱 ", index = 3)
    private String findPassEamil;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    @ExcelProperty(value = "供应商简称 ", index = 4)
    private String simName;

    /**
     * 供应商英文名称
     */
    @ApiModelProperty(value = "供应商英文名称")
    @ExcelProperty(value = "供应商英文名称 ", index = 5)
    private String eName;

    /**
     * 注册国家和地区
     */
    @ApiModelProperty(value = "注册国家和地区")
    @ExcelProperty(value = "注册国家和地区 ", index = 6)
    private String regCountryRegion;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    @ExcelProperty(value = "省 ", index = 7)
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @ExcelProperty(value = "城市 ", index = 8)
    private String city;

    /**
     * 区/县
     */
    @ApiModelProperty(value = "区/县")
    @ExcelProperty(value = "区/县 ", index = 9)
    private String county;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @ExcelProperty(value = "注册地址 ", index = 10)
    private String regAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    @ExcelProperty(value = "邮政编码 ", index = 11)
    private String emsNumber;

    /**
     * 网址
     */
    @ApiModelProperty(value = "网址")
    @ExcelProperty(value = "网址 ", index = 12)
    private String url;

    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    @ExcelProperty(value = "固定电话 ", index = 13)
    private String landlinePhone;

    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    @ExcelProperty(value = "分机 ", index = 14)
    private String extension;

    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    @ExcelProperty(value = "传真 ", index = 15)
    private String fax;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型")
    @ExcelProperty(value = "组织类型 ", index = 16)
    private String organizationType;

    /**
     * 法人代表
     */
    @ApiModelProperty(value = "法人代表")
    @ExcelProperty(value = "法人代表 ", index = 17)
    private String legalrep;

    /**
     * 企业性质
     */
    @ApiModelProperty(value = "企业性质")
    @ExcelProperty(value = "企业性质 ", index = 18)
    private String companyNature;

    /**
     * 中广核集团参股或控股公司
     */
    @ApiModelProperty(value = "中广核集团参股或控股公司")
    @ExcelProperty(value = "中广核集团参股或控股公司 ", index = 19)
    private String zghChild;

    /**
     * 注册资金币种
     */
    @ApiModelProperty(value = "注册资金币种")
    @ExcelProperty(value = "注册资金币种 ", index = 20)
    private String capitalCurrency;

    /**
     * 注册资本（万）
     */
    @ApiModelProperty(value = "注册资本（万）")
    @ExcelProperty(value = "注册资本（万） ", index = 21)
    private BigDecimal registeredCapital;

    /**
     * 上级主管单位
     */
    @ApiModelProperty(value = "上级主管单位")
    @ExcelProperty(value = "上级主管单位 ", index = 22)
    private String parentOrg;

    /**
     * 主要控股公司
     */
    @ApiModelProperty(value = "主要控股公司")
    @ExcelProperty(value = "主要控股公司 ", index = 23)
    private String majorShareholder;


    /**
     * 可提供产品/服务
     */
    @ApiModelProperty(value = "可提供产品/服务")
    @ExcelProperty(value = "可提供产品/服务文字描述 ", index = 24)
    private String productsServices;

    /**
     * 可提供产品/服务文字描述
     */
    @ApiModelProperty(value = "可提供产品/服务文字描述")
    @ExcelProperty(value = "可提供产品/服务文字描述 ", index = 25)
    private String productsServicesDesc;



    /**
     * 公司简介
     */
    @ApiModelProperty(value = "公司简介")
    @ExcelProperty(value = "公司简介 ", index = 25)
    private String companyOverview;

    /**
     * 营业执照注册号/统一社会信用代码
     */
    @ApiModelProperty(value = "营业执照注册号/统一社会信用代码")
    @ExcelProperty(value = "营业执照注册号/统一社会信用代码 ", index = 26)
    private String businessLicenseNum;

    /**
     * 营业执照有效期起
     */
    @ApiModelProperty(value = "营业执照有效期起")
    @ExcelProperty(value = "营业执照有效期起 ", index = 27)
    private Date businessLicenseStart;

    /**
     * 营业执照有效期至
     */
    @ApiModelProperty(value = "营业执照有效期至")
    @ExcelProperty(value = "营业执照有效期至 ", index = 28)
    private Date businessLicenseEnd;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    @ExcelProperty(value = "经营范围 ", index = 29)
    private String operationScope;

    /**
     * 是否推荐供应商
     */
    @ApiModelProperty(value = "是否推荐供应商")
    @ExcelProperty(value = "是否推荐供应商 ", index = 30)
    private String recommendSupplier;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    @ExcelProperty(value = "板块名称 ", index = 31)
    private String sectorName;

    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    @ExcelProperty(value = "供应商级别 ", index = 32)
    private String supplierLevel;

    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    @ExcelProperty(value = "资审有效期 ", index = 33)
    private Date qualValidity;

    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    @ExcelProperty(value = "采购品类 ", index = 34)
    private String procurementCat;

    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    @ExcelProperty(value = "采购品类编码 ", index = 35)
    private String procCatCode;

    /**
     * 供货范围文本描述
     */
    @ApiModelProperty(value = "供货范围文本描述")
    @ExcelProperty(value = "供货范围文本描述 ", index = 36)
    private String deliveryScopeDesc;

    /**
     * 供应商分类
     */
    @ApiModelProperty(value = "供应商分类")
    @ExcelProperty(value = "供应商分类 ", index = 37)
    private String supplierType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 38)
    private String number;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    @ExcelProperty(value = "供应商类别 ", index = 39)
    private String supplierCategory;

    /**
     * 供应商缴费有效截止日期
     */
    @ApiModelProperty(value = "供应商缴费有效截止日期")
    @ExcelProperty(value = "供应商缴费有效截止日期", index = 40)
    private Date paymentEffectiveDeadline;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    @ExcelProperty(value = "联系人姓名", index = 41)
    private String contractName;

    /**
     * 联系人手机
     */
    @ApiModelProperty(value = "联系人手机")
    @ExcelProperty(value = "联系人手机", index = 42)
    private String contractTel;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    @ExcelProperty(value = "联系人邮箱", index = 43)
    private String contractEmail;

    /**
     * 二级公司编码
     */
    @ApiModelProperty(value = "二级公司编码")
    private String secondaryCompanyCode;

    /**
     * 供应商类别
     */
    @ApiModelProperty(value = "供应商类别")
    private String secondaryCompanyName;

    /**
     * 营业执照注册日期
     */
    @ApiModelProperty(value = "营业执照注册日期")
    private Date licenseRegDate;

    /**
     * 营业执照有效日期
     */
    @ApiModelProperty(value = "营业执照有效日期")
    private Date licenseValidDate;
}
