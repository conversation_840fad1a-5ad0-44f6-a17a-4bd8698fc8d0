<template>
  <div class="list-box">
    <PartProjectList />
  </div>
</template>
<script lang='ts' setup>
import PartProjectList from './PartProjectList.vue';
</script>

<style lang='less' scoped>
.home-page {
  background: #fff;
  width: 100%;
  padding: 0 0 1px 0;
  .ant-row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  //.task-box {
  //  height: 400px;
  //}

  .user-box {
    height: 120px;
    margin: 0 0 ~`getPrefixVar('content-margin') ` 0;
  }

  .msg-box {
    height: 260px;
  }

  .part-plan-box,
  .part-project {
    height: 920px;
  }

  .chart-box {
    height: 320px;
  }

  .more {
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: #595959;
    line-height: 22px;
    cursor: pointer;
    transition: 0.2s;

    > span {
      margin-right: 10px;
    }
  }
}

.statistical-box {
  padding: 0;
}
</style>
