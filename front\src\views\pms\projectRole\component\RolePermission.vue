<template>
  <div class="h-full flex">
    <!--系统管理角色-->
    <template v-if="$attrs.roleItem?.roleType === 1">
      <div class="menu-wrap flex flex-ver">
        <div class="title">
          菜单列表
        </div>
        <div class="flex-f1 menu-main">
          <Menu
            v-bind="$attrs"
            @select="onMenuSelect"
          />
        </div>
      </div>
    </template>

    <!--项目管理角色-->
    <template v-if="$attrs.roleItem?.roleType === 0">
      <div class="menu-wrap flex flex-ver">
        <div class="flex flex-ac">
          <div class="flex-f1 title">
            页面列表
          </div>
          <div class="pr10">
            <Button @click="addPage">
              添加
            </Button>
          </div>
        </div>
        <div class="flex-f1 menu-main">
          <PageList
            ref="pageListRef"
            :key="$attrs.roleId"
            :role-id="$attrs.roleId"
            @menuChange="menuChange"
          />
        </div>
      </div>
    </template>

    <div class="flex-f1 flex flex-ver">
      <div class="title">
        页面功能赋权
      </div>
      <div class="flex-f1">
        <FunctionalEmpowerment
          v-if="pageId"
          :key="pageId"
          :role-type="$attrs.roleItem?.roleType"
          :role-id="$attrs.roleId"
          :page-id="pageId"
        />
        <div
          v-else
          class="h-full flex flex-pac"
        >
          <Empty description="请选择页面" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, ref, toRefs,
} from 'vue';
import { Button, Empty } from 'ant-design-vue';
import Menu from './Menu.vue';
import FunctionalEmpowerment from './FunctionalEmpowerment.vue';
import PageList from './PageList.vue';

export default defineComponent({
  name: 'RolePermission',
  components: {
    Menu,
    FunctionalEmpowerment,
    PageList,
    Button,
    Empty,
  },
  setup() {
    const pageListRef = ref(null);

    const state = reactive({
      pageId: '',
    });

    function menuChange(item) {
      state.pageId = item.id;
    }

    function onMenuSelect(selectedKeys, { node }) {
      if (selectedKeys.length) {
        const { className, id, pageId } = node.dataRef;
        // if (className === 'Page') {
        //   state.pageId = id;
        // } else {
        //   state.pageId = '';
        // }
        state.pageId = pageId;
      } else {
        state.pageId = '';
      }
    }

    return {
      ...toRefs(state),
      pageListRef,
      addPage() {
        pageListRef.value.openAddPageModal();
      },
      menuChange,
      onMenuSelect,
    };
  },
});
</script>

<style scoped lang="less">
  .title {
    font-size: 14px;
    font-weight: bold;
    padding: 15px;
  }

  .menu-main {
    border-right: 1px solid rgba(217, 221, 232, 1);
    width: 260px;
    overflow-y: auto;
  }
</style>
