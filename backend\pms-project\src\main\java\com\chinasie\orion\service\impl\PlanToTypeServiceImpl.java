package com.chinasie.orion.service.impl;


import com.chinasie.orion.domain.entity.PlanToType;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToTypeRepository;
import com.chinasie.orion.service.PlanToTypeService;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/17:39
 * @description:
 */
@Service
public class PlanToTypeServiceImpl extends OrionBaseServiceImpl<PlanToTypeRepository, PlanToType> implements PlanToTypeService {


    @Override
    public PlanToType saveParam(String id, String planTypeId) throws Exception {
        PlanToType planToType = new PlanToType();
        planToType.setToId(planTypeId);
        planToType.setFromId(id);
        this.save(planToType);
        return planToType;
    }
}
