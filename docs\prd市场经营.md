# 市场经营模块产品设计文档

## 1. 模块概述

市场经营模块是系统的核心业务模块之一，主要包含以下功能：
- 线索管理（线索池、线索录入）
- 需求管理
- 报价管理
- 合同管理
- 客户管理
- 里程碑管理
- 我的草稿
- 帮助中心
- 报表中心（需求报表、报价报表、合同报表、里程碑报表）

## 2. 功能页面详细说明

### 2.1 市场经营一览

![市场经营一览](./images/front_system/市场经营/市场经营_市场经营一览.png)

#### 页面布局
- 顶部：系统导航栏，包含用户信息、消息通知等
- 左侧：功能菜单树，展示市场经营各子模块
- 主区域：市场经营数据概览仪表板

#### 组件说明
1. 数据统计卡片区域
   - 线索数量统计卡片
   - 需求数量统计卡片
   - 报价数量统计卡片
   - 合同数量统计卡片
   - 预测收入统计卡片

2. 图表展示区域
   - 销售漏斗图：展示从线索到合同的转化情况
   - 月度趋势图：展示各业务指标的月度变化趋势
   - 区域分布图：展示业务在不同地区的分布情况

#### 事件操作
- 点击数据卡片：跳转到对应功能模块的列表页面
- 图表交互：支持时间范围筛选、数据钻取、图表缩放
- 刷新按钮：重新加载最新数据

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(MarketOverview.vue)
    participant A as API层(Api.js)
    participant C as ManagementStaticsController
    participant S as ManagementStaticsService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据
    A-->>F: 权限验证通过

    F->>A: getStatistics() 获取统计数据
    A->>C: POST /pms/managementStatistics/overview
    Note over A,C: 前端文件: src/views/market/Overview.vue<br/>调用函数: getStatistics()
    C->>S: ManagementStaticsService.getOverviewStatistics()
    Note over C,S: Controller: ManagementStaticsController<br/>Service: ManagementStaticsService
    S->>R: 查询缓存 market:overview:stats
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询统计数据
        Note over S,D: SQL: SELECT COUNT(*) FROM pms_lead_management<br/>SQL: SELECT COUNT(*) FROM pms_requirement_mangement<br/>SQL: SELECT COUNT(*) FROM pms_quotation_management<br/>SQL: SELECT COUNT(*) FROM pms_market_contract
        D-->>S: 返回统计结果
        S->>R: 更新缓存 market:overview:stats (15分钟)
    end
    S-->>C: 返回统计数据
    C-->>A: ResponseDTO<OverviewStatisticsVO>
    A-->>F: 渲染统计卡片

    F->>A: getMilestoneCompletion() 获取里程碑数据
    A->>C: POST /pms/managementStatistics/milestoneCompletion
    C->>S: ManagementStaticsService.milestoneCompletion()
    S->>D: 查询里程碑完成情况
    Note over S,D: SQL: SELECT * FROM pms_contract_milestone WHERE status IN ('COMPLETED', 'IN_PROGRESS')
    D-->>S: 返回里程碑数据
    S-->>C: MilestoneCompletionVO
    C-->>A: 返回里程碑数据
    A-->>F: 渲染图表组件

    F->>A: getTrendData() 获取趋势数据
    A->>C: POST /pms/managementStatistics/trendAnalysis
    C->>S: ManagementStaticsService.getTrendAnalysis()
    S->>R: 查询缓存 market:trend:data
    alt 缓存未命中
        S->>D: 查询月度趋势数据
        Note over S,D: SQL: SELECT DATE_FORMAT(create_time, '%Y-%m') as month, COUNT(*) as count<br/>FROM pms_lead_management GROUP BY month
        D-->>S: 返回趋势数据
        S->>R: 更新缓存 market:trend:data (1小时)
    end
    S-->>C: TrendAnalysisVO
    C-->>A: 返回趋势数据
    A-->>F: 渲染趋势图表
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 线索数量 | leadCount | pms_lead_management | COUNT(*) | 统计总数 | 统计所有未删除的线索记录 |
| 需求数量 | requirementCount | pms_requirement_mangement | COUNT(*) | 统计总数 | 统计所有未删除的需求记录 |
| 报价数量 | quotationCount | pms_quotation_management | COUNT(*) | 统计总数 | 统计所有未删除的报价记录 |
| 合同数量 | contractCount | pms_market_contract | COUNT(*) | 统计总数 | 统计所有未删除的合同记录 |
| 预测收入 | predictedIncome | pms_quotation_management | SUM(total_amount) | 金额汇总 | 汇总状态为'已发出'和'已确认'的报价金额 |
| 里程碑完成率 | milestoneCompletionRate | pms_contract_milestone | COUNT(CASE WHEN milestone_status='COMPLETED') / COUNT(*) * 100 | 百分比计算 | 已完成里程碑数/总里程碑数*100 |
| 月度趋势数据 | monthlyTrend | 多表联合 | DATE_FORMAT(create_time, '%Y-%m') | 按月分组 | 按创建时间月份统计各模块数据变化 |

#### 数据异常排查指南

**线索数量异常**：
1. 检查 pms_lead_management 表的 is_deleted 字段是否正确
2. 确认统计时间范围筛选条件
3. 检查数据权限过滤逻辑

**预测收入异常**：
1. 检查 pms_quotation_management 表的 quotation_status 字段值
2. 确认 total_amount 字段数据类型和精度
3. 检查汇率转换逻辑（如涉及多币种）

**里程碑完成率异常**：
1. 检查 pms_contract_milestone 表的 milestone_status 枚举值
2. 确认里程碑与合同的关联关系
3. 检查里程碑状态更新时机

#### API接口
- POST /pms/managementStatistics/overview - 获取概览统计数据
- POST /pms/managementStatistics/milestoneCompletion - 获取里程碑完成情况
- POST /pms/managementStatistics/trendAnalysis - 获取趋势分析数据
- GET /pmi/power/function - 获取页面权限信息

### 2.2 线索管理

#### 2.2.1 线索池

![线索池](./images/front_system/市场经营/市场经营_线索持.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：线索列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 线索名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、未处理、已分配、已转化、已失效）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建线索按钮（主要按钮）
   - 批量分配按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 线索列表表格
   - 复选框列（支持全选/反选）
   - 线索名称（可点击查看详情）
   - 客户名称
   - 联系人姓名
   - 联系电话
   - 线索状态（带状态标签）
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、分配）

#### 线索池子页面

![全部线索](./images/front_system/市场经营/市场经营_线索池_全部线索.png)

![分配给我](./images/front_system/市场经营/市场经营_线索池_分配给我.png)

![预测收入](./images/front_system/市场经营/市场经营_线索池_预测收入.png)

#### 事件操作
- 点击新建线索：打开新建线索表单弹窗
- 点击批量分配：打开批量分配弹窗，可选择分配人员
- 点击导出：根据当前筛选条件导出Excel文件
- 点击编辑：打开编辑线索表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击分配：打开单个线索分配弹窗
- 点击线索名称：跳转到线索详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(LeadList.vue)
    participant A as API层(Api.js)
    participant C as LeadManagementController
    participant S as LeadManagementService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)
    participant F2 as 文件服务(RES)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getLeadList() 获取线索列表
    A->>C: POST /pms/leadManagement/page
    Note over A,C: 前端文件: src/views/market/leads/LeadList.vue<br/>调用函数: getLeadList()<br/>权限验证: pageCode='leadManagement', containerCode='PMS_XSGL_container_01'
    C->>S: LeadManagementService.pages()
    Note over C,S: Controller: LeadManagementController<br/>Service: LeadManagementService
    S->>R: 查询缓存 market:lead:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询线索数据
        Note over S,D: SQL: SELECT * FROM pms_lead_management<br/>WHERE status = ? AND customer_name LIKE ?<br/>ORDER BY create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:lead:list:${searchHash} (30分钟)
    end
    S-->>C: Page<LeadManagementVO>
    C-->>A: ResponseDTO<Page<LeadManagementVO>>
    A-->>F: 渲染线索列表表格

    F->>A: createLead() 新建线索
    A->>C: POST /pms/leadManagement/add
    Note over A,C: 前端文件: src/views/market/leads/LeadForm.vue<br/>调用函数: createLead()
    C->>S: LeadManagementService.create()
    S->>D: 插入线索数据
    Note over S,D: SQL: INSERT INTO pms_lead_management<br/>(lead_name, customer_name, contact_person, contact_phone, ...)<br/>VALUES (?, ?, ?, ?, ...)
    D-->>S: 返回插入结果
    S->>R: 清除相关缓存 market:lead:list:*
    S->>M: 发送消息通知
    Note over S,M: 消息类型: 线索创建通知<br/>接收人: 销售经理
    M-->>S: 消息发送成功
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> leadId
    A-->>F: 显示创建成功提示

    F->>A: assignLead() 分配线索
    A->>C: PUT /pms/leadManagement/assign
    C->>S: LeadManagementService.assign()
    S->>D: 更新线索分配信息
    Note over S,D: SQL: UPDATE pms_lead_management<br/>SET assigned_to = ?, status = 'ASSIGNED'<br/>WHERE id = ?
    D-->>S: 更新成功
    S->>R: 更新缓存 market:lead:detail:${leadId}
    S->>M: 发送分配通知消息
    Note over S,M: 消息类型: 线索分配通知<br/>接收人: 被分配人
    M-->>S: 消息发送成功
    S-->>C: 返回分配结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示分配成功提示

    F->>A: exportLeads() 导出线索
    A->>C: POST /pms/leadManagement/export/excel
    C->>S: LeadManagementService.exportExcel()
    S->>D: 查询导出数据
    Note over S,D: SQL: SELECT * FROM pms_lead_management<br/>WHERE conditions...
    D-->>S: 返回导出数据
    S-->>C: 生成Excel文件流
    C-->>A: 返回文件流
    A-->>F: 下载Excel文件
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 线索名称 | leadName | pms_lead_management | lead_name | 直接映射 | 线索的业务名称，用于标识线索 |
| 客户名称 | customerName | pms_lead_management | customer_name | 直接映射 | 线索对应的客户名称 |
| 联系人 | contactPerson | pms_lead_management | contact_person | 直接映射 | 客户方联系人姓名 |
| 联系电话 | contactPhone | pms_lead_management | contact_phone | 直接映射 | 客户方联系电话 |
| 联系邮箱 | contactEmail | pms_lead_management | contact_email | 直接映射 | 客户方联系邮箱 |
| 线索来源 | leadSource | pms_lead_management | lead_source | 枚举转换 | 网站、电话、推荐等来源类型 |
| 线索优先级 | leadPriority | pms_lead_management | lead_priority | 枚举转换 | 高、中、低优先级显示 |
| 线索状态 | leadStatus | pms_lead_management | lead_status | 枚举转换 | 未处理、已分配、已转化、已失效 |
| 预计金额 | estimatedAmount | pms_lead_management | estimated_amount | 金额格式化 | 显示为货币格式，保留2位小数 |
| 预计成交时间 | estimatedCloseDate | pms_lead_management | estimated_close_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 所属行业 | industry | pms_lead_management | industry | 直接映射 | 客户所属行业分类 |
| 客户规模 | companySize | pms_lead_management | company_size | 枚举转换 | 大型、中型、小型企业规模 |
| 分配给 | assignedTo | pms_lead_management + sys_user | assigned_to → user_name | 关联查询 | 通过assigned_to关联sys_user表获取用户姓名 |
| 创建时间 | createTime | pms_lead_management | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_lead_management | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_lead_management + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人姓名 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 线索名称 | leadNameSearch | lead_name | LIKE '%value%' | 模糊查询线索名称 |
| 客户名称 | customerNameSearch | customer_name | LIKE '%value%' | 模糊查询客户名称 |
| 线索状态 | leadStatusSearch | lead_status | = 'value' | 精确匹配状态值 |
| 创建时间范围 | createTimeRange | create_time | BETWEEN startDate AND endDate | 时间范围查询 |
| 分配人 | assignedToSearch | assigned_to | = userId | 精确匹配用户ID |

#### 数据异常排查指南

**线索列表为空**：
1. 检查 pms_lead_management 表的 is_deleted = 0 条件
2. 确认用户数据权限范围
3. 检查筛选条件是否过于严格

**分配人显示异常**：
1. 检查 sys_user 表中对应用户记录是否存在
2. 确认 assigned_to 字段值是否为有效的用户ID
3. 检查用户状态是否为启用状态

**金额显示异常**：
1. 检查 estimated_amount 字段数据类型（DECIMAL）
2. 确认前端金额格式化函数是否正确
3. 检查是否存在NULL值处理

**状态显示异常**：
1. 检查 lead_status 字段的枚举值定义
2. 确认前端状态映射配置是否完整
3. 检查是否存在未定义的状态值

#### API接口
- POST /pms/leadManagement/page - 获取线索分页列表
- POST /pms/leadManagement/add - 创建新线索
- PUT /pms/leadManagement/edit - 更新线索信息
- DELETE /pms/leadManagement/remove - 删除线索
- PUT /pms/leadManagement/assign - 分配线索
- POST /pms/leadManagement/export/excel - 导出线索数据
- GET /pmi/power/function - 获取页面权限信息

#### 2.2.2 线索录入

![线索录入](./images/front_system/市场经营/市场经营_线索录入.png)

#### 页面布局
- 顶部：页面标题和操作按钮
- 主体：线索录入表单
- 底部：保存、取消按钮

#### 组件说明
1. 基本信息区域
   - 线索名称（必填）
   - 客户名称（必填，支持下拉选择或新建）
   - 联系人姓名（必填）
   - 联系电话（必填）
   - 联系邮箱
   - 线索来源（下拉选择）
   - 线索优先级（高、中、低）

2. 详细信息区域
   - 线索描述（富文本编辑器）
   - 预计金额
   - 预计成交时间
   - 所属行业
   - 客户规模

3. 附件上传区域
   - 支持多文件上传
   - 文件类型限制
   - 文件大小限制

#### 新建线索弹窗

![新建线索](./images/front_system/市场经营/市场经营_线索录入_新建线索.png)

#### 事件操作
- 点击保存：验证表单数据，提交创建线索
- 点击取消：关闭表单，返回线索池
- 文件上传：支持拖拽上传和点击上传
- 客户选择：支持搜索现有客户或新建客户

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(LeadForm.vue)
    participant A as API层(Api.js)
    participant C as LeadManagementController
    participant S as LeadManagementService
    participant CC as CustomerController
    participant CS as CustomerService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant F2 as 文件服务(RES)
    participant P as 权限服务(PMI)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据
    A-->>F: 权限验证通过

    F->>A: getCustomerList() 获取客户下拉列表
    A->>CC: GET /pms/customerManagement/list
    Note over A,CC: 前端文件: src/views/market/leads/LeadForm.vue<br/>调用函数: getCustomerList()
    CC->>CS: CustomerService.getList()
    CS->>R: 查询缓存 market:customer:list
    alt 缓存命中
        R-->>CS: 返回缓存数据
    else 缓存未命中
        CS->>D: 查询客户列表
        Note over CS,D: SQL: SELECT id, customer_name FROM pms_customer_management<br/>WHERE is_deleted = 0 ORDER BY customer_name
        D-->>CS: 返回客户列表
        CS->>R: 更新缓存 market:customer:list (1小时)
    end
    CS-->>CC: List<CustomerVO>
    CC-->>A: ResponseDTO<List<CustomerVO>>
    A-->>F: 填充客户下拉选择器

    F->>A: uploadFile() 上传附件
    A->>F2: POST /res/file/upload
    Note over A,F2: 文件上传到文件服务<br/>支持多文件上传
    F2-->>A: 返回文件信息 FileVO
    A-->>F: 显示上传成功，保存文件ID

    F->>A: createLead() 提交线索表单
    A->>C: POST /pms/leadManagement/add
    Note over A,C: 前端文件: src/views/market/leads/LeadForm.vue<br/>调用函数: createLead()<br/>表单验证: 必填字段验证
    C->>S: LeadManagementService.create()
    Note over C,S: Controller: LeadManagementController<br/>Service: LeadManagementService

    alt 客户不存在，需要新建
        S->>CS: CustomerService.create()
        CS->>D: 插入新客户
        Note over CS,D: SQL: INSERT INTO pms_customer_management<br/>(customer_name, contact_person, contact_phone, ...)<br/>VALUES (?, ?, ?, ...)
        D-->>CS: 返回客户ID
        CS->>R: 清除客户缓存 market:customer:list
        CS-->>S: 返回新客户ID
    end

    S->>D: 插入线索数据
    Note over S,D: SQL: INSERT INTO pms_lead_management<br/>(lead_name, customer_name, contact_person, contact_phone,<br/>lead_source, lead_priority, description, estimated_amount,<br/>estimated_close_date, industry, company_size, attachment_urls)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    D-->>S: 返回线索ID

    alt 有附件上传
        S->>F2: 关联附件到线索
        Note over S,F2: 更新文件的data_id为线索ID
        F2-->>S: 关联成功
    end

    S->>R: 清除相关缓存 market:lead:list:*
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> leadId
    A-->>F: 显示创建成功，跳转到线索列表
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 线索名称 | leadName | pms_lead_management | lead_name | 直接映射 | 必填，线索的业务标识名称 |
| 客户名称 | customerName | pms_lead_management | customer_name | 下拉选择/新建 | 可从客户列表选择或新建客户 |
| 联系人姓名 | contactPerson | pms_lead_management | contact_person | 直接映射 | 必填，客户方主要联系人 |
| 联系电话 | contactPhone | pms_lead_management | contact_phone | 直接映射 | 必填，支持手机和固话格式 |
| 联系邮箱 | contactEmail | pms_lead_management | contact_email | 邮箱格式验证 | 可选，需验证邮箱格式 |
| 线索来源 | leadSource | pms_lead_management | lead_source | 下拉选择 | 网站、电话、推荐、展会等 |
| 线索优先级 | leadPriority | pms_lead_management | lead_priority | 下拉选择 | 高、中、低三个级别 |
| 线索描述 | description | pms_lead_management | description | 富文本转换 | 支持富文本编辑，存储HTML格式 |
| 预计金额 | estimatedAmount | pms_lead_management | estimated_amount | 数字验证 | 必须为正数，最多2位小数 |
| 预计成交时间 | estimatedCloseDate | pms_lead_management | estimated_close_date | 日期选择 | 不能早于当前日期 |
| 所属行业 | industry | pms_lead_management | industry | 下拉选择 | 从行业字典表获取 |
| 客户规模 | companySize | pms_lead_management | company_size | 下拉选择 | 大型、中型、小型、微型 |
| 附件文件 | attachmentUrls | pms_lead_management | attachment_urls | JSON数组 | 存储文件ID数组，关联文件服务 |

#### 客户下拉选择字段对应关系

| 显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|---------|-----------|-----------|-----------|----------|------|
| 客户名称 | customerName | pms_customer_management | customer_name | 直接映射 | 下拉选项显示文本 |
| 客户ID | customerId | pms_customer_management | id | 直接映射 | 下拉选项值，用于关联 |
| 客户类型 | customerType | pms_customer_management | customer_type | 枚举转换 | 企业、个人等类型标识 |

#### 表单验证规则

| 字段名 | 验证规则 | 错误提示 | 数据库约束 |
|--------|----------|----------|-----------|
| leadName | 必填，长度1-100 | 请输入线索名称，不超过100字符 | VARCHAR(255) NOT NULL |
| customerName | 必填，长度1-100 | 请选择或输入客户名称 | VARCHAR(255) NOT NULL |
| contactPerson | 必填，长度1-50 | 请输入联系人姓名 | VARCHAR(100) |
| contactPhone | 必填，手机/固话格式 | 请输入正确的联系电话 | VARCHAR(20) |
| contactEmail | 邮箱格式 | 请输入正确的邮箱地址 | VARCHAR(100) |
| estimatedAmount | 正数，最多2位小数 | 请输入正确的金额 | DECIMAL(15,2) |
| estimatedCloseDate | 不早于今天 | 预计成交时间不能早于今天 | DATETIME |

#### 数据异常排查指南

**客户下拉列表为空**：
1. 检查 pms_customer_management 表是否有数据
2. 确认 is_deleted = 0 条件
3. 检查用户是否有客户查看权限

**文件上传失败**：
1. 检查文件服务是否正常运行
2. 确认文件大小和类型限制
3. 检查文件存储路径权限

**表单提交失败**：
1. 检查必填字段是否都已填写
2. 确认数据格式验证是否通过
3. 检查数据库字段长度限制

**客户新建失败**：
1. 检查客户名称是否重复
2. 确认客户表必填字段
3. 检查用户是否有客户创建权限

#### API接口
- POST /pms/leadManagement/add - 创建线索
- GET /pms/customerManagement/list - 获取客户列表
- POST /pms/customerManagement/add - 创建新客户
- POST /res/file/upload - 上传附件
- GET /pmi/power/function - 获取页面权限信息

### 2.3 需求管理

#### 2.3.1 需求列表

![需求管理](./images/front_system/市场经营/市场经营_需求管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：需求列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 需求编号输入框
   - 需求名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已拒绝、已报名、已分发）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建需求按钮（主要按钮）
   - 批量分发按钮（需选中数据）
   - 批量报名按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 需求列表表格
   - 复选框列（支持全选/反选）
   - 需求编号（自动生成，可点击查看详情）
   - 需求名称
   - 客户名称
   - 负责人
   - 需求状态（带状态标签）
   - 预计金额
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、分发、报名、查看详情）

#### 需求管理子页面

![基本信息](./images/front_system/市场经营/市场经营_需求管理_基本信息.png)

![基本信息新建](./images/front_system/市场经营/市场经营_需求管理_基本信息_新建.png)

![创建需求](./images/front_system/市场经营/市场经营_需求管理_创建需求.png)

![双方签约主体信息](./images/front_system/市场经营/市场经营_需求管理_双方签约主体信息.png)

![报价基本信息](./images/front_system/市场经营/市场经营_需求管理_报价基本信息.png)

![附件](./images/front_system/市场经营/市场经营_需求管理_附件.png)

#### 事件操作
- 点击新建需求：打开新建需求表单页面
- 点击编辑：打开编辑需求表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击分发：打开分发弹窗，选择分发对象
- 点击报名：确认报名参与该需求
- 点击导出：根据当前筛选条件导出Excel文件
- 点击需求编号：跳转到需求详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(RequirementList.vue)
    participant A as API层(Api.js)
    participant C as RequirementMangementController
    participant S as RequirementMangementService
    participant W as 工作流服务(WF)
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)
    participant F2 as 文件服务(RES)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getRequirementList() 获取需求列表
    A->>C: POST /pms/requirementMangement/page
    Note over A,C: 前端文件: src/views/market/requirements/RequirementList.vue<br/>调用函数: getRequirementList()<br/>权限验证: pageCode='requirementMangement'
    C->>S: RequirementMangementService.pages()
    Note over C,S: Controller: RequirementMangementController<br/>Service: RequirementMangementService
    S->>R: 查询缓存 market:req:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询需求数据
        Note over S,D: SQL: SELECT * FROM pms_requirement_mangement<br/>WHERE requirement_status = ? AND customer_name LIKE ?<br/>ORDER BY create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:req:list:${searchHash} (30分钟)
    end
    S-->>C: Page<RequirementMangementVO>
    C-->>A: ResponseDTO<Page<RequirementMangementVO>>
    A-->>F: 渲染需求列表表格

    F->>A: createRequirement() 新建需求
    A->>C: POST /pms/requirementMangement/add
    Note over A,C: 前端文件: src/views/market/requirements/RequirementForm.vue<br/>调用函数: createRequirement()
    C->>S: RequirementMangementService.save()
    S->>D: 插入需求数据
    Note over S,D: SQL: INSERT INTO pms_requirement_mangement<br/>(requirement_no, requirement_name, customer_name, requirement_type,<br/>requirement_status, description, estimated_amount, ...)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ...)
    D-->>S: 返回需求ID

    alt 需要启动工作流
        S->>W: 启动需求审批流程
        Note over S,W: 工作流类型: 需求审批<br/>业务ID: requirementId<br/>申请人: 当前用户
        W-->>S: 返回流程实例ID
        S->>D: 更新需求状态为待审核
        Note over S,D: SQL: UPDATE pms_requirement_mangement<br/>SET requirement_status = 'PENDING_APPROVAL',<br/>workflow_instance_id = ? WHERE id = ?
        D-->>S: 更新成功
    end

    S->>R: 清除相关缓存 market:req:list:*
    S->>M: 发送消息通知
    Note over S,M: 消息类型: 需求创建通知<br/>接收人: 需求审核人
    M-->>S: 消息发送成功
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> requirementId
    A-->>F: 显示创建成功提示

    F->>A: distributeRequirement() 分发需求
    A->>C: POST /pms/requirementMangement/distribute
    C->>S: RequirementMangementService.distribute()
    S->>D: 更新需求状态
    Note over S,D: SQL: UPDATE pms_requirement_mangement<br/>SET requirement_status = 'DISTRIBUTED'<br/>WHERE id = ?
    D-->>S: 更新成功
    S->>M: 发送分发通知
    Note over S,M: 消息类型: 需求分发通知<br/>接收人: 相关部门人员
    M-->>S: 消息发送成功
    S-->>C: 返回分发结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示分发成功提示

    F->>A: applyRequirement() 报名需求
    A->>C: POST /pms/requirementMangement/apply
    C->>S: RequirementMangementService.apply()
    S->>D: 插入报名记录
    Note over S,D: SQL: INSERT INTO pms_requirement_apply<br/>(requirement_id, apply_user_id, apply_time, apply_reason)<br/>VALUES (?, ?, ?, ?)
    D-->>S: 插入成功
    S->>M: 发送报名通知
    Note over S,M: 消息类型: 需求报名通知<br/>接收人: 需求负责人
    M-->>S: 消息发送成功
    S-->>C: 返回报名结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示报名成功提示
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 需求编号 | requirementNo | pms_requirement_mangement | requirement_no | 自动生成 | REQ+年月日+4位序号，如REQ202412010001 |
| 需求名称 | requirementName | pms_requirement_mangement | requirement_name | 直接映射 | 需求的业务标识名称 |
| 客户名称 | customerName | pms_requirement_mangement | customer_name | 直接映射 | 需求对应的客户名称 |
| 需求类型 | requirementType | pms_requirement_mangement | requirement_type | 枚举转换 | 软件开发、系统集成、咨询服务等 |
| 需求状态 | requirementStatus | pms_requirement_mangement | requirement_status | 枚举转换 | 草稿、待审核、已审核、已拒绝、已分发 |
| 需求描述 | description | pms_requirement_mangement | description | 富文本转换 | 支持富文本，存储HTML格式 |
| 预计金额 | estimatedAmount | pms_requirement_mangement | estimated_amount | 金额格式化 | 显示为货币格式，保留2位小数 |
| 预计开始时间 | estimatedStartDate | pms_requirement_mangement | estimated_start_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 预计结束时间 | estimatedEndDate | pms_requirement_mangement | estimated_end_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 优先级 | priority | pms_requirement_mangement | priority | 枚举转换 | 高、中、低优先级显示 |
| 负责人 | responsiblePerson | pms_requirement_mangement + sys_user | responsible_person → user_name | 关联查询 | 通过responsible_person关联sys_user表 |
| 客户联系人 | customerContact | pms_requirement_mangement | customer_contact | 直接映射 | 客户方联系人姓名 |
| 客户电话 | customerPhone | pms_requirement_mangement | customer_phone | 直接映射 | 客户方联系电话 |
| 附件 | attachmentUrls | pms_requirement_mangement | attachment_urls | JSON数组解析 | 解析文件ID数组，获取文件信息 |
| 创建时间 | createTime | pms_requirement_mangement | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_requirement_mangement | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_requirement_mangement + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 需求编号 | requirementNoSearch | requirement_no | LIKE '%value%' | 模糊查询需求编号 |
| 需求名称 | requirementNameSearch | requirement_name | LIKE '%value%' | 模糊查询需求名称 |
| 客户名称 | customerNameSearch | customer_name | LIKE '%value%' | 模糊查询客户名称 |
| 需求状态 | requirementStatusSearch | requirement_status | = 'value' | 精确匹配状态值 |
| 需求类型 | requirementTypeSearch | requirement_type | = 'value' | 精确匹配类型值 |
| 创建时间范围 | createTimeRange | create_time | BETWEEN startDate AND endDate | 时间范围查询 |
| 负责人 | responsiblePersonSearch | responsible_person | = userId | 精确匹配用户ID |

#### 工作流相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 工作流状态 | workflowStatus | pms_requirement_mangement | workflow_instance_id | 关联工作流 | 通过工作流实例ID获取审批状态 |
| 当前审批人 | currentApprover | 工作流系统 | - | 工作流查询 | 从工作流系统获取当前待审批人 |
| 审批意见 | approvalComment | 工作流系统 | - | 工作流查询 | 从工作流系统获取审批意见 |

#### 数据异常排查指南

**需求编号重复**：
1. 检查需求编号生成规则是否正确
2. 确认数据库唯一索引是否生效
3. 检查并发创建时的锁机制

**负责人显示异常**：
1. 检查 sys_user 表中对应用户是否存在
2. 确认 responsible_person 字段值是否有效
3. 检查用户状态是否为启用

**工作流状态异常**：
1. 检查工作流服务是否正常运行
2. 确认 workflow_instance_id 是否正确
3. 检查工作流模板配置是否正确

**附件显示异常**：
1. 检查文件服务是否正常
2. 确认 attachment_urls 字段JSON格式
3. 检查文件是否被删除或移动

#### API接口
- POST /pms/requirementMangement/page - 获取需求分页列表
- POST /pms/requirementMangement/add - 创建新需求
- PUT /pms/requirementMangement/edit - 更新需求信息
- DELETE /pms/requirementMangement/remove - 删除需求
- POST /pms/requirementMangement/distribute - 分发需求
- POST /pms/requirementMangement/apply - 报名需求
- POST /pms/requirementMangement/export/excel - 导出需求数据
- GET /pmi/power/function - 获取页面权限信息
- POST /wf/flowTemplateBusiness/one/start - 启动工作流

### 2.4 报价管理

#### 2.4.1 报价列表

![报价管理](./images/front_system/市场经营/市场经营_报价管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：报价列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 报价编号输入框
   - 报价名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已发出、已中标、已失效）
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建报价按钮（主要按钮）
   - 批量发出按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 报价列表表格
   - 复选框列（支持全选/反选）
   - 报价编号（自动生成，可点击查看详情）
   - 报价名称
   - 客户名称
   - 负责人
   - 报价状态（带状态标签）
   - 报价金额
   - 有效期
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、发出、确认、查看详情）

#### 报价管理子页面

![基本信息](./images/front_system/市场经营/市场经营_报价管理_基本信息.png)

![基本信息创建](./images/front_system/市场经营/市场经营_报价管理_基本信息_创建.png)

![创建报价](./images/front_system/市场经营/市场经营_报价管理_创建.png)

![创建报价详情](./images/front_system/市场经营/市场经营_报价管理_创建报价.png)

![双方签约主体信息](./images/front_system/市场经营/市场经营_报价管理_创建双方签约主体信息.png)

#### 事件操作
- 点击新建报价：打开新建报价表单页面
- 点击编辑：打开编辑报价表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击发出：确认发出报价给客户
- 点击确认：确认报价单内容
- 点击导出：根据当前筛选条件导出Excel文件
- 点击报价编号：跳转到报价详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(QuotationList.vue)
    participant A as API层(Api.js)
    participant C as QuotationManagementController
    participant S as QuotationManagementService
    participant W as 工作流服务(WF)
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)
    participant F2 as 文件服务(RES)
    participant E as 邮件服务(EMAIL)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getQuotationList() 获取报价列表
    A->>C: POST /pms/quotationManagement/page
    Note over A,C: 前端文件: src/views/market/quotations/QuotationList.vue<br/>调用函数: getQuotationList()<br/>权限验证: pageCode='quotationManagement'
    C->>S: QuotationManagementService.pages()
    Note over C,S: Controller: QuotationManagementController<br/>Service: QuotationManagementService
    S->>R: 查询缓存 market:quote:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询报价数据
        Note over S,D: SQL: SELECT * FROM pms_quotation_management<br/>WHERE quotation_status = ? AND customer_name LIKE ?<br/>ORDER BY create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:quote:list:${searchHash} (30分钟)
    end
    S-->>C: Page<QuotationManagementVO>
    C-->>A: ResponseDTO<Page<QuotationManagementVO>>
    A-->>F: 渲染报价列表表格

    F->>A: createQuotation() 新建报价
    A->>C: POST /pms/quotationManagement/add
    Note over A,C: 前端文件: src/views/market/quotations/QuotationForm.vue<br/>调用函数: createQuotation()
    C->>S: QuotationManagementService.save()
    S->>D: 插入报价数据
    Note over S,D: SQL: INSERT INTO pms_quotation_management<br/>(quotation_no, quotation_name, customer_name, requirement_id,<br/>quotation_status, total_amount, valid_until, description, ...)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ?, ...)
    D-->>S: 返回报价ID

    alt 需要启动工作流
        S->>W: 启动报价审批流程
        Note over S,W: 工作流类型: 报价审批<br/>业务ID: quotationId<br/>申请人: 当前用户
        W-->>S: 返回流程实例ID
        S->>D: 更新报价状态为待审核
        Note over S,D: SQL: UPDATE pms_quotation_management<br/>SET quotation_status = 'PENDING_APPROVAL',<br/>workflow_instance_id = ? WHERE id = ?
        D-->>S: 更新成功
    end

    S->>R: 清除相关缓存 market:quote:list:*
    S->>M: 发送消息通知
    Note over S,M: 消息类型: 报价创建通知<br/>接收人: 报价审核人
    M-->>S: 消息发送成功
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> quotationId
    A-->>F: 显示创建成功提示

    F->>A: sendQuotation() 发出报价
    A->>C: POST /pms/quotationManagement/sendOut
    C->>S: QuotationManagementService.sendOut()
    S->>D: 更新报价状态
    Note over S,D: SQL: UPDATE pms_quotation_management<br/>SET quotation_status = 'SENT', send_time = NOW()<br/>WHERE id = ?
    D-->>S: 更新成功

    alt 发送邮件给客户
        S->>E: 发送报价邮件
        Note over S,E: 邮件内容: 报价单PDF<br/>收件人: 客户邮箱<br/>抄送: 销售人员
        E-->>S: 邮件发送成功
    end

    S->>R: 更新缓存 market:quote:detail:${quotationId}
    S->>M: 发送发出通知
    Note over S,M: 消息类型: 报价发出通知<br/>接收人: 相关人员
    M-->>S: 消息发送成功
    S-->>C: 返回发出结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示发出成功提示

    F->>A: confirmQuotation() 确认报价
    A->>C: POST /pms/quotationManagement/confirm
    C->>S: QuotationManagementService.confirm()
    S->>D: 更新报价状态为已确认
    Note over S,D: SQL: UPDATE pms_quotation_management<br/>SET quotation_status = 'CONFIRMED', confirm_time = NOW()<br/>WHERE id = ?
    D-->>S: 更新成功
    S->>M: 发送确认通知
    Note over S,M: 消息类型: 报价确认通知<br/>接收人: 销售团队
    M-->>S: 消息发送成功
    S-->>C: 返回确认结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示确认成功提示
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 报价编号 | quotationNo | pms_quotation_management | quotation_no | 自动生成 | QUO+年月日+4位序号，如QUO202412010001 |
| 报价名称 | quotationName | pms_quotation_management | quotation_name | 直接映射 | 报价的业务标识名称 |
| 客户名称 | customerName | pms_quotation_management | customer_name | 直接映射 | 报价对应的客户名称 |
| 关联需求 | requirementId | pms_quotation_management + pms_requirement_mangement | requirement_id → requirement_name | 关联查询 | 通过requirement_id关联需求表获取需求名称 |
| 报价状态 | quotationStatus | pms_quotation_management | quotation_status | 枚举转换 | 草稿、待审核、已审核、已发出、已中标、已失效 |
| 报价总金额 | totalAmount | pms_quotation_management | total_amount | 金额格式化 | 显示为货币格式，保留2位小数 |
| 有效期至 | validUntil | pms_quotation_management | valid_until | 日期格式化 | YYYY-MM-DD格式显示 |
| 报价日期 | quotationDate | pms_quotation_management | quotation_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 报价说明 | description | pms_quotation_management | description | 富文本转换 | 支持富文本，存储HTML格式 |
| 条款条件 | termsConditions | pms_quotation_management | terms_conditions | 富文本转换 | 支持富文本，存储HTML格式 |
| 负责人 | responsiblePerson | pms_quotation_management + sys_user | responsible_person → user_name | 关联查询 | 通过responsible_person关联sys_user表 |
| 客户联系人 | customerContact | pms_quotation_management | customer_contact | 直接映射 | 客户方联系人姓名 |
| 客户电话 | customerPhone | pms_quotation_management | customer_phone | 直接映射 | 客户方联系电话 |
| 附件 | attachmentUrls | pms_quotation_management | attachment_urls | JSON数组解析 | 解析文件ID数组，获取文件信息 |
| 发送时间 | sendTime | pms_quotation_management | send_time | 时间格式化 | 报价发出时间，YYYY-MM-DD HH:mm:ss |
| 确认时间 | confirmTime | pms_quotation_management | confirm_time | 时间格式化 | 报价确认时间，YYYY-MM-DD HH:mm:ss |
| 创建时间 | createTime | pms_quotation_management | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_quotation_management | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_quotation_management + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 报价编号 | quotationNoSearch | quotation_no | LIKE '%value%' | 模糊查询报价编号 |
| 报价名称 | quotationNameSearch | quotation_name | LIKE '%value%' | 模糊查询报价名称 |
| 客户名称 | customerNameSearch | customer_name | LIKE '%value%' | 模糊查询客户名称 |
| 报价状态 | quotationStatusSearch | quotation_status | = 'value' | 精确匹配状态值 |
| 金额范围 | amountRange | total_amount | BETWEEN minAmount AND maxAmount | 金额范围查询 |
| 报价日期范围 | quotationDateRange | quotation_date | BETWEEN startDate AND endDate | 日期范围查询 |
| 负责人 | responsiblePersonSearch | responsible_person | = userId | 精确匹配用户ID |

#### 报价明细相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 明细项目名称 | itemName | pms_quotation_item | item_name | 直接映射 | 报价明细项目名称 |
| 明细项目描述 | itemDescription | pms_quotation_item | item_description | 直接映射 | 报价明细项目描述 |
| 数量 | quantity | pms_quotation_item | quantity | 数字格式化 | 项目数量 |
| 单价 | unitPrice | pms_quotation_item | unit_price | 金额格式化 | 项目单价 |
| 小计 | subtotal | pms_quotation_item | quantity * unit_price | 计算字段 | 数量*单价 |

#### 工作流和邮件相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 工作流状态 | workflowStatus | pms_quotation_management | workflow_instance_id | 关联工作流 | 通过工作流实例ID获取审批状态 |
| 邮件发送状态 | emailStatus | pms_quotation_management | email_send_status | 枚举转换 | 未发送、已发送、发送失败 |
| 客户邮箱 | customerEmail | pms_quotation_management | customer_email | 直接映射 | 用于发送报价邮件 |

#### 数据异常排查指南

**报价编号重复**：
1. 检查报价编号生成规则是否正确
2. 确认数据库唯一索引是否生效
3. 检查并发创建时的锁机制

**报价总金额计算错误**：
1. 检查报价明细表数据是否正确
2. 确认单价和数量的数据类型
3. 检查金额计算逻辑和精度

**邮件发送失败**：
1. 检查邮件服务配置是否正确
2. 确认客户邮箱地址格式
3. 检查邮件模板是否存在

**关联需求显示异常**：
1. 检查需求表中对应记录是否存在
2. 确认 requirement_id 字段值是否有效
3. 检查需求是否被删除

#### API接口
- POST /pms/quotationManagement/page - 获取报价分页列表
- POST /pms/quotationManagement/add - 创建新报价
- PUT /pms/quotationManagement/edit - 更新报价信息
- DELETE /pms/quotationManagement/remove - 删除报价
- POST /pms/quotationManagement/sendOut - 发出报价
- POST /pms/quotationManagement/confirm - 确认报价
- POST /pms/quotationManagement/export/excel - 导出报价数据
- GET /pmi/power/function - 获取页面权限信息
- POST /wf/flowTemplateBusiness/one/start - 启动工作流

### 2.5 合同管理

#### 2.5.1 合同列表

![合同管理](./images/front_system/市场经营/市场经营_合同管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：合同列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 合同编号输入框
   - 合同名称输入框
   - 客户名称输入框
   - 状态下拉选择器（全部、草稿、待审核、已审核、已签署、执行中、已完成、已终止）
   - 时间范围选择器（创建时间、签署时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建合同按钮（主要按钮）
   - 批量审核按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 合同列表表格
   - 复选框列（支持全选/反选）
   - 合同编号（自动生成，可点击查看详情）
   - 合同名称
   - 客户名称
   - 负责人
   - 合同状态（带状态标签）
   - 合同金额
   - 签署时间
   - 创建时间
   - 更新时间
   - 操作列（编辑、删除、审核、查看详情）

#### 合同管理子页面

![合同创建](./images/front_system/市场经营/市场经营_合同管理_创建.png)

#### 事件操作
- 点击新建合同：打开新建合同表单页面
- 点击编辑：打开编辑合同表单页面
- 点击删除：显示确认对话框，确认后删除
- 点击审核：打开审核弹窗，填写审核意见
- 点击导出：根据当前筛选条件导出Excel文件
- 点击合同编号：跳转到合同详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(MarketContractList.vue)
    participant A as API层(Api.js)
    participant C as MarketContractController
    participant S as MarketContractService
    participant W as 工作流服务(WF)
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)
    participant F2 as 文件服务(RES)
    participant DTC as 第三方合同服务(ICM)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getContractList() 获取合同列表
    A->>C: POST /pms/marketContract/page
    Note over A,C: 前端文件: src/views/market/contracts/MarketContractList.vue<br/>调用函数: getContractList()<br/>权限验证: pageCode='marketContract'
    C->>S: MarketContractService.pages()
    Note over C,S: Controller: MarketContractController<br/>Service: MarketContractService
    S->>R: 查询缓存 market:contract:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询合同数据
        Note over S,D: SQL: SELECT * FROM pms_market_contract<br/>WHERE contract_status = ? AND customer_name LIKE ?<br/>ORDER BY create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:contract:list:${searchHash} (30分钟)
    end
    S-->>C: Page<MarketContractVO>
    C-->>A: ResponseDTO<Page<MarketContractVO>>
    A-->>F: 渲染合同列表表格

    F->>A: createContract() 新建合同
    A->>C: POST /pms/marketContract/add
    Note over A,C: 前端文件: src/views/market/contracts/MarketContractForm.vue<br/>调用函数: createContract()
    C->>S: MarketContractService.save()
    S->>D: 插入合同数据
    Note over S,D: SQL: INSERT INTO pms_market_contract<br/>(contract_no, contract_name, customer_name, quotation_id,<br/>contract_status, contract_amount, sign_date, start_date, end_date, ...)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ...)
    D-->>S: 返回合同ID

    alt 同步到第三方合同系统
        S->>DTC: 同步合同信息到ICM
        Note over S,DTC: 接口: /third/api/marketContract/add<br/>数据: 合同基本信息
        DTC-->>S: 同步成功
    end

    alt 需要启动工作流
        S->>W: 启动合同审批流程
        Note over S,W: 工作流类型: 合同审批<br/>业务ID: contractId<br/>申请人: 当前用户
        W-->>S: 返回流程实例ID
        S->>D: 更新合同状态为待审核
        Note over S,D: SQL: UPDATE pms_market_contract<br/>SET contract_status = 'PENDING_APPROVAL',<br/>workflow_instance_id = ? WHERE id = ?
        D-->>S: 更新成功
    end

    S->>R: 清除相关缓存 market:contract:list:*
    S->>M: 发送消息通知
    Note over S,M: 消息类型: 合同创建通知<br/>接收人: 合同审核人
    M-->>S: 消息发送成功
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> contractId
    A-->>F: 显示创建成功提示

    F->>A: approveContract() 审核合同
    A->>C: POST /pms/marketContract/approve
    C->>S: MarketContractService.approve()
    S->>D: 更新合同状态
    Note over S,D: SQL: UPDATE pms_market_contract<br/>SET contract_status = 'APPROVED', approve_time = NOW(),<br/>approve_user_id = ?, approve_comment = ?<br/>WHERE id = ?
    D-->>S: 更新成功

    alt 同步审核状态到第三方系统
        S->>DTC: 同步审核状态到ICM
        Note over S,DTC: 接口: /third/api/marketContract/edit<br/>数据: 审核状态和意见
        DTC-->>S: 同步成功
    end

    S->>W: 完成工作流节点
    Note over S,W: 更新工作流状态为已完成
    W-->>S: 工作流完成
    S->>R: 更新缓存 market:contract:detail:${contractId}
    S->>M: 发送审核通知
    Note over S,M: 消息类型: 合同审核通知<br/>接收人: 申请人和相关人员
    M-->>S: 消息发送成功
    S-->>C: 返回审核结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示审核成功提示

    F->>A: exportContracts() 导出合同
    A->>C: POST /pms/marketContract/export/excel
    C->>S: MarketContractService.exportExcel()
    S->>D: 查询导出数据
    Note over S,D: SQL: SELECT * FROM pms_market_contract<br/>WHERE conditions...
    D-->>S: 返回导出数据
    S-->>C: 生成Excel文件流
    C-->>A: 返回文件流
    A-->>F: 下载Excel文件
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 合同编号 | contractNo | pms_market_contract | contract_no | 自动生成 | CON+年月日+4位序号，如CON202412010001 |
| 合同名称 | contractName | pms_market_contract | contract_name | 直接映射 | 合同的业务标识名称 |
| 客户名称 | customerName | pms_market_contract | customer_name | 直接映射 | 合同对应的客户名称 |
| 关联报价 | quotationId | pms_market_contract + pms_quotation_management | quotation_id → quotation_name | 关联查询 | 通过quotation_id关联报价表获取报价名称 |
| 合同状态 | contractStatus | pms_market_contract | contract_status | 枚举转换 | 草稿、待审核、已审核、已签署、执行中、已完成、已终止 |
| 合同金额 | contractAmount | pms_market_contract | contract_amount | 金额格式化 | 显示为货币格式，保留2位小数 |
| 签署日期 | signDate | pms_market_contract | sign_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 合同开始日期 | startDate | pms_market_contract | start_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 合同结束日期 | endDate | pms_market_contract | end_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 合同类型 | contractType | pms_market_contract | contract_type | 枚举转换 | 销售合同、服务合同、采购合同等 |
| 付款方式 | paymentMethod | pms_market_contract | payment_method | 枚举转换 | 一次性付款、分期付款、按进度付款 |
| 合同描述 | description | pms_market_contract | description | 富文本转换 | 支持富文本，存储HTML格式 |
| 合同条款 | contractTerms | pms_market_contract | contract_terms | 富文本转换 | 支持富文本，存储HTML格式 |
| 负责人 | responsiblePerson | pms_market_contract + sys_user | responsible_person → user_name | 关联查询 | 通过responsible_person关联sys_user表 |
| 客户联系人 | customerContact | pms_market_contract | customer_contact | 直接映射 | 客户方联系人姓名 |
| 客户电话 | customerPhone | pms_market_contract | customer_phone | 直接映射 | 客户方联系电话 |
| 附件 | attachmentUrls | pms_market_contract | attachment_urls | JSON数组解析 | 解析文件ID数组，获取文件信息 |
| 审核时间 | approveTime | pms_market_contract | approve_time | 时间格式化 | 合同审核时间，YYYY-MM-DD HH:mm:ss |
| 审核人 | approveUserId | pms_market_contract + sys_user | approve_user_id → user_name | 关联查询 | 通过approve_user_id关联sys_user表 |
| 审核意见 | approveComment | pms_market_contract | approve_comment | 直接映射 | 审核意见内容 |
| 创建时间 | createTime | pms_market_contract | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_market_contract | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_market_contract + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 合同编号 | contractNoSearch | contract_no | LIKE '%value%' | 模糊查询合同编号 |
| 合同名称 | contractNameSearch | contract_name | LIKE '%value%' | 模糊查询合同名称 |
| 客户名称 | customerNameSearch | customer_name | LIKE '%value%' | 模糊查询客户名称 |
| 合同状态 | contractStatusSearch | contract_status | = 'value' | 精确匹配状态值 |
| 合同类型 | contractTypeSearch | contract_type | = 'value' | 精确匹配类型值 |
| 金额范围 | amountRange | contract_amount | BETWEEN minAmount AND maxAmount | 金额范围查询 |
| 签署日期范围 | signDateRange | sign_date | BETWEEN startDate AND endDate | 日期范围查询 |
| 负责人 | responsiblePersonSearch | responsible_person | = userId | 精确匹配用户ID |

#### 第三方系统同步字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 第三方合同ID | thirdPartyContractId | pms_market_contract | third_party_contract_id | 直接映射 | ICM系统中的合同ID |
| 同步状态 | syncStatus | pms_market_contract | sync_status | 枚举转换 | 未同步、已同步、同步失败 |
| 同步时间 | syncTime | pms_market_contract | sync_time | 时间格式化 | 最后同步时间 |

#### 工作流相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 工作流状态 | workflowStatus | pms_market_contract | workflow_instance_id | 关联工作流 | 通过工作流实例ID获取审批状态 |
| 当前审批人 | currentApprover | 工作流系统 | - | 工作流查询 | 从工作流系统获取当前待审批人 |
| 审批历史 | approvalHistory | 工作流系统 | - | 工作流查询 | 从工作流系统获取审批历史 |

#### 数据异常排查指南

**合同编号重复**：
1. 检查合同编号生成规则是否正确
2. 确认数据库唯一索引是否生效
3. 检查并发创建时的锁机制

**第三方系统同步失败**：
1. 检查ICM系统是否正常运行
2. 确认网络连接和接口权限
3. 检查同步数据格式是否正确

**合同金额计算错误**：
1. 检查关联报价的金额是否正确
2. 确认金额字段数据类型和精度
3. 检查汇率转换逻辑（如涉及）

**审核流程异常**：
1. 检查工作流服务是否正常
2. 确认审核人权限配置
3. 检查工作流模板配置

**关联报价显示异常**：
1. 检查报价表中对应记录是否存在
2. 确认 quotation_id 字段值是否有效
3. 检查报价是否被删除

#### API接口
- POST /pms/marketContract/page - 获取合同分页列表
- POST /pms/marketContract/add - 创建新合同
- PUT /pms/marketContract/edit - 更新合同信息
- DELETE /pms/marketContract/remove - 删除合同
- POST /pms/marketContract/approve - 审核合同
- POST /pms/marketContract/export/excel - 导出合同数据
- GET /pmi/power/function - 获取页面权限信息
- POST /wf/flowTemplateBusiness/one/start - 启动工作流
- POST /third/api/marketContract/add - 同步合同到第三方系统
- POST /third/api/marketContract/edit - 更新第三方系统合同状态

### 2.6 客户管理

![客户管理](./images/front_system/市场经营/市场经营_客户管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：客户列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 客户名称输入框
   - 客户类型下拉选择器
   - 客户等级下拉选择器
   - 所属行业下拉选择器
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建客户按钮（主要按钮）
   - 导出按钮
   - 刷新按钮

3. 客户列表表格
   - 复选框列（支持全选/反选）
   - 客户名称（可点击查看详情）
   - 客户类型
   - 客户等级
   - 所属行业
   - 联系人
   - 联系电话
   - 创建时间
   - 操作列（编辑、删除、查看详情）

#### 事件操作
- 点击新建客户：打开新建客户表单弹窗
- 点击编辑：打开编辑客户表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击导出：根据当前筛选条件导出Excel文件
- 点击客户名称：跳转到客户详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(CustomerList.vue)
    participant A as API层(Api.js)
    participant C as CustomerManagementController
    participant S as CustomerManagementService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant P as 权限服务(PMI)
    participant CRM as 第三方CRM系统

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getCustomerList() 获取客户列表
    A->>C: POST /pms/customerManagement/page
    Note over A,C: 前端文件: src/views/market/customers/CustomerList.vue<br/>调用函数: getCustomerList()<br/>权限验证: pageCode='customerManagement'
    C->>S: CustomerManagementService.pages()
    Note over C,S: Controller: CustomerManagementController<br/>Service: CustomerManagementService
    S->>R: 查询缓存 market:customer:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询客户数据
        Note over S,D: SQL: SELECT * FROM pms_customer_management<br/>WHERE customer_type = ? AND customer_name LIKE ?<br/>ORDER BY create_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:customer:list:${searchHash} (1小时)
    end
    S-->>C: Page<CustomerManagementVO>
    C-->>A: ResponseDTO<Page<CustomerManagementVO>>
    A-->>F: 渲染客户列表表格

    F->>A: createCustomer() 新建客户
    A->>C: POST /pms/customerManagement/add
    Note over A,C: 前端文件: src/views/market/customers/CustomerForm.vue<br/>调用函数: createCustomer()
    C->>S: CustomerManagementService.save()
    S->>D: 插入客户数据
    Note over S,D: SQL: INSERT INTO pms_customer_management<br/>(customer_name, customer_type, customer_level, industry,<br/>company_size, contact_person, contact_phone, contact_email, address, ...)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ...)
    D-->>S: 返回客户ID

    alt 同步到第三方CRM系统
        S->>CRM: 同步客户信息到CRM
        Note over S,CRM: 接口: /crm/api/customer/sync<br/>数据: 客户基本信息和联系方式
        CRM-->>S: 同步成功，返回CRM客户ID
        S->>D: 更新客户的CRM ID
        Note over S,D: SQL: UPDATE pms_customer_management<br/>SET crm_customer_id = ? WHERE id = ?
        D-->>S: 更新成功
    end

    S->>R: 清除相关缓存 market:customer:list:*
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> customerId
    A-->>F: 显示创建成功提示

    F->>A: updateCustomer() 更新客户
    A->>C: PUT /pms/customerManagement/edit
    C->>S: CustomerManagementService.update()
    S->>D: 更新客户数据
    Note over S,D: SQL: UPDATE pms_customer_management<br/>SET customer_name = ?, customer_type = ?, customer_level = ?,<br/>contact_person = ?, contact_phone = ?, update_time = NOW()<br/>WHERE id = ?
    D-->>S: 更新成功

    alt 同步更新到第三方CRM系统
        S->>CRM: 同步更新客户信息到CRM
        Note over S,CRM: 接口: /crm/api/customer/update<br/>数据: 更新的客户信息
        CRM-->>S: 同步成功
    end

    S->>R: 更新缓存 market:customer:detail:${customerId}
    S->>R: 清除列表缓存 market:customer:list:*
    S-->>C: 返回更新结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示更新成功提示

    F->>A: exportCustomers() 导出客户
    A->>C: POST /pms/customerManagement/export/excel
    C->>S: CustomerManagementService.exportExcel()
    S->>D: 查询导出数据
    Note over S,D: SQL: SELECT * FROM pms_customer_management<br/>WHERE conditions...
    D-->>S: 返回导出数据
    S-->>C: 生成Excel文件流
    C-->>A: 返回文件流
    A-->>F: 下载Excel文件
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 客户名称 | customerName | pms_customer_management | customer_name | 直接映射 | 客户的公司或个人名称 |
| 客户类型 | customerType | pms_customer_management | customer_type | 枚举转换 | 企业客户、个人客户、政府机构等 |
| 客户等级 | customerLevel | pms_customer_management | customer_level | 枚举转换 | A级、B级、C级、D级客户等级 |
| 所属行业 | industry | pms_customer_management | industry | 直接映射 | 客户所属行业分类 |
| 公司规模 | companySize | pms_customer_management | company_size | 枚举转换 | 大型、中型、小型、微型企业 |
| 联系人姓名 | contactPerson | pms_customer_management | contact_person | 直接映射 | 主要联系人姓名 |
| 联系电话 | contactPhone | pms_customer_management | contact_phone | 直接映射 | 主要联系电话 |
| 联系邮箱 | contactEmail | pms_customer_management | contact_email | 邮箱格式验证 | 主要联系邮箱 |
| 公司地址 | address | pms_customer_management | address | 直接映射 | 客户公司地址 |
| 邮政编码 | postalCode | pms_customer_management | postal_code | 直接映射 | 地址邮政编码 |
| 所在地区 | region | pms_customer_management | region | 直接映射 | 省市区信息 |
| 客户来源 | customerSource | pms_customer_management | customer_source | 枚举转换 | 网站、推荐、展会、电话等 |
| 客户状态 | customerStatus | pms_customer_management | customer_status | 枚举转换 | 潜在客户、正式客户、流失客户 |
| 信用等级 | creditRating | pms_customer_management | credit_rating | 枚举转换 | AAA、AA、A、B、C信用等级 |
| 年营业额 | annualRevenue | pms_customer_management | annual_revenue | 金额格式化 | 客户年营业额，货币格式显示 |
| 员工人数 | employeeCount | pms_customer_management | employee_count | 数字格式化 | 客户公司员工人数 |
| 网站地址 | website | pms_customer_management | website | URL格式验证 | 客户公司官网地址 |
| 客户描述 | description | pms_customer_management | description | 富文本转换 | 客户详细描述信息 |
| 备注信息 | remarks | pms_customer_management | remarks | 直接映射 | 客户备注信息 |
| 创建时间 | createTime | pms_customer_management | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_customer_management | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_customer_management + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 客户名称 | customerNameSearch | customer_name | LIKE '%value%' | 模糊查询客户名称 |
| 客户类型 | customerTypeSearch | customer_type | = 'value' | 精确匹配客户类型 |
| 客户等级 | customerLevelSearch | customer_level | = 'value' | 精确匹配客户等级 |
| 所属行业 | industrySearch | industry | = 'value' | 精确匹配行业 |
| 客户状态 | customerStatusSearch | customer_status | = 'value' | 精确匹配客户状态 |
| 所在地区 | regionSearch | region | LIKE '%value%' | 模糊查询地区 |
| 创建时间范围 | createTimeRange | create_time | BETWEEN startDate AND endDate | 时间范围查询 |

#### CRM系统同步字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| CRM客户ID | crmCustomerId | pms_customer_management | crm_customer_id | 直接映射 | CRM系统中的客户ID |
| 同步状态 | syncStatus | pms_customer_management | sync_status | 枚举转换 | 未同步、已同步、同步失败 |
| 同步时间 | syncTime | pms_customer_management | sync_time | 时间格式化 | 最后同步时间 |
| 同步错误信息 | syncErrorMsg | pms_customer_management | sync_error_msg | 直接映射 | 同步失败时的错误信息 |

#### 客户统计相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 线索数量 | leadCount | pms_lead_management | COUNT(*) WHERE customer_name = ? | 统计计算 | 该客户的线索总数 |
| 需求数量 | requirementCount | pms_requirement_mangement | COUNT(*) WHERE customer_name = ? | 统计计算 | 该客户的需求总数 |
| 报价数量 | quotationCount | pms_quotation_management | COUNT(*) WHERE customer_name = ? | 统计计算 | 该客户的报价总数 |
| 合同数量 | contractCount | pms_market_contract | COUNT(*) WHERE customer_name = ? | 统计计算 | 该客户的合同总数 |
| 合同总金额 | totalContractAmount | pms_market_contract | SUM(contract_amount) WHERE customer_name = ? | 金额汇总 | 该客户的合同总金额 |

#### 数据异常排查指南

**客户名称重复**：
1. 检查客户名称唯一性约束
2. 确认是否为同一客户的不同记录
3. 检查数据导入时的去重逻辑

**CRM同步失败**：
1. 检查CRM系统是否正常运行
2. 确认网络连接和接口权限
3. 检查同步数据格式是否正确
4. 查看 sync_error_msg 字段的错误信息

**客户统计数据异常**：
1. 检查关联表中的客户名称是否一致
2. 确认统计SQL的WHERE条件
3. 检查是否存在数据权限过滤

**地区信息显示异常**：
1. 检查地区字典表数据
2. 确认地区编码格式
3. 检查地区层级关系

**联系方式格式异常**：
1. 检查电话号码格式验证规则
2. 确认邮箱地址格式验证
3. 检查网站地址URL格式

#### API接口
- POST /pms/customerManagement/page - 获取客户分页列表
- POST /pms/customerManagement/add - 创建新客户
- PUT /pms/customerManagement/edit - 更新客户信息
- DELETE /pms/customerManagement/remove - 删除客户
- POST /pms/customerManagement/export/excel - 导出客户数据
- GET /pmi/power/function - 获取页面权限信息
- POST /crm/api/customer/sync - 同步客户到CRM系统
- PUT /crm/api/customer/update - 更新CRM系统客户信息

### 2.7 里程碑管理

![里程碑管理](./images/front_system/市场经营/市场经营_里程碑管理.png)

#### 页面布局
- 顶部：搜索栏、筛选条件、操作按钮区域
- 中部：里程碑列表表格，支持多选
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 里程碑名称输入框
   - 合同名称输入框
   - 状态下拉选择器（全部、未开始、进行中、已完成、已逾期）
   - 时间范围选择器（计划时间、实际时间）
   - 搜索按钮、重置按钮

2. 操作按钮区域
   - 新建里程碑按钮（主要按钮）
   - 批量更新按钮（需选中数据）
   - 导出按钮
   - 刷新按钮

3. 里程碑列表表格
   - 复选框列（支持全选/反选）
   - 里程碑名称（可点击查看详情）
   - 合同名称
   - 计划开始时间
   - 计划完成时间
   - 实际完成时间
   - 完成状态（带状态标签）
   - 负责人
   - 操作列（编辑、删除、更新状态、查看详情）

#### 事件操作
- 点击新建里程碑：打开新建里程碑表单弹窗
- 点击编辑：打开编辑里程碑表单弹窗
- 点击删除：显示确认对话框，确认后删除
- 点击更新状态：打开状态更新弹窗
- 点击导出：根据当前筛选条件导出Excel文件
- 点击里程碑名称：跳转到里程碑详情页面

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(MilestoneList.vue)
    participant A as API层(Api.js)
    participant C as ContractMilestoneController
    participant S as ContractMilestoneService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant M as 消息中心(MSC)
    participant P as 权限服务(PMI)
    participant PM as 项目管理系统(PMI)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getMilestoneList() 获取里程碑列表
    A->>C: POST /pms/contractMilestone/page
    Note over A,C: 前端文件: src/views/market/milestones/MilestoneList.vue<br/>调用函数: getMilestoneList()<br/>权限验证: pageCode='contractMilestone'
    C->>S: ContractMilestoneService.pages()
    Note over C,S: Controller: ContractMilestoneController<br/>Service: ContractMilestoneService
    S->>R: 查询缓存 market:milestone:list:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询里程碑数据
        Note over S,D: SQL: SELECT cm.*, mc.contract_name FROM pms_contract_milestone cm<br/>LEFT JOIN pms_market_contract mc ON cm.contract_id = mc.id<br/>WHERE cm.milestone_status = ? AND mc.contract_name LIKE ?<br/>ORDER BY cm.plan_start_date DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:milestone:list:${searchHash} (30分钟)
    end
    S-->>C: Page<ContractMilestoneVO>
    C-->>A: ResponseDTO<Page<ContractMilestoneVO>>
    A-->>F: 渲染里程碑列表表格

    F->>A: createMilestone() 新建里程碑
    A->>C: POST /pms/contractMilestone/add
    Note over A,C: 前端文件: src/views/market/milestones/MilestoneForm.vue<br/>调用函数: createMilestone()
    C->>S: ContractMilestoneService.save()
    S->>D: 插入里程碑数据
    Note over S,D: SQL: INSERT INTO pms_contract_milestone<br/>(milestone_name, contract_id, plan_start_date, plan_end_date,<br/>milestone_status, responsible_person, description, ...)<br/>VALUES (?, ?, ?, ?, ?, ?, ?, ...)
    D-->>S: 返回里程碑ID

    alt 同步到项目管理系统
        S->>PM: 同步里程碑到项目管理
        Note over S,PM: 接口: /pmi/api/milestone/sync<br/>数据: 里程碑计划和负责人信息
        PM-->>S: 同步成功，返回项目里程碑ID
        S->>D: 更新里程碑的项目ID
        Note over S,D: SQL: UPDATE pms_contract_milestone<br/>SET project_milestone_id = ? WHERE id = ?
        D-->>S: 更新成功
    end

    S->>R: 清除相关缓存 market:milestone:list:*
    S->>M: 发送消息通知
    Note over S,M: 消息类型: 里程碑创建通知<br/>接收人: 负责人和相关人员
    M-->>S: 消息发送成功
    S-->>C: 返回创建结果
    C-->>A: ResponseDTO<String> milestoneId
    A-->>F: 显示创建成功提示

    F->>A: updateMilestoneStatus() 更新里程碑状态
    A->>C: PUT /pms/contractMilestone/updateStatus
    C->>S: ContractMilestoneService.updateStatus()
    S->>D: 更新里程碑状态
    Note over S,D: SQL: UPDATE pms_contract_milestone<br/>SET milestone_status = ?, actual_end_date = ?,<br/>completion_rate = ?, update_time = NOW()<br/>WHERE id = ?
    D-->>S: 更新成功

    alt 同步状态到项目管理系统
        S->>PM: 同步里程碑状态到项目管理
        Note over S,PM: 接口: /pmi/api/milestone/updateStatus<br/>数据: 里程碑状态和完成情况
        PM-->>S: 同步成功
    end

    S->>R: 更新缓存 market:milestone:detail:${milestoneId}
    S->>R: 清除列表缓存 market:milestone:list:*
    S->>M: 发送状态更新通知
    Note over S,M: 消息类型: 里程碑状态更新通知<br/>接收人: 合同负责人和项目经理
    M-->>S: 消息发送成功
    S-->>C: 返回更新结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示更新成功提示

    F->>A: exportMilestones() 导出里程碑
    A->>C: POST /pms/contractMilestone/export/excel
    C->>S: ContractMilestoneService.exportExcel()
    S->>D: 查询导出数据
    Note over S,D: SQL: SELECT cm.*, mc.contract_name, mc.customer_name<br/>FROM pms_contract_milestone cm<br/>LEFT JOIN pms_market_contract mc ON cm.contract_id = mc.id<br/>WHERE conditions...
    D-->>S: 返回导出数据
    S-->>C: 生成Excel文件流
    C-->>A: 返回文件流
    A-->>F: 下载Excel文件
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 里程碑名称 | milestoneName | pms_contract_milestone | milestone_name | 直接映射 | 里程碑的业务标识名称 |
| 关联合同 | contractId | pms_contract_milestone + pms_market_contract | contract_id → contract_name | 关联查询 | 通过contract_id关联合同表获取合同名称 |
| 合同编号 | contractNo | pms_market_contract | contract_no | 关联查询 | 通过contract_id关联获取合同编号 |
| 客户名称 | customerName | pms_market_contract | customer_name | 关联查询 | 通过contract_id关联获取客户名称 |
| 里程碑状态 | milestoneStatus | pms_contract_milestone | milestone_status | 枚举转换 | 未开始、进行中、已完成、已逾期、已取消 |
| 计划开始日期 | planStartDate | pms_contract_milestone | plan_start_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 计划结束日期 | planEndDate | pms_contract_milestone | plan_end_date | 日期格式化 | YYYY-MM-DD格式显示 |
| 实际开始日期 | actualStartDate | pms_contract_milestone | actual_start_date | 日期格式化 | YYYY-MM-DD格式显示，可为空 |
| 实际结束日期 | actualEndDate | pms_contract_milestone | actual_end_date | 日期格式化 | YYYY-MM-DD格式显示，可为空 |
| 完成率 | completionRate | pms_contract_milestone | completion_rate | 百分比格式化 | 显示为百分比，如85% |
| 负责人 | responsiblePerson | pms_contract_milestone + sys_user | responsible_person → user_name | 关联查询 | 通过responsible_person关联sys_user表 |
| 里程碑描述 | description | pms_contract_milestone | description | 富文本转换 | 支持富文本，存储HTML格式 |
| 交付物 | deliverables | pms_contract_milestone | deliverables | 富文本转换 | 里程碑交付物描述 |
| 验收标准 | acceptanceCriteria | pms_contract_milestone | acceptance_criteria | 富文本转换 | 里程碑验收标准 |
| 风险说明 | riskDescription | pms_contract_milestone | risk_description | 直接映射 | 里程碑风险描述 |
| 备注信息 | remarks | pms_contract_milestone | remarks | 直接映射 | 里程碑备注信息 |
| 创建时间 | createTime | pms_contract_milestone | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_contract_milestone | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_contract_milestone + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 里程碑名称 | milestoneNameSearch | milestone_name | LIKE '%value%' | 模糊查询里程碑名称 |
| 合同名称 | contractNameSearch | contract_id → contract_name | LIKE '%value%' | 通过关联查询合同名称 |
| 客户名称 | customerNameSearch | contract_id → customer_name | LIKE '%value%' | 通过关联查询客户名称 |
| 里程碑状态 | milestoneStatusSearch | milestone_status | = 'value' | 精确匹配状态值 |
| 负责人 | responsiblePersonSearch | responsible_person | = userId | 精确匹配用户ID |
| 计划开始时间范围 | planStartDateRange | plan_start_date | BETWEEN startDate AND endDate | 日期范围查询 |
| 计划结束时间范围 | planEndDateRange | plan_end_date | BETWEEN startDate AND endDate | 日期范围查询 |

#### 项目管理系统同步字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 项目里程碑ID | projectMilestoneId | pms_contract_milestone | project_milestone_id | 直接映射 | 项目管理系统中的里程碑ID |
| 同步状态 | syncStatus | pms_contract_milestone | sync_status | 枚举转换 | 未同步、已同步、同步失败 |
| 同步时间 | syncTime | pms_contract_milestone | sync_time | 时间格式化 | 最后同步时间 |
| 同步错误信息 | syncErrorMsg | pms_contract_milestone | sync_error_msg | 直接映射 | 同步失败时的错误信息 |

#### 计算字段

| 页面显示字段 | 前端字段名 | 计算逻辑 | 说明 |
|-------------|-----------|----------|------|
| 计划工期 | plannedDuration | plan_end_date - plan_start_date | 计划结束日期减去计划开始日期 |
| 实际工期 | actualDuration | actual_end_date - actual_start_date | 实际结束日期减去实际开始日期 |
| 是否逾期 | isOverdue | actual_end_date > plan_end_date OR (milestone_status != 'COMPLETED' AND NOW() > plan_end_date) | 判断是否逾期 |
| 逾期天数 | overdueDays | DATEDIFF(NOW(), plan_end_date) | 当前日期减去计划结束日期 |
| 剩余天数 | remainingDays | DATEDIFF(plan_end_date, NOW()) | 计划结束日期减去当前日期 |

#### 数据异常排查指南

**里程碑状态异常**：
1. 检查 milestone_status 字段的枚举值定义
2. 确认状态流转逻辑是否正确
3. 检查状态更新权限

**关联合同显示异常**：
1. 检查合同表中对应记录是否存在
2. 确认 contract_id 字段值是否有效
3. 检查合同是否被删除

**项目管理系统同步失败**：
1. 检查项目管理系统是否正常运行
2. 确认网络连接和接口权限
3. 检查同步数据格式是否正确
4. 查看 sync_error_msg 字段的错误信息

**完成率计算异常**：
1. 检查 completion_rate 字段数据类型（0-100）
2. 确认完成率更新逻辑
3. 检查是否存在负数或超过100的值

**日期逻辑异常**：
1. 检查实际开始日期不能早于计划开始日期
2. 确认实际结束日期不能早于实际开始日期
3. 检查日期格式是否正确

**负责人显示异常**：
1. 检查 sys_user 表中对应用户是否存在
2. 确认 responsible_person 字段值是否有效
3. 检查用户状态是否为启用

#### API接口
- POST /pms/contractMilestone/page - 获取里程碑分页列表
- POST /pms/contractMilestone/add - 创建新里程碑
- PUT /pms/contractMilestone/edit - 更新里程碑信息
- DELETE /pms/contractMilestone/remove - 删除里程碑
- PUT /pms/contractMilestone/updateStatus - 更新里程碑状态
- POST /pms/contractMilestone/export/excel - 导出里程碑数据
- GET /pmi/power/function - 获取页面权限信息
- POST /pmi/api/milestone/sync - 同步里程碑到项目管理系统
- PUT /pmi/api/milestone/updateStatus - 更新项目管理系统里程碑状态

### 2.8 我的草稿

![我的草稿](./images/front_system/市场经营/市场经营_我的草稿.png)

#### 页面布局
- 顶部：搜索栏、筛选条件区域
- 中部：草稿列表表格
- 底部：分页器，显示总数和页码

#### 组件说明
1. 搜索筛选区域
   - 草稿类型下拉选择器（全部、线索、需求、报价、合同）
   - 草稿名称输入框
   - 时间范围选择器（创建时间、更新时间）
   - 搜索按钮、重置按钮

2. 草稿列表表格
   - 草稿类型（带类型标签）
   - 草稿名称（可点击继续编辑）
   - 创建时间
   - 最后修改时间
   - 操作列（继续编辑、删除）

#### 事件操作
- 点击草稿名称：跳转到对应的编辑页面继续编辑
- 点击继续编辑：跳转到对应的编辑页面
- 点击删除：显示确认对话框，确认后删除草稿

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(DraftList.vue)
    participant A as API层(Api.js)
    participant C as DraftController
    participant S as DraftService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant P as 权限服务(PMI)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据 PageButtonAuthorityVO
    A-->>F: 权限验证通过，显示对应按钮

    F->>A: getDraftList() 获取草稿列表
    A->>C: POST /pms/draft/page
    Note over A,C: 前端文件: src/views/market/drafts/DraftList.vue<br/>调用函数: getDraftList()<br/>权限验证: pageCode='draft'
    C->>S: DraftService.pages()
    Note over C,S: Controller: DraftController<br/>Service: DraftService
    S->>R: 查询缓存 market:draft:list:${userId}:${searchHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 分页查询草稿数据
        Note over S,D: SQL: SELECT * FROM pms_draft<br/>WHERE create_by = ? AND draft_type = ?<br/>ORDER BY update_time DESC LIMIT ?, ?
        D-->>S: 返回分页结果
        S->>R: 更新缓存 market:draft:list:${userId}:${searchHash} (15分钟)
    end
    S-->>C: Page<DraftVO>
    C-->>A: ResponseDTO<Page<DraftVO>>
    A-->>F: 渲染草稿列表表格

    F->>A: continueDraft() 继续编辑草稿
    A->>C: GET /pms/draft/detail/{draftId}
    C->>S: DraftService.getDetail()
    S->>R: 查询缓存 market:draft:detail:${draftId}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询草稿详情
        Note over S,D: SQL: SELECT * FROM pms_draft WHERE id = ?
        D-->>S: 返回草稿详情
        S->>R: 更新缓存 market:draft:detail:${draftId} (30分钟)
    end
    S-->>C: DraftDetailVO
    C-->>A: ResponseDTO<DraftDetailVO>
    A-->>F: 跳转到对应的编辑页面，填充草稿数据

    F->>A: deleteDraft() 删除草稿
    A->>C: DELETE /pms/draft/remove/{draftId}
    C->>S: DraftService.delete()
    S->>D: 删除草稿数据
    Note over S,D: SQL: UPDATE pms_draft SET is_deleted = 1<br/>WHERE id = ? AND create_by = ?
    D-->>S: 删除成功
    S->>R: 清除相关缓存
    Note over S,R: 清除: market:draft:list:${userId}:*<br/>清除: market:draft:detail:${draftId}
    S-->>C: 返回删除结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示删除成功提示，刷新列表
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 草稿标题 | draftTitle | pms_draft | draft_title | 直接映射 | 草稿的标题名称 |
| 草稿类型 | draftType | pms_draft | draft_type | 枚举转换 | 线索、需求、报价、合同等类型 |
| 草稿内容 | draftContent | pms_draft | draft_content | JSON解析 | 存储为JSON格式的表单数据 |
| 草稿状态 | draftStatus | pms_draft | draft_status | 枚举转换 | 草稿、已保存、已提交 |
| 最后编辑时间 | lastEditTime | pms_draft | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建时间 | createTime | pms_draft | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_draft + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 草稿标题 | draftTitleSearch | draft_title | LIKE '%value%' | 模糊查询草稿标题 |
| 草稿类型 | draftTypeSearch | draft_type | = 'value' | 精确匹配草稿类型 |
| 创建时间范围 | createTimeRange | create_time | BETWEEN startDate AND endDate | 时间范围查询 |

#### 草稿内容字段映射

| 草稿类型 | 内容字段 | JSON路径 | 对应表单字段 | 说明 |
|---------|---------|----------|-------------|------|
| 线索草稿 | leadName | $.leadName | 线索名称 | 线索表单的线索名称字段 |
| 线索草稿 | customerName | $.customerName | 客户名称 | 线索表单的客户名称字段 |
| 线索草稿 | contactPerson | $.contactPerson | 联系人 | 线索表单的联系人字段 |
| 需求草稿 | requirementName | $.requirementName | 需求名称 | 需求表单的需求名称字段 |
| 需求草稿 | customerName | $.customerName | 客户名称 | 需求表单的客户名称字段 |
| 需求草稿 | description | $.description | 需求描述 | 需求表单的描述字段 |
| 报价草稿 | quotationName | $.quotationName | 报价名称 | 报价表单的报价名称字段 |
| 报价草稿 | customerName | $.customerName | 客户名称 | 报价表单的客户名称字段 |
| 报价草稿 | totalAmount | $.totalAmount | 报价金额 | 报价表单的总金额字段 |
| 合同草稿 | contractName | $.contractName | 合同名称 | 合同表单的合同名称字段 |
| 合同草稿 | customerName | $.customerName | 客户名称 | 合同表单的客户名称字段 |
| 合同草稿 | contractAmount | $.contractAmount | 合同金额 | 合同表单的合同金额字段 |

#### 草稿操作相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 说明 |
|-------------|-----------|-----------|-----------|----------|------|
| 目标页面 | targetPage | pms_draft | target_page | 直接映射 | 草稿对应的目标编辑页面路径 |
| 表单验证状态 | validationStatus | 前端计算 | - | JSON内容验证 | 根据草稿内容验证表单完整性 |
| 可继续编辑 | canContinueEdit | 前端计算 | draft_status | 状态判断 | 只有草稿状态的可以继续编辑 |

#### 数据异常排查指南

**草稿列表为空**：
1. 检查 pms_draft 表的 is_deleted = 0 条件
2. 确认 create_by = 当前用户ID 的过滤条件
3. 检查用户是否有草稿查看权限

**草稿内容解析失败**：
1. 检查 draft_content 字段的JSON格式是否正确
2. 确认JSON内容是否包含必要的字段
3. 检查前端JSON解析逻辑

**继续编辑跳转失败**：
1. 检查 target_page 字段是否包含正确的页面路径
2. 确认目标页面路由是否存在
3. 检查页面权限是否允许访问

**草稿删除失败**：
1. 检查用户是否为草稿创建人
2. 确认草稿状态是否允许删除
3. 检查删除权限配置

**草稿类型显示异常**：
1. 检查 draft_type 字段的枚举值定义
2. 确认前端类型映射配置是否完整
3. 检查是否存在未定义的类型值

#### API接口
- POST /pms/draft/page - 获取草稿分页列表
- GET /pms/draft/detail/{draftId} - 获取草稿详情
- DELETE /pms/draft/remove/{draftId} - 删除草稿
- GET /pmi/power/function - 获取页面权限信息

### 2.9 帮助中心

![帮助中心](./images/front_system/市场经营/市场经营_帮助中心.png)

#### 页面布局
- 左侧：帮助分类导航树
- 右侧：帮助内容展示区域

#### 组件说明
1. 分类导航树
   - 常见问题
   - 操作指南
   - 功能介绍
   - 联系我们

2. 内容展示区域
   - 帮助文章标题
   - 帮助文章内容（支持富文本）
   - 相关链接
   - 反馈按钮

#### 事件操作
- 点击分类：展开/收起子分类
- 点击帮助文章：显示文章详细内容
- 点击反馈：打开反馈表单

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(HelpCenter.vue)
    participant A as API层(Api.js)
    participant C as HelpCenterController
    participant S as HelpCenterService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant P as 权限服务(PMI)
    participant ES as 搜索引擎(ElasticSearch)

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据
    A-->>F: 权限验证通过

    F->>A: getHelpCategories() 获取帮助分类
    A->>C: GET /pms/helpCenter/categories
    Note over A,C: 前端文件: src/views/market/help/HelpCenter.vue<br/>调用函数: getHelpCategories()
    C->>S: HelpCenterService.getCategories()
    Note over C,S: Controller: HelpCenterController<br/>Service: HelpCenterService
    S->>R: 查询缓存 help:categories
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询帮助分类
        Note over S,D: SQL: SELECT * FROM pms_help_category<br/>WHERE is_active = 1 ORDER BY sort_order
        D-->>S: 返回分类数据
        S->>R: 更新缓存 help:categories (2小时)
    end
    S-->>C: List<HelpCategoryVO>
    C-->>A: ResponseDTO<List<HelpCategoryVO>>
    A-->>F: 渲染分类导航树

    F->>A: getHelpArticles() 获取帮助文章
    A->>C: GET /pms/helpCenter/articles
    C->>S: HelpCenterService.getArticles()
    S->>R: 查询缓存 help:articles:${categoryId}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询帮助文章
        Note over S,D: SQL: SELECT * FROM pms_help_article<br/>WHERE category_id = ? AND is_published = 1<br/>ORDER BY sort_order, create_time DESC
        D-->>S: 返回文章数据
        S->>R: 更新缓存 help:articles:${categoryId} (1小时)
    end
    S-->>C: List<HelpArticleVO>
    C-->>A: ResponseDTO<List<HelpArticleVO>>
    A-->>F: 渲染文章列表

    F->>A: searchArticles() 搜索帮助文章
    A->>C: POST /pms/helpCenter/search
    Note over A,C: 搜索关键词: keyword
    C->>S: HelpCenterService.searchArticles()
    S->>ES: 全文搜索帮助文章
    Note over S,ES: 搜索索引: help_articles<br/>搜索字段: title, content, tags
    ES-->>S: 返回搜索结果
    S-->>C: List<HelpArticleVO>
    C-->>A: ResponseDTO<List<HelpArticleVO>>
    A-->>F: 渲染搜索结果

    F->>A: getArticleDetail() 获取文章详情
    A->>C: GET /pms/helpCenter/article/{articleId}
    C->>S: HelpCenterService.getArticleDetail()
    S->>R: 查询缓存 help:article:detail:${articleId}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询文章详情
        Note over S,D: SQL: SELECT * FROM pms_help_article<br/>WHERE id = ? AND is_published = 1
        D-->>S: 返回文章详情
        S->>R: 更新缓存 help:article:detail:${articleId} (2小时)
    end

    S->>D: 更新文章阅读次数
    Note over S,D: SQL: UPDATE pms_help_article<br/>SET view_count = view_count + 1 WHERE id = ?
    D-->>S: 更新成功

    S-->>C: HelpArticleDetailVO
    C-->>A: ResponseDTO<HelpArticleDetailVO>
    A-->>F: 显示文章详细内容

    F->>A: submitFeedback() 提交反馈
    A->>C: POST /pms/helpCenter/feedback
    Note over A,C: 反馈内容: feedback content
    C->>S: HelpCenterService.submitFeedback()
    S->>D: 插入反馈数据
    Note over S,D: SQL: INSERT INTO pms_help_feedback<br/>(article_id, user_id, feedback_type, content, create_time)<br/>VALUES (?, ?, ?, ?, NOW())
    D-->>S: 插入成功
    S-->>C: 返回提交结果
    C-->>A: ResponseDTO<Boolean>
    A-->>F: 显示提交成功提示
```

#### 页面字段与数据库字段对应关系

#### 帮助分类字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 分类名称 | categoryName | pms_help_category | category_name | 直接映射 | 帮助分类的名称 |
| 分类图标 | categoryIcon | pms_help_category | category_icon | 直接映射 | 分类图标的CSS类名或图片URL |
| 分类描述 | categoryDescription | pms_help_category | category_description | 直接映射 | 分类的详细描述 |
| 排序号 | sortOrder | pms_help_category | sort_order | 数字格式化 | 分类显示顺序 |
| 父分类ID | parentId | pms_help_category | parent_id | 直接映射 | 父分类ID，用于构建树形结构 |
| 是否启用 | isActive | pms_help_category | is_active | 布尔转换 | 分类是否启用显示 |

#### 帮助文章字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 文章标题 | articleTitle | pms_help_article | article_title | 直接映射 | 帮助文章的标题 |
| 文章内容 | articleContent | pms_help_article | article_content | 富文本转换 | 文章的详细内容，支持HTML格式 |
| 文章摘要 | articleSummary | pms_help_article | article_summary | 直接映射 | 文章的简要摘要 |
| 所属分类 | categoryId | pms_help_article + pms_help_category | category_id → category_name | 关联查询 | 通过category_id关联分类表获取分类名称 |
| 文章标签 | tags | pms_help_article | tags | 数组转换 | 文章标签，以逗号分隔存储 |
| 阅读次数 | viewCount | pms_help_article | view_count | 数字格式化 | 文章被阅读的次数 |
| 点赞次数 | likeCount | pms_help_article | like_count | 数字格式化 | 文章被点赞的次数 |
| 是否发布 | isPublished | pms_help_article | is_published | 布尔转换 | 文章是否已发布 |
| 发布时间 | publishTime | pms_help_article | publish_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 排序号 | sortOrder | pms_help_article | sort_order | 数字格式化 | 文章在分类中的显示顺序 |
| 创建时间 | createTime | pms_help_article | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 更新时间 | updateTime | pms_help_article | update_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |
| 创建人 | createBy | pms_help_article + sys_user | create_by → user_name | 关联查询 | 通过create_by关联sys_user表获取创建人 |

#### 搜索相关字段

| 页面显示字段 | 前端字段名 | 搜索来源 | 搜索字段 | 转换逻辑 | 说明 |
|-------------|-----------|----------|----------|----------|------|
| 搜索关键词 | searchKeyword | 用户输入 | - | 直接映射 | 用户输入的搜索关键词 |
| 搜索结果标题 | resultTitle | ElasticSearch | article_title | 高亮处理 | 搜索结果中的文章标题，关键词高亮 |
| 搜索结果摘要 | resultSummary | ElasticSearch | article_content | 摘要截取 | 搜索结果中的内容摘要，包含关键词上下文 |
| 相关度得分 | relevanceScore | ElasticSearch | _score | 数字格式化 | 搜索结果的相关度评分 |

#### 反馈相关字段

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 反馈类型 | feedbackType | pms_help_feedback | feedback_type | 枚举转换 | 有用、无用、建议、错误等类型 |
| 反馈内容 | feedbackContent | pms_help_feedback | content | 直接映射 | 用户提交的反馈内容 |
| 关联文章 | articleId | pms_help_feedback + pms_help_article | article_id → article_title | 关联查询 | 反馈对应的文章标题 |
| 反馈用户 | userId | pms_help_feedback + sys_user | user_id → user_name | 关联查询 | 提交反馈的用户姓名 |
| 反馈时间 | feedbackTime | pms_help_feedback | create_time | 时间格式化 | YYYY-MM-DD HH:mm:ss格式显示 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 分类筛选 | categoryFilter | category_id | = categoryId | 精确匹配分类ID |
| 关键词搜索 | keywordSearch | article_title, article_content | LIKE '%keyword%' | 在标题和内容中模糊搜索 |
| 标签筛选 | tagFilter | tags | LIKE '%tag%' | 在标签中模糊搜索 |

#### 数据异常排查指南

**分类树形结构异常**：
1. 检查 parent_id 字段是否正确设置
2. 确认是否存在循环引用
3. 检查分类层级深度是否合理

**文章内容显示异常**：
1. 检查 article_content 字段的HTML格式
2. 确认富文本编辑器的安全过滤
3. 检查图片和链接的有效性

**搜索功能异常**：
1. 检查ElasticSearch服务是否正常运行
2. 确认搜索索引是否已创建和同步
3. 检查搜索关键词的分词效果

**阅读次数统计异常**：
1. 检查 view_count 字段的更新逻辑
2. 确认是否存在重复计数
3. 检查统计更新的并发控制

**反馈提交失败**：
1. 检查用户登录状态
2. 确认反馈内容长度限制
3. 检查反馈类型枚举值

#### API接口
- GET /pms/helpCenter/categories - 获取帮助分类
- GET /pms/helpCenter/articles - 获取帮助文章列表
- POST /pms/helpCenter/search - 搜索帮助文章
- GET /pms/helpCenter/article/{articleId} - 获取文章详情
- POST /pms/helpCenter/feedback - 提交反馈
- GET /pmi/power/function - 获取页面权限信息

### 2.10 报表中心

#### 2.10.1 需求报表

![需求报表](./images/front_system/市场经营/市场经营_需求报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 状态多选框
   - 部门选择器
   - 客户类型选择器

2. 数据展示区域
   - 需求统计表格
   - 需求趋势图表
   - 需求状态分布饼图

#### 前后台对接时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(RequirementReport.vue)
    participant A as API层(Api.js)
    participant C as ReportController
    participant S as ReportService
    participant R as Redis缓存
    participant D as MySQL数据库
    participant P as 权限服务(PMI)
    participant BI as BI分析引擎

    F->>A: 页面初始化 onMounted()
    A->>P: 获取页面权限 /pmi/power/function
    P-->>A: 返回权限数据
    A-->>F: 权限验证通过

    F->>A: getRequirementReport() 获取需求报表
    A->>C: POST /pms/report/requirement/statistics
    Note over A,C: 前端文件: src/views/market/reports/RequirementReport.vue<br/>调用函数: getRequirementReport()<br/>筛选条件: 时间范围、状态、部门等
    C->>S: ReportService.getRequirementStatistics()
    Note over C,S: Controller: ReportController<br/>Service: ReportService
    S->>R: 查询缓存 report:requirement:${filterHash}
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询需求统计数据
        Note over S,D: SQL: SELECT requirement_status, COUNT(*) as count,<br/>SUM(estimated_amount) as total_amount,<br/>DATE_FORMAT(create_time, '%Y-%m') as month<br/>FROM pms_requirement_mangement<br/>WHERE create_time BETWEEN ? AND ?<br/>GROUP BY requirement_status, month
        D-->>S: 返回统计结果

        alt 需要BI分析
            S->>BI: 调用BI分析引擎
            Note over S,BI: 分析类型: 需求趋势分析<br/>数据源: 需求统计数据
            BI-->>S: 返回分析结果
        end

        S->>R: 更新缓存 report:requirement:${filterHash} (1小时)
    end
    S-->>C: RequirementReportVO
    C-->>A: ResponseDTO<RequirementReportVO>
    A-->>F: 渲染报表图表和表格

    F->>A: exportRequirementReport() 导出需求报表
    A->>C: POST /pms/report/requirement/export
    C->>S: ReportService.exportRequirementReport()
    S->>D: 查询详细报表数据
    Note over S,D: SQL: SELECT rm.*, u.user_name as responsible_name<br/>FROM pms_requirement_mangement rm<br/>LEFT JOIN sys_user u ON rm.responsible_person = u.id<br/>WHERE rm.create_time BETWEEN ? AND ?
    D-->>S: 返回详细数据
    S-->>C: 生成Excel报表文件
    C-->>A: 返回文件流
    A-->>F: 下载Excel文件
```

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 需求总数 | totalRequirements | pms_requirement_mangement | COUNT(*) | 统计计算 | 统计指定时间范围内的需求总数 |
| 已完成需求数 | completedRequirements | pms_requirement_mangement | COUNT(*) WHERE requirement_status = 'COMPLETED' | 条件统计 | 统计已完成状态的需求数量 |
| 进行中需求数 | inProgressRequirements | pms_requirement_mangement | COUNT(*) WHERE requirement_status = 'IN_PROGRESS' | 条件统计 | 统计进行中状态的需求数量 |
| 需求完成率 | completionRate | 计算字段 | completedRequirements / totalRequirements * 100 | 百分比计算 | 已完成需求数/总需求数*100 |
| 需求类型分布 | requirementTypeDistribution | pms_requirement_mangement | requirement_type, COUNT(*) GROUP BY requirement_type | 分组统计 | 按需求类型分组统计数量 |
| 月度需求趋势 | monthlyTrend | pms_requirement_mangement | DATE_FORMAT(create_time, '%Y-%m'), COUNT(*) GROUP BY month | 时间分组 | 按月份统计需求创建数量 |
| 平均处理时长 | avgProcessingTime | pms_requirement_mangement | AVG(DATEDIFF(update_time, create_time)) | 平均值计算 | 计算需求从创建到完成的平均天数 |
| 需求金额统计 | amountStatistics | pms_requirement_mangement | SUM(estimated_amount), AVG(estimated_amount) | 金额统计 | 需求预计金额的总和和平均值 |
| 负责人工作量 | workloadByPerson | pms_requirement_mangement + sys_user | responsible_person → user_name, COUNT(*) | 关联统计 | 按负责人统计需求数量 |

#### 筛选条件字段对应关系

| 筛选字段 | 前端字段名 | 数据库字段 | 查询条件 | 说明 |
|---------|-----------|-----------|----------|------|
| 时间范围 | dateRange | create_time | BETWEEN startDate AND endDate | 按创建时间筛选 |
| 需求状态 | statusFilter | requirement_status | IN (statusList) | 多选状态筛选 |
| 需求类型 | typeFilter | requirement_type | IN (typeList) | 多选类型筛选 |
| 负责人 | personFilter | responsible_person | IN (personList) | 多选负责人筛选 |
| 客户名称 | customerFilter | customer_name | LIKE '%customerName%' | 客户名称模糊筛选 |

#### 数据异常排查指南

**统计数据为0**：
1. 检查时间范围筛选条件是否过于严格
2. 确认数据权限是否正确
3. 检查 is_deleted = 0 条件

**完成率计算异常**：
1. 检查需求状态枚举值定义
2. 确认状态更新逻辑是否正确
3. 检查除零错误处理

**趋势图数据异常**：
1. 检查日期格式化函数
2. 确认时间范围是否合理
3. 检查数据库时区设置

#### API接口
- POST /pms/report/requirement/statistics - 获取需求报表统计数据
- POST /pms/report/requirement/export - 导出需求报表
- GET /pmi/power/function - 获取页面权限信息

#### 2.10.2 报价报表

![报价报表](./images/front_system/市场经营/市场经营_报价报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 状态多选框
   - 金额范围选择器
   - 客户类型选择器

2. 数据展示区域
   - 报价统计表格
   - 报价金额趋势图
   - 报价成功率分析图

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 报价总数 | totalQuotations | pms_quotation_management | COUNT(*) | 统计计算 | 统计指定时间范围内的报价总数 |
| 已发出报价数 | sentQuotations | pms_quotation_management | COUNT(*) WHERE quotation_status = 'SENT' | 条件统计 | 统计已发出状态的报价数量 |
| 已中标报价数 | wonQuotations | pms_quotation_management | COUNT(*) WHERE quotation_status = 'WON' | 条件统计 | 统计已中标状态的报价数量 |
| 中标率 | winRate | 计算字段 | wonQuotations / sentQuotations * 100 | 百分比计算 | 已中标报价数/已发出报价数*100 |
| 报价总金额 | totalAmount | pms_quotation_management | SUM(total_amount) | 金额汇总 | 所有报价的总金额 |
| 平均报价金额 | avgAmount | pms_quotation_management | AVG(total_amount) | 平均值计算 | 报价的平均金额 |
| 月度报价趋势 | monthlyTrend | pms_quotation_management | DATE_FORMAT(quotation_date, '%Y-%m'), COUNT(*) GROUP BY month | 时间分组 | 按月份统计报价数量 |
| 客户报价分布 | customerDistribution | pms_quotation_management | customer_name, COUNT(*) GROUP BY customer_name | 分组统计 | 按客户分组统计报价数量 |
| 负责人业绩 | performanceByPerson | pms_quotation_management + sys_user | responsible_person → user_name, COUNT(*), SUM(total_amount) | 关联统计 | 按负责人统计报价数量和金额 |

#### API接口
- POST /pms/report/quotation/statistics - 获取报价报表统计数据
- POST /pms/report/quotation/export - 导出报价报表
- GET /pmi/power/function - 获取页面权限信息

#### 2.10.3 合同报表

![合同报表](./images/front_system/市场经营/市场经营_合同报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 合同状态多选框
   - 金额范围选择器
   - 客户类型选择器

2. 数据展示区域
   - 合同统计表格
   - 合同金额趋势图
   - 合同执行状态分析图

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 合同总数 | totalContracts | pms_market_contract | COUNT(*) | 统计计算 | 统计指定时间范围内的合同总数 |
| 已签署合同数 | signedContracts | pms_market_contract | COUNT(*) WHERE contract_status = 'SIGNED' | 条件统计 | 统计已签署状态的合同数量 |
| 执行中合同数 | executingContracts | pms_market_contract | COUNT(*) WHERE contract_status = 'EXECUTING' | 条件统计 | 统计执行中状态的合同数量 |
| 已完成合同数 | completedContracts | pms_market_contract | COUNT(*) WHERE contract_status = 'COMPLETED' | 条件统计 | 统计已完成状态的合同数量 |
| 合同总金额 | totalAmount | pms_market_contract | SUM(contract_amount) | 金额汇总 | 所有合同的总金额 |
| 平均合同金额 | avgAmount | pms_market_contract | AVG(contract_amount) | 平均值计算 | 合同的平均金额 |
| 月度签约趋势 | monthlySignTrend | pms_market_contract | DATE_FORMAT(sign_date, '%Y-%m'), COUNT(*) GROUP BY month | 时间分组 | 按月份统计合同签署数量 |
| 客户合同分布 | customerDistribution | pms_market_contract | customer_name, COUNT(*), SUM(contract_amount) GROUP BY customer_name | 分组统计 | 按客户分组统计合同数量和金额 |
| 合同类型分布 | typeDistribution | pms_market_contract | contract_type, COUNT(*) GROUP BY contract_type | 分组统计 | 按合同类型分组统计数量 |
| 负责人业绩 | performanceByPerson | pms_market_contract + sys_user | responsible_person → user_name, COUNT(*), SUM(contract_amount) | 关联统计 | 按负责人统计合同数量和金额 |

#### API接口
- POST /pms/report/contract/statistics - 获取合同报表统计数据
- POST /pms/report/contract/export - 导出合同报表
- GET /pmi/power/function - 获取页面权限信息

#### 2.10.4 里程碑报表

![里程碑报表](./images/front_system/市场经营/市场经营_里程碑报表.png)

#### 页面布局
- 顶部：报表筛选条件区域
- 中部：报表数据展示区域（表格+图表）
- 底部：导出按钮

#### 组件说明
1. 筛选条件区域
   - 时间范围选择器
   - 里程碑状态多选框
   - 合同选择器
   - 负责人选择器

2. 数据展示区域
   - 里程碑统计表格
   - 里程碑完成率趋势图
   - 里程碑逾期分析图

#### 页面字段与数据库字段对应关系

| 页面显示字段 | 前端字段名 | 数据库来源 | 数据库字段 | 转换逻辑 | 数据溯源说明 |
|-------------|-----------|-----------|-----------|----------|-------------|
| 里程碑总数 | totalMilestones | pms_contract_milestone | COUNT(*) | 统计计算 | 统计指定时间范围内的里程碑总数 |
| 已完成里程碑数 | completedMilestones | pms_contract_milestone | COUNT(*) WHERE milestone_status = 'COMPLETED' | 条件统计 | 统计已完成状态的里程碑数量 |
| 进行中里程碑数 | inProgressMilestones | pms_contract_milestone | COUNT(*) WHERE milestone_status = 'IN_PROGRESS' | 条件统计 | 统计进行中状态的里程碑数量 |
| 逾期里程碑数 | overdueMilestones | pms_contract_milestone | COUNT(*) WHERE milestone_status = 'OVERDUE' | 条件统计 | 统计逾期状态的里程碑数量 |
| 里程碑完成率 | completionRate | 计算字段 | completedMilestones / totalMilestones * 100 | 百分比计算 | 已完成里程碑数/总里程碑数*100 |
| 逾期率 | overdueRate | 计算字段 | overdueMilestones / totalMilestones * 100 | 百分比计算 | 逾期里程碑数/总里程碑数*100 |
| 平均完成时长 | avgCompletionTime | pms_contract_milestone | AVG(DATEDIFF(actual_end_date, actual_start_date)) | 平均值计算 | 计算里程碑平均完成天数 |
| 月度完成趋势 | monthlyCompletionTrend | pms_contract_milestone | DATE_FORMAT(actual_end_date, '%Y-%m'), COUNT(*) GROUP BY month | 时间分组 | 按月份统计里程碑完成数量 |
| 合同里程碑分布 | contractDistribution | pms_contract_milestone + pms_market_contract | contract_id → contract_name, COUNT(*) GROUP BY contract_name | 关联统计 | 按合同分组统计里程碑数量 |
| 负责人工作量 | workloadByPerson | pms_contract_milestone + sys_user | responsible_person → user_name, COUNT(*) GROUP BY user_name | 关联统计 | 按负责人统计里程碑数量 |
| 里程碑类型分布 | typeDistribution | pms_contract_milestone | milestone_type, COUNT(*) GROUP BY milestone_type | 分组统计 | 按里程碑类型分组统计数量 |

#### 计算字段详细说明

| 计算字段 | 计算公式 | 数据来源 | 说明 |
|---------|----------|----------|------|
| 按时完成率 | (按时完成数 / 总完成数) * 100 | actual_end_date <= plan_end_date | 在计划时间内完成的里程碑比例 |
| 平均延期天数 | AVG(DATEDIFF(actual_end_date, plan_end_date)) | actual_end_date > plan_end_date | 延期里程碑的平均延期天数 |
| 工期偏差率 | ((实际工期 - 计划工期) / 计划工期) * 100 | (actual_end_date - actual_start_date) vs (plan_end_date - plan_start_date) | 实际工期与计划工期的偏差百分比 |

#### 数据异常排查指南

**里程碑统计数据异常**：
1. 检查里程碑状态枚举值定义
2. 确认状态更新时机和逻辑
3. 检查关联合同是否存在

**完成率计算错误**：
1. 检查日期字段是否为NULL
2. 确认状态流转逻辑
3. 检查除零错误处理

**工期计算异常**：
1. 检查日期字段的数据类型
2. 确认时区设置是否正确
3. 检查工作日计算逻辑

#### API接口
- POST /pms/report/milestone/statistics - 获取里程碑报表统计数据
- POST /pms/report/milestone/export - 导出里程碑报表
- GET /pmi/power/function - 获取页面权限信息

## 3. 技术实现

### 3.1 第三方服务集成

#### 3.1.1 消息中心服务(MSC)

**服务说明**：负责系统内消息通知、待办事项管理

**集成接口**：
- POST /message - 创建消息
- PUT /message-center/todo-status - 更新待办状态
- PUT /message-center/done-cancel/todo-status - 待办变已办并取消其他人待办
- DELETE /message/revocation/{businessId} - 撤回消息

**使用场景**：
- 线索分配通知
- 需求审批通知
- 报价发出通知
- 合同审核通知
- 里程碑状态更新通知

#### 3.1.2 工作流服务(WF)

**服务说明**：负责业务流程审批管理

**集成接口**：
- POST /flowTemplateBusiness/one/start - 启动单个工作流
- POST /flowTemplateBusiness/batch/start - 批量启动工作流
- GET /new/{businessId} - 根据业务ID获取最新流程实例
- POST /processInstanceAssignee/assigneeList/byBusinessIds - 获取审批人列表

**使用场景**：
- 需求审批流程
- 报价审批流程
- 合同审批流程

#### 3.1.3 权限服务(PMI)

**服务说明**：负责用户权限验证和功能权限控制

**集成接口**：
- GET /pmi/power/function - 获取页面功能权限
- POST /base-area/getByAreaIds - 根据地区ID获取地区信息
- POST /holidays/compute/interval-days/batch - 批量计算工作日

**使用场景**：
- 页面按钮权限控制
- 数据访问权限验证
- 地区信息获取
- 工作日计算

#### 3.1.4 文件服务(RES)

**服务说明**：负责文件上传、下载、管理

**集成接口**：
- POST /res/file/upload - 上传文件
- GET /res/file/download/{fileId} - 下载文件
- POST /res/file/batch - 批量保存文件关联
- GET /res/file/list/{dataId} - 获取关联文件列表

**使用场景**：
- 线索附件上传
- 需求文档上传
- 报价文件上传
- 合同附件管理

#### 3.1.5 第三方合同服务(ICM)

**服务说明**：与外部合同管理系统集成

**集成接口**：
- POST /third/api/marketContract/add - 同步合同到第三方系统
- POST /third/api/marketContract/edit - 更新第三方系统合同信息

**使用场景**：
- 合同信息同步
- 合同状态同步

#### 3.1.6 CRM系统集成

**服务说明**：与客户关系管理系统集成

**集成接口**：
- POST /crm/api/customer/sync - 同步客户信息到CRM
- PUT /crm/api/customer/update - 更新CRM客户信息

**使用场景**：
- 客户信息同步
- 客户数据统一管理

### 3.2 前后端对接时序图

#### 3.2.1 线索管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(LeadList.vue)
    participant C as LeadController
    participant S as LeadService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: GET /api/leads/list
    Note over F,C: 前端文件: src/views/market/leads/LeadList.vue<br/>调用函数: getLeadList()
    C->>S: LeadService.getList()
    Note over C,S: Controller: LeadController.list()<br/>Service: LeadService.getList()
    S->>R: 查询缓存 market:lead:list:hash
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询数据库表 pms_lead_management
        Note over S,D: SQL: SELECT * FROM pms_lead_management WHERE ...
        D-->>S: 返回数据
        S->>R: 更新缓存 market:lead:list:hash
    end
    S-->>C: 返回数据
    C-->>F: 返回结果
```

#### 3.1.2 需求管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(RequirementList.vue)
    participant C as RequirementController
    participant S as RequirementService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: POST /api/requirements/create
    Note over F,C: 前端文件: src/views/market/requirements/RequirementForm.vue<br/>调用函数: createRequirement()
    C->>S: RequirementService.create()
    Note over C,S: Controller: RequirementMangementController.create()<br/>Service: RequirementMangementService.save()
    S->>D: 插入数据到 pms_requirement_mangement
    Note over S,D: SQL: INSERT INTO pms_requirement_mangement (...)
    D-->>S: 返回插入结果
    S->>R: 清除相关缓存 market:req:list:*
    S-->>C: 返回创建结果
    C-->>F: 返回结果
```

#### 3.1.3 报价管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(QuoteList.vue)
    participant C as QuotationController
    participant S as QuotationService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: POST /api/quotes/send
    Note over F,C: 前端文件: src/views/market/quotes/QuoteForm.vue<br/>调用函数: sendQuote()
    C->>S: QuotationService.sendOut()
    Note over C,S: Controller: QuotationManagementController.sendOut()<br/>Service: QuotationManagementService.sendOut()
    S->>D: 更新报价状态到 pms_quotation_management
    Note over S,D: SQL: UPDATE pms_quotation_management SET status = 'SENT'
    D-->>S: 返回更新结果
    S->>R: 更新缓存 market:quote:detail:id
    S-->>C: 返回发送结果
    C-->>F: 返回结果
```

#### 3.1.4 合同管理时序图

```mermaid
sequenceDiagram
    participant F as 前端页面(ContractList.vue)
    participant C as MarketContractController
    participant S as MarketContractService
    participant R as Redis
    participant D as MySQL数据库

    F->>C: GET /api/contracts/list
    Note over F,C: 前端文件: src/views/market/contracts/ContractList.vue<br/>调用函数: getContractList()
    C->>S: MarketContractService.getList()
    Note over C,S: Controller: MarketContractController.list()<br/>Service: MarketContractService.getList()
    S->>R: 查询缓存 market:contract:list:hash
    alt 缓存命中
        R-->>S: 返回缓存数据
    else 缓存未命中
        S->>D: 查询数据库表 pms_market_contract
        Note over S,D: SQL: SELECT * FROM pms_market_contract WHERE ...
        D-->>S: 返回数据
        S->>R: 更新缓存 market:contract:list:hash
    end
    S-->>C: 返回数据
    C-->>F: 返回结果
```

### 3.3 数据库设计

#### 3.3.1 线索管理表(pms_lead_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| lead_name | varchar | 255 | NO | | 线索名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| contact_person | varchar | 100 | YES | | 联系人 |
| contact_phone | varchar | 20 | YES | | 联系电话 |
| contact_email | varchar | 100 | YES | | 联系邮箱 |
| lead_source | varchar | 50 | YES | | 线索来源 |
| lead_priority | varchar | 20 | YES | | 线索优先级 |
| lead_status | varchar | 20 | YES | | 线索状态 |
| description | text | | YES | | 线索描述 |
| estimated_amount | decimal | 15,2 | YES | | 预计金额 |
| estimated_close_date | datetime | | YES | | 预计成交时间 |
| industry | varchar | 100 | YES | | 所属行业 |
| company_size | varchar | 50 | YES | | 客户规模 |
| assigned_to | bigint | 20 | YES | | 分配给 |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.2 需求管理表(pms_requirement_mangement)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| requirement_no | varchar | 50 | NO | | 需求编号 |
| requirement_name | varchar | 255 | NO | | 需求名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| requirement_type | varchar | 50 | YES | | 需求类型 |
| requirement_status | varchar | 20 | YES | | 需求状态 |
| description | text | | YES | | 需求描述 |
| estimated_amount | decimal | 15,2 | YES | | 预计金额 |
| estimated_start_date | datetime | | YES | | 预计开始时间 |
| estimated_end_date | datetime | | YES | | 预计结束时间 |
| priority | varchar | 20 | YES | | 优先级 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.3 报价管理表(pms_quotation_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| quotation_no | varchar | 50 | NO | | 报价编号 |
| quotation_name | varchar | 255 | NO | | 报价名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| requirement_id | bigint | 20 | YES | | 关联需求ID |
| quotation_status | varchar | 20 | YES | | 报价状态 |
| total_amount | decimal | 15,2 | YES | | 报价总金额 |
| valid_until | datetime | | YES | | 有效期至 |
| quotation_date | datetime | | YES | | 报价日期 |
| description | text | | YES | | 报价说明 |
| terms_conditions | text | | YES | | 条款条件 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.4 合同管理表(pms_market_contract)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| contract_no | varchar | 50 | NO | | 合同编号 |
| contract_name | varchar | 255 | NO | | 合同名称 |
| customer_name | varchar | 255 | NO | | 客户名称 |
| quotation_id | bigint | 20 | YES | | 关联报价ID |
| contract_status | varchar | 20 | YES | | 合同状态 |
| contract_amount | decimal | 15,2 | YES | | 合同金额 |
| sign_date | datetime | | YES | | 签署日期 |
| start_date | datetime | | YES | | 开始日期 |
| end_date | datetime | | YES | | 结束日期 |
| description | text | | YES | | 合同描述 |
| terms_conditions | text | | YES | | 条款条件 |
| responsible_person | bigint | 20 | YES | | 负责人 |
| customer_contact | varchar | 100 | YES | | 客户联系人 |
| customer_phone | varchar | 20 | YES | | 客户电话 |
| attachment_urls | text | | YES | | 附件URLs |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

#### 3.2.5 客户管理表(pms_customer_management)

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id | bigint | 20 | NO | | 主键ID |
| customer_name | varchar | 255 | NO | | 客户名称 |
| customer_type | varchar | 50 | YES | | 客户类型 |
| customer_level | varchar | 20 | YES | | 客户等级 |
| industry | varchar | 100 | YES | | 所属行业 |
| company_size | varchar | 50 | YES | | 公司规模 |
| contact_person | varchar | 100 | YES | | 联系人 |
| contact_phone | varchar | 20 | YES | | 联系电话 |
| contact_email | varchar | 100 | YES | | 联系邮箱 |
| address | varchar | 500 | YES | | 地址 |
| description | text | | YES | | 客户描述 |
| create_time | datetime | | NO | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | NO | CURRENT_TIMESTAMP | 更新时间 |
| create_by | bigint | 20 | YES | | 创建人 |
| update_by | bigint | 20 | YES | | 更新人 |
| is_deleted | tinyint | 1 | NO | 0 | 是否删除 |

### 3.4 前端页面字段与数据库字段映射关系

#### 3.4.1 线索管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| leadName | 线索名称 | lead_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| contactPerson | 联系人 | contact_person | String | 直接映射 |
| contactPhone | 联系电话 | contact_phone | String | 直接映射 |
| contactEmail | 联系邮箱 | contact_email | String | 直接映射 |
| leadSource | 线索来源 | lead_source | String | 下拉选择值映射 |
| leadPriority | 线索优先级 | lead_priority | String | 枚举值映射(高/中/低) |
| leadStatus | 线索状态 | lead_status | String | 枚举值映射 |
| description | 线索描述 | description | String | 富文本转换 |
| estimatedAmount | 预计金额 | estimated_amount | Number | 数字格式化 |
| estimatedCloseDate | 预计成交时间 | estimated_close_date | Date | 日期格式转换 |
| industry | 所属行业 | industry | String | 下拉选择值映射 |
| companySize | 客户规模 | company_size | String | 下拉选择值映射 |
| assignedTo | 分配给 | assigned_to | Number | 用户ID映射 |
| createTime | 创建时间 | create_time | Date | 时间格式化显示 |
| updateTime | 更新时间 | update_time | Date | 时间格式化显示 |

#### 3.3.2 需求管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| requirementNo | 需求编号 | requirement_no | String | 自动生成编号 |
| requirementName | 需求名称 | requirement_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| requirementType | 需求类型 | requirement_type | String | 下拉选择值映射 |
| requirementStatus | 需求状态 | requirement_status | String | 枚举值映射 |
| description | 需求描述 | description | String | 富文本转换 |
| estimatedAmount | 预计金额 | estimated_amount | Number | 数字格式化 |
| estimatedStartDate | 预计开始时间 | estimated_start_date | Date | 日期格式转换 |
| estimatedEndDate | 预计结束时间 | estimated_end_date | Date | 日期格式转换 |
| priority | 优先级 | priority | String | 枚举值映射(高/中/低) |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

#### 3.3.3 报价管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| quotationNo | 报价编号 | quotation_no | String | 自动生成编号 |
| quotationName | 报价名称 | quotation_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| requirementId | 关联需求ID | requirement_id | Number | 外键关联 |
| quotationStatus | 报价状态 | quotation_status | String | 枚举值映射 |
| totalAmount | 报价总金额 | total_amount | Number | 数字格式化 |
| validUntil | 有效期至 | valid_until | Date | 日期格式转换 |
| quotationDate | 报价日期 | quotation_date | Date | 日期格式转换 |
| description | 报价说明 | description | String | 富文本转换 |
| termsConditions | 条款条件 | terms_conditions | String | 富文本转换 |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

#### 3.3.4 合同管理字段映射

| 前端字段名 | 前端显示名称 | 数据库字段名 | 数据类型转换 | 说明 |
|------------|--------------|--------------|--------------|------|
| contractNo | 合同编号 | contract_no | String | 自动生成编号 |
| contractName | 合同名称 | contract_name | String | 直接映射 |
| customerName | 客户名称 | customer_name | String | 直接映射 |
| quotationId | 关联报价ID | quotation_id | Number | 外键关联 |
| contractStatus | 合同状态 | contract_status | String | 枚举值映射 |
| contractAmount | 合同金额 | contract_amount | Number | 数字格式化 |
| signDate | 签署日期 | sign_date | Date | 日期格式转换 |
| startDate | 开始日期 | start_date | Date | 日期格式转换 |
| endDate | 结束日期 | end_date | Date | 日期格式转换 |
| description | 合同描述 | description | String | 富文本转换 |
| termsConditions | 条款条件 | terms_conditions | String | 富文本转换 |
| responsiblePerson | 负责人 | responsible_person | Number | 用户ID映射 |
| customerContact | 客户联系人 | customer_contact | String | 直接映射 |
| customerPhone | 客户电话 | customer_phone | String | 直接映射 |
| attachmentUrls | 附件URLs | attachment_urls | Array | JSON数组转换 |

### 3.5 物理模型图

#### 3.5.1 市场经营模块ER图

```mermaid
erDiagram
    PMS_LEAD_MANAGEMENT {
        bigint id PK
        varchar lead_name
        varchar customer_name
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        varchar lead_source
        varchar lead_priority
        varchar lead_status
        text description
        decimal estimated_amount
        datetime estimated_close_date
        varchar industry
        varchar company_size
        bigint assigned_to
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_REQUIREMENT_MANGEMENT {
        bigint id PK
        varchar requirement_no
        varchar requirement_name
        varchar customer_name
        varchar requirement_type
        varchar requirement_status
        text description
        decimal estimated_amount
        datetime estimated_start_date
        datetime estimated_end_date
        varchar priority
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_QUOTATION_MANAGEMENT {
        bigint id PK
        varchar quotation_no
        varchar quotation_name
        varchar customer_name
        bigint requirement_id FK
        varchar quotation_status
        decimal total_amount
        datetime valid_until
        datetime quotation_date
        text description
        text terms_conditions
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_MARKET_CONTRACT {
        bigint id PK
        varchar contract_no
        varchar contract_name
        varchar customer_name
        bigint quotation_id FK
        varchar contract_status
        decimal contract_amount
        datetime sign_date
        datetime start_date
        datetime end_date
        text description
        text terms_conditions
        bigint responsible_person
        varchar customer_contact
        varchar customer_phone
        text attachment_urls
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_CUSTOMER_MANAGEMENT {
        bigint id PK
        varchar customer_name
        varchar customer_type
        varchar customer_level
        varchar industry
        varchar company_size
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        varchar address
        text description
        datetime create_time
        datetime update_time
        bigint create_by
        bigint update_by
        tinyint is_deleted
    }

    PMS_REQUIREMENT_MANGEMENT ||--o{ PMS_QUOTATION_MANAGEMENT : "一对多"
    PMS_QUOTATION_MANAGEMENT ||--o{ PMS_MARKET_CONTRACT : "一对多"
    PMS_CUSTOMER_MANAGEMENT ||--o{ PMS_LEAD_MANAGEMENT : "一对多"
    PMS_CUSTOMER_MANAGEMENT ||--o{ PMS_REQUIREMENT_MANGEMENT : "一对多"
```

### 3.6 Redis缓存设计

#### 3.6.1 缓存Key设计规范

| 模块 | 缓存Key格式 | 过期时间 | 说明 |
|------|-------------|----------|------|
| 线索管理 | market:lead:list:{hash} | 30分钟 | 线索列表缓存 |
| 线索管理 | market:lead:detail:{id} | 1小时 | 线索详情缓存 |
| 需求管理 | market:req:list:{hash} | 30分钟 | 需求列表缓存 |
| 需求管理 | market:req:detail:{id} | 1小时 | 需求详情缓存 |
| 报价管理 | market:quote:list:{hash} | 30分钟 | 报价列表缓存 |
| 报价管理 | market:quote:detail:{id} | 1小时 | 报价详情缓存 |
| 合同管理 | market:contract:list:{hash} | 30分钟 | 合同列表缓存 |
| 合同管理 | market:contract:detail:{id} | 1小时 | 合同详情缓存 |
| 客户管理 | market:customer:list:{hash} | 1小时 | 客户列表缓存 |
| 客户管理 | market:customer:detail:{id} | 2小时 | 客户详情缓存 |
| 统计数据 | market:stats:overview | 15分钟 | 概览统计缓存 |
| 报表数据 | market:report:{type}:{hash} | 1小时 | 报表数据缓存 |

#### 3.6.2 缓存更新策略

1. **列表缓存更新**：当有新增、修改、删除操作时，清除对应的列表缓存
2. **详情缓存更新**：当有修改操作时，更新对应的详情缓存
3. **统计缓存更新**：当有业务数据变更时，清除统计缓存，下次访问时重新计算
4. **报表缓存更新**：定时任务每小时更新一次报表缓存

## 4. 总结

市场经营模块作为系统的核心业务模块，涵盖了从线索获取到合同签署的完整销售流程。通过详细的页面设计、完善的API接口、清晰的数据库设计和高效的缓存策略，为用户提供了完整的市场经营管理解决方案。

### 4.1 主要功能特点

1. **完整的销售流程管理**：从线索到合同的全流程跟踪
2. **灵活的数据筛选和搜索**：支持多维度的数据查询
3. **丰富的报表分析**：提供多种维度的数据分析和可视化
4. **高效的缓存机制**：提升系统响应速度和用户体验
5. **规范的前后端对接**：清晰的API设计和数据映射关系

### 4.2 技术架构优势

1. **前后端分离**：Vue.js前端 + Spring Boot后端
2. **数据库设计规范**：遵循数据库设计范式，支持业务扩展
3. **缓存策略完善**：Redis缓存提升系统性能
4. **API设计RESTful**：符合REST规范，易于维护和扩展
