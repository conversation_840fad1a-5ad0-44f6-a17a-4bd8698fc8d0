package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.PurchProjectWeeklyDTO;
import com.chinasie.orion.domain.entity.PurchProjectWeekly;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyExcelVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * PurchProjectWeekly Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Mapper
public interface PurchProjectWeeklyMapper extends OrionBaseMapper<PurchProjectWeekly> {

    /**
     * 查询总数
     * @param purchProjectWeeklyDTO
     * @return
     */
    Integer selectTotal(@Param("param") PurchProjectWeeklyDTO purchProjectWeeklyDTO);

    /**
     * 查询分页
     * @param purchProjectWeeklyDTO
     * @return
     */
    PurchProjectWeeklyExcelVO selectPage(@Param("param") PurchProjectWeeklyDTO purchProjectWeeklyDTO);
}

