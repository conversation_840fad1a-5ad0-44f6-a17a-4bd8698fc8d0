package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PlaneCost DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:59:04
 */
@ApiModel(value = "PlaneCostDTO对象", description = "机票费用")
@Data
@ExcelIgnoreUnannotated
public class PlaneCostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;

    /**
     * 机票单号
     */
    @ApiModelProperty(value = "机票单号")
    @ExcelProperty(value = "机票单号 ", index = 0)
    private String planeNo;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @ExcelProperty(value = "中心编号 ", index = 1)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 2)
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @ExcelProperty(value = "部门编号 ", index = 3)
    private String deptNo;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称 ", index = 4)
    private String deptName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 5)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 6)
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 7)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 8)
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 9)
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 10)
    private String userName;

    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    @ExcelProperty(value = "差旅任务编号 ", index = 11)
    private String taskNo;

    /**
     * 出发日期
     */
    @ApiModelProperty(value = "出发日期")
    @ExcelProperty(value = "出发日期 ", index = 12)
    private Date startTime;

    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    @ExcelProperty(value = "出发地 ", index = 13)
    private String fromCity;

    /**
     * 到达地
     */
    @ApiModelProperty(value = "到达地")
    @ExcelProperty(value = "到达地 ", index = 14)
    private String toCity;

    /**
     * 折扣机票金额
     */
    @ApiModelProperty(value = "折扣机票金额")
    @ExcelProperty(value = "折扣机票金额 ", index = 15)
    private BigDecimal discountPrice;

    /**
     * 全价机票金额
     */
    @ApiModelProperty(value = "全价机票金额")
    @ExcelProperty(value = "全价机票金额 ", index = 16)
    private BigDecimal fullPrice;

    /**
     * 机票折数
     */
    @ApiModelProperty(value = "机票折数")
    @ExcelProperty(value = "机票折数 ", index = 17)
    private BigDecimal discountCount;

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @ExcelProperty(value = "流程状态 ", index = 18)
    private String flowStatus;




}
