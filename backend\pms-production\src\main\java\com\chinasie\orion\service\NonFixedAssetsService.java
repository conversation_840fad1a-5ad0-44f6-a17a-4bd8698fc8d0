package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.NonFixedAssetsDTO;
import com.chinasie.orion.domain.entity.NonFixedAssets;
import com.chinasie.orion.domain.vo.NonFixedAssetsVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * NcfFormZftvVEfTF 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
public interface NonFixedAssetsService extends OrionBaseService<NonFixedAssets> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    NonFixedAssetsVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormZftvVEfTFDTO
     */
    String create(NonFixedAssetsDTO ncfFormZftvVEfTFDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormZftvVEfTFDTO
     */
    Boolean edit(NonFixedAssetsDTO ncfFormZftvVEfTFDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NonFixedAssetsVO> pages(Page<NonFixedAssetsDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NonFixedAssetsVO> vos) throws Exception;

    /**
     *
     * @param number
     * @return
     */
    NonFixedAssetsVO detailByNumber(String number);
}
