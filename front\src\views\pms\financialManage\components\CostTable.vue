<script setup lang="ts">
import {
  ref,
  Ref,
} from 'vue';
import {
  OrionTable,
  isPower,
  BasicButton,
  downloadByData,
  useModal,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import { CostFilterConfig } from '../filterIndex';
import { getDetailAperture } from '../report';

const props = withDefaults(defineProps<{
  record: Record<string, any>,
  costType: string,
  powerData?: any,
  date?: string,
}>(), {});

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectionKeys: Ref<string[]> = ref([]);
const loadStatus: Ref<boolean> = ref(false);
const [register, { openModal }] = useModal();

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const conditions = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition));

    // 将 conditions 转换为 { field1: value1, field2: value2, ... } 的形式
    const query = conditions.reduce((acc, condition) => {
      if (condition.values && condition.values.length > 0) {
        acc[condition.field] = condition.values.join(','); // 假设 values 是一个数组，这里用逗号连接
      }
      return acc;
    }, {});

    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      ...query,
      query: {
        companyId: props.record?.companyId,
        wbsExpertiseCenter: props.record?.wbsExpertiseCenter,
        businessClassification: props.record?.businessClassification,
        projectId: props.record?.projectId,
        type: props.record?.type,
        costType: props?.costType,
        year: props?.date,
      },
      power: {
        pageCode: 'RYJSWH_001',
      },
    };
  }
  return {
    ...params,
    query: {
      companyId: props.record?.companyId,
      wbsExpertiseCenter: props.record?.wbsExpertiseCenter,
      businessClassification: props.record?.businessClassification,
      projectId: props.record?.projectId,
      type: props.record?.type,
      costType: props?.costType,
      year: props?.date,
    },
    power: {
      pageCode: 'RYJSWH_001',
    },
  };
}

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectionKeys.value = _keys;
    },
  },
  showToolButton: false,
  isSpacing: true,
  showSmallSearch: false,
  isFilter2: true,
  smallSearchField: false,
  filterConfig: {
    fields: CostFilterConfig,
  },
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams || [], 'costShare/page', 'POST');
  },
  columns: getDetailAperture(),
};

// 导出
async function handleExport() {
  const params = {
    companyId: props.record?.companyId,
    wbsExpertiseCenter: props.record?.wbsExpertiseCenter,
    businessClassification: props.record?.businessClassification,
    projectId: props.record?.projectId,
    type: props.record?.type,
    costType: props?.costType,
    year: props?.date,
  };
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/costShare/export/excel', params, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMQKJMXBB_001_container_02_button_03', props.powerData)"
        type="primary"
        ghost
        icon="sie-icon-daochu"
        @click="handleExport()"
      >
        导出
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
