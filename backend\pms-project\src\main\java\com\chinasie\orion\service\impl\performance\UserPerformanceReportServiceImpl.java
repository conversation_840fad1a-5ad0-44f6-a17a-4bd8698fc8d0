package com.chinasie.orion.service.impl.performance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeReportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.performance.DeliverablePerformanceVO;
import com.chinasie.orion.domain.vo.performance.ProjectByUserVO;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceVO;
import com.chinasie.orion.domain.vo.performance.UserPerformanceReportVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pas.api.domain.vo.WorkHoursReportDetailInfoVO;
import com.chinasie.orion.pas.api.service.WorkHoursReportDetailForUserService;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.performance.UserPerformanceReportService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
@Service
public class UserPerformanceReportServiceImpl implements UserPerformanceReportService {

    @Resource
    private UserDOMapper userDOMapper;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private DeliverableService deliverableService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectSchemeContentService projectSchemeContentService;

    @Autowired
    private WorkHoursReportDetailForUserService workHoursReportDetailForUserService;

    public Page<UserPerformanceReportVO> getPage(Page<UserDO> pageRequest) throws Exception {
        LambdaQueryWrapperX<UserDO> condition = new LambdaQueryWrapperX<>(UserDO.class);
        if (!CollectionUtil.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(UserDO::getCreateTime);
        PageResult<UserDO> page = userDOMapper.selectPage(pageRequest, condition);
        List<UserPerformanceReportVO> vos = BeanCopyUtils.convertListTo(page.getContent(), UserPerformanceReportVO::new);
        setEveryName(vos);

        Page<UserPerformanceReportVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public void exportByExcel(Page<UserDO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<UserDO> condition = new LambdaQueryWrapperX<>(UserDO.class);
        if (!CollectionUtil.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(UserDO::getCreateTime);
        List<UserDO> userDOList = userDOMapper.selectList(condition);

        List<UserPerformanceReportVO> vos = BeanCopyUtils.convertListTo(userDOList, UserPerformanceReportVO::new);
        setEveryName(vos);
        String fileName = "人员参与度报表导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", UserPerformanceReportVO.class,vos );

    }

    private void setEveryName(List<UserPerformanceReportVO> vos) throws Exception {
        if (CollectionUtil.isNotEmpty(vos)) {
            List<String> userIdList = vos.stream().map(UserPerformanceReportVO::getId).collect(Collectors.toList());
            //用户信息
            Map<String, SimpleUser> simpleUserMap = userRedisHelper.getSimpleUserByIds(userIdList).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
            //用户参与项目
            Map<String, Set<String>> projectIdMap = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class)
                    .select(ProjectRoleUser::getUserId, ProjectRoleUser::getProjectId)
                    .in(ProjectRoleUser::getUserId, userIdList)
                    .innerJoin(Project.class, Project::getId, ProjectRole::getProjectId)
                    .isNotNull(ProjectRoleUser::getProjectId)).stream()
                    .collect(Collectors.groupingBy(ProjectRoleUser::getUserId, Collectors.mapping(ProjectRoleUser::getProjectId, Collectors.toSet())));
            List<String> projectIdList = projectIdMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
            //用户负责项目计划、交付物、反馈记录
            Map<String, List<ProjectScheme>> projectSchemeMap;
            Map<String, Long> deliverableMap = new HashMap<>();
            Map<String, Long> projectSchemeContentMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(projectIdList)) {
                projectSchemeMap = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                        .select(ProjectScheme::getId, ProjectScheme::getParentChain, ProjectScheme::getStatus, ProjectScheme::getCircumstance,
                                ProjectScheme::getDurationDays, ProjectScheme::getActualEndTime, ProjectScheme::getActualBeginTime, ProjectScheme::getRspUser)
                        .in(ProjectScheme::getProjectId, projectIdList)
                        .in(ProjectScheme::getRspUser, userIdList)).stream().collect(Collectors.groupingBy(ProjectScheme::getRspUser));
                List<String> projectSchemeIdList = projectSchemeMap.values().stream().flatMap(Collection::stream).map(ProjectScheme::getId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(projectSchemeIdList)) {
                    deliverableMap.putAll(deliverableService.list(new LambdaQueryWrapperX<>(Deliverable.class)
                            .select(Deliverable::getPlanId, Deliverable::getId)
                            .in(Deliverable::getPlanId, projectSchemeIdList))
                            .stream().collect(Collectors.groupingBy(Deliverable::getPlanId, Collectors.counting())));
                    projectSchemeContentMap.putAll(projectSchemeContentService.list(new LambdaQueryWrapperX<>(ProjectSchemeContent.class)
                            .select(ProjectSchemeContent::getId, ProjectSchemeContent::getProjectSchemeId)
                            .in(ProjectSchemeContent::getProjectSchemeId, projectSchemeIdList))
                            .stream().collect(Collectors.groupingBy(ProjectSchemeContent::getProjectSchemeId, Collectors.counting())));
                }
            } else {
                projectSchemeMap = new HashMap<>();
            }
            //用户工时
            Map<String, List<WorkHoursReportDetailInfoVO>> workHoursMap = workHoursReportDetailForUserService.getWorkHoursReportDetailListByUser(userIdList)
                    .stream().collect(Collectors.groupingBy(WorkHoursReportDetailInfoVO::getCreatorId));

            int sort = 1;
            for (UserPerformanceReportVO vo : vos) {
                vo.setSort(sort++);
                String id = vo.getId();
                SimpleUser simpleUser = simpleUserMap.get(id);
                if (simpleUser!= null) {
                    vo.setOrgName(simpleUser.getOrgName());
                    vo.setDeptName(simpleUser.getDeptName());
                }
                if (projectIdMap.containsKey(id)) {
                    vo.setProjectNum(projectIdMap.get(id).size());
                }
                if (projectSchemeMap.containsKey(id)) {
                    List<ProjectScheme> projectSchemeList = projectSchemeMap.get(id);
                    List<String> projectSchemeIdList = projectSchemeList.stream().map(ProjectScheme::getId).collect(Collectors.toList());
                    vo.setResTaskNum(projectSchemeList.size());
                    vo.setCompleteTaskNum(projectSchemeList.stream().filter(f -> Status.FINISHED.getCode().equals(f.getStatus())).count());
                    vo.setOverdueTaskNum(projectSchemeList.stream().filter(f -> Status.CIRCUMSTANCE_OVERD.getCode().equals(f.getCircumstance()) || Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode().equals(f.getCircumstance())).count());
                    //todo 变更计划数
                    vo.setDeliverNum(projectSchemeList.stream().mapToLong(m -> deliverableMap.getOrDefault(m.getId(), 0L)).sum());
                    BigDecimal resTaskNum = new BigDecimal(vo.getResTaskNum());
                    if (BigDecimal.ZERO.compareTo(resTaskNum) != 0) {
                        BigDecimal completeNormalTaskNum = new BigDecimal(projectSchemeList.stream().filter(f -> Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode().equals(f.getCircumstance())).count());
                        vo.setTaskOnTimeCompleteRate(completeNormalTaskNum.divide(resTaskNum, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%");
                    }
                    vo.setTaskFeedbackNum(projectSchemeList.stream().mapToLong(m -> projectSchemeContentMap.getOrDefault(m.getId(), 0L)).sum());
                    //计划工期 父级子级同时存在只算父级
                    int durationDays = 0;
                    int actualDurationDays = 0;
                    for (ProjectScheme projectScheme : projectSchemeList) {
                        String parentChain = projectScheme.getParentChain();
                        if (StrUtil.isNotBlank(parentChain) && Arrays.stream(parentChain.split(",")).noneMatch(projectSchemeIdList::contains)) {
                            if (projectScheme.getDurationDays() != null) {
                                durationDays += projectScheme.getDurationDays();
                            }
                            if (projectScheme.getActualBeginTime() != null && projectScheme.getActualEndTime() != null) {
                                actualDurationDays += DateUtil.betweenDay(projectScheme.getActualBeginTime(), projectScheme.getActualEndTime(), true);
                            }
                        }
                    }
                    vo.setDurationDays(durationDays);
                    vo.setActualDurationDays(actualDurationDays);
                    List<WorkHoursReportDetailInfoVO> workHoursList = workHoursMap.getOrDefault(id, new ArrayList<>());
                    double manHour = workHoursList.stream().mapToDouble(WorkHoursReportDetailInfoVO::getWorkHour).sum();
                    vo.setManHour(new BigDecimal(manHour).divide(new BigDecimal("8"), 2, RoundingMode.HALF_UP).doubleValue());
                    if (manHour != 0) {
                        double onTimeManHour = workHoursList.stream()
                                .filter(f -> f.getWorkDate() != null
                                        && DateUtil.formatDate(f.getWorkDate()).equals(DateUtil.formatDate(f.getCreateTime())))
                                .mapToDouble(WorkHoursReportDetailInfoVO::getWorkHour).sum();
                        BigDecimal manHourRate = new BigDecimal(onTimeManHour).divide(new BigDecimal(manHour), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                        vo.setManHourRate(manHourRate.stripTrailingZeros().toPlainString() + "%");
                    }

                }
            }
        }
    }

    @Override
    public Page<ProjectByUserVO> pageProjectByUserId(String userId, Page<ProjectDTO> pageRequest) {
        return projectService.pageProjectByUserId(userId, pageRequest);
    }

    @Override
    public void exportProjectByUserId(String userId, HttpServletResponse response) throws Exception {
        projectService.exportExcelByUserId(userId, response);
    }

    @Override
    public Page<ProjectSchemePerformanceVO> pageProjectSchemeByUserId(Page<ProjectSchemeReportDTO> pageRequest) throws Exception {
     return projectSchemeService.pageProjectSchemeByUserId(pageRequest);
    }

    @Override
    public void exportProjectSchemeByUserId(ProjectSchemeReportDTO projectSchemeReportDTO, HttpServletResponse response) throws Exception {
        projectSchemeService.exportExcelByUserId(projectSchemeReportDTO, response);
    }

    @Override
    public Page<DeliverablePerformanceVO> pageDeliverableByUserId(String userId, Page<DeliverableDTO> pageRequest) throws Exception {
        return deliverableService.pageDeliverableByUserId(userId, pageRequest);
    }

    @Override
    public void exportDeliverableByUserId(String userId, Page<DeliverableDTO> pageRequest, HttpServletResponse response) throws Exception {
        deliverableService.exportExcelByUserId(userId, pageRequest, response);
    }
}
