<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="selectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMLX_container_05_button_01',powerData)"
        type="primary"
        icon="add"
        @click="addTableNode"
      >
        新增
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMLX_container_05_button_03',powerData)"
        icon="sie-icon-shanchu"
        :disabled="selectedRowKeys.length===0"
        @click="deleteBatch"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import {
  h, inject, ref, Ref,
} from 'vue';
import {
  BasicButton, isPower, openDrawer, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import AddProductMessage from '../components/AddProductMessage.vue';
import { declarationData } from '../keys';

const tableRef = ref();
// 表格勾选数据
const detailsData = inject(declarationData);
const selectedRowKeys = ref([]);
const powerData = inject('powerData');
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: {},
  smallSearchField: ['name'],
  showIndexColumn: true,
  api: (tableParams) => {
    tableParams.query = {
      approvalId: detailsData.value.id,
    };
    return new Api('/pms').fetch(tableParams, 'projectApprovalProduct/page', 'POST');
  },
  columns: [
    {
      title: '产品编号',
      align: 'left',
      dataIndex: 'number',
    },
    {
      title: '产品名称',
      align: 'left',
      dataIndex: 'name',
    },
    {
      title: '产品型号',
      align: 'left',
      dataIndex: 'productModelNumber',
    },
    {
      title: '产品组',
      align: 'left',
      dataIndex: 'productGroupName',
      width: 170,
    },
    {
      title: '物料等级',
      align: 'left',
      dataIndex: 'materialLevelName',
    },
    {
      title: '产品类别',
      dataIndex: 'productClassifyName',
      width: 150,
    },
    {
      title: '产品二级分类',
      align: 'left',
      dataIndex: 'productSecondClassifyName',
    },
    {
      title: '高质量等级',
      align: 'left',
      dataIndex: 'highQualityLevelName',
    },
    {
      title: '国产化管控',
      align: 'left',
      dataIndex: 'localizedControlName',
    },
    {
      title: '所需台套',
      dataIndex: 'requiredUnitNum',
      align: 'left',
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      dataIndex: 'actions',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: () => isPower('PMS_XMLX_container_05_button_02', powerData),
      onClick: (record) => {
        openFormDrawer({
          type: 'edit',
          id: record.id,
        });
        // action('edit', record);
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMLX_container_05_button_03', powerData),
      onClick: (record) => {
        // goDelete([record.id]);
        deleteBatchData([record.id], 'one');
      },
    },
  ],
});

function addTableNode() {
  openFormDrawer({
    type: 'add',
    approvalId: detailsData.value.id,
  });
}

function openFormDrawer(drawerData) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: drawerData.type === 'add' ? '新增产品' : '编辑产品',
    width: 1000,
    content() {
      return h(AddProductMessage, {
        ref: drawerRef,
        drawerData,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      reload();
    },
  });
}

function reload() {
  tableRef.value.reload({ page: 1 });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    async onOk() {
      await new Api('/pms').fetch(params, 'projectApprovalProduct/remove', 'DELETE');
      // await props.deleteQuestionBatchApi(params);
      message.success('删除成功');
      reload();
    },
  });
}

</script>
