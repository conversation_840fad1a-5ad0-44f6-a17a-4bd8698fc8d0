<template>
  <UploadList
    ref="tableRef"
    class="pay-node-table"
    :listApi="listApi"
    type="page"
    :saveApi="saveApi"
    :batchDeleteApi="batchDeleteApi"
    :deleteApi="deleteApi"
    :powerCode="powerCode"
    :powerData="powerData"
  />
</template>
<script lang="ts">
import { defineComponent, inject, Ref } from 'vue';
import { isPower, UploadList } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import Api from '/@/api';
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    UploadList,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData:Ref = inject('powerData');
    let formData: any = inject('formData');

    async function listApi() {
      return new Api('/res/manage/file/listByIds').fetch([riskItemId.value], '', 'POST');
    }

    async function saveApi(filesRes) {
      let api = '/res/manage/file/batch';
      let fieldList = filesRes.map((item) => {
        item.dataId = riskItemId.value;
        item.projectId = formData?.value?.projectId;
        return item;
      });
      if (formData?.value?.projectId) {
        api = '/pms/document/saveBatch';
      }
      return new Api(api).fetch(fieldList, '', 'POST');
    }
    async function deleteApi(deleteApi) {
      return removeBatchDetailsApi([deleteApi.id]);
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return removeBatchDetailsApi(rows.map((item) => item.id));
    }

    const powerCode = {
      delete: 'PMS_FXGLXQ_container_02_03_button_01',
      download: 'PMS_FXGLXQ_container_02_03_button_03',
      upload: 'PMS_FXGLXQ_container_02_03_button_02',
      preview: 'PMS_XMSBXQ_container_04_button_03',
    };

    let riskItemId: any = inject('riskItemId');

    return {
      powerData,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCode,
    };
  },
  methods: { isPower },

});
</script>
<style lang="less" scoped>
</style>
