package  com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.dto.PersonManageLedgerDTO;
import com.chinasie.orion.domain.vo.PersonManageLedgerVO;
import com.chinasie.orion.service.PersonManageLedgerService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonManageLedger 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:49
 */
@RestController
@RequestMapping("/person-manage-ledger")
@Api(tags = "人员管理台账")
public class PersonManageLedgerController {

    @Autowired
    private PersonManageLedgerService personManageLedgerService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员管理台账】数据", type = "PersonManageLedger", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PersonManageLedgerVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PersonManageLedgerVO rsp = personManageLedgerService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personManageLedgerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【人员管理台账】数据【{{#personManageLedgerDTO.number}}--{{#personManageLedgerDTO.name}}】", type = "PersonManageLedger", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonManageLedgerDTO personManageLedgerDTO) throws Exception {
        String rsp =  personManageLedgerService.create(personManageLedgerDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personManageLedgerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员管理台账】数据【{{#personManageLedgerDTO.number}}--{{#personManageLedgerDTO.name}}】", type = "PersonManageLedger", subType = "编辑", bizNo = "{{#personManageLedgerDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonManageLedgerDTO personManageLedgerDTO) throws Exception {
        Boolean rsp = personManageLedgerService.edit(personManageLedgerDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【人员管理台账】数据", type = "PersonManageLedger", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personManageLedgerService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【人员管理台账】数据", type = "PersonManageLedger", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personManageLedgerService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员管理台账】数据", type = "PersonManageLedger", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonManageLedgerVO>> pages(@RequestBody Page<PersonManageLedgerDTO> pageRequest) throws Exception {
        Page<PersonManageLedgerVO> rsp =  personManageLedgerService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
