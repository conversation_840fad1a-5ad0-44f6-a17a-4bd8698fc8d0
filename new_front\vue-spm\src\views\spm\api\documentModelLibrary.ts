import Api from '/@/api';

/**
 * 分页
 * @param mainTableId 参数
 * @param params 参数
 */
export const page = (mainTableId, params) => new Api(`/spm/documentModelLibrary/page/${mainTableId}`).fetch(params, '', 'POST');
/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/spm/documentModelLibrary/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/documentModelLibrary/edit').fetch(params, '', 'PUT');
/**
 * 详情
 * @param id 参数
 * @param pageCode 参数
 */
export const documentModelLibraryById = (id, pageCode = '') => new Api(`/spm/documentModelLibrary/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');
/**
 * 启用
 * @param ids 参数
 */
export const enable = (ids) => new Api('/spm/documentModelLibrary/enable').fetch(ids, '', 'PUT');
/**
 * 禁用
 * @param ids 参数
 */
export const disable = (ids) => new Api('/spm/documentModelLibrary/disable').fetch(ids, '', 'PUT');
/**
 * 删除
 * @param ids 参数
 */
export const remove = (ids) => new Api('/spm/documentModelLibrary/remove').fetch(ids, '', 'DELETE');
/**
 * 版本记录
 * @param id 参数
 */
export const getVersionRecords = (id) => new Api(`/spm/documentModelLibrary/getVersionRecords/${id}`).fetch('', '', 'GET');
/**
 * 升版
 * @param id 参数
 */
export const upVersion = (id) => new Api(`/spm/documentModelLibrary/upVersion/${id}`).fetch('', '', 'GET');
