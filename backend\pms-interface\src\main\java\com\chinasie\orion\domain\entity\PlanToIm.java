package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PlanToIm Entity对象
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@TableName(value = "pmsx_plan_to_im")
@ApiModel(value = "PlanToImEntity对象", description = "计划和接口的关联关系")
@Data
public class PlanToIm extends ObjectEntity implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

}
