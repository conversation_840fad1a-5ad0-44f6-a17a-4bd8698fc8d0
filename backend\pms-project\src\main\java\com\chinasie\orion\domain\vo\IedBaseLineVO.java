package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * IedBaseLine VO对象
 *
 * <AUTHOR>
 * @since 2024-01-30 13:47:05
 */
@ApiModel(value = "IedBaseLineVO对象", description = "ied基线")
@Data
public class IedBaseLineVO extends ObjectVO implements Serializable{

            /**
         * 计划提交时间
         */
        @ApiModelProperty(value = "计划提交时间")
        private Date planSubmitTime;

        /**
         * 编写人
         */
        @ApiModelProperty(value = "编写人")
        private String writer;

        /**
         * 责任人
         */
        @ApiModelProperty(value = "责任人")
        private String resPerson;

        /**
         * 责任部门
         */
        @ApiModelProperty(value = "责任部门")
        private String resDept;

        /**
         * 类型
         */
        @ApiModelProperty(value = "类型")
        private String type;

        /**
         * 文件状态
         */
        @ApiModelProperty(value = "文件状态")
        private String fileStatus;

        /**
         * 版本
         */
        @ApiModelProperty(value = "版本")
        private String revId;

        /**
         * 项目id
         */
        @ApiModelProperty(value = "项目id")
        private String projectId;

        /**
         * 原ied主键
         */
        @ApiModelProperty(value = "原ied主键")
        private String oldId;

        /**
         * 基线ID
         */
        @ApiModelProperty(value = "基线ID")
        private String baseLineId;

    }
