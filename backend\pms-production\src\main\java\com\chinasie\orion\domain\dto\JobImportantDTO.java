package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "JobImportantDTO对象", description = "重大项目统计")
@Data
@ExcelIgnoreUnannotated
public class JobImportantDTO  implements Serializable {
    @ApiModelProperty(value = "重大项目总数")
    private long bigTotal;

    @ApiModelProperty(value = "重大项目已完成数")
    private long bigCompleted;

    @ApiModelProperty(value = "实施中作业数")
    private long operation;

    @ApiModelProperty(value = "已完成作业数")
    private long completed;
    @ApiModelProperty(value = "作业总数")
    private long jobTotal;


}
