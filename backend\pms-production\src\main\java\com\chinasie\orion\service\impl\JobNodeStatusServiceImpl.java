package com.chinasie.orion.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.domain.entity.JobNodeStatus;
import com.chinasie.orion.domain.dto.JobNodeStatusDTO;
import com.chinasie.orion.domain.vo.JobNodeStatusVO;
import com.chinasie.orion.service.JobNodeStatusService;
import com.chinasie.orion.repository.JobNodeStatusMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobNodeStatus 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-08 14:45:20
 */
@Service
@Slf4j
public class JobNodeStatusServiceImpl extends  OrionBaseServiceImpl<JobNodeStatusMapper, JobNodeStatus>   implements JobNodeStatusService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private LockTemplate lockTemplate;
    private String LOCKED_KEY ="pmsx::JobManage-life-cycle::locked::id::";


    private String LOCKED_KEY_BATCH ="pmsx::JobManage-life-cycle::locked::id::batch";
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobNodeStatusVO detail(String id,String pageCode) throws Exception {
        JobNodeStatus jobNodeStatus =this.getById(id);
        JobNodeStatusVO result = BeanCopyUtils.convertTo(jobNodeStatus,JobNodeStatusVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobNodeStatusDTO
     */
    @Override
    public  String create(JobNodeStatusDTO jobNodeStatusDTO) throws Exception {
        JobNodeStatus jobNodeStatus =BeanCopyUtils.convertTo(jobNodeStatusDTO,JobNodeStatus::new);
        this.save(jobNodeStatus);

        String rsp=jobNodeStatus.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobNodeStatusDTO
     */
    @Override
    public Boolean edit(JobNodeStatusDTO jobNodeStatusDTO) throws Exception {
        JobNodeStatus jobNodeStatus =BeanCopyUtils.convertTo(jobNodeStatusDTO,JobNodeStatus::new);

        this.updateById(jobNodeStatus);

        String rsp=jobNodeStatus.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobNodeStatusVO> pages( Page<JobNodeStatusDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobNodeStatus> condition = new LambdaQueryWrapperX<>( JobNodeStatus. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobNodeStatus::getCreateTime);


        Page<JobNodeStatus> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobNodeStatus::new));

        PageResult<JobNodeStatus> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobNodeStatusVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobNodeStatusVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobNodeStatusVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业节点执行状态表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobNodeStatusDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobNodeStatusExcelListener excelReadListener = new JobNodeStatusExcelListener();
        EasyExcel.read(inputStream,JobNodeStatusDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobNodeStatusDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业节点执行状态表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobNodeStatus> jobNodeStatuses =BeanCopyUtils.convertListTo(dtoS,JobNodeStatus::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobNodeStatus-import::id", importId, jobNodeStatuses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobNodeStatus> jobNodeStatuses = (List<JobNodeStatus>) orionJ2CacheService.get("pmsx::JobNodeStatus-import::id", importId);
        log.info("作业节点执行状态表导入的入库数据={}", JSONUtil.toJsonStr(jobNodeStatuses));

        this.saveBatch(jobNodeStatuses);
        orionJ2CacheService.delete("pmsx::JobNodeStatus-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobNodeStatus-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobNodeStatus> condition = new LambdaQueryWrapperX<>( JobNodeStatus. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobNodeStatus::getCreateTime);
        List<JobNodeStatus> jobNodeStatuses =   this.list(condition);

        List<JobNodeStatusDTO> dtos = BeanCopyUtils.convertListTo(jobNodeStatuses, JobNodeStatusDTO::new);

        String fileName = "作业节点执行状态表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobNodeStatusDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobNodeStatusVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<JobNodeStatusVO> listByJobId(String jobId) throws Exception {
        LambdaQueryWrapperX<JobNodeStatus> wrapperX = new LambdaQueryWrapperX<>(JobNodeStatus.class);
        wrapperX.eq(JobNodeStatus::getJobId,jobId);
        wrapperX.select(JobNodeStatus::getJobId,JobNodeStatus::getId,JobNodeStatus::getNodeKeyJson);
        List<JobNodeStatus> jobNodeStatusList =this.list(wrapperX);
        if(CollectionUtils.isEmpty(jobNodeStatusList)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(jobNodeStatusList,JobNodeStatusVO::new);
    }

    @Override
    public void setNodeStatus(String jobId, List<String>  keyList) throws Exception {
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY+jobId, 3000L, 5000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        try {
            LambdaQueryWrapperX<JobNodeStatus> wrapperX = new LambdaQueryWrapperX<>(JobNodeStatus.class);
            wrapperX.eq(JobNodeStatus::getJobId, jobId);
            wrapperX.select(JobNodeStatus::getJobId, JobNodeStatus::getId, JobNodeStatus::getNodeKeyJson);
            List<JobNodeStatus> jobNodeStatuses = this.list(wrapperX);
            if (CollectionUtils.isEmpty(jobNodeStatuses)) {
                JobNodeStatus jobNodeStatus = new JobNodeStatus();
                jobNodeStatus.setJobId(jobId);
                jobNodeStatus.setNodeKeyJson(JSONUtil.toJsonStr(keyList));
                this.save(jobNodeStatus);
            }else{
                JobNodeStatus jobNodeStatus =  jobNodeStatuses.get(0);
                String nodeKeyJson=  jobNodeStatus.getNodeKeyJson();
                if(StrUtil.isNotBlank(nodeKeyJson)){
                    List<String> keys = JSONUtil.toList(nodeKeyJson, String.class);
                    if(CollectionUtils.isEmpty(keys)){
                        keys = new ArrayList<>();
                    }
                    Set<String> keySet = keys.stream().collect(Collectors.toSet());
                    List<String> finalKeys = keys;
                    AtomicBoolean b = new AtomicBoolean(false);
                    keyList.forEach(key->{
                        if(!finalKeys.contains(key)){
                            finalKeys.add(key);
                            b.set(true);
                        }
                    });
                    if(b.get()){
                        LambdaUpdateWrapper<JobNodeStatus> updateWrapper = new LambdaUpdateWrapper<>(JobNodeStatus.class);
                        updateWrapper.eq(JobNodeStatus::getJobId, jobId);
                        updateWrapper.set(JobNodeStatus::getNodeKeyJson,JSONUtil.toJsonStr(keys));
                        this.update(updateWrapper);
                    }
               }
            }
        }catch (Exception e){
            log.error("setNodeStatus error",e);
        }finally {
            lockTemplate.releaseLock(lockInfo);
        }
    }

    @Override
    public void setNodeStatusByIdList(List<String> jobIdList, String operator) {
        if(CollectionUtils.isEmpty(jobIdList)){
            return;
        }
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY_BATCH, 3000L, 5000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        try {
            LambdaQueryWrapperX<JobNodeStatus> wrapperX = new LambdaQueryWrapperX<>(JobNodeStatus.class);
            wrapperX.in(JobNodeStatus::getJobId, jobIdList);
            wrapperX.select(JobNodeStatus::getJobId, JobNodeStatus::getId, JobNodeStatus::getNodeKeyJson);
            List<JobNodeStatus> jobNodeStatuses = this.list(wrapperX);
            List<String> nodeKeyList=    Arrays.asList(operator);
            if (CollectionUtils.isEmpty(jobNodeStatuses)) {
               this.saveEntityList(jobIdList,nodeKeyList);

            }else{
                List<JobNodeStatus> updateList =new ArrayList<>();
                jobNodeStatuses.stream().forEach(item->{
                    // 移除掉 已经存在的
                    jobIdList.remove(item.getJobId());
                    // 节点状态JSON
                    String nodeKeyJson=  item.getNodeKeyJson();
                    if(StrUtil.isNotBlank(nodeKeyJson)){
                        List<String> keys = JSONUtil.toList(nodeKeyJson, String.class);
                        if(CollectionUtils.isEmpty(keys)){
                            keys = new ArrayList<>();
                        }
                        Set<String> keySet = keys.stream().collect(Collectors.toSet());
                        List<String> finalKeys = keys;
                        AtomicBoolean b = new AtomicBoolean(false);
                        nodeKeyList.forEach(key->{
                            if(!finalKeys.contains(key)){
                                finalKeys.add(key);
                                b.set(true);
                            }
                        });
                        if(b.get()){
                            item.setNodeKeyJson(JSONUtil.toJsonStr(keys));
                            updateList.add(item);
                        }
                    }
                });

                // 将需要更新的进行更新
                if(!CollectionUtils.isEmpty(updateList)){
                    this.updateBatchById(updateList);
                }
                if(!CollectionUtils.isEmpty(jobIdList)){
                    this.saveEntityList(jobIdList,nodeKeyList);
                }
            }
        }catch (Exception e){
            log.error("setNodeStatus error",e);
        }finally {
            lockTemplate.releaseLock(lockInfo);
        }
    }

    private void saveEntityList(List<String> jobIdList, List<String> nodeKeyList) {
        List<JobNodeStatus> jobNodeStatusList = new ArrayList<>();
        for (String jobId : jobIdList) {
            JobNodeStatus jobNodeStatus = new JobNodeStatus();
            jobNodeStatus.setJobId(jobId);
            jobNodeStatus.setNodeKeyJson(JSONUtil.toJsonStr(nodeKeyList));
            jobNodeStatusList.add(jobNodeStatus);
        }
        this.saveBatch(jobNodeStatusList);
    }

    public static class JobNodeStatusExcelListener extends AnalysisEventListener<JobNodeStatusDTO> {

        private final List<JobNodeStatusDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobNodeStatusDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobNodeStatusDTO> getData() {
            return data;
        }
    }


}
