package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * LaborCostAcceptanceStatistics VO对象
 *
 * <AUTHOR>
 * @since 2024-10-29 14:23:45
 */
@ApiModel(value = "LaborCostAcceptanceStatisticsVO对象", description = "验收人力成本费用统计")
@Data
public class LaborCostAcceptanceStatisticsVO extends  ObjectVO   implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    private String acceptanceId;


    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    private String jobGrade;


    /**
     * 计划需求人数
     */
    @ApiModelProperty(value = "计划需求人数")
    private Integer planUserCount;


    /**
     * 实际人数
     */
    @ApiModelProperty(value = "实际人数")
    private Integer actualUserCount;


    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    private BigDecimal workload;


    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    private BigDecimal jobGradeAmt;


    /**
     * 岗级总价 (元)
     */
    @ApiModelProperty(value = "岗级总价 (元)")
    private BigDecimal jobGradeTotalAmt;




}
