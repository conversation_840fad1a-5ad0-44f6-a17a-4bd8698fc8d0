<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
  http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>net.java.dev.jna</groupId>
  <artifactId>jna</artifactId>
  <version>5.11.0</version>
  <packaging>jar</packaging>

  <name>Java Native Access</name>
  <description>Java Native Access</description>
  <url>https://github.com/java-native-access/jna</url>

  <licenses>
    <license>
      <name>LGPL-2.1-or-later</name>
      <url>https://www.gnu.org/licenses/old-licenses/lgpl-2.1</url>
      <distribution>repo</distribution>
      <comments>
        Java Native Access (JNA) is licensed under the LGPL, version 2.1 or
        later, or the Apache License, version 2.0. You can freely decide which
        license you want to apply to the project.
      </comments>
    </license>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>
        Java Native Access (JNA) is licensed under the LGPL, version 2.1 or
        later, or the Apache License, version 2.0. You can freely decide which
        license you want to apply to the project.
      </comments>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:https://github.com/java-native-access/jna</connection>
    <developerConnection>scm:git:ssh://**************/java-native-access/jna.git</developerConnection>
    <url>https://github.com/java-native-access/jna</url>
  </scm>

  <developers>
    <developer>
      <id>twall</id>
      <name>Timothy Wall</name>
      <roles>
        <role>Owner</role>
      </roles>
    </developer>
    <developer>
      <email><EMAIL></email>
      <name>Matthias Bläsing</name>
      <url>https://github.com/matthiasblaesing/</url>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

</project>
