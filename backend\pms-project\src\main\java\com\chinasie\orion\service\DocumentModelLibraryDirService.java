package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.DocumentModelLibraryDirDTO;
import com.chinasie.orion.domain.entity.DocumentModelLibraryDir;
import com.chinasie.orion.domain.vo.DocumentModelLibraryDirVO;

import com.chinasie.orion.mybatis.service.OrionTreeNodeService;


/**
 * <p>
 * DocumentModelLibraryDir 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
public interface DocumentModelLibraryDirService  extends  OrionTreeNodeService<DocumentModelLibraryDir, DocumentModelLibraryDirDTO, DocumentModelLibraryDirVO>  {

}
