package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "EvaluationPerformanceVO对象", description = "项目履约评价详情")
@Data
public class EvaluationPerformanceVO extends ObjectVO implements Serializable {
    /**
     * 用户满意度
     */
    @ApiModelProperty(value = "用户满意度描述")
    private String userSatisfaction;
    /**
     * 用户满意度评分
     */
    @ApiModelProperty(value = "用户满意度评分")
    private Integer userSatisfactionScore;
    /**
     * 合同履行
     */
    @ApiModelProperty(value = "合同履行描述")
    private String performanceContract;
    /**
     * 合同履行评分
     */
    @ApiModelProperty(value = "合同履行评分")
    private Integer performanceContractScore;
    /**
     * 响应能力
     */
    @ApiModelProperty(value = "响应能力描述")
    private String responsiveness;
    /**
     * 响应能力评分
     */
    @ApiModelProperty(value = "响应能力评分")
    private Integer responsivenessScore;
    /**
     * 变更管理
     */
    @ApiModelProperty(value = "变更管理描述")
    private String changeManage;
    /**
     * 变更管理评分
     */
    @ApiModelProperty(value = "变更管理评分")
    private Integer changeManageScore;
    /**
     * 问题解决
     */
    @ApiModelProperty(value = "问题解决描述")
    private String problemSolve;
    /**
     * 问题解决评分
     */
    @ApiModelProperty(value = "问题解决评分")
    private Integer problemSolveScore;
    /**
     * 信息安全和保密
     */
    @ApiModelProperty(value = "信息安全和保密描述")
    private String informSecConfidentiality;
    /**
     * 信息安全和保密评分
     */
    @ApiModelProperty(value = "信息安全和保密评分")
    private Integer informSecConfidentialityScore;
    /**
     * 长期伙伴关系
     */
    @ApiModelProperty(value = "长期伙伴关系描述")
    private String longTermPartner;
    /**
     * 长期伙伴关系评分
     */
    @ApiModelProperty(value = "长期伙伴关系评分")
    private Integer longTermPartnerScore;

}
