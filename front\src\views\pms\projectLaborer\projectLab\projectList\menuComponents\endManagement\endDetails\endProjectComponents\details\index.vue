<template>
  <div class="detail-wrap">
    <div>
      <basicTitle :title="'预览'">
        <!--          <pdmImage-->
        <!--            :img-url="pictureBase + formState.projectImage"-->
        <!--            :show-delete="formType == 'edit'"-->
        <!--            @deleteImgUrl="deleteImgUrl"-->
        <!--          />-->
        <!--                  <img-->
        <!--                    class=""-->
        <!--                    src="datavalue[0].projectImage"-->
        <!--                  >-->
      </basicTitle>
    </div>

    <div>
      <basicTitle :title="'基本信息'">
        <div class="productLibraryDetails_right_content">
          <a-form
            ref="formRef"
            :model="formState"
            class="pdmFormClass"
            :rules="formType == 'details' ? {} : rules"
            :label-col="{ span: 5 }"
            :wrapper-col="{ span: 19 }"
            label-align="left"
          >
            <a-form-item
              label="项目编号"
              name="code"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.number }}</span>
            </a-form-item>
            <a-form-item
              label="项目名称"
              name="name"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.name }}</span>
            </a-form-item>
            <a-form-item
              label="结项类型"
              name="type"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.typeName }}</span>
            </a-form-item>

            <a-form-item
              label="开始时间"
              name="startTime"
              :style="{ height: '15px' }"
            >
              <span>{{
                formState.startTime ? dayjs(formState.startTime).format('YYYY-MM-DD') : ''
              }}</span>
            </a-form-item>
            <a-form-item
              label="实际结束时间"
              name="endTime"
              :style="{ height: '15px' }"
            >
              <span>{{
                formState.endTime ? dayjs(formState.endTime).format('YYYY-MM-DD') : ''
              }}</span>
            </a-form-item>

            <a-form-item
              label="状态"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.statusName }}</span>
            </a-form-item>

            <a-form-item
              label="负责人"
              name="principalId"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.principalName }}</span>
            </a-form-item>

            <a-form-item
              label="结项申请理由"
              :style="{ height: '60px' }"
            >
              <span
                class="descriptionStyle"
              >{{ formState.reason }}</span>
            </a-form-item>
            <a-form-item
              label="修改人"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.modifyName }}</span>
            </a-form-item>
            <a-form-item
              label="修改时间"
              :style="{ height: '15px' }"
            >
              <span>{{
                formState.modifyTime
                  ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                  : ''
              }}</span>
            </a-form-item>
            <a-form-item
              label="创建人"
              :style="{ height: '15px' }"
            >
              <span>{{ formState.creatorName }}</span>
            </a-form-item>
            <a-form-item
              label="创建时间"
              :style="{ height: '15px' }"
            >
              <span>{{
                formState.createTime
                  ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                  : ''
              }}</span>
            </a-form-item>
          </a-form>
        </div>
      </basicTitle>
    </div>
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, onMounted, reactive, toRefs, watch,
} from 'vue';
import { Form } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
/* api */
import dayjs from 'dayjs';
import { isPower } from 'lyra-component-vue3';

export default defineComponent({
  name: 'ProjectLabdetail',
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    basicTitle,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const formData = inject('formData', {});
    const getFormData = inject('getFormData');
    const state = reactive({
      btnObjectData: {
        edit: { show: computed(() => isPower('JX_container_button_02', state.powerData)) },
      },
      formState: formData?.value,
      contentHeight: 600,
      powerData: [],
    });
    state.powerData = inject('powerData');
    watch(
      () => formData?.value,
      (val) => {
        state.formState = val;
      },
    );

    onMounted(() => {
      state.contentHeight = document.body.clientHeight - 365;
    });
    const updateData = () => {
      getFormData.value();
    };

    return {
      ...toRefs(state),
      dayjs,
      updateData,
    };
  },
});
</script>
<style lang="less" scoped>
.detail-wrap {
  display: flex;
  padding: ~`getPrefixVar('content-padding-top')`  ~`getPrefixVar('content-padding-left')`;
  >div {
    flex: 1;
    &:nth-child(1) {
      margin-right: ~`getPrefixVar('content-padding-left')`;
    }
  }
}
</style>
