package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.IncomePlanDataControlDTO;
import com.chinasie.orion.domain.entity.IncomePlanDataControl;
import com.chinasie.orion.domain.vo.IncomePlanDataControlVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;


/**
 * <p>
 * IncomePlanDataControl 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
public interface IncomePlanDataControlService  extends  OrionBaseService<IncomePlanDataControl>  {


        /**
         *  详情
         *
         * * @param id
         */
    IncomePlanDataControlVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param incomePlanDataControlDTO
         */
        String create(IncomePlanDataControlDTO incomePlanDataControlDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param incomePlanDataControlDTO
         */
        Boolean edit(IncomePlanDataControlDTO incomePlanDataControlDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<IncomePlanDataControlVO> pages( Page<IncomePlanDataControlDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<IncomePlanDataControlVO> vos)throws Exception;

        Boolean lock(List<IncomePlanDataControlDTO> incomePlanDataControlDTOs) throws ParseException;

        Boolean unLock(List<IncomePlanDataControlDTO> incomePlanDataControlDTOs);

        void saveIncomePlanDataControl(String incomePlanId);

        List<IncomePlanDataControlVO>  getList(String id,List<List<SearchCondition>> searchConditions) throws Exception;
}
