package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.dto.NcfPurchProjectImplementationDTO;
import com.chinasie.orion.management.domain.vo.NcfPurchProjectImplementationVO;
import com.chinasie.orion.management.service.NcfPurchProjectImplementationService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * NcfPurchProjectImplementation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
@RestController
@RequestMapping("/ncfPurchProjectImplementation")
@Api(tags = "采购项目实施表")
public class NcfPurchProjectImplementationController {

    @Autowired
    private NcfPurchProjectImplementationService ncfPurchProjectImplementationService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据", type = "采购项目实施表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfPurchProjectImplementationVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfPurchProjectImplementationVO rsp = ncfPurchProjectImplementationService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfPurchProjectImplementationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfPurchProjectImplementationDTO.name}}】", type = "采购项目实施表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception {
        String rsp = ncfPurchProjectImplementationService.create(ncfPurchProjectImplementationDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfPurchProjectImplementationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfPurchProjectImplementationDTO.name}}】", type = "采购项目实施表", subType = "编辑", bizNo = "{{#ncfPurchProjectImplementationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfPurchProjectImplementationDTO ncfPurchProjectImplementationDTO) throws Exception {
        Boolean rsp = ncfPurchProjectImplementationService.edit(ncfPurchProjectImplementationDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购项目实施表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfPurchProjectImplementationService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购项目实施表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfPurchProjectImplementationService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购项目实施表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfPurchProjectImplementationVO>> pages(@RequestBody Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception {
        Page<NcfPurchProjectImplementationVO> rsp = ncfPurchProjectImplementationService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购项目实施表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购项目实施表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfPurchProjectImplementationService.downloadExcelTpl(response);
    }

    @ApiOperation("采购项目实施表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购项目实施表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfPurchProjectImplementationService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购项目实施表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购项目实施表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfPurchProjectImplementationService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购项目实施表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购项目实施表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfPurchProjectImplementationService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购项目实施表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购项目实施表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfPurchProjectImplementationDTO> pageRequest, HttpServletResponse response) throws Exception {
        ncfPurchProjectImplementationService.exportByExcel(pageRequest, response);
    }

    /**
     * 查询总数及金额
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询总数及金额")
    @LogRecord(success = "【{USER{#logUserId}}】查询总数及金额", type = "采购项目实施表", subType = "查询总数及金额", bizNo = "")
    @RequestMapping(value = "/getNumMoney", method = RequestMethod.POST)
    public ResponseDTO<Map<String,Object>> getNumMoney(@RequestBody Page<NcfPurchProjectImplementationDTO> pageRequest) throws Exception {
        Map<String,Object> map = ncfPurchProjectImplementationService.getNumMoney(pageRequest);
        return new ResponseDTO<>(map);
    }
}
