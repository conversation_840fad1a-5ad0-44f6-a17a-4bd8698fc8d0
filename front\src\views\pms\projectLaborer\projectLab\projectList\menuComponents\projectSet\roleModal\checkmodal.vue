<template>
  <div class="checkDetails">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="440"
      class="checkDetailsDrawer pdmRightDrawer"
      @close="close"
    >
      <div class="role-content">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane
            key="1"
            tab="概述"
            class="tabPaneStyle"
          />
        </a-tabs>
        <basicTitle
          :title="'基本信息'"
          class="checkDetailsMessage"
        >
          <div class="messageContent">
            <template
              v-for="(item, index) in valueList"
              :key="index"
            >
              <div class="messageContent_row">
                <span class="messageContent_row_label">{{ item.label }}</span>
                <span class="messageContent_row_value">{{
                  ['modifyTime', 'createTime'].includes(item.fieldName)
                    ? dayjs(dataValue[0][item.fieldName]).format('YYYY-MM-DD HH:mm:ss')
                    : dataValue[0][item.fieldName]
                }}</span>
              </div>
            </template>
          </div>
        </basicTitle>
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch,
} from 'vue';
import { Tabs } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    basicTitle,
    aTabs: Tabs,
    aTabPane: Tabs.TabPane,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      visible: false,
      title: '查看详情',
      nextCheck: false,
      activeKey: '1',
      valueList: [
        {
          label: '编号',
          fieldName: 'number',
        },
        {
          label: '名称',
          fieldName: 'name',
        },
        {
          label: '状态',
          fieldName: 'takeEffectName',
        },
        {
          label: '描述',
          fieldName: 'remark',
        },
        {
          label: '修改人',
          fieldName: 'modifyName',
        },
        {
          label: '修改时间',
          fieldName: 'modifyTime',
        },
        {
          label: '创建人',
          fieldName: 'creatorName',
        },
        {
          label: '创建时间',
          fieldName: 'createTime',
        },
      ],
      dataValue: {},
    });
    const formRef = ref();

    watch(
      () => props.data,
      (newVal) => {
        state.visible = true;
        state.dataValue = { ...newVal };
      },
      {
        deep: true,
      },
    );

    return {
      ...toRefs(state),
      formRef,
      close() {
        state.visible = false;
        emit('close', false);
      },
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
.role-content{
  padding:16px 20px;
}
</style>
