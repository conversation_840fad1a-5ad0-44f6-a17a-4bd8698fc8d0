<template>
  <div class="addNodeModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      destroy-on-close
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <template />
        <a-form-item
          label="名称"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <a-form-item
          label="问题内容"
          name="content"
        >
          <a-textarea
            v-model:value="formState.content"
            show-count
            :maxlength="255"
            placeholder="请输入问题内容"
            :rows="4"
          />
        </a-form-item>

        <a-form-item
          label="问题类型"
          name="questionType"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.questionType"
            size="large"
            placeholder="请选择问题类型"
          >
            <a-select-option
              v-for="(item, index) in questionType"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="问题来源"
          name="questionSource"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.questionSource"
            size="large"
            placeholder="请选择问题来源"
          >
            <a-select-option
              v-for="(item, index) in questionSourceName"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="严重程度"
          name="seriousLevel"
        >
          <a-select
            v-model:value="formState.seriousLevel"
            size="large"
            placeholder="请选择严重程度"
          >
            <a-select-option
              v-for="(item, index) in seriousLevel"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="提出人"
          name="exhibitorId"
        >
          <a-select
            v-model:value="formState.exhibitorId"
            show-search
            placeholder="请输入提出人"
            :options="roleOption"
            size="large"
            :filter-option="filterHandle"
            @search="handleChange"
          />
        </a-form-item>

        <a-form-item
          label="提出时间"
          name="proposedTime"
        >
          <a-date-picker
            v-model:value="formState.proposedTime"
            size="large"
            placeholder="请选择提出时间"
          />
        </a-form-item>
        <a-form-item
          label="期望完成时间"
          name="predictEndTime"
        >
          <a-date-picker
            v-model:value="formState.predictEndTime"
            size="large"
            placeholder="请选择预计完成时间"
          />
        </a-form-item>
        <a-form-item
          label="接收人"
          name="recipient"
        >
          <a-select
            v-model:value="formState.recipient"
            show-search
            placeholder="请输入接收人"
            :options="recipient"
            size="large"
            :filter-option="filterHandle"
            @search="handleChange"
          />
        </a-form-item>
        <a-form-item
          label="负责人"
          name="principalId"
        >
          <a-select
            v-model:value="formState.principalId"
            show-search
            placeholder="请输入负责人"
            :options="principalId"
            size="large"
            :filter-option="filterHandle"
            @search="handleChange"
          />
        </a-form-item>
        <a-form-item
          label="优先级"
          name="priorityLevel"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.priorityLevel"
            size="large"
            placeholder="请选择优先级"
          >
            <a-select-option
              v-for="(item, index) in priorityLevel"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="进度"
          name="schedule"
          style="text-align: left"
        >
          <div class="components-input-demo-presuffix">
            <a-input
              v-model:value="formState.schedule"
              placeholder="请输入进度"
              size="large"
              suffix="%"
            />
          </div>
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import {
  editQuestionApi,
  addquestionApi,
  questionTypeApi,
  questionLevelApi,
  questionSourceApi,
} from '/@/views/pms/projectLaborer/api/questionManage';
import { priorityLevelApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';

import dayjs from 'dayjs';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: <any>{
      type: Array,
      default: () => [],
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      visible: false,
      formState: <any>{
        name: undefined,
        // 类型
        questionType: undefined,
        // 问题来源
        questionSource: undefined,
        // 提出人
        exhibitorId: undefined,
        // 负责人
        principalId: undefined,
        // 接收人
        recipient: undefined,
        // 提出时间
        proposedTime: null,
        // 预计完成时间
        predictEndTime: null,
        // 应对策略5
        seriousLevel: undefined,
        // 应对措施 255字节
        content: undefined,
        // 进度
        schedule: undefined,
        // 优先级
        priorityLevel: undefined,
      },
      selectValue: '',
      title: '',
      nextCheck: false,
      loading: false,
      oldformState: <any>{},
      questionType: <any>[],
      // 问题来源
      questionSourceName: <any>[],
      // 优先级
      priorityLevel: <any>[],
      // 提出人
      exhibitorId: <any>[],
      // 负责人
      principalId: <any>[],
      // 接收人
      recipient: <any>[],
      roleOption: <any>[],
      // 应对策略5
      seriousLevel: <any>[],
      showVisible: false,
      // 表单是否发生变化
      flag: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入项目名称',
          trigger: 'blur',
        },
        {
          min: 1,
          max: 12,
          message: '项目名称长度应在1~12位',
          trigger: 'blur',
        },
      ],
    };
    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;
        try {
          await getRole('', props.Projectid);
          state.questionType = await questionTypeApi();
          state.priorityLevel = await priorityLevelApi();
          state.questionSourceName = await questionSourceApi();
          state.seriousLevel = await questionLevelApi();
        } catch (err) {
          message.warning('内部错误');
        }
        if (value.formType == 'add') {
          state.title = '创建信息';
          state.formType = 'add';
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          state.oldformState = { ...props.listData[0] };

          for (let namex in state.formState) {
            if (props.listData[0][namex]) {
              state.formState[namex] = props.listData[0][namex];
            }
          }
          state.formState.id = props.listData[0].id;
        }
      },
    );
    /* 侦听表单是否变化 */
    //   watch(
    //     state.formState,
    //     () => {
    //       console.log('数据变化');
    //       state.flag = true;
    //     },
    //     { deep: true, immediate: true }
    //   );
    /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      formRef.value.resetFields();
      state.visible = false;
      state.flag = false;
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      const httpValue = { ...state.formState };
      if (state.formState.predictEndTime) {
        httpValue.predictEndTime = dayjs(
          dayjs(state.formState.predictEndTime).format('YYYY-MM-DD'),
        ).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      }
      if (state.formState.proposedTime) {
        httpValue.proposedTime = dayjs(
          dayjs(state.formState.proposedTime).format('YYYY-MM-DD'),
        ).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      }

      httpValue.projectId = props.Projectid;
      zhttp(httpValue);
    };
    const addSuccess = () => {
      message.success('保存成功');
      state.loading = false;
      if (state.nextCheck) {
        formRef.value.resetFields();
      } else {
        state.visible = false;
      }
      emit('success', false);
      formRef.value.resetFields();
    };
    const zhttp = (httpValue) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
            editQuestionApi(httpValue)
              .then(() => {
                addSuccess();
              })
              .catch(() => {
                message.warning('操作失败');
              });
          } else {
            addquestionApi(httpValue)
              .then(() => {
                addSuccess();
              })
              .catch(() => {
                message.warning('操作失败');
              });
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };
      /* 模糊搜索系列 */
    const handleChange = (value) => {
      try {
        getRole(value, props.Projectid);
      } catch (err) {
        message.warning('内部错误');
      }
    };
    const getRole = async (value, idkey) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        state.roleOption = state.recipient = state.principalId = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      });
    };
    const filterHandle = (newvalue, option) => option;
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      handleChange,
      filterHandle,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
</style>
