package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.UserSatisfactionEvaluationDTO;
import com.chinasie.orion.domain.vo.UserSatisfactionEvaluationVO;
import com.chinasie.orion.service.UserSatisfactionEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * UserSatisfactionEvaluation 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
@RestController
@RequestMapping("/userSatisfactionEvaluation")
@Api(tags = "人员满意度评价")
public class  UserSatisfactionEvaluationController  {

    @Autowired
    private UserSatisfactionEvaluationService userSatisfactionEvaluationService;



    /**
     * 编辑
     *
     * @param userSatisfactionEvaluationDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#userSatisfactionEvaluationDTO.name}}】", type = "人员满意度评价", subType = "编辑", bizNo = "{{#userSatisfactionEvaluationDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  UserSatisfactionEvaluationDTO userSatisfactionEvaluationDTO) throws Exception {
        Boolean rsp = userSatisfactionEvaluationService.edit(userSatisfactionEvaluationDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 树
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "树")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "人员满意度评价", subType = "树", bizNo = "")
    @RequestMapping(value = "/tree", method = RequestMethod.POST)
    public ResponseDTO<List<UserSatisfactionEvaluationVO>> tree(@RequestBody UserSatisfactionEvaluationDTO dto) throws Exception {
        List<UserSatisfactionEvaluationVO> rsp =  userSatisfactionEvaluationService.tree( dto);
        return new ResponseDTO<>(rsp);
    }


}
