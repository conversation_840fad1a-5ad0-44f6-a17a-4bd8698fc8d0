package com.chinasie.orion.management.constant;



public enum RequirementResouceEnum {

    PUBLIC_NETWORK_DEM("public_network_dem","外网公招"),
    INTERIOR_OT("interior_ot","内网其他"),
    EXTERNAL_OT("external_ot","外部其他"),
    ECP_OPEN_TE("ECP_open_te","ECP公开招标"),
    ECP_ENQ("ECP_enq","ECP询价");
    public String name;
    public String desc;

    RequirementResouceEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (RequirementResouceEnum lt : RequirementResouceEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

}