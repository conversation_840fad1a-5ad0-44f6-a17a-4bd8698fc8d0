<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_SYGL_button_01',powerData)"
        type="primary"
        icon="sie-icon-chongzhi"
        @click="goTB"
      >
        同步产品信息
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_SYGL_button_02',powerData)"
        icon="sie-icon-qidongliucheng"
        :disabled="tableInfo.disabled"
        @click="action('saleOver')"
      >
        销售是否结束
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_SYGL_button_03',powerData)"
        icon="sie-icon-hetongqianding"
        :disabled="tableInfo.disabled"
        @click="action('expectedOutcomes')"
      >
        现预期总产出变更
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_SYGL_button_04',powerData)"
        icon="sie-icon-daochu"
        @click="down"
      >
        导出
      </BasicButton>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import {
  h, reactive, ref, onMounted, inject,
} from 'vue';
import {
  OrionTable, BasicButton, useITable, isPower, downloadByData,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import AddItem from './src/AddItem.vue';
import { formatMoney, openContentDrawer, openContentModal } from '/@/views/pms/utils/utils';
import SimpleSet from './src/SimpleSet.vue';

const router = useRouter();
const route = useRoute();
const powerData = inject('powerData');
const downParams: any = ref({});
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: {},
  smallSearchField: ['productNumber'],
  showIndexColumn: true,
  api: (tableParams) => {
    tableParams.power = {
      pageCode: 'PMS0004',
      containerCode: 'IncomeManagement',
    };
    tableParams.query = {
      projectId: route.query.id,
    };
    downParams.value = tableParams;
    return new Api('/pms/projectIncome/page').fetch(tableParams, '', 'POST');
  },
  columns: [
    {
      title: '产品编码',
      align: 'left',
      dataIndex: 'productNumber',
    },
    {
      title: '需求评审产品编码',
      align: 'left',
      dataIndex: 'productName',
    },
    {
      title: '产品名称',
      align: 'left',
      dataIndex: 'productName',
    },
    {
      title: '产品型号',
      align: 'left',
      dataIndex: 'productModelNumber',
      width: 170,
    },
    {
      title: '军/民品分类',
      align: 'left',
      dataIndex: 'militaryCivilianName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondClassifyName',
      width: 150,
    },
    {
      title: '销售是否结束',
      align: 'left',
      dataIndex: 'saleOver',
      customRender: ({ text }) => (text ? '是' : '否'),
    },
    {
      title: '是否已签单',
      dataIndex: 'signBill',
      align: 'left',
      customRender: ({ text }) => (text ? '是' : '否'),
    },
    {
      title: '预期合同年份',
      dataIndex: 'expectedContractYear',
      align: 'left',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY') : ''),
    },
    {
      title: '原预期产品单价',
      dataIndex: 'expectedProductPrice',
      align: 'left',
    },
    {
      title: '原预期总产出',
      dataIndex: 'origExpectedOutcome',
      align: 'left',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '现预期总产出',
      dataIndex: 'expectedOutcomes',
      align: 'left',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '已签订合同金额',
      dataIndex: 'contractAmount',
      align: 'left',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '预期差异比',
      dataIndex: 'expectedDiffRate',
      align: 'left',
    },
    {
      title: '完成百分比',
      dataIndex: 'completeRate',
      align: 'left',
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      dataIndex: 'actions',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record) => record?.edit && isPower('PMS_SYGL_button_05', powerData),
      onClick: (record) => {
        edit('edit', record);
      },
    },
    {
      text: '查看',
      isShow: () => isPower('PMS_SYGL_button_06', powerData),
      onClick: (record) => {
        router.push({
          name: 'IncomeManagementDetails',
          params: {
            id: record.id,
          },
        });
      },
    },
  ],
});
const [tableRef, tableInfo]: any = useITable({});
const state = reactive({
  powerData: [],
});

function reload() {
  tableRef.value.reload({ page: 1 });
}

function goTB(keys) {
  Modal.confirm({
    title: '提示',
    content: '确认同步数据吗?',
    onOk: async () => await new Api(`/pms/projectIncome/sync?projectId=${route.query.id}`).fetch('', '', 'POST').then(() => {
      reload();
    }),
  });
}

function action(type) {
  openContentModal(
    {
      title: type === 'saleOver' ? '是否销售结束' : '现逾期总产出',
      width: 400,
      height: 300,
      content: (h) => h(SimpleSet, {
        type,
        rows: tableInfo.rows,
      }),
      onOk: async (data) => {
        data.ids = tableInfo.keys;
        let path = type === 'saleOver' ? '/pms/projectIncome/updateSaleOver' : '/pms/projectIncome/updateExpectedOutcomes';
        return await new Api(path).fetch(data, '', 'PUT').then(() => {
          reload();
        });
      },
    },
  );
}

function edit(type, record) {
  openContentDrawer(
    {
      title: '编辑',
      width: 400,
      content: (h) => h(AddItem, {
        type,
        detail: record,
      }),
      onOk: async (data) => {
        data.id = record.id;
        let path = '/pms/projectIncome/edit';
        return await new Api(path).fetch(data, '', 'PUT').then(() => {
          reload();
        });
      },
    },
  );
}

function down() {
  const { searchConditions, query } = downParams.value;
  downloadByData('/pms/projectIncome/export/excel', {
    searchConditions,
    query,
  }, '', 'POST', true, false, '导出处理完成，现在开始下载');
}

</script>
