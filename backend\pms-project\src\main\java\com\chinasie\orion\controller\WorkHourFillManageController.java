package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.WorkHourFillDTO;
import com.chinasie.orion.domain.dto.WorkHourFillManageDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.vo.WorkHourFillDayVO;
import com.chinasie.orion.domain.vo.WorkHourFillInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillManageVO;
import com.chinasie.orion.domain.vo.WorkHourFillVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.WorkHourFillManageService;
import com.chinasie.orion.service.WorkHourFillService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * WorkHourFill 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@RestController
@RequestMapping("/manage/workHourFill")
@Api(tags = "工时填报(项目经理)")
public class WorkHourFillManageController {

    @Autowired
    private WorkHourFillManageService workHourFillManageService;


    /**
     * 新增
     *
     * @param workHourFillManageDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "工时填报(项目经理)", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> create(@RequestBody @Validated List<WorkHourFillManageDTO> workHourFillManageDTOList) throws Exception {
        Boolean rsp = workHourFillManageService.create(workHourFillManageDTOList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提交
     *
     * @param workHourFillManageDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提交", type = "工时填报(项目经理)", subType = "提交", bizNo = "")
    public ResponseDTO<Boolean> submit(@RequestBody List<WorkHourFillManageDTO> workHourFillManageDTOList) throws Exception {
        Boolean rsp = workHourFillManageService.submit(workHourFillManageDTOList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提交（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交（批量）")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提交", type = "工时填报(项目经理)", subType = "提交（批量）", bizNo = "")
    public ResponseDTO<Boolean> batchSubmit(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = workHourFillManageService.batchSubmit(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 编辑
     *
     * @param workHourFillManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "工时填报(项目经理)", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody WorkHourFillManageDTO workHourFillManageDTO) throws Exception {
        Boolean rsp = workHourFillManageService.edit(workHourFillManageDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "工时填报(项目经理)", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择要删除的数据!");
        }
        Boolean rsp = workHourFillManageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "工时填报(项目经理)", subType = "分页", bizNo = "")
    public ResponseDTO<Page<WorkHourFillManageVO>> pages(@RequestBody Page<WorkHourFillPageDTO> pageRequest) throws Exception {
        Page<WorkHourFillManageVO> rsp = workHourFillManageService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
