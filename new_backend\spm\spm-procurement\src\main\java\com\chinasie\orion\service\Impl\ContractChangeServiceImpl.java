package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.ContractChangeDTO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.entity.ContractChange;
import com.chinasie.orion.domain.vo.ContractChangeVO;
import com.chinasie.orion.repository.ContractChangeMapper;
import com.chinasie.orion.service.ContractChangeService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractChange 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractChangeServiceImpl extends OrionBaseServiceImpl<ContractChangeMapper, ContractChange> implements ContractChangeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractChangeVO detail(String id, String pageCode) throws Exception {
        ContractChange contractChange = this.getById(id);
        ContractChangeVO result = BeanCopyUtils.convertTo(contractChange, ContractChangeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractChangeDTO
     */
    @Override
    public String create(ContractChangeDTO contractChangeDTO) throws Exception {
        ContractChange contractChange = BeanCopyUtils.convertTo(contractChangeDTO, ContractChange::new);
        this.save(contractChange);

        String rsp = contractChange.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractChangeDTO
     */
    @Override
    public Boolean edit(ContractChangeDTO contractChangeDTO) throws Exception {
        ContractChange contractChange = BeanCopyUtils.convertTo(contractChangeDTO, ContractChange::new);

        this.updateById(contractChange);

        String rsp = contractChange.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractChangeVO> pages(Page<ContractChangeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractChange> condition = new LambdaQueryWrapperX<>(ContractChange.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (pageRequest.getQuery() != null) {
            //变更申请日期段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ContractChange::getChangeRequestDate, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        condition.orderByDesc(ContractChange::getChangeRequestDate);

        Page<ContractChange> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractChange::new));

        PageResult<ContractChange> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractChangeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractChangeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractChangeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractChangeVO> getByCode(Page<ContractChangeDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractChange> condition = new LambdaQueryWrapperX<>(ContractChange.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractChange::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractChange::getCreateTime);
        Page<ContractChange> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractChange::new));

        PageResult<ContractChange> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractChangeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractChangeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractChangeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同变更信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractChangeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractChangeExcelListener excelReadListener = new ContractChangeExcelListener();
        EasyExcel.read(inputStream, ContractChangeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractChangeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同变更信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractChange> contractChangees = BeanCopyUtils.convertListTo(dtoS, ContractChange::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractChange-import::id", importId, contractChangees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractChange> contractChangees = (List<ContractChange>) orionJ2CacheService.get("ncf::ContractChange-import::id", importId);
        log.info("合同变更信息表导入的入库数据={}", JSONUtil.toJsonStr(contractChangees));

        this.saveBatch(contractChangees);
        orionJ2CacheService.delete("ncf::ContractChange-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractChange-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractChange> condition = new LambdaQueryWrapperX<>(ContractChange.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractChange::getCreateTime);
        List<ContractChange> contractChangees = this.list(condition);

        List<ContractChangeDTO> dtos = BeanCopyUtils.convertListTo(contractChangees, ContractChangeDTO::new);

        String fileName = "合同变更信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractChangeDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractChangeVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractChangeExcelListener extends AnalysisEventListener<ContractChangeDTO> {

        private final List<ContractChangeDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractChangeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractChangeDTO> getData() {
            return data;
        }
    }


}
