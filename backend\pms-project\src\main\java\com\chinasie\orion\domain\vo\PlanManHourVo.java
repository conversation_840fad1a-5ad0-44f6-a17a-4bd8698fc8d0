package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.PlanDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/28/17:01
 * @description:
 */
@Data
@ApiModel(value = "PlanManHourVo对象", description = "计划工时详情")
public class PlanManHourVo extends PlanDTO {
    @ApiModelProperty("工时列表")
    private List<ManHourVo> manHourVos;
}
