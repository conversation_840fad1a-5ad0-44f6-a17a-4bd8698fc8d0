/**
 * 根据节点ID查看单据
 * @param nodeId
 * @record record
 * @param router
 * @param updateAction 设置页面菜单id
 */
export function onViewDocuments(nodeId: string, record: any, { router, updateAction } : {router:any, updateAction: (actionId: string)=>void}) {
  let routeName: string;
  // 这里处理项目详情页下的切换
  if (nodeId === '3-1' || nodeId === '3-2' || nodeId === '3-2-1' || nodeId === '3-2-2') {
    updateAction('project_plan');
    return;
  }
  // 根据不同的 id 值跳转到不同的页面
  if (nodeId === '0-1') {
    routeName = 'MarketDemandManagementDetails';
  } else if (nodeId === '0-2') {
    routeName = 'PASClueChangeDetails';
  } else if (nodeId === '1-1') {
    routeName = 'MarketDemandManagementDetails';
  } else if (nodeId === '1-2') {
    routeName = 'QuotationManagementDetails';
  } else if (nodeId === '1-3') {
    routeName = 'QuotationManagementDetails';
  } else if (nodeId === '1-4') {
    routeName = 'ContractMangeDetail';
  } else if (nodeId === '1-5') {
    routeName = 'ZghProjectInitiationDetail';
  } else if (nodeId === '1-6') {
    routeName = 'ZghProjectInitiationDetail';
  } else if (nodeId === '2-1') {
    routeName = 'PurchaseProjectApplication';
  } else if (nodeId === '2-2') {
    routeName = 'ProjectOngoing';
  } else if (nodeId === '2-3') {
    routeName = 'PurchaseContract';
  } else if (nodeId === '2-4') {
    routeName = 'ContractManagement';
  } else if (nodeId === '2-5') {
    routeName = 'NoPurchaseContract';
  } else if (nodeId === '2-6') {
    routeName = 'ContractPaymentManage';
  } else if (nodeId === '3-3') {
    routeName = 'PurchaseContract';
  } else if (nodeId === '3-4') {
    routeName = 'DailyPlan';
  } else if (nodeId === '3-2-3') {
    routeName = 'ContractManagementC';
  } else if (nodeId === '4-1') {
    routeName = 'ContractManagementC';
  }

  if (routeName && nodeId === '1-4') {
    let queryParamId = record.id;
    let htNum = record.number;
    router.push({
      name: routeName,
      query: {
        id: queryParamId,
        htNum,
      },
    });
  } else if (routeName && (nodeId === '1-5' || nodeId === '1-6')) {
    router.push({
      name: routeName,
      query: {
        projectId: record.id,
        projectCode: record.projectNumber,
      },
      params: {
        id: record?.id,
      },
    });
  } else if (routeName && record?.id) {
    router.push({
      name: routeName,
      params: {
        id: record?.id,
      },
    });
  } else {
    router.push({
      name: routeName,
    });
  }
}