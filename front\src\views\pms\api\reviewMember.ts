import Api from '/@/api';

/**
 * 获取专家和评审成员
 */
export const reviewMemberList = (adminTableId) => new Api(`/pms/reviewMember/list/${adminTableId}`).fetch('', '', 'GET');
/**
 * 新增专家
 */
export const addExpert = (params) => new Api('/pms/reviewMember/add/expert').fetch(params, '', 'POST');
/**
 * 新增组员
 */
export const addCrew = (params) => new Api('/pms/reviewMember/add/crew').fetch(params, '', 'POST');
/**
 * 设置组长
 */
export const setAdmin = (id) => new Api(`/pms/reviewMember/setAdmin/${id}`).fetch('', '', 'GET');
/**
 * 删除
 */
export const reviewMemberDelete = (id) => new Api(`/pms/reviewMember/${id}`).fetch('', '', 'DELETE');
