<script setup lang="ts">
import {
  BasicButton,
  Layout,
  OrionTable,
  isPower,
  openDrawer,
  openModal,
} from 'lyra-component-vue3';

import {
  ref,
} from 'vue';
import dayjs from 'dayjs';
import DrawerAdd from './components/DrawerAdd.vue';
import SingleInventory from './components/SingleInventory.vue';
import StockOrder from './components/StockOrder.vue';
import { jumpName } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
import { add, edit, page } from '/@/views/pms/api/projectMaterialPlan';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});

const headAuthList = ref([]);
const powerCode = {
  pageCode: 'PMS0004',
  headCode: 'PMS_XMXQ_container_06_06_01',
  containerCode: 'PMS_XMXQ_container_06_06_02',
  headAdd: 'PMS_XMXQ_container_06_06_01_button_01', // 新增物资计划
  listEdit: 'PMS_XMXQ_container_06_06_02_button_01', // 编辑物资计划
  materialNum: 'PMS_XMXQ_container_06_06_02_button_02', // 查看-研发单台物料量
  bUtil: 'PMS_XMXQ_container_06_06_02_button_03', // 查看-备料单
};

const tableRef = ref(null);
const columns = [
  {
    title: '物料编码',
    dataIndex: 'number',
    fixed: 'left',
  },
  {
    title: '物料描述',
    dataIndex: 'remark',
  },
  {
    title: '基本单位',
    dataIndex: 'baseUnit',
  },
  {
    title: '计划数量',
    dataIndex: 'planNum',
  },
  {
    title: '计划使用时间',
    dataIndex: 'planUseTime',
    type: 'date',
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
  },
  {
    title: '研发单台物料量',
    dataIndex: 'materialNum',
    customRender({ text, record }) {
      if (isPower(powerCode.materialNum, record.rdAuthList)) {
        return jumpName(text, () => singleInventory(record));
      }
      return text;
    },
  },
  {
    title: '台数',
    dataIndex: 'units',
  },
  {
    title: '研发总物料量',
    dataIndex: 'materialTotalNum',
  },
  {
    title: '备料量',
    dataIndex: 'preparationNum',
  },
  {
    title: '备料单',
    dataIndex: 'bUtil',
    customRender({ record }) {
      if (isPower(powerCode.bUtil, record.rdAuthList)) {
        return jumpName('详情', () => stockOrder(record));
      }
      return '详情';
    },
  },
  {
    title: '历史采购周期',
    dataIndex: 'procurementCycle',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record) => isPower(powerCode.listEdit, record.rdAuthList),
    async onClick(record) {
      handleEdit(record.id);
    },
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['number'],
  columns,
  actions,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headCode,
      containerCode: powerCode.containerCode,
    };
    params.query = {
      projectId: props.id,
    };
    return page(params).then((res) => {
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
};
const singleInventory = (record) => {
  openModal({
    title: '研发物料父级查询',
    width: 1000,
    height: 500,
    footer: false,
    content(h) {
      return h(SingleInventory, {
        productId: props.id,
        number: record.number, // 物料编码
        remark: record.remark, // 物料描述
        baseUnit: record.baseUnit, // 基本单位
        materialNum: record.materialNum, // 研发单台物料量
      });
    },
  });
};
const stockOrder = (record) => {
  // openModal({
  //   title: '备料单查询',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(StockOrder, {
  //       number: record.number, // 物料编码
  //       remark: record.remark, // 物料描述
  //       baseUnit: record.baseUnit, // 基本单位
  //       preparationNum: record.preparationNum, // 备料量
  //     });
  //   },
  // });
};
// 新增
const handleAdd = () => {
  const refDrawer = ref();
  openDrawer({
    title: '新增物资计划',
    width: 800,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
      });
    },
    async onOk() {
      const values = await refDrawer.value.getValues();
      const data = {
        ...values,
        projectId: props.id,
      };
      await add(data);
      tableRef.value.reload();
    },
  });
};
// 编辑
const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑物资计划',
    width: 800,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const values = await refDrawer.value.getValues();
      const data = {
        ...values,
        id,
        projectId: props.id,
      };
      await edit(data);
      tableRef.value.reload();
    },
  });
};

</script>

<template>
  <Layout
    v-get-power="{powerData:headAuthList}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd, headAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新增物资计划
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
