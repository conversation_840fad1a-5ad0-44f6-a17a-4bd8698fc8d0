package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;

/**
 * ProjectCollectionToProject DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@ApiModel(value = "ProjectCollectionToProjectDTO对象", description = "项目集关联项目")
@Data
public class ProjectCollectionToProjectDTO  implements Serializable {
    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer sort;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private String modifyId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    private String fromClass;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;
}
