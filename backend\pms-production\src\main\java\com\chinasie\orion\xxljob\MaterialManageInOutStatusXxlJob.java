package com.chinasie.orion.xxljob;

import com.chinasie.orion.constant.MaterialTypeEnum;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.entity.MaterialOutManage;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.MaterialManageMapper;
import com.chinasie.orion.service.MaterialManageService;
import com.chinasie.orion.service.MaterialOutManageService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MaterialManageInOutStatusXxlJob {

    @Autowired
    private MaterialManageService materialManageService;

    @Autowired
    private MaterialOutManageService materialOutManageService;
    @Autowired
    private MaterialManageMapper materialManageMapper;


    @XxlJob("materialManageInOutStatus")
    public void materialManageInOutStatus() throws Exception {
        //查待(已离场)入场及入场数据
        LambdaQueryWrapperX<MaterialManage> wrapperX = new LambdaQueryWrapperX<>(MaterialManage.class);
        wrapperX.in(MaterialManage::getStatus, Arrays.asList(0, 1, 2));
        List<MaterialManage> list = materialManageService.list(wrapperX);

        // 将数据分组 氛围 待入场和已入场的数据列表
        Map<Integer, List<MaterialManage>> map = list.stream().collect(Collectors.groupingBy(LyraEntity::getStatus));

        List<MaterialManage> draftList = new ArrayList<>();
        List<MaterialManage> disableList = new ArrayList<>();
        map.forEach((k, v) -> {
            if (Objects.equals(k, StatusEnum.DRAFT.getIndex())) {
                // 如果是 待入场的数据 那么需要
                for (MaterialManage materialManage : v) {
                    Date actInDate = materialManage.getActInDate();
                    // 实际入场时间不为空 并且 当前时间是同一天
                    if (Objects.nonNull(actInDate) && TooUtils.isSameDay(actInDate)) {
                        // 那么需要更新状态为已入场
                        materialManage.setStatus(StatusEnum.ENABLE.getIndex());
                        draftList.add(materialManage);
                    }
                }
            } else if (Objects.equals(k, StatusEnum.ENABLE.getIndex())) {
                // 如果是已入场的数据 那么需要 判断是否已经到达 离场时间
                for (MaterialManage materialManage : v) {
                    Date outDate = materialManage.getOutDate();
                    // 出场时间不为空 并且 当前时间是同一天
                    if (Objects.nonNull(outDate) && TooUtils.isSameDay(outDate)) {
                        // 那么需要更新状态为已离场
                        materialManage.setStatus(StatusEnum.DISABLE.getIndex());
                        disableList.add(materialManage);
                    }
                }
            } else if (Objects.equals(k, StatusEnum.DISABLE.getIndex())) {
                // 如果是已离场数据 那么需要 判断是否已经到达 入场时间
                for (MaterialManage materialManage : v) {
                    if (Objects.equals(materialManage.getIsAgainIn(), Boolean.TRUE)) {
                        Date inDate = materialManage.getInDate();
                        // 入场时间不为空 并且 当前时间是同一天
                        if (Objects.nonNull(inDate) && TooUtils.isSameDay(inDate)) {
                            // 那么需要更新状态为待入场
                            materialManage.setStatus(StatusEnum.DRAFT.getIndex());
                            draftList.add(materialManage);
                        }
                    }
                }
            }
        });
        // 对于 需要入场的数据 需要入场处理
        if (!CollectionUtils.isEmpty(draftList)) {
            materialManageService.updateBatchById(draftList);
            // 添加台账
            List<MaterialOutManage> materialOutManages = BeanCopyUtils.convertListTo(draftList, MaterialOutManage::new);
            materialOutManages.forEach(materialOutManage -> {
                materialOutManage.setSourceId(materialOutManage.getId());
                materialOutManage.setType(MaterialTypeEnum.INPUT.getKey());
                materialOutManage.setCreateTime(null);
                materialOutManage.setModifyTime(null);
                materialOutManage.setId(materialOutManage.getId() + TooUtils.getCurrentDate(TooUtils.yyyyMMdd));
            });
            materialOutManageService.saveBatch(materialOutManages);
        }
        // 对于 需要离场数据 需要离场处理
        if (!CollectionUtils.isEmpty(disableList)) {
            materialManageService.updateBatchById(disableList);
            // 添加台账
            List<String> materialIdList = disableList.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList());
            // 获取 物资ID对应的最后一次的台账 然后执行离场操作 记录最后一次台账中的立场信息然后进行记录 离场台账
            Map<String, MaterialOutManage> idToEntity = materialOutManageService.getMateriaIdToLastOutMaterial(materialIdList);

            List<String> idList = new ArrayList<>();
            List<MaterialOutManage> materialOutManages = new ArrayList<>();
            for (Map.Entry<String, MaterialOutManage> stringMaterialOutManageEntry : idToEntity.entrySet()) {
                MaterialOutManage materialOutManage = stringMaterialOutManageEntry.getValue();
                materialOutManage.setSourceId(materialOutManage.getId());
                materialOutManage.setStatus(StatusEnum.DISABLE.getIndex());
                materialOutManage.setType(MaterialTypeEnum.OUT.getKey());
                materialOutManage.setCreateTime(null);
                materialOutManage.setModifyTime(null);
                materialOutManage.setId(materialOutManage.getSourceId() + TooUtils.getCurrentDate(TooUtils.yyyyMMdd));
                idList.add(materialOutManage.getId());
                materialOutManages.add(materialOutManage);
            }
            materialOutManageService.saveBatch(materialOutManages);
            materialManageMapper.updateStatusByIdList(idList, StatusEnum.DISABLE.getIndex());
        }
    }
}
