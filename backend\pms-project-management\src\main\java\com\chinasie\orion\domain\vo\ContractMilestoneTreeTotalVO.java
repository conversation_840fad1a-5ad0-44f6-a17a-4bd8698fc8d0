package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ContractMilestone VO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneVO对象", description = "合同里程碑")
@Data
public class ContractMilestoneTreeTotalVO implements Serializable{

    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmt;

    /**
     * 当前已验收金额
     */
    @ApiModelProperty(value = "当前已验收金额")
    private BigDecimal actualMilestoneTotalAmt;

    /**
     * 合同总价金额
     */
    @ApiModelProperty(value = "合同总价金额")
    private BigDecimal contractTotalPriceAmt;

    /**
     * 里程碑树列表
     */
    @ApiModelProperty(value = "里程碑树列表")
    List<ContractMilestoneTreeVO> contractMilestoneTree;

}
