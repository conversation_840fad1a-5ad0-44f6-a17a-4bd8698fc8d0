<template>
  <div class="process flex flex-ac p-b-tb p-b-lr">
    <span class="label">审批编号：</span>
    <span class="value">{{ $props.detail?.contractPayNodeConfirmVO?.['number'] ||'--' }}</span>
  </div>
  <div class="process-placeholder" />

  <DetailsLayout
    title="节点审核信息"
    :column="2"
    :data-source="$props.detail?.contractPayNodeConfirmVO"
    :list="state.processInfo"
  >
    <template #table>
      <BasicButton
        :disabled="state.processSelect.length===0"
        @click="handleBatchDownload('process')"
      >
        批量下载
      </BasicButton>
      <div
        class="m-b-t"
        style="height: 180px;overflow: hidden"
      >
        <OrionTable
          ref="processTableRef"
          :options="processTableOptions"
          @selection-change="selectionChange($event,'process')"
        />
      </div>
    </template>
  </DetailsLayout>

  <DetailsLayout
    title="节点确认信息"
    :column="2"
    :data-source="$props.detail?.contractPayNodeConfirmVO"
    :list="state.confirmInfo"
  >
    <template #table>
      <div style="height: 180px;overflow: hidden">
        <OrionTable
          ref="confirmTableRef"
          :options="confirmTableOptions"
        />
      </div>
    </template>
    <template #serviceComplete="{text}">
      {{ text==='0'?'否':text==='1'?'是':text==='2'?'不适用':'--' }}
    </template>
  </DetailsLayout>

  <DetailsLayout
    title="节点确认资料"
  >
    <template #table>
      <BasicButton
        :disabled="state.infoSelect.length===0"
        @click="handleBatchDownload('info')"
      >
        批量下载
      </BasicButton>
      <div
        class="m-b-t"
        style="height: 180px;overflow: hidden"
      >
        <OrionTable
          ref="infoTableRef"
          :options="infoTableOptions"
          @selection-change="selectionChange($event,'info')"
        />
      </div>
    </template>
  </DetailsLayout>
</template>

<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  computed, h, onMounted, reactive, ref, Ref,
} from 'vue';
import {
  BasicButton, downLoadById, getDict, OrionTable,
} from 'lyra-component-vue3';

const props = defineProps<{
    detail:any
}>();

const state = reactive({
  processInfo: [],
  processSelect: [],
  confirmInfo: [],
  infoSelect: [],
  payNodeTypeOptions: [],
});

const processTableRef:Ref = ref();
const processTableOptions = {
  rowSelection: {},
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: props.detail?.auditDocumentVOList ?? [],
  columns: [
    {
      title: '名称',
      dataIndex: 'fullName',
      customRender({
        text, record,
      }) {
        return h('span', {
          title: text,
        }, [
          h('div', {
            class: 'action-btn flex-te',
            onClick(e:Event) {
              e.stopPropagation();
              window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
            },
          }, text),
        ]);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载',
      onClick({ id }) {
        downLoadById(id);
      },
    },
  ],
};

const confirmTableRef:Ref = ref();
const confirmTableOptions = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: computed(() => props.detail?.contractPayNodeVOList?.map((item) => ({
    ...item,
  })) ?? []),
  columns: [
    {
      title: '支付节点',
      dataIndex: 'payType',
      customRender({ text, record }) {
        return h('span', `${state.payNodeTypeOptions.find((result) => result.value === record.payType)?.description}`);
      },
    },
    {
      title: '支付说明',
      dataIndex: 'payDesc',
    },
  ],
};

const infoTableRef:Ref = ref();
const infoTableOptions = {
  rowSelection: {},
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: props.detail?.documentVOList ?? [],
  columns: [
    {
      title: '名称',
      dataIndex: 'name',
      customRender({
        text, record,
      }) {
        return h('span', {
          title: text + record.filePostfix,
        }, [
          h('div', {
            class: 'action-btn flex-te',
            onClick(e:Event) {
              e.stopPropagation();
              window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
            },
          }, text),
        ]);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载',
      onClick({ id }) {
        downLoadById(id);
      },
    },
  ],
};

onMounted(() => {
  initData();
  getPayNodeOptions();
});

async function getPayNodeOptions() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
}

function initData() {
  state.processInfo = [
    {
      label: '审核状态',
      field: ['dataStatus', 'name'],
    },
    {
      label: '审核时间',
      field: 'auditDate',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '审核人',
      field: 'auditUserName',
    },
    {
      label: '审核人工号',
      field: 'auditUserCode',
    },
    {
      label: '审核意见',
      field: 'auditDesc',
      wrap: true,
    },
  ];

  state.confirmInfo = [
    {
      label: '提交人',
      field: 'submitUserIdName',
    },
    {
      label: '提交人工号',
      field: 'submitUserIdCode',
    },
    {
      label: '服务确认是否已完成',
      field: 'serviceComplete',
      slot: true,
      slotName: 'serviceComplete',
    },
    {
      label: '提交时间',
      field: 'submitDate',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '节点确认说明',
      field: 'confirmDesc',
      gridColumn: '1/3',
      wrap: true,
    },
  ];
}
// 批量下载
function handleBatchDownload(key) {
  state[`${key}Select`]?.forEach((item) => {
    downLoadById(item?.id);
  });
}

// 表格多选
function selectionChange({ rows }, key) {
  state[`${key}Select`] = rows;
}

</script>

<style scoped lang="less">
.process{
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  right: 0;
  background-color: ~`getPrefixVar('content-background')`;
}

.process-placeholder{
  width: 100%;
  height: 54px;
}
</style>
