<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2010-2015 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    http://glassfish.java.net/public/CDDL+GPL_1_1.html
    or packager/legal/LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at packager/legal/LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>net.java</groupId>
        <artifactId>jvnet-parent</artifactId>
        <version>4</version>
    </parent>

    <groupId>com.sun.jersey</groupId>
    <artifactId>jersey-project</artifactId>
    <version>1.19</version>
    <packaging>pom</packaging>
    <name>jersey-project</name>

    <description>
        Jersey is the open source (under dual CDDL+GPL license) JAX-RS (JSR 311)
        production quality Reference Implementation for building
        RESTful Web services.
    </description>
    <url>https://jersey.java.net/</url>
    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <issueManagement>
        <system>JIRA</system>
        <url>http://java.net/jira/browse/JERSEY/</url>
    </issueManagement>
    <ciManagement>
        <system>Hudson</system>
        <url>http://hudson.glassfish.org/job/Jersey-trunk-multiplatform/</url>
    </ciManagement>
    <scm>
        <connection>scm:svn:https://svn.java.net/svn/jersey~svn/trunk/jersey/</connection>
        <developerConnection>scm:svn:https://svn.java.net/svn/jersey~svn/trunk/jersey/</developerConnection>
        <tag>HEAD</tag>
        <url>http://java.net/projects/jersey/sources/svn/show/trunk/jersey</url>
    </scm>

    <mailingLists>
        <mailingList>
            <name>Jersey Users List</name>
            <archive>http://java.net/projects/jersey/lists/users/archive</archive>
            <post><EMAIL></post>
        </mailingList>
        <mailingList>
            <name>Jersey Dev List</name>
            <archive>http://java.net/projects/jersey/lists/dev/archive</archive>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>

    <licenses>
        <license>
            <name>CDDL 1.1</name>
            <url>http://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>http://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <profiles>
        <profile>
            <id>release</id>
            <modules>
                <module>archetypes</module>
                <module>archive</module>
                <module>jersey-bundle</module>
                <module>glassfish</module>
                <module>ri</module>
            </modules>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifact</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>experimental</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <modules>
                <module>experimental</module>
                <module>archetypes</module>
                <module>archive</module>
                <module>jersey-bundle</module>
                <module>glassfish</module>
                <module>ri</module>
            </modules>
        </profile>
        <profile>
            <id>osgi-tests</id>
            <modules>
                <module>osgi</module>
                <module>archetypes</module>
                <module>archive</module>
                <module>jersey-bundle</module>
                <module>glassfish</module>
                <module>ri</module>
            </modules>
        </profile>
        <profile>
            <id>test</id>
            <modules>
                <module>osgi</module>
                <module>tests</module>
                <module>archetypes</module>
                <module>archive</module>
                <module>jersey-bundle</module>
                <module>glassfish</module>
                <module>ri</module>
            </modules>
        </profile>
        <profile>
            <id>cobertura</id>
            <activation>
                <property>
                    <name>cobertura</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>net.sourceforge.cobertura</groupId>
                    <artifactId>cobertura</artifactId>
                    <optional>true</optional>
                    <version>1.9.4.1</version>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>cobertura-instrument</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>instrument</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-jar-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>cobertura-jar</id>
                                <phase>post-integration-test</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                                <configuration>
                                    <classifier>cobertura</classifier>
                                    <classesDirectory>${basedir}/target/generated-classes/cobertura</classesDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-install-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>cobertura-install</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>install</goal>
                                </goals>
                                <configuration>
                                    <classifier>cobertura</classifier>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- To see ignored test for MOXy JAXB provider look for this profile in submodules -->
            <id>moxy</id>
            <activation>
                <property>
                    <name>moxy</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.eclipse.persistence</groupId>
                    <artifactId>org.eclipse.persistence.moxy</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <forkCount>1</forkCount>
                            <reuseForks>false</reuseForks>
                            <systemPropertyVariables>
                                <javax.xml.bind.JAXBContext>org.eclipse.persistence.jaxb.JAXBContextFactory</javax.xml.bind.JAXBContext>
                            </systemPropertyVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>xdk</id>
            <activation>
                <property>
                    <name>xdk</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <additionalClasspathElements>
                                <additionalClasspathElement>${xdk.absolute.path}</additionalClasspathElement>
                            </additionalClasspathElements>
                            <classpathDependencyExcludes>
                                <classpathDependencyExcludes>xerces:xercesImpl</classpathDependencyExcludes>
                            </classpathDependencyExcludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <modules>
        <module>contribs</module>
        <module>jersey-atom</module>
        <module>jersey-client</module>
        <module>jersey-core</module>
        <module>jersey-documentation</module>
        <module>jersey-fastinfoset</module>
        <module>jersey-grizzly</module>
        <module>jersey-grizzly2</module>
        <module>jersey-grizzly2-servlet</module>
        <module>jersey-json</module>
        <module>jersey-server-linking</module>
        <module>jersey-server</module>
        <module>jersey-servlet</module>
        <module>jersey-test-framework</module>
        <module>jersey-tests</module>
        <module>samples</module>
    </modules>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.0-beta-4</version>
                    <configuration>
                        <format>{0,date,MM/dd/yyyy hh:mm aa}</format>
                        <items>
                            <item>timestamp</item>
                        </items>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>validate</phase>
                            <goals>
                                <goal>create</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.2</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            </manifest>
                            <manifestEntries>
                                <Build-Id>${project.version} ${buildNumber}</Build-Id>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.3</version>
                    <inherited>true</inherited>
                    <configuration>
                        <source>1.6</source>
                        <target>1.6</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.1.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.5</version>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>2.3.6</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>2.3.2</version>
                    <configuration>
                        <xmlOutput>true</xmlOutput>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.4.3</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.7</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.28</version>
                    <configuration>
                        <excludeFile>etc/config/copyright-exclude</excludeFile>
                        <!--svn|mercurial|git - defaults to svn-->
                        <scm>svn</scm>
                        <!-- turn on/off debugging -->
                        <debug>false</debug>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                        <templateFile>etc/config/copyright.txt</templateFile>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <_versionpolicy>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy>
                        <_nodefaultversion>false</_nodefaultversion>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>osgi-bundle</id>
                        <phase>package</phase>
                        <goals>
                            <goal>bundle</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <version>1.28</version>
                <configuration>
                    <excludeFile>etc/config/copyright-exclude</excludeFile>
                    <!--svn|mercurial|git - defaults to svn-->
                    <scm>git</scm>
                    <!-- turn on/off debugging -->
                    <debug>false</debug>
                    <!-- skip files not under SCM-->
                    <scmOnly>true</scmOnly>
                    <!-- turn off warnings -->
                    <warn>true</warn>
                    <!-- for use with repair -->
                    <update>false</update>
                    <!-- check that year is correct -->
                    <ignoreYear>false</ignoreYear>
                    <templateFile>etc/config/copyright.txt</templateFile>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>repository.jboss.org</id>
            <name>jboss Maven Repository</name>
            <url>http://repository.jboss.org/nexus/content/groups/public/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>eclipselink.repository</id>
            <name>Eclipse Maven Repository</name>
            <url>http://www.eclipse.org/downloads/download.php?r=1&amp;nf=1&amp;file=/rt/eclipselink/maven.repo</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>jsr311-api</artifactId>
                <version>${jaxrs.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>2.2.3-1</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.8.2</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-jaxrs</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-xc</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.persistence</groupId>
                <artifactId>org.eclipse.persistence.moxy</artifactId>
                <version>${moxy.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <xdk.absolute.path></xdk.absolute.path>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <gf.embedded.version>3.1.1</gf.embedded.version>
        <grizzly2.version>2.2.16</grizzly2.version>
        <hamcrest.version>1.3</hamcrest.version>
        <jackson.version>1.9.2</jackson.version>
        <jaxrs.version>1.1.1</jaxrs.version>
        <moxy.version>2.3.2</moxy.version>
        <servlet3.version>3.0.1</servlet3.version>
    </properties>

</project>
