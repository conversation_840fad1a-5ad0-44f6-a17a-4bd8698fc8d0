package com.chinasie.orion.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.AdvancePaymentInvoicedDTO;
import com.chinasie.orion.domain.dto.InvoiceInformationDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInvoiced;
import com.chinasie.orion.domain.entity.InvoiceInformation;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.domain.vo.InvoiceInformationVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.InvoiceInformationMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.InvoiceInformationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * InvoiceInformation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@Service
@Slf4j
public class InvoiceInformationServiceImpl extends  OrionBaseServiceImpl<InvoiceInformationMapper, InvoiceInformation>   implements InvoiceInformationService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private InvoiceInformationMapper invoiceInformationMapper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public InvoiceInformationVO detail(String id, String pageCode) throws Exception {
        InvoiceInformation invoiceInformation =this.getById(id);
        InvoiceInformationVO result = BeanCopyUtils.convertTo(invoiceInformation,InvoiceInformationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param invoiceInformationDTO
     */
    @Override
    public  String create(InvoiceInformationDTO invoiceInformationDTO) throws Exception {
        InvoiceInformation invoiceInformation =BeanCopyUtils.convertTo(invoiceInformationDTO,InvoiceInformation::new);
        this.save(invoiceInformation);

        String rsp=invoiceInformation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param invoiceInformationDTO
     */
    @Override
    public Boolean edit(InvoiceInformationDTO invoiceInformationDTO) throws Exception {
        InvoiceInformation invoiceInformation =BeanCopyUtils.convertTo(invoiceInformationDTO,InvoiceInformation::new);

        this.updateById(invoiceInformation);

        String rsp=invoiceInformation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<InvoiceInformationVO> pages( Page<InvoiceInformationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<InvoiceInformation> condition = new LambdaQueryWrapperX<>( InvoiceInformation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        InvoiceInformationDTO query = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(query)){
            condition.eqIfPresent(InvoiceInformation::getContractId,query.getContractId());
        }
        condition.orderByDesc(InvoiceInformation::getCreateTime);


        Page<InvoiceInformation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), InvoiceInformation::new));

        PageResult<InvoiceInformation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<InvoiceInformationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<InvoiceInformationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), InvoiceInformationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "发票信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", InvoiceInformationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            InvoiceInformationExcelListener excelReadListener = new InvoiceInformationExcelListener();
        EasyExcel.read(inputStream,InvoiceInformationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<InvoiceInformationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("发票信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<InvoiceInformation> invoiceInformationes =BeanCopyUtils.convertListTo(dtoS,InvoiceInformation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::InvoiceInformation-import::id", importId, invoiceInformationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }

    @Override
    public InvoiceInformationVO getTotal(String contractId) {
        if(StrUtil.isBlank(contractId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"合同id为空");
        }
        return invoiceInformationMapper.getTotal(contractId);
    }



    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<InvoiceInformation> invoiceInformationes = (List<InvoiceInformation>) orionJ2CacheService.get("pmsx::InvoiceInformation-import::id", importId);
        log.info("发票信息导入的入库数据={}", JSONUtil.toJsonStr(invoiceInformationes));

        this.saveBatch(invoiceInformationes);
        orionJ2CacheService.delete("pmsx::InvoiceInformation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::InvoiceInformation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<InvoiceInformation> condition = new LambdaQueryWrapperX<>( InvoiceInformation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(InvoiceInformation::getCreateTime);
        List<InvoiceInformation> invoiceInformationes =   this.list(condition);

        List<InvoiceInformationDTO> dtos = BeanCopyUtils.convertListTo(invoiceInformationes, InvoiceInformationDTO::new);

        String fileName = "发票信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", InvoiceInformationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<InvoiceInformationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class InvoiceInformationExcelListener extends AnalysisEventListener<InvoiceInformationDTO> {

        private final List<InvoiceInformationDTO> data = new ArrayList<>();

        @Override
        public void invoke(InvoiceInformationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<InvoiceInformationDTO> getData() {
            return data;
        }
    }


}
