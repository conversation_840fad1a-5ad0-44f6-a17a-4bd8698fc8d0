package com.chinasie.orion.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum CenterPlanEnum {

    /**
     * 状态
     */
    UNSUBMIT(121, "未提交"),
    /**
     * 调整中未提交
     */
    EDIT(110, "未提交"),
    UNREVIEWED(120, "待审核"),
    REJECTED(140, "已驳回"),
    PASSED(160, "已通过"),
    EDITABLE(1, "可调整");


    private Integer key;

    private String desc;

    CenterPlanEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public Integer getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<Integer, String> getMap(){
        HashMap<Integer, String> res = new HashMap<>();
        res.put(UNSUBMIT.key, UNSUBMIT.desc);
        res.put(UNREVIEWED.key, UNREVIEWED.desc);
        res.put(REJECTED.key, REJECTED.desc);
        res.put(PASSED.key, PASSED.desc);
        res.put(EDITABLE.key, EDITABLE.desc);
        res.put(EDIT.key, EDIT.desc);
        return res;
    }

    /**
     * 获取未审核通过的状态
     * @return 结果
     */
    public static List<Integer> getStatusList(){
        List<Integer> status = new ArrayList<>();
        status.add(CenterPlanEnum.EDIT.getKey());
        status.add(CenterPlanEnum.UNREVIEWED.getKey());
        status.add(CenterPlanEnum.REJECTED.getKey());
        status.add(CenterPlanEnum.UNSUBMIT.getKey());
        return status;
    }
}
