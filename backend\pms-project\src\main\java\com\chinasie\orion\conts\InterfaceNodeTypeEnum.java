package com.chinasie.orion.conts;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/19:58
 * @description:
 */
public enum InterfaceNodeTypeEnum {
    NODE_INTERFACE_MANAGEMENT("Node_interface_finish", "接口审批完成"),
    NODE_INTERFACE_IDEA("Node_ideaform_finish", "意见单审批完成"),
            ;
    private String code;

    private String description;

    InterfaceNodeTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
