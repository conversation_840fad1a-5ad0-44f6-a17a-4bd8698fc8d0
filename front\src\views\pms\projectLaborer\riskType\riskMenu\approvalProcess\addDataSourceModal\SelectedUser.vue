<template>
  <div class="selected-wrap flex flex-ver">
    <div class="title flex">
      <h3 class="flex-f1">
        已选择<span>({{ selectedUser.length }})</span>
      </h3>
      <div
        v-if="selectedUser.length"
        class="fz12 action-btn"
        @click="deleteAll"
      >
        删除全部
      </div>
    </div>
    <div class="flex-f1 selected-main">
      <div
        v-for="(item, index) in selectedUser"
        :key="index"
        class="user-list flex-te"
      >
        <div class="user-name">
          {{ item.name }}
        </div>
        <div
          class="delete plr10"
          @click="deleteUser(item)"
        >
          移除
        </div>
      </div>
    </div>
    <a-button
      type="primary"
      class="m10"
      :loading="loading"
      @click="addOk"
    >
      确认添加
    </a-button>
  </div>
</template>

<script lang="ts">
import { reactive, defineComponent, toRefs } from 'vue';
import { Button } from 'ant-design-vue';

export default defineComponent({
  name: 'SelectedUser',
  components: { AButton: Button },
  props: {
    selectedUser: {
      type: Array,
      default: () => [],
    },
  },
  emits: [
    'deleteUser',
    'deleteAll',
    'addOk',
  ],
  setup(props, { emit }) {
    const state = reactive({
      loading: false,
    });
    function addOk() {
      state.loading = true;
      setTimeout(() => {
        emit('addOk');
      }, 5000);
      // emit('addOk')
    }
    return {
      ...toRefs(state),
      deleteUser(userItem) {
        emit('deleteUser', userItem);
      },
      deleteAll() {
        emit('deleteAll');
      },
      addOk,
    };
  },
});
</script>

<style scoped lang="less">
  .selected-wrap {
    //background: #f6f6f6;
    width: 100%;
    height: 100%;

    > .title {
      //background: #eef2f3;
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      border-bottom: 1px solid ~`getPrefixVar('border-color-base')` ;
      color: #535353;

      > h3 {
        font-weight: bold;
      }
    }

    > .selected-main {
      overflow-y: auto;
    }
  }

  .user-list {
    height: 36px;
    line-height: 36px;
    position: relative;
    padding: 0 10px;
    width: 100%;

    &:hover {
      background: #fff;

      .delete {
        opacity: 1;
        pointer-events: inherit;
      }
    }

    .delete {
      position: absolute;
      right: 5px;
      top: 0;
      font-size: 12px;
      color: #797979;
      opacity: 0;
      pointer-events: none;
      cursor: pointer;
      transition: 0.3s;
      //height: 36px;
      &:hover {
        //background-color: #eef1fc;
        //border-radius: 8px;
        color: ~`getPrefixVar('primary-color')`;
      }
    }
  }
</style>
