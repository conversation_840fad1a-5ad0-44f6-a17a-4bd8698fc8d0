<script setup lang="ts">
import {
  OrionTable, BasicCard,
} from 'lyra-component-vue3';
import {
  inject, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/src/api';
import dayjs from 'dayjs';
import { BasicInjectionsKey } from '../../../tokens/basicKeys';

const tableRef: Ref = ref();
const route = useRoute();
const basicInfo = inject(BasicInjectionsKey);
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  columns: [
    {
      title: '采购申请号',
      dataIndex: 'code',
    },
    {
      title: '采购立项号',
      dataIndex: 'projectCode',
    },
    {
      title: '采购立项金额',
      dataIndex: 'money',
    },
    {
      title: '采购立项审批完成时间',
      dataIndex: 'projectEndTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/spm/ncfFormpurchaseRequest/base/getByCodePage').fetch({
    ...params,
    query: {
      contractNumber: basicInfo?.data?.contractNumber,
    },
  }, '', 'POST'),
};
</script>

<template>
  <BasicCard
    :is-border="false"
    title="立项基本信息"
  >
    <div style="height: 360px;overflow: hidden;">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
        false
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
