<script setup lang="ts">
import { BasicButton, isPower, OrionTable } from 'lyra-component-vue3';
import {
  computed, CSSProperties, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { openMaterialDrawer } from '/@/views/pms/overhaulManagement/utils';
import { Modal } from 'ant-design-vue';
import { message } from 'ant-design-vue/lib/components';
import dayjs from 'dayjs';

const detailsData: Record<string, any> = inject('detailsData');
const updateLife: Function = inject('updateLife');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  showToolButton: false,
  isSpacing: false,
  smallSearchField: [
    'assetName',
    'number',
    'productCode',
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-material').fetch({
    ...params,
    query: {
      jobId: detailsData?.id,
      repairRound: detailsData?.repairRound,
    },
    power: {
      pageCode: 'PMSOverhaulOperationDetails',
      containerCode: 'PMS_DXZYXQNEW_container_01_02',
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '资产类型',
      dataIndex: 'assetTypeName',
      width: 100,
    },
    {
      title: '资产代码',
      dataIndex: 'assetCode',
    },
    {
      title: '资产编码/条码',
      dataIndex: 'number',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
    },
    {
      title: '数量',
      dataIndex: 'demandNum',
      width: 100,
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      dataIndex: 'specificationModel',
    },
    {
      title: '计划任务起止日期',
      dataIndex: 'startAndEndDateList',
      width: 240,
      edit: true,
      editComponent: 'RangePicker',
      editComponentProps: {
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
        disabledDate: (current) => current && (current > dayjs(detailsData.endTime) || current < dayjs(detailsData.beginTime)),
      },
      editCellSaveApi(value, oldValue, { record }) {
        return new Promise((resolve, reject) => {
          new Api('/pms/job-material/edit/date').fetch({
            id: record?.id,
            startAndEndDateList: value,
          }, '', 'PUT').then(() => {
            resolve(true);
            updateTable();
          }).catch(() => {
            reject();
          });
        });
      },
      editValueFormat(text, record) {
        return [record?.planBeginDate, record?.planEndDate].filter((item) => item).map((item) => dayjs(item).format('YYYY-MM-DD')).join(' 至 ');
      },
      editComponentValueFormat({ record }) {
        return [record?.planBeginDate, record?.planEndDate].filter((item) => item);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      isShow: (record) => isPower('PMS_DXZYXQNEW_container_01_02_button_03', record?.rdAuthList),
      modalTitle: '移除提示！',
      modalContent: '确认移除当前数据？',
      modal: (record) => deleteApi([record.id]),
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

const toolButtons = computed(() => [
  {
    text: '选择物资',
    event: 'add',
    icon: 'sie-icon-tianjiaxinzeng',
    type: 'primary',
    powerCode: 'PMS_DXZYXQNEW_container_01_02_button_01',
  },
  {
    text: '移除',
    event: 'remove',
    icon: 'sie-icon-shanchu',
    disabled: selectedKeys.value.length === 0,
    powerCode: 'PMS_DXZYXQNEW_container_01_02_button_02',
  },
]);

function handleTool(event: string) {
  switch (event) {
    case 'add':
      openMaterialDrawer({
        operationType: 'pick',
        jobId: detailsData.id,
      }, () => {
        updateTable();
        updateLife();
      });
      break;
    case 'remove':
      if (selectedRows.value.some((item) => item.materialManageVO?.status === 1)) {
        return message.info('选择数据中存在已入场状态，请重新选择！');
      }
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除这条数据吗？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-material/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      style="position: relative"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-is-power="[item.powerCode]"
          v-bind="item"
          @click="handleTool(item.event)"
        >
          {{ item.text }}
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
