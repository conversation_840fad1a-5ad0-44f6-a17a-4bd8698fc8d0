package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "MajorRepairPlanMemberLevelDTO对象", description = "大修分层成员")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanMemberLevelDTO {
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "电话")
    private String phone;
}
