<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_title">
          <div class="btnItem" />
        </div>
        <div class="productLibraryIndex_table">
          <BasicTable
            ref="tableRef"
            :row-selection="{ type: 'checkbox' }"
            :columns="columns"
            :can-resize="true"
            :show-index-column="false"
            :pagination="false"
            :api="api"
            row-key="id"
            :beforeFetch="beforeFetch"
          >
            <template #createTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
            <template #schedule="{ text }">
              <Progress
                :percent="text"
                size="small"
                style="padding-right: 40px"
              />
            </template>

            <template #statusName="{ record }">
              <span
                :style="{
                  color: record.statusName == '未完成' ? 'red' : ''
                }"
              >
                {{ record.statusName }}
              </span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <checkDetails :data="nodeData" />
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <SearchModal
        @register="searchRegister"
        @searchEmit="searchEmit"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, toRefs, unref, inject, onMounted, computed, h,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, isPower, useDrawer,
} from 'lyra-component-vue3';
import { Menu, message, Progress } from 'ant-design-vue';
import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import dayjs from 'dayjs';
import checkDetails from './modal/checkmodal.vue';
import { contactRiskTableApi } from '/@/views/pms/projectLaborer/api/questionManage';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';
import SearchModal from './SearchModal.vue';
const tableRef = ref(null);
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    BasicTable,
    InfoCircleOutlined,
    messageModal,
    checkDetails,
    newButtonModal,
    Progress,
    SearchModal,
  },
  setup() {
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      editdataSource: {},
      addNodeModalData: {},
      showVisible: false,
      message: '',
      nodeData: <any>[],
      searchData: {},
      params: {},
      queryCondition: [],

      searchStatus: '',
      powerData: [],
      hah: {},
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        check: { show: computed(() => isPower('WT_container_button_08', state.powerData)) },
        open: { show: computed(() => isPower('WT_container_button_09', state.powerData)) },
        search: { show: computed(() => isPower('WT_container_button_26', state.powerData)) },
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'name',
          align: 'left',
          width: '220px',
          // sorter: true,
          ellipsis: true,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => isPower('WT_container_button_08', state.powerData)) ? 'action-btn' : '',
                title: text,
                onClick(e) {
                  if (isPower('WT_container_button_08', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '风险类型',
          dataIndex: 'riskTypeName',
          key: 'riskType',
          width: '80px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'riskTypeName' },
          // sorter: true
          ellipsis: true,
        },
        {
          title: '发生概率',
          dataIndex: 'riskProbabilityName',
          key: 'riskProbabilityName',
          width: '100px',
          align: 'left',
          slots: { customRender: 'riskProbabilityName' },

          // sorter: true,
          ellipsis: true,
        },
        {
          title: '影响程度',
          dataIndex: 'riskInfluenceName',
          key: 'riskInfluenceName',

          width: '120px',
          align: 'left',
          slots: { customRender: 'riskInfluenceName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '预估发生时间',
          dataIndex: 'predictStartTimeName',
          key: 'predictStartTimeName',
          width: '120px',
          align: 'left',
          slots: { customRender: 'predictStartTimeName' },
          // sorter: true,
          ellipsis: true,
        },
        {
          title: '应对策略',
          dataIndex: 'copingStrategyName',
          key: 'copingStrategyName',

          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'copingStrategyName' },
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'statusName',

          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'statusName' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalName',

          width: '120px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          key: 'createTime',

          width: '170px',
          align: 'left',
          // sorter: true,
          ellipsis: true,
          slots: { customRender: 'createTime' },
        },
      ],
    });
    const clickType = (type) => {
      switch (type) {
        case 'check':
          checkData();
          break;
        case 'open':
          openDetail();
          break;
        case 'search':
          openSearchDrawer(true);
          break;
      }
    };
    async function searchEmit(data) {
      state.hah = data;
      await getTableAction().reload();
      setTimeout(() => {
        state.hah = {};
      });
    }
    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    const router = useRouter();
    const zkKeys = () => getTableAction().getSelectRowKeys();
    const zkRows = () => getTableAction().getSelectRows();
    let qusetionItemId: any = inject('qusetionItemId');
    let projectId: any = inject('projectId');
    const love = {
      id: qusetionItemId.value,
      className: 'QuestionManagement',
      moduleName: '项目管理-问题管理-关联风险',
      type: 'GET',
      remark: `获取/搜索了【${qusetionItemId.value}】关联风险列表`,
    };
    const getFormData = async (obj = computed(() => state.hah)) =>
    // return await new Api('/pmsx').fetch(params, `project/getPage/`, 'POST');
      await contactRiskTableApi(qusetionItemId.value, love, obj);
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = JSON.parse(JSON.stringify(zkRows()[0]));
    };
    const checkData2 = (data) => {
      state.nodeData = JSON.parse(JSON.stringify(data));
    };
    const lengthCheckHandle = () => {
      if (zkKeys().length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (zkKeys().length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };

    const riskDetailsIndexLocal = useIndex('riskDetailsIndexLocal');
    onMounted(() => {
      riskDetailsIndexLocal.value = 0;
    });
    const openDetail = () => {
      if (lengthCheckHandle()) return;
      toDetails(zkRows()[0]);
    };
    const toDetails = (data) => {
      // router.push({
      //   name: 'MenuComponents',
      //   query: {
      //     id: data.id,
      //     type: 0
      //   }
      // });
      router.push({
        name: 'RiskDetails',
        query: {
          id: data.id,
          projectId: projectId.value,
          type: 0,
        },
      });
    };
    function beforeFetch(T) {
      T = state.hah;
      return T;
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      confirm,
      dayjs,
      tableRef,
      api: getFormData,
      searchRegister,
      searchEmit,
      beforeFetch,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
