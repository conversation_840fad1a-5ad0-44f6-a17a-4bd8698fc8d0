package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectInventoryDTO;
import com.chinasie.orion.management.domain.entity.ProjectInventory;
import com.chinasie.orion.management.domain.vo.ProjectInventoryVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectInventory 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
public interface ProjectInventoryService extends OrionBaseService<ProjectInventory> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectInventoryVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectInventoryDTO
     */
    String create(ProjectInventoryDTO projectInventoryDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectInventoryDTO
     */
    Boolean edit(ProjectInventoryDTO projectInventoryDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectInventoryVO> pages(Page<ProjectInventoryDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectInventoryVO> vos) throws Exception;

    /**
     * 根据编号查询详情
     * <p>
     * * @param number
     */
    List<ProjectInventoryVO> getList(String number);
}
