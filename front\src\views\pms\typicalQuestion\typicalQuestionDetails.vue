<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower, BasicScrollbar,
} from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect, ComputedRef,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { openFormDrawer } from './utils';
import AddTableFlowModal from './components/AddTableFlowModal.vue';
import BasicInfo from './modal/BasicInfo.vue';
import BasicQuestionInfo from './modal/BasicQuestionInfo.vue';
import Api from '/@/api';
import AssociationPlan from '/@/views/pms/components/associationPlan/associationPlan.vue';
import AssociationRisk from '/@/views/pms/components/associationRisk/associationRisk.vue';
import AssociationAlter from '/@/views/pms/components/associationAlter/associationAlter.vue';
import AssociationReview from '/@/views/pms/components/associationReview/associationReview.vue';

const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsPowerData: Ref = ref({});
provide('detailsPowerData', detailsPowerData);
const detailsData: Ref<Record<any, any>> = ref();
provide('formData', computed(() => detailsData.value));
const powerData: Ref = ref();
provide('powerData', computed(() => powerData));
const menuData: Ref<any[]> = ref([]);

function menuChange({ id }) {
  actionId.value = id;
  // console.log(actionId.value);
}

onMounted(async () => {
  await getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms').fetch({
      pageCode: 'detail-container-5e5296-RStTRGxJJ',
    }, `questionLibrary/${dataId.value}`, 'GET');
    // detailsPowerData.value = result.detailAuthList;
    result.projectCode = result.number;
    result.ownerName = result.creatorName;
    detailsData.value = result;
    if (!actionId.value) {
      actionId.value = 'basicInfo';
    }
  } finally {
    loading.value = false;
  }
}

const actions = computed(() => [
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => isPower('edit-button-5e5296-RStTRGxJJ-YbzcFT7A', detailsPowerData.value),
    onClick() {
      openFormDrawer(AddTableFlowModal, { id: dataId.value }, getDetails);
    },
  },
  {
    text: '启用',
    icon: 'sie-icon-qiyong',
    isShow: () => detailsData.value?.status === 101,
    onClick() {
      useBatchData([detailsData.value.id]);
    },
  },
  {
    text: '禁用',
    icon: 'sie-icon-jinyong',
    isShow: () => detailsData.value?.status !== 101,
    onClick() {
      banBatchData([detailsData.value.id]);
    },
  },
]);
function useBatchData(params) {
  Modal.confirm({
    title: '启用提示',
    content: '是否启用当前的数据',
    onOk() {
      new Api('/pms').fetch(params, 'questionLibrary/use/batch', 'PUT').then((res) => {
        message.success('启用成功。');
        getDetails();
      });
    },
  });
}
function banBatchData(params) {
  Modal.confirm({
    title: '禁用提示',
    content: '是否禁用当前的数据',
    onOk() {
      new Api('/pms').fetch(params, 'questionLibrary/ban/batch', 'PUT').then((res) => {
        message.success('禁用成功。');
        getDetails();
      });
    },
  });
}
async function getPlanTableDataApi(params) {
  if (!detailsData.value.questionId) {
    return Promise.resolve([]);
  }
  return new Api(`/pas/question-management/relation/plan/${detailsData.value.questionId}`).fetch(params, '', 'POST');
}
// 关联风险
async function getRiskTableDataApi(params) {
  if (!detailsData.value.questionId) {
    return Promise.resolve([]);
  }
  params.query = {
    questionId: detailsData.value.questionId,
  };
  return new Api('/pas').fetch(params, 'questionRelationRisk/relationRisk/page', 'POST');
}
async function getAlterTableDataApi(params) {
  if (!detailsData.value.questionId) {
    return Promise.resolve([]);
  }
  return new Api('/pas').fetch({
    ...params,
    query: {
      dataSourceId: detailsData.value.questionId,
    },
  }, 'ecr/questionRelationEcr/page', 'POST');
}
async function getReviewTableDataApi(params) {
  if (!detailsData.value.questionSource) {
    return Promise.resolve([]);
  }
  let res = await new Api('/pms').fetch(params, `review/${detailsData.value.questionSource}`, 'GET');
  return [res];
}
function getPowerDataHandle(data) {
  powerData.value = data;
  menuData.value = [
    {
      id: 'basicInfo',
      name: '基本信息',
      isShow: isPower('PMS_DXWTKXQ_container_01', powerData),
    },
    {
      id: 'basicQuestionInfo',
      name: '问题关联信息',
      isShow: isPower('PMS_DXWTKXQ_container_02', powerData),
    },
    {
      name: '关联内容',
      id: 'content',
      children: [
        {
          name: '关联计划',
          id: 'plan',
          isShow: isPower('PMS_DXWTKXQ_container_03', powerData),
        },
        {
          name: '关联风险',
          id: 'risk',
          isShow: isPower('PMS_DXWTKXQ_container_04', powerData),
        },
        {
          name: '关联变更',
          id: 'changeApply',
          isShow: isPower('PMS_DXWTKXQ_container_05', powerData),
        },
        {
          name: '评审单',
          id: 'review',
          isShow: isPower('PMS_DXWTKXQ_container_06', powerData),
        },
      ].filter((item) => typeof item?.isShow === 'undefined' || item?.isShow),
    },
  ].filter((item) => typeof item?.isShow === 'undefined' || item?.isShow);
  actionId.value = menuData.value[0].id;
}
</script>

<template>
  <Layout3
    v-get-power="{pageCode: 'PMS7026',getPowerDataHandle}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="detailsData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <div
      v-loading="loading"
      class="layout-content"
    >
      <BasicInfo v-if="'basicInfo'===actionId && isPower('tabs-container-5e5296-RStTRGxJJ-YbzcFT7A',detailsPowerData)" />
      <BasicQuestionInfo
        v-if="actionId==='basicQuestionInfo'"
      />
      <AssociationPlan
        v-if="actionId==='plan'"
        pageType="check"
        :getPlanTableDataApi="getPlanTableDataApi"
      />
      <AssociationRisk
        v-if="actionId==='risk'"
        pageType="check"
        :getRiskTableDataApi="getRiskTableDataApi"
      />
      <AssociationAlter
        v-if="actionId==='changeApply'"
        pageType="check"
        :getAlterTableDataApi="getAlterTableDataApi"
      />

      <AssociationReview
        v-if="actionId==='review'"
        page-type="check"
        :getReviewTableDataApi="getReviewTableDataApi"
      />
    </div>
  </Layout3>
</template>

<style scoped lang="less">
.layout-content{
  height: 100%;
  padding-top: 1px;
}
</style>
