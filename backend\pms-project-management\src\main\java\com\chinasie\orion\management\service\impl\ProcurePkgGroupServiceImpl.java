package com.chinasie.orion.management.service.impl;

import com.chinasie.orion.management.domain.dto.ProcurePkgGroupDTO;
import com.chinasie.orion.management.domain.entity.ProcurePkgGroup;
import com.chinasie.orion.management.domain.vo.ProcurePkgGroupVO;
import com.chinasie.orion.management.repository.ProcurePkgGroupMapper;
import com.chinasie.orion.management.service.ProcurePkgGroupService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProcurePkgGroup 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:49:40
 */
@Service
@Slf4j
public class ProcurePkgGroupServiceImpl extends  OrionBaseServiceImpl<ProcurePkgGroupMapper, ProcurePkgGroup>   implements ProcurePkgGroupService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProcurePkgGroupVO detail(String id, String pageCode) throws Exception {
        ProcurePkgGroup procurePkgGroup =this.getById(id);
        ProcurePkgGroupVO result = BeanCopyUtils.convertTo(procurePkgGroup,ProcurePkgGroupVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param procurePkgGroupDTO
     */
    @Override
    public  String create(ProcurePkgGroupDTO procurePkgGroupDTO) throws Exception {
        ProcurePkgGroup procurePkgGroup =BeanCopyUtils.convertTo(procurePkgGroupDTO,ProcurePkgGroup::new);
        this.save(procurePkgGroup);

        String rsp=procurePkgGroup.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param procurePkgGroupDTO
     */
    @Override
    public Boolean edit(ProcurePkgGroupDTO procurePkgGroupDTO) throws Exception {
        ProcurePkgGroup procurePkgGroup =BeanCopyUtils.convertTo(procurePkgGroupDTO,ProcurePkgGroup::new);

        this.updateById(procurePkgGroup);

        String rsp=procurePkgGroup.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProcurePkgGroupVO> pages( Page<ProcurePkgGroupDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProcurePkgGroup> condition = new LambdaQueryWrapperX<>( ProcurePkgGroup. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProcurePkgGroup::getCreateTime);


        Page<ProcurePkgGroup> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProcurePkgGroup::new));

        PageResult<ProcurePkgGroup> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProcurePkgGroupVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProcurePkgGroupVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProcurePkgGroupVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购项目包组导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProcurePkgGroupDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProcurePkgGroupExcelListener excelReadListener = new ProcurePkgGroupExcelListener();
        EasyExcel.read(inputStream,ProcurePkgGroupDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProcurePkgGroupDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购项目包组导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProcurePkgGroup> procurePkgGroupes =BeanCopyUtils.convertListTo(dtoS,ProcurePkgGroup::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProcurePkgGroup-import::id", importId, procurePkgGroupes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProcurePkgGroup> procurePkgGroupes = (List<ProcurePkgGroup>) orionJ2CacheService.get("pmsx::ProcurePkgGroup-import::id", importId);
        log.info("采购项目包组导入的入库数据={}", JSONUtil.toJsonStr(procurePkgGroupes));

        this.saveBatch(procurePkgGroupes);
        orionJ2CacheService.delete("pmsx::ProcurePkgGroup-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProcurePkgGroup-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProcurePkgGroup> condition = new LambdaQueryWrapperX<>( ProcurePkgGroup. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProcurePkgGroup::getCreateTime);
        List<ProcurePkgGroup> procurePkgGroupes =   this.list(condition);

        List<ProcurePkgGroupDTO> dtos = BeanCopyUtils.convertListTo(procurePkgGroupes, ProcurePkgGroupDTO::new);

        String fileName = "采购项目包组数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProcurePkgGroupDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProcurePkgGroupVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProcurePkgGroupExcelListener extends AnalysisEventListener<ProcurePkgGroupDTO> {

        private final List<ProcurePkgGroupDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProcurePkgGroupDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProcurePkgGroupDTO> getData() {
            return data;
        }
    }


}
