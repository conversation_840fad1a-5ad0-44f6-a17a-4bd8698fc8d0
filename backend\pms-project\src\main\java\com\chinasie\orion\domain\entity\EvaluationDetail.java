package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * EvaluationDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
@TableName(value = "pmsx_evaluation_detail")
@ApiModel(value = "EvaluationDetail对象", description = "项目评价详情")
@Data
public class EvaluationDetail extends ObjectEntity implements Serializable{

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    @TableField(value = "score" )
    private Integer score;

    /**
     * 数据字典所对应的编码
     */
    @ApiModelProperty(value = "数据字典所对应的编码")
    @TableField(value = "evaluation_detail_type" )
    private String evaluationDetailType;

    /**
     * 评价内容
     */
    @ApiModelProperty(value = "评价内容")
    @TableField(value = "evaluation_content" )
    private String evaluationContent;

    /**
     * 评价id
     */
    @ApiModelProperty(value = "评价id")
    @TableField(value = "evaluation_id" )
    private String evaluationId;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

}
