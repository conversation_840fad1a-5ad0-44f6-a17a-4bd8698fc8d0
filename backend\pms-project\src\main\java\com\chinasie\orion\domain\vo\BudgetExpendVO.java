package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

import java.util.List;

/**
 * BudgetExpend VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetExpendVO对象", description = "项目支出")
@Data
public class BudgetExpendVO extends ObjectVO implements Serializable {


    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    private String name;


    /**
     * 预算申请编码
     */
    @ApiModelProperty(value = "预算编码")
    private String number;


    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    private String costCenterId;

    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;


    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    private String expenseSubjectNumber;


    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String expenseSubjectName;


    /**
     * 期间类型
     */
    @ApiModelProperty(value = "期间类型")
    private String timeType;

    @ApiModelProperty(value = "期间类型名称")
    private String timeTypeName;


    /**
     * 预算期间
     */
    @ApiModelProperty(value = "预算期间")
    private String budgetTime;


    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型")
    private String budgetObjectType;



    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型名称")
    private String budgetObjectTypeName;


    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    private String budgetObjectId;


    @ApiModelProperty(value = "预算对象名称")
    private String budgetObjectName;



    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    private BigDecimal expendMoney;


    /**
     * 剩余金额
     */
    @ApiModelProperty(value = "剩余金额")
    private BigDecimal residueMoney;


    /**
     * 支出单Id
     */
    @ApiModelProperty(value = "支出单Id")
    private String formId;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 审核通过预算金额
     */
    @ApiModelProperty(value = "审核通过预算金额")
    private BigDecimal approveBudgetMoney;


    @ApiModelProperty(value = "预算Id")
    private String budgetId;


}
