<template>
  <layout2 left-title="">
    <DetailsLayout
      title="合同基本信息"
      :column="3"
      :data-source="detail?.projectContractVO"
      :list="basicList"
    />
    <DetailsLayout
      title="合同质保信息"
      :list="warrantyInfo"
      :column="3"
      :data-source="detail?.projectContractVO"
    />
    <DetailsLayout
      title="甲方签约主体信息"
    >
      <div
        style="height: 220px;overflow: hidden"
      >
        <OrionTable
          ref="oursTableRef"
          :options="oursTable"
        />
      </div>
    </DetailsLayout>

    <DetailsLayout
      title="乙方签约主体信息"
    >
      <div
        style="height: 220px;overflow: hidden"
      >
        <OrionTable
          ref="partnerTableRef"
          :options="partnerTable"
        />
      </div>
    </DetailsLayout>

    <DetailsLayout
      :list="otherInfo"
      :data-source="detail?.projectContractVO"
      title="合同其他信息"
    />
  </layout2>
</template>

<script setup lang="ts">

import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  h, inject, nextTick, onMounted, reactive, Ref, ref,
} from 'vue';
import { getDict, Layout2, OrionTable } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';

const route = useRoute();
const state = reactive({
  contractCategoryOptions: [],
  contractTypeOptions: [],
});

onMounted(() => {
  init();
});

async function init() {
  state.contractCategoryOptions = await getDict('dict1716408088866783232');
  state.contractTypeOptions = await getDict('dict1716409722757906432');
}

const detail:any = inject('allData', {});

// 合同主体信息
const basicList = [
  {
    label: '合同名称',
    field: 'name',
  },
  {
    label: '合同编号',
    field: 'number',
  },
  {
    label: '合同类别',
    field: 'contractCategory',
    valueRender({ text, record }) {
      return h('span', state.contractCategoryOptions.find((item) => item.value === text)?.description);
    },
  },
  {
    label: '合同类型',
    field: 'contractType',
    valueRender({ text, record }) {
      return h('span', state.contractTypeOptions.find((item) => item.value === text)?.description);
    },
  },
  {
    label: '合同负责人',
    field: 'principalName',
  },

  {
    label: '合同负责人工号',
    field: 'principalCode',
  },

  {
    label: '合同负责部门',
    field: 'rspDeptName',
  },
  {
    label: '合同总金额',
    field: 'contractMoney',
    isMoney: true,
  },
  {
    label: '合同币种',
    field: 'currency',
  },
  {
    label: '合同创建人',
    field: 'creatorName',
  },
  {
    label: '合同创建人工号',
    field: 'creatorCode',
  },
  {
    label: '合同开始日期',
    field: 'startDate',
    formatTime: 'YYYY-MM-DD',
  },

  {
    label: '合同结束日期',
    field: 'endDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '合同签订日期',
    field: 'signDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '合同创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
];

// 甲方信息
const oursTableRef:Ref = ref();
const oursTable = reactive({
  pagination: false,
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
  rowKey: 'id',
  columns: [
    {
      title: '甲方签约主体全称',
      dataIndex: 'signedMainName',
    },
    {
      title: '公司税号',
      dataIndex: 'companyDutyParagraph',
    },
    {
      title: '联系地址',
      dataIndex: 'contactAddress',
    },
    {
      title: '商务联系人',
      dataIndex: 'busContactPerson',
    },
    {
      title: '商务联系电话',
      dataIndex: 'busContactPhone',
    },
    {
      title: '项目联系人',
      dataIndex: 'projectContactPerson',
    },
    {
      title: '项目联系人电话',
      dataIndex: 'projectContactPhone',
      width: 150,
    },
    {
      title: '电子邮箱',
      dataIndex: 'contractEmail',
    },
  ],
});
// 乙方信息
const partnerTableRef:Ref = ref();
const partnerTable = {
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
  pagination: false,
  rowKey: 'id',
  columns: [
    {
      title: '乙方签约主体全称',
      dataIndex: 'signedMainName',
    },
    {
      title: '公司税号',
      dataIndex: 'companyDutyParagraph',
    },
    {
      title: '联系地址',
      dataIndex: 'contactAddress',
    },
    {
      title: '商务联系人',
      dataIndex: 'busContactPerson',
    },
    {
      title: '商务联系电话',
      dataIndex: 'busContactPhone',
    },
    {
      title: '项目联系人',
      dataIndex: 'projectContactPerson',
    },
    {
      title: '项目联系人电话',
      dataIndex: 'projectContactPhone',
      width: 150,
    },
    {
      title: '电子邮箱',
      dataIndex: 'contractEmail',
    },
  ],
};
// 质保信息
const warrantyInfo = [
  {
    label: '是否具有质保期',
    field: 'isGuaranteePeriod',
    isBoolean: true,
  },
  {
    label: '预计质保期到期日期',
    field: 'guaranteeEndDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '是否具有质保金',
    field: 'isGuaranteeMoney',
    isBoolean: true,
  },
  {
    label: '质保金金额',
    field: 'guaranteeAmt',
    isMoney: true,
  },

];

// 其他信息
const otherInfo = [

  {
    label: '备注',
    field: 'remark',
  },
];

onMounted(() => {
  getDetail();
});

// 初始化数据
async function getDetail() {
  nextTick(() => {
    let contractOurSignedMainVO = [];
    let contractSupplierSignedMainVO = [];
    contractOurSignedMainVO.push(detail.value?.contractOurSignedMainVO);
    contractSupplierSignedMainVO.push(detail.value?.contractSupplierSignedMainVO);
    oursTableRef.value?.setTableData(contractOurSignedMainVO);
    partnerTableRef.value?.setTableData(contractSupplierSignedMainVO);
  });
}

</script>

<style scoped lang="less">
.slot-wrap{
  white-space: nowrap;
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 0;

  .label{
    color: ~`getPrefixVar('primary-10')`;
    margin-left: auto;
  }
  .value{
    margin-right: auto;
  }
}
</style>
