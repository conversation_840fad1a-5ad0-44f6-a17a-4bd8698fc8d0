<script setup lang="ts">
import {
  OrionTable,
  //  BasicButton,
  downloadByData,
} from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref, watchEffect,
} from 'vue';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { get as _get } from 'lodash-es';
import { BasicInjectionsKey } from '../../tokens/basicKeys';
import { parsePriceByNumber } from '/@/views/spm/purchaseManage/purchaseModule/utils';
import MoneyRow from '../../components/MoneyRow.vue';

const rowMoney = reactive([
  {
    key: 'currency',
    title: '币种',
    value: '',
    suffix: '',
  },
  {
    key: 'finalPrice',
    title: '框架合同总金额（原币）',
    value: '',
    suffix: '',
  },
  {
    key: 'usedAmt',
    title: '子订单金额汇总（原币）',
    value: '',
    suffix: '',
  },
  {
    key: 'unusedAmt',
    title: '未结算金额汇总（原币）',
    value: '',
    suffix: '',
  },
  {
    key: 'leftOveramt',
    title: '剩余金额（原币）',
    value: '',
    suffix: '',
  },
  {
    key: 'usedAmtProp',
    title: '已使用额度占比',
    value: '',
    suffix: '',
  },
  {
    key: 'leftOveramtProp',
    title: '剩余金额占比',
    value: '',
    suffix: '',
  },
]);

const pageSearchConditions = ref();
const tableRef: Ref = ref();
const basicInfo = inject(BasicInjectionsKey);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  isFilter2: false,
  showSmallSearch: false,
  showTableSetting: false,
  expandIconColumnIndex: 1,
  smallSearchField: ['purchReqDocCode'],
  showIndexColumn: false,
  filterConfig: {
    fields: [
      {
        field: 'purchReqDocCode',
        fieldName: '采购申请号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'num',
      width: 60,
    },
    {
      title: '采购申请号/行号',
      dataIndex: 'purchReqDocCode',
    },
    {
      title: '采购申请发起时间',
      dataIndex: 'purchaseRequestInitTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '采购立项完成时间',
      dataIndex: 'projectEndTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '商务负责人',
      dataIndex: 'businessLeader',
    },
    {
      title: '采购申请金额（本位币）',
      dataIndex: 'money',
      width: 180,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '采购申请金额（原币）',
      dataIndex: 'originalMoney',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '子订单金额（原币）',
      dataIndex: 'usedAmt',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '未结算金额（原币）',
      dataIndex: 'unusedAmt',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params: Record<string, any>) => {
    const searchConditionBody = {
      contractNumber: _get(basicInfo, 'data.contractNumber'),
    };
    pageSearchConditions.value = searchConditionBody;
    return new Api('/spm/requireInfo/getByCode').fetch(searchConditionBody, '', 'POST');
  },
};

const getMoney = async (params:any) => {
  try {
    const res = await new Api('/spm/requireInfo/getTotal').fetch({
      ...(params || {}),
    }, '', 'POST');
    if (res) {
      rowMoney.forEach((item) => {
        item.value = res[item.key];
      });
    }
  } catch (e) {
  }
};

watchEffect(async () => {
  await getMoney({ contractNumber: _get(basicInfo, 'data.contractNumber') });
});

async function handleExportApi() {
  delete pageSearchConditions?.value?.pageNum;
  delete pageSearchConditions?.value?.pageSize;
  Modal.confirm({
    title: '系统提示！',
    content: '确认导出所选数据？',
    onOk() {
      return new Promise((resolve) => {
        downloadByData('/spm/requireInfo/export/excel', {
          ...pageSearchConditions.value,
        }).then(() => {
          resolve('');
        }).catch((e) => {
          resolve(e);
        });
      });
    },
  });
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    class="scroll-table"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <MoneyRow
        :data="rowMoney"
        :isSize="true"
      />
      <!-- <BasicButton
        icon="sie-icon-daochu"
        class="mr-btn"
        @click="handleExportApi()"
      >
        导出
      </BasicButton> -->
    </template>
  </OrionTable>
</template>

<style scoped lang="less">
.mr-btn {
  margin-left: 15px;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
