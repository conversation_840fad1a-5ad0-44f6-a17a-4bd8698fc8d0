package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectDemandStatisticsDTO;
import com.chinasie.orion.domain.vo.DemandManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDemandStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectDemandStatisticsService {

    ProjectDemandStatisticsVO getProjectDemandStatusStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO);

    List<ProjectDemandStatisticsVO> getProjectDemandRspUserStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO);

    List<ProjectDemandStatisticsVO> getProjectDemandChangeStatusStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO);

    List<ProjectDemandStatisticsVO> getProjectDemandCreateStatistics(ProjectDemandStatisticsDTO projectDemandStatisticsDTO);

    Page<DemandManagementVO> getProjectDemandPages(Page<ProjectDemandStatisticsDTO> pageRequest) throws Exception;
}
