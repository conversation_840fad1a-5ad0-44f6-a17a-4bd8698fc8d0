package com.chinasie.orion.domain.dto.job;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/16:23
 * @description:
 */
@Data
public class JobRiskMeasureDTO  implements Serializable {
    @ApiModelProperty(value = "作业ID")
    private String jobId;
    @ApiModelProperty(value = "附件对象类表")
    private List<FileDTO> fileDTOList;
}
