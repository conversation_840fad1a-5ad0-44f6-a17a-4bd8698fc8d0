package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

import java.util.List;
/**
 * RequireReviewForm VO对象
 *
 * <AUTHOR>
 * @since 2024-06-03 20:42:14
 */
@ApiModel(value = "RequireReviewFormVO对象", description = "需求评审单")
@Data
public class RequireReviewFormVO extends  ObjectVO   implements Serializable{

            /**
         * 需求评审标识
         */
        @ApiModelProperty(value = "需求评审标识")
        private String requireReviewLogo;


        /**
         * 军兵种
         */
        @ApiModelProperty(value = "军兵种")
        private String armyArms;


        /**
         * 应用场景
         */
        @ApiModelProperty(value = "应用场景")
        private String applicationScenarios;


        /**
         * 是否定型
         */
        @ApiModelProperty(value = "是否定型")
        private String isCaseHardened;


        /**
         * 是否军检
         */
        @ApiModelProperty(value = "是否军检")
        private String idExamine;


        /**
         * 产品线
         */
        @ApiModelProperty(value = "产品线")
        private String productLine;


        /**
         * 公司预计签订金额-共计
         */
        @ApiModelProperty(value = "公司预计签订金额-共计")
        private BigDecimal totalSignAmount;


        /**
         * 预计签订金额
         */
        @ApiModelProperty(value = "预计签订金额")
        private BigDecimal signAmount;


        /**
         * 市场需求容量
         */
        @ApiModelProperty(value = "市场需求容量")
        private String demandCapacity;


        @ApiModelProperty(value = "商机标识")
        private String leadSign;

        /**
         * 商机名称
         */
        @ApiModelProperty(value = "商机名称")
        private String leadName;


        /**
         * 签单客户
         */
        @ApiModelProperty(value = "签单客户")
        private String signClient;

        /**
         * 产品竞争情况
         */
        @ApiModelProperty(value = "产品竞争情况")
        private String competitionSituation;


    

}
