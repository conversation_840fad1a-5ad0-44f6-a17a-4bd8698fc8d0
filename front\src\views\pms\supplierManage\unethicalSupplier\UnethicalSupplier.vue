<script setup lang="ts">
import {
  BasicButton, BasicTableAction, downloadByData, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, watchEffect,
} from 'vue';
import { useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import { parseBooleanToRender } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const router = useRouter();
const powerData = ref();
// 拿到表格
const tableRef: Ref = ref();

// 不良供应商数据展示初始变量
const suppliers = ref([]);
// 苏州院申报数量
const supplierCount = ref(0);
// 年度引入数量
const yearNum = ref(0);

// 导出全部初始变量
const defaultExportConditions = ref(null);

// 表格数据

const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: true,
  smallSearchField: ['supplierName', 'supplierCode'],
  isFilter2: true,
  rowSelection: {},
  filterConfig: {
    fields: [
      {
        field: 'supplierName',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicationNumber',
        fieldName: '供应商编码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'restrictedType',
        fieldName: '受限类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify([
          {
            label: 'ECP通报批评',
            name: 'ECP通报批评',
            value: 'ECP通报批评',
          },
          {
            label: '整改',
            name: '整改',
            value: '整改',
          },
          {
            label: '禁止使用',
            name: '禁止使用',
            value: '禁止使用',
          },
          {
            label: '观察警告',
            name: '观察警告',
            value: '观察警告',
          },
          {
            label: '限制投标',
            name: '限制投标',
            value: '限制投标',
          },
          {
            label: '黑名单',
            name: '黑名单',
            value: '黑名单',
          },
        ]),
      },
      {
        field: 'blacklistType',
        fieldName: '黑名单类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'projectName',
        fieldName: '项目名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractNumber',
        fieldName: '合同编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractName',
        fieldName: '合同名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicant',
        fieldName: '申请人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'declaringCompany',
        fieldName: '申报公司',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'restrictedScope',
        fieldName: '受限范围',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        searchFieldName: null,
        constValue: JSON.stringify([
          {
            label: '公司层受限供应商',
            name: '公司层受限供应商',
            value: '公司层受限供应商',
          },
          {
            label: '集团层受限供应商',
            name: '集团层受限供应商',
            value: '集团层受限供应商',
          },
        ]),
      },
    ],
  },
  columns: [
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 110,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 220,
    },
    {
      title: '申请编号',
      dataIndex: 'applicationNumber',
      width: 150,
    },
    {
      title: '受限类型',
      dataIndex: 'restrictedType',
      width: 100,
    },
    {
      title: '整改状态',
      dataIndex: 'rectificationStatus',
      width: 100,
    },
    {
      title: '黑名单类型',
      dataIndex: 'blacklistType',
      width: 100,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 300,
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 150,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 220,
    },
    {
      title: '申报日期',
      dataIndex: 'declarationDate',
      width: 130,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      width: 150,
    },
    {
      title: '申报公司',
      dataIndex: 'declaringCompany',
      width: 220,
    },
    {
      title: '内容描述',
      dataIndex: 'contentDescription',
      width: 220,
    },
    {
      title: '审批完成时间',
      dataIndex: 'approvalCompletionTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否解冻',
      dataIndex: 'whetherThawed',
      width: 100,
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '受限范围',
      dataIndex: 'restrictedScope',
      width: 150,
    },
    {
      title: '集团发送SAP',
      dataIndex: 'groupSendsSap',
      width: 150,
    },
  ],
  api: async (params:Record<string, any>) => {
    defaultExportConditions.value = params.searchConditions ? [...params.searchConditions] : null;
    return await new Api('/pms/supplierRestrictedRecord').fetch({
      power: {
        pageCode: 'PMS00005',
        containerCode: 'PMS_BLXW_container_02',
      },
      ...params,
    }, 'getRestrictedRecordPages', 'POST');
  },
};

// 表格左上角按钮定义
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '导出全部',
    code: 'PMS_BLXW_container_01_button_01',
  },
].filter((item) => isPower(item.code, powerData.value)));

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 批量导出按钮事件
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length === 0) {
        exportConditions = defaultExportConditions.value;
      } else if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      }
      downloadByData('/pms/supplierRestrictedRecord/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

const goToStatistics = async () => {
  try {
    const result = await new Api('/pms/supplierRestrictedRecord/getNum').fetch(
      { searchConditions: defaultExportConditions.value ? defaultExportConditions.value : null },
      '',
      'POST',
    );
    yearNum.value = result.ndNum;
    supplierCount.value = result.szNum;
    return result;
  } catch (e) {
  }
};

// 表格操作区域按钮点击事件
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      router.push({
        name: 'UnethicalSupplier',
        params: {
          id: record.id,
        },
      });
      break;
  }
}

const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};

watchEffect(() => {
  goToStatistics();
});
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS00005',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-daochu"
            @click="exportTableData(selectKeys)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
        <div class="supplier-count-box">
          <div class="supplier-count-box-top">
            苏州院申报数量：{{ supplierCount || 0 }}个
          </div>
          <div class="supplier-count-box-top">
            年度申报数量：{{ yearNum || 0 }} 个
          </div>
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.supplier-count-box {
  display: flex !important;
  align-items: center;
  background-color: #fff;

  .supplier-count-box-top{
    margin-left: 15px;
    margin-right: 15px;
  }
}

.toolbar-container{
  display: flex;
  align-items: center !important;
}

:deep(.flex-f1 .flex){
  align-items: center;
}

.supplier-count-box-special{
  margin-left: 50px;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>