package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:42
 * @description:
 */
@Data
@ApiModel(value = "StakeholderDTO对象", description = "干系人/不只是人")
public class StakeholderDTO extends ObjectDTO {

    /**
     * 地址
     */
    @Size(max = 255, message = "地址过长")
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 联系信息
     */
    @Size(max = 64, message = "联系信息过长")
    @ApiModelProperty(value = "联系信息")
    private String contactInfo;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactType;

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;
}
