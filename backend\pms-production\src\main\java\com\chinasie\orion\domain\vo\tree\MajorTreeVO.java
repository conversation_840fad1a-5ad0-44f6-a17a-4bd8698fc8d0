package com.chinasie.orion.domain.vo.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/20/9:11
 * @description:
 */
@Data
public class MajorTreeVO implements Serializable {

    @ApiModelProperty(value = "专业ID")
    private String id ;
    @ApiModelProperty(value = "专业名称")
    private String name ;
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName ;
    @ApiModelProperty(value = "责任人id")
    private String rspUserId ;
    @ApiModelProperty(value = "父级ID")
    private String parentId ;
    @ApiModelProperty(value = "全路径")
    private String chainPath ;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound ;
    @ApiModelProperty(value = "层级所处顺序")
    private Integer sort ;
    @ApiModelProperty(value = "组织类型")
    private String type ;


}
