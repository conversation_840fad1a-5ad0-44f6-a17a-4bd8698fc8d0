package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlanData DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划填报数据")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataExportDTO  implements Serializable{



    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编码", index = 0)
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @ColumnWidth(20)
    @ExcelProperty(value = "合同名称", index = 1)
    private String contractName;

    @ApiModelProperty(value = "合同里程碑名称")
    @ColumnWidth(20)
    @ExcelProperty(value = "合同里程碑名称", index = 2)
    private String milestoneName;

    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编码(备注 )", index = 3)
    private String remark;

    @ApiModelProperty(value = "甲方合同号")
    @ExcelProperty(value = "甲方合同号", index = 4)
    private String partyAContractNumber;

    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称", index = 5)
    private String partyADeptIdName;

    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号", index = 6)
    private String number;

    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态", index = 7)
    private String lockStatusName;

    @ApiModelProperty(value = "数据状态")
    @ExcelProperty(value = "数据状态", index = 8)
    private String statusName;

    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型", index = 9)
    private String incomeConfirmTypeName;

    @ApiModelProperty(value = "预计开票/暂估日期")
    @ExcelProperty(value = "预计开票/暂估日期", index = 10)
    @DateTimeFormat("yyyy-MM-dd")
    private Date estimateInvoiceDate;

    @ApiModelProperty(value = "税率(%)")
    @ExcelProperty(value = "税率(%)", index = 11)
    private String taxRateName;


    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    @ExcelProperty(value = "本次暂估金额（价税合计）", index = 12)
    private BigDecimal estimateAmt;


    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "本次开票金额（价税合计）")
    @ExcelProperty(value = "本次开票金额（价税合计）", index = 13)
    private BigDecimal invAmt;


    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "本次作废发票合计（价税合计）")
    @ExcelProperty(value = "本次作废发票合计（价税合计）", index = 14)
    private BigDecimal cancelInvAmt;


    /**
     * 里程碑已暂估金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（价税合计）")
    @ExcelProperty(value = "里程碑已暂估金额（价税合计） ", index = 15)
    private BigDecimal milestonEstimateAmt;


    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    @ExcelProperty(value = "本次冲销暂估金额（价税合计）", index = 16)
    private BigDecimal writeOffAmt;


    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    @ExcelProperty(value = "里程碑已预收款开票金额（价税合计）", index = 17)
    private BigDecimal milestonePrePaidInvAmt;


    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    @ExcelProperty(value = "预收款转收入金额（价税合计） ", index = 18)
    private BigDecimal advancePayIncomeAmt;


    /**
     * 本次收入计划金额（价税合计）
     */
    @ApiModelProperty(value = "本次收入计划金额（不含税）")
    @ExcelProperty(value = "本次收入计划金额（不含税） ", index = 19)
    private BigDecimal incomePlanAmt;



    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 20)
    private String expertiseCenterName;


    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 21)
    private String expertiseStationName;


    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号", index = 22)
    private String projectNumber;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 23)
    private String projectName;


    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "项目负责人 ", index = 24)
    private String projectRspUserName;


    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    @ExcelProperty(value = "开票/收入确认公司 ", index = 25)
    private String billingCompanyName;



    @ApiModelProperty(value = "集团内/外")
    @ExcelProperty(value = "集团内/外 ", index = 26)
    private String internalExternalName;


    @ApiModelProperty(value = "所属行业")
    @ExcelProperty(value = "集团内/外 ", index = 27)
    private String industry;



    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额（价税合计）")
    @ExcelProperty(value = "里程碑金额（价税合计） ", index =28)
    private BigDecimal milestoneAmt;


    /**
     * 里程碑已开票收入金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已开票收入金额（价税合计）")
    @ExcelProperty(value = "里程碑已开票收入金额（价税合计）", index = 29)
    private BigDecimal milestoneInvAmt;


    /**
     * 里程碑未开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑未开票金额（价税合计）")
    @ExcelProperty(value = "里程碑未开票金额（价税合计） ", index = 30)
    private BigDecimal milestoneNoInvAmt;


    /**
     * 本次暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次暂估金额（不含税）")
    @ExcelProperty(value = "本次暂估金额（不含税） ", index = 31)
    private BigDecimal estimateAmtExTax;


    /**
     * 本次开票金额（不含税）
     */
    @ApiModelProperty(value = "本次开票金额（不含税）")
    @ExcelProperty(value = "本次开票金额（不含税） ", index = 32)
    private BigDecimal invAmtExTax;


    /**
     * 本次作废发票金额（不含税）
     */
    @ApiModelProperty(value = "本次作废发票金额（不含税）")
    @ExcelProperty(value = "本次作废发票金额（不含税） ", index = 33)
    private BigDecimal cancelInvAmtExTax;


    /**
     * 里程碑已暂估金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（不含税）")
    @ExcelProperty(value = "里程碑已暂估金额（不含税）", index = 34)
    private BigDecimal milestoneAmtExTax;


    /**
     * 本次冲销暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（不含税）")
    @ExcelProperty(value = "本次冲销暂估金额（不含税）", index = 35)
    private BigDecimal writeOffAmtExTax;


    /**
     * 里程碑已预收款开票金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（不含税）")
    @ExcelProperty(value = "里程碑已预收款开票金额（不含税）", index = 36)
    private BigDecimal milestoneInvAmtExTax;


    /**
     * 预收款转收入金额（不含税）
     */
    @ApiModelProperty(value = "预收款转收入金额（不含税）")
    @ExcelProperty(value = "预收款转收入金额（不含税） ", index = 37)
    private BigDecimal advPayIncomeAmtExTax;


    /**
     * 本月是否申报收入计划
     */
    @ApiModelProperty(value = "本月是否申报收入计划")
    @ExcelProperty(value = "本月是否申报收入计划 ", index = 38)
    private String isRevenue;


    /**
     * 不申报收入计划原因
     */
    @ApiModelProperty(value = "不申报收入计划原因")
    @ExcelProperty(value = "不申报收入计划原因 ", index = 39)
    private String noRevenuePlanReason;


    /**
     * 其他说明
     */
    @ApiModelProperty(value = "其他说明")
    @ExcelProperty(value = "其他说明 ", index = 40)
    private String otherNotes;


    @ApiModelProperty(value = "风险状态")
    @ExcelProperty(value = "风险状态 ", index = 41)
    private String riskTypeName;

    @ApiModelProperty(value = "修改收入计划原因")
    @ExcelProperty(value = "修改收入计划原因 ", index = 42)
    private String changeReason;


    @ApiModelProperty(value = "风险环节")
    @ExcelProperty(value = "风险环节 ", index = 43)
    private String riskLinkName;

    @ApiModelProperty(value = "验收日期")
    @ExcelProperty(value = "验收日期 ", index = 44)
    @DateTimeFormat("yyyy-MM-dd")
    private String acceptanceDate;



}
