<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.alibaba</groupId>
  <artifactId>easyexcel-support</artifactId>
  <version>3.1.5</version>
  <name>easyexcel-support</name>
  <description>easyexcel is a excel handle tools written in Java</description>
  <url>https://github.com/alibaba/easyexcel/easyexcel-support</url>
  <inceptionYear>2018</inceptionYear>
  <organization>
    <name>Alibaba Group</name>
    <url>https://github.com/alibaba</url>
  </organization>
  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>jipengfei</id>
      <name>jipengfei</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>zhuangjiaju</id>
      <name>Jiaju Zhuang</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://**************/alibaba/easyexcel.git/easyexcel-support</connection>
    <url>https://github.com/alibaba/easyexcel/easyexcel-support</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.32</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.20</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.13</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
