package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.IdeaFormToIdeaForm;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

/**
 * <p>
 * IdeaFormToIdeaForm Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@Mapper
public interface IdeaFormToIdeaFormMapper extends OrionBaseMapper<IdeaFormToIdeaForm> {
}

