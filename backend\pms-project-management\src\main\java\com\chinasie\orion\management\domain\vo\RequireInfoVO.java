package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * RequireInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "RequireInfoVO对象", description = "需求单")
@Data
public class RequireInfoVO extends ObjectVO implements Serializable {

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private Date time;


    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;


    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String respDept;


    /**
     * 技术责任人
     */
    @ApiModelProperty(value = "技术责任人")
    private String techUser;


    /**
     * 汇总金额
     */
    @ApiModelProperty(value = "汇总金额")
    private BigDecimal totalAmt;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


    /**
     * 剩余未使用金额
     */
    @ApiModelProperty(value = "剩余未使用金额")
    private BigDecimal unusedAmt;


    /**
     * 已使用金额
     */
    @ApiModelProperty(value = "已使用金额")
    private BigDecimal usedAmt;


    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    private String projectID;


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String purchReqDocCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    @ApiModelProperty(value = "采购申请发起时间")
    private Date purchaseRequestInitTime;

    @ApiModelProperty(value = "采购立项完成时间")
    private Date projectEndTime;

    @ApiModelProperty(value = "采购申请金额（元）")
    private BigDecimal money;

    @ApiModelProperty(value = "商务负责人")
    private String businessLeader;

    @ApiModelProperty(value = "采购申请金额(原币)")
    private BigDecimal originalMoney;

    @ApiModelProperty(value = "父级")
    private String parentId;

    @ApiModelProperty(value = "框架合同总金额（原币）")
    private BigDecimal finalPrice;

    @ApiModelProperty(value = "序号")
    private String num;


    @ApiModelProperty(value = "子项")
    private List<RequireInfoVO> children;

}
