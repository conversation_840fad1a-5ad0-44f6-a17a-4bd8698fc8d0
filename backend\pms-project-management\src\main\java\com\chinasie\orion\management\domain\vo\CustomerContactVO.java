package com.chinasie.orion.management.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * customerContact VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
@ApiModel(value = "customerContactVO对象", description = "客户管理详情")
@Data
public class CustomerContactVO extends ObjectVO implements Serializable{

            /**
         * 备注
         */
        @ApiModelProperty(value = "备注")
        private String pRemark;


        /**
         * 邮箱
         */
        @ApiModelProperty(value = "邮箱")
        private String mail;


        /**
         * 手机
         */
        @ApiModelProperty(value = "手机")
        private String phoneNumber;


        /**
         * 职位
         */
        @ApiModelProperty(value = "职位")
        private String jobName;


        /**
         * 主要联系人
         */
        @ApiModelProperty(value = "主要联系人")
        private String isMain;

        /**
         * 主要联系人
         */
        @ApiModelProperty(value = "主要联系人中文")
        private String isMainName;


        /**
         * 性别
         */
        @ApiModelProperty(value = "性别")
        private String sex;


        /**
         * 角色
         */
        @ApiModelProperty(value = "角色")
        private String roleName;


        /**
         * 姓名
         */
        @ApiModelProperty(value = "姓名")
        private String name;


        /**
         * 部门
         */
        @ApiModelProperty(value = "部门")
        private String department;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


        /**
         * 主表ID
         */
        @ApiModelProperty(value = "主表ID")
        private String mainTableId;


    

}
