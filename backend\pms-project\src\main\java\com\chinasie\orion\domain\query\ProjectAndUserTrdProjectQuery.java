package com.chinasie.orion.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 项目 + 项目成员 第三方接口 根据项目查询类
 *
 * <AUTHOR>
 * @since 2024年9月24日
 */
@Data
@ApiModel(value = "ProjectAndUserTrdProjectQuery", description = "项目 + 项目成员 第三方接口 根据项目查询类")
public class ProjectAndUserTrdProjectQuery {

    @ApiModelProperty(value = "项目编码")
    @NotNull
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

}
