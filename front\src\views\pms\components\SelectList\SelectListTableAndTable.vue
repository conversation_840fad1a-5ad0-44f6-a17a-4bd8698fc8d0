<template>
  <div
    class="add-role-user"
  >
    <div
      v-if="showLeftList"
      class="add-role-user-left"
    >
      <BasicScrollbar
        @scroll="scroll"
      >
        <div class="list-content">
          <template
            v-for="(item,index) in listData"
            :key="index"
          >
            <div
              class="list-item"
              :class="{'list-item-active':state.selectedKey===item.id}"
              @click="selectChange(item)"
            >
              {{ item.name }}
            </div>
          </template>
        </div>
      </BasicScrollbar>
    </div>
    <div
      class="add-role-user-content"
    >
      <orion-table
        ref="tableRef"
        :options="options"
        @smallSearch="keywordSearch"
      />
    </div>
    <div class="add-role-user-right">
      <RightList
        :rightName="rightName"
        :selectedUser="selectTableData"
        @deleteUser="deleteSelectedUser"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  OrionTable, BasicScrollbar,
} from 'lyra-component-vue3';
import {
  Ref, ref, computed, defineProps, reactive, onMounted,
} from 'vue';
import { Tree as ATree, InputSearch as AInputSearch } from 'ant-design-vue';
import RightList from './RightList.vue';

const props = defineProps({
  getTableData: {
    type: Function,
    default: null,
  },
  showTreeSearch: {
    type: Boolean,
    default: false,
  },
  getListApi: {
    type: Function,
    default: null,
  },
  columns: {
    type: Array,
    default: () => [],
  },
  showLeftList: {
    type: Boolean,
    default: false,
  },
  isTableTree: {
    type: Boolean,
    default: false,
  },
  selectType: {
    type: String,
    default: 'check',
  },
  rightName: {
    type: String,
    default: 'name',
  },
  selectData: {
    type: Array,
    default: () => [],
  },
});
const selectTableData:Ref<Record<any, any>[]> = ref([]);
const selectedRowKeys:Ref<string[]> = ref([]);
const pageNum:Ref<number> = ref(1);
const totalPages:Ref<number> = ref(1);
const listData:Ref<Record<any, any>[]> = ref([]);
const selectListKeys:Ref<any[]> = ref([]);
const state = reactive({
  treeData: [],
  selectedKey: '',
  expandedKeys: [],
  searchTree: '',
});
const keyword:Ref<string> = ref('');
const tableRef = ref();
function keywordSearch(val: string) {
  keyword.value = val;
  tableRef.value.reload();
}
const options = ref({
  deleteToolButton: 'add|enable|disable|delete',
  pagination: !props.isTableTree,
  showIndexColumn: !props.isTableTree,
  rowSelection: {
    type: computed(() => props.selectType),
    selectedRowKeys: computed(() => selectedRowKeys.value),
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      if (selected) {
        if (props.selectType === 'radio') {
          selectedRowKeys.value = [record.id];
          selectTableData.value = [record];
        } else {
          selectedRowKeys.value.push(record.id);
          selectTableData.value.push(record);
        }
      } else {
        selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
        selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      let tableData = tableRef.value.getDataSource();
      if (selected) {
        tableData.forEach((item) => {
          if (selectedRowKeys.value.indexOf(item.id) < 0) {
            selectedRowKeys.value.push(item.id);
            selectTableData.value.push(item);
          }
        });
      } else {
        tableData.forEach((item) => {
          selectTableData.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
          selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(item.id), 1);
        });
        // let tableData=tabl
      }
    },
  },
  showTableSetting: false,
  // 数据接口
  api: (params) => {
    if (!props.getTableData) {
      return new Promise((resolve, reject) => resolve([]));
    }
    if (props.showLeftList) {
      if (!state.selectedKey) {
        return new Promise((resolve, reject) => resolve([]));
      }
      return props.getTableData(state.selectedKey, params);
    }
    return props.getTableData(params);
  },
  // 展示的列数据

  columns: props.columns.filter((item) => item.dataIndex !== 'action') || [],
});
function deleteSelectedUser(record) {
  if (record) {
    selectTableData.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
    selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
  } else {
    selectTableData.value = [];
    selectedRowKeys.value = [];
  }
}
onMounted(() => {
  if (props.showLeftList && props.getListApi) {
    props.getListApi({
      pageNum: pageNum.value,
      pageSize: 30,
    }).then((res) => {
      listData.value = res.content;
      totalPages.value = res.totalPages;
      if (listData.value.length > 0) {
        state.selectedKey = listData.value[0].id;
        tableRef.value.reload({ page: 1 });
      }
    });
  }
  if (props.selectData.length > 0) {
    selectedRowKeys.value = props.selectData.map((item) => item.id);
    selectTableData.value = props.selectData;
  }
});
const selectChange = (data) => {
  if (state.selectedKey === data.id) return;
  state.selectedKey = data.id;
  tableRef.value.reload({ page: 1 });
};
defineExpose({
  getFormData,
});
function scroll(val) {
  let listHeight = document.getElementsByClassName('list-content')[0].clientHeight;
  let maxHeight = listHeight - document.getElementsByClassName('add-role-user-left')[0].clientHeight;
  if (val.scrollTop === maxHeight + 20) {
    if (pageNum.value === totalPages.value) return;
    pageNum.value += 1;
    props.getListApi({
      pageNum: pageNum.value,
      pageSize: 30,
    }).then((res) => {
      listData.value = listData.value.concat(res.content);
    });
  }
}
function getFormData() {
  return new Promise((resolve, reject) => {
    if (selectedRowKeys.value.length > 0) {
      return resolve({
        selectedRowKeys: selectedRowKeys.value,
        selectTableData: selectTableData.value,
      });
    }
    return reject('请选择数据');
  });
}
</script>
<style lang="less" scoped>
.add-role-user{
  display: flex;
  height: 100%;
  width: 100%;
  justify-content: space-between;
  .add-role-user-left{
    width: 250px;
    border-right: 1px solid #dddddd;
    padding: 10px;
    .list-content{
      .list-item{
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        margin-bottom: 5px;
        &:hover{
          background: #e3f4fc;
        }
      }

      .list-item-active{
        background: #e3f4fc;
      }
    }
  }
  .add-role-user-left-search{
    :deep(.basic-scrollbar){
      height: calc(~'100% - 30px');
    }
  }
  .add-role-user-content{
    flex:1;
    width: 1px;
    height: 100%;
    overflow: hidden;
  }
  .add-role-user-right{
    border-left: 1px solid ~`getPrefixVar('border-color-base')`;
    width: 220px;
  }
}
.basic-button{
   margin-left: 0 !important;
   &:last-child{
     margin-right: 0;
   }
 }
</style>