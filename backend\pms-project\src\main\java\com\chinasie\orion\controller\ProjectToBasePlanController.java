package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.NewProjectToBasePlanDTO;
import com.chinasie.orion.domain.vo.NewProjectToBasePlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectToBasePlanService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * NewProjectToBasePlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
@RestController
@RequestMapping("/new-project-to-base-plan")
@Api(tags = "项目和综合计划的关系表（1;N）")
public class ProjectToBasePlanController {

    @Autowired
    private ProjectToBasePlanService newProjectToBasePlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目和综合计划的关系表（1;N）", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NewProjectToBasePlanVO> getSingleDetail(@PathVariable(value = "id") String id) throws Exception {
        NewProjectToBasePlanVO rsp = newProjectToBasePlanService.getSingleDetail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param newProjectToBasePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目和综合计划的关系表（1;N）", subType = "新增", bizNo = "")
    public ResponseDTO<NewProjectToBasePlanVO> createEntity(@RequestBody NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception {
        NewProjectToBasePlanVO rsp =  newProjectToBasePlanService.createEntity(newProjectToBasePlanDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param newProjectToBasePlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目和综合计划的关系表（1;N）", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> updateEntity(@RequestBody  NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception {
        Boolean rsp = newProjectToBasePlanService.updateEntity(newProjectToBasePlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除（批量）", type = "项目和综合计划的关系表（1;N）", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> deleteByIdList(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = newProjectToBasePlanService.deleteByIdList(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 删除 通过年度计划id列表
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过年度计划id列表删除（批量）")
    @RequestMapping(value = "/plan/id/list", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】通过年度计划id列表删除（批量）", type = "项目和综合计划的关系表（1;N）", subType = "通过年度计划id列表删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> deleteByPlanIdList(@RequestBody List<String> planIdList) throws Exception {
        Boolean rsp = newProjectToBasePlanService.deleteByPlanIdList(planIdList);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "项目和综合计划的关系表（1;N）", subType = "分页", bizNo = "")
    public ResponseDTO<Page<NewProjectToBasePlanVO>> pages(@RequestBody Page<NewProjectToBasePlanDTO> pageRequest) throws Exception {
        Page<NewProjectToBasePlanVO> rsp =  newProjectToBasePlanService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
