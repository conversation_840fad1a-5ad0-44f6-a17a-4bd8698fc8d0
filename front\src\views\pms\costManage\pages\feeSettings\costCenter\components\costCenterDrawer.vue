<template>
  <BasicDrawer
    width="50%"
    :title="state.drawerName"
    :showFooter="true"
    @ok="confirmDrawer"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <BasicForm
      v-if="state.showForm"
      ref="formRef"
      @register="registerForm"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';
const emit = defineEmits(['updatePage']);
const formRef = ref();
const state = reactive({
  drawerName: '',
  formData: {},
  visibleStatus: false,
  showForm: true,
  id: '',
});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, formData }) => {
    if (openProps.type === 'edit') {
      state.drawerName = '编辑成本';
      state.id = openProps.formData.id;
      formRef.value.setFieldsValue({
        number: openProps.formData.number,
        name: openProps.formData.name,
        status: openProps.formData.status,
      });
    } else {
      state.drawerName = '新增成本';
    }
    // 设置为已打开状态
    state.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
  if (!visible) {
    resetForm();
  }
}

const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '成本中心编码',
    defaultValue: '新增完成时自动生成编码',
    componentProps: {
      disabled: true,
      placeholder: '新增完成时自动生成编码',
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '成本中心名称',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'status',
    component: 'Select',
    label: '状态',
    defaultValue: 1,
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
  },
];

const [
  registerForm,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 24,
  },
});
function resetForm() {
  formRef.value.resetFields();
}
async function confirmDrawer() {
  await formRef.value.validate();
  const values = await formRef.value.getFieldsValue();
  changeOkLoading(true);
  if (state.drawerName === '编辑成本') {
    const data = {
      id: state.id,
      name: values.name,
      status: values.status,
    };
    await new Api('/pms/costCenter/').fetch(data, '', 'PUT').then(() => {
    }).finally(() => {
      changeOkLoading(false);
    });
  } else {
    const data = {
      name: values.name,
      status: values.status,
    };
    await new Api('/pms/costCenter').fetch(data, '', 'POST').then(async (res) => {
    }).finally(() => {
      changeOkLoading(false);
    });
  }
  closeDrawer();
  emit('updatePage');// 更新父组件数据
}

</script>
