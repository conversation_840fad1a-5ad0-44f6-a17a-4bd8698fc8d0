package org.jeecg.modules.demo.dkm.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.base.BaseServiceImpl;
import org.jeecg.modules.demo.dkm.config.DuplicateCheckConfig;
import org.jeecg.modules.demo.dkm.config.FieldCheckConfig;
import org.jeecg.modules.demo.dkm.entity.Quotation;
import org.jeecg.modules.demo.dkm.mapper.QuotationMapper;
import org.jeecg.modules.demo.dkm.service.QuotationService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityCheckService;
import org.jeecg.modules.demo.dkm.service.base.DataIntegrityExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 报价管理数据校验服务实现
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class QuotationServiceImpl extends BaseServiceImpl<QuotationMapper, Quotation> 
    implements QuotationService {

    @Autowired
    private QuotationMapper quotationMapper;
    
    // 使用组合方式引入数据准确性检查服务
    private final DataIntegrityCheckService<Quotation> dataIntegrityCheckService;
    
    // 使用组合方式引入数据准确性Excel导出服务
    private final DataIntegrityExcelService<Quotation> dataIntegrityExcelService;
    
    public QuotationServiceImpl() {
        // 创建匿名内部类实例
        this.dataIntegrityCheckService = new DataIntegrityCheckService<Quotation>() {};
        this.dataIntegrityExcelService = new DataIntegrityExcelService<Quotation>() {};
    }

    @Override
    public String checkQuotationDataIntegrity() {
        log.info("开始检查pmsx_quotation_management表数据准确性");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<Quotation>> fieldConfigs = Arrays.asList(
                // 报价编号缺失配置
                FieldCheckConfig.create(
                        "报价编号",
                        Quotation::getQuotationId,
                        Arrays.asList("报价ID", "报价名称"),
                        Quotation::getId,
                        Quotation::getQuotationName
                )
        );
        
        // 查询报价名称重复的数据
        List<Quotation> duplicateNameQuotations = quotationMapper.findDuplicateQuotationNames();
        
        // 定义报价名称重复的检查项
        List<DuplicateCheckConfig<Quotation>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "报价名称",
                        duplicateNameQuotations,
                        Arrays.asList("报价ID", "报价名称", "所属需求"),
                        Quotation::getId,
                        Quotation::getQuotationName,
                        Quotation::getRequirementName
                )
        );

        // 使用数据准确性检查服务执行检查
        String result = dataIntegrityCheckService.executeIntegrityCheckWithDuplicates(
                "报价管理表(pmsx_quotation_management)", 
                () -> quotationMapper.findQuotationDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pmsx_quotation_management表数据准确性检查");
        return result;
    }
    
    @Override
    public String checkQuotationDataIntegrityExcel() {
        log.info("开始生成pmsx_quotation_management表数据准确性Excel报告");
        
        // 配置各字段的检查条件、表头和行数据生成逻辑
        List<FieldCheckConfig<Quotation>> fieldConfigs = Arrays.asList(
                // 报价编号缺失配置
                FieldCheckConfig.create(
                        "报价编号为空",
                        Quotation::getQuotationId,
                        Arrays.asList("报价ID", "报价名称"),
                        Quotation::getId,
                        Quotation::getQuotationName
                )
        );
        
        // 查询报价名称重复的数据
        List<Quotation> duplicateNameQuotations = quotationMapper.findDuplicateQuotationNames();
        
        // 定义报价名称重复的检查项
        List<DuplicateCheckConfig<Quotation>> duplicateConfigs = Collections.singletonList(
                DuplicateCheckConfig.create(
                        "报价名称重名",
                        duplicateNameQuotations,
                        Arrays.asList("报价ID", "报价名称", "所属需求"),
                        Quotation::getId,
                        Quotation::getQuotationName,
                        Quotation::getRequirementName
                )
        );

        // 直接使用数据准确性Excel导出服务生成Excel报告
        String excelFilePath = dataIntegrityExcelService.executeIntegrityCheckWithDuplicatesToExcel(
                "报价管理表", 
                () -> quotationMapper.findQuotationDataIntegrityIssues(),
                fieldConfigs,
                duplicateConfigs
        );
        
        log.info("完成pmsx_quotation_management表数据准确性Excel报告生成，文件路径：{}", excelFilePath);
        return excelFilePath;
    }
} 