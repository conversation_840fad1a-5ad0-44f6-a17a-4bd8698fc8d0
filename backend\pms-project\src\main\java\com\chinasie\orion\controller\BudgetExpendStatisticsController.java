package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.dto.BudgetManagementDTO;
import com.chinasie.orion.domain.vo.BudgetExpendDetailVO;
import com.chinasie.orion.domain.vo.BudgetExpendStatisticsVO;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.domain.vo.BudgetStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetExpendStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/budgetExpendStatistics")
    @Api(tags = "成本核算统计")
public class BudgetExpendStatisticsController {

    @Autowired
    private BudgetExpendStatisticsService budgetExpendStatisticsService;
    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "成本核算统计", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<BudgetExpendDetailVO>> pages(@RequestBody PageRequest<BudgetExpendFormDTO> pageRequest) throws Exception {
        Page<BudgetExpendDetailVO> rsp =  budgetExpendStatisticsService.getPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "科目统计")
    @RequestMapping(value = "/getBudgetExpendStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】科目统计", type = "成本核算统计", subType = "科目统计", bizNo = "")
    public ResponseDTO<List<BudgetExpendStatisticsVO>> pages(@RequestBody BudgetExpendFormDTO BudgetExpendFormDTO) throws Exception {
        List<BudgetExpendStatisticsVO> rsp =  budgetExpendStatisticsService.getBudgetExpendStatistics(BudgetExpendFormDTO.getProjectId());
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "总数统计")
    @RequestMapping(value = "/getTotalList/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】总数统计", type = "成本核算统计", subType = "总数统计", bizNo = "")
    public ResponseDTO<BudgetStatisticsVO> pages(@PathVariable("projectId") String projectId) throws Exception {
        BudgetStatisticsVO rsp =  budgetExpendStatisticsService.getTotalList(projectId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("导出（Excel）")
    @GetMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "成本核算统计", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestParam String expenseSubjectId,@RequestParam String projectId, HttpServletResponse response) throws Exception {
        budgetExpendStatisticsService.export(expenseSubjectId,projectId, response);
    }

    @ApiOperation("导出全部（Excel）")
    @GetMapping(value = "/exportAll/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出全部数据", type = "成本核算统计", subType = "导出全部数据", bizNo = "")
    public void exportAllByExcel(@RequestParam String projectId, HttpServletResponse response) throws Exception {
        budgetExpendStatisticsService.exportAll(projectId, response);
    }
}
