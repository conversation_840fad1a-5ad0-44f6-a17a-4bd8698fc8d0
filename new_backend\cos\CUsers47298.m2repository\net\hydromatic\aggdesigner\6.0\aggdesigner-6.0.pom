<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>net.hydromatic</groupId>
  <artifactId>aggdesigner</artifactId>
  <packaging>pom</packaging>
  <version>6.0</version>
  <name>Aggregate Designer</name>
  <description>Designs aggregate tables</description>
  <url>http://github.com/julianhyde/aggdesigner</url>
  <inceptionYear>2006</inceptionYear>

  <organization>
    <name><PERSON></name>
    <url>http://www.hydromatic.net</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>julian<PERSON></id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <url>https://github.com/julianhyde</url>
      <roles>
        <role>architect</role>
        <role>developer</role>
      </roles>
      <timezone>-8</timezone>
      <properties />
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>Mondrian developers list</name>
      <post><EMAIL></post>
      <archive>http://lists.pentaho.org/pipermail/mondrian/</archive>
    </mailingList>
  </mailingLists>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- The following list is sorted. -->
    <commons-io.version>1.3.1</commons-io.version>
    <commons-lang.version>2.4</commons-lang.version>
    <commons-logging.version>1.1.3</commons-logging.version>
    <dom4j.version>1.6.1</dom4j.version>
    <foodmart-data-hsqldb.version>0.4</foodmart-data-hsqldb.version>
    <hsqldb.version>2.3.1</hsqldb.version>
    <jaxen.version>1.1-beta-6</jaxen.version>
    <jmock.version>2.4.0</jmock.version>
    <junit.version>4.4</junit.version>
    <maven-compiler-plugin.version>2.3.2</maven-compiler-plugin.version>
    <maven-dependency-plugin.version>2.8</maven-dependency-plugin.version>
    <maven-gpg-plugin.version>1.5</maven-gpg-plugin.version>
    <maven-jar-plugin.version>2.2</maven-jar-plugin.version>
    <maven-javadoc-plugin.version>2.9.1</maven-javadoc-plugin.version>
    <maven-scm-provider.version>1.9.1</maven-scm-provider.version>
    <maven-source-plugin.version>2.2.1</maven-source-plugin.version>
    <maven-surefire-plugin.version>2.17</maven-surefire-plugin.version>
    <mondrian.version>3.6.7</mondrian.version>
    <mysql-connector-java.version>5.1.17</mysql-connector-java.version>
    <properties-maven-plugin.version>1.0-alpha-2</properties-maven-plugin.version>
  </properties>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <site>
      <id>aggdesigner.website</id>
      <name>Aggregate Designer Website</name>
      <url>file:/home/<USER>/web2/aggdesigner</url>
    </site>
  </distributionManagement>

  <issueManagement>
    <system>github</system>
    <url>https://github.com/julianhyde/aggdesigner/issues</url>
  </issueManagement>

  <scm>
    <connection>scm:git:git://github.com/julianhyde/aggdesigner.git</connection>
    <developerConnection>scm:git:**************:julianhyde/aggdesigner.git</developerConnection>
    <url>http://github.com/julianhyde/aggdesigner/tree/master</url>
    <tag>aggdesigner-6.0</tag>
  </scm>

  <modules>
    <module>pentaho-aggdesigner-algorithm</module>
    <module>pentaho-aggdesigner-core</module>
  </modules>

  <dependencyManagement>
    <!-- Dependency versions for all sub-modules.
         Sorted by groupId, artifactId. -->
    <dependencies>
      <dependency>
        <groupId>commons-lang</groupId>
        <artifactId>commons-lang</artifactId>
        <version>${commons-lang.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commons-logging.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>net.hydromatic</groupId>
        <artifactId>aggdesigner-algorithm</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>pentaho</groupId>
        <artifactId>mondrian</artifactId>
        <version>${mondrian.version}</version>
      </dependency>
      <dependency>
        <groupId>dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>${dom4j.version}</version>
      </dependency>
      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>${jaxen.version}</version>
      </dependency>
  
      <!-- Test dependencies. -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.jmock</groupId>
        <artifactId>jmock-junit4</artifactId>
        <version>${jmock.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.jmock</groupId>
        <artifactId>jmock-legacy</artifactId>
        <version>${jmock.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>net.hydromatic</groupId>
        <artifactId>aggdesigner-algorithm</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>${hsqldb.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql-connector-java.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>net.hydromatic</groupId>
        <artifactId>foodmart-data-hsqldb</artifactId>
        <version>${foodmart-data-hsqldb.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <configuration>
          <links>
            <link>http://docs.oracle.com/javase/8/docs/api/</link>
          </links>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <build>
    <defaultGoal>package</defaultGoal>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>${maven-dependency-plugin.version}</version>
        <executions>
          <execution>
            <id>properties</id>
            <goals>
              <goal>properties</goal>
            </goals>
            <phase>initialize</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source-plugin.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>verify</phase>
            <goals>
              <goal>jar-no-fork</goal>
              <goal>test-jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>properties-maven-plugin</artifactId>
        <version>${properties-maven-plugin.version}</version>
        <executions>
          <execution>
            <phase>initialize</phase>
            <goals>
              <goal>read-project-properties</goal>
            </goals>
            <configuration>
              <files>
                <file>build.properties</file>
              </files>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <!-- If we don't specify gitexe version, git doesn't
             commit during release process. -->
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <dependencies>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-provider-gitexe</artifactId>
            <version>${maven-scm-provider.version}</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
