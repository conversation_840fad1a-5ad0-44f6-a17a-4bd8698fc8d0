<script setup lang="ts">
import {
  BasicButton, BasicCard, BasicTableAction, OrionTable,
} from 'lyra-component-vue3';
import {
  h,
  inject, reactive, ref, Ref,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '预算名称',
      field: 'name',
    },
    {
      label: '预算编码',
      field: 'number',
    },
    {
      label: '成本中心',
      field: 'costCenterName',
    },
    {
      label: '科目编码',
      field: 'expenseSubjectNumber',
    },
    {
      label: '科目名称',
      field: 'expenseSubjectName',
    },
    {
      label: '期间类型',
      field: 'timeTypeName',
    },
    {
      label: '预算期间',
      field: 'budgetTime',
    },
    {
      label: '预算对象类型',
      field: 'budgetObjectTypeName',
    },
    {
      label: '预算总额',
      field: 'budgetMoney',
    },
    {
      label: '剩余金额',
      field: 'residueMoney',
    },
    {
      label: '币种',
      field: 'currencyName',
    },
  ],
  column: 4,
  dataSource: detailsData,
});

</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
