package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ContractDictConstant;
import com.chinasie.orion.constant.ProjectContractPayNodeStatusEnum;
import com.chinasie.orion.domain.dto.ContractPayNodeDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeStatusConfirmDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ContractPayNodeConfirmVO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.domain.vo.ContractPayNodeVO;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ContractPayNodeConfirmAuditRecordService;
import com.chinasie.orion.service.ContractPayNodeService;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ContractPayNode 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@Service
public class ContractPayNodeServiceImpl extends OrionBaseServiceImpl<ContractPayNodeRepository, ContractPayNode> implements ContractPayNodeService {

    @Autowired
    private ContractPayNodeRepository contractPayNodeRepository;

    @Autowired
    private ContractPayNodeConfirmRepository contractPayNodeConfirmRepository;

    @Autowired
    private ContractPayNodeConfirmNodeRepository contractPayNodeConfirmNodeRepository;

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Autowired
    private ContractPayNodeStatusConfirmRepository contractPayNodeStatusConfirmRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private DictBo dictBo;

    @Autowired
    private ContractPayNodeConfirmAuditRecordService contractPayNodeConfirmAuditRecordService;

    @Autowired
    private ClassRedisHelper classRedisHelper;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractPayNodeDetailVO detail(String id) throws Exception {
        ContractPayNodeDetailVO contractPayNodeDetailVO = new ContractPayNodeDetailVO();
        ContractPayNode contractPayNode = contractPayNodeRepository.selectById(id);
        if (contractPayNode == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点信息未找到!");
        }
        ContractPayNodeVO contractPayNodeVO = BeanCopyUtils.convertTo(contractPayNode, ContractPayNodeVO::new);
        Map<String, String> payTypeValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.PAY_TYPE);
        Map<String, String> settlementTypeValueToDesMap = dictBo.getDictValueToDesMap(ContractDictConstant.SETTLEMENT_TYPE);
        if (StringUtils.hasText(contractPayNodeVO.getPayType())) {
            contractPayNodeVO.setPayTypeName(payTypeValueToDesMap.get(contractPayNodeVO.getPayType()));
        }
        if (StringUtils.hasText(contractPayNodeVO.getSettlementType())) {
            contractPayNodeVO.setSettlementType(settlementTypeValueToDesMap.get(contractPayNodeVO.getSettlementType()));
        }


        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractPayNode.getContractId());
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);

        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodes = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getNodeId, id);
        if (!CollectionUtils.isBlank(contractPayNodeConfirmNodes)) {
            List<String> confirmIdList = contractPayNodeConfirmNodes.stream().map(ContractPayNodeConfirmNode :: getConfirmId).collect(Collectors.toList());
            List<ContractPayNodeConfirm> contractPayNodeConfirmList = contractPayNodeConfirmRepository.selectList(ContractPayNodeConfirm :: getId,confirmIdList);
            if (!CollectionUtils.isBlank(contractPayNodeConfirmList)) {
                ContractPayNodeConfirm contractPayNodeConfirm = contractPayNodeConfirmList.get(0);
                ContractPayNodeConfirmVO contractPayNodeConfirmVO = BeanCopyUtils.convertTo(contractPayNodeConfirm, ContractPayNodeConfirmVO::new);
                String auditUserId = contractPayNodeConfirmVO.getAuditUserId();
                if (StringUtils.hasText(auditUserId)) {
                    UserVO rspUser = userRedisHelper.getUserById(auditUserId);
                    contractPayNodeConfirmVO.setAuditUserName(null == rspUser ? "" : rspUser.getName());
                    contractPayNodeConfirmVO.setAuditUserCode(null == rspUser ? "" : rspUser.getCode());
                }

                String submitUserId = contractPayNodeConfirmVO.getSubmitUserId();
                if (StringUtils.hasText(submitUserId)) {
                    UserVO rspUser = userRedisHelper.getUserById(submitUserId);
                    contractPayNodeConfirmVO.setSubmitUserIdName(null == rspUser ? "" : rspUser.getName());
                    contractPayNodeConfirmVO.setSubmitUserIdCode(null == rspUser ? "" : rspUser.getCode());
                }

                contractPayNodeDetailVO.setContractPayNodeConfirmVO(contractPayNodeConfirmVO);
                List<DocumentVO> documentVOList = documentService.getDocumentList(contractPayNodeConfirmVO.getSubmitId(), null);
                contractPayNodeDetailVO.setDocumentVOList(documentVOList);
                LambdaQueryWrapperX<ContractPayNodeConfirmAuditRecord> auditRecordWrapperX = new LambdaQueryWrapperX<>();
                auditRecordWrapperX.eq(ContractPayNodeConfirmAuditRecord :: getConfirmId,contractPayNodeConfirm.getId());
                List<ContractPayNodeConfirmAuditRecord> auditRecordList = contractPayNodeConfirmAuditRecordService.list(auditRecordWrapperX);
                if(!CollectionUtils.isBlank(auditRecordList)){
                    List<String> auditIds = auditRecordList.stream().map(ContractPayNodeConfirmAuditRecord :: getId).collect(Collectors.toList());
                    List<DocumentVO> auditDocumentVOList =  documentService.getDocumentListByIdList(auditIds);
                    contractPayNodeDetailVO.setAuditDocumentVOList(auditDocumentVOList);
                }
            }

        }
        contractPayNodeDetailVO.setContractPayNodeVO(contractPayNodeVO);
        contractPayNodeDetailVO.setContractPayNodeVOList(contractPayNodeVOList);



        return contractPayNodeDetailVO;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ContractPayNodeVO> pages(Page<ContractPayNodeDTO> pageRequest) throws Exception {
        Page<ContractPayNode> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayNode::new));
        ContractPayNodeDTO contractPayNodeDTO = pageRequest.getQuery();
        LambdaQueryWrapper<ContractPayNode> queryWrapper = new LambdaQueryWrapper<>();
        if (contractPayNodeDTO != null) {
            if (StringUtils.hasText(contractPayNodeDTO.getContractId())) {
                queryWrapper.eq(ContractPayNode::getContractId, contractPayNodeDTO.getContractId());
            }
        }
        PageResult<ContractPayNode> page = contractPayNodeRepository.selectPage(realPageRequest, queryWrapper);

        Page<ContractPayNodeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayNodeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayNodeVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ContractPayNodeVO> listByContractId(String contractId) throws Exception {
        List<ContractPayNode> list = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<ContractPayNodeVO> vos = BeanCopyUtils.convertListTo(list, ContractPayNodeVO::new);
        return vos;
    }

    @Override
    public Boolean statusConfirm(ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO) throws Exception {
        List<String> nodeIdList = contractPayNodeStatusConfirmDTO.getNodeIdList();
        String contractId = contractPayNodeStatusConfirmDTO.getContractId();
        if (CollectionUtils.isBlank(nodeIdList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点不能为空!");
        }
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if (projectContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目合同未找到!");
        }
        nodeIdList = nodeIdList.stream().distinct().collect(Collectors.toList());
        LambdaQueryWrapper<ContractPayNode> contractPayNodeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        contractPayNodeLambdaQueryWrapper.eq(ContractPayNode::getContractId, contractId);
        contractPayNodeLambdaQueryWrapper.in(ContractPayNode::getId, nodeIdList);
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(contractPayNodeLambdaQueryWrapper);
        if (CollectionUtils.isBlank(contractPayNodeList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点id无效!");
        }

        Map<String, ContractPayNode> contractPayNodeMap = contractPayNodeList.stream().collect(Collectors.toMap(ContractPayNode::getId, Function.identity()));
        nodeIdList.forEach(p -> {
            ContractPayNode contractPayNode = contractPayNodeMap.get(p);
            if (contractPayNode == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, p + "节点id未找到节点数据!");
            }
            if (ProjectContractPayNodeStatusEnum.COMPLETE.getStatus().equals(contractPayNode.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "节点支付已完成不能发起支付状态确认!");
            }
//            if (ProjectContractPayNodeStatusEnum.CREATED.getStatus().equals(contractPayNode.getStatus())) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, contractPayNode.getPayType() + "节点支付未开始不能发起支付状态确认!");
//            }
        });
        List<ContractPayNodeStatusConfirm> contractPayNodeStatusConfirms = new ArrayList<>();
        String confirmId = classRedisHelper.getUUID(ContractPayNodeStatusConfirm.class.getSimpleName());
//        String confirmId = IdUtils.orionUUID(ContractPayNodeStatusConfirm.class);
        nodeIdList.forEach(p -> {
            ContractPayNodeStatusConfirm contractPayNodeStatusConfirm = new ContractPayNodeStatusConfirm();
            contractPayNodeStatusConfirm.setConfirmId(confirmId);
            contractPayNodeStatusConfirm.setContractId(contractId);
            contractPayNodeStatusConfirm.setNodeId(p);
            contractPayNodeStatusConfirms.add(contractPayNodeStatusConfirm);
        });

        contractPayNodeList.forEach(p -> {
            p.setStatus(ProjectContractPayNodeStatusEnum.COMPLETE.getStatus());
            p.setPayDate(new Date());
        });

        contractPayNodeRepository.updateBatch(contractPayNodeList, contractPayNodeList.size());


        contractPayNodeStatusConfirmRepository.insertBatch(contractPayNodeStatusConfirms);


        return true;
    }
}
