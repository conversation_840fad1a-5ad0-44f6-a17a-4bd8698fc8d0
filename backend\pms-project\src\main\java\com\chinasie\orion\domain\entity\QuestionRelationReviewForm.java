package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * QuestionRelationReviewForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-15 11:03:43
 */
@TableName(value = "pmsx_question_relation_review_form")
@ApiModel(value = "QuestionRelationReviewFormEntity对象", description = "问题与评审单关系表")
@Data
public class QuestionRelationReviewForm extends ObjectEntity implements Serializable{

/**
 * 问题ID
 */
@ApiModelProperty(value = "问题ID")
@TableField(value = "from_id")
private String fromId;

/**
 * 问题类名
 */
@ApiModelProperty(value = "问题类名")
@TableField(value = "from_class")
private String fromClass;

/**
 * 评审单ID
 */
@ApiModelProperty(value = "评审单ID")
@TableField(value = "to_id")
private String toId;

/**
 * 评审单类名
 */
@ApiModelProperty(value = "评审单类名")
@TableField(value = "to_class")
private String toClass;

}
