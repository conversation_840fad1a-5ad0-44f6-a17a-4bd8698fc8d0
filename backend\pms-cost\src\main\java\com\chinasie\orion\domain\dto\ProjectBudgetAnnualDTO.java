package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectBudgetAnnualDTO implements Serializable {
    @ExcelProperty(value = "项目id列表")
    @Size(min = 1,message = "项目参数不能为空")
    private List<String> projectIdList;
    @ExcelProperty(value = "年度")
    @NotEmpty(message = "年度参数不能为空")
    private String annual;
}
