package com.chinasie.orion.management.constant;

/**
 * 客户行业
 */


public enum CustomerIndustryEnum {

    NUCLEAR_ENERGY("nuclear_energy","核电"),
    NEW_ENERGY("new_energy","新能源"),
    WIND_ENERGY("wind_energy","风能"),
    FOSSIL_FUEL_ENERGY("fossil_fuel_energy","火电"),
    OTHER("other","其他");


    private String name;
    private String desc;

    CustomerIndustryEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (CustomerIndustryEnum lt : CustomerIndustryEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}