package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PersonJobPostEqu;
import com.chinasie.orion.domain.dto.PersonJobPostEquDTO;
import com.chinasie.orion.domain.vo.PersonJobPostEquVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * PersonJobPostEqu 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:42
 */
public interface PersonJobPostEquService  extends  OrionBaseService<PersonJobPostEqu>  {


        /**
         *  详情
         *
         * * @param id
         */
    PersonJobPostEquVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param personJobPostEquDTO
         */
        String create(PersonJobPostEquDTO personJobPostEquDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param personJobPostEquDTO
         */
        Boolean edit(PersonJobPostEquDTO personJobPostEquDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<PersonJobPostEquVO> pages( Page<PersonJobPostEquDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<PersonJobPostEquVO> vos)throws Exception;

    List<PersonJobPostEquVO> listByUserCode(String userCode) throws Exception;

    List<PersonJobPostEquVO> listByUserCodeList(List<String> userCodeList, String jobCode);
}
