<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, openBasicSelectModal, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  computed,
  h, nextTick, onMounted, ref, Ref,
} from 'vue';
import { getJobNoProps, useMaterialSearch } from '/@/views/pms/materialManage/components/hooks';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  record: any
}>();

const flag: Ref<boolean> = ref(false);
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'assetType',
    component: 'SelectDictVal',
    label: '资产类型',
    required: true,
    componentProps: {
      disabled: !!props?.record?.id,
      dictNumber: 'pms_supplies_type',
      async onChange(value: string) {
        await resetFields();
        await setFieldsValue({
          assetType: value,
        });
      },
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '资产编码/条码',
    required: true,
    componentProps({ formModel }) {
      return {
        disabled: !!props?.record?.id,
        readonly: true,
        onClick() {
          if (!formModel.assetType) {
            message.info('请先选择资产类型');
            return;
          }
          openBasicSelectModal({
            title: '请选择',
            ...getNumberProps(formModel.assetType, formModel),
            onOk(records: any[]) {
              const numberItem = records?.[0];
              if (numberItem) {
                setValueByMaterial(numberItem, formModel);
              }
            },
          } as any);
        },
      };
    },
  },
  {
    field: 'assetCode',
    component: 'Input',
    label: '资产代码',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'assetName',
    component: 'Input',
    label: '资产名称',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'costCenter',
    component: 'TreeSelectOrg',
    label: '成本中心名称',
    required: true,
    componentProps({ formModel }) {
      return {
        fieldNames: { value: 'deptCode' },
        disabled: formModel?.assetType === 'pms_fixed_assets',
        placeholder: formModel?.assetType === 'pms_fixed_assets' ? ' ' : '请选择',
        allowClear: formModel?.assetType !== 'pms_fixed_assets',
      };
    },
  },
  {
    field: 'specificationModel',
    component: 'Input',
    label: '规格型号',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'isVerification',
    component: 'Select',
    label: '是否需要检定',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps({ formModel }) {
      return {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
        onChange(value: boolean) {
          flag.value = value;
          formModel.isOverdue = value && formModel.nextVerificationDate && dayjs(formModel.nextVerificationDate).valueOf() < dayjs().valueOf();
          clearValidate();
        },
      };
    },
  },
  {
    field: 'nextVerificationDate',
    component: 'DatePicker',
    label: '下次检定日期',
    required: computed(() => flag.value),
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        onChange(date) {
          formModel.isOverdue = formModel.isVerification && date && dayjs(date).valueOf() < dayjs().valueOf();
        },
      };
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资管理信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'baseCode',
    component: 'ApiSelect',
    label: '资产存放地',
    defaultValue: props?.record?.baseCode,
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
        api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
      };
    },
  },
  {
    field: 'actInDate',
    component: 'DatePicker',
    label: '资产入库日期',
    required: true,
    componentProps: {
      disabled: !!props?.record?.id,
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    field: 'inputStockNum',
    component: 'InputNumber',
    label: '资产入库数量',
    required: true,
    defaultValue: 1,
    componentProps({ formModel }) {
      return {
        disabled: formModel?.assetType === 'pms_fixed_assets',
        placeholder: formModel?.assetType === 'pms_fixed_assets' ? ' ' : '请输入',
        allowClear: formModel?.assetType !== 'pms_fixed_assets',
        min: 1,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'isMetering',
    component: 'Select',
    label: '是否计量器具',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'rspUserNo',
    component: 'SelectUser',
    label: '责任人工号',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps() {
      return {
        showField: 'code',
        selectUserModalProps: {
          selectType: 'radio',
        },
        onChange(user: any[]) {
          setFieldsValue({
            rspUserName: user[0]?.name,
          });
        },
      };
    },
  },
  {
    field: 'rspUserName',
    component: 'Input',
    label: '责任人姓名',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'useUserNo',
    component: 'SelectUser',
    label: '使用人工号',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      showField: 'code',
      selectUserModalProps: {
        selectType: 'radio',
      },
      onChange(user: any[]) {
        setFieldsValue({
          useUserName: user[0]?.name,
        });
      },
    },
  },
  {
    field: 'useUserName',
    component: 'Input',
    label: '使用人姓名',
    componentProps: {
      disabled: true,
      placeholder: ' ',
    },
  },
  {
    field: 'isReport',
    component: 'Select',
    label: '是否向电厂报备',
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'isOverdue',
    component: 'Select',
    label: '检定是否超期',
    componentProps() {
      return {
        disabled: true,
        placeholder: '',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      };
    },
  },
  {
    field: 'jobNo',
    component: 'Input',
    label: '物资应用作业（工单号）',
    required: true,
    componentProps({ formModel }) {
      return {
        readonly: true,
        onClick() {
          openBasicSelectModal({
            title: '请选择',
            ...getJobNoProps(),
            onOk(records) {
              const numberItem = records?.[0];
              if (numberItem) {
                setFieldsValue({
                  jobName: numberItem?.name,
                  jobNo: numberItem.number,
                });
              } else {
                formModel.jobName && setFieldsValue({
                  jobName: '',
                  jobNo: '',
                });
              }
            },
          } as any);
        },
      };
    },
  },
  {
    field: 'jobName',
    component: 'Input',
    label: '作业名称',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, resetFields, getFieldsValue,
    clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
const {
  setValueByMaterial, getNumberProps,
} = useMaterialSearch(setFieldsValue);

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/material-manage').fetch('', props?.record?.id, 'GET');
    flag.value = !!result?.isVerification;
    await setFieldsValue({
      ...result,
      inputStockNum: result?.inputStockNum || 1,
      rspUserNo: [
        {
          id: result?.rspUserId,
          name: result?.rspUserName,
          code: result?.rspUserNo,
        },
      ],
      useUserNo: [
        {
          id: result?.useUserId,
          name: result?.useUserName,
          code: result?.useUserNo,
        },
      ],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      useUserNo: formValues.useUserNo[0]?.code,
      rspUserNo: formValues.rspUserNo[0]?.code,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/material-manage').fetch({
        ...params,
        id: props?.record?.id,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}

:deep(.ant-picker-input>input[disabled]) {
  color: #000;
}
</style>
