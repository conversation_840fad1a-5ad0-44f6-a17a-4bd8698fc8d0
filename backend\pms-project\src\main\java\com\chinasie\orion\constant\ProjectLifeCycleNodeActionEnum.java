package com.chinasie.orion.constant;



import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yk
 * @date: 2023/10/30 11:00
 * @description: 项目生命周期按钮枚举
 */
public enum  ProjectLifeCycleNodeActionEnum{

    START_DECLARE("startDeclare", ProjectLifeCycleNodeEnum.PROJECT_DECLARE.getNodeKey(), "发起申报"),
    VIEW_DECLARE("viewDeclare",  ProjectLifeCycleNodeEnum.PROJECT_DECLARE.getNodeKey(), "查看申报"),
    START_CONTRACT_SIGNING("startContractSigning",  ProjectLifeCycleNodeEnum.CONTRACT_SIGNING.getNodeKey(), "发起合同签订"),
    START_APPROVAL("startApproval",  ProjectLifeCycleNodeEnum.PROJECT_APPROVAL.getNodeKey(), "发起立项"),
    VIEW_APPROVAL("viewApproval",  ProjectLifeCycleNodeEnum.PROJECT_APPROVAL.getNodeKey(), "查看立项"),
    MAINTENANCE_MEMBER("maintenanceMember",  ProjectLifeCycleNodeEnum.MEMBER_MANAGEMENT.getNodeKey(), "维护项目成员"),
    WORK_ESTIMATE("workEstimate",  ProjectLifeCycleNodeEnum.MAN_HOUR_MANAGEMENT.getNodeKey(), "项目工时预估"),
    WORK_FILL("workFill",  ProjectLifeCycleNodeEnum.MAN_HOUR_MANAGEMENT.getNodeKey(), "工时填报"),
    PREPARE_BUDGET("prepareBudget",  ProjectLifeCycleNodeEnum.BUDGET_MANAGEMENT.getNodeKey(), "编制项目预算"),
    MANAGE_COST("manageCost",  ProjectLifeCycleNodeEnum.COST_MANAGEMENT.getNodeKey(), "管理项目成本"),
    MANAGE_REVENUE("manageRevenue",  ProjectLifeCycleNodeEnum.REVENUE_MANAGEMENT.getNodeKey(), "管理项目营收"),
    PREPARE_MATERIAL_PLAN("prepareMaterialPlan",  ProjectLifeCycleNodeEnum.MATERIAL_MANAGEMENT.getNodeKey(), "编制物资计划"),
    MATERIAL_STORAGE("materialStorage",  ProjectLifeCycleNodeEnum.MATERIAL_MANAGEMENT.getNodeKey(), "添加物资入库"),
    ADD_PURCHASING_ORDER("addPurchasingOrder",  ProjectLifeCycleNodeEnum.PURCHASING_MANAGEMENT.getNodeKey(), "添加采购订单"),
    ADD_CONTRACT("addContract",  ProjectLifeCycleNodeEnum.CONTRACT_MANAGEMENT.getNodeKey(), "添加项目合同"),
    MAINTENANCE_RISK("maintenanceRisk",  ProjectLifeCycleNodeEnum.RISK_MANAGEMENT.getNodeKey(), "维护项目风险"),
    MAINTENANCE_PROBLEM("maintenanceProblem",  ProjectLifeCycleNodeEnum.PROBLEM_MANAGEMENT.getNodeKey(), "维护项目问题"),
    START_CHANGE("startChange",  ProjectLifeCycleNodeEnum.CHANGE_MANAGEMENT.getNodeKey(), "发起项目变更"),
    VIEW_DELIVER("viewDeliver",  ProjectLifeCycleNodeEnum.DELIVER_MANAGEMENT.getNodeKey(), "查看项目交付物"),
    MANAGE_DOCUMENT("manageDocument",  ProjectLifeCycleNodeEnum.PROJECT_DOCUMENT.getNodeKey(), "管理项目文档"),
    START_ACCEPTANCE("startAcceptance",  ProjectLifeCycleNodeEnum.PROJECT_ACCEPTANCE.getNodeKey(), "发起项目验收"),
    VIEW_ACCEPTANCE("viewAcceptance",  ProjectLifeCycleNodeEnum.PROJECT_ACCEPTANCE.getNodeKey(), "查看验收"),
    START_SCHEME("startScheme",  ProjectLifeCycleNodeEnum.PLAN_MANAGEMENT.getNodeKey(), "项目计划编制"),
    START_EVALUATION("startEvaluation",  ProjectLifeCycleNodeEnum.PROJECT_EVALUATION.getNodeKey(), "发起项目评价"),
    ;
    // 节点KEY
    private final String nodeKey;
    // 按钮key
    private final String key;
    // 操作名
    private final String label;

    ProjectLifeCycleNodeActionEnum(String key, String nodeKey, String label) {
        this.key = key;
        this.nodeKey = nodeKey;
        this.label = label;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }

    public static List<ProjectLifeCycleNodeActionEnum> getProjectLifeCycleNodeActionByNodeKey(String nodeKey) {
        ProjectLifeCycleNodeActionEnum[] values =  ProjectLifeCycleNodeActionEnum.values();
        List<ProjectLifeCycleNodeActionEnum> list = Arrays.asList(values);
        List<ProjectLifeCycleNodeActionEnum> nodeKeyAction = list.stream().filter(p -> (p.nodeKey.equals(nodeKey))).collect(Collectors.toList());
        return nodeKeyAction;
    }
}
