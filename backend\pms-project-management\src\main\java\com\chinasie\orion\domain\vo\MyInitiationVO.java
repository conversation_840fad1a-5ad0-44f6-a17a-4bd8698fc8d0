package com.chinasie.orion.domain.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class MyInitiationVO {
    // 唯一标识源的ID
    private String sourceId;

    // 项目的唯一标识
    private String projectId;

    // 任务标题
    private String title;

    // 接受时间
    private LocalDateTime acceptTime;

    // 接受时间的格式化字符串
    private String formatAcceptTime;

    // 创建者的用户ID
    private String creatorId;

    // 任务的类型
    private String taskType;

    // 任务类型的名称
    private String taskTypeName;

    // 任务详细信息的链接
    private String detailUrl;

    // 消息链接
    private String messageUrl;

    // 用户名称
    private String userName;

    // 父级链的描述
    private String parentChain;

    // 父级任务的ID
    private String parentId;

    // 风险来源
    private Integer since;


    // 风险类型
    private String riskType;



}
