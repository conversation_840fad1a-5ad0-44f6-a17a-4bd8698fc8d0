<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- for more information, see the documentation of this POM: http://maven.apache.org/pom/asf/ -->
  <groupId>org.apache</groupId>
  <artifactId>apache</artifactId>
  <version>32</version>
  <packaging>pom</packaging>

  <name>The Apache Software Foundation</name>
  <description>
    The Apache Software Foundation provides support for the Apache community of open-source software projects.
    The Apache projects are characterized by a collaborative, consensus based development process, an open and
    pragmatic software license, and a desire to create high quality software that leads the way in its field.
    We consider ourselves not simply a group of projects sharing a server, but rather a community of developers
    and users.
  </description>
  <url>https://www.apache.org/</url>
  <organization>
    <name>The Apache Software Foundation</name>
    <url>https://www.apache.org/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <mailingLists>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://mail-archives.apache.org/mod_mbox/www-announce/</archive>
    </mailingList>
  </mailingLists>

  <modules>
    <module>docs</module>
  </modules>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-apache-parent.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-apache-parent.git</developerConnection>
    <url>https://github.com/apache/maven-apache-parent/tree/${project.scm.tag}</url>
    <tag>apache-32</tag>
  </scm>

  <distributionManagement>
    <repository>
      <id>${distMgmtReleasesId}</id>
      <name>${distMgmtReleasesName}</name>
      <url>${distMgmtReleasesUrl}</url>
    </repository>
    <snapshotRepository>
      <id>${distMgmtSnapshotsId}</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <distMgmtReleasesId>apache.releases.https</distMgmtReleasesId>
    <distMgmtReleasesName>Apache Release Distribution Repository</distMgmtReleasesName>
    <distMgmtReleasesUrl>https://repository.apache.org/service/local/staging/deploy/maven2</distMgmtReleasesUrl>
    <distMgmtSnapshotsId>apache.snapshots.https</distMgmtSnapshotsId>
    <distMgmtSnapshotsName>Apache Development Snapshot Repository</distMgmtSnapshotsName>
    <distMgmtSnapshotsUrl>https://repository.apache.org/content/repositories/snapshots</distMgmtSnapshotsUrl>
    <organization.logo>https://www.apache.org/images/asf_logo_wide_2016.png</organization.logo>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <sourceReleaseAssemblyDescriptor>source-release</sourceReleaseAssemblyDescriptor>
    <gpg.useagent>true</gpg.useagent>
    <minimalMavenBuildVersion>3.6.3</minimalMavenBuildVersion>
    <minimalJavaBuildVersion>1.8</minimalJavaBuildVersion>
    <maven.compiler.source>${maven.compiler.target}</maven.compiler.source>
    <maven.compiler.target>7</maven.compiler.target><!-- use version numbers without the "1." prefix (supported since javac 5) -->
    <surefire.version>3.2.5</surefire.version><!-- for surefire, failsafe and surefire-report -->
    <assembly.tarLongFileMode>posix</assembly.tarLongFileMode>

    <version.maven-fluido-skin>1.11.2</version.maven-fluido-skin>
    <project.build.outputTimestamp>2024-04-13T16:31:25Z</project.build.outputTimestamp>

    <version.apache-rat-plugin>0.16.1</version.apache-rat-plugin>
    <version.apache-resource-bundles>1.5</version.apache-resource-bundles>
    <version.checksum-maven-plugin>1.11</version.checksum-maven-plugin>
    <version.maven-antrun-plugin>3.1.0</version.maven-antrun-plugin>
    <version.maven-assembly-plugin>3.7.1</version.maven-assembly-plugin>
    <version.maven-clean-plugin>3.3.2</version.maven-clean-plugin>
    <version.maven-checkstyle-plugin>3.3.1</version.maven-checkstyle-plugin>
    <version.maven-compiler-plugin>3.13.0</version.maven-compiler-plugin>
    <version.maven-dependency-plugin>3.6.1</version.maven-dependency-plugin>
    <version.maven-deploy-plugin>3.1.1</version.maven-deploy-plugin>
    <version.maven-ear-plugin>3.3.0</version.maven-ear-plugin>
    <version.maven-enforcer-plugin>3.4.1</version.maven-enforcer-plugin>
    <version.maven-gpg-plugin>3.2.3</version.maven-gpg-plugin>
    <version.maven-help-plugin>3.4.0</version.maven-help-plugin>
    <version.maven-install-plugin>3.1.1</version.maven-install-plugin>
    <version.maven-invoker-plugin>3.6.1</version.maven-invoker-plugin>
    <version.maven-jar-plugin>3.4.0</version.maven-jar-plugin>
    <version.maven-javadoc-plugin>3.6.3</version.maven-javadoc-plugin>
    <version.maven-plugin-tools>3.12.0</version.maven-plugin-tools>
    <version.maven-project-info-reports-plugin>3.5.0</version.maven-project-info-reports-plugin>
    <version.maven-release-plugin>3.0.1</version.maven-release-plugin>
    <version.maven-remote-resources-plugin>3.2.0</version.maven-remote-resources-plugin>
    <version.maven-resources-plugin>3.3.1</version.maven-resources-plugin>
    <version.maven-scm-plugin>2.0.1</version.maven-scm-plugin>
    <version.maven-scm-publish-plugin>3.2.1</version.maven-scm-publish-plugin>
    <version.maven-shade-plugin>3.5.2</version.maven-shade-plugin>
    <version.maven-site-plugin>3.12.1</version.maven-site-plugin>
    <version.maven-source-plugin>3.3.1</version.maven-source-plugin>
    <version.maven-surefire>${surefire.version}</version.maven-surefire>
    <version.maven-war-plugin>3.4.0</version.maven-war-plugin>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <version>${version.maven-plugin-tools}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Repository</name>
      <url>https://repository.apache.org/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Repository</name>
      <url>https://repository.apache.org/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </pluginRepository>
  </pluginRepositories>

  <build>
    <pluginManagement>
      <plugins>
        <!-- set versions of common plugins for reproducibility, ordered alphabetically -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${version.maven-antrun-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${version.maven-assembly-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${version.maven-clean-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${version.maven-checkstyle-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${version.maven-compiler-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${version.maven-dependency-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${version.maven-deploy-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-ear-plugin</artifactId>
          <version>${version.maven-ear-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${version.maven-enforcer-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${version.maven-surefire}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${version.maven-gpg-plugin}</version>
          <configuration>
            <gpgArguments>
              <arg>--digest-algo=SHA512</arg>
            </gpgArguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-help-plugin</artifactId>
          <version>${version.maven-help-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${version.maven-install-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>${version.maven-invoker-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${version.maven-jar-plugin}</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${version.maven-javadoc-plugin}</version>
          <configuration>
            <notimestamp>true</notimestamp><!-- avoid noise for svn/gitpubsub -->
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${version.maven-plugin-tools}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-report-plugin</artifactId>
          <version>${version.maven-plugin-tools}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${version.maven-project-info-reports-plugin}</version>
          <configuration>
            <pluginManagementExcludes>
              <exclude>org.eclipse.m2e:lifecycle-mapping</exclude>
            </pluginManagementExcludes>
          </configuration>
        </plugin>
        <!-- START SNIPPET: release-plugin-configuration -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${version.maven-release-plugin}</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy</goals>
            <releaseProfiles>apache-release</releaseProfiles>
          </configuration>
        </plugin>
        <!-- END SNIPPET: release-plugin-configuration -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>${version.maven-remote-resources-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${version.maven-resources-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-plugin</artifactId>
          <version>${version.maven-scm-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${version.maven-scm-publish-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${version.maven-site-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${version.maven-source-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${version.maven-surefire}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${version.maven-surefire}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${version.maven-war-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${version.maven-shade-plugin}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${version.apache-rat-plugin}</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <!-- We want to package up license resources in the JARs produced -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <id>process-resource-bundles</id>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <resourceBundles>
                <resourceBundle>org.apache.apache.resources:apache-jar-resource-bundle:${version.apache-resource-bundles}</resourceBundle>
              </resourceBundles>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-maven-version</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>${minimalMavenBuildVersion}</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>enforce-java-version</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>${minimalJavaBuildVersion}</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <!-- we have dedicated module for documentation, so skip site plugin at all -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <inherited>false</inherited>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <!-- START SNIPPET: release-profile -->
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <!-- Create a source-release artifact that contains the fully buildable
               project directory source structure. This is the artifact which is
               the official subject of any release vote. -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>${version.apache-resource-bundles}</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>${sourceReleaseAssemblyDescriptor}</descriptorRef>
                  </descriptorRefs>
                  <tarLongFileMode>${assembly.tarLongFileMode}</tarLongFileMode>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- calculate checksums of source release for Apache dist area -->
          <plugin>
            <groupId>net.nicoulaj.maven.plugins</groupId>
            <artifactId>checksum-maven-plugin</artifactId>
            <version>${version.checksum-maven-plugin}</version>
            <executions>
              <execution>
                <id>source-release-checksum</id>
                <goals>
                  <goal>artifacts</goal>
                </goals>
                <!-- execute prior to maven-gpg-plugin:sign due to https://github.com/nicoulaj/checksum-maven-plugin/issues/112 -->
                <phase>post-integration-test</phase>
                <configuration>
                  <algorithms>
                    <algorithm>SHA-512</algorithm>
                  </algorithms>
                  <!-- https://maven.apache.org/apache-resource-bundles/#source-release-assembly-descriptor -->
                  <includeClassifiers>source-release</includeClassifiers>
                  <excludeMainArtifact>true</excludeMainArtifact>
                  <csvSummary>false</csvSummary>
                  <!-- attach SHA-512 checksum as well to upload to Maven Staging Repo, 
                       as this eases uploading from stage to dist and doesn't do harm in Maven Central -->
                  <attachChecksums>true</attachChecksums>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- We want to sign the artifact, the POM, and all attached artifacts (except for SHA-512 checksum) -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-release-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- END SNIPPET: release-profile -->
    <profile>
      <id>jdk9+</id>
      <activation>
        <!-- this does not support toolchains, https://issues.apache.org/jira/browse/MNG-6943 -->
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <!-- https://maven.apache.org/plugins/maven-compiler-plugin/examples/set-compiler-release.html, affects m-compiler-p and m-javadoc-p -->
        <maven.compiler.release>${maven.compiler.target}</maven.compiler.release>
      </properties>
    </profile>
    <profile>
      <id>only-eclipse</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <!-- Disable execution of some plugins in m2e (https://www.eclipse.org/m2e/documentation/m2e-execution-not-covered.html) -->
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <version>1.0.0</version><!-- as this is an artificial artifact only use in a profile (https://bugs.eclipse.org/bugs/show_bug.cgi?id=367870#c18) -->
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <pluginExecution>
                      <!-- no native m2e support yet (https://issues.apache.org/jira/browse/MRRESOURCES-85) -->
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-remote-resources-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>process</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>
