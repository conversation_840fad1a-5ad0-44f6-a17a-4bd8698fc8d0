package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestAttachmentDTO;
import com.chinasie.orion.domain.entity.NcfFormpurchaseRequestAttachment;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestAttachmentVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * NcfFormpurchaseRequestAttachment 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
public interface NcfFormpurchaseRequestAttachmentService extends OrionBaseService<NcfFormpurchaseRequestAttachment> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormpurchaseRequestAttachmentVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestAttachmentDTO
     */
    boolean create(List<NcfFormpurchaseRequestAttachmentDTO> ncfFormpurchaseRequestAttachmentDTOs) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestAttachmentDTO
     */
    Boolean edit(NcfFormpurchaseRequestAttachmentDTO ncfFormpurchaseRequestAttachmentDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormpurchaseRequestAttachmentVO> pages(Page<NcfFormpurchaseRequestAttachmentDTO> pageRequest) throws Exception;

    /**
     * 根据编号查询附件
     * <p>
     * * @param code
     */
    List<NcfFormpurchaseRequestAttachmentVO> getAttachmentsByCode(String code);

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormpurchaseRequestAttachmentVO> vos) throws Exception;
}
