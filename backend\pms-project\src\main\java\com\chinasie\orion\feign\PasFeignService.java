package com.chinasie.orion.feign;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.pas.DemandTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.EcrDTO;
import com.chinasie.orion.domain.dto.pas.QuestionTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.RiskTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectRiskStatisticsDTO;
import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.dto.QualityStepDTO;
import com.chinasie.orion.feign.vo.BusinessOpportunityVO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/13 10:38
 */
@FeignClient(name = "pas", path = "",configuration = FeignConfig.class)
@Lazy
public interface PasFeignService {

    /**
     * 添加风险属性值
     * @param riskTypeAttributeValueDTOList
     * @return
     * @throws Exception
     */
    @PostMapping("/risk-type-attribute-value")
    ResponseDTO<List<RiskTypeAttributeValueDTO>> addRiskTypeAttributeValue(@RequestBody List<RiskTypeAttributeValueDTO> riskTypeAttributeValueDTOList) throws Exception;

    /**
     * 添加需求属性值
     * @param demandTypeAttributeValueDTOList
     * @return
     * @throws Exception
     */
    @PostMapping("/demand-type-attribute-value")
    ResponseDTO<List<DemandTypeAttributeValueDTO>> addDemandTypeAttributeValue(@RequestBody List<DemandTypeAttributeValueDTO> demandTypeAttributeValueDTOList) throws Exception;

    /**
     * 添加问题属性值
     * @param questionTypeAttributeValueDTOList
     * @return
     * @throws Exception
     */
    @PostMapping("/question-type-attribute-value")
    ResponseDTO<List<QuestionTypeAttributeValueDTO>> addQuestionTypeAttributeValue(@RequestBody List<QuestionTypeAttributeValueDTO> questionTypeAttributeValueDTOList) throws Exception;

    /**
     * 获取类型及属性值
     * @param typeId
     * @param demandId
     * @return
     * @throws Exception
     */
    @GetMapping("/demand-type/attribute-value")
    ResponseDTO<TypeAndTypeAttrValueVO> getDemandTypeAndAttributeValues(@RequestParam("typeId") String typeId, @RequestParam("demandId") String demandId) throws Exception;

    /**
     * 获取类型及属性值
     * @param typeId
     * @param questionId
     * @return
     * @throws Exception
     */
    @GetMapping("/question-type/attribute-value")
    ResponseDTO<TypeAndTypeAttrValueVO> getQuestionTypeAndAttributeValues(@RequestParam("typeId") String typeId, @RequestParam("questionId") String questionId) throws Exception;

    /**
     * 获取类型及属性值
     * @param typeId
     * @param riskId
     * @return
     * @throws Exception
     */
    @GetMapping("/risk-type/attribute-value")
    ResponseDTO<TypeAndTypeAttrValueVO> getRiskTypeAndAttributeValues(@RequestParam("typeId") String typeId, @RequestParam("riskId") String riskId) throws Exception;

    /**
     * 删除属性值
     * @param riskIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/risk-type-attribute-value/byRiskIds")
    ResponseDTO<Boolean> deleteTypeAttributeValueByRiskIds(@RequestBody List<String> riskIds) throws Exception;

    /**
     * 删除属性值
     * @param demandIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/demand-type-attribute-value/byDemandIds")
    ResponseDTO<Boolean> deleteTypeAttributeValueByDemandIds(@RequestBody List<String> demandIds) throws Exception;

    /**
     * 删除属性值
     * @param questionIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/question-type-attribute-value/byQuestionIds")
    ResponseDTO<Boolean> deleteTypeAttributeValueByQuestionIds(@RequestBody List<String> questionIds) throws Exception;


    /**
     * 通过风险删除风险目录与风险的关系
     * @param riskIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/risk-dir-to-risk-management/byToIds")
    ResponseDTO<Boolean> deleteRiskDirToRiskManagementByRiskIds(@RequestBody List<String> riskIds) throws Exception;

    /**
     * 通过需求删除需求目录与需求的关系
     * @param demandIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/demand-dir-to-demand-management/byToIds")
    ResponseDTO<Boolean> deleteDemandDirToDemandManagementByDemandIds(@RequestBody List<String> demandIds) throws Exception;

    /**
     * 通过问题删除问题目录与问题的关系
     * @param questionIds
     * @return
     * @throws Exception
     */
    @DeleteMapping("/question-dir-to-question-management/byToIds")
    ResponseDTO<Boolean> deleteQuestionDirToQuestionManagementBQuestionIds(@RequestBody List<String> questionIds) throws Exception;

    /**
     * 通过ids获取风险类型
     * @param ids
     * @return
     * @throws Exception
     */
    @PostMapping("/risk-type/list/ids")
    ResponseDTO<List<SimpleVo>> getRiskTypeByIds(@RequestBody List<String> ids) throws Exception;

    /**
     * 通过ids获取需求类型
     * @param ids
     * @return
     * @throws Exception
     */
    @PostMapping("/demand-type/list/ids")
    ResponseDTO<List<SimpleVo>> getDemandTypeByIds(@RequestBody List<String> ids) throws Exception;



    /**
     * 通过ids获取问题类型
     * @param ids
     * @return
     * @throws Exception
     */
    @PostMapping("/question-type/list/ids")
    ResponseDTO<List<SimpleVo>> getQuestionTypeByIds(@RequestBody List<String> ids) throws Exception;


    /**
     * 添加
     *
     * @param ecrDTO
     * @return
     */
    @ApiOperation("添加")
    @RequestMapping(value = "/ecr", method = {RequestMethod.POST})
    ResponseDTO<String> add(@RequestBody EcrDTO ecrDTO);


    @RequestMapping(value = "/lead-management/getDeclareLeadPages", method = RequestMethod.POST)
    ResponseDTO<Page<LeadManagementVO>> getDeclareLeadPages(@RequestBody Page<LeadManagementDTO> pageRequest) throws Exception;


    @RequestMapping(value = "/lead-management/getDeclareLeadList", method = RequestMethod.POST)
    ResponseDTO<List<LeadManagementVO>> getDeclareLeadList(@RequestBody List<String> ids) throws Exception;

    @RequestMapping(value = "/lead-management-to-sci-apply", method = RequestMethod.POST)
    ResponseDTO<String> add(@RequestBody LeadManagementToSciApplyDTO leadManagementToSciApplyDTO);

    @ApiOperation(value = "列表")
    @RequestMapping(value = "/projectContract/list/byIds", method = RequestMethod.POST)
    ResponseDTO<List<ProjectContractVO>> listByIds(@RequestBody List<String> ids) throws Exception;

    @RequestMapping(value = "/contractToPurchaseOrderInfo/purchaseOrder/{id}", method = RequestMethod.GET)
    ResponseDTO<List<String>> getPurchaseOrderIds(@PathVariable(value = "id") String id) throws Exception;

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/contractPayNode/list/byIds", method = RequestMethod.POST)
     ResponseDTO<List<ContractPayNodeVO>> getContractPayNodeVO(@RequestBody List<String> ids) throws Exception;

    @ApiOperation(value = "支付状态变更")
    @RequestMapping(value = "/contractPayNode/status/change", method = RequestMethod.POST)
    ResponseDTO<Boolean> statusChange(@RequestBody ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO) throws Exception;


    @ApiOperation(value = "删除")
    @RequestMapping(value = "/lead-management-to-sci-apply/sciRemove/{fromId}", method = RequestMethod.DELETE)
    ResponseDTO<Boolean> removeSCI(@PathVariable("fromId") String fromId, @RequestBody List<String> toIds) throws Exception;

    @ApiOperation(value = "根据项目Id获取合同")
    @RequestMapping(value = "/projectContract/list/{projectId}", method = RequestMethod.GET)
    public ResponseDTO<List<ProjectContractVO>> listByProjectId(@PathVariable(value = "projectId") String projectId) throws Exception;

    @PostMapping(value = "/risk-management/getRiskManagementStatisticPage")
    public ResponseDTO<Page<RiskManagementVO>> getRiskManagementStatisticPage(@RequestBody Page<ProjectRiskStatisticsDTO> pageRequest) throws Exception;


    @PostMapping(value="/relation-to-im")
    ResponseDTO<Boolean> addRelationToIm(@RequestBody RelationDTO relationDTO);

    @DeleteMapping(value="/relation-to-im/batch/{fromId}")
    ResponseDTO<Boolean> removeListByFromId(@PathVariable("fromId")  String fromId,@RequestBody List<String> toIdList);


    @RequestMapping(value = "/interOutTrialBasicData/getUsePage", method = RequestMethod.POST)
    ResponseDTO<Page<InterOutTrialBasicDataVO>> getUsePage(@RequestBody Page<InterOutTrialBasicDataDTO> pageRequest) ;
    @PostMapping(value = "/qualityStep/List/keyword")
    ResponseDTO<List<QualityStepVO>> list(@RequestBody List<String> ids, @RequestParam(value = "keyword") String keyword);



    @RequestMapping(value = "/qualityStep/page", method = RequestMethod.POST)
    ResponseDTO<Page<QualityStepVO>> pages(@RequestBody Page<QualityStepDTO> pageRequest);

    @ApiOperation(value = "根据线索Id获取线索")
    @RequestMapping(value = "/lead-management/simple/list", method = RequestMethod.POST)
    ResponseDTO<List<Map<String,Object>>> listByClueIds(@RequestBody List<String> numberList) throws Exception;

    @ApiOperation(value = "分页查询所有线索")
    @RequestMapping(value = "/lead-management/pool/pages", method = RequestMethod.POST)
    ResponseDTO<Page<Map<String,Object>>> getCluePages(@RequestBody Page<Map<String,Object>> pageRequest);

    @ApiOperation(value = "分页查询预测的线索")
    @RequestMapping(value = "/lead-management/predicted/pages",method = RequestMethod.POST)
    ResponseDTO<Page<Map<String,Object>>> getCluePredictedPages(@RequestBody Page<Map<String,Object>> pageRequest);


    /**
     * 根据id获取商机数据
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/businessOpportunity/List/byIds", method = RequestMethod.POST)
    ResponseDTO<List<BusinessOpportunityVO>> getListByIds(@RequestBody List<String> ids);

    @ApiOperation("通过ids获取风险类型")
    @PostMapping(value = "/risk-type/list/ids")
    ResponseDTO<List<SimpleVO>> getTypeByIds(@RequestBody List<String> ids) throws Exception;

    // 根据id获取线索响应数据
    @RequestMapping(value = "/lead-management-conversion/getVOByIds", method = RequestMethod.POST)
    public ResponseDTO<List<LeadManagementConversionVO>> getVOByIds(@RequestBody List<String> ids);
}
