module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: ['plugin:vue/vue3-recommended', 'airbnb-base'],
  parserOptions: {
    ecmaVersion: 'latest',
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    'import/extensions': 'off',
    'import/no-unresolved': 'off',
    'import/prefer-default-export': 'off',
    'import/no-absolute-path': 'off',
    'no-undef': 'off',
    'no-unused-expressions': 'off',
    'guard-for-in': 'off',
    'no-param-reassign': 'off',
    'array-callback-return': 'off',
    'import/no-extraneous-dependencies': 'off',
    'no-shadow': 'off',
    'no-unused-vars': 'off',
    'max-len': 'off',
    'no-use-before-define': 'off',
    'no-useless-escape': 'off',
    'consistent-return': 'off',
    'no-nested-ternary': 'off',
    'implicit-arrow-linebreak': 'off',
    'class-methods-use-this': 'off',
    'no-unsafe-optional-chaining': 'off',
    'no-underscore-dangle': 'off',
    'no-promise-executor-return': 'off',
    'no-redeclare': 'off',
    'no-spaced-func': 'off',
    'func-call-spacing': 'off',
    'no-plusplus': 'off',
    'no-continue': 'off',
    'import/newline-after-import': 'off',
    'default-case': 'off',
    'no-case-declarations': 'off',
    'no-restricted-globals': 'off',
    'symbol-description': 'off',
    'default-param-last': 'off',
    'vue/multi-word-component-names': 'off',
    radix: 'off',
    'no-restricted-syntax': 'off',
    'no-eval': 'off',
    'vue/no-reserved-component-names': 'off',
    'no-empty': 'off',
    'prefer-destructuring': 'off',
    'no-return-assign': 'off',
    'no-unreachable': 'off',
    'eol-last': 'off',
    'no-return-await': 'off',
    'prefer-promise-reject-errors': 'off',
    'prefer-const': 'off',
    'vue/attribute-hyphenation': 'off',
    'no-console': ['error', { allow: ['error', 'info'] }],
    'linebreak-style': ['off', 'windows'],
    'import/no-mutable-exports': 'off',
    'vue/v-on-event-hyphenation': 'off',
    'object-property-newline': [
      'warn',
      {
        allowAllPropertiesOnSameLine: false,
      },
    ],
    'array-bracket-newline': ['warn', { multiline: true }],
    'array-element-newline': ['warn'],
    'no-trailing-spaces': 'error', // 可自动修复
    'no-multiple-empty-lines': ['error', { max: 1 }], // 可自动修复
    'vue/html-indent': ['error', 2], // 2 空格缩进
  },
};
