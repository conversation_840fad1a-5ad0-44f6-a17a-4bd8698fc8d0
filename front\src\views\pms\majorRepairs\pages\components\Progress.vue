<script setup lang="ts">
import { Empty } from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const detailsData: Record<string, any> = inject('detailsData');

const tableHeads: Array<{
  key: string,
  name: string,
  class?: string
}> = [
  {
    key: 'NPRN',
    name: '待准备',
  },
  {
    key: 'ASGN',
    name: '已分配',
  },
  {
    key: 'INPL',
    name: '准备中',
  },
  {
    key: 'PLND',
    name: '已准备',
  },
  {
    key: 'RPLN',
    name: '重新准备',
  },
  {
    key: 'APPV',
    name: '已批准',
    class: 'bg1',
  },
  {
    key: 'SCHD',
    name: '已计划',
    class: 'bg1',
  },
  {
    key: 'RTW',
    name: '已下达',
    class: 'bg2',
  },
  {
    key: 'WIP',
    name: '执行中',
    class: 'bg2',
  },
  {
    key: 'CSR',
    name: '工作完成',
    class: 'bg3',
  },
  {
    key: 'CPL',
    name: '关闭',
    class: 'bg3',
  },
  {
    key: 'REJ',
    name: '拒绝',
    class: 'bg3',
  },
];

const list: Ref<any[]> = ref([]);
const loading = ref<boolean>(false);

async function getData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage/develop/list').fetch({
      repairRound: detailsData?.repairRound,
    }, '', 'GET');
    list.value = result || [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getData();
});
</script>

<template>
  <div
    v-loading="loading"
    class="table-container"
  >
    <table>
      <thead>
        <tr>
          <th
            scope="col"
            rowspan="3"
          >
            中心名称
          </th>
          <th
            scope="col"
            colspan="5"
          >
            作业准备
          </th>
          <th
            scope="col"
            colspan="2"
            class="bg1"
          >
            准备完成
          </th>
          <th
            scope="col"
            colspan="2"
            class="bg2"
          >
            作业实施
          </th>
          <th
            scope="col"
            class="bg3"
            colspan="3"
          >
            作业关闭
          </th>
        </tr>

        <tr>
          <th
            v-for="item in tableHeads"
            :key="item.key"
            scope="col"
            :class="item.class"
          >
            {{ item.key }}
          </th>
        </tr>
        <tr>
          <th
            v-for="item in tableHeads"
            :key="item.key"
            scope="col"
            :class="item.class"
          >
            {{ item.name }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="item in list"
          :key="item.toString()"
        >
          <td>{{ item.rspDeptName }}</td>
          <td
            v-for="table in tableHeads"
            :key="table.key"
            :class="table.class"
          >
            {{ item?.jobStatusCountList?.find(v => v.jobStatusKey === table.key)?.count }}
          </td>
        </tr>
      </tbody>
    </table>
    <div
      v-if="list.length===0"
      class="empty-wrap"
    >
      <Empty />
    </div>
  </div>
</template>

<style scoped lang="less">
.table-container {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;

  table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    font-size: 14px;
    letter-spacing: 1px;

    thead {
      th {
        border: 2px solid #fff;
        background-color: #eee;
        padding: 10px 0;
      }
    }

    tbody {
      td {
        text-align: center;
        padding: 10px 0;
        background-color: #eee;
        border: 2px solid #fff;
      }
    }
  }
}

.bg1 {
  background-color: rgb(214, 255, 235) !important;
}

.bg2 {
  background-color: rgb(235, 235, 255) !important;
}

.bg3 {
  background-color: rgb(255, 192, 192) !important;
}

.empty-wrap {
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
