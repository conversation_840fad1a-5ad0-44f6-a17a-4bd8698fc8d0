package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCyclePhaseDTO;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProjectLifeCyclePhaseService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import java.util.List;

/**
 * <p>
 * ProjectLifeCycle 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@RestController
@RequestMapping("/projectLifeCycle")
@Api(tags = "全生命周期")
public class ProjectLifeCycleController {

    @Autowired
    private ProjectLifeCyclePhaseService projectLifeCyclePhaseService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据", type = "全生命周期", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectLifeCyclePhaseDTO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectLifeCyclePhaseDTO rsp = projectLifeCyclePhaseService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 阶段设置
     *
     * @param projectSchemeIdList
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "阶段设置")
    @RequestMapping(value = "/phaseSetting", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】阶段设置【{{#projectSchemeIdList.toString()}}】", type = "全生命周期", subType = "阶段设置", bizNo = "{{#projectId}}")
    public ResponseDTO<Boolean> phaseSetting(@RequestBody List<String> projectSchemeIdList, @RequestParam("projectId") String projectId) throws Exception {
        Boolean rsp =  projectLifeCyclePhaseService.phaseSetting(projectSchemeIdList, projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectLifeCyclePhaseDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/phase", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了阶段", type = "全生命周期", subType = "编辑", bizNo = "{{#projectLifeCyclePhaseDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectLifeCyclePhaseDTO projectLifeCyclePhaseDTO) throws Exception {
        Boolean rsp = projectLifeCyclePhaseService.edit(projectLifeCyclePhaseDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 全生命周期阶段选择模板
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "全生命周期阶段选择模板")
    @RequestMapping(value = "/phase/template", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】全生命周期阶段选择模板【{{#templateId}】", type = "全生命周期", subType = "全生命周期阶段选择模板", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@RequestParam("id") String id, @RequestParam("templateId") String templateId) throws Exception {
        Boolean rsp = projectLifeCyclePhaseService.setTemplate(id, templateId);
        return new ResponseDTO(rsp);
    }

    /**
     * 获取全生命周期
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取全生命周期")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取全生命周期", type = "全生命周期", subType = "获取全生命周期", bizNo = "{{#projectId}}")
    public ResponseDTO<ProjectLifeCycleVO> getProjectLifeCycleByProjectId(@RequestParam("projectId") String projectId) throws Exception {
        ProjectLifeCycleVO rsp = projectLifeCyclePhaseService.getProjectLifeCycleByProjectId(projectId);
        return new ResponseDTO(rsp);
    }
}
