package com.chinasie.orion.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.manager.TaskStatusProcessor;
import com.chinasie.orion.service.CollaborativeCompilationTaskService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 逾期
 * 临期
 * 正常
 * <p>
 * 已创建（待下发）
 * 已下发（进行中）
 * 已完成
 */
@Slf4j
@Component
@AllArgsConstructor
public class TaskStatusProcessorImpl implements TaskStatusProcessor {





    @Override
    public void statusHandle(List<CollaborativeCompilationTask> tasks) throws Exception {
        if (CollUtil.isEmpty(tasks)) {
            log.warn("不存在项目计划");
            return;
        }
        tasks.forEach(item -> overdue(item));
    }

    /**
     * 逾期处理
     * 临期处理
     *
     * @param task
     */
    private void overdue(CollaborativeCompilationTask task) {
        if (Status.PENDING.getCode().equals(task.getStatus()) || Status.FALLBACK.getCode().equals(task.getStatus())&&Status.CONFIRMED.getCode().equals(task.getStatus())){
            return;
        }
        if(Status.COMPLETE_APPROVAL.getCode().equals(task.getCircumstance())){
            return;
        }
        // 计划时间为空 不处理？
        if (Objects.isNull(task.getEndTime())){
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(task.getEndTime());
        calendar.add(Calendar.DATE,1);
        calendar.add(Calendar.SECOND,-1);
        Date endTime =  calendar.getTime();
        // 如果已经完成
        if (Status.FINISHED.getCode().equals(task.getStatus())) {
            // 但是实际时间 在 计划时间只有 任然设置为  已逾期 ，已完成
            if (Objects.nonNull(task.getActualEndTime()) && task.getActualEndTime().after(endTime)) {
                task.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode());
                return;
            }else{
                task.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode());
                return;
            }
        }
        if (Status.EXECUTING.getCode().equals(task.getStatus())&&new Date().after(endTime)) {
            task.setCircumstance(Status.CIRCUMSTANCE_OVERD.getCode());
            return;
        }
        long day = DateUtil.betweenDay(new Date(), endTime, true);
        if (Status.EXECUTING.getCode().equals(task.getStatus())&&day < 5) {
            task.setCircumstance(Status.CIRCUMSTANCE_NEAR.getCode());
            return;
        }
        if (Status.EXECUTING.getCode().equals(task.getStatus())) {
            task.setCircumstance(Status.CIRCUMSTANCE_NORMAL.getCode());
        }
    }

}
