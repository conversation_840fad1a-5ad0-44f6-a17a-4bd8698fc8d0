package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPurchaseSupplierInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:11:33
 */
@ApiModel(value = "ProjectPurchaseSupplierInfoVO对象", description = "项目采购供应商信息")
@Data
public class ProjectPurchaseSupplierInfoVO extends ObjectVO implements Serializable{

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String supplierName;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    private String contactEmail;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    private String purchaseId;

}
