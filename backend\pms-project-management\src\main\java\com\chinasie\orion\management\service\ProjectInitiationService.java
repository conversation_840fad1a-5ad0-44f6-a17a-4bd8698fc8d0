package com.chinasie.orion.management.service;


import com.chinasie.orion.domain.vo.MarketContractApiVO;
import com.chinasie.orion.management.domain.dto.ProjectInitiationDTO;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.vo.ProjectInitiationVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectInitiation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
public interface ProjectInitiationService extends OrionBaseService<ProjectInitiation> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectInitiationVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectInitiationDTO
     */
    String create(ProjectInitiationDTO projectInitiationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectInitiationDTO
     */
    Boolean edit(ProjectInitiationDTO projectInitiationDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectInitiationVO> pages(Page<ProjectInitiationDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectInitiationVO> vos) throws Exception;

    /**
     *  权限分页
     * @param pageRequest
     * @return
     */
    Page<ProjectInitiationVO> permissionPages(Page<ProjectInitiationDTO> pageRequest);

    /**
     * 项目关联立项分页
     * @param pageRequest
     * @return
     */
    Page<ProjectInitiationVO> pagePages(Page<ProjectInitiationDTO> pageRequest) throws Exception;

    /**
     * 保存项目关联立项关系
     * @param projectInitiationDTOs
     * @return
     */
    Boolean projectSave(List<ProjectInitiationDTO> projectInitiationDTOs);

    /**
     * 查询合同信息
     * @param projectNumbers
     * @return
     */
    List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers);
}
