<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, openFile, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal, Popover } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { openFormDrawer } from '../utils';
import CertificateEdit from './CertificateEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const powerCodePrefix: string = inject('powerCodePrefix');
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  height: 300,
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '证书类型',
      dataIndex: 'certificateTypeName',
    },
    {
      title: '证书名称',
      dataIndex: 'certificateName',
      customRender({ record, text }) {
        if (isPower(`${powerCodePrefix}_container_02_02_button_03`, powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.certificateId),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '证书级别',
      dataIndex: 'certificateLevelName',
      width: 80,
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
    },
    {
      title: '证书编号',
      dataIndex: 'number',
    },
    {
      title: '发证日期',
      dataIndex: 'obtainDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '有效期至',
      dataIndex: 'validToDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '初次取证日期',
      dataIndex: 'initialCertificationDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '证书信息',
      dataIndex: 'fileVOList',
      customRender({ text }) {
        if (isPower(`${powerCodePrefix}_container_02_02_button_04`, powerData.value)) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn',
              onClick() {
                openFile(item);
              },
            }, item.name)),
          });
        }
        return '';
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/basic-user-certificate').fetch({
    ...params,
    query: {
      userCode: detailsData?.userCode,
    },
  }, 'page', 'POST'),
};

function navDetails(id: string) {
  router.push({
    name: 'PMSCertificateStandardsDetails',
    params: {
      id,
    },
  });
}

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增证书',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: `${powerCodePrefix}_container_02_01_button_01`,
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: `${powerCodePrefix}_container_02_01_button_02`,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(CertificateEdit, { userCode: detailsData?.userCode }, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower(`${powerCodePrefix}_container_02_02_button_01`, powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower(`${powerCodePrefix}_container_02_02_button_02`, powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(CertificateEdit, record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/basic-user-certificate').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      v-if="detailsData.userStatus === '在职'"
      #toolbarLeft
    >
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.powerCode]"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template
      v-if="detailsData.userStatus === '在职'"
      #actions="{record}"
    >
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
