<template>
  <Layout3
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    :onMenuChange="contentTabsChange"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}/{{ projectInfo?.revId }}
        </div>
      </div>
    </template>
    <template #header-right>
      <BasicTableAction
        :actions="rightBtnActions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectInfo.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <div class="deliverableContent">
      <Summarize
        v-if="actionId===3333331 && isPower('JFW_container_02', powerData)"
        :id="father.id"
        :project-id="father.projectId"
      />
      <Document
        v-if="actionId===3333332 && isPower('JFW_container_03', powerData)"
        :id="projectInfo.id"
        :project-id="projectInfo.projectId"
      />
      <Process
        v-if="actionId===3333333"
        :id="projectInfo.id"
      />
      <Colophon
        v-if="actionId===3333334 && isPower('JFW_container_05', powerData)"
        :id="projectInfo.id"
        :revKey="projectInfo.revKey"
      />
      <PushModel />
      <!--    变更管理-->
      <ChangeApply
        v-if="actionId===3333335"
        :formId="projectInfo?.id"
        :projectId="projectInfo?.projectId"
        :addApi="addChangeApply"
        ecrDirName="项目"
        :showBtn="showBtn"
      />
    </div>
    <!--    编辑-->
    <CostCenterDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  Ref, ref, computed, getCurrentInstance, onMounted, provide, reactive, toRefs, watchEffect, nextTick,
} from 'vue';
import { WorkflowAction, WorkflowProps } from 'lyra-workflow-component-vue3';
import {
  isPower, Layout3, useProjectPower, BasicTableAction, ITableActionItem, useDrawer,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import Summarize from './summarize/index.vue';
import Document from './document/index.vue';
import Colophon from './colophon/index.vue';
import Flow from './flow/index.vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import Api from '/@/api';
import { ChangeApply } from '../../ChangeApply';
import { setTitleByRootTabsKey } from '/@/utils';
import CostCenterDrawer from './summarize/costCenterDrawer.vue';
import Process from '/@/views/pms/components/Process.vue';

export default {
  name: 'Drawer',
  components: {
    CostCenterDrawer,
    PushModel,
    Layout3,
    Summarize,
    Colophon,
    Document,
    ChangeApply,
    BasicTableAction,
    WorkflowAction,
    Process,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const route = useRoute();
    const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
    const state = reactive({
      // father: props.data,
      father: route.query,
      actionId: 0,
      projectInfo: {},
      powerData: [],
      showBtn: computed(() => isPower('PMS_JFW_container_03_button_01', state.powerData)),
    });
    // state.powerData = inject('powerData');
    const state6 = reactive({
      tabsOption: [],
    });

    async function getForm() {
      await new Api('/pms')
        .fetch({ pageCode: 'PMS0031' }, `deliverable/${state.father.id}`)
        .then((res) => {
          state.projectInfo = res;
          state6.tabsOption = [];
          state.powerData = res?.detailAuthList ?? [];
          setTitleByRootTabsKey(route?.query?.rootTabsKey, res.name);
          processRef.value?.setProps({
            businessData: res,
          });
          isPower('PMS_JFW_container_01', state.powerData) && state6.tabsOption.push({
            name: '基本信息',
            id: 3333331,
          });
          // isPower('PMS_JFW_container_02', state.powerData) && state6.tabsOption.push({
          //   name: '交付物文件',
          //   id: 3333332,
          // });
          // isPower('PMS_JFW_container_03', state.powerData) && state6.tabsOption.push({
          //   name: '变更管理',
          //   id: 3333335,
          // });
          // isPower('PMS_JFW_container_04', state.powerData) && state6.tabsOption.push({
          //   name: '流程',
          //   id: 3333333,
          // });
          // isPower('PMS_JFW_container_05', state.powerData) && state6.tabsOption.push({
          //   name: '版本记录',
          //   id: 3333334,
          // });
          if (!state.actionId) {
            state.actionId = state6.tabsOption[0].id || 0;
          }
        })
        .catch((_) => {
        });
    }

    const processRef: Ref = ref();
    const processViewRef: Ref = ref();
    const rightBtnActions = computed(() => [
      {
        text: '编辑',
        isShow: computed(() => isPower('PMS_JFW_container_01_button_01', state.powerData)),
        icon: 'sie-icon-bianji',
        onClick() {
          openEditDrawer(true, {
            type: 'edit',
            formData: state.projectInfo,
          });
        },
      },
      (processRef.value?.isAdd ? {
        text: '添加流程',
        icon: 'sie-icon-tianjiaxinzeng',
        onClick() {
          processRef.value?.onAddTemplate({
            messageUrl: route.fullPath,
          });
        },
      } : undefined),

    ].filter((item) => item));

    onMounted(async () => {
      await getForm();
    });

    function onClose() {
      state.father.visible = false;
    }

    function onSubmit() {
    }

    function contentTabsChange(index) {
      state.actionId = index.id;
    }

    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    );
    provide(
      'formData',
      computed(() => state.projectInfo),
    );

    provide(
      'deliverDetailsInfo',
      computed(() => state.projectInfo),
    );

    function getDetails() {
      return state.projectInfo;
    }
    async function updatePage() {
      await getForm();
    }
    const workflowProps = computed(() => ({
      Api,
      businessData: state.projectInfo,
      afterEvent: (type, props) => {
        processViewRef.value?.init();
      },
    }));
    provide('getForm', getForm);
    provide('getDetails', getDetails);
    const addChangeApply = (id, params) => new Api('/pms').fetch(params, `deliverable/change/${id}`, 'POST');

    return {
      ...toRefs(state),
      ...toRefs(state6),
      onClose,
      onSubmit,
      contentTabsChange,
      isPower,
      addChangeApply,
      rightBtnActions,
      processRef,
      workflowProps,
      updatePage,
      registerEditDrawer,
    };
  },
};
</script>

<style scoped lang="less">

.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;
  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}

.deliverableContent {
  height: 100%
}
</style>
