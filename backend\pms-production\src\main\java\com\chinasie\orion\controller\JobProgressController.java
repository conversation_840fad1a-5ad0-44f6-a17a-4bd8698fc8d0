package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.excel.JobProgressParamDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobProgress;
import com.chinasie.orion.domain.dto.JobProgressDTO;
import com.chinasie.orion.domain.vo.JobProgressVO;
import com.chinasie.orion.service.JobProgressService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobProgress 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:48:03
 */
@RestController
@RequestMapping("/job-progress")
@Api(tags = "作业工作进展")
public class  JobProgressController  {

    @Autowired
    private JobProgressService jobProgressService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业工作进展】详情", type = "JobProgress", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobProgressVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobProgressVO rsp = jobProgressService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobProgressDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业工作进展】数据", type = "JobProgress", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobProgressDTO jobProgressDTO) throws Exception {
        String rsp =  jobProgressService.create(jobProgressDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobProgressDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业工作进展】数据", type = "JobProgress", subType = "编辑", bizNo = "{{#jobProgressDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobProgressDTO jobProgressDTO) throws Exception {
        Boolean rsp = jobProgressService.edit(jobProgressDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业工作进展】数据", type = "JobProgress", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobProgressService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业工作进展】数据", type = "JobProgress", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobProgressService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业工作进展】分页数据", type = "JobProgress", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobProgressVO>> pages(@RequestBody Page<JobProgressDTO> pageRequest) throws Exception {
        Page<JobProgressVO> rsp =  jobProgressService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业工作进展导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "JobProgress", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobProgressService.downloadExcelTpl(response);
    }

    @ApiOperation("作业工作进展导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "JobProgress", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = jobProgressService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业工作进展导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "JobProgress", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobProgressService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业工作进展导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "JobProgress", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobProgressService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业工作进展导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "JobProgress", subType = "导出数据", bizNo = "")
    public void exportByExcel(@Validated @RequestBody JobProgressParamDTO progressParamDTO, HttpServletResponse response) throws Exception {
        jobProgressService.exportByExcel(progressParamDTO, response);
    }
}
