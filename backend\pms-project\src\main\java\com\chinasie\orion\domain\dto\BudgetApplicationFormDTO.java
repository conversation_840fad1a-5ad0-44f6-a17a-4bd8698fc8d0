package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetApplicationFrom DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetApplicationFromDTO对象", description = "预算申请单")
@Data
@ExcelIgnoreUnannotated
public class BudgetApplicationFormDTO extends ObjectDTO implements Serializable {

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题")
    @ExcelProperty(value = "申请标题 ", index = 1)
    private String name;

    /**
     * 申请预算单编码
     */
    @ApiModelProperty(value = "申请预算单编码")
    @ExcelProperty(value = "申请预算单编码 ", index = 2)
    private String number;

    /**
     * 申请预算金额
     */
    @ApiModelProperty(value = "申请预算金额")
    @ExcelProperty(value = "申请预算金额 ", index = 3)
    private BigDecimal budgetMoney;

    /**
     * 申请预算条目数
     */
    @ApiModelProperty(value = "申请预算条目数")
    @ExcelProperty(value = "申请预算条目数 ", index = 4)
    private Integer budgetCount;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    @ExcelProperty(value = "立项id ", index = 5)
    private String approvalId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 6)
    private String projectId;

    @ApiModelProperty(value = "调整数据")
    private List<BudgetApplicationDTO> budgetApplicationDTOS;


}
