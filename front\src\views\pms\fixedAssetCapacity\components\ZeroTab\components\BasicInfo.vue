<script setup lang="ts">
import { BasicCard, UploadList } from 'lyra-component-vue3';
import {
  computed,
  inject, reactive, ref,
} from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { useGlobalFixedAssetCapacityState } from '/@/views/pms/fixedAssetCapacity/components/hooks/globalState';
import { map } from 'lodash-es';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const { updateFixedAssetCapacityKey } = useGlobalFixedAssetCapacityState();
const baseInfoProps = reactive({
  list: [
    {
      label: '资产代码',
      field: 'code',
    },
    {
      label: '产品编码',
      field: 'productCode',
    },
    {
      label: '工具状态',
      field: 'toolStatusName',
    },
    {
      label: '检定维护周期',
      field: 'maintenanceCycle',
    },
    {
      label: '资产名称',
      field: 'name',
    },
    {
      label: '数量',
      field: 'numCount',
    },
    {
      label: '成本中心名称',
      field: 'costCenterName',
    },
    {
      label: '规格型号',
      field: 'spModel',
    },
    {
      label: '是否需要检定',
      field: 'isNeedVerification',
      isBoolean: true,
    },
    {
      label: '下次检定日期',
      field: 'nextVerificationTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '责任人工号',
      field: 'rspUserNumber',
    },
    {
      label: '责任人姓名',
      field: 'rspUserName',
    },
    {
      label: '使用人工号',
      field: 'useUserNumber',
    },
    {
      label: '使用人姓名',
      field: 'useUserName',
    },
    {
      label: '资产存放地',
      field: 'storageLocationName',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
const fileList = computed(() => detailsData?.fileList ?? []);

async function handleSaveApi(file) {
  try {
    const result = new Api('/pms/fixed-assets/upload/fixedAssets').fetch({
      dataId: detailsData?.id,
      fileDTOList: file,
    }, '', 'PUT').then((res) => {
      updateFixedAssetCapacityKey();
    });
  } catch (e) {}
}
async function handleDeleteApi(row) {
  await handleBatchDeleteApi({
    keys: [row.id],
    rows: [row],
  });
}
async function handleBatchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return new Api('/pms/fixed-assets').fetch(map(rows, (item) => item.id), '', 'DELETE').then((res) => updateFixedAssetCapacityKey());
}
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
  <BasicCard
    title="计量证书信息"
    :isBorder="true"
  >
    <div class="file-list">
      <UploadList
        :height="500"
        :isFileEdit="false"
        :saveApi="handleSaveApi"
        :listData="fileList"
        :deleteApi="handleDeleteApi"
        :batchDeleteApi="handleBatchDeleteApi"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.file-list{
  height: 500px;
  overflow: hidden;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>
