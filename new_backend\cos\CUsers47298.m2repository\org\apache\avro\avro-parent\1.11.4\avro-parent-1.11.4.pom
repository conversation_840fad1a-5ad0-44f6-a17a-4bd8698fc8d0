<?xml version="1.0" encoding="UTF-8"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.avro</groupId>
    <artifactId>avro-toplevel</artifactId>
    <version>1.11.4</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>

  <artifactId>avro-parent</artifactId>
  <packaging>pom</packaging>

  <name>Apache Avro Java</name>
  <url>https://avro.apache.org</url>
  <description>Avro parent Java project</description>

  <properties>
    <main.basedir>${project.parent.basedir}</main.basedir>

    <!-- version properties for dependencies -->
    <hadoop.version>3.3.6</hadoop.version>
    <jackson-bom.version>2.14.3</jackson-bom.version>
    <servlet-api.version>4.0.1</servlet-api.version>
    <jetty.version>9.4.54.v20240208</jetty.version>
    <jopt-simple.version>5.0.4</jopt-simple.version>
    <junit5.version>5.10.2</junit5.version>
    <netty.version>4.1.107.Final</netty.version>
    <protobuf.version>3.25.3</protobuf.version>
    <thrift.version>0.16.0</thrift.version>
    <slf4j.version>1.7.36</slf4j.version>
    <reload4j.version>1.2.25</reload4j.version>
    <snappy.version>********</snappy.version>
    <velocity.version>2.3</velocity.version>
    <maven-core.version>3.3.9</maven-core.version>
    <ant.version>1.10.14</ant.version>
    <commons-cli.version>1.6.0</commons-cli.version>
    <commons-compress.version>1.26.2</commons-compress.version>
    <commons-text.version>1.10.0</commons-text.version>
    <tukaani.version>1.9</tukaani.version>
    <mockito.version>4.11.0</mockito.version>
    <hamcrest.version>2.2</hamcrest.version>
    <grpc.version>1.62.2</grpc.version>
    <zstd-jni.version>1.5.5-5</zstd-jni.version>
    <!-- version properties for plugins -->
    <archetype-plugin.version>3.2.1</archetype-plugin.version>
    <bundle-plugin-version>5.1.9</bundle-plugin-version>
    <cyclonedx-maven-plugin.version>2.7.11</cyclonedx-maven-plugin.version>
    <exec-plugin.version>3.2.0</exec-plugin.version>
    <file-management.version>3.1.0</file-management.version>
    <javacc-plugin.version>3.0.3</javacc-plugin.version>
    <javacc.version>7.0.13</javacc.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
  </properties>

  <modules>
    <module>android</module>
    <module>avro</module>
    <module>compiler</module>
    <module>maven-plugin</module>
    <module>ipc</module>
    <module>ipc-jetty</module>
    <module>ipc-netty</module>
    <module>trevni</module>
    <module>tools</module>
    <module>mapred</module>
    <module>protobuf</module>
    <module>thrift</module>
    <module>archetypes</module>
    <module>grpc</module>
    <module>integration-test</module>
    <module>perf</module>
  </modules>

  <build>
    <!-- defines the default plugin configuration that all child projects inherit from.
      Like dependencyManagement, this provides configuration, version, and other
      parameters if the plugins are used by child projects -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${checkstyle-plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>${checkstyle.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <compilerArgs>
              <arg>-parameters</arg>
              <!--<arg>-Werror</arg>-->
              <arg>-Xlint:all</arg>
              <!-- Override options warnings to support cross-compilation -->
              <arg>-Xlint:-options</arg>
              <!-- Temporary lint overrides, to be removed over time. -->
              <arg>-Xlint:-rawtypes</arg>
              <arg>-Xlint:-serial</arg>
              <arg>-Xlint:-unchecked</arg>
              <!--<arg>-Xlint:sunapi</arg>-->
              <!--<arg>-XDenableSunApiLintControl</arg>-->
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <!--We want to be able to resuse the test-jars for mapred
              to test avro-tool
              see https://maven.apache.org/guides/mini/guide-attached-tests.html
          -->
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <archive>
              <manifest>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <includes>
              <!-- Avro naming convention for JUnit tests -->
              <include>**/Test**</include>
            </includes>
            <excludes>
              <!-- A few inner classes are not to be tested -->
              <exclude>**/*$*</exclude>
              <!-- exclude the generated classes under apache.avro.test, some of
                these match **/Test** and are not JUnit tests -->
              <exclude>**/apache/avro/test/**</exclude>
            </excludes>
            <enableAssertions>false</enableAssertions>
            <trimStackTrace>false</trimStackTrace>
            <runOrder>random</runOrder>
            <parallel>all</parallel>
            <useUnlimitedThreads>true</useUnlimitedThreads>
            <!--<perCoreThreadCount>true</perCoreThreadCount>-->

            <!-- TestSpecificCompiler instantiates a Java compiler to test output results,
                 this does not work with a manifest-only-jar to set the classpath for the javac.
                 This may cause problems on some platforms.
                 See https://maven.apache.org/plugins/maven-surefire-plugin/examples/class-loading.html
                 for more information. -->
            <useManifestOnlyJar>false</useManifestOnlyJar>
            <!-- configures unit test standard error and standard out to go to a file per test
                 rather than the console. -->
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <failIfNoTests>false</failIfNoTests>
            <argLine>-Xmx1000m</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <configuration>
            <links>
              <link>https://docs.oracle.com/javase/8/docs/api/</link>
              <link>https://hadoop.apache.org/docs/current/api/</link>
            </links>
            <tagletArtifacts>
              <tagletArtifact>
                <groupId>org.apache.maven.plugin-tools</groupId>
                <artifactId>maven-plugin-tools-javadoc</artifactId>
                <version>${plugin-tools-javadoc.version}</version>
              </tagletArtifact>
            </tagletArtifacts>
            <excludePackageNames>org.apache.avro.compiler.idl,*.internal</excludePackageNames>
            <quiet>true</quiet>
            <encoding>UTF-8</encoding>
            <additionalJOption>-Xdoclint:none</additionalJOption>
            <detectOfflineLinks>false</detectOfflineLinks>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.javacc.plugin</groupId>
          <artifactId>javacc-maven-plugin</artifactId>
          <version>${javacc-plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>net.java.dev.javacc</groupId>
              <artifactId>javacc</artifactId>
              <version>${javacc.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>${exec-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${spotless-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.cyclonedx</groupId>
          <artifactId>cyclonedx-maven-plugin</artifactId>
          <version>${cyclonedx-maven-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${bundle-plugin-version}</version>
        <extensions>true</extensions>
        <inherited>true</inherited>
        <configuration>
          <instructions>
            <Bundle-Name>${project.name}</Bundle-Name>
            <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
            <Export-Package>${osgi.export}</Export-Package>
            <Import-Package>${osgi.import}</Import-Package>
            <DynamicImport-Package>${osgi.dynamic.import}</DynamicImport-Package>
            <Private-Package>${osgi.private}</Private-Package>
            <Require-Bundle>${osgi.bundles}</Require-Bundle>
            <Bundle-Activator>${osgi.activator}</Bundle-Activator>
          </instructions>
          <supportedProjectTypes>
            <supportedProjectType>bundle</supportedProjectType>
          </supportedProjectTypes>
          <unpackBundle>true</unpackBundle>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <consoleOutput>true</consoleOutput>
          <configLocation>checkstyle.xml</configLocation>
          <suppressionsLocation>suppressions.xml</suppressionsLocation>
        </configuration>
        <!-- Runs by default in the verify phase  (mvn verify or later in the build cycle)
             the 'check' goal will fail the build if it does not pass.  "mvn checkstyle:check"
             will do this alone, or "mvn checkstyle:checkstyle" will report but not break  -->
        <executions>
          <execution>
            <id>checkstyle-check</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <configuration>
          <java>
            <eclipse>
              <!-- Avro uses Sun's java code style conventions with 2 spaces, this is a modified version of
                   the eclipse formatter -->
              <file>${main.basedir}/lang/java/eclipse-java-formatter.xml</file>
              <version>4.19.0</version>
            </eclipse>
            <!-- Temporarily disabled for JDK 16+ builds -->
            <!--<removeUnusedImports/>-->
            <replaceRegex>
              <name>Remove wildcard imports</name>
              <searchRegex>import\s+[^\*\s]+\*;(\r\n|\r|\n)</searchRegex>
              <replacement>$1</replacement>
            </replaceRegex>
          </java>
        </configuration>
        <executions>
          <execution>
            <!-- Runs in compile phase to fail fast in case of formatting issues.-->
            <id>spotless-check</id>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.cyclonedx</groupId>
        <artifactId>cyclonedx-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>makeBom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
  </reporting>

  <profiles>
    <profile>
      <id>m2e</id>
      <activation>
        <property><name>m2e.version</name></property>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <version>1.0.0</version>
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>com.diffplug.spotless</groupId>
                        <artifactId>spotless-maven-plugin</artifactId>
                        <goals>
                          <goal>check</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-plugin-plugin</artifactId>
                        <goals>
                          <goal>helpmojo</goal>
                          <goal>descriptor</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <versionRange>[$${exec-plugin.version},)</versionRange>
                        <goals>
                          <goal>exec</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>javacc-maven-plugin</artifactId>
                        <versionRange>[${javacc-plugin.version},)</versionRange>
                        <goals>
                          <goal>javacc</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-maven-plugin</artifactId>
                        <versionRange>[${project.version},)</versionRange>
                        <goals>
                          <goal>protocol</goal>
                          <goal>idl-protocol</goal>
                          <goal>schema</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore />
                      </action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-antrun-plugin</artifactId>
              <version>${maven-antrun-plugin.version}</version>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>interop-data-test</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <includes>
                <include>**/DataFileInteropTest*</include>
              </includes>
              <excludes>
              </excludes>
              <enableAssertions>false</enableAssertions>
              <forkCount>1</forkCount>
              <reuseForks>true</reuseForks>
              <redirectTestOutputToFile>false</redirectTestOutputToFile>
              <systemPropertyVariables>
                <test.dir>../../../build/interop/data/</test.dir>
              </systemPropertyVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>mac</id>
      <activation>
        <os>
        <family>mac</family>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <systemPropertyVariables>
                <!-- avro-mapred will fail in tests on mac without this -->
                <java.security.krb5.realm>OX.AC.UK</java.security.krb5.realm>
                <java.security.krb5.kdc>kdc0.ox.ac.uk:kdc1.ox.ac.uk</java.security.krb5.kdc>
              </systemPropertyVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- This profile is used on tests to validate backwards compatibility with Hadoop 2 -->
    <profile>
      <id>hadoop2</id>
      <properties>
        <hadoop.version>2.10.1</hadoop.version>
      </properties>
    </profile>
  </profiles>

  <!-- dependencyManagement can be used to define dependency versions, scopes, and
    excludes to be shared by child projects. Child projects will not inherit these dependencies,
    rather they inherit the properties of the below dependencies (such as version) if
    the dependency is specified in the child. -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
          <groupId>org.eclipse.jetty</groupId>
          <artifactId>jetty-servlet</artifactId>
          <version>${jetty.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <dependency>
          <groupId>javax.servlet</groupId>
          <artifactId>javax.servlet-api</artifactId>
          <version>${servlet-api.version}</version>
      </dependency>
      <dependency>
        <groupId>net.sf.jopt-simple</groupId>
        <artifactId>jopt-simple</artifactId>
        <version>${jopt-simple.version}</version>
      </dependency>
      <!-- hadoop's execution environment provides its own jars. -->
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-client</artifactId>
        <version>${hadoop.version}</version>
        <exclusions>
          <exclusion> <!-- GPL -->
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xerial.snappy</groupId>
        <artifactId>snappy-java</artifactId>
        <version>${snappy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${commons-compress.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-core</artifactId>
        <version>${grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-stub</artifactId>
        <version>${grpc.version}</version>
        <exclusions>
          <exclusion> <!-- GPL -->
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-netty</artifactId>
        <version>${grpc.version}</version>
        <exclusions>
          <exclusion> <!-- GPL -->
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.tukaani</groupId>
        <artifactId>xz</artifactId>
        <version>${tukaani.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.luben</groupId>
        <artifactId>zstd-jni</artifactId>
        <version>${zstd-jni.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- dependencies defines dependencies that all child projects share. Child projects
    will inherit these dependencies directly, and can opt out if necessary with <excludes> -->
  <dependencies>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>${junit5.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${junit5.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>${slf4j.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

</project>
