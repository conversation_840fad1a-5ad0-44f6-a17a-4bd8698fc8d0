package com.chinasie.orion.service.impl;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.entity.AuthorizeJobPostRequirement;
import com.chinasie.orion.domain.dto.AuthorizeJobPostRequirementDTO;
import com.chinasie.orion.domain.entity.JobPostAuthorize;
import com.chinasie.orion.domain.vo.AuthorizeJobPostRequirementVO;
import com.chinasie.orion.domain.vo.job.JobPostAuthorizeInfoVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.service.AuthorizeJobPostRequirementService;
import com.chinasie.orion.repository.AuthorizeJobPostRequirementMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CertificateInfoService;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.JobPostAuthorizeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * AuthorizeJobPostRequirement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:47
 */
@Service
@Slf4j
public class AuthorizeJobPostRequirementServiceImpl extends  OrionBaseServiceImpl<AuthorizeJobPostRequirementMapper, AuthorizeJobPostRequirement>   implements AuthorizeJobPostRequirementService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private CertificateInfoService certificateInfoService;

    private JobPostAuthorizeService jobPostAuthorizeService;
    @Autowired
    public void setJobPostAuthorizeService(JobPostAuthorizeService jobPostAuthorizeService) {
        this.jobPostAuthorizeService = jobPostAuthorizeService;
    }


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  AuthorizeJobPostRequirementVO detail(String id,String pageCode) throws Exception {
        AuthorizeJobPostRequirement authorizeJobPostRequirement =this.getById(id);
        AuthorizeJobPostRequirementVO result = BeanCopyUtils.convertTo(authorizeJobPostRequirement,AuthorizeJobPostRequirementVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param authorizeJobPostRequirementDTO
     */
    @Override
    public  String create(AuthorizeJobPostRequirementDTO authorizeJobPostRequirementDTO) throws Exception {
        AuthorizeJobPostRequirement authorizeJobPostRequirement =BeanCopyUtils.convertTo(authorizeJobPostRequirementDTO,AuthorizeJobPostRequirement::new);
        this.save(authorizeJobPostRequirement);

        String rsp=authorizeJobPostRequirement.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param authorizeJobPostRequirementDTO
     */
    @Override
    public Boolean edit(AuthorizeJobPostRequirementDTO authorizeJobPostRequirementDTO) throws Exception {
        AuthorizeJobPostRequirement authorizeJobPostRequirement =BeanCopyUtils.convertTo(authorizeJobPostRequirementDTO,AuthorizeJobPostRequirement::new);

        this.updateById(authorizeJobPostRequirement);

        String rsp=authorizeJobPostRequirement.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AuthorizeJobPostRequirementVO> pages( Page<AuthorizeJobPostRequirementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AuthorizeJobPostRequirement> condition = new LambdaQueryWrapperX<>( AuthorizeJobPostRequirement. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AuthorizeJobPostRequirement::getCreateTime);


        Page<AuthorizeJobPostRequirement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AuthorizeJobPostRequirement::new));

        PageResult<AuthorizeJobPostRequirement> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AuthorizeJobPostRequirementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AuthorizeJobPostRequirementVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AuthorizeJobPostRequirementVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<AuthorizeJobPostRequirementVO> listByAuthorizeId(String authorizeId) throws Exception {
        LambdaQueryWrapperX<AuthorizeJobPostRequirement> condition = new LambdaQueryWrapperX<>( AuthorizeJobPostRequirement. class);
        condition.eq(AuthorizeJobPostRequirement::getAuthorizeManageId,authorizeId);
        List<AuthorizeJobPostRequirement> list =  this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<AuthorizeJobPostRequirementVO> requirementVOList= BeanCopyUtils.convertListTo(list,AuthorizeJobPostRequirementVO::new);
        this.setEveryName(requirementVOList);

        JobPostAuthorizeInfoVO jobPostAuthorizeInfoVO = jobPostAuthorizeService.getSimpleBy(authorizeId);

        if(Objects.isNull(jobPostAuthorizeInfoVO)){
            return  requirementVOList;
        }
        String baseCode= jobPostAuthorizeInfoVO.getBaseCode();
        String baseName = jobPostAuthorizeInfoVO.getBaseName();
        requirementVOList.forEach(item->{
            item.setBaseCode(baseCode);
            item.setBaseName(baseName);

        });
        return requirementVOList;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "岗位授权要求检验表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AuthorizeJobPostRequirementDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            AuthorizeJobPostRequirementExcelListener excelReadListener = new AuthorizeJobPostRequirementExcelListener();
        EasyExcel.read(inputStream,AuthorizeJobPostRequirementDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AuthorizeJobPostRequirementDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("岗位授权要求检验表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AuthorizeJobPostRequirement> authorizeJobPostRequirementes =BeanCopyUtils.convertListTo(dtoS,AuthorizeJobPostRequirement::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AuthorizeJobPostRequirement-import::id", importId, authorizeJobPostRequirementes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AuthorizeJobPostRequirement> authorizeJobPostRequirementes = (List<AuthorizeJobPostRequirement>) orionJ2CacheService.get("pmsx::AuthorizeJobPostRequirement-import::id", importId);
        log.info("岗位授权要求检验表导入的入库数据={}", JSONUtil.toJsonStr(authorizeJobPostRequirementes));

        this.saveBatch(authorizeJobPostRequirementes);
        orionJ2CacheService.delete("pmsx::AuthorizeJobPostRequirement-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AuthorizeJobPostRequirement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AuthorizeJobPostRequirement> condition = new LambdaQueryWrapperX<>( AuthorizeJobPostRequirement. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AuthorizeJobPostRequirement::getCreateTime);
        List<AuthorizeJobPostRequirement> authorizeJobPostRequirementes =   this.list(condition);

        List<AuthorizeJobPostRequirementDTO> dtos = BeanCopyUtils.convertListTo(authorizeJobPostRequirementes, AuthorizeJobPostRequirementDTO::new);

        String fileName = "岗位授权要求检验表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AuthorizeJobPostRequirementDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AuthorizeJobPostRequirementVO> vos)throws Exception {
        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode(DictConts.REQUIREMENT_TYPE);

        Map<String,String> numberToName=new HashMap<>();
        for (DictValueVO dictValueVO : dictListByCode) {
            numberToName.put(dictValueVO.getNumber(),dictValueVO.getDescription());
        }
        List<DictValueVO> dictListByCode1 = dictRedisHelper.getDictListByCode(DictConts.TRAIN_TYPE_DICT);
        for (DictValueVO dictValueVO : dictListByCode1) {
            numberToName.put(dictValueVO.getNumber(),dictValueVO.getDescription());
        }

        Map<String,String> cerNumberToName= certificateInfoService.allMap();
        vos.forEach(vo->{
            vo.setTypeName(numberToName.getOrDefault(vo.getType(),""));
            vo.setTrainName(numberToName.getOrDefault(vo.getTrainNumber(),""));
            vo.setCertificateName(cerNumberToName.getOrDefault(vo.getCertificateNumber(),""));
        });
    }

    @Override
    public void saveOrUpdateList(List<String> authorizeIdList,List<AuthorizeJobPostRequirement> all) {
        if(CollectionUtils.isEmpty(all)){
            return;
        }
        LambdaQueryWrapperX<AuthorizeJobPostRequirement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(AuthorizeJobPostRequirement.class);
        lambdaQueryWrapperX.in(AuthorizeJobPostRequirement::getAuthorizeManageId,authorizeIdList);
        this.remove(lambdaQueryWrapperX);
        this.saveBatch(all);
    }


    public static class AuthorizeJobPostRequirementExcelListener extends AnalysisEventListener<AuthorizeJobPostRequirementDTO> {

        private final List<AuthorizeJobPostRequirementDTO> data = new ArrayList<>();

        @Override
        public void invoke(AuthorizeJobPostRequirementDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AuthorizeJobPostRequirementDTO> getData() {
            return data;
        }
    }


}
