<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <!--
   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
  -->

  <name>LZ4 and xxHash</name>
  <description>Java ports and bindings of the LZ4 compression algorithm and the xxHash hashing algorithm</description>
  <url>https://github.com/lz4/lz4-java</url>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.lz4</groupId>
  <artifactId>lz4-java</artifactId>
  <packaging>jar</packaging>
  <version>1.7.1</version>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>git://github.com/lz4/lz4-java.git</url>
    <connection>https://github.com/lz4/lz4-java</connection>
  </scm>
  <developers>
    <developer>
      <id>jpountz</id>
      <name>Adrien Grand</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>odaira</id>
      <name>Rei Odaira</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>com.carrotsearch.randomizedtesting</groupId>
      <artifactId>junit4-ant</artifactId>
      <version>2.5.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
