<template>
  <OrionTable :options="tableOptions" />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
export default defineComponent({
  name: 'TodoListTable',
  components: {
    OrionTable,
  },
  setup() {
    const tableOptions = {
      canResize: false,
      maxHeight: 300,
      showToolButton: false,
      showSmallSearch: false,
      showIndexColumn: false,
      columns: [
        {
          title: '待办类型',
          align: 'left',
          width: 150,
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '待办内容',
          dataIndex: 'content',
        },
        {
          title: '接收时间',
          dataIndex: 'time',
          type: 'dateTime',
        },
        {
          title: '发起人',
          width: 130,
          dataIndex: 'user',
        },
      ],
    };

    return {
      tableOptions,
    };
  },
});
</script>

<style scoped></style>
