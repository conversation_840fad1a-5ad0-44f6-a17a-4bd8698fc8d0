package com.chinasie.orion.domain.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class RepairRoundAnd {

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "大修名称")
    private String repairName;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    @ApiModelProperty(value = "基地code")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    private String baseName;
}
