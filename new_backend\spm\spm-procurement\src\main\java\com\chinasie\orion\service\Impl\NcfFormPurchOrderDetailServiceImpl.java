package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.NcfFormPurchOrderDetailDTO;
import com.chinasie.orion.domain.entity.NcfFormPurchOrderCollect;
import com.chinasie.orion.domain.entity.NcfFormPurchOrderDetail;
import com.chinasie.orion.domain.vo.NcfFormPurchOrderDetailVO;
import com.chinasie.orion.domain.vo.NumMoneyVO;
import com.chinasie.orion.repository.NcfFormPurchOrderCollectMapper;
import com.chinasie.orion.repository.NcfFormPurchOrderDetailMapper;
import com.chinasie.orion.service.NcfFormPurchOrderDetailService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfFormPurchOrderDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:56:15
 */
@Service
@Slf4j
public class NcfFormPurchOrderDetailServiceImpl extends OrionBaseServiceImpl<NcfFormPurchOrderDetailMapper, NcfFormPurchOrderDetail> implements NcfFormPurchOrderDetailService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private NcfFormPurchOrderCollectMapper ncfFormPurchOrderCollectMapper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormPurchOrderDetailVO detail(String id, String pageCode) throws Exception {
        NcfFormPurchOrderDetail ncfFormPurchOrderDetail = this.getById(id);
        NcfFormPurchOrderDetailVO result = BeanCopyUtils.convertTo(ncfFormPurchOrderDetail, NcfFormPurchOrderDetailVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormPurchOrderDetailDTO
     */
    @Override
    public String create(NcfFormPurchOrderDetailDTO ncfFormPurchOrderDetailDTO) throws Exception {
        NcfFormPurchOrderDetail ncfFormPurchOrderDetail = BeanCopyUtils.convertTo(ncfFormPurchOrderDetailDTO, NcfFormPurchOrderDetail::new);
        this.save(ncfFormPurchOrderDetail);

        String rsp = ncfFormPurchOrderDetail.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormPurchOrderDetailDTO
     */
    @Override
    public Boolean edit(NcfFormPurchOrderDetailDTO ncfFormPurchOrderDetailDTO) throws Exception {
        NcfFormPurchOrderDetail ncfFormPurchOrderDetail = BeanCopyUtils.convertTo(ncfFormPurchOrderDetailDTO, NcfFormPurchOrderDetail::new);

        this.updateById(ncfFormPurchOrderDetail);

        String rsp = ncfFormPurchOrderDetail.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormPurchOrderDetailVO> pages(Page<NcfFormPurchOrderDetailDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormPurchOrderDetail> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //下单时间
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormPurchOrderDetail::getOrderTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        condition.orderByDesc(NcfFormPurchOrderDetail::getCreateTime);


        Page<NcfFormPurchOrderDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormPurchOrderDetail::new));

        PageResult<NcfFormPurchOrderDetail> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormPurchOrderDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormPurchOrderDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormPurchOrderDetailVO::new);
        if(CollectionUtils.isEmpty(vos)){
            return pageResult;
        }
        List<String> orderNumbers = vos.stream().map(NcfFormPurchOrderDetailVO :: getOrderNumber).collect(Collectors.toList());
        List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollects = ncfFormPurchOrderCollectMapper.selectList(NcfFormPurchOrderCollect :: getOrderNumber,orderNumbers);
        Map<String,String> orderCollectMap = ncfFormPurchOrderCollects.stream().filter(item -> item .getOrderPayDay() != null).collect(Collectors.toMap(NcfFormPurchOrderCollect :: getOrderNumber,NcfFormPurchOrderCollect :: getOrderPayDay));
        vos.forEach(item ->{
            item.setOrderPayDay(orderCollectMap.get(item.getOrderNumber()) == null ? "": orderCollectMap.get(item.getOrderNumber()).toString());
        });
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public NumMoneyVO getNumMoney(Page<NcfFormPurchOrderDetailDTO> pageRequest) {
        LambdaQueryWrapperX<NcfFormPurchOrderDetail> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormPurchOrderDetail::getCreateTime);
        String sql = "count(*) as number," +
                "sum(order_amount) as money";
        condition.select(sql);
        Map map = this.getMap(condition);
        NumMoneyVO vo = new NumMoneyVO();
        vo.setNumber(map.get("number") == null ? 0 : Integer.parseInt(map.get("number").toString()));
        vo.setMoney(map.get("money") == null ? 0 : Double.parseDouble(map.get("money").toString()));
        return vo;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商城集采订单（明细表）导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderDetailDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormPurchOrderDetailExcelListener excelReadListener = new NcfFormPurchOrderDetailExcelListener();
        EasyExcel.read(inputStream, NcfFormPurchOrderDetailDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormPurchOrderDetailDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商城集采订单（明细表）导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormPurchOrderDetail> ncfFormPurchOrderDetailes = BeanCopyUtils.convertListTo(dtoS, NcfFormPurchOrderDetail::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NcfFormPurchOrderDetail-import::id", importId, ncfFormPurchOrderDetailes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormPurchOrderDetail> ncfFormPurchOrderDetailes = (List<NcfFormPurchOrderDetail>) orionJ2CacheService.get("pms::NcfFormPurchOrderDetail-import::id", importId);
        log.info("商城集采订单（明细表）导入的入库数据={}", JSONUtil.toJsonStr(ncfFormPurchOrderDetailes));

        this.saveBatch(ncfFormPurchOrderDetailes);
        orionJ2CacheService.delete("pms::NcfFormPurchOrderDetail-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NcfFormPurchOrderDetail-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormPurchOrderDetailDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormPurchOrderDetail> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderDetail.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //下单时间
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormPurchOrderDetail::getOrderTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        condition.orderByDesc(NcfFormPurchOrderDetail::getCreateTime);
        List<NcfFormPurchOrderDetail> ncfFormPurchOrderDetailes = this.list(condition);

        List<NcfFormPurchOrderDetailDTO> dtos = BeanCopyUtils.convertListTo(ncfFormPurchOrderDetailes, NcfFormPurchOrderDetailDTO::new);

        String fileName = "商城集采订单（明细表）数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderDetailDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormPurchOrderDetailVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @Async
    public void updateOrderPayDay() {
        //【订单状态】待收货/待确认服务-待评价
        //根据订单状态更新订单待支付
        //最开始数据同步的时候，如果订单状态是待收货/待确认服务，计算【订单最后一次交货时间】与当前系统日期的差值，如果订单状态一直是待收货/待确认服务，则【订单待支付】就一直累计，每天+1
        // 直到状态变更为待支付，订单待支付的值就不再变
        //主表，子表逻辑一致
        List<NcfFormPurchOrderDetail> updateLists = new ArrayList<>();
        List<NcfFormPurchOrderDetail> collectes = this.list(new LambdaQueryWrapperX<>(NcfFormPurchOrderDetail.class));
        collectes.forEach(vo -> {
            if (("待收货").equals(vo.getOrderState()) || ("待确认").equals(vo.getOrderState())) {
                //如果订单待支付为null，表示第一次同步数据,设置为【订单最后一次交货时间】与当前系统日期的差值
                if (vo.getOrderPayDay() == null) {
                    LocalDate now = LocalDate.now();
                    if (vo.getTimeOfDelivery() != null) {
                        LocalDate deliveryDate = vo.getTimeOfDelivery().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        long daysBetween = ChronoUnit.DAYS.between(deliveryDate, now);
                        vo.setOrderPayDay(String.valueOf(daysBetween));
                    }
                } else {
                    //如果订单待支付不null，表示不是第一次同步数据，订单待支付在之前的基础上+1
                    vo.setOrderPayDay(new BigDecimal(vo.getOrderPayDay()).add(BigDecimal.ONE).toString());
                }
                updateLists.add(vo);
            }
            else{
                if(vo.getOrderPayDay() != null){
                    vo.setOrderPayDay("0");
                }
                if(("待评价").equals(vo.getOrderState())){
                    vo.setOrderPayDay("已完成");
                }
                updateLists.add(vo);
            }
        });

        //更新数据
        //由于数据量可能很大，改为批量提交，1000条数据提交一次
        //开启事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = updateLists.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<NcfFormPurchOrderDetail> subList = updateLists.subList(i, Math.min(i + batchSize, size));
                this.updateBatchById(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }


    public static class NcfFormPurchOrderDetailExcelListener extends AnalysisEventListener<NcfFormPurchOrderDetailDTO> {

        private final List<NcfFormPurchOrderDetailDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormPurchOrderDetailDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormPurchOrderDetailDTO> getData() {
            return data;
        }
    }


}
