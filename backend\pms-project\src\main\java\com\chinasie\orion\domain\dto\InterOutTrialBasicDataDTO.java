package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * InterOutTrialBasicData DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-29 15:50:29
 */
@ApiModel(value = "InterOutTrialBasicDataDTO对象", description = "内外部试验项目基础数据")
@Data
@ExcelIgnoreUnannotated
public class InterOutTrialBasicDataDTO extends ObjectDTO implements Serializable {


    /**
     * 试验项目
     */
    @ApiModelProperty(value = "试验项目")
    @ExcelProperty(value = "试验项目 ", index = 0)
    private String name;
    /**
     * 台数
     */
    @ApiModelProperty(value = "台数")
    @ExcelProperty(value = "台数 ", index = 1)
    private Integer num;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @ExcelProperty(value = "批次 ", index = 2)
    private Integer batch;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 3)
    private BigDecimal price;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位 ", index = 4)
    private String unit;

    /**
     * 设备参数
     */
    @ApiModelProperty(value = "设备参数")
    @ExcelProperty(value = "设备参数 ", index = 5)
    private String deviceParam;


}
