package com.chinasie.orion.management.constant;

public enum MileStoneTrackConfirmEnum {


    TRUE("true","是"),
    FALSE("false","否");
    ;

    private String code;
    private String desc;

    MileStoneTrackConfirmEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (MileStoneTrackConfirmEnum lt : MileStoneTrackConfirmEnum.values()) {
            if(lt.code.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

}
