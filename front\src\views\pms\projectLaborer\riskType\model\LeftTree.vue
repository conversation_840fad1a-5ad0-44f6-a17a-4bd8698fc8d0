<template>
  <div class="treeBox flex flex-ver">
    <div class="treeBox_title">
      <span class="treeBox_title_span">{{ titleName }}</span>
      <PlusCircleOutlined
        v-if="true"
        class="mr5"
        @click="addNode(0)"
      />
    </div>
    <AInputSearch
      v-model:value="searchValue"
      style="margin-bottom: 8px"
      placeholder="请输入名称或编码"
      @search="searchChange"
    />
    <div
      v-if="treeData?.length>0"
      class="treeContent123 flex-f1"
    >
      <BasicScrollbar>
        <ATree
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
          class="treeBoxContent"
          :field-names="{title:'name',key:'id'}"
          :expanded-keys="expandedKeys"
          @select="selectNode"
          @expand="expandNode"
        >
          <template #title="treeNode">
            <div
              class="treeNode123 flex-te"
              :class="{nodeType123:searchValue.length>0&&treeNode.name.indexOf(searchValue)>-1}"
              :title=" treeNode.name"
            >
              <span
                v-if="treeNode.icon"
                class="fa iconClass123"
                :class="treeNode.icon"
              />
              <span class="treeNodeName123">{{ treeNode.name }}</span>
            </div>
            <div class="operation123">
              <PlusCircleOutlined
                v-if="true"
                @click="addNode(treeNode)"
              />
              <ADropdown
                v-if="true"
                :trigger="['click'] "
                overlay-class-name="treeDropDown"
              >
                <MoreOutlined />
                <template #overlay>
                  <AMenu class="treeDropDown">
                    <AMenuItem key="0">
                      <a
                        class="aMenuItem123"
                        @click="deleteNode(treeNode)"
                      >删除</a>
                    </AMenuItem>
                  </AMenu>
                </template>
              </ADropdown>
            </div>
          </template>
        </ATree>
      </BasicScrollbar>
    </div>
    <a-empty
      v-else
      description="暂无数据"
      class="mt60"
    />
    <AEDrawer
      @register="DrawerR"
      @addSuccess="addSuccess"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs, watch,
} from 'vue';
import {
  PlusCircleOutlined,
  MoreOutlined,
} from '@ant-design/icons-vue';
import {
  Tree, Dropdown, Menu, Input, Modal, message, Empty,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { isPower, useDrawer, BasicScrollbar } from 'lyra-component-vue3';
import AEDrawer from './AEDrawer.vue';
import Api from '/@/api';
export default defineComponent({
  name: 'LeftTree',
  components: {
    ATree: Tree,
    ADropdown: Dropdown,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    PlusCircleOutlined,
    MoreOutlined,
    AInputSearch: Input.Search,
    AEDrawer,
    AEmpty: Empty,
    BasicScrollbar,
  },
  props: {
    reloadAll: {},
  },
  emits: ['addNode', 'selectChange'],
  setup(props, { emit }) {
    const [DrawerR, DrawerM] = useDrawer();
    const route = useRoute();
    const state:any = reactive({
      titleName: '风险类型管理',
      searchValue: '',
      selectedKeys: [],
      autoExpandParent: false,
      dataList: [],
      treeData: [],
      nodeData: {},
      expandedKeys: [],
      powerCode: {
        addCode: '',
        deleteCode: '',
      },
    });
    const searchChange = (val) => {
      getFormData();
    };
    const selectNode = (selectedKeys, { selected, node }) => {
      // console.log("----- selectedKeys -----", selectedKeys,selected,node)
      if (selectedKeys.length === 0) {
        state.selectedKeys = [node.id];
        return;
      }
      state.selectedKeys = selectedKeys;

      state.nodeData = node.dataRef;
    };
    onMounted(() => {
      getFormData();
    });
    async function getFormData() {
      // basicConfig.getDocumentTree(state.searchValue).then((res) => {
      //   state.treeData = res;
      //   if (state.treeData.length > 0) {
      //     state.selectedKeys = [state.treeData[0]?.id];
      //     state.nodeData = state.treeData[0];
      //   }
      // });
      state.treeData = await new Api(`/pms/projectPlan-type/tree?keyword=${state.searchValue}`).fetch('', '', 'GET');
      if (state.treeData.length > 0) {
        if (state.selectedKeys?.length < 1) {
          state.selectedKeys = [state.treeData[0]?.id];
          state.nodeData = state.treeData[0];
        }
      }
    }
    const addNode = (data) => {
      if (data === 0) {
        DrawerM.setDrawerProps({
          title: '添加风险类型',
        });
        DrawerM.openDrawer(true, ['add', { data }]);
        // emit('addNode', { parentId: 0, parentName: '全部' });
        // state.nodeData = {};
      } else {
        DrawerM.setDrawerProps({
          title: '添加风险类型',
        });
        const allP = getParentId(state.treeData, data.data.id);
        const showType = allP.reverse().map((item) => item.name).join('/');
        DrawerM.openDrawer(true, [
          'add',
          {
            data,
            showType,
          },
        ]);
        // state.nodeData = data.dataRef;
        // // let nodeList = findParent(data.dataRef.id, state.treeData);
        // emit('addNode', { parentId: data.dataRef.id, parentName: data.dataRef.name });
      }
    };
    function getParentId(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          return [list[i]];
        }
        if (list[i].children) {
          let node = getParentId(list[i].children, id);
          if (node !== undefined) {
            return node.concat(list[i]);
          }
        }
      }
    }
    const deleteNode = (treeNode) => {
      let nodeData = treeNode.dataRef;
      Modal.confirm({
        title: '操作提示',
        content: '是否删除该节点',
        async onOk() {
          return await new Api(`/pms/projectPlan-type/${nodeData.id}`).fetch('', '', 'DELETE').then(() => {
            message.success('删除成功');
            let dataIndex;
            if (nodeData.parentId === '0') {
              for (let i = 0; i < state.treeData.length; i++) {
                if (state.treeData[i].id === nodeData.id) {
                  dataIndex = i;
                  break;
                }
              }
              state.treeData.splice(dataIndex, 1);
              state.nodeData = state.treeData.length > 0 ? state.treeData[0] : {};
              state.selectedKeys = state.treeData.length > 0 ? [state.treeData[0].id] : [];
            } else {
              let parantData = findParent(nodeData.id, state.treeData);
              for (let i = 0; i < parantData.children.length; i++) {
                if (parantData.children[i].id === nodeData.id) {
                  dataIndex = i;
                  break;
                }
              }
              parantData.children.splice(dataIndex, 1);
              state.nodeData = parantData.children.length > 0 ? parantData.children[0] : parantData;
              state.selectedKeys = parantData.children.length > 0 ? [parantData.children[0].id] : [parantData.id];
            }
            state.treeData = [...state.treeData];
          });
        },
      });
    };
    function findParent(id, data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.children && item.children.some((item1) => item1.id === id)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          let itemData = findParent(id, item.children);
          if (itemData) {
            return itemData;
          }
        }
      }
    }
    watch(
      () => state.selectedKeys,
      (val) => {
        emit('selectChange', state.nodeData);
      },
    );
    watch(
      () => props.reloadAll,
      (val) => {
        getFormData();
      },
    );

    const expandNode = (expandedKeys, { expanded: bool, node }) => {
      state.expandedKeys = expandedKeys;
    };
    function addSuccess() {
      getFormData();
    }
    return {
      ...toRefs(state),
      searchChange,
      addNode,
      selectNode,
      deleteNode,
      expandNode,
      isPower,
      DrawerR,
      addSuccess,
    };
  },
});
</script>
<style lang="less" scoped>
.treeBox{
  height: 100%;
  box-sizing: border-box;
  .iconClass123{
    font-size: 18px;
    vertical-align: middle;
    padding-right: 5px;
  }
  .treeNodeName123{
    vertical-align: middle;
  }
  .treeBox_title{
    padding-bottom: 15px;
    display: flex;
    justify-content: space-between;
    .treeBox_title_span{
      font-weight: 500;
      font-style: normal;
      font-size: 16px;
      color: #444B5E;
    }
    .anticon{
      font-size: 20px;
      margin-top: 3px;
      color: #444B5E;
      cursor: pointer;
    }
  }
  :deep(.treeBoxContent){
    .ant-tree-treenode{
      width:100%;
      padding: 5px 0px;
      position: relative;
      .ant-tree-switcher{
        height:30px;
        line-height:30px;
      }
      .ant-tree-node-content-wrapper{
        width: 100%;
        &:hover{
          background: none;
        }
      }
      &:hover{
        .operation123{
          display: block;
        }

        .treeNode123{
          color:~`getPrefixVar('primary-color')`;
        }
      }
    }
    .ant-tree-treenode-selected{
      background:~`getPrefixVar('primary-color-deprecated-f-12')`;
      .ant-tree-node-selected{
        background: none;
      }
      .operation123{
        display: block;
      }
      .treeNode123{
          color:~`getPrefixVar('primary-color')`;
      }
    }
  }
  .treeNode123{
    color:#444B5E;
    font-size: 14px;
    height:30px;
    line-height:30px;
    width: 160px;
  }
  .operation123{
    display: none;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px;
    font-size: 18px;
    color: #444B5E;
    z-index: 1000;
    .anticon{
      margin-left: 5px;
      cursor: pointer;
    }

  }
  .nodeType123{
    color: red;
  }
}
.treeDropDown{
  .aMenuItem123{
    display: block;
    width: 80px;
    color: #444B5E;
  }
}
</style>
