package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.PerformanceTemplateToIndicatorDTO;
import com.chinasie.orion.domain.entity.PerformanceTemplateToIndicator;
import com.chinasie.orion.domain.vo.PerformanceTemplateToIndicatorVO;
import com.chinasie.orion.domain.vo.PerformanceTemplateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PerformanceTemplateToIndicator 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
public interface PerformanceTemplateToIndicatorService extends OrionBaseService<PerformanceTemplateToIndicator> {

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;

    /**
     * 启用
     * <p>
     * * @param pageRequest
     */
    Boolean enable(List<String> ids);

    /**
     * 禁用
     * <p>
     * * @param pageRequest
     */
    Boolean disEnable(List<String> ids);

    /**
     * @Description: 添加指标
     * @param:
     * @return:
     * @date: 2024/3/26 20:37
     * <AUTHOR>
     */
    Boolean create(String templateId, List<PerformanceTemplateToIndicator> performanceTemplateDTO);
}
