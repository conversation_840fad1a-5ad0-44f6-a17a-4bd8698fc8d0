package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * MarketContract Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@TableName(value = "pms_market_contract")
@ApiModel(value = "MarketContractEntity对象", description = "市场合同")
@Data
public class MarketContract extends ObjectEntity implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "number")
    private String number;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "name")
    private String name;

    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同id")
    @TableField(value = "frame_contract_id")
    private String frameContractId;


    /**
     * 市场合同类型
     */
    @ApiModelProperty(value = "市场合同类型")
    @TableField(value = "contract_type")
    @FieldBind(dataBind = DictDataBind.class, type = "market_contract_type", target = "contractTypeName")
    private String contractType;

    /**
     * 市场合同类型名称
     */
    @ApiModelProperty(value = "市场合同类型名称")
    @TableField(exist = false)
    private String contractTypeName;

    /**
     * 报价单id
     */
    @ApiModelProperty(value = "报价单id")
    @TableField(value = "quote_id")
    private String quoteId;

    /**
     * 报价编号
     */
    @ApiModelProperty(value = "报价编号")
    @TableField(value = "quote_number")
    private String quoteNumber;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @TableField(value = "tech_rsp_user")
    @FieldBind(dataBind = UserDataBind.class, target = "techRspUserName")
    private String techRspUser;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    @TableField(exist = false)
    private String techRspUserName;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @TableField(value = "commerce_rsp_user")
    @FieldBind(dataBind = UserDataBind.class, target = "commerceRspUserName")
    private String commerceRspUser;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    @TableField(exist = false)
    private String commerceRspUserName;


    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @TableField(value = "tech_rsp_dept")
    @FieldBind(dataBind = DeptDataBind.class, target = "techRspDeptName")
    private String techRspDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    @TableField(exist = false)
    private String techRspDeptName;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @TableField(value = "contract_amt")
    private BigDecimal contractAmt;

    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "框架合同金额")
    @TableField(value = "frame_contract_amt")
    private BigDecimal frameContractAmt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    @FieldBind(dataBind = DictDataBind.class, type = "currency_type", target = "currencyName")
    private String currency;


    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    @TableField(exist = false)
    private String currencyName;


    /**
     * 关联交易审批
     */
    @ApiModelProperty(value = "关联交易审批")
    @TableField(value = "rel_trans_appr")
    private Boolean relTransAppr;

    /**
     * 交易审批单号
     */
    @ApiModelProperty(value = "交易审批单号")
    @TableField(value = "trans_appr_number")
    private String transApprNumber;

    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "交易审批id")
    @TableField(value = "trans_appr_id")
    private String transApprId;

    /**
     * 主要内容
     */
    @ApiModelProperty(value = "主要内容")
    @TableField(value = "content")
    private String content;

    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    @TableField(value = "quality_level")
    @FieldBind(dataBind = DictDataBind.class, type = "quality_level", target = "qualityLevelName")
    private String qualityLevel;

    /**
     * 质保等级名称
     */
    @ApiModelProperty(value = "质保等级名称")
    @TableField(exist = false)
    private String qualityLevelName;


    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    @TableField(value = "close_date")
    private Date closeDate;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    @TableField(value = "close_user_id")
    @FieldBind(dataBind = UserDataBind.class, target = "closeUserName")
    private String closeUserId;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    @TableField(exist = false)
    private String closeUserName;

    /**
     * 关闭方式
     */
    @ApiModelProperty(value = "关闭方式")
    @TableField(value = "close_type")
    private String closeType;

    /**
     * 是否需要采购
     */
    @ApiModelProperty(value = "是否需要采购")
    @TableField(value = "is_purchase")
    private Boolean isPurchase;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求Id")
    @TableField(value = "requirement_id")
    private String requirementId;

    /**
     * 合同签署人id
     */
    @ApiModelProperty(value = "合同签署人id")
    @TableField(value = "contract_sign_user_id")
    private String contractSignUserId;

    /**
     * 合同签署人名称
     */
    @ApiModelProperty(value = "合同签署人名称")
    @TableField(value = "contract_sign_user_name")
    private String contractSignUserName;

    /**
     * 客户id。pms_customer_info id
     */
    @ApiModelProperty(value = "客户id。pms_customer_info id")
    @TableField(value = "cust_person_id")
    private String custPersonId;

    @ApiModelProperty(value = "客户-客户关系。编码")
    @TableField(value = "cust_group_in_out")
    private String custGroupInOut;

    @ApiModelProperty(value = "客户-业务收入类型。编码")
    @TableField(value = "cust_bus_revenue_type")
    private String custBusRevenueType;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    @TableField(value = "cust_sale_bus_type")
    private String custSaleBusType;

    @ApiModelProperty(value = "所级负责人")
    @TableField(value = "office_leader")
    private String officeLeader;

    /**
     * 合同获取方式
     */
    @ApiModelProperty(value = "合同获取方式")
    @TableField(value = "contract_method")
    @FieldBind(dataBind = DictDataBind.class, type = "market_contract_method", target = "contractMethodName")
    private String contractMethod;

    /**
     * 合同获取方式名称
     */
    @ApiModelProperty(value = "合同获取方式名称")
    @TableField(exist = false)
    private String contractMethodName;


    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    @TableField(value = "priority")
    private String priority;

    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    @TableField(value = "departmental")
    private String departmental;

    @ApiModelProperty(value = "框架下子订单类型")
    @TableField(value = "subOrder_type")
    private String subOrderType;

    @ApiModelProperty(value = "客户项目性质")
    @TableField(value = "client_project_nature")
    private String clientProjectNature;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField(value = "business_type")
    private String businessType;

    /**
     * 已回款金额
     */
    @ApiModelProperty(value = "已回款金额")
    @TableField(value = "returned_money")
    private BigDecimal returnedMoney;

    /**
     * 数据来源：0-系统录入(包含新增，导入)；1-数据导入(数据库导入)
     */
    @ApiModelProperty(value = "数据来源")
    @TableField(value = "data_sources")
    private Boolean dataSources;
    /**
     * 签订时间
     */
    @ApiModelProperty(value = "签订时间")
    @TableField(value = "sign_time")
    private Date signTime;

    /**
     * 不关联交易原因
     */
    @ApiModelProperty(value = "不关联交易原因")
    @TableField(value = "unrelated_reason")
    private String unrelatedReason;



    // 需求
    @ApiModelProperty(value = "客户名称")
    @TableField(exist = false)
    private String resSource;
    @TableField(exist = false)
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;
    @TableField(exist = false)
    @ApiModelProperty(value = "客户技术接口人")
    private String custTecPerson;
    @TableField(exist = false)
    @ApiModelProperty(value = "需求编号")
    private String requirementNumber;
    @TableField(exist = false)
    @ApiModelProperty(value = "配合部门接口人")
    private String cooperatePerson;
    @ApiModelProperty(value = "配合部门")
    @TableField(exist = false)
    private String cooperateDpt;
    @ApiModelProperty(value = "需求标题")
    @TableField(exist = false)
    private String requirementName;
    @ApiModelProperty(value = "客户商务接口人")
    @TableField(exist = false)
    private String custBsPerson;
    @ApiModelProperty(value = "需求创建人id")
    @TableField(exist = false)
    private String requirementCreatorId;

    // 报价
    @ApiModelProperty(value = "报价金额")
    @TableField(exist = false)
    private BigDecimal quoteAmt;
    @ApiModelProperty(value = "底线价格")
    @TableField(exist = false)
    private BigDecimal floorPrice;
    @ApiModelProperty(value = "报价单编码")
    @TableField(exist = false)
    private String quotationId;
    @ApiModelProperty(value = "报价名称")
    @TableField(exist = false)
    private String quoteName; // 字段要和VO对应
    @ApiModelProperty(value = "创建人-报价")
    @TableField(exist = false)
    private String quotationCreatorId;




    // 市场合同签署信息
    @ApiModelProperty(value = "合同id")
   @TableField(exist = false)
    private String contractId;
    @ApiModelProperty(value = "签署结果")
    @TableField(exist = false)
    private Boolean signResult;
    @ApiModelProperty(value = "合同签署日期")
    @TableField(exist = false)
    private Date signDate;
    @ApiModelProperty(value = "合同生效日期")
    @TableField(exist = false)
    private Date effectDate;
    @ApiModelProperty(value = "合同完结日期")
    @TableField(exist = false)
    private Date completeDate;
    @ApiModelProperty(value = "合同完结类型")
    @TableField(exist = false)
    private String completeType;
    @ApiModelProperty(value = "合同完结类型名称")
    @TableField(exist = false)
    private String completeTypeName;
    @ApiModelProperty(value = "终止签署原因")
    @TableField(exist = false)
    private String endSignReason;
    @ApiModelProperty(value = "客户合同编号")
    @TableField(exist = false)
    private String custContractNo;
    @TableField(exist = false)
    private String contractSignId;

    // 框架合同
    @ApiModelProperty(value = "关联框架合同编号--商城子订单")
    @TableField(exist = false)
    private String frameContractNumber;
    @ApiModelProperty(value = "关联框架合同名称--商城子订单")
    @TableField(exist = false)
    private String frameContractName;

    // 关联交易表单
    @ApiModelProperty(value = "表单状态")
    @TableField(exist = false)
    private String formStatus;

}
