package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomeProvisionInformation DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 11:32:18
 */
@ApiModel(value = "IncomeProvisionInformationDTO对象", description = "收入计提信息表")
@Data
@ExcelIgnoreUnannotated
public class IncomeProvisionInformationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份 ", index = 0)
    private String year;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @ExcelProperty(value = "凭证号 ", index = 1)
    private String voucherNumber;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    @ExcelProperty(value = "挂账金额 ", index = 2)
    private BigDecimal accruedAmt;

    /**
     * 已冲销金额
     */
    @ApiModelProperty(value = "已冲销金额")
    @ExcelProperty(value = "已冲销金额 ", index = 3)
    private BigDecimal amortizedAmt;

    /**
     * 剩余未冲销金额
     */
    @ApiModelProperty(value = "剩余未冲销金额")
    @ExcelProperty(value = "剩余未冲销金额 ", index = 4)
    private BigDecimal remainUnamortizedAmt;

    /**
     * 本次冲销金额
     */
    @ApiModelProperty(value = "本次冲销金额")
    @ExcelProperty(value = "本次冲销金额 ", index = 5)
    private BigDecimal amortizationAmount;

    /**
     * 本次冲销金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销金额（不含税）")
    @ExcelProperty(value = "本次冲销金额（不含税） ", index = 6)
    private BigDecimal amortizationAmtExTax;

    /**
     * 不冲销原因
     */
    @ApiModelProperty(value = "不冲销原因")
    @ExcelProperty(value = "不冲销原因 ", index = 7)
    private String noAmortizationReason;

    /**
     * 收入填报Id
     */
    @ApiModelProperty(value = "收入填报Id")
    @ExcelProperty(value = "收入填报Id ", index = 8)
    private String incomePlanId;

    /**
     * 收入填报数据ID
     */
    @ApiModelProperty(value = "收入填报数据ID")
    @ExcelProperty(value = "收入填报数据ID ", index = 9)
    private String incomePlanDataId;




}
