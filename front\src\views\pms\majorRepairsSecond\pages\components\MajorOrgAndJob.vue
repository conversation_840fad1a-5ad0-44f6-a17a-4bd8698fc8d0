<script setup lang="ts">
import {
  BasicButton, BasicImport, OrionATable, randomString, useModal, isPower, downloadByData,
} from 'lyra-component-vue3';
import {
  computed, inject, provide, ref,
} from 'vue';
import {
  Dropdown, Menu, MenuItem, RadioButton, RadioGroup, Modal,
} from 'ant-design-vue';
import useMajorOrgAndJob from './hooks/useMajorOrgAndJob';
import { getImportProps } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';

const props = withDefaults(defineProps<{
  isDetails: boolean
  updateKey: string
}>(), {
  isDetails: false,
});

const emits = defineEmits<{
  (e: 'update:updateKey', key: string): void
}>();

const radioType = ref<'preparation' | 'operation'>('preparation');
const keyword = ref<string | undefined>(undefined);
const allFlagKeys = ref(false);
const expandFlag = ref(false);
const detailsData: Record<string, any> = inject('detailsData');
const powerData = inject('powerData');
const loadStatus = ref(false);
function updateTreeTableKey() {
  emits('update:updateKey', randomString());
}

provide('updateTreeTableKey', updateTreeTableKey);

const {
  expandedRowKeys, columns, innerColumns, dataApi, actions, innerActions, data, loadingRef,
} = useMajorOrgAndJob({
  radioType,
  keyword,
  allFlagKeys,
}, {
  isDetails: props.isDetails,
  updateTable,
  updateTreeTableKey,
});

function updateTable(allFlag) {
  allFlagKeys.value = allFlag;
  dataApi();
}

function changeRadio() {
  data.value = [];
  dataApi();
}

function expandAll() {
  allFlagKeys.value = !expandFlag.value;
  expandFlag.value = !expandFlag.value;
  dataApi();
}

const [register, { openModal: openImportModal }] = useModal();
const activeImportKey = ref('');
const downloadFileObj = computed(() => {
  switch (activeImportKey.value) {
    case '1':
      return {
        url: `/pms/relationOrgToJob/job/export/excel?repairRound=${detailsData?.repairRound}`,
        method: 'POST',
      };
    case '2':
      return {
        url: `/pms/relationOrgToJob/work/export/excel?repairRound=${detailsData?.repairRound}`,
        method: 'POST',
      };
    default:
      return {};
  }
});

const importConfig = computed<object | null>(() => {
  switch (activeImportKey.value) {
    case '1':
      return getImportProps({
        downloadUrl: '',
        checkUrl: `/pms/relationOrgToJob/job/import/excel/check?repairRound=${detailsData?.repairRound}`,
        importUrl: '/pms/relationOrgToJob/job/import/excel',
        cancelUrl: '/pms/relationOrgToJob/job/import/excel/cancel',
      }, dataApi);
    case '2':
      return getImportProps({
        downloadUrl: '',
        checkUrl: `/pms/relationOrgToJob/work/import/excel/check?repairRound=${detailsData?.repairRound}`,
        importUrl: '/pms/relationOrgToJob/work/import/excel',
        cancelUrl: '/pms/relationOrgToJob/job/import/excel/cancel',
      }, dataApi);
    default:
      return null;
  }
});

function handleMenuImport({ key }) {
  activeImportKey.value = key;
  switch (key) {
    case '1':
      openImportModal(true);
      break;
    case '2':
      openImportModal(true);
      break;
  }
}

function handleMenuOutPort({ key }) {
  activeImportKey.value = key;
  switch (key) {
    case '1':
      handleOutExport(1);
      break;
    case '2':
      handleOutExport(2);
      break;
  }
}

// 导出
async function handleOutExport(type: number) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      const params = {
        repairRound: detailsData?.repairRound,
      };
      loadStatus.value = true;
      const url = type === 1 ? '/pms/majorExcel/job/export' : '/pms/majorExcel/work/export';
      await downloadByData(url, params, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

</script>

<template>
  <OrionATable
    v-model:expandedRowKeys="expandedRowKeys"
    v-model:keyword="keyword"
    style="position: relative"
    :dataSource="data"
    :columns="columns"
    :scroll="{y:500}"
    :loading="loadingRef"
    :pagination="false"
    :rowKey="(r)=>r?.data?.id"
    :actions="actions"
    :innerActions="innerActions"
    :innerColumns="innerColumns"
    @search="dataApi"
  >
    <template #buttons>
      <div class="flex flex-pac">
        <BasicButton
          :icon="expandFlag ? 'fa-angle-up' : 'fa-angle-down'"
          @click="expandAll()"
        >
          {{ expandFlag ? '收起' : '展开所有' }}
        </BasicButton>

        <Dropdown
          v-if="isPower('PMS_DXXQEC_container_02_button_03', powerData)"
          :trigger="['click']"
        >
          <BasicButton
            type="primary"
            ghost
            icon="sie-icon-daoru"
          >
            导入
          </BasicButton>
          <template #overlay>
            <Menu @click="handleMenuImport">
              <MenuItem key="1">
                作业基本信息导入
              </MenuItem>
              <MenuItem key="2">
                每日开工作业信息导入
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
        <Dropdown
          v-if="isPower('PMS_DXXQEC_container_02_button_04', powerData)"
          :trigger="['click']"
        >
          <BasicButton
            type="primary"
            ghost
            icon="sie-icon-daoru"
          >
            导出
          </BasicButton>
          <template #overlay>
            <Menu @click="handleMenuOutPort">
              <MenuItem key="1">
                作业基本信息导出
              </MenuItem>
              <MenuItem key="2">
                每日开工作业信息导出
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>

        <BasicButton
          icon="sie-icon-chongzhi"
          style="margin-left: auto"
          @click="updateTable(true)"
        >
          刷新统计
        </BasicButton>
        <RadioGroup
          v-model:value="radioType"
          @change="changeRadio"
        >
          <RadioButton value="preparation">
            大修准备
          </RadioButton>
          <RadioButton value="operation">
            大修实施
          </RadioButton>
        </RadioGroup>
      </div>
    </template>
  </OrionATable>

  <BasicImport
    v-bind="importConfig"
    :downloadFileObj="downloadFileObj"
    @register="register"
  />
</template>
<style lang="less">
.ant-table-cell.required-cell {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    left: calc(50% - 35px);
  }
}
</style>
<style scoped lang="less">
:deep(.ant-table-cell.required) {
  &::after {
    position: absolute;
    content: '*';
    top: 50%;
    left: 2px;
    transform: translateY(-50%);
    color: red;
  }

  &.center::after {
    left: calc(50% - 35px);
  }
}

:deep(.permission-write) {
  position: absolute;
  top: 0;
  right: 0;
  content: '';
  width: 18px;
  height: 18px;
  background-color: #78b7e3;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

// 操作栏样式
:deep(.ant-space.ant-space-horizontal) {
  gap: 0 !important;
  width: 100%;
}

:deep(.ant-space-item) {
  width: 100%;
}

:deep(.ant-picker) {
  padding: 8px 11px;
}

:deep(.basic-import-select-wrap) {
  .ant-input {
    height: 40px;
  }

  .ant-input-search-button {
    height: 40px;
  }
}

:deep(.ant-select-selector) {
  height: 40px !important;

  .ant-select-selection-search {
    .ant-select-selection-search-input {
      height: 40px;
    }
  }

  .ant-select-selection-placeholder {
    line-height: 40px;
  }
}

:deep(.ant-select-selection-search) {
  height: 40px !important;

  .ant-select-selection-search-input {
    height: 40px;
  }
}

:deep(.ant-select-single .ant-select-selector .ant-select-selection-item) {
  line-height: 40px;
}

:deep(.clamp-hover) {
  display: inline-block;
  width: 100%;
  min-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;

  &:hover {
    color: #1890ff;
  }
}

</style>
