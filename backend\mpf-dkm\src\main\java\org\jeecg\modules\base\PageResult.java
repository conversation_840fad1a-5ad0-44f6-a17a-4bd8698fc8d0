package org.jeecg.modules.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.Collections;

@ApiModel("分页结果")
@Data
public class PageResult<T> {
    @ApiModelProperty("总数")
    private Long total;
    @ApiModelProperty("行数据")
    private Collection<T> rows;

    @ApiModelProperty("当前页数")
    private Long pageNo;

    @ApiModelProperty("页码")
    private Long page;

    @ApiModelProperty("每页条数")
    private Long pageSize;

    public PageResult(Long total, Collection<T> rows) {
        this.total = total;
        this.rows = rows;
    }

    public PageResult(Long total, Collection<T> rows,Long pageNo,Long page,Long pageSize) {
        this.total = total;
        this.rows = rows;

        this.pageNo = pageNo;
        this.page = page;
        this.pageSize = pageSize;
    }

    public static PageResult empty(Long total){
        return new PageResult(total, Collections.EMPTY_LIST);
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Collection<T> getRows() {
        return rows;
    }

    public void setRows(Collection<T> rows) {
        this.rows = rows;
    }

    public static <T>PageResult<T> emptyPageResult(){
        return new PageResult<T>(0L, Collections.emptyList());
    }
}
