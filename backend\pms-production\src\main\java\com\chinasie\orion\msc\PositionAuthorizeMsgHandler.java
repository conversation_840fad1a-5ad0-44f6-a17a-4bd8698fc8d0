package com.chinasie.orion.msc;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.PersonJobPostAuthorize;
import com.chinasie.orion.domain.vo.BasicUserCertificateVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class PositionAuthorizeMsgHandler implements MscB<PERSON>Handler<PersonJobPostAuthorize> {

    @Autowired
    UserRedisHelper userRedisHelper;


    @Override
    public SendMessageDTO buildMsc(PersonJobPostAuthorize personJobPostAuthorize, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        SimpleUser simpleUserByCode = userRedisHelper.getSimpleUserByCode(personJobPostAuthorize.getUserCode());
        messageMap.put("$beginDate$", DateUtil.format(personJobPostAuthorize.getStartData(), "yyyy-MM-dd"));
        messageMap.put("$baseName$",personJobPostAuthorize.getBaseName());
        messageMap.put("$position$",personJobPostAuthorize.getJobPostName());

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessId(personJobPostAuthorize.getId())
                .todoStatus(0)
                .messageUrl("/pms/employee-capability-pool/"+objects[0].toString())
                .messageUrlName("员工能力库详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(Collections.singletonList(simpleUserByCode.getId()))
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(personJobPostAuthorize.getCreatorId())
                .platformId(personJobPostAuthorize.getPlatformId())
                .orgId(personJobPostAuthorize.getOrgId())
                .build();

        return sendMsc;
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_POSITION_AUTHORIZE;
    }
}
