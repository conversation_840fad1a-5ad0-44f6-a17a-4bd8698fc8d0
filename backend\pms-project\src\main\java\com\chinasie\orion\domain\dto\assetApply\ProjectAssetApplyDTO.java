package com.chinasie.orion.domain.dto.assetApply;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectAssetApply DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:14:44
 */
@ApiModel(value = "ProjectAssetApplyDTO对象", description = "资产转固申请主表")
@Data
@ExcelIgnoreUnannotated
public class ProjectAssetApplyDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 0)
    private String projectId;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @ExcelProperty(value = "申请编号 ", index = 1)
    private String number;

    /**
     * 申请名称
     */
    @ApiModelProperty(value = "申请名称")
    @Size(max = 100, message = "申请名称过长，建议控制在500字符以内")
    @ExcelProperty(value = "申请名称 ", index = 2)
    private String name;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 3)
    private String resPerson;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @ExcelProperty(value = "申请部门 ", index = 4)
    private String resDept;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @ExcelProperty(value = "申请时间 ", index = 5)
    private Date resTime;

    /**
     * 申请说明
     */
    @ApiModelProperty(value = "申请说明")
    @Size(max = 500, message = "申请说明过长，建议控制在500字符以内")
    @ExcelProperty(value = "申请说明 ", index = 6)
    private String resDescribe;

    /**
     * 转固时间（生效时间）
     */
    @ApiModelProperty(value = "转固时间（生效时间）")
    @ExcelProperty(value = "转固时间（生效时间） ", index = 7)
    private Date finishTime;

    @ApiModelProperty(value = "附件")
    private List<FileDTO> attachments;


}
