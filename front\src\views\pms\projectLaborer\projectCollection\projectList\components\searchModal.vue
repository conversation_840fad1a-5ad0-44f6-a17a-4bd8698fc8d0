<template>
  <div class="searchModal">
    <BasicDrawer
      :visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <div class="search_title mb15">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入内容"
          size="large"
          @search="searchData"
        />
      </div>
      <basicTitle :title="'筛选属性'">
        <!-- <div class="rowItem">
          <div class="rowItem_label">类型：</div>
          <a-select
            ref="select"
            v-model:value="FormData.className"
            placeholder="此字段暂不配置"
            size="large"
            :options="classNameoptions"
            disabled
          />
        </div> -->
        <div class="rowItem">
          <div class="rowItem_label">
            状态：
          </div>
          <a-select
            ref="select"
            v-model:value="FormData.status"
            :placeholder="'请选择项目状态'"
            size="large"
            :options="statusNameoptions"
            show-search
          />
        </div>
        <div class="rowItem timewidth">
          <div class="rowItem_label">
            立项日期:
          </div>
          <RangePicker
            v-model:value="time.projectApproveTime"
            size="large"
            allow-clear
            :placeholder="['年 / 月 / 日', '年 / 月 / 日']"
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
            }"
          />
        </div>
        <div class="rowItem timewidth">
          <div class="rowItem_label">
            开始日期:
          </div>
          <RangePicker
            v-model:value="time.projectStartTime"
            size="large"
            allow-clear
            :placeholder="['年 / 月 / 日', '年 / 月 / 日']"
            default-value=""
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
            }"
          />
        </div>
        <div class="rowItem timewidth">
          <div class="rowItem_label">
            结束日期:
          </div>
          <RangePicker
            v-model:value="time.projectEndTime"
            size="large"
            allow-clear
            :placeholder="['年 / 月 / 日', '年 / 月 / 日']"
            :show-time="{
              hideDisabledOptions: true,
              defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')]
            }"
          />
        </div>
        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </basicTitle>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  message, Drawer, Select, Input, DatePicker, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import moment, { Moment } from 'moment';
import dayjs from 'dayjs';
//   import moment, { Moment } from 'moment';
import { BasicDrawer } from 'lyra-component-vue3';
import { getProductStatusApi } from '/@/views/pms/projectLaborer/api/projectLab';
export default defineComponent({
  components: {
    BasicDrawer,
    basicTitle,
    aSelect: Select,
    aInputSearch: Input.Search,
    RangePicker: DatePicker.RangePicker,
    AButton: Button,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'search'],
  setup(props, { emit }) {
    const state = reactive({
      statusNameoptions: [
        {
          value: '1001',
          label: '未发布',
        },
        {
          value: '1002',
          label: '已发布',
        },
        {
          value: '1003',
          label: '已结项',
        },
      ],
      classNameoptions: [
        {
          value: '104',
          label: '型号项目',
        },
        {
          value: '105',
          label: '科研项目',
        },
        {
          value: '106',
          label: '民品项目',
        },
        {
          value: '107',
          label: '军品项目',
        },
      ],
      time: <any>{
        /* 开始 */
        projectStartTime: [],
        /* 结束 */
        projectEndTime: [],
        /* 成立 */
        projectApproveTime: [],
      },
      FormData: <any>{
        /* 状态 */
        status: undefined,
        /* 产品类型 */
        //   className: ''
      },
      // visible: false,
      title: '搜索',
      productModelIdOptions: [],
      projectIdOptions: [],
      secretLevelOption: [],
      statusOptions: [],
      nameValue: '',
      statusId: '',
    });
    // watch(
    //   () => props.data,
    //   async (newVal, odlVal) => {
    //     state.visible = true;
    //     state.statusId = await getProductStatusApi(props.listData[0].id)
    //   },
    // );
    onMounted(async () => {

    });
    /* x按钮 */
    const x = () => {
      closeDrawer();
      // clearFormFields()
    };
      /* 取消 */
    const close = () => {
      // state.visible = false;
      closeDrawer();
      // clearFormFields()
    };
    const onSubmit = () => {
      let params = {};

      for (const item in state.FormData) {
        params[item] = state.FormData[item];
      }
      // console.log('测试🚀🚀 ~~~ state.time.projectStartTime[0]?._d', state.time);

      let queryConditionArr = [];
      queryConditionArr = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'projectStartTime',
          type: 'bt',
          link: 'and',
          value: [
            state.time.projectStartTime.length > 0 && state.time.projectStartTime[0]?.$d
              ? dayjs(
                dayjs(state.time.projectStartTime[0]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
            state.time.projectStartTime.length > 0 && state.time.projectStartTime[1]?.$d
              ? dayjs(
                dayjs(state.time.projectStartTime[1]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
          ],
        },
        {
          column: 'projectEndTime',
          type: 'bt',
          link: 'and',
          value: [
            state.time.projectEndTime.length > 0 && state.time.projectEndTime[0]?.$d
              ? dayjs(
                dayjs(state.time.projectEndTime[0]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
            state.time.projectEndTime.length > 0 && state.time.projectEndTime[1]?.$d
              ? dayjs(
                dayjs(state.time.projectEndTime[1]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
          ],
        },
        {
          column: 'projectApproveTime',
          type: 'bt',
          link: 'and',
          value: [
            state.time.projectApproveTime.length > 0 && state.time.projectApproveTime[0]?.$d
              ? dayjs(
                dayjs(state.time.projectApproveTime[0]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
            state.time.projectApproveTime.length > 0 && state.time.projectApproveTime[1]?.$d
              ? dayjs(
                dayjs(state.time.projectApproveTime[1]?.$d).format('YYYY-MM-DD HH:mm:ss'),
              ).unix() * 1000
              : '',
          ],
        },
      ];
      const queryCondition = queryConditionArr.filter((item, index) => {
        if (item.value === '') {
          delete queryConditionArr[index];
        } else {
          return item.value[0] !== '';
        }
      });

      emit('search', {
        params,
        queryCondition,
      });
      closeDrawer();
      // clearFormFields();
    };

    function closeDrawer() {
      emit('update:visible', false);
    }

    function clearFormFields() {
      state.FormData = {};
      state.time = {};
      state.nameValue = '';
      state.time = {
        /* 开始 */
        projectStartTime: [],
        /* 结束 */
        projectEndTime: [],
        /* 成立 */
        projectApproveTime: [],
      };
    }

    const searchData = () => {
      onSubmit();
    };

    return {
      ...toRefs(state),
      moment,
      close,
      onSubmit,
      searchData,
      x,
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  .searchModalDrawer {
    .ant-drawer-body {
      padding: 60px 0px 80px 0px !important;
    }
    .search_title {
      padding: 10px 0px;
      border-bottom: 1px solid #d2d7e1;
      text-align: center;
      margin-bottom: 10px;
      .ant-input-search {
        width: 310px;
      }
    }
    .basicTitle {
      padding: 0px 15px;
    }
    .rowItem {
      margin-bottom: 10px;
      .rowItem_label {
        padding-left: 5px;
        color: #444b5e;
      }
      .ant-select {
        width: 100%;
      }
    }

    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0;
      text-align: center;
      width: 280px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 110px;
      border-radius: 4px;
    }
    .bgDC {
      width: 110px;
      margin-left: 15px;
      border-radius: 4px;
    }
  }
  .timewidth {
    :deep(.ant-calendar-picker-input) {
      width: 310px !important;
    }
  }
  .nodeForm {
    padding: 10px 10px 80px 10px;
  }
  .ant-form-item{
    display: block;
  }
  .nextCheck {
    height: 40px;
    line-height: 40px;
  }
  .nodeItemBtn {
    position: fixed;
    bottom: 0px;
    padding: 20px 0;
    text-align: center;
    width: 280px;
    height: 80px;
    background: #ffffff;
    margin-bottom: 0px;
  }
  .cancelBtn {
    color: #5172dc;
    background: #5172dc19;
    width: 110px;
    border-radius: 4px;
  }
  .bgDC {
    width: 110px;
    margin-left: 15px;
    border-radius: 4px;
  }
  .rowItem {
    margin-bottom: 10px;
    .rowItem_label {
      padding-left: 5px;
      color: #444b5e;
    }
    .ant-select {
      width: 100%;
    }
  }
</style>
