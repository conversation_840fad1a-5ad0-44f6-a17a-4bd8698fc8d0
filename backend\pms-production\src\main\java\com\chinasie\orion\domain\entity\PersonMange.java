package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * PersonMange Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@TableName(value = "pmsx_person_mange")
@ApiModel(value = "PersonMangeEntity对象", description = "人员管理")
@Data
public class PersonMange extends ObjectEntity implements Serializable {

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @TableField(value = "number")
    private String number;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门id")
    @TableField(value = "contact_dept")
    private String contactDept;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门编号")
    @TableField(value = "contact_dept_code")
    private String contactDeptCode;
    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门名称")
    @TableField(value = "contact_dept_name")
    private String contactDeptName;
    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室id")
    @TableField(value = "contact_office")
    private String contactOffice;

    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室编号")
    @TableField(value = "contact_office_code")
    private String contactOfficeCode;
    @ApiModelProperty(value = "接口科室名称")
    @TableField(value = "contact_office_name")
    private String contactOfficeName;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    @TableField(value = "contact_user")
    private String contactUser;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人编号")
    @TableField(value = "contact_user_code")
    private String contactUserCode;
    @ApiModelProperty(value = "接口人名称")
    @TableField(value = "contact_user_name")
    private String contactUserName;
    /**
     * 进入形式
     */
    @ApiModelProperty(value = "进入形式")
    @TableField(value = "enter_mode")
    private String enterMode;

    /**
     * 大修/日常
     */
    @ApiModelProperty(value = "大修/日常")
    @TableField(value = "work_type")
    private String workType;

    /**
     * 实际到厂时间
     */
//    @ApiModelProperty(value = "实际到厂时间")
//    @TableField(value = "actual_enter_date")
//    private Date actualEnterDate;
//
//    /**
//     * 实际离厂时间
//     */
//    @ApiModelProperty(value = "实际离厂时间")
//    @TableField(value = "actual_leave_date")
//    private Date actualLeaveDate;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    @TableField(value = "leave_reason", updateStrategy = FieldStrategy.IGNORED)
    private String leaveReason;

    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    @TableField(value = "leave_remark", updateStrategy = FieldStrategy.IGNORED)
    private String leaveRemark;

//    /**
//     * 计划到厂时间
//     */
//    @ApiModelProperty(value = "计划到厂时间")
//    @TableField(value = "plan_enter_date")
//    private Date planEnterDate;
//
//    /**
//     * 计划离厂时间
//     */
//    @ApiModelProperty(value = "计划离厂时间")
//    @TableField(value = "plan_leave_date")
//    private Date planLeaveDate;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;



    @ApiModelProperty(value = "基地内承担的主要项目：项目ID")
    @TableField(value = "base_place_project")
    private String basePlaceProject;
    @ApiModelProperty(value = "基地内承担的主要项目：项目名称")
    @TableField(value = "base_place_project_name")
    private String basePlaceProjectName;

    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;
    @ApiModelProperty(value = "身高/米")
    @TableField(value = "height_str")
    private String heightStr;
    @ApiModelProperty(value = "体重")
    @TableField(value = "weight_str")
    private String weightStr;
    @ApiModelProperty(value = "职业禁忌症：有/无")
    @TableField(value = "job_taboos")
    private String jobTaboos;

    @ApiModelProperty(value = "职业禁忌症名称")
    @TableField(value = "job_taboos_name")
    private String jobTaboosName;

    @ApiModelProperty(value = "涉及控制区作业: 是/否 true/false")
    @TableField(value = "design_ctrl_zone_op")
    private Boolean designCtrlZoneOp;


    @ApiModelProperty(value = "化学品/毒物使用或接触作业：是/否 true/false")
    @TableField(value = "chemical_toxin_use_job")
    private Boolean chemicalToxinUseJob;

    @ApiModelProperty(value = "工作负责人：是/否 true/false")
    @TableField(value = "work_res_person")
    private Boolean workResPerson;

    @ApiModelProperty(value = "准备工程师：是/否 true/false")
    @TableField(value = "preparation_engineer")
    private Boolean preparationEngineer;

    @ApiModelProperty(value = "QC：是/否 true/false ")
    @TableField(value = "qc_str")
    private Boolean qcStr;

    @ApiModelProperty(value = "QC工作年限")
    @TableField(value = "qc_work_year")
    private String qcWorkYear;

    @ApiModelProperty(value = "专职安全员：是/否 true/false ")
    @TableField(value = "fu_ti_saf_off")
    private Boolean fuTiSafOff;

    @ApiModelProperty(value = "兼职安全员：是/否 true/false ")
    @TableField(value = "pa_ti_saf_off")
    private Boolean paTiSafOff;

    @ApiModelProperty(value = "特种作业持证情况(含无损检测资质")
    @TableField(value = "spe_task_cert_sit")
    private String speTaskCertSit;

    @ApiModelProperty(value = "一年内参与过集团内大修、高剂量人员(年个人剂量>8mSv为高剂量人员)")
    @TableField(value = "participate_or_not")
    private String participateONot;


    @ApiModelProperty(value = "新人：是/否 true/false")
    @TableField(value = "newcomer")
    private Boolean newcomer;

    @ApiModelProperty(value = "新人类型")
    @TableField(value = "newcomer_type")
    private String newcomerType;


    @ApiModelProperty(value = "新人对口人")
    @TableField(value = "newcomer_match_person", updateStrategy = FieldStrategy.ALWAYS)
    private String newcomerMatchPerson;
    @ApiModelProperty(value = "新人对口人编号")
    @TableField(value = "newcomer_match_person_code")
    private String newcomerMatchPersonCode;

    @ApiModelProperty(value = "授权状态")
    @TableField(value = "authorization_status")
    private String authorizationStatus;

    @ApiModelProperty(value = "是否基地常驻")
    @TableField(value = "is_base_permanent")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "新人对口人名称")
    @TableField(exist = false)
    private String newcomerMatchPersonName;



    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    @TableField(value = "is_join_year_major_repair")
    private Boolean isJoinYearMajorRepair;

    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    @TableField(value = "is_height_measure_person")
    private Boolean isHeightMeasurePerson;

    @ApiModelProperty(value = "主工作中心")
    @TableField(value = "main_work_center")
    private String mainWorkCenter;

    @ApiModelProperty(value = "是否作业")
    @TableField(value = "is_job")
    private Boolean isJob;


    @ApiModelProperty(value = "实际入场日期")
    @TableField(value = "act_in_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    @TableField(value = "act_out_date",updateStrategy = FieldStrategy.IGNORED)
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    @TableField(value = "in_date", updateStrategy = FieldStrategy.IGNORED)
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    @TableField(value = "out_date", updateStrategy = FieldStrategy.IGNORED)
    private Date outDate;
    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    @TableField(value = "is_finish_out_handover", updateStrategy = FieldStrategy.IGNORED)
    private Boolean isFinishOutHandover;

    @ApiModelProperty(value = "是否再次入场")
    @TableField(value = "is_again_in", updateStrategy = FieldStrategy.IGNORED)
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场倒计时（天）")
    @TableField(value = "in_days")
    private long inDays;

    /**
     * 性别
     */
    @TableField(exist = false)
    private String sex;

    /**
     * 员工能力库人员id
     */
    @TableField(exist = false)
    private String basicUserId;

    @TableField(exist = false)
    String code;


    @TableField(exist = false)
    String name;

    @TableField(exist = false)
    String jobPostName;
}
