package com.chinasie.orion.constant;

import lombok.Getter;

/**
 * &#064;author:  wyc
 * &#064;date:
 * &#064;description:
 */
@Getter
public enum MarketContractSupplierEnum {

    SRG_SU("SRG_SU","GB"),
    SRG_NING("SRG_NING","GF"),
    GH_SHENG("GH_SHENG","GE"),
    GH_SU("GH_SU","GC"),
    ;

    private final String code;

    private final String desc;

    MarketContractSupplierEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
