<template>
  <Menu
    v-model:selectedKeys="selectedKeys"
    class="menu-class"
  >
    <MenuItem key="0">
      <template #icon>
        <Icon
          icon="orion-icon-team"
          size="20"
        />
      </template>
      项目初始成员
    </MenuItem>
    <MenuItem
      v-for="item in menuOptions"
      :key="item.id"
      class="pl50"
    >
      {{ item.name }}
    </MenuItem>
  </Menu>
</template>

<script setup lang="ts">
import { Menu, MenuItem } from 'ant-design-vue';
import { Icon } from 'lyra-component-vue3';
import { computed, ref, watch } from 'vue';

const menuOptions = ref([
  {
    id: 1,
    name: '主管领导',
  },
  {
    id: 2,
    name: '项目经理',
  },
  {
    id: 3,
    name: '项目副经理',
  },
]);
const selectedKeys = ref(['0']);

watch(selectedKeys, (val) => {
  console.log(val[0]);
});

defineExpose({
  menuId: computed(() => selectedKeys.value[0]),
});
</script>

<style scoped lang="less">
.menu-class{
  border-right: none;
}
</style>
