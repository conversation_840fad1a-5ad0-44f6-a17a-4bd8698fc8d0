<?xml version="1.0" encoding="UTF-8"?>

<!--
Copyright The Codehaus Foundation.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.spice</groupId>
    <artifactId>spice-parent</artifactId>
    <version>16</version>
  </parent>

  <groupId>org.codehaus.plexus</groupId>  
  <artifactId>plexus-utils</artifactId>
  <version>2.1</version>

  <name>Plexus Common Utilities</name>
  <description>A collection of various utility classes to ease working with strings, files, command lines, XML and more.</description>
  <url>http://plexus.codehaus.org/plexus-utils</url>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-utils.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-utils.git</developerConnection>
    <url>http://github.com/sonatype/plexus-utils</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLXUTILS</url>
  </issueManagement>

  <distributionManagement>
    <repository>
      <id>plexus-releases</id>
      <name>Plexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>plexus-snapshots</id>
      <name>Plexus Snapshot Repository</name>
      <url>${plexusDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>codehaus.org</id>
      <url>dav:https://dav.codehaus.org/plexus</url>
    </site>
  </distributionManagement>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.4</source>
          <target>1.4</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.6</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- required to ensure the test classes are used, not surefire's plexus-utils -->
          <childDelegation>true</childDelegation>
          <excludes>
            <exclude>org/codehaus/plexus/util/FileBasedTestCase.java</exclude>
            <exclude>**/Test*.java</exclude>
          </excludes>
          <systemProperties>
            <property>
              <name>JAVA_HOME</name>
              <value>${JAVA_HOME}</value>
            </property>
            <property>
              <name>M2_HOME</name>
              <value>${M2_HOME}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
