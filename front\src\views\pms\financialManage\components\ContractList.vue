<script lang="ts" setup>
import {
  ref,
  h,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';
import SelectEdit from './SelectEdit.vue';

const tableRef = ref(null);
const contactObject = ref();
const selectedRows = ref([]);

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const valuesList = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition.values));

    // 如果需要将所有 values 合并成一个扁平的数组
    const flattenedValues = valuesList.flat();
    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: { name: flattenedValues[0] },
    };
  }
  return params;
}

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {
    type: 'radio',
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  maxHeight: 475,
  showSmallSearch: true,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  smallSearchField: ['name'],
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams || [], 'marketContract/getIncomeMarketContractPage', 'POST');
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'number',
      width: 170,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
    },
    {
      title: '商务负责人',
      dataIndex: 'busRspUserName',
      width: 150,
    },
    {
      title: '里程碑编号',
      dataIndex: 'milestoneNumber',
      width: 200,
      customRender({ text, record }) {
        return record?.contractMilestoneVOS ? h(SelectEdit, {
          component: 'Select',
          record,
          text,
          componentValue: text || record.contractMilestoneVOS?.[0].milestoneName,
          componentProps: {
            options: record.contractMilestoneVOS?.map((item: any) => ({
              label: item.milestoneName,
              value: item.id,
              number: item.Number,
            })),
          },
          async onSubmit(data, resolve: (value: string) => void) {
            record.milestoneNumber = data.label;
            contactObject.value = data;
            record.milestoneName = data.label;
            resolve('');
          },
        }) : text;
      },
    },
    {
      title: '里程碑名称',
      dataIndex: 'milestoneName',
      width: 200,
      customRender({ text, record }) {
        return text || record.contractMilestoneVOS?.[0].milestoneName;
      },
    },
  ],
};

function updateTable() {
  tableRef.value?.reload();
}

defineExpose({
  async onSubmit() {
    let selectRow = selectedRows.value;
    if (selectRow?.length === 0) {
      message.warning('请选择一条数据');
    } else {
      const arr = cloneDeep(selectRow);
      if (arr[0].id === '1' && arr[0].name.includes('未签订合同')) {
        return arr[0];
      }
      arr.forEach((item) => {
        item.milestoneName = contactObject.value ? contactObject.value?.label : item.contractMilestoneVOS[0].milestoneName;
        item.milestoneId = contactObject.value ? contactObject.value?.value : item.contractMilestoneVOS[0].id;
        item.milestoneNumber = contactObject.value ? contactObject.value?.number : item.contractMilestoneVOS[0].number;
      });
      const array = await new Api(`/pms/incomePlanData/getMilestone/${arr[0].milestoneId}`).fetch('', '', 'GET');
      const result = {
        ...array,
        estimateInvoiceDate: array.estimateInvoiceDate ? dayjs(array.estimateInvoiceDate).format('YYYY-MM-DD') : '',
      };
      return result;
    }
  },
});
</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped></style>
