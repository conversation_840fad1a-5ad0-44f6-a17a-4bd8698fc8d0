<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright (C) 2011, FuseSource Corp.  All rights reserved.

      http://fusesource.com

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are
  met:

     * Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the following disclaimer
  in the documentation and/or other materials provided with the
  distribution.
     * Neither the name of FuseSource Corp. nor the names of its
  contributors may be used to endorse or promote products derived from
  this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.fusesource.leveldbjni</groupId>
    <artifactId>leveldbjni-project</artifactId>
    <version>1.8</version>
  </parent>

  <groupId>org.fusesource.leveldbjni</groupId>
  <artifactId>leveldbjni-all</artifactId>
  <version>1.8</version>
  <packaging>bundle</packaging>
  
  <name>${project.artifactId}</name>
  <description>An uber jar which contains all the leveldbjni platform libraries and dependencies</description>
      
  <dependencies>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni-osx</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni-linux32</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni-linux64</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni-win32</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.fusesource.leveldbjni</groupId>
      <artifactId>leveldbjni-win64</artifactId>
      <version>1.8</version>
      <scope>provided</scope>
    </dependency>
    
  </dependencies>

  <build>
     
    <plugins>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.3.7</version>
        <extensions>true</extensions>
        <inherited>true</inherited>
        <configuration>
          <instructions>
            <Bundle-Name>${project.artifactId}</Bundle-Name>
            <Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
            <Export-Package>
              org.fusesource.leveldbjni;version=${project.version},
              org.iq80.leveldb*;version=${leveldb-api-version},</Export-Package>
            <Import-Package></Import-Package>
            <DynamicImport-Package></DynamicImport-Package>
            <Private-Package>org.fusesource.hawtjni*,org.fusesource.leveldbjni.internal*,!*</Private-Package>
            <Implementation-Title>LevelDB JNI</Implementation-Title>
            <Implementation-Version>${project.version}</Implementation-Version>
            <Embed-Dependency>
              *;groupId=org.fusesource.leveldbjni;inline=META-INF/native/*,
            </Embed-Dependency>
            <Embed-Transitive>true</Embed-Transitive>
            <Bundle-NativeCode>
              META-INF/native/windows32/leveldbjni.dll;osname=Win32;processor=x86,
              META-INF/native/windows64/leveldbjni.dll;osname=Win32;processor=x86-64,
              META-INF/native/osx/libleveldbjni.jnilib;osname=macosx;processor=x86,
              META-INF/native/osx/libleveldbjni.jnilib;osname=macosx;processor=x86-64,
              META-INF/native/linux32/libleveldbjni.so;osname=Linux;processor=x86,
              META-INF/native/linux64/libleveldbjni.so;osname=Linux;processor=x86-64
            </Bundle-NativeCode>
         </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  
  <profiles>
    <profile>
      <id>release</id>
      
      <dependencies>
        <dependency>
          <groupId>org.fusesource.leveldbjni</groupId>
          <artifactId>leveldbjni</artifactId>
          <version>1.8</version>
          <classifier>sources</classifier>
          <optional>true</optional>
        </dependency>
        <dependency>
          <groupId>org.iq80.leveldb</groupId>
          <artifactId>leveldb-api</artifactId>
          <version>${leveldb-api-version}</version>
          <classifier>sources</classifier>
          <optional>true</optional>
        </dependency>
        <dependency>
          <groupId>org.fusesource.hawtjni</groupId>
          <artifactId>hawtjni-runtime</artifactId>
          <version>${hawtjni-version}</version>
          <classifier>sources</classifier>
          <optional>true</optional>
        </dependency>
      </dependencies>
          
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <configuration>
              <skipSource>true</skipSource>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <id>uber-sources-jar</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <descriptors>
                    <descriptor>${basedir}/src/main/descriptors/uber-sources.xml</descriptor>
                  </descriptors>
                  <appendAssemblyId>true</appendAssemblyId>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  
</project>
