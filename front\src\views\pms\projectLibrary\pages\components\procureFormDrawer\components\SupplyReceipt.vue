<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import {
  h, inject, onMounted, Ref,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const dataId:Ref = inject('dataId');
const detailsData:Ref<Record<string, any>> = inject('detailsData');
const schemas: FormSchema[] = [
  {
    field: 'formTitle',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '供应商信息',
        isFormItem: true,
        isTitle: true,
      });
    },
  },
  {
    field: 'supplierName',
    component: 'Input',
    label: '供应商',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入供应商名称',
    },
  },
  {
    field: 'contactPerson',
    component: 'Input',
    label: '联系人',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入供应商联系人',
    },
  },
  {
    field: 'contactPhone',
    component: 'InputNumber',
    label: '联系人电话',
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      maxlength: 100,
      controls: false,
      precision: 0,
      placeholder: '请输入供应商联系人电话',
    },
  },
  {
    field: 'contactEmail',
    component: 'Input',
    label: '联系人邮箱',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入供应商联系人邮箱',
    },
  },
  {
    field: 'formTitle',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(DetailsLayout, {
        title: '收货',
        isFormItem: true,
        isTitle: true,
      });
    },
  },
  {
    field: 'receivePerson',
    component: 'Input',
    label: '收货人姓名',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入收货人姓名',
    },
  },
  {
    field: 'receivePhone',
    component: 'InputNumber',
    label: '收货人电话',
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      maxlength: 100,
      controls: false,
      precision: 0,
      placeholder: '请输入收货人电话',
    },
  },
  {
    field: 'receiveAddress',
    component: 'Input',
    label: '收货地址',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入收货地址',
    },
  },
  {
    field: 'receiveEmail',
    component: 'Input',
    label: '收货人邮箱',
    required: true,
    componentProps: {
      maxlength: 100,
      placeholder: '请输入收货人邮箱',
    },
  },
];
const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  if (dataId.value) {
    initForm();
  }
});

function initForm() {
  setFieldsValue({
    ...detailsData.value.projectPurchaseSupplierInfoVO,
    ...detailsData.value.projectPurchaseReceiveInfoVO,
    contactPhone: Number(detailsData.value?.projectPurchaseSupplierInfoVO?.contactPhone),
    receivePhone: Number(detailsData.value?.projectPurchaseReceiveInfoVO?.receivePhone),
  });
}

defineExpose({
  validate,
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
