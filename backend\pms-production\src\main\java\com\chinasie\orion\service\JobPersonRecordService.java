package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.JobPersonRecord;
import com.chinasie.orion.domain.dto.JobPersonRecordDTO;
import com.chinasie.orion.domain.vo.JobPersonRecordVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobPersonRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
public interface JobPersonRecordService  extends  OrionBaseService<JobPersonRecord>  {


        /**
         *  详情
         *
         * * @param id
         */
    JobPersonRecordVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param jobPersonRecordDTO
         */
        String create(JobPersonRecordDTO jobPersonRecordDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param jobPersonRecordDTO
         */
        Boolean edit(JobPersonRecordDTO jobPersonRecordDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<JobPersonRecordVO> pages( Page<JobPersonRecordDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<JobPersonRecordVO> vos)throws Exception;

    void addRelation(String jobId, String personManageId, String userCode);
}
