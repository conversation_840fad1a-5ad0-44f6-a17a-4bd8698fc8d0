package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/24/10:55
 * @description:
 */
public enum ProdActionItemEnum {
    UPDATE_SOLUTIONS("UPDATE_SOLUTIONS","改进措施"),
    FEEDBACK_INFORMATION("FEEDBACK_INFORMATION","反馈信息");
    private String type;
    private String desc;
    ProdActionItemEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public String getType() {
        return type;
    }
    public String getDesc() {
        return desc;
    }

}
