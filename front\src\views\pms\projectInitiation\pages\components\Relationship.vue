<template>
  <div class="drawer-content">
    <BasicButton
      type="primary"
      icon="add"
      @click="addNewData"
    >
      添加一行
    </BasicButton>
    <div class="flex-content">
      <div
        v-for="(item, index) in selectValueList"
        :key="index"
        class="flex-box"
      >
        <div class="number-box">
          {{ index + 1 }}
        </div>
        <div class="relation-box">
          <!--前后置计划-->
          <a-select
            v-model:value="item.taskType"
            placeholder="请选择任务类型"
            style="width: 100%"
            :options="[
              {label: '前置任务',value: 'pre'},
              {label: '后置任务',value: 'post'},
            ]"
          />
        </div>
        <div class="select-box">
          <a-select
            v-model:value="item.taskId"
            placeholder="请选择任务"
            style="width: 100%"
            :options="selectTaskList"
            showSearch
          />
        </div>
        <span
          class="action-btn"
          @click="deleteItem(index)"
        >删除</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { message, Select as ASelect } from 'ant-design-vue';
import { BasicButton } from 'lyra-component-vue3';
import {
  computed, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    drawerData:object,
}>(), {
  drawerData: () => ({}),
});

const selectValueList:Ref<Record<any, any>[]> = ref([]);
const selectTaskList = computed(() => props.drawerData.optionsList.map((val) => ({
  label: val.name,
  value: val.id,
  disabled: selectValueList.value.map((item) => item.taskId).includes(val.id),
})));
onMounted(() => {
  if (Array.isArray(props.drawerData.record?.taskPreVOList) && props.drawerData.record?.taskPreVOList.length > 0) {
    props.drawerData.record?.taskPreVOList.forEach((item) => {
      selectValueList.value.push({
        taskId: item.taskId,
        taskType: 'pre',
      });
    });
  }
  if (Array.isArray(props.drawerData.record?.taskPostVOList) && props.drawerData.record?.taskPostVOList.length > 0) {
    props.drawerData.record?.taskPostVOList.forEach((item) => {
      selectValueList.value.push({
        taskId: item.taskId,
        taskType: 'post',
      });
    });
  }
});
function addNewData() {
  selectValueList.value.push({
    taskId: undefined,
    taskType: undefined,
  });
}
const deleteItem = async (index) => {
  selectValueList.value.splice(index, 1);
};

defineExpose({
  async onSubmit() {
    if (!selectValueList.value.map((item) => item.taskType)?.every((item) => item)) {
      message.error('请选择计划类型');
      return;
    }
    if (!selectValueList.value.map((item) => item.taskId)?.every((item) => item)) {
      message.error('请选择计划');
    }

    const params = {
      approvalTaskPrePostDTOS: selectValueList.value.map((item) => ({
        approvalId: props.drawerData.approvalId,
        preTaskId: item.taskType === 'pre' ? item.taskId : null,
        postTaskId: item.taskType === 'post' ? item.taskId : null,
      })),
      taskIds: [props.drawerData.taskId],
    };
    await new Api('/pms').fetch(params, 'approvalTaskPrePost/createBatch', 'POST');
    message.success('设置前后置关系成功');
  },
});
</script>
<style lang="less" scoped>
.drawer-content{
  padding: 20px;
}
.flex-content {
  display: flex;
  align-items: center;
  flex-flow: wrap;
  margin-top: 20px;
  gap: 10px 0;
  .flex-box {
    width:100%;
    display: flex;
    align-items: center;
    gap: 0 15px;
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 145px;
      height: 32px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
    }
    .select-box {
      flex:1;
    }
    .action-btn{
      display: inline-block;
      width: 30px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>