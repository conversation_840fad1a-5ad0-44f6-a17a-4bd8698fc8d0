<template>
  <div>
    <BasicButton
      class="m-b-lr m-b-tb"
      type="primary"
      icon="sie-icon-tianjiaxinzeng"
      :loading="disabledBtn"
      @click="openCheckProcureModal(true, {});"
    >
      关联采购订单
    </BasicButton>
    <Layout2 left-title="采购订单">
      <template #left>
        <SpinMain
          v-if="loading"
        />
        <BasicMenu
          v-else
          :defaultActionId="orderId"
          :showHeader="false"
          :menuData="menus"
          actionLineAlign="right"
          @menuChange="menuChange"
        />
      </template>
      <DetailsLayout
        title="采购订单信息"
        :list="orderInfo"
        :spinning="orderLoading"
        :data-source="orderDetail"
        :column="3"
      >
        <template #table>
          <div
            style="height: 500px;overflow: hidden;"
          >
            <OrionTable
              ref="tableRef"
              :options="tableOptions"
            />
          </div>
        </template>
      </DetailsLayout>
    </Layout2>
    <!--关联采购订单-->
    <CheckProcureModal
      :onCheckProjectCallback="checkProjectCallback"
      @register="registerCheckProcure"
    />
  </div>
</template>

<script setup lang="ts">
import {
  BasicButton, BasicMenu, Layout2, OrionTable, useModal,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { getPurchaseOrder, getPurOrderList, postOrderListInfoPages } from '/@/views/pms/projectLaborer/projectLab/api';
import {
  computed, h, inject, nextTick, onMounted, ref, Ref, unref, watchEffect,
} from 'vue';
import dayjs from 'dayjs';
import { contractDetailKey } from '../types';
import SpinMain from '/@/views/pms/projectLaborer/components/SpanMain/SpinMain.vue';
import CheckProcureModal from './components/CheckProcureModal/Index.vue';
// import CheckProcureModal from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/components/AddContract/components/CheckProcureModal/Index.vue';
const [registerCheckProcure, { openModal: openCheckProcureModal }] = useModal();
const menus: Ref = ref([]);
// const contractDetail = inject(contractDetailKey);
const contractDetail:any = inject('allData', {});
const orderId: Ref<string> = ref('');
const orderDetail: Ref = ref({});
const orderTableData = ref();
const loading: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const projectNumber:string = inject('projectNumber');
const disabledBtn = ref(false);
const orderInfo: Ref = ref([
  {
    label: '采购订单号',
    field: 'number',
  },
  {
    label: '采购类型',
    field: 'purchaseTypeName',
    gridColumn: '2/4',
  },
  // {
  //   label: '是否签订纸质合同',
  //   field: 'isSignContract',
  //   isBoolean: true,
  // },
  {
    label: '是否具有质保金',
    field: 'isGuaranteeMoney',
    isBoolean: true,
  },
  {
    label: '质保金金额',
    field: 'guaranteeAmt',
  },
]);

const orderNumber = computed(() => menus.value?.filter((item) => item.id === unref(orderId))?.[0]?.orderNumber);

const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  immediate: false,
  // api: (params) => postOrderListInfoPages({
  //   ...params,
  //   query: {
  //     projectNumber,
  //     contractNumber: unref(contractDetail)?.number,
  //     orderNumber: unref(orderNumber),
  //   },
  // }),
  dataSource: computed(() => orderTableData.value),
  columns: [
    // {
    //   title: '采购计划编号',
    //   dataIndex: 'purchaseApplyForNumber',
    // },
    // {
    //   title: '采购申请号',
    //   dataIndex: 'purchaseApplyForNumber',
    // },
    // {
    //   title: '采购单行号',
    //   dataIndex: 'purchaseApplyForLineNumber',
    // },
    {
      title: '物资/服务计划编号',
      dataIndex: 'planNumber',
    },
    // {
    //   title: '计划类型',
    //   dataIndex: 'sortText',
    // },
    {
      title: '物资/服务编码',
      dataIndex: 'goodsServiceNumber',
    },
    {
      title: '物资/服务描述',
      dataIndex: 'description',
    },
    {
      title: orderDetail.value.purchaseType === 'goodsType' ? '规格型号' : '服务期限',
      dataIndex: 'normsModel',
    },
    // {
    //   title: '服务期限',
    //   dataIndex: 'taxCode',
    // },

    {
      title: '计量单位',
      dataIndex: 'unitCodeName',
    },
    {
      title: '需求数量',
      dataIndex: 'purchaseAmount',
    },

    {
      title: '需求日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '需求人',
      dataIndex: 'creatorName',
    },
    // {
    //   title: '采购员',
    //   dataIndex: 'taxCode',
    // },

    {
      title: '税率',
      dataIndex: 'taxRate',
      customRender({ text }) {
        return h('div', `${text}%`);
      },
    },
    {
      title: '单价（不含税）',
      dataIndex: 'noTaxPrice',
      isMoney: true,

    },
    {
      title: '总金额（不含税）',
      dataIndex: 'noTaxTotalAmt',
      isMoney: true,

    },
    {
      title: '单价（含税）',
      dataIndex: 'haveTaxPrice',
      isMoney: true,

    },
    {
      title: '总金额（含税）',
      dataIndex: 'haveTaxTotalAmt',
      isMoney: true,

    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ],
};

onMounted(() => {
  getMenus();
});

// 默认选中第一条数据
watchEffect(() => {
  if (menus.value?.length && !orderId.value) {
    orderId.value = menus.value[0]?.id;
    getOrderInfo();
    updateTable();
  }
});

async function getMenus() {
  loading.value = true;
  try {
    const result = await getPurOrderList({ id: unref(contractDetail).projectContractVO.id });
    menus.value = result.map((item) => ({
      ...item,
      // name: item.orderNumber,
      name: item.number,

    }));
  } finally {
    loading.value = false;
  }
}

const orderLoading: Ref<boolean> = ref(false);

// 获取采购订单信息
async function getOrderInfo() {
  if (!unref(orderId)) return;
  orderLoading.value = true;
  try {
    const orderData = await getPurchaseOrder(unref(orderId));
    orderDetail.value = orderData.projectPurchaseOrderInfoVO;
    orderDetail.value.isGuaranteeMoney = contractDetail.value?.projectContractVO.isGuaranteeMoney;
    orderDetail.value.guaranteeAmt = contractDetail.value?.projectContractVO.guaranteeAmt;
    orderTableData.value = orderData.projectPurchaseOrderDetailVOList;
    // console.log('222', orderData);
  } finally {
    orderLoading.value = false;
  }
}

function menuChange({ id }) {
  if (orderId.value === id) return;
  orderId.value = id;
  getOrderInfo();
  updateTable();
}

function updateTable() {
  nextTick(() => {
    tableRef.value.reload();
  });
}
async function checkProjectCallback(data) {
  disabledBtn.value = true;
  const schemeIds = await data.map((item) => item.id);
  new Api('/pas/projectContract/createPurchaseOrderInfo').fetch({
    projectContractDTO: { id: unref(contractDetail).projectContractVO.id },
    purchaseOrderIds: schemeIds,
  }, '', 'POST').then(() => {
    message.success('操作成功');
    getMenus();
  }).finally(() => {
    disabledBtn.value = false;
    updateTable();
  });
}
</script>

<style scoped>

</style>
