<template>
  <div>
    <WorkflowView
      v-if="deliverDetailsInfo.id"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
  </div>
</template>

<script>
import { WorkflowView, WorkflowProps } from 'lyra-workflow-component-vue3';
import {
  onMounted, reactive, toRefs, inject, Ref, computed, ref, ComputedRef, watchEffect,
} from 'vue';
import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api';
import { stampDate } from '/@/views/pms/projectLaborer/utils/dateUtil';
export default {
  name: 'Index',
  components: { WorkflowView },
  props: {
    id: String,
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const processViewRef = ref();
    const userStore = useUserStore();
    const formData = inject('projectInfo', {});
    const state = reactive({
      flowModel: null,
      id: props.id,
      form: {
        className: undefined,
        name: undefined,
        projectId: undefined,
      },
      userId: userStore.getUserInfo.id,
      templateList: [],
      approvalTableColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '编号',
          dataIndex: 'number',
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: 'status' },
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          slots: { customRender: 'modifyTime' },
        },
      ],
    });

    onMounted(() => {

    });

    // 获取流程按钮

    const deliverDetailsInfo = inject('deliverDetailsInfo');

    const workflowProps = computed(() => ({
      Api,
      businessData: deliverDetailsInfo.value,
      afterEvent: (type, props) => {
        processViewRef.value?.init();
      },
    }));

    return {
      ...toRefs(state),
      processViewRef,
      workflowProps,
      deliverDetailsInfo,
    };
  },
};
</script>

<style scoped></style>
