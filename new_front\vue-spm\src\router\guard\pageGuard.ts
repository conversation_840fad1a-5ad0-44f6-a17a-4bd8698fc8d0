import type { Router } from 'vue-router';
import { setRouteChange } from '/@/logics/mitt/routeChange';
import { guardQueue } from '/@/router';

export function createPageGuard(router: Router) {
  const loadedPageMap = new Map<string, boolean>();

  const beforeGuard = router.beforeEach(async (to) => {
    to.meta.loaded = !!loadedPageMap.get(to.path);
    // Notify routing changes
    setRouteChange(to);

    return true;
  });

  const afterGuard = router.afterEach((to) => {
    loadedPageMap.set(to.path, true);
  });

  guardQueue.push(beforeGuard);
  guardQueue.push(afterGuard);
}
