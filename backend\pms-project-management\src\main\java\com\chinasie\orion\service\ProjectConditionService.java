package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectConditionDTO;
import com.chinasie.orion.domain.entity.ProjectCondition;
import com.chinasie.orion.domain.vo.ProjectConditionVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ProjectConditionService  extends OrionBaseService<ProjectCondition> {


    /**
     *  详情
     *
     * * @param id
     */
    ProjectConditionVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectConditionDTO
     */
    String create(ProjectConditionDTO projectConditionDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectConditionDTO
     */
    Boolean edit(ProjectConditionDTO projectConditionDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectConditionVO> pages(Page<ProjectConditionDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectConditionVO> vos)throws Exception;


    /**
     *  定时插入项目数据
     *
     * * @param searchConditions
     * * @param response
     */
    void insertProjectConditionData() throws Exception;
}
