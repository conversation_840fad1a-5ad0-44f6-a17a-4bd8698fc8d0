<script setup lang="ts">
import {
  BasicModal, DataStatusTag, isPower, OrionTable, useModalInner,
} from 'lyra-component-vue3';
import {
  computed, h, inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

const route = useRoute();
const tableRef:Ref = ref();
const dataId: Ref<string> = ref(route.query.id as string);
const paramsData = ref();
let projectId: any = inject('projectId');
const visibleModal:Ref<boolean> = ref(false);
const [register, { closeModal, changeOkLoading }] = useModalInner(() => {
  visibleModal.value = true;
});
const emits = defineEmits<{
  (e:'checkProjectCallback', data):void
}>();
const router = useRouter();
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  rowSelection: {
    type: 'checkbox',
  },
  api: (params) => new Api('/pas/risk-management/getPage').fetch({
    ...params,
    query: {
      questionId: route.query.itemId,
      projectId: projectId.value,
    },

  }, '', 'POST'),
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      key: 'number',

      width: '120px',
      // sorter: true,
      ellipsis: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      align: 'left',
      minWidth: 220,
      // sorter: true,
      ellipsis: true,
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: 'action-btn',
            title: text,
            onClick(e) {
              router.push({
                name: 'RiskManagementDetails',
                query: {
                  itemId: record.id,
                  projectId: projectId.value,
                  type: 0,
                },
              });
            },
          },
          text,
        );
      },
    },
    {
      title: '风险描述',
      dataIndex: 'remark',
      key: 'remark',
      width: '120px',
      align: 'left',
      slots: { customRender: 'riskInfluenceName' },
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      key: 'dataStatus',

      width: '120px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },

    {
      title: '风险类型',
      dataIndex: 'riskTypeName',
      key: 'riskType',
      width: '80px',
      margin: '0 20px 0 0',
      align: 'left',
      slots: { customRender: 'riskTypeName' },
      ellipsis: true,
    },
    {
      title: '发生概率',
      dataIndex: 'riskProbabilityName',
      key: 'riskProbabilityName',
      width: '100px',
      align: 'left',
      slots: { customRender: 'riskProbabilityName' },
      ellipsis: true,
    },
    {
      title: '影响程度',
      dataIndex: 'riskInfluenceName',
      key: 'riskInfluenceName',

      width: '120px',
      align: 'left',
      slots: { customRender: 'riskInfluenceName' },
      ellipsis: true,
    },
    {
      title: '预估发生时间',
      dataIndex: 'predictStartTimeName',
      key: 'predictStartTimeName',
      width: '120px',
      align: 'left',
      slots: { customRender: 'predictStartTimeName' },
      // sorter: true,
      ellipsis: true,
    },

    {
      title: '负责人',
      dataIndex: 'principalName',
      key: 'principalName',

      width: '120px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'principalName' },
    },

    {
      title: '期望完成时间',
      dataIndex: 'createTime',
      key: 'createTime',

      width: '170px',
      align: 'left',
      // sorter: true,
      ellipsis: true,
      slots: { customRender: 'createTime' },
    },

    {
      title: '应对策略',
      dataIndex: 'copingStrategyName',
      key: 'copingStrategyName',
      width: '120px',
      align: 'left',
      ellipsis: true,
    },
    {
      title: '应对措施',
      dataIndex: 'solutions',
      key: 'solutions',
      width: '120px',
      align: 'left',
      ellipsis: true,
      slots: { customRender: 'solutions' },
    },
  ],

};

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (visibleModal.value = visible);
}
async function onOk() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length) {
    const data:Record<string, any> = selectRows;
    changeOkLoading(true);
    try {
      emits('checkProjectCallback', data);
      closeModal();
    } finally {
      changeOkLoading(false);
    }
  } else {
    message.warn('请选择关联风险');
  }
}
defineExpose({
  getSelectRows: computed(() => tableRef.value.getSelectRows),
});
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    width="1200px"
    :height="500"
    title="关联风险"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <div style="height: 500px;overflow: hidden">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
      />
    </div>
  </BasicModal>
</template>

<style scoped lang="less">
.clue{
  width: 560px;
}
:deep(.ant-basic-form){
  padding:0 !important;
}
</style>
