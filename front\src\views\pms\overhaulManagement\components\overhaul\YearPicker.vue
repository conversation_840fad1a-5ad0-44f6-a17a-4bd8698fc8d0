<script setup lang="ts">
import { Icon } from 'lyra-component-vue3';
import { computed, ref, Ref } from 'vue';
import dayjs from 'dayjs';

const props = defineProps<{
  years: number[]
}>();

const emits = defineEmits<{
  (e: 'update:years', year: number[]): void
}>();

const startYear: Ref<number> = ref(props.years[0]);
const endYear = computed(() => startYear.value + 1);

function handlePrev() {
  if (startYear.value > 2000) {
    startYear.value--;
  }
  emits('update:years', [startYear.value, endYear.value]);
}

function handleNext() {
  startYear.value++;
  emits('update:years', [startYear.value, endYear.value]);
}
</script>

<template>
  <div class="year-picker">
    <div
      class="prev btn"
      @click="handlePrev"
    >
      <Icon icon="orion-icon-left" />
    </div>
    <div class="year">
      <span>{{ startYear }}</span>~
      <span>{{ endYear }}</span>
    </div>
    <div
      class="next btn"
      @click="handleNext"
    >
      <Icon icon="orion-icon-right" />
    </div>
  </div>
</template>

<style scoped lang="less">
.year-picker {
  display: flex;
  align-items: center;
  user-select: none;

  .btn {
    background-color: #EEEEEE;
    padding: 5px 10px;
    cursor: pointer;
  }

  .year {
    background-color: #FAFAFA;
    padding: 0 10px;
    height: 32px;
    display: flex;
    align-items: center;
  }
}
</style>
