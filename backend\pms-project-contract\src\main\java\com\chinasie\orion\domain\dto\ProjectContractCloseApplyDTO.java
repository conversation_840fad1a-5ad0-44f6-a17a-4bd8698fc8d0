package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * ProjectContractCloseApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
@ApiModel(value = "ProjectContractCloseApplyDTO对象", description = "项目合同关闭申请")
@Data
public class ProjectContractCloseApplyDTO extends ObjectDTO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @NotBlank(message = "合同编号不能为空")
    private String contractNumber;

    /**
     * 合同关闭申请单号
     */
    @ApiModelProperty(value = "合同关闭申请单号")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applyUserId;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private Boolean applyDate;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    @Size(max = 1000, message = "关闭原因过长，建议控制在1000字符以内")
    private String closeReason;

}
