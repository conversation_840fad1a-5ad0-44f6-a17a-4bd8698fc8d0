<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  DataStatusTag,
  IOrionTableActionItem,
  isPower,
  OrionTable,
} from 'lyra-component-vue3';
import {
  h, inject, nextTick, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import { openPathSavingForm } from '/@/views/pms/majorRepairs/utils';
import Api from '/@/api';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { parseBooleanToRender } from '/@/views/pms/utils/utils';
import { processSearchConditions } from '/@/views/pms/majorRepairsSecond/pages/utils/util';
import { usePathExcel } from '/@/views/pms/majorRepairsSecond/hooks/useImportAndExport';
import { isBoolean } from 'lodash-es';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const { exportApi } = usePathExcel();
async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}

const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  smallSearchField: ['jobManageNumber'],
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '中心名称',
      dataIndex: 'jobManageCenter',
    },
    {
      title: '工单号',
      dataIndex: 'jobManageNumber',
    },
    {
      title: '工作抬头',
      dataIndex: 'workJobTitle:',
    },
    {
      title: '工作名称',
      dataIndex: 'jobManageName',
    },
    {
      title: '大修轮次',
      dataIndex: 'majorRepairTurn',
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      customRender({ text }) {
        return text
          ? '是'
          : isBoolean(text) && !text ? '否' : '';
      },
    },
    {
      title: '实际结束时间',
      dataIndex: 'actualEndTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '大修状态',
      dataIndex: 'majorRepairStatusName',
    },
    {
      title: '关键路径是否节约',
      dataIndex: 'isEconomize',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '是否可沿用',
      dataIndex: 'isContinueUse',
      customRender({ text, record }) {
        return record.status === 130
          ? parseBooleanToRender(text)
          : '';
      },
    },
    {
      title: '业务状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/major-repair-plan-economize').fetch({
    ...params,
    searchConditions: [],
    query: {
      majorRepairTurn: detailsData?.repairRound,
      keyword: processSearchConditions(params),
    },
    power: {
      pageCode: 'PMSMajorRepairsSecondDetail',
      containerCode: 'PMS_DXXQEC_container_06',
    },
  }, 'page', 'POST'),
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_DXXQEC_container_06_button_03', record?.rdAuthList),
  },
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_DXXQEC_container_06_button_02', record?.rdAuthList) && record?.status !== 130,
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record) => isPower('PMS_DXXQEC_container_06_button_01', record?.rdAuthList) && record?.status !== 130,
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id);
      break;
    case 'edit':
      openPathSavingForm(record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除当前数据？',
        onOk: () => deleteApi([record?.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSMajorRepairsPathDetails',
    params: {
      id,
    },
  });
}

function handleDelete() {
  Modal.confirm({
    title: '删除提示！',
    content: '确认删除已选择的数据？',
    onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
  });
}

function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/major-repair-plan-economize/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((err) => {
      reject(err);
    });
  });
}
</script>

<template>
  <div class="path-save">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_DXXQEC_container_06_button_04']"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openPathSavingForm({
            repairRound:detailsData?.repairRound
          },updateTable)"
        >
          新增
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_DXXQEC_container_06_button_05']"
          icon="sie-icon-shanchu"
          :disabled="selectedRows.length===0"
          @click="handleDelete"
        >
          删除
        </BasicButton>
        <BasicButton
          icon="sie-icon-daochu"
          @click="exportApi({
            repairRound:detailsData?.repairRound,
          })"
        >
          导出
        </BasicButton>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.path-save {
  height: 500px;
  overflow: hidden;
}
</style>
