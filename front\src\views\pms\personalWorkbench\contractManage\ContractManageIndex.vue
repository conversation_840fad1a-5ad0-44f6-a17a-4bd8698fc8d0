<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template #affiliation="{ record }">
        <span
          class="action-btn"
          @click="projectDetail(record)"
        >{{ record.projectName }}</span>
      </template>
      <template #toolbarLeft>
        <!--        <AddButton @click="onAdd">-->
        <!--          创建合同-->
        <!--        </AddButton>-->
        <BasicButton
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>

    <AddContractDrawer @register="addContractRegister" />
  </Layout>
</template>

<script setup lang="ts">
import { h, ref, inject } from 'vue';
import {
  AddButton, BasicButton, BasicTitle1, OrionTable, useDrawer, Layout,
} from 'lyra-component-vue3';
import { formatMoney } from '/@/views/pms/utils/utils';
import {
  message, Modal, Progress, Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { AddContractDrawer } from './components';
import Api from '/@/api';

const router = useRouter();
const props = defineProps<{
  projectId: string
}>();

const tableRef = ref();

const [addContractRegister, { openDrawer }] = useDrawer();
const detail:any = inject('allData', {});
const route = useRoute();
const selectRows = ref([]);
function onAdd() {
  openDrawer(true, {
    type: 'add',
    projectId: props.projectId,
    successChange() {
      tableReload();
    },
  });
}
function selectionChange({ rows }) {
  selectRows.value = rows;
}
// 项目详情页
const projectDetail = (row) => {
  router.push({
    name: 'MenuComponents',
    query: {
      id: row.projectId,
    },
  });
};
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的合同？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pas/projectContract').fetch(ids, '', 'DELETE')
      .then(() => {
        tableReload();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function tableReload() {
  tableRef.value?.reload();
}
const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  isFilter2: true,
  filterConfigName: 'PMS_PERSONALWORKBENCH_CONTRACTMANAGE_CONTRACTMANAGEINDEX',
  // smallSearchField: ['number', 'name', 'projectName', 'signedMainName'],
  api: (params) => new Api('/pas/projectContract/userPage').fetch(setSearch(params), '', 'POST'),
  columns: [
    {
      title: '合同编号',
      dataIndex: 'number',
    },
    {
      title: '合同名称',
      dataIndex: 'name',
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'ContractManageDetail',
              query: {
                id: record.id,
              },
            });
          },
        }, text);
      },
    },
    {
      title: '所属项目',
      dataIndex: 'projectName',
      width: 300,
      slots: { customRender: 'affiliation' },
    },
    {
      title: '合同状态',
      dataIndex: 'dataStatus',
      slots: { customRender: 'status' },
    },
    {
      title: '合同类别',
      dataIndex: 'contractCategoryName',
    },
    {
      title: '合同类型',
      dataIndex: 'contractTypeName',
    },
    {
      title: '供应商',
      dataIndex: 'signedMainName',
    },
    {
      title: '合同总金额',
      dataIndex: 'contractMoney',
      align: 'right',
      customRender({ text }) {
        return h('span', formatMoney(text));
      },
    },
    {
      title: '合同支付进度',
      dataIndex: 'paymentSchedule',
      customRender({
        record,
        text,
      }) {
        return h(Tooltip, {
          class: 'tooltip-class',
          title: record.payPercentage === 0 ? `未支付金额：${formatMoney(record.contractMoney || 0) || ''}` : `未支付金额：${formatMoney(record.payAmt || 0) || ''}`,
        }, h(Progress, {
          percent: isNaN(Number(text)) ? 0 : Number(Number(Number(text) * 100).toFixed()),
          size: 'small',
        }));
      },
    },
    {
      title: '合同负责人',
      dataIndex: 'principalName',
    },
    {
      title: '合同开始日期',
      dataIndex: 'startDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },

    {
      title: '合同结束日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },

    {
      title: '合同签订日期',
      dataIndex: 'signDate',
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD') : '');
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 200,
      customRender({ text }) {
        return h('span', text ? dayjs(text)
          .format('YYYY-MM-DD HH:mm:ss') : '');
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 160,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record) {
        router.push({
          name: 'ContractManageDetail',
          query: {
            id: record.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow(record) {
        return record.dataStatus?.statusValue === 101;
      },
      onClick(record) {
        openDrawer(true, {
          type: 'edit',
          projectId: props.projectId,
          editData: record,
          successChange() {
            tableReload();
          },
        });
      },
    },
    {
      text: '删除',
      isShow(record) {
        return record.dataStatus?.statusValue === 101;
      },
      modal(record) {
        return new Api('/pas/projectContract').fetch([record.id], '', 'DELETE').then(() => {
          tableReload();
          message.success('删除成功');
        });
      },
    },
  ],
};
function setSearch(params) {
  if (params?.searchConditions) {
    const search = params.searchConditions.map((item: Record<string, any>) => ({
      name: item?.[0]?.values?.[0],
    }));
    params.query = search[0];
    delete params?.searchConditions;
  }
  return params;
}
</script>

<style scoped lang="less">
.title{
  margin-left: 20px;
}
</style>
