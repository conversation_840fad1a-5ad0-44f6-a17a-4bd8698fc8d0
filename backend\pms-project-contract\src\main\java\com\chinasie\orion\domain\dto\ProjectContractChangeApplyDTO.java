package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * ProjectContractChangeApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@ApiModel(value = "ProjectContractChangeApplyDTO对象", description = "项目合同变更申请信息")
@Data
public class ProjectContractChangeApplyDTO extends ObjectDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空")
    private String contractId;

    /**
     * 变更申请单号
     */
    @ApiModelProperty(value = "变更申请单号")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applyUserId;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @NotBlank(message = "变更原因不能为空")
    @Size(max = 1000, message = "变更原因过长，建议控制在1000字符以内")
    private String changeReason;


    /**
     * 合同节点是否变更
     */
    @ApiModelProperty(value = "合同节点是否变更")
    private Boolean isPayNode;

    /**
     * 合同附件是否变更
     */
    @ApiModelProperty(value = "合同附件是否变更")
    private Boolean isContractFile;

    /**
     * 父变更id
     */
    @ApiModelProperty(value = "父变更id")
    private String parentId;

}
