import Wrap from './Wrap.vue';
import WrapChange from './components/WrapChange.vue';
import ProjectBasicInfo from './ProjectBasicInfo.vue';
import Milestone from './Milestone.vue';
import ProjectWarning from './ProjectWarning.vue';
import ProjectHours from './ProjectHours.vue';
import ProjectMaterials from './ProjectMaterials.vue';
import ProjectRevenue from './ProjectRevenue.vue';
import ProjectBudget from './ProjectBudget.vue';
import ProjectScheme from './ProjectScheme.vue';
import ProjectLifeMonitor from './components/ProjectLifeMonitor.vue';
import AbnormalPlanDetail from './components/AbnormalPlanDetail.vue';
import ProjectInExpenditure from './components/ProjectInExpenditure.vue';
import ProjectKnowledgeDocument from './components/ProjectKnowledgeDocument.vue';
import CostBudget from './CostBudget/index.vue';
import GanteView from './GanteView.vue';
import ProjectRate from './projectRate.vue';

export {
  Wrap,
  ProjectBasicInfo,
  Milestone,
  WrapChange,
  ProjectWarning,
  ProjectHours,
  ProjectMaterials,
  ProjectRevenue,
  ProjectBudget,
  ProjectScheme,
  ProjectLifeMonitor,
  AbnormalPlanDetail,
  ProjectInExpenditure,
  ProjectKnowledgeDocument,
  CostBudget,
  GanteView,
  ProjectRate,
};
