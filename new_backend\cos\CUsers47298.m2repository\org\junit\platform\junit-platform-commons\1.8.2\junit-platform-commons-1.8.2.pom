<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.junit.platform</groupId>
  <artifactId>junit-platform-commons</artifactId>
  <version>1.8.2</version>
  <name>JUnit Platform Commons</name>
  <description>Module "junit-platform-commons" of JUnit 5.</description>
  <url>https://junit.org/junit5/</url>
  <licenses>
    <license>
      <name>Eclipse Public License v2.0</name>
      <url>https://www.eclipse.org/legal/epl-v20.html</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>bechte</id>
      <name>Stefan Bechtold</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jlink</id>
      <name>Johannes Link</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>marcphilipp</id>
      <name>Marc Philipp</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>mmerdes</id>
      <name>Matthias Merdes</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>sbrannen</id>
      <name>Sam Brannen</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>sormuras</id>
      <name>Christian Stein</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>juliette-derancourt</id>
      <name>Juliette de Rancourt</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/junit-team/junit5.git</connection>
    <developerConnection>scm:git:git://github.com/junit-team/junit5.git</developerConnection>
    <url>https://github.com/junit-team/junit5</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.8.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>org.apiguardian</groupId>
      <artifactId>apiguardian-api</artifactId>
      <version>1.1.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
