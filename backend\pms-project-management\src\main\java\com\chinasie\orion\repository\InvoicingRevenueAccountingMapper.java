package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * InvoicingRevenueAccounting Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:28:31
 */
@Mapper
public interface InvoicingRevenueAccountingMapper extends OrionBaseMapper<InvoicingRevenueAccounting> {

    InvoicingRevenueAccountingVO getTotal(@Param("contractId") String contractId);

    List<InvoicingRevenueAccountingVO> getMilestoneTotal(@Param("milestoneIds") List<String> milestoneIds);
}

