package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.math.BigDecimal;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateExpenseSubject DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-06 10:05:25
 */
@ApiModel(value = "ProjectApprovalEstimateExpenseSubjectDTO对象", description = "概算科目")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateExpenseSubjectDTO extends ObjectDTO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateExpenseSubjectDTO> {

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    private String name;


    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @ExcelProperty(value = "项目立项id ", index = 0)
    private String projectApprovalId;

    /**
     * 概算金额
     */
    @ApiModelProperty(value = "概算金额")
    @ExcelProperty(value = "概算金额 ", index = 1)
    private BigDecimal amount;

    /**
     * 父级
     */
    @ApiModelProperty(value = "父级")
    @ExcelProperty(value = "父级 ", index = 2)
    private String parentId;

//    /**
//     * 是否必填
//     */
//    @ApiModelProperty(value = "是否必填")
//    @ExcelProperty(value = "是否必填 ", index = 3)
//    private Boolean required;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 4)
    private String number;

    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    @ExcelProperty(value = "公式 ", index = 5)
    private String formula;

    /**
     * 子项
     */
    @ApiModelProperty(value = "子项")
    private List<ProjectApprovalEstimateExpenseSubjectDTO> children;



}
