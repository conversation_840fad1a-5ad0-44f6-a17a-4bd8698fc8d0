package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectInternalAssociation Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-16 14:25:49
 */
@ApiModel(value = "ProjectInternalAssociationVO对象", description = "项目内部关联表")
@Data
public class ProjectInternalAssociationVO extends ObjectVO implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 内部类型
     */
    @ApiModelProperty(value = "内部类型")
    private String type;

    /**
     * 内部数据id
     */
    @ApiModelProperty(value = "内部数据id")
    private String innerId;

    /**
     * 内部名称
     */
    @ApiModelProperty(value = "内部名称")
    private String innerName;

}
