<script setup lang="ts">

import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import Api from '/@/api';
import { declarationData, updateDeclarationData } from '../keys';
import {
  computed, h, inject, onMounted, reactive, ref, unref, watch,
} from 'vue';
import {
  BasicButton, DataStatusTag, OrionTable, openModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Template from '/@/views/pms/projectLaborer/knowledgeEditData/Template.vue';
import CorrelationCueTable from '/@/views/pms/projectInitiation/pages/components/CorrelationCueTable.vue';

const tableRef = ref(null);
const tableClueRef = ref(null);
// 立项详情数据
const data = inject(declarationData);
const updateHandler = inject(updateDeclarationData);
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  rowSelection: {},
  dataSource: computed(() => data.value?.contract ?? []),
  columns: [
    {
      title: '销售合同号',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '项目合同名称',
      dataIndex: 'contractName',
      minWidth: 120,
    },
    // {
    //   title: '项目预估金额',
    //   dataIndex: 'estimateAmt',
    //   width: 120,
    // },
    {
      title: '客户编号',
      dataIndex: 'resDeptName',
      width: 100,
    },
    {
      title: '客户名称',
      dataIndex: 'cusName',
      width: 100,
    },
    {
      title: '客户关系',
      dataIndex: 'projectTypeName',
      width: 110,
    },
    {
      title: '客户关系',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 100,
    },
    {
      title: '合同金额',
      dataIndex: 'contractAmt',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 100,
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      customRender({ text }) {
        // return text ? dayjs(text).format('YYYY-MM-DD') : '';
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
    {
      title: '合同负责人',
      dataIndex: 'createTime',
      customRender({ text }) {
        // return text ? dayjs(text).format('YYYY-MM-DD') : '';
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {
      },
    },
  ],
};
const tableClueOptions = {
  rowSelection: {},
  deleteToolButton: 'add|enable|disable',
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'number',
  batchDeleteApi: async (params) => {
    const { ids = [] } = params ?? {};
    const source = computed(() => data.value?.clue?.result ?? []);
    const computedData = source.value.filter((item) => !ids.includes(item.number)).map((item) => item.number);
    await postConcatClue(computedData);
    tableRef.value.reload();
  },
  dataSource: computed(() => data.value?.clue?.result ?? []),
  columns: [
    {
      title: '线索编码',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '线索名称',
      dataIndex: 'name',
    },
    {
      title: '线索来源',
      dataIndex: 'sourceId',
      width: 110,
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {
      },
    },
  ],
};

const postConcatClue = async (keys) => {
  const body = {
    clueNumbers: keys.join(','),
    id: data.value?.projectInitiation?.id,
  };
  await new Api('/pms').fetch(body, 'projectInitiation/edit', 'PUT');
  await updateHandler?.();
};
const handleConcatClue = () => {
  const modal = openModal({
    title: '弹窗标题',
    width: 1000,
    content(h) {
      return h(CorrelationCueTable, {
        ref: tableClueRef,
      });
    },
    onOk: async () => {
      const getTableRef = tableClueRef.value.getTableRef();
      const params = getTableRef.getSelectRowKeys();
      if (!params.length) await Promise.reject(false);
      await postConcatClue(params);
      modal.close();
    },
  });
};
</script>

<template>
  <div class="associated-content">
    <DetailsLayout
      border-bottom
      title="关联项目合同"
    >
      <template #table />
      <OrionTable
        :options="tableOptions"
      >
        <template #name="{text,record}">
          <div
            class="action-btn"
            @click="handleDetail(record)"
          >
            {{ text }}
          </div>
        </template>
      </OrionTable>
    </DetailsLayout>
    <DetailsLayout
      border-bottom
      title="关联线索"
    >
      <template #table />
      <OrionTable
        ref="tableRef"
        :options="tableClueOptions"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="orion-icon-reload"
            @click="handleConcatClue"
          >
            关联线索
          </BasicButton>
        </template>
        <template #name="{text,record}">
          <div
            class="action-btn"
            @click="handleDetail(record)"
          >
            {{ text }}
          </div>
        </template>
      </OrionTable>
    </DetailsLayout>
  </div>
</template>

<style lang="less">
.associated-content {
  .details-container-content {
    padding-bottom: ~`getPrefixVar('content-padding-top')`;
  }

  div[data-qiankun="pms"] .table-header-hide .ant-basic-table .orion-table-header-wrap {
    display: flex;
  }
}

</style>