package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.domain.entity.projectStatistics.DeliverableStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;

import com.chinasie.orion.service.DeliverableService;
import com.chinasie.orion.service.projectStatistics.DeliverableStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ProjectDeliverableStatisticXxlJob {
    @Autowired
    private DeliverableService deliverableService;

    @Autowired
    private DeliverableStatusStatisticsService deliverableStatusStatisticsService;

    @XxlJob("projectDeliverableStatisticDailyCount")
    public void projectDeliverableStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<DeliverableStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeliverableStatusStatistics :: getDateStr,nowDate);
        deliverableStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<Deliverable> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(Deliverable :: getProjectId);
        schemeLambdaQueryWrapper.groupBy(Deliverable :: getProjectId);
        schemeLambdaQueryWrapper.leftJoin(ProjectScheme.class,ProjectScheme::getId,Deliverable::getPlanId);
        schemeLambdaQueryWrapper.select(" t.project_id projectId," +
                "IFNULL( sum( CASE  WHEN t1.`status`=101 THEN 1 ELSE 0 END ), 0 ) unFinishedCount ," +
                "IFNULL( sum( CASE  WHEN t1.`status`=111 THEN 1 ELSE 0 END ), 0 ) as finishedCount," +
                "IFNULL( sum( CASE  WHEN t1.`status`=130 THEN 1 ELSE 0 END ), 0 ) as processCount");
        List<Map<String, Object>> maps = deliverableService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<DeliverableStatusStatistics> deliverableStatusStatisticsList = new ArrayList<>();
        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            DeliverableStatusStatistics deliverableStatusStatistics =new DeliverableStatusStatistics();
            deliverableStatusStatistics.setNowDay(new Date());
            deliverableStatusStatistics.setDateStr(nowDate);
            deliverableStatusStatistics.setProjectId(projectId);
            deliverableStatusStatistics.setUk(nowDate+":"+projectId);
            //deliverableStatusStatistics.setTypeId(type);
            deliverableStatusStatistics.setUnFinishedCount(Integer.parseInt(p.get("unFinishedCount").toString()));
            deliverableStatusStatistics.setFinishedCount(Integer.parseInt(p.get("finishedCount").toString()));
            deliverableStatusStatistics.setProcessCount(Integer.parseInt(p.get("processCount").toString()));
            deliverableStatusStatisticsList.add(deliverableStatusStatistics);
        });
        deliverableStatusStatisticsService.saveBatch(deliverableStatusStatisticsList);
    }
}
