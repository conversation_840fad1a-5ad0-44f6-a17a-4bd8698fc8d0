package com.chinasie.orion.constant;

/**
 * 技术配置人员人力成本验收状态
 */
public enum LaborCostAcceptanceStatusEnum {

    /**
     * 状态
     */
    UNSUBMIT(101, "未提交"),
    REJECT(140, "驳回"),
    COMPLATE(130, "已通过");


    private Integer key;

    private String desc;

    LaborCostAcceptanceStatusEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public Integer getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }


}
