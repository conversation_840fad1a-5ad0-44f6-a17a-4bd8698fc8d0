package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.DemandManagementService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:59
 * @description:
 */
@RestController
@RequestMapping("/demand-management")
@Api(tags = "需求管理")
public class DemandManagementController {

    @Resource
    private DemandManagementService demandManagementService;
    @Resource
    private DictBo dictBo;

    @ApiOperation("新增需求管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementDTO", dataType = "DemandManagementDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增需求管理", type = "需求管理", subType = "新增需求管理", bizNo = "")
    public ResponseDTO<String> saveDemandManagement(@RequestBody DemandManagementDTO demandManagementDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.saveDemandManagement(demandManagementDTO));
    }

    @ApiOperation("获取需求管理树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementQueryDTO", dataType = "DemandManagementQueryDTO")
    })
    @LogRecord(success = "【{USER{#logUserId}}】获取需求管理树", type = "需求管理", subType = "获取需求管理树", bizNo = "")
    @PostMapping(value = "/getTree")
    public ResponseDTO<List<DemandManagementTreeVO>> getDemandManagementTree(@RequestBody DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.getDemandManagementTree(demandManagementQueryDTO));
    }

    @ApiOperation("获取需求管理树 简洁版")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementQueryDTO", dataType = "DemandManagementQueryDTO")
    })
    @PostMapping(value = "/getSimpleTree")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求管理树 简洁版", type = "需求管理", subType = "获取需求管理树 简洁版", bizNo = "")
    public ResponseDTO<TreeSimpleVO> getDemandManagementSimpleTree(@RequestBody DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.getDemandManagementSimpleTree(demandManagementQueryDTO));
    }

    @ApiOperation("搜索需求管理 分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】搜索需求管理 分页", type = "需求管理", subType = "搜索需求管理 分页", bizNo = "")
    public ResponseDTO<PageResult<DemandManagementTreeVO>> getDemandManagementPage(@RequestBody PageRequest<DemandManagementDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(demandManagementService.getDemandManagementPage(pageRequest));
    }

    @ApiOperation("获取需求管理详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求管理详情", type = "需求管理", subType = "获取需求管理详情", bizNo = "{{#id}}")
    public ResponseDTO<DemandManagementVO> getDemandManagementDetail(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO<>(demandManagementService.getDemandManagementDetail(id));
    }

    @ApiOperation("编辑需求管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "demandManagementDTO", dataType = "DemandManagementDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑需求管理", type = "需求管理", subType = "编辑需求管理", bizNo = "")
    public ResponseDTO<Boolean> editDemandManagement(@RequestBody DemandManagementDTO demandManagementDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.editDemandManagement(demandManagementDTO));
    }

    @ApiOperation("批量删除需求管理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除需求管理", type = "需求管理", subType = "批量删除需求管理", bizNo = "")
    public ResponseDTO<Boolean> removeDemandManagement(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(demandManagementService.removeDemandManagement(ids));
    }


    @ApiOperation("批量与需求关联的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeRelationPlan")
    @LogRecord(success = "【{USER{#logUserId}}】批量与需求关联的关系", type = "需求管理", subType = "批量与需求关联的关系", bizNo = "")
    public ResponseDTO<Boolean> removeRelationPlan(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = demandManagementService.removeRelationPlan(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取需求来源")
    @GetMapping(value = "/demandSource")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求来源", type = "需求管理", subType = "获取需求来源", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getDemandSource() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.DEMAND_SOURCE));
    }

    @ApiOperation("获取需求类型")
    @GetMapping(value = "/demandType")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求类型", type = "需求管理", subType = "获取需求类型", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getDemandType() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.DEMAND_TYPE));
    }

    @ApiOperation("获取优先级")
    @GetMapping(value = "/priorityLevel")
    @LogRecord(success = "【{USER{#logUserId}}】获取优先级", type = "需求管理", subType = "获取优先级", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getPriorityLevel() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.PRIORITY_LEVEL));
    }


    @ApiOperation("新增关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @PostMapping(value = "/relation/plan")
    @LogRecord(success = "【{USER{#logUserId}}】新增关联计划", type = "需求管理", subType = "新增关联计划", bizNo = "")
    public ResponseDTO<Boolean> relationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.relationToPlan(relationToPlanDTO));
    }

    @ApiOperation("批量删除关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @DeleteMapping(value = "/relation/plan/batch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除关联计划", type = "需求管理", subType = "批量删除关联计划", bizNo = "")
    public ResponseDTO<Boolean> removeRelationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.removeRelationToPlan(relationToPlanDTO));
    }

    @ApiOperation("获取关联计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String"),
            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
    })
    @PostMapping(value = "/relation/plan/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取关联计划列表", type = "需求管理", subType = "获取关联计划列表", bizNo = "")
    public ResponseDTO<List<PlanDetailVo>> getPlanListByManagement(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        return new ResponseDTO<>(demandManagementService.getPlanListByManagement(id, planQueryDTO));
    }

    @ApiOperation("需求转任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String"),
            @ApiImplicitParam(name = "planDTOList", dataType = "List")
    })
    @PostMapping(value = "/demandChangePlans/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】需求转任务", type = "需求管理", subType = "需求转任务", bizNo = "")
    public ResponseDTO<Boolean> demandChangePlans(@PathVariable("id") String id, @RequestBody List<PlanDTO> planDTOList) throws Exception {
        return new ResponseDTO<>(demandManagementService.demandChangePlans(id, planDTOList));
    }


    @ApiOperation("获取需求列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
    })
    @PostMapping(value = "/search/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求列表", type = "需求管理", subType = "获取需求列表", bizNo = "")
    public ResponseDTO<PlanSearchDataVo> searchList(@RequestBody KeywordDto keywordDto) throws Exception {
        return new ResponseDTO<>(demandManagementService.searchList(keywordDto));
    }
}
