<template>
  <div class="productLibraryDetails5920 layoutPage5920">
    <div
      class="productLibraryDetails_content5920 layoutPage_content5920"
      :style="{ height: contentHeight + 130 + 'px' }"
    >
      <div class="productLibraryDetails_left">
        <basicTitle :title="'预览'">
          <pdmImage
            :img-url="pictureBase + formState.projectImage"
            :show-delete="formType == 'edit'"
            @deleteImgUrl="deleteImgUrl"
          />
        </basicTitle>
      </div>

      <div class="productLibraryDetails_right">
        <basicTitle :title="'基本信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :rules="formType == 'details' ? {} : rules"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item
                label="编号"
                name="code"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.number }}</span>
              </a-form-item>
              <a-form-item
                label="名称"
                name="name"
                :style="{ height: '15px' }"
              >
                <a-input
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.name"
                  style="width: 330px"
                  placeholder="请输入名称"
                />
                <span v-else>{{ formState.name }}</span>
              </a-form-item>
              <a-form-item
                label="所属项目"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.projectName }}</span>
              </a-form-item>
              <a-form-item label="问题内容">
                <aTextarea
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.content"
                  style="width: 330px"
                  placeholder="请输入问题内容"
                  show-count
                  :maxlength="255"
                  :rows="4"
                />
                <span
                  v-else
                  class="descriptionStyle"
                >{{ formState.content }}</span>
              </a-form-item>

              <a-form-item
                label="问题类型"
                name="questionType"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.questionType"
                  placeholder="请选择问题类型"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in questionType"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.questionTypeName }}</span>
              </a-form-item>
              <a-form-item
                label="问题来源"
                name="questionSource"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.questionSource"
                  placeholder="请选择问题来源"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in questionSource"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.questionSourceName }}</span>
              </a-form-item>
              <a-form-item
                label="严重程度"
                name="seriousLevel"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.seriousLevel"
                  placeholder="请选择严重程度"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in seriousLevel"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.seriousLevelName }}</span>
              </a-form-item>
              <!--name solutions copingStrategy principalId remark predictEndTime,proposedTime,predictStartTime exhibitorId seriousLevel questionSource riskType-->

              <a-form-item
                label="提出人"
                name="exhibitorId"
                :style="{ height: '15px' }"
              >
                <!-- <a-select
                v-model:value="newformdata.exhibitorId"
                :filter-option="filterHandle"
                placeholder="请选择提出人"
                :options="exhibitorId"
                @search="handleChange2"
                v-if="formType == 'edit'"
                style="width: 330px"
                show-search
              >
              </a-select>
              <span v-else>{{ formState.exhibitorIdName }}</span> -->
                <a-input
                  v-if="formType === 'edit'"
                  v-model:value="newformdata.exhibitor"
                  style="width: 330px"
                  placeholder="请输入提出人"
                />
                <span v-else>{{ formState.exhibitor }}</span>
              </a-form-item>

              <!-- YYYY-MM-DD HH:mm:ss -->
              <a-form-item
                label="提出时间"
                name="proposedTime"
                :style="{ height: '15px' }"
              >
                <a-date-picker
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.proposedTime"
                  style="width: 330px"
                  placeholder="年 / 月 / 日"
                />

                <span v-else>{{
                  formState.proposedTime ? dayjs(formState.proposedTime).format('YYYY-MM-DD') : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="期望完成时间"
                name="predictEndTime"
                :style="{ height: '15px' }"
              >
                <a-date-picker
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.predictEndTime"
                  style="width: 330px"
                  placeholder="年 / 月 / 日"
                />

                <span v-else>{{
                  formState.predictEndTime
                    ? dayjs(formState.predictEndTime).format('YYYY-MM-DD')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="接收人"
                name="recipient"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.recipient"
                  :filter-option="filterHandle"
                  placeholder="请输入接收人"
                  :options="recipient"
                  style="width: 330px"
                  show-search
                  @search="handleChange3"
                />
                <span v-else>{{ formState.recipientName }}</span>
              </a-form-item>
              <a-form-item
                label="负责人"
                name="principalId"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.principalId"
                  :filter-option="filterHandle"
                  placeholder="请输入负责人"
                  :options="roleOption"
                  style="width: 330px"
                  show-search
                  @search="handleChange"
                />
                <span v-else>{{ formState.principalName }}</span>
              </a-form-item>
              <a-form-item
                label="状态"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.statusName }}</span>
              </a-form-item>

              <a-form-item
                label="优先级"
                name="priorityLevel"
                :style="{ height: '15px' }"
              >
                <a-select
                  v-if="formType == 'edit'"
                  v-model:value="newformdata.priorityLevel"
                  placeholder="请选择优先级"
                  style="width: 330px"
                >
                  <a-select-option
                    v-for="(item, index) in priorityLevel"
                    :key="index"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
                <span v-else>{{ formState.priorityLevelName }}</span>
              </a-form-item>

              <a-form-item
                label="进度"
                name="schedule"
                :style="{ height: '15px' }"
              >
                <div
                  v-if="formType == 'edit'"
                  style="width: 330px"
                >
                  <!-- <a-input
                  v-if="formType == 'edit'"
                  placeholder="请输入进度"
                  v-model:value="newformdata.schedule"
                  suffix="%"
                  style="width: 330px"
                /> -->
                  <a-input-number
                    v-if="formType == 'edit'"
                    v-model:value="newformdata.schedule"
                    placeholder="请输入进度"
                    :min="0"
                    :max="100"
                    :formatter="
                      (value) => {
                        return value && `${value}%`;
                      }
                    "
                    :parser="(value) => value.replace('%', '')"
                  />
                </div>

                <span v-else>{{ formState.scheduleName }}</span>
              </a-form-item>

              <!-- <a-form-item label="预览图" v-if="formType == 'edit'">
              <upload @successChange="successChange" />
            </a-form-item> -->
              <a-form-item
                label="修改人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.modifyName }}</span>
              </a-form-item>
              <a-form-item
                label="修改时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.modifyTime
                    ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="创建人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.creatorName }}</span>
              </a-form-item>
              <a-form-item
                label="创建时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.createTime
                    ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>
      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
    </div>

    <!-- <checkDetails :data="dataRow" /> -->
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, nextTick, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  DatePicker, Form, Input, InputNumber, message, Select, Table,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { pictureBase } from '/@/views/pms/projectLaborer/api/picture';
import { roleListApi } from '/@/views/pms/projectLaborer/api/riskManege';
import {
  editQuestionApi,
  questionDetailsPageApi,
  questionLevelApi,
  questionSourceApi,
  questionTypeApi,
} from '/@/views/pms/projectLaborer/api/questionManage';
import { priorityLevelApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import dayjs from 'dayjs';
import upload from '/@/views/pms/projectLaborer/componentsList/upload/index.vue';
import pdmImage from '/@/views/pms/projectLaborer/componentsList/image/index.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import { isPower } from 'lyra-component-vue3';

export default defineComponent({
  // name: 'ProjectLabdetail',
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aSelect: Select,
    // aTable: Table,
    aInput: Input,
    aTextarea: Input.TextArea,
    pdmImage,
    basicTitle,
    //   checkDetails,
    // upload,
    newButtonModal,
    ADatePicker: DatePicker,
    ASelectOption: Select.Option,
    AInputNumber: InputNumber,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  emits: ['editSuccess'],
  setup(props, { emit }) {
    //   const route = useRoute();
    //   const layoutModelStore = layoutModel();
    const formRef = ref();
    const state = reactive({
      formType: 'details',
      formState: <any>{},
      /* 编辑表单 */
      newformdata: <any>{
        name: '',
        content: '',
        schedule: '',
        priorityLevel: '',
        principalId: '',
        recipient: '',
        predictEndTime: undefined,
        proposedTime: undefined,
        //   predictStartTime: '',
        //   exhibitorId: '',
        seriousLevel: '',
        questionSource: '',
        questionType: '',
        /* 识别人和负责人 回显处理字段 */
        //   exhibitorIdName: '',
        principalName: '',
        recipientName: '',
        exhibitor: '',
        id: '',
        projectId: '',
      },
      treeData: [],
      parentId: 1,
      oldFormState: <any>{},
      message: '',
      showVisible: false,
      btnType: '',
      dataRow: {},

      contentHeight: 500,

      // 负责人
      roleOption: <any>[],
      // 接收人
      recipient: <any>[],
      // 识别人
      // exhibitorId: <any>[],
      // 类型
      questionType: <any>[],
      // 问题来源
      questionSource: [],
      // 程度
      seriousLevel: [],
      // 预估发生时间
      // predictStartTime: [],
      // 应对策略
      priorityLevel: [],
      powerData: [],
    });
    let projectId: any = inject('projectId');
    state.powerData = inject('powerData');

    const state6 = reactive({
      btnObjectData: {
        edit: { show: computed(() => isPower('WT_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      },
    });
    const rules = {
      // name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
      // ownerName: [{ required: true, message: '请输入项目经理', trigger: 'blur' }],
      // number: [{ required: true, message: '请输入项目编号', trigger: 'blur' }]
      // startTime: [{ required: true, message: '请选择立项时间', trigger: 'blur' }],
      // endTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
      // projectEndTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }]
    };
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editFormData();
          break;
        case 'determine':
          okRow();
          break;
        case 'cancel':
          cancel();
          break;
      }
    };
    const editFormData = async () => {
      // console.log('点击了编辑');
      try {
        state.questionType = await questionTypeApi();
        state.questionSource = await questionSourceApi();
        state.seriousLevel = await questionLevelApi();
        //   state.predictStartTime = await startEffectRiskListApi();
        state.priorityLevel = await priorityLevelApi();
      } catch (err) {
      }

      /* <!--name solutions priorityLevel principalId,recipient, remark predictEndTime proposedTime predictStartTime exhibitorId seriousLevel questionSource questionType--> */
      for (let namex in state.newformdata) {
        if (namex === 'proposedTime' || namex === 'predictEndTime') {
          state.newformdata[namex] = state.formState[namex] ? dayjs(state.formState[namex]) : undefined;
        } else {
          state.newformdata[namex] = state.formState[namex];
        }
      }
      state.newformdata.principalId = state.newformdata.principalName;
      state.newformdata.recipient = state.newformdata.recipientName;
      // state.newformdata.exhibitorId = state.newformdata.exhibitorIdName;
      /* ---------- */
      state.formType = 'edit';
      state6.btnObjectData = {
        edit: { show: false },
        determine: { show: true },
        cancel: { show: true },
      };
    };
    const okRow = () => {
      formRef.value
        .validate()
        .then(() => {
          const newformdata666:any = JSON.parse(JSON.stringify(state.newformdata));
          if (!newformdata666.predictEndTime) {
            newformdata666.predictEndTime = null;
          } else {
            newformdata666.predictEndTime = newformdata666.predictEndTime
              ? newformdata666.predictEndTime
              : state.formState.predictEndTime;
          }
          if (!newformdata666.proposedTime) {
            newformdata666.proposedTime = null;
          } else {
            newformdata666.proposedTime = newformdata666.proposedTime
              ? newformdata666.proposedTime
              : state.formState.proposedTime;
          }
          /* 如果值不变,则赋值回旧值 */
          if (newformdata666.principalId === state.formState.principalName) {
            newformdata666.principalId = state.formState.principalId;
          }
          if (newformdata666.recipient === state.formState.recipientName) {
            newformdata666.recipient = state.formState.recipient;
          }
          // if (state.newformdata.exhibitorId == state.formState.exhibitorIdName) {
          //   state.newformdata.exhibitorId = state.formState.exhibitorId;
          // }
          // delete state.newformdata.exhibitorIdName;
          delete newformdata666.principalName;
          delete newformdata666.recipientName;
          editQuestionApi(newformdata666)
            .then(async () => {
              message.success('保存成功');
              emit('editSuccess');
              state6.btnObjectData = {
                edit: { show: computed(() => isPower('WT_container_button_02', state.powerData)) },
                determine: { show: false },
                cancel: { show: false },
              };
              state.formType = 'details';
              state.newformdata = {
                name: '',
                content: '',
                schedule: '',
                priorityLevel: '',
                principalId: '',
                recipient: '',
                predictEndTime: dayjs(''),
                proposedTime: dayjs(''),
                //   predictStartTime: '',
                //   exhibitorId: '',
                seriousLevel: '',
                questionSource: '',
                questionType: '',
                /* 识别人和负责人 */
                //   exhibitorIdName: '',
                principalName: '',
                recipientName: '',
                id: '',
                projectId: '',
                exhibitor: '',
              };
              await getDetail();
            })
            .catch(() => {});
        })
        .catch((error) => {
        });
    };

    const cancel = () => {
      state.formType = 'details';
      state.formState.projectImage = '';
      state6.btnObjectData = {
        edit: { show: computed(() => isPower('WT_container_button_02', state.powerData)) },
        determine: { show: false },
        cancel: { show: false },
      };
      getDetail();
    };

    onMounted(async () => {
      state.contentHeight = document.body.clientHeight - 365;
      await getDetail();
    });
    /* 获取详情 */
    const getDetail = () => {
      questionDetailsPageApi(props.id)
        .then((res) => {
          if (res) {
            //   console.log('测试🚀 ~ file: index.vue ~ line 55 ~ res', res);
            state.formState = { ...res };
            //   state.oldFormState = JSON.parse(JSON.stringify(res));
          }
        })
        .catch(() => {});
    };
      /* 图片上传回调 */
      //   const successChange = (data) => {
      //     state.formState.projectImage = data.imageId;
      //     state.newformdata.projectImage = data.imageId;
      //   };
    const deleteImgUrl = () => {
      state.formState.projectImage = '';
      state.newformdata.projectImage = '';
    };
    const handleChange = (value) => {
      state.roleOption = [];
      try {
        getRole(value, projectId.value, 'role');
      } catch (err) {
      }
    };
    const handleChange2 = (value) => {
      state.exhibitorId = [];

      try {
        getRole(value, projectId.value, 'role2');
        //   console.log('测试🚀🚀 ~~~ props.id', props.id);
      } catch (err) {
      }
    };
    const handleChange3 = (value) => {
      state.recipient = [];

      try {
        getRole(value, projectId.value, 'role3');
        //   console.log('测试🚀🚀 ~~~ props.id', props.id);
      } catch (err) {
      }
    };
    const getRole = async (value, idkey, typeString) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        nextTick(() => {
          const qq = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          if (typeString === 'role') {
            state.roleOption = qq;
          }
          if (typeString === 'role2') {
            state.exhibitorId = qq;
          }
          if (typeString === 'role3') {
            state.recipient = qq;
          }
        });
      });
    };
    const filterHandle = (inputValue, option) => option;
    return {
      ...toRefs(state),
      ...toRefs(state6),
      formRef,
      rules,
      cancel,
      okRow,
      editFormData,
      // successChange,
      deleteImgUrl,
      clickType,
      dayjs,
      handleChange,
      handleChange2,
      handleChange3,
      filterHandle,
      pictureBase,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/DetailStyle.less');
</style>
