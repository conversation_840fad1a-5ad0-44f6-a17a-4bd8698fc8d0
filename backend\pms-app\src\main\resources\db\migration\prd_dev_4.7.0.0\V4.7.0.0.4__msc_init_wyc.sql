INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1876190391968923648', '需求分发反馈', 'NODE_REQUIREMENT_FEEDBACK', 'vub01876189284676861952', NULL, 0, 'SYS', '1,2', '1', NULL, 'u0rn1813482156561539072', 1, NULL, '314j1000000000000000000', '2025-01-06 16:53:42', '314j1000000000000000000', '2025-01-06 16:54:11', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`, `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`, `business_node_id`, `type`, `template_flag`, `custom`) VALUES ('35rj1876190503176699904', 'BusinessNodeChannel', '314j1000000000000000000', '2025-01-06 16:54:09', '314j1000000000000000000', '2025-01-06 16:54:09', '314j1000000000000000000', NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', 1, 1, '0a0y1876190391968923648', 'SYS', b'1', 'vub01876189284676861952');

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01876189284676861952', '需求分发反馈', 'PMS_REQUIREMENT_FEEDBACK', '您有一条需求$name$收到新的反馈意见，请查看。', '1', 1, '', '314j1000000000000000000', '2025-01-06 16:49:18', '314j1000000000000000000', '2025-01-06 16:50:37', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');