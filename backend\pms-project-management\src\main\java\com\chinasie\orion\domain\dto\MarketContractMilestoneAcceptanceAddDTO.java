package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MarketContractMilestoneAcceptanceAddDTO extends  ObjectDTO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;

    /**
     * 验收人
     */
    @ApiModelProperty(value = "验收人")
    private String acceptanceUserId;

    /**
     * 验收人
     */
    @ApiModelProperty(value = "验收人名称")
    private String acceptanceUserName;

    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptDate;

    /**
     * 本次验收比例
     */
    @ApiModelProperty(value = "本次验收比例")
    private BigDecimal acceptanceRatio;


    @ApiModelProperty(value = "验收单")
    private List<MarketContractMilestoneAcceptanceAddMessageDTO> messageList;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    private List<FileDTO> fileList;


}
