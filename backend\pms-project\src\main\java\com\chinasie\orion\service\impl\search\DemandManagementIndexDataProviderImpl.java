package com.chinasie.orion.service.impl.search;


import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.repository.DemandManagementRepository;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.search.client.ClientDataProvider;
import com.chinasie.orion.search.common.domain.IndexData;
import com.chinasie.orion.search.common.domain.IndexDataCategory;
import com.chinasie.orion.search.common.domain.IndexDataDetail;
import com.chinasie.orion.search.common.domain.IndexDataType;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 需求 索引数据提供者.
 *
 * <AUTHOR>
 */
@Component
public class DemandManagementIndexDataProviderImpl implements ClientDataProvider {

    @Autowired
    private DemandManagementRepository demandManagementRepository;


    @Autowired
    private ProjectRepository projectRepository;

    @Override
    public String library() {
        return "PMS";
    }

    @Override
    public String module() {
        return "DemandManagement_DATA";
    }

    /**
     * 获取上次索引以来新增的数据.
     *
     * @param lastIndexTime 上次数据索引时间
     * @param limitSize     返回数据量大小
     * @return
     */
    @Override
    public List<IndexData> fetchIndexData(Date lastIndexTime, Integer limitSize) {
        return demandManagementRepository.fetchIndexData(lastIndexTime, limitSize);
    }

    /**
     * 获取被索引数据的详情.
     *
     * @param dataId 数据Id
     * @return
     */
    @Override
    public IndexDataDetail findDetailById(String dataId) {
        DemandManagement demandManagement =  demandManagementRepository.selectById(dataId);
        if(demandManagement == null){
            return null;
        }
        IndexDataDetail result = BeanCopyUtils.convertTo(demandManagement, IndexDataDetail::new);
        Project project =  projectRepository.selectById(demandManagement.getProjectId());
        if(project != null){
            result.setDeptId(project.getResDept());
        }
        result.setTitle(demandManagement.getName());
        result.setContent(demandManagement.getRemark());
        // 设置删除标志
        result.setDeleted(Objects.equals(demandManagement.getLogicStatus(), -1));
        result.getProps().put("status", demandManagement.getStatus());
        return result;
    }

    /**
     * 获取被索引数据的类别信息.
     *
     * @param categoryIds
     * @return
     */
    @Override
    public List<IndexDataCategory> fetchCategories(List<String> categoryIds) {
        return null;
    }

    /**
     * 获取别索引数据的类型信息.
     *
     * @param dataTypeIds
     * @return
     */
    @Override
    public List<IndexDataType> fetchDataTypes(List<String> dataTypeIds) {
        return null;
    }
}
