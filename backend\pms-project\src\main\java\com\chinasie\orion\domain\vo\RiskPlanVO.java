package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/2 14:42
 */
@Data
@ApiModel(value = "RiskPlanVO对象", description = "风险预案")
public class RiskPlanVO extends ObjectVO {

    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    private String riskProbability;
    @ApiModelProperty(value = "风险概率名称")
    private String riskProbabilityName;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    private String riskInfluence;
    @ApiModelProperty(value = "影响程度名称")
    private String riskInfluenceName;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;
    @ApiModelProperty(value = "风险类型名称")
    private String riskTypeName;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    private String solutions;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
}
