package com.chinasie.orion.service.impl.approval;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.FunctionEnums;
import com.chinasie.orion.constant.ProjectApprovalEstimateEnum;
import com.chinasie.orion.domain.dto.approval.FormulaDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateExpenseSubject;
import com.chinasie.orion.domain.vo.approval.FunctionVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateExpenseSubjectVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateTemplateExpenseSubjectMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateExpenseSubjectService;
import com.chinasie.orion.util.AviatorUtils;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ProjectApprovalEstimateTemplateExpenseSubject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@Service
@Slf4j
public class ProjectApprovalEstimateTemplateExpenseSubjectServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateTemplateExpenseSubjectMapper, ProjectApprovalEstimateTemplateExpenseSubject> implements ProjectApprovalEstimateTemplateExpenseSubjectService {


    @Autowired
    private AviatorUtils aviatorUtils;


    @Autowired
    private ClassRedisHelper classRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalEstimateTemplateExpenseSubjectVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubject projectApprovalEstimateTemplateExpenseSubject =this.getById(id);


        ProjectApprovalEstimateTemplateExpenseSubjectVO result = BeanCopyUtils.convertTo(projectApprovalEstimateTemplateExpenseSubject,ProjectApprovalEstimateTemplateExpenseSubjectVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     */
    @Override
    public  String create(ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubject projectApprovalEstimateTemplateExpenseSubject =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateExpenseSubjectDTO,ProjectApprovalEstimateTemplateExpenseSubject::new);
        this.save(projectApprovalEstimateTemplateExpenseSubject);
        return projectApprovalEstimateTemplateExpenseSubject.getId();
    }

    @Override
    public boolean createBatch(List<ProjectApprovalEstimateTemplateExpenseSubjectDTO> subjectList) throws Exception {
        if (CollectionUtil.isEmpty(subjectList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateExpenseSubject> condition = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateExpenseSubject. class);
        condition.eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId,subjectList.get(0).getEstimateTemplateId());
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = this.list(condition);

        List<String> subjects = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(list)){
            subjects = list.stream().map(ProjectApprovalEstimateTemplateExpenseSubject::getNumber).collect(Collectors.toList());
        }
        List<ProjectApprovalEstimateTemplateExpenseSubjectVO> tree = TreeUtils.tree(BeanCopyUtils.convertListTo(subjectList, ProjectApprovalEstimateTemplateExpenseSubjectVO::new));

        List<ProjectApprovalEstimateTemplateExpenseSubject> subject = new ArrayList<>();
        this.saveBatch(handleData(tree, "0", subject, subjects));
        return Boolean.TRUE;
    }

    private List<ProjectApprovalEstimateTemplateExpenseSubject> handleData(List<ProjectApprovalEstimateTemplateExpenseSubjectVO> subjectList,
                                                                   String parentId, List<ProjectApprovalEstimateTemplateExpenseSubject> subject, List<String> subjects) throws Exception {
        if (CollectionUtil.isNotEmpty(subjectList)) {
            for (ProjectApprovalEstimateTemplateExpenseSubjectVO projectApprovalEstimateExpenseSubjectDTO : subjectList) {
                if (subjects.contains(projectApprovalEstimateExpenseSubjectDTO.getNumber())){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"已存在相同数据，无法添加");
                }
                ProjectApprovalEstimateTemplateExpenseSubject projectApprovalEstimateExpenseSubject = BeanCopyUtils.convertTo(projectApprovalEstimateExpenseSubjectDTO, ProjectApprovalEstimateTemplateExpenseSubject::new);
                projectApprovalEstimateExpenseSubject.setParentId(parentId);
                projectApprovalEstimateExpenseSubject.setId(classRedisHelper.getUUID(ProjectApprovalEstimateTemplateExpenseSubject.class.getSimpleName()));
                subject.add(projectApprovalEstimateExpenseSubject);
                handleData(projectApprovalEstimateExpenseSubjectDTO.getChildren(), projectApprovalEstimateExpenseSubject.getId(),subject, subjects);
            }
        }
        return subject;
    }



    /**
     *  编辑
     *
     * * @param projectApprovalEstimateTemplateExpenseSubjectDTO
     */
    @Override
    public Boolean edit(ProjectApprovalEstimateTemplateExpenseSubjectDTO projectApprovalEstimateTemplateExpenseSubjectDTO) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubject projectApprovalEstimateTemplateExpenseSubject =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateExpenseSubjectDTO,ProjectApprovalEstimateTemplateExpenseSubject::new);

        this.updateById(projectApprovalEstimateTemplateExpenseSubject);
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectApprovalEstimateTemplateExpenseSubject approvalEstimateExpenseSubject = this.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplateExpenseSubject.class)
                .select(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId)
                .in(ProjectApprovalEstimateTemplateExpenseSubject::getId, ids).last("limit 1"));
        if (ObjectUtil.isEmpty(approvalEstimateExpenseSubject)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String projectApprovalId = approvalEstimateExpenseSubject.getEstimateTemplateId();
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplateExpenseSubject.class)
                .select(ProjectApprovalEstimateTemplateExpenseSubject::getNumber, ProjectApprovalEstimateTemplateExpenseSubject::getParentId, ProjectApprovalEstimateTemplateExpenseSubject::getId, ProjectApprovalEstimateTemplateExpenseSubject::getFormula)
                .eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId, projectApprovalId));
        if (CollectionUtil.isEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        Map<String, List<String>> parentMap = list.stream().collect(Collectors.groupingBy(ProjectApprovalEstimateTemplateExpenseSubject::getParentId, Collectors.mapping(ProjectApprovalEstimateTemplateExpenseSubject::getId, Collectors.toList())));
        List<String> deleteIdList = new ArrayList<>();
        getChildIdList(parentMap, deleteIdList, ids);
        List<String> deleteNumberList = new ArrayList<>();
        List<String> unDeleteFormulaList = new ArrayList<>();
        for (ProjectApprovalEstimateTemplateExpenseSubject projectApprovalEstimateExpenseSubject : list) {
            if (deleteIdList.contains(projectApprovalEstimateExpenseSubject.getId())) {
                deleteNumberList.add(projectApprovalEstimateExpenseSubject.getNumber());
            } else {
                unDeleteFormulaList.add(projectApprovalEstimateExpenseSubject.getFormula());
            }
        }
        if (unDeleteFormulaList.stream().filter(Objects::nonNull).anyMatch(a -> deleteNumberList.stream().anyMatch(a::contains))) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "存在科目的公式与要删除的科目有关联，删除失败！");
        }
        this.removeBatchByIds(deleteIdList);
        return true;
    }

    /**
     * 获取自身及子级ids
     * @param parentMap
     * @param childIdList
     * @param parentIdList
     */
    private void getChildIdList(Map<String, List<String>> parentMap,
                                List<String> childIdList,
                                List<String> parentIdList) {
        for (String parentId : parentIdList) {
            childIdList.add(parentId);
            if (parentMap.containsKey(parentId)) {
                getChildIdList(parentMap, childIdList, parentMap.get(parentId));
            }
        }
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalEstimateTemplateExpenseSubjectVO> pages( Page<ProjectApprovalEstimateTemplateExpenseSubjectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateExpenseSubject> condition = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateExpenseSubject. class);
        if (CollectionUtil.isNotEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectApprovalEstimateTemplateExpenseSubject::getCreateTime);


        Page<ProjectApprovalEstimateTemplateExpenseSubject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalEstimateTemplateExpenseSubject::new));

        PageResult<ProjectApprovalEstimateTemplateExpenseSubject> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalEstimateTemplateExpenseSubjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalEstimateTemplateExpenseSubjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalEstimateTemplateExpenseSubjectVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }



    @Override
    public void  setEveryName(List<ProjectApprovalEstimateTemplateExpenseSubjectVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }


    @Override
    public Boolean setFormula(FormulaDTO formulaDTO) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubject byId = this.getById(formulaDTO.getId());
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplateExpenseSubject.class)
                .eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId, byId.getEstimateTemplateId()));
        if (CollectionUtil.isEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        Map<String, List<ProjectApprovalEstimateTemplateExpenseSubject>> parentMap = list.stream().filter(f -> StrUtil.isNotBlank(f.getParentId())).collect(Collectors.groupingBy(ProjectApprovalEstimateTemplateExpenseSubject::getParentId));

        if (parentMap.containsKey(byId.getId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "只能在叶节点设置公式");
        }

        Map<String, Object> variableMap = new HashMap<>();
        for (ProjectApprovalEstimateTemplateExpenseSubject entity : list) {
            if (parentMap.containsKey(entity.getId())) {
                variableMap.put(entity.getNumber(), parentMap.get(entity.getId())
                        .stream().map(ProjectApprovalEstimateTemplateExpenseSubject::getNumber).collect(Collectors.joining("+")));
            } else if (StrUtil.isNotBlank(entity.getFormula())) {
                variableMap.put(entity.getNumber(), entity.getFormula());
            } else {
                variableMap.put(entity.getNumber(), 1);
            }
        }
        variableMap.put(ProjectApprovalEstimateEnum.PEOPLE_NUM.getKey(), 1);
        aviatorUtils.checkFormula(variableMap, formulaDTO.getFormula(), null, 0);
        byId.setFormula(formulaDTO.getFormula());
        byId.setFormulaName(formulaDTO.getFormulaName());
        return this.updateById(byId);
    }

    @Override
    public Boolean cancelFormula(String id) throws Exception {
        ProjectApprovalEstimateTemplateExpenseSubject byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        byId.setFormula("");
        byId.setFormulaName("");
        return this.updateById(byId);
    }

    @Override
    public List<FunctionVO> getFunctionList() {
        return Arrays.stream(FunctionEnums.values())
                .map(m -> new FunctionVO(m.getName(), m.getValue())).collect(Collectors.toList());
    }

    @Override
    public List<ProjectApprovalEstimateTemplateExpenseSubjectVO> getFormulaVariable(String estimateTemplateId) {
        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateExpenseSubject. class);
        lambdaQueryWrapperX.eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId, estimateTemplateId);
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = this.list(lambdaQueryWrapperX);
        List<ProjectApprovalEstimateTemplateExpenseSubjectVO> tree;
        if (CollectionUtil.isNotEmpty(list)){
            tree = TreeUtils.tree(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateTemplateExpenseSubjectVO::new));
        } else {
            tree = new ArrayList<>();
        }
        ProjectApprovalEstimateTemplateExpenseSubjectVO peopleNum = new ProjectApprovalEstimateTemplateExpenseSubjectVO();
        peopleNum.setId(IdUtil.simpleUUID());
        peopleNum.setNumber(ProjectApprovalEstimateEnum.PEOPLE_NUM.getKey());
        peopleNum.setName(ProjectApprovalEstimateEnum.PEOPLE_NUM.getDesc());
        tree.add(peopleNum);
        return tree;
    }
}
