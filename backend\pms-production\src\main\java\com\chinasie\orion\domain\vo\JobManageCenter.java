package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobManageCenter {


    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenter;

    @ApiModelProperty("中心名称")
    String centerName;

    String code;

    /**
     * 父级唯一值
     */
    String parentCode;

    List<JobManageTree> jobList;
}
