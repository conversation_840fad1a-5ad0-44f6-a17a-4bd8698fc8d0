<template>
  <div class="project-life">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <div
          class="every-table-slots"
        >
          <BasicButton
            v-if="isPower('TZJH_container_button_01', powerData)"
            icon="add"
            type="primary"
            @click="addTable('investment')"
          >
            申报投资计划
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_02', powerData)"
            icon="sie-icon-tiaozhengbumen"
            type="primary"
            @click="addTable('annual')"
          >
            调整投资计划
          </BasicButton>
          <BasicButton
            v-if="isPower('TZJH_container_button_03', powerData)"
            icon="edit"
            type="primary"
            :disabled="disabledEdit"
            @click="editTable"
          >
            编辑
          </BasicButton>

          <BasicButton
            v-if="isPower('TZJH_container_button_04', powerData)"
            icon="sie-icon-tijiao"
            type="primary"
            :disabled="!(selectedRows.length===1&&selectedRows[0].status===100)"
            :loading="loadingProcess"
            @click="changeProcess"
          >
            提交
          </BasicButton>

          <BasicButton
            v-if="isPower('TZJH_container_button_05', powerData)"
            icon="sie-icon-daochu"
            type="primary"
            :disabled="selectedRowKeys.length===0"
            @click="exportTable"
          >
            导出
          </BasicButton>

          <BasicButton
            v-if="isPower('TZJH_container_button_06', powerData)"
            icon="delete"
            type="primary"
            :disabled="disabledDelete"
            @click="deleteTable"
          >
            删除
          </BasicButton>
        </div>
      </template>

      <template #name="{record}">
        <div
          :title="record.name"
          class="flex-te"
          @click="changePage(record)"
        >
          <span
            class="action-btn table-row-name"
          >{{ record.name }}</span>
        </div>
      </template>
    </OrionTable>
    <!--      创建概算-->
    <AddEstimateTableNode
      @register="register"
      @update="updateE"
    />
    <!--      年度调整-->
    <AnnualInvestment
      @register="registerAnnual"
      @update="update"
    />
    <!--      投资计划-->
    <AddInvestmentPlan
      @register="registerPlan"
      @update="update"
    /> <!--流程-->
    <!--    <ProcessSelect-->
    <!--      v-show="false"-->
    <!--      v-if="showProcess"-->
    <!--      ref="processRef"-->
    <!--      :selectedRows="selectedRows"-->
    <!--      href="/pms/annualInvestment"-->
    <!--      :processName="processName"-->
    <!--      biz-catalog-name="申请年度计划审批"-->
    <!--    />-->
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, nextTick, provide, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicButton, downloadByData, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import AddEstimateTableNode from '../components/AddEstimateTableNode.vue';
import AnnualInvestment from '../components/AnnualInvestment.vue';
import {
  deleteYearPlanList, getInitFrmData, getYearPlanList, initTableColumns,
} from '../index';
import AddInvestmentPlan from '../components/AddInvestmentPlan.vue';
// import ProcessSelect from '/@/views/pms/projectManage/components/ProcessSelect.vue';
import Api from '/@/api';

export default defineComponent({
  name: 'InvestmentPlan',
  components: {
    // ProcessSelect,
    OrionTable,
    BasicButton,
    AddEstimateTableNode,
    AnnualInvestment,
    AddInvestmentPlan,
  },
  setup() {
    const powerData = inject('powerData', null);

    // 往子组件中注入, 根据实际情况，自行在子组件中接收  const powerData =  inject('powerData')
    // provide('powerData', powerData);
    const formData:Ref = inject('formData');
    const getFormData = inject('getFormData', null);
    const router = useRouter();
    const state = reactive({
      processName: '',
      // processName: computed(() => ),
      formId: formData.value?.id,
      params: {},
      showProcess: false,
      loadingProcess: false,
      selectedRowKeys: [],
      selectedRows: [],
      disabledDelete: computed(() => {
        if (state.selectedRowKeys.length === 0) {
          return true;
        }
        return !state.selectedRows.every((item) => item.status === 100);
      }),
      disabledEdit: computed(() => {
        if (state.selectedRows.length === 1) {
          if (state.selectedRows[0].status === 100) {
            return false;
          }
          if (state.selectedRows[0].status === 107 || state.selectedRows[0].status === 121) {
            return !state.selectedRows[0].isEdit;
          }
          return true;
        }
        return true;
      }),
    });
    const processRef = ref();
    const [register, { openDrawer }] = useDrawer();
    const [registerAnnual, { openDrawer: openDrawerAnnual }] = useDrawer();
    const [registerPlan, { openDrawer: openDrawerPlan }] = useDrawer();

    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys: computed(() => state.selectedRowKeys),
        onSelect: (record, selected, selectedRows, nativeEvent) => {
          state.selectedRows = selectedRows;
          state.selectedRowKeys = selectedRows.map((item) => item.id);
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          if (selected) {
            state.selectedRows = selectedRows;
            state.selectedRowKeys = selectedRows.map((item) => item.id);
          } else {
            state.selectedRows = [];
            state.selectedRowKeys = [];
          }
        },
      },
      // showIndexColumn: false,
      showSmallSearch: false,
      pagination: false,
      // dataSource: [],
      api: async (params) => {
        state.params = {
          searchConditions: params.searchConditions || [],
        };
        return getYearPlanList(params, formData.value?.id);
      },
      columns: initTableColumns(),
    });
    async function addTable(type = '') {
      if (type === 'investment') {
        getInitFrmData(formData.value?.id).then((res) => {
          openDrawerPlan(true, {
            type: 'add',
            investmentId: formData.value?.id,
            data: res,
          });
        }).catch((err) => {
          openDrawer(true, {
            type: 'add',
            data: { investmentSchemeId: formData.value?.id },
          });
        });
      } else {
        if (state.selectedRowKeys.length === 0) {
          const id = await new Api(`/pms/yearInvestmentScheme/canchange/${formData.value?.id}`).fetch('', '', 'post');
          openDrawerAnnual(true, {
            type: 'add',
            id,
          });
          return;
        }
        openDrawerAnnual(true, {
          type: 'edit',
          id: state.selectedRowKeys[0],
        });
      }
    }
    function editTable() {
      if (state.selectedRows[0].dataStatus.name.indexOf('调整') >= 0) {
        openDrawerAnnual(true, {
          type: 'edit',
          id: state.selectedRowKeys[0],
        });
      } else {
        openDrawerPlan(true, {
          type: 'edit',
          id: state.selectedRows[0].id,
        });
      }
    }
    function changePage(record) {
      router.push({
        name: 'AnnualInvestment',
        params: {
          id: record.id,
        },
      });
    }
    function changeProcess() {
      if (state.loadingProcess) return;
      state.showProcess = true;
      state.loadingProcess = true;
      let itemData = state.selectedRows[0];
      let name = `${itemData?.yearName}年投资计划`;
      if (itemData?.dataStatus?.name.indexOf('调整') < 0) {
        name += '申报表';
      } else {
        name += '调整申请表';
      }
      state.processName = name;
      nextTick(() => {
        processRef.value.bpmnMain.batchStartFlow(state.selectedRowKeys, () => {
          update();
        });
      });
    }
    function deleteTable() {
      Modal.confirm({
        title: '删除提示',
        content: '是否删除所选的数据？',
        onOk() {
          deleteYearPlanList(state.selectedRowKeys).then((res) => {
            message.success('删除数据成功');
            state.selectedRowKeys = [];
            tableRef.value.reload();
          });
        },
      });
    }
    function update(data) {
      state.selectedRowKeys = [];
      state.selectedRows = [];
      if (data.type === 'save') {
        tableRef.value.reload();
      } else {
        state.showProcess = true;
        state.loadingProcess = true;
        state.selectedRowKeys = [data.record.id];
        state.selectedRows = [data.record];
        let itemData = state.selectedRows[0];
        let name = `${itemData?.yearName}年投资计划`;
        if (itemData?.dataStatus?.name.indexOf('调整') < 0) {
          name += '申报表';
        } else {
          name += '调整申请表';
        }
        state.processName = name;
        nextTick(() => {
          processRef.value.bpmnMain.batchStartFlow([data.id], () => {
            // update();
          });
        });
      }
    }

    function exportTable() {
      Modal.confirm({
        title: '确认导出',
        content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
        onOk() {
          downloadByData('/pms/yearInvestmentScheme/export/v2', state.selectedRowKeys, '', 'POST', true, false, '导出处理完成，现在开始下载');
        },
      });
    }
    function reLoadTable() {
      state.showProcess = false;
      state.loadingProcess = false;
      message.success('提交流程成功');
      state.selectedRowKeys = [];
      state.selectedRows = [];
      tableRef.value.reload({ page: 1 });
    }
    provide('reLoadTable', reLoadTable);
    function updateE() {
      getFormData('updateE');
    }

    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      addTable,
      register,
      registerAnnual,
      changePage,
      deleteTable,
      registerPlan,
      changeProcess,
      editTable,
      update,
      processRef,
      exportTable,
      updateE,
      powerData,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
.project-life{
    height: 352px;
    overflow: hidden;
    position: relative;
}
</style>
