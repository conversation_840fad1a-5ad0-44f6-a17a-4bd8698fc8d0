package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ContractMilestone VO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneVO对象", description = "合同里程碑")
@Data
public class ContractMilestoneTreeVO extends ObjectVO implements Serializable {

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;


    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;


    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    private String parentId;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String busRspUser;


    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "计划验收日期")
    private Date planAcceptDate;


    /**
     * 关联合同编号
     */
    @ApiModelProperty(value = "关联合同编号")
    private String relationContractNumber;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "成本业务分类")
    private String costBusType;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "成本业务分类名称")
    private String costBusTypeName;


    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;


    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertDept;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;


    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String rspUser;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptDate;


    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;


    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String busRspUserName;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    private String undertDeptName;

    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    private Date expectAcceptDate;

    /**
     * 剩余天数
     */
    @ApiModelProperty(value = "剩余天数")
    private Long surplusDays;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名")
    private String cusPersonName;

    /**
     * 子里程碑
     */
    @ApiModelProperty(value = "子里程碑")
    private List<ContractMilestoneTreeVO> children;

}
