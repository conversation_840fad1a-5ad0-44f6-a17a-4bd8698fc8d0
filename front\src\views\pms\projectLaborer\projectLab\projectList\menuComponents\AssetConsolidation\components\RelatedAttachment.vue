<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import { computed, inject } from 'vue';
import { get } from 'lodash-es';

const detailsData = inject('detailsData');
const attachmentsListData = computed(() => get(detailsData, 'attachments') ?? []);
</script>

<template>
  <div class="related-attachments">
    <UploadList
      ref="contractFilesRef"
      :is-spacing="false"
      :listData="attachmentsListData"
      :height="200"
      :isFileDelete="false"
      :edit="false"
      type="modal"
    />
  </div>
</template>

<style scoped lang="less">
.related-attachments{
  padding-bottom: 20px;
}
</style>