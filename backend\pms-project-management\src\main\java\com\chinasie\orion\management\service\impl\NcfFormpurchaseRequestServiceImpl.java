package com.chinasie.orion.management.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.ContractLineInfoVO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.management.domain.vo.PurchaseRequestNumVO;
import com.chinasie.orion.management.repository.ContractLineInfoMapper;
import com.chinasie.orion.management.repository.NcfFormpurchaseRequestMapper;
import com.chinasie.orion.management.repository.NcfPurchProjectImplementationMapper;
import com.chinasie.orion.management.service.ContractInfoService;
import com.chinasie.orion.management.service.ContractSupplierRecordService;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfFormpurchaseRequest 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@Service
@Slf4j
public class NcfFormpurchaseRequestServiceImpl extends OrionBaseServiceImpl<NcfFormpurchaseRequestMapper, NcfFormpurchaseRequest> implements NcfFormpurchaseRequestService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Autowired
    private LyraFileBO fileBo;
    @Autowired
    private ContractInfoService contractInfoService;

    @Autowired
    private ContractSupplierRecordService contractSupplierRecordService;

    @Autowired
    private ContractLineInfoMapper contractLineInfoMapper;

    @Autowired
    private NcfPurchProjectImplementationMapper ncfPurchProjectImplementationMapper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormpurchaseRequestVO detail(String id, String pageCode) throws Exception {
        NcfFormpurchaseRequest ncfFormpurchaseRequest = this.getById(id);
        if(ncfFormpurchaseRequest != null){
            LambdaQueryWrapperX<ContractInfo> contractInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractInfoLambdaQueryWrapperX.eq(ContractInfo::getPurchaseApplicant, ncfFormpurchaseRequest.getCode());
            List<ContractInfo> list =  contractInfoService.list(contractInfoLambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(list)){
                ContractInfo contractInfo = list.get(0);
                String contractNumber = contractInfo.getContractNumber();
                LambdaQueryWrapperX<ContractSupplierRecord> supplierRecordLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                supplierRecordLambdaQueryWrapperX.eq(ContractInfo::getContractNumber, contractNumber);
                List<ContractSupplierRecord> supplierList = contractSupplierRecordService.list(supplierRecordLambdaQueryWrapperX);
                if(!CollectionUtils.isEmpty(supplierList)){
                    List<String> supplierNames = supplierList.stream().map(ContractSupplierRecord :: getSupplierName).collect(Collectors.toList());
                    ncfFormpurchaseRequest.setRecPtSupList(supplierNames.stream()
                            .collect(Collectors.joining(", ")));
                }
            }
        }

        NcfFormpurchaseRequestVO result = BeanCopyUtils.convertTo(ncfFormpurchaseRequest, NcfFormpurchaseRequestVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestDTO
     */
    @Override
    public String create(NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception {
        NcfFormpurchaseRequest ncfFormpurchaseRequest = BeanCopyUtils.convertTo(ncfFormpurchaseRequestDTO, NcfFormpurchaseRequest::new);
        this.save(ncfFormpurchaseRequest);

        String rsp = ncfFormpurchaseRequest.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestDTO
     */
    @Override
    public Boolean edit(NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception {
        NcfFormpurchaseRequest ncfFormpurchaseRequest = BeanCopyUtils.convertTo(ncfFormpurchaseRequestDTO, NcfFormpurchaseRequest::new);

        this.updateById(ncfFormpurchaseRequest);

        String rsp = ncfFormpurchaseRequest.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormpurchaseRequestVO> pages(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //采购立项时间
            if (pageRequest.getQuery().getStartDate() != null || pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormpurchaseRequest::getProjectEndTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
            //wbs编号，从申请明细查出wbsid，再查主表
            if (StringUtils.hasText(pageRequest.getQuery().getWbsId())) {
                List<String> projectCodes = namedParameterJdbcTemplate.queryForList("select project_code from ncf_form_purchase_request_detail where logic_status = 1 and wbs_id like :wbsId",
                        Collections.singletonMap("wbsId", "%" + pageRequest.getQuery().getWbsId() + "%"), String.class);
                //判空
                if (!CollectionUtils.isEmpty(projectCodes)) {
                    condition.in(NcfFormpurchaseRequest::getCode, projectCodes);
                }
            }

        }

        condition.orderByDesc(NcfFormpurchaseRequest::getCreateTime);


        Page<NcfFormpurchaseRequest> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequest::new));

        PageResult<NcfFormpurchaseRequest> page = this.getBaseMapper().selectPage(realPageRequest, condition);


        Page<NcfFormpurchaseRequestVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<NcfFormpurchaseRequestVO> getByCodePage(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NcfFormpurchaseRequest::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getContractNumber() == null) {
            throw new Exception("合同编号为空，请输入");
        }
        //根据合同编码查对应的采购申请编码，根据查询的采购申请编码，查询对应的采购申请数据
        LambdaQueryWrapperX<ContractInfo> infoQueryWrapper = new LambdaQueryWrapperX<>(ContractInfo.class);
        infoQueryWrapper.eq(ContractInfo::getContractNumber, pageRequest.getQuery().getContractNumber());
        List<ContractInfo> contractInfos = contractInfoService.list(infoQueryWrapper);
        List<String> codeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(contractInfos)) {
            codeList = contractInfos.stream().map(ContractInfo::getPurchaseApplicant).filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(codeList)) {
            condition.in(NcfFormpurchaseRequest::getCode, codeList);
        } else {
            return new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), pageRequest.getTotalSize());
        }
        Map<String,ContractInfo> contractInfoMap = contractInfos.stream().filter(item -> item.getPurchaseApplicant() != null).collect(Collectors.toMap(ContractInfo::getPurchaseApplicant, Function.identity(), (k1, k2) -> k1));
        Page<NcfFormpurchaseRequest> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormpurchaseRequest::new));

        PageResult<NcfFormpurchaseRequest> page = this.getBaseMapper().selectPage(realPageRequest, condition);


        Page<NcfFormpurchaseRequestVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormpurchaseRequestVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestVO::new);
        vos.forEach(vo ->{
            ContractInfo contractInfo = contractInfoMap.get(vo.getCode());
            if(contractInfo != null && contractInfo.getProjectEndTime() != null){
                vo.setProjectApprovalEndTime(contractInfo.getProjectEndTime().toString());
            }
        });
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<NcfFormpurchaseRequestVO> getBaseByCodePage(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception {


        LambdaQueryWrapperX<ContractLineInfo> condition = new LambdaQueryWrapperX<>(ContractLineInfo.class);
        condition.eq(ContractLineInfo::getContractNumber,pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractLineInfo::getCreateTime);
        List<ContractLineInfo> contractLineInfos = contractLineInfoMapper.selectList(condition);
        //收集采购申请号
        List<String> collect = contractLineInfos.stream().map(ContractLineInfo::getProcurementApplicantNumber).filter(Objects::nonNull).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(collect)){
            List<NcfPurchProjectImplementation> projectImplementations = ncfPurchProjectImplementationMapper.selectList(NcfPurchProjectImplementation::getEcpPurchaseAppNo,collect);
            if(!CollectionUtils.isEmpty(projectImplementations)){
                List<String> purchReqDocCodes = projectImplementations.stream().map(NcfPurchProjectImplementation::getPurchReqDocCode).filter(Objects::nonNull).collect(Collectors.toList());
                collect.addAll(purchReqDocCodes);
            }
        }
        Page<NcfFormpurchaseRequestVO> pageResult = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        if(CollectionUtils.isEmpty(collect)){
            return pageResult;
        }
        LambdaQueryWrapperX<NcfFormpurchaseRequest> conditionX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        conditionX.in(NcfFormpurchaseRequest::getCode,collect);

        Page<NcfFormpurchaseRequest> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<NcfFormpurchaseRequest> page = this.getBaseMapper().selectPage(realPageRequest, conditionX);

        List<NcfFormpurchaseRequestVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormpurchaseRequestVO::new);
        pageResult.setContent(vos);
        return pageResult;

    }

    @Override
    public PurchaseRequestNumVO getByCode(NcfFormpurchaseRequestDTO dto) throws Exception {
        PurchaseRequestNumVO vo = new PurchaseRequestNumVO();
        String ssSql = "select count(1) as ssNum from pms_ncf_purch_project_implementation where logic_status = 1  and upm_approval_complete is null  and purch_req_doc_code = '" + dto.getCode() + "'";
        Map<String, Object> ssMap = this.namedParameterJdbcTemplate.queryForMap(ssSql, new HashMap<>());
        if (ssMap.get("ssNum") != null) {
            vo.setSsNum(Integer.parseInt(ssMap.get("ssNum").toString()));
        }
        String htSql = "select count(1) as htNum from ncf_form_contract_info where logic_status = 1  and purchase_applicant = '" + dto.getCode() + "' and  LEFT(contract_number,CHAR_LENGTH(contract_number) -8) not in (select contract_number from ncf_form_contract_info)";
        Map<String, Object> htMap = this.namedParameterJdbcTemplate.queryForMap(htSql, new HashMap<>());
        if (htMap.get("htNum") != null) {
            vo.setHtNum(Integer.parseInt(htMap.get("htNum").toString()));
        }
        return vo;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购申请主表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormpurchaseRequestExcelListener excelReadListener = new NcfFormpurchaseRequestExcelListener();
        EasyExcel.read(inputStream, NcfFormpurchaseRequestDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormpurchaseRequestDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购申请主表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormpurchaseRequest> ncfFormpurchaseRequestes = BeanCopyUtils.convertListTo(dtoS, NcfFormpurchaseRequest::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::NcfFormpurchaseRequest-import::id", importId, ncfFormpurchaseRequestes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormpurchaseRequest> ncfFormpurchaseRequestes = (List<NcfFormpurchaseRequest>) orionJ2CacheService.get("ncf::NcfFormpurchaseRequest-import::id", importId);
        log.info("采购申请主表导入的入库数据={}", JSONUtil.toJsonStr(ncfFormpurchaseRequestes));

        this.saveBatch(ncfFormpurchaseRequestes);
        orionJ2CacheService.delete("ncf::NcfFormpurchaseRequest-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::NcfFormpurchaseRequest-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //采购立项时间
            if (pageRequest.getQuery().getStartDate() != null || pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormpurchaseRequest::getProjectEndTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }

            //wbs编号，从申请明细查出wbsid，再查主表
            if (StringUtils.hasText(pageRequest.getQuery().getWbsId())) {
                List<String> projectCodes = namedParameterJdbcTemplate.queryForList("select project_code from ncf_form_purchase_request_detail where logic_status = 1 and wbs_id like :wbsId",
                        Collections.singletonMap("wbsId", "%" + pageRequest.getQuery().getWbsId() + "%"), String.class);
                //判空
                if (!CollectionUtils.isEmpty(projectCodes)) {
                    condition.in(NcfFormpurchaseRequest::getCode, projectCodes);
                }
            }
            //单多筛选框
            if (CollectionUtil.isNotEmpty(pageRequest.getQuery().getIds())){
                condition.in(NcfFormpurchaseRequest::getId,pageRequest.getQuery().getIds());
            }


        }
        condition.orderByDesc(NcfFormpurchaseRequest::getCreateTime);
        List<NcfFormpurchaseRequest> ncfFormpurchaseRequestes = this.list(condition);

        List<NcfFormpurchaseRequestDTO> dtos = BeanCopyUtils.convertListTo(ncfFormpurchaseRequestes, NcfFormpurchaseRequestDTO::new);
        //是否转换字体
        for (NcfFormpurchaseRequestDTO dto : dtos) {
            if (ObjectUtil.isEmpty(dto.getWithSafety())){
                dto.setWithSafety("");
            }else {
                String withSafety = dto.getWithSafety();
                if ("0".equals(withSafety)){
                    dto.setWithSafety("否");
                }
                if ("1".equals(withSafety)){
                    dto.setWithSafety("是");
                }
            }
            String isFrameContrac = dto.getIsFrameContrac();
            if ("0".equals(isFrameContrac)){
                dto.setIsFrameContrac("否");
            }
            if ("1".equals(isFrameContrac)){
                dto.setIsFrameContrac("是");
            }
        }

        String fileName = "采购申请主表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormpurchaseRequestDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormpurchaseRequestVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Map<String, Object> getNumMoney(Page<NcfFormpurchaseRequestDTO> pageRequest) {
        LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //采购立项时间
            if (pageRequest.getQuery().getStartDate() != null || pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormpurchaseRequest::getProjectEndTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }

            //wbs编号，从申请明细查出wbsid，再查主表
            if (StringUtils.hasText(pageRequest.getQuery().getWbsId())) {
                List<String> projectCodes = namedParameterJdbcTemplate.queryForList("select project_code from ncf_form_purchase_request_detail where logic_status = 1 and wbs_id like :wbsId",
                        Collections.singletonMap("wbsId", "%" + pageRequest.getQuery().getWbsId() + "%"), String.class);
                //判空
                if (!CollectionUtils.isEmpty(projectCodes)) {
                    condition.in(NcfFormpurchaseRequest::getCode, projectCodes);
                }
            }
        }
        condition.orderByDesc(NcfFormpurchaseRequest::getCreateTime);
        String sql = " count(*) as total," +
                "sum(money) as allMoney";
        condition.select(sql);
        Map map = this.getMap(condition);

        return map;
    }

    @Override
    public void uploadFile(NcfFormpurchaseRequestDTO dto) {
        List<FileDTO> attachments = Optional.ofNullable(dto.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> f.setDataId(dto.getId()));
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
        }
    }


    public static class NcfFormpurchaseRequestExcelListener extends AnalysisEventListener<NcfFormpurchaseRequestDTO> {

        private final List<NcfFormpurchaseRequestDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormpurchaseRequestDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormpurchaseRequestDTO> getData() {
            return data;
        }
    }


}
