<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
    @close="handleClose"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :rules="rules"
      :model="father.form"
    >
      <a-form-item
        label="计划名称"
        name="name"
      >
        <a-input
          v-model:value="father.form.name"
          placeholder="请输入计划名称"
          allow-clear
          :maxlength="64"
          size="large"
        />
      </a-form-item>
      <a-form-item
        label="所属类型"
        name="planType"
      >
        <a-select
          v-model:value="father.form.planType"
          placeholder="请选择所属类型"
          allow-clear
          :options="subJectList"
          size="large"
        />
      </a-form-item>
      <a-form-item
        label="负责人"
        name="principalId"
      >
        <a-select
          v-model:value="father.form.principalId"
          placeholder="请选择负责人"
          allow-clear
          show-search
          :filter-option="filterOption"
          :options="namesList"
          size="large"
          @change="handlePrincipal(namesList, father.form.principalId)"
        />
      </a-form-item>
      <a-form-item label="优先级">
        <a-select
          v-model:value="father.form.priorityLevel"
          placeholder="请选择优先级"
          allow-clear
          :options="priorityList"
          size="large"
        />
      </a-form-item>
      <a-form-item
        label="开始日期"
        name="planPredictStartTime"
      >
        <a-date-picker
          v-model:value="father.form.planPredictStartTime"
          show-time
          type="date"
          placeholder="请选择开始日期"
          allow-clear
          :disabledDate="disabledStartDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          size="large"
          class="w-full"
        />
      </a-form-item>
      <a-form-item
        label="结束日期"
        name="planPredictEndTime"
      >
        <a-date-picker
          v-model:value="father.form.planPredictEndTime"
          show-time
          type="date"
          placeholder="请选择结束日期"
          allow-clear
          :disabledDate="disabledEndDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          size="large"
          class="w-full"
        />
      </a-form-item>
      <a-form-item
        label="预估工时"
        name="manHour"
      >
        <a-input-number
          v-model:value="father.form.manHour"
          :min="0"
          :max="10000"
          :parser="parser"
          placeholder="请输入工时"
          size="large"
        />
      </a-form-item>
      <a-form-item
        v-if="father.type === 'edit'"
        label="状态"
      >
        <a-select
          v-model:value="father.form.status"
          class="w-full"
          placeholder="请选择状态"
          :options="statusList"
          size="large"
        />
      </a-form-item>
      <a-form-item label="描述">
        <a-textarea
          v-model:value="father.form.remark"
          placeholder="请输入描述"
          allow-clear
          :maxlength="255"
          size="large"
          :autosize="{ minRows: 3, maxRows: 6 }"
        />
      </a-form-item>
    </a-form>
    <a-checkbox
      v-if="father.type === 'create-task'"
      v-model:checked="isGo"
      size="large"
    >
      继续创建下一个
    </a-checkbox>
    <div class="drawer-footer">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-button
            size="large"
            block
            @click="handleClose"
          >
            取消
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            size="large"
            type="primary"
            block
            :loading="loading"
            @click="handleSave(father.type, isGo)"
          >
            确认
          </a-button>
        </a-col>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import {
  computed, onBeforeMount, reactive, toRefs, ref,
} from 'vue';
import {
  Row,
  Col,
  Drawer,
  Form,
  Checkbox,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  InputNumber,
} from 'ant-design-vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
import dayjs from 'dayjs';
import { isStringNumber } from 'lyra-component-vue3';
import { index } from '../data';
export default {
  name: 'EditPlan',
  components: {
    ARow: Row,
    ACol: Col,
    AInput: Input,
    AInputNumber: InputNumber,
    AButton: Button,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
    ACheckbox: Checkbox,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['update:data', 'submit'],
  setup(props, { emit }) {
    const state = reactive({
      manHour: 1,
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: computed({
        get() {
          return props.data;
        },
        set(val) {
          console.log('val', val);
          emit('update:data', val);
        },
      }),
      isGo: false,
      loading: false,
      formRef: ref(),
      priorityList: [], // 优先级
      namesList: [], // 负责人
      subJectList: [], // 所属类型
      statusList: [],
      projectId: parseURL().id,
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
        planType: [
          {
            required: true,
            message: '所属类型不能为空',
            trigger: 'change',
          },
        ],
        principalId: [
          {
            required: true,
            message: '负责人不能为空',
            trigger: 'change',
          },
        ],
      },
    });
    function getPriority() {
      new Api('/pms').fetch('', 'plan/priority/list', 'POST').then((res) => {
        state.priorityList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }

    function getProjectRole() {
      const qury = `/${state.projectId}?name=`;
      new Api('/pms').fetch('', `project-role-user/getListByName${qury}`, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }
    function getSubject() {
      const url = `task-subject/getList/${state.projectId}`;
      new Api('/pms').fetch('', url, 'GET').then((res) => {
        if (state.father.type === 'create-task') {
          state.father.form.planType = res && res.length ? res[0].id : undefined;
        }
        state.subJectList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
    }

    function filterOption(inputValue, treeNode) {
      return treeNode.props.label.includes(inputValue);
    }

    function disabledStartDate(current) {
      if (!current || !state.father.form.planPredictEndTime) {
        return false;
      }
      return current && current > dayjs(state.father.form.planPredictEndTime);
    }

    function disabledEndDate(current) {
      if (!current || !state.father.form.planPredictStartTime) {
        return false;
      }
      // return state.father.form.planPredictStartTime.valueOf() >= endValue.valueOf();
      return current && current < dayjs(state.father.form.planPredictStartTime);
    }
    function handleSave(type, isGo) {
      state.formRef
        .validate()
        .then(() => {
          state.loading = true;
          const love = {
            id: type === 'create-task' ? '' : state.father.form?.id,
            name: state.father.form?.name,
            className: 'Plan', // 列表中获取也可根据实际情况手动输入
            moduleName: '项目管理-计划管理-项目计划', // 模块名称
            type: type === 'create-task' ? 'SAVE' : 'UPDATE', // 操作类型
            remark: `${type === 'create-task' ? '新增' : '编辑'}了【${state.father.form?.name}】`, //	根据实际情况描述
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'plan', type === 'create-task' ? 'POST' : 'PUT')
            .then(() => {
              state.loading = false;
              message.success('操作成功');
              if (type === 'create-task' && isGo) {
                state.father.form = {
                  ...index.addForm(),
                  parentId: state.father.form.parentId,
                  projectId: state.father.form.projectId,
                };
              } else {
                emit('submit', true);
              }
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    function handleClose() {
      emit('submit', false);
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    function getStatus() {
      const url2 = `project-task-status/policy/status/list/${state.father.form.id}`;
      new Api('/pms').fetch('', url2, 'GET').then((res) => {
        state.statusList = res.map((s) => ({
          label: s.name,
          value: s.value,
        }));
      });
    }
    onBeforeMount(() => {
      if (state.father.type === 'edit') {
        getStatus();
      }
      getPriority(); // 计划优先级
      getProjectRole(); // 负责人
      getSubject(); // 所属类型
    });

    function parser(str) {
      if (isStringNumber(str)) {
        if (str - 0 === 0) {
          return str;
        }
        return (str - 0).toFixed(1);
      }
      return undefined;
    }

    return {
      ...toRefs(state),
      disabledStartDate,
      disabledEndDate,
      handleSave,
      handleClose,
      handlePrincipal,
      filterOption,
      parser,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
</style>
