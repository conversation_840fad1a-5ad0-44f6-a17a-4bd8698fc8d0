package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractChange Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 14:16:28
 */
@ApiModel(value = "ProjectContractChangeVO对象", description = "项目合同信息(变更)")
@Data
public class ProjectContractChangeVO extends ObjectVO implements Serializable {

    /**
     * 变更申请单id
     */
    @ApiModelProperty(value = "变更申请单id")
    private String changeId;

    /**
     * 变更申请单号
     */
    @ApiModelProperty(value = "变更申请单号")
    private String changeNumber;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    private String fieldCode;

    /**
     * 修改前值
     */
    @ApiModelProperty(value = "修改前值")
    private Object oldValue;

    /**
     * 修改后值
     */
    @ApiModelProperty(value = "修改后值")
    private Object newValue;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * keyValue
     */
    @ApiModelProperty(value = "keyValue")
    private String keyValue;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isNull;

}
