package com.chinasie.orion.management.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_info")
@ApiModel(value = "ContractInfoEntity对象", description = "合同主表信息")
@Data

public class ContractInfo extends ObjectEntity implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 是否框架合同
     */
    @ApiModelProperty(value = "是否框架合同")
    @TableField(value = "is_fream")
    private String isFream;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "purchase_applicant")
    private String purchaseApplicant;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同执行状态
     */
    @ApiModelProperty(value = "合同执行状态")
    @TableField(value = "status_name")
    private String statusName;

    /**
     * 框架合同剩余金额
     */
    @ApiModelProperty(value = "框架合同剩余金额")
    @TableField(value = "fream_residue_amount")
    private BigDecimal freamResidueAmount;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableField(value = "type")
    private String type;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    @TableField(value = "procurement_order_number")
    private String procurementOrderNumber;

    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 采购立项金额
     */
    @ApiModelProperty(value = "采购立项金额")
    @TableField(value = "procurement_amount")
    private BigDecimal procurementAmount;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @TableField(value = "factory_name")
    private String factoryName;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "business_rsp_user")
    private String businessRspUser;

    /**
     * 变更金额
     */
    @ApiModelProperty(value = "变更金额")
    @TableField(value = "change_money")
    private BigDecimal changeMoney;

    /**
     * 变更比例
     */
    @ApiModelProperty(value = "变更比例")
    @TableField(value = "change_percent")
    private String changePercent;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @TableField(value = "pay_money")
    private BigDecimal payMoney;

    /**
     * 是否合同终止
     */
    @ApiModelProperty(value = "是否合同终止")
    @TableField(value = "is_contract_terminate")
    private String isContractTerminate;

    /**
     * 索赔金额
     */
    @ApiModelProperty(value = "索赔金额")
    @TableField(value = "claim_amount")
    private BigDecimal claimAmount;

    /**
     * 索赔比例
     */
    @ApiModelProperty(value = "索赔比例")
    @TableField(value = "claim_percent")
    private String claimPercent;

    /**
     * 终止金额
     */
    @ApiModelProperty(value = "终止金额")
    @TableField(value = "terminate_amount")
    private BigDecimal terminateAmount;

    /**
     * 终止比例
     */
    @ApiModelProperty(value = "终止比例")
    @TableField(value = "terminate_percent")
    private String terminatePercent;

    /**
     * 框架开始时间
     */
    @ApiModelProperty(value = "框架开始时间")
    @TableField(value = "fream_begin_time")
    private Date freamBeginTime;

    /**
     * 框架结束时间
     */
    @ApiModelProperty(value = "框架结束时间")
    @TableField(value = "fream_end_time")
    private Date freamEndTime;

    /**
     * 框架合同已使用金额
     */
    @ApiModelProperty(value = "框架合同已使用金额")
    @TableField(value = "fream_used_amount")
    private BigDecimal freamUsedAmount;

    /**
     * 是否框架有效期内
     */
    @ApiModelProperty(value = "是否框架有效期内")
    @TableField(value = "is_fream_period")
    private String isFreamPeriod;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @TableField(value = "pay_percent")
    private String payPercent;

    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    @TableField(value = "procurement_way")
    private String procurementWay;

    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    @TableField(value = "procurement_cycle")
    private String procurementCycle;

    /**
     * 节约金额
     */
    @ApiModelProperty(value = "节约金额")
    @TableField(value = "amount_saved")
    private BigDecimal amountSaved;

    /**
     * 商务活动类型
     */
    @ApiModelProperty(value = "商务活动类型")
    @TableField(value = "business_activity_type")
    private String businessActivityType;

    /**
     * 最终采购方式
     */
    @ApiModelProperty(value = "最终采购方式")
    @TableField(value = "end_procurement_way")
    private String endProcurementWay;

    /**
     * 商务文件类型
     */
    @ApiModelProperty(value = "商务文件类型")
    @TableField(value = "business_file_type")
    private String businessFileType;

    /**
     * 预计合同开始日期
     */
    @ApiModelProperty(value = "预计合同开始日期")
    @TableField(value = "estimated_start_time")
    private Date estimatedStartTime;

    /**
     * 预计合同结束日期
     */
    @ApiModelProperty(value = "预计合同结束日期")
    @TableField(value = "estimated_end_time")
    private Date estimatedEndTime;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    @TableField(value = "pay_way")
    private String payWay;

    /**
     * 合同履约状态
     */
    @ApiModelProperty(value = "合同履约状态")
    @TableField(value = "execution_status_name")
    private String executionStatusName;

    /**
     * 标的类别
     */
    @ApiModelProperty(value = "标的类别")
    @TableField(value = "object_type")
    private String objectType;

    /**
     * 类别占比（%）
     */
    @ApiModelProperty(value = "类别占比（%）")
    @TableField(value = "type_percent")
    private String typePercent;

    /**
     * 审批价格（RMB）
     */
    @ApiModelProperty(value = "审批价格（RMB）")
    @TableField(value = "approved_price")
    private BigDecimal approvedPrice;

    /**
     * 最终价格（原币）
     */
    @ApiModelProperty(value = "最终价格（原币）")
    @TableField(value = "final_price")
    private BigDecimal finalPrice;

    /**
     * 实际合同开始日期
     */
    @ApiModelProperty(value = "实际合同开始日期")
    @TableField(value = "actual_start_time")
    private Date actualStartTime;

    /**
     * 实际合同结束日期
     */
    @ApiModelProperty(value = "实际合同结束日期")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;

    /**
     * 预计合同交付日期
     */
    @ApiModelProperty(value = "预计合同交付日期")
    @TableField(value = "estimated_delivery_time")
    private Date estimatedDeliveryTime;

    /**
     * 验收结果
     */
    @ApiModelProperty(value = "验收结果")
    @TableField(value = "acceptance_results")
    private String acceptanceResults;

    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @TableField(value = "actual_acceptance_times")
    private Date actualAcceptanceTimes;

    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    @TableField(value = "is_public_launch")
    private String isPublicLaunch;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 是否办理履约保证金
     */
    @ApiModelProperty(value = "是否办理履约保证金")
    @TableField(value = "is_performance_bond")
    private String isPerformanceBond;

    /**
     * 保证金支付方式
     */
    @ApiModelProperty(value = "保证金支付方式")
    @TableField(value = "margin_payment_method")
    private String marginPaymentMethod;

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @TableField(value = "security_deposit")
    private BigDecimal securityDeposit;

    /**
     * 是否参与计算
     */
    @ApiModelProperty(value = "是否参与计算	")
    @TableField(value = "is_calculation")
    private String isCalculation;

    /**
     * 采购立项审批完成时间
     */
    @ApiModelProperty(value = "采购立项审批完成时间")
    @TableField(value = "project_end_time")
    private Date projectEndTime;

    /**
     * 合同推荐审批完成时间
     */
    @ApiModelProperty(value = "合同推荐审批完成时间")
    @TableField(value = "recommend_end_time")
    private Date recommendEndTime;

    /**
     * 商务是否推荐供应商
     */
    @ApiModelProperty(value = "商务是否推荐供应商")
    @TableField(value = "is_biz_recommend")
    private String isBizRecommend;

    /**
     * 价格模式
     */
    @ApiModelProperty(value = "价格模式")
    @TableField(value = "price_model")
    private String priceModel;

    /**
     * 所属部处
     */
    @ApiModelProperty(value = "所属部处")
    @TableField(value = "subdivision")
    private String subdivision;

    /**
     * 是否填写一次验收合格
     */
    @ApiModelProperty(value = "是否填写一次验收合格")
    @TableField(value = "is_fill_onetime_acceptance")
    private Boolean isFillOnetimeAcceptance;

    /**
     * 预警日期
     */
    @ApiModelProperty(value = "预警日期")
    @TableField(value = "warning_day")
    private String warningDay;

    /**
     * 预警金额
     */
    @ApiModelProperty(value = "预警金额")
    @TableField(value = "warning_money")
    private String warningMoney;

    @ApiModelProperty(value = "供应商来源")
    @TableField(value = "supplier_from")
    private String supplierFrom;

    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 是否非技术推荐供应商参与
     */
    @ApiModelProperty(value = "是否非技术推荐供应商参与")
    @TableField(value = "is_tech_supplier")
    private String isTechSupplier;

    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    @TableField(value = "purchase_plan_code")
    private String purchasePlanCode;

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    @TableField(value = "application_request_code")
    private String purchaseRequestCode;

    @ApiModelProperty(value = "申请单名称")
    @TableField(value = "application_name")
    private String applicationName;

    @ApiModelProperty(value = "归口管理")
    @TableField(value = "bk_manage")
    private String bkManage;

    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    @TableField(value = "contact_amount_after_change")
    private BigDecimal contactAmountAfterChange;

    @ApiModelProperty(value = "累计索赔金额（含本次）")
    @TableField(value = "cumulative_claim_amount")
    private BigDecimal cumulativeClaimAmount;

    @ApiModelProperty(value = "总累计索赔占原合同价%")
    @TableField(value = "total_claim_pct_of_orig_price")
    private String totalClaimPctOfOrigPrice;

    @ApiModelProperty(value = "发送Sap时间")
    @TableField(value = "send_sap_time")
    private Date sendSapTime;



    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @TableField(exist = false)
    private String technicalRspUser;

    /**
     * 采购组
     */
    @ApiModelProperty(value = "采购组")
    @TableField(exist = false)
    private String procurementGroupName;

    @ApiModelProperty(value = "合同数量")
    @TableField(exist = false)
    private Integer contractNum;

}
