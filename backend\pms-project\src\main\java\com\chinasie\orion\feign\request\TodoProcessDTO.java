package com.chinasie.orion.feign.request;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yk
 * @date: 2023/9/19 9:48
 * @description:
 */
@ApiModel(value = "TodoProcessDTO对象", description = "待办流程")
@Data
public class TodoProcessDTO extends ObjectDTO implements Serializable {

    /**
     * 实例名称
     */
    @ApiModelProperty(value = "实例名称")
    private String processName;

    /**
     * 数据类型编号
     */
    @ApiModelProperty(value = "数据类型编号")
    private String dataTypeCode;


}
