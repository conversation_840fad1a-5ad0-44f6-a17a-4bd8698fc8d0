package com.chinasie.orion.bo;

import com.chinasie.orion.domain.dto.pas.DemandTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.QuestionTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.RiskTypeAttributeValueDTO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TypeAndTypeAttrValueVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/13 10:44
 */
@Component
public class PasBo {

    @Resource
    private PasFeignService pasFeignService;

    public void addRiskTypeAttributeValue(List<RiskTypeAttributeValueDTO> riskTypeAttributeValueDTOList) throws Exception {
        ResponseDTO<List<RiskTypeAttributeValueDTO>> responseDTO = pasFeignService.addRiskTypeAttributeValue(riskTypeAttributeValueDTOList);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
    }

    public void addDemandTypeAttributeValue(List<DemandTypeAttributeValueDTO> demandTypeAttributeValueDTOList) throws Exception {
        ResponseDTO<List<DemandTypeAttributeValueDTO>> responseDTO = pasFeignService.addDemandTypeAttributeValue(demandTypeAttributeValueDTOList);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
    }

    public void addQuestionTypeAttributeValue(List<QuestionTypeAttributeValueDTO> questionTypeAttributeValueDTOList) throws Exception {
        ResponseDTO<List<QuestionTypeAttributeValueDTO>> responseDTO = pasFeignService.addQuestionTypeAttributeValue(questionTypeAttributeValueDTOList);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
    }

    public TypeAndTypeAttrValueVO getDemandTypeAndAttributeValues(String typeId, String demandId) throws Exception {
        ResponseDTO<TypeAndTypeAttrValueVO> responseDTO = pasFeignService.getDemandTypeAndAttributeValues(typeId, demandId);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public TypeAndTypeAttrValueVO getQuestionTypeAndAttributeValues(String typeId, String questionId) throws Exception {
        ResponseDTO<TypeAndTypeAttrValueVO> responseDTO = pasFeignService.getQuestionTypeAndAttributeValues(typeId, questionId);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public TypeAndTypeAttrValueVO getRiskTypeAndAttributeValues(String typeId, String riskId) throws Exception {
        ResponseDTO<TypeAndTypeAttrValueVO> responseDTO = pasFeignService.getRiskTypeAndAttributeValues(typeId, riskId);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteTypeAttributeValueByRiskIds(List<String> riskIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteTypeAttributeValueByRiskIds(riskIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteTypeAttributeValueByDemandIds(List<String> demandIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteTypeAttributeValueByDemandIds(demandIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteTypeAttributeValueByQuestionIds(List<String> questionIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteTypeAttributeValueByQuestionIds(questionIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteRiskDirToRiskManagementByRiskIds(List<String> riskIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteRiskDirToRiskManagementByRiskIds(riskIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteDemandDirToDemandManagementByDemandIds(List<String> demandIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteDemandDirToDemandManagementByDemandIds(demandIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Boolean deleteQuestionDirToQuestionManagementBQuestionIds(List<String> questionIds) throws Exception {
        ResponseDTO<Boolean> responseDTO = pasFeignService.deleteQuestionDirToQuestionManagementBQuestionIds(questionIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult();
    }

    public Map<String, String> getRiskTyIdToNameMap(List<String> riskIds) throws Exception {
        ResponseDTO<List<SimpleVo>> responseDTO = pasFeignService.getRiskTypeByIds(riskIds);
        if(ResponseUtils.fail(responseDTO)){
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, responseDTO.getMessage());
        }
        return responseDTO.getResult().stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName));
    }
}
