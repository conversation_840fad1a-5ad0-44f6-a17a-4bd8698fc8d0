<script setup lang="ts">
import {
  computed, h, ref,
} from 'vue';
import {
  openDrawer,
  BasicButton,
  DataStatusTag, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';

import { message, Modal } from 'ant-design-vue';
import { useRoute } from 'vue-router';

import DrawerAdd from './components/DrawerAdd.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import {
  page, reviewEssentialsDelete, remove, edit, add, start, stop,
} from '/@/views/pms/api/reviewEssentials';

const route = useRoute();
const id = route.params.id;
const name = route.query.name;
setTitleByRootTabsKey(route?.query?.rootTabsKey, name);
const headAuthList = ref([]);

const searchConditions = ref([]);
const powerCode = {
  pageCode: 'PMS_PSYDK_DETAIL',
  headContainerCode: 'PSYDK_DETAIL_container_01',
  containerCode: 'PSYDK_DETAIL_container_02',
  headAdd: 'PSYDK_DETAIL_container_01_button_01',
  headEnable: 'PSYDK_DETAIL_container_01_button_02',
  headDisable: 'PSYDK_DETAIL_container_01_button_03',
  headDelete: 'PSYDK_DETAIL_container_01_button_04',

  containerView: 'PSYDK_DETAIL_container_02_button_01',
  containerEdit: 'PSYDK_DETAIL_container_02_button_02',
  containerDelete: 'PSYDK_DETAIL_container_02_button_03',
  containerEnable: 'PSYDK_DETAIL_container_02_button_04',
  containerDisable: 'PSYDK_DETAIL_container_02_button_05',
};
const tableRef = ref(null);
const columns = [
  {
    title: '评审阶段',
    dataIndex: 'reviewPhaseName',
  },
  {
    title: '要点类型',
    dataIndex: 'essentialsTypeName',
  },
  {
    title: '评审要点内容',
    dataIndex: 'content',
    minWidth: 250,
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record) => isPower(powerCode.containerEdit, record.rdAuthList),
    async onClick(record) {
      handleEdit(record.id);
    },
  },
  {
    text: (record) => (record.status === 1 ? '禁用' : '启用'),
    isShow: (record) => isPower(powerCode.containerEnable, record.rdAuthList) || isPower(powerCode.containerDisable, record.rdAuthList),
    onClick(record) {
      // 处理启用禁用传参
      const status = record.status ? 0 : 1;
      handleEffectBatch([record.id], status);
    },
  },
  {
    text: '删除',
    isShow: (record) => isPower(powerCode.containerDelete, record.rdAuthList),
    async modal(record) {
      await reviewEssentialsDelete(record.id);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },

];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  // 权限配置使用计算属性
  deleteToolButton: computed(() => {
    let str = 'add';
    if (!isPower(powerCode.headDelete, headAuthList.value)) str += '|delete';
    if (!isPower(powerCode.headEnable, headAuthList.value)) str += '|enable';
    if (!isPower(powerCode.headDisable, headAuthList.value)) str += '|disable';
    return str;
  }),
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['content'],
  rowSelection: {},
  columns,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headContainerCode,
      containerCode: powerCode.containerCode,
    };
    searchConditions.value = params?.searchConditions || [];
    return page(params, id).then((res) => {
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  isFilter2: false,
  filterConfigName: 'PAS_REVIEW_GIST_LIBRARY',
  actions,
  // 批量删除自定义api，有特殊情况才使用, data={ids:'选中的id组','选中的原始数据'}
  batchDeleteApi: async ({ ids }) => {
    await remove(ids);
    tableRef.value.reload();
  },
  // 配置新增表单项自动新增、编辑才生效
  // 批量自定义启动禁用
  batchEnableDisableApi: async ({ ids, type }) => {
    type ? await start(ids) : await stop(ids);
    tableRef.value.reload();
  },

};
const handleEffectBatch = (idList, takeEffect) => {
  Modal.confirm({
    title: `${takeEffect === 0 ? '禁用' : '启用'}确认提示`,
    content: `请确认是否${takeEffect === 0 ? '禁用' : '启用'}该数据？`,
    async onOk() {
      takeEffect ? await start(idList) : await stop(idList);
      message.success(`${takeEffect === 0 ? '禁用' : '启用'}成功`);
      tableRef.value.reload();
    },
  });
};
const handleAdd = () => {
  const refDrawer = ref();
  openDrawer({
    title: '添加评审要点',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
      });
    },
    async onOk() {
      const { formMethods: { validate, resetFields } } = refDrawer.value;
      const values = await validate();
      await add({
        ...values,
        mainTableId: id,
      });
      resetFields();
      tableRef.value.reload();
    },
  });
};
const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑评审要点',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const { formMethods: { validate }, getDataDetail } = refDrawer.value;
      const values = await validate();
      const dataDetail = getDataDetail();
      await edit({
        ...dataDetail,
        ...values,
      });
      tableRef.value.reload();
    },
  });
};

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMS_PSYDK_DETAIL'}"
    :options="{ body: { scroll: true } }"
    :contentTitle="name"
  >
    <OrionTable
      ref="tableRef"
      v-get-power="{pageCode:'PAS01019'}"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd, headAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          添加要点
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
