package com.chinasie.orion.management.constant;

/**
 * 客户范围字典
 */

public enum ProjectLabelEnum {

    XMLX("项目立项","100"),
    XMYLX("项目预立项","101");

    private String name;
    private String desc;

    ProjectLabelEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ProjectLabelEnum lt : ProjectLabelEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}