<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, inject, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { openFormDrawer } from './utils';
import BudgetAdjustmentEdit from './BudgetAdjustmentEdit.vue';
import BasicInfo from './components/BasicInfo.vue';
import Api from '/@/api';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('BasicInfo');
const dataId = computed(() => route.query?.id);
const detailsPowerData: Ref = ref(null);
provide('detailsPowerData', detailsPowerData);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  id: detailsData.id,
  name: detailsData.name,
  className: detailsData.className,
  projectCode: detailsData.number,
}));

const menuData = computed(() => [
  {
    id: 'BasicInfo',
    name: '基本信息',
    powerCode: 'PMS_YSTZXQ_container_02',
  },

  {
    id: 'WorkflowView',
    name: '流程',
    powerCode: 'PMS_YSTZXQ_container_03',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/budgetAdjustmentFrom').fetch({
      // pageCode: 'detail-container-demo111',
    }, dataId.value, 'GET');

    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}
const workflowViewRef: Ref = ref();
const workflowActionRef: Ref = ref();

const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: { ...detailsData },
  afterEvent() {
    workflowViewRef.value?.init();
    getDetails();
  },
}));
// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd);
const projectId:string = inject('projectId');

const actions = computed(() => [
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: detailsData.status === 120 && isPower('PMS_YSTZXQ_container_01_button_01', detailsPowerData.value),

    onClick() {
      openFormDrawer(BudgetAdjustmentEdit, projectId, detailsData, getDetails);
    },
  },
  {
    text: '发起流程',
    isShow: showWorkflowAdd.value && isPower('PMS_YSTZXQ_container_01_button_02', detailsPowerData.value),
    icon: 'sie-icon-qidongliucheng',
    onClick() {
      handleAddWorkflow();
    },
  },
]);
// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}
function getPowerDataHandle(data) {
  detailsPowerData.value = data;
}
</script>

<template>
  <Layout3
    v-if="detailsData.id"
    v-get-power="{pageCode:'BudgetAdjustmentDetails', getPowerDataHandle}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <template v-if="detailsData?.id">
      <BasicInfo v-if="'BasicInfo'===actionId" />
      <!--审批流程-->
      <WorkflowView
        v-if="actionId==='WorkflowView'"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>

    <template #footer>
      <WorkflowAction
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
