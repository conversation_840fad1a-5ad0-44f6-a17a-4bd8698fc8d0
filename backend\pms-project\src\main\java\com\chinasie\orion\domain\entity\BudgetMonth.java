package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * BudgetMonth Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:58:25
 */
@TableName(value = "pmsx_bud_budget_month")
@ApiModel(value = "BudgetMonth对象", description = "费用预算月度年度表")
@Data
public class BudgetMonth extends ObjectEntity implements Serializable{

    /**
     * 项目预算表ID
     */
    @ApiModelProperty(value = "项目预算表ID")
    @TableField(value = "budget_project_id" )
    private String budgetProjectId;

    /**
     * 项目预算名
     */
    @ApiModelProperty(value = "项目预算名")
    @TableField(value = "budget_project_name" )
    private String budgetProjectName;

    /**
     * 年度预算支出
     */
    @ApiModelProperty(value = "年度预算支出")
    @TableField(value = "year_expense" )
    private BigDecimal yearExpense;

    /**
     * 年度实际支出
     */
    @ApiModelProperty(value = "年度实际支出")
    @TableField(value = "year_fact_expense" )
    private BigDecimal yearFactExpense;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    @TableField(value = "january_money" )
    private BigDecimal januaryMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    @TableField(value = "february_money" )
    private BigDecimal februaryMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    @TableField(value = "march_money" )
    private BigDecimal marchMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    @TableField(value = "april_money" )
    private BigDecimal aprilMoney;

    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    @TableField(value = "may_money" )
    private BigDecimal mayMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    @TableField(value = "june_money" )
    private BigDecimal juneMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    @TableField(value = "july_money" )
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    @TableField(value = "august_money" )
    private BigDecimal augustMoney;

    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    @TableField(value = "september_money" )
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    @TableField(value = "october_money" )
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    @TableField(value = "november_money" )
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    @TableField(value = "december_money" )
    private BigDecimal decemberMoney;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

}
