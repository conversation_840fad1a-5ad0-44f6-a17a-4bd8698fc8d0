package com.chinasie.orion.domain.entity.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectWeekly Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
@TableName(value = "pms_project_weekly")
@ApiModel(value = "ProjectWeekly对象", description = "项目周报表")
@Data
public class ProjectWeekly extends ObjectEntity implements Serializable{

    /**
     * 所在年份第几周
     */
    @ApiModelProperty(value = "所在年份第几周")
    @TableField(value = "week" )
    private Integer week;

    /**
     * 一周的开始时间
     */
    @ApiModelProperty(value = "一周的开始时间")
    @TableField(value = "week_begin" )
    private Date weekBegin;

    /**
     * 一周结束时间
     */
    @ApiModelProperty(value = "一周结束时间")
    @TableField(value = "week_end" )
    private Date weekEnd;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "resp" )
    private String resp;

    @ApiModelProperty(value = "整体进度")
    @TableField(value = "overall_progress" )
    private Integer overallProgress;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    @TableField(value = "evaluate" )
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    @TableField(value = "score" )
    private BigDecimal score;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    @TableField(value = "reviewed_by" )
    private String reviewedBy;

    /**
     * 抄送人多个时用英文逗号分隔
     */
    @ApiModelProperty(value = "抄送人多个时用英文逗号分隔")
    @TableField(value = "carbon_copy_by" )
    private String carbonCopyBy;

    /**
     * 内容总结
     */
    @ApiModelProperty(value = "内容总结")
    @TableField(value = "content_summarize" )
    private String contentSummarize;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 评价时间
     */
    @ApiModelProperty(value = "评价时间")
    @TableField(value = "evaluate_date" )
    private Date evaluateDate;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    @TableField(value = "summary" )
    private String summary;

}
