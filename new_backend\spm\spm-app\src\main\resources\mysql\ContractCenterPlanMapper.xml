<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ContractCenterPlanMapper">


    <select id="contractCenterPlanStatistics" resultType="com.chinasie.orion.domain.entity.ContractCenterPlanStatistics">
        SELECT
            a.cost_type_id,
            a.contract_number,
            a.contract_name,
            a.center_code,
            a.center_name,
            a.num,
            DATE_FORMAT(a.year,'%Y') as year,
            b.cost_name,
            DATE_FORMAT(year,'%m') dataMonth,
            b.cost_type_number,
            b.unit_price
        FROM
            pmsx_contract_center_plan a
            JOIN pmsx_contract_cost_type b ON a.cost_type_id = b.id
            where
              DATE_FORMAT(a.year,'%Y') = #{year}
              and a.center_code = #{centerCode}
              and b.cost_type_number = #{costTypeNumber}
              and a.contract_number = #{contractNo}
    </select>


    <select id="getPlanGroupByCenterByDeptCode" resultType="com.chinasie.orion.domain.vo.PlanVO">
        SELECT
            pcm.contract_number AS 'contractNumber',
            pcm.contract_name AS 'contractName',
            nfci.`status` AS 'contractStatus',
            pcm.STATUS AS 'status',
            pccp.center_code AS 'centerCode',
            pccp.center_name AS 'centerName'
        FROM
            pmsx_contract_main pcm
                INNER JOIN ncf_form_contract_info nfci ON pcm.contract_number = nfci.contract_number
                INNER JOIN pmsx_contract_center_plan pccp ON pccp.contract_number = pcm.contract_number
        WHERE
            pcm.logic_status = 1
          AND YEAR ( pcm.YEAR ) = #{year}
          AND pccp.center_code in
        <foreach collection='deptCode' item='code' open='(' separator=',' close=')'>
            #{code}
        </foreach>
    </select>

</mapper>
