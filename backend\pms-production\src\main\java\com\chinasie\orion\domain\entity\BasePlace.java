package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * NcfFormHQJHpsfrK Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
@TableName(value = "pmsx_base_place")
@ApiModel(value = "BasePlace对象", description = "基地库")
@Data
public class BasePlace extends ObjectEntity implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "code")
    private String code;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "name")
    private String name;

    /**
     * 基地所在城市
     */
    @ApiModelProperty(value = "基地所在城市")
    @TableField(value = "city")
    private String city;

    /**
     * 对应项目部id
     */
    @ApiModelProperty(value = "对应项目部id")
    @TableField(value = "project_dept_id")
    private String projectDeptId;

    /**
     * 对应项目部名称
     */
    @ApiModelProperty(value = "对应项目部名称")
    @TableField(value = "project_dept_name")
    private String projectDeptName;

    /**
     * 对应项目部编号
     */
    @ApiModelProperty(value = "对应项目部编号")
    @TableField(value = "project_dept_number")
    private String projectDeptNumber;
}
