package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobSecurityMeasure Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
@TableName(value = "pmsx_job_security_measure")
@ApiModel(value = "JobSecurityMeasureEntity对象", description = "作业安措信息")
@Data

public class JobSecurityMeasure extends  ObjectEntity  implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 安全措施编码
     */
    @ApiModelProperty(value = "安全措施编码")
    @TableField(value = "measure_code")
    private String measureCode;

    /**
     * 安全措施描述
     */
    @ApiModelProperty(value = "安全措施描述")
    @TableField(value = "measure_desc")
    private String measureDesc;

    /**
     * 措施类型
     */
    @ApiModelProperty(value = "措施类型")
    @TableField(value = "measure_type")
    private String measureType;

    /**
     * 措施长文本
     */
    @ApiModelProperty(value = "措施长文本")
    @TableField(value = "measure_text")
    private String measureText;


    @ApiModelProperty(value = "订单号")
    @TableField(value = "job_code")
    private String jobCode;
    @ApiModelProperty(value = "风险分析")
    @TableField(value = "risk_analy")
    private String riskAnaly;
    @ApiModelProperty(value = "计划工厂")
    @TableField(value = "plan_factory")
    private String planFactory;
    @ApiModelProperty(value = "风险代码")
    @TableField(value = "risk_code")
    private String riskCode;
    @ApiModelProperty(value = "md5_value")
    @TableField(value = "encry_key")
    private String encryKey;


}
