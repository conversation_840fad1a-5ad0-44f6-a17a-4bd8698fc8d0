package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.projectscheme.FallBackDTO;
import com.chinasie.orion.domain.dto.projectscheme.IssueDTO;
import com.chinasie.orion.domain.dto.projectscheme.StatDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.request.ListRequest;
import com.chinasie.orion.domain.request.SchemeExportRequest;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.projectscheme.SchemeGanttVO;
import com.chinasie.orion.domain.vo.projectscheme.SchemeOperateLogVO;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.pas.api.domain.dto.EcrDTO;
import com.chinasie.orion.pas.api.domain.dto.EcrProjectSchemeAddDTO;
import com.chinasie.orion.pas.api.domain.vo.EcrVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeExcelService;
import com.chinasie.orion.service.ProjectSchemeListService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.xxl.ProjectShemeXxljob;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeController
 *
 * @author: yangFy
 * @date: 2023/4/19 16:25
 * @description
 * <p>
 * 项目计划
 * </p>
 */
@RestController
@RequestMapping("/projectScheme")
@Api(tags = "项目计划管理")
public class ProjectSchemeController {
    @Resource
    private ProjectShemeXxljob projectShemeXxljob;

    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private ProjectSchemeExcelService schemeExcelService;

    @Resource(name = "projectSchemeList")
    private ProjectSchemeListService schemeListService;

    @Resource(name = "projectSchemeGantt")
    private ProjectSchemeListService schemeGanttService;

    @ApiOperation("添加计划(批量'同级')")
    @PostMapping(value = "/createBatch/{pid}")
    @LogRecord(success = "【{USER{#logUserId}}】新增计划", type = "项目计划", subType = "批量添加(过程记录)", bizNo = "{{#saveIds.toString()}}")
    public ResponseDTO<List<String>> createBatch(@PathVariable String pid, @RequestBody List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception {
        List<String> rsp = projectSchemeService.createBatch(pid, projectSchemeDTOS);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("添加计划(批量'父子级')")
    @PostMapping(value = "/createTree/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】新增计划", type = "项目计划", subType = "批量添加(过程记录)", bizNo = "{{#rsp.toString()}}")
    public ResponseDTO<List<String>> createTreeBatch(@PathVariable("projectId") String projectId, @RequestParam(value = "parentId", required = false, defaultValue = "0") String parentId, @RequestBody List<ProjectSchemeDTO> projectSchemeDTOS) throws Exception {
        List<String> rsp = projectSchemeService.createTreeBatch(projectId, parentId, projectSchemeDTOS);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("编辑项目计划")
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectSchemeDTO.name}}】", type = "QuestionLibrary", subType = "编辑(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    public ResponseDTO<ResponseVO> edit(@RequestBody @Validated ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.edit(projectSchemeDTO));
    }


    @ApiOperation("批量执行完成项目计划")
    @PutMapping(value = "/finish/batch")
    @LogRecord(success = "【{USER{#logUserId}}】执行完成", type = "项目计划", subType = "批量执行完成项目计划(过程记录)", bizNo = "{{#projectSchemeIds.toString()}}")
    public ResponseDTO<Boolean> finishBatch(@RequestBody List<String> projectSchemeIds) throws Exception {
        return ResponseDTO.success(projectSchemeService.finishBatch(projectSchemeIds));
    }

    @ApiOperation("删除(批量)")
    @DeleteMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目计划", subType = "删除(批量)", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> delete(@RequestBody List<String> ids) throws Exception {
        return ResponseDTO.success(projectSchemeService.deleteByIds(ids));
    }

    @ApiOperation("项目计划详情")
    @GetMapping(value = "/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划详情", type = "项目计划", subType = "项目计划详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeVO> getDetail(@PathVariable("id") String id,
                                                  @RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        return ResponseDTO.success(projectSchemeService.getDetail(id, pageCode));
    }


    @ApiOperation("项目计划详情")
    @PostMapping (value = "/list/id")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划详情", type = "项目计划", subType = "项目计划详情", bizNo = "{{#id}}")
    public ResponseDTO<List<ProjectScheme>> listByIds(@RequestBody List<String> ids) throws Exception {
        List<ProjectScheme> schemeList = projectSchemeService.listByIds(ids);
        return ResponseDTO.success(schemeList);
    }

    @ApiOperation("项目计划列表")
    @PostMapping(value = "/list")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划列表", type = "项目计划", subType = "项目计划列表", bizNo = "{{#request.toString()}}")
    public ResponseDTO<List<ProjectScheme>> projectSchemeList(@RequestBody ListRequest request) throws Exception {
        return ResponseDTO.success(schemeListService.schemeList(request));
    }

    @ApiOperation("树查询父计划")
    @GetMapping(value = "/parentTree/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】执行完成", type = "项目计划", subType = "查询项目详情的父计划", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeVO> parentTree(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.parentTree(id));
    }

    @ApiOperation("树查询子计划")
    @PostMapping(value = "/tree")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询项目详情的子计划", type = "项目计划", subType = "查询项目详情的子计划", bizNo = "{{#schemeDTO.toString()}}")
    })
    public ResponseDTO<Page<ProjectSchemeVO>> tree(@RequestBody ProjectSchemeDTO schemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.tree(schemeDTO));
    }

    @ApiOperation("上移")
    @PutMapping(value = "/up/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】上移", type = "项目计划", subType = "上移(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> up(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.up(id));
    }

    @ApiOperation("下移")
    @PutMapping(value = "/down/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】下移", type = "项目计划", subType = "下移(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> down(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.down(id));
    }

    @ApiOperation("拖拽")
    @PutMapping(value = "/drag")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】拖拽", type = "项目计划", subType = "拖拽(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<Boolean> drag(@RequestBody @Validated ProjectSchemeDragDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.drag(projectSchemeDTO));
    }

    @ApiOperation("置顶")
    @PutMapping(value = "/top/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】置顶", type = "项目计划", subType = "置顶(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> top(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.top(id));
    }

    @ApiOperation("取消置顶")
    @PutMapping(value = "/unTop/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】取消置顶", type = "项目计划", subType = "取消置顶(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> unTop(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.unTop(id));
    }

    @ApiOperation("计划下发")
    @PutMapping(value = "/issue")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】计划下发", type = "项目计划", subType = "计划下发(过程记录)", bizNo = "{{#issueDTO.toString()}}")
    })
    public ResponseDTO<Boolean> issue(@RequestBody IssueDTO issueDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.issue(issueDTO));
    }

//    @ApiOperation("计划重新下发")
//    @PutMapping(value = "/issue/again")
//    @LogRecord(success = "【{USER{#logUserId}}】重新下发", type = "项目计划", bizNo = "{{#issueDTO.schemeIds.toString()}}")
//    public ResponseDTO<Boolean> issueAgain(@RequestBody IssueDTO issueDTO) throws Exception {
//        return ResponseDTO.success(projectSchemeService.issueAgain(issueDTO));
//    }



    @ApiOperation("甘特图")
    @PutMapping(value = "/gantt/{projectId}")
    @LogRecord( success = "【{USER{#logUserId}}】甘特图", type = "项目计划", subType = "甘特图", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SchemeGanttVO>> gantt(@PathVariable("projectId") String projectId,
                                                  @RequestBody GantDTO gantDTO) throws Exception {
        return ResponseDTO.success(schemeGanttService.ganttPic(projectId,gantDTO));
    }

    @ApiOperation("甘特图部门查询")
    @PutMapping(value = "/gantt/dep/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】甘特图部门查询", type = "项目计划", subType = "甘特图部门查询", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SchemeVO>> ganttDep(@PathVariable("projectId") String projectId) throws Exception {
        return ResponseDTO.success(projectSchemeService.ganttDep(projectId));
    }
    @ApiOperation("甘特图人员查询")
    @PutMapping(value = "/gantt/User/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】甘特图人员查询", type = "项目计划", subType = "甘特图人员查询", bizNo = "{{#projectId}}")
    public ResponseDTO<List<SchemeVO>> ganttUser(@PathVariable("projectId") String projectId) throws Exception {
        return ResponseDTO.success(projectSchemeService.ganttUser(projectId));
    }

    @ApiOperation("计划导入校验（Excel）")
    @PostMapping(value = "/import/excel/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】计划导入校验（Excel）", type = "项目计划", subType = "计划导入校验（Excel）", bizNo = "{{#projectId}}")

    public ResponseDTO<ImportExcelCheckResultVO> importByExcel(@PathVariable("projectId") String projectId, String pid, @RequestBody MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = schemeExcelService.importExcel(projectId, pid, file);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("计划导入确认")
    @PostMapping(value = "/import/excel/verify/{importId}")
    @LogRecord( success = "【{USER{#logUserId}}】计划导入确认", type = "项目计划", subType = "计划导入确认", bizNo = "{{#importId}}")
    public ResponseDTO<Boolean> importByExcelVerify(@PathVariable("importId") String importId) throws Exception {
        boolean rsp = schemeExcelService.importByExcelVerify(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("计划导入取消")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @LogRecords(@LogRecord( success = "【{USER{#logUserId}}】计划导入取消", type = "项目计划", subType = "计划导入取消", bizNo = "{{#importId}}"))
    public ResponseDTO<Boolean> importByExcelCancel(@PathVariable("importId") String importId) throws Exception {
        boolean rsp = schemeExcelService.importByExcelCancel(importId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("计划导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecords( @LogRecord( success = "【{USER{#logUserId}}】计划导出（Excel）", type = "项目计划", subType = "计划导出（Excel）", bizNo = "{{#request.toString()}}"))
    public void exportByExcel(@RequestBody SchemeExportRequest request, HttpServletResponse response) throws Exception {
        schemeExcelService.exportExcel(request, response);
    }

    @ApiOperation("模版下载（Excel）")
    @PostMapping(value = "/template/download")
    @LogRecords( @LogRecord( success = "【{USER{#logUserId}}】模版下载（Excel）", type = "项目计划", subType = "模版下载（Excel）", bizNo = ""))
    public void templateDownload(HttpServletResponse response) throws Exception {
        ExcelUtils.writeTemplate(response, ProjectSchemeTempleteDownloadExcelDTO.class, "项目计划导入模版.xlsx", "项目计划导入模版", "sheet1");
    }


    @ApiOperation("计划完成确认")
    @PutMapping(value = "/finish")
    @LogRecords( @LogRecord( success = "【{USER{#logUserId}}】计划完成确认", type = "项目计划", subType = "计划完成确认", bizNo = "{{#projectSchemeDTO.toString()}}"))
    public ResponseDTO<Boolean> finish(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.finish(projectSchemeDTO));
    }

    @ApiOperation("待发布/已发布项目计划列表")
    @PostMapping(value = "/list/{projectId}")
    @LogRecords( @LogRecord( success = "【{USER{#logUserId}}】待发布/已发布项目计划列表", type = "项目计划", subType = "待发布/已发布项目计划列表", bizNo = "{{#projectId}}"))
    public ResponseDTO<List<ProjectSchemeVO>> getSchemeList(@PathVariable("projectId") String projectId) throws Exception {
        return ResponseDTO.success(projectSchemeService.getSchemeList(projectId));
    }

    @ApiOperation("里程碑列表")
    @PostMapping(value = "/{projectId}/milestone")
    @LogRecords( @LogRecord( success = "【{USER{#logUserId}}】里程碑列表", type = "项目计划", subType = "里程碑列表", bizNo = "{{#projectId}}"))
    public ResponseDTO<List<ProjectSchemeVO>> getAllMilestoneByProjectId(@PathVariable("projectId") String projectId) throws Exception {
        return ResponseDTO.success(projectSchemeService.getAllMilestoneByProjectId(projectId));
    }

    @ApiOperation("历史数据处理")
    @PostMapping(value = "/history/handle")
    @LogRecord( success = "【{USER{#logUserId}}】历史数据处理", type = "项目计划", subType = "历史数据处理", bizNo = "")
    public ResponseDTO<Boolean> dataHandle() throws Exception {
        projectSchemeService.dataHandle();
        return ResponseDTO.success(true);
    }


    @ApiOperation("计划补充超时原因")
    @PostMapping(value = "/writeDelayReason")
    @LogRecord( success = "【{USER{#logUserId}}】计划补充超时原因", type = "项目计划", subType = "计划补充超时原因", bizNo = "")
    public ResponseDTO<Boolean> writeDelayReason(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        Boolean rsp = projectSchemeService.writeDelayReason(projectSchemeDTO);
        return ResponseDTO.success(rsp);
    }


    @ApiOperation("计划催办")
    @PostMapping(value = "/urgePlan")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】计划催办", type = "项目计划", subType = "计划催办(过程记录)", bizNo = "{{#urgePlanRequestDTO.projectSchemeId}}")
    })
    public ResponseDTO<Boolean> urgePlan(@RequestBody UrgePlanRequestDTO urgePlanRequestDTO) throws Exception {
        Boolean rsp = projectSchemeService.urgePlan(urgePlanRequestDTO);
        return ResponseDTO.success(rsp);
    }


    /**
     * 获取这个人的项目下的需要走流程的项目计划
     */

    @ApiOperation("获取这个人的项目下的需要走流程的项目计划")
    @RequestMapping(value = "/pageByUserAndProjectCode/{userCode}/{projectCode}", method = {RequestMethod.POST})
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】获取这个人的项目下的需要走流程的项目计划", type = "项目计划", subType = "获取这个人的项目下的需要走流程的项目计划", bizNo = "{{#userCode}}")
    })
    public ResponseDTO<Page<ProjectSchemeVO>> pageProjectSchemeByUserCode(@PathVariable("userCode") String userCode,
                                                                          @PathVariable("projectCode") String projectCode,
                                                                          @RequestBody Page<ProjectSchemeDTO> pageRequest) throws Exception {
        Page<ProjectSchemeVO> rsp = projectSchemeService.pageProjectSchemeByUserCode(userCode, projectCode, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 第三方修改项目计划
     */

    @ApiOperation("第三方修改项目计划")
    @RequestMapping(value = "/updateProcessOrStatusByNumber", method = {RequestMethod.POST})
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】第三方修改项目计划", type = "项目计划", subType = "第三方修改项目计划", bizNo = "{{#projectSchemeDTO.number}}")
    })
    public ResponseDTO<Boolean> updateProjectSchemeByThird(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        Boolean rsp = projectSchemeService.updateProjectSchemeByThird(projectSchemeDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("新增关联需求")
    @PostMapping(value = "/relation/demand")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联需求", type = "项目计划", subType = "关联需求(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToDemand(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToDemand(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("删除关联需求")
    @DeleteMapping(value = "/relation/demand/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联需求", type = "项目计划", subType = "删除关联需求(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToDemand(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToDemand(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联需求")
    @PostMapping(value = "/relation/demand/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联需求", type = "项目计划", subType = "查询关联需求", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<List<DemandManagementVO>> relationToDemandLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<DemandManagementVO> rsp = projectSchemeService.relationToDemandLists(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("新增关联问题")
    @PostMapping(value = "/relation/question")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联问题", type = "项目计划", subType = "关联问题(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToQuestion(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToQuestion(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联问题")
    @DeleteMapping(value = "/relation/question/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联问题", type = "项目计划", subType = "删除关联问题(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToQuestion(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToQuestion(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联问题")
    @PostMapping(value = "/relation/question/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联问题", type = "项目计划", subType = "查询关联问题", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<List<QuestionManagementVO>> relationToQuestionLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<QuestionManagementVO> rsp = projectSchemeService.relationToQuestionLists(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("新增关联风险")
    @PostMapping(value = "/relation/risk")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联风险", type = "项目计划", subType = "关联风险(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToRisk(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToRisk(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联风险")
    @DeleteMapping(value = "/relation/risk/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联风险", type = "项目计划", subType = "删除关联风险(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToRisk(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToRisk(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联风险")
    @PostMapping(value = "/relation/risk/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联风险", type = "项目计划", subType = "查询关联风险", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<List<RiskManagementVO>> relationToRiskLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<RiskManagementVO> rsp = projectSchemeService.relationToRiskLists(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("新增关联物资设备")
    @PostMapping(value = "/relation/fixedAsset")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联风险", type = "项目计划", subType = "关联风险(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToFixedAsset(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToFixedAsset(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联物资设备")
    @DeleteMapping(value = "/relation/fixedAsset/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联风险", type = "项目计划", subType = "删除关联风险(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToFixedAsset(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToFixedAsset(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联物资设备")
    @PostMapping(value = "/relation/fixedAsset/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联风险", type = "项目计划", subType = "查询关联物资设备", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<List<FixedAssetsVO>> relationToFixedAssetLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<FixedAssetsVO> rsp = projectSchemeService.relationToFixedAssetLists(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("新增关联采购行")
    @PostMapping(value = "/relation/ncFormpurchase")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联风险", type = "项目计划", subType = "新增关联采购行", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> relationToNcFormpurchase(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToNcFormpurchase(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联采购行")
    @DeleteMapping(value = "/relation/ncFormpurchase/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联风险", type = "项目计划", subType = "删除关联风险(过程记录)", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<Boolean> removeRelationToNcFormpurchase(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToNcFormpurchase(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联采购行")
    @PostMapping(value = "/relation/ncFormpurchase/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联采购行", type = "项目计划", subType = "查询关联采购行", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    })
    public ResponseDTO<List<NcfFormpurchaseRequestDetailAPIAsset>> relationToNcFormpurchaseLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<NcfFormpurchaseRequestDetailAPIAsset> rsp = projectSchemeService.relationToNcFormpurchaseLists(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取计划列表")
    @PostMapping(value = "/search/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取计划列表", type = "项目计划", subType = "获取计划列表", bizNo = "")
    public ResponseDTO<PlanSearchDataVo> searchList(@RequestBody KeywordDto keywordDto) throws Exception {
        PlanSearchDataVo rsp = projectSchemeService.searchList(keywordDto);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("获取计划列表")
    @PostMapping(value = "/search")
    @LogRecord(success = "【{USER{#logUserId}}】获取计划列表", type = "项目计划", subType = "获取计划列表", bizNo = "")
    public ResponseDTO<List<PlanDetailVo>> search(@RequestBody SearchDTO searchDTO) throws Exception {
        List<PlanDetailVo> rsp = projectSchemeService.search(searchDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("项目计划列表")
    @PostMapping(value = "/userPages/{type}")
    @LogRecord(success = "【{USER{#logUserId}}】获取计划列表", type = "项目计划", subType = "获取计划列表", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeVO>> projectSchemePages(@PathVariable("type") String type,@RequestBody Page<ProjectSchemeDTO> pageRequest) throws Exception {
        return ResponseDTO.success(projectSchemeService.userPages(type,pageRequest));
    }

    @ApiOperation("新增关联IED")
    @PostMapping(value = "/relation/ied")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】新增计划关联IED", type = "项目计划", subType = "关联IED(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> relationToDeliverGoals(@RequestParam("id") String id, @RequestBody List<String> deliverGoalsIdList) throws Exception {
        Boolean rsp = projectSchemeService.relationToDeliverGoals(id, deliverGoalsIdList);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联IED")
    @DeleteMapping(value = "/relation/ied/remove")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】删除计划关联IED", type = "项目计划", subType = "删除关联IED(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<Boolean> removeRelationToDeliverGoals(@RequestParam("id") String id, @RequestBody List<String> deliverGoalsIdList) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToDeliverGoals(id, deliverGoalsIdList);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联IED")
    @GetMapping(value = "/relation/ied/lists/{id}")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】查询关联IED", type = "项目计划", subType = "查询关联IED(过程记录)", bizNo = "{{#id}}")
    })
    public ResponseDTO<List<DeliverGoalsVO>> relationToDeliverGoals(@PathVariable("id") String id) throws Exception {
        List<DeliverGoalsVO> rsp = projectSchemeService.relationToDeliverGoals(id);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("我的项目计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", dataType = "String"),
            @ApiImplicitParam(name = "start", dataType = "String"),
            @ApiImplicitParam(name = "end", dataType = "String")
    })
    @GetMapping(value = "/list/{userId}/{start}/{end}")
    @LogRecord(success = "【{USER{#logUserId}}】获取我的项目计划", type = "项目计划", subType = "获取我的项目计划", bizNo = "")
    ResponseDTO<List<ProjectSchemeVO>> workHoursHainByRspUser(@PathVariable("userId") String userId,
                                            @PathVariable("start") String start,
                                            @PathVariable("end") String end){
        List<ProjectSchemeVO> list = projectSchemeService.workHoursHainByRspUser(userId,start,end);
        return new ResponseDTO<>(list);
    }

    @ApiOperation("工时获取项目计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", dataType = "String")
    })
    @GetMapping(value = "/listBy/{userId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取工时获取项目计划", type = "项目计划", subType = "获取工时获取项目计划", bizNo = "")
    ResponseDTO<List<ProjectSchemeVO>> workHoursHainByRspUser(@PathVariable("userId") String userId){
        List<ProjectSchemeVO> list = projectSchemeService.workHoursHainByRspUser(userId,null,null);
        return new ResponseDTO<>(list);
    }

    @ApiOperation("工时根据id集合获取项目计划")
    @PostMapping(value = "/list/by-ids")
    @LogRecord(success = "【{USER{#logUserId}}】获取工时根据id集合获取项目计划", type = "项目计划", subType = "获取工时根据id集合获取项目计划", bizNo = "")
    ResponseDTO<List<ProjectSchemeVO>> getListByIds(@RequestBody List<String> ids){
        List<ProjectSchemeVO> list = projectSchemeService.getListByIds(ids);
        return new ResponseDTO<>(list);
    }

    @ApiOperation("日报填报合获取项目计划")
    @PostMapping(value = "/list/getSchemeByUser")
    @LogRecord(success = "【{USER{#logUserId}}】获取日报填报合获取项目计划", type = "项目计划", subType = "获取日报填报合获取项目计划", bizNo = "")
    ResponseDTO<Page<ProjectSchemeVO>> getListByIds(@RequestBody Page<ProjectSchemeDailyStatementDTO> pageRequest){
        Page<ProjectSchemeVO> page = projectSchemeService.getSchemeByUsers(pageRequest);
        return new ResponseDTO<>(page);
    }

    @ApiOperation("绩效根据审批人id获取计划分页")
    @PostMapping(value = "/getPageByexamineUser")
    @LogRecord(success = "【{USER{#logUserId}}】获取绩效根据审批人id获取计划分页", type = "项目计划", subType = "获取绩效根据审批人id获取计划分页", bizNo = "")
    ResponseDTO<Page<ProjectSchemeVO>> getPageByProjectId(@RequestBody Page<ProjectSchemeDTO> pageRequest){
        Page<ProjectSchemeVO> page = projectSchemeService.getPageByexamineUser(pageRequest);
        return new ResponseDTO<>(page);
    }

    @ApiOperation("用户项目计划个数")
    @PostMapping(value = "/list/getSchemeByUser/count")
    @LogRecord(success = "【{USER{#logUserId}}】获取用户项目计划个数", type = "项目计划", subType = "获取用户项目计划个数", bizNo = "")
    ResponseDTO getSchemeByRspUsers(@RequestBody ProjectSchemeForUsersDTO schemeForUsersDTO){
        Map<String, List<ProjectScheme>> schemeByRspUsersForTime = projectSchemeService.getSchemeByRspUsersForTime(schemeForUsersDTO.getUserIds(), schemeForUsersDTO.getBeginTime(), schemeForUsersDTO.getEndTime());
        return new ResponseDTO<>(schemeByRspUsersForTime);
    }

    @ApiOperation("指定用户的项目计划列表")
    @PostMapping(value = "/userId/Pages/{userId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取指定用户的项目计划列表", type = "项目计划", subType = "获取指定用户的项目计划列表", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeVO>> projectSchemeByUserIdPages(@PathVariable("userId") String userId,@RequestBody Page<ProjectSchemeDTO> pageRequest) throws Exception {
        return ResponseDTO.success(projectSchemeService.schemeByUserIdPages(userId,pageRequest));
    }


    @ApiOperation("计划评分完成确认")
    @GetMapping(value = "/examineTypeFinish/{id}")
    @Transactional
    @LogRecord(success = "【{USER{#logUserId}}】计划评分完成确认", type = "项目计划", subType = "计划评分完成确认(过程记录)",bizNo = "{{#id}}")
    public ResponseDTO<Boolean> examineTypeFinish(@PathVariable("id") String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.examineTypeFinish(id));
    }


    @ApiOperation("绩效考核根据任务id获取计划")
    @GetMapping(value = "getById/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】绩效考核根据任务id获取计划", type = "项目计划", subType = "绩效考核根据任务id获取计划", bizNo = "{{#id}}")
    public ResponseDTO<ProjectSchemeVO> getById(@PathVariable("id") String id) throws Exception {
        ProjectSchemeVO bySchemeId = projectSchemeService.getBySchemeId(id);
        return new ResponseDTO<>(bySchemeId);
    }


    @ApiOperation("更新实际开始时间")
    @LogRecord(success = "【{USER{#logUserId}}】更新实际执行时间", type = "项目计划", subType = "更新实际开始时间(过程记录)",bizNo = "{{#id}}")
    @PutMapping(value = "/projectScheme/actualBeginTime/update")
    ResponseDTO<Boolean> updateActualBeginTime(@RequestParam("id") String id){
        Boolean result = projectSchemeService.updateActualBeginTime(id);
        return new ResponseDTO<>(result);
    }

    @ApiOperation("项目计划退回")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划退回", type = "项目计划", subType = "项目计划退回(过程记录)", bizNo = "{{#fallBackDTO.id}}")
    @PostMapping(value = "/fallback")
    ResponseDTO<Boolean> schemeFallback(@RequestBody FallBackDTO fallBackDTO){
        Boolean result = projectSchemeService.schemeFallback(fallBackDTO);
        return new ResponseDTO<>(result);
    }

    @ApiOperation("获取项目所有里程碑分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了里程碑数据", type = "ProjectScheme", subType = "分页查询", bizNo = "")
    @PostMapping(value = "/milestone/page")
    public ResponseDTO<Page<ProjectSchemeVO>> getMilestonePage(@RequestBody Page<ProjectSchemeDTO> pageRequest) throws Exception {
        return ResponseDTO.success(projectSchemeService.getMilestonePage(pageRequest));
    }

    @ApiOperation("计划撤回")
    @LogRecord(success = "【{USER{#logUserId}}】项目计划撤回", type = "项目计划", subType = "项目计划撤回(过程记录)", bizNo = "{{#id}}")
    @GetMapping(value = "/revocation")
    public ResponseDTO<String> revocation(@RequestParam String id) throws Exception {
        String rsp = projectSchemeService.revocation(id);
        return ResponseDTO.success(rsp);
    }


    @ApiOperation("完成确认")
    @PutMapping(value = "/completeConfirmation")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】项目计划完成确认", type = "项目计划", subType = "项目计划完成确认(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<Boolean> completeConfirmation(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.completeConfirmation(projectSchemeDTO));
    }

    @ApiOperation("暂停计划")
    @PutMapping(value = "/suspendScheme")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】项目计划暂停", type = "项目计划", subType = "项目计划暂停(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<Boolean> suspendScheme(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.suspendScheme(projectSchemeDTO));
    }


    @ApiOperation("终止计划")
    @PutMapping(value = "/terminateScheme")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】项目计划终止", type = "项目计划", subType = "项目计划终止(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<Boolean> terminateScheme(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.terminateScheme(projectSchemeDTO));
    }

    @ApiOperation("启动计划")
    @PutMapping(value = "/startScheme")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】项目计划启动", type = "项目计划", subType = "项目计划启动(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<Boolean> startScheme(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.startScheme(projectSchemeDTO));
    }



    @ApiOperation("引入立项计划")
    @GetMapping(value = "/getApprovalScheme")
    @LogRecords({
    })
    public ResponseDTO<Boolean> getApprovalScheme(@RequestParam String projectId) throws Exception {
        return ResponseDTO.success(projectSchemeService.getApprovalScheme(projectId));
    }

    @ApiOperation("创建项目计划")
    @PostMapping(value = "/create")
    @LogRecords({
            @LogRecord(success = "【{USER{#logUserId}}】创建项目计划", type = "项目计划", subType = "创建项目计划(过程记录)", bizNo = "{{#projectSchemeDTO.id}}")
    })
    public ResponseDTO<String> createScheme(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.createScheme(projectSchemeDTO));
    }

    @ApiOperation("新增关联质控措施")
    @PostMapping(value = "/relation/qualityItem")
    @LogRecord(success = "【{USER{#logUserId}}】计划新增关联质控措施", type = "项目计划", subType = "新增关联质控措施(过程记录)",bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    public ResponseDTO<Boolean> relationToQualityItem(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.relationToQualityItem(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联质控措施")
    @DeleteMapping(value = "/relation/qualityItem/remove")
    @LogRecord(success = "【{USER{#logUserId}}】计划删除关联质控措施", type = "项目计划", subType = "删除关联质控措施(过程记录)",bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    public ResponseDTO<Boolean> removeRelationToQualityItem(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = projectSchemeService.removeRelationToQualityItem(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联质控措施")
    @PostMapping(value = "/relation/qualityItem/lists/{id}")
    public ResponseDTO<List<QualityItemVO>> relationToQualityItemLists(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        List<QualityItemVO> rsp = projectSchemeService.relationToQualityItem(id, planQueryDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("过程记录")
    @PostMapping (value = "logs/list/id")
    public ResponseDTO<List<SchemeOperateLogVO>> logListById(@RequestParam("id") String id) throws Exception {
        List<SchemeOperateLogVO> logList = projectSchemeService.logListById(id);
        return ResponseDTO.success(logList);
    }
    @ApiOperation("通过项目计划获取反馈信息")
    @LogRecord(success = "【{USER{#logUserId}}】查询了项目计划反馈信息", type = "ProjectScheme", subType = "列表查询", bizNo = "")
    @GetMapping(value = "/projectSchemeContent/list")
    public ResponseDTO<Page<ProjectSchemeVO>> getProjectSchemeContentList(@RequestParam String id) throws Exception {
        return ResponseDTO.success(projectSchemeService.getProjectSchemeContentList(id));
    }

    @ApiOperation("通过前后置关系计算时间")
    @LogRecord(success = "【{USER{#logUserId}}】通过前后置关系计算时间", type = "ProjectScheme", subType = "计算时间", bizNo = "")
    @PostMapping(value = "/calculationTime/byPrePostRelation")
    public ResponseDTO<List<ProjectSchemeTemplateEditVO>> getTimeByPrePostRelation(@RequestBody ProjectSchemeTemplateEditDTO projectSchemeTemplateEditDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.getTimeByPrePostRelation(projectSchemeTemplateEditDTO));
    }
    @ApiOperation("获取项目所有计划分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了里程碑数据", type = "ProjectScheme", subType = "分页查询", bizNo = "")
    @PostMapping(value = "/page/getSchemePage")
    public ResponseDTO<Page<ProjectSchemeVO>> getSchemePage(@RequestBody Page<ProjectSchemeDTO> pageRequest) throws Exception {
        return ResponseDTO.success(projectSchemeService.getSchemePage(pageRequest));
    }

    @ApiOperation("通用变更添加受影响对象计划和里程碑查询")
    @PostMapping(value = "/page/getPlanOrMilestone")
    @LogRecord(success = "【{USER{#logUserId}}】通用变更添加受影响对象计划和里程碑查询", type = "ProjectScheme", subType = "通用变更添加受影响对象计划和里程碑查询", bizNo = "")
    public ResponseDTO<Page<ProjectSchemeVO>> getPlanOrMilestone(@RequestBody Page<SearchDTO> pageRequest,@RequestParam String type) throws Exception {
        return ResponseDTO.success(projectSchemeService.getPlanOrMilestone(pageRequest, type));
    }

    @ApiOperation("计划转办")
    @LogRecord(success = "【{USER{#logUserId}}】计划转办", type = "项目计划", subType = "计划转办(过程记录)",bizNo = "{{#projectSchemeDTO.id}}")
    @PutMapping(value = "/transfer")
    public ResponseDTO<Boolean> transfer(@RequestBody ProjectSchemeDTO projectSchemeDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.transfer(projectSchemeDTO));
    }

    @ApiOperation("计划变更创建")
    @PostMapping(value = "/createEcrScheme")
    @LogRecord(success = "【{USER{#logUserId}}】计划变更创建", type = "项目计划", subType = "计划变更创建(过程记录)",bizNo = "{{#ecrProjectSchemeAddDTO.id}}")
    public ResponseDTO<Boolean> createEcrScheme(@RequestBody EcrProjectSchemeAddDTO ecrProjectSchemeAddDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.createEcrScheme(ecrProjectSchemeAddDTO));
    }

    @ApiOperation("计划变更分页查询")
    @PostMapping(value = "/getEcrSchemePage")
    @LogRecord(success = "【{USER{#logUserId}}】计划变更分页查询", type = "项目计划", subType = "计划变更分页查询(过程记录)",bizNo = "")
    public ResponseDTO<Page<EcrVO>> getEcrSchemePage(@RequestBody Page<EcrDTO> page) throws Exception {
        return ResponseDTO.success(projectSchemeService.getEcrSchemePage(page));
    }

    @ApiOperation("个人中心履约计划统计")
    @PostMapping(value = "/getProject/stat")
    @LogRecord(success = "【{USER{#logUserId}}】个人中心履约计划统计", type = "项目计划", subType = "个人中心履约计划统计(过程记录)",bizNo = "")
    public ResponseDTO<ProjectCenterStatVO> getProjectStat(@RequestBody StatDTO statDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.getProjectStat(statDTO.getYear(),statDTO.getType()));
    }

    @ApiOperation("个人中心履约计划统计头部")
    @PostMapping(value = "/getProject/statHead")
    @LogRecord(success = "【{USER{#logUserId}}】个人中心履约计划统计头部", type = "项目计划", subType = "个人中心履约计划统计头部(过程记录)",bizNo = "")
    public ResponseDTO<ProjectCenterStatVO.PlanMilestoneData> getProjectStatHead(@RequestBody StatDTO statDTO) throws Exception {
        return ResponseDTO.success(projectSchemeService.getProjectStatHead(statDTO.getYear(),statDTO.getType()));
    }

    @ApiOperation("测试")
    @PostMapping(value = "/test")
    @LogRecord(success = "【{USER{#logUserId}}】测试", type = "项目计划", subType = "测试(过程记录)",bizNo = "")
    public ResponseDTO<Boolean> test() throws Exception {
        return ResponseDTO.success(projectSchemeService.test());
    }



    @ApiOperation("测试定时器")
    @GetMapping(value = "/test-xxl")
    @LogRecord(success = "【{USER{#logUserId}}】测试", type = "项目计划", subType = "测试(过程记录)",bizNo = "")
    public void testXXl() throws Exception {
        projectShemeXxljob.projectSchemeTryUpdateStatus();
    }
}
