package com.chinasie.orion.domain.vo.reporting;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ProjectDailyStatement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@ApiModel(value = "ProjectDailyStatementVO对象", description = "计划日报")
@Data
public class ProjectDailyStatementCardVO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date daily;

    /**
     * 日报状态
     */
    @ApiModelProperty(value = "日报状态")
    private Integer busStatus;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    private String creatorName;


    @ApiModelProperty(value = "状态名称")
    private String  busStatusName;

    @ApiModelProperty(value = "是否审核")
    private Boolean audit;
    @ApiModelProperty(value = "是否修改")
    private Boolean edit;
    @ApiModelProperty(value = "是否提醒")
    private Boolean warn;
    @ApiModelProperty(value = "是否提交")
    private Boolean commit;

    /**
     * 日报内容
     */
    @ApiModelProperty(value = "日报内容")
    private List<ProjectDailyStatementContentVO> projectDailyStatementContentVOList;
}
