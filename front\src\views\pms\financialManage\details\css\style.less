.customColors {
  :deep(.ant-btn) {
    margin-bottom: 3px;
    background-color: #ceedfc;
    color: #1890FF;
    border-color: #ceedfc;
  }
}

.select-btn {
  position: absolute;
  right: 115px;
  .ant-radio-button-wrapper:last-child {
    color: #f5222d;
    border-right: 0;
    border-radius: 0;
  }
}

:deep(.select-row) {
  display: flex;
  flex-direction: row;
  align-items: center;
}
:deep(.ant-basic-table.edit-disabled-color .surely-table-cell[disabled="true"]):before{
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  display: inline-block;
  width: 1px;
  background-color: rgb(244 240 240);
  height: 100%;
}

:deep(.surely-table-fix-left){
  .strip{
    .surely-table-cell-inner:before{
      display: none;
    }
  }
}
:deep(.surely-table-header){
  border-bottom: 1px solid #ebebeb;
}
:deep(.strip) {
  .surely-table-cell-inner:before {
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    display: inline-block;
    width: 1px;
    background-color: #e4e4e4;
    height: 100%;
    z-index: 9999;
  }
  border-bottom: 1px solid #efe7e7!important;
}

:deep(.surely-table-cell-inner) {
  .is-red {
    color: #f5222d;
  }

  .box-big {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #1890FF;
    cursor: pointer;
    .status-show {
      margin-right: 5px;
    }
  }

  .box-big.space {
    margin: 0 10px;
  }
}

.flex-r-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-total {
  .mb-10 {
    margin-left: 20px;
  }
}

:deep(.user-name), :deep(.status) {
  display: none;
}

:deep(.header-wrap .project-title) {
  width: auto!important;
}

:deep(.header-main) {
  border-right: none!important;
}

.total-num-left {
  span {
    font-weight: 500;
  }

  span:last-child {
    margin-left: 20px;
  }
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  .font-weight {
    font-weight: bold;
    font-size: 18px;
    margin-right: 20px;
  }
}

.header-common-s {
  background: inherit;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-weight: 400;
  font-style: normal;
  font-size: 12px;
  text-align: center;
  padding: 0 5px;
  height: 22px;
  line-height: 20px;
}

.header-green-s {
  width: 70px;
  border-color: #52C41A;
  color: #52C41A;
}

.header-blue-s {
  width: 70px;
  border-color: #1890FF;
  color: #1890FF;
}

:deep(.surely-table-cell-inner) {
  .common-center {
    display: flex;
    justify-content: center;
  }

  .common-s {
    background: inherit;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    text-align: center;
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
  }

  .green-s {
    width: 50px;
    background-color: rgba(246, 255, 237, 1);
    border-color: rgba(183, 235, 143, 1);
    color: #52C41A;
  }

  .warn-s {
    width: 50px;
    background-color: rgba(255, 251, 230, 1);
    border-color: rgba(255, 229, 143, 1);
    color: #FAAD14;
  }

  .blue-s {
    width: auto;
    background-color: rgba(230, 247, 255, 1);
    border-color: rgba(145, 213, 255, 1);
    color: #1890FF;
  }

  .red-s {
    width: 50px;
    background-color: rgba(254, 240, 239, 1);
    border-color: rgba(255, 163, 158, 1);
    color: #F5222D;
  }
}
:deep(.surely-table-summary.surely-table-summary-fixed.surely-table-summary-fixed-bottom) {
  height: 40px!important;
}
:deep(.header-wrap){
  height: auto;
  padding: 10px 0;
}
:deep(.ant-input-number-input){
  text-align: right;
  padding-right: 28px;
}
:deep(.orion-table-header-wrap){
  .flex-f1{
    .flex{
      .flex:nth-child(3){
        .ant-btn{
          border-radius: 0 3px 3px 0;
        }
      }
    }
  }
}
:deep(.surely-table-header){
  .surely-table-center-viewport{
    .surely-table-header-container{
      .surely-table-row-wrapper{
        .surely-table-header-cell-title{
          .header-column-wrap{
            .flex-te{
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              word-wrap: break-word;
              white-space: break-spaces;
            }
          }
        }
      }
    }
  }
}

.custom-class p {
  margin: 0; /* 移除默认的段落间距 */
}

.custom-class {
  white-space: pre-wrap; /* 保留空白符序列，但是正常地进行换行 */
}
