<template>
  <OrionTable :options="tableOptions" />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { formatMoney } from '/@/views/pms/utils/utils';
import { useRoute } from 'vue-router';

const route = useRoute();
const props = defineProps({});
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: false,
  rowKey: 'contractId',
  showIndexColumn: true,
  smallSearchField: ['name'],
  api: (tableParams) => new Api(`/pms/projectIncome/contract/list?id=${route.params.id}`).fetch('', '', 'GET'),
  columns: [
    {
      title: '合同编码',
      dataIndex: 'contractNumber',
      align: 'left',
    },
    {
      title: '合同年份',
      align: 'left',
      dataIndex: 'contractYear',
    },
    {
      title: '客户代码',
      align: 'left',
      dataIndex: 'clientCode',
    },
    {
      title: '销售部门',
      align: 'left',
      dataIndex: 'saleDept',
    },
    {
      title: '客户经理',
      align: 'left',
      dataIndex: 'accountManager',
    },
    {
      title: '商机标识',
      align: 'left',
      dataIndex: 'opportunityNumber',
    },
    {
      title: '合同产品编码',
      align: 'left',
      dataIndex: 'contrProductNumber',
    },
    {
      title: '商务员',
      align: 'left',
      dataIndex: 'clerk',
    },
    {
      title: '产品实际单价',
      align: 'left',
      dataIndex: 'productPrice',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
    {
      title: '产品金额',
      align: 'left',
      dataIndex: 'productAmount',
      customRender: ({ text }) => (text ? formatMoney(text) : ''),
    },
  ],
});
</script>

<style scoped lang="less"></style>
