package com.chinasie.orion.domain.vo.projectOverviewZgh;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 项目里程碑
 */
@Data
@ApiModel(value = "ProjectMilestoneVO", description = "项目里程碑")
public class ProjectMilestoneVO {
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "时间")
    private Date beginTime;
    @ApiModelProperty(value = "状态")
    private Integer typeId= 0;
    @ApiModelProperty(value = "数量")
    private String typeName ;
}
