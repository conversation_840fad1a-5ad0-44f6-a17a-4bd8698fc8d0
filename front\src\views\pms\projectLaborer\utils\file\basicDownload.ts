import Api from '../../api';
import { message } from 'ant-design-vue';
import { bigFileDownload, ChangeType } from './bigFileDownload';
import { downloadByData } from '../download';
import type { BaseFileInfo } from './types';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';

enum BasicDownLoadDefaultConfig {
  //  大文件阈值 100M
  BIG_FILE_SIZE = 1024 * 1024 * 100
}

interface FilePath extends DownloadBasic {
  filePath: string;
}

interface FileId extends DownloadBasic {
  fileId: string;
}

interface DownloadBasic {
  // 自定义下载Api
  api?: (BaseFileInfo) => any;
  // 是否获取书签
  isBookmark?: boolean;
  // 获取书签Api
  bookmarkApi?: (BaseFileInfo) => any;
  // 大文件阈值，当大于这个值启用大文件下载, 单位：字节
  bigFileSize?: number;
  bigFileConfig?: {
    // 分块大小 default 10M
    chunkSize?: number;
    // 并发数 default 1
    poolLimit?: number;
    onChange?: ChangeType;
  };
}

/**
 * 文件下载器
 * @description 集成大文件、书签、文件ID下载,
 * @param config 下载配置
 */
export async function basicDownload(config: FilePath | FileId) {
  const {
    api, bookmarkApi, isBookmark, bigFileSize,
  } = config;
  let fileInfo: BaseFileInfo = await getFileInfo(config);
  fileInfo.fileId = fileInfo.id;
  if (!fileInfo) {
    message.error('未获取到文件数据');
    return;
  }
  const { fileId, fileSize } = fileInfo;

  // 启用大文件下载
  if (
    (bigFileSize && Number(fileSize) > bigFileSize)
    || Number(fileSize) > BasicDownLoadDefaultConfig.BIG_FILE_SIZE
  ) {
    // debugger;
    await bigFileDownload({
      fileId,
      ...(config?.bigFileConfig ? { ...config.bigFileConfig } : {}),
    });
    return;
  }

  // 自定下载Api
  if (api) {
    api(fileInfo);
    return;
  }

  // 带书签的下载
  if (isBookmark || bookmarkApi) {
    await bookMarkDownload({
      isBookmark,
      bookmarkApi,
    }, fileInfo);
    return;
  }

  // 通过文件ID进行下载
  await downLoadById(fileId);
}

/**
 * 带书签的下载
 * @param config
 * @param fileInfo
 */
async function bookMarkDownload(config: DownloadBasic, fileInfo: BaseFileInfo) {
  const { bookmarkApi, isBookmark } = config;
  const { id, className, dataId } = fileInfo;
  let bookmark: object | null = {};
  if (bookmarkApi) {
    bookmark = await bookmarkApi(fileInfo);
  } else if (isBookmark) {
    bookmark = await getBookmark(id, dataId, className);
  }

  await downloadByData(
    '/file/book/download',
    {
      value: bookmark,
      fileId: fileInfo.id,
    },
    '',
    'POST',
    false,
  );
}

/**
 * 获取文件信息
 * @param config
 */
function getFileInfo(config: { fileId: string } | { filePath: string }): Promise<BaseFileInfo> {
  const { fileId, filePath } = config as any;
  return new Api('/res/file/list')
    .fetch(
      {
        ...(fileId
          ? {
            fileId,
          }
          : {}),
        ...(filePath
          ? {
            filePath,
          }
          : {}),
      },
      '',
      'POST',
    )
    .then((data) => {
      if (data && data.length) {
        return data[0];
      }
      return null;
    });
}

/**
 * 获取书签
 * @param fileId
 * @param dataId 文档ID
 * @param className 文档className
 */
function getBookmark(fileId, dataId, className) {
  return new Api(`/pms/bookmark/getBookmark/${dataId}/${fileId}/${className}`).fetch({}, '', 'GET');
}
