ALTER TABLE `pmsx_quotation_management`
    ADD COLUMN `send_out_user` varchar(64) NULL COMMENT '系统中触发发出报价的用户',
    ADD COLUMN `send_out_time` datetime    NULL COMMENT '系统中触发发出报价的时间';


INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`,
                                    `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`,
                                    `unique_key`, `logic_status`, `share`, `build_in`)
VALUES ('vub01831656513515290624', '需求分发确认', 'pms_requirement_confirm',
        '需求「$name$」已被「$userName$」确认为不响应，原因：[$desc$]', '1', 1, '', 'user00000000000000000100000000000000',
        '2024-09-05 19:31:58', 'user00000000000000000100000000000000', '2024-09-05 19:32:01',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');



INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`,
                                 `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`,
                                 `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`,
                                 `unique_key`, `logic_status`, `share`, `build_in`)
VALUES ('0a0y1831656754897485824', '需求分发确认拒绝通知', 'pms_requirement_confirm', 'vub01831656513515290624', NULL,
        0, 'SYS', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL,
        'user00000000000000000100000000000000', '2024-09-05 19:32:56', 'user00000000000000000100000000000000',
        '2024-09-05 19:33:37', NULL, 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1, b'0', b'0');



INSERT INTO `msc_business_node_channel` (`id`, `class_name`, `creator_id`, `modify_time`, `owner_id`, `create_time`,
                                         `modify_id`, `remark`, `platform_id`, `org_id`, `status`, `logic_status`,
                                         `business_node_id`, `type`, `template_flag`, `custom`)
VALUES ('35rj1831656916751482880', 'BusinessNodeChannel', 'user00000000000000000100000000000000', '2024-09-05 19:33:34',
        'user00000000000000000100000000000000', '2024-09-05 19:33:34', 'user00000000000000000100000000000000', NULL,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', 1, 1, '0a0y1831656754897485824',
        'SYS', b'0', NULL);



CREATE TABLE `pmsx_requirement_management_mark`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `class_name`     varchar(64) COMMENT '创建人',
    `creator_id`     varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`    datetime    NOT NULL COMMENT '修改时间',
    `owner_id`       varchar(64) COMMENT '拥有者',
    `create_time`    datetime    NOT NULL COMMENT '创建时间',
    `modify_id`      varchar(64) NOT NULL COMMENT '修改人',
    `remark`         text(1024) COMMENT '备注',
    `platform_id`    varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`         varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`         int         NOT NULL COMMENT '状态',
    `logic_status`   int         NOT NULL COMMENT '逻辑删除字段',
    `requirement_id` varchar(64) NOT NULL COMMENT '需求管理id，关联 pms_requirement_mangement id',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='需求管理标注';



INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`,
                         `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`,
                         `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`,
                         `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`)
VALUES ('gtjl1831661417757581312', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_requirement_management_mark',
        'RequirementManagementMark', 'm65r', NULL, 'pmsx', 'requirementmanagementmark', 'common', NULL, NULL, NULL,
        NULL, NULL, NULL, 1, '需求管理标注', '314j1000000000000000000', '2024-09-05 19:51:27',
        '314j1000000000000000000', '2024-09-05 20:04:50', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, '314j1000000000000000000', 0);



INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827520', 'gtjl1831661417757581312', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL,
        1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827521', 'gtjl1831661417757581312', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827522', 'gtjl1831661417757581312', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827523', 'gtjl1831661417757581312', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27',
        '314j1000000000000000000', '2024-09-05 19:51:27', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827524', 'gtjl1831661417757581312', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827525', 'gtjl1831661417757581312', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27',
        '314j1000000000000000000', '2024-09-05 19:51:27', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827526', 'gtjl1831661417757581312', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827527', 'gtjl1831661417757581312', 'remark', 'Text', 1024, NULL, 'remark', NULL, NULL, NULL,
        NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:43', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827528', 'gtjl1831661417757581312', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27',
        '314j1000000000000000000', '2024-09-05 19:51:27', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827529', 'gtjl1831661417757581312', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827530', 'gtjl1831661417757581312', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27', '314j1000000000000000000',
        '2024-09-05 19:51:27', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831661417870827531', 'gtjl1831661417757581312', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 19:51:27',
        '314j1000000000000000000', '2024-09-05 19:51:27', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831664756746526720', 'gtjl1831661417757581312', 'requirementId', 'Varchar', 64, NULL, 'requirementid',
        NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-05 20:04:43',
        '314j1000000000000000000', '2024-09-05 20:04:43', '需求管理id，关联 pms_requirement_mangement id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
