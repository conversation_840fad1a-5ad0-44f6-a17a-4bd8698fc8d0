package com.chinasie.orion.domain.entity.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewEssentials Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
@TableName(value = "pmsx_review_essentials")
@ApiModel(value = "ReviewEssentialsEntity对象", description = "评审要点")
@Data
public class ReviewEssentials extends ObjectEntity implements Serializable {

    /**
     * 评审要点内容
     */
    @ApiModelProperty(value = "评审要点内容")
    @TableField(value = "content")
    private String content;

    /**
     * 评审阶段
     */
    @ApiModelProperty(value = "评审阶段")
    @TableField(value = "review_phase")
    private String reviewPhase;

    /**
     * 要点类型
     */
    @ApiModelProperty(value = "要点类型")
    @TableField(value = "essentials_type")
    private String essentialsType;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableField(value = "id")
    private String id;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
