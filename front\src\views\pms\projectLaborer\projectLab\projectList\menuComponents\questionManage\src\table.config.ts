export const columns = [
  {
    title: '编号',
    dataIndex: 'number',
    key: 'number',
    align: 'left',
    width: '150px',
    sorter: true,
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: '300px',
    align: 'left',
    slots: { customRender: 'name' },
    sorter: true,
    ellipsis: true,
  },

  {
    title: '提出人',
    dataIndex: 'exhibitor',
    key: 'exhibitor',
    width: '70px',
    margin: '0 20px 0 0',
    align: 'left',
    slots: { customRender: 'exhibitorId' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '提出时间',
    dataIndex: 'proposedTime',
    key: 'proposedTime',
    width: '100px',
    align: 'left',
    slots: { customRender: 'proposedTime' },

    sorter: true,
    ellipsis: true,
  },
  {
    title: '期望完成时间',
    dataIndex: 'predictEndTime',
    key: 'predictEndTime',
    width: '110px',
    align: 'left',
    slots: { customRender: 'predictEndTime' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '严重程度',
    dataIndex: 'seriousLevelName',
    key: 'seriousLevel',
    width: '70px',
    align: 'left',
    slots: { customRender: 'seriousLevelName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevelName',
    key: 'priorityLevel',
    width: '70px',
    align: 'left',
    slots: { customRender: 'seriousLevel' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '进度',
    dataIndex: 'schedule',
    key: 'schedule',
    width: '70px',
    align: 'left',
    slots: { customRender: 'schedule' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',
    width: '70px',
    align: 'left',
    slots: { customRender: 'statusName' },
    sorter: true,
    ellipsis: true,
  },
  {
    title: '负责人',
    dataIndex: 'principalName',
    key: 'principalId',
    width: '70px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'principalName' },
  },
  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    key: 'modifyTime',
    width: '150px',
    align: 'left',
    sorter: true,
    ellipsis: true,
    slots: { customRender: 'modifyTime' },
  },
];
