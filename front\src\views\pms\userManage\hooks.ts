import {
  computed, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';

export function useBaseData() {
  const basePlaceCode: Ref<string> = ref();
  const baseOptions: Ref<any[]> = ref([]);
  const fetching: Ref<boolean> = ref(false);
  const baseName = computed(() => baseOptions.value.find((item) => item.code === basePlaceCode.value)?.name);

  async function getBaseOptions() {
    fetching.value = true;
    try {
      const result = await new Api('/pms/base-place/list').fetch('', '', 'POST');
      baseOptions.value = result || [];
      basePlaceCode.value = baseOptions.value?.[0]?.code;
    } finally {
      fetching.value = false;
    }
  }

  onMounted(async () => {
    await getBaseOptions();
  });

  return {
    basePlaceCode,
    baseOptions,
    fetching,
    baseName,
  };
}
