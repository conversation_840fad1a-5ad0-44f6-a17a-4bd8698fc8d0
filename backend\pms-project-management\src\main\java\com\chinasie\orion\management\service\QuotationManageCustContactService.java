package com.chinasie.orion.management.service;


import com.chinasie.orion.management.domain.dto.QuotationManageCustContactDTO;
import com.chinasie.orion.management.domain.entity.QuotationManageCustContact;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.vo.QuotationManageCustContactVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;


/**
 * <p>
 * QuotationManageCustContact 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:59
 */
public interface QuotationManageCustContactService extends OrionBaseService<QuotationManageCustContact> {

    /**
     * 详情
     * <p>
     * * @param id
     */
    QuotationManageCustContactVO detail(String id, String pageCode);

    /**
     * 保存报检单客户的相关联系人
     *
     * @param contacts  联系人
     * @param quotation 报检单
     */
    void saveQuotationContacts(List<QuotationManageCustContact> contacts, QuotationManagement quotation);


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids);


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<QuotationManageCustContactVO> pages(Page<QuotationManageCustContactDTO> pageRequest);


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<QuotationManageCustContactVO> vos);
}
