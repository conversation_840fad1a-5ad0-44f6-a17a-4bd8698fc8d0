package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.YearInvestmentSchemeMonthFeedbackDTO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeMonthFeedbackVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.YearInvestmentSchemeMonthFeedbackService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * YearInvestmentSchemeMonthFeedback 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15 19:21:49
 */
@RestController
@RequestMapping("/year-investment-scheme-month-feedback")
@Api(tags = "月度投资计划反馈")
public class YearInvestmentSchemeMonthFeedbackController {

    @Autowired
    private YearInvestmentSchemeMonthFeedbackService yearInvestmentSchemeMonthFeedbackService;


    /**
     * 自动获取的值
     *
     * @param yearId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "自动获取的值")
    @RequestMapping(value = "/init/{yearId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】自动获取的值", type = "月度投资计划反馈", subType = "自动获取的值", bizNo = "")
    public ResponseDTO<YearInvestmentSchemeMonthFeedbackVO> initValue(@PathVariable("yearId") String yearId) throws Exception {
        YearInvestmentSchemeMonthFeedbackVO rsp = yearInvestmentSchemeMonthFeedbackService.initValue(yearId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "月度投资计划反馈", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<YearInvestmentSchemeMonthFeedbackVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        YearInvestmentSchemeMonthFeedbackVO rsp = yearInvestmentSchemeMonthFeedbackService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param yearInvestmentSchemeMonthFeedbackDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "月度投资计划反馈", subType = "新增", bizNo = "")
    public ResponseDTO<YearInvestmentSchemeMonthFeedbackVO> create(@RequestBody YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception {
        YearInvestmentSchemeMonthFeedbackVO rsp = yearInvestmentSchemeMonthFeedbackService.create(yearInvestmentSchemeMonthFeedbackDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param yearInvestmentSchemeMonthFeedbackDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "月度投资计划反馈", subType = "编辑", bizNo = "")
    public ResponseDTO<YearInvestmentSchemeMonthFeedbackVO> edit(@RequestBody YearInvestmentSchemeMonthFeedbackDTO yearInvestmentSchemeMonthFeedbackDTO) throws Exception {
        YearInvestmentSchemeMonthFeedbackVO rsp = yearInvestmentSchemeMonthFeedbackService.edit(yearInvestmentSchemeMonthFeedbackDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "月度投资计划反馈", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = yearInvestmentSchemeMonthFeedbackService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 列表
     *
     * @param yearId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list/{yearId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】列表", type = "月度投资计划反馈", subType = "列表", bizNo = "{{#id}}")
    public ResponseDTO<List<YearInvestmentSchemeMonthFeedbackVO>> list(@PathVariable("yearId") String yearId,
                                                                       @RequestParam(required = false) String pageCode,
                                                                       @RequestParam(required = false) String containerCode) throws Exception {
        List<YearInvestmentSchemeMonthFeedbackVO> rsp = yearInvestmentSchemeMonthFeedbackService.list(yearId,pageCode,containerCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 每月15号需要给项目负责人发送待办提醒通知，引导其完成月度计划反馈的编制
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "每月15号需要给项目负责人发送待办提醒通知，引导其完成月度计划反馈的编制")
    @RequestMapping(value = "/sendTodo", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】每月15号需要给项目负责人发送待办提醒通知，引导其完成月度计划反馈的编制", type = "月度投资计划反馈", subType = "每月15号需要给项目负责人发送待办提醒通知，引导其完成月度计划反馈的编制", bizNo = "")
    public void sendTodo() throws Exception {
        yearInvestmentSchemeMonthFeedbackService.sendTodo();

    }

}
