package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.domain.vo.SafetyQualityEnvVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

@ApiModel(value = "SafetyQualityEnvStatisticDTO对象", description = "安质环")
@Data
@ExcelIgnoreUnannotated
public class SafetyQualityEnvStatisticDTO {
    private List<SafetyQualityEnvVO> safetyQualityEnvVOList;
    /**
     * 考核级别统计
     */
    @ApiModelProperty(value = "考核级别统计")
    private HashMap<String,Integer> evenStatisticMap;
}
