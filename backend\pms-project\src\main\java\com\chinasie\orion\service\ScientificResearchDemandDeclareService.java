package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.LeadManagementDTO;
import com.chinasie.orion.domain.dto.ScientificResearchDemandDeclareDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.entity.ScientificResearchDemandDeclare;
import com.chinasie.orion.domain.vo.LeadManagementVO;
import com.chinasie.orion.domain.vo.ScientificResearchDemandDeclareVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ScientificResearchDemandDeclare 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 11:20:05
 */
public interface ScientificResearchDemandDeclareService extends OrionBaseService<ScientificResearchDemandDeclare> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ScientificResearchDemandDeclareVO detail(String id,String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param scientificResearchDemandDeclareDTO
     */
    ScientificResearchDemandDeclareVO create(ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param scientificResearchDemandDeclareDTO
     */
    ScientificResearchDemandDeclareVO edit(ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;

    Boolean removeByIds(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ScientificResearchDemandDeclareVO> pages(Page<ScientificResearchDemandDeclareDTO> pageRequest) throws Exception;

    /**
     * 第三方获取分页
     *
     * <AUTHOR>
     * @date 2023/11/24 09:27
     */
    Page<ScientificResearchDemandDeclareVO> thirdPages(Page<SearchDTO> pageRequest);

    Page<LeadManagementVO> getLeadPage(Page<LeadManagementDTO> pageRequest) throws Exception;


    Boolean deleteRelateProject(String id, List<String> projectIds) throws Exception;
}

