package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ConnectedMilestones VO对象
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@ApiModel(value = "ConnectedMilestonesVO对象", description = "挂接里程碑")
@Data
public class ConnectedMilestonesVO extends ObjectVO implements Serializable {

    /**
     * 合同里程碑
     */
    @ApiModelProperty(value = "合同里程碑")
    private String milestoneName;

    @ApiModelProperty(value = "合同里程碑Id")
    private String milestoneId;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String certificateSerialNumber;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    private Date postingDate;

    /**
     * 确认收入金额
     */
    @ApiModelProperty(value = "确认收入金额")
    private BigDecimal confirmRevenueAmount;

    /**
     * 冲销暂估金额
     */
    @ApiModelProperty(value = "冲销暂估金额")
    private BigDecimal reverseAmount;

    /**
     * 合同编号(备注)
     */
    @ApiModelProperty(value = "合同编号(备注)")
    private String contractNumRemark;

    /**
     * 里程碑名称(备注)
     */
    @ApiModelProperty(value = "里程碑名称(备注)")
    private String mileStoneRemark;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 挂接状态:0未挂接，1已挂接
     */
    @ApiModelProperty(value = "挂接状态:0未挂接，1已挂接")
    private Integer hangingConnectStatus;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    private String partyADeptId;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String number;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    private String expertiseCenter;



    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    private String expertiseStation;


    /**
     * 收入计划月份
     */
    @ApiModelProperty(value = "收入计划月份")
    private Date estimateInvoiceDate;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeConfirmType;

    @ApiModelProperty(value = "收入确认类型")
    private String incomeConfirmTypeName;

    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    private String billingCompany;


    /**
     * 甲方单位名称
     */
    @ApiModelProperty(value = "甲方单位名称")
    private String partyADeptIdName;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterName;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationName;

    /**
     * 开票/收入确认公司名称
     */
    @ApiModelProperty(value = "开票/收入确认公司名称")
    private String billingCompanyName;

}
