package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.MarketContractDivisionLeaderDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractApiVO;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * MarketContract Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@Mapper
public interface MarketContractMapper extends OrionBaseMapper<MarketContract> {
    /**
     * 根据项目编号查询市场合同
     *
     * @param projectNumbers
     * @return
     */
    List<MarketContractApiVO> findByProjectNumber(@Param("projectNumbers") List<String> projectNumbers);

    /**
     * 查询子合同信息
     *
     * @param contractIds
     * @return
     */
    List<MarketContractApiVO> findByFrameContractIds(@Param("contractIds") List<String> contractIds);

    MarketContractDivisionLeaderDTO getDeptByCodeAndOrgId(@Param("deptCode") String deptCode, @Param("orgId") String orgId);

}

