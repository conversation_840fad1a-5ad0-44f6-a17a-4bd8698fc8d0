package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/03/29/13:42
 * @description:
 */
@Data
public class RoleUserParamDTO implements Serializable {
    @ApiModelProperty(value = "前后置ID")
    @Size(min = 1,message = "角色ID不能为空")
    private List<String> roleIdList;

    @ApiModelProperty(value = "前后置ID")
    @Size(min = 1,message = "项目ID不能为空")
    private List<String> projectIdList;
}
