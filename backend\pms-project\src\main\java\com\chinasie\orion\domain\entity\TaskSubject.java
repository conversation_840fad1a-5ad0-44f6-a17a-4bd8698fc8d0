package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:16
 * @description:
 */
@Data
@TableName(value = "pms_task_subject")
@ApiModel(value = "TaskSubject对象", description = "任务科目")
public class TaskSubject extends ObjectEntity {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    @TableField(value = "take_effect")
    private Integer takeEffect;



}
