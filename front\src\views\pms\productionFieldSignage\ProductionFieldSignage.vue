<script lang="ts">
import {
  Col, Progress,
  Row,
} from 'ant-design-vue';

import {
  BasicCard,
  Chart,
  Layout,
  Select,
} from 'lyra-component-vue3';

import {
  defineComponent, onMounted,
  reactive,
  ref,
  toRefs,
} from 'vue';

import Api from '/@/api';
import OverhaulCalendar from '/@/views/pms/majorRepairs/components/MajorRepairsTable.vue';

import {
  option3,
  option5,
} from '/@/views/pms/productionFieldSignage/ChartOption';
export default defineComponent({
  name: 'ProductionFieldSignage',
  components: {
    ARow: Row,
    ACol: Col,
    Select,
    Chart,
    AProgress: Progress,
    OverhaulCalendar,
    BasicCard,

  },
  emits: ['update'],
  setup(props, { emit }) {
    const state: any = reactive({

      locationCode: '',
      locationCode1: '',

      mainBusinessData: [],
      classificationData: {},
      jxName: '中心',
      jxvalue: 1,
      jxDatas: [],
      jxName2: '安质环偏差',
      jxValue2: 'getSafetyRankingList',
      MajorRepairsData: {},

      baseName: '',
      startDate: '2023-01-01',
      endDate: '2024-12-31',

    });
    onMounted(() => {
      getAllData();
    });
    // 获取全部数据
    function getAllData() {
      getSafetyOptions();
      getSafetyData();
      getMainBusiness();
      getClueType();
      getBasePersonnel();
      jxoptions2Change('getSafetyRankingList');
      getKeyDemandRatio();
      // getMajorRepairsData();
    }

    // 安质环关键指标options
    let safetyOptions = ref([]);
    async function getSafetyOptions() {
      try {
        const result = await new Api('/pms/productionDashboard/getLocation').fetch({
        }, '', 'POST');
        safetyOptions.value = result.map((item: any) => ({
          label: item.name,
          value: item.code,
        }));
        safetyOptions.value = [
          {
            label: '全部',
            value: '',
          },
          ...safetyOptions.value,
        ];
      } finally {
        loading.value = false;
      }
    }
    const loading = ref(false);

    // safetyChartData: {},
    let safetyChartData = ref({});
    // 安质环关键指标
    async function getSafetyData() {
      loading.value = true;
      try {
        const result = await new Api('/pms/productionDashboard/getKeySafety').fetch({
          locationCode: state.locationCode,
        }, '', 'POST');
        // safetyData.value = result || {};

        safetyChartData.value = {
          // tooltip: {
          //   trigger: 'axis',
          //   axisPointer: {
          //     type: 'cross',
          //     crossStyle: {
          //       color: '#999',
          //     },
          //   },
          // },
          // toolbox: {
          //   feature: {
          //     dataView: {
          //       show: true,
          //       readOnly: false,
          //     },
          //     magicType: {
          //       show: true,
          //       type: ['line', 'bar'],
          //     },
          //     restore: { show: true },
          //     saveAsImage: { show: true },
          //   },
          // },
          grid: {
            // top: '0%',
            left: '3%',
            right: '5%',
            bottom: '2%',
            containLabel: true,
          },
          legend: {
            // data: ['外部监督', '内部自查', '2023趋势', '2024趋势', '标准考核值'],
            data: [
              '外部监督',
              '内部自查',
              '2023趋势',
              '2024趋势',
            ],
            // data: result.map((item: any) => item.label),
          },
          xAxis: [
            {
              type: 'category',
              // data: [
              //   'A类违章数量',
              //   'B类违章数量',
              //   'C类违章数量',
              //   'F1事件数量',
              //   'F2事件数量',
              //   '指标事件',
              // ],

              data: result.map((item: any) => item.label),
              axisPointer: {
                type: 'shadow',
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              // name: 'Precipitation',

              axisLabel: {
                formatter: '{value}',
              },
            },
            {
              type: 'value',

              // name: 'Temperature',
              // min: 0,
              // max: 25,
              // interval: 5,
              axisLabel: {
                formatter: '{value} ',
              },
            },
          ],
          series: [
            {
              name: '外部监督',
              type: 'bar',
              tooltip: {
                valueFormatter(value) {
                  return `${value as number} `;
                },
              },
              // data: [
              //   0,
              //   1,
              //   4,
              //   2,
              //   9,
              //   0,
              // ],
              data: result.map((item: any) => item.outNum),
            },
            {
              name: '内部自查',
              type: 'bar',
              tooltip: {
                valueFormatter(value) {
                  return `${value as number} `;
                },
              },
              // data: [
              //   0,
              //   0,
              //   0,
              //   0,
              //   0,
              //   0,
              // ],
              data: result.map((item: any) => item.inNum),
            },
            {
              name: '2023趋势',
              type: 'line',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter(value) {
                  return `${value as number} `;
                },
              },
              // data: [
              //   2,
              //   4,
              //   6,
              //   7,
              //   10,
              //   12,
              // ],
              data: result.map((item: any) => item.lastNum),
            },
            {
              name: '2024趋势',
              type: 'line',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter(value) {
                  return `${value as number} `;
                },
              },
              // data: [
              //   0,
              //   3,
              //   4,
              //   6,
              //   9,
              //   10,

              // ],
              data: result.map((item: any) => item.num),
            },
            // {
            //   name: '标准考核值',
            //   type: 'line',
            //   yAxisIndex: 1,
            //   tooltip: {
            //     valueFormatter(value) {
            //       return `${value as number} °C`;
            //     },
            //   },
            //   data: [
            //     1, 4, 6, 9, 13, 18, 23,
            //   ],
            // },
          ],
        };
      } finally {
        loading.value = false;
      }
    }

    // 核电主业考核指标状态
    async function getMainBusiness() {
      try {
        const result = await new Api('/pms/productionDashboard/getIndexStatus').fetch({
          locationCode: state.locationCode,
        }, '', 'POST');
        state.mainBusinessData = result || [];
      } finally {
        loading.value = false;
      }
    }
    // 线索分类占比
    let classificationChartData = ref({});
    async function getClueType() {
      try {
        const result = await new Api('/pms/productionDashboard/getLeadNum').fetch({
        }, '', 'POST');
        state.classificationData = result || {};
        let chartData = result.map((item: any) => ({
          value: item.leadTypeNumber ? item.leadTypeNumber : 0,
          name: item.leadTypeName ? item.leadTypeName : '',
        }));
        classificationChartData.value = {
          grid: {
            top: '0%',
            left: '1%',
            right: '2%',
            bottom: '0%',
            containLabel: true,
          },

          title: {
            // text: '合同类型',
            // subtext: 'Fake Data',
            // left: 'center',
          },
          tooltip: {
            trigger: 'item',
          },
          legend: {
            orient: 'vertical',
            left: 'right',
          },
          series: [
            {
              labelLine: {
                show: false, // 隐藏提示线
              },
              label: {
                show: false,
              },
              type: 'pie',
              radius: '50%',
              data: chartData,
              // data: [
              //   {
              //     value: 413,
              //     name: '需求跟踪',
              //   },
              //   {
              //     value: 200,
              //     name: '技术引领',
              //   },
              // ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        };
      } finally {
        loading.value = false;
      }
    }
    // 需求关键比率
    let keyDemandRatio = ref({});
    async function getKeyDemandRatio() {
      keyDemandRatio.value = {
        title: [
          {
            text: '需求响应转换率',
            left: '15%',
            top: '60%',
            textAlign: 'center',
            textStyle: {
              fontSize: 16, // 字体大小
              color: '#333', // 字体颜色
              fontWeight: 'bold', // 字体粗细
              lineHeight: 30,
            },
          },
          {
            text: '后台需求承接率',
            left: '50%',
            top: '60%',
            textAlign: 'center',
            textStyle: {
              fontSize: 16, // 字体大小
              color: '#333', // 字体颜色
              fontWeight: 'bold', // 字体粗细
              lineHeight: 30,
            },
          },
          {
            text: '需求实现承接率',
            left: '84%',
            top: '60%',
            textAlign: 'center',
            textStyle: {
              fontSize: 16, // 字体大小
              color: '#333', // 字体颜色
              fontWeight: 'bold', // 字体粗细
              lineHeight: 30,
            },
          },
        ],
        tooltip: {
          show: false, // 禁止鼠标移入显示提示
        },
        series: [
          {
            name: '需求响应转换率',
            type: 'pie',
            radius: ['25%', '40%'],
            center: ['15%', '40%'],
            data: [
              {
                value: 55,
                name: '需求响应转换率',
                itemStyle: {
                  color: '#5270cc',
                },
              },
              {
                value: 45,
                name: '其他',
                itemStyle: {
                  // 灰色
                  color: '#d7d7d7',
                },
                emphasis: {
                  label: {
                    show: false, // 禁止鼠标移入时显示label
                  },
                },
              },
            ],
            label: {
              position: 'center',
              formatter: '{d}%',
              show: true,
              fontSize: 30,
              fontWeight: 'bold',
            },
            labelLine: {
              show: false,
            },
          },
          {
            name: '后台需求承接率',
            type: 'pie',
            radius: ['25%', '40%'],
            center: ['50%', '40%'],
            data: [
              {
                value: 57,
                name: '后台需求承接率',
                itemStyle: {
                  color: '#5270cc',
                },
              },
              {
                value: 43,
                name: '其他',
                itemStyle: {
                  // 灰色
                  color: '#d7d7d7',
                },
                emphasis: {
                  label: {
                    show: false, // 禁止鼠标移入时显示label
                  },
                },
              },
            ],
            label: {
              position: 'center',
              formatter: '{d}%',
              show: true,
              fontSize: 30,
              fontWeight: 'bold',
            },
            labelLine: {
              show: false,
            },
          },
          {
            name: '需求实现承接率',
            type: 'pie',
            radius: ['25%', '40%'],
            center: ['85%', '40%'],
            data: [
              {
                value: 75,
                name: '需求实现承接率',
                itemStyle: {
                  color: '#78cc52',
                },
              },
              {
                value: 25,
                name: '其他',
                itemStyle: {
                  // 灰色
                  color: '#d7d7d7',
                },
                emphasis: {
                  label: {
                    show: false, // 禁止鼠标移入时显示label
                  },
                },
              },
            ],
            label: {
              position: 'center',
              formatter: '{d}%',
              show: true,
              fontSize: 30,
              fontWeight: 'bold',
            },
            labelLine: {
              show: false,
            },
          },
        ],
      };
    }

    // 基地人员
    let basePersonnel = ref({});
    async function getBasePersonnel() {
      try {
        // 转成 FormData
        let formData = new FormData();
        formData.append('locationCode', state.locationCode1);

        const result = await new Api('/pms/productionDashboard/getPersonNum').fetch(formData, '', 'POST');
        basePersonnel.value = result || {};
        // if (state.locationCode1 === 'HYH') {
        //   basePersonnel.value = {
        //     number: 220,
        //     businessTripNumber: 20,
        //     permanentNumber: 200,
        //   };
        // } else if (state.locationCode1 === 'DYW') {
        //   basePersonnel.value = {
        //     number: 440,
        //     businessTripNumber: 40,
        //     permanentNumber: 400,
        //   };
        // } else if (state.locationCode1 === 'YJ') {
        //   basePersonnel.value = {
        //     number: 187,
        //     businessTripNumber: 17,
        //     permanentNumber: 170,
        //   };
        // } else if (state.locationCode1 === 'ND') {
        //   basePersonnel.value = {
        //     number: 187,
        //     businessTripNumber: 17,
        //     permanentNumber: 170,
        //   };
        // } else if (state.locationCode1 === 'TS') {
        //   basePersonnel.value = {
        //     number: 187,
        //     businessTripNumber: 17,
        //     permanentNumber: 170,
        //   };
        // } else if (state.locationCode1 === 'FCG') {
        //   basePersonnel.value = {
        //     number: 110,
        //     businessTripNumber: 10,
        //     permanentNumber: 100,
        //   };
        // } else {
        //   basePersonnel.value = {
        //     // 合计
        //     number: 1331,
        //     businessTripNumber: 121,
        //     permanentNumber: 1210,
        //   };
        // }
      } finally {
        loading.value = false;
      }
    }

    const jxoptions = [
      {
        label: '中心',
        value: 1,
      },
      {
        label: '项目部',
        value: 2,
      },
    ];
    const jxoptions2 = [
      {
        label: '安质环偏差',
        value: 'getSafetyRankingList',
      },
      {
        label: '营收额',
        value: 'getRevenueRankingList',
      },
      {
        label: '大修工期节约',
        value: 'getDurationRankingList',
      },
      {
        label: '集体剂量降低',
        value: 'getDoseRankingList',
      },
    ];
    function jxoptionsChange(data: number) {
      if (data === 1) {
        state.jxName = '中心';
      } else if (data === 2) {
        state.jxName = '项目部';
      }
    }

    async function jxoptions2Change(url) {
      if (url === 'getSafetyRankingList') {
        state.jxName2 = '安质环偏差';
      } else if (url === 'getRevenueRankingList') {
        state.jxName2 = '营收额';
      } else if (url === 'getDurationRankingList') {
        state.jxName2 = '大修工期节约';
      } else if (url === 'getDoseRankingList') {
        state.jxName2 = '集体剂量降低';
      }
      try {
        const result = await new Api(`/pms/productionDashboard/${url}`).fetch({
        }, '', 'POST');
        state.jxDatas = result || [];
      } finally {
        loading.value = false;
      }
    }

    return {
      loading,
      ...toRefs(state),
      safetyChartData,
      classificationChartData,
      safetyOptions,
      basePersonnel,
      getSafetyData,

      jxoptions,
      jxoptions2,
      jxoptionsChange,
      jxoptions2Change,
      getBasePersonnel,
      keyDemandRatio,

      option3,
      option5,
    };
  },
});
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <div
      v-if="loading"
      class="w-full h-full flex flex-pac"
    >
      <Spin />
    </div>
    <div class="page-title" />
    <div class="page-com">
      <ARow :gutter="[16, 16]">
        <ACol :span="18">
          <BasicCard
            :isBorder="true"
            title="安质环关键指标"
            class="card-border card-1"
          >
            <template #titleRight>
              <Select
                v-model:value="locationCode"
                :showSearch="false"
                style="width: 120px;"
                size="small"
                :options="safetyOptions"
                @change="getSafetyData"
              />
            </template>
            <div class="card-com chart-box">
              <Chart
                ref="state"
                :option="safetyChartData"
              />
            </div>
          </BasicCard>
        </ACol>
        <ACol :span="6">
          <BasicCard
            :isBorder="true"
            title="核电主业考核指标状态"
            class="card-border card-2"
          >
            <div class="card-com">
              <div class="card-table">
                <div class="card-table-th">
                  <div class="card-table-td">
                    考核类型
                  </div>
                  <div class="card-table-td">
                    目标值
                  </div>
                  <div class="card-table-td card-table-td3">
                    实际值
                  </div>
                  <div class="card-table-td">
                    状态灯
                  </div>
                </div>
                <div class="card-table-content">
                  <div
                    v-for="item in mainBusinessData"
                    :key="item.id"
                    class="card-table-tr"
                  >
                    <div
                      class="card-table-td"
                      :title="item.indexType"
                    >
                      {{ item.indexType }}
                    </div>
                    <div
                      class="card-table-td"
                      :title="item.indexTarget"
                    >
                      {{ item.indexTarget }}
                    </div>
                    <div
                      class="card-table-td card-table-td3"
                      :title="item.indexActual"
                    >
                      {{ item.indexActual }}
                    </div>
                    <div class="card-table-td">
                      <div :class="'ztd-' + item.indexStatus" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BasicCard>
        </ACol>

        <ACol
          :xxxl="6"
          :xl="9"
        >
          <BasicCard
            :isBorder="true"
            title="线索分类占比"
            class="card-border card-3"
          >
            <div class="card-com chart-box">
              <Chart :option="classificationChartData" />
            </div>
          </BasicCard>

          <BasicCard
            :isBorder="true"
            title="需求关键比率"
            class="card-border card-4"
          >
            <div class="card-com chart-box">
              <Chart :option="keyDemandRatio" />
              <!-- <div class="aprogress-box">
                <div class="aprogress-title">
                  紧急响应及时率
                </div>
                <div class="aprogress-com">
                  <AProgress
                    title="title"
                    :percent="50"
                    status="active"
                  />
                </div>
              </div>
              <div class="aprogress-box">
                <div class="aprogress-title">
                  紧急响应满意率
                </div>
                <div class="aprogress-com">
                  <AProgress
                    title="title"
                    :percent="50"
                    status="active"
                  />
                </div>
              </div> -->
            </div>
          </BasicCard>
        </ACol>

        <ACol
          :xxxl="12"
          :xl="15"
        >
          <BasicCard
            :isBorder="true"
            title="各响应类型合同额占比"
            class="card-border card-6"
          >
            <div class="card-com chart-box">
              <Chart
                ref="chartRef"
                :option="option3"
              />
            </div>
          </BasicCard>
          <BasicCard
            :isBorder="true"
            title="累计基地营业额（万元)"
            class="card-border card-7"
          >
            <div class="card-com chart-box">
              <Chart
                ref="chartRef"
                :option="option5"
              />
            </div>
          </BasicCard>
        </ACol>

        <ACol
          :xxxl="6"
          :xl="24"
        >
          <BasicCard
            :isBorder="true"
            title="基地人员"
            class="card-border card-8"
          >
            <template #titleRight>
              <Select
                v-model:value="locationCode1"
                :showSearch="false"
                style="width: 120px;"
                size="small"
                :options="safetyOptions"
                @change="getBasePersonnel"
              />
            </template>
            <div class="card-com ">
              <div class="aprogress-box">
                <div class="aprogress-title">
                  总人数
                </div>
                <div class="aprogress-com">
                  <AProgress
                    title="title"
                    status="active"
                    :percent="100"
                    :showInfo="true"
                    :format="() => basePersonnel.number"
                  />
                </div>
              </div>
              <div class="aprogress-box">
                <div class="aprogress-title">
                  出差人数
                </div>
                <div class="aprogress-com">
                  <AProgress
                    title="title"
                    :percent="(basePersonnel.businessTripNumber / basePersonnel.number) * 100"
                    status="active"
                    :showInfo="true"
                    :format="() => basePersonnel.businessTripNumber"
                  />
                </div>
              </div>
              <div class="aprogress-box">
                <div class="aprogress-title">
                  作业人员
                </div>
                <div class="aprogress-com">
                  <AProgress
                    title="title"
                    :percent="(basePersonnel.permanentNumber / basePersonnel.number) * 100"
                    status="active"
                    :showInfo="true"
                    :format="() => basePersonnel.permanentNumber"
                  />
                </div>
              </div>
            </div>
          </BasicCard>

          <BasicCard
            :isBorder="true"
            title="绩效排名"
            class="card-border card-9"
          >
            <template #titleRight>
              <div class="acard-title-2">
                <Select
                  v-model:value="jxvalue"
                  :showSearch="false"
                  class="acard-title-2-left"
                  :options="jxoptions"
                  size="small"
                  @change="jxoptionsChange"
                />
                <Select
                  v-model:value="jxValue2"
                  :showSearch="false"
                  :options="jxoptions2"
                  size="small"
                  @change="jxoptions2Change"
                />
              </div>
            </template>
            <div class="card-com">
              <div class="card-com-tit ">
                <img
                  class="card-com-tit-img"
                  src="./img/ss.png"
                > 各单位绩效排名TOP3
              </div>
              <div class="card-table">
                <div class="card-table-th">
                  <div class="card-table-td">
                    排名
                  </div>
                  <div class="card-table-td">
                    {{ jxName }}
                  </div>
                  <!-- <div class="card-table-td card-table-td3">
                    合同数（个）
                  </div> -->
                  <div class="card-table-td">
                    {{ jxName2 }}
                  </div>
                </div>
                <div class="card-table-content">
                  <div
                    v-for="(item, index) in jxDatas"
                    :key="item.name"
                    class="card-table-tr"
                  >
                    <div class="card-table-td">
                      <div
                        v-if="index == 0"
                        class="ztd-jp"
                      >
                        <img
                          class="ztd-jp-img"
                          src="./img/jp1.png"
                        >
                      </div>
                      <div
                        v-if="index == 1"
                        class="ztd-jp"
                      >
                        <img
                          class="ztd-jp-img"
                          src="./img/jp2.png"
                        >
                      </div>
                      <div
                        v-if="index == 2"
                        class="ztd-jp"
                      >
                        <img
                          class="ztd-jp-img"
                          src="./img/jp3.png"
                        >
                      </div>
                    </div>
                    <!-- <div class="card-table-td">
                      {{ item.target }}
                    </div> -->
                    <div
                      class="card-table-td card-table-td3"
                      :title="item.locationName"
                    >
                      {{ item.locationName }}
                    </div>
                    <div
                      class="card-table-td"
                      :title="item.figure"
                    >
                      {{ item.figure }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BasicCard>
        </ACol>
        <ACol :span="24">
          <BasicCard
            :isBorder="true"
            title="多基地大修准备及实施状态"
            class="card-border "
          >
            <OverhaulCalendar :safetyOptions="safetyOptions" />
          </BasicCard>
        </ACol>
      </ARow>
    </div>
  </Layout>
</template>

<style scoped lang="less">
.card-border {
  border: 1px solid var(--ant-border-color-base);
  padding: 10px 15px;
  margin: 0 !important;
}

.fc-license-message {
  display: none !important;
}

.page-com {
  padding: 20px 10px;
  // background-color: #040a46;
  // color: #f0f0f0;
  color: #333;
}

.rightAngle-card {
  border-radius: 0px;
  /* 你可以根据需要调整这个值 */
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 153, 255, 1);
  overflow: hidden;
}

.rounded-card {
  border-radius: 10px;
  background: inherit;
  background-color: rgba(255, 255, 255, 0);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: #0e515f;
  overflow: hidden;
}

.acard {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: 600;
    // color: #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    height: 60px;
    box-sizing: border-box;

    &-1 {
      // color: #333;
    }

    &-2 {
      // color: #333;
    }
  }

}

.card-com {
  padding: 10px 5px 10px 5px;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

}

.card-1 {
  height: 420px;

  .chart-box {
    height: 350px;
  }
}

.card-2 {
  height: 420px;

  .chart-box {
    width: 100%;
    height: 380px;
  }

  .card-table {
    height: 340px;
    overflow: hidden;

    .card-table-th {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      font-size: 12px;
      font-weight: 400;

      // color: #f0f0f0;
      .card-table-td {
        // width: 25%;
        flex: 1;
        text-align: center;
      }

      .card-table-td3 {
        width: 30%;
      }
    }

    .card-table-content {
      height: 300px;
      overflow: hidden;
      overflow-y: auto;

      .card-table-tr {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        font-style: normal;
        font-size: 10px;

        .card-table-td {
          // width: 25%;
          flex: 1;
          text-align: center;
          // 鼠标手
          cursor: pointer;

          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          padding: 0 5px;

          .ztd-red {
            margin: 0 auto;
            width: 40px;
            height: 20px;
            border-radius: 50%;
            background-color: red;
          }

          .ztd-yellow {
            margin: 0 auto;
            width: 40px;
            height: 20px;
            border-radius: 50%;
            background-color: yellow;
          }

          .ztd-green {
            margin: 0 auto;
            width: 40px;
            height: 20px;
            border-radius: 50%;
            background-color: green;
          }

        }

        .card-table-td3 {
          width: 30%;
        }
      }
    }
  }
}

.card-3 {
  height: 360px;

  .chart-box {
    width: 100%;
    height: 320px;
  }
}

.card-4 {
  margin-top: 16px !important;
  height: 340px;
  .chart-box {
    width: 100%;
    height: 300px;
  }
  // padding-top: 10px;
  .card-com {
    padding-top: 10px
  }

  .aprogress-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 0;
    font-size: 12px;

    .aprogress-title {
      width: 140px;
      text-align: right;
    }

    .aprogress-com {
      width: 100%;
      padding: 0 10px;
    }

  }
}

.card-5 {
  margin-top: 16px !important;
  height: 240px;

  .card-com {
    padding-top: 20px;
  }

  .aprogress-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 0;
    font-size: 12px;

    .aprogress-title {
      width: 110px;
      text-align: right;
    }

    .aprogress-com {
      width: 100%;
      padding: 0 10px;
    }

  }

}

.card-6 {
  height: 360px;

  .chart-box {
    width: 100%;
    height: 326px;
  }
}

.card-7 {
  height: 340px;
  margin-top: 16px !important;

  .chart-box {
    width: 100%;
    height: 280px;
  }
}

.card-8 {
  // margin-top: 16px;
  height: 240px;

  .card-com {
    padding-top: 20px;
  }

  .aprogress-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 0;
    font-size: 12px;

    .aprogress-title {
      width: 110px;
      text-align: right;
    }

    .aprogress-com {
      width: 100%;
      padding: 0 10px;
    }
  }

}

.card-9 {
  margin-top: 16px !important;
  height: 460px;

  .acard-title-2 {
    width: 50%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      margin-right: 10px;
    }
  }

  .card-com {
    padding: 10px 5px 10px 5px;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;

    .card-com-tit {
      font-size: 18px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 0 30px;

      .card-com-tit-img {
        margin-right: 20px;
        width: 40px;
        height: 40px;

      }
    }

    .card-table {

      overflow: hidden;

      .card-table-th {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        font-size: 12px;
        font-weight: 400;

        // color: #f0f0f0;
        .card-table-td {
          // width: 25%;
          flex: 1;
          text-align: center;
          // 鼠标手

        }

        .card-table-td3 {
          width: 30%;
        }
      }

      .card-table-content {
        height: 400px;
        overflow: hidden;
        overflow-y: auto;

        .card-table-tr {
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          font-style: normal;
          font-size: 10px;

          .card-table-td {
            // width: 25%;
            flex: 1;
            text-align: center;
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            padding: 0 5px;

            .ztd-jp {
              margin: 0 auto;
              width: 20px;
              height: 20px;

              .ztd-jp-img {
                width: 20px;
                height: 20px;
              }
            }

            // .ztd-red {
            //   margin: 0 auto;
            //   width: 40px;
            //   height: 40px;
            //   // border-radius: 50%;
            //   // background-color: red;
            //   background:url(./img/jp1.png) no-repeat cover ;
            // }

            .ztd-yellow {
              margin: 0 auto;
              width: 40px;
              height: 40px;
              // border-radius: 50%;
              // background-color: yellow;
              background: url(./img/jp2.png) no-repeat cover;
            }

            .ztd-green {
              margin: 0 auto;
              width: 40px;
              height: 20px;
              border-radius: 50%;
              background-color: green;
            }

          }

          .card-table-td3 {
            width: 30%;
          }
        }
      }
    }

  }

}
</style>
