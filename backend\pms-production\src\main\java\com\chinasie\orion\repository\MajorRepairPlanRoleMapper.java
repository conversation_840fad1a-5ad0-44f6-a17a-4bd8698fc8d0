package com.chinasie.orion.repository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.JobHighRiskStatisticDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanRole;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * MajorRepairPlanRole Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@Mapper
public interface MajorRepairPlanRoleMapper extends  OrionBaseMapper  <MajorRepairPlanRole> {


}

