<?xml version="1.0" encoding="UTF-8"?>
<!--suppress ALL -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.github.yulichang</groupId>
    <artifactId>mybatis-plus-join-adapter</artifactId>
    <version>1.4.13</version>
  </parent>
  <groupId>com.github.yulichang</groupId>
  <artifactId>mybatis-plus-join-adapter-base</artifactId>
  <version>1.4.13</version>
  <name>mybatis-plus-join-adapter-base</name>
  <description>An enhanced toolkit of Mybatis-Plus to simplify development.</description>
  <url>https://github.com/yulichang/mybatis-plus-join</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>mybatis-plus-join</id>
      <name>yulichang</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/yulichang/mybatis-plus-join.git</connection>
    <developerConnection>scm:git:https://github.com/yulichang/mybatis-plus-join.git</developerConnection>
    <url>https://github.com/yulichang/mybatis-plus-join</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-extension</artifactId>
      <version>${mpj.mybatis.plus.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.yulichang</groupId>
      <artifactId>mybatis-plus-join-adapter-jsqlparser</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.github.yulichang</groupId>
      <artifactId>mybatis-plus-join-adapter-jsqlparser-v46</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
