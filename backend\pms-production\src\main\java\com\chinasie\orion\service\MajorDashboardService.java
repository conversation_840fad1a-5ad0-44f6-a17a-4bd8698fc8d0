package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.major.BaseMaterialCountVO;
import com.chinasie.orion.domain.vo.major.JobDashBoardVO;
import com.chinasie.orion.domain.vo.major.MaterialDashboardVO;
import com.chinasie.orion.domain.vo.major.PersonDashboardVO;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/18:52
 * @description:
 */
public interface MajorDashboardService {


    /**
     *  作业维度 统计看板
     * @param repairRound
     * @return
     */
    JobDashBoardVO jobDashboardCount(String repairRound);

    PersonDashboardVO personDashboardCount(String repairRound) throws InterruptedException;

    MaterialDashboardVO materialDashboardCount(String repairRound) throws InterruptedException;
}
