package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * TrainManage Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
@TableName(value = "pmsx_train_manage")
@ApiModel(value = "TrainManageEntity对象", description = "培训管理")
@Data

public class TrainManage extends  ObjectEntity  implements Serializable{

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训key-来自于培训字典")
    @TableField(value = "train_key")
    private String trainKey;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 培训类型
     */
    @ApiModelProperty(value = "培训类型")
    @TableField(value = "type")
    private String type;

    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    @TableField(value = "name")
    private String name;

    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    @TableField(value = "is_check")
    private Boolean isCheck;

    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 拟完成时间
     */
    @ApiModelProperty(value = "拟完成时间")
    @TableField(value = "complete_date")
    private Date completeDate;

    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    @TableField(value = "lesson_hour")
    private BigDecimal lessonHour;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    @TableField(value = "content")
    private String content;


    //todo 到期时间  规则缺失需要确定
    @ApiModelProperty(value = "到期时间")
    @TableField(value = "expire_time")
    private Date expireTime;

    @ApiModelProperty(value = "有效期限（月）")
    @TableField(value = "expiration_month")
    private Integer expirationMonth;
}
