package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;
import java.util.List;

/**
 * ProjectDeclare Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
@ApiModel(value = "ProjectDeclareDTO对象", description = "项目申报")
@Data
public class ProjectDeclareDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @NotBlank(message = "项目编号不能为空")
    private String projectNumber;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    /**
     * 项目预估金额
     */
    @ApiModelProperty(value = "项目预估金额")
    private BigDecimal estimateAmt;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDeptId;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resUserId;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectType;

    /**
     * 项目子类型
     */
    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    /**
     * 项目申请理由
     */
    @ApiModelProperty(value = "项目申请理由")
    private String applyReason;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @NotNull(message = "项目结束时间不能为空!")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @NotNull(message = "项目开始时间不能为空!")
    private Date projectStartTime;

    /**
     * 项目申报申请单编码
     */
    @ApiModelProperty(value = "项目申报申请单编码")
    private String number;

    /**
     * 项目背景摘要
     */
    @ApiModelProperty(value = "项目背景摘要")
    private String projectBackground;

    /**
     * 项目目标摘要
     */
    @ApiModelProperty(value = "项目目标摘要")
    private String projectTarget;

    /**
     * 技术方案摘要
     */
    @ApiModelProperty(value = "技术方案摘要")
    private String technologyPlan;

    /**
     * 支持性材料
     */
    @ApiModelProperty(value = "技术方案摘要")
    List<FileInfoDTO> fileInfoDTOList;

    @ApiModelProperty(value = "申报搜索条件")
    private String name;

    @ApiModelProperty("综合计划列表 key 为id value为number")
    private List<RelevancyPlanDTO> planList;
}
