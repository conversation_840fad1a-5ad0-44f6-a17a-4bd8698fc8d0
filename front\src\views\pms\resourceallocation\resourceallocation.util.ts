import dayjs from 'dayjs';
import { openDrawer } from 'lyra-component-vue3';
import ResourceAllocationForm from './components/ResourceAllocationForm.vue';
import { ref, Ref, h } from 'vue';


//无参默认查询当前月及之后两个月的日期
export function getDaysBySection(x?: string, y?: string): Array<object> {
    const startDate = x ? dayjs(x) : '';
    const endDate = y ? dayjs(y) : '';

    const sectionDays = [];
    if (startDate && endDate) {
        // 迭代从开始时间到结束时间的每个月
        let currentXYDate = startDate;
        while (dayjs(currentXYDate).isBefore(endDate) || dayjs(currentXYDate).isSame(endDate, 'month')) {
            const month = dayjs(currentXYDate).format('M'); // 获取月份数字（不带前导零）
            const year = dayjs(currentXYDate).format('YYYY'); // 获取年份
            let daysInMonth = dayjs(currentXYDate).daysInMonth(); // 获取天数
            let resultObj = {
                month: month,
                year: year,
                days: daysInMonth,
                startCountDay: 1
            }
            //计算起始时间
            if (currentXYDate.format('YYYY-MM') == startDate.format('YYYY-MM')) {
                daysInMonth = daysInMonth - (dayjs(startDate).day() - 1);
                resultObj.days = daysInMonth;
                resultObj.startCountDay = dayjs(startDate).day() - 1;
            }

            if (currentXYDate.format('YYYY-MM') == endDate.format('YYYY-MM')) {
                daysInMonth = (dayjs(startDate).day());
                resultObj.days = daysInMonth;
                resultObj.startCountDay = 1;
            }

            sectionDays.push(resultObj);

            // 移动到下一个月
            currentXYDate = dayjs(currentXYDate).add(1, 'month');
        }
        return sectionDays;
    }
    const currentDate = dayjs();


    // 循环获取当前月份及之后两个月的月份和天数
    for (let i = 0; i < 3; i++) {
        const futureDate = currentDate.add(i, 'month');
        const month = futureDate.format('M'); // 获取月份名称
        const year = futureDate.format('YYYY');  // 获取年份
        const daysInMonth = futureDate.daysInMonth(); // 获取天数

        sectionDays.push({
            month: month,
            year: year,
            days: daysInMonth
        });
    }
    return sectionDays;
}

export function getColorByRepairCodeOrName(x?: string): string {

    return '';

}


export function generateRandomString(length?: number) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return 'UPDATA-'+result;
}


 export function fillChildrenWithNull(data) {
    if (Array.isArray(data)) {
        data.forEach(item => {
            if (Array.isArray(item.children) && item.children.length === 0) {
                item.children = null;
            } else if (item.children) {
                fillChildrenWithNull(item.children);
            }
        });
    }
    return data;  
}


export function openResourceAllocationForm(...args: any[]) {
    const [record, column, title,markBusinessId, cb] = args;
    console.log("record=>", record);
    console.log("column=>", column);
    const drawerRef: Ref = ref();
    openDrawer({
        title: title,
        width: 680,
        content() {
            return h(ResourceAllocationForm, {
                ref: drawerRef,
                record,
                column,
                markBusinessId: markBusinessId,
                // number: record.number,
                // userName: record.name,
                // repairRound: 'H208',
                // name: record.name,
                // costCenterCode: record.costCenterCode,
                // markBusinessId: null
            });
        },
        async onOk() {
            // await drawerRef.value.submit();
            const valueLet = await drawerRef.value.getValues();
            console.log("valueLet=>", valueLet);
            cb?.();
        },
    });
}
