<?xml version="1.0" encoding="UTF-8"?>

<!-- ======================================================================= -->
<!--                                                                         -->
<!--   Copyright (c) 2007-2010, The JASYPT team (http://www.jasypt.org)      -->
<!--                                                                         -->
<!--   Licensed under the Apache License, Version 2.0 (the "License");       -->
<!--   you may not use this file except in compliance with the License.      -->
<!--   You may obtain a copy of the License at                               -->
<!--                                                                         -->
<!--       http://www.apache.org/licenses/LICENSE-2.0                        -->
<!--                                                                         -->
<!--   Unless required by applicable law or agreed to in writing, software   -->
<!--   distributed under the License is distributed on an "AS IS" BASIS,     -->
<!--   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or       -->
<!--   implied. See the License for the specific language governing          -->
<!--   permissions and limitations under the License.                        -->
<!--                                                                         -->
<!-- ======================================================================= -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jasypt</groupId>
  <artifactId>jasypt</artifactId>
  <packaging>jar</packaging>
  <version>1.9.3</version>
  <name>JASYPT: Java Simplified Encryption</name>
  <url>http://www.jasypt.org</url>

  <description>Java library which enables encryption in java apps with minimum effort.</description>



  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>The JASYPT team</name>
    <url>http://www.jasypt.org</url>
  </organization>
  
  <scm>
    <url>scm:git:**************:jasypt/jasypt.git</url>
    <connection>scm:git:**************:jasypt/jasypt.git</connection>
    <developerConnection>scm:git:**************:jasypt/jasypt.git</developerConnection>
    <tag>jasypt-1.9.3</tag>
  </scm>
  
  <developers>
    <developer>
      <id>dfernandez</id>
      <name>Daniel Fernandez</name>
      <email>dfernandez AT users.sourceforge.net</email>
      <roles>
        <role>Project admin</role>
      </roles>
    </developer>
  </developers>
  
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
    </repository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>


  <build>

    <resources>
    
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    
      <resource>
          <directory>.</directory>
          <targetPath>META-INF</targetPath>
          <includes>
              <include>LICENSE.txt</include>
              <include>NOTICE.txt</include>
          </includes>
      </resource>
      
    </resources>
    
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
    </testResources>
    
    <plugins>
    
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>2.1</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.6.0</version>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
          <encoding>US-ASCII</encoding>
        </configuration>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <encoding>US-ASCII</encoding>
        </configuration>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <doclint>none</doclint>
          <show>protected</show>
          <noqualifier>java.lang</noqualifier>
          <excludePackageNames>org.jasypt.contrib.*</excludePackageNames>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>1.1</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>verify</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
       
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.2</version>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/lite.xml</descriptor>
          </descriptors>
        </configuration>
        <executions>
          <execution>
            <id>make-assembly-deps</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <localCheckout>true</localCheckout>
        </configuration>
      </plugin>


    </plugins>
    
  </build>



  <dependencies>
    
    <dependency>
      <groupId>com.ibm.icu</groupId>
      <artifactId>icu4j</artifactId>
      <version>3.4.4</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <version>2.4</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    
    <dependency>
      <groupId>bouncycastle</groupId>
      <artifactId>bcprov-jdk12</artifactId>
      <version>140</version>
      <scope>test</scope>
    </dependency>
    
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.1</version>
      <scope>test</scope>
    </dependency>
    
    
  </dependencies>

  
</project>
