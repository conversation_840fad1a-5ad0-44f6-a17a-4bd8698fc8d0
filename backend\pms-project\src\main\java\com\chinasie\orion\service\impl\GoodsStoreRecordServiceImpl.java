package com.chinasie.orion.service.impl;





import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.GoodsStoreRecordDTO;
import com.chinasie.orion.domain.entity.GoodsServiceStore;
import com.chinasie.orion.domain.entity.GoodsStoreRecord;
import com.chinasie.orion.domain.vo.GoodsServiceStoreVO;
import com.chinasie.orion.domain.vo.GoodsStoreRecordVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.GoodsStoreRecordMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.GoodsServiceStoreService;
import com.chinasie.orion.service.GoodsStoreRecordService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * GoodsStoreRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 10:06:03
 */
@Service
public class GoodsStoreRecordServiceImpl extends OrionBaseServiceImpl<GoodsStoreRecordMapper, GoodsStoreRecord> implements GoodsStoreRecordService {

    @Resource
    private GoodsStoreRecordMapper goodsStoreRecordMapper;

    @Resource
    private GoodsServiceStoreService goodsServiceStoreService;

    @Resource
    private UserRedisHelper userRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public GoodsStoreRecordVO detail(String id) throws Exception {
        GoodsStoreRecord goodsStoreRecord =goodsStoreRecordMapper.selectById(id);
        GoodsStoreRecordVO result = BeanCopyUtils.convertTo(goodsStoreRecord,GoodsStoreRecordVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param goodsStoreRecordDTO
     */
    @Override
    public  GoodsStoreRecordVO create(GoodsStoreRecordDTO goodsStoreRecordDTO) throws Exception {
        GoodsStoreRecord goodsStoreRecord = BeanCopyUtils.convertTo(goodsStoreRecordDTO,GoodsStoreRecord::new);
        int insert = goodsStoreRecordMapper.insert(goodsStoreRecord);
        GoodsStoreRecordVO rsp = BeanCopyUtils.convertTo(goodsStoreRecord,GoodsStoreRecordVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param goodsStoreRecordDTO
     */
    @Override
    public Boolean edit(GoodsStoreRecordDTO goodsStoreRecordDTO) throws Exception {
        GoodsStoreRecord goodsStoreRecord =BeanCopyUtils.convertTo(goodsStoreRecordDTO,GoodsStoreRecord::new);
        int update =  goodsStoreRecordMapper.updateById(goodsStoreRecord);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = goodsStoreRecordMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public PageResult<GoodsStoreRecordVO> getGoodsStoreRecordPage(PageRequest<GoodsStoreRecordVO> pageRequest) throws Exception {
        LambdaQueryWrapperX<GoodsStoreRecord> pageCondition = new LambdaQueryWrapperX<>();
        GoodsStoreRecordVO query = pageRequest.getQuery();
        String goodsServiceStoreId = query.getGoodsServiceStoreId();
        if (StringUtils.hasText(goodsServiceStoreId)) {
            pageCondition.eq(GoodsStoreRecord::getGoodsServiceStoreId, goodsServiceStoreId);
        }
        pageCondition.orderByDesc(GoodsStoreRecord::getCreateTime);
        Page<GoodsStoreRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<GoodsStoreRecord> pageResult = page(realPageRequest, pageCondition);
        List<GoodsStoreRecord> records = pageResult.getRecords();
        HashMap<String, UserVO> userNameMap = this.getUserName();
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<GoodsStoreRecordVO> goodsStoreRecordVOS = BeanCopyUtils.convertListTo(records, GoodsStoreRecordVO::new);
        GoodsServiceStoreVO detail = goodsServiceStoreService.detail(goodsServiceStoreId);
        goodsStoreRecordVOS.forEach(e->{
            e.setGoodsServiceStoreVO(detail);
            e.setCreatorName(userNameMap.getOrDefault(e.getCreatorId(),new UserVO()).getName());
        });
        return new PageResult<>(goodsStoreRecordVOS, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
    }

    /**
     * 获取用户信息Map
     * @return 用户信息
     */
    private HashMap<String, UserVO> getUserName() {
        List<UserVO> users = userRedisHelper.getAllUser();
        HashMap<String, UserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)){
            Map<String, UserVO> collect = users.stream().collect(Collectors.toMap(UserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        return userMap;
    }

}
