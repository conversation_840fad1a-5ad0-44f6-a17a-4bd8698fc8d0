<template>
  <div class="process-table">
    <div class="top-form">
      <div class="process-title">
        <span>{{ planListTitle }}</span>
        <span
          v-if="isShow"
          class="action-btn"
          @click="editForm"
        >{{ formType===1?'修改申请表':'修改调整表' }}</span>
      </div>
      <div class="process-form">
        <a-row
          :gutter="[20, 20]"
          class="information-row"
        >
          <template
            v-for="item in filedList?.topField"
            :key="item.field"
          >
            <ACol
              :span="item.span"
              class="task-item"
            >
              <div class="item-title">
                {{ item.label }}：
              </div>
              <div
                class="item-value flex-te"
              >
                {{
                  item.formatter
                    ? item.formatter(formData[item.field])
                    : formData[item.field]
                }}
              </div>
            </ACol>
          </template>
        </a-row>
      </div>
    </div>
    <div class="information-content">
      <div class="process-title1">
        <span>分月计划</span>
        <span>单位：万元</span>
      </div>
      <div class="table-content">
        <div class="table-list">
          <div class="item-tr">
            <div class="item-th-value">
              <template
                v-for="(item,index) in formData.monthInvestmentSchemes"
                :key="index"
              >
                <div class="item-th">
                  {{ item.name }}
                </div>
              </template>
            </div>
          </div>
          <div class="item-tr">
            <div class="item-td-value">
              <template
                v-for="(item,index) in formData.monthInvestmentSchemes"
                :key="index"
              >
                <div class="item-td">
                  {{ formatMoney(item.predicate) }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-form">
      <div class="process-title1">
        {{ formData.yearName }}年形象进度
      </div>
      <div class="process-form">
        <a-row
          :gutter="[20, 20]"
          class="information-row"
        >
          <template
            v-for="item in filedList?.bottomField"
            :key="item.field"
          >
            <ACol
              :span="item.span"
              class="task-item"
            >
              <div class="item-title">
                {{ item.label }}：
              </div>
              <div
                class="item-value flex-te"
              >
                {{
                  item.formatter
                    ? item.formatter(formData[item.field])
                    : formData[item.field]
                }}
              </div>
            </ACol>
          </template>
        </a-row>
      </div>
    </div>
  </div>
  <!--      年度调整-->
  <AnnualInvestment
    @register="registerAnnual"
    @update="update"
  />
  <!--      投资计划-->
  <AddInvestmentPlan
    @register="registerPlan"
    @update="update"
  />
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, ref, toRefs, computed,
} from 'vue';
import { Col, Row } from 'ant-design-vue';
import { useDrawer, isPower } from 'lyra-component-vue3';
import { getYearPlanDetails } from '../../investmentPlan/index';
import { processInitForm, formatMoney } from '../index';
import AnnualInvestment from '../../investmentPlan/components/AnnualInvestment.vue';
import AddInvestmentPlan from '../../investmentPlan/components/AddInvestmentPlan.vue';
import { useUserStore } from '/@/store/modules/user';
export default defineComponent({
  components: {
    ARow: Row,
    ACol: Col,
    AnnualInvestment,
    AddInvestmentPlan,
  },
  props: {
    bpmnModuleData: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['update', 'showEdit'],
  setup(props, { emit }) {
    const userStore = useUserStore();
    // const menuActionItem = props.bpmnModuleData.menuActionItem || {};
    // let currentTasks = Array.isArray(menuActionItem?.currentTasks) && menuActionItem?.currentTasks.length > 0 ? props.bpmnModuleData.menuActionItem.currentTasks[0] : { assigneesUser: [] };
    // let showUse = currentTasks.applicant ? currentTasks.assignee === userStore.getUserInfo.id : currentTasks.assigneesUser.some((item) => item.id === userStore.getUserInfo.id);
    const showEditBtn = menuActionItem.status !== 'COMPLETED' && showUse;
    const [registerAnnual, { openDrawer: openDrawerAnnual }] = useDrawer();
    const [registerPlan, { openDrawer: openDrawerPlan }] = useDrawer();
    const state = reactive({
      formData: {},
      filedList: {},
      planListTitle: '',
      formType: 1,
      isShow: computed(() => isPower('TZJH_container_button_16', powerData) && showEditBtn),
    });
    onMounted(() => {
      getFormData();
    });

    const powerData = ref([]);
    function getFormData() {
      getYearPlanDetails(props.bpmnModuleData.menuActionItem.deliveries[0].deliveryId, 'PMS1002').then((res) => {
        let formatterFields = [
          'overallBudget',
          'overallReality',
          'projectAmount',
          'contractAmount',
        ];
        formatterFields.forEach((item) => {
          if (res[item] && Number(res[item])) {
            res[item] = Number((Number(res[item]) / 10000).toFixed(6));
          }
        });
        powerData.value = res?.detailAuthList ?? [];
        if (res.dataStatus.name.indexOf('调整') < 0) {
          state.planListTitle = '年度投资计划申请表';
          state.filedList = processInitForm(res.yearName, 1);
          state.formType = 1;
        } else {
          state.planListTitle = '年度投资计划调整申请';
          state.filedList = processInitForm(res.yearName, 2);
          state.formType = 2;
        }
        state.formData = res;
        emit('showEdit', {
          id: res.id,
          showEditBtn,
        });
      }).catch((err) => {
      });
    }
    function update() {
      getFormData();
      emit('update', state.formData.id);
    }
    function editForm() {
      if (state.formType === 1) {
        openDrawerPlan(true, {
          type: 'edit',
          id: state.formData.id,
        });
      } else {
        openDrawerAnnual(true, {
          type: 'edit',
          id: state.formData.id,
        });
      }
    }
    return {
      ...toRefs(state),
      formatMoney,
      registerAnnual,
      registerPlan,
      update,
      editForm,
      powerData,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
.process-table{
  height: 100%;
  overflow: hidden;
  width: 100%;
  margin-top:16px;
  .process-title{
    font-weight: 700;
    line-height: 1;
    font-size: 14px;
    padding-left: 15px;
    position: relative;
    display: flex;
    justify-content: space-between;
    &:before{
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      background: var(--ant-primary-color);
      width: 4px;
      height: 16px;
    }
    span+span{
      font-weight: 400;
    }
  }
  .process-title1{
    font-weight: 700;
    line-height: 1;
    font-size: 14px;
    padding-left: 15px;
    position: relative;
    display: flex;
    justify-content: space-between;
    span+span{
      font-weight: 400;
    }
  }
  .process-form{
    padding: 20px;
    width: 100%;
    .task-item {
      display: flex;
      line-height: 30px;
      min-height: 30px;
      padding: 5px 10px !important;

      .item-title {
        width: 200px;
        text-align: right;
      }
      .item-value {
        flex: 1;
      }
      .item-value-row{
        min-height: 80px;
      }
    }
  }
  .table-content{
    width: 100%;
    padding: 20px;
    .table-list{
      overflow: auto;
      width: 100%;
      border: 1px solid #cccccc;
      border-right: 0;
      border-bottom: 0;
      .item-tr{
        display: flex;
        width: 100%;
        .item-th{
          background: #f2f2f2;
          text-align: right;
          padding: 5px;
        }
        .item-td{
          padding: 5px;
        }
        .item-th,.item-td{
          border-bottom:1px solid #cccccc;
          border-right:1px solid #cccccc;
        }
        .item-th1,.item-td1{
          width: 100px;
        }
        .item-td-value{
          width:100%;
          display: flex;
          .item-td{
            flex:1;
            min-width: 150px;
            text-align: right;
          }
        }
        .item-th-value{
          width:100%;
          display: flex;
          .item-th{
            flex:1;
            min-width: 150px;
          }
        }
      }
      .item-tr+.item-tr{
        border-top:0
      }
    }
  }
}
</style>
