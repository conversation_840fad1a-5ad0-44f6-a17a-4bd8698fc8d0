<template>
  <BasicDrawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <a-tabs style="margin-top: -20px">
      <a-tab-pane tab="概述" />
    </a-tabs>
    <!--    <BasicTitle title="预览图">-->
    <!--      <div class="img-box">-->
    <!--        <img-->
    <!--          :src="father.form.planImage"-->
    <!--          alt=""-->
    <!--        >-->
    <!--      </div>-->
    <!--    </BasicTitle>-->
    <BasicTitle title="基本信息">
      <a-form
        :label-col="{ span: 7 }"
        :wrapper-col="{ span: 17 }"
      >
        <a-form-item label="编号">
          {{ father.form.number }}
        </a-form-item>
        <a-form-item label="名称">
          {{ father.form.name }}
        </a-form-item>
        <a-form-item label="风险类型">
          {{ father.form.riskTypeName }}
        </a-form-item>
        <a-form-item label="发生概率">
          {{ father.form.riskProbabilityName }}
        </a-form-item>
        <a-form-item label="影响程度">
          {{ father.form.riskInfluenceName }}
        </a-form-item>
        <a-form-item label="识别人">
          {{ father.form.discernPersonName }}
        </a-form-item>
        <a-form-item label="预估发生时间">
          {{ father.form.predictStartTimeName }}
        </a-form-item>
        <a-form-item label="期望完成日期">
          {{ formatDate(father.form.predictEndTime) }}
        </a-form-item>
        <a-form-item label="风险描述">
          {{ father.form.remark }}
        </a-form-item>
        <a-form-item label="负责人">
          {{ father.form.principalName }}
        </a-form-item>
        <a-form-item label="应对策略">
          {{ father.form.copingStrategyName }}
        </a-form-item>
        <a-form-item label="应对措施">
          {{ father.form.solutions }}
        </a-form-item>
        <a-form-item label="修改人">
          {{ father.form.modifyName }}
        </a-form-item>
        <a-form-item label="修改时间">
          {{ father.form.modifyTime }}
        </a-form-item>
        <a-form-item label="创建人">
          {{ father.form.creatorName }}
        </a-form-item>
        <a-form-item label="创建时间">
          {{ father.form.createTime }}
        </a-form-item>
      </a-form>
    </BasicTitle>
  </BasicDrawer>
</template>

<script>
import { computed, reactive, toRefs } from 'vue';
import { Drawer, Tabs, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import { formatDate } from '/@/views/pms/projectLaborer/utils';
// import BasicDrawer from "/@/components/Drawer/src/BasicDrawer.vue";
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default {
  name: 'ViewDetails',
  components: {
    BasicDrawer,
    BasicTitle,
    ATabs: Tabs,
    AForm: Form,
    AFormItem: Form.Item,
    ATabPane: Tabs.TabPane,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 55px)',
      },
      father: computed({
        get() {
          return props.data;
        },
      }),
    });

    return {
      ...toRefs(state),
      formatDate,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.ant-form-item) {
    margin-bottom: 10px;
    .ant-form-item-label {
      text-align: left;
      > label::after {
        content: '';
      }
    }
  }

  .img-box {
    height: 200px;
    margin-bottom: 10px;

    > img {
      height: 100%;
      width: 100%;
    }
  }
</style>
