package com.chinasie.orion.domain.dto.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/10
 * @description:
 */
@Data
public class ProjectApprovalEstimateInterOutTrialFeeCreateDTO {

    @NotEmpty(message = "未选择内外部试验数据")
    @ApiModelProperty(value = "内外部试验数据")
    private List<ProjectApprovalEstimateInterOutTrialFeeDTO> projectApprovalEstimateInterOutTrialFeeDTOList;

    @NotBlank(message = "类型不能为空")
    @ApiModelProperty(value = "类型")
    private String type;

    @NotBlank(message = "立项论证id不能为空")
    @ApiModelProperty(value = "立项论证id")
    private String projectApprovalId;
}
