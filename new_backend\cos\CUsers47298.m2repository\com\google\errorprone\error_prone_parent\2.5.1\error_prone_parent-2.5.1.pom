<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright 2011 The Error Prone Authors.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <name>Error Prone parent POM</name>
  <groupId>com.google.errorprone</groupId>
  <artifactId>error_prone_parent</artifactId>
  <version>2.5.1</version>
  <packaging>pom</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <guava.version>30.1-jre</guava.version>
    <gwt.version>2.8.2</gwt.version>
    <truth.version>1.1</truth.version>
    <javac.version>9+181-r4173-1</javac.version>
    <autoservice.version>1.0-rc6</autoservice.version>
    <autovalue.version>1.7</autovalue.version>
    <junit.version>4.13.1</junit.version>
    <dataflow.version>3.7.1</dataflow.version>
    <mockito.version>2.25.0</mockito.version>
    <compile.testing.version>0.18</compile.testing.version>
    <caffeine.version>2.8.8</caffeine.version>
    <flogger.version>0.5.1</flogger.version>
  </properties>

  <modules>
    <module>check_api</module>
    <module>test_helpers</module>
    <module>core</module>
    <module>annotation</module>
    <module>annotations</module>
    <module>type_annotations</module>
    <module>docgen</module>
    <module>docgen_processor</module>
    <module>refaster</module>
  </modules>

  <scm>
    <connection>scm:git:https://github.com/google/error-prone.git</connection>
    <developerConnection>scm:git:**************:google/error-prone.git</developerConnection>
    <url>https://github.com/google/error-prone</url>
  </scm>

  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.properties</include>
          <include>**/*.binarypb</include>
        </includes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/java</directory>
        <includes>
          <include>**/testdata/**</include>
        </includes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <dependencyReducedPomLocation>${project.build.directory}/dependency-reduced-pom.xml</dependencyReducedPomLocation>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M3</version>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0.5</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.5</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.1.1</version>
        <configuration>
          <source>8</source>
          <notimestamp>true</notimestamp>
          <doctitle>Error Prone ${project.version} API</doctitle>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <parameters />
          <testExcludes>
            <exclude>**/testdata/**</exclude>
          </testExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.7.1</version>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.19.1</version>
        <configuration>
          <argLine>-Xmx1g --illegal-access=permit</argLine>
          <trimStackTrace>false</trimStackTrace>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>java-8</id>
      <activation>
        <jdk>1.8</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>2.19.1</version>
            <configuration>
              <forkCount>4</forkCount>
              <reuseForks>true</reuseForks>
                <!-- put javac.jar on bootclasspath when executing tests -->
              <argLine>-Xmx1g -Xbootclasspath/p:${settings.localRepository}/com/google/errorprone/javac/${javac.version}/javac-${javac.version}.jar</argLine>
              <trimStackTrace>false</trimStackTrace>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
