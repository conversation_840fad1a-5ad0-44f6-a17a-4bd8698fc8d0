package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;

import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MilestoneIncomeAllocation;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class MilestoneIncomeAllocationHeadMsgHandler implements MscBuildHandler<ContractMilestone> {
    @Override
    public SendMessageDTO buildMsc(ContractMilestone contractMilestone, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("contractName",contractMilestone.getContractName());
        messageMap.put("milestoneName",contractMilestone.getMilestoneName());

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pas/milestones-details?id="+contractMilestone.getId())
                .messageUrlName("详情")
                .recipientIdList(Collections.singletonList(contractMilestone.getTechRspUser()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId("MilestoneIncomeAllocation_"+contractMilestone.getId())
                .platformId(contractMilestone.getPlatformId())
                .orgId(contractMilestone.getOrgId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return ContractMilestoneNode.CONTRACT_MILESTONE_INCOME;
    }
}
