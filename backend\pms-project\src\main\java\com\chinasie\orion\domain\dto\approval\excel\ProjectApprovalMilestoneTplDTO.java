package com.chinasie.orion.domain.dto.approval.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateDateConverter;
import com.alibaba.excel.converters.localdatetime.LocalDateTimeDateConverter;
import com.chinasie.orion.excel.annotations.ExcelSelected;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectApprovalMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class ProjectApprovalMilestoneTplDTO implements Serializable{

    @ExcelProperty(value = "序号")
    private String order;
    @ExcelProperty(value = "计划名称*")
    private String name;
    @ExcelProperty(value = "计划类型*")
    @ExcelSelected(source = {"里程碑"}, firstRow = 1)
    private String schemeType;
    @ExcelProperty(value = "计划开始时间*", index = 3)
    private String beginTime;
    @ExcelProperty(value = "计划结束时间*", index = 4)
    private String endTime;
    @ExcelProperty(value = "计划描述")
    private String schemeDesc;
}

