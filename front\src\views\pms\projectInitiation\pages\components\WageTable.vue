<template>
  <BasicCard
    title="工资及劳务费编制"
  >
    <div class="materials-table">
      <OrionTable
        ref="tableRef"
        :options="tableObjOptions"
        @selection-change="selectionChange"
      >
        <template
          v-if="props.status===101"
          #toolbarLeft
        >
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_018']"
            type="primary"
            icon="add"
            @click="addTableNode"
          >
            添加人员
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_019']"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_020']"
            type="primary"
            @click="saveTableData"
          >
            保存
          </BasicButton>
        </template>
        <template
          #requiredNum="{record}"
        >
          <AInputNumber
            v-model:value="record.requiredNum"
            :min="0"
            :disabled="props.status!==101"
            @change="changeValue($event,record)"
          />
          <!--          <span>{{ formatMoney(record.amount) }}</span>-->
        </template>
        <template
          #peopleDays="{record}"
        >
          <AInputNumber
            v-model:value="record.peopleDays"
            :min="0"
            :disabled="props.status!==101"
            @change="changeValue($event,record)"
          />
          <!--          <span>{{ formatMoney(record.amount) }}</span>-->
        </template>
        <template #footer>
          <div
            class="footer"
          >
            <span class="footer-label">汇总</span>
            <div class="footer-sum">
              <span class="footer-sum-span">人天总数：</span>
              <span
                class="footer-sum-value"
                style="margin-right: 20px"
              >{{ peopleSum }}</span>
              <span class="footer-sum-span">工资费用（元）：</span>
              <span class="footer-sum-value">{{ formatMoney(totalSum) }}</span>
            </div>
          </div>
        </template>
      </OrionTable>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">

import {
  markRaw, onMounted, onUnmounted, ref, Ref, unref, watch,
} from 'vue';
import {
  BasicCard, BasicButton, OrionTable, openSelectUserModal,
} from 'lyra-component-vue3';
import { message, Modal, InputNumber as AInputNumber } from 'ant-design-vue';
import dayjs from 'dayjs';
import { formatMoney } from '../../index';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string,
    status:number
}>(), {
  formId: '',
  status: 101,
});
const selectRowKeys:Ref<string[]> = ref([]);

function selectionChange({ keys }) {
  selectRowKeys.value = keys;
}
const totalSum:Ref<number> = ref(0);
const peopleSum:Ref<number> = ref(0);
const tableRef = ref();
const tableObjOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: true,
  expandIconColumnIndex: 3,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  api: async () => {
    let params = {
      projectApprovalId: props.formId,
    };
    let tableData = await new Api('/pms').fetch(params, 'projectApprovalEstimate/labor/editAmount', 'GET');
    totalSum.value = tableData.laborFee;
    peopleSum.value = tableData.peopleNum;
    return tableData.projectApprovalEstimateLaborFeeVOList;
  },
  columns: [
    {
      title: '员工姓名',
      dataIndex: 'name',
      minWidth: 200,
    },
    {
      title: '岗位',
      dataIndex: 'jobName',
      width: 200,
    },
    {
      title: '职级',
      dataIndex: 'jobPositionName',
      ellipsis: true,
      width: 100,
    },
    {
      title: '人天费用',
      dataIndex: 'peopleDayFee',
      width: 100,
    },
    {
      title: '人员需求数量',
      dataIndex: 'requiredNum',
      slots: { customRender: 'requiredNum' },
      width: 150,
    },
    {
      title: '周期（人天）',
      dataIndex: 'peopleDays',
      width: 150,
      slots: { customRender: 'peopleDays' },
    },
    {
      title: '工资费用',
      dataIndex: 'laborFee',
      customRender({ text }) {
        return text ? formatMoney(text) : '';
      },
    },
  ],
});

let initDataTimeOut = null;
function changeValue(event, record) {
  if (initDataTimeOut) {
    clearTimeout(initDataTimeOut);
  }
  initDataTimeOut = setTimeout(() => {
    record.laborFee = (!isNaN(record.peopleDayFee) ? record.peopleDayFee : 0) * (!isNaN(record.requiredNum) ? record.requiredNum : 0) * (!isNaN(record.peopleDays) ? record.peopleDays : 0);
    let dataSource = tableRef.value.getDataSource();
    totalSum.value = dataSource.reduce((acc, item1) => {
      let amount = !isNaN(item1.laborFee) ? item1.laborFee : 0;
      return acc + amount;
    }, 0);
    peopleSum.value = dataSource.reduce((acc, item1) => {
      let amount = (!isNaN(item1.requiredNum) ? item1.requiredNum : 0) * (!isNaN(item1.peopleDays) ? item1.peopleDays : 0);
      return acc + amount;
    }, 0);
  }, 300);
}

function addTableNode() {
  let dataSource = tableRef.value.getDataSource().map((item) => ({
    name: item.name,
    id: item.toId,
  }));
  openSelectUserModal(dataSource, {
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      let params = data.map((item) => ({
        name: item.name,
        id: item.id,
        peopleDayFee: item.peopleDayFee,
      }));
      new Api('/pms').fetch(params, `projectApprovalEstimate/labor/add/batch?projectApprovalId=${props.formId}`, 'POST').then(() => {
        message.success('添加人员成功');
        tableRef.value.reload();
      });
    },
  });
}

function saveTableData() {
  let params = tableRef.value.getDataSource().map((item) => ({
    id: item.id,
    requiredNum: item.requiredNum,
    peopleDays: item.peopleDays,
    projectApprovalId: props.formId,
    laborFee: item.laborFee,
  }));
  new Api('/pms').fetch(params, 'projectApprovalEstimate/labor/editAmount', 'PUT').then((res) => {
    message.success('保存成功');
    tableRef.value.reload();
  });
}
function deleteBatch() {
  Modal.confirm({
    title: '移除提示',
    content: '是否移除选中的人员',
    onOk() {
      new Api('/pms').fetch(selectRowKeys.value, 'projectApprovalEstimate/labor/remove', 'DELETE').then((res) => {
        message.success('移除成功。');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}
</script>
<style lang="less" scoped>
:deep(.basic-card-wrap){
  border-bottom: 0;
}
.materials-table{
  height: 450px;
  overflow: hidden;
}
:deep(.ant-basic-table){
  padding-right: 0 !important;
  padding-left: 0 !important;
}
:deep(.surely-table-body){
  height: 286px !important;
}
</style>