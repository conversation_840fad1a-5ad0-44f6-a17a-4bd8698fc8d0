package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * InvestmentSchemeEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:33:06
 */
@ApiModel(value = "InvestmentSchemeEstimateDTO对象", description = "概算")
@Data
public class InvestmentSchemeEstimateDTO extends ObjectDTO implements Serializable {

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用")
    private BigDecimal other = new BigDecimal("0");

    /**
     * 概算
     */
    @ApiModelProperty(value = "概算")
    private BigDecimal estimate = new BigDecimal("0");

    /**
     * 设备投资
     */
    @ApiModelProperty(value = "设备投资")
    private BigDecimal device = new BigDecimal("0");

    /**
     * 安装工程
     */
    @ApiModelProperty(value = "安装工程")
    private BigDecimal installation = new BigDecimal("0");

    /**
     * 建筑工程
     */
    @ApiModelProperty(value = "建筑工程")
    private BigDecimal architecture = new BigDecimal("0");

    /**
     * 估算/概算版本
     */
    @ApiModelProperty(value = "估算/概算版本")
    private String source;


    /**
     * 投资计划Id
     */
    @ApiModelProperty(value = "投资计划Id")
    private String investmentSchemeId;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 预备费
     */
    @ApiModelProperty(value = "预备费")
    private BigDecimal estimateReserve = new BigDecimal("0");

}
