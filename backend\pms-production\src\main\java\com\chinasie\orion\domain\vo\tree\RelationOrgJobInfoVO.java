package com.chinasie.orion.domain.vo.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/18/16:32
 * @description:
 */
@Data
public class RelationOrgJobInfoVO implements Serializable {
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "大修组织ID")
    private String repairOrgId;
    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "关系ID")
    private String  relationId;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;
    @ApiModelProperty(value = "是否重大项目")
    private Boolean  isMajorProject;
    @ApiModelProperty(value = "计划结束时间")
    private Date beginTime;
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;
    @ApiModelProperty(value = "工期")
    private Integer workDuration;
    private String  firstExecute;
    private String  firstExecuteName;
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;
    @ApiModelProperty(value = "高风险等级")
    private String heightRiskLevelName;
    private String antiForfeignLevel;
    @ApiModelProperty(value = "防异物等级名称")
    private String antiForfeignLevelName;
    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;

    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;

    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;

    @ApiModelProperty(value = "管理人员Id")
    private String managePersonId;

    @ApiModelProperty(value = "管理人员工号")
    private String managePersonCode;

    @ApiModelProperty(value = "管理人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作包审查状态")
    private Integer workPackageStatus;

}
