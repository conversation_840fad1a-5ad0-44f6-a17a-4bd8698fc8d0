<script setup lang="ts">
import {
  OrionTable, BasicButton, downloadByData, Layout2, isPower, Layout,
} from 'lyra-component-vue3';
import {
  computed, h, ref, Ref,
} from 'vue';
import {
  Modal,
  Space as ASpace,
  DatePicker as ADatePicker, Input, message,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import Api from '/@/api';
import { isBoolean } from 'lodash-es';

// 拿到表格
const tableRef: Ref = ref();
const powerData = ref();

// 标签起始数据
const tabsIndex = ref(0);

// 年份筛选器
const loading: Ref<boolean> = ref(false);
const currentYear = ref<Dayjs>(dayjs(new Date()));

// 标签内的文字
const pageMenu = computed(() => [
  {
    name: '基本信息',
    id: 0,
    code: 'PMS_JSPZRYGL_container_01',
  },
  {
    name: '数据分析',
    id: 1,
    code: 'PMS_JSPZRYGL_container_02',
  },
].filter((item) => isPower(item.code, powerData.value)));
// 基本信息导出全部初始变量
const basicExportConditions = ref(null);
// 统计分析导出全部初始变量
const detailExportConditions = ref(null);

// 基本信息数据
const tableOptions1 = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['fullName', 'userCode'],
  isFilter2: true,
  rowSelection: {},
  filterConfig: {
    fields: [
      {
        field: 'fullName',
        fieldName: '姓名',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'userCode',
        fieldName: '工号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'userStatus',
        fieldName: '人员状态',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  columns: [
    {
      title: '人员状态',
      dataIndex: 'userStatus',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 100,
    },
    {
      title: '身份证号码',
      dataIndex: 'idCard',
      width: 180,
    },
    {
      title: '工号',
      dataIndex: 'userCode', // 缺
      width: 100,
    },
    {
      title: '出生日期',
      dataIndex: 'dateOfBirth',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '民族',
      dataIndex: 'nation',
      width: 100,
    },
    {
      title: '婚姻状况',
      dataIndex: 'maritalStatus',
      width: 100,
    },
    {
      title: '联系方式',
      dataIndex: 'contactInformation',
      width: 130,
    },
    {
      title: '工作年限',
      dataIndex: 'workYear',
      width: 100,
    },
    {
      title: '最高学历',
      dataIndex: 'highestEducation',
      width: 100,
    },
    {
      title: '所学专业',
      dataIndex: 'major',
      width: 170,
    },
    {
      title: '职称',
      dataIndex: 'title',
      width: 100,
    },
    {
      title: '专业技术证书',
      dataIndex: 'professionalTechnicalCertificate',
      width: 150,
    },
    {
      title: '所在公司',
      dataIndex: 'companyName',
      width: 220,
    },
    {
      title: '所在部门/中心',
      dataIndex: 'deptName',
      width: 150,
    },
    {
      title: '所在研究所/专业家',
      dataIndex: 'instituteName',
      width: 200,
    },
    {
      title: '分管项目经理',
      dataIndex: 'departmentHeadProjectManager',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '所属供应商',
      dataIndex: 'affiliatedSupplier',
      width: 220,
    },
    {
      title: '是否项目制人员',
      dataIndex: 'projectBasedStaff',
      width: 150,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
      width: 200,
    },
    {
      title: '合同级别',
      dataIndex: 'contractLevel',
      width: 100,
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 380,
    },
    {
      title: '工作内容',
      dataIndex: 'jobContent',
      width: 380,
    },
    {
      title: '常驻服务地点',
      dataIndex: 'permanentServiceLocation',
      width: 150,
    },
    {
      title: '是否从事放射性工作',
      dataIndex: 'worksWithRadioactiveMaterials',
      width: 170,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '是否已完成体检',
      dataIndex: 'completedPhysicalExamination',
      width: 150,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '办卡或者授权',
      dataIndex: 'cardOrAuthorizationChoice',
      width: 150,
    },
    {
      title: '是否有亲属在集团内',
      dataIndex: 'hasRelativeInGroup',
      width: 160,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '亲属姓名',
      dataIndex: 'relativeName',
      width: 100,
    },
    {
      title: '亲属职务',
      dataIndex: 'relativePosition',
      width: 100,
    },
    {
      title: '亲属公司',
      dataIndex: 'relativeCompany',
      width: 220,
    },
    {
      title: '是否技术配置',
      dataIndex: 'technicalConfiguration',
      width: 110,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '预计离岗时间',
      dataIndex: 'expectedDepartureDate',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否违反相关安全规范',
      dataIndex: 'violatedSafetyRegulations',
      width: 180,
      customRender({ text }) {
        return isBoolean(text) ? text ? '是' : '否' : '';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 200,
    },
    {
      title: '入场时间',
      dataIndex: 'entryTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '离场时间',
      dataIndex: 'departureTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作人',
      dataIndex: 'operationName',
      width: 100,
    },
    {
      title: '锁定状态',
      dataIndex: 'lockedStatus',
      width: 100,
    },
  ],
  // 基本信息接口位置
  api: (params:Record<string, any>) => {
    basicExportConditions.value = params.searchConditions;
    return new Api('/spm/basic-user/getUserInfo').fetch({
      ...params,
    }, '', 'POST');
  },
};

// 数据分析数据
const tableOptions2 = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: ['indexName'],
  isFilter2: false,
  rowSelection: {},
  columns: [
    {
      title: '指标名称',
      dataIndex: 'indexName',
    },
    {
      title: '1月',
      dataIndex: 'jan',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.jan,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.jan = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                jan: record.jan,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '2月',
      dataIndex: 'feb',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.feb,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.feb = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                feb: record.feb,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '3月',
      dataIndex: 'mar',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.mar,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.mar = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                mar: record.mar,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '一季度',
      dataIndex: 'firstQuarter',
      customRender({ record }) {
        return convertPercentageFunction(record.id, record.jan, record.feb, record.mar);
      },
    },
    {
      title: '4月',
      dataIndex: 'apr',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.apr,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.apr = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                apr: record.apr,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '5月',
      dataIndex: 'may',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.may,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.may = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                may: record.may,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '6月',
      dataIndex: 'jun',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.jun,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.jun = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                jun: record.jun,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '二季度',
      dataIndex: 'secondQuarter',
      customRender({ record }) {
        return convertPercentageFunction(record.id, record.apr, record.may, record.jun);
      },
    },
    {
      title: '7月',
      dataIndex: 'jul',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.jul,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.jul = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                jul: record.jul,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '8月',
      dataIndex: 'aug',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.aug,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.aug = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                aug: record.aug,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '9月',
      dataIndex: 'sept',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.sept,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.sept = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                sept: record.sept,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '三季度',
      dataIndex: 'thirdQuarter',
      customRender({ record }) {
        return convertPercentageFunction(record.id, record.jul, record.aug, record.sept);
      },
    },
    {
      title: '10月',
      dataIndex: 'oct',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.oct,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.oct = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                oct: record.oct,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '11月',
      dataIndex: 'nov',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.nov,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.nov = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                nov: record.nov,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '12月',
      dataIndex: 'dece',
      customRender({ record }) {
        if (['demandCount', 'budgetRate'].includes(record?.projectGraphType)) {
          return h(Input, {
            value: record.dece,
            onChange(e) {
              if (!/^\d*%?$/.test(e.target.value)) {
                message.error('只能输入数字和百分号');
                e.target.value = '';
              }
              record.dece = e.target.value;
            },
            onBlur(e) {
              if (!e.target.value) {
                return;
              }
              const body = {
                id: record.id,
                dece: record.dece,
              };
              new Api('/spm/projectGraph').fetch(body, 'edit', 'PUT').then((res) => {
                tableRef.value?.reload?.();
              });
            },
          });
        }
      },
    },
    {
      title: '四季度',
      dataIndex: 'fourthQuarter',
      customRender({ record }) {
        return convertPercentageFunction(record.id, record.oct, record.nov, record.dece);
      },
    },
  ],
  // 明细表接口位置
  api: (params:Record<string, any>) => {
    detailExportConditions.value = params.searchConditions;
    return new Api('/spm/projectGraph/page').fetch({
      ...params,
      searchConditions: [
        [
          {
            field: 'indexName',
            fieldType: 'String',
            values: [params.searchConditions?.[0]?.[0]?.values?.[0] ?? ''],
            queryType: 'like',
          },
          {
            field: 'indexYear',
            fieldType: 'String',
            values: [`${currentYear.value.year()}`],
            queryType: 'eq',
          },
        ],
      ],
    }, '', 'POST');
  },
};

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 将百分数转换成小数
function convertPercentage(item:string) {
  // 去掉百分号并转换为数字
  let percentage = parseFloat(item.replace('%', ''));
  // 将百分比转换为小数
  let decimal = percentage / 100;
  // 显示结果
  return decimal;
}
const convertPercentageFunction = (id:string, jan:string, feb:string, mar:string) => {
  if (id === '1807737535334117376') {
    const firstQuarterSum = parseFloat(jan || '0') + parseFloat(feb || '0') + parseFloat(mar || '0');
    return firstQuarterSum.toFixed(2); // 保留两位小数
  } if (id === '1807737535535443968') {
    const firstQuarterSum = (convertPercentage(jan || '0') + convertPercentage(feb || '0') + convertPercentage(mar || '0')) / 3;
    return `${(firstQuarterSum * 100).toFixed(2)}%`; // 保留两位小数
  }
};

// 基本信息批量导出按钮事件（接口需加）
function exportTableDataBasic(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length === 0) {
        exportConditions = basicExportConditions.value;
      } else if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'userCode', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      }
      downloadByData('/spm/basic-user/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 数据分析批量导出按钮事件（接口需加）
function exportTableDataData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length === 0) {
        exportConditions = detailExportConditions.value;
      } else if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      }
      downloadByData('/spm/projectGraph/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 表格切换标签事件
const handleTabsChang = (index) => {
  tabsIndex.value = index;
};

// 年份筛选器切换处理事件
const handleYear = (val) => {
  tableRef.value.reload();
};
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};
</script>

<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    v-get-power="{pageCode: 'PMSTechnicalConfigurationList',getPowerDataHandle}"
    :tabs="pageMenu"
    :options="{ body: { scroll: true } }"
    @tabsChange="handleTabsChang"
  >
    <template v-if="tabsIndex===0">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions1"
        rowKey="userCode"
        :onSelectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="isPower('PMS_JSPZRYGL_container_01_button_01',powerData)"
            class="margin-right"
            type="primary"
            icon="sie-icon-daochu"
            @click="exportTableDataBasic(selectKeys)"
          >
            导出全部
          </BasicButton>
        </template>
      </OrionTable>
    </template>
    <template v-if="tabsIndex===1">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions2"
        :onSelectionChange="selectionChange"
      >
        <template #toolbarLeft>
          <BasicButton
            v-if="isPower('PMS_JSPZRYGL_container_02_button_01',powerData)"
            type="primary"
            icon="sie-icon-daochu"
            class="margin-right"
            @click="exportTableDataData(selectKeys)"
          >
            导出全部
          </BasicButton>
        </template>
        <template #toolbarRight>
          <a-space style="margin-left: 12px;">
            <a-date-picker
              v-model:value="currentYear"
              picker="year"
              @change="handleYear"
            />
          </a-space>
        </template>
      </OrionTable>
    </template>
  </Layout2>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
