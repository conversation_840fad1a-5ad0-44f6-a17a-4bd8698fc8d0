import { createGlobalState } from '@vueuse/shared';
import { effectScope, ref, watch } from 'vue';
import dayjs from 'dayjs';

export const useUpdateContractList = createGlobalState(() => {
  const refreshUpdateContractKey = ref('update');
  const currentFilterYear = ref(dayjs().format('YYYY'));
  const planListBodyParams = ref({});
  const updateContractKey = (val?:any) => {
    refreshUpdateContractKey.value = val || Math.round(Math.random() * 100000000).toString(32);
  };
  function setPlanListBodyParams(params:any) {
    planListBodyParams.value = params;
  }
  watch(() => [currentFilterYear.value], (val) => {
    updateContractKey();
  });

  return {
    refreshUpdateContractKey,
    updateContractKey,
    currentFilterYear,
    planListBodyParams,
    setPlanListBodyParams,
  };
});