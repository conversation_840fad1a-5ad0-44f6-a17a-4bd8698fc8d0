<script setup lang="ts">
import {
  computed, h, inject, ref,
} from 'vue';
import { Modal, Space } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  BasicButton, isPower, OrionTable, randomString,
} from 'lyra-component-vue3';
import { get } from 'lodash-es';
import { useAssetsModal } from '../hooks/useAssetsModal';
import AssetsCenterModal from '../hooks/components/AssetsCenterModal.vue';
import {
  useCreatedOrEditableForm,
} from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/AssetConsolidation/hooks/useAssetsDrawerForm';
import Api from '/@/api';

const detailsData = inject('detailsData');
const updateRefreshKey = inject('updateRefreshKey');
const powerData = inject('powerData');

const tableRef = ref();
const selectKeys = ref([]);
const dataSource = computed(() => get(detailsData, 'detailAssetsVOList' ?? []));
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  ...parseRowSelection({
    rowSelection: {
      onChange(val) {
        selectKeys.value = val;
      },
    },
  }),
  columns: computed(() => [
    {
      title: '资产编码/资产名称',
      dataIndex: 'asset',
      fixed: 'left',
      width: 400,
    },
    {
      title: '资产类型',
      dataIndex: 'generalLedgerSubject',
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '数量',
      dataIndex: 'requiredQuantity',
      width: 80,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      width: 120,
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
      width: 120,
    },
    {
      title: '项目编号/名称',
      dataIndex: 'projectIdName',
      width: 300,
    },
    {
      title: '总账科目',
      dataIndex: 'generalLedgerSubject',
      width: 130,
    },
    {
      title: '成本中心',
      dataIndex: 'costCenter',
      width: 160,
    },
    {
      title: '关联预算',
      dataIndex: 'localCurrencyAmount',
      width: 160,
    },
    ...parseColAction({
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    }),
  ]).value,
  actions: [
    {
      text: '编辑',
      isShow: (record) => [
        isPower('PMS_ZCZGXQ_container_01_02_button_05', powerData.value),
        false,
        [101].includes(detailsData.status),
      ].every(Boolean),
      onClick(record) {
      },
    },
    {
      text: '移除',
      isShow: (record) => [isPower('PMS_ZCZGXQ_container_01_02_button_04', powerData.value), [101].includes(detailsData.status)].every(Boolean),
      onClick(record) {
        Modal.confirm({
          title: '移除警告',
          icon: h(ExclamationCircleOutlined),
          content: '确定移除这条数据',
          async onOk() {
            try {
              const targetKey = [record.id];
              return await handleRemoveRequest(targetKey);
            } catch {
            }
          },
          onCancel() {},
        });
      },
    },
  ],
};

const showAddBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_02_button_01', powerData.value), [101].includes(detailsData.status)].every(Boolean));
const showAddAssetBtn = computed(() => [
  isPower('PMS_ZCZGXQ_container_01_02_button_02', powerData.value),
  [101].includes(detailsData.status),
  false,
].every(Boolean));
const showRemoveBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_02_button_03', powerData.value), [101].includes(detailsData.status)].every(Boolean));

function parseColAction(cfg) {
  if (detailsData.status === 101) {
    return [cfg];
  }
  return [];
}
function parseRowSelection(cfg) {
  if (detailsData.status === 101) {
    return cfg;
  }
  return {};
}
function updateTable() {
  updateRefreshKey.value = randomString(50);
}
function handleAddWbsModal() {
  useAssetsModal(AssetsCenterModal, {
    title: '添加资产',
    detailsData,
  }, updateTable);
}
function handleRemoveRows() {
  Modal.confirm({
    title: '移除警告',
    icon: h(ExclamationCircleOutlined),
    content: '确定移除选中的数据',
    async onOk() {
      try {
        const targetKeys = tableRef.value?.getSelectRowKeys();
        return await handleRemoveRequest(targetKeys);
      } catch {
      }
    },
    onCancel() {},
  });
}
function handleAddAssets() {
  useCreatedOrEditableForm({
    title: '创建资产',
  }, updateTable);
}
function handleRemoveRequest(idxs) {
  return new Promise((resolve) => {
    new Api('/pms/projectAssetApplyDetailAssets/remove')
      .fetch(idxs, '', 'DELETE')
      .then((res) => {
        updateTable();
        resolve(res);
      });
  });
}
</script>

<template>
  <div class="associated-assets">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :dataSource="dataSource"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <Space
          :size="6"
          align="center"
        >
          <BasicButton
            v-if="showAddBtn"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddWbsModal"
          >
            添加
          </BasicButton>
          <BasicButton
            v-if="showAddAssetBtn"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="handleAddAssets"
          >
            新增资产
          </BasicButton>
          <BasicButton
            v-if="showRemoveBtn"
            :disabled="!selectKeys.length"
            icon="sie-icon-shanchu"
            @click="handleRemoveRows"
          >
            移除
          </BasicButton>
        </Space>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.associated-assets{
  height: 500px;
  overflow: hidden;
  padding-bottom: 16px;
  :deep(.ant-basic-table){
    padding: 16px 0 0 0 !important;
  }
}
</style>