<script setup lang="ts">
import {
  computed,
  onMounted, provide, Ref,
  ref,
} from 'vue';
import { BasicCard } from 'lyra-component-vue3';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import RoleTable from './components/RoleTable.vue';

const route = useRoute();
const dataId = computed(() => route.query?.id);
const loading = ref(false);
const powerData: Ref = ref(null);
provide('powerData', powerData);
const detailData: Ref = ref(null);
provide('detailData', detailData);

onMounted(() => {
  getDetails();
});

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api(`/pms/personRoleMaintenance/${dataId.value}`).fetch({
      pageCode: 'RYJSWHXQ_001',
    }, '', 'GET');
    powerData.value = result?.detailAuthList;
    detailData.value = result;
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div
    v-loading="loading"
    class="major-repairs-detail"
  >
    <BasicCard title="审批角色信息：">
      <BasicInfo />
    </BasicCard>
    <BasicCard title="审核人员变更记录：">
      <RoleTable />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
.major-repairs-detail {
  padding: ~ ` getPrefixVar('content-margin-top') ` 14px;
  background: #fff;
}
</style>
