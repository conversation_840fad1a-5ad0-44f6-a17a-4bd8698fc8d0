<template>
  <Layout
    :options="{ body: { scroll: true } }"
    class="boxs"
  >
    <template #left>
      <BusinessTree
        ref="leftTree"
        :get-tree-api="getTreeApi"
        :delete-tree-api="deleteTreeApi"
        title-name="需求管理"
        :add-api="addApi"
        :edit-api="editApi"
        @select-node="selectChange"
        @init-data="initTreeData"
      />
    </template>
    <div class="table-content">
      <OrionTable
        v-if="selectChangeData?.id"
        :key="selectChangeData?.id"
        ref="tableRef"
        :custom-header-cell="customHeaderCell"
        :options="options"
        @init-data="initData"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="add"
            @click="addLove"
          >
            新增需求
          </BasicButton>
        </template>>
        <!--        <template #tableTitle>-->
        <!--          <div class="demandTableTitle flex-f1">-->
        <!--            <div CLASS="mr10 flex-f1">-->
        <!--              <BasicButton-->
        <!--                type="primary"-->
        <!--                icon="add"-->
        <!--                @click="addLove"-->
        <!--              >-->
        <!--                新增需求-->
        <!--              </BasicButton>-->
        <!--            </div>-->
        <!--            <div>-->
        <!--              <a-input-search-->
        <!--                v-model:value="searchValue"-->
        <!--                placeholder="请输入编号或名称"-->
        <!--                @search.enter="search"-->
        <!--              />-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </template>-->

        <!--        <template-->
        <!--          v-if="showSearchTitle"-->
        <!--          #toolbarLeft-->
        <!--        >-->
        <!--          <div class="tableSearchTitle">-->
        <!--            <span class="titleSpan">共{{ total }}条符合条件的查询结果：</span>-->
        <!--            <div class="tagList">-->
        <!--              <template v-for="(item,key) in tableSearchTitle">-->
        <!--                <ATag-->
        <!--                  v-if="item.value"-->
        <!--                  :closable="true"-->
        <!--                  @close="handleClose(key)"-->
        <!--                >-->
        <!--                  <span v-if="typeof item.value==='string'">{{ item.label }} 条件 : {{ item.value }}</span>-->
        <!--                  <span v-else>{{ item.label }} 条件 : {{ dayjs(item.value[0]).format('YYYY-MM-DD') }}~{{ dayjs(item.value[1]).format('YYYY-MM-DD') }}</span>-->
        <!--                </ATag>-->
        <!--              </template>-->
        <!--            </div>-->
        <!--            <span-->
        <!--              class="clearSearch"-->
        <!--              @click="clearSearch"-->
        <!--            >清空条件</span>-->
        <!--          </div>-->
        <!--        </template>-->

        <template #action="{record}">
          <BasicTableAction :actions="actionsBtn(record)" />
        </template>
      </OrionTable>
      <AEmpty v-else />
    </div>
    <AddTableNode
      :treeData="treeData"
      @register="registerAddNode"
      @update="addSuccess"
    />
    <CheckDetailDrawer @register="goDetailDrawer" />

    <!--搜索抽屉-->
    <SearchDrawer
      @register="SearchDrawerRegister"
      @search="searchForm"
    />
  </Layout>
</template>

<script lang="ts">
import {
  defineComponent, h, provide, reactive, toRefs, watch,
} from 'vue';
import {
  BasicButton,
  BasicTableAction,
  BusinessTree,
  DataStatusTag,
  ITableActionItem,
  Layout,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import {
  Empty, Input, message, Modal,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import CheckDetailDrawer from './model/CheckDetailDrawer.vue';
import SearchDrawer from './model/SearchDrawer/index.vue';
import AddTableNode from './model/AddTableNode.vue';

export default defineComponent({
  name: 'Index',
  components: {
    BasicTableAction,
    AEmpty: Empty,
    Layout,
    OrionTable,
    CheckDetailDrawer,
    // AInputSearch: Input.Search,
    SearchDrawer,
    AddTableNode,
    BusinessTree,
    BasicButton,
  },
  props: {},
  emits: [],
  setup() {
    const [registerAddNode, { openDrawer }] = useDrawer();
    const [goDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
    const [SearchDrawerRegister, searchDrawerMethods] = useDrawer();
    const state = reactive({
      selectChangeData: {},
      reloadAll: new Date(),
      searchValue: null,
      tableRef: null,
      total: 0,
      searchParams: {} as any,
      treeData: [],
      tableSearchTitle: {
        name: {
          label: '名称或编号',
        },
        principalId: {
          label: '负责人',
        },
        priorityLevel: {
          label: '优先级',
        },
        status: {
          label: '状态',
        },
        exhibitor: {
          label: '提出人',
        },
        proposedTime: {
          label: '提出日期',
        },
        predictEndTime: {
          label: '期望完成日期',
        },
      },
      showSearchTitle: false,
    });
    const state3 = reactive({
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: true,
        isFilter2: true,
        filterConfigName: 'PAS_BECURRENTMANAGE_DEMANDMANAGE',
        smallSearchField: ['name'],
        api: (P) => new Api('/pas/demand-management/getPage').fetch(P, '', 'POST'),
        columns: [
          {
            title: '标题',
            dataIndex: 'name',
            minWidth: 200,
            ellipsis: true,
            customRender({ record, text }) {
              return h('span', {
                onClick: () => {
                  router.push({
                    name: 'DemandManagementDetails',
                    query: {
                      folderId: state.selectChangeData?.id,
                      itemId: record.id,
                    },
                  });
                },
                class: 'action-btn',
              }, text);
            },
          },
          {
            title: '编号',
            dataIndex: 'number',
            width: 120,
          },
          {
            title: '路径',
            dataIndex: 'dirName',
            width: 120,
          },
          {
            title: '提出人',
            dataIndex: 'exhibitorName',
            width: 120,
          },
          {
            title: '提出日期',
            dataIndex: 'proposedTime',
            width: 180,
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
          },
          {
            title: '期望完成日期',
            width: 180,
            dataIndex: 'predictEndTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
          },
          {
            title: '优先级',
            dataIndex: 'priorityLevelName',
            width: 80,
          },
          {
            title: '进度',
            dataIndex: 'scheduleName',
            width: 80,
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            width: 120,
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            width: 120,
          },
          {
            title: '修改日期',
            ellipsis: true,
            dataIndex: 'modifyTime',
            width: 180,
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ],
        beforeFetch,
      },
    });
    function beforeFetch(T) {
      let params:any = {
        proposedTime: state.searchParams?.proposedTime,
        predictEndTime: state.searchParams?.predictEndTime,
        exhibitor: state.searchParams?.exhibitor,
        principalId: state.searchParams?.principalId,
        priorityLevel: state.searchParams?.priorityLevel,
        status: state.searchParams?.status,
      };
      if (Array.isArray(params.proposedTime) && params.proposedTime.length > 0) {
        params.proposedStartTime = params.proposedTime?.[0];
        params.proposedEndTime = params.proposedTime?.[1];
        delete params.proposedTime;
      }
      if (Array.isArray(params.predictEndTime) && params.predictEndTime.length > 0) {
        params.queryPredictStartTime = params.predictEndTime?.[0];
        params.queryPredictEndTime = params.predictEndTime?.[1];
        delete params.predictEndTime;
      }
      return {
        ...T,
        query: {
          ...params,
          dirId: state.selectChangeData?.id,
        },
        queryCondition: [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
        ],
      };
    }
    const state6 = reactive({
      btnList: [
        // { type: 'add' },
        { type: 'check' },
        { type: 'open' },
        { type: 'edit' },
        { type: 'delete' },
        // { type: 'oddTask' },
        // { type: 'evenTask' },
        { type: 'search' },
      ],
    });
    const router = useRouter();
    async function btnClick(type) {
      switch (type) {
        case 'check':
          if (oddJudge()) {
            openDetailDrawer(true, { data: oddJudge()[0] });
          }
          break;
        case 'open':
          if (oddJudge()) {
            router.push({
              name: 'DemandManagementDetails',
              query: {
                folderId: state.selectChangeData?.id,
                itemId: oddJudge()[0].id,
              },
            });
          }

          break;
        case 'edit':
          if (oddJudge()) {
            openDrawer(true, {
              type: 'edit',
              data: { id: oddJudge()[0].id },
            });
          }
          break;
        case 'delete':
          if (evenJudge()) {
            deleteFn();
          }
          break;
        case 'search':
          searchDrawerMethods.openDrawer(true, state.searchParams);
          break;
      }
    }
    async function deleteFn() {
      Modal.confirm({
        title: '删除提示',
        content: '您确认要删除这些数据吗？',
        async onOk() {
          return await new Api('/pas/demand-management/removeBatch').fetch(evenJudge().map((item) => item.id), '', 'DELETE').then(() => {
            message.success('删除成功');
            state.tableRef.reload();
          });
        },
      });
    }
    function oddJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length > 1) {
        message.info('只能选择一条数据进行操作');
        return false;
      }
      if (selectColumns?.length === 0) {
        message.info('请择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function evenJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length === 0) {
        message.info('请至少选择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function addSuccess() {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    function selectChange(val) {
      state.selectChangeData = findData(state.treeData, val);
    }
    function findData(data, val) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === val) {
          return data[i];
        }
        if (Array.isArray(data[i].children) && data[i].children.length > 0) {
          let rowItem = findData(data[i].children, val);
          if (rowItem) return rowItem;
        }
      }
    }
    function reflashAll() {
      state.reloadAll = new Date();
    }
    provide('reloadAll', reflashAll);
    function addLove() {
      openDrawer(true, {
        type: 'add',
        data: {
          dirId: state.selectChangeData.id,
          projectId: state.selectChangeData.projectId,
        },
      });
    }
    watch(
      () => state.searchParams,
      (val) => {
        state.showSearchTitle = JSON.stringify(val) !== '{}';
      },
      {
        deep: true,
      },
    );
    // watch(state.searchParams, (val) => {
    //   console.log(val);
    //   state.showSearchTitle = JSON.stringify(val) !== '{}';
    // });
    function search(value) {
      if (value) {
        state.searchParams.name = value;
        state.tableSearchTitle.name.value = value;
      } else {
        delete state.searchParams.name;
        delete state.tableSearchTitle.name.value;
      }
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    function searchForm(data) {
      state.searchParams = data;
      if (data.name) {
        state.searchValue = data.name;
      } else {
        state.searchValue = '';
      }
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      for (let name in state.tableSearchTitle) {
        delete state.tableSearchTitle[name].value;
      }
      for (let name in data) {
        if (data[name] && typeof state.tableSearchTitle[name] !== 'undefined') {
          if (typeof data[`${name}Name`] !== 'undefined') {
            state.tableSearchTitle[name].value = data[`${name}Name`];
          } else {
            state.tableSearchTitle[name].value = data[name];
          }
        }
      }
      state.tableRef.reload();
    }
    function initData(data) {
      setTimeout(() => {
        state.total = state.tableRef.getPaginationRef().total;
      }, 100);
    }
    function handleClose(key) {
      if (key === 'name') {
        state.searchValue = '';
      }
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      delete state.tableSearchTitle[key].value;
      delete state.searchParams[key];
      delete state.searchParams[`${key}Name`];
      state.tableRef.reload();
    }
    // function clearSearch() {
    //   for (name in state.tableSearchTitle) {
    //     delete state.tableSearchTitle[name].value;
    //   }
    //   for (name in state.searchParams) {
    //     delete state.searchParams[name];
    //     delete state.searchParams[`${name}Name`];
    //   }
    //   let pageSize = state.tableRef.getPaginationRef().pageSize;
    //   state.tableRef.setPagination({ current: 1, pageSize });
    //   state.searchValue = '';
    //   state.tableRef.reload();
    // }
    function getTreeApi(params) {
      let keyword = { keyword: params.searchText };
      return new Api('/pas').fetch(keyword, 'demand-dir/tree', 'GET').then((res) => res);
    }
    function deleteTreeApi(params) {
      return new Api(`/pas/demand-dir/${params[0]}`).fetch('', '', 'DELETE');
    }
    function addApi(params) {
      return new Api('/pas/demand-dir').fetch(params, '', 'POST');
    }
    function editApi(params) {
      return new Api('/pas/demand-dir').fetch(params, '', 'PUT');
    }
    function initTreeData(data) {
      state.treeData = data;
    }

    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '编辑',
          onClick() {
            openDrawer(true, {
              type: 'edit',
              data: { id: record.id },
            });
          },
        },
        {
          text: '删除',
          modal() {
            return new Api('/pas/demand-management/removeBatch').fetch([record.id], '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      selectChange,
      ...toRefs(state6),
      ...toRefs(state3),
      btnClick,
      addSuccess,
      addLove,
      goDetailDrawer,
      search,
      SearchDrawerRegister,
      searchForm,
      initData,
      dayjs,
      handleClose,
      // clearSearch,
      registerAddNode,
      getTreeApi,
      deleteTreeApi,
      addApi,
      editApi,
      initTreeData,
      actionsBtn,
      customHeaderCell,
    };
  },
});
</script>

<style  lang="less" scoped>
.content{
  .tableSearchTitle{
    display: flex;
    .titleSpan{
        width: 180px;
    }
    .tagList{
      flex: 1;
      .ant-tag{
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }
    .clearSearch{
      margin-left: 5px;
      color: #1890ff;
      cursor: pointer;
    }
  }
}

.table-content{
  .ant-empty{
    top: 50%;
    position: absolute;
    width: 100%;
  }
}
.boxs{
  &.layout2-wrap{
    :deep(.left){
      overflow: hidden !important;
      //background-color: red !important;
      box-sizing: border-box !important;
      width: 280px !important;
      margin-right: 0 !important;
      border-right: 1px solid #e5e7eb;
      .left-wrap{
        overflow: hidden !important;
        padding: 0 !important;
      }
    }
  }
  .demandTableTitle{
    display: flex;
    justify-content: space-between;
  }
  //:deep(.layout2-wrap){
  //  background-color: red !important;
  //  &>.left{
  //    box-sizing: content-box;
  //    width: 350px !important;
  //    margin-right: 0 !important;
  //    border-right: 1px solid #e5e7eb;
  //    .left-warp,.flex,.flex-ver{
  //      padding:0 !important;
  //    }
  //  }
  //}
}

</style>
