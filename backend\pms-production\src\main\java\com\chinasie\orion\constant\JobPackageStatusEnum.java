package com.chinasie.orion.constant;

import org.apache.hadoop.util.hash.Hash;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
public enum JobPackageStatusEnum implements Serializable {

    /**
     * 待审查
     */
    UNCHAKED(101, "待审查"),
    /**
     * 流程中
     */
    PROGRESS(110,"流程中"),
    /**
     * 已审查
     */
    CHAKED(130,"已审查");

    Integer status;

    String name;

    JobPackageStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public static HashMap<Integer,String> getMap(){
        return new HashMap<Integer,String>(){{
            put(UNCHAKED.getStatus(), UNCHAKED.getName());
            put(PROGRESS.getStatus(), PROGRESS.getName());
            put(CHAKED.getStatus(), CHAKED.getName());
        }};
    }
}
