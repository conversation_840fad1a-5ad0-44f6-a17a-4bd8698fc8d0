package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MarketContractCustContact VO对象
 *
 * <AUTHOR>
 * @since 2024-09-06 16:48:09
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MarketContractCustContactVO对象", description = "市场合同-客户-联系人")
@Data
public class MarketContractCustContactVO extends ObjectVO implements Serializable {

    /**
     * 市场合同id，pms_market_contract id
     */
    @ApiModelProperty(value = "市场合同id，pms_market_contract id")
    private String contractId;


    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    private String custContactId;


    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactName;


    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    private String contactPhone;


    /**
     * 联系人类型；business.商务联系人；technology.技术负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人")
    private String contactType;

    private String contactTypeName;

}
