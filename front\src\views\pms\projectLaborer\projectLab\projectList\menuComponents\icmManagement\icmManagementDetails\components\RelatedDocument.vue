<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="state.details?.status===130"
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        新增意见单
      </BasicButton>
    </template>
  </OrionTable>
  <RelatedDocumentDrawer
    ref="addDrawerRef"
    @update="reloadTable()"
  />
</template>

<script setup lang="ts">
import {
  h, reactive, ref, watch, inject, onMounted,
} from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import RelatedDocumentDrawer from './relatedDocumentDrawer/Drawer.vue';

const router = useRouter();
const emits = defineEmits([]);
const props = defineProps({});
const detailsInfo: any = inject('detailsInfo', {});
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
const state = reactive({
  selectRows: [],
  details: {},
});
onMounted(() => {
  state.details = detailsInfo.value;
});
const addDrawerRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  showIndexColumn: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'id',
  api: async (params) => {
    params.id = state.details?.id;
    params.currentFormType = state.details?.formType;
    return await new Api('/pms/idea-form/correlationList').fetch(params, '', 'POST').finally(() => {
      state.selectRows = [];
    });
  },
  columns: [
    {
      title: '单据名称',
      dataIndex: 'desc',
      align: 'left',
      minWidth: 200,
      customRender: ({ record, text }) => h('span', {
        class: record?.formType === 'transmission_form' ? '' : 'action-btn',
        onClick: async () => {
          if (record?.formType === 'transmission_form') return;
          await router.push({
            name: 'OIcmManagementDetailsIndex',
            params: {
              id: record?.id,
            },
          });
        },
      }, text),
    },
    {
      title: '发布方',
      align: 'left',
      dataIndex: 'publishDeptName',
    },
    {
      title: '接收方',
      align: 'left',
      dataIndex: 'reviewDeptName',
    },
    {
      title: '单据编号',
      align: 'left',
      dataIndex: 'number',
    },
    {
      title: '单据状态',
      align: 'left',
      dataIndex: 'dataStatus',
      customRender: ({ record }) => h('span', {}, record.dataStatus?.name ?? ''),
    },
    {
      title: '单据类型',
      align: 'left',
      dataIndex: 'formTypeName',
    },
    {
      title: '创建人',
      align: 'left',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      align: 'left',
      dataIndex: 'createTime',
      customRender: ({ text }) => (text ? h('span', {}, dayjs(text).format('YYYY-MM-DD')) : ''),
    },
  ],
});

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  addDrawerRef.value.openDrawer({
    action: 'add',
    info: {
      id: state.details?.id,
      projectId: state.details?.projectId,
      record: state.details,
    },
  });
}
</script>

<style scoped lang="less"></style>
