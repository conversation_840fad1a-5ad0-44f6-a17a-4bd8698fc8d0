<!--
 * @Description:安置环看板
 * @Autor: laotao117
 * @Date: 2024-08-21 11:11:33
 * @LastEditors: laotao117
 * @LastEditTime: 2024-10-29 18:56:48
-->
<template>
  <Layout
    v-get-power="{ pageCode: 'PMSSQEBulletinBoardIndex', getPowerDataHandle }"
    :options="{ body: { scroll: true } }"
  >
    <div
      v-if="state.loading"
      class="w-full h-full flex flex-pac"
    >
      <Spin />
    </div>
    <div class="page-com">
      <Row>
        <Col
          :span="24"
          class="mb20"
        >
          <indicatorDisk />
        </Col>
        <Col
          :span="24"
          class="mb20"
        >
          <performanceRanking />
        </Col>
        <Col
          :span="24"
          class="mb20"
        >
          <jobInformation />
        </Col>
        <!-- <Col
          :span="24"
          class="mb20"
        >
          <hazardInvestigation />
        </Col> -->
      </Row>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import {
  Col,
  Row,
} from 'ant-design-vue';
import {
  Layout,
} from 'lyra-component-vue3';
import indicatorDisk from './component/indicatorDisk.vue';
// performanceRanking
import performanceRanking from './component/performanceRanking.vue';
// jobInformation
import jobInformation from './component/jobInformation.vue';
// hazardInvestigation
import {
  onMounted,
  provide,
  reactive,
} from 'vue';
import hazardInvestigation from './component/hazardInvestigation.vue';
import { usePagePower } from '/@/views/pms/hooks';

const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);
const state: any = reactive({
  loading: false,
});
onMounted(() => {
});

</script>
<style scoped lang="less">
.page-com {
  padding: 20px 10px;
  color: #333;
}

.mb20 {
  margin-bottom: 20px;
}
</style>
