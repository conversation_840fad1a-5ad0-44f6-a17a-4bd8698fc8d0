<script setup lang="ts">
import {
  BasicButton,
  BasicImport,
  BasicTableAction,
  DataStatusTag,
  IOrionTableActionItem,
  openBasicSelectModal,
  openModal,
  OrionTable,
  useModal,
} from 'lyra-component-vue3';
import {
  h, inject, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { Modal, Popover } from 'ant-design-vue';
import Api from '/@/api';
import router from '/@/router';
import { openOverhaulDailyForm } from '/@/views/pms/overhaulManagement/utils';

const planDetailsData: Ref<Record<string, any>> = inject('formData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  maxHeight: 300,
  showToolButton: false,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '工单号',
      dataIndex: 'number',
    },
    {
      title: '作业名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        return h('span', {
          class: 'flex-te action-btn',
          title: text,
          onClick: () => navDetails(record),
        }, text);
      },
    },
    {
      title: '作业负责人',
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
    },
    {
      title: '高风险',
      dataIndex: 'isHighRisk',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '首次执行',
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
    },
    {
      title: '作业状态',
      dataIndex: 'busDataStatus',
      customRender({ text, record }) {
        if (record?.phase && text) {
          return h(Popover, null, {
            content: () => record.phase,
            default: () => h(DataStatusTag, { statusData: text }),
          });
        }
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '计划开工时间',
      dataIndex: 'beginTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束时间',
      dataIndex: 'endTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-manage/project').fetch({
    ...params,
    query: {
      projectNumber: planDetailsData.value?.projectNumber,
      planSchemeId: planDetailsData.value?.id,
    },
  }, 'page', 'POST'),
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
  },
  {
    text: '编辑',
    event: 'edit',
  },
  {
    text: '移除',
    modal(record) {
      return deleteApi([record.id]);
    },
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openOverhaulDailyForm(record, updateTable);
      break;
    case 'view':
      navDetails(record);
      break;
  }
}

function navDetails(record: Record<string, any>) {
  switch (planDetailsData.value?.workContent) {
    case 'pms_major_repair_con': // 大修
      openModal.closeAll();
      router.push({
        name: 'OverhaulOperationDetails',
        params: {
          id: record?.id,
        },
        query: {
          id: record?.repairRound,
        },
      });
      break;
    case 'pms_daily_con': // 日常
      openModal.closeAll();
      router.push({
        name: 'PMSDailyWorkDetails',
        params: {
          id: record?.id,
        },
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/job-manage/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

function saveApi(ids: any[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/job-manage/project/save').fetch(ids, '', 'POST').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

function handleRemove() {
  Modal.confirm({
    title: '移除提示！',
    content: '确认移除已选择的数据？',
    onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
  });
}

function getCustomerProps() {
  return {
    placeholder: '请选择',
    selectType: 'checkbox',
    smallSearchField: [
      'name',
      'number',
      'repairRound',
    ],
    showSmallSearch: true,
    tableApi: (params: any) => new Api('/pms/job-manage/project/page').fetch({
      ...params,
      query: {
        repairRound: planDetailsData.value?.repairRound,
        norO: planDetailsData.value?.workContent === 'pms_major_repair_con' ? 'O' : planDetailsData.value?.workContent === 'pms_daily_con' ? 'N' : '',
      },
    }, '', 'POST').then((res) => ({ ...res })),
    tableColumns: [
      {
        title: '工单号',
        dataIndex: 'number',
      },
      {
        title: '作业名称',
        dataIndex: 'name',
      },
      {
        title: '系统条件',
        dataIndex: 'norO',
      },
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
      },
      {
        title: '工作中心',
        dataIndex: 'workCenter',
      },
      {
        title: '状态',
        dataIndex: 'dataStatus',
        customRender({ text }) {
          return text ? h(DataStatusTag, { statusData: text }) : '';
        },
      },
    ],
  };
}

// 新增作业关联计划字段
function handleAddWork() {
  openBasicSelectModal({
    title: '添加作业',
    ...getCustomerProps(),
    onOk(records) {
      let workList = records.map((item) => {
        item.projectNumber = planDetailsData?.value?.projectNumber;
        item.planSchemeId = planDetailsData?.value?.id;
        item.planSchemeName = planDetailsData?.value?.name;
        return item;
      });
      saveApi(workList);
    },
  } as any);
}

// 导入相关
const [register, { openModal: openImportModal }] = useModal();
const downloadFileObj = {
  url: '/pms/job-manage/download/excel/tpl',
  method: 'GET',
};

function requestBasicImport(files: any[]) {
  const formData = new FormData();
  formData.append('file', files[0]);
  return new Api(`/pms/job-manage/import/excel/check/${planDetailsData?.value?.id}`).fetch(formData, '', 'POST');
}

function requestSuccessImport(importId: string) {
  return new Promise((resolve, reject) => {
    new Api(`/pms/job-manage/import/excel/${importId}`).fetch('', '', 'POST').then(() => {
      updateTable();
      resolve(true);
    }).catch((e) => {
      reject(e);
    });
  });
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="handleAddWork"
      >
        新增作业
      </BasicButton>
      <BasicButton
        icon="sie-icon-daoru"
        @click="openImportModal(true, {})"
      >
        导入
      </BasicButton>
      <BasicButton
        icon="sie-icon-shanchu"
        :disabled="selectedRows.length===0"
        @click="handleRemove()"
      >
        移除
      </BasicButton>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>

  <BasicImport
    :requestBasicImport="requestBasicImport"
    :requestSuccessImport="requestSuccessImport"
    :downloadFileObj="downloadFileObj"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
