package com.chinasie.orion.domain.dto.material;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/10:09
 * @description:
 */

@Data
public class OutBoundDTO implements Serializable {

    /**
     * 物质去向
     */
    @ApiModelProperty(value = "物质去向")
    @ExcelProperty(value = "物质去向 ", index = 6)
    private String materialDestination;

    /**
     * 出库原因
     */
    @ApiModelProperty(value = "出库原因")
    @ExcelProperty(value = "出库原因 ", index = 7)
    private String outReason;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    @ExcelProperty(value = "出库时间 ", index = 8)
    private Date outDate;


    private List<String> idList;
}
