<template>
  <div class="base-echart">
    <div
      ref="echartBoxRef"
      :style="{ width: w2h.width, height: w2h.height }"
    />
  </div>
</template>

<script lang="ts">
import {
  onMounted, watchEffect, defineComponent, reactive, toRefs,
} from 'vue';
import useEchart from '../../zkhooks/useEchart';

export default defineComponent({
  props: {
    options: {
      type: Object,
      default: () => ({}),
    },
    w2h: {
      type: Object,
      default: () => ({
        width: '100%',
        height: '500px',
      }),
    },
  },
  setup(props) {
    const state = reactive({
      echartBoxRef: {},
    });
    onMounted(() => {
      const { setOptions } = useEchart(state.echartBoxRef as HTMLElement);
      watchEffect(() => {
        setOptions(props.options);
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<!--<style scoped></style>-->
