package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.constant.TrainEquivalentStatus;
import com.chinasie.orion.domain.entity.PersonTrainEquRecord;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.entity.TrainEquivalent;
import com.chinasie.orion.service.*;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;

/**
 * 培训等效状态变更
 */
@Component
@Slf4j
public class TrainEquivalentChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "TrainEquivalent";

    @Resource
    private TrainEquivalentService trainEquivalentService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private PersonTrainEquRecordService personTrainEquRecordService;

    @Resource
    private TrainManageService  trainManageService;

    @Resource
    private PersonTrainInfoRecordService personTrainInfoRecordService;

    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("培训等效状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("培训等效状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    public void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<TrainEquivalent> wrapper=new LambdaUpdateWrapper<>(TrainEquivalent.class);
        wrapper.eq(TrainEquivalent::getId,message.getBusinessId());
        wrapper.set(TrainEquivalent::getStatus,message.getStatus());
        if(Objects.equals(TrainEquivalentStatus.FINISH.getStatus(),message.getStatus())){
            wrapper.set(TrainEquivalent::getIsEquivalent,Boolean.TRUE);
        }
        boolean result = trainEquivalentService.update(wrapper);

        TrainEquivalent equivalent=  trainEquivalentService.getById(message.getBusinessId());
        if(Objects.equals(TrainEquivalentStatus.FINISH.getStatus(),message.getStatus())){
            PersonTrainInfoRecord personTrainInfoRecord= personTrainInfoRecordService.getById(equivalent.getUkKey());
            PersonTrainEquRecord personTrainEquRecord = new PersonTrainEquRecord();
            personTrainEquRecord.setTrainNumber(equivalent.getTrainNumber());
            personTrainEquRecord.setEquivalentDate(equivalent.getEquivalentDate());
            personTrainEquRecord.setEquivalentBaseName(equivalent.getBaseName());
            if(null == personTrainInfoRecord){
                log.info("培训数据不存在，结构数据参数{}----同步落地培训", equivalent.getTrainNumber());
            }
            // TRAINNumber 能对应
            personTrainEquRecord.setSourceId(equivalent.getId());
            personTrainEquRecord.setFormTrainNumber(null == personTrainInfoRecord ? null:personTrainInfoRecord.getId());
            personTrainEquRecord.setEquivalentBaseCode(equivalent.getEquivalentBaseCode());
            personTrainEquRecord.setEquivalentBaseName(equivalent.getEquivalentBaseName());
            personTrainEquRecord.setTrainBaseCode(equivalent.getBaseCode());
            personTrainEquRecord.setCreatorId(equivalent.getCreatorId());
            personTrainEquRecord.setModifyId(equivalent.getModifyId());
            personTrainEquRecord.setOwnerId(equivalent.getOwnerId());
            personTrainEquRecord.setPlatformId(equivalent.getPlatformId());
            personTrainEquRecord.setOrgId(equivalent.getOrgId());
            personTrainEquRecord.setSourceId(equivalent.getId());
            personTrainEquRecord.setUserCode(equivalent.getUserCode());
            // 修改等效认定时间为 当前时间
            personTrainEquRecord.setEquivalentDate(new Date());
            personTrainEquRecordService.save(personTrainEquRecord);

            personTrainInfoRecord.setIsEquivalent(Boolean.TRUE);
            personTrainInfoRecordService.updateById(personTrainInfoRecord);
        }
        log.info("培训等效状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

}
