package org.jeecg.modules.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface IBaseService<T> extends IService<T> {
    <R> PageResult<R> customPageList(IPage<R> pageResult);

    PageResult<T> pageList(PageParam pageParam, Wrapper<T> wrapper);

    List<T> listByIds(Collection<? extends Serializable> idList);

}
