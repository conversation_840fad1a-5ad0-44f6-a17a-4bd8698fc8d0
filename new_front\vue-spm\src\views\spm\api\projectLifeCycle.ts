import Api from '/@/api';

/**
 * 获取全生命周期
 * @param projectId
 */
export async function list(projectId) {
  return new Api(`/spm/projectLifeCycle/list?projectId=${projectId}`).fetch('', '', 'GET');
}
/**
 * 编辑
 * @param params
 */
export async function phase(params) {
  return new Api('/spm/projectLifeCycle/phase').fetch(params, '', 'PUT');
}

/**
 * 全生命周期阶段选择模板
 * @param id 阶段id
 * @param templateId 模板的id
 */
export async function phaseTemplate(id, templateId) {
  return new Api(`/spm/projectLifeCycle/phase/template?id=${id}&templateId=${templateId}`).fetch('', '', 'PUT');
}
/**
 * 阶段设置
 * @param ids
 */
export async function phaseSetting(ids, projectId) {
  return new Api(`/spm/projectLifeCycle/phaseSetting?projectId=${projectId}`).fetch(ids, '', 'POST');
}
/**
 * 详情
 * @param id
 */
export async function projectLifeCycle(id) {
  return new Api(`/spm/projectLifeCycle/${id}`).fetch('', '', 'GET');
}
