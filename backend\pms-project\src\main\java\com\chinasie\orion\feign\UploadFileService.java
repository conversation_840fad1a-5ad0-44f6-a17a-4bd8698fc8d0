//package com.chinasie.orion.feign;
//
//import com.chinasie.orion.domain.dto.file.FileDto;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.sdk.core.conf.FeignConfig;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2021/10/19/10:25
// * @description:
// */
//@FeignClient(name = "upload", path = "", configuration = FeignConfig.class)
//public interface UploadFileService {
//
//    /**
//     * 通过数据ID获取 文件列表
//     *
//     * @param file
//     * @return
//     * @throws Exception
//     */
//    @PostMapping(value = "/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    ResponseDTO<FileDto> fileUpload(MultipartFile file) throws Exception;
//
//    /**
//     *  获取文件http地址
//     * @param fileDtoList
//     * @return
//     * @throws Exception
//     */
//    @PostMapping(value = "/file/downloadUrl")
//    ResponseDTO<List<FileDto>> downloadUrl(@RequestBody List<FileDto> fileDtoList) throws Exception;
//}
