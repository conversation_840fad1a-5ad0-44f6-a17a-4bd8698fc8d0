package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ContractSupplierSignedSubject DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:50:47
 */
@ApiModel(value = "ContractSupplierSignedSubjectDTO对象", description = "乙方签约主体")
@Data
@ExcelIgnoreUnannotated
public class ContractSupplierSignedSubjectDTO extends ObjectDTO implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    @ExcelProperty(value = "签约主体名称 ", index = 1)
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @ExcelProperty(value = "公司税号 ", index = 2)
    private String companyDutyParagraph;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @ExcelProperty(value = "主要联系人 ", index = 3)
    private String mainContactPerson;

    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    @ExcelProperty(value = "主要联系人电话 ", index = 4)
    private String mainContactPhone;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @ExcelProperty(value = "邮箱 ", index = 5)
    private String contractEmail;

    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    private String techContactPerson;

    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    private String techContactDept;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;



}
