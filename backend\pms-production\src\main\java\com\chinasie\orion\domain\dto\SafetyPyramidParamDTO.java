package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/9:05
 * @description:
 */
@Data
public class SafetyPyramidParamDTO implements Serializable {
    @ApiModelProperty(value = "大修轮次")
    private List<String> repairRoundList;

    @ApiModelProperty(value = "时间范围")
    private List<Date>  dateScope;
}
