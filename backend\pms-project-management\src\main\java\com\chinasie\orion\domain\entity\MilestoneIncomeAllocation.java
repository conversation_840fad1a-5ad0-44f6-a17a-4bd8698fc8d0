package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.lang.String;
import java.util.List;

/**
 * MilestoneIncomeAllocation Entity对象
 *
 * <AUTHOR>
 * @since 2025-01-07 10:50:47
 */
@TableName(value = "pmsx_milestone_income_allocation")
@ApiModel(value = "MilestoneIncomeAllocationEntity对象", description = "里程碑收入分配表")
@Data

public class MilestoneIncomeAllocation extends  ObjectEntity  implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;



}
