package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectQuestionStatisticsDTO;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.projectStatistics.QuestionStatusStatistics;
import com.chinasie.orion.domain.vo.DemandManagementVO;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectQuestionStatisticsVO;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.QuestionManagementRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionManagementService;
import com.chinasie.orion.service.projectStatistics.ProjectQuestionStatisticsService;
import com.chinasie.orion.service.projectStatistics.QuestionStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectQuestionStatisticsServiceImpl implements ProjectQuestionStatisticsService {
    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired
    private QuestionStatusStatisticsService questionStatusStatisticsService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private QuestionManagementRepository questionManagementRepository;

    @Resource
    private DictBo dictBo;

    @Resource
    private PasFeignService pasFeignService;

    @Override
    public ProjectQuestionStatisticsVO getProjectQuestionStatusStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) {
        ProjectQuestionStatisticsVO projectQuestionStatisticsVO = new ProjectQuestionStatisticsVO();
        LambdaQueryWrapperX<QuestionManagement> projectQuestionLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectQuestionLambdaQueryWrapperX.select("status,count(id) as count");
        projectQuestionLambdaQueryWrapperX.eq(QuestionManagement::getProjectId, projectQuestionStatisticsDTO.getProjectId());
        projectQuestionLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getQuestionType, projectQuestionStatisticsDTO.getQuestionType());
        projectQuestionLambdaQueryWrapperX.groupBy(QuestionManagement::getStatus);
        List<Map<String, Object>> list = questionManagementService.listMaps(projectQuestionLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("101".equals(map.get("status").toString())) {
                projectQuestionStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectQuestionStatisticsVO.setFinishedCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("102".equals(map.get("status").toString())) {
                projectQuestionStatisticsVO.setCloseCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectQuestionStatisticsVO;
    }

    @Override
    public List<ProjectQuestionStatisticsVO> getProjectQuestionRspUserStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) {
        List<ProjectQuestionStatisticsVO> projectQuestionStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<QuestionManagement> projectQuestionLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectQuestionLambdaQueryWrapperX.select("principal_id as rspUser,IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) unFinishedCount ," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as finishedCount," +
                "IFNULL( sum( CASE  WHEN `status`=102 THEN 1 ELSE 0 END ), 0 ) as closeCount");
        projectQuestionLambdaQueryWrapperX.eq(QuestionManagement::getProjectId, projectQuestionStatisticsDTO.getProjectId());
        projectQuestionLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getQuestionType, projectQuestionStatisticsDTO.getQuestionType());
        projectQuestionLambdaQueryWrapperX.groupBy(QuestionManagement::getPrincipalId);
        List<Map<String, Object>> list = questionManagementService.listMaps(projectQuestionLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectQuestionStatisticsVO projectQuestionStatisticsVO = new ProjectQuestionStatisticsVO();
            projectQuestionStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectQuestionStatisticsVO.setRspuserName(userVO.getName());
            }
            projectQuestionStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("unFinishedCount").toString()));
            projectQuestionStatisticsVO.setFinishedCount(Integer.parseInt(map.get("finishedCount").toString()));
            projectQuestionStatisticsVO.setCloseCount(Integer.parseInt(map.get("closeCount").toString()));
            projectQuestionStatisticsVOs.add(projectQuestionStatisticsVO);
        }
        return projectQuestionStatisticsVOs;
    }

    @Override
    public List<ProjectQuestionStatisticsVO> getProjectQuestionChangeStatusStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) {
        List<ProjectQuestionStatisticsVO> projectQuestionStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<QuestionStatusStatistics> projectQuestionLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectQuestionStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql = sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN un_finished_count ELSE 0 END ), 0 ) as unFinishedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN   DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN finished_count ELSE 0 END ), 0 ) as  finishedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN close_count ELSE 0 END ), 0 ) as closeCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectQuestionLambdaQueryWrapperX.select(sql);
        projectQuestionLambdaQueryWrapperX.eq(QuestionStatusStatistics::getProjectId, projectQuestionStatisticsDTO.getProjectId());
        projectQuestionLambdaQueryWrapperX.eqIfPresent(QuestionStatusStatistics::getTypeId, projectQuestionStatisticsDTO.getQuestionType());
        List<Map<String, Object>> list = questionStatusStatisticsService.listMaps(projectQuestionLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectQuestionStatisticsVO projectQuestionStatisticsVO = new ProjectQuestionStatisticsVO();
            projectQuestionStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("unFinishedCountTime" + i).toString()));
            projectQuestionStatisticsVO.setFinishedCount(Integer.parseInt(map.get("finishedCountTime" + i).toString()));
            projectQuestionStatisticsVO.setCloseCount(Integer.parseInt(map.get("closeCountTime" + i).toString()));
            projectQuestionStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectQuestionStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectQuestionStatisticsVOs.add(projectQuestionStatisticsVO);
        }
        Collections.reverse(projectQuestionStatisticsVOs);
        return projectQuestionStatisticsVOs;
    }

    @Override
    public List<ProjectQuestionStatisticsVO> getProjectQuestionCreateStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO) {
        List<ProjectQuestionStatisticsVO> projectQuestionStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<QuestionManagement> projectQuestionLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectQuestionStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectQuestionStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectQuestionLambdaQueryWrapperX.select(sql);
        projectQuestionLambdaQueryWrapperX.eq(QuestionManagement::getProjectId, projectQuestionStatisticsDTO.getProjectId());
        projectQuestionLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getQuestionType, projectQuestionStatisticsDTO.getQuestionType());
        List<Map<String, Object>> list = questionManagementService.listMaps(projectQuestionLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectQuestionStatisticsVO projectQuestionStatisticsVO = new ProjectQuestionStatisticsVO();
            projectQuestionStatisticsVO.setUnFinishedCount(Integer.parseInt(map.get("time" + i).toString()));
            projectQuestionStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectQuestionStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectQuestionStatisticsVOs.add(projectQuestionStatisticsVO);
        }
        Collections.reverse(projectQuestionStatisticsVOs);
        return projectQuestionStatisticsVOs;
    }

    @Override
    public Page<QuestionManagementVO> getProjectQuestionPages(Page<ProjectQuestionStatisticsDTO> pageRequest) throws Exception {
        Page<QuestionManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuestionManagement::new));
        LambdaQueryWrapperX<QuestionManagement> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(QuestionManagement.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO = pageRequest.getQuery();
            objectLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getStatus, projectQuestionStatisticsDTO.getStatus());
            objectLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getQuestionType, projectQuestionStatisticsDTO.getQuestionType());
            objectLambdaQueryWrapperX.eq(QuestionManagement::getProjectId, projectQuestionStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(QuestionManagement::getPrincipalId, projectQuestionStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectQuestionStatisticsDTO.getCreateTime())) {
                if (projectQuestionStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(QuestionManagement::getCreateTime,DateUtil.beginOfDay(projectQuestionStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectQuestionStatisticsDTO.getCreateTime()));
                }
                if (projectQuestionStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(QuestionManagement::getCreateTime,DateUtil.beginOfWeek(projectQuestionStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectQuestionStatisticsDTO.getCreateTime()));
                }
                if (projectQuestionStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(QuestionManagement::getCreateTime,DateUtil.beginOfQuarter(projectQuestionStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectQuestionStatisticsDTO.getCreateTime()));
                }
                if (projectQuestionStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(QuestionManagement::getCreateTime,DateUtil.beginOfMonth(projectQuestionStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectQuestionStatisticsDTO.getCreateTime()));
                }
                if (projectQuestionStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(QuestionManagement::getCreateTime,DateUtil.beginOfYear(projectQuestionStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectQuestionStatisticsDTO.getCreateTime()));
                }
            }
        }
        PageResult<QuestionManagement> page = questionManagementRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<QuestionManagementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuestionManagementVO> questionVOS = BeanCopyUtils.convertListTo(page.getContent(), QuestionManagementVO::new);
        if (!CollectionUtil.isNotEmpty(questionVOS)) {
            return pageResult;
        }
        Map<String, String> questionSeriousLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.QUESTION_SERIOUS_LEVEL);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);

        List<String> questionIds=questionVOS.stream().map(QuestionManagementVO::getQuestionType).collect(Collectors.toList());
        List<SimpleVo> simpleVos=pasFeignService.getQuestionTypeByIds(questionIds).getResult();
        Map simpleVoMap= simpleVos.stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName));

        questionVOS.forEach(o -> {
            o.setSeriousLevelName(questionSeriousLevelValueToDesMap.get(o.getSeriousLevel()));
            o.setPriorityLevelName(priorityLevelValueToDesMap.get(o.getPriorityLevel()));
            o.setQuestionTypeName(simpleVoMap.get(o.getQuestionType()).toString());
            o.setScheduleName(ObjectUtil.isNotNull(o.getSchedule()) ? o.getSchedule() + "%" : "0%");
        });
        pageResult.setContent(questionVOS);
        return pageResult;

    }
}
