package com.chinasie.orion.constant.review;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum OpinionTypeEnum {
    LOTUS(0, "文审"),
    APPRAISAL(1, "评审");
    private Integer value;
    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    OpinionTypeEnum(Integer status, String desc) {
        this.value = status;
        this.desc = desc;
    }
    public static OpinionTypeEnum MemberEnumMapping(int code) {
        switch (code) {
            case 0:
                return OpinionTypeEnum.LOTUS;
            case 1:
                return OpinionTypeEnum.APPRAISAL;
        }
        return OpinionTypeEnum.APPRAISAL;
    }
}
