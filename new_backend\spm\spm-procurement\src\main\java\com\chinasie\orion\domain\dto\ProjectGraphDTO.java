package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectGraph DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
@ApiModel(value = "ProjectGraphDTO对象", description = "技术人员统计表")
@Data
@ExcelIgnoreUnannotated
public class ProjectGraphDTO extends ObjectDTO implements Serializable {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private Integer indexOrder;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @ExcelProperty(value = "指标名称 ", index = 1)
    private String indexName;

    /**
     * 一月
     */
    @ApiModelProperty(value = "一月")
    @ExcelProperty(value = "一月 ", index = 2)
    private String jan;

    /**
     * 二月
     */
    @ApiModelProperty(value = "二月")
    @ExcelProperty(value = "二月 ", index = 3)
    private String feb;

    /**
     * 三月
     */
    @ApiModelProperty(value = "三月")
    @ExcelProperty(value = "三月 ", index = 4)
    private String mar;

    /**
     * 第一季度
     */
    @ApiModelProperty(value = "第一季度")
    @ExcelProperty(value = "第一季度 ", index = 5)
    private String firstQuarter;

    /**
     * 四月
     */
    @ApiModelProperty(value = "四月")
    @ExcelProperty(value = "四月 ", index = 6)
    private String apr;

    /**
     * 五月
     */
    @ApiModelProperty(value = "五月")
    @ExcelProperty(value = "五月 ", index = 7)
    private String may;

    /**
     * 六月
     */
    @ApiModelProperty(value = "六月")
    @ExcelProperty(value = "六月 ", index = 8)
    private String jun;

    /**
     * 七月
     */
    @ApiModelProperty(value = "七月")
    @ExcelProperty(value = "七月 ", index = 9)
    private String jul;

    /**
     * 八月
     */
    @ApiModelProperty(value = "八月")
    @ExcelProperty(value = "八月 ", index = 10)
    private String aug;

    /**
     * 第二季度
     */
    @ApiModelProperty(value = "第二季度")
    @ExcelProperty(value = "第二季度 ", index = 11)
    private String secondQuarter;

    /**
     * 九月
     */
    @ApiModelProperty(value = "九月")
    @ExcelProperty(value = "九月 ", index = 12)
    private String sept;

    /**
     * 第三季度
     */
    @ApiModelProperty(value = "第三季度")
    @ExcelProperty(value = "第三季度 ", index = 13)
    private String thirdQuarter;

    /**
     * 十月
     */
    @ApiModelProperty(value = "十月")
    @ExcelProperty(value = "十月 ", index = 14)
    private String oct;

    /**
     * 十一月
     */
    @ApiModelProperty(value = "十一月")
    @ExcelProperty(value = "十一月 ", index = 15)
    private String nov;

    /**
     * 十二月
     */
    @ApiModelProperty(value = "十二月")
    @ExcelProperty(value = "十二月 ", index = 16)
    private String dece;

    /**
     * 第四季度
     */
    @ApiModelProperty(value = "第四季度")
    @ExcelProperty(value = "第四季度 ", index = 17)
    private String fourthQuarter;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @ExcelProperty(value = "年度 ", index = 18)
    private String indexYear;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String projectGraphType;

}
