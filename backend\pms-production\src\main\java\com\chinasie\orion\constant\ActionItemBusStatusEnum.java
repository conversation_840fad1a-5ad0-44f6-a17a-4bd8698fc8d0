package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/28/11:38
 * @description:
 */
public enum ActionItemBusStatusEnum {

    WAIT_PUSH(101,"待发布")
    ,RUN_EXECUTE(110,"执行中")
    ,WAIT_FEEDBACK(111,"待反馈")
    ,WAIT_VERIFY(120,"待验证")
    ,VERIFY_PASS(121,"验证通过")
    ,CLOSE(130,"已关闭")
    ;


    private Integer status;

    private String desc;


    ActionItemBusStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> getStatusMap(){
        Map<String,String> map = new HashMap<>();
        ActionItemBusStatusEnum[] manageBusStatusEnums = ActionItemBusStatusEnum.values();
        for (ActionItemBusStatusEnum manageBusStatusEnum : manageBusStatusEnums) {
            map.put(manageBusStatusEnum.getStatus().toString(),manageBusStatusEnum.getDesc());
        }
        return  map;
    }




    public static List<Integer> getNotEditAndNotDel(){
        return List.of(RUN_EXECUTE.getStatus(),WAIT_VERIFY.getStatus()
                ,RUN_EXECUTE.getStatus(),VERIFY_PASS.getStatus(),CLOSE.getStatus(),WAIT_FEEDBACK.getStatus());
    }




}
