<template>
  <basic-echart :echartConfig="echartConfig" />
</template>
<script>
import {
  defineComponent, reactive, toRefs, computed,
} from 'vue';
import BasicEchart from './zkEchart.vue';
export default defineComponent({
  components: {
    BasicEchart,
  },
  setup() {
    const state = reactive({
      echartConfig: {
        options: {},
      },
    });
    state.echartConfig.options = computed(() => ({
      title: {
        text: 'Stacked Line',
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['Email'],
        right: '5%',
      },
      grid: {
        left: '1%',
        right: '2%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [
          'Mon',
          'Tue',
          'Wed',
          'Thu',
          'Fri',
          'Sat',
          'Sun',
        ],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: 'Email',
          type: 'line',
          stack: 'Total',
          data: [
            120,
            132,
            101,
            134,
            90,
            230,
            210,
          ],
        },
      ],
    }));
    return {
      ...toRefs(state),
    };
  },
});
</script>
<!--<style lang="less" scoped></style>-->
