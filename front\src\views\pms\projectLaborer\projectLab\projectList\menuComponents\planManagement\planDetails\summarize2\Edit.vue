<template>
  <a-form
    ref="formRef"
    label-align="left"
    :rules="rules"
    :model="father.form"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 14 }"
  >
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <a-form-item
            label="名称"
            name="name"
          >
            <a-input
              v-model:value="father.form.name"
              style="width: 100%"
              placeholder="请输入计划名称"
              allow-clear
              :maxlength="64"
              size="large"
            />
          </a-form-item>
          <a-form-item label="计划编号">
            {{ father.form.number }}
          </a-form-item>
          <a-form-item
            label="负责人"
            name="principalId"
          >
            <a-select
              v-model:value="father.form.principalId"
              class="w-full"
              placeholder="请选择负责人"
              allow-clear
              show-search
              :filter-option="filterOption"
              :options="namesList"
              size="large"
              @change="handlePrincipal(namesList, father.form.principalId)"
            />
          </a-form-item>
          <a-form-item
            label="结束日期"
            name="planPredictEndTime"
          >
            <a-date-picker
              v-model:value="father.form.planPredictEndTime"
              show-time
              type="date"
              class="w-full"
              placeholder="请选择结束日期"
              allow-clear
              value-format="YYYY-MM-DD HH:mm:ss"
              size="large"
            />
          </a-form-item>
          <a-form-item
            label="状态"
            name="status"
          >
            <a-select
              v-model:value="father.form.status"
              class="w-full"
              placeholder="请选择状态"
              :options="statusList"
              size="large"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="描述">
            <a-textarea
              v-model:value="father.form.remark"
              placeholder="请输入描述"
              allow-clear
              :maxlength="255"
              size="large"
            />
          </a-form-item>
          <a-form-item label="修改人">
            {{ father.form.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ formatDate(father.form.modifyTime) }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ formatDate(father.form.createTime) }}
          </a-form-item>
        </BasicTitle>
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
&nbsp;
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import {
  Row, Col, Input, DatePicker, Select, message, InputNumber,
  Form,
} from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import dayjs from 'dayjs';
import {
  reactive, toRefs, onMounted, ref,
} from 'vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
    AInputNumber: InputNumber,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      father: props.data,
      formRef: ref(),
      namesList: [],
      statusList: [],
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
        planPredictEndTime: [
          {
            required: true,
            message: '结束时间不能为空',
            trigger: 'change',
            type: 'string',
          },
        ],
        principalId: [
          {
            required: true,
            message: '负责人不能为空',
            trigger: 'change',
          },
        ],
        status: [
          {
            required: true,
            message: '状态不能为空',
            trigger: 'change',
            type: 'number',
          },
        ],
      },
    });

    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }
    function filterOption(inputValue, node) {
      return node.props.label.includes(inputValue);
    }
    function init() {
      const url1 = `project-role-user/getListByName/${parseURL().projectId}?name=`;
      new Api('/pms').fetch('', url1, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });

      const url2 = 'milestone/status/list';
      new Api('/pms').fetch('', url2, 'GET').then((res) => {
        state.statusList = res.map((s) => ({
          label: s.name,
          value: s.value,
        }));
      });
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }

    function submit(cb) {
      state.formRef
        .validate()
        .then(() => {
          cb(state.father.form);
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    onMounted(() => {
      init();
    });

    return {
      ...toRefs(state),
      formatDate,
      submit,
      handlePrincipal,
      filterOption,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.basicTitle) {
    .basicTitle_content {
      padding-left: 40px !important;
      height: calc(100vh - 300px);
      overflow: auto;
    }
  }
</style>
