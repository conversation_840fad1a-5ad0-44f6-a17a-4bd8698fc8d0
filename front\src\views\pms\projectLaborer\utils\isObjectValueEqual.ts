import { isEqual, isEqualWith } from 'lodash-es';
import { unref, isProxy, toRaw } from 'vue';
import { isArray, isUnDef, isNull } from '/@/utils/is';
/**
 *
 * @description 判断两个对象相同属性的属性值是否相等
 * @param obj    对比的对象
 * @param otherObj  需要对比的对象
 * @param props   可选参数,如果没有,对比两个对象上的所有属性
 * @return boolean
 *
 * */
export const isObjectValueEqual = (obj: object, otherObj: object, props?: string[]) => {
  const objRaw = isProxy(obj) ? toRaw(obj) : unref(obj); // 如果参数为 ref，则返回内部值，否则返回参数本身
  const otherObjRaw = isProxy(otherObj) ? toRaw(otherObj) : unref(otherObj);

  if (isArray(props)) {
    return isEqualWith(objRaw, otherObjRaw, (data: any, otherData: any) => props.every((item) => {
      let value = data[item];
      let equalValue = otherData[item];
      // 如果是 undefined 和 null 则重置为空字符串
      if (isUnDef(value) || isNull(value)) {
        value = '';
      }
      if (isUnDef(equalValue) || isNull(equalValue)) {
        equalValue = '';
      }
      return isEqual(value, equalValue);
    }));
  }
  return isEqual(objRaw, otherObjRaw);
};
