package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.JobMaterialRecord;
import com.chinasie.orion.domain.dto.JobMaterialRecordDTO;
import com.chinasie.orion.domain.vo.JobMaterialRecordVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobMaterialRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:37
 */
public interface JobMaterialRecordService extends OrionBaseService<JobMaterialRecord> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobMaterialRecordVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobMaterialRecordDTO
     */
    String create(JobMaterialRecordDTO jobMaterialRecordDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobMaterialRecordDTO
     */
    Boolean edit(JobMaterialRecordDTO jobMaterialRecordDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobMaterialRecordVO> pages(Page<JobMaterialRecordDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobMaterialRecordVO> vos) throws Exception;

    void addOrUpdate(String jobId, String number, String jobMaterialId);

    void addRelation(String jobId, String materialId, String number);
}
