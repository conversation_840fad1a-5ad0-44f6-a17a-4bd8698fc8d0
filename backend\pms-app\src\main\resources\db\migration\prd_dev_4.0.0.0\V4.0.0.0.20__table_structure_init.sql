ALTER TABLE `pmsx_project_order`
    ADD COLUMN `order_name` varchar(255) NULL COMMENT '订单名称' AFTER `order_number`;
ALTER TABLE `pmsx_project_order`
    ADD COLUMN `po_number` varchar(64) NULL COMMENT 'po订单号' AFTER `bear_org_name`,
ADD COLUMN `e_channel_number` varchar(64) NULL COMMENT '电商渠道订单号' AFTER `po_number`;

ALTER TABLE `pmsx_project_order`
    ADD COLUMN `order_time` datetime NULL COMMENT '下单时间' AFTER `e_channel_number`,
ADD COLUMN `pr_company` varchar(255) NULL COMMENT 'PR公司' AFTER `order_time`,
ADD COLUMN `delivery_time` datetime NULL COMMENT '要求到货时间' AFTER `pr_company`;


ALTER TABLE `pmsx_project_invoice`
    ADD COLUMN `payment_method` varchar(255) NULL COMMENT '结算方式' AFTER `order_number`,
ADD COLUMN `acceptance_method` varchar(255) NULL COMMENT '验收方式' AFTER `payment_method`,
ADD COLUMN `total_order_amount_tax` decimal(11, 2) NULL COMMENT '订单总金额（含税含费）' AFTER `acceptance_method`,
ADD COLUMN `total_order_amount` decimal(11, 2) NULL COMMENT '订单不含税总金额' AFTER `total_order_amount_tax`;



ALTER TABLE `pmsx_project_receive`
    ADD COLUMN `supplier_number` varchar(255) NULL COMMENT '供应商编码' AFTER `order_number`,
ADD COLUMN `supplier_name` varchar(255) NULL COMMENT '供应商名称' AFTER `supplier_number`,
ADD COLUMN `receive_director` varchar(255) NULL COMMENT '收货负责人' AFTER `supplier_name`,
ADD COLUMN `receive_reviewer` varchar(255) NULL COMMENT '收货审核人' AFTER `receive_director`,
ADD COLUMN `pay_director` varchar(255) NULL COMMENT '支付负责人' AFTER `receive_reviewer`,
ADD COLUMN `reconciliation_person` varchar(255) NULL COMMENT '对账人' AFTER `pay_director`;



ALTER TABLE `pmsx_project_flow`
    ADD COLUMN `order_status` varchar(255) NULL COMMENT '商城系统订单状态' AFTER `bear_org`,
ADD COLUMN `order_signature_status` varchar(255) NULL COMMENT '预订单签章状态' AFTER `order_status`,
ADD COLUMN `contract_signature_status` varchar(255) NULL COMMENT '最终合同签章状态' AFTER `order_signature_status`,
ADD COLUMN `sales_status` varchar(255) NULL COMMENT '售后状态' AFTER `contract_signature_status`,
ADD COLUMN `order_signature_date` datetime NULL DEFAULT NULL COMMENT '预订单签章时间' AFTER `sales_status`,
ADD COLUMN `contract_signature_date` datetime NULL COMMENT '最终合同签章时间' AFTER `order_signature_date`;



ALTER TABLE `pms_customer_info`
    MODIFY COLUMN `cus_num_count` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '企业规模' AFTER `public_account_info`,
    MODIFY COLUMN `cus_address` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '通讯地址' AFTER `cus_num_count`,
    ADD COLUMN `regist_status` varchar(255) NULL COMMENT '登记状态' AFTER `comtpye`,
    ADD COLUMN `paid_in_capital` varchar(255) NULL COMMENT '实缴资本' AFTER `regist_status`,
    ADD COLUMN `approved_date` datetime NULL COMMENT '核准日期' AFTER `paid_in_capital`,
    ADD COLUMN `province` varchar(255) NULL COMMENT '所属省份' AFTER `approved_date`,
    ADD COLUMN `city` varchar(255) NULL COMMENT '所属城市' AFTER `province`,
    ADD COLUMN `county` varchar(255) NULL COMMENT '所属区县' AFTER `city`,
    ADD COLUMN `category` varchar(255) NULL COMMENT '国标行业门类' AFTER `county`,
    ADD COLUMN `large_category` varchar(255) NULL COMMENT '国标行业大类' AFTER `category`,
    ADD COLUMN `middle_category` varchar(255) NULL COMMENT '国标行业中类' AFTER `large_category`,
    ADD COLUMN `english_name` varchar(255) NULL COMMENT '英文名' AFTER `middle_category`,
    ADD COLUMN `tel` varchar(255) NULL COMMENT '联系电话' AFTER `english_name`,
    ADD COLUMN `other_tel` varchar(255) NULL COMMENT '其他电话' AFTER `tel`,
    ADD COLUMN `email` varchar(255) NULL COMMENT '邮箱' AFTER `other_tel`;


ALTER TABLE `pms_requirement_mangement`
    ADD COLUMN `cust_person_name` varchar(255) NULL COMMENT '客户名称' AFTER `cust_person`;
