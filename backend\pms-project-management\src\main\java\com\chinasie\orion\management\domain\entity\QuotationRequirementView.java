package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Keafmd
 *
 * @ClassName: QuotationRequirementView
 * @Description: 报价的关联的需求，获取相关的对象
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/8/13 11:32
 * @Blog: https://keafmd.blog.csdn.net/
 */
@TableName("quotation_requirement_view")
@ApiModel(value = "QuotationRequirementView", description = "报价需求管理")
@Data
public class QuotationRequirementView extends QuotationManagement {
    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @TableField(value = "tech_res")
    private String techRes;

    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "business_person")
    private String businessPerson;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @TableField(value = "req_ownership")
    private String reqOwnership;

}
