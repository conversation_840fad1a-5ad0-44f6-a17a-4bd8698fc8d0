package com.chinasie.orion.domain.dto;

import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.vo.ProjectRoleUserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:44
 * @description:
 */
@ApiModel(value = "ProjectRoleDTO对象", description = "项目角色")
@Data
public class ProjectRoleDTO extends ObjectDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用 0:禁用 1：启用")
    private Integer takeEffect;

    /**
     * 业务角色ID
     */
    @ApiModelProperty(value = "业务角色ID")
    private String businessId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    /**
     * 项目code
     */
    @ApiModelProperty(value = "项目code")
    private String code;

    @ApiModelProperty(value = "角色code")
    private String roleCode;

    @ApiModelProperty(value = "人员")
    private List<ProjectRoleUser> userVOList;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public Integer getTakeEffect() {
        return takeEffect;
    }

    public void setTakeEffect(Integer takeEffect) {
        this.takeEffect = takeEffect;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }
}
