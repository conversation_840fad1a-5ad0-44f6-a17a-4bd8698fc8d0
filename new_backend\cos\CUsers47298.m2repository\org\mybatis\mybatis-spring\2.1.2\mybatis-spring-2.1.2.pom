<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright 2010-2023 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.mybatis</groupId>
    <artifactId>mybatis-parent</artifactId>
    <version>36</version>
    <relativePath />
  </parent>

  <artifactId>mybatis-spring</artifactId>
  <version>2.1.2</version>
  <packaging>jar</packaging>

  <name>mybatis-spring</name>
  <description>An easy-to-use Spring bridge for MyBatis sql mapping framework.</description>
  <url>http://www.mybatis.org/spring/</url>

  <contributors>
    <contributor>
      <name>Alex Rykov</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Andrius Juozapaitis</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Giovanni Cuccu</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Kay Huber</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Lishu Luo</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Michael Lanyon</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Raj Nagappan</name>
      <email><EMAIL></email>
    </contributor>

    <contributor>
      <name>Tomas Pinos</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <url>http://github.com/mybatis/spring</url>
    <connection>scm:git:ssh://github.com/mybatis/spring.git</connection>
    <developerConnection>scm:git:ssh://**************/mybatis/spring.git</developerConnection>
    <tag>mybatis-spring-2.1.2</tag>
  </scm>
  <issueManagement>
    <system>GitHub Issue Management</system>
    <url>https://github.com/mybatis/spring/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GitHub Actions</system>
    <url>https://github.com/mybatis/spring/actions</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>gh-pages</id>
      <name>Mybatis GitHub Pages</name>
      <url>git:ssh://**************/mybatis/spring.git?gh-pages#</url>
    </site>
  </distributionManagement>

  <properties>
    <clirr.comparisonVersion>1.3.2</clirr.comparisonVersion>
    <findbugs.onlyAnalyze>org.mybatis.spring.*,org.mybatis.spring.mapper.*,org.mybatis.spring.support.*,org.mybatis.spring.transaction.*</findbugs.onlyAnalyze>
    <gcu.product>Spring</gcu.product>
    <osgi.import>org.springframework.batch.*;resolution:=optional,*</osgi.import>
    <osgi.dynamicImport>*</osgi.dynamicImport>

    <mybatis.version>3.5.14</mybatis.version>
    <spring.version>5.3.31</spring.version>
    <spring-batch.version>4.3.10</spring-batch.version>
    <module.name>org.mybatis.spring</module.name>

    <junit.version>5.9.1</junit.version>

    <jacoco.plugin>0.8.11</jacoco.plugin>

    <!-- Reproducible Builds -->
    <project.build.outputTimestamp>1700907186</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <!-- Compile dependencies -->
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>${mybatis.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>${spring.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>${spring.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.batch</groupId>
      <artifactId>spring-batch-infrastructure</artifactId>
      <version>${spring-batch.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- Test dependencies -->

    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jdbc</artifactId>
      <version>5.0.9</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.derby</groupId>
      <artifactId>derby</artifactId>
      <version>10.14.2.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.jboss.byteman</groupId>
      <artifactId>byteman-bmunit</artifactId>
      <version>4.0.20</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>${spring.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>${spring.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.batch</groupId>
      <artifactId>spring-batch-core</artifactId>
      <version>${spring-batch.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.batch</groupId>
      <artifactId>spring-batch-test</artifactId>
      <version>${spring-batch.version}</version>
      <scope>test</scope>
      <exclusions>
           <exclusion>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
           </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.7.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>2.0.5</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.9.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.23.1</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.mockrunner</groupId>
      <artifactId>mockrunner-core</artifactId>
      <version>2.0.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.kirkk</groupId>
          <artifactId>jaranalyzer</artifactId>
        </exclusion>
        <exclusion>
          <groupId>jdom</groupId>
          <artifactId>jdom</artifactId>
        </exclusion>
        <exclusion>
          <groupId>nekohtml</groupId>
          <artifactId>nekohtml</artifactId>
        </exclusion>
        <exclusion>
             <groupId>junit</groupId>
             <artifactId>junit</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mockrunner</groupId>
      <artifactId>mockrunner-ejb</artifactId>
      <version>2.0.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-beanutils</groupId>
          <artifactId>commons-beanutils</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mockejb</groupId>
          <artifactId>mockejb</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mockrunner</groupId>
      <artifactId>mockrunner-jdbc</artifactId>
      <version>2.0.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>jakarta.transaction</groupId>
      <artifactId>jakarta.transaction-api</artifactId>
      <version>1.3.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>4.0.4</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <systemProperties>
            <property>
              <name>derby.stream.error.file</name>
              <value>${project.build.directory}/derby.log</value>
            </property>
            <property>
              <name>com.atomikos.icatch.log_base_dir</name>
              <value>${project.build.directory}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <!-- prepare site content by filtering ${project.*} values-->
          <execution>
            <id>filter-site</id>
            <phase>pre-site</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/site-src</outputDirectory>
              <resources>
                <resource>
                  <directory>src/site</directory>
                  <filtering>true</filtering>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <locales>en,es,zh_CN,ja,ko</locales>
          <siteDirectory>${project.build.directory}/site-src</siteDirectory>
       </configuration>
      </plugin>
    </plugins>

    <resources>
      <resource>
        <directory>${project.basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>LICENSE</include>
          <include>NOTICE</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.basedir}/src/main/resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>${project.build.testSourceDirectory}</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
  </build>

  <repositories>
    <repository>
      <id>sonatype-oss-snapshots</id>
      <name>Sonatype OSS Snapshots Repository</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
  </repositories>

</project>
