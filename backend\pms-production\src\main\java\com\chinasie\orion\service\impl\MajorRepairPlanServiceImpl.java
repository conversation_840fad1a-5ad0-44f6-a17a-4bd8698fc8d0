package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.OrionRoleConfig;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.MajorRepairOrgDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.CountVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.domain.vo.SimStatusVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.permission.core.annotation.OrionDataPermission;
import com.chinasie.orion.repository.MajorRepairPlanMapper;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:41
 * @description:
 */

@Service
@Slf4j
public class MajorRepairPlanServiceImpl extends OrionBaseServiceImpl<MajorRepairPlanMapper, MajorRepairPlan> implements MajorRepairPlanService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private JobManageService jobManageService;

    @Autowired
    private JobPostAuthorizeService jobPostAuthorizeService;

    @Autowired
    private JobMaterialService jobMaterialService;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private OrionRoleConfig roleLevelsConfig;

    @Autowired
    private MajorRepairPlanRoleService majorRepairPlanRoleService;

    @Autowired
    @Lazy
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private DataStatusNBO dataStatusNBO;



    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MajorRepairPlanVO detail(String id, String pageCode) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlan> wrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        wrapperX.eq(MajorRepairPlan::getId,id).or().eq(MajorRepairPlan::getRepairRound,id);
        wrapperX.last(" limit 1");
        MajorRepairPlan majorRepairPlan = this.getOne(wrapperX);
//        MajorRepairPlan majorRepairPlan = this.getById(id);
        if(Objects.isNull(majorRepairPlan)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据被删除或者不存在");
        }
        MajorRepairPlanVO result = BeanCopyUtils.convertTo(majorRepairPlan, MajorRepairPlanVO::new);
        setEveryName(Collections.singletonList(result));

        String repairRound = majorRepairPlan.getRepairRound();
        Date actualBeginTime = result.getActualBeginTime();
        if (Objects.nonNull(actualBeginTime) && Objects.nonNull(result.getActualEndTime())){
            DateTime dateTime = DateUtil.beginOfDay(actualBeginTime);
            result.setActualWorkDuration(DateUtil.betweenDay(dateTime, result.getActualEndTime(), true));
        }
        LogRecordContext.putVariable("repairRound", repairRound);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param majorRepairPlanDTO
     */
    @Override
    public String create(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        MajorRepairPlan majorRepairPlan = BeanCopyUtils.convertTo(majorRepairPlanDTO, MajorRepairPlan::new);
        if (isExist(majorRepairPlan.getRepairRound(), majorRepairPlanDTO.getId())) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该大修轮次数据已经存在，请重新输入");
        }
        this.save(majorRepairPlan);
        this.saveMajorRepairOrg(majorRepairPlan.getRepairRound());
        String rsp = majorRepairPlan.getId();
        setRole(majorRepairPlanDTO);
        return rsp;
    }

    public void saveMajorRepairOrg(String repairRound) throws Exception {
        MajorRepairOrgDTO majorRepairOrgDTO = new MajorRepairOrgDTO();
        majorRepairOrgDTO.setName("大修指挥部");
        // 默认第二层 作为大修下的顶层角色
        majorRepairOrgDTO.setCode("ROLE001");
        majorRepairOrgDTO.setRepairRound(repairRound);
        majorRepairOrgDTO.setLevel(2);
        majorRepairOrgDTO.setLevelType(MajorRepairOrgEnum.LEVEL_TYPE_REPAIR_ROLE.getCode());
        majorRepairOrgDTO.setParentId("0");
        majorRepairOrgDTO.setSort(1);
        majorRepairOrgService.create(majorRepairOrgDTO);
    }

    @OrionDataPermission()
    public void setRole(MajorRepairPlanDTO majorRepairPlanDTO){
        // 初始化大修角色
        ArrayList<MajorRepairPlanRole> majorRepairPlanRoles = new ArrayList<>();
        ArrayList<String> allRoles = new ArrayList<>();

        allRoles.addAll(roleLevelsConfig.getFirstLevel());
        allRoles.addAll(roleLevelsConfig.getTwoLevel());
        allRoles.addAll(roleLevelsConfig.getThreeLevel());

        LambdaQueryWrapperX<RoleDO> lambdaQueryWrapper = new LambdaQueryWrapperX<>(RoleDO.class);
        lambdaQueryWrapper.eq(RoleDO::getLogicStatus, 1)
                .in(RoleDO::getCode, allRoles);
        List<RoleDO> roleDOS = roleDOMapper.selectList(lambdaQueryWrapper);

        roleDOS.forEach(item -> {
            MajorRepairPlanRole majorRepairPlanRole = new MajorRepairPlanRole();
            BeanCopyUtils.copyProperties(item, majorRepairPlanRole);
            majorRepairPlanRole.setRoleCode(item.getCode());
            majorRepairPlanRole.setId("");
            majorRepairPlanRole.setMajorRepairTurn(majorRepairPlanDTO.getRepairRound());

            // 根据角色在配置中的级别设置 roleLevel
            if (roleLevelsConfig.getFirstLevel().contains(item.getCode())) {
                majorRepairPlanRole.setRoleLevel("1");
            } else if (roleLevelsConfig.getTwoLevel().contains(item.getCode())) {
                majorRepairPlanRole.setRoleLevel("2");
            } else if (roleLevelsConfig.getThreeLevel().contains(item.getCode())) {
                majorRepairPlanRole.setRoleLevel("3");
            }

            majorRepairPlanRoles.add(majorRepairPlanRole);
        });

        majorRepairPlanRoleService.saveBatch(majorRepairPlanRoles);


    }

    @Override
    public Boolean isExist(String repairRound, String id) {
        LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        lambdaQueryWrapperX.eq(MajorRepairPlan::getRepairRound, repairRound);
        if (StringUtils.hasText(id)) {
            lambdaQueryWrapperX.ne(MajorRepairPlan::getId, id);
        }
        List<MajorRepairPlan> list = this.list(lambdaQueryWrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public MajorRepairPlan getNameByRepairRound(String repairRound) {
        LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        lambdaQueryWrapperX.eq(MajorRepairPlan::getRepairRound, repairRound);
        lambdaQueryWrapperX.select(MajorRepairPlan::getRepairRound,MajorRepairPlan::getName,MajorRepairPlan::getRepairManager,MajorRepairPlan::getId);
        List<MajorRepairPlan> majorRepairPlanList= this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanList)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该大修不存在，请重新进去大修");
        }
        return majorRepairPlanList.get(0);
    }

    /**
     * 编辑
     * <p>
     * * @param majorRepairPlanDTO
     */
    @Override
    public Boolean edit(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        MajorRepairPlan majorRepairPlan = BeanCopyUtils.convertTo(majorRepairPlanDTO, MajorRepairPlan::new);
        if (isExist(majorRepairPlan.getRepairRound(), majorRepairPlanDTO.getId())) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该大修轮次数据已经存在，请重新输入");
        }
        this.updateById(majorRepairPlan);
        List<MajorRepairPlan> majorRepairPlanList = new ArrayList<>();
        this.setEntity(majorRepairPlan,majorRepairPlanList);
        if(!CollectionUtils.isEmpty(majorRepairPlanList)){
            // 时间如果有值 ，可根据时间触发状态变更
            this.updateBatchById(majorRepairPlanList);
        }
        String rsp = majorRepairPlan.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        //判断作业
        List<CountVO> countJobs = this.getBaseMapper().countJobManageByRepairPlanId(ids);
        countJobs.forEach(item->{
            Integer count = item.getCount();
            if (count>0){
                throw new BaseException(MyExceptionCode.ERROR_DELETE_DATA.getErrorCode(),"大修已关联作业，无法删除");
            }
        });
        //判断计划
        List<CountVO> countPlans = this.getBaseMapper().countProjectPlanByRepairPlanId(ids);
        countPlans.forEach(item->{
            Integer count = item.getCount();
            if (count>0){
                throw new BaseException(MyExceptionCode.ERROR_DELETE_DATA.getErrorCode(),"大修已关联计划，无法删除");
            }
        });

        LambdaQueryWrapperX<MajorRepairPlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        lambdaQueryWrapperX.in(MajorRepairPlan::getId, ids);
        lambdaQueryWrapperX.select(MajorRepairPlan::getRepairRound);
        List<MajorRepairPlan> majorRepairPlans = this.list(lambdaQueryWrapperX);
        LogRecordContext.putVariable("repairRounds", majorRepairPlans.stream().map(MajorRepairPlan::getRepairRound).collect(Collectors.joining(",")));
        LogRecordContext.putVariable("ids", majorRepairPlans.stream().map(MajorRepairPlan::getId).collect(Collectors.joining(",")));
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairPlanVO> pages(Page<MajorRepairPlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairPlan::getCreateTime);


        Page<MajorRepairPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlan::new));

        PageResult<MajorRepairPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<MajorRepairPlanVO> vos) throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }

        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(DictConts.MAJOR_REAPAIR_TYPE);
        Map<String, String> numberToDesc = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        List<String> managerIdList = vos.stream().map(MajorRepairPlanVO::getRepairManager).distinct().collect(Collectors.toList());

        Map<String, UserVO> idToEntity = userRedisHelper.getUserMapByUserIds(managerIdList);

        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(MajorRepairPlan.class.getSimpleName());
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));

        vos.forEach(vo -> {
            vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
            vo.setTypeName(numberToDesc.getOrDefault(vo.getType(), ""));
            vo.setRepairManagerName(idToEntity.getOrDefault(vo.getRepairManager(), new UserVO()).getName());
        });


    }

    @Override
    public List<SimStatusVO> statusList() {
        MajorRepairStatusEnum[] values = MajorRepairStatusEnum.values();
        List<SimStatusVO> statusVOList = new ArrayList<>();
        for (MajorRepairStatusEnum value : values) {
            SimStatusVO statusVO = new SimStatusVO();
            statusVO.setStatus(value.getStatus());
            statusVO.setDesc(value.getDesc());
            statusVOList.add(statusVO);
        }
        return statusVOList;
    }

    @Override
    public List<MajorRepairPlanVO> listByNumberList(List<String> majorRepairTurnList) {
        if (CollectionUtils.isEmpty(majorRepairTurnList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition.in(MajorRepairPlan::getRepairRound, majorRepairTurnList);
        List<MajorRepairPlan> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(list, MajorRepairPlanVO::new);
    }

    @Override
    public List<MajorRepairPlanVO> listByEntity(MajorRepairPlanDTO majorRepairPlanDTO) {
        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition.setEntity(BeanCopyUtils.convertTo(majorRepairPlanDTO, MajorRepairPlan::new));
        Boolean isFinish =majorRepairPlanDTO.getIsFinish();
        if(!Objects.isNull(isFinish)){
            if(!isFinish){
                condition.ne(MajorRepairPlan::getStatus,MajorRepairStatusEnum.FINISH.getStatus());
            }else{
                condition.eq(MajorRepairPlan::getStatus,MajorRepairStatusEnum.FINISH.getStatus());
            }
        }
        List<MajorRepairPlan> majorRepairPlanList = this.list(condition);
        if (CollectionUtils.isEmpty(majorRepairPlanList)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(majorRepairPlanList, MajorRepairPlanVO::new);
    }

    @Override
    public List<String> getRepairRoundList() {
        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition.select(MajorRepairPlan::getRepairRound);
        List<MajorRepairPlan> majorRepairPlanList = this.list(condition);
        if (CollectionUtils.isEmpty(majorRepairPlanList)) {
            return new ArrayList<>();
        }
        return majorRepairPlanList.stream().map(MajorRepairPlan::getRepairRound).distinct().collect(Collectors.toList());
    }

    @Override
    public void majorRepairPlanChangeStatusHandler() {
        LambdaQueryWrapperX<MajorRepairPlan> majorRepairPlanLambdaQueryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        majorRepairPlanLambdaQueryWrapperX.ne(MajorRepairPlan::getStatus, JobManageStatusEnum.FINISH.getStatus());

        List<MajorRepairPlan> repairPlans = this.list(majorRepairPlanLambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(repairPlans)){
            return;
        }

        List<MajorRepairPlan> repairPlanList = new ArrayList();
        for (MajorRepairPlan repairPlan : repairPlans) {
            this.setEntity(repairPlan,repairPlanList);
        }

        if(!CollectionUtils.isEmpty(repairPlanList)){
            this.updateBatchById(repairPlanList);
        }

    }

    @Override
    public List<SimpleVO> leTowlist() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        ZonedDateTime nowDateTime = now.atZone(ZoneId.systemDefault());
        now.atZone(ZoneId.systemDefault());
        Date nowDate = Date.from(nowDateTime.toInstant());
        // 计算两年前的时间
        LocalDateTime twoYearsAgo = now.minusYears(2);
        ZonedDateTime twoYearsDateTime = twoYearsAgo.atZone(ZoneId.systemDefault());

        // 将ZonedDateTime转换为Date
        Date date = Date.from(twoYearsDateTime.toInstant());
        LambdaQueryWrapperX<MajorRepairPlan> wrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        wrapperX.ge(MajorRepairPlan::getBeginTime,date);
        wrapperX.le(MajorRepairPlan::getBeginTime,nowDate);
        List<MajorRepairPlan> repairPlans = this.list(wrapperX);
        if(CollectionUtils.isEmpty(repairPlans)){
            return  new ArrayList<>();
        }
        List<SimpleVO> simpleVOList = new ArrayList<>();
        for (MajorRepairPlan repairPlan : repairPlans) {
            SimpleVO simpleVO = new SimpleVO();
            simpleVO.setId(repairPlan.getId());
            simpleVO.setName(repairPlan.getName());
            simpleVOList.add(simpleVO);
        }
         return simpleVOList;
    }

    @Override
    public Map<String, String> getSipMapList(List<String> relationMajorIdList) {
        LambdaQueryWrapperX<MajorRepairPlan> majorRepairPlanLambdaQueryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        majorRepairPlanLambdaQueryWrapperX.in(MajorRepairPlan::getId,relationMajorIdList);
        majorRepairPlanLambdaQueryWrapperX.select(MajorRepairPlan::getId,MajorRepairPlan::getName);
        List<MajorRepairPlan> majorRepairPlanList = this.list(majorRepairPlanLambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanList)){
            return  new HashMap<>();
        }
        return  majorRepairPlanList.stream().collect(Collectors.toMap(LyraEntity::getId,MajorRepairPlan::getName));
    }

    @Override
    public MajorRepairPlan getSimpleByRepairRound(String repairRound) {
        LambdaQueryWrapperX<MajorRepairPlan> wrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        wrapperX.eq(MajorRepairPlan::getRepairRound,repairRound);
        wrapperX.select(MajorRepairPlan::getBaseCode,MajorRepairPlan::getRepairRound);
        List<MajorRepairPlan> majorRepairPlanList = this.list(wrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanList)){
            return  null;
        }
        return majorRepairPlanList.get(0);
    }

    @Override
    public List<MajorRepairPlanVO> unfinishList() {
        LambdaQueryWrapperX<MajorRepairPlan> wrapperX = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        wrapperX.select(MajorRepairPlan::getId,MajorRepairPlan::getBaseCode,MajorRepairPlan::getRepairRound);
        wrapperX.ne(MajorRepairPlan::getStatus,JobManageBusStatusEnum.FINISH.getStatus());
        List<MajorRepairPlan> majorRepairPlanList= this.list(wrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanList)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(majorRepairPlanList, MajorRepairPlanVO::new);
    }

    public void setEntity(MajorRepairPlan repairPlan,List<MajorRepairPlan> repairPlanList){
        Date beginTime = repairPlan.getBeginTime();
        Date actualBeginTime = repairPlan.getActualBeginTime();
        Date date = new Date();
        // 对比开始时间和结束时间
        DateTime dateTime= DateUtil.beginOfDay(date);
        boolean b = false;
        if(null !=actualBeginTime ){
            //  当前时间 >=  实际时间
            if(dateTime.compareTo(actualBeginTime)>=0){
//                if(Objects.equals(MajorRepairStatusEnum.PREPARE.getStatus(),repairPlan.getStatus())){
                    repairPlan.setStatus(MajorRepairStatusEnum.IMPL.getStatus());
                    b = true;
//                }
            }else{
                repairPlan.setStatus(MajorRepairStatusEnum.PREPARE.getStatus());
                b = true;
            }
        }
        Date endTime = repairPlan.getEndTime();
        Date actualEndTime = repairPlan.getActualEndTime();
        if(null != endTime && null != actualEndTime ){
            // 实际结束 <=当前时间
            if(actualEndTime.compareTo(dateTime)<=0){
                repairPlan.setStatus(MajorRepairStatusEnum.FINISH.getStatus());
                b = true;
            }
        }
        if(b){
            repairPlanList.add(repairPlan);
        }
    }

}
