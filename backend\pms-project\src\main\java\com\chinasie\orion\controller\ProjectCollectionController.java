package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectCollectionDTO;
import com.chinasie.orion.domain.vo.ProjectCollectionStatisticsVO;
import com.chinasie.orion.domain.vo.ProjectCollectionTreeVO;
import com.chinasie.orion.domain.vo.ProjectCollectionVO;
import com.chinasie.orion.domain.vo.ProjectSchemeMilestoneNodeVO;
import com.chinasie.orion.service.ProjectCollectionService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * ProjectCollection 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@RestController
@RequestMapping("/projectCollection")
@Api(tags = "ProjectCollectionTreeUtil")
public class ProjectCollectionController {

    @Autowired
    private ProjectCollectionService projectCollectionService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "ProjectCollectionTreeUtil", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectCollectionVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectCollectionVO rsp = projectCollectionService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectCollectionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectCollectionDTO}}】", type = "ProjectCollectionTreeUtil", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectCollectionVO> create(@RequestBody ProjectCollectionDTO projectCollectionDTO) throws Exception {
        ProjectCollectionVO rsp =  projectCollectionService.create(projectCollectionDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectCollectionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectCollectionDTO}}】", type = "ProjectCollectionTreeUtil", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectCollectionDTO projectCollectionDTO) throws Exception {
        Boolean rsp = projectCollectionService.edit(projectCollectionDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据【{{#ids}}】", type = "ProjectCollectionTreeUtil", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectCollectionService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ProjectCollectionTreeUtil", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectCollectionStatisticsVO>> pages(@RequestBody Page<ProjectCollectionDTO> pageRequest) throws Exception {
        Page<ProjectCollectionStatisticsVO> rsp =  projectCollectionService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 树列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "树列表")
    @RequestMapping(value = "/tree/{id}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ProjectCollectionTreeUtil", subType = "树列表", bizNo = "")
    public ResponseDTO<List<ProjectCollectionTreeVO>> tree(@PathVariable("id") String id,@RequestBody ProjectCollectionDTO projectCollectionDTO) throws Exception {
        List<ProjectCollectionTreeVO> rsp = projectCollectionService.tree(id,projectCollectionDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 树列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "统计列表")
    @RequestMapping(value = "/treeStatistics/{id}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】统计列表", type = "ProjectCollectionTreeUtil", subType = "统计列表", bizNo = "")
    public ResponseDTO<List<ProjectCollectionTreeVO>> treeStatistics(@PathVariable("id") String id,@RequestBody ProjectCollectionDTO projectCollectionDTO) throws Exception {
        List<ProjectCollectionTreeVO> rsp = projectCollectionService.treeStatistics(id,projectCollectionDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "添加组合分页")
    @RequestMapping(value = "/getPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】添加组合分页", type = "ProjectCollectionTreeUtil", subType = "添加组合分页", bizNo = "")
    public ResponseDTO<Page<ProjectCollectionVO>> getPages(@RequestBody Page<ProjectCollectionDTO> pageRequest) throws Exception {
        Page<ProjectCollectionVO> rsp =  projectCollectionService.getPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     *
     * @param projectCollectionDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增子组合关联")
    @RequestMapping(value = "/create/toProject", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增子组合关联【{{#projectCollectionDTO}}】", type = "ProjectCollectionTreeUtil", subType = "新增子组合关联", bizNo = "")
    public ResponseDTO<Boolean> createProjectCollection(@RequestBody ProjectCollectionDTO projectCollectionDTO) throws Exception {
        Boolean rsp =  projectCollectionService.createProjectCollection(projectCollectionDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "移除组合关联")
    @RequestMapping(value = "/remove/toProject", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】移除组合关联【{{#parentId}}】", type = "ProjectCollectionTreeUtil", subType = "移除组合关联", bizNo = "{{#parentId}}")
    public ResponseDTO<Boolean> createProjectCollection(@RequestParam("parentId") String parentId, @RequestParam("id") String id) throws Exception {
        Boolean rsp =  projectCollectionService.removeProjectCollection(parentId,id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取责任人详情")
    @RequestMapping(value = "/detailPerson/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取责任人详情【{{#id}}】", type = "ProjectCollectionTreeUtil", subType = "获取责任人详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectCollectionVO> detailPerson(@PathVariable(value = "id") String id) throws Exception {
        ProjectCollectionVO rsp = projectCollectionService.detailPerson(id);
        return new ResponseDTO<>(rsp);
    }

}
