<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  IOrionTableActionItem,
  isPower,
  Layout,
  OrionTable,
  Select,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref, unref,
} from 'vue';
import { FormItem, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import PmsJobPostLibraryEdit from './PmsJobPostLibraryEdit.vue';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const baseCode: Ref<string> = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '岗位编码',
      dataIndex: 'number',
    },
    {
      title: '岗位名称',
      dataIndex: 'name',
      customRender({ text, record }) {
        return h('span', {
          class: 'flex-te action-btn',
          title: text,
          onClick: () => navDetails(record?.id),
        }, text);
      },
    },
    {
      title: '所属机构',
      dataIndex: 'baseName',
    },
    {
      title: '授权时间（月）',
      dataIndex: 'authorizationTime',
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-post-library').fetch({
    ...params,
    query: {
      baseCode: unref(baseCode),
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加岗位',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_ZYGWK_container_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: 'PMS_ZYGWK_container_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(PmsJobPostLibraryEdit, '岗位', null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_ZYGWK_container_02_button_01', powerData.value),
  },
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_ZYGWK_container_02_button_02', powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower('PMS_ZYGWK_container_02_button_03', powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(PmsJobPostLibraryEdit, '岗位', record, updateTable);
      break;
    case 'view':
      navDetails(record?.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSJobPositionsDetails',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-post-library').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}

const { powerData, getPowerDataHandle } = usePagePower();

function getBaseApi() {
  return new Api('/pms/base-place/list').fetch('', '', 'POST');
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSJobPositions',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="getButtonProps(button)"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
        <FormItem
          label="基地名称"
          class="mb0i"
        >
          <Select
            v-model:value="baseCode"
            style="width: 200px"
            allow-clear
            :api="getBaseApi"
            :fieldNames="{label:'name',value:'code'}"
            @change="updateTable()"
          />
        </FormItem>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
