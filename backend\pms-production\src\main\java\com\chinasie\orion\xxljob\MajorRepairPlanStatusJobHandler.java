package com.chinasie.orion.xxljob;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/28/0:17
 * @description:
 */

@Component
public class MajorRepairPlanStatusJobHandler {

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    @XxlJob("majorRepairPlanChangeStatus")
    public void majorRepairPlanChangeStatusHandler() {
        try {
            XxlJobHelper.log("大修计划状态变更出发，开始时间：{}", DateUtil.date());
            majorRepairPlanService.majorRepairPlanChangeStatusHandler();
        } catch (Exception e) {
            XxlJobHelper.log("大修计划状态变更出发，开始时间：{}，执行异常，原因：{}", DateUtil.date(), e.getMessage(), e);
        }
    }
}
