package com.chinasie.orion.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: lsy
 * @date: 2024/5/23
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class DeliverablePerformanceVO {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer sort;

    private String id;

    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号", index = 1)
    private String number;

    @ApiModelProperty(value = "交付物名称")
    @ExcelProperty(value = "交付物名称", index = 2)
    private String name;

    /**
     * 状态对象
     */
    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @ExcelProperty(value = "状态", index = 3)
    private String statusName;

    @ApiModelProperty(value = "所属项目")
    @ExcelProperty(value = "所属项目", index = 4)
    private String projectName;

    @ApiModelProperty(value = "所属任务")
    @ExcelProperty(value = "所属任务", index = 5)
    private String planName;

    /**
     *  计划交付物时间
     */
    @ApiModelProperty(value = "计划交付物时间")
    @ExcelProperty(value = "计划交付物时间", index = 6)
    private Date predictDeliverTime;

    @ApiModelProperty(value = "实际交付物时间")
    @ExcelProperty(value = "实际交付物时间", index = 7)
    private Date deliveryTime;
}
