package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.job.SaveJobManageDTO;
import com.chinasie.orion.domain.dto.jobDown.JobImplDownDTO;
import com.chinasie.orion.domain.dto.jobDown.JobPrepDownDTO;
import com.chinasie.orion.domain.dto.tree.*;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.*;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.domain.dto.RelationOrgToJobDTO;
import com.chinasie.orion.domain.vo.RelationOrgToJobVO;
import com.chinasie.orion.service.RelationOrgToJobService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * RelationOrgToJob 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@RestController
@RequestMapping("/relationOrgToJob")
@Api(tags = "关系-大修组织工单关系")
public class  RelationOrgToJobController  {

    @Autowired
    private RelationOrgToJobService relationOrgToJobService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修组织工单】详情", type = "RelationOrgToJob", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<RelationOrgToJobVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        RelationOrgToJobVO rsp = relationOrgToJobService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 新增
     *
     * @param relationOrgToJobDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【大修组织工单】数据", type = "RelationOrgToJob", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody RelationOrgToJobDTO relationOrgToJobDTO) throws Exception {
        String rsp =  relationOrgToJobService.create(relationOrgToJobDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 编辑
     *
     * @param relationOrgToJobDTO 关系组织工单关系
     * @return 是否成功
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【大修组织工单】数据【{{#relationOrgToJobDTO.name}}】", type = "RelationOrgToJob", subType = "编辑", bizNo = "{{#relationOrgToJobDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  RelationOrgToJobDTO relationOrgToJobDTO) throws Exception {
        Boolean rsp = relationOrgToJobService.edit(relationOrgToJobDTO);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【大修组织工单】数据", type = "RelationOrgToJob", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = relationOrgToJobService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }
    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【大修组织工单】数据", type = "RelationOrgToJob", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = relationOrgToJobService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修组织工单】分页数据", type = "RelationOrgToJob", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<RelationOrgToJobVO>> pages(@RequestBody Page<RelationOrgToJobDTO> pageRequest) throws Exception {
        Page<RelationOrgToJobVO> rsp =  relationOrgToJobService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
    @ApiOperation("关系-大修组织工单关系导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "RelationOrgToJob", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        relationOrgToJobService.exportByExcel(searchConditions, response);
    }


    @ApiOperation(value = "获取工单结构树-准备")
    @LogRecord(success = "【{USER{#logUserId}}】大修轮次【{{#searchVO.repairRound}}】的结构树-准备数据", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/prepare/tree", method = RequestMethod.POST)
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<JobDetailVO>>>> prepareTree(@RequestBody SearchVO searchVO) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<JobDetailVO>>> treeNodeVO =  relationOrgToJobService.prepareTree( searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "获取工单结构树-实施")
    @LogRecord(success = "【{USER{#logUserId}}】查询大修轮次【{{#searchVO.repairRound}}】的结构树-实施数据", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/impl/tree", method = RequestMethod.POST)
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>>> implTree(@RequestBody SearchVO searchVO) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>> treeNodeVO =  relationOrgToJobService.implTree( searchVO);
        return new ResponseDTO<>(treeNodeVO);
    }

    @ApiOperation(value = "作业编辑-准备")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑大修轮次【{{#jobPrepareEditDTO.repairRound}}】中工单号【{{#jobPrepareEditDTO.jobNumber}}】的基础信息", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/prepare/job/edit", method = RequestMethod.PUT)
    public ResponseDTO<JobPrepareEditVO> prepareJobEdit(@Validated @RequestBody JobPrepareEditDTO jobPrepareEditDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.prepareJobEdit( jobPrepareEditDTO));
    }

    @ApiOperation(value = "作业编辑-实施")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑大修轮次【{{#jobPrepareEditDTO.repairRound}}】中工单号【{{#jobPrepareEditDTO.jobNumber}}】的基础信息", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/impl/job/edit", method = RequestMethod.PUT)
    public ResponseDTO<JobImplEditVO> implJobEdit(@Validated @RequestBody JobImlEditDTO jobPrepareEditDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.implJobEdit( jobPrepareEditDTO));
    }

    @ApiOperation(value = "大修实施-修改开工的数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑大修轮次【{{#jobPrepareEditDTO.repairRound}}】中工单号【{{#jobPrepareEditDTO.jobNumber}}】的基础信息", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/impl/work/edit", method = RequestMethod.PUT)
    public ResponseDTO<JobWorkImplEditVO> implWokEdit(@RequestBody BeforeAndAfterFourEditDTO beforeAndAfterFourEditDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.implWokEdit( beforeAndAfterFourEditDTO));
    }


    @ApiOperation(value = "大修作业移除-通过关系ID列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】移除大修工单轮次【{{#repairRound}}】中工单号【{{#jobNumberStr}}】的基础信息", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    @RequestMapping(value = "/job/remove/batch", method = RequestMethod.DELETE)
    public ResponseDTO<Boolean> jobRemoveBatch(@RequestBody List<String> idList) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.jobRemoveBatch(idList));
    }


    @ApiOperation("批量保存接口-工单")
    @PostMapping("/add/batch")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】操作大修轮次【{{#repairRound}}】中的大修组织【{{#majorRepairOrg}}】移除工单号【{{#delNumberList}}】的基础信息", type = "RelationOrgToJob", subType = "树查询", bizNo = "")
    public ResponseDTO<Boolean> addBatch(@Validated @RequestBody SaveJobManageDTO saveJobManageDTO){
        Boolean res = relationOrgToJobService.addBatch(saveJobManageDTO);
        return new ResponseDTO<>(res);
    }



    @ApiOperation("批量保存接口-协助")
    @PostMapping("/assist/add/batch")
    @LogRecord(success = "【{USER{#logUserId}}】操作大修轮次【{{#repairRound}}】工单号协助【{{#assistDTO.jobNumber}}】的基础信息", type = "RelationOrgToJob", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> assistAddBatch(@RequestBody  AssistDTO assistDTO){
        Boolean res = relationOrgToJobService.assistAddBatch(assistDTO);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("获取准备阶段的统计下转工单列表")
    @RequestMapping(value = "/prepare/down/list", method = RequestMethod.POST)
    public ResponseDTO<List<JobDownVO>> prepareDownList(@RequestBody JobPrepDownDTO jobPrepDownDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.prepareDownList(jobPrepDownDTO));
    }



    @ApiOperation("获取实施阶段的统计下转工单列表")
    @RequestMapping(value = "/impl/down/list", method = RequestMethod.POST)
    public ResponseDTO<List<JobDownVO>> implDownList(@RequestBody JobImplDownDTO jobImplDownDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.impleDownList(jobImplDownDTO));
    }






    @ApiOperation(value = "作业编辑(全局)")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】下转编辑大修轮次【{{#jobAllEditDTO.repairRound}}】中工单号【{{#jobAllEditDTO.jobNumber}}】的基础信息", type = "JobAllEditDTO", subType = "下钻编辑", bizNo = "")
    @RequestMapping(value = "/all/job/edit", method = RequestMethod.PUT)
    public ResponseDTO<JobAllEditVO> allJobEdit(@Validated @RequestBody JobAllEditDTO jobAllEditDTO) throws Exception {
        return new ResponseDTO<>(relationOrgToJobService.allJobEdit(jobAllEditDTO));
    }



}
