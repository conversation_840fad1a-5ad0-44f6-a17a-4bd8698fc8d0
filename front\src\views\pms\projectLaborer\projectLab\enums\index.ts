export enum RelationEnum {
    '前置关系' = 100061,
    '后置关系'
}

export enum LevelEnum {
    '1级' = 100021,
    '2级',
    '3级',
    '4级',
    '5级',
    '6级',
    '7级',
    '8级',
    '9级',
    '10级'
}

export enum SituationEnum {
    '正常' = 100011,
    '已临期',
    '已逾期'
}

export enum SituationColorEnum{
    '#52c41a'=100011,
    '#f50',
    '#ff0000',
    '#faad14',
    '#2db7f5',

}
export const statusColor = {
  100011: '#52c41a', // 正常
  100012: '#ff0000', // 已临期
  100013: '#ff0000', // 已逾期
  100014: '#ffa500', // 退回
  100015: '#52c41a', // 完成
  100016: '#52c41a', // 逾期完成
  100017: '#2db7f5', // 待责任人处理
  100018: '#2db7f5', // 调整申请中
  100019: '#2db7f5', // 待完成确认
  100020: '#2db7f5', // 下发审批中
  100021: '#2db7f5', // 确认审批中
  100022: '#2db7f5', // 暂停审批中
  100023: '#2db7f5', // 终止审批中
  100024: '#2db7f5', // 启动审批中
};
export const objectColor = {
  taskDecomposition_1: '#73d13d', // 协同编制
  taskDecomposition_2: '#ff3399', // 收益策划
  taskDecomposition_3: '#2a7dc9', // 概算策划
  taskDecomposition_4: '#ff6633', // 风险策划
  taskDecomposition_5: '#722ed1', // 里程碑策划
  taskDecomposition_7: '#996600', // 资源策划
  taskDecomposition_6: '#6699ff', // 产品策划
};
export const planActiveColor = {
  designPlan: '#ff9900',
  assessmentPlan: '#2a7dc9',
  testPlan: '#1890ff',
  testAssessment: '#1890ff',
  qualityPlan: '#73d13d',
  riskPlan: '#722ed1',
};

export enum StatusEnum{
    '待发布'=100051,
    '发布中',
    '已完成'
}

export enum TypeEnum{
    '计划'=100001,
    '里程碑'
}
