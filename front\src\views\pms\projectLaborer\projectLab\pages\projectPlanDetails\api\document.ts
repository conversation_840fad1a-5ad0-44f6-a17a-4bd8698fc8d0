import Api from '/@/api';
// @ts-ignore
import { pageLoading } from '/@/store/modules/pageLoading';
import { useUserStore } from '/@/store/modules/user';
import { loadEnv } from 'vite';
const userStore = useUserStore();

const Yapi = '/pdm';
const basicConfig = (type) => {
  const api = getApi(type);
  let ns = 'pas';
  return {
    getDocumentTree: (params) => new Api(`/${ns}`).fetch(params, `${api.documentTree}`, 'GET'),
    getDocumentDetails: (id) => new Api(`/${ns}`).fetch({}, api.documentDetails + id, 'GET'),
    getDocumentAdd: (params) => new Api(`/${ns}`).fetch(params, api.documentAdd, 'POST'),
    getDocumentEdit: (params) => new Api(`/${ns}`).fetch(params, api.documentEdit, 'PUT'),
    getDocumentDelete: (params) => new Api(`/${ns}`).fetch(params, api.documentDelete, 'DELETE'),
    getDocumentUse: (id) => new Api(`/${ns}`).fetch('', api.documentUse + id, 'PUT'),
    getDocumentBan: (id) => new Api(`/${ns}`).fetch('', api.documentBan + id, 'PUT'),
    getClassificationTypeList: (id, params = {}) => new Api(`/${ns}`).fetch(params, api.classificationTypeList + id, 'get'),
    getClassificationTypeAdd: (docTypeId, params) => new Api(`/${ns}`).fetch(params, api.classificationTypeAdd + docTypeId, 'POST'),
    getClassificationTypeEdit: (params) => new Api(`/${ns}`).fetch(params, api.classificationTypeEdit, 'PUT'),
    getClassificationTypeDelete: (docTypeId, params) => new Api(`/${ns}`).fetch(params, api.classificationTypeDelete + docTypeId, 'DELETE'),
    getClassificationTypeReference: (params) => (tableParams) => new Api(`/${ns}`).fetch({
      ...tableParams,
      ...params,
    }, api.classificationTypeReference, 'POST'),
    getTypeRulesList: () => new Api(`/${ns}`).fetch('', api.typeRulesList, 'GET'),
    getTypeRulesListValue: (docTypeId) => new Api(`/${ns}`).fetch('', api.typeRulesListValue + docTypeId, 'GET'),
    getTypeRulesSave: (docTypeId, params) => new Api(`/${ns}`).fetch(params, api.typeRulesSave + docTypeId, 'POST'),
    getTypeRulesReset: (docTypeId) => new Api(`/${ns}`).fetch('', api.typeRulesReset + docTypeId, 'DELETE'),
    getProcessList: (docTypeId) => new Api(`/${ns}`).fetch('', api.processList + docTypeId, 'GET'),
    getProcessQuote: (docTypeId, params) => new Api(`/${ns}`).fetch(params, api.processQuote + docTypeId, 'POST'),
    getProcessDelete: (docTypeId, params) => new Api(`/${ns}`).fetch(params, api.processDelete + docTypeId, 'DELETE'),
    getProcessPage: (params) => new Api(`/${ns}`).fetch(params, api.processPage, 'POST'),
    getFlowWorkTree: () => new Api('/workflow').fetch('', 'category/tree', 'GET'),
    getFlowWorkList: (params) => (tableParams) => new Api('/workflow').fetch({
      ...params,
      ...tableParams,
    }, 'process-template/major/page', 'POST'),
    getWorkFlowMenu: (params) => new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST'),
    getWorkFlowSave: (params) => new Api('/workflow').fetch(params, 'process-instance', 'POST'),
    getWorkFlowEdit: (params) => new Api('/workflow').fetch(params, 'process-instance', 'PUT'),
    getWorkFlowJournal: (params) => new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET'),
    getApprovalListApi: (params) => new Api('/workflow').fetch(params, 'act-prearranged/delivery', 'POST'),
    getWorkFlowTaskBtn: (params) => new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET'),
    getAllTaskPage: (data) => {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      return new Api('/workflow').fetch('', url, 'POST');
    }, // (data) => new Api('/workflow').fetch({ processDefinitionId: data.procDefId, userId: userStore.getUserInfo.id }, 'process-instance/task-definition/page', 'POST'), // 获取流程表格数据
  };
};
function getApi(type) {
  const api = {
    PASChangeApplyType: {
      documentTree: 'ecr-type/tree', // 列表树
      documentDetails: 'ecr-type/', // 获取节点详情
      documentAdd: 'ecr-type', // 新增节点
      documentEdit: 'ecr-type', // 编辑
      documentDelete: 'ecr-type', // 删除
      documentUse: 'ecr-type/use/', // 启用
      documentBan: 'ecr-type/ban/', // 禁用
      classificationTypeList: 'ecr-type-to-ecr-attr/list/', // 分类属性列表
      classificationTypeAdd: 'ecr-type-to-ecr-attr/add/', // 分类属性添加
      classificationTypeEdit: 'ecr-attr', // 分类属性编辑
      classificationTypeDelete: 'ecr-type-to-ecr-attr/remove/', // 分类属性移除
      classificationTypeReference: 'ecr-attr/page', // 引用属性列表
      typeRulesList: 'ecr-conf/list/', // 类型规则列,
      typeRulesListValue: 'ecr-type-to-ecr-conf/list/', // 类型规则列表
      typeRulesSave: 'ecr-type-to-ecr-conf/quote/', // 类型规则保存
      typeRulesReset: 'ecr-type-to-ecr-conf/remove/', // 类型规则重置
      processList: 'ecr-type-to-process/list/', // 流程列表
      processQuote: 'ecr-type-to-process/quote/', // 流程引用
      processDelete: 'ecr-type-to-process/remove/', // 流程移除
      processPage: 'ecr-type-to-process/process/page', // 流程分页
    },
    PASChangeNoticeType: {
      documentTree: 'ecn-type/tree', // 列表树
      documentDetails: 'ecn-type/', // 获取节点详情
      documentAdd: 'ecn-type', // 新增节点
      documentEdit: 'ecn-type', // 编辑
      documentDelete: 'ecn-type', // 删除
      documentUse: 'ecn-type/use/', // 启用
      documentBan: 'ecn-type/ban/', // 禁用
      classificationTypeList: 'ecn-type-to-ecn-attr/list/', // 分类属性列表
      classificationTypeAdd: 'ecn-type-to-ecn-attr/add/', // 分类属性添加
      classificationTypeEdit: 'ecn-attr', // 分类属性编辑
      classificationTypeDelete: 'ecn-type-to-ecn-attr/remove/', // 分类属性移除
      classificationTypeReference: 'ecn-attr/page', // 引用属性列表
      typeRulesList: 'ecn-conf/list/', // 类型规则列,
      typeRulesListValue: 'ecn-type-to-ecn-conf/list/', // 类型规则列表
      typeRulesSave: 'ecn-type-to-ecn-conf/quote/', // 类型规则保存
      typeRulesReset: 'ecn-type-to-ecn-conf/remove/', // 类型规则重置
      processList: 'ecn-type-to-process/list/', // 流程列表
      processQuote: 'ecn-type-to-process/quote/', // 流程引用
      processDelete: 'ecn-type-to-process/remove/', // 流程移除
      processPage: 'ecn-type-to-process/process/page', // 流程分页
    },
  };
  return api[type];
  // return api[type];
}

export function getBasicConfig(type = 'PASChangeNoticeType') {
  // if (!basicConfig[type]) {
  //   console.error('该ClassName未配置');
  // }
  return basicConfig(type);
}
const pageLoadingStore = pageLoading();
export function changeLoading(val) {
  pageLoadingStore.setLoading(val);
}
