<template>
  <BasicModal
    v-bind="$attrs"
    title="支付状态确定"
    :width="500"
    :min-height="250"
    :showFooter="true"
    @register="register"

    @cancel="closeModal"
    @ok="okHandle"
  >
    <BasicForm
      ref="formRef"
      @register="registerForm"
    >
      <template #orderAndNodeParamDTOList>
        <div style="padding:20px;">
          <DetailsLayout
            :is-form-item="true"
            title="节点"
          >
            <div
              class="flex flex-ver flex-fs"
            >
              <Select
                v-model:value="state.orderAndNodeParamDTOList"
                mode="multiple"
                style="width: 300px"
                placeholder="请选择节点"
                option-label-prop="label"
                :options="state.orderList"
                @change="changeNode($event,index)"
              >
                <template #option="{label,value,payDesc}">
                  <div
                    v-if="value==='all'"
                    class="option-item"
                  >
                    {{ label }}
                  </div>
                  <div
                    v-else
                    class="option-item"
                  >
                    <span
                      class="option-item-content"
                      style="width:50%;"
                    >{{ label }}</span>
                    <span
                      class="option-item-desc flex-te"
                      style="width:50%;"
                    >{{ payDesc }}</span>
                  </div>
                </template>
              </Select>
            </div>
          </detailslayout>
        </div>
      </template>
      <!--      <template #footer>-->
      <!--        <div-->
      <!--          class="flex flex-pe"-->
      <!--        >-->
      <!--          <BasicButton @click="closeModal()">-->
      <!--            取消-->
      <!--          </BasicButton>-->
      <!--          <BasicButton-->
      <!--            :loading="state.btnLoading"-->
      <!--            @click="okHandle"-->
      <!--          >-->
      <!--            确定-->
      <!--          </BasicButton>-->
      <!--        </div>-->
      <!--      </template>-->
    </BasicForm>
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicForm } from '/@/components/Form';
import {
  useModalInner, BasicModal, getDict, BasicButton, useForm,
} from 'lyra-component-vue3';
import { message, Select } from 'ant-design-vue';
import {
  inject,
  nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { Rule } from 'ant-design-vue/es/form';
import {
  postContractPayNodeStatusConfirm,
  putContractPayNodeConfirm,
} from '/@/views/pms/projectLaborer/projectLab/api';
const state = reactive({
  payNodeTypeOptions: [],
  orderList: [],
  orderAndNodeParamDTOList: [],
  btnLoading: false,
});
const emit = defineEmits<{
  (e: 'loadingChange', loading: boolean): void
}>();
const formRef: Ref = ref();
const route = useRoute();
const [register, { closeModal }] = useModalInner(
  () => {
  },
);
// 订单节点选择校验规则
const validateNodes = async (_rule: Rule) => {
  if (state.orderAndNodeParamDTOList?.some((item) =>
    !item)) {
    return Promise.reject('请选择');
  }
  return Promise.resolve();
};
const updateTabs: (option: { id: string }) => void = inject('updateTabs');
const [registerForm, formMethods] = useForm({
  actionColOptions: {
    span: 24,
  },
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'orderAndNodeParamDTOList',
      slot: 'orderAndNodeParamDTOList',
      component: 'Input',
      rules: [{ validator: validateNodes }],
      colProps: {
        span: 24,
      },
    },

  ],
});

onMounted(() => {
  getPayNodeOptions();
  initFormData();
});
// 获取支付状态的字典
async function getPayNodeOptions() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
}
// 初始化表单数据
async function initFormData() {
  try {
    state.orderList = await getNodeOptions();
  } finally {
    emit('loadingChange', false);
  }
}
// 获取节点下拉数据
async function getNodeOptions() {
  const result = await new Api(`/pas/contractPayNodeConfirm/payNodes/statusConfirm/list/contractId/${route.query.id}`).fetch('', '', 'GET').finally(() => {
  });
  let nodeOptions = result?.map((item, index) => ({
    label: `${index + 1}-${state.payNodeTypeOptions.find((result) => result.value === item.payType)?.description}`,
    value: item.id,
    payDesc: item.payDesc || '',
    key: item.id,
    disabled: !item.isConfirm,
    payDescription: item.payDesc || '',
    serialNumber: item.serialNumber,
  }));
  nodeOptions.unshift({
    label: '全部',
    value: 'all',
    disabled: nodeOptions.every((item) => item.disabled),
  });
  return nodeOptions;
}

function changeNode(val: string[], index: number) {
  if (val.includes('all')) {
    state.orderAndNodeParamDTOList = state.orderList.filter((item) => !item.disabled && item.value !== 'all').map((item) =>
      item.value);
  }
  nextTick(() => {
    formMethods.validateFields(['orderAndNodeParamDTOList']);
  });
}

async function okHandle() {
  // formRef.value.formMethods.validate();
  if (state.orderAndNodeParamDTOList && state.orderAndNodeParamDTOList.length > 0) {
    const params = {
      nodeIdList: state.orderAndNodeParamDTOList,
      contractId: route.query.id,
    };
    await postContractPayNodeStatusConfirm(params);
    message.success('节点状态确认成功');
    updateTabs({ id: 4 });
  } else {
    message.warning('请选择节点');
  }
}

</script>
<style scoped lang="less">
.flex-fs {
  align-items: flex-start;
}

</style>
