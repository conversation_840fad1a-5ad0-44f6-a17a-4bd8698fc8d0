package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收入实时概况
 */
@Data
@ApiModel(value = "ProjectPlanStatusAndOverdueCountVO", description = "计划状态,计划执行异常")
public class ProjectPlanStatusAndOverdueCountVO {

    @ApiModelProperty("计划状态计数")
    private PlanStatusCountVO planStatusCount;

    @ApiModelProperty("计划执行异常计数")
    private PlanOverdueCountVO planOverdueCount;


    @Data
    @ApiModel(value = "PlanStatusCountVO", description = "计划状态")
    public static class PlanStatusCountVO {
        @ApiModelProperty("项目计划总数")
        private Integer total= 0;

        @ApiModelProperty("未开始")
        private Integer todo= 0;

        @ApiModelProperty("进行中")
        private Integer doing= 0;

        @ApiModelProperty("已完成")
        private Integer done= 0;
    }

    @Data
    @ApiModel(value = "PlanOverdueCountVO", description = "计划执行异常")
    public static class PlanOverdueCountVO {
        @ApiModelProperty("异常总数")
        private Integer total= 0;

        @ApiModelProperty("已逾期")
        private Integer overdue= 0;

        @ApiModelProperty("临期")
        private Integer doing= 0;

        @ApiModelProperty("逾期完成")
        private Integer overdueDone= 0;
    }
}
