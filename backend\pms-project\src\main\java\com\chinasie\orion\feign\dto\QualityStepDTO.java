package com.chinasie.orion.feign.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * QualityStep DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:22:45
 */
@ApiModel(value = "QualityStepDTO对象", description = "质控措施")
@Data
@ExcelIgnoreUnannotated
public class QualityStepDTO extends ObjectDTO implements Serializable {

    /**
     * 质控点
     */
    @ApiModelProperty(value = "质控点")
    @ExcelProperty(value = "质控点 ", index = 0)
    @NotBlank(message = "质控点不可为空")
    @Length(max = 100, message = "质控点长度不能超过100")
    private String point;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 1)
    private String number;

    /**
     * 方案
     */
    @ApiModelProperty(value = "方案")
    @ExcelProperty(value = "方案 ", index = 2)
    @NotBlank(message = "控制方案不可为空")
    @Length(max = 1000, message = "方案长度不能超过1000")
    private String scheme;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @ExcelProperty(value = "类型 ", index = 3)
    @NotBlank(message = "控制方案不可为空")
    private String type;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    @ExcelProperty(value = "阶段 ", index = 4)
    @Length(max = 50, message = "阶段长度不能超过50")
    private String stage;

    /**
     * 过程
     */
    @ApiModelProperty(value = "过程")
    @ExcelProperty(value = "过程 ", index = 5)
    @NotBlank(message = "过程不可为空")
    private String process;

    /**
     * 活动
     */
    @ApiModelProperty(value = "活动")
    @ExcelProperty(value = "活动 ", index = 6)
    @NotBlank(message = "质控活动不可为空")
    private String activity;

    /**
     * 交付文件名称
     */
    @ApiModelProperty(value = "交付文件名称")
    @ExcelProperty(value = "交付文件名称 ", index = 6)
    @Length(max = 50, message = "交付文件名称长度不能超过50")
    private String deliveryFileName;


    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private String messageId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private String modelId;

    /**
     * 引入查询
     */
    @ApiModelProperty(value = "引入查询")
    private Boolean introduce;
    /**
     * 是模板库查询
     */
    @ApiModelProperty(value = "是模板库查询（ture是，false不是）")
    private Boolean isModel;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 质量管控项编码
     */
    @ApiModelProperty(value = "质量管控项编码")
    private List<String> qualityItemCodes;
}
