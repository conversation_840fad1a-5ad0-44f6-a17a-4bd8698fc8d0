package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.MarketContractSignFeedbackVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * MarketContractSign DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@ApiModel(value = "MarketContractSignDTO对象", description = "市场合同签署信息")
@Data
@ExcelIgnoreUnannotated
public class MarketContractSignDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @ExcelProperty(value = "合同id ", index = 0)
    @NotEmpty(message = "合同id不能为空")
    private String contractId;

    /**
     * 签署结果
     */
    @ApiModelProperty(value = "签署结果")
    @ExcelProperty(value = "签署结果 ", index = 1)
    @NotNull(message = "请选择签署结果")
    private Boolean signResult;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    @ExcelProperty(value = "合同签署日期 ", index = 2)
    private Date signDate;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    @ExcelProperty(value = "合同生效日期 ", index = 3)
    private Date effectDate;

    /**
     * 合同完结日期
     */
    @ApiModelProperty(value = "合同完结日期")
    @ExcelProperty(value = "合同完结日期 ", index = 4)
    private Date completeDate;

    /**
     * 合同完结类型
     */
    @ApiModelProperty(value = "合同完结类型")
    @ExcelProperty(value = "合同完结类型 ", index = 5)
    private String completeType;

    /**
     * 终止签署原因
     */
    @ApiModelProperty(value = "终止签署原因")
    @ExcelProperty(value = "终止签署原因 ", index = 6)
    private String endSignReason;

    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @ExcelProperty(value = "客户合同编号")
    private String custContractNo;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileInfoDTOList;

    /**
     * 甲方名称编号
     */
    @ApiModelProperty(value = "主体名称编号")
    @ExcelProperty(value = "甲方名称编号")
    private List<MarketContractSignFeedbackVO> marketContractSignFeedbackVOs;




}
