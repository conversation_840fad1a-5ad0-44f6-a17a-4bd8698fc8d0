ALTER TABLE `pms_contract_milestone`
    MODIFY COLUMN `expect_accept_date` datetime COMMENT '初始预估验收日期';

ALTER TABLE `pms_contract_milestone`
    MODIFY COLUMN `except_acceptance_amt` decimal(20,2) COMMENT '初始预估验收金额';

ALTER TABLE `pms_contract_milestone` ADD COLUMN `planned_acceptance_amount` decimal(20,2) COMMENT '计划验收金额';
ALTER TABLE `pms_contract_milestone` ADD COLUMN `planned_acceptance_date` datetime COMMENT '计划验收日期';

UPDATE `dme_dict_value` SET `dict_id` = 'dict1862311952813064192', `label` = '', `value` = 'expect_accept_date', `sub_dict_id` = '', `sort` = 0, `number` = 'expect_accept_date', `status` = 1, `description` = '初始预估验收日期', `creator_id` = '314j1000000000000000000', `create_time` = '2024-11-29 09:46:51', `modify_id` = '314j1000000000000000000', `modify_time` = '2024-11-29 09:46:51', `platform_id` = 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', `unique_key` = NULL, `class_name` = 'DictValue', `logic_status` = 1, `owner_id` = '314j1000000000000000000', `remark` = NULL WHERE `id` = 'v0ss1862312231755251712';
UPDATE `dme_dict_value` SET `dict_id` = 'dict1862310899522666496', `label` = '', `value` = 'except_acceptance_amt', `sub_dict_id` = '', `sort` = 0, `number` = 'except_acceptance_amt', `status` = 1, `description` = '初始预估验收金额', `creator_id` = '314j1000000000000000000', `create_time` = '2024-11-29 09:45:04', `modify_id` = '314j1000000000000000000', `modify_time` = '2024-11-29 09:45:04', `platform_id` = 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', `unique_key` = NULL, `class_name` = 'DictValue', `logic_status` = 1, `owner_id` = '314j1000000000000000000', `remark` = NULL WHERE `id` = 'v0ss1862311785565192192';

ALTER TABLE `pmsx_mile_stone_log` ADD COLUMN `is_provisional_estimate` bit(1) COMMENT '是否需要暂估';

ALTER TABLE `pmsx_mile_stone_log` ADD COLUMN `planned_estimated_date` DATETIME COMMENT '计划暂估日期';