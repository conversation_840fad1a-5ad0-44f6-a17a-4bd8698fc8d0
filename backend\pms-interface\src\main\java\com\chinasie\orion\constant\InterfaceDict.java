package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/10:31
 * @description:
 */
public class InterfaceDict {

    public static Map<String,String> keyToDescMap = new HashMap<>();

    static {
        InterfaceTypeDict[] values = InterfaceTypeDict.values();
        for (InterfaceTypeDict value : values) {
            keyToDescMap.put(value.getName(),value.getDesc());
        }

        InterfaceThirdVerifyDict[] typeDicts = InterfaceThirdVerifyDict.values();
        for (InterfaceThirdVerifyDict value : typeDicts) {
            keyToDescMap.put(value.getName(),value.getDesc());
        }

        FormTypeDict[] formTypeDicts = FormTypeDict.values();
        for (FormTypeDict value : formTypeDicts) {
            keyToDescMap.put(value.getName(),value.getDesc());
        }

        ReplySuggest[] replySuggests = ReplySuggest.values();
        for (ReplySuggest value : replySuggests) {
            keyToDescMap.put(value.getName(),value.getDesc());
        }
    }

    /**
     *  接口类型字典
     */
    public   enum   InterfaceTypeDict{
        IM_INSIDE("inside","内部接口"),
        IM_EXTERNAL("external","外部接口");
        private String name;
        private String desc;

        InterfaceTypeDict(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }

        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }


    }

    /**
     * 第三方审查备案
     */
    public   enum   InterfaceThirdVerifyDict{
        IM_INSPECT("inspect","审查"),
        IM_EXTERNAL("record","备案"),
        IM_NO_HAVE("no_have","无"),;
        private String name;
        private String desc;

        InterfaceThirdVerifyDict(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }
        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 单据类型
     */
    public   enum   FormTypeDict{
        IM_TRANSMISSION_FORM("transmission_form","传递单"),
        IM_OPINION_FORM("opinion_form","意见单");
        private String name;
        private String desc;

        FormTypeDict(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }
        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 单据类型
     */
    public   enum   ReplySuggest{
        AGREE("agree","同意"),
        DISAGREE("disagree","不同意");
        private String name;
        private String desc;

        ReplySuggest(String name, String desc) {
            this.name = name;
            this.desc = desc;
        }
        public String getName() {
            return name;
        }

        public String getDesc() {
            return desc;
        }
    }

    public   enum   StateDict{
        FIN("FIN"),
        PIN ("PIN");
        private String key;

        StateDict(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    }
}
