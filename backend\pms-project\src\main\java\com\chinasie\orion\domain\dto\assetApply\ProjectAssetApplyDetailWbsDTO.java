package com.chinasie.orion.domain.dto.assetApply;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProjectAssetApplyDetailWbs DTO对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:30:30
 */
@ApiModel(value = "ProjectAssetApplyDetailWbsDTO对象", description = "资产转固申请详情表-WBS")
@Data
@ExcelIgnoreUnannotated
public class ProjectAssetApplyDetailWbsDTO extends  ObjectDTO   implements Serializable{
    /**
     * WBS预算数据信息Id
     */
    @ApiModelProperty(value = "projectId")
    @ExcelProperty(value = "projectId ", index = 0)
    private String projectId;
    /**
     * WBS预算数据信息Id
     */
    @ApiModelProperty(value = "WBS预算数据信息Id")
    @ExcelProperty(value = "WBS预算数据信息Id ", index = 0)
    private List<String> wbsIds;

    /**
     * 转固主表id
     */
    @ApiModelProperty(value = "转固主表id")
    @ExcelProperty(value = "转固主表id ", index = 1)
    private String assetApplyId;




}
