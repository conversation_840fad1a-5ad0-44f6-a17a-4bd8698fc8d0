package com.chinasie.orion.management.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.management.domain.dto.QuotationManagementDTO;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * RequirementMangement Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
@Mapper
public interface RequirementMangementMapper extends OrionBaseMapper<RequirementMangement> {

    /**
     * 查询主数据
     *
     * @param page  表格对象
     * @param query 查询条件
     * @return page
     */
    Page<RequirementMangementVO> queryByPage(Page<?> page, @Param("query") RequirementMangementDTO query);

}

