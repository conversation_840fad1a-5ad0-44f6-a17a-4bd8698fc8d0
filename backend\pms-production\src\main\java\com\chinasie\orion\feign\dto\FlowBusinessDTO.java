package com.chinasie.orion.feign.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/01/11:07
 * @description:
 */
@NoArgsConstructor
@Data
public class FlowBusinessDTO implements Serializable {
    @NotEmpty(message = "数据类型不能为空")
    private String dataTypeCode;
    @NotEmpty(message = "数据ID不能为空")
    private String businessId;
    private String businessName;
    @NotEmpty(message = "数据地址不能为空")
    private String messageUrl;
    private String templateId;

    private String orgId;
    private String creatorId;
    private String platformId;

    @ApiModelProperty("表单数据")
    private JSONObject formData;
}
