package com.chinasie.orion.constant;

import com.chinasie.orion.domain.vo.SimVO;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/15/15:24
 * @description:
 */
public enum MajorRepairStatusEnum {
    PREPARE(121,"大修准备")
    ,IMPL(110,"大修实施")
    ,FINISH(160,"大修完结")
    ;


    private Integer status;

    private String desc;

    MajorRepairStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
