package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.PersonTrainEquRecord;
import com.chinasie.orion.domain.dto.PersonTrainEquRecordDTO;
import com.chinasie.orion.domain.vo.PersonTrainEquRecordVO;

import com.chinasie.orion.service.PersonTrainEquRecordService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonTrainEquRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:55
 */
@RestController
@RequestMapping("/personTrainEquRecord")
@Api(tags = "人员培训等效信息记录")
public class  PersonTrainEquRecordController  {

    @Autowired
    private PersonTrainEquRecordService personTrainEquRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【人员培训等效信息记录】", type = "PersonTrainEquRecord", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<PersonTrainEquRecordVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PersonTrainEquRecordVO rsp = personTrainEquRecordService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personTrainEquRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【人员培训等效信息记录】数据【{{#personTrainEquRecordDTO.trainNumber}}】", type = "PersonTrainEquRecord", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonTrainEquRecordDTO personTrainEquRecordDTO) throws Exception {
        String rsp =  personTrainEquRecordService.create(personTrainEquRecordDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personTrainEquRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员培训等效信息记录】数据【{{#personTrainEquRecordDTO.trainNumber}}】", type = "PersonTrainEquRecord", subType = "编辑", bizNo = "{{#personTrainEquRecordDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonTrainEquRecordDTO personTrainEquRecordDTO) throws Exception {
        Boolean rsp = personTrainEquRecordService.edit(personTrainEquRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【人员培训等效信息记录】数据", type = "PersonTrainEquRecord", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personTrainEquRecordService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【人员培训等效信息记录】数据", type = "PersonTrainEquRecord", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personTrainEquRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员培训等效信息记录】数据", type = "PersonTrainEquRecord", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonTrainEquRecordVO>> pages(@RequestBody Page<PersonTrainEquRecordDTO> pageRequest) throws Exception {
        Page<PersonTrainEquRecordVO> rsp =  personTrainEquRecordService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员培训等效信息记录导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【人员培训等效信息记录】导入模板", type = "PersonTrainEquRecord", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        personTrainEquRecordService.downloadExcelTpl(response);
    }

    @ApiOperation("人员培训等效信息记录导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【人员培训等效信息记录】导入", type = "PersonTrainEquRecord", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = personTrainEquRecordService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员培训等效信息记录导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【人员培训等效信息记录】导入", type = "PersonTrainEquRecord", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personTrainEquRecordService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消人员培训等效信息记录导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【人员培训等效信息记录】导入", type = "PersonTrainEquRecord", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personTrainEquRecordService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人员培训等效信息记录导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【人员培训等效信息记录】数据", type = "PersonTrainEquRecord", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        personTrainEquRecordService.exportByExcel(searchConditions, response);
    }
}
