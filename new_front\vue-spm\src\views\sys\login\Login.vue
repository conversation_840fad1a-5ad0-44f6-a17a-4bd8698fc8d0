<template>
  <div
    class="login"
    :style="{ backgroundImage: `url(${getProjectConfig.loginPath})` }"
  >
    <div class="login-des">
      <!--      <h1><img src="../../../assets/images/<EMAIL>" alt="" /></h1>-->
      <!--      <div class="info">-->
      <!--        <h3>猎户座企业级协同研发平台</h3>-->
      <!--        <b></b>-->
      <!--        <p>自主创新 勇攀高峰</p>-->
      <!--      </div>-->
    </div>
    <div class="login-form-right">
      <div class="title mb10">
        <span>欢迎登录</span>
        <b class="ml10">Welcome to login</b>
      </div>
      <a-input
        ref="accountRef"
        v-model:value="formData.account"
        placeholder="请输入登录账户"
        class="mb20"
        :maxlength="32"
        size="large"
        @keyup.enter="login"
      >
        <template #prefix>
          <UserOutlined class="icon-style" />
        </template>
      </a-input>
      <a-input
        v-model:value="formData.password"
        placeholder="请输入登录密码"
        type="password"
        class="mb20"
        :maxlength="32"
        size="large"
        @keyup.enter="login"
      >
        <template #prefix>
          <LockOutlined class="icon-style" />
        </template>
      </a-input>
      <p
        v-if="msg"
        class="error mb30"
      >
        {{ msg }}
      </p>
      <a-button
        size="large"
        type="primary"
        :block="true"
        :loading="formState.loading"
        class="login-btn"
        @click="login"
      >
        登录
      </a-button>

      <!--      <div class="forget-pwd">-->
      <!--        &lt;!&ndash;        <a-checkbox v-model:checked="checked" @change="onChange"> 记住密码 </a-checkbox>&ndash;&gt;-->
      <!--        &lt;!&ndash;        <a-button type="link" @click="openModal()"> 忘记密码？ </a-button>&ndash;&gt;-->
      <!--      </div>-->
      <!--      <AppLocalePicker v-if="showLocale" class="login-form__locale" />-->
      <p class="copy-right">
        Copyright @ 2020 广州赛意信息科技股份有限公司
      </p>
      <BasicModal
        :show-cancel-btn="false"
        :show-ok-btn="false"
        v-bind="$attrs"
        title="提示"
        class="modal-style"
        :width="450"
        @register="register"
      >
        <div class="modal-content">
          <p>如果您忘记密码请联系系统管理员</p>
          <p>联系方式：<span>13616881688</span></p>
        </div>
      </BasicModal>
      <BasicModal
        v-bind="$attrs"
        title="修改初始密码"
        class="modal-style"
        :width="450"
        @register="registerResetPwd"
      >
        <template #footer>
          <a-button @click="openResetPwd(false)">
            取消
          </a-button>
          <a-button
            type="primary"
            @click="saveAction()"
          >
            确定
          </a-button>
        </template>
        <div class="reset-pwd">
          <p>注：密码规则为字母大小写 + 数字 + #&_.</p>
          <div class="form-style">
            <AForm
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
            >
              <a-form-item label="初始密码">
                <a-input
                  v-model:value="resetParam.initPassword"
                  type="password"
                  placeholder="请输入初始密码"
                  :maxlength="32"
                />
              </a-form-item>
              <a-form-item label="输入密码">
                <a-input
                  v-model:value="resetParam.password"
                  type="password"
                  :maxlength="32"
                  placeholder="请输入密码"
                />
              </a-form-item>
              <a-form-item label="确认密码">
                <a-input
                  v-model:value="resetParam.password2"
                  type="password"
                  :maxlength="32"
                  placeholder="请输入确认密码"
                />
              </a-form-item>
            </AForm>
          </div>
        </div>
      </BasicModal>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, ref, toRaw, watch, nextTick,
} from 'vue';
import { Checkbox, message, Form } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';

import { Button } from '/@/components/Button';
import { useUserStore } from '/@/store/modules/user';
import { useLocaleStoreWithOut } from '/@/store/modules/locale';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGlobSetting } from '/@/hooks/setting';
// import logo from '/@/assets/images/logo.png';
import { useI18n } from '/@/hooks/web/useI18n';
import { BasicModal, useModal } from '/@/components/Modal';
import WebCookie from '/@/utils/cache/cookie';
import { createStorage } from '/@/utils/cache/storageCache';
import { useAppStore } from '/@/store/modules/app';

export default defineComponent({
  name: 'Login',
  components: {
    //  BasicDragVerify,
    AButton: Button,
    // ACheckbox: Checkbox,
    // AppLocalePicker,
    UserOutlined,
    LockOutlined,
    BasicModal,
    AForm: Form,
  },
  setup() {
    const getProjectConfig = useAppStore().getProjectConfig;
    const userStore = useUserStore();
    const localeStore = useLocaleStoreWithOut();
    const cookieApi = new WebCookie();
    const formRef = ref<any>(null);
    const autoLoginRef = ref(false);
    const accountRef = ref(null);

    // 清除所有缓存
    userStore.clearState();

    const globSetting = useGlobSetting();
    const { notification } = useMessage();
    const { t } = useI18n();

    // const openLoginVerifyRef = computed(() => appStore.getProjectConfig.openLoginVerify);
    const formState = reactive({
      loading: false,
    });

    const formData = reactive({
      account: '',
      password: '',
      // verify: undefined,
    });

    const formRules = reactive({
      account: [
        {
          required: true,
          message: t('sys.login.accountPlaceholder'),
          trigger: 'blur',
        },
      ],
      password: [
        {
          required: true,
          message: t('sys.login.passwordPlaceholder'),
          trigger: 'blur',
        },
      ],
    });

    /**
       * @description 验证提示
       */
    const msg = ref('');
    const validateForm = function () {
      if (!formData.account || !formData.password) {
        msg.value = '请重新输入用户名或者密码';
        return false;
      }
      msg.value = '';
      return true;
    };
    watch(formData, (val) => {
      if (val.account || val.password) {
        validateForm();
      }
    });

    /**
       * 是否管理员
       */
    function isAdmin(account) {
      return (
        account === 'AdminSys'
          || account === 'adminsys1'
          || account === 'AdminSec'
          || account === 'AdminAudit'
          || account === 'Admin'
      );
    }

    /**
       * @description 登录
       */
    async function handleLogin() {
      // if (isAdmin(formData.account)) {
      //   message.error('登录失败');
      //   return false;
      // }
      if (validateForm()) {
        formState.loading = true;
        try {
          const userInfo: any = await userStore
            .login(
              toRaw({
                password: formData.password,
                username: formData.account,
                keepPassword: checked.value,
              }),
            )
            .catch((e) => {
              if (e.code === 80007) {
                openResetPwd();
              }
            });
            // 记住密码
          createStorage().set('keepPassword', checked.value);
          if (checked.value) {
            cookieApi.setCookie('username', formData.account);
            cookieApi.setCookie('password', formData.password);
          }
          if (userInfo) {
            if (userInfo.isFirstLogin) {
              openResetPwd();
            } else {
              notification.success({
                message: t('sys.login.loginSuccessTitle'),
                description: `${t('sys.login.loginSuccessDesc')}: ${formData.account}`,
                duration: 3,
              });
            }
          }
        } catch (error) {
        } finally {
          formState.loading = false;
        }
      }
    }

    // 忘记密码
    const [register, { openModal }] = useModal();

    /**
       * @description 重置初始密码
       */
    const [registerResetPwd, { openModal: openResetPwd }] = useModal();
    const resetParam = reactive({
      initPassword: '',
      password: '',
      password2: '',
    });
      /**
       * @description 修改后提交  todo验证
       */
    const saveAction = async function () {
      if (resetParam.password !== resetParam.password2) {
        message.warning('输入密码和确认密码不一致！', 2);
      } else {
        if (resetParam.password.length < 10) {
          message.warning('密码复杂度位10位以上，且需要包含字母、数字、特殊字符!', 2);
          return false;
        }
        try {
          const res = await userStore.updateInitPassword({
            username: formData.account,
            ...resetParam,
          });
          message.success('修改成功');
          if (res) {
            formData.password = resetParam.password;
            // 清空上一次填写的密码信息
            resetParam.initPassword = '';
            resetParam.password2 = '';
            resetParam.password = '';
            handleLogin();
          }
        } catch (error) {}
      }
    };

    // 记住密码
    const checked = ref(false);
    const onChange = function (e: any) {
      checked.value = e.target.checked;
    };

    onMounted(() => {
      const isChecked = createStorage().get('keepPassword');
      checked.value = isChecked;
      if (isChecked) {
        formData.account = cookieApi.getCookie('userName');
        formData.password = cookieApi.getCookie('password');
      }
    });

    // 聚焦输入框
    nextTick(() => {
      accountRef.value.focus();
    });

    return {
      getProjectConfig,
      formRef,
      accountRef,
      // verifyRef,
      formData,
      formState,
      formRules,
      login: handleLogin,
      autoLogin: autoLoginRef,
      // openLoginVerify: openLoginVerifyRef,
      title: globSetting && globSetting.title,
      // logo,
      t,
      showLocale: localeStore.getShowPicker,
      msg,

      checked,
      onChange,

      // 忘记密码
      register,
      openModal,

      // 重置初始密码
      registerResetPwd,
      openResetPwd,
      resetParam,
      saveAction,
    };
  },
});
</script>
<style scoped lang="less">
  .login-form__locale {
    position: absolute;
    top: 30px;
    right: 50px;
    z-index: 1;
  }

  .login-des {
    display: flex;
    flex-direction: column;
    padding: 50px;
    position: relative;
    height: 100%;

    h1 {
      img {
        width: 130px;
      }
    }

    .info {
      position: absolute;
      top: 50%;
      display: flex;
      flex-direction: column;
      margin-top: -180px;

      h3 {
        font-size: 60px;
        font-weight: bold;
        color: #fff;
        margin: 0;
        text-shadow: 5px 5px 10px rgba(0, 0, 0, 0.35);
      }

      b {
        width: 145px;
        height: 5px;
        background-color: #fff;
        display: block;
        margin: 15px 0;
      }

      p {
        font-size: 45px;
        color: #fff;
        margin: 0;
        text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.35);
      }
    }
  }

  .login-form-right {
    height: 100vh;
    width: 35%;
    max-width: 440px;
    right: 0;
    top: 0;
    position: fixed;
    z-index: 2;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    // justify-content: flex-end;
    padding: 50px;

    .title {
      span {
        font-size: 30px;
        font-weight: bold;
        color: #000;
      }
    }

    .error {
      color: #f30;
    }

    .forget-pwd {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px 0 30px 0;
    }

    .copy-right {
      color: #999;
      font-size: 14px;
      position: absolute;
      bottom: 0;
    }
  }

  .modal-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;

    p {
      font-size: 18px;
    }

    span {
      color: ~`getPrefixVar('primary-color')`;
      cursor: pointer;
    }
  }

  .login-btn {
    //background-color: rgba(18, 119, 199, 1);
    transition: all 0.2s ease;

    //&:hover {
    //  background-color: lighten(rgba(18, 119, 199, 1), 15%);
    //}
  }

  .icon-style {
    color: rgba(0, 0, 0, 0.25);
  }

  .reset-pwd {
    padding: 10px;

    p {
      padding: 10px;
      background-color: rgba(104, 111, 139, 0.1);
    }

    .ant-col {
      width: unset;
    }

    .form-style {
      .ant-form-item-control-wrapper {
        flex: 1;
      }
    }
  }

  .login {
    position: relative;
    height: 100vh;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    //background-image: url(/resource/img/login/login-bg.jpg);
  }
</style>
