<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive, watchEffect,
} from 'vue';

const baseInfoProps = reactive({
  list: [
    {
      label: '注册国家和地区',
      field: 'regCountryRegion',
    },
    {
      label: '省',
      field: 'province',
    },
    {
      label: '城市',
      field: 'city',
    },
    {
      label: '区/县',
      field: 'county',
    },
    {
      label: '注册地址',
      field: 'regAddress',
    },
    {
      label: '注册资金币种',
      field: 'capitalCurrency',
    },
    {
      label: '注册资本（万）',
      field: 'registeredCapital',
    },
  ],
  column: 2,
  dataSource: {},
});
const supplierInfo = inject('supplierInfo');
watchEffect(() => {
  baseInfoProps.dataSource = supplierInfo;
});
</script>

<template>
  <BasicCard
    title="注册信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>