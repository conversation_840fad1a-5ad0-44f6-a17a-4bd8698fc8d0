<template>
  <div
    v-if="!lineDetails.visible"
    v-loading="spinning"
    class="baseLine"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_03_03_button_02',powerData)"
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增基线
        </BasicButton>
      </template>
      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>
    </OrionTable>
  </div>
  <LineDetails
    v-if="lineDetails.visible"
    :data="lineDetails"
    @back="lineDetails.visible = false"
    @query="getLine"
  />
  <ViewDetails
    v-if="viewDetails.visible"
    :data="viewDetails"
  />
  <!-- 新增/编辑基线 -->
  <AddBaseLineNode
    v-if="pageType==='page'"
    @register="registerLine"
    @update="submitCreateBaseline"
  />
</template>

<script lang="ts">
import {
  Layout, BasicTable, isPower, useDrawer, OrionTable, ITableActionItem, BasicTableAction, BasicButton,
} from 'lyra-component-vue3';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import Pagination from '/@/views/pms/projectLaborer/knowledgeEditData/Pagination.vue';
import ViewDetails from './components/ViewDetails.vue';
import LineDetails from './components/LineDetails.vue';

import AddBaseLineNode from '../projectsPlan/components/AddBaseLineNode.vue';
import {
  Row, Col, Input, Spin, message, Modal,
} from 'ant-design-vue';
import {
  reactive, toRefs, onMounted, nextTick, ref, inject, computed, h,
} from 'vue';
import Api from '/@/api';
export default {
  name: 'Index',
  components: {
    OrionTable,
    ViewDetails,
    LineDetails,
    AddBaseLineNode,
    BasicTableAction,
    BasicButton,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [registerLine, { openDrawer: openLineDrawer }] = useDrawer();
    const state = reactive({
      powerData: [],
      tableRef: ref(),
      viewDetails: {},
      lineDetails: {
        visible: false,
        list: [],
        line: {},
        heightSTable: window.innerHeight - 390,
        isShowTable: true,
        dataSTableTree: [],
        loadingSTable: false,
        selectedRowKeys: [],
      },
      spinning: false,
    });
    state.powerData = inject('powerData');
    function getPage() {
      state.tableRef?.reload();
    }
    const addTableNode = () => {
      // let drawerData :any = {
      //   fromObjId: formData?.value?.id,
      //   fromObjName: formData?.value?.name,
      //   modelName: props.modelName,
      // };
      // let dataSource = tableRef.value.getDataSource();
      // if (dataSource.length > 0) {
      //   drawerData.dirId = dataSource[0].dirId;
      // }
      openLineDrawer(true, {
        type: 'add',
        data: '',
      });
    };

    function getLine(id) {
      state.lineDetails.loadingSTable = true;
      const love:any = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-基线管理', // 模块名称
        type: 'GET', // 操作类型
        remark: `打开查看了【${id}】详情`,
      };
      new Api('/pms', love)
        .fetch('', `base-line-info/detail/${id}`, 'GET')
        .then((res) => {
          const line = state.lineDetails.list.find((s) => s.id === id);
          state.lineDetails.line = line;
          state.lineDetails.visible = true;
          state.lineDetails.loadingSTable = false;
          state.lineDetails.dataSTableTree = res;
          state.lineDetails.isShowTable = true;
          nextTick(() => {
            state.lineDetails.isShowTable = true;
          });
        })
        .catch((_) => {
          state.lineDetails.loadingSTable = false;
        });
    }
    function getLineList() {
      const url = `base-line-info/list?projectId=${props.formId}`;
      new Api('/pms').fetch('', url, 'GET').then((res) => {
        state.lineDetails.list = res;
      });
    }

    function handleEdit(id) {
      new Api('/pms').fetch('', `base-line-info/${id}`, 'GET').then((res) => {
        openLineDrawer(true, {
          type: 'edit',
          data: res,
        });
      });
    }
    function submitCreateBaseline() {
      getPage();
    }

    onMounted(() => {
      getLineList();
    });

    function getListParams(params) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      rowSelection: {},
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_PLANMANAGE_BASELINEMANAGE',
      smallSearchField: ['name', 'number'],
      api(params) {
        return new Api('/pms')
          .fetch(getListParams({
            ...params,
            query: {
              projectId: props.formId,
            },
          }), 'base-line-info/page', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          width: '200px',
          align: 'left',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '名称',
          dataIndex: 'name',
          width: '300px',
          align: 'left',
          ellipsis: true,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('PMS_XMXQ_container_03_03_button_01', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('PMS_XMXQ_container_03_03_button_01', state.powerData)) {
                    getLine(record.id);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '描述',
          dataIndex: 'remark',
          align: 'left',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
          width: '200px',
          align: 'left',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '创建日期',
          dataIndex: 'createTime',
          width: '200px',
          align: 'left',
          sorter: true,
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn:ITableActionItem[] = [
      {
        text: '编辑',
        isShow: computed(() => isPower('PMS_XMXQ_container_03_03_button_03', state.powerData)),
        onClick(record: any) {
          handleEdit(record.id);
        },
      },
      {
        text: '删除',
        isShow: computed(() => isPower('PMS_XMXQ_container_03_03_button_04', state.powerData)),
        modal(record:any) {
          return new Api('/pms')
            .fetch([record.id], 'base-line-info/batch', 'DELETE')
            .then(() => {
              message.success('删除成功');
              state.tableRef?.reload();
            });
        },
      },
    ];

    return {
      ...toRefs(state),
      getPage,
      handleEdit,
      submitCreateBaseline,
      getLine,
      isPower,
      registerLine,
      tableOptions,
      actionsBtn,
      addTableNode,
    };
  },
};
</script>

<style lang="less" scoped>

</style>
