package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.TrainPersonDTO;
import com.chinasie.orion.domain.dto.train.EquivalentParamDTO;
import com.chinasie.orion.domain.dto.train.SettingTrainPersonScoreDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterUserDTO;
import com.chinasie.orion.domain.entity.TrainPerson;
import com.chinasie.orion.domain.vo.TrainPersonVO;
import com.chinasie.orion.domain.vo.train.PersonTrainVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * TrainPerson 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:14
 */
public interface TrainPersonService extends OrionBaseService<TrainPerson> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TrainPersonVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param trainPersonDTO
     */
    String create(TrainPersonDTO trainPersonDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param trainPersonDTO
     */
    Boolean edit(TrainPersonDTO trainPersonDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TrainPersonVO> pages(Page<TrainPersonDTO> pageRequest) throws Exception;


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TrainPersonVO> vos) throws Exception;



    List<TrainPersonVO> trainPersonList(TrainPersonDTO trainPersonDTO);

    /**
     *  批量创建 参培人员
     * @param trainPersonDTO
     * @return
     */
    Boolean createBatch(TrainCenterUserDTO trainPersonDTO);

    /**
     *  获取未到期并且当前人已经通过培训的培训列表
     * @param equivalentParamDTO
     * @return
     */
    List<PersonTrainVO> trainCurrentPersonList(EquivalentParamDTO equivalentParamDTO);

    List<PersonTrainVO> getPersonTrainList(String userCode, String baseCode);

    Boolean scoreOK(List<String> idList);

    Boolean scoreNotOK(List<String> idList);

    Boolean settingScore(SettingTrainPersonScoreDTO scoreDTO);

    /**
     *  通过中心获取用户编码
     * @param centerId
     * @return
     */
    List<String> userCodeByCenterId(String centerId,Boolean isCheck);

    Map<String, Long> numMapToCenterIdList(List<String> idList);



    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response,String centerId) throws Exception;
}
