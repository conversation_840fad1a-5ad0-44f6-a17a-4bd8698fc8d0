package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.SchemeToMaterialDTO;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.SchemeToMaterialService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SchemeToMaterial 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@RestController
@RequestMapping("/schemeToMaterial")
@Api(tags = "计划相关的物资")
@RequiredArgsConstructor
public class SchemeToMaterialController {

    private final SchemeToMaterialService schemeToMaterialService;

    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看物资【{{#id}}】", type = "PersonMange", subType = "查看", bizNo = "{{#id}}")
    public ResponseDTO<SchemeToMaterialVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SchemeToMaterialVO rsp = schemeToMaterialService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#schemeToMaterialDTO.name}}】", type = "SchemeToMaterial", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SchemeToMaterialDTO schemeToMaterialDTO) throws Exception {
        String rsp = schemeToMaterialService.create(schemeToMaterialDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#schemeToMaterialDTO.name}}】", type = "SchemeToMaterial", subType = "编辑", bizNo = "{{#schemeToMaterialDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SchemeToMaterialDTO schemeToMaterialDTO) throws Exception {
        Boolean rsp = schemeToMaterialService.edit(schemeToMaterialDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "SchemeToMaterial", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = schemeToMaterialService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "SchemeToMaterial", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = schemeToMaterialService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "SchemeToMaterial", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToMaterialVO>> pages(@RequestBody Page<SchemeToMaterialDTO> pageRequest) throws Exception {
        Page<SchemeToMaterialVO> rsp = schemeToMaterialService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取大修下的物资列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询大修下的物资列表", type = "SchemeToMaterial", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/material/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToMaterialVO>> materialPages(@RequestBody Page<SchemeToMaterialDTO> pageRequest) throws Exception {
        Page<SchemeToMaterialVO> rsp = schemeToMaterialService.materialPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("物资入场离场信息导出(Excel)")
    @PostMapping(value = "/export/scheme/material/excel", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】物资入场离场信息导出", type = "NonFixedAssets", subType = "物资入场离场信息导出", bizNo = "")
    public void exportSchemeMaterialExcel(@RequestBody SchemeToMaterialDTO query, HttpServletResponse response) {
        schemeToMaterialService.exportSchemeMaterialExcel(query, response);
    }

}
