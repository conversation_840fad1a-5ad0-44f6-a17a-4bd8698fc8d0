<template>
  <div
    ref="accountSummaryRef"
    class="account-summary-card-list"
  >
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="left-label">
        概算总金额
      </div>
      <div class="left-value">
        {{ formatMoney(summaryData?.allAmount) }}
        <span>元</span>
      </div>
      <div class="card-item-bottom">
        <div class="card-item-bottom-left">
          <span class="card-item-bottom-label">直接费</span>
          <span>{{ summaryData?.directFeeRate||'' }}</span>
        </div>
        <div class="card-item-bottom-right">
          <span class="card-item-bottom-label">间接费</span>
          <span>{{ summaryData?.indirectFeeRate||'' }}</span>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <MaterialsCharts :summaryData="summaryData" />
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="left-label">
        工资及劳务费
      </div>
      <div class="left-value-parent">
        <div class="left-value flex-te">
          {{ formatMoney(summaryData?.laborFee>10000?summaryData?.laborFee/10000:summaryData?.laborFee) }}
          <span>{{ summaryData?.laborFee>10000?'万元':'元' }}</span>
        </div>
        <div class="left-value-right flex-te">
          {{ summaryData?.peopleNum }}人天
        </div>
      </div>
      <div class="card-item-img" />
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="left-label">
        专用费
      </div>
      <div class="left-value flex-te">
        {{ formatMoney(summaryData?.dedicatedFee>10000?summaryData?.dedicatedFee/10000:summaryData?.dedicatedFee) }}
        <span>{{ summaryData?.dedicatedFee>10000?'万元':'元' }}</span>
      </div>
      <AProgress :percent="summaryData?.dedicatedFeeRate||0" />
    </div>
    <div
      class="card-item card-item-fee"
      :style="{width:cardWidth+'px'}"
    >
      <div class="card-item-indirect-fee">
        <div class="card-item-indirect-fee-img" />
        <div class="card-item-indirect-fee-value">
          <div class="left-label">
            间接费
          </div>
          <div class="left-value flex-te">
            {{ formatMoney(summaryData?.indirectFee>10000?summaryData?.indirectFee/10000:summaryData?.indirectFee) }}
            <span>{{ summaryData?.indirectFee>10000?'万元':'元' }}</span>
          </div>
        </div>
      </div>
      <div class="card-item-fee-bottom">
        <template
          v-for="(value, key) in summaryData?.indirectFeeRateMap"
          :key="key"
        >
          <div class="card-item-fee-bottom-val">
            <div class="val-label">
              {{ key }}：
            </div>
            <div class="val-label1">
              {{ value }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, OrionTable, BasicButton, BasicScrollbar,
} from 'lyra-component-vue3';
import { Progress as AProgress } from 'ant-design-vue';
import {
  onMounted, onUnmounted, ref, Ref,
} from 'vue';
import { formatMoney } from '/@/views/pms/projectInitiation/index';
import { Num } from 'windicss/types/lang/tokens';
import MaterialsCharts from './MaterialsCharts.vue';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string
}>(), {
  formId: '',
});
const accountSummaryRef = ref();
const summaryData:Ref<Record<any, any>> = ref({});
const cardWidth:Ref<number> = ref(270);
onMounted(() => {
  getSummary();
  onResize();
  // 监听窗口大小变化
  window.addEventListener('resize', onResize);
});
onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});
function onResize() {
  const tableWidth = accountSummaryRef.value.clientWidth;
  if ((tableWidth - 230) / 5 >= 270) {
    cardWidth.value = (tableWidth - 230) / 5;
  } else if ((tableWidth - 130) / 3 >= 270) {
    cardWidth.value = (tableWidth - 130) / 3;
  } else {
    cardWidth.value = (tableWidth - 80) / 2 >= 270 ? (tableWidth - 80) / 2 : 270;
  }
}
function getSummary() {
  new Api('/pms').fetch('', `projectApprovalEstimate/subject/summary?approvalId=${props.formId}`, 'GET').then((res) => {
    res.dedicatedFeeRate = res.dedicatedFeeRate ? Number(res.dedicatedFeeRate.split('%')[0]) : 0;
    res.materialFeeRate = res.materialFeeRate ? Number(res.materialFeeRate.split('%')[0]) : 0;
    summaryData.value = res;
  });
}
</script>

<style lang="less" scoped>

.account-summary-card-list{
  padding: 20px 10px;
  display: flex;
  gap: 20px 50px;
  flex-wrap: wrap;

  .card-item{
    border: 1px solid #e9e9e9;
    height: 154px;
    width: 270px;
    padding:20px;
    .card-item-bottom{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 25px;
      span{
        font-size: 14px;
      }
      .card-item-bottom-label{
        padding-right: 8px;
        color: #0000006d;
      }
    }
    :deep(.left-label){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
    }
    :deep(.left-value){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      text-align: left;
      line-height: 38px;
      font-size: 30px;

      span{
        font-size: 16px;
      }
    }
    :deep(.left-money){
      font-size: 20px;
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      span{
        font-size: 16px;
      }
    }
    .left-value-parent{
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      .left-value-right{
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
        font-size: 16px;
      }
    }
    .card-item-img{
      background: url('../../img/u9416.png') no-repeat right bottom;
      width: 100%;
      height: 40px;
      margin-top: 16px;
    }
    .ant-progress{
      margin-top: 25px;
    }
    .card-item-indirect-fee{
      display: flex;
      padding: 0 20px 10px 20px;
      border-bottom: 1px solid #e9e9e9;
      .card-item-indirect-fee-img{
        width: 80px;
        background: url('../../img/u9441.png') no-repeat right bottom;
        background-size: 60px 60px;
        margin-right: 20px;
      }
    }
  }
  .card-item-fee{
    padding: 20px 0 !important;
    .card-item-fee-bottom{
      padding: 20px 10px 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .card-item-fee-bottom-val{
        display: flex;
        align-items: center;
        .val-label{
          font-weight: 400;
          font-style: normal;
          color: rgba(0, 0, 0, 0.42745098);
          font-size: 12px;
        }
        .val-label1{
          font-size: 12px;
        }
      }
    }
  }
}
</style>