package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel(value = "ProjectMilestoneViewVO", description = "项目里程碑统计")
public class ProjectMilestoneViewVO {
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "时间")
    private Date beginTime;
    @ApiModelProperty(value = "状态")
    private Integer typeId;
    @ApiModelProperty(value = "数量")
    private String typeName ;
}
