package com.chinasie.orion.util;
import com.chinasie.orion.constant.CommonConstant;
import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/13/23:59
 * @description:
 */
public class StatisticCalculator {
    public static <T extends NodeVO<?>> void calculateStatistics(List<TreeNodeVO<T>> nodes) {
        for (TreeNodeVO<T> node : nodes) {
            calculateStatisticsRecursively(node);
            List<TreeNodeVO<T>> children =  node.getChildren();
            if (children == null || children.isEmpty()) {
                updateNodeStatistics(node);
            }

        }
    }

    public static <T extends NodeVO<?>> void calculateStatisticsRecursively(TreeNodeVO<T> node) {
        List<TreeNodeVO<T>> children =  node.getChildren();
        if (children != null && !children.isEmpty()) {
            for (TreeNodeVO<T> child : node.getChildren()) {
                calculateStatisticsRecursively(child);
                updateParentStatistics(node, child);
            }
        }
    }



    private static <T extends NodeVO<?>> void updateNodeStatistics(TreeNodeVO<T> node) {
        if((Objects.nonNull(node.getData())&&Objects.nonNull(node.getData().getData()))){
            Class<?> clazz = node.getData().getData().getClass();
            if(CollectionUtils.isBlank(node.getBusinessDataList())){
                return;
            }
            for (Field field : clazz.getDeclaredFields()) {
                if (field.isAnnotationPresent(StatisticField.class)) {
                    String fieldName = field.getAnnotation(StatisticField.class).value();
                    StatisticType type = field.getAnnotation(StatisticField.class).type();
                    String[] fields = field.getAnnotation(StatisticField.class).fields();
                    try {
                            // 累加业务数据
                            switch (type) {
                                case SUM:
                                    for (T businessData : node.getBusinessDataList()) {
                                        // 特殊 对于不是协助的才进行统计 业务数据
                                        if(!businessData.getId().startsWith(CommonConstant.IS_ASSIST)){
                                            Object newParentValue = node.getFieldValue(fieldName);
                                            Object businessValue = node.getFieldValueToBusiness(fieldName, businessData);
                                            // 这里会修改node的数据
                                            statisticalCalculationLogic(node, newParentValue, businessValue, fieldName);
                                        }
                                    }
                                    break;
                                case PERCENTAGE:
                                    Object numerator = node.getFieldValue(fields[0]);
                                    Object denominator = node.getFieldValue(fields[1]);
                                    double numeratorValue = Double.parseDouble(numerator.toString());
                                    double denominatorValue = Double.parseDouble(denominator.toString());
                                    if(numeratorValue==0 || denominatorValue==0){
                                        node.setFieldValue(fieldName, 0);
                                    }else{
                                        double percentageValue = numeratorValue/denominatorValue* 100;
                                        node.setFieldValue(fieldName, percentageValue);
                                    }
                                    break;
                                default:
                                    break;
                            }

                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }

                }
            }
        }
    }



    private static <T extends NodeVO<?>> void updateParentStatistics(TreeNodeVO<T> parent, TreeNodeVO<T> child) {
        if((Objects.nonNull(parent.getData())&&Objects.nonNull(parent.getData().getData()))){
            Class<?> clazz = parent.getData().getData().getClass();
            for (Field field : clazz.getDeclaredFields()) {
                if (field.isAnnotationPresent(StatisticField.class)) {
                    String fieldName = field.getAnnotation(StatisticField.class).value();
                    StatisticType type = field.getAnnotation(StatisticField.class).type();
                    String[] fields = field.getAnnotation(StatisticField.class).fields();
                    try {
                        if(null != child.getData()){

                            Object childValue = child.getFieldValue(fieldName);
                            // 累加业务数据
                            List<T> businessDataList = child.getBusinessDataList();
                            switch (type) {
                                case SUM:
                                    if (null != businessDataList && !businessDataList.isEmpty()) {
                                            for (T businessData : child.getBusinessDataList()) {
                                                if(!businessData.getId().startsWith(CommonConstant.IS_ASSIST)) {
                                                    Object newParentValue = child.getFieldValue(fieldName);
                                                    Object businessValue = child.getFieldValueToBusiness(fieldName, businessData);
                                                    // 这里会修改child的数据
                                                    statisticalCalculationLogic(child, newParentValue, businessValue, fieldName);
                                            }
                                        }
                                    }
                                    childValue = child.getFieldValue(fieldName);
                                    Object parentValue = parent.getFieldValue(fieldName);
                                    statisticalCalculationLogic(parent, childValue, parentValue, fieldName);
                                    break;
                                case PERCENTAGE:
                                    Object numerator = parent.getFieldValue(fields[0]);
                                    Object denominator = parent.getFieldValue(fields[1]);
                                    double numeratorValue = Double.parseDouble(numerator.toString());
                                    double denominatorValue = Double.parseDouble(denominator.toString());
                                    if(numeratorValue==0 || denominatorValue==0){
                                        parent.setFieldValue(fieldName, 0);
                                    }else{
                                        //保留小数2位 四舍五入
                                        DecimalFormat df = new DecimalFormat("#.00");
                                        double percentageValue = numeratorValue/denominatorValue* 100;
                                        double roundedPercentageValue = Double.parseDouble(df.format(percentageValue));
                                        parent.setFieldValue(fieldName, roundedPercentageValue);
//                                        if (numerator instanceof Double && denominator instanceof Double  ) {
//                                            double percentageValue = numeratorValue/denominatorValue* 100;
//                                            parent.setFieldValue(fieldName, percentageValue);
//                                        }else if (numerator instanceof BigDecimal && denominator instanceof BigDecimal  ) {
//                                            double percentageValue = numeratorValue/denominatorValue* 100;
//                                            parent.setFieldValue(fieldName, percentageValue);
//                                        }else if (numerator instanceof Number && denominator instanceof Number  ) {
//                                            double percentageValue = numeratorValue/denominatorValue* 100;
//                                            parent.setFieldValue(fieldName, percentageValue);
//                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }

                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }

                }
            }
        }
    }


    private static <T extends NodeVO<?>> void statisticalCalculationLogic(TreeNodeVO<T> node, Object value, Object newValue1, String fieldName) throws IllegalAccessException {
        if (value instanceof Integer && newValue1 instanceof Integer) {
            Integer newBusinessValue = (Integer) newValue1 + (Integer) value;
            node.setFieldValue(fieldName, newBusinessValue);
        } else if (value instanceof Double && newValue1 instanceof Double) {
            Double newValue = (Double) newValue1 + (Double) value;
            node.setFieldValue(fieldName, newValue);
        } else if (value instanceof BigDecimal && newValue1 instanceof BigDecimal) {
            BigDecimal newValue = BigDecimal.valueOf(((BigDecimal) newValue1).doubleValue() + ((BigDecimal) value).doubleValue());
            node.setFieldValue(fieldName, newValue);
        }
    }

}
