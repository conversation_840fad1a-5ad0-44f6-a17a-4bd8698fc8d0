package com.chinasie.orion.service.impl;





import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.PersonJobPostAuthorizeDTO;
import com.chinasie.orion.domain.entity.PersonTrainEquRecord;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.dto.PersonTrainInfoRecordDTO;
import com.chinasie.orion.domain.vo.PersonJobPostEquVO;
import com.chinasie.orion.domain.vo.PersonTrainEquRecordVO;
import com.chinasie.orion.domain.vo.PersonTrainInfoRecordVO;


import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.service.PersonTrainEquRecordService;
import com.chinasie.orion.service.PersonTrainInfoRecordService;
import com.chinasie.orion.repository.PersonTrainInfoRecordMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PersonTrainInfoRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
@Service
@Slf4j
public class PersonTrainInfoRecordServiceImpl extends  OrionBaseServiceImpl<PersonTrainInfoRecordMapper, PersonTrainInfoRecord>   implements PersonTrainInfoRecordService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    private PersonTrainEquRecordService personTrainEquRecordService;
    @Autowired
    private FileApiService fileApiService;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PersonTrainInfoRecordVO detail(String id,String pageCode) throws Exception {
        PersonTrainInfoRecord personTrainInfoRecord =this.getById(id);
        PersonTrainInfoRecordVO result = BeanCopyUtils.convertTo(personTrainInfoRecord,PersonTrainInfoRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param personTrainInfoRecordDTO
     */
    @Override
    public  String create(PersonTrainInfoRecordDTO personTrainInfoRecordDTO) throws Exception {
        PersonTrainInfoRecord personTrainInfoRecord =BeanCopyUtils.convertTo(personTrainInfoRecordDTO,PersonTrainInfoRecord::new);
        this.save(personTrainInfoRecord);

        String rsp=personTrainInfoRecord.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personTrainInfoRecordDTO
     */
    @Override
    public Boolean edit(PersonTrainInfoRecordDTO personTrainInfoRecordDTO) throws Exception {
        PersonTrainInfoRecord personTrainInfoRecord =BeanCopyUtils.convertTo(personTrainInfoRecordDTO,PersonTrainInfoRecord::new);

        this.updateById(personTrainInfoRecord);

        String rsp=personTrainInfoRecord.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonTrainInfoRecordVO> pages( Page<PersonTrainInfoRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonTrainInfoRecord> condition = new LambdaQueryWrapperX<>( PersonTrainInfoRecord. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonTrainInfoRecord::getCreateTime);
        PersonTrainInfoRecordDTO postAuthorizeDTO=   pageRequest.getQuery();
        if(null != postAuthorizeDTO && StringUtils.hasText(postAuthorizeDTO.getUserCode())){
            condition.eq(PersonTrainInfoRecord::getUserCode,postAuthorizeDTO.getUserCode());
        }

        Page<PersonTrainInfoRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonTrainInfoRecord::new));

        PageResult<PersonTrainInfoRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonTrainInfoRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonTrainInfoRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonTrainInfoRecordVO::new);
        if(CollectionUtils.isEmpty(vos)){
            pageResult.setContent(vos);
            return pageResult;
        }

        setEveryName(vos);

        Map<String,List<PersonTrainEquRecordVO>> map = new HashMap<>();
        if(!Objects.isNull(postAuthorizeDTO)){
            List<PersonTrainEquRecordVO> equList=personTrainEquRecordService.listByUserCode(postAuthorizeDTO.getUserCode());
            if(!CollectionUtils.isEmpty(equList)){
                map=   equList.stream().collect(Collectors.groupingBy(item->String.format("%s_%s",item.getUserCode()
                        ,item.getFormTrainNumber())));
            }
        }
        Map<String, List<PersonTrainEquRecordVO>> finalMap = map;
        vos.forEach(item->{
            String key =String.format("%s_%s",item.getUserCode(),item.getId());
            List<PersonTrainEquRecordVO> personJobPostEquList = finalMap.getOrDefault(key, new ArrayList<>());
            item.setEquRecordVOList(personJobPostEquList);
        });

        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "用户培训信息落地导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonTrainInfoRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            PersonTrainInfoRecordExcelListener excelReadListener = new PersonTrainInfoRecordExcelListener();
        EasyExcel.read(inputStream,PersonTrainInfoRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonTrainInfoRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("用户培训信息落地导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PersonTrainInfoRecord> personTrainInfoRecordes =BeanCopyUtils.convertListTo(dtoS,PersonTrainInfoRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PersonTrainInfoRecord-import::id", importId, personTrainInfoRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonTrainInfoRecord> personTrainInfoRecordes = (List<PersonTrainInfoRecord>) orionJ2CacheService.get("pmsx::PersonTrainInfoRecord-import::id", importId);
        log.info("用户培训信息落地导入的入库数据={}", JSONUtil.toJsonStr(personTrainInfoRecordes));

        this.saveBatch(personTrainInfoRecordes);
        orionJ2CacheService.delete("pmsx::PersonTrainInfoRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PersonTrainInfoRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonTrainInfoRecord> condition = new LambdaQueryWrapperX<>( PersonTrainInfoRecord. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PersonTrainInfoRecord::getCreateTime);
        List<PersonTrainInfoRecord> personTrainInfoRecordes =   this.list(condition);

        List<PersonTrainInfoRecordDTO> dtos = BeanCopyUtils.convertListTo(personTrainInfoRecordes, PersonTrainInfoRecordDTO::new);

        String fileName = "用户培训信息落地数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonTrainInfoRecordDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PersonTrainInfoRecordVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<PersonTrainInfoRecord> listByUserCodeAndBaseCode(String userCode, String basePlaceCode, String keyWord) {
        if(StrUtil.isEmpty(userCode)){
            return  new ArrayList<>();
        }
        LambdaQueryWrapperX<PersonTrainInfoRecord> condition = new LambdaQueryWrapperX<>( PersonTrainInfoRecord. class);
        condition.eq(PersonTrainInfoRecord::getUserCode,userCode);
        if(StringUtils.hasText(basePlaceCode)){
            condition.ne(PersonTrainInfoRecord::getBaseCode,basePlaceCode);
        }
        if (StringUtils.hasText(keyWord)){
            condition.like(PersonTrainInfoRecord::getTrainName, keyWord);
        }
        return this.list(condition);
    }

    @Override
    public List<PersonTrainInfoRecord> listByUserCodeList(List<String> userCodeList) {
        LambdaQueryWrapperX<PersonTrainInfoRecord> condition = new LambdaQueryWrapperX<>( PersonTrainInfoRecord. class);
        condition.in(PersonTrainInfoRecord::getUserCode,userCodeList);
        return this.list(condition);
    }

    @Override
    public List<PersonTrainInfoRecordVO> listByEntity(PersonTrainInfoRecordDTO postAuthorizeDTO) throws Exception {
        LambdaQueryWrapperX<PersonTrainInfoRecord> condition = new LambdaQueryWrapperX<>( PersonTrainInfoRecord. class);

        if(null != postAuthorizeDTO && StringUtils.hasText(postAuthorizeDTO.getUserCode())){
            condition.eq(PersonTrainInfoRecord::getUserCode,postAuthorizeDTO.getUserCode());
        }
        condition.select(PersonTrainInfoRecord::getId,PersonTrainInfoRecord::getTrainName,PersonTrainInfoRecord::getTrainNumber
                ,PersonTrainInfoRecord::getTrainLecturer,PersonTrainInfoRecord::getEndDate,PersonTrainInfoRecord::getExpireTime
                ,PersonTrainInfoRecord::getLessonHour,PersonTrainInfoRecord::getIsEquivalent,PersonTrainInfoRecord::getBaseCode
                ,PersonTrainInfoRecord::getBaseName);
        List<PersonTrainInfoRecord> personTrainInfoRecordList= this.list(condition);
        if(CollectionUtils.isEmpty(personTrainInfoRecordList)){
            return  new ArrayList<>();
        }
        List<PersonTrainInfoRecordVO> vos =BeanCopyUtils.convertListTo(personTrainInfoRecordList, PersonTrainInfoRecordVO::new);
        setEveryName(vos);
        Map<String,List<PersonTrainEquRecordVO>> map = new HashMap<>();
        if(!Objects.isNull(postAuthorizeDTO)){
            List<PersonTrainEquRecordVO> equList=personTrainEquRecordService.listByUserCode(postAuthorizeDTO.getUserCode());
            if(!CollectionUtils.isEmpty(equList)){
                map=   equList.stream().collect(Collectors.groupingBy(item->String.format("%s_%s",item.getUserCode()
                        ,item.getFormTrainNumber())));
            }
        }
        Map<String, List<PersonTrainEquRecordVO>> finalMap = map;
        vos.forEach(item->{
            String key =String.format("%s_%s",item.getUserCode(),item.getId());
            List<PersonTrainEquRecordVO> personJobPostEquList = finalMap.getOrDefault(key, new ArrayList<>());
            item.setEquRecordVOList(personJobPostEquList);
        });
        return vos;
    }


    public static class PersonTrainInfoRecordExcelListener extends AnalysisEventListener<PersonTrainInfoRecordDTO> {

        private final List<PersonTrainInfoRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonTrainInfoRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonTrainInfoRecordDTO> getData() {
            return data;
        }
    }


}
