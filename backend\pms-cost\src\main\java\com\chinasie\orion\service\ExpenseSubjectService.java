package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import java.util.Map;

import com.chinasie.orion.domain.dto.ExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.CostCenter;
import com.chinasie.orion.domain.entity.ExpenseSubject;
import com.chinasie.orion.domain.vo.ExpenseSubjectVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ExpenseSubject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
public interface ExpenseSubjectService extends OrionBaseService<ExpenseSubject> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ExpenseSubjectVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param expenseSubjectDTO
     */
    ExpenseSubjectVO create(ExpenseSubjectDTO expenseSubjectDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param expenseSubjectDTO
     */
    Boolean edit(ExpenseSubjectDTO expenseSubjectDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean removeById(String id) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ExpenseSubjectVO> pages(Page<ExpenseSubjectDTO> pageRequest) throws Exception;

    /**
     * 禁用
     *
     * @param id
     * @return
     */
    Boolean ban(String id) throws Exception;

    /**
     * 启用
     *
     * @param id
     * @return
     */
    Boolean use(String id) throws Exception;


    /**
     * 启用
     *
     * @param searchText,status
     * @return
     */
    List<ExpenseSubjectVO> tree(String searchText, Integer status) throws Exception;

    /**
     * 通过IDs查询费用科目信息
     *
     * @param expenseSubjectIds 费用科目IDS
     * @return map
     * @throws Exception e
     */
    Map<String, ExpenseSubject> getExpenseSubjectMap(List<String> expenseSubjectIds) throws Exception;

    Map<String,List<String>> getFirstFloor();

    List<ExpenseSubjectVO>  getFirstExpenseSubject();

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ExpenseSubjectVO> treePages(Page<ExpenseSubjectDTO> pageRequest) throws Exception;

}

