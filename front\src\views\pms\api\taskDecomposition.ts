import Api from '/@/api';

/**
 * 列表树
 * @param mainTableId 参数
 */
export const listTree = (mainTableId) => new Api(`/pms/taskDecomposition/list/tree/${mainTableId}`).fetch('', '', 'POST');
/**
 * 删除
 * @param params 参数
 */
export const remove = (params) => new Api('/pms/taskDecomposition/remove').fetch(params, '', 'DELETE');
/**
 * 删除
 * @param id 参数
 */
export const removeById = (id) => new Api(`/pms/taskDecomposition/${id}`).fetch('', '', 'DELETE');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/pms/taskDecomposition/edit').fetch(params, '', 'PUT');
