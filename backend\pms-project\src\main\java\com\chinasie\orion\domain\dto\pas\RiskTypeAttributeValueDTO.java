package com.chinasie.orion.domain.dto.pas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * RiskTypeAttributeValueDTO 风险类型属性值DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-10-13
 */
@Data
@ApiModel(value = "RiskTypeAttributeValueDTO对象", description = "风险类型属性值")
public class RiskTypeAttributeValueDTO implements Serializable {
    /**
     * 分类主键
     */
    @ApiModelProperty(value = "分类主键")
    private String typeId;

    /**
     * 属性主键
     */
    @ApiModelProperty(value = "属性主键")
    private String attributeId;

    /**
     * 需求主键
     */
    @ApiModelProperty(value = "需求主键")
    private String riskId;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String value;


    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String name;
}
