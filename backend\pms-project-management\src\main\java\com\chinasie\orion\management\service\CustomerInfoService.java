package com.chinasie.orion.management.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.management.domain.dto.CustomerInfoDTO;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.vo.CustomerInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * customerInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 16:25:24
 */
public interface CustomerInfoService extends OrionBaseService<CustomerInfo> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    CustomerInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param customerInfoDTO
     */
    String create(CustomerInfoDTO customerInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param customerInfoDTO
     */
    Boolean edit(CustomerInfoDTO customerInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<CustomerInfoVO> pages(Page<CustomerInfoDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<String> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CustomerInfoVO> vos) throws Exception;

    String open(String id, String pageCode) throws Exception;

    String close(String id, String pageCode) throws Exception;

    Page<CustomerInfoVO> getIncomeCustomerInfoPages(Page<CustomerInfoDTO> pageRequest) throws Exception;
}
