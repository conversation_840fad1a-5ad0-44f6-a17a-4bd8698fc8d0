package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * AuthorPersonJobPostEqu DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:34
 */
@ApiModel(value = "AuthorPersonJobPostEquDTO对象", description = "作业人员岗位授权等效")
@Data
@ExcelIgnoreUnannotated
public class AuthorPersonJobPostEquDTO extends  ObjectDTO   implements Serializable{

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    @ExcelProperty(value = "人员ID ", index = 0)
    private String personId;

    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员编号/工号")
    @ExcelProperty(value = "人员编号/工号 ", index = 1)
    private String userCode;

    /**
     * 历史岗位授权ID
     */
    @ApiModelProperty(value = "历史岗位授权ID：落地的授权岗位ID")
    @ExcelProperty(value = "历史岗位授权ID：落地的授权岗位ID", index = 2)
    private String historyAuthorId;

    /**
     * 等效的现有授权ID
     */
    @ApiModelProperty(value = "等效的现有授权ID")
    @ExcelProperty(value = "等效的现有授权ID ", index = 3)
    private String authorId;

    /**
     * 授权管理ID
     */
    @ApiModelProperty(value = "授权管理ID")
    @ExcelProperty(value = "授权管理ID ", index = 4)
    private String authorManageId;

    /**
     * 作业岗位编码
     */
    @ApiModelProperty(value = "作业岗位编码")
    private String jobPostCode;

}
