package com.chinasie.orion.domain.vo.projectOverview;

import com.chinasie.orion.domain.vo.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/9:27
 * @description:
 */
@Data
@ApiModel(value = "ProjectOverviewVo对象", description = "项目详情")
public class ProjectOverviewVo extends ObjectVO {

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态id")
    private String statusId;

    /**
     * 项目状态名称
     */
    @ApiModelProperty(value = "项目状态名称")
    private String statusIdName;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 项目立项时间
     */
    @ApiModelProperty(value = "项目立项时间")
    private Date projectApproveTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date projectStartTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Double schedule;
    private String scheduleName;


    @ApiModelProperty(value = "项目经理名称")
    private String pm;

    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount=0;
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount=0;
    @ApiModelProperty(value = "进行中数量")
    private Integer runningCount=0;

    /**
     * 是否需要申报
     */
    @ApiModelProperty(value = "是否需要申报")
    private Boolean isDeclare;


    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    @ApiModelProperty(value = "项目子类型名称")
    private String projectSubTypeName;

//    @ApiModelProperty("计划列表")
//    private List<SimpleVO> schemeList = new ArrayList<>();
}
