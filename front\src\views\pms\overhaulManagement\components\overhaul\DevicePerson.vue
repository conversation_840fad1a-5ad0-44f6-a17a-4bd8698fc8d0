<script setup lang="ts">
import { openModal } from 'lyra-component-vue3';
import { Progress } from 'ant-design-vue';
import { get as _get } from 'lodash-es';
import {
  computed, CSSProperties, h, inject, onMounted, onUnmounted, reactive, ref, Ref, unref,
} from 'vue';
import { addResizeListener, removeResizeListener } from '/@/utils/event';
import TableCalendar from '/@/views/pms/overhaulManagement/components/TableCalendar/TableCalendar.vue';

const props = withDefaults(defineProps<{
  height?: string | number
  data: Record<string, any>
  personData:string | number
  materialData:string | number
}>(), {
  height: '240px',
});



const repairRound = inject('repairRound');

const containerRef: Ref = ref();
onMounted(() => {
  addResizeListener(containerRef.value, updateProgressWidth);
});

onUnmounted(() => {
  removeResizeListener(containerRef.value, updateProgressWidth);
});

const progressWidth: Ref<number> = ref(120);
const formatValueStyle: CSSProperties = reactive({
  fontSize: '18px',
  fontWeight: 500,
});
const formatLabelStyle: CSSProperties = reactive({
  fontSize: '14px',
  marginTop: '8px',
});

function updateProgressWidth() {
  if (!containerRef.value) return;
  const domWidth: number = Math.ceil((containerRef.value.offsetWidth - 120) / 4);
  if (domWidth > 170) {
    progressWidth.value = 170;
  } else if (domWidth < 100) {
    progressWidth.value = 100;
  } else {
    progressWidth.value = domWidth;
  }
}

const progressData = computed<Record<string, any>[]>(() => [
  {
    percent: 66,
    total: 0,
    strokeColor: '#6AD18E',
    class: 'rotate',
    key: '1',
    type: 'personOverlap',
    field: ['data', 'personOverlap'],
  },
  {
    percent: 0,
    total: 0,
    strokeColor: '#B1E6C7',
    field: [
      'data',
      'person',
      'actualJoinRatio',
    ],
    key: '2',
  },
  {
    percent: 66,
    total: 0,
    strokeColor: '#F39988',
    class: 'rotate',
    key: '3',
    type: 'materialOverlap',
    field: ['data', 'materialOverlap'],
  },
  {
    percent: 0,
    total: 0,
    strokeColor: '#FFC4BF',
    field: [
      'data',
      'material',
      'actualJoinRatio',
    ],
    key: '4',
  },
].map((item) => {
  if (item.field && !item.type) {
    item.percent = Math.floor(_get(props, item.field, 0) * 100);
    item.total = Number((_get(props, item.field, 0) * 100)?.toFixed(2) || 0);
  } else {
    item.total = Number(_get(props, item.field, 0)?.toFixed(2) || 0);
  }
  return item;
}));

function format(percent: number, key: string) {
  let value: string | number = 0;
  let label: string = '';
  switch (key) {
    case '1':
      value = percent;
      label = '人员重叠(天)';
      break;
    case '2':
      value = `${percent}%`;
      label = '人员入场率';
      break;
    case '3':
      value = percent;
      label = '设备重叠(天)';
      break;
    case '4':
      value = `${percent}%`;
      label = '设备入场率';
      break;
  }
  return h('div', { class: 'flex flex-ver flex-pac' }, {
    default: () => [h('span', { style: formatValueStyle }, value), h('span', { style: formatLabelStyle }, label)],
  });
}

function handleProgress(type: string) {
  if (type) {
    openModal({
      title: '重叠度',
      width: 1200,
      height: 700,
      content() {
        return h(TableCalendar, {
          type,
          repairRound: unref(repairRound),
          style: { padding: '12px 20px' },
        });
      },
      footer: {
        isOk: false,
        // @ts-ignore
        cancelText: '关闭',
      },
    });
  }
}
</script>

<template>
  <div
    ref="containerRef"
    class="chart-container"
    :style="{height:(typeof height==='number'?height+'px':height)}"
  >
    <Progress
      v-for="(item,index) in progressData"
      :key="index"
      stroke-linecap="square"
      :percent="item.percent"
      type="circle"
      :class="item.class"
      :strokeColor="item.strokeColor"
      :format="()=>format(item.total,item.key)"
      :width="progressWidth"
      :style="{transform:`rotate(${item.rotate||0})`}"
      @click="handleProgress(item.type)"
    />
  </div>
</template>

<style scoped lang="less">
.chart-container {
  width: 100%;
  box-shadow: 0 0 5px 0 #eee;
  border-radius: 4px;
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  justify-items: center;
  align-items: center;
}

:deep(.rotate) {
  .ant-progress-circle {
    transform: rotateZ(-120deg);
  }
}
</style>
