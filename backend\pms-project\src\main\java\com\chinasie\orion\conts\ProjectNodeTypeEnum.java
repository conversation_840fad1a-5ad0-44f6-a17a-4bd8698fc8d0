package com.chinasie.orion.conts;

/**
 * @author: yk
 * @date: 2023/10/21 11:13
 * @description:
 */
public enum ProjectNodeTypeEnum {
    PLAN("plan", "计划"),
    MILESTONE("milestone", "里程碑"),
    ;

    private String code;

    private String description;

    ProjectNodeTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
