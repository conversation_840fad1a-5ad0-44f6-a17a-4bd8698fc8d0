package com.chinasie.orion.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * ProjectMaterialPreparationInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:53:32
 */
@ApiModel(value = "ProjectMaterialPreparationInfoVO对象", description = "备料信息")
@Data
public class ProjectMaterialPreparationInfoVO extends  ObjectVO   implements Serializable{

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private String taskType;


    @ApiModelProperty(value = "任务类型名称")
    private String taskTypeName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialNumber;


    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer num;


    /**
     * 替代料组
     */
    @ApiModelProperty(value = "替代料组")
    private Integer replaceGroup;


    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    private Date requireCompleteTime;


    /**
     * 最短采购周期
     */
    @ApiModelProperty(value = "最短采购周期")
    private BigDecimal minProcurementCycle;


    /**
     * 标准采购周期
     */
    @ApiModelProperty(value = "标准采购周期")
    private BigDecimal procurementCycle;


    /**
     * 备料id
     */
    @ApiModelProperty(value = "备料id")
    private String preparationId;



    /**
     * 源
     */
    @ApiModelProperty(value = "源")
    private String source;


    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String basicUnit;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String materialName;


    /**
     * 上版本数量
     */
    @ApiModelProperty(value = "上版本数量")
    private Integer preRevNum;

    /**
     * 差异
     */
    @ApiModelProperty(value = "差异")
    private String different;

    /**
     * 差异原因
     */
    @ApiModelProperty(value = "差异原因")
    private String diffReason;

}
