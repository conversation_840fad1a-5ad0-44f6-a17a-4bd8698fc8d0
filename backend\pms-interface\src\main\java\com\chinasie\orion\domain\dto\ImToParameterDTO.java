package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.lang.String;

/**
 * ImToParameter DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@ApiModel(value = "ImToParameterDTO对象", description = "接口和参数的关系")
@Data
public class ImToParameterDTO extends ObjectDTO implements Serializable {

    /**
     * 接口ID
     */
    @ApiModelProperty(value = "接口ID")
    private String imId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    private String paramId;

    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
    @NotEmpty(message = "模板ID能为空")
    private String modelId;

}
