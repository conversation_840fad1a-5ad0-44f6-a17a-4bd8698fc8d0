package com.chinasie.orion.domain.dto.review.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ReviewOpinion DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class ReviewOpinionTplDTO implements Serializable {

    /**
     * 提出人
     */
    @ExcelProperty(value = "提出人", index = 0)
    private String presentedUser;

    /**
     * 意见/建议
     */
    @ExcelProperty(value = "意见/建议 ", index = 1)
    private String opinion;
}
