<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_table">
          <BasicUpload
            :max-number="100"
            :max-size="10"

            :show-up-modal="isShow"
            :is-button="false"
            :pagination="'false'"
            @uploadSuccessChange="uploadSuccessChange"
            @saveChange="saveChange"
          />
          <BasicTable
            class="pdmBasicTable"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :columns="columns"
            :data-source="dataSource"
            :bordered="false"
            :can-resize="true"
            :pagination="false"
            :show-index-column="false"
            :max-height="tableHeight"
            row-key="id"
            @change="handleChange"
            @rowClick="clickRow"
          >
            <template #modifyTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />

      <!-- 简易弹窗提醒 -->
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <SearchModal
        @register="searchRegister"
        @searchEmit="searchEmit"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import SearchModal from './SearchModal.vue';
import {
  computed, defineComponent, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
// import { BasicUpload } from '/@/components/BasicUpload';
// import { BasicUpload } from './BasicUpload';
import {
  BasicTable, BasicUpload, isPower, Layout, openFile, useDrawer,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
/* 格式化时间 */
// import { formatterTime } from '/@/utils/time';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
/* table */
import dayjs from 'dayjs';
import { columns } from './src/table.config';
import { getListDetailsApi, removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import { upDocApi } from '/@/views/pms/projectLaborer/api/docManagement';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    /* 表格 */
    BasicTable,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    newButtonModal,
    SearchModal,
    BasicUpload,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    //   const router = useRouter();
    //   const layoutModelStore = layoutModel();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();

    const state = reactive({
      fileId: undefined,
      searchvlaue: '',
      editdataSource: {},
      selectedRowKeys: [],
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],
        query: {
          projectId: '',
        },
        queryCondition: [],
      },
      pageSize: 10,
      current: 1,
      total: 20,
      addNodeModalData: {},
      selectedRows: [],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
      powerData: [],
    });
    let projectId: any = inject('projectId');
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        open: { show: computed(() => isPower('XQ_container_button_08', state.powerData)) },
        upload: { show: computed(() => isPower('XQ_container_button_09', state.powerData)) },
        download: { show: computed(() => isPower('XQ_container_button_10', state.powerData)) },
        delete: { show: computed(() => isPower('XQ_container_button_13', state.powerData)) },
        search: { show: computed(() => isPower('XQ_container_button_14', state.powerData)) },
        // import: { show: true },
        // export: { show: true },
      },
    });
    function saveChange(successAll) {
      // console.log('点击弹窗保存或单文件上传的返回', successAll);
      const fileArr = successAll.map((item) => ({
        filePath: item.result.filePath,
        filePostfix: item.result.filePostfix,
        fileSize: item.result.fileSize,
        name: item.result.name,
        dataId: demandItemId.value,
        fileTool: item.openTool,
        secretLevel: item.result.secretLevel,
        securityLimit: item.securityLimit,
        projectId: projectId.value,
      }));
      const love = {
        className: 'Plan',
        moduleName: '项目管理-需求管理-关联内容-关联文档',
        type: 'UPLOAD',
        remark: `上传了【${fileArr.map((item) => item?.name)}】`,
      };
      upDocApi(fileArr, love).then(() => {
        message.success('上传成功');
        getFormData();
      });
    }
    /* 分页 */
    //   const pagination = computed(() => ({
    //     pageSize: state.tablehttp.pageSize,
    //     current: state.tablehttp.pageNum,
    //     total: state.tablehttp.total,
    //     // showQuickJumper: true,
    //     showSizeChanger: true,
    //     showTotal: (total) => {
    //       return '共' + total + '条';
    //     }
    //   }));
    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      if (sorter) {
        state.tablehttp.orders[0].asc = sorter.order === 'ascend';
        state.tablehttp.orders[0].column = sorter.columnKey;
        getFormData();
      }
      // 如果是多选触发,则不更新页面
      // if (typeof pag.current == 'undefined') return;
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'open':
          if (lengthCheckHandle()) return;
          openFile(state.selectedRows[0]);
          break;
        case 'upload':
          uploadshow();
          break;
        case 'download':
          downLoadFilePath();
          break;
        case 'delete':
          multiDelete();
          break;
        case 'search':
          openSearchDrawer(true);
          break;
      }
    };
    const router = useRouter();
    const isShow = ref(false);
    /* 上传 */
    const uploadshow = () => {
      isShow.value = !isShow.value;
    };
    /* 下载 */
    function downLoadFilePath() {
      if (lengthCheckHandle()) return;
      downLoadById(state.selectedRowKeys);
    }
    /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 440;
      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      const love = {
        className: 'Plan',
        moduleName: '项目管理-需求管理-关联内容-关联文档',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      removeBatchDetailsApi(state.selectedRowKeys, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    let demandItemId: any = inject('demandItemId');

    const getFormData = async (obj = {}) => {
      state.tablehttp.query.projectId = demandItemId.value;
      // const res = await getListDetailsApi(state.tablehttp);
      const love = {
        id: demandItemId.value,
        className: 'DemandManagement',
        moduleName: '项目管理-需求管理-关联内容-关联文档', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${demandItemId.value}】`,
      };
      const res = await getListDetailsApi(demandItemId.value, love, obj);
      state.dataSource = res;
      // state.tablehttp.total = res.totalSize;
    };
    function searchEmit(data) {
      getFormData(data);
    }
    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.nodeData = {
        //   ...state.dataSource.filter((item) => {
        //     return item.id == state.selectedRowKeys[0];
        //   })
        ...state.selectedRows,
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;
      getFormData();
      state.searchvlaue = '';
    };
      /* 打开按钮 */
    const openDetail = () => {
      if (lengthCheckHandle()) return;

      toDetails(state.selectedRows[0]);
      state.searchvlaue = '';
    };
    const toDetails = (data) => {
      router.push({
        name: 'EndDetails',
        query: {
          id: data.id,
          type: 1,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];

      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickRow,
      clickType,
      columns,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      saveChange,
      isShow,
      isPower,
      searchRegister,
      searchEmit,
    };
  },

  // mounted() {}
});
</script>
<style lang="less" scoped>
  //   :deep(.pdmBasicTable) {
  //     .ant-table-wrapper {
  //       padding: 0;
  //     }
  //   }
  //   .checkDetailsMessage {
  //     margin: 0 15px 0 8px;
  //   }

  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
