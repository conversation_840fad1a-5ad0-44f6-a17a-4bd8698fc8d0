<template>
  <div class="searchModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <div class="search_title mb15">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入系统角色名字"
          size="large"
          @search="searchData"
        />
      </div>
      <div>
        <div class="rowItem">
          <div :style="{ padding: '0 0 0 12px', color: '#666', fontSize: '12px' }">
            共搜索出{{ systemRole.count || 0 }}项结果
          </div>
          <a-menu
            v-model:selectedKeys="selectedKeys"
            :style="{
              width: '96%',
              margin: '10px',
              borderRightWidth: 0,
              overflowX: 'auto',
              height: contentHeight - 520 + 'px'
            }"
            mode="vertical"
            multiple="true"
          >
            <a-menu-item
              v-for="item in systemRole.systemRoleVoList"
              :key="item.id"
              :item-value="item"
              :style="{ height: '100px', borderBottom: '1px dotted #ccc', margin: 0 }"
            >
              <div
                class="avatar"
                :style="{ height: '100px' }"
              >
                <div class="left">
                  <a-avatar
                    v-if="false"
                    :style="{ width: '65px', height: '65px' }"
                    shape="square"
                  />
                  <a-avatar
                    v-else
                    shape="square"
                    style="
                      width: 65px;
                      height: 65px;
                      text-align: center;
                      line-height: 60px;
                      font-size: 20px;
                    "
                  >
                    角色
                  </a-avatar>
                </div>

                <div class="right">
                  <h3 class="rightitem">
                    {{ item.name }}
                  </h3>
                  <div class="rightitem rightitem2">
                    编号:{{ item.code }}
                  </div>
                  <div class="rightitem rightitem2">
                    修改人:{{ item.modifyName }}
                  </div>
                </div>
              </div>
            </a-menu-item>
          </a-menu>
        </div>
        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, inject,
} from 'vue';
import {
  message, Drawer, Input, Menu, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { addAllRoleApi, getSystemRoleApi } from '/@/views/pms/projectLaborer/api/projectList';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aInputSearch: Input.Search,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    AButton: Button,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    let provideProjectId: any = inject('provideProjectId');
    const state = reactive({
      visible: false,
      title: '从系统添加角色',
      nameValue: '',
      selectedKeys: [],
      openKeys: [],
      systemRole: <any>{},
      loading: false,
    });
    watch(
      () => props.data,
      () => (state.visible = true),
    );
    /* 重置数据 */
    const resetData = () => {
      state.nameValue = '';
      state.selectedKeys = [];
      state.systemRole = {};
    };
      /* x按钮 */
    const x = () => resetData();
    /* 取消 */
    const close = () => {
      state.visible = false;
      resetData();
    };
      /* 模糊搜索系统角色 */
    const searchData = async (e) => {
      await getSystemRoleApi(e).then((res) => {
        state.systemRole = res;
        state.selectedKeys = [];
      });
    };
    const onSubmit = () => {
      if (state.selectedKeys.length === 0) {
        state.visible = false;
        return;
      }
      const systemRoleApiValue = [];
      state.selectedKeys.forEach((item) => {
        const iditem = state.systemRole.systemRoleVoList.filter((element) => item == element.id);
        if (iditem) {
          systemRoleApiValue.push(iditem);
        }
      });
      const addAllRole = systemRoleApiValue.map((value) => ({
        businessId: value[0].id,
        name: value[0].name,
        projectId: provideProjectId.value,
      }));
      state.loading = true;
      const love = {
        className: 'ProjectRole',
        moduleName: '项目管理-项目设置-项目角色',
        type: 'SAVE',
        remark: `新增了【${addAllRole.map((item) => item?.name)}】`,
      };
      addAllRoleApi(addAllRole, love)
        .then(() => {
          state.visible = false;
          resetData();
          emit('success');
          message.success('添加成功');
        })
        .catch(() => {
          state.visible = false;
        }).finally(() => {
          state.loading = false;
        });
    };
    const contentHeight = ref(0);
    onMounted(() => {
      contentHeight.value = document.body.clientHeight + 250;
    });

    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
      contentHeight,
    };
  },
});
</script>
<style lang="less" scoped>
  .searchModalDrawer {
    .ant-drawer-body {
      padding: 60px 0px 80px 0px !important;
    }
    .search_title {
      padding: 10px 0px;
      border-bottom: 1px solid #d2d7e1;
      text-align: center;
      margin-bottom: 10px;
      .ant-input-search {
        width: 310px;
      }
    }
    .basicTitle {
      padding: 0px 15px;
    }
    .rowItem {
      margin-bottom: 100px;
      //   background: red;
      //   height: 300px;
      .rowItem_label {
        padding-left: 5px;
        color: #444b5e;
      }
      .ant-select {
        width: 100%;
      }
    }

    //.nodeItemBtn {
    //  position: fixed;
    //  bottom: 0px;
    //  padding: 20px 0px;
    //  text-align: center;
    //  width: 310px;
    //  height: 80px;
    //  background: #ffffff;
    //  margin-bottom: 0px;
    //  text-align: center;
    //}
    //
    //.cancelBtn {
    //  color: #5172dc;
    //  background: #5172dc19;
    //  width: 120px;
    //  border-radius: 4px;
    //}
    //.bgDC {
    //  width: 120px;
    //  margin-left: 15px;
    //  border-radius: 4px;
    //}
  }
  .nodeItemBtn {
    position: fixed;
    bottom: 0px;
    padding: 20px 0;
    text-align: center;
    width: 280px;
    height: 80px;
    background: #ffffff;
    margin-bottom: 0px;
  }
  .cancelBtn {
    color: #5172dc;
    background: #5172dc19;
    width: 110px;
    border-radius: 4px;
  }
  .bgDC {
    width: 110px;
    margin-left: 15px;
    border-radius: 4px;
  }
  .ant-menu {
    /deep/li:hover {
      background: #eef1fc;
    }
  }
  .avatar {
    display: flex;
    align-items: center;
    .left {
      width: 60px;
      height: 60px;
    }
    .right {
      font-size: 16px;
      padding-left: 15px;
      .rightitem1 {
        font-size: 18px;
      }
      .rightitem {
        margin-bottom: 0;
      }
      .rightitem2 {
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #666666;
      }
    }
  }
</style>
