<template>
  <Layout2
    :options="{ body: { scroll: true } }"
    class="achievement"
    :contentTitle="contentTitle"
  >
    <template #left>
      <AchievementLeft
        :form-id="formId"
        :projectId="projectId"
        @actionClick="actionClick"
      />
    </template>
    <div class="achievement-right">
      <OrionTable
        v-if="showTable"
        ref="tableRef"
        :options="tableOptions"
      >
        <template #toolbarLeft>
          <AddButton
            type="primary"
            @click="addTableNode"
          >
            创建文件
          </AddButton>
          <BasicButton
            icon="sie-icon-del"
            @click="handleBatchDel"
          >
            删除
          </BasicButton>
        </template>
      </OrionTable>
      <Empty v-else />
      <AddAchievementNode
        @register="register"
        @update="updateForm"
      />
    </div>
  </Layout2>
</template>
<script setup lang="ts">
import {
  Layout2, OrionTable, BasicButton, downLoadById, Empty, useDrawer, Layout, AddButton,
} from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import { declarationData, declarationDataId } from '../keys';
import AchievementLeft from '../components/AchievementLeft.vue';
import AddAchievementNode from '../components/AddAchievementNode.vue';
import Api from '/@/api';
const formId: Ref = inject(declarationDataId);
const showTable = ref(false);
const tableId = ref();
const formData = inject(declarationData);
const projectId = formData.value?.projectId;
const tableRef: Ref = ref();
const [register, { openDrawer }] = useDrawer();
const contentTitle = ref();
const tableOptions = {
  showToolButton: false,
  showIndexColumns: true,
  rowSelection: {},
  api: (params) => new Api('/pms').fetch(params, `/projectAchievement/lists/${unref(tableId)}`, 'POST'),
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '所属里程碑计划',
      dataIndex: 'milestoneName',
    },
    {
      title: '密级',
      dataIndex: 'secrecyLevelName',
    },
    {
      title: '所属层级',
      dataIndex: 'hierarchyLevelName',
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
    },
    {
      title: '责任人',
      dataIndex: 'resPersonName',
    },
    {
      title: '计划提交时间',
      dataIndex: 'planCommitTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      onClick(record) {
        openDrawer(true, {
          type: 'edit',
          data: {
            formId: record.id,
            approvalId: unref(formId),
          },
        });
      },
    },
    {
      text: '删除',
      onClick(record) {
        Modal.confirm({
          title: '是否删除当前数据?',
          onOk() {
            deleteRows([record.id]);
          },
        });
      },
    },
    {
      text: '查看',
      // modal: (record) => batchDelete([record.id]),
    },
  ],
};
function handleBatchDel() {
  let selectRowKeys = tableRef.value.getSelectRowKeys();
  if (selectRowKeys.length === 0) {
    message.warning('请选择数据');
    return;
  }
  Modal.confirm({
    title: '是否删除当前选择数据?',
    onOk() {
      deleteRows(selectRowKeys);
    },
  });
}
function deleteRows(data) {
  new Api('/pms').fetch(data, 'projectAchievement', 'DELETE').then((res) => {
    message.success('删除数据成功');
    tableRef.value.reload();
  }).catch((err) => {
  });
}
function actionClick(item) {
  tableId.value = item.id;
  contentTitle.value = item.name;
  if (!showTable.value) {
    showTable.value = true;
  } else {
    tableRef.value.reload();
  }
}
function addTableNode() {
  openDrawer(true, {
    type: 'add',
    data: {
      floderId: unref(tableId),
      projectId: formData.value.projectId,
      approvalId: unref(formId),
    },
  });
}
function updateForm() {
  tableRef.value.reload();
}

onMounted(() => {
});
</script>

<style scoped lang="less">
.achievement-right{
  height: 100%;
  :deep(.ant-empty){
    position: absolute;
    top:50%;
    left:50%;
  }
}
</style>
