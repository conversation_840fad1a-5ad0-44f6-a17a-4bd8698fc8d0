package com.chinasie.orion.feign.request;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yk
 * @date: 2023/9/19 9:48
 * @description:
 */
@ApiModel(value = "ApplyProcessDTO对象", description = "我发起的流程")
@Data
public class ApplyProcessDTO extends ObjectDTO implements Serializable {

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "是否完成")
    private Boolean isComplete;

    /**
     * 流程定义key
     */
    @ApiModelProperty(value = "流程定义key")
    private String processDefinitionKey;


    /**
     * 流程定义id
     */
    @ApiModelProperty(value = "流程定义id")
    private String processDefinitionName;

    /**
     * businessKey
     */
    @ApiModelProperty(value = "businessKey")
    private String businessKey;

    /**
     * 数据类型编号
     */
    @ApiModelProperty(value = "数据类型编号")
    private String dataTypeCode;

}
