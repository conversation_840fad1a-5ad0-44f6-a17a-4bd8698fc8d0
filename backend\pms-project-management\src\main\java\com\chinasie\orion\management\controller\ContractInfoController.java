package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractInfoDTO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.vo.ContractInfoVO;
import com.chinasie.orion.management.service.ContractInfoService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ContractInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@RestController
@RequestMapping("/contractInfo")
@Api(tags = "合同主表信息")
public class ContractInfoController {

    @Autowired
    private ContractInfoService contractInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同主表信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractInfoVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ContractInfoVO rsp = contractInfoService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractInfoDTO.name}}】", type = "合同主表信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractInfoDTO contractInfoDTO) throws Exception {
        String rsp = contractInfoService.create(contractInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractInfoDTO.name}}】", type = "合同主表信息", subType = "编辑", bizNo = "{{#contractInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ContractInfoDTO contractInfoDTO) throws Exception {
        Boolean rsp = contractInfoService.edit(contractInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "合同主表信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "合同主表信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页查询合同信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页查询合同信息")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "合同主表信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractInfoVO>> pages(@RequestBody Page<ContractInfoDTO> pageRequest) throws Exception {
        Page<ContractInfoVO> rsp = contractInfoService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号子订单
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号子订单")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号子订单", type = "ContractLineInfo", subType = "根据合同编号子订单", bizNo = "")
    @RequestMapping(value = "/getByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractInfoVO>> getByCode(@RequestBody Page<ContractInfoDTO> pageRequest) throws Exception {
        Page<ContractInfoVO> rsp = contractInfoService.getByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同编号立项基本信息
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同编号立项基本信息")
    @LogRecord(success = "【{USER{#logUserId}}】根据合同编号立项基本信息", type = "ContractLineInfo", subType = "根据合同编号立项基本信息", bizNo = "")
    @RequestMapping(value = "/getLxByCode", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractInfoVO>> getLxByCode(@RequestBody Page<ContractInfoDTO> pageRequest) throws Exception {
        Page<ContractInfoVO> rsp = contractInfoService.getLxByCode(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 给表中合同执行状态  框架合同剩余金额  框架合同已使用金额  支付金额  支付比例  字段赋值
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "给表中合同执行状态  框架合同剩余金额  框架合同已使用金额  支付金额  支付比例  字段赋值")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "ContractLineInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/updateContractInfo", method = RequestMethod.POST)
    public void updateContractInfo() throws Exception {
        contractInfoService.updateContractInfo();
    }



    /**
     * 查询总数及金额
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询总数及金额")
    @LogRecord(success = "【{USER{#logUserId}}】查询总数及金额", type = "NcfFormpurchaseRequest", subType = "查询总数及金额", bizNo = "")
    @RequestMapping(value = "/getNumMoney", method = RequestMethod.POST)
    public ResponseDTO<Map<String,Object>> getNumMoney(@RequestBody Page<ContractInfoDTO> pageRequest) throws Exception {
        Map<String,Object> map = contractInfoService.getNumMoney(pageRequest);
        return new ResponseDTO<>(map);
    }


    @ApiOperation("合同主表信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "合同主表信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("合同主表信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "合同主表信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("合同主表信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "合同主表信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消合同主表信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "合同主表信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = contractInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("合同主表信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "ContractInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception {
        contractInfoService.exportByExcel(contractInfoDTO, response);
    }

    @ApiOperation("框架合同信息导出（Excel）")
    @PostMapping(value = "/exportFrame/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "ContractInfo", subType = "导出数据", bizNo = "")
    public void exportFrameByExcel(@RequestBody ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception {
        contractInfoService.exportFrameByExcel(contractInfoDTO, response);
    }
    @ApiOperation("框架发送邮件提醒")
    @PostMapping(value = "/sendEmailAndRemind")
    @LogRecord(success = "【{USER{#logUserId}}】框架发送邮件提醒", type = "合同主表信息", subType = "框架发送邮件提醒", bizNo = "")
    public void sendEmailAndRemind() throws Exception {
        contractInfoService.sendEmailAndRemind();
    }
}
