<script setup lang="ts">
import {
  BasicButton, BasicForm, FormSchema, getDict, OrionTable, useForm,
} from 'lyra-component-vue3';
import { FormItemRest, Input } from 'ant-design-vue';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = defineProps<{
  id: string,

  isView: boolean
}>();

const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '用例名称',
    required: true,
    componentProps: {
      placeholder: '请输入用例名称',
      disabled: props.isView,
    },
  },
  {
    field: 'priority',
    component: 'ApiSelect',
    label: '优先级',
    required: true,
    componentProps: {
      api: () => getDict('dict1767425826246279168'),
      labelField: 'description',
      placeholder: '请选择优先级',
      disabled: props.isView,
    },
  },
  {
    field: 'preCondition',
    component: 'InputTextArea',
    label: '前置条件',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      disabled: props.isView,
      placeholder: '请输入相关内容',
    },
  },
  {
    field: 'testUseCaseStepList',
    component: 'Input',
    label: '操作步骤',
    slot: 'step',
    colProps: {
      span: 24,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      disabled: props.isView,
      placeholder: '请输入相关内容',
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const tableRef: Ref = ref();

const selectedRowKeys = ref([]);
const tableOptions = {
  showSmallSearch: false,
  showTableSetting: false,
  showToolButton: false,
  rowSelection: {
    selectedRowKeys,
    onChange: (keys = [], selectedRows) => {
      selectedRowKeys.value = keys;
    },
  },
  api: (params: Record<string, any>) => {
    params.query = {
      id: props.id,
    };
    return new Api('/pms/projectCollection/getPages').fetch(params, '', 'POST');
  },

  columns: [
    {
      title: '项目集名称',
      dataIndex: 'name',

    },
    {
      title: '负责人',
      dataIndex: 'resPersonName',
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
  ],
};

const loading: Ref<boolean> = ref(false);

defineExpose({
  async onSubmit() {
    const params = {
      id: props.id,
      projectCollectionIds: selectedRowKeys.value,
    };
    await new Api('/pms/projectCollection/create/toProject').fetch(params, '', 'POST');
  },
});
</script>

<template>
  <div
    style="height: 500px; overflow: hidden"
    class="test-management"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.ant-basic-table-wrap.default-spacing) {
  padding: 0;
}
.test-management ::v-deep .default-spacing {
  padding-left: 0;
  padding-right: 0;
}
</style>