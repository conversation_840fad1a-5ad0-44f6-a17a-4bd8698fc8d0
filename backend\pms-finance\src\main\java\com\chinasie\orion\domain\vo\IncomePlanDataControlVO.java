package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomePlanDataControl VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
@ApiModel(value = "IncomePlanDataControlVO对象", description = "收入计划数据管控")
@Data
public class IncomePlanDataControlVO extends  ObjectVO   implements Serializable{

            /**
         * 锁定状态
         */
        @ApiModelProperty(value = "锁定状态")
        private String lockStatus;

        @ApiModelProperty(value = "锁定状态名称")
        private String lockStatusName;


        /**
         * 专业中心
         */
        @ApiModelProperty(value = "专业中心")
        private String expertiseCenter;

        @ApiModelProperty(value = "专业中心名称")
        private String expertiseCenterName;


        /**
         * 专业中心收入计划金额
         */
        @ApiModelProperty(value = "专业中心收入计划金额")
        private BigDecimal expertiseCenterMoney;


        /**
         * 收入计划填报Id
         */
        @ApiModelProperty(value = "收入计划填报Id")
        private String incomePlanId;


    

}
