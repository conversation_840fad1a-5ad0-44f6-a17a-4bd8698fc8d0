package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.IdAnalysisBo;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.bo.SysCodeBO;
import com.chinasie.orion.constant.AcceptanceFormStatusEnum;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.constant.NewProjectStatusEnum;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormCreateDTO;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormQueryDTO;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormType;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceFormFile;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceFormItem;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.AcceptanceFormFileRepository;
import com.chinasie.orion.repository.AcceptanceFormItemRepository;
import com.chinasie.orion.repository.AcceptanceFormRepository;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AcceptanceFormService;
import com.chinasie.orion.service.FileInfoService;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AcceptanceFormServiceImpl extends OrionBaseServiceImpl<AcceptanceFormRepository,AcceptanceForm> implements AcceptanceFormService {

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private ProjectService projectService;

    @Resource
    private LyraFileBO fileBo;

    @Autowired
    private SysCodeBO sysCodeBO;

//    @Autowired
//    private ProjectSuppliesManagementRepository projectSuppliesManagementRepository;

    @Autowired
    private AcceptanceFormRepository acceptanceFormRepository;

    @Autowired
    private AcceptanceFormItemRepository acceptanceFormItemRepository;

    @Autowired
    private AcceptanceFormFileRepository acceptanceFormFileRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

//    @Autowired
//    private ProjectCommentsService projectCommentsService;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    @Resource
    private SearchHelper searchHelper;

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Resource
    private FileInfoService fileInfoService;
    @Resource
    private IdAnalysisBo idAnalysisBo;

    @Autowired
    private DataStatusNBO dataStatusNBO;


    @Override
    public Page<AcceptanceFormListVO> pageInfo(Page<AcceptanceFormQueryDTO> pageRequest) throws Exception {


        Page<NewProjectHomePageVO> resultPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());


        // 修整查询请求
        if (pageRequest.getPageSize() > 100) {
            pageRequest.setPageSize(100);
        }

        // 设置筛选条件


        LambdaQueryWrapperX<AcceptanceForm> wrapper = new LambdaQueryWrapperX<>();
        wrapper.selectAll(AcceptanceForm.class);
        wrapper.leftJoin(UserDO.class, UserDO::getId, AcceptanceForm::getCreatorId);
        wrapper.orderByDesc(AcceptanceForm::getModifyTime);


        AcceptanceFormQueryDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        boolean pm = projectRoleUserService.isPmRoleUser(projectId);
        if (!pm) {
            wrapper.eq(AcceptanceForm::getCreatorId, CurrentUserHelper.getCurrentUserId());
        }
        wrapper.eq(AcceptanceForm::getProjectId, projectId);
        if (CollectionUtil.isNotEmpty(pageRequest.getSearchConditions())) {
            // 关键字搜索条件定制
            String keyword = pageRequest.getSearchConditions().get(0).stream().filter(cond -> cond.getField().equals("name")).map(cond -> cond.getValues().get(0).toString()).findFirst().orElse(null);
            if (StrUtil.isNotEmpty(keyword)) {
                final String tempKeyword = keyword;
                wrapper.and(c -> {
                    c.like(AcceptanceForm::getNumber, tempKeyword)
                            .or()
                            .like(UserDO::getName, tempKeyword);
                });
            }

            // 验收人名字搜索定制
            keyword = pageRequest.getSearchConditions().get(0).stream().filter(cond -> cond.getField().equals("userName")).map(cond -> cond.getValues().get(0).toString()).findFirst().orElse(null);
            if (StrUtil.isNotEmpty(keyword)) {
                wrapper.like(UserDO::getName, keyword);
            }

            List<SearchCondition> conditions = pageRequest.getSearchConditions().get(0).stream().filter(cond -> !ListUtil.of("name", "userName").contains(cond.getField())).collect(Collectors.toList());
            pageRequest.getSearchConditions().get(0).clear();
            pageRequest.getSearchConditions().get(0).addAll(conditions);
        }

        // 执行查询
        IPage<AcceptanceForm> reqPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<AcceptanceForm>();
        BeanCopyUtils.copyProperties(pageRequest, reqPage);
        IPage<AcceptanceForm> page = this.page(reqPage, wrapper);

        // 转换为VO对象
        Page<AcceptanceFormListVO> voPage = new Page<AcceptanceFormListVO>();
        BeanCopyUtils.copyProperties(page, voPage);
        List<AcceptanceFormListVO> items = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        page.getRecords().forEach((item) -> {
            userIds.add(item.getCreatorId());
            userIds.add(item.getModifyId());
            userIds.add(item.getCompleteUserId());
            items.add(BeanCopyUtils.convertTo(item, AcceptanceFormListVO::new));
        });


        // 补充用户名
        Map<String, UserVO> userMap = userRedisHelper.getUserByIds(userIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(UserVO::getId, entry -> entry));
        items.forEach((item) -> {
            UserVO userVO = userMap.get(item.getCreatorId());
            if (userVO != null) {
                item.setCompleteUserName(userVO.getName());
                item.setCreatorCode(userVO.getCode());
            }
            if (StrUtil.isNotEmpty(item.getCompleteUserId())) {
                userVO = userMap.get(item.getCompleteUserId());
                if (userVO != null) {
                    item.setCompleteUserName(userVO.getName());
                }
            }
            userVO = userMap.get(item.getModifyId());
            if (userVO != null) {
                item.setModifyName(userVO.getName());
            }
        });

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        PowerParams power = pageRequest.getPower();
        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(projectId, currentUserId);
        if (ObjectUtil.isEmpty(power)) {
            voPage.setContent(items);
            return voPage;
        }
        pmsAuthUtil.setRowAuths(currentUserId, power, items, AcceptanceFormListVO::getId, AcceptanceFormListVO::getDataStatus,
                AcceptanceFormListVO::setRdAuthList,
                AcceptanceFormListVO::getOwnerId,
                null,
                null, new HashMap<>() {{
                    put(projectId, roleCodeList);
                }});
        voPage.setContent(items);

        return voPage;
    }

    @Override
    public List<AcceptanceFormVO> listByIds(List<String> ids) throws Exception {
        List<AcceptanceForm> acceptanceFormList = acceptanceFormRepository.selectBatchIds(ids);
        List<AcceptanceFormVO> vos = BeanCopyUtils.convertListTo(acceptanceFormList, AcceptanceFormVO::new);
        return vos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AcceptanceFormVO createAcceptanceForm(AcceptanceFormCreateDTO createDTO) throws Exception {
        // 属性验证
        AcceptanceFormType acceptanceFormType = AcceptanceFormType.getType(createDTO.getType());
        if (acceptanceFormType == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_TYPE_NULL);
        }
        validateCreateRequest(createDTO, acceptanceFormType);

        AcceptanceForm form = new AcceptanceForm();
        form.setType(acceptanceFormType.name());
        form.setProjectId(createDTO.getProjectId());
        form.setStatus(AcceptanceFormStatusEnum.CREATED.getStatus());

        // 生成验收单编号
        List<CodeSegmentVO> codeRuleList = null;
        if (AcceptanceFormType.ACCEPTANCE_FORM.name().equals(createDTO.getType())) {
            // 采购计划验收单编码规则
            codeRuleList = sysCodeBO.getCodeRuleList("AcceptanceForm", "number");
        } else {
            // 项目验收单编码规则
            codeRuleList = sysCodeBO.getCodeRuleList("AcceptanceForm4Project", "number");
        }
        if (CollectionUtil.isNotEmpty(codeRuleList)) {
            String number = sysCodeBO.getCode(codeRuleList);
            form.setNumber(number);
        }
        if (StrUtil.isEmpty(form.getNumber())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NUMBER_NULL);
        }

        // 保存验收单
        this.save(form);

        // 处理关联采购立项计划
        if (AcceptanceFormType.ACCEPTANCE_FORM == acceptanceFormType) {
            final String formId = form.getId();
            List<AcceptanceFormItem> items = createDTO.getItemIds().stream().map(itemId -> {
                AcceptanceFormItem item = new AcceptanceFormItem();
                item.setClassName("AcceptanceFormItem");
                item.setAcceptanceFormId(formId);
                item.setSuppliesManagementId(itemId);
                return item;
            }).collect(Collectors.toList());
            // 存储验收单与采购计划立项明细关联关系
            acceptanceFormItemRepository.insertBatch(items);
//            acceptanceFormItemRepository.saveBatch(items);
            // 记录采购验收单编号
//            projectSuppliesManagementRepository.updateAcceptanceFormNumberByIds(createDTO.getItemIds(), form.getId(), form.getNumber());
        } else if (AcceptanceFormType.PROJECT == acceptanceFormType) {
            // 记录验收单id、编号到project
            projectService.updateAcceptanceFormInfo(createDTO.getProjectId(), form.getId(), form.getNumber());
        }

        // 重新查询一遍数据
        return findById(form.getId(), null);
    }

    private void validateCreateRequest(AcceptanceFormCreateDTO createDTO, AcceptanceFormType acceptanceFormType) throws Exception {
        if (StrUtil.isEmpty(createDTO.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PROJECT_ID_NULL);
        }

        if (AcceptanceFormType.PROJECT == acceptanceFormType) {
            // 验证项目状态.
            //
//            Project project = projectService.getByKey(createDTO.getProjectId());
            Project project = projectService.getById(createDTO.getProjectId());
            if (project == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PROJECT_ID_NULL, "项目不存在");
            }
            if (NewProjectStatusEnum.PROJECT_CHECK_AND_ACCEPT.getStatus().equals(project.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目已验收完成");
            }
//            if (!NewProjectStatusEnum.PROJECT_FINISH.getStatus().equals(project.getStatus())) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目未完工");
//            }
            LambdaQueryWrapperX<AcceptanceForm> queryWrapperX = new LambdaQueryWrapperX<>();
            queryWrapperX.eq(AcceptanceForm :: getProjectId,createDTO.getProjectId());
            queryWrapperX.eq(AcceptanceForm :: getType,AcceptanceFormType.PROJECT.name());
            List<AcceptanceForm> existsAcceptanceForm = acceptanceFormRepository.selectList(queryWrapperX);
            if (CollectionUtil.isNotEmpty(existsAcceptanceForm)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目已有验收单");
            }
        } else if (AcceptanceFormType.ACCEPTANCE_FORM == acceptanceFormType) {
            // 验证关联采购计划单. status=130: 已完工, status=160: 已验收.
            if (CollectionUtil.isEmpty(createDTO.getItemIds())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_ACCEPTANCE_FORM_ITEMS_NULL, "缺少采购计划立项明细");
            }

//            List<ProcurementPlanApprovalListVO> listItems = projectSuppliesManagementRepository.querySimpleListItemsByIds(createDTO.getItemIds());
//            List<Integer> statusList = listItems.stream().map(ProcurementPlanApprovalListVO::getStatus).distinct().collect(Collectors.toList());
//            if (CollectionUtil.isEmpty(statusList)) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_ACCEPTANCE_FORM_ITEMS_NULL, "缺少采购计划立项明细");
//            }
//            if (!statusList.contains(ProjectSuppliesManagementStatusEnum.FINISHED.getStatus())) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "请勾选正确的数据进行操作");
//            }
//            listItems = listItems.stream().filter(item -> StrUtil.isNotEmpty(item.getAcceptanceFormNumber())).collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(listItems)) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "已有验收数据，请重新选择");
//            }
        }
    }

    @Override
    public AcceptanceFormVO findById(String acceptanceFormId, String pageCode) throws Exception {
        AcceptanceForm form = getById(acceptanceFormId);
        if (form == null) {
            return null;
        }

        AcceptanceFormVO vo = BeanCopyUtils.convertTo(form, AcceptanceFormVO::new);

        // 设置项目信息
//        ProjectVO projectVO = projectService.getSingleDetail(vo.getProjectId(), null);
        ProjectVO projectVO = projectService.getProjectDetail(vo.getProjectId(), null);
        vo.setProjectName(projectVO.getName());
        vo.setProjectNumber(projectVO.getNumber());
        vo.setProjectBeginTime(projectVO.getProjectStartTime());
        vo.setProjectEndTime(projectVO.getProjectEndTime());
        vo.setProjectRspUserId(projectVO.getResPerson());
        vo.setProjectRspSubDeptId(projectVO.getResDept());

        // 补充关系人名
        List<String> userIds = ListUtil.of(vo.getCompleteUserId(), vo.getCreatorId(), vo.getProjectRspUserId()).stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, UserVO> userVOMap = userRedisHelper.getUserByIds(userIds).stream()
                .collect(Collectors.toMap(UserVO::getId, entry -> entry));
        UserVO userVO = userVOMap.get(vo.getCreatorId());
        if (userVO != null) {
            vo.setCreatorCode(userVO.getCode());
        }
        userVO = userVOMap.get(vo.getProjectRspUserId());
        if (userVO != null) {
            vo.setProjectRspUserName(userVO.getName());
        }
        if (StrUtil.isNotEmpty(vo.getCompleteUserId())) {
            userVO = userVOMap.get(vo.getCompleteUserId());
            vo.setCompleteUserName(userVO.getName());
        }

        // 补充组织名
//        DeptVO orgVO = deptRedisHelper.getDeptById(vo.getProjectRspSubDeptId());
//        if (orgVO != null) {
//            vo.setProjectRspSubDeptName(orgVO.getName());
//        }

        // 补充enables逻辑
        vo.getEnables().put("acceptForm", AcceptanceFormStatusEnum.CREATED.getStatus().equals(vo.getStatus()));
        //设置权限
        if (StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(vo.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(vo, currentUserId, pageCode, form.getDataStatus(), AcceptanceFormVO::setDetailAuthList, vo.getCreatorId(), null, null, roleCodeList);

        }
        return vo;
    }


    @Override
    public AcceptanceFormVO findByProjectId(String projectId, String pageCode) throws Exception {
        AcceptanceForm form = acceptanceFormRepository.selectOne(AcceptanceForm :: getProjectId,projectId);
        if (form == null) {
            return null;
        }

        AcceptanceFormVO vo = BeanCopyUtils.convertTo(form, AcceptanceFormVO::new);
        // 设置项目信息
        ProjectVO projectVO = projectService.getProjectDetail(vo.getProjectId(), null);
        vo.setProjectName(projectVO.getName());
        vo.setProjectNumber(projectVO.getNumber());
        // 补充关系人名
        List<String> userIds = ListUtil.of(vo.getCompleteUserId(), vo.getCreatorId(), vo.getProjectRspUserId()).stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, UserBaseCacheVO> userVOMap = userRedisHelper.getUserBaseCacheByIds(userIds).stream()
                .collect(Collectors.toMap(UserBaseCacheVO::getId, entry -> entry));
        UserBaseCacheVO userVO = userVOMap.get(vo.getCreatorId());
        if (userVO != null) {
            vo.setCreatorCode(userVO.getCode());
        }
        userVO = userVOMap.get(vo.getProjectRspUserId());
        if (userVO != null) {
            vo.setProjectRspUserName(userVO.getName());
        }
        if (StrUtil.isNotEmpty(vo.getCompleteUserId())) {
            userVO = userVOMap.get(vo.getCompleteUserId());
            vo.setCompleteUserName(userVO.getName());
        }
        if (StrUtil.isNotEmpty(vo.getCreatorId())) {
            vo.setCreatorName(userVOMap.getOrDefault(vo.getCreatorId(),new UserBaseCacheVO()).getName());
        }
        Map<Integer, DataStatusVO> dataStatusMap = dataStatusNBO.getDataStatusMapByClassName(AcceptanceForm.class.getSimpleName());
        if (dataStatusMap.containsKey(vo.getStatus())) {
            vo.setDataStatus(dataStatusMap.get(vo.getStatus()));
        }
        // 补充enables逻辑
        vo.getEnables().put("acceptForm", AcceptanceFormStatusEnum.CREATED.getStatus().equals(vo.getStatus()));
        //设置权限
        if (StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(vo.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(vo, currentUserId, pageCode, form.getDataStatus(), AcceptanceFormVO::setDetailAuthList, vo.getCreatorId(), null, null, roleCodeList);

        }
        return vo;
    }

//    @Override
//    public Page<ProcurementPlanApprovalListVO> pageQueryProcurementPlanApproval(Page<ProcurementPlanApprovalQueryDTO> pageRequest) throws Exception {
//        // 修正查询请求
//        if (pageRequest.getPageSize() > 100) {
//            pageRequest.setPageSize(100);
//        }
//
//        Page<ProjectSuppliesManagement> suppliesManagementPage = projectSuppliesManagementRepository.fetchByAcceptanceFormId(pageRequest);
//        Page<ProcurementPlanApprovalListVO> voPage = new Page<>();
//        BeanCopyUtils.copyProperties(suppliesManagementPage, voPage);
//        voPage.setQuery(null);
//
//        // 数据转换
//        List<String> userIds = suppliesManagementPage.getContent().stream().map(ProjectSuppliesManagement::getDemandPerson).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        Map<String, UserVO> userVOMap = userRedisHelper.getUserByIds(userIds).stream().collect(Collectors.toMap(UserVO::getId, entry -> entry));
//        List<ProcurementPlanApprovalListVO> items = suppliesManagementPage.getContent().stream().map(item -> {
//            ProcurementPlanApprovalListVO vo = BeanCopyUtils.convertTo(item, ProcurementPlanApprovalListVO::new);
//            UserVO userVO = userVOMap.get(item.getDemandPerson());
//            if (userVO != null) {
//                vo.setDemandPersonName(userVO.getName());
//            }
//            return vo;
//        }).collect(Collectors.toList());
//        voPage.setContent(items);
//        List<ProcurementPlanApprovalListVO> vos = voPage.getContent();
//        PowerParams power = pageRequest.getPower();
//        ProcurementPlanApprovalQueryDTO query = pageRequest.getQuery();
//        String currentUserId = CurrentUserHelper.getCurrentUserId();
//        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(query.getProjectId(), currentUserId);
//        pmsAuthUtil.setRowAuths(currentUserId, power, vos, ProcurementPlanApprovalListVO::getId, ProcurementPlanApprovalListVO::getDataStatus, ProcurementPlanApprovalListVO::setRdAuthList,
//                ProcurementPlanApprovalListVO::getCreatorId, ProcurementPlanApprovalListVO::getModifyId, null, roleCodeList);
//        voPage.setContent(vos);
//        return voPage;
//    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changeAcceptanceFormStatus(String acceptanceFormId, String status) throws Exception {
        if (status.equals("COMPLETE")) {
            // 兼容旧的API定义
            status = AcceptanceFormStatusEnum.CHECK_AND_ACCEPTED.name();
        }

        AcceptanceFormStatusEnum statusType = AcceptanceFormStatusEnum.getStatus(status);
        if (AcceptanceFormStatusEnum.CHECK_AND_ACCEPTED != statusType) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, String.format("无效的操作%s", status));
        }

        AcceptanceForm acceptanceForm = this.getById(acceptanceFormId);
        if (acceptanceForm == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }
        if (!AcceptanceFormStatusEnum.CREATED.getStatus().equals(acceptanceForm.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "仅处于验收中的验收单才能验收");
        }

        LambdaUpdateWrapper<AcceptanceForm> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(AcceptanceForm :: getStatus,statusType.getStatus());
        updateWrapper.set(AcceptanceForm :: getCompleteUserId,CurrentUserHelper.getCurrentUserId());
        updateWrapper.set(AcceptanceForm :: getCompleteTime,new Date());
        updateWrapper.eq(AcceptanceForm :: getId,acceptanceFormId);
        Boolean optRet = this.update(updateWrapper);
        if (Objects.equals(Boolean.FALSE, optRet)) {
            return false;
        }

        if (AcceptanceFormType.checkType(AcceptanceFormType.PROJECT, acceptanceForm.getType())) {
            // 更新项目状态为: 已验收
//            Boolean existsNonStatusItems = projectSuppliesManagementRepository.existsNonStatusItems(acceptanceForm.getProjectId(), ListUtil.of(
//                    ProjectSuppliesManagementStatusEnum.CHECK_AND_ACCEPT.getStatus(),
//                    ProjectSuppliesManagementStatusEnum.DELETED.getStatus()
//            ));
///*
//            existsNonStatusItems = acceptanceFormItemRepository.existsNonStatusItems(acceptanceForm.getProjectId(), ListUtil.of(
//                    ProjectSuppliesManagementStatusEnum.CHECK_AND_ACCEPT.getStatus()
//                ));
//*/
//            if (existsNonStatusItems) {
//                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "尚有采购行未完成验收确认，请先完成所有采购行验收确认");
//            }
            projectService.updateStatus(acceptanceForm.getProjectId(), NewProjectStatusEnum.PROJECT_CHECK_AND_ACCEPT);
            //todo 评价注释
//            projectCommentsService.enterProjectComments(acceptanceForm.getProjectId());
        } else {
            // 更新关联采购计划明细的状态为: 已验收
            List<String> itemIds = this.queryRelatedSuppliesManagementIds(acceptanceForm.getId());
//            projectSuppliesManagementRepository.updateStatusByIdList(ProjectSuppliesManagementStatusEnum.CHECK_AND_ACCEPT.getStatus(), itemIds);
        }
        return true;
    }

    @Override
    public Boolean saveRelatedFiles(String acceptanceFormId, List<FileDTO> fileDTOList) throws Exception {
        // 调用res服务保存文件到res
        fileDTOList.forEach(item -> {
            item.setDataId(acceptanceFormId);
            item.setDataType(FileConstant.FILETYPE_ACCEPTANCE_FORM_FILE);
            item.setRevKey(UUID.randomUUID().toString().replaceAll("-", ""));
            item.setRevOrder(1);
        });
        List<String> fileIds = fileBo.addBatch(fileDTOList);
        searchHelper.sendDataChangeMessage(acceptanceFormId);
        //保存项目文件库
        String projectId =acceptanceFormRepository.findProjectIdByFormId(acceptanceFormId);
        List<FileVO> fileDtoList1 = fileBo.getFileByIds(fileIds);
        List<String> dataIdList = fileDtoList1.stream().map(FileVO::getDataId).distinct().collect(Collectors.toList());
        List<FileInfo> fileInfos = BeanCopyUtils.convertListTo(fileDtoList1, FileInfo::new);
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIdList);
        fileInfos.forEach(o -> {
            o.setProjectId(projectId);
            o.setClassName(idToClassNameMap.get(o.getDataId()) + "File");
            o.setNumber(String.format("WJ%s", IdUtil.objectId()));
        });
        fileInfoService.saveBatch(fileInfos);

        // 保存文件关联关系, 手动控制事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            List<AcceptanceFormFile> files = fileIds.stream().map(fileId -> {
                AcceptanceFormFile file = new AcceptanceFormFile();
                file.setAcceptanceFormId(acceptanceFormId);
                file.setFileId(fileId);
                return file;
            }).collect(Collectors.toList());
            acceptanceFormFileRepository.insertBatch(files);

            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }

        return true;
    }

    @Override
    public Page<FileExtVO> listRelatedFiles(String acceptanceFormId, Page pageRequest) throws Exception {
        // 获取验收文件分页数据.
        LambdaQueryWrapperX<AcceptanceFormFile> wrapper = new LambdaQueryWrapperX<>(AcceptanceFormFile.class);
        wrapper.orderByDesc(AcceptanceFormFile::getCreateTime);
        wrapper.eq(AcceptanceFormFile::getAcceptanceFormId, acceptanceFormId);
        PageResult<AcceptanceFormFile> filePage = acceptanceFormFileRepository.selectPage(pageRequest, wrapper);

        // 获取res文件信息
        List<String> fileIdList = filePage.getContent().stream().map(AcceptanceFormFile::getFileId).collect(Collectors.toList());
        List<FileVO> fileDTOList = fileBo.getFileByIds(fileIdList)
                .stream()
                .sorted(Comparator.comparing(FileVO::getCreateTime).reversed())
                .collect(Collectors.toList());
        List<String> userIds = fileDTOList.stream().map(FileVO::getCreatorId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, UserVO> userVOMap = userRedisHelper.getUserByIds(userIds).stream().collect(Collectors.toMap(UserVO::getId, entry -> entry));

        List<FileExtVO> vos = BeanCopyUtils.convertListTo(fileDTOList, FileExtVO::new);
        vos.stream().forEach(item -> {
            UserVO userVO = userVOMap.get(item.getCreatorId());
            if (userVO != null) {
                item.setCreateUserName(userVO.getName());
                item.setCreatorCode(userVO.getCode());
            }
        });


        // FIXME 待处理验收人工号
        return new Page<>(filePage.getPageNum(), filePage.getPageSize(), filePage.getTotalSize(), vos);
    }

    @Override
    public Boolean deleteRelatedFile(String acceptanceFormId, List<String> fileIds) throws Exception {
        LambdaQueryWrapperX<AcceptanceFormFile> wrapper = new LambdaQueryWrapperX<>(AcceptanceFormFile.class);
        wrapper.eq(AcceptanceFormFile::getAcceptanceFormId, acceptanceFormId);
        wrapper.in(AcceptanceFormFile::getFileId, fileIds);
        acceptanceFormFileRepository.deleteJoin(wrapper);
        searchHelper.sendDataChangeMessage(acceptanceFormId);
        return true;
    }




//    @Override
//    public List<AcceptanceFormItem> saveBatch(List<AcceptanceFormItem> acceptanceFormItems) throws Exception {
//        UserVO currentUser = this.currentUserHelper.getUser();
//        OrionTableInfo orionTableInfo = OrionTableInfoHelper.getOrionTableInfo(this.entityClass);
//        for (AcceptanceFormItem item : acceptanceFormItems) {
//            this.setCreateDefaultValue(item, currentUser, orionTableInfo);
//        }
//        return super.saveBatch(acceptanceFormItems);
//    }

    /**
     * 获取验收单关联的所有采购计划项id.
     *
     * @param acceptanceFormId 验收单Id
     * @return
     */
    public List<String> queryRelatedSuppliesManagementIds(String acceptanceFormId) {
        String sql = "SELECT supplies_management_id  FROM pms_acceptance_form_item WHERE acceptance_form_id = :acceptanceFormId AND logic_status = 1";
        Map<String, Object> params = new HashMap<>();
        params.put("acceptanceFormId", acceptanceFormId);
        return this.namedParameterJdbcTemplate.queryForList(sql, params, String.class);
    }

//    /**
//     * 判断特定项目下是否存在非status的采购计划验收单.
//     *
//     * @param projectId
//     * @param statusList
//     * @return
//     */
//    public Boolean existsNonStatusItems(String projectId, List<Integer> statusList) {
//        String sql = "SELECT 1 FROM pms_acceptance_form_item afi LEFT OUTER JOIN pms_project_supplies_management psm ON psm.id = afi.supplies_management_id WHERE psm.project_id = :projectId AND psm.status NOT IN (:status) LIMIT 1";
//        Map<String, Object> params = new HashMap<>();
//        params.put("projectId", projectId);
//        params.put("status", statusList);
//        List<Integer> result = this.namedParameterJdbcTemplate.queryForList(sql, params, Integer.class);
//        return CollectionUtil.isNotEmpty(result);
//    }


//    @Override
//    public List<AcceptanceFormFile> saveBatch(List<AcceptanceFormFile> acceptanceFormFiles) throws Exception {
//        UserVO currentUser = this.currentUserHelper.getUser();
//        OrionTableInfo orionTableInfo = OrionTableInfoHelper.getOrionTableInfo(this.entityClass);
//        for (AcceptanceFormFile item : acceptanceFormFiles) {
//            this.setCreateDefaultValue(item, currentUser, orionTableInfo);
//        }
//        return super.saveBatch(acceptanceFormFiles);
//    }
}
