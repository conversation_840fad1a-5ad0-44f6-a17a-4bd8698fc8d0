package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ContractPayNodeConfirmAuditRecordDTO;
import com.chinasie.orion.domain.entity.ContractPayNode;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirm;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirmAuditRecord;
import com.chinasie.orion.domain.entity.ContractPayNodeConfirmNode;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ContractPayNodeConfirmAuditRecordRepository;
import com.chinasie.orion.repository.ContractPayNodeConfirmNodeRepository;
import com.chinasie.orion.repository.ContractPayNodeConfirmRepository;
import com.chinasie.orion.repository.ContractPayNodeRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractPayNodeConfirmAuditRecordService;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * ContractPayNodeConfirmAuditRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-27 15:12:55
 */
@Service
public class ContractPayNodeConfirmAuditRecordServiceImpl extends OrionBaseServiceImpl<ContractPayNodeConfirmAuditRecordRepository, ContractPayNodeConfirmAuditRecord> implements ContractPayNodeConfirmAuditRecordService {

    @Autowired
    private ContractPayNodeConfirmAuditRecordRepository contractPayNodeConfirmAuditRecordRepository;

    @Autowired
    private ContractPayNodeConfirmRepository contractPayNodeConfirmRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ContractPayNodeRepository contractPayNodeRepository;

    @Autowired
    private ContractPayNodeConfirmNodeRepository contractPayNodeConfirmNodeRepository;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractPayNodeDetailVO detail(String id) throws Exception {
        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = contractPayNodeConfirmAuditRecordRepository.selectById(id);
        ContractPayNodeDetailVO contractPayNodeDetailVO = new ContractPayNodeDetailVO();
        ContractPayNodeConfirm contractPayNodeConfirm = contractPayNodeConfirmRepository.selectById(contractPayNodeConfirmAuditRecord.getConfirmId());
        if (contractPayNodeConfirm == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "节点确认信息未找到!");
        }
        ContractPayNodeConfirmVO contractPayNodeConfirmVO = BeanCopyUtils.convertTo(contractPayNodeConfirm, ContractPayNodeConfirmVO::new);
        contractPayNodeConfirmVO.setAuditDate(contractPayNodeConfirmAuditRecord.getAuditDate());
        contractPayNodeConfirmVO.setAuditDesc(contractPayNodeConfirmAuditRecord.getAuditComment());
        String auditUserId = contractPayNodeConfirmAuditRecord.getAuditUserId();
        if (StringUtils.hasText(auditUserId)) {
            UserVO rspUser = userRedisHelper.getUserById(auditUserId);
            contractPayNodeConfirmVO.setAuditUserName(null == rspUser ? "" : rspUser.getName());
            contractPayNodeConfirmVO.setAuditUserCode(null == rspUser ? "" : rspUser.getCode());
        }

        String submitUserId = contractPayNodeConfirmVO.getSubmitUserId();
        if (StringUtils.hasText(submitUserId)) {
            UserVO rspUser = userRedisHelper.getUserById(submitUserId);
            contractPayNodeConfirmVO.setSubmitUserIdName(null == rspUser ? "" : rspUser.getName());
            contractPayNodeConfirmVO.setSubmitUserIdCode(null == rspUser ? "" : rspUser.getCode());
        }

        List<DocumentVO> documentVOList = documentService.getDocumentList(contractPayNodeConfirmAuditRecord.getSubmitId(), null);

        List<ContractPayNodeConfirmNode> contractPayNodeConfirmNodeList = contractPayNodeConfirmNodeRepository.selectList(ContractPayNodeConfirmNode::getConfirmId, contractPayNodeConfirmAuditRecord.getConfirmId());
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectBatchIds(contractPayNodeConfirmNodeList.stream().map(ContractPayNodeConfirmNode::getNodeId).collect(Collectors.toList()));
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);

        contractPayNodeDetailVO.setContractPayNodeConfirmVO(contractPayNodeConfirmVO);
        contractPayNodeDetailVO.setContractPayNodeVOList(contractPayNodeVOList);
        contractPayNodeDetailVO.setDocumentVOList(documentVOList);

        List<DocumentVO> auditDocumentVOList = documentService.getDocumentList(id, null);
        contractPayNodeDetailVO.setAuditDocumentVOList(auditDocumentVOList);
        return contractPayNodeDetailVO;
    }

    /**
     * 新增
     * <p>
     * * @param contractPayNodeConfirmAuditRecordDTO
     */
    @Override
    public ContractPayNodeConfirmAuditRecordVO create(ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception {
        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = BeanCopyUtils.convertTo(contractPayNodeConfirmAuditRecordDTO, ContractPayNodeConfirmAuditRecord::new);
        ContractPayNodeConfirm contractPayNodeConfirm = contractPayNodeConfirmRepository.selectById(contractPayNodeConfirmAuditRecordDTO.getConfirmId());
        if(contractPayNodeConfirm == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "节点确认信息未找到!");
        }
        contractPayNodeConfirmAuditRecord.setSubmitId(contractPayNodeConfirm.getSubmitId());
        int insert = contractPayNodeConfirmAuditRecordRepository.insert(contractPayNodeConfirmAuditRecord);
        ContractPayNodeConfirmAuditRecordVO rsp = BeanCopyUtils.convertTo(contractPayNodeConfirmAuditRecord, ContractPayNodeConfirmAuditRecordVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractPayNodeConfirmAuditRecordDTO
     */
    @Override
    public Boolean edit(ContractPayNodeConfirmAuditRecordDTO contractPayNodeConfirmAuditRecordDTO) throws Exception {
        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = BeanCopyUtils.convertTo(contractPayNodeConfirmAuditRecordDTO, ContractPayNodeConfirmAuditRecord::new);
        int update = contractPayNodeConfirmAuditRecordRepository.updateById(contractPayNodeConfirmAuditRecord);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = contractPayNodeConfirmAuditRecordRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ContractPayNodeConfirmAuditRecordVO> pages(Page<ContractPayNodeConfirmAuditRecordDTO> pageRequest) throws Exception {
        Page<ContractPayNodeConfirmAuditRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractPayNodeConfirmAuditRecord::new));
        ContractPayNodeConfirmAuditRecord contractPayNodeConfirmAuditRecord = realPageRequest.getQuery();
        LambdaQueryWrapper<ContractPayNodeConfirmAuditRecord> auditRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();

        if(contractPayNodeConfirmAuditRecord != null){
            if(StringUtils.hasText(contractPayNodeConfirmAuditRecord.getConfirmId())){
                auditRecordLambdaQueryWrapper.eq(ContractPayNodeConfirmAuditRecord :: getConfirmId, contractPayNodeConfirmAuditRecord.getConfirmId());
            }
        }
        PageResult<ContractPayNodeConfirmAuditRecord> page = contractPayNodeConfirmAuditRecordRepository.selectPage(realPageRequest, auditRecordLambdaQueryWrapper);

        Page<ContractPayNodeConfirmAuditRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractPayNodeConfirmAuditRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractPayNodeConfirmAuditRecordVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
