package com.chinasie.orion.domain.dto.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/06/21:16
 * @description:
 */
@Data
public class EquivalentParamDTO  implements Serializable {

    @ApiModelProperty(value = "关键词")
    private String keyWord;
    @ApiModelProperty(value = "不在培训中心Id")
    private List<String> outTrainCenterIdList;
    @ApiModelProperty(value = "申请等效的基地编号")
    private String basePlaceCode;
}
