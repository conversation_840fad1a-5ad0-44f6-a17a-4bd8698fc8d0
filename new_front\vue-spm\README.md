# 项目说明
## 技术栈

Vue3 + antdv + vben + less + webpack + qiankun + lyra-component-vue3 

## 基础说明

1. qiankun 微服务方案
2. Vben 基础页面框架方案
3. lyra-component-vue3 项目内自己维护的组件库，包含了常用的组件，以及Vben提供的组件，进行了单独维护和版本管理
4. 图标：集成了 fontawesome 和项目内部设计自行设计的图标，已集成到 lyra-component-vue3 包内的Icon组件

## 配置菜单说明
1. 配置本地开发调试菜单，该菜单不与线上环境有关系，只对开发进行预览使用 `/src/store/modules/testMenu.ts`
   1. component：组件地址
   2. path：路由地址
   3. meta.title 菜单显示名称
2. 线上菜单配置：
   1. 发布以后，到页面管理新增页面
   2. 菜单管理配置菜单
   3. 分配角色权限



