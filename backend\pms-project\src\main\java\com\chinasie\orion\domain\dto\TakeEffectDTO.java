package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/13 13:29
 * @description:
 */
@ApiModel(value = "TakeEffectDTO对象", description = "启用禁用")
public class TakeEffectDTO implements Serializable {

    /**
     * id
     */
    @NotEmpty(message = "idList不能为空")
    @ApiModelProperty(value = "idList")
    private List<String> idList;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用 0:禁用 1：启用")
    private Integer takeEffect;

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public Integer getTakeEffect() {
        return takeEffect;
    }

    public void setTakeEffect(Integer takeEffect) {
        this.takeEffect = takeEffect;
    }
}
