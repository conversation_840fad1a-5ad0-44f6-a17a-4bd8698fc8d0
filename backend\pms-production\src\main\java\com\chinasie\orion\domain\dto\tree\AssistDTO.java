package com.chinasie.orion.domain.dto.tree;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/21/11:21
 * @description:
 */
@Data
public class AssistDTO implements Serializable {

    @ApiModelProperty("添加的大修组织IDs")
    private List<String> addMajorRepairOrgIds;

    @ApiModelProperty("移除的大修组织IDs")
    private List<String> delMajorRepairOrgIds;

    @ApiModelProperty("作业编号")
    private String jobNumber;


}
