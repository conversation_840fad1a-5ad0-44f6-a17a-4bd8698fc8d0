<template>
  <BasicDrawer
    v-model:is-continue="state.isContinue"
    destroyOnClose
    showFooter
    :width="1200"
    :title="state.title"
    :show-continue="state.showContinue"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <DrawerForm ref="formModalRef" />
    <AsyncForm :isRequire="true" />
    <div class="ml20 mr20">
      汇报总结
    </div>
    <div class="ml20 mr20">
      <InputTextArea row="4" />
    </div>
    <AsyncForm
      :isRequire="false"
    />
    <template #appendFooter>
      <BasicButton type="primary">
        提交
      </BasicButton>
    </template>
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import { BasicDrawer, useDrawer, BasicButton } from 'lyra-component-vue3';
import { message, Input } from 'ant-design-vue';
import DrawerForm from './DrawerForm.vue';
import Api from '/@/api';
import AsyncForm from './AsyncForm.vue';

const InputTextArea = Input.TextArea;
const [modalRegister, modalMethods] = useDrawer();
const props = defineProps({
  // details: {
  //   type: Object,
  //   default: () => {},
  // },
});
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    isContinue: false,
    showContinue: true,
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
  }
  if (data.action === 'edit') {
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增周报');
  data?.action === 'edit' && (state.title = '编辑周报');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

// 确定
async function confirm() {
  await formModalRef.value && formModalRef.value.formMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      if (!state?.isContinue) {
        modalMethods.openDrawer(false);
      } else {
        formModalRef.value && formModalRef.value.formMethods.resetFields();
      }
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

// 请求操作
async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.formMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  // console.log('params', params);
  if (state.action === 'add') {
    return await new Api('').fetch('', '', 'POST');
  }
  if (state.action === 'edit') {
    return await new Api('').fetch('', '', 'POST');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
