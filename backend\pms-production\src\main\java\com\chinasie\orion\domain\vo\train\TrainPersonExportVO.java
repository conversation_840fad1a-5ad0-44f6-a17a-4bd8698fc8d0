package com.chinasie.orion.domain.vo.train;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/01/15:47
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class TrainPersonExportVO  implements Serializable {

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @ExcelProperty(value = "员工号 ", index = 0)
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 1)
    private String fullName;


    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @ExcelProperty(value = "性别 ", index = 2)
    private String sex;


    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    @ExcelProperty(value = "现任职务 ", index = 3)
    private String nowPosition;

    @ApiModelProperty(value = "所属中心")
    @ExcelProperty(value = "所属中心 ", index = 4)
    private String trainCenterName;



    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @ExcelProperty(value = "公司 ", index = 5)
    private String companyName;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门 ", index = 6)
    private String deptName;

    /**
     * 研究所名称
     */
    @ApiModelProperty(value = "研究所")
    @ExcelProperty(value = "研究所名称 ", index = 7)
    private String instituteName;


    /**
     * 到期日期
     */
    @ApiModelProperty(value = "进入基地时间")
    @ExcelProperty(value = "进入基地时间 ", index = 8)
    private Date endDate;

    @ApiModelProperty(value = "是否合格")
    @ExcelProperty(value = "是否合格 ", index = 9)
    private String isOK;

    @ApiModelProperty(value = "培训成绩")
    @ExcelProperty(value = "培训成绩 ", index = 10)
    private BigDecimal score;



}
