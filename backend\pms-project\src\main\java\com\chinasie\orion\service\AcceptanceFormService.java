package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormCreateDTO;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormQueryDTO;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.domain.vo.AcceptanceFormListVO;
import com.chinasie.orion.domain.vo.AcceptanceFormVO;
import com.chinasie.orion.domain.vo.FileExtVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface AcceptanceFormService extends OrionBaseService<AcceptanceForm> {
    /**
     * 获取验收单分页列表数据.
     *
     * @param pageRequest
     * @return
     */
    Page<AcceptanceFormListVO> pageInfo(Page<AcceptanceFormQueryDTO> pageRequest) throws Exception;

    /**
     * 创建验收单.
     *
     * @param createDTO
     * @return
     */
    AcceptanceFormVO createAcceptanceForm(AcceptanceFormCreateDTO createDTO) throws Exception;

    /**
     * 根据Id查询验收单详情.
     *
     * @param acceptanceFormId
     * @return
     */
    AcceptanceFormVO findById(String acceptanceFormId,String pageCode) throws Exception;

    /**
     * 批量获取数据
     * @param ids
     * @return
     * @throws Exception
     */
    List<AcceptanceFormVO> listByIds(List<String> ids) throws Exception;

    /**
     * 根据Id查询验收单详情.
     *
     * @param projectId
     * @return
     */
    AcceptanceFormVO findByProjectId(String projectId,String pageCode) throws Exception;

//    /**
//     * 分页获取验收单关联的采购计划明细.
//     *
//     * @param pageRequest
//     * @return
//     */
//    Page<ProcurementPlanApprovalListVO> pageQueryProcurementPlanApproval(Page<ProcurementPlanApprovalQueryDTO> pageRequest) throws Exception;

    /**
     * 变更验收单状态.
     *
     * @param acceptanceFormId
     * @param status
     * @return
     */
    Boolean changeAcceptanceFormStatus(String acceptanceFormId, String status) throws Exception;

    /**
     * 新增验收文件.
     *
     * @param acceptanceFormId
     * @param fileDTOList
     * @return
     * @throws Exception
     */
    Boolean saveRelatedFiles(String acceptanceFormId, List<FileDTO> fileDTOList) throws Exception;

    /**
     * 获取验收文件分页列表.
     *
     * @param acceptanceFormId
     * @param pageRequest
     * @return
     */
    Page<FileExtVO> listRelatedFiles(String acceptanceFormId, Page pageRequest) throws Exception;

    /**
     * 删除验收文件，解除验收文件和验收单关系.
     *
     * @param acceptanceFormId
     * @param fileIds
     * @return
     */
    Boolean deleteRelatedFile(String acceptanceFormId, List<String> fileIds) throws Exception;
}
