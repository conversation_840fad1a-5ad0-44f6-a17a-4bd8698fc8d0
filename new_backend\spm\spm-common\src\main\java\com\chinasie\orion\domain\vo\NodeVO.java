package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import org.springframework.data.redis.connection.DataType;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/12/16:35
 * @description:
 */
public class NodeVO<T> implements Serializable {
    @ApiModelProperty("节点ID")
    private String id;
    @ApiModelProperty("父级ID")
    private String parentId;
    @ApiModelProperty("节点名称")
    private String name;
    @ApiModelProperty("当前节点责任人")
    private String rspUserId;
    @ApiModelProperty("当前节点责任人名称")
    private String rspUserName;

    @ApiModelProperty("当前节点链路路径")
    private String chainPath;

    @Getter
    @ApiModelProperty(value = "数据id")
    private String dataId;

    @ApiModelProperty("当前节点唯一code")
    private String code;
    @ApiModelProperty("业务信息对象")
    private T data;
    @ApiModelProperty("当前节点类型")
    private String nodeType;
    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty("是否分割标识：表当前节点和下一个节点之间需要插入分割对象-空对象")
    private Boolean isDelimiter=Boolean.FALSE;

    private String DataType;

    private Set<String> roleList;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRspUserId() {
        return rspUserId;
    }

    public void setRspUserId(String rspUserId) {
        this.rspUserId = rspUserId;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public NodeVO() {
    }

    public NodeVO(String id, String parentId, String name, String rspUserId,  T data) {
        this.id = id;
        this.parentId = parentId;
        this.name = name;
        this.rspUserId = rspUserId;
        this.data = data;
    }

    public Set<String> getRoleList() {
        return roleList;
    }

    public void setRoleList(Set<String> roleList) {
        this.roleList = roleList;
    }

    public String getRspUserName() {
        return rspUserName;
    }

    public void setRspUserName(String rspUserName) {
        this.rspUserName = rspUserName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public Boolean getDelimiter() {
        return isDelimiter;
    }

    public void setDelimiter(Boolean delimiter) {
        isDelimiter = delimiter;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public Integer getSort() {
        return sort;
    }
    public void setSort(Integer sort) {
        this.sort = sort;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDataType() {
        return dataType;
    }
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getChainPath() {
		return chainPath;
	}

    public void setChainPath(String chainPath) {
		this.chainPath = chainPath;
	}

}
