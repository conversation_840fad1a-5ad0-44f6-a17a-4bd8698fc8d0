<script setup lang="ts">
import {
  provide, ref, Ref, toRaw, useAttrs,
} from 'vue';
import { message } from 'ant-design-vue';
import ModalMain from './ModalMain.vue';
import Api from '/@/api';
const attrs: Record<string, any> = useAttrs();
provide('attrs', attrs);
const mainRef: Ref = ref();

async function onOk() {
  return new Promise((resolve, reject) => {
    const tableData: Array<any> = mainRef.value.getSelectedData().map((item: Record<string, any>) => toRaw(item));
    const treeNode: Record<string, any> = toRaw(mainRef.value.getTreeNode());
    if (tableData.length === 0) {
      message.warning('请选择数据');
      reject();
      return;
    }
    if (attrs.treeApi) {
      (async () => {
        try {
          await attrs.onOk({
            treeNode,
            tableData,
          });
          resolve(true);
        } catch (e) {
          reject();
        }
      })();
      return;
    }
    new Api('/pms/projectInternalAssociation').fetch({
      innerId: tableData[0].id,
      innerName: tableData[0].name,
      name: treeNode.name,
      projectId: treeNode.number === 'project' ? tableData[0].id : tableData[0]?.projectId,
      type: treeNode.number,
    }, '', 'POST').then(async (res) => {
      await attrs.onOk({
        treeNode,
        tableData,
        associatedData: res,
      });
      resolve(true);
    }).catch((err) => {
      reject(err);
    });
  });
}

defineExpose({
  onOk,
});

</script>

<template>
  <ModalMain ref="mainRef" />
</template>

<style scoped lang="less">

</style>
