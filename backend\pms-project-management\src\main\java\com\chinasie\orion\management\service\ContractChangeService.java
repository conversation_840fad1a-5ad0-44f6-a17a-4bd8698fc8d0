package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractChangeDTO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.entity.ContractChange;
import com.chinasie.orion.management.domain.vo.ContractChangeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ContractChange 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface ContractChangeService extends OrionBaseService<ContractChange> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractChangeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractChangeDTO
     */
    String create(ContractChangeDTO contractChangeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractChangeDTO
     */
    Boolean edit(ContractChangeDTO contractChangeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     */
    Page<ContractChangeVO> pages(Page<ContractChangeDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合同变更信息
     * <p>
     * * @param code
     */
    Page<ContractChangeVO> getByCode(Page<ContractChangeDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ContractChangeVO> vos) throws Exception;
}
