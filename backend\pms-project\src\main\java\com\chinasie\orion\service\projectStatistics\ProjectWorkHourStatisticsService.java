package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectWorkHourStatisticsDTO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectWorkHourStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectWorkHourStatisticsService {
    ProjectWorkHourStatisticsVO getProjectWorkHourTotalStatistics(ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO);

    List<ProjectWorkHourStatisticsVO> getProjectWorkHourTimeStatistics(ProjectWorkHourStatisticsDTO projectWorkHourStatisticsDTO);

    Page<ProjectWorkHourStatisticsVO> getProjectWorkHourPages(Page<ProjectWorkHourStatisticsDTO> pageRequest) throws Exception;
}
