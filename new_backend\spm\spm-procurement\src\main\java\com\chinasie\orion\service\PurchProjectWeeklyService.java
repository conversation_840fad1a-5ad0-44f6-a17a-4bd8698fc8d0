package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.PurchProjectWeeklyDTO;
import com.chinasie.orion.domain.entity.PurchProjectWeekly;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyExcelVO;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * PurchProjectWeekly 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
public interface PurchProjectWeeklyService extends OrionBaseService<PurchProjectWeekly> {

    /**
     * 详情
     * <p>
     * * @param id
     */
    PurchProjectWeeklyVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param purchProjectWeeklyDTO
     */
    String create(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception;

    /**
     * 新增校验
     * <p>
     * * @param purchProjectWeeklyDTO
     */
    Boolean checkCreate(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param purchProjectWeeklyDTO
     */
    Boolean edit(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception;

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PurchProjectWeeklyVO> pages(Page<PurchProjectWeeklyDTO> pageRequest) throws Exception;

    /**
     * 导出
     */
    void exportByExcel(Page<PurchProjectWeeklyDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     */
    void setEveryName(List<PurchProjectWeeklyVO> vos) throws Exception;

    /**
     * 报表
     * @param pageRequest
     * @return
     */
    Page<PurchProjectWeeklyExcelVO> pageExcel(Page<PurchProjectWeeklyDTO> pageRequest);

    /**
     * 查询上周工作安排
     * @param purchProjectWeeklyDTO
     * @return
     */
    String selectLastWeekPlan(PurchProjectWeeklyDTO purchProjectWeeklyDTO);

    /**
     * 查询已填的周
     * @param purchProjectWeeklyDTO
     * @return
     */
    List<Integer> selectFinishWeek(PurchProjectWeeklyDTO purchProjectWeeklyDTO);
}
