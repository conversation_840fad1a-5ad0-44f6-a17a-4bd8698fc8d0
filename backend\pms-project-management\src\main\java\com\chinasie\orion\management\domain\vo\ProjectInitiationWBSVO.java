package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectInitiationWBS VO对象
 *
 * <AUTHOR>
 * @since 2024-07-16 14:52:10
 */
@ApiModel(value = "ProjectInitiationWBSVO对象", description = "项目立项WBS预算")
@Data
public class ProjectInitiationWBSVO extends ObjectVO implements Serializable {

    /**
     * 项目层次等级
     */
    @ApiModelProperty(value = "项目层次等级")
    private String projectLevel;


    /**
     * 工作分解结构元素 (WBS 元素)
     */
    @ApiModelProperty(value = "工作分解结构元素 (WBS 元素)")
    private String wbsElement;


    /**
     * PS: 短描述 (第一行文本)
     */
    @ApiModelProperty(value = "PS: 短描述 (第一行文本)")
    private String description;


    /**
     * 功能范围
     */
    @ApiModelProperty(value = "功能范围")
    private String functionalScope;


    /**
     * I开头状态
     */
    @ApiModelProperty(value = "I开头状态")
    private String initialStatus;


    /**
     * 利润中心编码
     */
    @ApiModelProperty(value = "利润中心编码")
    private String profitCenterCode;


    /**
     * 利润中心名称
     */
    @ApiModelProperty(value = "利润中心名称")
    private String profitCenterName;


    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String company;


    /**
     * 负责人编号
     */
    @ApiModelProperty(value = "负责人编号")
    private String directorCode;


    /**
     * 负责人姓名（项目管理者）
     */
    @ApiModelProperty(value = "负责人姓名（项目管理者）")
    private String directorName;


    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    private String projectNumber;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private String business;


    /**
     * 业务分类名称
     */
    @ApiModelProperty(value = "业务分类名称")
    private String businessName;


}
