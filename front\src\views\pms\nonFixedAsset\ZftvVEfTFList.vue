<script setup lang="ts">
import {
  Layout,
  OrionTable,
  BasicTableAction,
  IOrionTableActionItem,
  BasicButton,
  BasicImport,
  useModal,
  downloadByData,
  isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import ZftvVEfTFEdit from './ZftvVEfTFEdit.vue';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '资产条码',
      dataIndex: 'barcode',
      customRender({ text, record }) {
        if (isPower('PMS_FGDZCBZK_container_02_button_02', powerData.value)) {
          return h('span', {
            class: 'flex-te action-btn',
            title: text,
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('span', {
          class: 'flex-te',
          title: text,
        }, text);
      },
    },
    {
      title: '资产名称',
      dataIndex: 'name',
    },
    {
      title: '规格型号',
      dataIndex: 'spModel',
    },
    {
      title: '是否需要检定',
      dataIndex: 'isNeedVerification',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
  ],
  smallSearchField: ['barcode', 'name'],
  api: (params: Record<string, any>) => new Api('/pms/non-fixed-assets').fetch({
    ...params,
    power: {
      containerCode: 'table-list-container-042a7f-ZftvVEfTF-SARy3AYf',
      pageCode: 'list-container-042a7f-ZftvVEfTF',
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增资产',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_FGDZCBZK_container_01_button_01',
  },
  {
    event: 'import',
    text: '导入',
    icon: 'sie-icon-daoru',
    powerCode: 'PMS_FGDZCBZK_container_01_button_02',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    powerCode: 'PMS_FGDZCBZK_container_01_button_03',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
    powerCode: 'PMS_FGDZCBZK_container_01_button_04',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(ZftvVEfTFEdit, null, updateTable);
      break;
    case 'import':
      openImportModal(true, {});
      break;
    case 'export':
      Modal.confirm({
        title: '导出提示！',
        content: '确认导出数据？',
        onOk() {
          return downloadByData('/pms/non-fixed-assets/export/excel', []);
        },
      });
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_FGDZCBZK_container_02_button_01', powerData.value),
  },
  {
    text: '查看',
    event: 'view',
    isShow: () => isPower('PMS_FGDZCBZK_container_02_button_02', powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower('PMS_FGDZCBZK_container_02_button_03', powerData.value),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(ZftvVEfTFEdit, record, updateTable);
      break;
    case 'view':
      navDetails(record?.id);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSNonFixedAssetDetails',
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/non-fixed-assets').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

const [register, { openModal: openImportModal }] = useModal();
const downloadFileObj = {
  url: '/pms/non-fixed-assets/download/excel/tpl',
  method: 'GET',
};

function requestBasicImport(files: any[]) {
  const formData = new FormData();
  formData.append('file', files[0]);
  return new Api('/pms/non-fixed-assets/import/excel/check').fetch(formData, '', 'POST');
}

function requestSuccessImport(importId: string) {
  return new Promise((resolve, reject) => {
    new Api(`/pms/non-fixed-assets/import/excel/${importId}`).fetch('', '', 'POST').then(() => {
      updateTable();
      resolve(true);
    }).catch((e) => {
      reject(e);
    });
  });
}

function changeImportModalFlag({ succ, successImportFlag }) {
  if (!successImportFlag && succ) {
    return new Api(`/pms/non-fixed-assets/import/excel/cancel/${succ}`).fetch('', '', 'POST');
  }
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSNonFixedAsset',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>

    <BasicImport
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      :downloadFileObj="downloadFileObj"
      @register="register"
      @change-import-modal-flag="changeImportModalFlag"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
