package com.chinasie.orion.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.manager.SchemeStatusProcessor;
import com.chinasie.orion.service.ProjectSchemeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 逾期
 * 临期
 * 正常
 * <p>
 * 已创建（待下发）
 * 已下发（进行中）
 * 已完成
 */
@Slf4j
@Component
@AllArgsConstructor
public class SchemeStatusProcessorImpl implements SchemeStatusProcessor {

    private final ProjectSchemeService projectSchemeService;

    @Override
    public void statusHandle(String projectId) throws Exception {
        List<ProjectScheme> projectSchemes = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class)
                .eq(ProjectScheme::getProjectId, projectId));
        statusHandle(projectSchemes);
    }


    @Override
    public void statusHandleById(List<String> projectSchemeIds) throws Exception {
        if (CollUtil.isEmpty(projectSchemeIds)) {
            log.warn("项目计划状态处理，传入项目计划id为空");
            return;
        }
        List<ProjectScheme> projectSchemes = projectSchemeService.listByIds(projectSchemeIds);
        statusHandle(projectSchemes);
    }

    @Override
    public void statusHandle(List<ProjectScheme> projectSchemes) throws Exception {
        if (CollUtil.isEmpty(projectSchemes)) {
            log.warn("不存在项目计划");
            return;
        }
        List<ProjectScheme> projectSchemeList = projectSchemes.stream().filter(item -> overdue(item)).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(projectSchemeList)){
            return;
        }
        LambdaUpdateWrapper<ProjectScheme> updateWrapper = new LambdaUpdateWrapper<>();
//        projectSchemeList.stream().forEach(item->{
//            updateWrapper.clear();
//            updateWrapper.set(ProjectScheme::getCircumstance,item.getCircumstance());
//            updateWrapper.eq(ObjectEntity::getId,item.getId());
//            projectSchemeService.update(updateWrapper);
//        });
        if(CollectionUtils.isEmpty(projectSchemes)){
            return;
        }
        Map<Integer,List<ProjectScheme>> map =projectSchemeList.stream().collect(Collectors.groupingBy(ProjectScheme::getCircumstance));
        for (Map.Entry<Integer, List<ProjectScheme>> integerListEntry : map.entrySet()) {
            List<ProjectScheme> list = integerListEntry.getValue();
            if(!CollectionUtils.isEmpty(list)){
                updateWrapper.clear();
                updateWrapper.set(ProjectScheme::getCircumstance,integerListEntry.getKey());
                updateWrapper.in(ObjectEntity::getId,list.stream().map(ObjectEntity::getId).distinct().collect(Collectors.toList()));
                projectSchemeService.update(updateWrapper);
            }
        }

//        projectSchemeService.updateBatchById(projectSchemeList);
    }

    /**
     * 逾期处理
     * 临期处理
     *
     * @param scheme
     */
    private Boolean overdue(ProjectScheme scheme) {
        if (Status.PENDING.getCode().equals(scheme.getStatus()) || Status.FALLBACK.getCode().equals(scheme.getStatus())&&Status.CONFIRMED.getCode().equals(scheme.getStatus())){
            return Boolean.FALSE;
        }
        if(Status.ISSUE_APPROVAL.getCode().equals(scheme.getCircumstance())||
                Status.AFFIRM_APPROVAL.getCode().equals(scheme.getCircumstance())||
                Status.SUSPEND_APPROVAL.getCode().equals(scheme.getCircumstance())||
                Status.TERMINATION_APPROVAL.getCode().equals(scheme.getCircumstance())||
                Status.START_APPROVAL.getCode().equals(scheme.getCircumstance())){
            return Boolean.FALSE;
        }
        if (Objects.isNull(scheme.getEndTime())){
            return Boolean.FALSE;
        }
        // 创建一个Calendar实例，用于后续操作日期
        Calendar calendar = Calendar.getInstance();
        // 设置Calendar的时间为方案的结束时间
        calendar.setTime(scheme.getEndTime());
        // 将日期向后增加一天，目的是获取结束时间的下一天
        calendar.add(Calendar.DATE, 1);
        // 将时间减去一秒，确保得到的结束时间是结束日期的最后一天的最后一秒
        calendar.add(Calendar.SECOND, -1);
        // 获取经过修改后的日期时间，并赋值给endTime
        Date endTime = calendar.getTime();
        //TODO 审查 吴锋 最好改成hutool工具
//        DateTime dateTime = DateUtil.endOfDay(scheme.getEndTime());

        if (Status.FINISHED.getCode().equals(scheme.getStatus())) {
            if (Objects.nonNull(scheme.getActualEndTime()) && scheme.getActualEndTime().after(endTime)) {
                if(isEqu(scheme.getCircumstance(),Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode())){
                    scheme.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_OVERD.getCode());
                    return Boolean.TRUE;
                }
            }else{
                if(isEqu(scheme.getCircumstance(),Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode())){
                    scheme.setCircumstance(Status.CIRCUMSTANCE_COMPLETE_NORMAL.getCode());
                    return Boolean.TRUE;
                }
            }
        }
        // 状态执行中 执行情况逾期的 结束时间小于当天 改为 逾期
        if (Status.EXECUTING.getCode().equals(scheme.getStatus())&&new Date().after(endTime)
            && isEqu(scheme.getCircumstance(),Status.CIRCUMSTANCE_OVERD.getCode())) {
            scheme.setCircumstance(Status.CIRCUMSTANCE_OVERD.getCode());
            return Boolean.TRUE;
        }
        //计算结束日期和当前时间天数
        LocalDate date1 = LocalDate.now();
        Instant instant = endTime.toInstant();
        LocalDate date2 = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        long diffInDays = ChronoUnit.DAYS.between(date1, date2);


        // 状态执行中 执行情况临期的 结束时间小于5天 改为 已临期
        if (Status.EXECUTING.getCode().equals(scheme.getStatus()) && 0 <= diffInDays && diffInDays < 5) {
            scheme.setCircumstance(Status.CIRCUMSTANCE_NEAR.getCode());
            return Boolean.TRUE;
        }
        if (Status.EXECUTING.getCode().equals(scheme.getStatus()) && diffInDays >= 5) {
            scheme.setCircumstance(Status.CIRCUMSTANCE_NORMAL.getCode());
            return Boolean.TRUE;
        }
        // 执行中 正常的 改为 临期
//        if (Status.EXECUTING.getCode().equals(scheme.getStatus()) && isEqu(scheme.getCircumstance(),Status.CIRCUMSTANCE_NORMAL.getCode())) {
//            scheme.setCircumstance(Status.CIRCUMSTANCE_NEAR.getCode());
//            return Boolean.TRUE;
//        }
        return Boolean.FALSE;
    }


    public Boolean isEqu(Integer code,Integer circumstance){
        if(Objects.equals(code,circumstance)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
