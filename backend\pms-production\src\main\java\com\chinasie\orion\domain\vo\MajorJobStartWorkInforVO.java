package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;
/**
 * MajorJobStartWorkInfor VO对象
 *
 * <AUTHOR>
 * @since 2024-11-19 09:46:56
 */
@ApiModel(value = "MajorJobStartWorkInforVO对象", description = "大修工单开工信息")
@Data
public class MajorJobStartWorkInforVO extends  ObjectVO   implements Serializable{

            /**
         * 工单编号
         */
        @ApiModelProperty(value = "工单编号")
        private String jobNumber;


        /**
         * 开工日期
         */
        @ApiModelProperty(value = "开工日期")
        private Date startWorkDate;
        @ApiModelProperty(value = "开工日期-字符串")
        private String startWorkDateStr;

        /**
         * 上午开工状态
         */
        @ApiModelProperty(value = "上午开工状态")
        private Boolean morningStatus;


        /**
         * 下午开工状态
         */
        @ApiModelProperty(value = "下午开工状态")
        private Boolean afternoonStatus;


        /**
         * 夜间状态
         */
        @ApiModelProperty(value = "夜间状态")
        private Boolean nightStatus;


    

}
