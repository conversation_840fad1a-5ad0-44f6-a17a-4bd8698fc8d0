package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobPackage DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:59
 */
@ApiModel(value = "JobPackageDTO对象", description = "作业工作包信息")
@Data
@ExcelIgnoreUnannotated
public class JobPackageDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @ExcelProperty(value = "作业id ", index = 0)
    private String jobId;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    @ExcelProperty(value = "功能位置 ", index = 1)
    private String functionalLocation;

    /**
     * 设备/系统
     */
    @ApiModelProperty(value = "设备/系统")
    @ExcelProperty(value = "设备/系统 ", index = 2)
    private String equipmentSystem;




}
