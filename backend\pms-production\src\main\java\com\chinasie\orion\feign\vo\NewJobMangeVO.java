package com.chinasie.orion.feign.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/14/16:38
 * @description:
 */
@Data
public class NewJobMangeVO implements Serializable {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @JSONField(name = "aufnr")
    private String number;

    /**
     * 作业名
     */
    @ApiModelProperty(value = "作业名称")
    @JSONField(name = "ktext")
    private String name;


    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    @JSONField(name = "norO")
    private String nOrO;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @JSONField(name = "revnr")
    private String repairRound;


    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    @JSONField(name = "arbpl")
    private String workCenter;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    private String rspDept;

    /**
     * 作业基地
     */
    @ApiModelProperty(value = "作业基地")
    private String jobBaseName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer workDuration;

    /**
     * 实际开工时间
     */
    @ApiModelProperty(value = "实际开工时间")
    @JSONField(name = "snDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualBeginTime;

    /**
     * 实际完工时间
     */
    @ApiModelProperty(value = "实际完工时间")
    @JSONField(name = "soDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;

    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    private String phase;


}
