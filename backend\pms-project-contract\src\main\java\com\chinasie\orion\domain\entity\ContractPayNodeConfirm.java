package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractPayNodeConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@TableName(value = "pms_contract_pay_node_confirm")
@ApiModel(value = "ContractPayNodeConfirm对象", description = "项目合同支付节点确认")
@Data
public class ContractPayNodeConfirm extends ObjectEntity implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 审核编号
     */
    @ApiModelProperty(value = "审核编号")
    @TableField(value = "number")
    private String number;

    /**
     * 材料审核人
     */
    @ApiModelProperty(value = "材料审核人")
    @TableField(value = "audit_user_id")
    private String auditUserId;

    /**
     * 实际材料审核人
     */
    @ApiModelProperty(value = "实际材料审核人")
    @TableField(value = "actual_audit_user_id")
    private String actualAuditUserId;


    /**
     * 最新提交id
     */
    @ApiModelProperty(value = "最新提交id")
    @TableField(value = "submit_id")
    private String submitId;


    /**
     * 审核记录id
     */
    @ApiModelProperty(value = "审核记录id")
    @TableField(value = "audit_id")
    private String auditId;


    /**
     * 材料提交人
     */
    @ApiModelProperty(value = "材料提交人")
    @TableField(value = "submit_user_id")
    private String submitUserId;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @TableField(value = "audit_date")
    private Date auditDate;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @TableField(value = "submit_date")
    private Date submitDate;

    /**
     * 服务确认是否已完成
     */
    @ApiModelProperty(value = "服务确认是否已完成")
    @TableField(value = "service_complete")
    private String serviceComplete;

    /**
     * 节点确认说明
     */
    @ApiModelProperty(value = "节点确认说明")
    @TableField(value = "confirm_desc")
    private String confirmDesc;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    @TableField(value = "audit_desc")
    private String auditDesc;

}
