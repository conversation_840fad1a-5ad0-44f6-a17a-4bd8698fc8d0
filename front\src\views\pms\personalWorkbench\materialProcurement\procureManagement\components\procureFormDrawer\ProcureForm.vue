<script setup lang="ts">
import { BasicScrollbar, BasicTabs } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import Info from './components/Info.vue';
import SupplyReceipt from './components/SupplyReceipt.vue';
import Files from './components/Files.vue';

const tabsIndex: Ref<number> = ref(0);
const tabs: Record<string, string>[] = [
  { name: '订单信息' },
  { name: '订单供收' },
  { name: '订单附件' },
];
const infoRef:Ref = ref();
const supplyRef:Ref = ref();
const filesRef:Ref = ref();

defineExpose({
  infoValidate: () => infoRef.value?.validate(),
  getOrderList: () => infoRef.value?.validateOrderList(),
  supplyValidate: () => supplyRef.value?.validate(),
  getFileList: () => filesRef.value?.getData(),
});

</script>

<template>
  <BasicTabs
    v-model:tabsIndex="tabsIndex"
    :tabs="tabs"
  >
    <template #tabsItem="{item,index}">
      <div :class="[{'required':index!==2}]">
        {{ item.name }}
      </div>
    </template>
  </BasicTabs>
  <BasicScrollbar style="height: calc(100% - 50px)">
    <!--订单信息-->
    <Info
      v-show="tabsIndex===0"
      ref="infoRef"
    />
    <!--订单供收-->
    <SupplyReceipt
      v-show="tabsIndex===1"
      ref="supplyRef"
    />
    <!--订单附件-->
    <Files
      v-show="tabsIndex===2"
      ref="filesRef"
    />
  </BasicScrollbar>
</template>

<style lang="less">
.required {
  &::before {
    display: inline-block;
    font-family: SimSun, sans-serif;
    margin-right: 4px;
    color: #ff4d4f;
    line-height: 1;
    content: "*";
  }
}
</style>
