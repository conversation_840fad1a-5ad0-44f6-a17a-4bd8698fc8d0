package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/05/18/22:26
 * @description:
 */
@ApiModel(value = "MoneyStatisticsVO对象", description = "金额统计VO")
@Data
public class MoneyStatisticsVO {
    @ApiModelProperty(value = "总体预算金额")
    private BigDecimal allBudgeMoney;
    @ApiModelProperty(value = "总体实际金额")
    private BigDecimal allPracticalMoney;
    @ApiModelProperty(value = "总体立项金额")
    private BigDecimal allProjectApprovalMoney;

    @ApiModelProperty(value = "总体合同金额")
    private BigDecimal allContactMoney;

    public MoneyStatisticsVO(BigDecimal allBudgeMoney, BigDecimal allPracticalMoney, BigDecimal allProjectApprovalMoney, BigDecimal allContactMoney) {
        this.allBudgeMoney = allBudgeMoney;
        this.allPracticalMoney = allPracticalMoney;
        this.allProjectApprovalMoney = allProjectApprovalMoney;
        this.allContactMoney = allContactMoney;
    }

    public MoneyStatisticsVO() {
    }
}
