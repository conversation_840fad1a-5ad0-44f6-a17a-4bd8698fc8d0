package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.MilestoneIncomeAllocationDTO;
import com.chinasie.orion.domain.entity.MilestoneIncomeAllocation;
import com.chinasie.orion.domain.vo.MilestoneIncomeAllocationVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MilestoneIncomeAllocation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07 10:50:47
 */
public interface MilestoneIncomeAllocationService extends OrionBaseService<MilestoneIncomeAllocation> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    MilestoneIncomeAllocationVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param milestoneIncomeAllocationDTO
     */
    String create(MilestoneIncomeAllocationDTO milestoneIncomeAllocationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param milestoneIncomeAllocationDTO
     */
    Boolean edit(MilestoneIncomeAllocationDTO milestoneIncomeAllocationDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<MilestoneIncomeAllocationVO> pages(Page<MilestoneIncomeAllocationDTO> pageRequest) throws Exception;


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<MilestoneIncomeAllocationVO> vos) throws Exception;
}
