package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProductPlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
@TableName(value = "pms_product_plan")
@ApiModel(value = "ProductPlanEntity对象", description = "产品策划")
@Data

public class ProductPlan extends  ObjectEntity  implements Serializable{

    /**
     * 项目立项ID
     */
    @ApiModelProperty(value = "项目立项ID")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 技术指标描述
     */
    @ApiModelProperty(value = "技术指标描述")
    @TableField(value = "description")
    private String description;

    /**
     * 技术指标名称
     */
    @ApiModelProperty(value = "技术指标名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
