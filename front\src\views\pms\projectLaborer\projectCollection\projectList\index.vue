<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="isTable"
      class="card-list-table"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="add"
          @click="addNode"
        >
          创建项目集
        </BasicButton>
        <BasicButtonGroup>
          <BasicButton
            :type="isTable?'':'primary'"
            :ghost="!isTable"
            @click="isTable=false"
          >
            卡片视图
          </BasicButton>
          <BasicButton
            :type="isTable?'primary':''"
            :ghost="isTable"
            @click="isTable=true"
          >
            列表视图
          </BasicButton>
        </BasicButtonGroup>
      </template>
      <template #projectApproveTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectStartTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectEndTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>

      <template #schedule="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>

      <template #statusIdName="{ record }">
        <DataStatusTag :status-data="record?.dataStatus" />
      </template>

      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>

      <template
        v-if="!isTable"
        #otherContent="{dataSource}"
      >
        <div
          v-if="dataSource.length"
          ref="cardGrid"
          class="card-grid"
          :style="{'grid-template-columns': `repeat(${gridNum}, minmax(340px,1fr))`}"
        >
          <CardItem
            v-for="item in dataSource"
            :key="item.id"
            :powerData="powerData"
            :record="item"
            :onActionClick="actionClick"
          />
        </div>
        <Empty
          v-else
          class="w-full h-full flex flex-ver flex-ac flex-pc"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </template>
    </OrionTable>
    <checkDetails :data="nodeData" />
    <searchModal
      v-model:visible="searchModalVisible"
      :data="searchData"
      @search="searchTable"
    />
    <PushModel />

    <AddTableNode
      @update="successSave"
      @register="registerAdd"
    />
  </Layout>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  getCurrentInstance,
  h, inject,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  Ref,
  toRefs,
  unref, watch,
} from 'vue';
import {
  BasicButton,
  BasicButtonGroup,
  BasicTableAction,
  DataStatusTag, getDict,
  isPower,
  ITableActionItem,
  Layout,
  OrionTable,
  useDrawer,
  useProjectPower,
} from 'lyra-component-vue3';
import {
  Empty, message, Progress, Modal,
} from 'ant-design-vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import checkDetails from './components/checkmodal.vue';
import searchModal from './components/searchModal.vue';
import Api from '/@/api';

import AddTableNode from './components/AddTableNode.vue';
import CardItem from './components/CardItem.vue';

const tableRef = ref(null);
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    PushModel,
    Layout,
    checkDetails,
    Progress,
    searchModal,
    AddTableNode,
    DataStatusTag,
    OrionTable,
    BasicButtonGroup,
    BasicButton,
    BasicTableAction,
    CardItem,
    Empty,
  },
  setup() {
    let ids: string[] = [];
    try {
      ids = JSON.parse(sessionStorage.getItem('ids'));
    } finally {
      sessionStorage.removeItem('ids');
    }

    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    const [registerModal, { openDrawer: openDrawerModal }] = useDrawer();
    const isTable: Ref<boolean> = ref(false);
    const powerData: any = inject('powerData', {});
    const state = reactive({
      searchvlaue: '',
      showVisible: false,
      message: '',
      powerData: [],
      nodeData: [],
      // 搜索弹窗
      searchModalVisible: false,
      searchData: {},
      params: {},
      queryCondition: [],
      searchStatus: '',
      projectCollectionTypeDict: [],
      projectCollectionLevelDict: [],
      // btnList: [
      //   { type: 'edit', powerCode: 'PMS_XMLB_container_01_button_01' },
      //   { type: 'delete', powerCode: 'PMS_XMLB_container_01_button_02' },
      // ],
    });

    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    // const internalInstance = getCurrentInstance();
    // // 获取权限
    // async function getProjectPower() {
    //   return new Promise((resolve, reject) => {
    //     useProjectPower(
    //       { pageCode: 'PMS0003' },
    //       (powerList) => {
    //         resolve(powerList || []);
    //       },
    //       internalInstance,
    //     );
    //   });
    // }
    onMounted(async () => {
      // state.powerData = await getProjectPower();
      onResize();
      getAllDict();
      window.addEventListener('resize', onResize);
    });
    const router = useRouter();
    const zkKeys = () => getTableAction().getSelectRowKeys();
    const searchTable = (params) => {
      state.searchStatus = 'eveSearch';
      state.queryCondition = params.queryCondition;
      state.params = params.params;
      successSave();
    };
    const toDetails = (data) => {
      router.push({
        name: 'ProjectCollectionDetail',
        query: {
          id: data.id,
        },
      });
    };
    const addNode = () => {
      openDrawerAdd(true, { type: 'add' });
    };
    const onSearch = () => {
      state.searchStatus = 'oddSearch';
      successSave();
    };
    const successSave = () => {
      getTableAction().reload({
        page: 1,
      });
      getTableAction().clearSelectedRowKeys();
      state.searchStatus = '';
    };

    function getListParams(params) {
      if (ids?.length) {
        params.query = {
          ids,
          jumpFlag: true,
        };
      }
      return params;
      // if (params.searchConditions) {
      //   return {
      //     ...params,
      //     queryCondition: params.searchConditions.map((item) => ({
      //       column: item?.[0]?.field,
      //       type: 'like',
      //       link: 'or',
      //       value: item?.[0]?.values?.[0],
      //     })),
      //   };
      // }
      // return params;
    }

    function reloadTable() {
      tableRef.value?.reload();
    }

    function deleteRecords(ids: string[]) {
      return new Api('/pms')
        .fetch(ids, 'projectCollection', 'DELETE')
        .then(() => {
          message.success('删除成功');
        });
    }

    const tableOptions = {
      rowSelection: {},
      smallSearchField: ['name', 'number'],
      deleteToolButton: 'add|delete|enable|disable',
      api: (params) => new Api('/pms/projectCollection/pages').fetch({
        ...getListParams(params),
      }, '', 'POST').then((res) => res),
      batchDeleteApi({ ids }) {
        return deleteRecords(ids);
      },
      columns: [
        {
          title: '项目名称',
          dataIndex: 'name',
          minWidth: 220,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: 'action-btn',
                title: text,
                onClick(e) {
                  toDetails(record);

                  e.stopPropagation();
                },
              },
              text,
            );
          },
        },

        {
          title: '状态',
          dataIndex: 'statusIdName',
          width: '100px',
          slots: { customRender: 'statusIdName' },
        },
        {
          title: '负责人',
          dataIndex: 'resPersonName',
          width: '120px',
        },
        // {
        //   title: '项目集合类型',
        //   dataIndex: 'projectCollectionType',
        //   width: '120px',
        //   customRender: ({
        //     text,
        //   }) => state.projectCollectionTypeDict.find((item) => item.value === text)?.description,
        // },
        {
          title: '项目集级别',
          dataIndex: 'projectCollectionLevel',
          width: '120px',
          customRender: ({
            text,
          }) => state.projectCollectionLevelDict.find((item) => item.value === text)?.description,
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '--';
          },
        },
        {
          title: '描述',
          dataIndex: 'remark',
          width: '120px',
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn: ITableActionItem[] = [
      {
        text: '编辑',
        // isShow: computed(() => state.btnList.some((item) => item.type === 'edit')),

        onClick(record: any) {
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
        },
      },
      {
        text: '删除',
        // isShow: computed(() => state.btnList.some((item) => item.type === 'delete')),

        modal(record: any) {
          return deleteRecords([record.id]).then(() => {
            reloadTable();
          });
        },
      },
    ];

    onUnmounted(() => {
      window.removeEventListener('resize', onResize);
    });

    const gridNum: Ref<number> = ref();

    watch(() => tableRef.value, () => {
      onResize();
    });
    function onResize() {
      if (!tableRef.value) {
        return;
      }
      const tableWidth = tableRef.value.$el.clientWidth - 60;
      let num = parseInt(tableWidth / 340);
      gridNum.value = parseInt(tableWidth / (340 + Math.ceil((num - 1 < 0 ? 0 : num - 1) * 20) / num));
    }

    /**
     * 用户收藏项目
     * @param params
     */
    async function postUserLike(params: { projectId: string }) {
      return new Api('/pms/user-like-project').fetch(params, '', 'post');
    }

    /**
     * 用户取消收藏项目
     * @param params
     */
    async function deleteUserLike(params: Array<string>) {
      return new Api('/pms/user-like-project').fetch(params, '', 'delete');
    }

    // 操作区点击事件
    const actionClick = async (key, record) => {
      switch (key) {
        case 'edit':
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
          break;
        case 'del':
          Modal.confirm({
            title: '删除确认提示？',
            content: '请确认是否删除该项目，删除后不可恢复？',
            onOk() {
              return deleteRecords([record.id]).then(() => {
                reloadTable();
              });
            },
          });
          break;
        case 'look':
          return toDetails(record);
          // 收藏
        case 'collect':
          if (record.like) {
            Modal.confirm({
              title: '温馨提示',
              content: '请确认是否取消关注该项目？',
              onOk() {
                return new Promise((resolve) => {
                  deleteUserLike([record.like.id])
                    .then(() => {
                      getTableAction().reload({
                        page: 1,
                      });
                      message.success('已取消关注');
                      resolve(true);
                    })
                    .catch(() => {
                      resolve('');
                    });
                });
              },
              onCancel() {
                Modal.destroyAll();
              },
            });
          } else {
            await postUserLike({
              projectId: record.id,
            });
            getTableAction().reload({
              page: 1,
            });
            message.success(`${record.name}关注成功！`);
          }
          break;
      }
    };
    async function getAllDict() {
      state.projectCollectionTypeDict = await getDict('dict1774974536895303680').then((res) => res?.map((item) => ({
        ...item,
        label: item.description,
        value: item.value,
      })) ?? []);

      state.projectCollectionLevelDict = await getDict('dict1774974891121053696').then((res) => res?.map((item) => ({
        ...item,
        label: item.description,
        value: item.value,
      })) ?? []);
    }
    return {
      ...toRefs(state),
      isTable,
      addNode,
      dayjs,
      onSearch,
      successSave,
      searchTable,
      tableRef,
      isPower,
      registerModal,
      registerAdd,
      tableOptions,
      actionsBtn,
      Empty,
      onResize,
      gridNum,
      actionClick,
    };
  },
});
</script>
<style lang="less" scoped>
.card-grid {
  display: grid;
  gap: 16px 20px;
}

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
</style>
