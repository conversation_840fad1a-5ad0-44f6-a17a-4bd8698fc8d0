<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright MapStruct Authors.

    Licensed under the Apache License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.mapstruct</groupId>
    <artifactId>mapstruct-parent</artifactId>
    <version>1.3.1.Final</version>
    <packaging>pom</packaging>

    <name>MapStruct Parent</name>
    <description>An annotation processor for generating type-safe bean mappers</description>
    <url>http://mapstruct.org/</url>
    <inceptionYear>2012</inceptionYear>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <com.jolira.hickory.version>1.0.0</com.jolira.hickory.version>
        <!-- We can't go to 3.0.0-M2 as it has a regression. See https://issues.apache.org/jira/browse/MENFORCER-306 -->
        <org.apache.maven.plugins.enforcer.version>3.0.0-M1</org.apache.maven.plugins.enforcer.version>
        <org.apache.maven.plugins.surefire.version>2.22.1</org.apache.maven.plugins.surefire.version>
        <org.apache.maven.plugins.javadoc.version>3.0.1</org.apache.maven.plugins.javadoc.version>
        <org.springframework.version>4.0.3.RELEASE</org.springframework.version>
        <org.eclipse.tycho.compiler-jdt.version>0.26.0</org.eclipse.tycho.compiler-jdt.version>
        <com.puppycrawl.tools.checkstyle.version>8.14</com.puppycrawl.tools.checkstyle.version>
        <add.release.arguments />
        <forkCount>1</forkCount>
        <assertj.version>3.11.1</assertj.version>
        <!-- automatically run annotation processors within the incremental compilation -->
        <!-- Needed for the hickory processor-->
        <m2e.apt.activation>jdt_apt</m2e.apt.activation>
    </properties>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>gunnarmorling</id>
            <name>Gunnar Morling</name>
            <email><EMAIL></email>
            <url>http://www.gunnarmorling.de/</url>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://github.com/mapstruct/mapstruct.git</connection>
        <developerConnection>scm:git:**************:mapstruct/mapstruct.git</developerConnection>
        <url>https://github.com/mapstruct/mapstruct/</url>
      <tag>1.3.1.Final</tag>
  </scm>

    <distributionManagement>
        <repository>
            <id>sonatype-nexus-staging</id>
            <name>Nexus Release Repository</name>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <snapshotRepository>
            <id>sonatype-nexus-snapshots</id>
            <name>Sonatype Nexus Snapshots</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <issueManagement>
        <system>GitHub Issues</system>
        <url>https://github.com/mapstruct/mapstruct/issues</url>
    </issueManagement>

    <ciManagement>
        <system>Travis CI</system>
        <url>https://travis-ci.org/mapstruct/mapstruct</url>
    </ciManagement>

    <mailingLists>
        <mailingList>
            <name>mapstruct-users</name>
            <archive>https://groups.google.com/forum/?fromgroups#!forum/mapstruct-users</archive>
        </mailingList>
    </mailingLists>

    <prerequisites>
        <maven>3.0.3</maven>
    </prerequisites>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.21</version>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>19.0</version>
            </dependency>
            <dependency>
                <groupId>com.jolira</groupId>
                <artifactId>hickory</artifactId>
                <version>${com.jolira.hickory.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
            </dependency>
            <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>${com.puppycrawl.tools.checkstyle.version}</version>
            </dependency>

            <!-- CDI, Weld, Arquillian -->
            <dependency>
                <groupId>javax.enterprise</groupId>
                <artifactId>cdi-api</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>1</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.arquillian</groupId>
                <artifactId>arquillian-bom</artifactId>
                <version>1.0.2.Final</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.jboss.arquillian.container</groupId>
                <artifactId>arquillian-weld-se-embedded-1.1</artifactId>
                <version>1.0.0.CR7</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.weld</groupId>
                <artifactId>weld-core</artifactId>
                <version>2.3.2.Final</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>3.0-b01</version>
            </dependency>

            <!-- Spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${org.springframework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${org.springframework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${org.springframework.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.3</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.18</version>
            </dependency>
            <dependency>
                <groupId>org.immutables</groupId>
                <artifactId>value</artifactId>
                <version>2.5.6</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.value</groupId>
                <artifactId>auto-value</artifactId>
                <version>1.5</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.6.0</version>
            </dependency>
            <dependency>
                <groupId>org.inferred</groupId>
                <artifactId>freebuilder</artifactId>
                <version>1.14.6</version>
            </dependency>

            <!-- Joda-Time -->
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.9</version>
            </dependency>

            <!-- Plexus Eclipse Compiler -->
            <dependency>
                <groupId>org.eclipse.tycho</groupId>
                <artifactId>tycho-compiler-jdt</artifactId>
                <version>${org.eclipse.tycho.compiler-jdt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-container-default</artifactId>
                <version>1.6</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-utils</artifactId>
                <version>3.0.20</version>
            </dependency>

            <!-- Project modules -->
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>${project.groupId}</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.shared</groupId>
                <artifactId>maven-verifier</artifactId>
                <version>1.5</version>
            </dependency>

            <!-- Not directly used but required for dependency convergence -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.cal10n</groupId>
                <artifactId>cal10n-api</artifactId>
                <version>0.7.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <configLocation>build-config/checkstyle.xml</configLocation>
                        <consoleOutput>true</consoleOutput>
                        <failsOnError>true</failsOnError>
                        <violationSeverity>error</violationSeverity>
                        <includeTestSourceDirectory>true</includeTestSourceDirectory>
                        <propertyExpansion>basedir=${basedir}</propertyExpansion>
                        <!--
                            Excluding generated sources; Not required for the Maven build, but the Eclipse CS plug-in
                            would check the generated files without these exclusions; Note that the exclusions must be
                            specified as patterns within a source folder, so we can't exclude generated-sources
                            altogether
                        -->
                        <excludes>**/*Prism.java,*/itest/jaxb/xsd/*</excludes>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>mapstruct-build-config</artifactId>
                            <version>${project.version}</version>
                        </dependency>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${com.puppycrawl.tools.checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0-M1</version>
                    <configuration>
                        <deployAtEnd>true</deployAtEnd>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${org.apache.maven.plugins.enforcer.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>de.andrena.tools.nopackagecycles</groupId>
                            <artifactId>no-package-cycles-enforcer-rule</artifactId>
                            <version>1.0.9</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>4.0.0</version>
                    <executions>
                        <execution>
                            <id>bundle-manifest</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>manifest</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${org.apache.maven.plugins.javadoc.version}</version>
                    <configuration>
                        <quiet>true</quiet>
                        <excludePackageNames>org.mapstruct.ap.internal.prism;org.mapstruct.itest.jaxb.xsd.*</excludePackageNames>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                    <configuration>
                        <arguments>-DskipTests ${add.release.arguments}</arguments>
                        <preparationGoals>clean install</preparationGoals>
                        <pushChanges>false</pushChanges>
                        <localCheckout>true</localCheckout>
                        <tagNameFormat>@{project.version}</tagNameFormat>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <useReleaseProfile>false</useReleaseProfile>
                        <releaseProfiles>release</releaseProfiles>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.7.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${org.apache.maven.plugins.surefire.version}</version>
                    <configuration>
                        <forkCount>${forkCount}</forkCount>
                        <!-- Travis build workaround -->
                        <argLine>-Xms1024m -Xmx3072m</argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>com.mycila.maven-license-plugin</groupId>
                    <artifactId>maven-license-plugin</artifactId>
                    <version>1.10.b1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>animal-sniffer-maven-plugin</artifactId>
                    <version>1.17</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>6.2.1</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-dependency-plugin</artifactId>
                                        <versionRange>[2.4,)</versionRange>
                                        <goals>
                                            <goal>copy-dependencies</goal>
                                            <goal>unpack-dependencies</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.jacoco</groupId>
                                        <artifactId>jacoco-maven-plugin</artifactId>
                                        <versionRange>[0.7,)</versionRange>
                                        <goals>
                                            <goal>prepare-agent</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.codehaus.mojo</groupId>
                                        <artifactId>properties-maven-plugin</artifactId>
                                        <versionRange>[1.0-alpha-2,)</versionRange>
                                        <goals>
                                            <goal>set-system-properties</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.2</version>
                </plugin>
                <plugin>
                    <groupId>org.jvnet.jaxb2.maven2</groupId>
                    <artifactId>maven-jaxb2-plugin</artifactId>
                    <version>0.14.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>1.0-alpha-2</version>
                </plugin>
                <plugin>
                    <groupId>de.thetaphi</groupId>
                    <artifactId>forbiddenapis</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <signaturesFiles>
                            <signaturesFile>${project.basedir}/../etc/forbidden-apis.txt</signaturesFile>
                        </signaturesFiles>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.github.siom79.japicmp</groupId>
                    <artifactId>japicmp-maven-plugin</artifactId>
                    <version>0.13.0</version>
                    <executions>
                        <execution>
                            <phase>verify</phase>
                            <goals>
                                <goal>cmp</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <newVersion>
                            <file>
                                <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
                            </file>
                        </newVersion>
                        <parameter>
                            <oldVersionPattern>\d+\.\d+\.\d+\.Final</oldVersionPattern>
                            <breakBuildBasedOnSemanticVersioning>true</breakBuildBasedOnSemanticVersioning>
                            <overrideCompatibilityChangeParameters>
                                <overrideCompatibilityChangeParameter>
                                    <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                                    <binaryCompatible>true</binaryCompatible>
                                    <sourceCompatible>false</sourceCompatible>
                                    <semanticVersionLevel>MINOR</semanticVersionLevel>
                                </overrideCompatibilityChangeParameter>
                            </overrideCompatibilityChangeParameters>
                        </parameter>
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>com.mycila.maven-license-plugin</groupId>
                <artifactId>maven-license-plugin</artifactId>
                <configuration>
                    <header>${basedir}/../etc/license.txt</header>
                    <strictCheck>true</strictCheck>
                    <excludes>
                        <exclude>**/.idea/**</exclude>
                        <exclude>**/build-config/checkstyle.xml</exclude>
                        <exclude>**/build-config/import-control.xml</exclude>
                        <exclude>copyright.txt</exclude>
                        <exclude>**/LICENSE.txt</exclude>
                        <exclude>**/mapstruct.xml</exclude>
                        <exclude>**/toolchains-*.xml</exclude>
                        <exclude>**/travis-settings.xml</exclude>
                        <exclude>**/eclipse-formatter-config.xml</exclude>
                        <exclude>**/forbidden-apis.txt</exclude>
                        <exclude>**/checkstyle-for-generated-sources.xml</exclude>
                        <exclude>**/nb-configuration.xml</exclude>
                        <exclude>maven-settings.xml</exclude>
                        <exclude>readme.md</exclude>
                        <exclude>CONTRIBUTING.md</exclude>
                        <exclude>.gitattributes</exclude>
                        <exclude>.gitignore</exclude>
                        <exclude>.factorypath</exclude>
                        <exclude>.checkstyle</exclude>
                        <exclude>*.yml</exclude>
                        <exclude>**/*.asciidoc</exclude>
                        <exclude>**/binding.xjb</exclude>
                    </excludes>
                    <mapping>
                        <java>SLASHSTAR_STYLE</java>
                        <proto>SLASHSTAR_STYLE</proto>
                    </mapping>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>check-java-api-signature</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <signature>
                                <groupId>org.codehaus.mojo.signature</groupId>
                                <artifactId>java18</artifactId>
                                <version>1.0</version>
                            </signature>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <!-- Overriding version specified in execution in OSS super POM -->
                <version>${org.apache.maven.plugins.enforcer.version}</version>
                <executions>
                    <execution>
                        <id>enforce-mapstruct-rules</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[1.8,)</version>
                                </requireJavaVersion>
                                <DependencyConvergence />
                                <requirePluginVersions />
                                <requireMavenVersion>
                                    <version>[3.0.3,)</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>enforce-no-package-cycles</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <phase>test-compile</phase>
                        <configuration>
                            <rules>
                                <NoPackageCyclesRule implementation="de.andrena.tools.nopackagecycles.NoPackageCyclesRule">
                                    <includeTests>false</includeTests>
                                </NoPackageCyclesRule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-javadoc</id>
                        <goals>
                            <goal>javadoc-no-fork</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
