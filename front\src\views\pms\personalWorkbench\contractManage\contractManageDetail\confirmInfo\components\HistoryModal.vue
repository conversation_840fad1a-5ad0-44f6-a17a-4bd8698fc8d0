<template>
  <BasicModal
    v-bind="$attrs"
    title="审核历史"
    width="1000px"
    :height="500"
    :showFooter="true"
    :showConfirm="false"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <template #footer>
      <BasicButton @click="()=>closeModal()">
        关闭
      </BasicButton>
    </template>
    <HistoryModalMain
      v-if="state.visibleStatus"
      ref="modalMainRef"
      :record="state.record"
      @close-modal="closeModal"
    />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner, BasicButton } from 'lyra-component-vue3';
import { reactive } from 'vue';
import HistoryModalMain from './HistoryModalMain.vue';

const state = reactive({
  visibleStatus: false,
  record: {},
});

const [modalRegister, { closeModal }] = useModalInner((openProps) => {
  state.record = openProps;
  state.visibleStatus = true;
});

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

</script>

<style scoped>

</style>
