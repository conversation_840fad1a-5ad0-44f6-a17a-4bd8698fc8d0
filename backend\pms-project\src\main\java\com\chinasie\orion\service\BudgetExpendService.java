package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetExpendDTO;
import com.chinasie.orion.domain.entity.BudgetExpend;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetExpendVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetExpend 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
public interface BudgetExpendService extends OrionBaseService<BudgetExpend> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetExpendVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetExpendDTO
     */
    String create(BudgetExpendDTO budgetExpendDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetExpendDTO
     */
    Boolean edit(BudgetExpendDTO budgetExpendDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetExpendVO> pages(Page<BudgetExpendDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetExpendVO> vos) throws Exception;


    List<BudgetExpendVO> getList(String formId) throws Exception;



}
