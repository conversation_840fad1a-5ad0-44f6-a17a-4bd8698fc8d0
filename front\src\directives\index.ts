/**
 * Configure and register global directives
 */
import type { App } from 'vue';
import { setupPermissionDirective } from './permission';
import { setupLoadingDirective } from './loading';
import { setupHighlightDirective } from './hightLight';
import { setupClickOutsideDirective } from './clickOutside';
import { setupIntersectionDirective } from './intersection';

export function setupGlobDirectives(app: App) {
  setupPermissionDirective(app);
  setupLoadingDirective(app);
  setupHighlightDirective(app);
  setupClickOutsideDirective(app);
  setupIntersectionDirective(app);
}
