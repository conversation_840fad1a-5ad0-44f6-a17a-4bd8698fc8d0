<template>
  <div class="addNodeModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          label="名称"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <a-form-item
          label="联系方式"
          name="contactType"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.contactType"
            :options="concactTypeOption"
            size="large"
          />
        </a-form-item>
        <a-form-item
          label="联系信息"
          name="contactInfo"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.contactInfo"
            placeholder="请输入联系信息"
            size="large"
          />
        </a-form-item>

        <a-form-item
          label="地址"
          name="address"
        >
          <a-textarea
            v-model:value="formState.address"
            style="height: 120px"
            placeholder="请输入地址"
          />
        </a-form-item>
        <a-form-item
          label="描述"
          name="remark"
        >
          <a-textarea
            v-model:value="formState.remark"
            style="height: 120px"
            placeholder="请输入描述"
          />
        </a-form-item>
        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  Checkbox, Drawer, Input, Button, Form, message, Select,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
/* api */
import {
  getContactTypeApi,
  addStakeHolderApi,
  editStakeHolderApi,
} from '/@/views/pms/projectLaborer/api/projectList';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    aSelect: Select,
    messageModal,
    InfoCircleOutlined,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: <any>{
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      /* 联系方式 */
      // contacttype: [],
      /* 联系类型 */
      concactTypeOption: [],
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: <any>{
        name: '',
        contactType: '',
        contactInfo: '',
        address: '',
        remark: '',
      },
      showVisible: false,
      // 表单是否发生变化
      flag: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入名称',
        },
        {
          min: 1,
          max: 25,
          message: '名称长度应在1~25位',
        },
      ],
      // contactType: [{ required: true, message: '请输入联系方式' }],
      // contactInfo: [{ required: true, message: '请输入联系信息' }],
      // address: [{ required: true, message: '请输入地址' }],
      // remark: [{ required: true, message: '请输入项目描述' }]
    };
    watch(
      () => props.data,
      (value) => {
        //   console.log('测试🚀 ~ file: addProjectModal.vue ~ line 154 ~ value', value);
        //   //   oldvalue.value = {};
        //   console.log(
        //     '测试🚀 ~ file: addProjectModal.vue ~ line 155 ~ props.listData',
        //     props.listData
        //   );
        state.visible = true;
        state.nextCheck = false;
        if (value.formType == 'add') {
          state.title = '新增信息';
          state.formType = 'add';
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          // for (let namex in props.listData[0]) {
          //   state.formState[namex] = props.listData[0][namex];
          // }
          for (let namex in state.formState) {
            state.formState[namex] = props.listData[0][namex];
          }
        }
      },
    );
    /* 侦听表单是否变化 */
    watch(
      state.formState,
      () => {
        //   console.log('数据变化');
        state.flag = true;
      },
      {
        deep: true,
        immediate: true,
      },
    );
    onMounted(async () => {
      const res = await getContactTypeApi();
      state.concactTypeOption = res.map((item) => ({
        value: item.id,
        label: item.name,
      }));
    });
    /* 表单取消按钮 */
    const cancel = () => {
      formRef.value.resetFields();
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
      state.flag = false;
      formRef.value.resetFields();
      state.formState = {
        name: '',
        contactType: '',
        contactInfo: '',
        address: '',
        remark: '',
      };
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };

    /* 提交按钮 */
    const onSubmit = () => {
      state.formType === 'edit'
        ? zhttp(editStakeHolderApi(state.formState))
        : zhttp(addStakeHolderApi(state.formState));
    };
    const zhttp = (fn) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          fn.then(() => {
            message.success('保存成功');
            state.loading = false;
            if (state.nextCheck) {
              formRef.value.resetFields();
            } else {
              state.visible = false;
            }
            emit('success', false);
            state.formState = {
              name: '',
              contactType: '',
              contactInfo: '',
              address: '',
              remark: '',
            };
          }).catch((err) => {
            console.log('测试🚀 ~ file: addProjectModal.vue ~ line 242 ~ err', err);
            state.loading = false;
          });
        })
        .catch((error) => {
          console.log('error', error);
        });
    };

    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
</style>
