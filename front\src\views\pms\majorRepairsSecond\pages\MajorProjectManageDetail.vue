<script setup lang="ts">
import {
  nextTick, onActivated,
  onBeforeMount, provide,
  reactive, ref, Ref, unref,
} from 'vue';
import {
  Breadcrumb,
  BreadcrumbItem,
  Collapse as ACollapse,
  CollapsePanel as ACollapsePanel,
  Modal,
  Space as ASpace,
} from 'ant-design-vue';
import {
  BasicButton, DataStatusTag, Icon, isPower, randomString,
} from 'lyra-component-vue3';
import { BarsOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import { forEach, get as _get, set } from 'lodash-es';
import ProjectInfo from './components/majorProjectManageDetail/ProjectInfo.vue';
import MpmWorkDetail from './components/majorProjectManageDetail/MpmWorkDetail.vue';
import PersonManage from './components/majorProjectManageDetail/PersonManage.vue';
import MaterialsManage from './components/majorProjectManageDetail/MaterialsManage.vue';
import ProgressInfo from './components/majorProjectManageDetail/ProgressInfo.vue';
import Api from '/@/api';
import { parseDecimalToPre } from '/@/views/pms/utils/utils';

const route = useRoute();
const router = useRouter();
const key: Ref<string> = ref();
const isKeep: Ref<boolean> = ref(false);

const loading: Ref<boolean> = ref(false);
const detailId = ref(route.params.id);
const majorProjectData = reactive({});
const routes: Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}> = [
  {
    breadcrumbName: '大修管理',
    to: {
      name: 'Overhaul',
    },
  },
  {
    breadcrumbName: '大修详情',
    to: {
      name: 'MajorRepairsSecondDetail',
      params: {
        id: route.query?.repairId,
      },
    },
  },
  {
    breadcrumbName: '项目详情',
  },
];
const activeKey = ref([
  'projectDetail',
  'mpmWorkDetail',
  'personManage',
  'materialsManage',
  'progressInfo',
]);
const memberManageStatistics = ref([
  ['计划入场人数', 0],
  ['计划入场率', 0],
  ['实际入场人数', 0],
  ['实际入场率', 0],
  ['已离场人数', 0],
]);
const materialsManageStatistics = ref([
  ['计划入场数', 0],
  ['计划入场率', 0],
  ['实际入场数', 0],
  ['实际入场率', 0],
  ['已离场物资数', 0],
]);

provide('MajorProjectDetailData', majorProjectData);

function handleRoute(to) {
  router.push(to);
}
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/importantProject').fetch('', unref(detailId));
    loading.value = false;
    forEach(result, (val, key) => {
      set(majorProjectData || {}, key, val);
    });
  } catch (e) {} finally {
    loading.value = false;
  }
}
function handleUpdatePersonStatistic(result) {
  memberManageStatistics.value = [
    ['计划入场人数', _get(result, 'planNumberJoin', 0)],
    ['计划入场率', parseDecimalToPre(_get(result, 'planJoinRatio', 0) * 100)],
    ['实际入场人数', _get(result, 'actualNumberJoin', 0)],
    ['实际入场率', parseDecimalToPre(_get(result, 'actualJoinRatio', 0) * 100)],
    ['已离场人数', _get(result, 'outNumber', 0)],
  ];
}
function handleUpdateMaterialsStatistic(result) {
  materialsManageStatistics.value = [
    ['计划入场数', _get(result, 'planNumberJoin', 0)],
    ['计划入场率', parseDecimalToPre(_get(result, 'planJoinRatio', 0) * 100)],
    ['实际入场数', _get(result, 'actualNumberJoin', 0)],
    ['实际入场率', parseDecimalToPre(_get(result, 'actualJoinRatio', 0) * 100)],
    ['已离场物资数', _get(result, 'outNumber', 0)],
  ];
}

onBeforeMount(async () => {
  await nextTick();
  await getDetailData();
});
onActivated(() => {
  if (!isKeep.value) return isKeep.value = true;
  key.value = randomString();
});
</script>

<template>
  <div
    v-loading="loading"
    class="major-project-detail"
  >
    <div class="header-top">
      <a-space
        :size="12"
        align="baseline"
      >
        <h2>{{ majorProjectData.projectName }}</h2>
        <span class="repair-round">{{ majorProjectData.repairRound }}</span>
      </a-space>
      <Breadcrumb>
        <BreadcrumbItem
          v-for="(item,index) in routes"
          :key="index"
        >
          <bars-outlined v-if="index===0" />
          <span
            v-if="item.to"
            class="link"
            @click="handleRoute(item.to)"
          >{{ item.breadcrumbName }}</span>
          <span v-else>{{ item.breadcrumbName }}</span>
        </BreadcrumbItem>
      </Breadcrumb>
    </div>
    <a-collapse
      v-if="majorProjectData?.id"
      :key="key"
      v-model:activeKey="activeKey"
      collapsible="icon"
      :ghost="false"
      :bordered="false"
      style="background: #fff"
    >
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>

      <!--项目详情-->
      <a-collapse-panel
        key="projectDetail"
        header="项目详情"
      >
        <ProjectInfo />
      </a-collapse-panel>
      <!--进展信息-->
      <a-collapse-panel
        key="progressInfo"
        header="进展信息"
      >
        <ProgressInfo />
      </a-collapse-panel>

      <!--作业详情-->
      <a-collapse-panel
        key="mpmWorkDetail"
        header="作业详情"
      >
        <MpmWorkDetail />
      </a-collapse-panel>

      <!--人员管理-->
      <a-collapse-panel
        key="personManage"
      >
        <template #header>
          <div class="custom-header">
            <span class="header-title">人员管理</span>
            （
            <a-space :size="12">
              <template
                v-for="(item,index) in memberManageStatistics"
                :key="index"
              >
                {{ item.join(" ") }}
              </template>
            </a-space>
            ）
          </div>
        </template>
        <PersonManage @update-person-statistic="handleUpdatePersonStatistic" />
      </a-collapse-panel>

      <!--物资管理-->
      <a-collapse-panel
        key="materialsManage"
      >
        <template #header>
          <div class="custom-header">
            <span class="header-title">物资管理</span>
            （
            <a-space :size="12">
              <template
                v-for="(item,index) in materialsManageStatistics"
                :key="index"
              >
                {{ item.join(" ") }}
              </template>
            </a-space>
            ）
          </div>
        </template>
        <MaterialsManage @update-materials-statistic="handleUpdateMaterialsStatistic" />
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style scoped lang="less">
.major-project-detail{
  padding: ~`getPrefixVar('content-margin-top')` 14px;
  background: #fff;
  .link {
    cursor: pointer;

    &:hover {
      color: ~`getPrefixVar('primary-color')`;
    }
  }
  .header-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')`;
    position: relative;

    &:after {
      content: "";
      left: 16px;
      right: 16px;
      bottom: 0;
      position: absolute;
      pointer-events: none;
      background: #E8E8E8;
      height: 1px;
    }

    h2 {
      margin-bottom: 0;
      padding-bottom: 0;
      font-size: 18px;
      font-weight: 700;
    }

    .repair-round {
      font-weight: normal;
    }
  }

  :deep(.ant-collapse) {
    height: calc(100vh - 170px);
    overflow-x: auto;

    &.ant-collapse-borderless {
      & > .ant-collapse-item {
        border-bottom: 0;

        .basic-card-wrap {
          margin: 0 !important;
        }

        .card-content.spacing {
          margin: 0 !important;

          .details-grid {
            padding: 0;
          }
        }
      }

      .ant-collapse-header {
        display: inline-flex;
      }

      .ant-collapse-header, .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #000000;
      }

      & > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
        margin-right: 6px;
      }

      .custom-header {
        height: 27px;
        display: flex;
        align-items: flex-end;
        font-size: 14px;
        color: rgb(174 174 174);
        font-weight: normal;
      }

      .ant-basic-table.default-spacing {
        padding: 0;

        .orion-table-header-wrap {
          padding: 0;
        }
      }
    }

    .ant-collapse-content-box {
      padding-bottom: 0;
    }
  }
}
</style>
