package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectApprovalMilestoneNodePrePost Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 17:39:58
 */
@TableName(value = "pmsx_project_approval_milestone_node_pre_post")
@ApiModel(value = "ProjectApprovalMilestoneNodePrePostEntity对象", description = "项目立项里程碑节点前后置关系")
@Data

public class ProjectApprovalMilestoneNodePrePost extends  ObjectEntity  implements Serializable{

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @TableField(value = "pre_scheme_id")
    private String preSchemeId;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @TableField(value = "post_scheme_id")
    private String postSchemeId;

    /**
     * 立项里程碑id
     */
    @ApiModelProperty(value = "立项里程碑id")
    @TableField(value = "approval_milestone_id")
    private String ApprovalMilestoneId;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @TableField(value = "type")
    private String type;

}
