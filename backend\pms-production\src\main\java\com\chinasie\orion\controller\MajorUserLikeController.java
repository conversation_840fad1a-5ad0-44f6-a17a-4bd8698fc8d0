package com.chinasie.orion.controller;

import com.chinasie.orion.constant.JobManageStatusEnum;
import com.chinasie.orion.domain.dto.job.MajorUserLikeParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MajorUserLike;
import com.chinasie.orion.domain.dto.MajorUserLikeDTO;
import com.chinasie.orion.domain.vo.MajorUserLikeVO;

import com.chinasie.orion.service.MajorUserLikeService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import java.util.stream.Collectors;

import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MajorUserLike 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
@RestController
@RequestMapping("/majorUserLike")
@Api(tags = "用户关注的大修")
public class  MajorUserLikeController  {

    @Autowired
    private MajorUserLikeService majorUserLikeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<MajorUserLikeVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MajorUserLikeVO rsp = majorUserLikeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorUserLikeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【用户关注的大修】数据【{{#majorUserLikeDTO.repairRound}}】", type = "MajorUserLike", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MajorUserLikeDTO majorUserLikeDTO) throws Exception {
        String rsp =  majorUserLikeService.create(majorUserLikeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     */
    @ApiOperation(value = "批量新增/修改")
    @RequestMapping(value = "/add/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】 批量新增【用户关注的大修】 关注大修【{{#repairRounds}}】", type = "MajorUserLike", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> addBatch(@RequestBody List<MajorUserLikeParamDTO> majorUserLikeDTOList, JobManageStatusEnum statusEnum) throws Exception {
        Boolean bool=  majorUserLikeService.addBatch(majorUserLikeDTOList,statusEnum);
        LogRecordContext.putVariable("repairRounds", majorUserLikeDTOList.stream().map(MajorUserLikeParamDTO::getRepairRound).collect(Collectors.joining(",")));
        return new ResponseDTO<>(bool);
    }

    /**
     * 编辑
     *
     * @param majorUserLikeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【用户关注的大修】数据【{{#majorUserLikeDTO.repairRound}}】", type = "MajorUserLike", subType = "编辑", bizNo = "{{#majorUserLikeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorUserLikeDTO majorUserLikeDTO) throws Exception {
        Boolean rsp = majorUserLikeService.edit(majorUserLikeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【用户关注的大修】数据", type = "MajorUserLike", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorUserLikeService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【用户关注的大修】数据", type = "MajorUserLike", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorUserLikeService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【用户关注的大修】数据", type = "MajorUserLike", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorUserLikeVO>> pages(@RequestBody Page<MajorUserLikeDTO> pageRequest) throws Exception {
        Page<MajorUserLikeVO> rsp =  majorUserLikeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取对应状态中的大修列表
     *
     * @param statusEnum
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取对应状态中的大修列表--当前人关注的或者未关注的top3")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【用户关注的大修】top3数据", type = "MajorUserLike", subType = "一层", bizNo = "")
    @RequestMapping(value = "/major/info", method = RequestMethod.POST)
    public ResponseDTO<List<MajorUserLikeVO>> majorInfoList(JobManageStatusEnum statusEnum) throws Exception {
        List<MajorUserLikeVO> rsp =  majorUserLikeService.majorInfoList( statusEnum);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取对应状态中的大修列表
     * @param statusEnum
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取对应状态中的大修列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【用户关注的大修】大修数据", type = "MajorUserLike", subType = "一层", bizNo = "")
    @RequestMapping(value = "/major/list", method = RequestMethod.POST)
    public ResponseDTO<List<MajorRepairPlanVO>> majorList(JobManageStatusEnum statusEnum) throws Exception {
        List<MajorRepairPlanVO> rsp =  majorUserLikeService.majorList( statusEnum);
        return new ResponseDTO<>(rsp);
    }
}
