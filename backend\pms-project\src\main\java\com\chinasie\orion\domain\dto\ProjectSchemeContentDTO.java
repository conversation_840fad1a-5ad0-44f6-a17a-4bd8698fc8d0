package com.chinasie.orion.domain.dto;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * PmsProjectSchemeContentDTO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:26
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeContentDTO对象", description = "项目计划记录内容")
public class ProjectSchemeContentDTO extends ObjectDTO {

    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;

    @ApiModelProperty(value = "进度")
    private Double schedule;
    /**
     *
     */
    @ApiModelProperty(value = "内容")
    private String content;

}
