<template>
  <OrionTable
    v-if="roleId"
    ref="tableRef"
    class="pdmBasicTable"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="btnShow && isPower('PMS_XMXQ_container_09_01_01_button_01', powerData)"
        type="primary"
        icon="orion-icon-upload"
        @click="uploadShow"
      >
        上传附件
      </BasicButton>
      <BasicButton
        v-if="btnShow && isPower('PMS_XMXQ_container_09_01_01_button_04', powerData)"
        @click="setTemplate"
      >
        设置模板
      </BasicButton>
      <BasicButton
        v-if="btnShow && isPower('PMS_XMXQ_container_09_01_01_button_05', powerData)"
        :disabled="!selectedData.length"
        :loading="loading"
        @click="generateProjectFile"
      >
        生成项目档案
      </BasicButton>
    </template>

    <template #modifyTime="{ text }">
      {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
    </template>
    <template #checkIn="{ record }:Record<string,{checkIn:string}>">
      <span class="flex-te">{{ record.checkIn !== userId ? '签入' : '签出' }} </span>
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>

  <BasicUpload
    :max-number="100"
    :max-size="10"
    :show-up-modal="isShow"
    :is-button="false"
    :pagination="'false'"
    @saveChange="saveChange"
  />
</template>
<script lang="ts">
import {
  computed,
  defineComponent, h, inject, reactive, ref, toRefs, watch,
} from 'vue';
import {
  BasicButton,
  BasicTableAction,
  BasicUpload, DataStatusTag,
  isPower,
  ITableActionItem,
  openFile, openTreeSelectModal,
  OrionTable,
} from 'lyra-component-vue3';
import { Input, message, Modal } from 'ant-design-vue';
//   文件下载
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import dayjs from 'dayjs';
import {
  deleteDocTableItemApi,
  getPageApi,
  resetDocNameApi,
  upDocApi,
} from '/@/views/pms/projectLaborer/api/docManagement';
import { useUserStore } from '/@/store/modules/user';
import { useRouter } from 'vue-router';
import { findNodeAll } from '/@/utils/helper/treeHelper';
import record from '/@/views/pms/projectLaborer/components/BpmnModules/src/BpmnMain/component/Record/index.vue';
import Api from '/@/api';
import { generateDocument } from '/@/views/pms/api/document';

export default defineComponent({
  name: '',
  components: {
    BasicTableAction,
    BasicUpload,
    BasicButton,
    OrionTable,
  },
  props: {
    roleId: {
      type: String,
      default: '',
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    pageType: {
      type: String,
      default: 'page',
    },
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['updateTree'],
  setup(props, { emit }) {
    const router = useRouter();
    const userStore: any = useUserStore();
    const tableRef = ref(null);
    const selectUserData = ref([]);
    const selectedData = ref([]);
    const loading = ref(false);
    const state = reactive({
      userId: userStore.getUserInfo.id,
      fileId: undefined,
      //   成员id
      dataId: '',
      powerData: [],
    });
    state.powerData = inject('powerData');

    watch(
      () => props.roleId,
      async (value) => {
        state.dataId = value;
        await getFormData();
      },
    );

    const isShow = ref(false);
    /* 上传 */
    const uploadShow = () => {
      isShow.value = !isShow.value;
    };

    let provideProjectId: any = inject('provideProjectId');

    function saveChange(successAll: any[]) {
      const fileArr = successAll.map((item) => ({
        id: item?.result?.id,
        filePath: item.result.filePath,
        filePostfix: item.result.filePostfix,
        fileSize: item.result.fileSize,
        name: item.result.name,
        dataId: props.roleId,
        fileTool: item.openTool,
        secretLevel: item.result.secretLevel,
        securityLimit: item.securityLimit,
        projectId: provideProjectId.value,
      }));
      upDocApi(fileArr).then(() => {
        message.success('上传成功');
        getFormData();
        emit('updateTree');
      });
    }

    const getFormData = async () => {
      tableRef.value?.reload();
    };

    /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
    };

    // 生成 queryCondition
    function getListParams(params: Record<string, any>) {
      if (params.searchConditions) {
        return {
          ...params,
          queryCondition: params.searchConditions.map((item: Record<string, any>) => ({
            column: item?.[0]?.field,
            type: 'like',
            link: 'or',
            value: item?.[0]?.values?.[0],
          })),
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: isPower('PMS_XMXQ_container_09_01_01_button_02', state.powerData) ? 'add|enable|disable' : 'add|enable|disable|delete',
      rowSelection: {},
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_DOCUMENTMANAGE',
      api(params: Record<string, any>) {
        return getPageApi(getListParams({
          ...params,
          query: {
            dataId: props.roleId,
            projectId: provideProjectId.value,
          },
          power: {
            pageCode: 'PMS0004',
            containerCode: 'PMS_XMXQ_container_09_01_02',
          },
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          queryCondition: [],
        }));
      },
      async batchDeleteApi({ ids }) {
        await deleteDocTableItemApi(ids);
        message.success('删除成功');
        await getFormData();
      },
      columns: [
        {
          title: '名称',
          dataIndex: 'fullName',
          minWidth: 380,
          slots: { customRender: 'name' },
        },
        {
          title: '文档来源',
          dataIndex: 'sourceName',
          minWidth: 380,
          customRender({ text, record }: Record<string, Record<string, any>>) {
            return h('div', {
              onClick: () => handleDetail(record),
              class: props.btnShow ? '' : 'action-btn',
            }, text);
          },
        },
        {
          title: '版本',
          dataIndex: 'revId',
          width: '80px',
          align: 'left',
          slots: { customRender: 'revId' },
        },
        {
          title: '状态',
          dataIndex: 'checkIn',
          width: '70px',
          slots: { customRender: 'checkIn' },
        },
        {
          title: '所有者',
          dataIndex: 'ownerName',
          width: '120px',
          slots: { customRender: 'ownerName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          width: '180px',
          slots: { customRender: 'modifyTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 200,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn: Array<ITableActionItem> = [
      {
        text: '打开',
        isShow: () => isPower('PMS_XMXQ_container_09_01_02_button_01', state.powerData),
        onClick(record: any) {
          openFile(record);
        },
      },
      {
        text: '重命名',
        isShow: () => isPower('PMS_XMXQ_container_09_01_02_button_02', state.powerData),
        onClick(record: any) {
          let name = ref(record.name);
          Modal.confirm({
            icon: null,
            title: '重命名',
            content: h(Input, {
              value: name,
              style: {
                width: '100%',
              },
              onChange(e) {
                name.value = e.target.value;
              },
            }),
            async onOk() {
              await resetDocNameApi({
                id: record.id,
                name: name.value,
              });
              message.success('操作成功');
              await getFormData();
            },
          });
        },
      },
      {
        text: '下载',
        isShow: () => isPower('PMS_XMXQ_container_09_01_02_button_03', state.powerData),
        onClick(record: any) {
          downLoadById(record.id);
        },
      },
      {
        text: '删除',
        isShow: () => isPower('PMS_XMXQ_container_09_01_02_button_04', state.powerData),
        modal(record: any) {
          return deleteDocTableItemApi([record.id]).then(() => {
            message.success('删除成功');
            getFormData();
          });
        },
      },
    ];

    // 通过名称获取类型
    function getTypeByName(name: string): string {
      if (name.includes('计划')) {
        return 'plan';
      }
      if (name.includes('需求')) {
        return 'demand';
      }
      if (name.includes('风险')) {
        return 'risk';
      }
      if (name.includes('问题')) {
        return 'question';
      }
      if (name.includes('结项')) {
        return 'accept';
      }
    }

    // 文档来源跳转对应详情
    function handleDetail(record: Record<string, any>) {
      let type = '';
      findNodeAll(props.treeData, (node) => {
        if (node.key === record.documentTypeId) {
          type = getTypeByName(node.title);
        }
      }, {
        id: 'key',
        pid: 'parentId',
      });
      switch (type) {
        case 'plan': // 计划管理详情
          router.push({
            name: 'ProPlanDetails',
            params: {
              id: record.dataId,
            },
          });
          break;
        case 'demand': // 需求管理详情
          router.push({
            name: 'PMSDemandManagementDetails',
            query: {
              folderId: provideProjectId.value,
              itemId: record?.dataId,
              dirName: record?.sourceName,
            },
          });
          break;
        case 'risk': // 风险管理详情
          router.push({
            name: 'PMSRiskManagementDetails',
            query: {
              folderId: provideProjectId.value,
              itemId: record?.dataId,
              dirName: record?.sourceName,
            },
          });
          break;
        case 'question': // 问题管理详情
          router.push({
            name: 'PMSQuestionManagementDetails',
            query: {
              folderId: provideProjectId.value,
              itemId: record?.dataId,
              dirName: record?.sourceName,
            },
          });
          break;
        case 'accept': // 项目验收详情
          router.push({
            name: 'ProjectAcceptanceDetail',
            query: {
              projectId: provideProjectId.value,
              id: record.dataId,
            },
          });
          break;
      }
    }

    const getList = () => new Api('/res/bookmark-Document-TemplateType/tree').fetch('', '', 'GET');
    const page = (params, id) => new Api(`/res/bookmark-Document-Template/pages/${id}?status=open`).fetch(params, '', 'POST');

    function setTemplate() {
      openTreeSelectModal({
        title: '评审要点',
        width: '80%',
        selectType: 'radio',
        selectedData: selectedData.value,
        treeApi: () => getList(),
        columns: [
          {
            title: '模板编号',
            dataIndex: 'number',
          },
          {
            title: '模板名称',
            dataIndex: 'name',
          },
          {
            title: '类型',
            dataIndex: 'typeName',
          },
          {
            title: '模板标识',
            dataIndex: 'mark',
          },
          {
            title: '版本',
            dataIndex: 'revId',
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            customRender: ({ record }) => h(DataStatusTag, {
              statusData: record.dataStatus,
            }),
          },
          {
            title: '创建人',
            dataIndex: 'creatorName',
          },
          {
            title: '发布时间',
            dataIndex: 'createTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
          },
        ],
        tableApi(option) {
          const params: Record<string, any> = {
            pageNum: option.pageNum,
            pageSize: option.pageSize,
            searchConditions: option.searchConditions,
          };
          return page(params, option.node?.id);
        },
        onOk({ tableData }) {
          selectedData.value = tableData;
        },
      });
    }

    async function generateProjectFile() {
      try {
        loading.value = true;
        const templateId = selectedData.value[0]?.id;
        const projectId = provideProjectId.value;
        await generateDocument(projectId, templateId);
        await getFormData();
      } finally {
        loading.value = false;
      }
    }

    return {
      ...toRefs(state),
      isPower,
      dayjs,
      successSave,
      saveChange,
      //* *** 直接使用 ****
      selectUserData,
      uploadShow,
      isShow,
      tableRef,
      tableOptions,
      actionsBtn,
      setTemplate,
      generateProjectFile,
      selectedData,
      loading,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
