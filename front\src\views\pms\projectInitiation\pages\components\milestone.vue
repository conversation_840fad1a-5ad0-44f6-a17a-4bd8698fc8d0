<script setup lang="ts">
import {
  Layout,
  OrionTable,
  BasicButton,
  BasicUpload,
  downLoadById,
  DataStatusTag,
  useDrawer,
  openFile,
  openModal,
  getDictByNumber,
  useModal, BasicImport,
  downloadByData as basicDownloadByData, openDrawer,
} from 'lyra-component-vue3';
import {
  h,
  inject, onMounted, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { message, Modal, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';
import { declarationData, declarationDataId } from '../keys';
import MilestoneDrawer from '../../components/milestoneDrawer/Index.vue';
import { SituationColorEnum } from '/@/views/pms/projectLaborer/projectLab/enums';
import AddPlan from '../modal/AddPlan.vue';
import EditMilestone from '../modal/EditMilestone.vue';
import EditBarSelect from './milestoneEditList/EditBarSelect.vue';
import AddTableModal
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/components/AddTableModal.vue';
import BudgetRequestEdit
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetRequest/BudgetRequestEdit.vue';
const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const dataId: Ref = inject(declarationDataId);
const data = inject(declarationData);
const tableRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const schemeActivityListOptions = ref();
const schemeTypeOptions = [
  {
    label: '里程碑节点',
    value: 'milestone',
  },
  {
    label: '计划',
    value: 'plan',
  },
];
const selectedRowKeys = ref([]);
const downloadFileObj = {
  url: '/pms/projectApprovalMilestone/download/excel/tpl',
  method: 'GET',
};

function requestBasicImport(data) {
  let params = new FormData();
  params.append('file', data[0]);
  params.append('approvalId', dataId.value);
  return new Api('').fetch(params, '/pms/projectApprovalMilestone/import/excel/check', 'POST');
}

const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/projectApprovalMilestone/import/excel/${importId}/${dataId.value}`).fetch('', '', 'post')
      .then(() => {
        resolve({
          result: true,
        });
      });
  });
function changeImportModalFlag({
  successImportFlag,
  succ,
}) {
  if (successImportFlag) {
    updateTable();
  } else if (succ) {
    new Api(`/pms/projectApprovalMilestone/import/excel/cancel/${succ}`).fetch('', '', 'post');
  }
}
const tableOptions = {
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    selectedRowKeys,
    onChange: (keys = []) => {
      selectedRowKeys.value = keys;
    },
  },
  showSmallSearch: false,
  api(params) {
    params.query = {
      approvalId: dataId?.value,
    };
    return new Api('/pms/projectApprovalMilestone/pages').fetch(params, '', 'POST');
  },

  columns: [
    {
      title: '里程碑名称',
      dataIndex: 'name',
      minWidth: 300,
      fixed: 'left',
      slots: { customRender: 'name' },
    },
    {
      title: '层级',
      dataIndex: 'level',
      width: 100,
      slots: { customRender: 'level' },
    },
    {
      title: '计划类型',
      dataIndex: 'schemeType',
      width: 120,
      customRender({ text, record }) {
        return text === 'milestone' ? '里程碑节点' : '里程碑节点';
      },
    },
    {
      title: '计划活动项',
      dataIndex: 'schemeActivityList',
      customRender({ text, record }) {
        return h(EditBarSelect, {
          value: text,
          options: schemeActivityListOptions.value,
          mode: 'multiple',
          fieldNames: {
            label: 'description',
            value: 'value',
          },
          // 只有【待发布】状态可进行行操作
          isEdit: record.status === 101,
          onChange: async (value) => {
            record.schemeActivityList = value;
            await onTypeChange(record);
          },
        });
      },
    },
    // {
    //   title: '计划状态',
    //   dataIndex: 'dataStatus',
    //   width: 100,
    //   customRender({ record }) {
    //     return record.dataStatus
    //       ? h(DataStatusTag, {
    //         statusData: record.dataStatus,
    //       })
    //       : '';
    //   },
    // },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 120,
      // customRender({ text }) {
      //   return text ? dayjs(text)
      //     .format('YYYY-MM-DD') : '';
      // },
      // customRender({ text, record }) {
      //   const label = text ? dayjs(text).format('YYYY-MM-DD') : '';
      //   return h(EditBarDatePicker, {
      //     value: label,
      //     // 只有【待发布】【计划退回】两种状态可进行行操作
      //     isEdit: record.status === 101 || record.status === 101,
      //     onChange: async (value) => {
      //       record.beginTime = value;
      //       await onTypeChange(record);
      //       // 修改计划开始日期后台会自动计算相应的计划结束日期-调用接口刷新数据
      //       updateForm();
      //     },
      //   });
      // },
    },
    {
      title: '工期',
      dataIndex: 'durationDays',
      width: 120,
      // customRender({ text }) {
      //   return text ? dayjs(text)
      //     .format('YYYY-MM-DD') : '';
      // },
      // customRender({ text, record }) {
      //   const label = text ? dayjs(text).format('YYYY-MM-DD') : '';
      //   return h(EditBarDatePicker, {
      //     value: label,
      //     // 只有【待发布】【计划退回】两种状态可进行行操作
      //     isEdit: record.status === 101 || record.status === 101,
      //     onChange: async (value) => {
      //       record.beginTime = value;
      //       await onTypeChange(record);
      //       // 修改计划开始日期后台会自动计算相应的计划结束日期-调用接口刷新数据
      //       updateForm();
      //     },
      //   });
      // },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 120,
      // customRender({ text }) {
      //   return text ? dayjs(text)
      //     .format('YYYY-MM-DD') : '';
      // },
      // customRender({ text, record }) {
      //   const label = text ? dayjs(text).format('YYYY-MM-DD') : '';
      //   return h(EditBarDatePicker, {
      //     value: label,
      //     // 只有【待发布】【计划退回】两种状态可进行行操作
      //     isEdit: record.status === 101 || record.status === 101,
      //     onChange: async (value) => {
      //       record.endTime = value;
      //       await onTypeChange(record);
      //     },
      //   });
      // },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      fixed: 'right',
      slots: { customRender: 'action' },
      resizable: false,
    },
  ],
  actions: [
    {
      isShow: (record) => record.status === 101,
      text: '编辑',
      onClick(record) {
        openFormDrawer(EditMilestone, record, updateTable);
      },
    },
    {
      text: '删除',
      modal(record) {
        return batchDelete([record.id]);
      },
    },
  ],

};

function preview(fileData) {
  openFile(fileData);
}
// 修改方法
async function onTypeChange(record) {
  await updateEdit(record);
  message.success('保存成功');
}
function updateEdit(record: Record<string, any>) {
  return new Api('').fetch(record, '/pms/projectApprovalMilestone', 'PUT');
}
onMounted(async () => {
  schemeActivityListOptions.value = await getDictByNumber('planActive');
});

function updateTable() {
  tableRef.value.reload();
}

// 表格多选

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectApprovalMilestone').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行操作');
    return false;
  }
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectedRowKeys.value),
  });
}
function addMilestonePlan() {
  const addMilestonePlanRef = ref(null);
  openModal({
    title: '里程碑编制',
    width: 1100,
    height: 700,
    content(h) {
      return h(AddPlan, {
        ref: addMilestonePlanRef,
        schemeActivityListOptions,
      });
    },
    async onOk() {
      let res = await addMilestonePlanRef.value.saveData();
      if (res && res.length > 0) {
        const arr = res.map((item) => ({
          name: item.name,
          approvalId: dataId.value || '',
          schemeType: item.schemeType || '',
          schemeActivityList: item.schemeActivityList || [],
          beginTime: dayjs(item.beginTime).format('YYYY-MM-DD') || '',
          durationDays: item.durationDays || '',
          endTime: dayjs(item.endTime).format('YYYY-MM-DD') || '',
          schemeDesc: item.schemeDesc || '',
        }));
        new Api('/pms/projectApprovalMilestone').fetch(arr, '', 'POST')
          .then((res) => {
            updateTable();
          })
          .catch(() => {

          });
      } else {
        return new Promise((resolve, reject) => {
          reject('');
        });
      }
    },
  });
}
const handleImport = () => {
  openImportModal(true, {});
};
const [register, { openModal: openImportModal }] = useModal();
const exportFile = async () => {
  await basicDownloadByData(
    `/api/pms/projectApprovalMilestone/export/excel?approvalId=${dataId.value}`,

    [],
    '',
    'POST',
    false,
    false,
  );
};

function openFormDrawer(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑' : '新增',
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        formId: record?.id,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      cb?.();
    },
  });
}

</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="里程碑计划"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="add"
          @click="addMilestonePlan"
        >
          计划编制
        </BasicButton>
        <BasicButton
          icon="sie-icon-daoru"
          @click="handleImport"
        >
          导入
        </BasicButton>
        <BasicButton
          icon="sie-icon-daochu"
          @click="exportFile"
        >
          导出
        </BasicButton>
        <BasicButton
          icon="sie-icon-del"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <!--编辑,新增里程碑-->
    <MilestoneDrawer
      :onConfirmCallback="updateTable"
      @register="registerCreateAndEdit"
    />
    <!-- 导入 -->
    <BasicImport

      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
