package com.chinasie.orion.service.impl;

import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.domain.dto.QuestionRelationReviewFormDTO;
import com.chinasie.orion.domain.entity.QuestionRelationReviewForm;
import com.chinasie.orion.domain.vo.QuestionRelationReviewFormVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.QuestionRelationReviewFormMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionRelationReviewFormService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * QuestionRelationReviewForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15 11:03:43
 */
@Service
@Slf4j
public class QuestionRelationReviewFormServiceImpl extends OrionBaseServiceImpl<QuestionRelationReviewFormMapper, QuestionRelationReviewForm> implements QuestionRelationReviewFormService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QuestionRelationReviewFormVO detail(String id, String pageCode) throws Exception {
        QuestionRelationReviewForm questionRelationReviewForm =this.getById(id);
        QuestionRelationReviewFormVO result = BeanCopyUtils.convertTo(questionRelationReviewForm,QuestionRelationReviewFormVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param questionRelationReviewFormDTO
     */
    @Override
    public  String create(QuestionRelationReviewFormDTO questionRelationReviewFormDTO) throws Exception {
        QuestionRelationReviewForm questionRelationReviewForm =BeanCopyUtils.convertTo(questionRelationReviewFormDTO,QuestionRelationReviewForm::new);
        this.save(questionRelationReviewForm);

        String rsp=questionRelationReviewForm.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param questionRelationReviewFormDTO
     */
    @Override
    public Boolean edit(QuestionRelationReviewFormDTO questionRelationReviewFormDTO) throws Exception {
        QuestionRelationReviewForm questionRelationReviewForm =BeanCopyUtils.convertTo(questionRelationReviewFormDTO,QuestionRelationReviewForm::new);

        this.updateById(questionRelationReviewForm);

        String rsp=questionRelationReviewForm.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QuestionRelationReviewFormVO> pages( Page<QuestionRelationReviewFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QuestionRelationReviewForm> condition = new LambdaQueryWrapperX<>( QuestionRelationReviewForm. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QuestionRelationReviewForm::getCreateTime);


        Page<QuestionRelationReviewForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuestionRelationReviewForm::new));

        PageResult<QuestionRelationReviewForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QuestionRelationReviewFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuestionRelationReviewFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QuestionRelationReviewFormVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "问题与评审单关系表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionRelationReviewFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            QuestionRelationReviewFormExcelListener excelReadListener = new QuestionRelationReviewFormExcelListener();
        EasyExcel.read(inputStream,QuestionRelationReviewFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QuestionRelationReviewFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("问题与评审单关系表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<QuestionRelationReviewForm> questionRelationReviewFormes =BeanCopyUtils.convertListTo(dtoS,QuestionRelationReviewForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::QuestionRelationReviewForm-import::id", importId, questionRelationReviewFormes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QuestionRelationReviewForm> questionRelationReviewFormes = (List<QuestionRelationReviewForm>) orionJ2CacheService.get("pmsx::QuestionRelationReviewForm-import::id", importId);
        log.info("问题与评审单关系表导入的入库数据={}", JSONUtil.toJsonStr(questionRelationReviewFormes));

        this.saveBatch(questionRelationReviewFormes);
        orionJ2CacheService.delete("pmsx::QuestionRelationReviewForm-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::QuestionRelationReviewForm-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuestionRelationReviewForm> condition = new LambdaQueryWrapperX<>( QuestionRelationReviewForm. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QuestionRelationReviewForm::getCreateTime);
        List<QuestionRelationReviewForm> questionRelationReviewFormes =   this.list(condition);

        List<QuestionRelationReviewFormDTO> dtos = BeanCopyUtils.convertListTo(questionRelationReviewFormes, QuestionRelationReviewFormDTO::new);

        String fileName = "问题与评审单关系表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionRelationReviewFormDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<QuestionRelationReviewFormVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class QuestionRelationReviewFormExcelListener extends AnalysisEventListener<QuestionRelationReviewFormDTO> {

        private final List<QuestionRelationReviewFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(QuestionRelationReviewFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QuestionRelationReviewFormDTO> getData() {
            return data;
        }
    }


}
