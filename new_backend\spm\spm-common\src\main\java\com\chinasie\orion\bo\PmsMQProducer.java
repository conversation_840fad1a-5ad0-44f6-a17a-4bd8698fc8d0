package com.chinasie.orion.bo;

import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.amqp.entity.OrionNormalMessageDTO;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.amqp.handler.RabbitMQMessageHandler;
import com.chinasie.orion.util.json.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/23 14:16
 */
@Component
public class PmsMQProducer {
    private final Logger logger = LoggerFactory.getLogger(PmsMQProducer.class);

    @Autowired
    private RabbitMQMessageHandler rabbitMQMessageHandler;

    /**
     * 发送消息
     *
     * @param mscMessageDTO 消息实体
     * @throws Exception 异常
     */
    public void sendPmsMessage(SendMessageDTO mscMessageDTO) {
        logger.info("发送消息={}", JsonUtils.obj2String(mscMessageDTO));
        try {
            rabbitMQMessageHandler.sendMessage(mscMessageDTO);
        } catch (Exception e) {
            logger.error("发送消息异常", e);
        }
    }

    //发送延时消息
    public void sendPmsDelayedMessage(SendMessageDTO mscMessageDTO,String delayTime){
        logger.info("-------------------发送延时短信 = " + JSONObject.toJSONString(mscMessageDTO)+",延迟时间："+delayTime);
        try {
            rabbitMQMessageHandler.sendDelayMessage(mscMessageDTO,delayTime);
        } catch (Exception e) {
            logger.error("发送消息异常", e);
        }
    }


    /**
     * 发送消息
     *
     * @param orionNormalMessageDTO 消息实体
     * @throws Exception 异常
     */
    public void sendPmsMessage(OrionNormalMessageDTO orionNormalMessageDTO) {
        logger.info("发送消息={}", JSONObject.toJSONString(orionNormalMessageDTO));
        try {
            rabbitMQMessageHandler.sendNormalMessage(orionNormalMessageDTO);
        } catch (Exception e) {
            logger.error("发送消息异常", e);
        }
    }

    //发送延时消息
    public void sendPmsDelayedMessage(OrionNormalMessageDTO orionNormalMessageDTO,String delayTime){
        logger.info("-------------------发送延时短信 = " + JSONObject.toJSONString(orionNormalMessageDTO)+",延迟时间："+delayTime);
        try {
            rabbitMQMessageHandler.sendDelayNormalMessage(orionNormalMessageDTO,delayTime);
        } catch (Exception e) {
            logger.error("发送消息异常", e);
        }
    }

}
