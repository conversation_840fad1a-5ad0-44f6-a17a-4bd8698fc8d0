//package com.chinasie.orion.service.impl;
//
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.chinasie.orion.bo.UserBo;
//import com.chinasie.orion.domain.entity.Plan;
//import com.chinasie.orion.domain.entity.PlanToResource;
//import com.chinasie.orion.domain.vo.PlanResourceVo;
//import com.chinasie.orion.domain.vo.ProjectRoleUserSearchVO;
//import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
//import com.chinasie.orion.repository.PlanToResourceRepository;
//
//import com.chinasie.orion.sdk.service.impl.OrionRelationServiceImpl;
//import com.chinasie.orion.service.PlanService;
//import com.chinasie.orion.service.PlanToResourceService;
//import com.chinasie.orion.service.ProjectRoleUserService;
//import com.chinasie.orion.util.BeanCopyUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.ObjectUtils;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/03/18/10:56
// * @description:
// */
//@Service
//public class PlanToResourceServiceImpl extends OrionBaseServiceImpl<PlanToResourceRepository,PlanToResource> implements PlanToResourceService {
//
//    @Resource
//    private ProjectRoleUserService projectRoleUserService;
//    @Resource
//    private PlanService planService;
//    @Resource
//    private UserBo userBo;
//
//
//    @Override
//    public List<PlanResourceVo> personList(String planId) throws Exception {
//        List<PlanResourceVo> list = new ArrayList<>();
//        Plan planDTO = planService.getById(planId);
//        if(ObjectUtils.isEmpty(planDTO)){
//            return list;
//        }
//        String projectId = planDTO.getProjectId();
//
//        LambdaQueryWrapper<PlanToResource> planToResourceOrionChainWrapper = new LambdaQueryWrapper<>(PlanToResource.class);
//        planToResourceOrionChainWrapper.eq(PlanToResource::getType, 1);
//        planToResourceOrionChainWrapper.eq(PlanToResource::getFromId,planId);
//        List<PlanToResource> planToResources = this.list(planToResourceOrionChainWrapper);
//        if(CollectionUtils.isEmpty(planToResources)){
//            return list;
//        }
//        List<String> idList = new ArrayList<>();
//        List<String> userIdList = new ArrayList<>();
//        for (PlanToResource planToResource : planToResources) {
//            idList.add(planToResource.getToId());
//            userIdList.add(planToResource.getModifyId());
//        }
//        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(userIdList);
//        List<ProjectRoleUserSearchVO> projectRoleUserByIds = projectRoleUserService.getProjectRoleUserByIds(projectId, idList);
//        Map<String,ProjectRoleUserSearchVO> idToUserVoMap = new HashMap<>();
//        if(!CollectionUtils.isEmpty(projectRoleUserByIds)){
//            idToUserVoMap = projectRoleUserByIds.stream().collect(Collectors.toMap(ProjectRoleUserSearchVO::getUserId,o->o));
//        }
//        for (PlanToResource planToResource : planToResources) {
//            PlanResourceVo planResourceVo = new PlanResourceVo();
//            String toId = planToResource.getToId();
//            BeanCopyUtils.copyProperties(planToResource,planResourceVo);
//            String modifyId = planToResource.getModifyId();
//            ProjectRoleUserSearchVO projectRoleUserSearchVO = idToUserVoMap.get(toId);
//            if(!ObjectUtils.isEmpty(projectRoleUserSearchVO)){
//                planResourceVo.setCostNum(0);
//                planResourceVo.setCreatorName(projectRoleUserSearchVO.getName());
//                planResourceVo.setDept(projectRoleUserSearchVO.getDept());
//                planResourceVo.setDeptName(projectRoleUserSearchVO.getDeptName());
//                planResourceVo.setNumber(projectRoleUserSearchVO.getNumber());
//                planResourceVo.setResourceCount(1);
//
//            }
//            planResourceVo.setModifyName(nameByUserIdMap.get(modifyId));
//            list.add(planResourceVo);
//        }
//        return list;
//    }
//}
