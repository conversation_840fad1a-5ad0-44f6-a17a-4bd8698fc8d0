package com.chinasie.orion.service.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.LaborCostAcceptanceStatisticsDTO;
import com.chinasie.orion.domain.entity.LaborCostAcceptanceStatistics;
import com.chinasie.orion.domain.vo.LaborCostAcceptanceStatisticsVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.LaborCostAcceptanceStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.LaborCostAcceptanceStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * LaborCostAcceptanceStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29 14:23:45
 */
@Service
@Slf4j
public class LaborCostAcceptanceStatisticsServiceImpl extends  OrionBaseServiceImpl<LaborCostAcceptanceStatisticsMapper, LaborCostAcceptanceStatistics>   implements LaborCostAcceptanceStatisticsService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public LaborCostAcceptanceStatisticsVO detail(String id, String pageCode) throws Exception {
        LaborCostAcceptanceStatistics laborCostAcceptanceStatistics =this.getById(id);
        LaborCostAcceptanceStatisticsVO result = BeanCopyUtils.convertTo(laborCostAcceptanceStatistics,LaborCostAcceptanceStatisticsVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param laborCostAcceptanceStatisticsDTO
     */
    @Override
    public  String create(LaborCostAcceptanceStatisticsDTO laborCostAcceptanceStatisticsDTO) throws Exception {
        LaborCostAcceptanceStatistics laborCostAcceptanceStatistics =BeanCopyUtils.convertTo(laborCostAcceptanceStatisticsDTO,LaborCostAcceptanceStatistics::new);
        this.save(laborCostAcceptanceStatistics);

        String rsp=laborCostAcceptanceStatistics.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param laborCostAcceptanceStatisticsDTO
     */
    @Override
    public Boolean edit(LaborCostAcceptanceStatisticsDTO laborCostAcceptanceStatisticsDTO) throws Exception {
        LaborCostAcceptanceStatistics laborCostAcceptanceStatistics =BeanCopyUtils.convertTo(laborCostAcceptanceStatisticsDTO,LaborCostAcceptanceStatistics::new);

        this.updateById(laborCostAcceptanceStatistics);

        String rsp=laborCostAcceptanceStatistics.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<LaborCostAcceptanceStatisticsVO> pages( Page<LaborCostAcceptanceStatisticsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<LaborCostAcceptanceStatistics> condition = new LambdaQueryWrapperX<>( LaborCostAcceptanceStatistics. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(LaborCostAcceptanceStatistics::getCreateTime);


        Page<LaborCostAcceptanceStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), LaborCostAcceptanceStatistics::new));

        PageResult<LaborCostAcceptanceStatistics> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<LaborCostAcceptanceStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<LaborCostAcceptanceStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), LaborCostAcceptanceStatisticsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "验收人力成本费用统计导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceStatisticsDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        LaborCostAcceptanceStatisticsExcelListener excelReadListener = new LaborCostAcceptanceStatisticsExcelListener();
        EasyExcel.read(inputStream,LaborCostAcceptanceStatisticsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<LaborCostAcceptanceStatisticsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("验收人力成本费用统计导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<LaborCostAcceptanceStatistics> laborCostAcceptanceStatisticses =BeanCopyUtils.convertListTo(dtoS,LaborCostAcceptanceStatistics::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::LaborCostAcceptanceStatistics-import::id", importId, laborCostAcceptanceStatisticses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<LaborCostAcceptanceStatistics> laborCostAcceptanceStatisticses = (List<LaborCostAcceptanceStatistics>) orionJ2CacheService.get("pmsx::LaborCostAcceptanceStatistics-import::id", importId);
        log.info("验收人力成本费用统计导入的入库数据={}", JSONUtil.toJsonStr(laborCostAcceptanceStatisticses));

        this.saveBatch(laborCostAcceptanceStatisticses);
        orionJ2CacheService.delete("pmsx::LaborCostAcceptanceStatistics-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::LaborCostAcceptanceStatistics-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<LaborCostAcceptanceStatistics> condition = new LambdaQueryWrapperX<>( LaborCostAcceptanceStatistics. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(LaborCostAcceptanceStatistics::getCreateTime);
        List<LaborCostAcceptanceStatistics> laborCostAcceptanceStatisticses =   this.list(condition);

        List<LaborCostAcceptanceStatisticsDTO> dtos = BeanCopyUtils.convertListTo(laborCostAcceptanceStatisticses, LaborCostAcceptanceStatisticsDTO::new);

        String fileName = "验收人力成本费用统计数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceStatisticsDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<LaborCostAcceptanceStatisticsVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class LaborCostAcceptanceStatisticsExcelListener extends AnalysisEventListener<LaborCostAcceptanceStatisticsDTO> {

        private final List<LaborCostAcceptanceStatisticsDTO> data = new ArrayList<>();

        @Override
        public void invoke(LaborCostAcceptanceStatisticsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<LaborCostAcceptanceStatisticsDTO> getData() {
            return data;
        }
    }


}
