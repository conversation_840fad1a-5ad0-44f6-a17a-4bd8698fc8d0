package com.chinasie.orion.service.impl;





import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.ProjectFinanceIndex;
import com.chinasie.orion.domain.dto.ProjectFinanceIndexDTO;
import com.chinasie.orion.domain.vo.ProjectFinanceIndexVO;



import com.chinasie.orion.service.ProjectFinanceIndexService;
import com.chinasie.orion.repository.ProjectFinanceIndexMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectFinanceIndex 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
@Service
@Slf4j
public class ProjectFinanceIndexServiceImpl extends  OrionBaseServiceImpl<ProjectFinanceIndexMapper, ProjectFinanceIndex>   implements ProjectFinanceIndexService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProjectFinanceIndexVO detail(String id,String pageCode) throws Exception {
        ProjectFinanceIndex projectFinanceIndex =this.getById(id);
        ProjectFinanceIndexVO result = BeanCopyUtils.convertTo(projectFinanceIndex,ProjectFinanceIndexVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectFinanceIndexDTO
     */
    @Override
    public  String create(ProjectFinanceIndexDTO projectFinanceIndexDTO) throws Exception {
        ProjectFinanceIndex projectFinanceIndex =BeanCopyUtils.convertTo(projectFinanceIndexDTO,ProjectFinanceIndex::new);
        this.save(projectFinanceIndex);

        String rsp=projectFinanceIndex.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectFinanceIndexDTO
     */
    @Override
    public Boolean edit(ProjectFinanceIndexDTO projectFinanceIndexDTO) throws Exception {
        ProjectFinanceIndex projectFinanceIndex =BeanCopyUtils.convertTo(projectFinanceIndexDTO,ProjectFinanceIndex::new);

        this.updateById(projectFinanceIndex);

        String rsp=projectFinanceIndex.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectFinanceIndexVO> pages( Page<ProjectFinanceIndexDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectFinanceIndex> condition = new LambdaQueryWrapperX<>( ProjectFinanceIndex. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if(ObjectUtils.isNotEmpty(pageRequest.getQuery())){
            if(StringUtils.hasText(pageRequest.getQuery().getProjectId())){
                condition.eq(ProjectFinanceIndex::getProjectId,pageRequest.getQuery().getProjectId());
            }
        }
        condition.orderByDesc(ProjectFinanceIndex::getCreateTime);


        Page<ProjectFinanceIndex> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectFinanceIndex::new));

        PageResult<ProjectFinanceIndex> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectFinanceIndexVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectFinanceIndexVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectFinanceIndexVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目-财务指标导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFinanceIndexDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectFinanceIndexExcelListener excelReadListener = new ProjectFinanceIndexExcelListener();
        EasyExcel.read(inputStream,ProjectFinanceIndexDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectFinanceIndexDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目-财务指标导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectFinanceIndex> projectFinanceIndexes =BeanCopyUtils.convertListTo(dtoS,ProjectFinanceIndex::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectFinanceIndex-import::id", importId, projectFinanceIndexes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectFinanceIndex> projectFinanceIndexes = (List<ProjectFinanceIndex>) orionJ2CacheService.get("pmsx::ProjectFinanceIndex-import::id", importId);
        log.info("项目-财务指标导入的入库数据={}", JSONUtil.toJsonStr(projectFinanceIndexes));

        this.saveBatch(projectFinanceIndexes);
        orionJ2CacheService.delete("pmsx::ProjectFinanceIndex-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectFinanceIndex-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectFinanceIndex> condition = new LambdaQueryWrapperX<>( ProjectFinanceIndex. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectFinanceIndex::getCreateTime);
        List<ProjectFinanceIndex> projectFinanceIndexes =   this.list(condition);

        List<ProjectFinanceIndexDTO> dtos = BeanCopyUtils.convertListTo(projectFinanceIndexes, ProjectFinanceIndexDTO::new);

        String fileName = "项目-财务指标数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFinanceIndexDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectFinanceIndexVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<ProjectFinanceIndexVO> listByInfo(ProjectFinanceIndexDTO projectFinanceIndexDTO) {
        List<ProjectFinanceIndex> financeIndices = this.list(new LambdaQueryWrapper<ProjectFinanceIndex>()
                .eq(ProjectFinanceIndex::getProjectId,projectFinanceIndexDTO.getProjectId()));
        List<ProjectFinanceIndexVO> vos = BeanCopyUtils.convertListTo(financeIndices, ProjectFinanceIndexVO::new);
        return vos;
    }


    public static class ProjectFinanceIndexExcelListener extends AnalysisEventListener<ProjectFinanceIndexDTO> {

        private final List<ProjectFinanceIndexDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectFinanceIndexDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectFinanceIndexDTO> getData() {
            return data;
        }
    }


}
