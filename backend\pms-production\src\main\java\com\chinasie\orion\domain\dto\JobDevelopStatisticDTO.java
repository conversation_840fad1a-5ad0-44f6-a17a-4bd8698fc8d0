package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.domain.vo.job.JobDevelopVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JobDevelopStatisticDTO DTO对象
 *
 */
@ApiModel(value = "JobDevelopStatisticDTO对象", description = "进展详情与统计")
@Data
@ExcelIgnoreUnannotated
public class JobDevelopStatisticDTO {

    private List<JobDevelopVO> jobDevelopVOS;

    private Map<String,Long> statisticMap;




}
