package com.chinasie.orion.domain.dto;

import com.chinasie.orion.page.SearchCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/22 14:55
 */
@Data
public class DeliverableQueryDTO implements Serializable {

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;


    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     *  预计交付时间
     */
    @ApiModelProperty(value = "预计交付时间")
    private List<Long> predictDeliverTime;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    private List<Long> deliveryTime;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "搜索")
    private List<List<SearchCondition>> searchConditions;

}
