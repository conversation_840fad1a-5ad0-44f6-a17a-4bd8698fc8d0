package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.tree.OrionTreeNodeDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * DocumentModelLibraryDir DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@ApiModel(value = "DocumentModelLibraryDirDTO对象", description = "文档模板库文件夹")
@Data
@ExcelIgnoreUnannotated
public class DocumentModelLibraryDirDTO extends  OrionTreeNodeDTO   implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 0)
    private String number;




}
