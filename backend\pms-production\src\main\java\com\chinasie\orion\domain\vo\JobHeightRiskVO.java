package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.Date;
import java.util.List;
/**
 * JobHeightRisk VO对象
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
@ApiModel(value = "JobHeightRiskVO对象", description = "作业高风险")
@Data
public class JobHeightRiskVO extends  ObjectVO   implements Serializable{

            /**
         * 风险级别
         */
        @ApiModelProperty(value = "风险级别")
        private String riskLevel;


        /**
         * 风险类型
         */
        @ApiModelProperty(value = "风险类型")
        private String riskTypeName;


        /**
         * 判断标准
         */
        @ApiModelProperty(value = "判断标准")
        private String judgmentStandards;


        /**
         * 作业编号
         */
        @ApiModelProperty(value = "作业编号")
        private String jobNumber;

        /**
         * 工作主题
         */
        @ApiModelProperty(value = "工作主题")
        private String workTopics;

        /**
         * 工单号
         */
        @ApiModelProperty(value = "工单号")
        private String workOrderNo;

        /**
         * 作业地点
         */
        @ApiModelProperty(value = "作业地点")
        private String jobAddress;

        /**
         * 作业地点名称
         */
        @ApiModelProperty(value = "作业地点名称")
        private String jobAddressName;

        /**
         * 计划开工时间
         */
        @ApiModelProperty(value = "计划开工时间")
        private Date planCommencementDate;

        /**
         * 工作描述
         */
        @ApiModelProperty(value = "工作描述")
        private String jobContent;

        /**
         * 作业部门
         */
        @ApiModelProperty(value = "作业部门")
        private String operatingDept;

        /**
         * 作业部门名称
         */
        @ApiModelProperty(value = "作业部门名称")
        private String operatingDeptName;

        /**
         * 项目负责人
         */
        @ApiModelProperty(value = "项目负责人")
        private String workOwnerName;

        /**
         * 项目负责人电话
         */
        @ApiModelProperty(value = "项目负责人电话")
        private String workOwnerPhone;

        /**
         * 管理人
         */
        @ApiModelProperty(value = "管理人")
        private String managerName;

        /**
         * 管理人电话
         */
        @ApiModelProperty(value = "管理人电话")
        private String managerPhone;

        /**
         * 作业过程状态
         */
        @ApiModelProperty(value = "作业过程状态")
        private String processStatus;

        /**
         * 当前环节
         */
        @ApiModelProperty(value = "当前环节")
        private String currentPhase;

        /**
         * 检查人
         */
        @ApiModelProperty(value = "监督人")
        private String checkName;


        /**
         * 监督人电话
         */
        @ApiModelProperty(value = "监督人电话")
        private String checkPhone;
}
