package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/17:40
 * @description:
 */
@Data
@TableName(value = "pms_plan_to_deliver_goals")
@ApiModel(value = "PlanToDeliverGoals", description = "计划和交付目标的关系")
public class PlanToDeliverGoals implements Serializable {

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;
    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    @TableField(value = "creator_id",fill = FieldFill.INSERT)
    private String creatorId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    @TableField(value = "to_class")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    @TableField(value = "from_class")
    private String fromClass;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

}
