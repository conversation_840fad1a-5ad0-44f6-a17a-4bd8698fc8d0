package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "PersonMangeVO对象", description = "人员管理")
@Data
public class LeadNumVO {

    /**
     * 线索类型
     */
    @ApiModelProperty(value = "线索类型")
    private Object LeadType;

    /**
     * 线索类型名称
     */
    @ApiModelProperty(value = "线索类型名称")
    private Object LeadTypeName;

    /**
     * 线索数量
     */
    @ApiModelProperty(value = "线索数量")
    private Long LeadTypeNumber;

    /**
     * 所占百分比
     */
    @ApiModelProperty(value = "所占百分比")
    private float LeadTypePercentage;

}

