package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.constant.JobPostAuthorizeEnum;
import com.chinasie.orion.domain.dto.PersonJobPostAuthorizeDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.AuthorizeJobPostRequirementVO;
import com.chinasie.orion.domain.vo.PmsJobPostLibraryVO;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.service.*;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 作业授权信息状态变更
 */
@Component
@Slf4j
public class JobPostAuthorizeChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "JobPostAuthorize";

    @Resource
    private JobPostAuthorizeService jobPostAuthorizeService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private PersonJobPostAuthorizeService personJobPostAuthorizeService;

    @Resource
    private PmsJobPostLibraryService pmsJobPostLibraryService;

    @Resource
    private JobManageService jobManageService;

    @Resource
    private AuthorPersonJobPostEquService authorPersonJobPostEquService;

    @Resource
    private PersonJobPostEquService postEquService;

    @Resource
    private BasePlaceService basePlaceService;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("作业授权信息状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
            if (Objects.nonNull(classVO)) {
                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(classVO.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
            }
        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("作业授权信息状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    public void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<JobPostAuthorize> wrapper=new LambdaUpdateWrapper<>(JobPostAuthorize.class);
        wrapper.eq(JobPostAuthorize::getId,message.getBusinessId());
        wrapper.set(JobPostAuthorize::getStatus,message.getStatus());
        if(Objects.equals(JobPostAuthorizeEnum.FINISH.getStatus(),message.getStatus())){
            wrapper.set(JobPostAuthorize::getIsAuthorization,2);
        }
        wrapper.set(JobPostAuthorize::getAuthorizeStatus,message.getStatus());
        boolean result = jobPostAuthorizeService.update(wrapper);
        // 如果岗位授权成功 需要判断授权的方式 ,如果是 非等效方式，需要将授权落地 （岗位）
        if(Objects.equals(JobPostAuthorizeEnum.FINISH.getStatus(),message.getStatus())){
            // 授权通过
            Date date = new Date();

            JobPostAuthorize entity = jobPostAuthorizeService.getById(message.getBusinessId());
            if(Objects.isNull(entity)){
                return;
            }
            Boolean isApplyJobEqu = entity.getIsApplyJobEqu();
        //  后续基地维护到缓存 （基地变动的记录极地）
            Map<String,String> codeToName = basePlaceService.allMapSimpleList();
            JobManage jobManage =  jobManageService.getById(entity.getJobId());
            if(null != isApplyJobEqu && !isApplyJobEqu){
                PersonJobPostAuthorizeDTO postAuthorizeDTO = new PersonJobPostAuthorizeDTO();
                postAuthorizeDTO.setAuthorizeStatus(JobPostAuthorizeEnum.FINISH.getStatus());
                postAuthorizeDTO.setAuthorizeStatusName(JobPostAuthorizeEnum.FINISH.getDesc());
                postAuthorizeDTO.setJobPostCode(entity.getJobPostCode());
                postAuthorizeDTO.setBaseCode(entity.getBasePlaceCode());
                postAuthorizeDTO.setBaseName(entity.getBasePlaceName());
                postAuthorizeDTO.setEndDate(entity.getEndDate());
                postAuthorizeDTO.setIsEquivalent(isApplyJobEqu);
                postAuthorizeDTO.setUserCode(entity.getUserCode());
                postAuthorizeDTO.setCreatorId(entity.getCreatorId());
                postAuthorizeDTO.setModifyId(entity.getModifyId());
                postAuthorizeDTO.setOwnerId(entity.getOwnerId());
                postAuthorizeDTO.setPlatformId(entity.getPlatformId());
                postAuthorizeDTO.setOrgId(entity.getOrgId());
                PmsJobPostLibraryVO postLibraryVO = pmsJobPostLibraryService.detailByNumber(entity.getJobPostCode());
                if(!Objects.isNull(postLibraryVO)){
                    postAuthorizeDTO.setJobPostName(postLibraryVO.getName());
                }


                if(!Objects.isNull(jobManage)){
                    postAuthorizeDTO.setJobCode(jobManage.getNumber());
                    postAuthorizeDTO.setRepairRound(jobManage.getRepairRound());
                }
                // 如果获取的 授权数据为空就重新赋值基地信息
                if(StrUtil.isEmpty(postAuthorizeDTO.getBaseCode())){
                    String baseName= codeToName.get(jobManage.getJobBase());

                    postAuthorizeDTO.setBaseCode(jobManage.getJobBase());
                    if(StrUtil.isNotEmpty(jobManage.getJobBaseName())){
                        postAuthorizeDTO.setBaseName(jobManage.getJobBaseName());
                    }else{
                        postAuthorizeDTO.setBaseName(baseName);
                    }
                    entity.setBasePlaceCode(jobManage.getJobBase());
                    entity.setBasePlaceName(postAuthorizeDTO.getBaseName());
                    jobPostAuthorizeService.updateById(entity);
                }
                postAuthorizeDTO.setSourceId(entity.getId());
                personJobPostAuthorizeService.create(postAuthorizeDTO);
            }

            // 如果等效
            if(isApplyJobEqu){
                // 如果等效 那么需要插入 等效信息
                //1.查询等效信息
                List<AuthorPersonJobPostEqu> requirementVOList= authorPersonJobPostEquService.listByAuthorManageId(message.getBusinessId());
                if(!CollectionUtils.isEmpty(requirementVOList)){
                    List<PersonJobPostEqu> personJobPostEqus = new ArrayList<>();
                    List<String> jobAuthorizeIdList = new ArrayList<>();
                    for (AuthorPersonJobPostEqu item : requirementVOList) {
                        jobAuthorizeIdList.add(item.getHistoryAuthorId());
                    }
                    List<PersonJobPostAuthorize> historyList = personJobPostAuthorizeService.listByIds(jobAuthorizeIdList.stream().distinct().collect(Collectors.toList()));
                    Map<String,PersonJobPostAuthorize> idToEntityMap = historyList.stream().collect(Collectors.toMap(LyraEntity::getId, Function.identity()));

                    Date endDate = null;
                    List<PersonJobPostAuthorize> personJobPostAuthorizes =new ArrayList<>();
                    String baseName= codeToName.get(jobManage.getJobBase());
                    for (AuthorPersonJobPostEqu item : requirementVOList) {
                        PersonJobPostEqu personJobPostEqu = new PersonJobPostEqu();
                        personJobPostEqu.setJobPostCode(item.getJobPostCode());
                        PersonJobPostAuthorize postAuthorize = idToEntityMap.getOrDefault(item.getHistoryAuthorId(),new PersonJobPostAuthorize());
//                        personJobPostEqu.setBaseCode(entity.getBasePlaceCode());
                        personJobPostEqu.setBaseCode(jobManage.getJobBase());
                        personJobPostEqu.setBaseName(baseName);
                        personJobPostEqu.setUserCode(postAuthorize.getUserCode());
                        personJobPostEqu.setEquivalentDate(date);
                        personJobPostEqu.setCreatorId(item.getCreatorId());
                        personJobPostEqu.setModifyId(item.getModifyId());
                        personJobPostEqu.setOwnerId(item.getOwnerId());
                        personJobPostEqu.setPlatformId(item.getPlatformId());
                        personJobPostEqu.setOrgId(item.getOrgId());
                        personJobPostEqu.setSourceId(item.getAuthorManageId());
                        personJobPostEqu.setFormRecordId(postAuthorize.getId());
                        personJobPostEqus.add(personJobPostEqu);
                        endDate= postAuthorize.getEndDate();
                        postAuthorize.setIsEquivalent(Boolean.TRUE);
                        personJobPostAuthorizes.add(postAuthorize);
                    }
                    entity.setEndDate(endDate);
                    // 修改岗位授权数据的 结束时间
                    jobPostAuthorizeService.updateById(entity);
                    // 修改被等效数据 改为已等效
                    personJobPostAuthorizeService.updateBatchById(personJobPostAuthorizes);
                    log.info("等效数据处理-JSON:{}", JSONUtil.toJsonStr(personJobPostEqus));
                    // 新增等效数据
                    postEquService.saveBatch(personJobPostEqus);
                }
            }
        };
        log.info("作业授权信息状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

}
