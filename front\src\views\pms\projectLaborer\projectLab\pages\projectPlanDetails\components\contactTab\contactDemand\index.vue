<template>
  <layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @buttonClick="tableButtonClick"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_OJJHXQ_container_02_01_button_01',powerData)"
          type="primary"
          icon="add"
          @click="() => clickType('add')"
        >
          添加
        </BasicButton>
      </template>
    </OrionTable>

    <!-- 查看详情弹窗 -->
    <checkDetails :data="nodeData" />
    <!-- 简易弹窗提醒 -->
    <messageModal
      :title="'确认提示'"
      :show-visible="showVisible"
      @cancel="showVisible = false"
      @confirm="confirm"
    >
      <div class="messageVal">
        <InfoCircleOutlined />
        <span>{{ message }}</span>
      </div>
    </messageModal>
    <!-- 新建/编辑抽屉 -->
    <!-- 从系统添加 -->
    <AddSystemRole
      :formId="formId"
      :data="addSystemModalData"
      @success="successSave"
    />

    <!-- 高级搜索抽屉 -->
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, inject, h, ref,
} from 'vue';
import {
  Layout, BasicTable, isPower, useDrawer, OrionTable, BasicButton,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress, Modal,
} from 'ant-design-vue';
import {
  InfoCircleOutlined,
} from '@ant-design/icons-vue';
/* 格式化时间 */
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import addProjectModal from './modal/addProjectModal.vue';
import AddSystemRole from './modal/addSystemRole.vue';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import {
  delDemandContactPageApi,
  getDemandContactPageApi,
} from '/@/views/pms/projectLaborer/api/demandManagement';
import SearchModal from './SearchModal.vue';
import { postProjectSchemeMilestoneNodePages } from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';
import { formatDate } from '/@/views/pms/projectLaborer/utils';
import { deleteProjectSchemeMilestoneNode } from '/@/views/pms/planTemplateManagement/api';
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {

    BasicButton,
    Layout,
    //   basicTitle,

    //  提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,

    AddSystemRole,
    OrionTable,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData = inject('powerData');
    const router = useRouter();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      /* 搜索框value */
      searchvlaue: '',
      /* 编辑send */
      editdataSource: {},
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      // 条数
      /* 总数 */
      total: 20,
      addNodeModalData: {},
      /* 选择行id */
      selectedRows: [],
      addSystemModalData: {},

      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      powerData: [],

    });
    const tableOptions = {
      deleteToolButton: `'add|enable|disable'${isPower('PMS_OJJHXQ_container_02_01_button_02', powerData) ? '' : '|delete'}`,
      rowSelection: {},
      pagination: false,
      showSmallSearch: false,
      resizeHeightOffset: 60,
      api() {
        return new Api(`/pms/projectScheme/relation/demand/lists/${props.formId}`).fetch('', '', 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          ellipsis: true,
          customRender({ record, text }) {
            return isPower('PMS_OJJHXQ_container_02_01_button_03', powerData) ? h('span', {
              onClick: () => {
                router.push({
                  name: 'PMSDemandManagementDetails',
                  query: {
                    itemId: record.id,
                    // 确保以下属性存在才传递
                    ...(record.dirId && { folderId: record.dirId }),
                    ...(record.dirName && { dirName: record.dirName }),

                  },
                });
              },
              class: 'action-btn',
            }, text) : text;
          },
          minWidth: 200,
        },
        {
          title: '标题',
          dataIndex: 'name',
          // customRender({ record, text }) {
          //   return h(
          //     'span',
          //     {
          //       class: computed(() => (isPower('LCB_container_button_14', state.powerData) ? 'action-btn' : '')).value,
          //       title: text,
          //       onClick(e) {
          //         if (isPower('LCB_container_button_14', state.powerData)) {
          //           handleCheck(record.id);
          //         }
          //         e.stopPropagation();
          //       },
          //     },
          //     text,
          //   );
          // },
          align: 'left',
          minWidth: 250,
        },
        {
          title: '提出人',
          dataIndex: 'exhibitorName',
          align: 'left',
          width: 150,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          width: 150,
          customRender: ({ text }) => formatDate(text, 'YYYY-MM-DD'),
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          width: 150,
          customRender: ({ text }) => formatDate(text, 'YYYY-MM-DD'),
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 150,
        },
        {
          title: '进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          width: 150,
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
          width: 120,
          fixed: 'right',
        },
      ],
      actions: [
        {
          event: 'delete',
          text: '删除',
          isShow: isPower('PMS_OJJHXQ_container_02_01_button_02', powerData),
          modal(record) {
            const deleteParams = {
              toId: props.formId,
              fromIds: [record.id],
            };
            const love = {
            };
            return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/demand/remove', 'DELETE');
          },
        },
      ],
    };

    state.powerData = inject('powerData');
    const tableRef = ref(null);
    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
      // console.log('测试🚀🚀 ~~~ state.selectedRows', state.selectedRows);
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;

      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'check':
          checkData();
          break;
        case 'add':
          addSystemRoleHandle();
          break;
        case 'open':
          openPlan();
          break;
        case 'search':
          // state.searchData = {};
          openSearchDrawer(true);
          break;
      }
    };
    let projectId: any = inject('projectId');

    const openPlan = () => {
      if (lengthCheckHandle()) return;
      const url = `pms/planManagement/planDetails?id=${state.selectedRowKeys[0]}&projectId=${projectId.value}`;
      window.open(`/${url}`);
    };
    const addSystemRoleHandle = () => {
      // console.log('从系统创建角色');
      state.addSystemModalData = { formType: 'add' };
    };
      /* 编辑 */
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      deletrow();
    };
    onMounted(() => {
      getFormData();
    });

    /* 删除操作 */
    const deletrow = () => {
      const newArr = {
        id: props.formId,
        planIds: state.selectedRowKeys,
      };
      const love = {
      };
      delDemandContactPageApi(newArr, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };

    const deleteRow = () => {
      let selectKeys = tableRef.value.getSelectKeys();
      if (selectRow && selectRow.length === 0) {
        message.warning('请选择一条数据进行操作');
        return;
      }
      const newArr = {
        id: props.formId,
        planIds: state.selectKeys,
      };
      // debugger;
      const love = {
      };
      delDemandContactPageApi(newArr, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      tableRef.value.reload();
    };
    function searchEmit(data) {
      getFormData();
    }
    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...state.dataSource.filter((item) => item.id === state.selectedRowKeys[0]),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;

      getFormData();
      state.searchvlaue = '';
    };
      /* 打开按钮 */
    // const openDetail = () => {
    //   if (lengthCheckHandle()) return;
    //
    //   // toDetails(state.selectedRows[0]);
    //   // state.searchvlaue = '';
    // };
    const toDetails = (data) => {
      router.push({
        name: 'RiskDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
    };
      /* 批量删除 */
    const multiDelete = (keys) => {
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否对当前选中数据进行删除？',
        onOk() {
          const deleteParams = {
            toId: props.formId,
            fromIds: keys,
          };
          return new Api('/pms').fetch(deleteParams, 'projectScheme/relation/demand/remove', 'DELETE').then(() => {
            getFormData();
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
      ];
      state.tablehttp.query = { projectId: '' };
      getFormData();
    };
    function tableButtonClick(params) {
      if (params && params.type === 'delete') {
        multiDelete(params.selectColumns.keys);
      }
    }
    /* 新建项目成功回调 */
    const successSave = () => {
      getFormData();
      state.searchvlaue = '';
      onSearch();
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };
    return {
      ...toRefs(state),
      clickRow,
      clickType,
      onSelectChange,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      onSearch,
      successSave,
      searchTable,
      addSystemRoleHandle,
      searchRegister,
      searchEmit,
      tableOptions,
      tableRef,
      tableButtonClick,
      powerData,
      isPower,
    };
  },

  // mounted() {}
});
</script>
