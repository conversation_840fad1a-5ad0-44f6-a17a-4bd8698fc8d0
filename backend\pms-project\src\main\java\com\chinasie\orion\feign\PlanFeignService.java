package com.chinasie.orion.feign;

import com.chinasie.orion.domain.vo.SchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/13 10:38
 */
@FeignClient(name = "plan", path = "",configuration = FeignConfig.class)
@Lazy
public interface PlanFeignService {
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/scheme/list/byIds", method = RequestMethod.POST)
    public ResponseDTO<List<SchemeVO>> listByIds(@RequestBody List<String> ids) throws Exception;
}
