<script setup lang="ts">
import { h, Ref, ref } from 'vue';
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import { postProjectSchemeList } from '/@/views/pms/projectLaborer/projectLab/api';
import { treeToList } from '/@/utils/helper/treeHelper';
import {
  formatTreeKey,
  formatTreeTop,
  getDefaultExpandedRowKeys,
} from '/@/views/pms/projectLibrary/pages/components/qualityControlItem/utill';
import { message, Tag } from 'ant-design-vue';
import { SituationColorEnum } from '/@/views/pms/projectLaborer/projectLab/enums';
import dayjs from 'dayjs';
const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
});
const tableRef = ref(null);
const indexData: Ref<any[]> = ref([]);
const defaultExpandedRowKeys = ref<string[]>([]);
const selectedRowKeys = ref([]);
const selectedRows = ref([]);
const columns = [
  // 序号
  {
    title: '序号',
    dataIndex: 'index',
    width: 100,
    fixed: 'left',
    customRender: ({ record }) => indexData.value?.filter((v) => v?.id === record?.id)[0]?.index,
  },
  // 计划名称
  {
    title: '计划名称',
    dataIndex: 'name',
    minWidth: 300,
    fixed: 'left',
  },
  // 计划层级
  {
    title: '计划层级',
    dataIndex: 'level',
    width: 100,
    customRender: ({ text }) => `${text}级`,
  },
  // 计划类型
  {
    title: '计划类型',
    dataIndex: 'nodeType',
    width: 120,
    customRender: ({ text }) => (text === 'milestone' ? '里程碑节点' : '计划'),
  },
  // 计划属性
  // 计划状态
  {
    title: '计划状态',
    dataIndex: 'dataStatus',
    width: 100,
    customRender({ record }) {
      return record.dataStatus
        ? h(DataStatusTag, { statusData: record.dataStatus })
        : '';
    },
  },
  // 完成情况
  {
    title: '完成情况',
    dataIndex: 'circumstance',
    width: 120,
    customRender({ record }) {
      return h(
        Tag,
        { color: SituationColorEnum[record.circumstance] },
        `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`,
      );
    },
  },
  // 责任部门
  {
    title: '责任部门',
    dataIndex: 'rspSubDeptName',
    width: 120,
  },
  // 责任人
  {
    title: '责任人',
    dataIndex: 'rspUserName',
    width: 120,
  },
  // 计划开始日期
  {
    title: '计划开始日期',
    dataIndex: 'beginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 计划结束日期
  {
    title: '计划结束日期',
    dataIndex: 'endTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 实际开始日期
  {
    title: '实际开始日期',
    dataIndex: 'actualBeginTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  // 实际结束日期
  {
    title: '实际结束日期',
    dataIndex: 'actualEndTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
];
// 自定义用户选择
const onSelectChange = (record, selected) => {
  selectedRowKeys.value = selectedRowKeys.value.filter((item) => !item.endsWith('Top'));
  selectedRows.value = selectedRows.value.filter((item) => !item.key.endsWith('Top'));
  if (selected) {
    let rows = [];
    let rowKeys = [];
    if (record?.children) {
      rows = treeToList(record.children);
      rowKeys = rows.map((item) => item.id);
    }
    selectedRowKeys.value = [
      ...selectedRowKeys.value,
      record.id,
      ...rowKeys,
    ];
    selectedRows.value = [
      ...selectedRows.value,
      {
        ...record,
        key: record.id,
      },
      ...rows,
    ];
  } else if (record?.children) {
    let keys = [
      record.id,
      ...treeToList(record.children)
        .map((item) => item.id),
    ];
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => !keys.includes(item));
    selectedRows.value = selectedRows.value.filter((item) => !keys.includes(item.id));
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => item !== record.id);
    selectedRows.value = selectedRows.value.filter((item) => item.id !== record.id);
  }
  let arrTop = [];
  let rowTop = [];
  for (let i = 0; i < selectedRowKeys.value.length; i++) {
    if (!arrTop.includes(`${selectedRowKeys.value[i]}Top`)) {
      arrTop.push(`${selectedRowKeys.value[i]}Top`);
    }
    if (rowTop.every((item) => item.key !== `${selectedRowKeys.value[i]}Top`)) {
      rowTop.push({
        ...selectedRows.value[i],
        key: `${selectedRowKeys.value[i]}Top`,
      });
    }
  }
  selectedRowKeys.value = selectedRowKeys.value.concat(arrTop);
  selectedRows.value = selectedRows.value.concat(rowTop);
};
// 用户全选回调
const onSelectAll = (selected, rows) => {
  if (selected) {
    selectedRowKeys.value = rows.filter((item) => item)
      .map((item) => item.key);
    selectedRows.value = rows.filter((item) => item);
  } else {
    selectedRowKeys.value = [];
    selectedRows.value = [];
  }
};
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {
    selectedRowKeys,
    onSelect: onSelectChange,
    onSelectAll,
  },
  resizeHeightOffset: 150,
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: false,
  isFilter2: true,
  filterConfig: {},
  rowClassName: (record) => (record.topSort ? 'table-striped' : null),
  api: async (params) => {
    params.projectId = props.projectId;
    params.typeEnum = 'PROJECT_SCHEME';
    const result = await postProjectSchemeList(params);
    let allData = formatTreeTop(JSON.parse(JSON.stringify(result))
      .reverse(), [])
      .reverse();
    indexData.value = treeToList(formatTreeKey(allData.filter((v) => v.key.substr(-3) !== 'Top'), [], ''));
    return allData;
  },
  columns,
});

const initData = (data) => {
  defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
};

// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const keys = selectedRowKeys.value;
  if (keys.length > 0) {
    resolve(keys);
  } else {
    message.warning('请选择数据');
    reject();
  }
});

defineExpose({
  isSelectedAndGetData,
});
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      class="plan"
      :options="tableOptions"
      :expandIconColumnIndex="3"
      :rowKey="(record) => record.key"
      :expandedRowKeys="defaultExpandedRowKeys"
      @initData="initData"
    />
  </div>
</template>

<style scoped lang="less">

</style>
