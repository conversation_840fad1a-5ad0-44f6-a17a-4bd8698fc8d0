<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          label="所属分类"
          name="parentId"
        >
          <a-tree-select
            v-model:value="formState.parentId"
            style="width: 100%"
            :tree-data="parentId"
            placeholder="请选择所属分类"
            size="large"
          />
        </a-form-item>

        <a-form-item
          ref="name"
          label="名称:"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <!-- <a-form-item label="状态:">
          <a-select
            ref="select"
            v-model:value="formState.status"
            :placeholder="'请选择产品状态'"
            size="large"
            :options="statusNameoptions"
          />
        </a-form-item> -->

        <a-form-item label="描述:">
          <a-textarea
            v-model:value="formState.remark"
            style="height: 120px"
            placeholder="请输入描述"
          />
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </BasicDrawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  Image,
  TreeSelect,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
/* api */
//   import { addStatusApi, editStatusApi, getStatusApi } from '/@/views/pmsx/api/projectList';
import { getSimpleTreeApi, addDocApi, editDocApi } from '/@/views/pms/projectLaborer/api/docManagement';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    aSelect: Select,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: {
      type: Array,
      default: () => [],
    },
    id: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      // statusNameoptions: [],
      /* 联系方式 */
      contacttype: [],
      /* 联系类型 */
      concactTypeOption: [],
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: <any>{
        parentId: '',
        name: '',
        remark: '',
        id: '',
      },
      oldformState: {},
      showVisible: false,
      // 表单是否发生变化
      flag: false,
      // formState: <any>{},
      parentId: <any>[],
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
      ],
      parentId: [
        {
          required: true,
          message: '请选择所属分类',
          trigger: 'blur',
        },
      ],
    };
    watch(
      () => props.data,
      async (value) => {
        console.log(props.listData);
        state.visible = true;
        state.nextCheck = false;
        if (value.formType == 'add') {
          state.title = '新增信息';
          state.formType = 'add';
          if (value.defaultKey) {
            state.formState.parentId = value.defaultKey;
          }
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          state.oldformState = { ...props.listData };
          for (let namex in props.listData) {
            state.formState[namex] = props.listData[namex];
          }
        }
        const res = await getSimpleTreeApi({ projectId: props.id });
        state.parentId = convertTree(res);
      },
    );
    const convertTree = (tree) => {
      const result = [];
      tree.forEach((item) => {
        let children = item.child || [];
        // let expand = true;
        let { name: title, id: key, id: value } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          // expand,
          key,
          value,
        });
      });
      return result;
    };
      /* 侦听表单是否变化 */
    watch(
      state.formState,
      () => {
        state.flag = true;
      },
      {
        deep: true,
        immediate: true,
      },
    );
    /* 表单取消按钮 */
    const cancel = () => {
      console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
    };
      /* x按钮 */
    const close = () => {
      console.log('close', 'x按钮', 228);
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
      state.flag = false;
      formRef.value.resetFields();
      state.formState = {};
      state.oldformState = {};
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      console.log('195');
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
      state.oldformState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      if (state.formType === 'add') {
        delete state.formState.id;
      }
      state.formState.projectId = props.id;
      const love = {
        name: state.formState?.name,
        className: 'DocumentType',
        moduleName: '项目管理-文档管理-文档类型',
        type: 'SAVE',
        remark: `新增了【${state.formState?.name}】`,
      };
      const love2 = {
        id: state.formState?.id,
        name: state.formState?.name,
        className: 'DocumentType',
        moduleName: '项目管理-文档管理-文档类型',
        type: 'UPDATE',
        remark: `编辑了【${state.formState?.id}】`,
      };

      formRef.value
        .validate()
        .then(() => {
          state.formType === 'edit'
            ? zhttp(editDocApi(state.formState, love2))
            : zhttp(addDocApi(state.formState, love));
        })
        .catch((error) => {
          console.log('error', error);
        });
    };
    const zhttp = (fn) => {
      state.loading = true;
      console.log(111);
      fn.then(() => {
        message.success('保存成功');
        state.loading = false;
        if (state.nextCheck) {
          formRef.value.resetFields();
        } else {
          state.visible = false;
        }
        emit('success', false);
        state.formState = {};
        state.oldformState = {};
      }).catch((err) => {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 242 ~ err', err);
        state.loading = false;
      });
    };

    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
    };
  },
});
</script>
<style lang="less" scoped>
.nodeForm {
  padding: 10px 10px 80px 10px;
}
.ant-form-item{
  display: block;
}
.nextCheck {
  height: 40px;
  line-height: 40px;
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
  //.addNodeModalDrawer {
  //  .ant-checkbox-wrapper,
  //  .ant-form-item-label > label {
  //    color: #444b5e;
  //  }
  //  .nodeForm {
  //    padding: 10px 10px 80px 10px;
  //  }
  //  .ant-form-item-label {
  //    text-align: left;
  //    color: #444b5e;
  //  }
  //  .cancelBtn {
  //    color: #5172dc;
  //    background: #5172dc19;
  //    width: 120px;
  //    border-radius: 4px;
  //  }
  //  .bgDC {
  //    width: 120px;
  //    margin-left: 15px;
  //    border-radius: 4px;
  //  }
  //  .nextCheck {
  //    height: 40px;
  //    line-height: 40px;
  //  }
  //  .nodeItemBtn {
  //    position: fixed;
  //    bottom: 0px;
  //    padding: 20px 0px;
  //    text-align: center;
  //    width: 320px;
  //    height: 80px;
  //    background: #ffffff;
  //    margin-bottom: 0px;
  //  }
  //}
</style>
