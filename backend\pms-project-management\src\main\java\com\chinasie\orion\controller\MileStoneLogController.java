package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.dto.MileStoneExcelDto;
import com.chinasie.orion.domain.dto.MileStoneLogDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.MileStoneLogVO;
import com.chinasie.orion.service.MileStoneLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;





import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MileStoneLog 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
@RestController
@RequestMapping("/mileStoneLog")
@Api(tags = "里程碑执行记录")
public class  MileStoneLogController  {

    @Autowired
    private MileStoneLogService mileStoneLogService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "里程碑执行记录", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MileStoneLogVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        MileStoneLogVO rsp = mileStoneLogService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     *
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "里程碑执行记录")
    @RequestMapping(value = "/listMileStoneLog", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】里程碑执行记录【{{#mileStoneLogDTO.name}}】", type = "里程碑执行记录", subType = "里程碑执行记录", bizNo = "{{#id}}")
    public ResponseDTO listMileStoneLog(@RequestBody MileStoneLogDTO mileStoneLogDTO) throws Exception {
       List<MileStoneLogVO> list = mileStoneLogService.listMileStoneLog(mileStoneLogDTO);
        return new ResponseDTO<>(list);
    }


    /**
     * 新增
     *
     * @param mileStoneLogDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#mileStoneLogDTO.name}}】", type = "里程碑执行记录", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MileStoneLogDTO mileStoneLogDTO) throws Exception {
        String rsp =  mileStoneLogService.create(mileStoneLogDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param mileStoneLogDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#mileStoneLogDTO.name}}】", type = "里程碑执行记录", subType = "编辑", bizNo = "{{#mileStoneLogDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MileStoneLogDTO mileStoneLogDTO) throws Exception {
        Boolean rsp = mileStoneLogService.edit(mileStoneLogDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "里程碑执行记录", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = mileStoneLogService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "里程碑执行记录", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = mileStoneLogService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "里程碑执行记录", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MileStoneLogVO>> pages(@RequestBody Page<MileStoneLogDTO> pageRequest) throws Exception {
        Page<MileStoneLogVO> rsp =  mileStoneLogService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "跟踪确认分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】跟踪确认分页", type = "里程碑执行记录", subType = "跟踪确认分页", bizNo = "")
    @RequestMapping(value = "/reschedule/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MileStoneLogVO>> reschedulePages(@RequestBody Page<MileStoneLogDTO> pageRequest) throws Exception {
        Page<MileStoneLogVO> rsp =  mileStoneLogService.reschedulePages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "里程碑验收表单分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】里程碑验收表单分页", type = "里程碑执行记录", subType = "里程碑验收表单分页", bizNo = "")
    @RequestMapping(value = "/acceptance/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MileStoneLogVO>> acceptancePages(@RequestBody Page<MileStoneLogDTO> pageRequest) throws Exception {
        Page<MileStoneLogVO> rsp =  mileStoneLogService.acceptancePages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("里程碑执行记录导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "里程碑执行记录", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        mileStoneLogService.downloadExcelTpl(response);
    }

    @ApiOperation("里程碑执行记录导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "里程碑执行记录", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = mileStoneLogService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("里程碑执行记录导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "里程碑执行记录", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  mileStoneLogService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消里程碑执行记录导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "里程碑执行记录", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  mileStoneLogService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("里程碑执行记录导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "里程碑执行记录", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        mileStoneLogService.exportByExcel(searchConditions, response);
    }


    @ApiOperation(value = "里程碑树形报表导出")
    @PostMapping(value = "/export/excelData",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】里程碑树形报表导出", type = "ContractMilestone", subType = "里程碑树形报表导出", bizNo = "")
    public void exportExcelDatatree(@RequestBody MileStoneTreeVo dto, HttpServletResponse response) throws Exception {
        mileStoneLogService.exportExcelDatatree(dto,response);

    }

    @ApiOperation("里程碑导入下载模板")
    @PostMapping (value = "/download/excel/mileStone", produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "MileStone", subType = "导入下载模板", bizNo = "")
    public void downloadExcelMileStone(@RequestBody MileStoneExcelDto dto, HttpServletResponse response) throws Exception {
        mileStoneLogService.downloadExcelMileStone(dto,response);
    }

    @ApiOperation("里程碑导入下载模板校验")
    @PostMapping(value = "/import/excel/mileStoneCheck")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "MileStone", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> mileStoneCheck(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = mileStoneLogService.mileStoneCheck(file);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("里程碑导入下载模板确认导入")
    @PostMapping(value = "/import/excel/mileStoneImport")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "MileStone", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> mileStoneImport(@RequestBody MileStoneExcelDto dto) throws Exception {
        Boolean rsp =  mileStoneLogService.mileStoneImport(dto);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "里程碑树形报表导出新")
    @PostMapping(value = "/export/excelData/new",produces = "application/octet-stream")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出了合同内里程碑数据", type = "ContractMilestone", subType = "里程碑树形报表导出新", bizNo = "")
    public void exportExcelDatatreeNew(@RequestBody MileStoneTreeVo dto, HttpServletResponse response) throws Exception {
        mileStoneLogService.exportExcelDatatreeNew(dto,response);

    }

    @ApiOperation(value = "里程碑菜单分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "MileStoneLog", subType = "里程碑菜单分页", bizNo = "")
    @RequestMapping(value = "/page/menu", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractMilestoneVO>> pagesMenu(@RequestBody Page<ContractMilestoneDTO> pageRequest) throws Exception {
        Page<ContractMilestoneVO> rsp =  mileStoneLogService.pagesMenu(pageRequest);
        return new ResponseDTO<>(rsp);
    }


}
