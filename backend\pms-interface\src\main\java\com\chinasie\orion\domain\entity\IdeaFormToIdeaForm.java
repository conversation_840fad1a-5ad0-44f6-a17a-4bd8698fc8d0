package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IdeaFormToIdeaForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@TableName(value = "pmsx_idea_form_to_idea_form")
@ApiModel(value = "IdeaFormToIdeaFormEntity对象", description = "意见单和意见单的关系")
@Data
public class IdeaFormToIdeaForm extends ObjectEntity implements Serializable {

    /**
     * 来源id
     */
    @ApiModelProperty(value = "来源id")
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 目标ID
     */
    @ApiModelProperty(value = "目标ID")
    @TableField(value = "target_id")
    private String targetId;

}
