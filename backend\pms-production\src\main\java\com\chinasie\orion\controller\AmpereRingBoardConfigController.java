package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigJobDTO;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigDeptVO;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigJobVO;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigKpiVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingBoardConfigDeptService;
import com.chinasie.orion.service.AmpereRingBoardConfigJobService;
import com.chinasie.orion.service.AmpereRingBoardConfigKpiService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 * <p>
 * 安质环 看板维护
 **/
@Api(tags = "安质环看板维护")
@RestController
@RequestMapping("/ampere_ring/board/config")
public class AmpereRingBoardConfigController {

    @Autowired
    private AmpereRingBoardConfigKpiService ampereRingBoardConfigKpiService;

    @Autowired
    private AmpereRingBoardConfigDeptService ampereRingBoardConfigDeptService;

    @Autowired
    private AmpereRingBoardConfigJobService ampereRingBoardConfigJobService;

    //--------------------考核指标--------------------------

    /**
     * 考核指标的查询
     * @param kpiVo
     * @return
     */
    @ApiOperation("查询考核指标")
    @PostMapping("/query/kpi")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询考核指标数据", type = "AmpereRingBoardConfigKpi", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<AmpereRingBoardConfigKpiVO>> queryKpi(@RequestBody AmpereRingBoardConfigKpiVO kpiVo) {
        List<AmpereRingBoardConfigKpiVO> kpiVOS = ampereRingBoardConfigKpiService.queryKpi(kpiVo);
        return new ResponseDTO<>(kpiVOS);
    }

    /**
     * 考核指标的添加
     * @param  ampereRingBoardConfigKpiDTO
     * @return
     */
    @ApiOperation("考核指标的添加")
    @PostMapping("/kpi/add")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增考核指标数据【{{#ampereRingBoardConfigKpiDTO.event_level}}】", type = "AmpereRingBoardConfigKpi", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> kpiAdd(@RequestBody  AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO){
        Boolean id=ampereRingBoardConfigKpiService.kpiAdd(ampereRingBoardConfigKpiDTO);
        LogRecordContext.putVariable("id", id);
        return new ResponseDTO<>(id);
    }

    /**
     * 考核指标移除
     * @param ids
     * @return
     */
    @ApiOperation("考核指标移除")
    @DeleteMapping("/kpi/remove")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除考核指标数据", type = "AmpereRingBoardConfigKpi", subType = "批量删除", bizNo = "")
    public ResponseDTO<Boolean> removeIds(@RequestBody List<String> ids){
       Boolean rsp= ampereRingBoardConfigKpiService.removeIds(ids);
       return new ResponseDTO<>(rsp);
    }

    /**
     * 考核指标移动
     */
    @ApiOperation("考核指标移动")
    @PostMapping("/kpi/move")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量移动考核指标数据", type = "AmpereRingBoardConfigKpi", subType = "批量移动", bizNo = "")
    public ResponseDTO<Boolean> kpiMove(@RequestBody AmpereRingBoardConfigKpiDTO boardConfigKpiDTO){
        Boolean rsp= ampereRingBoardConfigKpiService.kpiMove(boardConfigKpiDTO);
        return new ResponseDTO<>(rsp);
    }
//-----------------安全生产/隐患排查 的部门维护---------------------------------------
    /**
     * 查询安全生产/隐患排查 部门分页
     * @param pageRequest
     * @return
     * @throws Exception
     */

    @ApiOperation("查询安全生产/隐患排查 部门分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安全生产/隐患排查 分页数据", type = "AmpereRingBoardConfigDept", subType = "分页查询", bizNo = "")
    @PostMapping(value = "/dept/config/page")
    public ResponseDTO<Page<AmpereRingBoardConfigDeptVO>> deptPages(@RequestBody @Validated Page<AmpereRingBoardConfigDeptVO> pageRequest) throws Exception {
        Page<AmpereRingBoardConfigDeptVO> rsp =  ampereRingBoardConfigDeptService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询安全生产/隐患排查 部门列表"
     * @param ampereRingBoardConfigDeptVO
     * @return
     * @throws Exception
     */
    @ApiOperation("查询安全生产/隐患排查 部门列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安全生产/隐患排查 部门列表数据", type = "AmpereRingBoardConfigDept", subType = "列表查询", bizNo = "")
    @PostMapping(value = "/dept/config/list")
    public ResponseDTO<List<AmpereRingBoardConfigDeptVO>> deptList(@RequestBody @Validated AmpereRingBoardConfigDeptVO ampereRingBoardConfigDeptVO) throws Exception {
        List<AmpereRingBoardConfigDeptVO> rsp =  ampereRingBoardConfigDeptService.list(ampereRingBoardConfigDeptVO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("隐患排查 的部门 移动")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】隐患排查 的部门移动", type = "AmpereRingBoardConfigDeptDTO", subType = "移动部门序号", bizNo = "")
    @PostMapping(value = "/dept/config/move")
    public ResponseDTO<Boolean> deptMove(@RequestBody @Validated AmpereRingBoardConfigDeptDTO configDeptDTO) throws Exception {
        Boolean rsp =  ampereRingBoardConfigDeptService.move(configDeptDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("安全生产/隐患排查 部门的看板是否展示")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】安全生产/隐患排查 部门的看板是否展示", type = "AmpereRingBoardConfigDeptDTO", subType = "控制部门看板是否展示", bizNo = "")
    @PostMapping(value = "/dept/config/isShow")
    public ResponseDTO<Boolean> deptIsShow(@RequestBody AmpereRingBoardConfigDeptDTO configDeptDTO) throws Exception {
        Boolean rsp =  ampereRingBoardConfigDeptService.isShow(configDeptDTO);
        return new ResponseDTO<>(rsp);
    }

    //-----------------------作业维护---------------------------------
    /**
     * 安质环看板作业维护
     */
    @ApiOperation("作业信息 的查询分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询作业信息分页数据", type = "AmpereRingBoardConfigJobVO", subType = "分页查询", bizNo = "")
    @PostMapping(value = "/job/config/pages")
    public ResponseDTO<Page<AmpereRingBoardConfigJobVO>> jobPages(@RequestBody  Page<AmpereRingBoardConfigJobVO> pageRequest) throws Exception {
        Page<AmpereRingBoardConfigJobVO> rsp =  ampereRingBoardConfigJobService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 安质环看板作业维护
     */
    @ApiOperation("作业信息 的查询列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询作业信息列表数据", type = "AmpereRingBoardConfigJobVO", subType = "列表查询", bizNo = "")
    @PostMapping(value = "/job/config/list")
    public ResponseDTO<List<AmpereRingBoardConfigJobVO>> jobList(@RequestBody  AmpereRingBoardConfigJobVO jobVO) throws Exception {
        List<AmpereRingBoardConfigJobVO> rsp =  ampereRingBoardConfigJobService.list(jobVO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 是否在看板中展示
     */
    @ApiOperation("作业信息 的查询列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】看板是否展示", type = "AmpereRingBoardConfigJobDTO", subType = "看板是否展示", bizNo = "")
    @PostMapping(value = "/job/config/isShow")
    public ResponseDTO<Boolean> jobIsShow(@RequestBody AmpereRingBoardConfigJobDTO jobDTO) throws Exception {
        Boolean rsp =  ampereRingBoardConfigJobService.isShow(jobDTO);
        return new ResponseDTO<>(rsp);
    }

}
