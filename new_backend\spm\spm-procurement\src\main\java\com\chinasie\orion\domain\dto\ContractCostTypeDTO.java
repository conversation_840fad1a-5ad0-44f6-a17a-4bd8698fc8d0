package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractCostType DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:39:59
 */
@ApiModel(value = "ContractCostTypeDTO对象", description = "合同计划成本类型")
@Data
@ExcelIgnoreUnannotated
public class ContractCostTypeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 成本类型编号
     */
    @ApiModelProperty(value = "成本类型编号")
    @ExcelProperty(value = "成本类型编号 ", index = 1)
    private String costTypeNumber;

    /**
     * 成本类型名称
     */
    @ApiModelProperty(value = "成本类型名称")
    @ExcelProperty(value = "成本类型名称 ", index = 2)
    private String costTypeName;

    /**
     * 成本名称
     */
    @ApiModelProperty(value = "成本名称")
    @ExcelProperty(value = "成本名称 ", index = 3)
    private String costName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 4)
    private BigDecimal unitPrice;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位 ", index = 5)
    private String unit;




}
