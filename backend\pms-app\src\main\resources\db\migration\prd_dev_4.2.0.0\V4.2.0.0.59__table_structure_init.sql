-- 新增字段 对于兼容安质环那边的 隐患表信息
-- alter TABLE pms_amperering_event_check_data_info
--     add COLUMN  `reviewer_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检查人名称',
--     add COLUMN   `dept_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检查人所在部门ID',
--     add COLUMN   `dept_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检查人所在部门名称',
--     add COLUMN   `dept_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检测人所在部门编号',
--     add COLUMN   `is_major_repair` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否大修',
--     add COLUMN   `hidden_event` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '隐患',
--     add COLUMN   `is_closed` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否已关闭',
--     add COLUMN   `current_process` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '当前流程',
--     add COLUMN   `rsp_dept_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '直接责任部门code',
--     add COLUMN    `assessment_level` varchar(64) DEFAULT NULL COMMENT '考核级别',
--     add COLUMN `major_repair_turn` varchar(64) DEFAULT NULL COMMENT '大修轮次',
--     add COLUMN	`is_assessed` bit(1) DEFAULT NULL COMMENT '是否考核',
--     add COLUMN `pyramid_category` varchar(256) DEFAULT NULL COMMENT '金字塔类别';

-- ALTER  TABLE pms_amperering_event_check_data_info
--     ADD COLUMN 		`reviewer_number` varchar(64) DEFAULT NULL COMMENT '检查人编号',
--     ADD COLUMN 	  `rsp_dept_name` varchar(64) DEFAULT NULL COMMENT '直接责任部门名称',
--     ADD COLUMN 	    `event_position` varchar(256) DEFAULT NULL COMMENT '事件位置',
--     ADD COLUMN 			`hidden_event` varchar(256) DEFAULT NULL COMMENT '事件领域',
--     ADD COLUMN			`classification_type` varchar(128) DEFAULT NULL COMMENT '分类类型',
--     ADD COLUMN    `hidden_danger_type` varchar(64) DEFAULT NULL COMMENT '隐患类型';
