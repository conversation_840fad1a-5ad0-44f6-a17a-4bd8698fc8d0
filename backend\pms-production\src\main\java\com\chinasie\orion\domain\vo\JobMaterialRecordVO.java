package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * JobMaterialRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:37
 */
@ApiModel(value = "JobMaterialRecordVO对象", description = "作业物资记录表")
@Data
public class JobMaterialRecordVO extends  ObjectVO   implements Serializable{

            /**
         * 作业ID
         */
        @ApiModelProperty(value = "作业ID")
        private String jobId;


        /**
         * 物资code
         */
        @ApiModelProperty(value = "物资code")
        private String materiaCode;


        /**
         * 物资管理ID
         */
        @ApiModelProperty(value = "物资管理ID")
        private String materiaManageId;


    

}
