<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.springframework.cloud</groupId>
	<artifactId>spring-cloud-build</artifactId>
	<version>3.1.5</version>
	<packaging>pom</packaging>
	<name>Spring Cloud Parent</name>
	<description>Spring Cloud parent pom, managing plugins and dependencies for Spring
		Cloud projects
	</description>
	<modules>
		<module>docs</module>
		<module>spring-cloud-build-dependencies</module>
		<module>spring-cloud-dependencies-parent</module>
		<module>spring-cloud-build-tools</module>
	</modules>
	<url>https://spring.io/spring-cloud</url>
	<properties>
		<java.version>1.8</java.version>
		<!-- delimiter that doesn't clash with Spring ${} placeholders -->
		<resource.delimiter>@</resource.delimiter>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>
		<main.basedir>${basedir}</main.basedir>
		<docs.main>${project.artifactId}</docs.main>
		<spring-boot.version>2.6.13</spring-boot.version>
		<spring-cloud-build.version>3.1.5</spring-cloud-build.version>
		<docs.resources.dir>${project.build.directory}/build-docs</docs.resources.dir>
		<docs.classes.dir>${project.build.directory}/build-docs-classes</docs.classes.dir>
		<refdocs.build.directory>${project.build.directory}/refdocs/</refdocs.build.directory>
		<spring-doc-resources.version>0.2.2.RELEASE</spring-doc-resources.version>
		<spring-asciidoctor-extensions-boot.version>0.6.0
		</spring-asciidoctor-extensions-boot.version>
		<spring-asciidoctor-extensions-block.version>0.6.0
		</spring-asciidoctor-extensions-block.version>
		<asciidoctorj-pdf.version>1.6.0</asciidoctorj-pdf.version>
		<asciidoctorj-diagram.version>2.1.2</asciidoctorj-diagram.version>
		<guides-project.version>${project.version}</guides-project.version>
		<guides-update.phase>deploy</guides-update.phase>
		<revision>${project.version}</revision>
		<asciidoctorj.version>2.5.1</asciidoctorj.version>
		<jruby-complete.version>********</jruby-complete.version>

		<!-- Sonar -->
		<sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
		<sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
		<sonar.jacoco.reportPath>${project.basedir}/../target/jacoco.exec
		</sonar.jacoco.reportPath>
		<sonar.language>java</sonar.language>

		<!-- Plugins -->
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
		<maven-eclipse-plugin.version>2.10</maven-eclipse-plugin.version>
		<!-- for backwards compatibility, use maven-checkstyle-plugin.version -->
		<checkstyle.version>${maven-checkstyle-plugin.version}</checkstyle.version>
		<puppycrawl-tools-checkstyle.version>8.43</puppycrawl-tools-checkstyle.version>
		<spring-javaformat.version>0.0.26</spring-javaformat.version>
		<maven-failsafe-plugin.version>2.22.2</maven-failsafe-plugin.version>
		<maven-javadoc-plugin.version>3.3.0</maven-javadoc-plugin.version>
		<maven-source-plugin.version>3.2.1</maven-source-plugin.version>
		<maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
		<maven-antrun-plugin.version>3.0.0</maven-antrun-plugin.version>
		<maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
		<maven-surefire-report-plugin.version>2.22.2</maven-surefire-report-plugin.version>
		<maven-war-plugin.version>3.3.1</maven-war-plugin.version>
		<exec-maven-plugin.version>1.6.0</exec-maven-plugin.version>
		<maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
		<git-commit-id-plugin.version>4.0.5</git-commit-id-plugin.version>
		<maven-shade-plugin.version>3.2.4</maven-shade-plugin.version>
		<maven-enforcer-plugin.version>3.0.0-M3</maven-enforcer-plugin.version><!-- @releaser:version-check-off -->
		<maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
		<maven-dependency-plugin.version>3.2.0</maven-dependency-plugin.version>
		<asciidoctor-maven-plugin.version>2.1.0</asciidoctor-maven-plugin.version>
		<build-helper-maven-plugin.version>3.2.0</build-helper-maven-plugin.version>
		<artifactory-maven-plugin.version>3.2.0</artifactory-maven-plugin.version>
		<license-maven-plugin.version>2.0.0</license-maven-plugin.version>
		<spring-javaformat-checkstyle.version>0.0.26</spring-javaformat-checkstyle.version>
		<maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
		<pitest-maven.version>1.6.7</pitest-maven.version>
		<pitest-junit5-plugin.version>0.14</pitest-junit5-plugin.version>
		<disable.checks>false</disable.checks>
		<maven-checkstyle-plugin.failsOnError>true</maven-checkstyle-plugin.failsOnError>
		<maven-checkstyle-plugin.failOnViolation>true
		</maven-checkstyle-plugin.failOnViolation>
		<maven-checkstyle-plugin.includeTestSourceDirectory>true
		</maven-checkstyle-plugin.includeTestSourceDirectory>
		<spring-cloud-build-checkstyle.branch>master
		</spring-cloud-build-checkstyle.branch>
		<checkstyle.suppressions.file>
			https://raw.githubusercontent.com/spring-cloud/spring-cloud-build/${spring-cloud-build-checkstyle.branch}/spring-cloud-build-tools/src/checkstyle/checkstyle-suppressions.xml
		</checkstyle.suppressions.file>
		<checkstyle.nohttp.file>
			https://raw.githubusercontent.com/spring-cloud/spring-cloud-build/${spring-cloud-build-checkstyle.branch}/spring-cloud-build-tools/src/checkstyle/nohttp-checkstyle.xml
		</checkstyle.nohttp.file>
		<checkstyle.additional.suppressions.file>
			${maven.multiModuleProjectDirectory}/src/checkstyle/checkstyle-suppressions.xml
		</checkstyle.additional.suppressions.file>
		<nohttp-checkstyle.version>0.0.7</nohttp-checkstyle.version>
		<disable.nohttp.checks>true</disable.nohttp.checks>
		<antelopetasks.version>3.2.10</antelopetasks.version>
		<ant-nodeps.version>1.8.1</ant-nodeps.version>
		<configprops.path>${project.basedir}/src/main/asciidoc/_configprops.adoc</configprops.path>
		<configprops.inclusionPattern>.*</configprops.inclusionPattern>
		<maven-dependency-plugin-for-docs.phase>generate-resources</maven-dependency-plugin-for-docs.phase>
		<maven-dependency-plugin-for-docs-classes.phase>generate-resources</maven-dependency-plugin-for-docs-classes.phase>
		<maven-dependency-plugin-for-guides.phase>generate-resources</maven-dependency-plugin-for-guides.phase>
		<generated-docs-multipage-output.dir>${project.build.directory}/generated-docs/reference/html</generated-docs-multipage-output.dir>
		<generated-docs-singlepage-output.dir>${project.build.directory}/generated-docs/reference/htmlsingle</generated-docs-singlepage-output.dir>
		<generated-docs-pdf-output.dir>${project.build.directory}/generated-docs/reference/pdf</generated-docs-pdf-output.dir>
		<javadoc.failOnError>false</javadoc.failOnError>
		<javadoc.failOnWarnings>false</javadoc.failOnWarnings>
		<maven-deploy-plugin-default.phase>deploy</maven-deploy-plugin-default.phase>
		<maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
		<maven-deploy-plugin.deployZipRepositoryId>repo.spring.io</maven-deploy-plugin.deployZipRepositoryId>
		<maven-deploy-plugin.deployZipUrl>https://repo.spring.io/libs-snapshot-local</maven-deploy-plugin.deployZipUrl>
		<upload-docs-zip.phase>none</upload-docs-zip.phase>
		<readme.main.classpath>${docs.classes.dir}</readme.main.classpath>
		<readme.class.path>readme.class.path</readme.class.path>
		<sonar-jacoco-listeners.version>5.14.0.18788</sonar-jacoco-listeners.version>
		<duplicate-finder-maven-plugin.version>1.5.0</duplicate-finder-maven-plugin.version>
		<duplicate-finder-maven-plugin.printEqualFiles>false</duplicate-finder-maven-plugin.printEqualFiles>
		<duplicate-finder-maven-plugin.failBuildInCaseOfDifferentContentConflict>false</duplicate-finder-maven-plugin.failBuildInCaseOfDifferentContentConflict>
		<duplicate-finder-maven-plugin.failBuildInCaseOfEqualContentConflict>false</duplicate-finder-maven-plugin.failBuildInCaseOfEqualContentConflict>
		<duplicate-finder-maven-plugin.failBuildInCaseOfConflict>true</duplicate-finder-maven-plugin.failBuildInCaseOfConflict>
		<duplicate-finder-maven-plugin.checkCompileClasspath>true</duplicate-finder-maven-plugin.checkCompileClasspath>
		<duplicate-finder-maven-plugin.checkRuntimeClasspath>true</duplicate-finder-maven-plugin.checkRuntimeClasspath>
		<duplicate-finder-maven-plugin.checkTestClasspath>true</duplicate-finder-maven-plugin.checkTestClasspath>
		<duplicate-finder-maven-plugin.skip>false</duplicate-finder-maven-plugin.skip>
		<duplicate-finder-maven-plugin.quiet>false</duplicate-finder-maven-plugin.quiet>
		<duplicate-finder-maven-plugin.preferLocal>true</duplicate-finder-maven-plugin.preferLocal>
		<duplicate-finder-maven-plugin.useResultFile>true</duplicate-finder-maven-plugin.useResultFile>
		<duplicate-finder-maven-plugin.resultFileMinClasspathCount>2</duplicate-finder-maven-plugin.resultFileMinClasspathCount>
		<duplicate-finder-maven-plugin.resultFile>${project.build.directory}/duplicate-finder-result.xml</duplicate-finder-maven-plugin.resultFile>
		<duplicate-finder-maven-plugin.includeBootClasspath>false</duplicate-finder-maven-plugin.includeBootClasspath>
		<duplicate-finder-maven-plugin.bootClasspathProperty>sun.boot.class.path</duplicate-finder-maven-plugin.bootClasspathProperty>
		<duplicate-finder-maven-plugin.useDefaultResourceIgnoreList>true</duplicate-finder-maven-plugin.useDefaultResourceIgnoreList>
		<duplicate-finder-maven-plugin.includePomProjects>false</duplicate-finder-maven-plugin.includePomProjects>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-build-dependencies</artifactId>
				<version>${spring-cloud-build.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<organization>
		<name>Pivotal Software, Inc.</name>
		<url>https://www.spring.io</url>
	</organization>
	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0</url>
			<comments>
				Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.
			</comments>
		</license>
	</licenses>
	<scm>
		<url>https://github.com/spring-cloud/spring-cloud-build</url>
		<connection>scm:git:git://github.com/spring-cloud/spring-cloud-build.git
		</connection>
		<developerConnection>
			scm:git:ssh://**************/spring-cloud/spring-cloud-build.git
		</developerConnection>
		<tag>HEAD</tag>
	</scm>
	<developers>
		<developer>
			<id>dsyer</id>
			<name>Dave Syer</name>
			<email>dsyer at pivotal.io</email>
			<organization>Pivotal Software, Inc.</organization>
			<organizationUrl>https://www.spring.io</organizationUrl>
			<roles>
				<role>lead</role>
			</roles>
		</developer>
		<developer>
			<id>sgibb</id>
			<name>Spencer Gibb</name>
			<email>sgibb at pivotal.io</email>
			<organization>Pivotal Software, Inc.</organization>
			<organizationUrl>https://www.spring.io</organizationUrl>
			<roles>
				<role>lead</role>
			</roles>
		</developer>
		<developer>
			<id>mgrzejszczak</id>
			<name>Marcin Grzejszczak</name>
			<email>mgrzejszczak at pivotal.io</email>
			<organization>Pivotal Software, Inc.</organization>
			<organizationUrl>https://www.spring.io</organizationUrl>
			<roles>
				<role>developer</role>
			</roles>
		</developer>
		<developer>
			<id>rbaxter</id>
			<name>Ryan Baxter</name>
			<email>rbaxter at pivotal.io</email>
			<organization>Pivotal Software, Inc.</organization>
			<organizationUrl>https://www.spring.io</organizationUrl>
			<roles>
				<role>developer</role>
			</roles>
		</developer>
		<developer>
			<id>omaciaszeksharma</id>
			<name>Olga Maciaszek-Sharma</name>
			<email>omaciaszeksharma at pivotal.io</email>
			<organization>Pivotal Software, Inc.</organization>
			<organizationUrl>https://www.spring.io</organizationUrl>
			<roles>
				<role>developer</role>
			</roles>
		</developer>
	</developers>
	<build>
		<!-- Turn on filtering by default for application properties -->
		<resources>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>**/application*.yml</include>
					<include>**/application*.properties</include>
				</includes>
			</resource>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<excludes>
					<exclude>**/application*.yml</exclude>
					<exclude>**/application*.properties</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>${maven-javadoc-plugin.version}</version>
				<executions>
					<execution>
						<id>javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
				<configuration>
					<failOnError>${javadoc.failOnError}</failOnError>
					<failOnWarnings>${javadoc.failOnWarnings}</failOnWarnings>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<version>${maven-source-plugin.version}</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>flatten-maven-plugin</artifactId>
					<inherited>true</inherited>
					<executions>
						<execution>
							<!-- Tidy up all POMs before they are published -->
							<id>flatten</id>
							<phase>process-resources</phase>
							<goals>
								<goal>flatten</goal>
							</goals>
							<configuration>
								<updatePomFile>true</updatePomFile>
								<flattenMode>oss</flattenMode>
								<pomElements>
									<parent>expand</parent>
									<distributionManagement>remove</distributionManagement>
									<repositories>remove</repositories>
								</pomElements>
							</configuration>
						</execution>
					</executions>
				</plugin>
				<!-- Apply more sensible defaults for user projects -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-eclipse-plugin</artifactId>
					<version>${maven-eclipse-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${maven-compiler-plugin.version}</version>
					<configuration>
						<source>${maven.compiler.source}</source>
						<target>${maven.compiler.target}</target>
						<compilerArgument>-parameters</compilerArgument>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-failsafe-plugin</artifactId>
					<version>${maven-failsafe-plugin.version}</version>
					<executions>
						<execution>
							<goals>
								<goal>integration-test</goal>
								<goal>verify</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-jar-plugin</artifactId>
					<version>${maven-jar-plugin.version}</version>
					<configuration>
						<archive>
							<manifest>
								<mainClass>${start-class}</mainClass>
								<addDefaultImplementationEntries>true
								</addDefaultImplementationEntries>
							</manifest>
						</archive>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>${maven-antrun-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>${maven-surefire-plugin.version}</version>
					<configuration>
						<includes>
							<include>**/*Tests.java</include>
							<include>**/*Test.java</include>
						</includes>
						<excludes>
							<exclude>**/Abstract*.java</exclude>
						</excludes>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-war-plugin</artifactId>
					<version>${maven-war-plugin.version}</version>
					<configuration>
						<failOnMissingWebXml>false</failOnMissingWebXml>
						<archive>
							<manifest>
								<mainClass>${start-class}</mainClass>
								<addDefaultImplementationEntries>true
								</addDefaultImplementationEntries>
							</manifest>
						</archive>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>exec-maven-plugin</artifactId>
					<version>${exec-maven-plugin.version}</version>
					<configuration>
						<mainClass>${start-class}</mainClass>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-resources-plugin</artifactId>
					<version>${maven-resources-plugin.version}</version>
					<configuration>
						<delimiters>
							<delimiter>${resource.delimiter}</delimiter>
						</delimiters>
						<useDefaultDelimiters>false</useDefaultDelimiters>
					</configuration>
				</plugin>
				<plugin>
					<groupId>io.spring.javaformat</groupId>
					<artifactId>spring-javaformat-maven-plugin</artifactId>
					<version>${spring-javaformat.version}</version>
					<executions>
						<execution>
							<phase>validate</phase>
							<configuration>
								<skip>${disable.checks}</skip>
							</configuration>
							<goals>
								<goal>apply</goal>
								<goal>validate</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-checkstyle-plugin</artifactId>
					<version>${maven-checkstyle-plugin.version}</version>
					<dependencies>
						<dependency>
							<groupId>com.puppycrawl.tools</groupId>
							<artifactId>checkstyle</artifactId>
							<version>${puppycrawl-tools-checkstyle.version}</version>
						</dependency>
						<dependency>
							<groupId>io.spring.javaformat</groupId>
							<artifactId>spring-javaformat-checkstyle</artifactId>
							<version>${spring-javaformat-checkstyle.version}</version>
						</dependency>
						<dependency>
							<groupId>org.springframework.cloud</groupId>
							<artifactId>spring-cloud-build-tools</artifactId>
							<version>${spring-cloud-build.version}</version>
						</dependency>
						<dependency>
							<groupId>io.spring.nohttp</groupId>
							<artifactId>nohttp-checkstyle</artifactId>
							<version>${nohttp-checkstyle.version}</version>
						</dependency>
					</dependencies>
					<executions>
						<execution>
							<id>checkstyle-validation</id>
							<phase>validate</phase>
							<inherited>true</inherited>
							<configuration>
								<skip>${disable.checks}</skip>
								<configLocation>checkstyle.xml</configLocation>
								<headerLocation>checkstyle-header.txt</headerLocation>
								<propertyExpansion>
									checkstyle.build.directory=${project.build.directory}
									checkstyle.suppressions.file=${checkstyle.suppressions.file}
									checkstyle.additional.suppressions.file=${checkstyle.additional.suppressions.file}
								</propertyExpansion>
								<consoleOutput>true</consoleOutput>
								<includeTestSourceDirectory>
									${maven-checkstyle-plugin.includeTestSourceDirectory}
								</includeTestSourceDirectory>
								<failsOnError>${maven-checkstyle-plugin.failsOnError}
								</failsOnError>
								<failOnViolation>
									${maven-checkstyle-plugin.failOnViolation}
								</failOnViolation>
							</configuration>
							<goals>
								<goal>check</goal>
							</goals>
						</execution>
						<execution>
							<id>no-http-checkstyle-validation</id>
							<phase>validate</phase>
							<inherited>true</inherited>
							<configuration>
								<skip>${disable.nohttp.checks}</skip>
								<configLocation>${checkstyle.nohttp.file}</configLocation>
								<includes>**/*</includes>
								<excludes>**/.idea/**/*,**/.git/**/*,**/target/**/*,**/*.log</excludes>
								<sourceDirectories>./</sourceDirectories>
							</configuration>
							<goals>
								<goal>check</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>pl.project13.maven</groupId>
					<artifactId>git-commit-id-plugin</artifactId>
					<version>${git-commit-id-plugin.version}</version>
					<executions>
						<execution>
							<goals>
								<goal>revision</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<verbose>true</verbose>
						<dateFormat>yyyy-MM-dd'T'HH:mm:ssZ</dateFormat>
						<generateGitPropertiesFile>true</generateGitPropertiesFile>
						<generateGitPropertiesFilename>
							${project.build.outputDirectory}/git.properties
						</generateGitPropertiesFilename>
						<skipPoms>false</skipPoms>
						<useNativeGit>true</useNativeGit>
					</configuration>
				</plugin>
				<!-- Support our own plugin -->
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>${spring-boot.version}</version>
					<executions>
						<execution>
							<goals>
								<goal>repackage</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<mainClass>${start-class}</mainClass>
					</configuration>
				</plugin>
				<!-- Support shade packaging (if the user does not want to use our plugin) -->
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-shade-plugin</artifactId>
					<version>${maven-shade-plugin.version}</version>
					<dependencies>
						<dependency>
							<groupId>org.springframework.boot</groupId>
							<artifactId>spring-boot-maven-plugin</artifactId>
							<version>${spring-boot.version}</version>
						</dependency>
					</dependencies>
					<configuration>
						<keepDependenciesWithProvidedScope>true
						</keepDependenciesWithProvidedScope>
						<createDependencyReducedPom>true</createDependencyReducedPom>
						<filters>
							<filter>
								<artifact>*:*</artifact>
								<excludes>
									<exclude>META-INF/*.SF</exclude>
									<exclude>META-INF/*.DSA</exclude>
									<exclude>META-INF/*.RSA</exclude>
								</excludes>
							</filter>
						</filters>
					</configuration>
					<executions>
						<execution>
							<phase>package</phase>
							<goals>
								<goal>shade</goal>
							</goals>
							<configuration>
								<transformers>
									<transformer
											implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
										<resource>META-INF/spring.handlers</resource>
									</transformer>
									<transformer
											implementation="org.springframework.boot.maven.PropertiesMergingResourceTransformer">
										<resource>META-INF/spring.factories</resource>
									</transformer>
									<transformer
											implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
										<resource>META-INF/spring.schemas</resource>
									</transformer>
									<transformer
											implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
									<transformer
											implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
										<mainClass>${start-class}</mainClass>
									</transformer>
								</transformers>
							</configuration>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-enforcer-plugin</artifactId>
					<version>${maven-enforcer-plugin.version}</version>
					<executions>
						<execution>
							<id>enforce-versions</id>
							<goals>
								<goal>enforce</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
						<fail>false</fail>
						<rules>
							<dependencyConvergence/>
						</rules>
					</configuration>
				</plugin>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.apache.maven.plugins
										</groupId>
										<artifactId>
											maven-checkstyle-plugin
										</artifactId>
										<versionRange>
											[2.17,)
										</versionRange>
										<goals>
											<goal>check</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-javadoc-plugin</artifactId>
					<version>${maven-javadoc-plugin.version}</version>
					<executions>
						<execution>
							<id>aggregate</id>
							<goals>
								<goal>aggregate</goal>
							</goals>
							<phase>site</phase>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.basepom.maven</groupId>
					<artifactId>duplicate-finder-maven-plugin</artifactId>
					<version>${duplicate-finder-maven-plugin.version}</version>
					<executions>
						<execution>
							<id>duplicate-validation</id>
							<phase>verify</phase>
							<goals>
								<goal>check</goal>
							</goals>
						</execution>
					</executions>
					<configuration>
					<printEqualFiles>${duplicate-finder-maven-plugin.printEqualFiles}</printEqualFiles>
					<failBuildInCaseOfDifferentContentConflict>${duplicate-finder-maven-plugin.failBuildInCaseOfDifferentContentConflict}</failBuildInCaseOfDifferentContentConflict>
					<failBuildInCaseOfEqualContentConflict>${duplicate-finder-maven-plugin.failBuildInCaseOfEqualContentConflict}</failBuildInCaseOfEqualContentConflict>
					<failBuildInCaseOfConflict>${duplicate-finder-maven-plugin.failBuildInCaseOfConflict}</failBuildInCaseOfConflict>
					<checkCompileClasspath>${duplicate-finder-maven-plugin.checkCompileClasspath}</checkCompileClasspath>
					<checkRuntimeClasspath>${duplicate-finder-maven-plugin.checkRuntimeClasspath}</checkRuntimeClasspath>
					<checkTestClasspath>${duplicate-finder-maven-plugin.checkTestClasspath}</checkTestClasspath>
					<skip>${duplicate-finder-maven-plugin.skip}</skip>
					<quiet>${duplicate-finder-maven-plugin.quiet}</quiet>
					<preferLocal>${duplicate-finder-maven-plugin.preferLocal}</preferLocal>
					<useResultFile>${duplicate-finder-maven-plugin.useResultFile}</useResultFile>
					<resultFileMinClasspathCount>${duplicate-finder-maven-plugin.resultFileMinClasspathCount}</resultFileMinClasspathCount>
					<resultFile>${duplicate-finder-maven-plugin.resultFile}</resultFile>
					<includeBootClasspath>${duplicate-finder-maven-plugin.includeBootClasspath}</includeBootClasspath>
					<bootClasspathProperty>${duplicate-finder-maven-plugin.bootClasspathProperty}</bootClasspathProperty>
					<useDefaultResourceIgnoreList>${duplicate-finder-maven-plugin.useDefaultResourceIgnoreList}</useDefaultResourceIgnoreList>
					<includePomProjects>${duplicate-finder-maven-plugin.includePomProjects}</includePomProjects>
					</configuration>
				</plugin>

			</plugins>
		</pluginManagement>
	</build>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>${maven-javadoc-plugin.version}</version>
				<reportSets>
					<reportSet>
						<id>non-aggregate</id>
						<configuration>
						</configuration>
						<reports>
							<report>javadoc</report>
						</reports>
					</reportSet>
					<reportSet>
						<id>aggregate</id>
						<configuration>
						</configuration>
						<reports>
							<report>aggregate</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-report-plugin</artifactId>
				<version>${maven-surefire-report-plugin.version}</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jxr-plugin</artifactId>
				<version>3.0.0</version>
			</plugin>
		</plugins>
	</reporting>
	<distributionManagement>
		<downloadUrl>https://github.com/spring-cloud</downloadUrl>
		<site>
			<id>spring-docs</id>
			<url>
				scp://static.springframework.org/var/www/domains/springframework.org/static/htdocs/spring-cloud/docs/${project.artifactId}/${project.version}
			</url>
		</site>
		<repository>
			<id>sonatype-nexus-staging</id>
			<name>Nexus Release Repository</name>
			<url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
		<snapshotRepository>
			<id>repo.spring.io</id>
			<name>Spring Snapshot Repository</name>
			<url>https://repo.spring.io/libs-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/snapshot</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/milestone</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/snapshot</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/milestone</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
				<pluginRepository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
		<profile>
			<id>milestone</id>
			<distributionManagement>
				<repository>
					<id>repo.spring.io</id>
					<name>Spring Milestone Repository</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
				</repository>
			</distributionManagement>
			<properties>
				<maven-deploy-plugin.deployZipUrl>https://repo.spring.io/libs-milestone-local</maven-deploy-plugin.deployZipUrl>
			</properties>
		</profile>
		<profile>
			<id>central</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>${maven-gpg-plugin.version}</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
			<properties>
				<maven-deploy-plugin.deployZipUrl>https://repo.spring.io/libs-release-local</maven-deploy-plugin.deployZipUrl>
			</properties>
		</profile>
		<profile>
			<id>guides</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-dependency-plugin</artifactId>
						<version>${maven-dependency-plugin.version}</version>
						<executions>
							<execution>
								<id>unpack-docs</id>
								<phase>${maven-dependency-plugin-for-guides.phase}</phase>
								<goals>
									<goal>unpack</goal>
								</goals>
								<configuration>
									<artifactItems>
										<artifactItem>
											<groupId>org.springframework.cloud
											</groupId>
											<artifactId>spring-cloud-build-docs
											</artifactId>
											<version>${spring-cloud-build.version}
											</version>
											<classifier>sources</classifier>
											<type>jar</type>
											<overWrite>false</overWrite>
											<outputDirectory>${docs.resources.dir}
											</outputDirectory>
										</artifactItem>
									</artifactItems>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<artifactId>exec-maven-plugin</artifactId>
						<groupId>org.codehaus.mojo</groupId>
						<executions>
							<execution>
								<id>Run guides update</id>
								<phase>${guides-update.phase}</phase>
								<goals>
									<goal>exec</goal>
								</goals>
								<configuration>
									<skip>${skipGuides}</skip>
									<workingDirectory>${maven.multiModuleProjectDirectory}</workingDirectory>
									<executable>${docs.resources.dir}/asciidoc/update-guides.sh</executable>
									<environmentVariables>
										<PROJECT_VERSION>${guides-project.version}</PROJECT_VERSION>
									</environmentVariables>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java8</id>
			<activation>
				<jdk>[1.8,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-javadoc-plugin</artifactId>
						<configuration>
							<additionalparam>-Xdoclint:none</additionalparam>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java11</id>
			<activation>
				<jdk>[1.11,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-javadoc-plugin</artifactId>
						<configuration>
							<!-- https://bugs.java.com/bugdatabase/view_bug.do?bug_id=8212233 -->
							<source>8</source>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<!-- TODO: remove this (it's covered with checkstyle, but not all projects use that yet -->
			<id>imports</id>
			<activation>
				<os>
					<family>!windows</family>
				</os>
				<file>
					<missing>.google</missing>
				</file>
				<property>
					<name>project.packaging</name>
					<value>jar</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>exec-maven-plugin</artifactId>
						<version>${exec-maven-plugin.version}</version>
						<executions>
							<execution>
								<id>imports</id>
								<phase>process-sources</phase>
								<configuration>
									<executable>egrep</executable>
									<successCodes>
										<successCode>1</successCode>
									</successCodes>
									<arguments>
										<argument>-rm</argument>
										<argument>1</argument>
										<!-- Forbidden Keywords -->
										<argument>^import com.google</argument>
										<!-- file patterns -->
										<argument>--include=*.java</argument>
										<!-- search path -->
										<argument>${project.basedir}</argument>
									</arguments>
								</configuration>
								<goals>
									<goal>exec</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>docs</id>
			<build>
				<pluginManagement>
					<plugins>
						<plugin>
							<groupId>org.apache.maven.plugins</groupId>
							<artifactId>maven-dependency-plugin</artifactId>
							<version>${maven-dependency-plugin.version}</version>
							<executions>
								<execution>
									<id>unpack-docs</id>
									<phase>${maven-dependency-plugin-for-docs.phase}</phase>
									<goals>
										<goal>unpack</goal>
									</goals>
									<configuration>
										<artifactItems>
											<artifactItem>
												<groupId>org.springframework.cloud
												</groupId>
												<artifactId>spring-cloud-build-docs
												</artifactId>
												<version>${spring-cloud-build.version}
												</version>
												<classifier>sources</classifier>
												<type>jar</type>
												<overWrite>true</overWrite>
												<outputDirectory>${docs.resources.dir}
												</outputDirectory>
											</artifactItem>
										</artifactItems>
									</configuration>
								</execution>
								<execution>
									<id>unpack-classes</id>
									<phase>${maven-dependency-plugin-for-docs-classes.phase}</phase>
									<goals>
										<goal>unpack</goal>
									</goals>
									<configuration>
										<artifactItems>
											<artifactItem>
												<groupId>org.springframework.cloud
												</groupId>
												<artifactId>spring-cloud-build-docs
												</artifactId>
												<version>${spring-cloud-build.version}
												</version>
												<classifier>jar-with-dependencies</classifier>
												<type>jar</type>
												<overWrite>true</overWrite>
												<outputDirectory>${docs.classes.dir}
												</outputDirectory>
											</artifactItem>
										</artifactItems>
									</configuration>
								</execution>
								<execution>
									<id>unpack-docs-resources</id>
									<phase>generate-resources</phase>
									<goals>
										<goal>unpack</goal>
									</goals>
									<configuration>
										<artifactItems>
											<artifactItem>
												<groupId>io.spring.docresources</groupId>
												<artifactId>spring-doc-resources</artifactId>
												<version>${spring-doc-resources.version}</version>
												<type>zip</type>
												<overWrite>true</overWrite>
												<outputDirectory>${refdocs.build.directory}</outputDirectory>
											</artifactItem>
										</artifactItems>
									</configuration>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>org.apache.maven.plugins</groupId>
							<artifactId>maven-resources-plugin</artifactId>
							<executions>
								<execution>
									<id>copy-asciidoc-resources</id>
									<phase>generate-resources</phase>
									<goals>
										<goal>copy-resources</goal>
									</goals>
									<configuration>
										<outputDirectory>${project.build.directory}/refdocs/</outputDirectory>
										<resources>
											<resource>
												<directory>src/main/asciidoc</directory>
												<excludes>
													<exclude>ghpages.sh</exclude>
												</excludes>
											</resource>
										</resources>
									</configuration>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>org.codehaus.mojo</groupId>
							<artifactId>exec-maven-plugin</artifactId>
							<version>${exec-maven-plugin.version}</version>
							<executions>
								<execution>
									<id>generate-configprops</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>java</goal>
									</goals>
								</execution>
							</executions>
							<dependencies>
								<dependency>
									<groupId>org.springframework.cloud
									</groupId>
									<artifactId>spring-cloud-build-docs
									</artifactId>
									<version>${spring-cloud-build.version}
									</version>
									<type>jar</type>
								</dependency>
							</dependencies>
							<configuration>
								<includePluginDependencies>
									true
								</includePluginDependencies>
								<mainClass>org.springframework.cloud.internal.Main</mainClass>
								<arguments>
									<argument>${configprops.path}</argument>
									<argument>${configprops.inclusionPattern}</argument>
								</arguments>
							</configuration>
						</plugin>
						<plugin>
							<groupId>org.asciidoctor</groupId>
							<artifactId>asciidoctor-maven-plugin</artifactId>
							<version>${asciidoctor-maven-plugin.version}</version>
							<dependencies>
								<dependency>
									<groupId>io.spring.asciidoctor</groupId>
									<artifactId>spring-asciidoctor-extensions-spring-boot</artifactId>
									<version>${spring-asciidoctor-extensions-boot.version}</version>
								</dependency>
								<dependency>
									<groupId>io.spring.asciidoctor</groupId>
									<artifactId>spring-asciidoctor-extensions-block-switch</artifactId>
									<version>${spring-asciidoctor-extensions-block.version}</version>
								</dependency>
								<dependency>
									<groupId>org.asciidoctor</groupId>
									<artifactId>asciidoctorj-pdf</artifactId>
									<version>${asciidoctorj-pdf.version}</version>
								</dependency>
								<dependency>
									<groupId>org.asciidoctor</groupId>
									<artifactId>asciidoctorj-diagram</artifactId>
									<version>${asciidoctorj-diagram.version}</version>
								</dependency>
							</dependencies>
							<configuration>
								<sourceDirectory>${refdocs.build.directory}</sourceDirectory>
								<resources>
									<resource>
										<directory>${refdocs.build.directory}</directory>
										<excludes>
											<exclude>**/*.*adoc</exclude>
										</excludes>
									</resource>
								</resources>
								<!-- tag::attributes[] -->
								<attributes>
									<docinfo>shared</docinfo>
									<allow-uri-read>true</allow-uri-read>
									<nofooter/>
									<toc>left</toc>
									<toc-levels>4</toc-levels>
									<sectlinks>true</sectlinks>
									<sources-root>${project.basedir}/src@</sources-root>
									<asciidoc-sources-root>${project.basedir}/src/main/asciidoc@</asciidoc-sources-root>
									<generated-resources-root>${project.basedir}/target/generated-resources@
									</generated-resources-root>
									<!-- Use this attribute the reference code from another module -->
									<!-- Note the @ at the end, lowering the precedence of the attribute -->
									<project-root>${maven.multiModuleProjectDirectory}@</project-root>
									<!-- It's mandatory for you to pass the docs.main property -->
									<github-repo>${docs.main}@</github-repo>
									<github-project>https://github.com/spring-cloud/${docs.main}@</github-project>
									<github-raw>
										https://raw.githubusercontent.com/spring-cloud/${docs.main}/${github-tag}@
									</github-raw>
									<github-code>https://github.com/spring-cloud/${docs.main}/tree/${github-tag}@
									</github-code>
									<github-issues>https://github.com/spring-cloud/${docs.main}/issues/@</github-issues>
									<github-wiki>https://github.com/spring-cloud/${docs.main}/wiki@</github-wiki>
									<github-master-code>https://github.com/spring-cloud/${docs.main}/tree/master@
									</github-master-code>
									<index-link>${index-link}@</index-link>

									<!-- Spring Cloud specific -->
									<!-- for backward compatibility -->
									<spring-cloud-version>${project.version}@</spring-cloud-version>
									<project-version>${project.version}@</project-version>
									<github-tag>${github-tag}@</github-tag>
									<version-type>${version-type}@</version-type>
									<docs-url>https://docs.spring.io/${docs.main}/docs/${project.version}@</docs-url>
									<raw-docs-url>${github-raw}@</raw-docs-url>
									<project-version>${project.version}@</project-version>
									<project-name>${docs.main}@</project-name>
									<source-highlighter>highlight.js</source-highlighter>
								</attributes>
								<!-- end::attributes[] -->
								<requires>
									<require>asciidoctor-diagram</require>
								</requires>
							</configuration>
							<executions>
								<execution>
									<id>generate-html-documentation</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>process-asciidoc</goal>
									</goals>
									<configuration>
										<backend>html5</backend>
										<sourceDocumentExtensions>
											<sourceDocumentExtension>.adoc</sourceDocumentExtension>
											<sourceDocumentExtension>.htmladoc</sourceDocumentExtension>
										</sourceDocumentExtensions>
										<outputDirectory>${generated-docs-multipage-output.dir}
										</outputDirectory>
										<doctype>book</doctype>
										<attributes>
											<docinfo>shared</docinfo>
											<stylesdir>css/</stylesdir>
											<stylesheet>spring.css</stylesheet>
											<linkcss>true</linkcss>
											<icons>font</icons>
											<highlightjsdir>js/highlight</highlightjsdir>
											<highlightjs-theme>github</highlightjs-theme>
											<imagesdir>./images</imagesdir>
											<attribute-missing>warn</attribute-missing>
											<source-highlighter>highlight.js</source-highlighter>
										</attributes>
										<logHandler>
											<outputToConsole>true</outputToConsole>
											<failIf>
												<!--												<severity>DEBUG</severity>-->
											</failIf>
										</logHandler>
									</configuration>
								</execution>
								<execution>
									<id>generate-htmlsingle-documentation</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>process-asciidoc</goal>
									</goals>
									<configuration>
										<backend>html5</backend>
										<sourceDocumentExtensions>
											<sourceDocumentExtension>.htmlsingleadoc</sourceDocumentExtension>
										</sourceDocumentExtensions>
										<outputDirectory>
											${generated-docs-singlepage-output.dir}
										</outputDirectory>
										<doctype>book</doctype>
										<attributes>
											<source-highlighter>highlight.js</source-highlighter>
											<docinfo>shared</docinfo>
											<stylesdir>css/</stylesdir>
											<stylesheet>spring.css</stylesheet>
											<linkcss>true</linkcss>
											<icons>font</icons>
											<highlightjsdir>js/highlight</highlightjsdir>
											<highlightjs-theme>github</highlightjs-theme>
											<imagesdir>./images</imagesdir>
										</attributes>
									</configuration>
								</execution>
								<execution>
									<id>generate-pdf-documentation</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>process-asciidoc</goal>
									</goals>
									<configuration>
										<backend>pdf</backend>
										<sourceDocumentExtensions>
											<sourceDocumentExtension>.pdfadoc</sourceDocumentExtension>
										</sourceDocumentExtensions>
										<outputDirectory>${generated-docs-pdf-output.dir}
										</outputDirectory>
										<resources>
											<resource>
												<directory>${refdocs.build.directory}</directory>
												<excludes>
													<exclude>**/*</exclude>
												</excludes>
											</resource>
										</resources>
									</configuration>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>org.apache.maven.plugins</groupId>
							<artifactId>maven-antrun-plugin</artifactId>
							<version>${maven-antrun-plugin.version}</version>
							<dependencies>
								<dependency>
									<groupId>ant-contrib</groupId>
									<artifactId>ant-contrib</artifactId>
									<version>1.0b3</version>
									<exclusions>
										<exclusion>
											<groupId>ant</groupId>
											<artifactId>ant</artifactId>
										</exclusion>
									</exclusions>
								</dependency>
								<dependency>
									<groupId>org.apache.ant</groupId>
									<artifactId>ant-nodeps</artifactId>
									<version>${ant-nodeps.version}</version>
								</dependency>
								<dependency>
									<groupId>org.tigris.antelope</groupId>
									<artifactId>antelopetasks</artifactId>
									<version>${antelopetasks.version}</version>
								</dependency>
								<dependency>
									<groupId>org.jruby</groupId>
									<artifactId>jruby-complete</artifactId>
									<version>${jruby-complete.version}</version>
								</dependency>
								<dependency>
									<groupId>org.asciidoctor</groupId>
									<artifactId>asciidoctorj</artifactId>
									<version>${asciidoctorj.version}</version>
								</dependency>
							</dependencies>
							<executions>
								<execution>
									<id>readme</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>run</goal>
									</goals>
									<configuration>
										<target>
											<path id="readme.class.path">
												<pathelement location="${readme.main.classpath}"/>
											</path>
											<java classname="org.springframework.cloud.internal.asciidoctor.ReadmeMain"
												  failonerror="yes" classpathref="${readme.class.path}">
												<!-- Input -->
												<arg value="${project.basedir}/src/main/asciidoc/README.adoc"/>
												<!-- Output -->
												<arg value="${main.basedir}/README.adoc"/>
											</java>
										</target>
									</configuration>
								</execution>
								<execution>
									<id>assert-no-unresolved-links</id>
									<phase>prepare-package</phase>
									<goals>
										<goal>run</goal>
									</goals>
									<configuration>
										<target>
											<fileset id="unresolved.file" dir="${basedir}/target/generated-docs/"
													 includes="**/*.html">
												<contains text="Unresolved"/>
											</fileset>
											<fail message="[Unresolved] Found...failing">
												<condition>
													<resourcecount when="greater"
																   count="0"
																   refid="unresolved.file"/>
												</condition>
											</fail>
										</target>
									</configuration>
								</execution>
								<execution>
									<id>package-and-attach-docs-zip</id>
									<phase>package</phase>
									<goals>
										<goal>run</goal>
									</goals>
									<configuration>
										<target>
											<zip
													destfile="${project.build.directory}/${project.artifactId}-${revision}.zip">
												<!--<zipfileset
														src="${project.build.directory}/${project.artifactId}-${revision}-javadoc.jar"
														prefix="api" />-->
												<!--												<fileset dir="${project.build.directory}/contents"/>-->
												<fileset dir="${project.build.directory}/generated-docs"/>
											</zip>
										</target>
									</configuration>
								</execution>
								<execution>
									<id>setup-maven-properties</id>
									<phase>initialize</phase>
									<goals>
										<goal>run</goal>
									</goals>
									<configuration>
										<exportAntProperties>true</exportAntProperties>
										<target>
											<taskdef resource="net/sf/antcontrib/antcontrib.properties"/>
											<taskdef name="stringutil" classname="ise.antelope.tasks.StringUtilTask"/>
											<var name="version-type" value="${revision}"/>
											<propertyregex property="version-type" override="true"
														   input="${version-type}" regexp=".*\.(.*)" replace="\1"/>
											<propertyregex property="version-type" override="true"
														   input="${version-type}" regexp="(M)\d+" replace="MILESTONE"/>
											<propertyregex property="version-type" override="true"
														   input="${version-type}" regexp="(RC)\d+"
														   replace="MILESTONE"/>
											<propertyregex property="version-type" override="true"
														   input="${version-type}" regexp="BUILD-(.*)"
														   replace="SNAPSHOT"/>
											<stringutil string="${version-type}" property="spring-cloud-repo">
												<lowercase/>
											</stringutil>
											<propertyregex property="git.branch" override="true"
														   input="${git.branch}" regexp="origin/(.*)"
														   replace="\1"/>
											<var name="github-tag" value="${git.branch}"/>
										</target>
									</configuration>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<groupId>org.jfrog.buildinfo</groupId>
							<artifactId>artifactory-maven-plugin</artifactId>
							<version>${artifactory-maven-plugin.version}</version>
							<executions>
								<execution>
									<id>build-info</id>
									<goals>
										<goal>publish</goal>
									</goals>
									<configuration>
										<deployProperties>
											<zip.deployed>true</zip.deployed>
											<zip.type>docs</zip.type>
										</deployProperties>
										<publisher>
											<contextUrl>https://repo.spring.io
											</contextUrl>
											<repoKey>libs-release-local</repoKey>
											<snapshotRepoKey>libs-snapshots-local
											</snapshotRepoKey>
										</publisher>
									</configuration>
								</execution>
							</executions>
						</plugin>
						<plugin>
							<artifactId>maven-deploy-plugin</artifactId>
							<version>${maven-deploy-plugin.version}</version>
							<executions>
								<execution>
									<id>default-deploy</id>
									<phase>${maven-deploy-plugin-default.phase}</phase>
									<goals>
										<goal>deploy</goal>
									</goals>
								</execution>
								<execution>
									<id>upload-docs-zip</id>
									<phase>${upload-docs-zip.phase}</phase>
									<goals>
										<goal>deploy-file</goal>
									</goals>
									<configuration>
										<generatePom>false</generatePom>
										<groupId>${project.groupId}</groupId>
										<artifactId>${project.artifactId}</artifactId>
										<version>${project.version}</version>
										<repositoryId>${maven-deploy-plugin.deployZipRepositoryId}</repositoryId>
										<url>${maven-deploy-plugin.deployZipUrl}</url>
										<file>${project.build.directory}/${project.artifactId}-${project.version}.zip</file>
										<packaging>zip;zip.type=docs;zip.deployed=false;</packaging>
									</configuration>
								</execution>
							</executions>
						</plugin>
					</plugins>
				</pluginManagement>
			</build>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/snapshot</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
					<releases>
						<enabled>false</enabled>
					</releases>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/milestone</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
				<pluginRepository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
		<profile>
			<id>sonar</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.8.5</version>
						<configuration>
							<destFile>${sonar.jacoco.reportPath}</destFile>
							<append>true</append>
						</configuration>
						<executions>
							<execution>
								<id>agent</id>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<version>${maven-surefire-plugin.version}</version>
						<configuration>
							<properties>
								<property>
									<name>listener</name>
									<value>org.sonar.java.jacoco.JUnitListener</value>
								</property>
							</properties>
						</configuration>
					</plugin>
				</plugins>
			</build>
			<dependencies>
				<dependency>
					<groupId>org.sonarsource.java</groupId>
					<artifactId>sonar-jacoco-listeners</artifactId>
					<version>${sonar-jacoco-listeners.version}</version>
					<scope>test</scope>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>license</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>license-maven-plugin</artifactId>
						<version>${license-maven-plugin.version}</version>
						<executions>
							<execution>
								<id>aggregate-licenses</id>
								<goals>
									<goal>license:aggregate-add-third-party</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>fast</id>
			<properties>
				<checkstyle.skip>true</checkstyle.skip>
			</properties>
		</profile>
		<profile>
			<id>mutation</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.pitest</groupId>
						<artifactId>pitest-maven</artifactId>
						<version>${pitest-maven.version}</version>
						<configuration>
							<!-- generate line coverage -->
							<exportLineCoverage>true</exportLineCoverage>
							<!-- in the "right" format -->
							<outputFormats><value>XML</value></outputFormats>
							<!-- and write reports where report-aggregate-module expects to find it -->
							<timestampedReports>false</timestampedReports>
							<!-- There are modules that don't have tests -->
							<failWhenNoMutations>false</failWhenNoMutations>
						</configuration>
						<executions>
							<execution>
								<id>mutationCoverage</id>
								<goals>
									<goal>mutationCoverage</goal>
								</goals>
							</execution>
						</executions>
						<dependencies>
							<dependency>
								<groupId>org.pitest</groupId>
								<artifactId>pitest-junit5-plugin</artifactId>
								<version>${pitest-junit5-plugin.version}</version>
							</dependency>
						</dependencies>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
