package com.chinasie.orion.domain.dto.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectGoodsStatisticsDTO对象", description = "项目物资统计")
public class ProjectGoodsStatisticsDTO {
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "物资类型")
    private String goodsType;
    @ApiModelProperty(value = "负责人id")
    private String rspUser;
    @ApiModelProperty(value = "时间类型")
    private String timeType;
    @ApiModelProperty(value = "新增时间")
    private Date createTime;
    @ApiModelProperty(value = "状态")
    private Integer status;
}
