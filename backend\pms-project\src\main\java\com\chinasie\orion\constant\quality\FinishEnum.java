package com.chinasie.orion.constant.quality;

import java.util.Objects;

public enum FinishEnum {
    OK(1, "已完成"),
    //直管领导审核中
    NO(0, "未完成"),
    ;

    private Integer code;
    private String name;


    FinishEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }


    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }


    private static FinishEnum getEnumByCode(Integer code) {
        for (FinishEnum performanceAppraiseStatusEnum : FinishEnum.values()) {
            if (Objects.equals(performanceAppraiseStatusEnum.getCode(), code)) {
                return performanceAppraiseStatusEnum;
            }
        }
        return null;
    }
}
