<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    :onMenuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template #header-right>
      <BasicTableAction
        v-if="projectInfo"
        :record="projectInfo"
        :actions="actions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="projectInfo.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <!--  概述  -->
    <DetailsTab
      v-if="actionId===9991"
      :details="projectInfo"
      :onUpdateDetail="change"
      @change="change"
    />

    <!--关联计划-->
    <ContactPlan
      v-if="actionId===999201"
      :id="projectInfo.id"
    />

    <!--关联风险-->
    <ContactRiskTab
      v-if="actionId===999202"
      :id="projectInfo.id"
    />

    <!--关联文档-->
    <RiskContactDoc
      v-if="actionId===999203"
      :id="projectInfo.id"
    />
    <!--关联变更-->
    <ContactAssociated
      v-if="actionId===999204"
      :id="projectInfo.id"
      from="question"
    />
    <!--    <Process-->
    <!--      v-if="actionId===9993"-->
    <!--    />-->
    <WorkflowView
      v-if="actionId===9993"
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
    <RelatedObjects
      v-if="actionId===9994"
      :relatedType="projectInfo.questionType"
    />

    <!--编辑-->
    <AddTableNode
      :tree-data="treeData"
      @register="register"
      @update="change"
    />
    <!--同意-->
    <ResolvedDrawer @register="ResolvedDrawerRegister" />
  </Layout3>
</template>

<script lang="ts">
import {
  computed,
  ComputedRef,
  defineComponent,
  getCurrentInstance,
  onMounted,
  provide,
  reactive,
  readonly,
  Ref,
  ref,
  toRefs,
  watchEffect,
} from 'vue';
import {
  BasicTableAction, isPower, ITableActionItem, Layout3, useDrawer,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import DetailsTab from './DetailsTab/index.vue';
import Api from '/@/api';
import { RelatedObjects } from '../../RelatedObjects';
import AddTableNode from '../model/AddTableNode.vue';
import { ResolvedDrawer } from './DetailsTab/drawer';
import { StatusEnum } from './DetailsTab/enum';
import RiskContactDoc from './contactTab/ContactDoc/index.vue';
import ContactPlan from './contactTab/contactPlan/index.vue';
import ContactRiskTab from './contactTab/contactRiskTab/index.vue';
import { setTitleByRootTabsKey } from '/@/utils';
import { renderNotAuthPage } from '/@/views/pms/utils';
import ContactAssociated from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/components/associatedChange/contactAssociated.vue';
export default defineComponent({
  components: {
    ContactAssociated,
    ResolvedDrawer,
    AddTableNode,
    BasicTableAction,
    Layout3,
    DetailsTab,
    RelatedObjects,
    RiskContactDoc,
    ContactPlan,
    ContactRiskTab,
    WorkflowView,
    WorkflowAction,
  },
  setup() {
    const route = useRoute();
    const [register, { openDrawer }] = useDrawer();
    const [ResolvedDrawerRegister, resolvedDrawerMethods] = useDrawer();
    const processRef: Ref = ref();
    const processViewRef: Ref = ref();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.itemId,
      projectId: '',
      projectInfo: {} as any,
      className: '',
      actionId: null,
      treeData: [],
    });

    const powerData: Ref<any[]> = ref(undefined);
    provide('powerData', powerData);

    const workflowProps: ComputedRef<WorkflowProps> = computed(() => ({
      Api,
      businessData: state.projectInfo,
      afterEvent: async () => {
        await getDetails();
        processViewRef.value?.init();
      },
    }));

    const tabsOption = computed(() => [
      isPower('PAS_WTGLXQ_container_01', powerData.value) ? {
        name: '概述',
        id: 9991,
      } : undefined,
      isPower('PAS_WTGLXQ_container_02', powerData.value) && (isPower('PAS_WTGLXQ_container_02_01', powerData.value) || isPower('PAS_WTGLXQ_container_02_02', powerData.value) || isPower('PAS_WTGLXQ_container_02_03', powerData.value)) ? {
        name: '关联内容',
        id: 9992,
        children: [
          isPower('PAS_WTGLXQ_container_02_01', powerData.value) ? {
            name: '关联计划',
            id: 999201,
          } : undefined,
          isPower('PAS_WTGLXQ_container_02_02', powerData.value) ? {
            name: '关联风险',
            id: 999202,
          } : undefined,
          {
            name: '关联变更',
            id: 999204,
          },
          isPower('PAS_WTGLXQ_container_02_03', powerData.value) ? {
            name: '关联文档',
            id: 999203,
          } : undefined,
        ],
      } : undefined,
      isPower('PAS_WTGLXQ_container_03', powerData.value) ? {
        name: '流程',
        id: 9993,
      } : undefined,
    ].filter((item) => item));

    watchEffect(() => {
      if (!state.actionId) {
        state.actionId = tabsOption.value[0]?.id;
      }
    });

    const currentInstance = getCurrentInstance();
    async function getDetails() {
      await new Api(`/pms/question-management/detail/${state.id}`).fetch({
        pageCode: 'PMSQuestionManagementDetails',
      }, '', 'GET').then((res: Record<string, any>) => {
        powerData.value = res?.detailAuthList || [];
        renderNotAuthPage({
          vm: currentInstance,
          powerData: powerData.value,
        });
        state.projectInfo = res;
        state.projectId = res.projectId;
        setTitleByRootTabsKey(route?.query?.rootTabsKey as string, res.name);
        processRef.value?.setProps({
          businessData: res,
        });
      });
    }

    provide(
      'formData',
      computed(() => state.projectInfo),
    );

    provide(
      'getFormData',
      getDetails,
    );
    onMounted(async () => {
      await getDetails();
      state.treeData = await new Api('/pas/question-dir/tree').fetch('', '', 'GET');
    });

    async function contentTabsChange2(index) {
      state.actionId = index.id;
    }

    /* 问题单条qusetionItemId和项目projectId */
    const qusetionItemId = ref(state.id);
    const projectId = ref(state.projectId);
    provide('qusetionItemId', readonly(qusetionItemId));
    provide('projectId', computed(() => state.projectId));
    provide(
      'projectInfo',
      computed(() => state.projectInfo),
    );

    function getDetailsx() {
      return state.projectInfo;
    }

    provide('getForm', getDetails);
    provide('getDetails', getDetailsx);

    function change() {
      getDetails();
    }

    const actions: Ref<ITableActionItem<any>[]> = computed(() => [
      {
        text: '添加流程',
        icon: 'sie-icon-tianjiaxinzeng',
        isShow: () => processRef.value?.isAdd && isPower('PAS_WTGLXQ_container_04_button_02', powerData.value),
        onClick() {
          processRef.value?.onAddTemplate({
            messageUrl: route.fullPath,
          });
        },
      },
      {
        text: '编辑',
        icon: 'sie-icon-bianji',
        isShow: () => isPower('PAS_WTGLXQ_container_04_button_01', powerData.value),
        onClick() {
          openDrawer(true, {
            type: 'edit',
            data: { id: route.query.itemId },
          });
        },
      },
      {
        text: '解决',
        icon: 'sie-icon-qiyong',
        isShow: () => isPower('PAS_WTGLXQ_container_04_button_03', powerData.value),
        onClick(record: any) {
          resolvedDrawerMethods.openDrawer(true, {
            questionId: record?.id,
            editStatus: StatusEnum.COMPLETED.status,
            title: '解决',
            onSuccess() {
              change();
            },
          });
        },
      },
      {
        text: '激活',
        isShow: () => isPower('PAS_WTGLXQ_container_04_button_04', powerData.value),
        icon: 'orion-icon-send',
        onClick(record: any) {
          resolvedDrawerMethods.openDrawer(true, {
            questionId: record?.id,
            editStatus: StatusEnum.INCOMPLETE.status,
            title: '激活',
            onSuccess() {
              change();
            },
          });
        },
      },
      {
        text: '关闭',
        isShow: () => isPower('PAS_WTGLXQ_container_04_button_05', powerData.value),
        icon: 'orion-icon-close-circle',
        onClick(record: any) {
          resolvedDrawerMethods.openDrawer(true, {
            questionId: record?.id,
            editStatus: StatusEnum.CLOSE.status,
            title: '关闭',
            onSuccess() {
              change();
            },
          });
        },
      },
    ].filter((item) => item));

    return {
      ...toRefs(state),
      contentTabsChange2,
      change,
      register,
      ResolvedDrawerRegister,
      actions,
      processRef,
      processViewRef,
      workflowProps,
      tabsOption,
    };
  },
});
</script>
<style lang="less" scoped>
.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;

  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
