<script setup lang="ts">
import {
  BasicButton, BasicTableAction, IOrionTableActionItem, Layout, OrionTable, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { openMaterialEnterForm } from '../utils';
import { useMaterialPageConfig } from './hooks';

const route = useRoute();
const router = useRouter();
const routeName = route.name as string;
const powerCodePrefix: Ref = inject('powerCodePrefix');
const pageConfig = useMaterialPageConfig(routeName);
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  columns: [
    {
      title: '资产存放地',
      dataIndex: 'storagePlaceName',
    },
    {
      title: '资产类型',
      dataIndex: 'assetTypeName',
    },
    {
      title: '资产代码',
      dataIndex: 'assetCode',
    },
    {
      title: '资产编码/条码',
      dataIndex: 'number',
      customRender({ text, record }) {
        if (isPower(`${powerCodePrefix.value}_container_03_02_button_01`, record?.rdAuthList)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
    },
    {
      title: '数量',
      dataIndex: 'demandNum',
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      dataIndex: 'specificationModel',
    },
    {
      title: '是否需要检定',
      dataIndex: 'isVerification',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '下次检定日期',
      dataIndex: 'nextVerificationDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '物资所在基地',
      dataIndex: 'baseName',
    },
    {
      title: '入库基地时间',
      dataIndex: 'actInDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-material').fetch({
    ...params,
    query: {
      jobId: detailsData?.id,
      repairRound: detailsData?.repairRound,
    },
    power: {
      containerCode: route.name,
      pageCode: `${powerCodePrefix.value}_container_03_01`,
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '选择物资',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: `${powerCodePrefix.value}_container_03_01_button_01`,
  },
  {
    event: 'batchDelete',
    text: '移除',
    icon: 'sie-icon-shanchu',
    powerCode: `${powerCodePrefix.value}_container_03_01_button_02`,
    disabled: selectedRows.value.length === 0,
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openMaterialEnterForm({ jobId: detailsData?.id }, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower(`${powerCodePrefix.value}_container_03_02_button_02`, record?.rdAuthList),
  },
  {
    text: '移除',
    event: 'delete',
    isShow: (record) => isPower(`${powerCodePrefix.value}_container_03_02_button_03`, record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openMaterialEnterForm(record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: pageConfig.detailRouteName,
    params: {
      id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-material').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
</script>

<template>
  <Layout
    v-get-power="{powerData}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.code]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
