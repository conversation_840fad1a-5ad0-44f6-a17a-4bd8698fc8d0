package com.chinasie.orion.service.impl.assetApply;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailWbsDTO;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailWbs;
import com.chinasie.orion.domain.vo.ProjectInitiationAPIWBS;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailWbsVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.assetApply.ProjectAssetApplyDetailWbsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectInitiationApiService;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailWbsService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;




/**
 * <p>
 * ProjectAssetApplyDetailWbs 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:30:30
 */
@Service
@Slf4j
public class ProjectAssetApplyDetailWbsServiceImpl extends  OrionBaseServiceImpl<ProjectAssetApplyDetailWbsMapper, ProjectAssetApplyDetailWbs>   implements ProjectAssetApplyDetailWbsService {


    @Resource
    private ProjectInitiationApiService projectInitiationApiService;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


//    /**
//     *  详情
//     *
//     * * @param id
//     */
//    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
//    public ProjectAssetApplyDetailWbsVO detail(String id, String pageCode) throws Exception {
//        ProjectAssetApplyDetailWbs projectAssetApplyDetailWbs =this.getById(id);
//        ProjectAssetApplyDetailWbsVO result = BeanCopyUtils.convertTo(projectAssetApplyDetailWbs,ProjectAssetApplyDetailWbsVO::new);
//        setEveryName(Collections.singletonList(result));
//
//
//        return result;
//    }

    /**
     *  新增
     *
     * * @param projectAssetApplyDetailWbsDTO
     */
    @Override
    public  String create(ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO) throws Exception {
        List<String> wbsIds = projectAssetApplyDetailWbsDTO.getWbsIds();
        LambdaQueryWrapperX<ProjectAssetApplyDetailWbs> wrapperX = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailWbs.class);
        wrapperX.eq(ProjectAssetApplyDetailWbs::getAssetApplyId, projectAssetApplyDetailWbsDTO.getAssetApplyId());
        wrapperX.in(ProjectAssetApplyDetailWbs::getWbsId, projectAssetApplyDetailWbsDTO.getWbsIds());
        List<ProjectAssetApplyDetailWbs> oldList = this.list(wrapperX);
        if(!CollectionUtil.isEmpty(oldList)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }
        List<ProjectAssetApplyDetailWbs> list = new ArrayList<>();
        for (String wbsId : wbsIds) {
            ProjectAssetApplyDetailWbs projectAssetApplyDetailWbs = new ProjectAssetApplyDetailWbs();
            projectAssetApplyDetailWbs.setAssetApplyId(projectAssetApplyDetailWbsDTO.getAssetApplyId());
            projectAssetApplyDetailWbs.setWbsId(wbsId);
            list.add(projectAssetApplyDetailWbs);
        }
        this.saveBatch(list);



        return "操作成功";
    }

//    /**
//     *  编辑
//     *
//     * * @param projectAssetApplyDetailWbsDTO
//     */
//    @Override
//    public Boolean edit(ProjectAssetApplyDetailWbsDTO projectAssetApplyDetailWbsDTO) throws Exception {
//        ProjectAssetApplyDetailWbs projectAssetApplyDetailWbs =BeanCopyUtils.convertTo(projectAssetApplyDetailWbsDTO,ProjectAssetApplyDetailWbs::new);
//
//        this.updateById(projectAssetApplyDetailWbs);
//
//        String rsp=projectAssetApplyDetailWbs.getId();
//
//
//
//        return true;
//    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectAssetApplyDetailWbsVO> pages( Page<ProjectAssetApplyDetailWbsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectAssetApplyDetailWbs> condition = new LambdaQueryWrapperX<>( ProjectAssetApplyDetailWbs. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectAssetApplyDetailWbs::getCreateTime);


        Page<ProjectAssetApplyDetailWbs> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectAssetApplyDetailWbs::new));

        PageResult<ProjectAssetApplyDetailWbs> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectAssetApplyDetailWbsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectAssetApplyDetailWbsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectAssetApplyDetailWbsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<ProjectAssetApplyDetailWbsVO> getPages(Page<ProjectAssetApplyDetailWbsDTO> pageRequest) throws Exception {
        Page<ProjectAssetApplyDetailWbsVO> pageResult = new Page<>();
        String projectId = pageRequest.getQuery().getProjectId();
        Page<ProjectInitiationAPIWBS> pages = projectInitiationApiService.getProjectInitiationAPIWBSByProjectId(projectId, pageRequest.getPageNum(), pageRequest.getPageSize());
        if(pages == null){
            return null;
        }
        BeanUtils.copyProperties(pages,pageResult);
        return pageResult;
    }

    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


//    @Override
//    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
//
//        String fileName = "资产转固申请详情表-WBS导入模板.xlsx";
//        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDetailWbsDTO.class, new ArrayList<>());
//
//    }
//
//
//    @Override
//    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {
//
//        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
//        InputStream inputStream = excel.getInputStream();
//        ProjectAssetApplyDetailWbsExcelListener excelReadListener = new ProjectAssetApplyDetailWbsExcelListener();
//        EasyExcel.read(inputStream,ProjectAssetApplyDetailWbsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
//        List<ProjectAssetApplyDetailWbsDTO> dtoS = excelReadListener.getData();
//        if (CollectionUtils.isEmpty(dtoS)) {
//            result.setCode(400);
//            result.setOom("数据不存在或导入模板表头解析错误，请检查");
//            return result;
//        }
//        if (dtoS.size() > 1000) {
//            result.setCode(400);
//            result.setOom("数据量超限");
//            return result;
//        }
//        log.info("资产转固申请详情表-WBS导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
//        List<ProjectAssetApplyDetailWbs> projectAssetApplyDetailWbses =BeanCopyUtils.convertListTo(dtoS,ProjectAssetApplyDetailWbs::new);
//
//        String importId = IdUtil.fastSimpleUUID();
//        orionJ2CacheService.set("pmsx::ProjectAssetApplyDetailWbs-import::id", importId, projectAssetApplyDetailWbses, 24 * 60 * 60);
//        result.setCode(200);
//        result.setSucc(importId);
//
//        return result;
//    }
//
//
//    @Override
//    public Boolean importByExcel(String importId) throws Exception {
//        List<ProjectAssetApplyDetailWbs> projectAssetApplyDetailWbses = (List<ProjectAssetApplyDetailWbs>) orionJ2CacheService.get("pmsx::ProjectAssetApplyDetailWbs-import::id", importId);
//        log.info("资产转固申请详情表-WBS导入的入库数据={}", JSONUtil.toJsonStr(projectAssetApplyDetailWbses));
//
//        this.saveBatch(projectAssetApplyDetailWbses);
//        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailWbs-import::id", importId);
//        return true;
//    }
//
//
//    @Override
//    public Boolean importCancelByExcel(String importId) throws Exception {
//        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailWbs-import::id", importId);
//        return true;
//    }
//
//
//    @Override
//    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
//        LambdaQueryWrapperX<ProjectAssetApplyDetailWbs> condition = new LambdaQueryWrapperX<>( ProjectAssetApplyDetailWbs. class);
//        if (!CollectionUtils.isEmpty(searchConditions)) {
//            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
//        }
//        condition.orderByDesc(ProjectAssetApplyDetailWbs::getCreateTime);
//        List<ProjectAssetApplyDetailWbs> projectAssetApplyDetailWbses =   this.list(condition);
//
//        List<ProjectAssetApplyDetailWbsDTO> dtos = BeanCopyUtils.convertListTo(projectAssetApplyDetailWbses, ProjectAssetApplyDetailWbsDTO::new);
//
//        String fileName = "资产转固申请详情表-WBS数据导出.xlsx";
//        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDetailWbsDTO.class,dtos );
//
//    }

    @Override
    public void  setEveryName(List<ProjectAssetApplyDetailWbsVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectAssetApplyDetailWbsExcelListener extends AnalysisEventListener<ProjectAssetApplyDetailWbsDTO> {

        private final List<ProjectAssetApplyDetailWbsDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectAssetApplyDetailWbsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectAssetApplyDetailWbsDTO> getData() {
            return data;
        }
    }


}

