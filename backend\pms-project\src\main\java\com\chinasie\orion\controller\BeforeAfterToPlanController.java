//package com.chinasie.orion.controller;
//
//import com.chinasie.orion.domain.dto.BeforeAfterParamDto;
//import com.chinasie.orion.domain.dto.BeforeAfterToPlanSimpleDto;
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.dto.plan.BeforeParamDto;
//import com.chinasie.orion.domain.vo.BeforeAndAfterPlanVo;
//import com.chinasie.orion.domain.vo.PlanSimpleVo;
//import com.chinasie.orion.domain.vo.SimpleVo;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.service.BeforeAfterToPlanService;
//import io.swagger.annotations.*;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/18/14:31
// * @description:
// */
//@RestController
//@RequestMapping("/before-after-to-plan")
//@Api(tags = "前后置关系对于计划")
//public class BeforeAfterToPlanController {
//
//    @Resource
//    private BeforeAfterToPlanService beforeAfterToPlanService;
//
//
//    @ApiOperation("新增前置或者后置关系")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "beforeAfterToPlan", dataType = "BeforeAfterToPlanSimpleDto")
//    })
//    @PostMapping(value = "")
//    public ResponseDTO<Boolean> savePlan( @RequestBody BeforeAfterToPlanSimpleDto beforeAfterToPlan) throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.saveByEntity(beforeAfterToPlan));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("移除前后置关系")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "uid", dataType = "String"),
//            @ApiImplicitParam(name = "type", dataType = "Integer"),
//            @ApiImplicitParam(name = "sourceId", dataType = "String")
//    })
//    @DeleteMapping(value = "/{id}/{sourceId}/{type}")
//    public ResponseDTO<String> delPlanToPlant(@ApiParam("数据Id") @PathVariable("id") String id,
//                                        @ApiParam("前后置类型 1-前置 2-后置") @PathVariable("type") Integer type
//                                        ,@ApiParam("所属计划ID") @PathVariable("sourceId") String sourceId) throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.delByIdAndTypeAndFormId(id,type,sourceId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("批量移除前后置关系")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "beforeAfterParamDto", dataType = "BeforeAfterParamDto")
//    })
//    @DeleteMapping(value = "/batch")
//    public ResponseDTO<Boolean> deleteByParam( @RequestBody BeforeAfterParamDto beforeAfterParamDto) throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.deleteByParam(beforeAfterParamDto));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取依赖类型列表")
//    @GetMapping(value = "/type/relation/list")
//    public ResponseDTO<List<SimpleVo>> getTypeList() throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.getDictList());
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//
//    @ApiOperation("获取前后置关联内容")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planParam", dataType = "PlanParam")
//    })
//    @PostMapping(value = "/list")
//    public ResponseDTO<List<BeforeAndAfterPlanVo>> list( @RequestBody PlanParam planParam) throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.list(planParam));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//
//    @ApiOperation("获取可前置任务列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planParam", dataType = "PlanParam")
//    })
//    @PostMapping(value = "/before/list")
//    public ResponseDTO<List<PlanSimpleVo>> list( @RequestBody  BeforeParamDto beforeParamDto)  throws Exception {
//        try {
//            return new ResponseDTO(beforeAfterToPlanService.beforeList(beforeParamDto));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//}
