package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.JobStudyReviewDTO;
import com.chinasie.orion.domain.entity.JobStudyReview;
import com.chinasie.orion.domain.vo.JobStudyReviewVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:29
 * @description:
 */

public interface JobStudyReviewService extends OrionBaseService<JobStudyReview> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobStudyReviewVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobStudyReviewDTO
     */
    String create(JobStudyReviewDTO jobStudyReviewDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobStudyReviewDTO
     */
    Boolean edit(JobStudyReviewDTO jobStudyReviewDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<JobStudyReviewVO> pages(Page<JobStudyReviewDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobStudyReviewVO> vos) throws Exception;

    /**
     *  新增日常数据： 默认新增一条研读审查的空数据
     * @param rsp
     */
    void saveDefault(String rsp);
}
