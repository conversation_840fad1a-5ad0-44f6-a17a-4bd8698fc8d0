package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * ContractSupplierSignedMain Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
@ApiModel(value = "ContractSupplierSignedMainDTO对象", description = "乙方签约主体")
@Data
public class ContractSupplierSignedMainDTO extends ObjectDTO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 签约主体全称
     */
    @ApiModelProperty(value = "签约主体全称")
    @NotBlank(message = "乙方签约主体全称不能为空")
    @Size(max = 100, message = "乙方签约主体全称过长，建议控制在100字符以内")
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @NotBlank(message = "乙方公司税号不能为空")
    @Size(max = 100, message = "乙方公司税号过长，建议控制在100字符以内")
    private String companyDutyParagraph;

    /**
     * 商务联系人
     */
    @ApiModelProperty(value = "商务联系人")
    @Size(max = 100, message = "商务联系人过长，建议控制在100字符以内")
    private String busContactPerson;

    /**
     * 商务联系人电话
     */
    @ApiModelProperty(value = "商务联系人电话")
    @Size(max = 20, message = "商务联系人电话过长，建议控制在20字符以内")
    @Pattern(regexp = "^[0-9]*$", message = "商务联系人电话格式错误")
    private String busContactPhone;

    /**
     * 项目联系人
     */
    @ApiModelProperty(value = "项目联系人")
    @Size(max = 100, message = "项目联系人过长，建议控制在100字符以内")
    private String projectContactPerson;

    /**
     * 项目联系人电话
     */
    @ApiModelProperty(value = "项目联系人电话")
    @Size(max = 20, message = "项目联系人电话过长，建议控制在20字符以内")
    @Pattern(regexp = "^[0-9]*$", message = "项目联系人电话格式错误")
    private String projectContactPhone;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    @Size(max = 100, message = "联系邮箱过长，建议控制在100字符以内")
    @Email(message = "联系邮箱格式错误")
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Size(max = 100, message = "联系地址过长，建议控制在100字符以内")
    private String contactAddress;

}
